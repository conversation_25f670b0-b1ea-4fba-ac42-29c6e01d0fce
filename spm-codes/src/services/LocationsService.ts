import axios from 'axios'
import servicesConfig from '../config/services'

const responseBody = res => res.data

const getHeader = () => {
    return {
        Channel: 'APP',
        Source: 'WEB_USER',
        Version: '2021-04-15',
    }
}

const requests = function (baseUrl) {
    return {
        get: url =>
            axios({
                url: baseUrl + url,
                method: 'GET',
                headers: getHeader(),
            }).then(responseBody),
        post: (url, body) =>
            axios({
                url: baseUrl + url,
                method: 'POST',
                headers: getHeader(),
                data: body,
            }).then(responseBody),
        put: (url, body) =>
            axios({
                url: baseUrl + url,
                method: 'PUT',
                headers: getHeader(),
                data: body,
            }).then(responseBody),
        delete: url =>
            axios({
                url: baseUrl + url,
                method: 'DELETE',
                headers: getHeader(),
            }).then(responseBody),
    }
}

const LocationsService = {
    ids: (ids) =>
        requests(servicesConfig.Locations).post(`/ids`, { ids }),
    getTags: (locationId: string) =>
        requests(servicesConfig.Locations).get(`/${locationId}/tags`),
    search: (companyId, deleted, status?, skip?, limit?, query?) =>
        requests(servicesConfig.Locations).get(`/search?deleted=${deleted}&companyId=${companyId}${status ? `&status=${status}` : ''}${skip ? `&skip=${skip}` : ''}${limit ? `&limit=${limit}` : ''}${query ? `&query=${encodeURIComponent(query)}` : ''}`),
    searchQuery: (companyId, deleted, query) =>
        requests(servicesConfig.Locations).get(`/search?deleted=${deleted}&companyId=${companyId}&query=${query ? encodeURIComponent(query) : query}&limit=50`),
    keys: (companyId, deleted, status?, skip?, limit?, query?) =>
        requests(servicesConfig.Locations).get(`/keys?deleted=${deleted}&companyId=${companyId}${skip ? `&skip=${skip}` : ''}${limit ? `&limit=${limit}` : ''}`),
    keysQuery: (companyId, deleted, query) =>
        requests(servicesConfig.Locations).get(`/keys?deleted=${deleted}&companyId=${companyId}&query=${query}&limit=50`),
}

export default LocationsService
