<template>
	<Filters
		:conditions="conditions"
		:filterMaster="filterMaster"
		v-on:update:conditions="$emit('update:conditions', $event)"
	/>
</template>

<script lang="ts">
import Vue from 'vue'
import { Campaign, Link } from '@/models';
import { Condition } from '@/models/trigger';
const Filters = () => import( './Filters.vue');

export default Vue.extend({
	props: ['conditions'],
	components: { Filters },
	data() {
		return {
			filterMaster: [
				{
					placeHolder: 'Select trigger link',
					title: 'Trigger link',
					value: 'link.id',
					valueType: 'text',
					type: 'select',
					options: [] as { [key: string]: any }[]
				},
			],
		}
	},
	async created() {
		const currentLocationId = this.$router.currentRoute.params.location_id;
		const calendarIdOption = lodash.find(this.filterMaster, { value: 'link.id' });
		if (calendarIdOption) {
			const links = await Link.getByLocationId(currentLocationId);
			calendarIdOption.options = links.map(link => {
				return {
					title: link.name,
					value: link.id
				}
			});
		}
	}
})
</script>

