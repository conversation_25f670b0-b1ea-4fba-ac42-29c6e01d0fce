<template>
  <div>
    <SideBarV2 v-if="getSideBarVersion === 'v2' && !$route.params.location_id"/>
    <AgencySideBar v-else-if="!$route.params.location_id" />
    <TopBar v-if="!$route.params.location_id" />
    <section class="hl_wrapper">
      <section class="custom-link" style="background-color:white;">
        <iframe
          :src="urlCompiled"
          style="height: calc(100vh - 95px);width:100%;border:none;"
          webkitallowfullscreen
          mozallowfullscreen
          allowfullscreen
        ></iframe>
        <div ref="emailBuilder" style="height:100%"></div>
      </section>
    </section>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { AuthUser, User, Company, Location } from '@/models'
import TopBar from '../../components/TopBar.vue'
import AgencySideBar from '../../components/agency/SideBar.vue'
import { getValues, formatName } from '@/util/template'
import handlebars from 'handlebars'
import SideBarV2 from '../../components/sidebar/SideBar.vue'

let unsubscribeLocations: () => void

export default Vue.extend({
  components: { TopBar, AgencySideBar, SideBarV2 },
  data() {
    return {
      authUser: {} as AuthUser,
      company: {} as Company,
      customMenuLink: {} as object,
      loading: false as boolean,
      urlCompiled: '' as string,
      currentLocationId: '' as string,
      params: {} as any,
    }
  },
  watch: {
    $route(to, from) {
      this.fetchData()
    },
  },
  async created() {},
  async mounted() {
    this.currentLocationId = this.$router.currentRoute.params.location_id || ''
    if (this.currentLocationId) {
      await this.fillParamsObject()
      await this.fetchData()
    } else {
      await this.fetchData()
    }
  },
  computed: {
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    getSideBarVersion(): string {
        return this.$store.getters['sidebarv2/getVersion'] 
    },
  },
  methods: {
    async fetchData() {
      this.authUser = await this.$store.dispatch('auth/get')
      this.company = await Company.getById(this.authUser.companyId)

      this.customMenuLink =
        this.company.customMenuLinks.find(
          item => item.id === this.$route.params.id
        ) || {}

      this.urlCompiled = this.currentLocationId
        ? handlebars.compile(this.customMenuLink.url)(this.params)
        : this.customMenuLink.url
    },
    async fillParamsObject() {
      const location = await Location.getById(this.currentLocationId)
      this.authUser = await this.$store.dispatch('auth/get')
      const user = await User.getById(this.authUser.userId)

      this.params.location = {
        id: new handlebars.SafeString(location.id || ''),
        name: new handlebars.SafeString(location.name || ''),
        city: new handlebars.SafeString(location.city || ''),
        state: new handlebars.SafeString(location.state || ''),
        country: new handlebars.SafeString(location.country || ''),
        address: new handlebars.SafeString(location.address || ''),
        email: new handlebars.SafeString(location.email || ''),
        phone: new handlebars.SafeString(location.phone || ''),
        postal_code: new handlebars.SafeString(location.postalCode || ''),
        full_address: new handlebars.SafeString(location.fullAddressLine || ''),
        website: new handlebars.SafeString(location.website || ''),
        logo_url: new handlebars.SafeString(location.logoUrl || ''),
      }
      this.params.location_owner = {
        first_name: new handlebars.SafeString(location?.prospectInfo?.first_name || ''),
        last_name: new handlebars.SafeString(location?.prospectInfo?.last_name || ''),
        email: new handlebars.SafeString(location?.prospectInfo?.email || ''),
      }
      this.params.user = {
        first_name: new handlebars.SafeString(formatName(user.firstName || '')),
        last_name: new handlebars.SafeString(formatName(user.lastName || '')),
        name: new handlebars.SafeString(formatName(user.name || '')),
        phone: new handlebars.SafeString(formatName(user.phone || '')),
        email: new handlebars.SafeString(formatName(user.email || ''))
      }
      const { custom_values } = await getValues(location.id, this.params)
      this.params = { ...this.params, custom_values }
    },
  },
})
</script>

<style scoped>
.spinner {
  display: table;
  margin: 0 auto;
}
.custom-link {
  padding-top: 6px;
  padding-bottom: 0;
}
</style>
