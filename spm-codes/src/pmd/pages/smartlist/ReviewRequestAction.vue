<template>
  <div class="form-group">
    <label>Review Request Options</label>
    <vSelect
      :options="availableOptions"
      label="name"
      v-model="selected"
      v-validate="'required'"
      :clearable="false"
      name="review_type">
    </vSelect>
    <span v-show="errors.has('review_type')" class="error">{{ errors.first('review_type') }}</span>
    <!-- <span v-show="errors.has('campaign')" class="error">Campaign is required.</span> -->
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import vSelect from 'vue-select'
import { IReviewRequestSpecs } from './server/bulkreq_interfaces'

export default Vue.extend({
  name: 'ReviewRequestAction',
  inject: ['parentValidator'],
  props: {
    action: Object as () => IReviewRequestSpecs,
    locationId: String,
  },
  components: {
    vSelect,
  },
  data() {
    return {
      selected: null as { [key: string]: any } | null,
      availableOptions: [{name: 'SMS review request', review_type:'sms'}, {name: 'Email review request', review_type:'email'}] as { [key: string]: any }[],
    }
  },
  methods: {},
  watch: {
    selected(selected: { [key: string]: any } | null) {
      if (this.action && selected) this.action.review_type = selected.review_type;
      else if (this.action) this.action.review_type = null;
    },
  },
  created() {
    this.$validator = this.parentValidator
  },
})
</script>
