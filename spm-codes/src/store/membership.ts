
import { Module, ActionContext } from 'vuex'
import { RootState, MembershipState } from './state_models'
import { LocationMembership } from '../models'

export const membership: Module<MembershipState, RootState> = {
  namespaced: true,
  state: { offers: [], products: [], locationId: undefined } as MembershipState,
  getters: {
    getOffers(state: MembershipState) {
      return [...state.offers];
    },
    getProducts(state: MembershipState) {
      return [...state.products]
    },
  },
  mutations: {
    setOffers (state, offers) {
      state.offers = offers;
    },
    setProducts (state, products) {
      state.products = products;
    },
    setLocationId (state, locId) {
      state.locationId = locId;
    },
    clearAll(state) {
      state.offers = [];
      state.products = [];
    }
  },
  actions: {
    async syncAll(
      context: ActionContext<MembershipState, RootState>,
      params: { locationId: string, forceRefresh?: boolean}
    ) {

      if(!params || !params.locationId) {
        context.commit('clearAll')
        return
      }
      
      const locationId = params.locationId;
      
      if (!params.forceRefresh && context.state.locationId === locationId) return;
      context.commit('setLocationId',params.locationId);
      
      context.commit('clearAll')
      const membership  = await LocationMembership.forLocation(locationId);
      if (context.state.locationId ===  locationId) { // some other call may have changed locationid
        if (membership.offers) context.commit('setOffers', membership.offers.map(a=> ({ key: a.offer_id, value: a.offer_name})));
        if (membership.products) context.commit('setProducts', membership.products.map(a=> ({ key: a.product_id, value: a.product_name})));
      }
    },
    logout: {
      root: true,
      handler: async (context: ActionContext<MembershipState, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
