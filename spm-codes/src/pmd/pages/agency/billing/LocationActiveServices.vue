<template>
    <div class="hl_billing--services">
        <div class="card">
            <div class="card-header">
                <h3 style="margin-bottom:0px">Active Services </h3>
            </div>
            <div class="card-body">
                <div class="service-card" v-if="formatedServices.length">
                    <div class="service-card-body">
                    <div class="table-wrap">
                        <table class="table">
                        <tbody>
                            <tr v-for="service in formatedServices" :key="service.plan.plan_level + '_' + service.id">
                                <td>
                                    <div :class="`plan-icon-wrap --${service.icon_bgcolor_name}`">
                                        <img :src="service.icon_url" class="plan-icon">
                                    </div>
                                    {{service.name}}
                                </td>
                                <td class="plan-name">
                                    <span :class="`label --${$options.planClasses[service.plan.plan_level-1]}`">{{service.plan.name}}</span>
                                </td>
                                <td class="plan-price">
                                    ${{service.plan.wholesale_recur_price}}/month
                                </td>
                            </tr>
                        </tbody>
                        </table>
                    </div>
                    </div>
                </div>
                <div class="no-history" v-else-if="loaded">
                    <moon-loader color="#37ca37" size="30px" />
                </div>
                <div class="no-active-service" v-else @click="$router.push({ name: 'marketplace-frame'})">
                    Add New Service &nbsp;
                    <i class="fas fa-cart-plus"></i>
                </div>
            </div>
        </div>
    </div>
</template>
<script lang='ts'>
// TODO: Move it to account subfolder, once created

import Vue from 'vue';
import { AgencyService } from '@/models';

export default Vue.extend({
    props: ['location'],
    data() {
        return {
            services: [] as AgencyService[],
            loaded: false,
		}
    },
    planClasses : ['basic','plus','premium','elite','basic','plus','premium','elite'],
    // mounted(){
    //     this.fetchServices();
    // },
    methods:{
        async fetchServices() {
            this.services = await AgencyService.getAllServices();
        },
    },
    computed:{
        formatedServices() {
            var services = [];
            if(this.location && this.location.services && this.location.services.length){
                if(this.services.length === 0){
                    this.fetchServices();
                }
				var allServices = [...this.services];
				Object.entries(this.location.services).forEach( ([key, service]) => {
                // this.location.services.forEach(service => {
                    var serviceIndex = allServices.findIndex( x=> x._id === service.service_id);
                    if(serviceIndex !== -1){
                        var currentService = allServices[serviceIndex]._data

                        var planIndex = currentService.plans.findIndex( x=> x.plan_level === service.plan_level)
                        if(planIndex !== -1){
                            currentService['plan'] = { ...currentService.plans[planIndex] };
                        }
                        services.push({...currentService, id: service.service_id })
                    }
                });
                this.loaded = true;
            }
            return services;
        }
    },
    watch: {
		'location.services': () => {

        },
    }
})
</script>
<style scoped>
.hl_billing--services .service-card .table .label.--basic{
    background-color:#ff870d
}
.hl_billing--services .service-card .table .label.--plus{
    background-color:#6e22ff
}
.plan-icon-wrap{
    background-color: #a7a7a7;
    display: inline-block;
    border-radius: 4px;
    height: 24px;
    width: 24px;
    margin-right: 5px;
}
.plan-icon-wrap.--blue{background-color:#1b23fc}
.plan-icon-wrap.--magenta{background-color:#ff1342}
.plan-icon-wrap.--lt-blue{background-color:#00b2ff}
.plan-icon-wrap.--green{background-color:#00da58}
.plan-icon-wrap.--purple{background-color:#ac42ff}
.plan-icon-wrap.--orange{background-color:#ff8913}
.plan-icon{
    height: 16px;
    width: 16px;
    margin: 4px;
}
.plan-name{
    text-align: center;
}
.plan-price{
    text-align: right;
}
.plan-remove{
    color: #ffc3c3;
    font-size: 14px;
    cursor: pointer;
    padding-left: 0px;
    text-align: center;
    width: 18px;
}
.no-active-service{
    text-align: center;
    cursor: pointer;
    padding: 20px;
    color: #979797;
}
.service-card .table tr:nth-child(1) td{
    border-top: none;
}
</style>
