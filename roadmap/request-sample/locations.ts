fetch("https://backend.leadconnectorhq.com/locations/search?deleted=false&companyId=o9a9azEFB9R4uO5BvCAF&limit=50", {
    "headers": {
      "accept": "application/json, text/plain, */*",
      "accept-language": "en-US,en;q=0.9",
      "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Uzbc5Z5r_z6DulTuXCBpvENwv-LS6F_NlHUpTHJazAw",
      "cache-control": "no-cache",
      "channel": "APP",
      "source": "WEB_USER",
      "token-id": "eyJhbGciOiJSUzI1NiIsImtpZCI6ImE0YTEwZGVjZTk4MzY2ZDZmNjNlMTY3Mjg2YWU5YjYxMWQyYmFhMjciLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KTUY0Tb0JYdntyRcvdiWJE4dUeZ02ivBsAw-cYUVA7waBwSpS-pIIWRkDcYsfWa8bt-Hv2bRXRVu8qIGdv5lcolLFhnz0SNLHDE6scwv2q9-VX2542SHAQEVFHjz_f9p_opJzfHQWvClu07QYy2UdXZvGnWoCfz5O-T-vLSKeouHbYXfVaVTRZVOl3CSGw7PM_TcGsRxY1cWF_oGPigcKvppAfI1hl2XAzAv3VzdM6fIvGAS1j18iwUvsQdPjAFYM89okG08K8BzIuptVkyt_sb1gK77zYMX2gdQARkxQc7wv5brf037v2-FkvMPeoLLVJNbufkDAYh8jXM8jj-UWA"
    },
    "referrer": "https://app.gohighlevel.com/",
    "referrerPolicy": "strict-origin-when-cross-origin",
    "body": null,
    "method": "GET",
    "mode": "cors",
    "credentials": "include"
  });