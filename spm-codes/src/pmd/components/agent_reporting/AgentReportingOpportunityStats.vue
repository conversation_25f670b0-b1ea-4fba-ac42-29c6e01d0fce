<template>
  <div class="col-md-6 mb-3">
    <div class="card">
      <div class="card-header">
        <div class="d-flex">
          <h2 class="mr-0">Opportunities</h2>
          <!-- <div class="ml-2 mt-1">
            <span
              style="margin-top: -7px;"
              class="input-group-addon"
              v-b-tooltip.hover
              title="trigger links (embedded in message) clicked as % of messages sent"
            >
              <i class="fas fa-question-circle"></i>
            </span>
          </div> -->
        </div>
      </div>
      <div v-if="!isLoading" class="card-body">
        <table class="table">
          <tbody>
            <tr>
              <td>Total Leads</td>
              <td>{{data && data.total ? data.total.count || 0 : 0}}</td>
              <td>
                <span v-html="getPercentHTML(data && data.total ? data.total.percentage || 0 : 0)"/>
              </td>
            </tr>
            <tr>
              <td>Open</td>
              <td>{{data && data.open ? data.open.count || 0 : 0}}</td>
              <td>
                <span v-html="getPercentHTML(data && data.open ? data.open.percentage || 0 : 0)"/>
              </td>
            </tr>
            <tr>
              <td>Won</td>
              <td>{{data && data.won ? data.won.count || 0 : 0}}</td>
              <td>
                <span v-html="getPercentHTML(data && data.won ? data.won.percentage || 0 : 0)"/>
              </td>
            </tr>
            <tr>
              <td>Abandoned</td>
              <td>{{data && data.abandoned ? data.abandoned.count || 0 : 0}}</td>
              <td>
                <span v-html="getPercentHTML(data && data.abandoned ? data.abandoned.percentage || 0 : 0)"/>
              </td>
            </tr>
            <tr>
              <td>Lost</td>
              <td>{{data && data.lost ? data.lost.count || 0 : 0}}</td>
              <td>
                <span v-html="getPercentHTML(data && data.lost ? data.lost.percentage || 0 : 0)"/>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div v-else class="card-body">
        <MoonLoader size="30px" radius="50%" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import MoonLoader from '../MoonLoader'

import get_percent_change_html from '../../../util/get_percent_change_html'

export default Vue.extend({
  components: {
    MoonLoader,
  },
  props: {
    isLoading: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default(){
        return {}
      }
    }
  },
  methods: {
    getPercentHTML : get_percent_change_html
  },
})
</script>

<style scoped>

</style>
