<template>
  <div>
    <div class="card">
      <div class="card-header d-flex justify-content-between flex-wrap">
        <div class="d-flex">
          <h2 class="mr-0">Call Metrics</h2>
        </div>
      </div>
      <div v-if="!isLoading" class="card-body">
        <div class="row justify-content-end mb-3 mr-1">
          <div class="total-stats mt-2">
            <span class="h5">Total Calls</span>
            <span class="px-3 h5">{{data && data.totalCalls ? data.totalCalls.count || 0 : 0}}</span>
            <span class="h5" v-html="getPercentHTML(data && data.totalCalls ? data.totalCalls.percentage : 0)"/>
          </div>
          <div class="ml-5 total-stats mt-2">
            <span class="h5">Total Call Time</span>
            <span class="px-3 h5">{{get_formatted_duration(data && data.totalCallDuration ? data.totalCallDuration.count || 0 : 0)}}</span>
            <span class="h5" v-html="getPercentHTML(data && data.totalCallDuration ? data.totalCallDuration.percentage : 0)"/>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6 col-lg-3 mt-2">
            <h5>Automation Calls</h5>
            <table class="table">
              <tbody>
                <tr>
                  <td>
                      Total Calls
                  </td>
                  <td>{{data && data.campaignTotalCall ? data.campaignTotalCall.count || 0 : 0}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.campaignTotalCall ? data.campaignTotalCall.percentage || 0 : 0)"/>
                  </td>
                </tr>
                <tr>
                  <td>
                    Answered
                  </td>
                  <td>{{data && data.leadAnswered ? data.leadAnswered.count || 0 : 0}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.leadAnswered ? data.leadAnswered.percentage || 0 : 0)"/>
                  </td>
                </tr>
                <tr>
                  <td>
                      Agent Missed
                  </td>
                  <td>{{data && data.agentMissed ? data.agentMissed.count || 0 : 0}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.agentMissed ? data.agentMissed.percentage || 0 : 0, true)"/>
                  </td>
                </tr>
                <tr>
                  <td>
                      Lead Missed
                  </td>
                  <td>{{data && data.leadMissed ? data.leadMissed.count || 0 : 0}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.leadMissed ? data.leadMissed.percentage || 0 : 0, true)"/>
                  </td>
                </tr>
                <tr>
                  <td>
                      Failed
                  </td>
                  <td>{{data && data.campaignFailedCall ? data.campaignFailedCall.count || 0 : 0}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.campaignFailedCall ? data.campaignFailedCall.percentage || 0 : 0, true)"/>
                  </td>
                </tr>
                <tr>
                  <td>
                   Avg. Call Duration
                  </td>
                  <td>{{get_formatted_duration(data && data.campaignCallDuration ? data.campaignCallDuration.count || 0 : 0)}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.campaignCallDuration ? data.campaignCallDuration.percentage || 0 : 0)"/>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="col-md-6 col-lg-3 mt-2">
            <h5>Manual Action Calls</h5>
            <table class="table">
              <tbody>
                <tr>
                  <td>
                    Total calls
                  </td>
                  <td>{{data && data.manualActionCall ? data.manualActionCall.count || 0 : 0}}</td>
                  <td>
                     <span v-html="getPercentHTML(data && data.manualActionCall ? data.manualActionCall.percentage || 0 : 0)"/>
                  </td>
                </tr>
                <tr>
                  <td>
                      Answered
                  </td>
                  <td>{{data && data.manualActionAnsweredCall ? data.manualActionAnsweredCall.count || 0 : 0}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.manualActionAnsweredCall ? data.manualActionAnsweredCall.percentage || 0 : 0)"/>
                  </td>
                </tr>
                <tr>
                  <td>
                      Missed
                  </td>
                  <td>{{data && data.manualActionMissedCall ? data.manualActionMissedCall.count || 0 : 0}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.manualActionMissedCall ? data.manualActionMissedCall.percentage || 0 : 0, true)"/>
                  </td>
                </tr>
                <tr>
                  <td>
                      <div>
                        Failed
                        <span
                          style="margin-top: -7px;"
                          class="input-group-addon"
                          v-b-tooltip.hover
                          title="Failed include Voicemail Calls"
                        >
                          <i class="fas fa-question-circle"></i>
                        </span>
                      </div>
                  </td>
                  <td>{{data && data.manualActionFailedCall ? data.manualActionFailedCall.count || 0 : 0}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.manualActionFailedCall ? data.manualActionFailedCall.percentage || 0 : 0, true)"/>
                  </td>
                </tr>
                <tr>
                  <td>
                      Avg. Call Duration
                  </td>
                  <td>{{get_formatted_duration(data && data.manualActionDuration ? data.manualActionDuration.count || 0 : 0)}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.manualActionDuration ? data.manualActionDuration.percentage || 0 : 0)"/>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="col-md-6 col-lg-3 mt-2">
            <h5>Dialer Calls</h5>
            <table class="table">
              <tbody>
                <tr>
                  <td>
                    Total calls
                  </td>
                  <td>{{data && data.dialerCall ? data.dialerCall.count || 0 : 0}}</td>
                  <td>
                     <span v-html="getPercentHTML(data && data.dialerCall ? data.dialerCall.percentage || 0 : 0)"/>
                  </td>
                </tr>
                <tr>
                  <td>
                      Answered
                  </td>
                  <td>{{data && data.dialerAnsweredCall ? data.dialerAnsweredCall.count || 0 : 0}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.dialerAnsweredCall ? data.dialerAnsweredCall.percentage || 0 : 0)"/>
                  </td>
                </tr>
                <tr>
                  <td>
                      Missed
                  </td>
                  <td>{{data && data.dialerMissedCall ? data.dialerMissedCall.count || 0 : 0}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.dialerMissedCall ? data.dialerMissedCall.percentage || 0 : 0, true)"/>
                  </td>
                </tr>
                <tr>
                  <td>
                      Failed
                  </td>
                  <td>{{data && data.dialerFailedCall ? data.dialerFailedCall.count || 0 : 0}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.dialerFailedCall ? data.dialerFailedCall.percentage || 0 : 0, true)"/>
                  </td>
                </tr>
                <tr>
                  <td>
                      Avg. Call Duration
                  </td>
                  <td>{{get_formatted_duration(data && data.dialerDuration ? data.dialerDuration.count || 0 : 0)}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.dialerDuration ? data.dialerDuration.percentage || 0 : 0)"/>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="col-md-6 col-lg-3 mt-2">
            <h5>Incoming Calls</h5>
            <table class="table">
              <tbody>
                <tr>
                  <td>
                    Total calls
                  </td>
                  <td>{{data && data.inboundTotalCall ? data.inboundTotalCall.count || 0 : 0}}</td>
                  <td>
                     <span v-html="getPercentHTML(data && data.inboundTotalCall ? data.inboundTotalCall.percentage || 0 : 0)"/>
                  </td>
                </tr>
                <tr>
                  <td>
                      Answered
                  </td>
                  <td>{{data && data.inboundAnswered ? data.inboundAnswered.count || 0 : 0}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.inboundAnswered ? data.inboundAnswered.percentage || 0 : 0)"/>
                  </td>
                </tr>
                <tr>
                  <td>
                      Missed
                  </td>
                  <td>{{data && data.inboundMissed ? data.inboundMissed.count || 0 : 0}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.inboundMissed ? data.inboundMissed.percentage || 0 : 0, true)"/>
                  </td>
                </tr>
                <tr>
                  <td>
                      Failed
                  </td>
                  <td>{{data && data.inboundFailed ? data.inboundFailed.count || 0 : 0}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.inboundFailed ? data.inboundFailed.percentage || 0 : 0, true)"/>
                  </td>
                </tr>
                <tr>
                  <td>
                      Avg. Call Duration
                  </td>
                  <td>{{get_formatted_duration(data && data.inboundCallDuration ? data.inboundCallDuration.count || 0 : 0)}}</td>
                  <td>
                    <span v-html="getPercentHTML(data && data.inboundCallDuration ? data.inboundCallDuration.percentage || 0 : 0)"/>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div v-else class="card-body pb-3">
        <MoonLoader size="30px" radius="50%" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import MoonLoader from '../MoonLoader'
import get_formatted_duration from '../../../util/get_formatted_duration'
import get_percent_change_html from '../../../util/get_percent_change_html'
export default Vue.extend({
  components: {
    MoonLoader,
  },
  props: {
    isLoading: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default(){
        return {}
      }
    }
  },
  methods: {
    get_formatted_duration: get_formatted_duration,
    getPercentHTML: get_percent_change_html
  },
})
</script>
<style scoped>
  .total-stats{
    border-bottom: 1px #cbd3d7 dotted;
  }
</style>
