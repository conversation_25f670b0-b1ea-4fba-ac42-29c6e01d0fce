<template>
	<div>
		<SideBar v-if="getSideBarVersion !== 'v2'" />
		<TopBar v-if="getSideBarVersion !== 'v2'"/>
		<section class="hl_wrapper">
			<section
				class="hl_wrapper--inner hl_agency hl_agency-sales-resources"
				id="ideas"
				style="background-color:white;padding-top: 6px !important;padding-bottom: 0px !important;"
			>
				<iframe
					src="https://www.gohighlevel.com/partner-program-preview"
					style="height: calc(100vh - 100px);width:100%;border:none;"
          id="partner_signup"
				></iframe>
			</section>
		</section>
	</div>
</template>



<script lang="ts">
import Vue from 'vue';
import { Location, User } from '@/models';
import TopBar from '../../components/TopBar.vue';
import SideBar from '../../components/agency/SideBar.vue';
import SideBarV2 from '../../components/sidebar/SideBar.vue'


let unsubscribeLocations: () => void;

export default Vue.extend({
	components: { TopBar, SideBar, SideBarV2 },
	created() {
    window.addEventListener('message', this.handleMessage);
	},
  beforeDestroy() {
    window.removeEventListener('message', this.handleMessage)
  },
  computed: {
	user() {
		const user = this.$store.state.user.user
		return user ? new User(user) : undefined
	},
	getSideBarVersion(): string {
		return this.$store.getters['sidebarv2/getVersion'] 
	},
  },
  methods: {
    handleMessage(e: any) {
      console.log(e);
      if (e.data === 'partners_list') {
          this.$router.replace({name: 'affiliates_list'})
      }
    }
  }
});
</script>

<style scoped>
.spinner {
	display: table;
	margin: 0 auto;
}
</style>

