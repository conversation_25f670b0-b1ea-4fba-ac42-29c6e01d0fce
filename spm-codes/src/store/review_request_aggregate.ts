import Vue from 'vue';
import { ReviewRequestAggregate } from '../models';
import { Module, ActionContext } from 'vuex';
import firebase from 'firebase/app';
import {
    ReviewRequestAggregateState,
    RootState,
    ReviewRequestAggregateData,
    ReviewRequestComponentData,
} from '@/store/state_models';
import * as moment from 'moment-timezone';
import { extendMoment } from 'moment-range';

let thisWeekCurrent: () => void;
let lastWeekCurrent: () => void;
let thisMonthCurrent: () => void;
let last6MonthsCurrent: () => void;
let thisYearCurrent: () => void;

function getInitialAggregateData(): ReviewRequestAggregateData {
    return {
        totalRequests: 0,
        leaderboard: {},
    };
}

function getInitialComponentData(): ReviewRequestComponentData {
    return {
        current: getInitialAggregateData(),
        fetched: false,
    };
}

function getInitialData(): ReviewRequestAggregateState {
    return {
        thisWeek: getInitialComponentData(),
        lastWeek: getInitialComponentData(),
        thisMonth: getInitialComponentData(),
        thisMonthWeekly: getInitialComponentData(),
        last6Months: getInitialComponentData(),
        lastSixMonthMonthly: getInitialComponentData(),
        thisYear: getInitialComponentData(),
        thisYearMonthly: getInitialComponentData(),
    };
}

function getReviewAggregate(docs: firebase.firestore.DocumentSnapshot[]) {
    const data = getInitialAggregateData();
    docs.forEach((snapshot) => {
        const aggregate = new ReviewRequestAggregate(snapshot);
        data.totalRequests += aggregate.totalRequests;
        lodash.forEach(aggregate.leaderBoard, (value, key) => {
            data.leaderboard[key] = (data.leaderboard[key] || 0) + value;
        });
    });
    return data;
}

export const reviewRequestAggregate: Module<ReviewRequestAggregateState, RootState> = {
    namespaced: true,
    state: getInitialData(),
    getters: {
        current: (state: ReviewRequestAggregateState, getters: any, rootState: RootState, rootGetters: any) => {
            return (filter: string) => {
                if (filter === 'This Week') {
                    return state.thisWeek.current;
                } else if (filter === 'Last Week') {
                    return state.lastWeek.current;
                } else if (filter === 'This Month') {
                    return state.thisMonth.current;
                } else if (filter === 'thisMonthWeekly') {
                    return state.thisMonthWeekly.current;
                }
                else if (filter === 'Last 6 Months') {
                    return state.last6Months.current;
                } else if (filter === 'lastSixMonthMonthly') {
                    return state.lastSixMonthMonthly.current;
                }
                else if (filter === 'This Year') {
                    return state.thisYear.current;
                }
                else if (filter === 'thisYearMonthly') {
                    return state.thisYearMonthly.current;
                }
            };
        },
    },
    mutations: {
        setThisWeek: (state: ReviewRequestAggregateState, payload: { [key: string]: any }) => {
            Vue.set(state.thisWeek, payload.type, payload.data);
        },
        setLastWeek: (state: ReviewRequestAggregateState, payload: { [key: string]: any }) => {
            Vue.set(state.lastWeek, payload.type, payload.data);
        },
        setThisMonth: (state: ReviewRequestAggregateState, payload: { [key: string]: any }) => {
            Vue.set(state.thisMonth, payload.type, payload.data);
        },
        setThisMonthWeekly: (state: ReviewRequestAggregateState, payload: { [key: string]: any }) => {
            Vue.set(state.thisMonthWeekly, payload.type, payload.data);
        },
        setLast6Months: (state: ReviewRequestAggregateState, payload: { [key: string]: any }) => {
            Vue.set(state.last6Months, payload.type, payload.data);
        },
        setLastSixMonthMonthly: (state: ReviewRequestAggregateState, payload: { [key: string]: any }) => {
            Vue.set(state.lastSixMonthMonthly, payload.type, payload.data);
        },
        setThisYear: (state: ReviewRequestAggregateState, payload: { [key: string]: any }) => {
            Vue.set(state.thisYear, payload.type, payload.data);
        },

        setThisYearMonthly: (state: ReviewRequestAggregateState, payload: { [key: string]: any }) => {
            Vue.set(state.thisYearMonthly, payload.type, payload.data);
        },
        reset: (state: ReviewRequestAggregateState) => {
            Object.assign(state, getInitialData());
        },
    },
    actions: {
        fetchThisWeek: {
            handler: async (context: ActionContext<ReviewRequestAggregateState, RootState>, locationId: string) => {
                if (context.state.thisWeek.fetched) {
                    return true;
                }
                context.commit('setThisWeek', { type: 'fetched', data: true });

                const currentWeek = moment()
                    .utc()
                    .startOf('week');
                const previousWeek = currentWeek.clone().subtract(1, 'week');

                if (thisWeekCurrent) { thisWeekCurrent(); }

                thisWeekCurrent = ReviewRequestAggregate.getByLocationWeekRealtime(locationId, currentWeek).onSnapshot(
                    (snapshot) => {
                        const aggregate = getReviewAggregate(snapshot.docs);
                        context.commit('setThisWeek', { type: 'current', data: aggregate });
                    }
                );
            },
        },
        fetchLastWeek: {
            handler: async (context: ActionContext<ReviewRequestAggregateState, RootState>, locationId: string) => {
                if (context.state.lastWeek.fetched) {
                    return true;
                }
                context.commit('setLastWeek', { type: 'fetched', data: true });

                const currentWeek = moment()
                    .utc()
                    .startOf('week')
                    .subtract(1, 'week');
                const previousWeek = currentWeek.clone().subtract(1, 'week');

                if (lastWeekCurrent) { lastWeekCurrent(); }

                lastWeekCurrent = ReviewRequestAggregate.getByLocationWeekRealtime(locationId, currentWeek).onSnapshot(
                    (snapshot) => {
                        const aggregate = getReviewAggregate(snapshot.docs);
                        context.commit('setLastWeek', { type: 'current', data: aggregate });
                    }
                );
            },
        },
        fetchThisMonth: {
            handler: async (context: ActionContext<ReviewRequestAggregateState, RootState>, locationId: string) => {
                if (context.state.thisMonth.fetched) {
                    return true;
                }
                context.commit('setThisMonth', { type: 'fetched', data: true });

                const startMonth = moment()
                    .utc()
                    .startOf('month');
                const endMonth = moment().utc().endOf('month');
                const previousPeriodStartMonth = startMonth.clone().subtract(1, 'month');
                const previousPeriodEndMonth = previousPeriodStartMonth.clone();

                if (thisMonthCurrent) { thisMonthCurrent(); }
                thisMonthCurrent = ReviewRequestAggregate.getByLocationYearMonthsRealtime(
                    locationId,
                    startMonth,
                    endMonth
                ).onSnapshot((snapshot) => {
                    const aggregate = (snapshot.docs);
                    context.commit('setThisMonth', { type: 'current', data: aggregate });
                });
            },
        },
        fetchThisMonthWeekly: {
            handler: async (context: ActionContext<ReviewRequestAggregateState, RootState>, locationId: string) => {
                if (context.state.thisMonthWeekly.fetched) {
                    return true;
                }
                context.commit('setThisMonthWeekly', { type: 'fetched', data: true });

                const startMonth = moment().utc()
                    .startOf('month');
                const endMonth = moment().endOf('month');
                const previousPeriodStartMonth = startMonth.clone().subtract(1, 'month');
                const previousPeriodEndMonth = previousPeriodStartMonth.clone();

                if (thisMonthCurrent) { thisMonthCurrent(); }



               // let monthRange = extendMoment(moment).range(startMonth, endMonth);

                let numberOfDays = Math.abs(moment().endOf('month').startOf('day').diff(moment().startOf('month').startOf('day'),'days'))
                // give ( actual month day - 1 ) 

                // Get all the weeks during the current month
                let weeks = []
                for (let i = 0; i <= numberOfDays; i++) {
                    let mday = startMonth.clone().add(i, 'days');
                    if (!weeks.find(eachWeek => eachWeek.weekNumber === mday.week())) {
                        if (mday.month() === startMonth.month()) {
                            const monthOnStartOfWeek = moment().year(mday.year()).week(mday.week()).startOf('week').month()
                            if (monthOnStartOfWeek === startMonth.month()) {
                                weeks.push({
                                    day: mday,
                                    weekNumber: mday.week()
                                });
                            }

                        }
                    }
                }
                weeks = weeks.map(eachWeek => {
                    return moment().year(eachWeek.day.year()).week(eachWeek.weekNumber).utc()
                        .startOf('week');
                })

                let weeklyReview = [];
                for (let eachWeek of weeks) {
                    let docs = await toPromise(ReviewRequestAggregate.getByLocationWeekRealtime(locationId, eachWeek.clone())) as firebase.firestore.DocumentSnapshot[];
                    const aggregate = getReviewAggregate(docs);
                    weeklyReview.push({
                        label: eachWeek,
                        aggregate
                    })
                }
                context.commit('setThisMonthWeekly', { type: 'current', data: weeklyReview });

            },
        },
        fetchLast6Months: {
            handler: async (context: ActionContext<ReviewRequestAggregateState, RootState>, locationId: string) => {
                if (context.state.last6Months.fetched) {
                    return true;
                }
                context.commit('setLast6Months', { type: 'fetched', data: true });

                const endMonth = moment()
                    .utc()
                    .startOf('month');
                const startMonth = moment()
                    .utc()
                    .startOf('month')
                    .subtract(6, 'months');
                const previousPeriodEndMonth = startMonth.clone().subtract(1, 'month');
                const previousPeriodStartMonth = previousPeriodEndMonth.clone().subtract(6, 'month');

                if (last6MonthsCurrent) { last6MonthsCurrent(); }

                last6MonthsCurrent = ReviewRequestAggregate.getByLocationYearMonthsRealtime(
                    locationId,
                    startMonth,
                    endMonth
                ).onSnapshot((snapshot) => {
                    const aggregate = getReviewAggregate(snapshot.docs);
                    context.commit('setLast6Months', { type: 'current', data: aggregate });
                });
            },
        },
        lastSixMonthMonthly: {
            handler: async (context: ActionContext<ReviewRequestAggregateState, RootState>, locationId: string) => {
                if (context.state.lastSixMonthMonthly.fetched) {
                    return true;
                }
               

                const endMonth = moment().utc()
                    .startOf('month');
                // const previousPeriodEndMonth = startMonth.clone().subtract(1, 'month');
               // const previousPeriodStartMonth = previousPeriodEndMonth.clone().subtract(6, 'month');

                let monthlyReview = [];
                for (let i = -1 ; i < 5 ; i++ ) {
                    let docs = await toPromise(ReviewRequestAggregate.getByLocationYearMonthRealtime(
                        locationId, 
                        endMonth.clone().subtract(i+1, 'month'))) as firebase.firestore.DocumentSnapshot[];;

                    const aggregate = getReviewAggregate(docs);
                    monthlyReview.push({
                        label: endMonth.clone().subtract(i+1, 'month'),
                        aggregate
                    })
                }
                
                context.commit('setLastSixMonthMonthly', { type: 'current', data: monthlyReview.reverse() });
                context.commit('setLastSixMonthMonthly', { type: 'fetched', data: true });
            },
        },
        fetchThisYear: {
            handler: async (context: ActionContext<ReviewRequestAggregateState, RootState>, locationId: string) => {
                if (context.state.thisYear.fetched) {
                    return true;
                }
                context.commit('setThisYear', { type: 'fetched', data: true });

                const startMonth = moment()
                    .utc()
                    .startOf('year');
                const endMonth = moment()
                    .utc()
                    .startOf('month');
                const previousPeriodEndMonth = startMonth.clone().subtract(1, 'month');
                const previousPeriodStartMonth = previousPeriodEndMonth.clone().startOf('year');

                if (thisYearCurrent) { thisYearCurrent(); }

                thisYearCurrent = ReviewRequestAggregate.getByLocationYearMonthsRealtime(
                    locationId,
                    startMonth,
                    endMonth
                ).onSnapshot((snapshot) => {
                    const aggregate = getReviewAggregate(snapshot.docs);
                    context.commit('setThisYear', { type: 'current', data: aggregate });
                });
            },
        },
        thisYearMonthly: {
            handler: async (context: ActionContext<ReviewRequestAggregateState, RootState>, locationId: string) => {
                if (context.state.thisYearMonthly.fetched) {
                    return true;
                }
                context.commit('setThisYearMonthly', { type: 'fetched', data: true });

                const endMonth = moment().utc()
                    .startOf('month');
              //  const previousPeriodEndMonth = startMonth.clone().subtract(1, 'month');
               // const previousPeriodStartMonth = previousPeriodEndMonth.clone().subtract(6, 'month');

                let monthlyReview = [];
                for (let i = -1 ; i < 11 ; i++ ) {
                    let curMonth =  endMonth.clone().subtract(i+1, 'month').utc()
                    if(curMonth.year() !== endMonth.year() ){
                        break;
                    }
                    let docs = await toPromise(ReviewRequestAggregate.getByLocationYearMonthRealtime(
                        locationId, 
                        curMonth)) as firebase.firestore.DocumentSnapshot[];;

                    const aggregate = getReviewAggregate(docs);
                    monthlyReview.push({
                        label: endMonth.clone().subtract(i+1, 'month'),
                        aggregate
                    })
                }
                
                context.commit('setThisYearMonthly', { type: 'current', data: monthlyReview.reverse() });
            },
        },
        reset: {
            handler: async (context: ActionContext<ReviewRequestAggregateState, RootState>) => {
                if (thisWeekCurrent) thisWeekCurrent();
                if (lastWeekCurrent) lastWeekCurrent();
                if (thisMonthCurrent) thisMonthCurrent();
                if (last6MonthsCurrent) last6MonthsCurrent();
                if (thisYearCurrent) thisYearCurrent();
                context.commit('reset');
            },
        },
    },
};


function toPromise(foo) {
    return new Promise((resolve, reject) => {
        foo.onSnapshot((docs => {
            return resolve(docs)
        }))
    })

}