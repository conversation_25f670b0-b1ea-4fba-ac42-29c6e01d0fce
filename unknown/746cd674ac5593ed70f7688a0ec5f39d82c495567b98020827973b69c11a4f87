<template>
  <div class="tab-content">
    <div
      class="tab-pane fade show active"
      id="assign-user"
      role="tabpanel"
      aria-labelledby="assign-user"
    >
      <div class="form-group">
        <label>Users</label>
        <vSelect
          multiple
          :options="users"
          label="name"
          v-model="assignTo"
          :clearable="false"
          placeholder="Select User"
          v-validate="'required'"
          name="assignTo"
          data-vv-as="assignTo"
          :reduce="user => user.id"
          @input="setValue('user_list', assignTo)"
        ></vSelect>
        <span v-show="errors.has('assignTo')" class="error"
          >User needs to be selected</span
        >
      </div>
      <div class="form-group">
        <div class="d-flex w-100 mb-2">
          <div class="col-toggle pr-2">
            <div class="toggle">
              <UIToggle
                :id="`allow-only-unassigned-tgl-${toggleIdentifier}`"
                v-model="onlyUnassignedContact"
                @change="setValue('only_unassigned_contact', $event)"
              />
              <label class="tgl-btn" :for="`allow-only-unassigned-tgl-${toggleIdentifier}`"></label>
            </div>
          </div>
          <div class="col-content pl-2">
            Only apply to unassigned contacts
          </div>
        </div>
      </div>
      <div v-if="assignTo.length > 1">
        <div class="form-group">
          <label>Split Traffic</label>
          <div class="form-input-dropdown dropdown">
            <div data-toggle="dropdown">
                <i class="icon icon-arrow-down-1"></i>
                <input type="text" class="form-control" data-lpignore="true" placeholder="Split Traffic" :value="trafficSplit | toTitleCase">
            </div>
            <div class="dropdown-menu">
                <a class="dropdown-item trigger-type" href="javascript:void(0);" v-for="option in trafficSplitOptions" :key="option" @click.prevent="setValue('traffic_split', option)">
                  <p>{{ option | toTitleCase }}</p>
                </a>
            </div>
          </div>
        </div>
        <div class="form-group" v-if="users.length && assignTo.length && trafficSplit === 'unevenly'">
          <label>Traffic Weightage</label>
          <div v-for="userId in assignTo" :key="userId" class="row weightage-field">
            <label :for="userId" class="col-md-5 weightage-label">{{userName(userId)}}:</label>
            <div class="col-md-7">
              <input
                type="number"
                class="form-control"
                :name="userId"
                placeholder="Weightage"
                :value="trafficWeightage[userId]"
                @keydown="weightageKeyDown"
                @input="setWeightageInput(userId, $event.target.value)"
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { User } from '@/models'
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'
import vSelect from "vue-select";

declare var $: any
export default Vue.extend({
  props: ['action', 'triggerType'],
  components: { vSelect },
  data() {
    return {
      assignTo: '',
      onlyUnassignedContact: true,
      currentLocationId: '',
      users: [] as User[],
      trafficSplitOptions: ['equally', 'unevenly'],
      trafficWeightage: {} as { [key: string]: string },
      toggleIdentifier: 0
    }
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.currentLocationId = id
    },
    assignTo() {
      this.setWeightage();
    },
    trafficSplit() {
      this.setWeightage();
    },
    trafficWeightage() {
      this.trafficWeightageChanged();
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    trafficSplit() {
      return this.action.traffic_split || ''
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    if (this.currentLocationId) {
      this.users = this.$store.state.users.users.map(user => {
        user.name = user.first_name + ' ' + user.last_name;
        return user
      });
    }
    if (this.action.user_list) this.assignTo = this.action.user_list;
    if (this.action.only_unassigned_contact !== undefined) this.onlyUnassignedContact = this.action.only_unassigned_contact;
    if (this.action.traffic_weightage) this.trafficWeightage = this.action.traffic_weightage;
    this.toggleIdentifier = Math.random() // Used for multiple toggles on the same trigger
  },
  methods: {
    trafficWeightageChanged() {
      let min = Math.min(...Object.values(this.trafficWeightage));
      if (min) {
        let actualWeightage = this.assignTo.map(userId => {
          return { id: userId, weightage: Math.round(this.trafficWeightage[userId] / min) }
        });
        let total = actualWeightage.reduce((total, weightage) => total + weightage.weightage, 0);
        let indexesMapped = 0;
        let trafficIndex = actualWeightage.map((userWeightage:any)=>{
            var indexes = [];
            for(var i= indexesMapped + 1;i<indexesMapped + 1 + userWeightage.weightage;i++)
            {
                indexes.push(i);
            }
            indexesMapped += userWeightage.weightage;
            return {id: userWeightage.id, indexes}
        })
        this.setValue('total_index', total);
        this.setValue('traffic_index', trafficIndex);
      }
    },
    userName(userId?: string) {
      let user = lodash.find(this.users, {id: userId})
      if (this.users && user) {
        return user.name
      }
    },
    weightageKeyDown(event) {
      if (event.key === '.' || event.key === 'e') {
        event.preventDefault();
      }
    },
    setValue(field: string, value: string) {
      Vue.set(this.action, field, value)
      this.$emit('update:action', this.action)
    },
    setWeightage() {
      this.trafficWeightage = {};
      if (this.assignTo.length === 1) {
        this.setValue('traffic_split', 'equally');
        this.trafficWeightage[this.assignTo[0]] = 1;
      } else {
        if (this.trafficSplit === 'equally') {
          this.assignTo.forEach(user => {
            this.trafficWeightage[user] = 1;
          })
        } else {
          this.assignTo.forEach(user => {
            this.trafficWeightage[user] = this.action.traffic_weightage && this.action.traffic_weightage[user] ? this.action.traffic_weightage[user]: 0;
          })
        }
      }
      this.setValue('traffic_weightage', this.trafficWeightage);
    },
    setWeightageInput(userId: string, value: number) {
      if (value && value <= 0) value = 1;
      this.trafficWeightage[userId] = value;
      this.setValue('traffic_weightage', this.trafficWeightage);
      this.trafficWeightageChanged();
    }
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  }
})
</script>

<style scoped>
.weightage-label {
  display: flex;
  align-items: center;
}
.weightage-field {
  margin-bottom: 5px;
}
</style>
