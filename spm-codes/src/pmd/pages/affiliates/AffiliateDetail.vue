<template>
  <div>
    <section class="hl_wrapper">
      <section class="hl_wrapper--inner marketplace pb-0" id="marketplace">
        <div v-if="!loading" class="padding-container">
          <ul class="hl_marketplace--results">
            <li>
              <Affiliate v-if="partner" :data="partner._data" :id="partner._id" :detailPage="true"/>
            </li>
          </ul>
          <AffiliateMoreDetail v-if="partner" :partner="partner"/>
        </div>
        <div v-else class="my-5">
          <MoonLoader size="30px" radius="50%" />
        </div>
      </section>
    </section>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import TopBar from '../../components/TopBar.vue'
import SideBar from '../../components/agency/SideBar.vue'
import Affiliate from '../../components/affiliates/Affiliate'
import AffiliateMoreDetail from '../../components/affiliates/AffiliateMoreDetail'
import { Partner } from '../../../models'
import MoonLoader from '../../components/MoonLoader'

export default Vue.extend({
  components: {
    TopBar,
    SideBar,
    Affiliate,
    AffiliateMoreDetail,
    MoonLoader
  },
  data() {
      return {
          partner: undefined as Partner | undefined,
          loading: false
      }
  },
  async created() {
      this.loading = true
      this.partner = await Partner.getById(this.$route.params.affiliate_id);
      this.loading = false
  },
})
</script>

<style scoped>
.hl_marketplace--results {
  list-style: none;
  padding: 0;
  margin: 0;
  margin-bottom: 40px;
}
.hl_marketplace--results li > a {
  display: block;
  color: #607179;
  border-radius: 4px;
}
.hl_marketplace--results li > a:hover .card {
  -webkit-box-shadow: 15px 26px 40px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 15px 26px 40px 0 rgba(0, 0, 0, 0.05);
}
</style>
