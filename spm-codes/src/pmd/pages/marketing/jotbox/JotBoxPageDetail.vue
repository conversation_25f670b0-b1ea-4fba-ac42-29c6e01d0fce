<template>
  <div class="user-detail-area rounded" v-if="!loading">
    <div class="user-info-header mt-4" v-if="! isActivityPage">
      <div class="media border-0 d-flex">
        <div class="media-body">
          <h6 class="text-muted">Page detail</h6>
        </div>
      </div>
    </div>
    <div class="bg-white user-info-body shadow-sm rounded">
      <table class="table user-info-table table-hl page-detail" v-if="pageDetails.length">
        <tbody>
          <tr v-for="data in pageDetails" :key="data.name">
            <td width="18%">{{ data.name }}</td>
            <td v-if="data.name == 'URL'" class="detail">
              <a :href="data.value" target="_blank">{{data.value}}</a>
            </td>
            <td v-else>{{data.value}}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="user-detail-area rounded" v-else>
    <div class="user-info-header mt-4">
      <div class="media border-0 d-flex">
        <div class="media-body">
          <h6 class="text-muted">Page detail</h6>
        </div>
      </div>
    </div>
    <div class="bg-white user-info-body shadow-sm rounded">
      <div class="loading">
        <table class="table user-info-table">
          <tbody>
            <tr v-for="index2 in 5" :key="index2">
              <td class="p-3 td-loading-short td-loading-short-page-detail">
                <span></span>
              </td>
              <td class="p-3 td-loading-short td-loading-short-page-detail">
                <span></span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    loading: {
      type: Boolean,
      default: true
    },
    isActivityPage: {
      type: Boolean,
      default: false
    },
    util: {
      type: Object,
    }
  },
  computed: {
    pageDetails() {
      return this.util.pageDetails()
    }
  }
})
</script>

<style scoped lang="scss">
table.page-detail {
  tbody {
    tr {
      td {
        &:first-child {
          font-weight: 500;
        }
        word-break: break-all;
      }
    }
  }
}
.loading {
  td {
    vertical-align: middle;
    &.td-loading-short {
      width: 50%;
      height: 45px;
      float: left;
      span {
        width: 270px;
        height: 25px;
        display: block;
        background: linear-gradient(to right, #eee 20%, #ddd 50%, #eee 80%);
        background-size: 700px 100px;
        animation-name: moving-gradient;
        animation-duration: 1.5s;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
      }
    }
  }
  div {
    height: 40px;
    vertical-align: middle;
    padding: 8px;
    span {
      display: block;
    }
    &.td-loading-short {
      max-width: 150px;
    }
    &.td-loading-short {
      float: left;
      span {
        width: 70px;
        height: 25px;
        background: linear-gradient(to right, #eee 20%, #ddd 50%, #eee 80%);
        background-size: 700px 100px;
        animation-name: moving-gradient;
        animation-duration: 1.5s;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
      }
    }
  }
  &:first-child {
    div {
      height: 25px;
      &.td-loading-short {
        max-width: 270px;
        span {
          width: 150px;
          height: 15px;
        }
      }
    }
  }
}
</style>
