<template>
	<Filters
		:conditions="conditions"
		:filterMaster="filterMaster"
		v-on:update:conditions="$emit('update:conditions', $event)"
	/>
</template>

<script lang="ts">
import Vue from 'vue'
import { Campaign, UserCalendar, Tag } from '@/models';
import { Condition } from '@/models/trigger';
const Filters = () => import('./Filters.vue');

export default Vue.extend({
	props: ['conditions'],
	components: { Filters },
	data() {
		return {
			filterMaster: [
        {
					placeHolder: 'Select Calendar Provider',
					title: 'In Calendar Provider',
					value: 'appointment_v3.calendarProviderId',
					valueType: 'text',
					type: 'select',
					options: [] as { [key: string]: any }[]
				},
        {
					placeHolder: 'Select status',
					title: 'Appointment status is',
					value: 'appointment_v3.status',
					valueType: 'text',
					type: 'select',
					options: [{ title: 'new', value: 'new' }, { title: 'confirmed', value: 'confirmed' }, { title: 'cancelled', value: 'cancelled' }, { title: 'Showed', value: 'showed' }, { title: 'No-show', value: 'noshow' }, { title: 'invalid', value: 'invalid' }]
        },
        {
          id: "has-tag",
          placeHolder: "Has Tag",
          title: "Has Tag",
          value: "contact.tags",
          valueType: "text",
          operator: "index-of-true",
          type: "select",
          options: [] as { [key: string]: any }[]
        },
			],
		}
  },
  watch: {
    conditions: {
      handler: function(val) {
        this.setServices(val)
      },
      deep: true
    }
  },
	async created() {
		const currentLocationId = this.$router.currentRoute.params.location_id;

    const calendarProviderIdOption = lodash.find(this.filterMaster, { value: 'appointment_v3.calendarProviderId' });
    if (calendarProviderIdOption) {
			calendarProviderIdOption.options = this.$store.state.calendarProviders.calendarProviders.map(calendarProvider => {
				return {
					title: calendarProvider.name,
					value: calendarProvider.id
				}
      });
    }

    this.setServices()

    const hasTagIdOption = lodash.find(this.filterMaster, {
      id: "has-tag"
    });
    let tagOptions = (await Tag.getByLocationId(currentLocationId)).map((tag)=>{
      return {
        title: tag.name,
        value: tag.name
      }
    });
    hasTagIdOption.options = tagOptions;
  },
  methods: {
    setServices() {
      const provider = this.conditions.find(x => x.field === 'appointment_v3.calendarProviderId' && x.value)
      const serviceIndex = this.filterMaster.findIndex(x => x.value === 'appointment_v3.calendarServiceId')
      const userIndex = this.filterMaster.findIndex(x => x.value === 'appointment_v3.userId')

      if (!provider) {
        if (serviceIndex !== -1) {
          this.filterMaster.splice(serviceIndex, 1)
        }
        if (userIndex !== -1) {
          this.filterMaster.splice(userIndex, 1)
        }
      } else {
        if (userIndex === -1) {
          const users = []
          const calendarProvider = this.$store.state.calendarProviders.calendarProviders.find(x => x.id === provider.value)
          if (calendarProvider) {
            const usersIds = calendarProvider.user_ids
            if (usersIds) {
              const users = this.$store.state.users.users.filter(x => usersIds.includes(x.id))
                .map(user => {
                  return {
                    title: [user.first_name, user.last_name].filter(val => val).join(' '),
                    value: user.id
                  }
                })

              if (users) {
                const userOption = {
                  placeHolder: 'Select User',
                  title: 'Select User',
                  value: 'appointment_v3.userId',
                  valueType: 'text',
                  type: 'select',
                  options: users
                }

                this.filterMaster.splice(1, 0, userOption)
              }
            }
          }
        }

        if (serviceIndex === -1) {
          const serviceOption = {
            placeHolder: 'Select Calendar Service',
            title: 'In Calendar Service',
            value: 'appointment_v3.calendarServiceId',
            valueType: 'text',
            type: 'select',
            options: []
          }

           this.filterMaster.splice(1, 0, serviceOption)
        }

        const providerOption = this.filterMaster.find(x => x.value === 'appointment_v3.calendarServiceId')

        providerOption.options = this.$store.state.calendars.calendars
          .filter(x => x.provider_id === provider.value)
          .map(calendarService => {
            return {
              title: calendarService.name,
              value: calendarService.id
            }
          })
      }
    }
  }
})
</script>

