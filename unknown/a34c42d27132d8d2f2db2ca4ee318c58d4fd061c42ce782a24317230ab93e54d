<template>
<div class="selectedItem">
  <div class="contact">
    <p class="call-info">Click on the call icon below to contact this person</p>
    <div class="contactBox">
      <i class="fa pointer" @click="$emit('removeAssignedItem')">
        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
			<path d="M6.84961 6L12 11.1504L11.1504 12L6 6.84961L0.849609 12L0 11.1504L5.15039 6L0 0.849609L0.849609 0L6 5.15039L11.1504 0L12 0.849609L6.84961 6Z" fill="#B2B7B4"/>
		</svg>
      </i>
      <div class="detail-box">
       <span class="img-circle">
        <Avatar :contact="selectedItem" :include_name="false" />
       </span>
        <div class="user-info">
        <p>{{selectedItem.fullName || 'unknown'}}</p>
         <PhoneNumber
            type="display"
            v-model="selectedItem.phone"
            :currentLocationId="currentLocationId"
          />
        </div>
      </div>
    </div>
  </div>
</div>
</template>

<script lang="ts">
import Vue from 'vue'
import PhoneNumber from '@/pmd/components/util/PhoneNumber.vue'
import SearchBox from '@/pmd/components/power_dialer/SearchBox.vue'
const Avatar = () => import('../Avatar.vue')
export default Vue.extend({
  props: ['currentLocationId', 'selectedItem'],
  components: { PhoneNumber, Avatar, SearchBox },
  data() {
    return {
      fetching: false,
    }
  }
})
</script>
<style lang="scss" scoped>
.selectedList {
  		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	.selectedItem {
		.contact {
			.call-info {
				font-size: 12px;
				line-height: 14px;
				text-align: center;
				color: #607179;
				opacity: 0.35;
				width: 175px;
				margin: auto;
				margin-bottom: 10px;
			}
			.contactBox {
				width: 268px;
				background: #FFFFFF;
				border: 2px solid rgba(172, 172, 172, 0.55);
				box-sizing: border-box;
				box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.11);
				border-radius: 7px;
				height: 78px;
				display: flex;
				align-items: center;
				justify-content: flex-start;
				position: relative;
				.fa {
					color: #B2B7B4;
					float: right;
					position: absolute;
					right: 10px;
					top: 10px
        }
        .detail-box{
          &:hover{
            background: transparent;
          }
        }
			}
		}
	}
}
</style>
