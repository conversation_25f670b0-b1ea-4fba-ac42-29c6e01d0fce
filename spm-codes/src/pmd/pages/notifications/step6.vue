<template>
    <div class="ps-content col-xs-9">
        <div>
            <div ng-form="launchStepForm" autocomplete="false" class="ng-pristine ng-invalid ng-invalid-required">
                <div class="notification-live-preview animated bounceInUp">
                    <div class="pb-2 text-success"><i class="fa fa-circle animated fadeIn infinite mr-1"></i> Live Preview</div>
                    <PsBubble :notification="notification" />
                    <!-- <div class="ps-bubble">
                        <div class="bubble-body mx-auto no-text-select ng-scope" ng-class="{'rtl-bubble' : notification.settings.rtl}">
                            <div class="bubble-icon">
                                <img data-ng-src="data:image/svg+xml;base64,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" src="data:image/svg+xml;base64,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">
                            </div>
                            <div class="bubble-content">
                                <div class="bubble-title text-truncate">
                                    <span ng-style="notification.settings.theme.title" class="ng-binding" style="color: rgb(120, 37, 243); background-color: rgb(242, 233, 255);">50 visitors</span>
                                </div>
                                <div class="bubble-description">
                                    <span class="ng-binding">jumped around and got crazy!</span>
                                </div>
                                <div class="bubble-time">
                                    <div class="ng-binding">in the past 30 minutes
                                        
                                        <div class="ps-link ng-scope" ng-if="!$parent.settings.whitelabel || ($parent.settings.whitelabel &amp;&amp; $parent.settings.branding.active)">
                                            <span class="bubble-bolt ng-binding">⚡ by</span>
                                            <a href="" ng-style="{'color' : $parent.$parent.settings.whitelabel ? $parent.$parent.settings.branding.color || '#7627f3' : '#7627f3'}" class="ng-binding" style="color: rgb(118, 39, 243);">ProveSource</a>
                                        </div>
                                    </div>
                                </div>
                                <span id="ps-bubble-close" ng-show="notification.settings.allowClose" class="ng-hide">
                                    <span class="close-before"></span>
                                    <span class="close-after"></span>
                                </span>
                            </div>
                        </div>
                    </div> -->
                </div>
                <div class="step-content" ng-switch="notification.type">
                    <div class="text-center launch-title">
                        <!--<i class="fa fa-flag-o fa-4x"></i>-->
                        <img src="https://media.giphy.com/media/9G5gpZRM83WBBK1bEW/giphy.gif">
                        <h3>Well Done.</h3>
                        <div>We are about to launch, please take your seat and fasten your seat belt.</div>
                    </div>

                    <div class="step-panel">
                        <div class="title">Identify Your Notification</div>
                        <div class="section">

                            <p class="description">What is the name of your notification?</p>

                            <div class="ps-url-input">
                                <div class="ps-input-frame">
                                    <form name="msgsndr13" data-lpignore="true" autocomplete="msgsndr13" class="-required -pattern msgsndr13">
                                        <label class="input-title" for="message_text">Notification Name</label>
                                        <input show-focus="shouldFocus" type="text" name="message_text" id="message_text" autofocus="" placeholder="visitors" required="required">
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="step-nav-buttons-container">
                    <div class="button-container no-text-select">
                        <button class="wizard-btn next ladda-button" type="button" :disabled="nextDisabled"><span class="ladda-label">Launch
                            </span><span class="ladda-spinner"></span></button>
                        <button class="wizard-btn back " v-on:click="$emit('go-back', 5)">Back
                        </button>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script lang='ts'>
import Vue from "vue";
import {
    NotificationTemplate
} from "@/models";

declare var $: any;

import PsBubble from "./psbubble.vue";

export default Vue.extend({
    components: {
        PsBubble
    },
    props: {
        notificationId: String
    },
    data() {
        return {
            notification: {} as NotificationTemplate
        };
    },
    computed: {
        nextDisabled(): boolean {
            return true;
        },
    },
    async created() {
        this.fetchData();
    },
    watch: {
        'notificationId': function (id) {
            this.fetchData();
        },
    },
    methods: {
        async fetchData() {
            this.notification = await NotificationTemplate.getById(this.notificationId);
        },
    }
})
</script>
