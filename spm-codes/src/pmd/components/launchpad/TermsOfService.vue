<template>
  <modal
    v-if="modalShow"
    :showCloseIcon="false"
    :noBackdropClose="true"
    :maxWidth="600"
    modalMaskBgColor="white"
    id="termsOfService"
  >
    <div class="hl_settings--body">
      <div class="container-fluid">
        <div class="row">
          <div class="hl_settings--main col-lg-12">
            <div class="card">
              <div class="card-body">
                <div class="text-center">
                  <img
                    class="highlevel-logo"
                    src="../../../assets/pmd/img/ghl_logo.png"
                  />
                  <div class="popup-notice">ONLY AGENCY ADMIN CAN SEE THIS</div>
                  <div class="popup-title">A Little Housekeeping</div>
                  <div class="popup-subtitle">
                    Make sure your billing address is up to date. This allows us
                    to send updated content, swag and other goodies to you in
                    the future.
                  </div>
                </div>
                <form
                  @submit.prevent="validateBeforeSubmit()"
                  data-vv-scope="form-tandc"
                  name="form-tandc"
                >
                  <div class="form-group">
                    <UITextLabel>Billing Address*</UITextLabel>
                    <UITextInput
                      placeholder="Address"
                      v-validate="'required'"
                      v-model="address"
                      name="address"
                    />
                    <span
                      v-show="errors.has('form-tandc.address')"
                      class="error"
                      >Address required</span
                    >
                  </div>
                  <div class="form-group">
                    <UITextLabel>Address line 2</UITextLabel>
                    <UITextInput
                      placeholder="Address Line"
                      v-model="addressLine"
                      name="addressline"
                    />
                  </div>
                  <div class="row">
                    <div class="col-sm-6">
                      <div class="form-group">
                        <UITextLabel>City*</UITextLabel>
                        <UITextInput
                          placeholder="City"
                          v-model="company.city"
                          v-validate="'required'"
                          name="city"
                        />
                        <span
                          v-show="errors.has('form-tandc.city')"
                          class="error"
                          >City required</span
                        >
                      </div>
                    </div>
                    <div class="col-sm-6">
                      <div class="form-group" v-if="company.country === 'US'">
                        <UITextLabel>State / Prov / Region *</UITextLabel>
                        <UISelect
                          placeholder="Choose one.."
                          :options="twStates"
                          :value="company.state"
                          name="state"
                          :class="{
                            input: true,
                          }"
                          @change="val => {company.state = val}"
                          v-validate="'required'"
                        />
                        <span
                          v-show="errors.has('form-tandc.state')"
                          class="error"
                          >State required</span
                        >
                      </div>
                      <div class="form-group" v-else>
                        <UITextLabel>State / Prov / Region *</UITextLabel>
                        <UITextInput
                          placeholder="State"
                          v-model="company.state"
                          name="state"
                          v-validate="'required'"
                        />
                        <span
                          v-show="errors.has('form-tandc.state')"
                          class="error"
                          >State required</span
                        >
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-sm-6">
                      <div class="form-group">
                        <UITextLabel>Country*</UITextLabel>
                        <UISelect
                          placeholder="Choose country..."
                          :options="twCountries"
                          :value="company.country"
                          name="country"
                          :class="{
                            input: true,
                          }"
                          @change="val => {company.country = val; clearState}"
                          v-validate="'required'"
                        />
                        <span
                          v-show="errors.has('form-tandc.country')"
                          class="error"
                          >Country required</span
                        >
                      </div>
                    </div>
                    <div class="col-sm-6">
                      <div class="form-group">
                        <UITextLabel>Postal Code*</UITextLabel>
                        <UITextInput
                          placeholder="Postal Code"
                          v-model="company.postalCode"
                          v-validate="'required'"
                          name="postalCode"
                        />
                        <span
                          v-show="errors.has('form-tandc.postalCode')"
                          class="error"
                          >Postal Code required</span
                        >
                      </div>
                    </div>
                  </div>
                  <div
                    class="form-group"
                    v-b-tooltip.hover
                    title="HighLevel has many features to resell to your customers and turn your agency into a SaaS."
                  >
                    <label style="display: inline; margin-right: 20px"
                      >Do you currently resell or intend to resell HighLevel?
                      *</label
                    >
                    <div class="form-check-inline" style="margin-right: 50px">
                      <label class="form-check-label">
                        <input
                          type="radio"
                          class="form-check-input"
                          name="isreselling"
                          :value="true"
                          v-model="company.isReselling"
                          v-validate="'required'"
                        />Yes
                      </label>
                    </div>
                    <div class="form-check-inline">
                      <label class="form-check-label">
                        <input
                          type="radio"
                          class="form-check-input"
                          name="isreselling"
                          :value="false"
                          v-model="company.isReselling"
                          v-validate="'required'"
                        />No
                      </label>
                    </div>
                    <span
                      v-show="errors.has('form-tandc.isreselling')"
                      class="error"
                      >Please select any one option</span
                    >
                  </div>
                  <div class="form-group text-center">
                    <UICheckbox
                      id="autofill-signature"
                      v-model="tandc"
                      name="termsofservice"
                    />
                    <span for="reply-signature"
                      >I accept HighLevel's updated
                      <a
                        href="https://www.gohighlevel.com/legal"
                        target="_blank"
                        style="color: blue; cursor: pointer"
                        >Terms of Service</a
                      >
                    </span>
                    <!-- <label class="checkbox-label" style="display: inline"
                      >I accept HighLevel's updated
                      <a
                        href="https://www.gohighlevel.com/legal"
                        target="_blank"
                        style="color: blue; cursor: pointer"
                        >Terms of Service</a
                      ></label
                    > -->
                  </div>
                  <div class="form-group text-center">
                    <button
                      :class="{ disalbed_btn: disabled }"
                      type="submit"
                      class="btn btn-success"
                      :disabled="disabled"
                    >
                      <moon-loader
                        v-if="accepting"
                        :loading="accepting"
                        color="#37ca37"
                        size="21px"
                      />
                      <span class="flex flex-row items-center" v-else
                        >Continue to App <i class="icon icon-arrow-right-2 mr-2"></i
                      ></span>
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </modal>
</template>
<script lang="ts">
import Vue from 'vue'
import Modal from '@/pmd/components/common/Modal.vue'
import { Company, User } from '@/models'
import { mapState } from 'vuex'
import { UserState } from '../../../store/state_models'
import moment from 'moment-timezone'
import countries from '@/util/countries'
import { trackGaPageView } from '@/util/helper'
import { isoToState } from '@/util/state_helper'

export default Vue.extend({
  props: ['userId'],
  components: {
    Modal,
  },
  data() {
    return {
      accepting: false,
      tandc: false,
      modalShow: false,
      countries: countries,
      address: '',
      addressLine: '',
      company: {} as Company,
      userInfo: {} as User,
      authUser: {} as any,
    }
  },
  async created() {
    this.$store.dispatch('init')
    this.authUser = await this.$store.dispatch('auth/get')
    this.userInfo = await User.getById(this.authUser.userId)
    this.company = await Company.getById(this.userInfo.companyId)
    this.isTermOfServiceAccepted()
  },
  computed: {
    disabled() {
      return !this.tandc
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    twStates() {
      return Object.entries(isoToState).map((state: any) => {
        return {
          value: state[0],
          label: state[1]
        }
      })
    },
    twCountries() {
      return Object.entries(countries).map((country: any) => {
        return {
          value: country[0],
          label: country[1]
        }
      })
    },
  },
  methods: {
    async isTermOfServiceAccepted() {
      if (this.user.id && !this.company.onboardingInfo?.pending) {
        if (
          this.user.type == 'agency' &&
          this.user.role == 'admin' &&
          !this.company.termsOfService
        ) {
          if (!this.company.termsOfService) {
            try {
              const termsOfServicePopup = await this.$http.get(
                `/terms_of_service_popup`
              )
              if (termsOfServicePopup.data.terms_of_service_popup)
                this.modalShow = true
            } catch (error) {
              console.log(error)
            }
          }
        }
      }
    },
    async validateBeforeSubmit() {
      const result = await this.$validator.validateAll('form-tandc')

      if (!result) {
        console.error('Correct them errors!', this.$validator.errors)
        return false
      }
      this.accepting = true
      this.company.termsOfServiceAcceptedBy = this.userId
      this.company.termsOfServiceAcceptedDate = moment().utc()
      this.company.termsOfService = this.tandc
      this.company.address = this.address
      if (this.addressLine) this.company.address += `, ${this.addressLine}`
      await this.company.save()
      let termsOfserviceAcceptedDate = moment()
        .utc()
        .format('MMM. D, YYYY [at] h:mm A z')
      if (this.company.timezone) {
        termsOfserviceAcceptedDate = moment()
          .tz(this.company.timezone)
          .format('MMM. D, YYYY [at] h:mm A z')
      }

      await Promise.all([
        this.$http.put('/onboarding/customer_update', {
          metadata: {
            reseller: this.company.isReselling ? 'Yes' : 'No',
            tos_accepted_by: this.user?.fullName,
            tos_accepted_date: termsOfserviceAcceptedDate,
          },
        }),
        this.$http.post('/onboarding', {
          is_reselling: this.company.isReselling ? 'Yes' : 'No',
        }),
      ])
      this.accepting = false
      this.modalShow = false
      trackGaPageView('onb-biz-address')
      this.$root.$emit('billing_address_update')
    },
    clearState() {
      this.company.state = ''
    },
  },
})
</script>
<style scoped>
.terms-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.highlevel-logo {
  width: 155px;
  height: 35px;
  margin-top: 0px;
}
.popup-title {
  font-weight: bold;
  font-size: 14px;
  margin-top: 10px;
  color: #000;
}
.popup-subtitle {
  font-weight: normal;
  font-size: 12px;
  color: rgb(168, 164, 164);
}
.form-container {
  display: flex;
  flex-direction: row;
  margin-top: 100px;
}
.form-element {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.checkbox-label {
  margin-left: 10px;
  margin-top: 10px;
  font-size: 12px;
}
.button-grp {
  margin-bottom: 20px;
  margin-top: 10px;
}
.button-grp button {
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
}
.button-grp button i {
  margin-left: 11px;
  margin-right: 10px;
}
.form-body {
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: start;
  width: 80%;
}
.form-body .form-group {
  width: 100%;
  margin-top: 10px;
}
.select-style {
  -webkit-appearance: auto !important;
  -moz-appearance: auto !important;
  appearance: auto !important;
  height: 50px !important;
}
.disalbed_btn {
  cursor: not-allowed;
}
#termsOfService .card {
  margin-bottom: 0px !important;
  font-size: 12px !important;
}
#termsOfService .form-control {
  /* padding: 10px 20px !important; */
  line-height: 1;
}
#termsOfService .form-group {
  margin-bottom: 0.75rem;
}
#termsOfService .popup-notice {
  margin-top: 10px;
  font-size: 17px;
  font-weight: bold;
  color: #ffbc00;
}
</style>