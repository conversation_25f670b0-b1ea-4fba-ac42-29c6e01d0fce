<template>
  <div id="review_request_behaviour">
    <div>
      <div class="card">
        <div class="card-header">
          <h3>Review Request Behavior</h3>
        </div>
        <div class="card-body">
          <h6>When review request button is clicked</h6>
          <label
            >Decide what happens when you click the review request or check-in
            client button</label
          >

          <div class="custom-control custom-radio">
            <input
              type="radio"
              value="immediate"
              id="immidiately_once_radio"
              name="review_request_behaviour"
              class="custom-control-input"
              v-model="reviewSettings.review_request_behaviour" 
              v-on:change="saveReviewSettings"
            />
            <label class="custom-control-label" for="immidiately_once_radio"  id="immidiately_once_radio_label"
              >Send review request immediately once</label
            >
          </div>
          <div class="custom-control custom-radio">
            <input
              type="radio"
              id="custom_schedule_radio"
              value="custom_schedule"
              name="review_request_behaviour"
              class="custom-control-input"
              v-model="reviewSettings.review_request_behaviour"
              v-on:change="saveReviewSettings"
            />
            <label class="custom-control-label" for="custom_schedule_radio" id="custom_schedule_radio_label"
              >Follow a custom schedule for review requests</label
            >
          </div>
          <!--           <div class="custom-control custom-radio">
            <input
              type="radio"
              id="customRadio2"
              name="customRadio"
              class="custom-control-input"
            />
            <label class="custom-control-label" for="customRadio2"
              >Add to a campaign I have built</label
            >
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
// @ts-nocheck
import Vue from 'vue'
import EmulatorComponent from '../EmulatorComponent.vue'

import vue2Dropzone from 'vue2-dropzone'
import 'vue2-dropzone/dist/vue2Dropzone.min.css'
import { Location } from '@/models'
import { v4 as uuid } from 'uuid'
import firebase from 'firebase/app'

export default Vue.extend({
  props:{
    location:Object as Location
  },
  data() {
    return {
      local_after_check_in_time: {
        type: 'generic',
        unit: 'hours',
        value: 0,
      },
      reviewSettings: {
        review_request_behaviour: 'immediate',
      },
      smsSettings: {
        message: '',
        after_check_in_hours: '0',
        image_enabled: false,
      } as { [key: string]: any },
    }
  },
  components: {},
  computed: {},
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.fetchData(id)
    },
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchData()
  },
  methods: {
    async saveReviewSettings() {
      this.location.settings.review = this.reviewSettings
      let updateObj = {
          'settings.review': this.location.settings.review,
          date_updated: firebase.firestore.FieldValue.serverTimestamp(),
      }
      if (this.reviewSettings.review_request_behaviour == 'immediate') {
        try {
          this.location.settings.email.after_check_in_hours = 0
          this.location.settings.email.repeat_review_request_in = -1
          updateObj[`settings.email.after_check_in_hours`] = 0;
          updateObj[`settings.email.repeat_review_request_in`] = -1;
        } catch (e) {}
        try {
          this.location.settings.sms.after_check_in_hours = 0
          this.location.settings.sms.repeat_review_request_in = -1
          updateObj[`settings.sms.after_check_in_hours`] = 0;
          updateObj[`settings.sms.repeat_review_request_in`] = -1;
        } catch (e) {}
      }
      await this.location.ref.update(updateObj)
      this.$emit('locationUpdated', this.reviewSettings.review_request_behaviour );
    },
    async fetchData() {
      this.reviewSettings = {
        ...this.reviewSettings,
        ...this.location.settings.review,
      }
    },

    async validateBeforeSubmit() {},
  },
})
</script>
<style lang="scss">
#review_request_behaviour {
  .card {
    .card-body {
      max-width: unset;
      padding: 20px 30px;
      h6 {
        font-size: 0.9rem;
      }
      .custom-control-label {
        font-size: 1rem;
      }
    }
  }
}
</style>>
