<template>
  <div>
    <div class="saas-seperator"></div>
    <div class="row">
      <div class="col-lg-6">
        <manage-saas-mode
          :current-saas-mode="currentSaasMode"
          :updating="updatingSaasMode"
          @input="toggleSaasMode"
        />
        <!-- <SaasSubscriptionPlanV2
          v-if="
            currentSaasMode &&
            location &&
            location.settings.saas_settings &&
            location.settings.saas_settings.saas_plan_id
          "
          :locationId="locationId"
          :saasSettings="location.settings.saas_settings"
        /> -->
        <SaasSubscriptionPlanV2
          v-if="saasModeActivated"
          :locationId="locationId"
          :saasSettings="location.settings.saas_settings"
          :subscriptionData="subscriptionData"
          @refresh="fetchStripeSubscriptionDetails"
          @attachPlan="showManageLocationSubscriptionModal = true"
        />
        <ManageLocationCredits
          v-if="saasModeActivated"
          :locationId="locationId"
          :saasSettings="location.settings.saas_settings"
          :subscriptionData="subscriptionData"
          @attachPlan="showManageLocationSubscriptionModal = true"
        />
        <location-twilio-rebilling
          v-if="saasModeActivated || saasModePending"
          :can-update="saasModeActivated"
          :settings="twilioRebillingSettings"
          :updating="twilioRebilling.updating"
          :error="twilioRebilling.error"
          :existing-twilio-account="twilioRebilling.existingTwilioAccount"
          @update="updateTwilioRebillingSettings"
          @replaceExistingAccount="resolveForExistingTwilioAccount"
        />
        <email-rebilling
          v-if="saasModeActivated || saasModePending"
          :can-update="saasModeActivated"
          :settings="mailgunRebillingSettings"
          :updating="mailgunRebilling.updating"
          :error="mailgunRebilling.error"
          :locationId="locationId"
          :location="location"
          :company="company"
          @update="updateMailgunRebillingSettings"
          @reset="reset"
        />
      </div>
      <div class="col-lg-6">
        <SaasEnableProducts
          v-if="
            saasModeActivated && location && location.settings.saas_settings
          "
          :saasPlanId="location.settings.saas_settings.saas_plan_id"
          :locationId="locationId"
          @attachPlan="showManageLocationSubscriptionModal = true"
        />
      </div>
    </div>
    <request-card-modal
      v-if="showRequestCardModal"
      :show="showRequestCardModal"
      @close="showRequestCardModal = false"
      :location-id="locationId"
      @success="e => handleRequestCardSuccess(e)"
      @createUser="showCreateUserModal"
    />
    <request-card-success-modal
      v-if="showRequestCardSuccessModal"
      :show="showRequestCardSuccessModal"
      :message="requestCardSuccessMessage"
      @close="showRequestCardSuccessModal = false"
    />
    <EditTeamMemberModal
      :values="teamMemberModalValues"
      :defaults="{
        role: 'admin',
        type: 'account',
        locationId: locationId,
      }"
      @hidden="teamMemberModalValues = { visible: false }"
      @created="newUserCreated"
    />
    <ManageLocationSubscription
      :locationId="locationId"
      v-if="showManageLocationSubscriptionModal"
      :show="showManageLocationSubscriptionModal"
      @close="handleSuccessAttachPlan"
      @success="handleSuccessAttachPlan"
      @skip="handleSuccessAttachPlan"
      :skippable="$route.query.saas_setup === 'true'"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import RequestCardModal from '@/pmd/components/saas/agency/RequestCardModal.vue'
import RequestCardSuccessModal from '@/pmd/components/saas/agency/RequestCardSuccessModal.vue'
const EditTeamMemberModal = () =>
  import(
    /* webpackChunkName: "team-edit" */ '@/pmd/components/EditTeamMemberModal.vue'
  )

const ManageSaasMode = () =>
  import('@/pmd/components/saas/agency/ManageSaasMode.vue')
const LocationTwilioRebilling = () =>
  import('@/pmd/components/saas/agency/LocationTwilioRebilling.vue')

const EmailRebilling = () =>
  import('@/pmd/components/saas/agency/EmailRebilling.vue')

import SaasSubscriptionPlanV2 from '@/pmd/components/saas/saas_account_details/SaasAccountDetailsActiveSubscription.vue'
import SaasEnableProducts from '@/pmd/components/saas/location_billing/SaasEnableProducts.vue'
import ManageLocationSubscription from '@/pmd/components/saas/saas_account_details/ManageLocationSubscriptionModal.vue'
import fetchActiveSubscriptionMixin from '@/pmd/components/saas/saas_account_details/fetchActiveSubscriptionMixin.js'
import ManageLocationCredits from '@/pmd/components/saas/saas_account_details/ManageLocationCredits.vue'

import { ISaasSettings } from '@/models/location'
// import { TwilioAccount } from '@/models'

export default Vue.extend({
  props: ['locationId', 'location', 'company'],
  mixins: [fetchActiveSubscriptionMixin],
  components: {
    ManageSaasMode,
    LocationTwilioRebilling,
    SaasSubscriptionPlanV2,
    SaasEnableProducts,
    RequestCardModal,
    RequestCardSuccessModal,
    EditTeamMemberModal,
    EmailRebilling,
    ManageLocationSubscription,
    ManageLocationCredits,
  },
  data() {
    return {
      updatingSaasMode: false,
      twilioRebilling: {
        updating: false,
        error: '',
        existingTwilioAccount: null as Record<string, string> | null,
        newSettings: {},
      },
      mailgunRebilling: {
        updating: false,
        error: '',
        newSettings: {},
      },
      showRequestCardModal: false,
      showRequestCardSuccessModal: false,
      requestCardSuccessMessage: '',
      teamMemberModalValues: {},
      showManageLocationSubscriptionModal: false,
    }
  },
  created() {
    if (this.$route.query.saas_setup === 'true') {
      setTimeout(() => {
        this.showRequestCardModal = true
      }, 3000)
    }
  },
  computed: {
    saasPlanId(): string | undefined {
      return this.currentSaasMode ? this.location.settings.saas_settings.saas_plan_id : undefined
    },
    currentSaasMode(): ISaasSettings['saas_mode'] | null {
      if (this.location) {
        return this.location.settings.saas_settings.saas_mode
      } else {
        return null
      }
    },
    saasModeActivated(): boolean {
      if (this.currentSaasMode) {
        return this.currentSaasMode === 'activated'
      } else {
        return false
      }
    },
    saasModePending(): boolean {
      if (this.currentSaasMode) {
        return this.currentSaasMode === 'setup_pending'
      } else {
        return false
      }
    },
    twilioRebillingSettings(): ISaasSettings['twilio_rebilling'] | null {
      if (this.location) {
        const saasSettings = this.location.settings.saas_settings
        return saasSettings.twilio_rebilling
      } else {
        return null
      }
    },
    mailgunRebillingSettings(): ISaasSettings['mailgun_rebilling'] | null {
      if (this.location) {
        const saasSettings = this.location.settings.saas_settings
        return (
          saasSettings.mailgun_rebilling || {
            price: 0.0007,
            markup: 5,
            enabled: false,
          }
        )
      } else {
        return null
      }
    },
  },
  watch: {
    saasPlanId: {
      handler: function (saasPlanId) {
        this.fetchStripeSubscriptionDetails()
      },
      // immediate: true,
      // deep: true,
    },
  },
  methods: {
    async toggleSaasMode(activate: boolean) {
      console.log('set saas mode --> ', activate)
      if (!this.location) return

      this.updatingSaasMode = true

      if (
        this.saasModeActivated &&
        !confirm(
          'Are you sure, you want to disable saas mode for this location?'
        )
      ) {
        this.updatingSaasMode = false
        return
      }

      try {
        const stripeAccountId = this.company?.stripeConnectId

        if (!stripeAccountId) {
          alert('Please ensure Stripe Connect is enabled!')
          throw new Error('Stripe Connect not enabled!')
        }

        if (!this.saasModeActivated && !this.saasModePending) {
          this.showRequestCardModal = true
          // const payload = {
          //   stripeAccountId,
          //   name: this.location.name,
          //   email: this.location.prospectInfo.email,
          //   description: `Enable Saas mode for ${this.location.name}`,
          // }
          // const resp = await this.saasService.post(
          //   `/saas-config/${this.locationId}/enable-saas`,
          //   payload
          // )
          // console.log('got resp --> ', resp)
          // alert('Saas mode activated!')
        } else {
          const payload = {
            stripeAccountId,
            stripeCustomerId: this.location.settings.saas_settings
              .stripe_customer_id,
          }
          const resp = await this.saasService.post(
            `/saas-config/${this.locationId}/disable-saas`,
            payload
          )
          alert('Saas mode deactivated!')
        }
      } catch (error) {
        console.error('Error while updating saas mode --> ', error)
      } finally {
        setTimeout(() => {
          this.updatingSaasMode = false
        }, 500)
      }
    },
    async updateMailgunRebillingSettings(updatedSettings: any) {
      try {
        if (!this.location) return

        this.mailgunRebilling.updating = true

        await this.saasService.post(
          `/saas-config/${this.locationId}/update-mailgun-rebilling`,
          updatedSettings
        )
      } catch (error) {
        console.error(
          'Error while updating mailgun rebilling settings -->',
          error
        )
        this.mailgunRebilling.error =
          'Error while updating mailgun rebilling settings!'
      } finally {
        this.mailgunRebilling.updating = false
      }
    },
    async updateTwilioRebillingSettings(updatedSettings: any) {
      try {
        this.twilioRebilling.updating = true
        this.twilioRebilling.existingTwilioAccount = null
        this.twilioRebilling.newSettings = updatedSettings

        if (this.location) {
          const locationSettings = this.location.settings

          const rebillingSettings =
            locationSettings.saas_settings.twilio_rebilling
          if (!(rebillingSettings && rebillingSettings.enabled) && updatedSettings.enabled) {
            // enabling rebilling

            const { data } = await this.$http.get(
              `/twilio/check_subaccount_for_rebilling/${this.locationId}?company_id=${this.location.companyId}`
            )

            const {
              createNew,
              sameTwilioAccountAcrossLocations,
              twilioAccountUnderAgency,
              twilioAccount,
              canBeRebilled
            } = data

            if (createNew) {
              await this.setupNewTwilioAccount()
            } else if (!canBeRebilled) {
              this.twilioRebilling.existingTwilioAccount = {
                token: twilioAccount.token,
                accountSID: twilioAccount.accountSID,
                forceNew: sameTwilioAccountAcrossLocations
                  ? 'Create new Twilio account'
                  : '',
              }

              this.twilioRebilling.updating = false
              return
            }
          }

          await this.saasService.post(
            `/saas-config/${this.locationId}/update-twilio-rebilling`,
            updatedSettings
          )
          // locationSettings.saas_settings.twilio_rebilling = updatedSettings;
          // this.location.settings = locationSettings;
          // await this.save()
        }
      } catch (error) {
        console.error(
          'Error while updating twilio rebilling settings -->',
          error
        )
        this.twilioRebilling.error =
          'Error while updating twilio rebilling settings!'
      } finally {
        this.twilioRebilling.updating = false
        // this.twilioRebilling.existingTwilioAccount = null;
      }
    },
    async resolveForExistingTwilioAccount(createNewAccount: boolean) {
      try {
        this.twilioRebilling.updating = true
        if (createNewAccount) {
          // maybe delete the existing account --> this.twilioRebilling.existingTwilioAccount
          await this.$http.delete(
            `/twilio/accounts?location_id=${this.locationId}`
          )
          await this.setupNewTwilioAccount()
        }

        await this.saasService.post(
          `/saas-config/${this.locationId}/update-twilio-rebilling`,
          this.twilioRebilling.newSettings
        )
      } catch (error) {
        console.error(
          'Error while updating twilio rebilling settings -->',
          error
        )
        this.twilioRebilling.error =
          'Error while updating twilio rebilling settings!'
      } finally {
        this.twilioRebilling.updating = false
        this.twilioRebilling.existingTwilioAccount = null
      }
    },
    async setupNewTwilioAccount() {
      // setup twilio account
      try {
        await this.$http.post(`/twilio/set_up_sub_account/${this.locationId}?company_id=${this.company.id}`)
      } catch (error) {
        console.error(`Error while creating twilio subaccount --> `, error)
        alert('Failed to setup twilio subaccount!')
        throw error
      }
    },
    showCreateUserModal(locationId: string) {
      this.showRequestCardModal = false
      this.teamMemberModalValues = {
        visible: true,
        companyId: this.company.id,
        isEditing: false,
      }
    },
    newUserCreated(locationId) {
      this.teamMemberModalValues = { visible: false }
      setTimeout(() => {
        this.showRequestCardModal = true
      }, 500)
    },
    handleRequestCardSuccess(e: any) {
      const { msg, response } = e
      this.showRequestCardModal = false
      this.requestCardSuccessMessage = msg
      if (response.paymentMethodAdded) {
        this.showManageLocationSubscriptionModal = true
      } else {
        this.showRequestCardSuccessModal = true
      }
    },
    handleSuccessAttachPlan(e:any){
      // const { saasPlanId, stripePriceId } = e;
      this.showManageLocationSubscriptionModal = false
      if (this.$route.query.saas_setup === 'true') {
        this.showRequestCardSuccessModal = true
      }
    },
    reset() {
      this.mailgunRebilling.error = ''
    },
  },
})
</script>

<style lang="scss">
.saas-seperator {
  height: 1px;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  position: relative;
  margin-top: 24px;
  &:after {
    content: 'SaaS Mode Details';
    position: absolute;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #f2f7fa;
    padding: 4px 12px;
    color: rgba(0, 0, 0, 0.3);
  }
}
</style>
