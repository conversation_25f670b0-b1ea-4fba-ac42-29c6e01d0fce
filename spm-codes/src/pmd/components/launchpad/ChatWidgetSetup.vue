<template>
  <div class="container-fluid">
    <div class="webchat-center-containter">
      <div class="webchat-title text-center mb-3">Install Your Widget</div>
      <div class="mb-3">
        Do you have administrative rights to install webchat widget?
      </div>
      <div class="webchat-form-container">
        <div class="form-check webchat-form-group mt-2 mb-1">
          <input
            type="radio"
            class="form-check-input"
            v-model="hasAccess"
            value="yes"
            name="hasAccess"
            id="hasAccessYes"
          />
          <label class="form-check-label" for="hasAccessYes">
            Yes, I can install the plug-in.
          </label>
        </div>
        <div class="form-check webchat-form-group mb-2">
          <input
            type="radio"
            class="form-check-input"
            v-model="hasAccess"
            value="no"
            name="hasAccess"
            id="hasAccessNo"
          />
          <label class="form-check-label" for="hasAccessNo">
            No, I can't install the plug-in.
          </label>
        </div>
        <div
          class="webchat-cms-select text-center webchat-change-cms mb-2"
          @click="changeCms"
          v-if="cmsName.toLowerCase() == 'other'"
        >
          Let me choose my CMS
        </div>
        <div
          class="webchat-cms-select text-center webchat-change-cms mb-2"
          @click="changeCms"
          v-else
        >
          Not using {{ cmsName }}?
        </div>
        <button
          :class="{ disalbed_btn: disableAction }"
          type="button"
          class="launchpad-filled-button launchpad-blue-btn my-3"
          :disabled="disableAction"
          style="width: 95%"
          @click="showSteps"
        >
          <moon-loader
            v-if="sending"
            :loading="sending"
            color="#37ca37"
            size="21px"
          />
          <span v-if="hasAccess === 'no'">Send Email Instructions</span>
          <span v-else>Start Install</span>
        </button>
      </div>
      <div
        class="text-center webchat-back mt-2"
        style="cursor: pointer"
        @click="backToLaunchpad"
      >
        Back
      </div>
    </div>
    <ChatWidgetInstruction
      :cms="cmsName"
      :modalShow="showModal"
      :modalType="modalType"
      :websiteScreenshot="websiteScreenshot"
      :cmsInstruction="cmsData"
    />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import ChatWidgetInstruction from './ChatWidgetInstruction.vue'

export default Vue.extend({
  props: ['websiteScreenshot', 'cms'],
  components: {
    ChatWidgetInstruction,
  },
  data() {
    return {
      sending: false,
      hasAccess: undefined,
      showModal: false,
      modalType: 'steps',
      showInstructionModal: false,
      currentLocationId: '',
      cmsName: this.cms,
      cmsData: {} as any,
      cmsInstructions: {
        wordpress: {
          heading: 'Install with WordPress Plug-in',
          steps: [
            {
              text: 'Sign in to your WordPress administration account.',
              img: 'wordpress.png',
              logo: true,
            },
            {
              text: 'Go to <b>Plugins</b>.',
              img: 'WordPress0-min.png',
            },
            {
              text: 'Click <b>Add New</b>.',
              img: 'WordPress1-min.png',
            },
            {
              text: 'Search for Lead Connector and click <b>Install Now</b>.',
              img: 'WordPress2-min.png',
            },
            {
              text: 'Click <b>Activate</b>.',
              img: 'WordPress3-min.png',
            },
            {
              text: 'Copy your API key.',
              img: undefined,
              apiKey: true,
            },
            {
              text: 'Click <b>Lead Connector</b> in left side Navigation.',
              img: 'WordPress4-min.png',
            },
            {
              text: 'Copy and paste your api key.',
              img: 'WordPress5-min.png',
            },
            {
              text: 'Click on <b>Enable Chat-widget</b>',
              img: 'WordPress6-min.png',
            },
            {
              text: 'Click <b>Pull and Save</b>.',
              img: 'WordPress7-min.png',
            },
            {
              text: "Let's make sure widget is working on your website",
              screenshot: true,
            },
          ],
        },
        /* wix: {
          heading: 'Install Webchat on Wix',
          steps: [
            {
              text: 'Sign in to your wix administration account.',
              img: 'wix.svg',
            },
            {
              text: 'Go to <b>Settings.</b>',
              img: 'wix.svg',
            },
            {
              text: 'In Advanced Settings select <b>Custom Code.</b>',
              img: 'Wix0-min.png',
            },
            {
              text: 'Click <b>New Tool</b>',
              img: 'wix.svg',
            },
            {
              text: 'Copy your widget code.',
              img: undefined,
              widgetCode: true,
            },
            {
              text: 'Click <b>Custom.</b>',
              img: 'wix.svg',
            },
            {
              text: 'Copy your widget code.',
              img: 'wix.svg',
            },
            {
              text:
                'In the footer, paste the widget code above the <b><</b><b>/body></b> tag.',
              img: 'wix.svg',
            },
            {
              text: "Let's make sure widget is working on your website.",
              screenshot: true,
            },
          ],
        }, */
        squarespace: {
          heading: 'Install Webchat on Squarespace',
          steps: [
            {
              text: 'Sign in to your Squarespace administration account.',
              img: 'squarespace.png',
              logo: true,
            },
            {
              text: 'Go to <b>Settings.</b>.',
              img: 'Squarespace0-min.png',
            },
            {
              text: 'Click <b>Advanced.</b>.',
              img: 'Squarespace1-min.png',
            },
            {
              text: 'Copy your widget code.',
              img: undefined,
              widgetCode: true,
            },
            {
              text: 'Select <b>Code Injection.</b>',
              img: 'Squarespace2-min.png',
            },
            {
              text:
                'Copy and paste the widget code into the <b>HEADER HTML</b>.',
              img: 'Squarespace3-min.png',
            },
            {
              text: 'Click <b>Save.</b>',
              img: 'Squarespace4-min.png',
            },
            {
              text: "Let's make sure widget is working on your website",
              screenshot: true,
            },
          ],
        },
        joomla: {
          heading: 'Install Webchat on Joomla',
          steps: [
            {
              text: 'Sign in to your Joomla administration account.',
              img: 'joomla.png',
              logo: true,
            },
            {
              text: 'Go to <b>Extensions</b> and select <b>Module Manager.</>',
              img: 'Joomla0-min.png',
            },
            {
              text: 'Click <b>New.</b>',
              img: 'Joomla1-min.png',
            },
            {
              text: 'Click <b>Custom HTML.</b>',
              img: 'Joomla2-min.png',
            },
            {
              text: 'In the <b>Position</b> drop-down, select <b>Footer.</b>',
              img: 'Joomla3-min.png',
            },
            {
              text: 'Copy your widget code.',
              img: undefined,
              widgetCode: true,
            },
            {
              text:
                'In the Custom HTML editor, in Tools select <b>Source Code.</b>',
              img: 'Joomla4-min.png',
            },
            {
              text: 'Copy and paste the widget code, and click <b>Ok.</b>',
              img: 'Joomla5-min.png',
            },
            {
              text: 'Click Menu <b>Assignment.</b>',
              img: 'Joomla7-min.png',
            },
            {
              text:
                'Select <b>On all pages</b> from the Module Assignment drop-down.',
              img: 'Joomla8-min.png',
            },
            {
              text: 'Click <b>Save.</b>',
              img: 'Joomla9-min.png',
            },
            {
              text: "Let's make sure widget is working on your website",
              screenshot: true,
            },
          ],
        },
        /* dealer: {
          heading: 'Install Webchat on Dealer.com',
          steps: [
            {
              text:
                '<li>Navigate to Dealer.com\'s <a href="https://www.dealer.com/support/thirdparty/" target="_blank">third party support form</a>.</li>',
              img: 'dealer.png',
            },
            {
              text:
                'Provide your contact infromation and the contact information for your HighLevel point of contact.',
              img: 'dealer.png',
            },
            {
              text: 'Click <b>Next.</b>',
              img: 'dealer.png',
            },
            {
              text: 'Select <b>Implementation (Install).</b>',
              img: 'dealer.png',
            },
            {
              text: 'Copy your widget code.',
              img: undefined,
              widgetCode: true,
            },
            {
              text:
                'For the Subject/Project Name field, enter HighLevel Webchat.',
              img: 'dealer.png',
            },
            {
              text:
                'In the Message field, paste the widget code and type <i>"I would like to add this custom script to my website."</i>',
              img: 'dealer.png',
            },
            {
              text: 'Click <b>Submit</b>.',
              img: 'dealer.png',
            },
            {
              text: "Let's make sure widget is working on your website",
              screenshot: true,
            },
          ],
        }, */
        duda: {
          heading: 'Install Webchat on Duda',
          steps: [
            {
              text: 'Sign in your Duda administration account.',
              img: 'duda.png',
              logo: true,
            },
            {
              text: 'Click <b>Settings.</b>',
              img: 'Duda0-min.png',
            },
            {
              text: 'Click <b>HEAD HTML.</b>',
              img: 'Duda1-min.png',
            },
            {
              text: 'Copy your widget code.',
              img: undefined,
              widgetCode: true,
            },
            {
              text:
                'Copy and paste the widget code into the <b>Body End HTML</b>.',
              img: 'Duda2-min.png',
            },
            {
              text: 'Click <b>Save.</b>',
              img: 'Duda3-min.png',
            },
            {
              text: 'Click <b>Republish.</b>',
              img: 'Duda4-min.png',
            },
            {
              text: "Let's make sure widget is working on your website",
              screenshot: true,
            },
          ],
        },
        shopify: {
          heading: 'Install Webchat on Shopify',
          steps: [
            {
              text: 'Sign in to your Shopify administration account.',
              img: 'shopify.png',
              logo: true,
            },
            {
              text: 'Go to <b>Online Store</b> and select <b>Themes.</b>',
              img: 'Shopify0-min.png',
            },
            {
              text: 'Click <b>Actions</b> and select <b>Edit code.</b>',
              img: 'Shopify1-min.png',
            },
            {
              text: 'Copy your widget code.',
              img: undefined,
              widgetCode: true,
            },
            {
              text: 'Expand <b>Layout</b> and click theme.liquid.',
              img: 'Shopify2-min.png',
            },
            {
              text:
                'Copy and paste the widget code above the <b><</b><b>/body></b> tag.',
              img: 'Shopify3-min.png',
            },
            {
              text: 'Click <b>Save.</b>',
              img: 'Shopify4-min.png',
            },
            {
              text: "Let's make sure widget is working on your website.",
              screenshot: true,
            },
          ],
        },
        weebly: {
          heading: 'Install Webchat on Weebly',
          steps: [
            {
              text: 'Sign in to your Weebly administration account.',
              img: 'weebly-logo-2.png',
              logo: true,
            },
            {
              text: 'Go to <b>Theme.</b>',
              img: 'Weebly1-min.png',
            },
            {
              text: 'Click <b>Edit HTML/CSS.</b>',
              img: 'Weebly2-min.png',
            },
            {
              text: 'Copy your widget code.',
              img: undefined,
              widgetCode: true,
            },
            {
              text: 'Click <b>header.html.</b>',
              img: 'Weebly3-min.png',
            },
            {
              text:
                'In the header, paste the widget code above the <b><</b><b>/body></b> tag.',
              img: 'Weebly4-min.png',
            },
            {
              text: 'Click <b>Save.</b>',
              img: 'Weebly5-min.png',
            },
            {
              text: 'Click <b>Publish</b>',
              img: 'Weebly6-min.png',
            },
            {
              text: "Let's make sure widget is working on your website.",
              screenshot: true,
            },
          ],
        },
        /* godaddy: {
          heading: 'Install Webchat on GoDaddy',
          steps: [
            {
              text: 'Sign in to your GoDaddy administration account.',
              img: 'godaddy.png',
            },
            {
              text: 'Select your website.',
              img: 'GoDaddy1-min.png',
            },
            {
              text: 'Click <b>Edit Site.</b>',
              img: 'GoDaddy2-min.png',
            },
            {
              text: 'Click <b>Pages & Sections.</b>',
              img: 'GoDaddy3-min.png',
            },
            {
              text: 'Click <b>Add Section.</b>',
              img: 'GoDaddy4-min.png',
            },
            {
              text: 'Copy your widget code.',
              img: undefined,
              widgetCode: true,
            },
            {
              text: 'Select <b>HTML</b> and click <b>Add.</b>',
              img: 'GoDaddy5-min.png',
            },
            {
              text: 'In custom code, paste the widget code.',
              img: 'GoDaddy6-min.png',
            },
            {
              text: 'Set forced height to 600 pixels.',
              img: 'GoDaddy7-min.png',
            },
            {
              text: 'Click <b>Done.</b>',
              img: 'GoDaddy8-min.png',
            },
            {
              text: 'Click <b>Publish.</b>',
              img: 'GoDaddy9-min.png',
            },
            {
              text: "Let's make sure widget is working on your website.",
              screenshot: true,
            },
          ],
        }, */
        other: {
          heading: 'Install Webchat on your Website',
          steps: [
            {
              text: 'Sign in to your (CMS) account.',
              img: 'Other1-min.png',
            },
            {
              text: 'Copy your widget code.',
              img: undefined,
              widgetCode: true,
            },
            {
              text:
                'Copy and paste the widget code snippet right before the <b><</b><b>/body></b> tag on each page you want the widget to appear.',
              img: 'Other2-min.png',
            },
            {
              text: 'Save the changes.',
              img: 'Other2-min.png',
            },
            {
              text: "Let's make sure widget is working on your website.",
              screenshot: true,
            },
          ],
        },
      },
    }
  },
  computed: {
    disableAction(): boolean {
      return this.hasAccess === undefined
    },
  },
  mounted() {
    this.$root.$on('set-cms', (cms: any) => {
      this.cmsName = cms
      this.cmsData = this.cmsInstructions[cms.toLowerCase()]
    })
    this.cmsData = this.cmsInstructions[this.cms.toLowerCase()]
    this.$root.$on('close-modal', () => {
      this.modalType = 'steps'
      this.showModal = false
    })
    this.$root.$on('modal-type', (type: string) => {
      this.showModal = true
      this.modalType = type
    })
    if (this.cmsName.toLowerCase() == 'other') {
      this.changeCms()
    }
  },
  watch: {
    cms: async function () {
      this.cmsName = this.cms
      this.cmsData = this.cmsInstructions[this.cms.toLowerCase()]
    },
  },
  methods: {
    backToLaunchpad() {
      this.$root.$emit('hide-widget-setup')
    },
    showSteps() {
      this.showModal = true
      if (this.hasAccess == 'yes') {
        this.modalType = 'steps'
      } else {
        this.modalType = 'send-instructions'
      }
    },
    changeCms() {
      this.showModal = true
      this.modalType = 'cms-select'
    },
  },
})
</script>
<style scoped>
.webchat-title {
  color: rgba(31, 41, 55, 1);
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: bold;
}
.webchat-subtitle {
  color: rgba(31, 41, 55, 1);
  font-size: 1.5rem;
  line-height: 2rem;
}
.webchat-center-containter {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.webchat-form-container {
  width: 300px;
  background: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.launchpad-filled-button {
  margin-left: 0.75rem;
  display: inline-flex;
  justify-content: center;
  padding: 0.65rem 1.5rem;
  border-color: transparent;
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  font-size: 0.75rem;
  line-height: 1.25rem;
  font-weight: 500;
  font-family: 'Roboto';
  border-radius: 3px;
  cursor: pointer;
  white-space: nowrap;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-left: auto;
  margin-right: auto;
}
.launchpad-filled-button:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.launchpad-blue-btn {
  color: #fff;
  background-color: #4263eb;
}
.launchpad-blue-btn:disabled {
  opacity: 0.5;
}
.launchpad-blue-btn:hover {
  background-color: #4263eb;
}
.disalbed_btn {
  cursor: not-allowed;
}
.webchat-form-group {
  width: 95%;
  border: 1px solid rgba(203, 213, 224, 1);
  height: 50px;
  display: flex;
  border-radius: 3px;
  flex-direction: column;
  justify-content: center;
  padding-left: 30px;
  cursor: pointer;
}
.webchat-change-cms {
  color: #4263eb;
  font-size: 0.75rem;
  font-weight: bold;
  cursor: pointer;
  opacity: 0.8;
}
</style>
