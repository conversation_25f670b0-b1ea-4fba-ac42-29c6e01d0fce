<template>
  <div class="tshirt-form">
    <div class="tshirt-form__info">
      Thank you for becoming a HighLevel Pro Agency. We’d love to send you a special Shirt! Please fill out the details below so we can ship it out ASAP!
    </div>

    <div class="tshirt-form__shipping-address">
      <div class="tshirt-form__heading">Shipping Address</div>
      <div class="row">
        <div class="tshirt-form__label col-sm-2">Full Name*</div>
        <div class="form-group col-sm-4">
          <input
              type="text"
              class="form-control"
              placeholder=""
              v-validate="'required'"
              name="fullname"
              v-model="fullname"
          >
          <span v-show="errors.has('fullname')" class="error">required</span>
        </div>
        <div class="tshirt-form__label col-sm-2">Email ID*</div>
        <div class="form-group col-sm-4">
          <input
              type="text"
              class="form-control"
              v-validate="'required'"
              name="email"
              v-model="email"
          >
          <span v-show="errors.has('email')" class="error">Email-id required</span>
        </div>
      </div>
      <div class="row">
        <div class="tshirt-form__label col-sm-2">Address 1*</div>
        <div class="form-group col-sm-10">
          <input
              type="text"
              class="form-control"
              placeholder="Line 1"
              v-validate="'required'"
              name="line1"
              v-model="line1"
          >
          <span v-show="errors.has('line1')" class="error">Address required</span>
        </div>
      </div>
      <div class="row">
        <div class="tshirt-form__label col-sm-2">Address 2</div>
        <div class="form-group col-sm-10">
          <input
              type="text"
              class="form-control"
              placeholder="Line 2"
              name="line2"
              v-model="line2"
          >
        </div>
      </div>
      <div class="row">
        <div class="tshirt-form__label col-sm-2">Country*</div>
        <div class="form-group col-sm-4">
          <input
              type="text"
              class="form-control"
              placeholder="Country"
              v-validate="'required'"
              name="country"
              v-model="country"
          >
          <span v-show="errors.has('country')" class="error">Country required</span>
        </div>
        <div class="tshirt-form__label col-sm-2">State*</div>
        <div class="form-group col-sm-4">
          <input
              type="text"
              class="form-control"
              placeholder="State"
              v-validate="'required'"
              name="state"
              v-model="state"
          >
          <span v-show="errors.has('state')" class="error">State required</span>
        </div>
      </div>
      <div class="row">
        <div class="tshirt-form__label col-sm-2">City*</div>
        <div class="form-group col-sm-4">
          <input
              type="text"
              class="form-control"
              placeholder="City"
              v-validate="'required'"
              name="city"
              v-model="city"
          >
          <span v-show="errors.has('city')" class="error">City required</span>
        </div>
        <div class="tshirt-form__label col-sm-2">Postal Code</div>
        <div class="form-group col-sm-4">
          <input
              type="text"
              class="form-control"
              placeholder="Potal Code"
              v-validate="'required'"
              name="postal"
              v-model="postal"
          >
          <span v-show="errors.has('postal')" class="error">Postal Code is required</span>
        </div>
      </div>
      <div class="row">
        <div class="tshirt-form__label col-sm-2">Phone*</div>
        <div class="form-group col-sm-10">
          <PhoneNumber
              class="form-control"
              placeholder="Phone"
              v-validate="'required|phone'"
              name="phone"
              v-model="phone"
          />
          <span v-show="errors.has('phone')" class="error">Invalid Phone Number</span>
        </div>
      </div>
    </div>

    <div class="tshirt-form__tshirt-info">
      <div class="tshirt-form__heading">T-Shirt Size</div>
      <div class="row">
        <div class="tshirt-form__label col-sm-2">Size</div>
        <div class="form-group col-sm-4">
          <vSelect
            :options="['S','M','L','XL','XXL','XXXL']"
            v-model="size"
            placeholder="Size"
            :clearable="false"
            v-validate="'required'"
            name="size"
          ></vSelect>
        </div>
        <div class="tshirt-form__label col-sm-2">Gender</div>
        <div class="form-group col-sm-4">
          <vSelect
            :options="['Male','Female']"
            v-model="gender"
            placeholder="Gender"
            :clearable="false"
            v-validate="'required'"
            name="gender"
          ></vSelect>
        </div>
      </div>
    </div>
    <div class="tshirt-form__info">
      Join our exclusive <a href="https://www.facebook.com/groups/403441270683211/" target="_blank" style="font-weight: 500">Facebook Pro Group</a>, to stay updated.
    </div>
    <div class="tshirt-form__actions">
      <div class="tshirt-form__skip-btn btn btn-light2" @click="$emit('submit')">Skip for now</div>
      <div class="tshirt-form__action-btn btn btn-success" @click="submitForm">
        <moon-loader v-if="loading" color="#ffffff" size="20px"/>
        <span v-else>Submit</span>
      </div>
    </div>
  </div>
</template>

<script>
const PhoneNumber = () => import('@/pmd/components/util/PhoneNumber.vue');
import vSelect from 'vue-select'
import moment from 'moment-timezone'
import defaults from '@/config'
import axios from 'axios'
import { trackGaEvent } from "@/util/helper";

export default {
  props:['company'],
  data(){
    return{
      fullname: '',
      email: '',
      line1: '',
      line2: '',
      country: '',
      state: '',
      city: '',
      postal: '',
      phone: '',
      size: 'XL',
      gender: 'male',
      loading: false,
    }
  },
  components:{
    PhoneNumber, vSelect
  },
  mounted(){
    const user = this.$store.state.user.user;
    if(user) {
      this.fullname = user.first_name + ' ' + user.last_name;
      this.phone = user.phone || '';
      this.email = user.email || '';
    }
  },
  methods:{
    handleSkip(){
      trackGaEvent('LevelUp Upgrade', this.company.id, `SKIPPED: Tshirt-form`, 1);
    },
    async submitForm(){
      const result = await this.$validator.validateAll();
      if (!result || this.loading) {
        return;
      }
      this.loading = true;
      let payload = {
        full_name: this.fullname,
        email: this.email,
        address1: this.line1,
        address2: this.line2,
        country: this.country,
        state: this.state,
        city: this.city,
        postal_code: this.postal,
        phone: this.phone,
        tshirt_size: this.size,
        gender: this.gender,
        company_id: this.company.id,
        company_name: this.company.name,
        timestamp: moment().toDate()
      }
      console.log(payload);
      try{
        let dbData = await this.$http.post(
          `/support/billing/record_497`,payload
        )
        console.log(dbData);
      } catch (err){
        console.log(err);
      }
      trackGaEvent('LevelUp Upgrade', this.company.id, `SUBMITTED: Tshirt-form`, 1);
      this.loading= false;
      this.$emit('submit');
    }
  }
}
</script>

<style>
.tshirt-form{
  padding: 24px 36px;
  /* overflow-y: auto;
  height: 460px; */
}
.tshirt-form__info{
  margin-bottom: 20px;
  font-size: 14px;
  opacity: 0.9;
  line-height: 20px;

  padding: 8px 16px;
  border-left: 4px solid #2196F3;
  background-color: #eef6fe;
}
.tshirt-form__heading{
  font-weight: bold;
  font-size: 17px;
  line-height: 20px;
  margin-bottom: 12px;
}
.tshirt-form__label{
  padding-left: 20px;
  display: flex;
  line-height: 50px;
  white-space: nowrap;
}
.tshirt-form__actions{
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 30px;
}
.tshirt-form__action-btn{
  /* display: block !important; */
  width: 140px;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  font-weight: 500;
  padding: 12px;
  /* margin: 50px auto 0px; */

  background-color: #27AE60;
  color: #ffffff;
  transition: all 0.3s ease-in-out;
}
.tshirt-form__action-btn:hover{
  background-color: #249151;
}

.tshirt-form__skip-btn{
  margin-right: 20px;
}
.tshirt-form .vs__selected{
  padding-left: 12px;
}
.tshirt-form .vs__actions{
  padding-right: 12px;
}
</style>
