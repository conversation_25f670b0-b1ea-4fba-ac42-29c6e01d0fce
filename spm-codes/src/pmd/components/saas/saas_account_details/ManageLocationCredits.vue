<template>
  <div>
    <div class="card">
      <div class="card-header">
        <h3>Manage Credits</h3>
      </div>
      <div class="card-body">
        <div class="manage-credits-body">
          <div class="__current-balance">
            <div>
              <span style="margin-right: 8px; font-weight: 500;">Current Balance: &nbsp;</span>
              <span
                class="list-inline-item saas-wallet__balance"
                :class="
                  parseInt(creditsData.currentBalance) < 0
                    ? '--negative'
                    : '--positive'
                "
                v-if="creditsData.currentBalance"
              >
                <i class="fas fa-wallet wallet-icon"></i>
                ${{ creditsData.currentBalance }}
                <span
                  class="hl_tooltip"
                  v-b-tooltip.hover
                  title="Remaining wallet balance of this SaaS Location"
                  ><i class="fas fa-info-circle"></i
                ></span>
              </span>
            </div>
            <div class="manage-credits__save-row">
              <div class="btn btn-primary" @click="openDebitPopup()">Debit</div>
              <div class="btn btn-success" @click="openCreditPopup()">Add Credits</div>
            </div>
          </div>
          <div class="__monthly-credits" v-if="subscriptionData && subscriptionData.planDetails && subscriptionData.planDetails.amount">
            <div class="__left-title">Deposit Complimentary credits of
              <div class="__left-title-value">&nbsp; $
                <input
                  type="number"
                  class="__left-input"
                  v-model="monthlyCredits"
                  :class="{'--error': invalidMonthlyCredits}"
                />
                per month
                <div class="tooltip__invalid-value" v-if="invalidMonthlyCredits" style="left: 64px;">
                  <span class="value" > Must be in range of 0-{{parseInt( this.subscriptionData.planDetails.amount / 2 )}} USD</span>
                </div>
              </div>
            </div>
            <!-- <br /> -->
            <div style="margin-top: 8px">
              <div class="__left-heading">Last Added on:</div>
              <span>{{creditsDate.last}}</span>
            </div>
            <div>
              <div class="__left-heading">Next Date:</div>
              <span>{{creditsDate.next}}</span>
            </div>
            <div class="manage-credits__save-row">
              <!-- <div class="btn btn-primary">Save</div> -->
              <i class="reminder" v-if="unsavedChanges">Unsaved Changes</i>
              <button
                class="btn"
                :class="unsavedChanges ? 'btn-primary' : '--grey'"
                :disabled="!unsavedChanges || updating"
                @click="updateMonthlyCredits"
              >
                {{ updating ? 'Saving..' : 'Save' }}
              </button>
            </div>
          </div>
          <div class="__no-monthly-credits" v-else>
            To manage monthly credits, <span class="--link" @click="$emit('attachPlan')"> Add a Subscription</span>
          </div>
        </div>
      </div>
    </div>
    <add-balance
      :balance-type="transactionType"
      :show-modal="creditsData.addBalance.showModal"
      :stripe-customer-id="saasSettings.stripe_customer_id"
      :stripeAccountId="this.company.stripe_connect_id"
      :location-id="locationId"
      :current-balance="creditsData.currentBalance"
      :currency-symbol="currencySymbol"
      :recharge="false"
      :active-card="{}"
      @success="getLocationWalletBalance"
      @ok="creditsData.addBalance.showModal = false"
      @cancel="creditsData.addBalance.showModal = false"
    />
  </div>
</template>

<script>
import AddBalance from '@/pmd/components/saas/location_billing/AddBalance.vue'
import * as moment from 'moment-timezone'

export default {
  props: ['saasSettings', 'locationId', 'subscriptionData'],
  components: {
    AddBalance,
  },
  data() {
    return {
      // walletBalance: '59',
      localMonthlyCredits: null,
      currencySymbol: '$',
      updating: false,
      invalidMonthlyCredits: false,
      monthlyCreditsMin: 0,
      // monthlyCreditsMax: 100,
      creditsData: {
        currentBalance: '0',
        rechargeThresholdBalance: 0,
        autoRechargeAmount: 0,
        loading: false,
        addBalance: {
          showModal: false,
        },
      },
      transactionType: 'CREDIT'
    }
  },
  created() {
    this.getLocationWalletBalance()
  },
  computed: {
    company() {
      return this.$store.state.company.company
    },
    monthlyConfiguredCredits(){
      if (this.saasSettings && this.saasSettings.complementary_credits && (this.saasSettings.complementary_credits.amount || this.saasSettings.complementary_credits.amount=== 0)) {
        return this.saasSettings.complementary_credits.amount
      } else if (this.subscriptionData && this.subscriptionData.planDetails && this.subscriptionData.planDetails.saasProduct && this.subscriptionData.planDetails.saasProduct.complementaryCredits) {
        return this.subscriptionData.planDetails.saasProduct.complementaryCredits.amount
      } else return 0
    },
    monthlyCredits: {
      get() {
        if (this.localMonthlyCredits !== null) {
          return this.localMonthlyCredits
        } else return this.monthlyConfiguredCredits;
      },
      set(value) {
        // console.log(value)
        this.localMonthlyCredits = parseInt(value)
        let newValue = parseInt(value)
        let maxLimit = parseInt(this.subscriptionData && this.subscriptionData.planDetails ? this.subscriptionData.planDetails.amount / 2 : 500);
        let delayDuration = 0
        if (value < 0) {
          newValue = 0
          delayDuration = 2100
          this.invalidMonthlyCredits = true
        } else if ( value > maxLimit) {
          newValue = maxLimit
          delayDuration = 2000
          this.invalidMonthlyCredits = true
        }
        setTimeout(() => {
          this.localMonthlyCredits = newValue
          this.invalidMonthlyCredits = false
        }, delayDuration)
      },
    },
    creditsDate() {
      let result = {
        last: '------',
        next: '------',
      }
      if (this.subscriptionData && this.subscriptionData.planDetails && this.subscriptionData.planDetails.subscription && this.subscriptionData.planDetails.subscription.current_period_start) {
        let currentPeriodStart = moment.unix(this.subscriptionData.planDetails.subscription.current_period_start)
        // console.log(currentPeriodStart)
        let diffMonths = moment().diff(currentPeriodStart, 'months')
        if (diffMonths > 0) {
          // on annual plan
          currentPeriodStart = currentPeriodStart.add(diffMonths, 'M')
        }
        result = {
          last: currentPeriodStart.format('MMM DD, YYYY'),
          next: currentPeriodStart.add(1, 'M').format('MMM DD, YYYY'),
        }
      }
      return result
    },
    unsavedChanges() {
      if (this.monthlyCredits !== this.monthlyConfiguredCredits) {
        return true
      } else return false
    },
  },
  methods: {
    openDebitPopup() {
      this.transactionType = 'DEBIT'
      this.creditsData.addBalance.showModal = true
    },
    openCreditPopup() {
      this.transactionType = 'CREDIT'
      this.creditsData.addBalance.showModal = true
    },
    async getLocationWalletBalance() {
      try {
        this.creditsData.loading = true

        const {
          data: {
            currentBalance,
            autoRechargeAmount,
            rechargeThresholdBalance,
          },
        } = await this.saasService.get(
          `/location-wallet/${this.locationId}`
        )
        this.creditsData.currentBalance = currentBalance
        this.creditsData.autoRechargeAmount = autoRechargeAmount
        this.creditsData.rechargeThresholdBalance = rechargeThresholdBalance
      } catch (error) {
        console.error('Error while fetching wallet data --> ', error)
      } finally {
        this.creditsData.loading = false
      }
    },
    async updateMonthlyCredits() {
      this.updating = true
      try {
        const {data} = await this.saasService.put(
          `/saas-config/${this.locationId}/complimentary-credits`,
          {
            type: 'monthly',
            amount: this.monthlyCredits,
            companyId: this.company.id,
          }
        )
        // this.$emit('refresh')
      } catch (err) {
        //
      } finally {
        this.updating = false
      }
    },
  },
}
</script>

<style lang="scss">
.card-header {
  display: flex;
  justify-items: space-between;
  padding: 25px 30px;
}

.manage-credits-body {
  // display: flex;
  // justify-content: space-between;
  .__monthly-credits {
    .__left-title {
      display: flex;
      align-items: center;
      // position: relative;
    }
    .__left-title-value {
      display: flex;
      align-items: center;
      position: relative;
      margin-left: 4px;
      .tooltip__invalid-value {
        position: absolute;
        left: 40px;
        top: -30px;
        // width: calc(100% - 24px);
        height: 20px;
        z-index: 5;
      }
      span.value {
        position: absolute;
        text-align: center;
        background: #2d3748;
        border-radius: 5px;
        color: white;
        padding: 4px 6px;
        border-radius: 2px;
        font-size: 12px;
        line-height: 16px;
        white-space: nowrap;
        transition: opacity 0.25s ease-in;
        transform: translateX(-50%);
        opacity: 0.8;
        &:before {
          content: '';
          width: 0px;
          height: 0px;
          position: absolute;
          border-left: 5px solid #2d3748;
          border-right: 5px solid transparent;
          border-top: 5px solid #2d3748;
          border-bottom: 5px solid transparent;
          left: calc(50% - 5px);
          top: 18px;
          transform: rotate(225deg);
        }
      }
    }
    .__left-input {
      width: 64px;
      background: rgba(255, 255, 255, 0.85);
      border: 1px solid #d1d5db;
      box-sizing: border-box;
      border-radius: 3px;
      outline: none;
      text-align: center;

      margin: 0px 8px;
      &:active,
      &:focus-within {
        border: 1px solid #63b3ed;
      }
      &.--error {
        border: 1px solid rgb(255, 34, 34);
      }
    }
    .__left-heading {
      font-weight: 500;
      width: 100px;
      display: inline-block;
    }
  }
  .__current-balance {
    border-bottom: 1px solid #e7e7e7;
    margin-bottom: 16px;
    padding-bottom: 16px;

    // display: flex;
    // align-items: center;
    // justify-content: space-between;
    .saas-wallet__balance {
      font-weight: 600;
      font-size: 17px;
      line-height: 20px;
      color: #48bb78;
      &.--negative {
        color: #f56565;
      }
      .wallet-icon {
        color: #26a69a;
      }
      .hl_tooltip {
        background-color: #ffffff;
        color: #718096;
      }
    }
    // .saas-wallet__balance{
    //   display: flex;
    //   width: 100%;
    //   justify-content: space-between;
    // }
  }
  .__no-monthly-credits {
    .--link {
      color: #158bf5;
      cursor: pointer;
      font-weight: 500;
    }
  }
}
.manage-credits__save-row {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 16px;
  .btn {
    margin-left: 12px;
    padding: 8px 12px !important;
    border-radius: 3px;
    &.--grey {
      background: #e2e8f0;
      color: #4a5568;
    }
  }
}
</style>
