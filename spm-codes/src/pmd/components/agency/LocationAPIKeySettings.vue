<template>
  <div>
    <div class="container-fluid mt-6">
      <div class="hl_settings--controls">
				<div class="hl_settings--controls-left">
					<h2>
						Location API Key
					</h2>
				</div>
				<div class="hl_settings--controls-right">
          <a
            href="javascript:void()"
            v-b-tooltip.hover="'Reset all location api keys'"
            @click.prevent="showAllRegenarateVerificationModal = true">
            Reset All Keys
          </a>
          <div class="search-form">
            <UITextInputGroup
              type="text"
              icon="icon-loupe"
              class="form-light"
              :placeholder="'Search location'"
              v-model="query"
              @input="onChange"
            />
            <div style="position: absolute; top: 33%; right: 10px">
              <moon-loader
                v-if="searchLoading"
                :loading="searchLoading"
                color="#1ca7ff"
                size="15px"
              />
            </div>
          </div>
        </div>
			</div>
    </div>

    <div class="card">
      <div class="--no-padding">
        <div class="table-wrap">
          <table class="table">
            <thead>
              <tr>
                <th>Name</th>
                <th>API Key</th>
                <th style="width: 1px;">Action</th>
              </tr>
            </thead>
            <tbody>
              <tr v-if="isLocationFetching">
                <td colspan="3" align="center">Fetching locations...</td>
              </tr>
              <tr v-for="location in locations" :key="location.id" v-else-if="locations.length">
                <td>
                  {{ location.name}}
                </td>
                <td>
                  <div v-if="location.apiKey" class="d-inline-block" style="cursor: pointer;" v-b-tooltip.hover.left="location.apiKey == copiedKey ? 'Copied!' : 'Copy'" @click.prevent.stop="copyKey(location.apiKey)">
                    {{ location.apiKey && location.apiKey.length ? location.apiKey.slice(0, 4) + '****-****-****-****-******' + location.apiKey.slice(-6) : '' }}
                    <i class="fas fa-clipboard ml-2 --dark copier zoomable" @click.prevent.stop="copyKey(location.apiKey)"></i>
                    <span v-if="location.apiKey && location.apiKey.length < 45" class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 ml-2">
                      Old
                    </span>
                  </div>
                </td>
                <td>
                  <div class="d-flex">
                    <i
                      class="icon icon-repeat pointer --light"
                      v-b-tooltip.hover
                      title="Re Generate key"
                      @click.prevent="showRegenarateVerificationModal = true; currentLocationId = location.id"
                    ></i>
                  </div>
                </td>
              </tr>

              <tr v-else>
                <td colspan="3" align="center">No locations found</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <nav aria-label="Page navigation" class="mb-3" v-if="! query">
      <ul class="pagination justify-content-end">
        <li class="page-item" :class="{disabled: skip < (this.limit + 1) }">
          <a
            class="page-link"
            href="javascript:void(0);"
            tabindex="-1"
            @click.prevent="previousPage"
          >Previous</a>
        </li>
        <li class="page-item" :class="{disabled: skip > 0 && locations.length < this.limit }">
          <!-- Max is always last page + 1  -->
          <a class="page-link" href="javascript:void(0);" @click.prevent="nextPage">Next</a>
        </li>
      </ul>
    </nav>

    <request-card-success-modal
      v-if="showSuccessModal"
      :show="showSuccessModal"
      :message="successMsg"
      @close="showSuccessModal = false"
    />
    <VerifyRegenrateKey
      :showModal="showRegenarateVerificationModal"
      @close="val => showRegenarateVerificationModal = val"
      title='Reset API key?'
      :msg="'This will reset API Key for sub account : '  + (this.currentLocaiton.name || this.currentLocaiton._id)"
      @hidden="showRegenarateVerificationModal = false; currentLocationId = ''"
      @confirm="regenarateLocationApiKey"
    />

    <VerifyRegenrateKey
      :showModal="showAllRegenarateVerificationModal"
      title="Reset all location keys?"
      msg="Are you sure you want to reset all the locations api keys?"
      @close="val => showAllRegenarateVerificationModal = val"
      @hidden="showAllRegenarateVerificationModal = false"
      @confirm="regenarateAllLocationApiKey"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import axios from 'axios'
import { Location } from '@/models'
import RequestCardSuccessModal from '@/pmd/components/saas/agency/RequestCardSuccessModal.vue'
import VerifyRegenrateKey from '@/pmd/components/common/VerifyRegenrateKey.vue'
import LocationsService from '../../../services/LocationsService'

export default Vue.extend({
  props: ['company', 'authUser'],
  components: {
    RequestCardSuccessModal,
    VerifyRegenrateKey
  },
  data: () => ({
    copiedKey: '',
    locations: [] as Location[],
    isLocationFetching: false,
    successMsg: '',
    showSuccessModal: false,
    searchLoading: false,
    limit: 50,
    skip: 0,
    query: '',
    showRegenarateVerificationModal: false,
    currentLocationId: '',
    showAllRegenarateVerificationModal: false,
    searchDebouncer: undefined,
    fetchCounter: 0
  }),
  methods: {
    onChange() {
      if (this.searchDebouncer) clearTimeout(this.searchDebouncer);
			this.searchDebouncer = setTimeout(this.filterLocation, 300);
    },
    async filterLocation() {
      this.fetchCounter++;
      const counter = this.fetchCounter

      if (this.query) {
        this.isLocationFetching = true
        this.locations = [];
        return LocationsService.keysQuery(this.authUser.companyId, false, this.query).then((response) => {
          if (counter !== this.fetchCounter) return
          this.locations = [];
          this.locations = this.locations.concat(response.locations.map((location) => {
              return { ...location, id: location._id }
          }))
          this.skip += this.limit
          this.isLocationFetching = false
        })
      } else {
        this.skip = 0;
        await this.loadMore();
      }
    },
    async loadMore(){
      this.fetchCounter++;
      const counter = this.fetchCounter
      this.isLocationFetching = true
      this.locations = [];
      return LocationsService.keys(this.authUser.companyId, false, undefined, this.skip, this.limit).then((response) => {
        if (counter !== this.fetchCounter) return
        this.locations = this.locations.concat(response.locations.map((location) => {
            return { ...location, id: location._id }
        }))
        this.skip += this.limit
        this.isLocationFetching = false
      })
    },
    async fetchLocations() {
      try {
        this.skip = 0;
        await this.loadMore()
      } catch (error) {
        console.error(error)
        alert('Something went wrong!')
      }
    },
    async regenarateLocationApiKey() {
      try {
        this.showRegenarateVerificationModal = false;
        const {data} = await axios.post(
          `/location/api-key/${this.currentLocationId}`,
          {
            company_id: this.company.id,
            userId: this.authUser.userId,
          }
        );
        this.currentLocationId = ''
        this.locations = [];
        await this.filterLocation()
      } catch(error) {
        console.log(error)
      }
    },
    async regenarateAllLocationApiKey() {
      try {
        this.showAllRegenarateVerificationModal = false;
        const {data} = await axios.post(
          `location/${this.company.id}/reset-api-key/`,
          {
            company_id: this.company.id,
            userId: this.authUser.userId,
          }
        );
        setTimeout(() => {
          this.successMsg = 'Request has been queued to change API Keys. It will take a few seconds.',
          this.showSuccessModal = true
        }, 1000)
      } catch(error) {
        console.log(error)
      }
    },
    copyKey(key: string) {
      this.copiedKey = key;
      this.clipboardCopy(key)

      setTimeout(() => {
        this.copiedKey = '';
      }, 2000)
    },
    previousPage() {
      if (this.isLocationFetching) {
        return
      }
      this.skip = this.skip - (this.limit * 2);
      this.loadMore()
    },
    nextPage() {
      if (this.isLocationFetching) {
        return
      }
      this.loadMore()
    }
  },
  computed: {
    currentLocaiton() {
      return this.currentLocationId ? this.locations.find(x => x._id === this.currentLocationId) : {}
    }
  },
  async created() {
    this.fetchLocations()
  }
})
</script>
<style scoped>
.sm-button::before {
  font-family: var(--ff);
  font-weight: 900;
  /* content: attr(data-icon); */
  content: var(--fa);
  font-style: normal;
}
</style>
