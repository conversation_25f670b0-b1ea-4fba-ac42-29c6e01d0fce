<template>
    <div class="hl_activity-history--item">
        <i class="icon" :class="iconClass">
        </i>
        <div class="hl_activity-history--item-header">
            <h4>{{heading}}</h4>
        </div>
        <p v-html="body"></p>
        <div class="hl_activity-history--item-footer">
            <p v-if="user">By:
                <strong>{{user.fullName}}</strong>
            </p>
            <p>{{ activity.dateAdded.calendar() }}</p>
        </div>
    </div>
</template>

<script lang="ts">
  import Vue from 'vue';
  import {ActionType, Activity, ActivityType, User} from '@/models';

  export default Vue.extend({
    props: {
      activity: Activity
    },
    data() {
      return {
        user: undefined as User | undefined,
      }
    },
    computed: {
      iconClass() {
        return `${this.icon} ${this.color}`;
      },
      icon() {
        switch (this.activity.type) {
          case ActivityType.NOTE:
            return 'icon-note';
          case ActivityType.TASK:
            return 'icon-task';
        }
      },
      color() {
        switch (this.activity.actionType) {
          case 'added':
            return '--green';
          case 'updated':
            return '--blue';
          case 'deleted':
            return '--yellow';
        }
      },
      heading(): string {
        if ([ActivityType.NOTE, ActivityType.TASK].indexOf(this.activity.type) != -1) {
          return (this.$options.filters ? this.$options.filters.capitalize(this.activity.actionType) : this.activity.actionType) + ' ' + this.activity.type;
        } else if (this.activity.type === ActivityType.PRODUCT) {
          let heading = '';
          switch (this.activity.activityData.type) {
            case 'review':
              heading += "Reviews product ";
              break;
            case 'listing':
              heading += "Listings product ";
              break;
            case 'conversation':
              heading += "Conversations product ";
              break;
          }
          heading += this.activity.actionType === ActionType.ADDED ? 'added' : 'removed';

          return heading;
        }

        return '';
      },
      body(): string {
        if ([ActivityType.NOTE, ActivityType.TASK].indexOf(this.activity.type)!=-1) {
            return this.activity.activityData.body;
        }

        return '';
      }
    },
    async created() {
      if (this.activity.userId) {
        this.user = await this.$store.dispatch('agencyUsers/syncGet', this.activity.userId);
      }
    }
})
</script>
