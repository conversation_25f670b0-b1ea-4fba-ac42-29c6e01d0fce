<template>
  <div class="col-md-3 p-0 rounded">
    <div class="filters">
      <div class="select-control-wrap">
        <select
          class="select-control rounded cmp-jotbox-sidebar__sel--source"
          v-model="selected"
          @change="sourceUpdate"

        >
          <option value="all">All {{ type }}</option>
          <option
            v-for="(item, index) in formbuilder"
            :value="item.id"
            :key="index"
          >
            {{ item.name }}
          </option>

          <optgroup label="Facebook" v-if="type == 'Form'">
            <option :value="`fb-${locationId}`">Lead Form</option>
          </optgroup>

          <optgroup label="Chat Widget" v-if="type == 'Form'">
            <option :value="`cwf-${locationId}`">Widget Form</option>
          </optgroup>
        </select>
      </div>

      <div
        class="form-group has-search rounded mb-0 position-relative mt-2 mb-2"
      >
        <UITextInputGroup
          type="text"
          icon="fa fa-search"
          class="border cmp-jotbox-sidebar__txt--filter-contact"
          placeholder="Search by name, email, phone and contact id"
          v-model="q"
          @input="filterContact"
        />
      </div>

      <div class="totals d-flex justify-content-between align-items-center">
        <div class>
          <label>
            Totals:
            <strong class="total-count">{{
              rows | formatNumberNoDecimal
            }}</strong>
            records | {{ currentPage }} of {{ totalPage }} pages
          </label>
        </div>
        <div class="select-control-wrap">
          <select class="select-control rounded cmp-jotbox-sidebar__sel--row-per-page" v-model="itemPerPage" >
            <option disabled>Per Page</option>
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
          </select>
        </div>
      </div>
    </div>

    <div class="user-list-detail bg-white shadow-sm rounded">
      <div
        class="d-flex justify-content-between align-items-center table-header"
      >
        <div class="p-2 bd-highlight">
          <div class="custom-control custom-checkbox form-history col-1 px-0">
            <UICheckbox
              class="custom-control-input bg-white"
              id="customCheck"
              @change="selectAll"
            />
            <label class="custom-control-label white-bg" for="customCheck">
              Select All
            </label>
          </div>
        </div>
        <div class="p-2 bd-highlight" v-if="rows > 0">
          <a
            href="#"
            data-tooltip="tooltip"
            data-placement="top"
            v-b-tooltip.hover
            title="Delete Submission"
            :class="{
              'text-grey': checkedItem.length < 1,
              'text-primary': checkedItem.length > 0,
            }"
            id="pg-form__button--delete-submission"
          >
            <i
              class="fa fa-trash cmp-jotbox-sidebar__btn--delete-hisotry"
              aria-hidden="true"
              @click="deleteHistory"
            ></i>
          </a>

          <a
            href="#"
            class="text-primary"
            data-tooltip="tooltip"
            data-placement="top"
            v-b-tooltip.hover
            :title="toolTipTitle"
            id="pg-form__button--download-submissions"
          >
            <i
              class="fa fa-download cmp-jotbox-sidebar__btn--download"
              aria-hidden="true"

              @click="$emit('downloadData', checkedItem)"
            ></i>
          </a>
        </div>
      </div>
      <div v-if="loading" class="loading">
        <div v-for="index in 8" :key="index">
          <div
            class="media border-bottom p-2 d-flex align-items-center submission"
          >
            <div class="date-stra small loading-checkbox">
              <span></span>
            </div>
            <div class="media-body">
              <p class="loading-name">
                <span></span>
              </p>
              <p class="small mt-0 pt-0 text-nowrap loading-email">
                <span></span>
              </p>
            </div>
            <div class="date-stra small loading-date">
              <span></span>
            </div>
          </div>
        </div>
      </div>

      <!-- eslint-disable vue/no-use-v-if-with-v-for,vue/no-confusing-v-for-v-if -->
      <div v-if="!loading" v-for="(item, index) in filterContacts" :key="index">
        <div
          class="media border-bottom p-2 d-flex align-items-center submission cmp-jotbox-sidebar__btn--show-details"
          :ref="'getDetails' + '_' + item.id"
          @click="showDetails(item, index)"
        >
          <div class="custom-control custom-checkbox form-history col-1 px-0">
            <input
              type="checkbox"
              class="custom-control-input bg-white"
              :id="'customCheck_' + item.id"
              :value="item.id"
              v-model="checkedItem"
            />
            <label
              class="custom-control-label white-bg"
              :for="'customCheck_' + item.id"
            >
              Select
            </label>
          </div>
          <div class="media-body">
            <p>
              <a style="color: inherit; fontweight: 600">
                {{
                  item.others && item.others.full_name
                    ? item.others.full_name
                    : item.name
                }}
              </a>
            </p>
            <p class="small mt-0 pt-0 text-nowrap">
              <a style="color: inherit">{{ item.email }}</a>
            </p>
          </div>
          <div class="date-stra small">
            {{ moment(item.createdAt).format('MMM Do YYYY') }}
          </div>

          <div class="dropdown ml-5">
            <svg
              type="button"
              id="dropdownMenuButton"
              data-toggle="dropdown"
              aria-haspopup="true"
              aria-expanded="false"
              width="1em"
              height="1em"
              viewBox="0 0 16 16"
              class="bi bi-three-dots-vertical"
              fill="currentColor"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fill-rule="evenodd"
                d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"
              />
            </svg>

            <div class="dropdown-menu" aria-labelledby="dropdownMenuButton" id="pg-form-survey__button--pdf">
              <a class="dropdown-item cmp-jotbox-sidebar__btn--download-pdf" href="#" @click="downloadAsPdf(item)"
                >Download PDF</a
              >
            </div>
          </div>
        </div>
      </div>
      <div v-if="rows > 0" class="py-3 text-center">
        <b-pagination
          v-model="currentPage"
          :total-rows="rows"
          :per-page="itemPerPage"
          first-number
        ></b-pagination>
      </div>
      <div v-if="filterContacts.length === 0 && !loading" class>
        <p style="fontweight: 600" class="p-4 text-center">No records found</p>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import moment from 'moment'
import { UxMessage } from '@/util/ux_message'
import { jsPDF } from 'jspdf'
/*global ga*/
Vue.prototype.moment = moment
export default Vue.extend({
  props: [
    'contactNumber',
    'formData',
    'formbuilder',
    'formId',
    'loading',
    'type',
    'totalRows',
    'page',
    'limit',
    'deletedItem',
    'locationId',
  ],
  inject: ['uxmessage'],
  data() {
    return {
      contactDelete: [],
      checkedItem: [],
      selected: this.formId,
      filterContacts: [],
      currentPage: this.page ? parseInt(this.page) : 1,
      itemPerPage: this.limit ? parseInt(this.limit) : 10,
      rows: this.totalRows,
      q: '',
      selectedAll: false,
    }
  },
  computed: {
    totalPage() {
      return Math.round(this.rows / this.itemPerPage + 0.5)
    },
    toolTipTitle() {
      if (this.checkedItem.length > 0) {
        return `Download selected ${
          this.checkedItem.length === 1 ? 'submission' : 'submissions'
        }`
      }

      return 'Download all submissions'
    },
  },
  watch: {
    formData(val) {
      this.filterContacts = val
    },
    totalRows() {
      this.rows = this.totalRows
    },
    formId() {
      this.selected = this.formId
    },
    itemPerPage() {
      this.currentPage = 1
      this.$router.push({
        query: {
          ...this.$route.query,
          page: this.currentPage,
          limit: this.itemPerPage,
        },
      })
    },
    currentPage() {
      this.$router.push({
        query: {
          ...this.$route.query,
          page: this.currentPage,
          limit: this.itemPerPage,
        },
      })
    },
    checkedItem(val) {
      setTimeout(() => {
        if (this.selectedAll && val.length != this.filterContacts.length) {
          this.selectedAll = false
        }

        if (!this.selectedAll && val.length == this.filterContacts.length) {
          this.selectedAll = true
        }
      }, 10)
    },
    deletedItem() {
      this.selectedAll = false
      this.checkedItem = []
    },
  },
  created() {
    this.q = this.$router.currentRoute.query.q
    if (this.q) {
      this.filterContact()
    }
  },
  methods: {
    sourceUpdate() {
      console.log('sourceUpdate', this.selected)
      this.currentPage = 1
      this.$router.push({ query: { ...this.$route.query, id: this.selected } })
    },
    filterContact() {
      this.currentPage = 1
      this.$router.push({
        query: { ...this.$route.query, q: this.q.toLowerCase() },
      })
    },
    deleteHistory() {
      if (this.checkedItem.length > 0) {
        this.$emit('deleteHistory', this.checkedItem)
      } else {
        this.uxmessage(
          UxMessage.warningType('Please select atleast one submission.'),
          true
        )
      }
    },
    showDetails(item, index) {
      this.highlightItems(item.id)
      this.$emit('showDetails', item, index)
    },
    highlightItems(id) {
      const currentRef = this.$refs['getDetails_' + id]
      const currentItem = document.querySelectorAll('.current-active')
      currentItem.forEach(value => {
        value.classList.remove('current-active')
      })
      if (currentRef && currentRef.length) {
        currentRef[0].classList.add('current-active')
      }
    },
    selectAll() {
      if (!this.filterContacts) return

      if (this.selectedAll) {
        this.checkedItem = []
        this.selectedAll = false
      } else {
        this.checkedItem = this.filterContacts.map(item => item.id)
        this.selectedAll = true
      }
    },
    async downloadAsPdf(item) {
      let form
      this.formbuilder.forEach(data => {
        if (data.id === item.others.formId) form = data
      })
      if (form.data.form_data.slides) {
        //Download survey
        ga('send', 'event', 'Survey_downloadPdf', this.locationId, form.id)
        let tempArr = []
        form.data.form_data.slides.forEach(slide => {
          tempArr = tempArr.concat(slide.slideData)
        })
        await this.downloadPDF(tempArr, item)
      } else {
        //Download form
        ga('send', 'event', 'Form_downloadPdf', this.locationId, form.id)
        const fields = form.data.form_data.form.fields
        await this.downloadPDF(fields, item)
      }
    },
    async downloadPDF(fields, item) {
      let values = item.others
      var doc = new jsPDF('p', 'mm', 'a4')

      var fontSize = 16
      var offsetY = 7.797777777777778
      var lineHeight = 9.49111111111111
      let marginBottom = 4

      let k = 0

      for (let i = 0; i < fields.length; i++) {
        let field = fields[i]

        const isPDFOverflow = (extraHeight = 18, forText) => {
          let currentHeight = lineHeight * k + (offsetY - 2)
          if (
            currentHeight + extraHeight >
            doc.internal.pageSize.getHeight() - marginBottom
          ) {
            k = 1
            if (forText) {
              for (let i = 0; i <= doc.getFontSize() / fontSize - 2; i++)
                k += 0.6
            }
            return true
          }
        }

        if (field.type === 'file_upload' && values[field.tag]) {
          let isFileType = value => {
            let flag = false
            Object.keys(value).forEach(id => {
              if (Object.prototype.hasOwnProperty.call(value[id], 'meta')) {
                flag = true
              }
            })
            return flag
          }

          doc.setFontSize(fontSize)
          doc.setFont('helvetica', 'bold')
          if (isPDFOverflow()) doc.addPage()
          var strArr1 = doc.splitTextToSize(
            field.label || field.name || field.placeholder || field.tag,
            doc.internal.pageSize.getWidth() - 40
          )
          strArr1.forEach(text => {
            if (isPDFOverflow()) doc.addPage()
            doc.text(text, 10, lineHeight * k++ + offsetY)
          })
          doc.setFont('helvetica', 'normal')
          doc.setTextColor('#0000FF')
          doc.setFontSize(fontSize - 2)

          if (isFileType(values[field.tag])) {
            console.log(values[field.tag])

            Object.keys(values[field.tag]).forEach(key => {
              if (values[field.tag][key].meta) {
                doc.textWithLink(
                  values[field.tag][key].meta.originalname.substring(0, 60) ||
                    'click here',
                  10,
                  lineHeight * k++ + (offsetY - 2),
                  { url: values[field.tag][key].url }
                )
              }
            })
          } else {
            doc.textWithLink(
              values[field.tag].meta.originalname.substring(0, 60) ||
                'click here',
              10,
              lineHeight * k++ + (offsetY - 2),
              { url: values[field.tag].url }
            )
          }

          doc.setTextColor(0, 0, 0)
        } else if (field.type === 'signature' && values[field.tag]) {
          doc.setFontSize(fontSize)
          doc.setFont('helvetica', 'bold')
          if (isPDFOverflow(41)) doc.addPage()
          strArr1 = doc.splitTextToSize(
            field.label || field.name || field.placeholder || field.tag,
            doc.internal.pageSize.getWidth() - 40
          )
          strArr1.forEach(text => {
            if (isPDFOverflow()) doc.addPage()
            doc.text(text, 10, lineHeight * k++ + offsetY)
          })
          doc.setFont('helvetica', 'normal')
          doc.setFontSize(fontSize - 2)
          let imgData = await this.getDataUri(values[field.tag].url)
          doc.addImage(
            imgData,
            'JPEG',
            10,
            lineHeight * k++ + (offsetY - 2),
            60,
            30
          )
          k++
          k++
          k++
        } else if (field.type === 'img' && field.url) {
          if (isPDFOverflow(125)) doc.addPage()
          let imgData = await this.getDataUri(field.url)
          doc.addImage(
            imgData,
            'JPEG',
            10,
            lineHeight * k++ + (offsetY - 2),
            doc.internal.pageSize.getWidth() - 30,
            100
          )
          k += 11
        } else if (field.type === 'h1' && field.label) {
          doc.setFontSize(fontSize)
          doc.setFont(field.font ? field.font : 'helvetica', 'bold')

          if (field.color) doc.setTextColor(field.color)

          if (field.size) {
            doc.setFontSize(field.size)
          } else {
            doc.setFontSize(40)
          }

          if (field.weight) {
            if (field.weight >= 300) {
              doc.setFont(field.font ? field.font : 'helvetica', 'normal')
            } else if (field.weight >= 700) {
              doc.setFont(field.font ? field.font : 'helvetica', 'bold')
            }
          } else {
            doc.setFont(field.font ? field.font : 'helvetica', 'normal')
          }

          for (let i = 0; i <= Math.ceil(doc.getFontSize() / fontSize) - 2; i++)
            k += 0.6

          var strArr = doc.splitTextToSize(
            field.label,
            doc.internal.pageSize.getWidth() - 40
          )
          strArr.forEach(text => {
            for (
              let i = 0;
              i <= Math.ceil(doc.getFontSize() / fontSize) - 3;
              i++
            )
              k += 0.9

            if (isPDFOverflow(Math.ceil(doc.getFontSize() / fontSize), true))
              doc.addPage()

            console.log(doc.getFontSize())
            doc.text(
              text,
              10,
              lineHeight * k++ + offsetY + doc.getFontSize() / fontSize
            )
          })

          doc.setTextColor(0, 0, 0)

          for (let i = 0; i <= Math.ceil(doc.getFontSize() / fontSize) - 2; i++)
            k += 0.6
        } else if (field.type === 'textbox_list' && values[field.tag]) {
          doc.setFontSize(fontSize)
          doc.setFont('helvetica', 'bold')

          strArr1 = doc.splitTextToSize(
            field.fieldName,
            doc.internal.pageSize.getWidth() - 40
          )
          strArr1.forEach(text => {
            if (isPDFOverflow()) doc.addPage()
            doc.text(text, 10, lineHeight * k++ + offsetY)
          })

          field.picklistOptions.forEach(option => {
            doc.setFontSize(fontSize)
            doc.setFont('helvetica', 'bold')
            if (isPDFOverflow()) doc.addPage()
            strArr1 = doc.splitTextToSize(
              option.label,
              doc.internal.pageSize.getWidth() - 40
            )
            strArr1.forEach(text => {
              if (isPDFOverflow()) doc.addPage()
              doc.text(text, 10, lineHeight * k++ + offsetY)
            })
            doc.setFont('helvetica', 'normal')
            doc.setFontSize(fontSize - 2)
            strArr = doc.splitTextToSize(
              values[field.tag][option.id],
              doc.internal.pageSize.getWidth() - 40
            )
            strArr.forEach(text => {
              if (isPDFOverflow()) doc.addPage()
              doc.text(text, 10, lineHeight * k++ + (offsetY - 2))
            })
          })
        } else if (values[field.tag]) {
          doc.setFontSize(fontSize)
          doc.setFont('helvetica', 'bold')
          if (isPDFOverflow()) doc.addPage()
          strArr1 = doc.splitTextToSize(
            field.label || field.name || field.placeholder || field.tag,
            doc.internal.pageSize.getWidth() - 40
          )
          strArr1.forEach(text => {
            if (isPDFOverflow()) doc.addPage()
            doc.text(text, 10, lineHeight * k++ + offsetY)
          })
          doc.setFont('helvetica', 'normal')
          doc.setFontSize(fontSize - 2)
          strArr = doc.splitTextToSize(
            values[field.tag],
            doc.internal.pageSize.getWidth() - 40
          )
          strArr.forEach(text => {
            if (isPDFOverflow()) doc.addPage()
            doc.text(text, 10, lineHeight * k++ + (offsetY - 2))
          })
        }
      }
      doc.save(`${item.contactId}.pdf`)
    },
    getDataUri(url) {
      return new Promise(resolve => {
        var image = new Image()
        image.setAttribute('crossOrigin', 'anonymous')

        image.onload = function () {
          var canvas = document.createElement('canvas')
          canvas.width = this.naturalWidth
          canvas.height = this.naturalHeight

          var ctx = canvas.getContext('2d')
          ctx.fillStyle = '#fff'
          ctx.fillRect(0, 0, canvas.width, canvas.height)

          canvas.getContext('2d').drawImage(this, 0, 0)

          resolve(canvas.toDataURL('image/jpeg'))
        }

        image.src = url
      })
    },
  },
})
</script>

<style scoped lang="scss">
.filters {
  width: 100%;
  padding-bottom: 10px;
  .select-control-wrap {
    background: transparent;
    padding: 0;
    .select-control {
      border: 1px solid #dee2e6;
      padding: 7px 35px 7px 10px;
    }
  }

  .has-search .form-control-feedback {
    position: absolute;
    z-index: 2;
    display: block;
    width: 15px;
    height: 15px;
    line-height: 1.375rem;
    text-align: center;
    pointer-events: none;
    color: #aaa;
    top: 9px;
    left: 7px;
  }

  .totals {
    margin-bottom: 0;
    label {
      margin-bottom: 0;
    }
    .select-control-wrap {
      max-width: 70px;
    }
  }

  .total-count {
    line-height: 2.5;
    text-align: center;
  }
}
.user-list-detail {
  .submission {
    cursor: pointer;
    &:hover,
    &.current-active {
      background: #f9fafb;
    }
  }
}

.form-builder-integrate-wrapper .media-body {
  width: 60%;
}
.pagination {
  justify-content: center;
  margin-bottom: 0;
}
.loading {
  div {
    .loading-name {
      width: 150px;
      height: 25px;
      span {
        display: block;
        width: 110px;
        height: 18px;
        background: linear-gradient(to right, #eee 20%, #ddd 50%, #eee 80%);
        background-size: 700px 100px;
        animation-name: moving-gradient;
        animation-duration: 1.5s;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
      }
    }

    .loading-email {
      width: 200px;
      height: 18px;
      span {
        display: block;
        width: 170px;
        height: 15px;
        background: linear-gradient(to right, #eee 20%, #ddd 50%, #eee 80%);
        background-size: 700px 100px;
        animation-name: moving-gradient;
        animation-duration: 1.5s;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
      }
    }
    .loading-date {
      width: 80px;
      height: 15px;
      span {
        display: block;
        width: 65px;
        height: 12px;
        background: linear-gradient(to right, #eee 20%, #ddd 50%, #eee 80%);
        background-size: 700px 100px;
        animation-name: moving-gradient;
        animation-duration: 1.5s;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
      }
    }
    .loading-checkbox {
      width: 25px;
      height: 15px;
      span {
        display: block;
        width: 18px;
        height: 12px;
        background: linear-gradient(to right, #eee 20%, #ddd 50%, #eee 80%);
        background-size: 700px 100px;
        animation-name: moving-gradient;
        animation-duration: 1.5s;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
      }
    }
  }
}
.primary-bg {
  border-top: 1px solid #dee2e6;
  border-bottom: 1px solid #dee2e6;
}
.form-history {
  label.custom-control-label {
    &::before {
      height: 17px;
      width: 17px;
    }

    &::after {
      top: 0.35rem;
      left: 0;
    }
  }
}
.table-header {
  border-radius: 4px;
  border-top: 3px solid #049efb;
  border-bottom: 1px solid #dee2e6;

  .text-primary {
    padding: 0 10px;
  }

  .text-grey,
  .text-grey:hover {
    color: #b3b5ce;
  }
}
</style>
