<template>
  <div :class="{'flex': isV2SideBar, 'v2-collapse': sideBarState, 'v2-open': !sideBarState, 'sidebar-v2-location': isV2SideBar && !isOnbording}">
    <SideBarV2 v-if="isV2SideBar && !hideSideTopBar && !isOnbording" :locationId="this.$route.params.location_id" />
    <SideBar v-else-if="!hideSideTopBar && !isV2SideBar" :disableAction="disableAction" />
    <TopBar v-if="!isOnbording && $route.name != 'location_user_update'" :disableAction="disableAction" />
    <!-- <keep-alive :include="cachedComponents"> -->
    <router-view 
      :class="{
        'hl_topbar-tabs': isTopMenuItemsExists, 
        'hl_wrapper': $route.path.includes('payments') && isV2SideBar, 
        'hl_topbar-tabs-padding': $route.name == 'appointments-reports-v2',
        'hl_overflow-y-scroll': $route.name == 'reputation_listing-v2' 
        }" 
    />
    <!-- </keep-alive> -->

    <warning-manager
      :uuid="this.$route.params.location_id"
      :disable="this.disableWM"
    >
      <no-payment-method-warning />
      <!-- <missing-website-warning /> -->
    </warning-manager>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import TopBar from '../components/TopBar.vue'
import SideBar from '../components/SideBar.vue'
import SideBarV2 from '../components/sidebar/SideBar.vue'
import NoPaymentMethodWarning from '@/pmd/components/saas/location_billing/NoPaymentMethodWarning.vue'
// import MissingWebsiteWarning from '@/pmd/components/common/MissingWebsiteWarning.vue'
import WarningManager from '@/pmd/components/common/WarningManager.vue'
import { User } from '@/models'
import config from '@/config'
import { EmailBuilderServices } from '../../services'

export default Vue.extend({
  components: {
    TopBar,
    SideBar,
    SideBarV2,
    NoPaymentMethodWarning,
    // MissingWebsiteWarning,
    WarningManager,
  },
  data() {
    return {
      cachedComponents: [
        'EmptyRouterParent',
        'LocationDashboard',
        'LocationAppointmentPage',
        'LocationOpportunitiesPage',
        'LocationConversationsPage',
        'LocationReviewsPage',
      ],
      disableAction: false,
      disableWM: false,
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      if (this.$route.params.location_id) {
        this.disableWM = false
        this.migrateEmailBuilder()
      } else {
        this.disableWM = true
      }
    },
  },
  computed: {
    company() {
      return this.$store.state.company.company
    },
    location() {
      return this.$store.getters['locations/getById'](
        this.$route.params.location_id
      )
    },
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    isV2SideBar() {
      return this.getSideBarVersion === 'v2' && this.$route.path.includes('/v2/location')
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
    hideSideTopBar() {
      if (this.$route.path.includes('user-update')) return true
      return false
    },
    sideBarState() {
      return this.$store.getters.getManualCollapseSidebar
    },
    isTopMenuItemsExists() {
      const navigation = this.$store.state.menuTabs
      if (this.isV2SideBar && navigation?.children?.length) {
        return true
      }
      return false
    },
    isSubItemsExists() {
      const navigation = this.$store.state.menuTabs
      if (this.isV2SideBar && navigation?.items?.length) {
        return true
      }
      return false
    },
    isOnbording() {
      return this.$router.currentRoute.query.onboarding && this.$router.currentRoute.query.onboarding == 'true'
    }
  },
  mounted() {
    this.migrateEmailBuilder()
    this.$root.$on('disable-launchpad-action', (data: string) => {
      const action = JSON.parse(data)
      console.log('Listen Disable Action')
      this.disableAction = action.disable
    })
  },
  methods: {
    migrateEmailBuilder() {

      if (this.$route.params.location_id) {
        EmailBuilderServices.migrateBulkRequests(
          this.$route.params.location_id
        )
          .then(({data}) => console.log('bulk requests migrated info:', data))
          .catch(err => console.warn(err))
      }
    },
  },
})
</script>
