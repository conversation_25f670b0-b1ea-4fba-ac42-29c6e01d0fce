<template>
  <div
    class="modal fade hl_add_payment_method--modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div
        class="modal-content transaction-successful"
        v-if="rechargeStatus.success"
      >
        <i class="fa fa-times hide-modal" @click="hideModal"></i>

        <div class="modal-body">
          <div class="check">
            <i class="fa fa-check" aria-hidden="true"></i>
          </div>
          <h4 v-if="recharge">Transaction Successful!</h4>
          <h4 v-else>Credits Added Successfully!</h4>
          <button @click="hideModal" class="btn cancel">Okay</button>
        </div>
      </div>
      <div class="modal-content" v-else>
        <i class="fa fa-times hide-modal" @click="hideModal"></i>
        <div class="modal-header">
          <div class="title" v-if="recharge">Add money to Wallet</div>
          <div class="title" v-else-if="balanceType !== 'CREDIT'">Remove Credits from Wallet</div>
          <div class="title" v-else>Add Complimentary Credits to Wallet</div>
        </div>
        <div class="modal-body">
          <p class="title">Amount</p>
          <div class="add-balance-input">
            <span>{{ currencySymbol }}</span>
            <input
              v-model="inputData.value"
              v-validate="'required|regex:^[0-9]+'"
              @input="amountChanged"
              placeholder="Amount"
              type="number"
              class="text-lg text-gray-800"
              :min="inputData.min"
              :max="maxAmount"
              :step="inputData.step"
              name="add-balance"
              id="add-balance"
            />
          </div>
          <div v-show="errors.has('add-balance')" class="error">
            <span v-if="balanceType === 'DEBIT'">
              Please provide an amount less than {{ maxAmount }}.
            </span>
            <span v-else>
              Please provide an amount between {{ inputData.min }} and
              {{ inputData.max }}.
            </span>
          </div>
          <p v-if="balanceType === 'CREDIT'" class="current-balance">
            Current Balance:
            <span>{{ currencySymbol }}{{ currentBalance }}</span>
          </p>
          <div v-if="balanceType === 'DEBIT'">
            <br />
            <p class="title">Description</p>
            <div class="add-balance-input">
              <input
                v-model="descriptionData.value"
                v-validate="'required'"
                @click="descriptionData.value = ''"
                @blur="setDescription"
                placeholder="Description"
                type="text"
                class="text-lg text-gray-800"
                name="description"
                id="description"
              />
            </div>
          </div>
          <div v-if="balanceType !== 'DEBIT'" class="info">
            <i class="fa fa-info-circle"></i>
            <span v-if="recharge">Money will be added to your wallet <span v-if="activeCard && activeCard.cardNumber">and your card ending in {{activeCard.cardNumber}} will be charged</span></span>
            <span v-else style="color: red;">These are complimentary credits and CC will not be charged.</span>
          </div>
          <div class="error recharge-error" v-if="rechargeStatus.error">
            {{ rechargeStatus.error }}
          </div>
        </div>
        <div class="modal-footer">
          <UIButton @click="hideModal" use="outline">Cancel</UIButton>
          <UIButton
            :disabled="rechargeStatus.processing"
            @click="confirm"
            use="tertiary"
          >
            {{ rechargeStatus.processing ? 'Processing..' : 'Proceed' }}
          </UIButton>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    balanceType: {
      type: String,
      default: 'CREDIT'
    },
    showModal: {
      type: Boolean,
      default: false,
    },
    recharge: {
      type: Boolean,
      default: true,
    },
    activeCard: {
      type: Object,
    },
    locationId: String,
    companyId: String,
    stripeAccountId: String,
    stripeCustomerId: String,
    currencySymbol: String,
    currentBalance: {
      type: String,
      default: '0',
    }
  },
  mounted() {
    $(this.$refs.modal).on('hidden.bs.modal', () => {
      this.hideModal()
    })
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  data() {
    return {
      inputData: {
        value: '',
        valid: true,
        min: 1,
        max: 1000,
        step: 1,
        error: '',
      },
      descriptionData: {
        value: 'Credits removed by Agency',
        set: false,
        error: ''
      },
      rechargeStatus: {
        processing: false,
        error: '',
        success: false,
      },
    }
  },
  computed: {
    company() {
      return this.$store.state.company.company
    },
    maxAmount() {
      return this.balanceType === 'CREDIT' ? this.inputData.max : this.currentBalance
    }
  },
  methods: {
    hideModal() {
      this.$emit('cancel')
    },
    async confirm() {
      await this.$validator.validateAll()
      if (this.$validator.errors && this.$validator.errors.items.length) {
        console.log('validation errors --> ', this.$validator.errors)
        return
      }

      const amount = parseFloat(this.inputData.value).toFixed(2)
      console.log('recharge for --> ', amount)

      this.rechargeStatus.processing = true
      this.rechargeStatus.error = ''
      if (!this.recharge) {
        return await this.addFreeCredits(amount)
      } else if(this.locationId) {
        this.locationWalletRecharge(amount)
      } else if(this.companyId) {
        this.agencyWalletRecharge(amount)
      } else {
        console.error('Add Balance Failed !! Invalid condition.')
      }

    },
    async locationWalletRecharge(amount: Number) {
      try {
        const { data } = await this.saasService.post(
          `/location-wallet/${this.locationId}/recharge`,
          {
            amount,
            // currency: 'INR', // TODO: Get this from location settings
            stripeCustomerId: this.stripeCustomerId,
            stripeAccountId: this.stripeAccountId,
          }
        )

        this.rechargeStatus.success = true
        this.$emit('success')

        setTimeout(() => {
          this.hideModal()
        }, 2500)
      } catch (error) {
        console.error('error while recharging --> ', error, error.response)
        const errorMessage =
          error?.response?.data?.msg ||
          error?.response?.data?.message ||
          'Recharge failed!'
        this.rechargeStatus.error = errorMessage
      } finally {
        this.rechargeStatus.processing = false
      }
    },
    async agencyWalletRecharge(amount: Number) {
      try {
        const { data } = await this.saasService.post(
          `/company-wallet/${this.companyId}/recharge`,
          {
            amount,
            // currency: 'INR', // TODO: Get this from location settings
            stripeCustomerId: this.stripeCustomerId,
          }
        )

        this.rechargeStatus.success = true
        this.$emit('success')

        setTimeout(() => {
          this.hideModal()
        }, 2500)
      } catch (error) {
        console.error('error while recharging --> ', error, error.response)
        const errorMessage =
          error?.response?.data?.msg ||
          error?.response?.data?.message ||
          'Recharge failed!'
        this.rechargeStatus.error = errorMessage
      } finally {
        this.rechargeStatus.processing = false
      }
    },
    async addFreeCredits(amount: Number) {
      try {
        const url = this.balanceType === 'CREDIT' ? `/location-wallet/${this.locationId}/free-credits?amount=${amount}` : `/location-wallet/${this.locationId}/debit-refund-amount?amount=${amount}&description=${this.descriptionData.value}`;
        const { data } = await this.saasService.post(
          url,
          {
            amount,
            companyId: this.company.id,
          }
        )

        this.rechargeStatus.success = true
        this.$emit('success')

        setTimeout(() => {
          this.hideModal()
        }, 2500)
      } catch (error) {
        console.error('error while recharging --> ', error, error.response)
        const errorMessage =
          error?.response?.data?.msg ||
          error?.response?.data?.message ||
          'failed to credit complimentary credits!'
        this.rechargeStatus.error = errorMessage
      } finally {
        this.rechargeStatus.processing = false
      }
    },
    amountChanged() {
      const amount = this.inputData.value
      if (!amount) return

      if (amount < this.inputData.min) {
        // this.inputData.value = Math.abs(amount)
      } else if (amount > this.inputData.max) {
        this.inputData.value = this.inputData.max
      }
    },
    setDescription() {
      this.descriptionData.value = this.descriptionData.value.length <= 0 ? 'Credits removed by Agency' : this.descriptionData.value
    }
  },
  watch: {
    showModal(newValue, oldValue) {
      if (newValue && !oldValue) {
        this.rechargeStatus.processing = false
        this.rechargeStatus.error = ''
        this.rechargeStatus.success = false
        this.inputData.value = ''
        this.inputData.valid = true
        this.inputData.error = ''
        $(this.$refs.modal).modal('show')
      } else if (!newValue && oldValue) {
        $(this.$refs.modal).modal('hide')
      }
    },
  },
})
</script>
<style scoped>
.modal-header {
  border: none;
  padding: 42px 32px;
}

.modal-header .title {
  font-size: 25px;
  line-height: 29px;
  color: #333333;
  font-weight: bold;
}

i.hide-modal {
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 16px;
  cursor: pointer;
  z-index: 2;
}

.modal-body {
  padding: 0 32px;
}

.modal-body p.title {
  font-size: 14px;
  line-height: 16px;
  color: #1a202c;
  font-weight: bold;
}

.add-balance-input {
  margin-top: 10px;
  margin-bottom: 3px;
  background: #ffffff;
  border: 1px solid #4263eb;
  border-radius: 3px;
  padding: 8px 12px;
  font-size: 18px;
  /* color: #cbd5e0; */
  display: flex;
  align-items: center;
}

.add-balance-input input {
  border: none;
  flex: 1;
  --tw-ring-color: none !important;
  --tw-ring-shadow: none !important;
}

.add-balance-input input[type='number']::-webkit-inner-spin-button,
.add-balance-input input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}

.add-balance-input input:focus {
  outline: none;
}

.modal-body p.current-balance {
  color: #4a5568;
  font-size: 13px;
  line-height: 15px;
  margin-top: 15px;
}

.modal-body p.current-balance span {
  color: #2f855a;
}

.modal-body .info {
  margin-top: 75px;
  color: #a0aec0;
  font-size: 13px;
  line-height: 15px;
}

.modal-body .info i {
  margin-right: 6px;
}

.modal-footer {
  border: none;
  display: flex;
  padding: 22.5px 32px;
}

.btn {
  font-size: 14px;
  border-radius: 3px;
  padding: 12px 16px;
  width: 155px;
}

.btn.cancel {
  background: #e2e8f0;
  color: #718096;
  margin-right: 12px;
}

.btn.confirm {
  /* background: #4299e1; */
  background: #188bf6;
  color: white;
}

.transaction-successful .modal-body {
  padding: 40px 32px;
  text-align: center;
}

.transaction-successful .check {
  border: 2px solid #68d391;
  padding: 32px;
  border-radius: 100%;
  height: 90px;
  width: 90px;
  display: flex;
  margin: auto;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #68d391;
}

.transaction-successful h4 {
  color: #4f4f4f;
  margin: 25px 0 44px 0;
}

.modal-body .recharge-error {
  margin: 6px 0;
  text-align: center;
}
</style>
