<template>
  <Builder v-if="location" :location="location" :user="user" />
</template>

<script lang="ts">
import Vue from 'vue'
import Builder from './Builder.vue'

export default Vue.extend({
  components: { Builder },
  computed: {
    locationId() {
      return this.$route.params.locationId
    },
    location() {
      const locations = this.$store.state.locations.locations
      return locations.find(x => x.id === this.$route.params.location_id)
    },
    user() {
      return this.$store.state.user.user
    },
  },
})
</script>
