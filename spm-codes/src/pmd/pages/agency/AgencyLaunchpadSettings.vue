<template>
  <div class="hl_settings--body">
    <div class="container-fluid">
      <div class="row">
        <div class="hl_settings--main col-lg-12">
          <div class="card">
            <div class="card-header">
              <h3>Launchpad Settings</h3>
            </div>
            <div class="card-body">
              <p
                class="text-center"
                style="
                  display: flex;
                  flex-direction: row;
                  justify-content: center;
                  align-items: center;
                "
              >
                <UICheckbox
                  class="tandc"
                  v-model="company.hideLaunchpad"
                  name="termsofservice"
                  autocomplete="tandc"
                  style="display: inline; margin-right: 5px"
                />
                Disable Launchpad for all my locations.
              </p>
            </div>
            <div class="card-footer">
              <div style="display: inline-block; position: relative">
                <UIButton
                  type="button"
                  use="primary"
                  @click.prevent="disableLaunchpad"
                  :loading="processing"
                >
                  Save
                </UIButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <modal
      v-if="showModal"
      :showCloseIcon="true"
      :noBackdropClose="true"
      :maxWidth="544"
    >
      <div class="card">
        <div class="card-header">
          <div class="d-inline-block align-middle">
            <h5 class="d-inline-block mx-2 --gray">
              <i class="fa fa-check-circle mx-2" aria-hidden="true"></i>
              <span class="capitalize">Confirmation</span>
            </h5>
          </div>
        </div>
        <div class="card-body">
          <p class="text-justify">
            Launchpad is a well thoughtout landing page that is proved to
            increase customer satisfaction and retention by increasing the
            product usage. <br />
            Connecting GMB, FB, Webchat plugin, using mobile app etc have proved
            to be great features that retain the customers for longer.
            <br /><br />
            Are you sure you want to disable Launchpad?
          </p>
        </div>
        <div class="card-footer modal-footer">
          <div style="display: inline-block; position: relative">
            <button
              :class="{ invisible: processing }"
              type="button"
              class="btn btn-sm btn-light mx-2"
              @click.prevent="save"
            >
              Yes
            </button>
            <button
              :class="{ invisible: processing }"
              type="button"
              class="btn btn-sm btn-blue mx-2"
              @click.prevent="greatChoice"
            >
              No
            </button>
            <div
              style="
                position: absolute;
                left: 50%;
                transform: translate(-50%, -50%);
                top: 50%;
              "
              v-show="processing"
            >
              <moon-loader :loading="processing" color="#37ca37" size="30px" />
            </div>
          </div>
        </div>
      </div>
    </modal>
    <modal
      v-if="showSuccessModal"
      :showCloseIcon="false"
      :noBackdropClose="false"
      :maxWidth="400"
    >
      <div class="card">
        <div class="card-body modal-footer">
          <p class="text-center">Great Choice!</p>
        </div>
      </div>
    </modal>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { AuthUser, Company } from '@/models'
import Modal from '@/pmd/components/common/Modal.vue'
import { trackGaEvent } from '@/util/helper'

export default Vue.extend({
  components: {
    Modal,
  },
  data: () => ({
    authUser: {} as AuthUser,
    company: {} as Company,
    processing: false,
    showModal: false,
    showSuccessModal: false,
  }),
  async created() {
    this.authUser = await this.$store.dispatch('auth/get')
    this.company = await Company.getById(this.authUser.companyId)
  },
  methods: {
    async save() {
      this.processing = true
      await this.company.save()
      this.processing = false
      this.showModal = false
      if (!this.company.hideLaunchpad) {
        this.greatChoice()
      }
      trackGaEvent(
        'Launchpad',
        'Hide Launchpad',
        `Click on the hide launchpad settings for company: ${this.company.id}, value: ${this.company.hideLaunchpad}`,
        this.company.hideLaunchpad ? 1 : 0
      )
    },
    async disableLaunchpad() {
      if (!this.company.hideLaunchpad) {
        await this.save()
      } else {
        this.showModal = true
      }
    },
    greatChoice() {
      this.showModal = false
      this.showSuccessModal = true
      this.company.hideLaunchpad = false
      setTimeout(() => {
        this.showSuccessModal = false
      }, 3000)
    },
  },
})
</script>
<style scoped>
.sm-button::before {
  font-family: var(--ff);
  font-weight: 900;
  /* content: attr(data-icon); */
  content: var(--fa);
  font-style: normal;
}
.modal-footer {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
</style>
