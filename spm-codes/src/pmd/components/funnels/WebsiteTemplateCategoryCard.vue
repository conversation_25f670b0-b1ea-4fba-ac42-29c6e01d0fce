<template>
  <div class="website-template-category card">
    <div class="card-body">
      <div class="image" :style="{ backgroundImage: `url(${ category.image })` }">
        <!-- <img v-if="category" :src="category.image" alt="Website Category Image" /> -->
      </div>
      <div class="content">
        <h5>{{ category.name }}</h5>
        <p>{{ categoryDescription }}</p>
        <UIButton use="primary">See Templates</UIButton>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { WebsiteTemplateCategory } from '../../../models'
export default Vue.extend({
  props: {
    category: {
      type: Object as () => WebsiteTemplateCategory,
      required: true
    }
  },
  computed: {
    categoryDescription(): string {
      const { description } = this.category
      const MAX_LENGTH = 75

      if (description.length > MAX_LENGTH) {
        return `${description.substring(0, MAX_LENGTH)}...`
      }

      return description
    }
  }
})
</script>
<style scoped>
.website-template-category {
  cursor: pointer;
  transition: transform 0.25s ease;
}

.website-template-category:hover {
  transform: translateY(-10px);
}

.website-template-category .card-body {
  padding: 0;
}

.website-template-category .image {
  background-color: darkgray;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  width: 100%;
  height: 200px;
  border-radius: 0.3125em 0.3125em 0 0;
}

.website-template-category .content {
  padding: 12px;
  text-align: center;
}

.website-template-category .content p {
  line-height: 1.5em;
  height: 50px;
}

.website-template-category .content button {
  margin-top: 12px;
}
</style>
