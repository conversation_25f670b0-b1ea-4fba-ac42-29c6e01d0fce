<template>
  <div>
    <SideBarV2 v-if="getSideBarVersion === 'v2'"/>
    <SideBar v-else />
    <TopBar />
    <section class="hl_wrapper">
      <section class="hl_wrapper--inner website-templates" id="websiteTemplates">
        <div class="container-fluid">
          <div class="hl_controls">
            <div class="hl_controls--left">
              <h1>{{ templateCategoryTitle }} Template Categories</h1>
            </div>
            <div class="hl_controls--right">
              <UIButton use="primary" @click="createNewCategory()">
                <i class="icon icon-plus mr-2"></i>&nbsp;Create New Category
              </UIButton>
            </div>
          </div>
          <div v-if="fetchingCategories || errorInFetching">
            <moon-loader :loading="fetchingCategories" color="#188bf6" size="30px" />
            <span
              v-if="errorInFetching"
              @click="fetchTemplateCategories()"
            >Error while fetching categories. Click here to retry!</span>
          </div>
          <div v-else>
            <div
              v-if="websiteTemplateCategories.length === 0"
            >No Categories found! Click on Create New Category button to add one.</div>
            <div class="row">
              <div
                class="col-sm-6 col-md-4 col-xl-3"
                v-for="category in websiteTemplateCategories"
                :key="category.id"
              >
                <router-link
                  :to="{name: 'website_template_category_detail', params: {id: category.id}}"
                  tag="div"
                >
                  <WebsiteTemplateCard :category="category" />
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </section>
      <AddNewWebsiteTemplateCategory
        :show-modal="addNewWebsiteTemplateCategoryShown"
        :templateCategoryTitle="templateCategoryTitle"
        @hide="hideNewWebsiteTemplateCategoryModal"
        @newTemplateCategoryDetails="gotNewTemplateCategoryDetails"
      />
    </section>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import TopBar from '../../components/TopBar.vue'
import SideBar from '../../components/agency/SideBar.vue'
import { WebsiteTemplateCategory, User } from '../../../models'
import AddNewWebsiteTemplateCategory from '../../components/agency/AddNewWebsiteTemplateCategory.vue'
import WebsiteTemplateCard from '@/pmd/components/funnels/WebsiteTemplateCategoryCard.vue'
import { WebsiteTemplateCategoryType } from '../../../models/website_template_category'
import SideBarV2 from '../../components/sidebar/SideBar.vue'

let websiteTemplateCategoryListener: () => void

export default Vue.extend({
  components: {
    TopBar,
    SideBar,
    AddNewWebsiteTemplateCategory,
    WebsiteTemplateCard,
    SideBarV2
  },
  data() {
    return {
      fetchingCategories: false,
      errorInFetching: false,
      websiteTemplateCategories: [] as WebsiteTemplateCategory[],
      addNewWebsiteTemplateCategoryShown: false
    }
  },
  beforeDestroy() {
    if (websiteTemplateCategoryListener) {
      websiteTemplateCategoryListener()
    }
  },
  computed: {
    templateType(): string {
      return this.$route.params.type
    },
    templateCategoryTitle(): string {
      switch (this.templateType) {
        case WebsiteTemplateCategoryType.Websites: {
          return 'Website'
        }
        case WebsiteTemplateCategoryType.Funnels: {
          return 'Funnel'
        }
        default: {
          return 'Website'
        }
      }
    },
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  mounted() {
    this.fetchTemplateCategories()
  },
  methods: {
    async fetchTemplateCategories() {
      this.fetchingCategories = true
      this.errorInFetching = false

      try {
        if (websiteTemplateCategoryListener) {
          websiteTemplateCategoryListener()
        }

        websiteTemplateCategoryListener = WebsiteTemplateCategory.getAllRealtime().onSnapshot(
          snapshot => {
            this.websiteTemplateCategories = snapshot.docs
              .map(w => new WebsiteTemplateCategory(w))
              .filter((category: WebsiteTemplateCategory) => {
                const categoryType =
                  category.type || WebsiteTemplateCategoryType.Websites
                return categoryType === this.templateType
              })
              .sort((catA, catB) => catA.name.localeCompare(catB.name))
          }
        )
      } catch (error) {
        console.error(
          'Error while fetching website template categories --> ',
          error
        )
        this.errorInFetching = true
      } finally {
        this.fetchingCategories = false
      }
    },
    createNewCategory() {
      console.log('show popup to create new category')
      this.addNewWebsiteTemplateCategoryShown = true
    },
    hideNewWebsiteTemplateCategoryModal() {
      this.addNewWebsiteTemplateCategoryShown = false
    },
    async gotNewTemplateCategoryDetails(categoryDetails: object) {
      console.log('got new category details --> ', categoryDetails)
      const categoryPayload = {
        ...categoryDetails,
        type: this.templateType || WebsiteTemplateCategoryType.Websites
      }
      try {
        const {
          data: { newCategoryId }
        } = await this.$http.post(
          '/website-template-category/create',
          categoryPayload
        )
        console.log('created new template category --> ', newCategoryId)
      } catch (error) {
        console.error('Error while creating new category --> ', error)
      }
      this.hideNewWebsiteTemplateCategoryModal()
    },
    loadTemplateCategory(categoryId: string) {
      this.$router.push('/')
    }
  },
  watch: {
    templateType() {
      this.fetchTemplateCategories()
    }
  }
})
</script>
