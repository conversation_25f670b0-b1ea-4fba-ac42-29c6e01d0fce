<template>
  <div
    class="hl_tasks--item"
    :class="taskClass"
    v-if="!(hideTask && task.completed)"
  >
    <div class="option">
      <input
        type="checkbox"
        :id="task.id"
        v-model="task.completed"
        @change.prevent="save"
      />
      <label :for="task.id"></label>
    </div>
    <div class="hl_tasks--item-header">
      <h4 style="cursor: pointer" @click.prevent="view">{{ task.title }}</h4>
      <p v-if="taskStatus >= 1 && taskStatus <= 3">{{ dueLateNote }}</p>
    </div>
    <div class="hl_products--item-body">
      <div class="hl_products--item-text">
        <p>{{ task.body }}</p>
      </div>
    </div>
    <div class="hl_tasks--item-footer">
      <p v-if="!contact">Contact: Deleted / Not Found</p>
      <p v-if="contact && contact.fullName">
        Contact:
        <strong>{{ contact.fullName }}</strong>
      </p>
      <p v-if="dueDate">
        Due:
        <strong>{{
          dueDate.format(getCountryDateFormat('month date year, time'))
        }}</strong>
      </p>
      <p v-if="user">
        Assigned:
        <strong>{{ user.fullName }}</strong>
      </p>

      <div class="task-actions">
        <i
          class="icon icon-edit --blue"
          style="margin-right: 10px"
          @click.prevent="editTask"
        ></i>
        <i class="icon icon-trash --gray" @click.prevent="removeTask"></i>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { User, AuthUser, Contact, getCountryDateFormat } from '@/models/'
import Task from '@/models/api/Task'
import moment from 'moment-timezone'
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'
import { UxMessage } from '@/util/ux_message'

export default Vue.extend({
  props: {
    task: Task,
    hideTask: Boolean,
  },
  inject: ['uxmessage'],
  watch: {
    task: async function (task) {
      this.user = undefined
      if (this.task.assignedTo) {
        this.user = await this.$store.dispatch(
          'agencyUsers/syncGet',
          this.task.assignedTo
        )
      }
    },
  },
  data() {
    return {
      user: undefined as User | undefined,
      authUser: {} as AuthUser,
      contact: {} as Contact,
      getCountryDateFormat: getCountryDateFormat,
    }
  },
  async created() {
    if (this.task.assignedTo) {
      this.user = await this.$store.dispatch(
        'agencyUsers/syncGet',
        this.task.assignedTo
      )
    }
    this.authUser = await this.$store.dispatch('auth/get')

    if (!this.$router.currentRoute.params.contact_id && this.task.contactId) {
      this.contact = await this.$store.dispatch(
        'contacts/syncGet',
        this.task.contactId
      )
    }
  },
  computed: {
    ...mapState('user', {
      userData: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    isV2SideBarEnabled() {
      return this.$store.getters['sidebarv2/getVersion'] == 'v2'
    },
    dueDate(): moment {
	  const date = moment(this.task.dueDate)
      return date
    },
    taskStatus(): number {
      if (this.task.completed) return 0
      if (this.dueDate) {
		const dueDate = moment(this.dueDate)
        if (dueDate.isBefore(moment(), 'day')) return 1
        if (dueDate.isSame(moment(), 'day')) return 2
        if (dueDate.subtract(3, 'days').isSameOrBefore(moment(), 'day'))
          return 3
      }
      return 4
    },
    taskClass(): { [key: string]: boolean } {
      const response: { [key: string]: boolean } = {}
      response[
        ['--done', '--late', '--due', '--due', ''][this.taskStatus]
      ] = true
      return response
    },
    dueLateNote(): string {
      if (this.taskStatus === 2) return 'Due today'
      let note = this.taskStatus === 1 ? 'Late ' : 'Due in '
      const days = Math.ceil(
        (this.taskStatus === 1
          ? moment().diff(this.dueDate, 'hours')
          : this.dueDate.diff(moment(), 'hours')) / 24
      )
      note += days
      note += days > 1 ? ' days' : ' day'
      return note
    },
  },
  methods: {
    save() {
      try {
        this.task.save()
      } catch (error) {
        this.uxmessage(UxMessage.errorType(error), true)
      }
    },
    async removeTask() {
      if (confirm('Are you sure you want to delete this task?')) {
        if(this.task.locationId){
          try {
            let resonse = await this.task.delete(this.task.locationId, this.task.id)
            if(resonse.succeded){
              this.$emit('TaskDeleted')
            }
          } catch (error) {
            this.uxmessage(UxMessage.errorType(error), true)
          }
        }else if(this.task.accountId){
          try {
            let resonse = await this.task.delete(this.task.accountId, this.task.id)
            if(resonse.succeded){
              this.$emit('TaskDeleted')
            }
          } catch (error) {
            this.uxmessage(UxMessage.errorType(error), true)
          }
        }
      }
    },
    view() {
      if (!this.task.contactId) {
        this.$router.push({
          name: 'account_detail',
          params: { account_id: this.task.accountId },
        })
      } else {
        this.$router.push({
          name: this.isV2SideBarEnabled ? 'contact_detail-v2' : 'contact_detail',
          params: { contact_id: this.task.contactId },
        })
      }
    },
    editTask() {
      const params = { ...this.$route.params } // make sure to send params to retain record navigation
      if (!this.task.contactId) {
        this.$router.push({
          name: 'account_detail',
          params: { account_id: this.task.accountId, ...params },
          query: Object.assign({}, this.$route.query, {
            task_id: this.task.id,
          }),
        })
      } else {
        this.$router.push({
          name: this.isV2SideBarEnabled ? 'contact_detail-v2' : 'contact_detail',
          params: { contact_id: this.task.contactId, ...params },
          query: Object.assign({}, this.$route.query, {
            task_id: this.task.id,
          }),
        })
      }
    },
  },
})
</script>

<style>
.icon-trash,
.icon-edit {
  cursor: pointer;
}
.task-actions {
  margin-left: auto;
}
</style>
