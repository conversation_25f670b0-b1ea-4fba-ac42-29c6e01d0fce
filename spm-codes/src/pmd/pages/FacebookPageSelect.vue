<template>
  <div class="hl_settings--body" style="width:100%;">
    <div class="container-fluid">
      <div class="row top-buffer">
        <div class="col-sm-2 col-md-2 facebook-icon-wrapper">
          <img
            src="/pmd/img/logo_medallions/fb.svg"
            style="height: 90px;"
            alt="Avatar Name"
          />
        </div>
        <div
          class="col-sm-8 col-md-8 "
          style="background: #ffffff; padding: 25px; border-radius: 4px"
        >
          <div class="row">
            <div class="col-sm-12">
              <h5 style="margin-bottom: 20px">
                Select which facebook page you want to link to this location
              </h5>
            </div>
          </div>
          <span v-if="!processing && facebookPages.length === 0">
            {{ errorMessage || `You don't have any pages under this account.` }}
          </span>
          <select
            v-if="facebookPages.length > 0"
            class="selectpicker"
            v-model="selectedPage"
            ref="facebookPageSelect"
            v-validate="'required'"
            name="facebookPage"
            data-vv-as="Facebook page"
          >
            <option value>Select Page</option>
            <option
              v-for="page in facebookPages"
              v-bind:key="page.id"
              :value="page"
              :selected="page.id == location.facebookPageId"
            >
              {{ page.name }} ({{ page.id }})
            </option>
          </select>
          <span v-show="errors.has('facebookPage')" class="--red">{{
            errors.first('facebookPage')
          }}</span>
        </div>
      </div>
      <div class="row mt-3" v-if="hasInstagramPage">
        <div class="col-sm-8 col-md-8 offset-md-2 offset-sm-2 d-flex align-items-center">
          <div class="icon-wrapper">
            <img
              src="/pmd/img/logo_medallions/insta.svg"
              style="height: 80px"
              alt="Avatar Name"
            />
            <i class="fas fa-exclamation-circle --green ig-icon"></i>
          </div>
          <p class="ml-2">
            Instagram is available for this Account!
          </p>
        </div>
      </div>
      <div class="row top-buffer">
        <div
          class="col-sm-8 col-md-8 offset-sm-2 offset-md-2"
          style="text-align: right"
        >
          <button
            v-if="!processing"
            type="submit"
            class="btn btn-success"
            @click.prevent="set_facebook_page"
          >
            {{ hasInstagramPage ? 'Connect Facebook & Instagram' : 'Connect page!' }}
          </button>
          <div
            v-if="processing"
            style="width: 130px; height: 43px; float: right"
          >
            <moon-loader :loading="processing" color="#37ca37" size="30px" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Location, OAuth2, User } from '@/models'
import { trackGaEvent } from '@/util/helper'

import lodash from 'lodash'

declare var $: any

export default Vue.extend({
  data() {
    return {
      facebookPages: [] as { [key: string]: any }[],
      currentLocationId: '',
      selectedPage: undefined as { [key: string]: any } | undefined,
      location: undefined as Location | undefined,
      processing: false,
      errorMessage: null,
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
    },
  },
  async created() {
    this.processing = true
    this.currentLocationId = this.$router.currentRoute.params.location_id
    const authUser = await this.$store.dispatch('auth/get')
    const user = await User.getById(authUser.userId)

    try {
      if (!this.currentLocationId) {
        this.errorMessage = 'No location id'
        throw new Error('No location id')
      }
      this.location = new Location(
        await this.$store.dispatch('locations/getCurrentLocation', this.currentLocationId)
      )
      const facebookConnection = await OAuth2.getByLocationIdAndTypeOnce(
        this.currentLocationId,
        OAuth2.TYPE_FACEBOOK
      )

      const isSupport = Boolean(this.$route.query.support === 'true')
      const isAdmin = user.isAdmin
      const isTheOwner =
        facebookConnection.ownerId &&
        facebookConnection.ownerId === authUser.userId

      if (!facebookConnection) {
        this.errorMessage = 'No facebook connection'
        throw new Error('No facebook connection')
      } else if (!isAdmin && !isSupport && !isTheOwner) {
        this.errorMessage =
          'Only the user who connected the account can change pages.'
        throw new Error(
          'Only the user who connected the account can change pages.'
        )
      }

      let response = await this.$http.get('/api/facebook/get_pages', {
        params: {
          access_token: facebookConnection.accessToken,
        },
      })

      console.log('Returning pages:', response.data)
      this.facebookPages = response.data
      this.processing = false
      if (this.facebookPages.length === 0) {
        opener.postMessage(
          { actionType: 'close', page: 'integrations_settings' },
          location.origin
        )
      }

      if (this.facebookPages && this.facebookPages.length > 0) {
        this.facebookPages = lodash.orderBy(
          this.facebookPages,
          [page => page.name.toLowerCase()],
          ['asc']
        )
      }
    } catch (err) {
      if (err.message === `Cannot read property 'postMessage' of null`)
        this.$router.push({ name: 'integrations_settings' })
      console.error(err)
      opener.postMessage(
        { actionType: 'close', page: 'integrations_settings' },
        location.origin
      )
    } finally {
      this.processing = false
    }
  },
  methods: {
    async set_facebook_page(event: any) {
      if (!this.currentLocationId || !this.selectedPage) return

      const result = await this.$validator.validateAll()
      if (!result) {
        return false
      }

      this.processing = true

      try {
        let response = await this.$http.post('/facebook/linkPage', {
          location_id: this.currentLocationId,
          page_id: this.selectedPage.id,
          page_token: this.selectedPage.access_token,
          page_name: this.selectedPage.name,
          page_link: this.selectedPage.link,
          instagram_page: this.selectedPage.instagram_business_account
        })

        trackGaEvent(
          'Integration_Settings',
          'Successfully Facebook connected',
          `Successfully Facebook connected for location: ${this.currentLocationId}`
        )

        opener.postMessage(
          { actionType: 'close', page: 'integrations_settings' },
          location.origin
        )
        window.close()
      } catch (err) {
        if (
          err.response &&
          err.response.data &&
          err.response.data.error == 'manage_pages_error'
        ) {
          // Redirect to integrations page after warning that we need Manage Pages perm
          console.log('Manage pages error')
          this.$uxMessage('confirmation', err.response.data.message, res => {
            if (res === 'ok') {
              this.$router.push({ name: 'integrations_settings' })
            }
          })
        }

        // Sends user back to integrations when he does not come from facebook
        if (err.message === `Cannot read property 'postMessage' of null`)
          this.$router.push({ name: 'integrations_settings' })
      } finally {
        this.processing = false
      }
    },
  },
  computed: {
    hasInstagramPage() {
      return Boolean(
        this.selectedPage && this.selectedPage.instagram_business_account
      )
    }
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
})
</script>

<style scoped>
.facebook-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.icon-wrapper {
  position: relative;
}
.ig-icon {
  position: absolute;
  right: 0px;
  font-size: 22px;
  background: #fff;
  border-radius: 50%;
}
</style>
