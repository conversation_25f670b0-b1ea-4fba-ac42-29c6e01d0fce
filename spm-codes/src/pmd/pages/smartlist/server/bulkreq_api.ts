// prettier-ignore
import axios from 'axios'
import {
  IOpSpecs,
  IBulkReq,
  IRecordOperationLog,
  ISprintSchedule,
  IBulkEmailSpecsUIOnly,
} from './bulkreq_interfaces'
import { IContactsQuery } from './contacts_server'
import { IBulkAction, StatusCheck } from '../vm/action_vm'
import store from '@/store'
import { Location } from '@/models'
import config from '@/config'
import moment from 'moment-timezone'
import { BulkRequestV2 } from './bulk_req_v2'
import { User } from '../../../../models'
import { omit as lodashOmit } from 'lodash'
import { IEventStats } from '../../../../models/evenstats'

export const ScheduledBulkActionsURL = `${config.bulkReqURL}/bulk_request`
export const EventStatsURL = `${config.emailReportingUrl}/agent_reporting`
export class ScheduledBulkActions {
  public static async getList(params: {
    status?: any
    timezone?: string
    from?: string
    to?: string
    page?: number
    page_size?: number
    opSource?: string,
    opType?:string,
    [key:string]: any
  }) {
    const currentUser = new User(await store.dispatch('user/get'))
    const currentLocation = await new Location(
      store.getters['locations/getCurrentLocation']
    )
    if (!currentLocation) return
    //if (!params.opSource) params.opSource = 'smartlist';
    // only send user id if it is a assigned to user
    const response = await axios.post(
      `${ScheduledBulkActionsURL}/operations/`,
      {
        locationId: currentLocation.id,
        ...params,
      })
    if (response.status === 200) {
      const items = response.data
      if (items && items.length > 0) {
        return items.map(a => new BulkRequestV2(a))
      }
    }
    return []
  }

  public static async getItemDetails(params: {
    bulkReqId?: any
  }): Promise<BulkRequestV2|null>{
    if (!params || !params.bulkReqId) return null;
    //if (!params.opSource) params.opSource = 'smartlist';
    // only send user id if it is a assigned to user
    const response = await axios.post(
      `${ScheduledBulkActionsURL}/details/${params.bulkReqId}/`)
    if (response.status === 200 && response.data) {
        return new BulkRequestV2(response.data)
    }
    return null
  }

  public static async getRecentItems(params: {
    locationId: any,
    userId?:any,
    limit?: number,
  }): Promise<BulkRequestV2[]|null>{
    if (!params || !params.locationId) return null;
    // console.log(`recent bulk actions fetch for - ${params.locationId}`)
    const response = await axios.post(
      `${ScheduledBulkActionsURL}/recentitems/${params.locationId}`, params)
    if (response.status === 200) {
      const items = response.data
      if (items && items.length > 0) {
        return items.map(a => new BulkRequestV2(a))
      }
    }
    return []
  }

  public static async getSMSEventStats(
    request: BulkRequestV2
  ): Promise<IEventStats | null> {
    // console.log('get event stats')
    if (!request) return null
    if (request.operationType !== 'bulk-sms') return null

    const currentUser = new User(await store.dispatch('user/get'))
    const currentLocation = await new Location(
      store.getters['locations/getCurrentLocation']
    )
    if (!currentLocation) return
    //if (!params.opSource) params.opSource = 'smartlist';
    // only send user id if it is a assigned to user
    const response = await axios.post(
      `${EventStatsURL}/sms_stats/bulk_request/`,
      {
        user_id:
          currentUser && currentUser.isAssignedTo ? currentUser.id : null,
        bulk_req_id: request.id,
        location_id: currentLocation.id,
      }
    )
    if (response.status === 200) {
      const items = response.data.response
      if (items && items.length > 0) {
        // console.log(items[0])
        return items[0]
      }
    }
    return null
  }

  public static async getEmailEventStats(
    request: BulkRequestV2
  ): Promise<IEventStats | null> {
    // console.log('get event stats')
    if (!request) return null
    if (request.operationType !== 'bulk-email') return null

    const currentUser = new User(await store.dispatch('user/get'))
    const currentLocation = await new Location(
      store.getters['locations/getCurrentLocation']
    )
    if (!currentLocation) return
    //if (!params.opSource) params.opSource = 'smartlist';
    // only send user id if it is a assigned to user
    const response = await axios.post(
      `${EventStatsURL}/email_stats/bulk_request/`,
      {
        location_id: currentLocation.id,
        bulk_req_id: request.id,
      }
    )
    if (response.status === 200) {
      const items = response.data.response
      if (items && items.length > 0) {
        // console.log(items[0])
        return items[0]
      }
    }
    return null
  }

  public static async action(params: {
    action: string
    bulkRequestId: string
  }) {
    const response = await axios.get(
      `${ScheduledBulkActionsURL}/action/${params.action}/${params.bulkRequestId}`,
      params
    )
    if (response.status === 200) {
      return 'Action scheduled'
    } else {
      console.log(response)
      return 'Action not scheduled'
    }
  }

  public static async pause(bulkRequestId: string) {
    return await ScheduledBulkActions.action({ action: 'pause', bulkRequestId })
  }

  public static async resume(bulkRequestId: string) {
    return await ScheduledBulkActions.action({
      action: 'resume',
      bulkRequestId,
    })
  }

  public static async cancel(bulkRequestId: string) {
    return await ScheduledBulkActions.action({
      action: 'cancel',
      bulkRequestId,
    })
  }
}

export class ScheduledBulkAction implements IBulkAction {
  private query: IContactsQuery
  private recordIds: string[]
  private locationId: string
  public name: string
  public totalContacts: Number
  public status: StatusCheck
  public actionSpecs: IOpSpecs
  private userName: string
  private userId: string
  protected taskFinishCallback: (status: string) => {}

  private constructor() {
    //if (!this.apiURL || this.apiURL.trim() === '' ) throw new Error('Specify right url to schedule the operation');
    //this.apiURL = config.bulkReqUrl;
  }

  public static async forQuery(
    specs: IOpSpecs,
    query: IContactsQuery,
    keepDynamicList: boolean = false
  ) {
    if (!specs) throw new Error('Specs required')
    if (!query) throw new Error('Provide a query to operate upon')
    if (!query.filters) throw new Error('Provide filters for query')
    const s = new ScheduledBulkAction()
    s.actionSpecs = specs
    const location = new Location(store.getters['locations/getCurrentLocation'])
    const currentUser = new User(await store.dispatch('user/get'))
    s.locationId = location.id
    s.query = query
    // keepDynamicList = true
    if (keepDynamicList === true) {
      s.query.sort = [
        { fieldName: 'date_added', direction: 'asc', isCustomField: false },
      ]
    }
    if (currentUser) {
      s.userName = currentUser.name
      s.userId = currentUser.id
    }
    return s
  }

  public static async forSelectedRecords(specs: IOpSpecs, recordIds: string[]) {
    if (!specs) throw new Error('Specs required')
    if (!recordIds || recordIds.length <= 0)
      throw new Error('Provide list of records for operation')
    const s = new ScheduledBulkAction()
    s.actionSpecs = specs
    const location = new Location(store.getters['locations/getCurrentLocation'])
    const currentUser = new User(await store.dispatch('user/get'))
    s.locationId = location.id
    s.recordIds = recordIds
    if (currentUser) {
      s.userName = currentUser.name
      s.userId = currentUser.id
    }
    //s.query.sort = [{fieldName: "date_added", direction: "asc", isCustomField: false}];
    return s
  }

  public static async justAuditTrail(opType: 'bulk-export' | 'bulk-merge-contact', opSpecs: any) {
    try {
      const s = new ScheduledBulkAction()
      const location = new Location(store.getters['locations/getCurrentLocation'])
      const currentUser = new User(await store.dispatch('user/get'))
      s.locationId = location.id
      s.recordIds = []
      s.actionSpecs = { opType, ...opSpecs, name: 'Export', note:'Export'}
      if (currentUser) {
        s.userName = currentUser.name
        s.userId = currentUser.id
      }
      //s.query.sort = [{fieldName: "date_added", direction: "asc", isCustomField: false}];
      s.schedule({
        byUserId: s?.userId,
        byUserName: s?.userName,
        locationId: s.locationId,
        opSource: "smartlist",
        opSpecs: s.actionSpecs,
        processingType: 'just-audit'
      })
    } catch(err){
      console.log(err);
    }
  }

  public async action(
    userId: string,
    params?: {
      opSpecs?: IOpSpecs
      sprintSchedule?: ISprintSchedule
      useDynamicList?: boolean
    },
    taskFinishCallback?: (status: string) => {}
  ): Promise<Boolean> {
    if (!params && !params.opSpecs) throw new Error('Operation specs required')
    this.taskFinishCallback = taskFinishCallback
    console.log(this.userName === userId)
    console.log(this.userName)
    try {
      if (params.opSpecs && params.opSpecs.opType === 'bulk-email') {
        const emSpecs = params.opSpecs as IBulkEmailSpecsUIOnly
        if (emSpecs.from || emSpecs.name) {
          emSpecs.from = `${emSpecs.name} <${emSpecs.from}>`
          params.opSpecs = emSpecs
        }
      }
      const reqPayload: IBulkReq = {
        locationId: this.locationId,
        byUserId: userId,
        byUserName: userId === this.userId ? this.userName : 'unknown',
        filters: this.query,
        recordIds: this.recordIds,
        sprintSchedule: params.sprintSchedule,
        opSpecs: params.opSpecs,
      }
      // params.useDynamicList = true;
      if (!reqPayload.recordIds && !params.useDynamicList) {
        reqPayload.freezeList = true
      }
      const result = await this.schedule(reqPayload)
      console.log(`Scheduled Bulk Operation ${result}`)
      this.markAsComplete('success')
    } catch (error) {
      console.log(error)
      if (error.message)
        this.markAsComplete(`Operation not scheduled. ${error.message}`)
      else this.markAsComplete(`Operation not scheduled. ${error}`)
    }
    // finally { // let the exception buble up to calling code.
    //     // this.unsubscribeImportRequest();
    //     // this.unsubscribeImportRequest = null;
    // }
    return true
  }

  protected markAsComplete(status: string) {
    if (this.taskFinishCallback) {
      this.taskFinishCallback(status)
      this.taskFinishCallback = null
    }
  }

  // tslint:disable-next-line: no-shadowed-variable
  public async schedule(req: IBulkReq): Promise<number> {
    if (!req) throw new Error('Provide payload to schedule bulk request')

    const isJustForAudit = req.opSpecs && (req.opSpecs.opType === 'bulk-merge-contact' || req.opSpecs.opType === 'bulk-export');

    if (!isJustForAudit && !req.filters && !req.recordIds)
      throw new Error('Provide list of record ids or give smart list query')

    //console.log('in schedule')
    if (!req.opSpecs) throw new Error('Provide specs for bulk request')
    try {
      console.log('Calling - ' + ScheduledBulkActionsURL)
      if (!req.opSource) req.opSource = 'smartlist'
      const response = await axios.post(ScheduledBulkActionsURL + '/', req)
      if (!response) {
        return Promise.reject('No response. Error saving bulk request')
      }
      if (response.status !== 200) {
        //console.log(response);
        return Promise.reject(response)
      } else return response.data
    } catch (error) {
      // console.log('in schedule error')
      // console.log(error.response)
      //console.log(error.response.data)
      if (error.response)
        throw new Error(
          error.response.data
            ? error.response.data.error || error.response.data
            : error
        )
      else throw new Error(error)
    }
  }

  public static async changeSchedule(params: {
    bulkRequestId: string
    sprintSchedule: ISprintSchedule | null
  }): Promise<number> {
    if (!params || !params.bulkRequestId)
      throw new Error('Provide payload to reschedule bulk request')
    if (!params.sprintSchedule) throw new Error('Specify changed schedule')

    try {
      // console.log('Calling - ' + ScheduledBulkActionsURL)
      const response = await axios.post(
        ScheduledBulkActionsURL + '/change_schedule',
        params
      )
      if (!response) {
        return Promise.reject('No response. Error changing schedule')
      }
      if (response.status !== 200) {
        //console.log(response);
        Promise.reject(response)
      } else return response.data
    } catch (error) {
      // console.log('in schedule error')
      // console.log(error.response)
      //console.log(error.response.data)
      if (error.response)
        throw new Error(
          error.response.data
            ? error.response.data.error || error.response.data
            : error
        )
      else throw new Error(error)
    }
  }

  public static async changeSprintLimit(params: {
    bulkRequestId:string,
    sprintLimit: number,
  }): Promise<number> {
    if (!params || !params.bulkRequestId)
      throw new Error('Provide payload to change batch quantity for bulk request')
    try {
      // console.log('Calling - ' + ScheduledBulkActionsURL)
      const response = await axios.post(
        ScheduledBulkActionsURL + '/change_sprint_limit',
        params
      )
      if (!response) {
        return Promise.reject('No response. Error changing schedule')
      }
      if (response.status !== 200) {
        //console.log(response);
        Promise.reject(response)
      } else return response.data
    } catch (error) {
      // console.log('in schedule error')
      // console.log(error.response)
      //console.log(error.response.data)
      if (error.response)
        throw new Error(
          error.response.data
            ? error.response.data.error || error.response.data
            : error
        )
      else throw new Error(error)
    }
  }
}

export class ContactBulkAction {
  _acd = null

  public get bulkReqId(): string {
    return this.data && this.data.bulkReqId
  }

  public get batchId(): string {
    return this.data && this.data.batchId
  }

  public get type(): string {
    return (this.data && this.data.actionType) || 'bulk'
  }

  public get action(): string {
    return (this.data && this.data.opDesc) || ''
  }

  public get contactId(): string {
    return this.data && this.data.recordId
  }

  public get name(): string {
    return this.data && this.data.name
  }

  public get email(): string {
    return this.data && this.data.email
  }

  public get phone(): string {
    return this.data && this.data.phone
  }

  public get date(): moment.Moment {
    if (this.data && this.data.actionDate && !this._acd) {
      return moment(this.data.actionDate)
    } else return null
  }

  public get status(): string {
    return this.data && this.data.status
  }

  public get error(): string {
    return this.data && this.data.error
  }

  private constructor(private data: IRecordOperationLog) {}

  public static async getContactHistory(
    contactId: string,
    page?: number
  ): Promise<{ items: ContactBulkAction[]; [key: string]: any }> {
    if (!contactId) throw new Error('Provide contact details to fetch history')

    const location = new Location(store.getters['locations/getCurrentLocation'])
    const response = await axios.get(
      `${ScheduledBulkActionsURL}/location/${
        location.id
      }/contact/${contactId}/${page || ''}`
    )
    console.log(response)
    if (!response) {
      return Promise.reject('No response. Error fetching contacts for query')
    }
    if (response.status !== 200) {
      //console.log(response);
      return Promise.reject(response.data)
    }
    const result = response && response.data
    console.log(result)
    if (result && result.items)
      return {
        items: result.items.map(a => new ContactBulkAction(a)),
        ...lodashOmit(result, ['items']),
      }
    else return { items: [] }
  }

  public static async getActionHistory(
    bulReqId: string,
    params: {
      locationId?: string
      showSuccess?: boolean | null
      showError?: boolean | null
      page?: number | null
      pageSize?: number | null
    }
  ): Promise<{ items: ContactBulkAction[]; [key: string]: any }> {
    if (!bulReqId)
      throw new Error('Provide bulk action details to fetch history')

    const location = new Location(store.getters['locations/getCurrentLocation'])

    const dto: { [key: string]: any } = {}
    if (params) {
      if (params.locationId) dto.location_id = params.locationId
      if (params.showSuccess) dto.show_success = params.showSuccess
      if (params.showError) dto.show_error = params.showError
      if (params.page) dto.page = params.page
      if (params.pageSize) dto.page_size = params.pageSize
    }
    ///${bulReqId}/${(params && params.page) || ''}/${(params && params.pageSize) || ''}
    const response = await axios.post(
      `${ScheduledBulkActionsURL}/${bulReqId}/`,
      dto
    )
    console.log(response)
    if (!response) {
      return Promise.reject('No response. Error fetching contacts for query')
    }
    if (response.status !== 200) {
      //console.log(response);
      return Promise.reject(response.data)
    }

    const result = response && response.data
    console.log(result)
    if (result && result.items)
      return {
        items: result.items.map(a => new ContactBulkAction(a)),
        ...lodashOmit(result, ['items']),
      }
    else return { items: [] }
  }
}
