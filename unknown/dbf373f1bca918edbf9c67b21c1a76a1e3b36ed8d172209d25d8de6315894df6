<template>
  <div>
    <div class="form-group">
      <label v-if="showTitles">Select Pipeline</label>
      <div class="form-input-dropdown dropdown">
        <div data-toggle="dropdown">
          <i class="icon icon-arrow-down-1"></i>
          <input
            type="text"
            class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
            placeholder="Select pipeline"
            :value="pipelineName"
            v-validate="'required'"
            name="pipeline"
          />
         <span v-if="allowValidations && !pipelineName"
              class="error"> * Pipeline required
          </span>

        </div>
        <div class="dropdown-menu">
          <a
            class="dropdown-item trigger-type"
            href="javascript:void(0);"
            v-for="pipeline in pipelines"
            :key="pipeline.id"
            @click.prevent="setValue('pipeline_id', pipeline.id)"
          >
            <p>{{ pipeline.name }}</p>
          </a>
        </div>
      </div>
    </div>
    <div class="form-group">
      <label v-if="showTitles">Select Stage</label>
      <div class="form-input-dropdown dropdown">
        <div data-toggle="dropdown">
          <i class="icon icon-arrow-down-1"></i>
          <input
            type="text"
            class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
            placeholder="Select stage"
            :value="pipelineStage"
            v-validate="'required'"
            name="stage"/>
         <span v-if="allowValidations && !pipelineStage"
              class="error"> * Stage required
          </span>
        </div>
        <div class="dropdown-menu">
          <a
            class="dropdown-item trigger-type"
            href="javascript:void(0);"
            v-for="stage in stages"
            :key="stage.id"
            @click.prevent="setValue('pipeline_stage_id', stage.id)">
            <p>{{ stage.name }}</p>
          </a>
        </div>
      </div>
    </div>
    <div class="form-group">
      <UITextLabel v-if="showTitles">Opportunity Name</UITextLabel>
      <UITextInputGroup
        type="text"
        placeholder="Opportunity Name"
        :value="opportunityName"
        v-validate="'handlebars'"
        data-vv-validate-on="input"
        name="opportunityName"
        @input="setValue('opportunity_name', $event)"
        :error="errors.has('opportunityName')"
        :errorMsg="errors.first('opportunityName')"/>
    </div>
    <div class="form-group">
      <UITextLabel v-if="showTitles">Opportunity Source</UITextLabel>
      <UITextInputGroup
        type="text"
        placeholder="Opportunity Source"
        v-validate="'handlebars'"
        data-vv-validate-on="input"
        name="opportunitySource"
        :value="opportunitySource"
        @input="setValue('opportunity_source', $event)"
        :error="errors.has('opportunitySource')"
        :errorMsg="errors.first('opportunitySource')"
      />
    </div>
    <div class="form-group">
      <label v-if="showTitles">Lead Value</label>
      <div class="form-input-dropdown dropdown">
        <div data-toggle="dropdown">
          <i class="icon icon-arrow-down-1"></i>
          <input
            type="text"
            class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
            placeholder="Lead value"
            :value="leadValue"
            v-validate="'handlebars'"
            data-vv-validate-on="input"
            name="leadValue"
            @input="setValue('monetary_value', $event.target.value)"
            @keypress="checkOpportunityValue"
          />
          <span v-show="errors.has('leadValue')" class="--red">{{errors.first('leadValue')}}</span>
        </div>
        <div class="dropdown-menu">
          <a
            class="dropdown-item trigger-type"
            href="javascript:void(0);"
            v-for="customField in leadValueOption"
            :key="customField.id"
            @click.prevent="
              setValue(
                'monetary_value',
                customField.model === 'contact'
                  ? `contact.${customField.id}`
                  : `custom_values.${customField.id}`
              )
            "
          >
            <p>
              {{
                customField.name.charAt(0).toUpperCase() +
                  customField.name.slice(1).toLowerCase()
              }}
            </p>
          </a>
        </div>
      </div>
    </div>
    <div class="form-group">
      <UITextLabel v-if="showTitles">Opportunity Status</UITextLabel>
      <div class="form-input-dropdown dropdown">
        <i class="icon icon-arrow-down-1"></i>
        <input
          type="text"
          class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
          data-toggle="dropdown"
          placeholder="Select status"
          :value="selectedStatus"
        />
        <div class="dropdown-menu">
          <a
            v-if="selectedStatus"
            class="dropdown-item trigger-type"
            href="javascript:void(0);"
            @click.prevent="removeValue('opportunity_status')"
          >
            <span style="color: #e03131;">Remove status</span>
          </a>
          <a
            v-if="selectedStatus"
            class="dropdown-item trigger-type"
            href="javascript:void(0);"
            @click.prevent="removeValue('opportunity_status')"
          >
            <span style="color: #e03131;">Remove status</span>
          </a>
          <a
            class="dropdown-item trigger-type"
            href="javascript:void(0);"
            v-for="opportunityStatus in status"
            :key="opportunityStatus"
            @click.prevent="setValue('opportunity_status', opportunityStatus)"
          >
            <p>{{ opportunityStatus | toTitleCase }}</p>
          </a>
        </div>
      </div>
    </div>
    <div class="form-group">
      <div class="d-flex w-100 mb-2" v-if="!hideAllowBackward">
        <div class="col-toggle pr-2">
          <div class="toggle">
            <UIToggle
              :id="`allow-backward-pipeline-tgl-${toggleIdentifier}`"
              v-model="allowBackward"
              @change="setValue('allow_backward', $event)"
            />
            <label class="tgl-btn" :for="`allow-backward-pipeline-tgl-${toggleIdentifier}`"></label>
          </div>
        </div>
        <div class="col-content pl-2">
          Allow opportunity to move to any previous stage in pipeline
        </div>
      </div>
      <div class="d-flex w-100" v-if="!hideAllowDuplicates">
        <div class="col-toggle pr-2">
          <div class="toggle">
            <UIToggle
              :id="`allow-multiple-opportunities-tgl-${toggleIdentifier}`"
              v-model="allowMultiple"
              @change="setValue('allow_multiple', $event)"/>
            <label class="tgl-btn" :for="`allow-multiple-opportunities-tgl-${toggleIdentifier}`"></label>
          </div>
        </div>
        <div class="col-content pl-2">
          Allow duplicate opportunities
        </div>
        <span class="ml-2" v-b-tooltip.hover title="This allows the system to create multiple opportunities for the same contact on the same pipeline.">
          <i class="fas fa-question-circle"></i>
        </span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Pipeline, Stage, Status, CustomField, CustomValue } from '@/models'

export default Vue.extend({
  props: ['action', 'hideAllowBackward', 'showTitles', 'hideAllowDuplicates'],
  inject: ["parentValidator"],
  data() {
    return {
      currentLocationId: '',
      allowBackward: false,
      allowMultiple: false,
      status: [] as { [key: string]: any },
      customFields: [] as CustomField[],
      customValues: [] as CustomValue[],
      leadValueOption: [] as { [key: string]: any }[],
      toggleIdentifier: 0,
      allowValidations: false
    }
  },
  computed: {
    pipelineName(): string {
      const pipeline = lodash.find(this.pipelines, {
        id: this.action.pipeline_id
      })
      if (pipeline) return pipeline.name
      return ''
    },
    pipelineStage(): string {
      const pipeline = lodash.find(this.pipelines, {
        id: this.action.pipeline_id
      })
      if (pipeline) {
        const stage = lodash.find(pipeline.stages, {
          id: this.action.pipeline_stage_id
        })
        if (stage) return stage.name
      }
      return ''
    },
    leadValue() {
      if (this.action && this.action.monetary_value) {
        if (this.action.monetary_value.toString().includes('contact')) {
          let customField = lodash.find(this.leadValueOption, {
            id: this.action.monetary_value.substr(8)
          })
          if (customField) return lodash.upperFirst(customField.name)
        } else if (
          this.action.monetary_value.toString().includes('custom_values')
        ) {
          let customValue = lodash.find(this.leadValueOption, {
            id: this.action.monetary_value.substr(14)
          })
          if (customValue) return lodash.upperFirst(customValue.name)
        } else {
          return this.action.monetary_value
        }
      }
      return ''
    },
    opportunityName(): string {
      if (this.action) return this.action.opportunity_name
      return ''
    },
    opportunitySource(): string {
      if (this.action) return this.action.opportunity_source
      return ''
    },
    pipelines() {
      return this.$store.state.pipelines.pipelines
    },
    stages(): Stage[] {
      const pipeline = lodash.find(this.pipelines, {
        id: this.action.pipeline_id
      })
      if (pipeline) return pipeline.stages
      return []
    },
    selectedStatus(): string {
      if (this.action.opportunity_status)
        return lodash.startCase(this.action.opportunity_status)
      return ''
    }
  },
  watch: {
    '$route.params.location_id': async function(id) {
      await this.$store.dispatch('pipelines/syncAll', id)
    }
  },
  async created() {
    await this.$store.dispatch('pipelines/syncAll', this.$route.params.location_id)
    if(this.parentValidator) {
      this.allowValidations = true;
      this.$validator = this.parentValidator;
    }
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.allowBackward = this.action.allow_backward
    this.allowMultiple = this.action.allow_multiple
    this.status = Object.values(Status)
    await CustomField.getByLocationId(this.currentLocationId)
      .get()
      .then((querySnapshot: any) => {
        this.customFields = querySnapshot.docs.map(
          (d: any) => new CustomField(d)
        )
      })
    this.leadValueOption = this.customFields.filter(c => {
      if (c.dataType === 'NUMERICAL' || c.dataType === 'MONETORY') {
        return c
      }
    })
    this.customValues = await CustomValue.getByLocationId(
      this.currentLocationId
    )
    this.customValues.map(c => {
      if (!isNaN(c.value)) this.leadValueOption.push(c)
    })
    this.toggleIdentifier = Math.random() // Used for multiple toggles on the same trigger
  },
  methods: {
    setValue(field: string, value: string) {
      if (field == 'pipeline_id' && this.action[field] !== value)
        Vue.delete(this.action, 'pipeline_stage_id')
      Vue.set(this.action, field, value)
      this.$emit('update:action', this.action)
    },
    checkOpportunityValue(event) {
      if (event.key === ',') {
        event.preventDefault()
      }
    },
    removeValue(field: string) {
      Vue.delete(this.action, field)
      this.$emit('update:action', this.action)
    }
  }
})
</script>
