<template>
  <div class="tab-pane fade show active" id="overview-tab" role="tabpanel" aria-labelledby="overview-tab">
    <div v-if="fetchingFunnelPages" class="hl_funnels--overview">
      <moon-loader :loading="fetchingFunnelPages" color="#188bf6" size="30px"/>
    </div>
    <div v-else class="hl_funnels--overview">
      <div v-if="funnelDomain" class="input-group">
        <div class="input-group-prepend">
          <router-link
            :to="{name: 'funnel_step_publish', params: { step_id: stepId, funnel_id: funnelId, location_id: locationId }}"
            tag="a"
            class="btn btn-light3"
            data-tooltip="tooltip"
            data-placement="top"
            title="Funnel Step Settings"
            data-original-title="Funnel Step Settings"
          >
            <i class="icon icon-settings-1"></i>
          </router-link>
        </div>
        <input readonly aria-readonly="" type="text" class="form-control" :value="funnelStepUrl">
        <div class="input-group-append">
          <a :href="funnelStepUrl" target="_blank" class="btn btn-light3" data-tooltip="tooltip" data-placement="top" title="Visit Funnel URL">
            <i class="icon icon-share-2"></i>
          </a>
        </div>
      </div>
      <div v-else>
        Please add a domain in the settings to see your Funnel live!
      </div>
      <FunnelPagesInStepOverview
        :funnel="funnel"
        :funnel-domain="funnelDomain"
        :funnel-step="funnelStep"
        :funnel-pages="funnelPages"
        :step-id="stepId"
        :is497-plan="is497Plan"
        @removePageFromStep="removePageFromStep"
        @addVariationPageToStep="addVariationPageToStep"
      />
      <hr>
      <div class="hl_funnels--overview-footer">
        <div class="overview-footer-left">
          <!-- <button disabled type="button" class="btn btn-light3 btn-sm">
            <i class="icon icon-close"></i> Remove From Funnel
          </button> -->
        </div>
        <div class="overview-footer-right space-x-2 mt-4">
          <UIButton @click="askDeleteConfirmation()" type="button" use="danger">
            <i class="icon icon-trash mr-2"></i> Delete Funnel Step
          </UIButton>
          <UIButton :disabled="cloningFunnelStep" @click="cloneFunnelStep()" type="button" use="outline">
            <i class="icon icon-duplicate mr-2"></i> {{ cloningFunnelStep ? 'Cloning' : 'Clone Funnel Step' }}
          </UIButton>
        </div>
      </div>
    </div>
    <ConfirmDeleteModal
      :showModal="showDeleteModal"
      @showDeleteModal="val => showDeleteModal = val"
      @hidden="showDeleteModal = false"
      @delete="deleteFunnelStep"
    />

    <ArchivedPages
      v-if="archivedPages.length && !fetchingFunnelPages && is497Plan"
      :pages="archivedPages"
      @restorePage="restorePage"
    />
  </div>

</template>
<script lang="ts">
import Vue from 'vue'
import { FunnelPage } from '../../../models'
import FunnelPagesInStepOverview from './FunnelPagesInStepOverview.vue'
import ConfirmDeleteModal from '../../components/funnels/ConfirmDeleteModal.vue'
import ArchivedPages from './ArchivedPages.vue'
import {
  generateFunnelPageUrl,
  getUniqueFunnelUrl,
  getValidUrl
} from '@/util/helper'
import lodash from 'lodash'

export default Vue.extend({
  components: {
    FunnelPagesInStepOverview,
    ConfirmDeleteModal,
    ArchivedPages
  },
  props: {
    funnel: {
      type: Object
    },
    funnelDomain: {
      type: String
    },
    funnelStep: {
      type: Object
    },
    stepId: {
      type: String
    },
    funnelId: {
      type: String
    },
    locationId: {
      type: String
    },
    cloningFunnelStep: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    const { pages } = this.funnelStep;
    this.fetchFunnelPages(pages);
    if(this.is497Plan) this.fetchArchivedPages();
  },

  computed: {
    funnelStepUrl(): string {
      const url = `${this.funnelDomain}${this.funnelStep.url}`
      return getValidUrl(encodeURIComponent(url))
    },
    is497Plan() {
      if (
        this.$store.state.company &&
        this.$store.state.company.company &&
        this.$store.state.company.company.stripe_active_plan &&
        this.$store.state.company.company.stripe_active_plan.includes('497')
      ) {
        return true;
      }
      return false;
    }
  },
  data() {
    return {
      funnelPages: [] as FunnelPage[],
      fetchingFunnelPages: true,
      showDeleteModal: false,
      archivedPages: [] as FunnelPage[],
      // cloningFunnelStep: false
    }
  },
  methods: {
    askDeleteConfirmation(){
      this.showDeleteModal = true;
    },
    deleteFunnelStep() {
      this.$emit('deleteFunnelStep', this.funnelStep)
      this.showDeleteModal = false
    },
    cloneFunnelStep() {
      // console.log('have to clone the entire step and all pages in it')
      // this.cloningFunnelStep = true
      // const payload = {
      //   step: this.funnelStep,
      //   pages: this.funnelPages
      // }

      // const payload = {
      //   stepI
      // }

      this.$emit('cloneFunnelStep', this.funnelStep.id)
    },
    fetchFunnelPages(pages: string[]) {
      this.fetchingFunnelPages = true

      const funnelPageRequests = pages.map(p => FunnelPage.getById(p))

      Promise.all(funnelPageRequests)
      .then((funnelPages: FunnelPage[]) => {
        // console.log('funnel pages --> ', funnelPages)
        this.funnelPages = funnelPages
      })
      .catch((err) => console.error(err))
      .finally(() => this.fetchingFunnelPages = false)
    },
    fetchArchivedPages(){
      FunnelPage.getAllByFunnelId(this.funnel.id, this.funnel.locationId)
        .then(pages => {
          this.archivedPages = lodash.orderBy(
            lodash.filter(pages, x => x.stepId === this.stepId && x.deleted === true),
              ['dateUpdated'],['desc']
          )
        })
        .catch((err) => console.error(err))
    },
    removePageFromStep(payload: Object) {
      this.$emit('removePageFromStep', payload)
    },
    addVariationPageToStep(payload: Object) {
      this.$emit('addFunnelPageToStep', payload)
    },
    async restorePage(page) {
      if (this.funnelStep.split) {
        alert('Cannot restore a page when a split test is running')
        return false
      }

      if (this.funnelStep.pages.length > 1) {
        alert('Cannot restore a page when a variation already exists')
        return false
      }

      const url = await getUniqueFunnelUrl(this.funnelDomain, page.url)
      page.url = await generateFunnelPageUrl(
        page.name,
        url,
        page.templateType
      )
      page.deleted = false
      await page.save()
      setTimeout(async () => {
        this.$emit('addFunnelPageToStep', {
          page,
          stepId: this.stepId
        })
      }, 50)
    }
  },
  watch: {
    // stepId: function() {
    //   // this.cloningFunnelStep = false;
    // },
    'funnelStep.pages': function(newValue) {
      // const { pages } = newValue
      this.fetchFunnelPages(newValue)
      if(this.is497Plan) this.fetchArchivedPages();
    }
  }
})
</script>
