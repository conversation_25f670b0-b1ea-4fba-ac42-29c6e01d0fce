<template>
  <Filters
    :conditions="conditions"
    :filterMaster="filterMaster"
    v-on:update:conditions="$emit('update:conditions', $event)"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import { Campaign } from '@/models';
import { Condition } from '@/models/trigger';
const Filters = () => import( './Filters.vue');

export default Vue.extend({
  props: ['conditions'],
  components: { Filters },
  data() {
    return {
      filterMaster: [
        {
          placeHolder: 'Select Status',
          title: 'DND flag is',
          value: 'contact.dnd',
          valueType: 'text',
          type: 'select',
          options: [{ title: 'Enabled', value: true }, { title: 'Disabled', value: false }]
        }
      ],
    }
  }
})
</script>

