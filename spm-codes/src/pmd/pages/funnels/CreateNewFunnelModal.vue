<template>
  <div
    class="modal fade hl_funnel-add-new-funnel--modal"
    id="new-funnel"
    tabindex="-1"
    role="dialog"
    aria-hidden="true"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Create New {{ entityName }}</h2>
            <button
              @click="hideModal()"
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div class="use-template" v-if="showTemplateLibraryLink">
              <label for="useTemplate">Use Template to create new {{ entityName }}</label>
              <h6>Jump start with an awesome prebuilt {{ entityName }}.</h6>
              <UIButton
                name="useTemplate"
                @click="loadTemplatePage()"
                use="secondary"
                class="justify-center"
              >
                <i class="fas fa-globe mr-2"></i>&nbsp;Go to Template Library
              </UIButton>
            </div>
            <div class="basic-funnel">
              <div class="form-group">
                <UITextInputGroup
                  v-model="funnelName"
                  type="text"
                  :label="`${ entityName } Name`"
                  :placeholder="`Name for your awesome ${ entityName }`"
                />
              </div>
              <UIButton
                type="submit"
                :disabled="disableSubmit"
                @click="createFunnel()"
                use="primary"
                class="justify-center"
              >{{ creatingNewFunnel ? 'Creating...' : `Create ${ entityName }` }}</UIButton>
            </div>
          </div>
        </div>
        <!-- <div class="modal-footer">
          <div class="modal-footer--inner">
            <button type="submit" :disabled="disableSubmit" @click="createFunnel()" class="btn btn-success">
              {{ creatingNewFunnel ? 'Creating...' : 'Create Funnel' }}
            </button>
          </div>
        </div>-->
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { UserState } from '../../../store/state_models'
import { User } from '../../../models'
import { mapState } from 'vuex'
export default Vue.extend({
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    createWebsite: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    newFunnelName(): String {
      return this.funnelName.trim()
    },
    disableSubmit(): Boolean {
      return !this.newFunnelName || this.creatingNewFunnel
    },
    entityName(): string {
      if (this.createWebsite) {
        return 'Website'
      } else {
        return 'Funnel'
      }
    },
    user(): User | undefined {
      return new User(this.$store.state.user.user) || undefined
    },
    showTemplateLibraryLink(): Boolean {
      return !!this.user && this.user.type === 'agency'
    }
  },
  data() {
    return {
      funnelName: '',
      creatingNewFunnel: false
    }
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hide')
    })
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  methods: {
    hideModal() {
      this.$emit('hide')
    },
    createFunnel() {
      const newFunnelDetails = { name: this.newFunnelName }
      this.creatingNewFunnel = true
      this.$emit('newFunnelDetails', newFunnelDetails)
    },
    loadTemplatePage() {
      this.hideModal()
      this.$router.push({
        name: this.createWebsite
          ? 'website_templates_category'
          : 'funnel_templates_category'
      })
    }
  },
  watch: {
    showModal(newValue, oldValue) {
      if (newValue && !oldValue) {
        // show the modal
        // reset form
        this.funnelName = ''
        $(this.$refs.modal).modal('show')
      } else if (!newValue && oldValue) {
        // hide the modal
        $(this.$refs.modal).modal('hide')
      }
    }
  }
})
</script>
<style scoped>
.modal-body--inner {
  display: flex;
}

.use-template,
.basic-funnel {
  flex-grow: 1;
  width: 50%;
  /* text-align: center; */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.use-template {
  padding-right: 10px;
  border-right: 1px solid lightgray;
}

.basic-funnel {
  padding-left: 10px;
}

.mark-as-website {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
