<template>
  <div class="container-fluid">
    <h2 class="summary-card__heading" @click="toggleShowAdvancedConfigurations" style="cursor:pointer;">Advanced Configuration
      <i class="fas fa-angle-down" v-if="!showAdvancedConfigurations"></i>
      <i class="fas fa-angle-up" v-else></i>
    </h2>
    <br/>
    <div class="card" style="padding: 32px;" v-if="showAdvancedConfigurations">
      <div class="option">
          <input type="checkbox" id="useOwnTwilio" v-model="useOwnTwilio" />
          <label for="useOwnTwilio">
             Use my Twilio Account
          </label>
      </div>
      <div style="margin-left: 32px;" v-if="useOwnTwilio">
        <div style="color: #EB5757; font-size: 12px;">
          Using this option is not recommended as it will result in loss of your currently used phone numbers. number pool and call recordings
        </div>
        <br/>
        <div class="option">
          <input type="checkbox" id="numberAcknowledge" v-model="numberAcknowledge" />
          <label for="numberAcknowledge">
             I acknowledge that I will lose phone numbers, number pool and call recordings
          </label>
        </div>
        <div class="option">
          <input type="checkbox" id="deliveryAcknowledge" v-model="deliveryAcknowledge" />
          <label for="deliveryAcknowledge">
             I acknowledge that SMS deliverability and Call Quality will be my responsibility
          </label>
        </div>
        <div class="option">
          <input type="checkbox" id="deliveryAcknowledge" v-model="a2pAcknowledge" />
          <label for="deliveryAcknowledge">
              I will handle my own A2P 10DLC compliance by logging in to Twilio
          </label>
        </div>
        <div class="row" style="margin-left: 24px; margin-top: 12px; width: 500px">
          <br />
          <div class="col-sm-12 flex-input">
            <div class="form-group">
              <UITextInputGroup
                type="text"
                placeholder="Account SID"
                label="Account SID"
                v-model="accountSID"
                v-validate="'required'"
                name="accountSID"
                @change="edited=true;isVerified=false"
                :error="errors.has('accountSID')"
                :errorMsg="'Account SID Required'"
              />
            </div>
            <!-- <i class="fa fa-check verfied-icon" v-if="isVerified"></i> -->
          </div>
          <div class="col-sm-12 flex-input">
            <div class="form-group">
              <UITextInputGroup
                type="text"
                placeholder="Auth Token"
                label="Auth Token"
                v-model="accountToken"
                v-validate="'required'"
                name="token"
                @change="edited=true;isVerified=false"
                :error="errors.has('token')"
                :errorMsg="'Auth Token Required'"
              />
            </div>
            <!-- <i class="fa fa-check verfied-icon"  v-if="isVerified"></i> -->
          </div>
        </div>
      </div>
      <div class="footer-row" v-if="useOwnTwilio">
        <UIButton
          type="button"
          @click="saveAccount"
        > Save
        </UIButton>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ['location'],
  data(){
    return {
      showAdvancedConfigurations: false,
      useOwnTwilio: false,
      numberAcknowledge: false,
      deliveryAcknowledge: false,
      a2pAcknowledge: false,
      isVerified: false,
      isLoading: false,
      edited: false,
      accountSID: '',
      accountToken:'',
    }
  },
  methods: {
    // verifyTwilio() {
    //   //
    // },
    toggleShowAdvancedConfigurations(){
      this.showAdvancedConfigurations = !this.showAdvancedConfigurations;
    },
    async saveAccount() {
			const result = await this.$validator.validateAll();
			if (!result) {
				return false;
			}
			this.isLoading = true;
      const locationId = this.location.id;
			try {
        const newSID  = this.accountSID && this.accountSID.trim().length ? this.accountSID.trim() : undefined
        const newToken = this.accountToken && this.accountToken.trim().length ? this.accountToken.trim() : undefined

				let body = {
					accountSID: newSID ,
					accountToken: newToken,
					company_id: this.location.company_id // For Auth guard only
				};
				let newTwilioAccount = await this.$http.post('/twilio/edit?location_id=' + locationId, body)
				if (newTwilioAccount.data === 'invalid') {
					alert("Invalid accountSID and access token");
					this.isLoading = false;
					return;
				} else if (newTwilioAccount.data === 'linked_for_rebilling') {
          alert(
            "An account with same SID is linked for twilio rebilling, can't use same for another account!"
          )
          this.isLoading = false
          return
        }
				let response = await this.$http.get('/twilio/create_application/' + locationId, {});
				this.$store.commit('agencyTwilio/setLocationTwilioEmpty', false);
				this.$emit('success',{ twilioAccount: newTwilioAccount, locationId: locationId});
				this.isLoading = false;
			} catch (ex) {
				console.error("Couldn't create twilio app:", ex);
				this.isLoading = false;
			}
		}
  }
}
</script>


<style scoped lang='scss'>
// .flex-input{
//   display: flex;
//   align-items: center;
//   i{
//     margin-left: 12px;
//     color: #37ca37;
//   }
// }
.footer-row{
  display: flex;
  justify-content: flex-end;
}
</style>
