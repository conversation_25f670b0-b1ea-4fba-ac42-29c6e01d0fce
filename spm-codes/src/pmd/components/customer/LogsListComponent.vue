<template>
  <div class="logs-list">
    <div class="card" v-if="audits.length > 0">
      <div class="card-body">
        <div id="auditLogs">
          <AuditLogCard
            v-for="audit in audits"
            :key="audit.id"
            :audit="audit"
          />
          <div v-if="loadingMore">
            <moon-loader color="#188bf6" size="20px" />
          </div>
        </div>
        <!-- <div class="load-more" v-if="hasMore">
          <a>Load more logs</a>
        </div> -->
      </div>
    </div>
    <template v-else>
      <div class="item-center">
        <moon-loader :loading="loading" color="#188bf6" size="20px" />
        <template v-if="!loading">
          <div class="text-center">
            <i
              class="fa fa-chart-line"
              aria-hidden="true"
              style="font-size: 20px; color: #bdbdbd"
            ></i>
          </div>
          <p>
            No Logs for this contact
            <br />
          </p>
        </template>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import ContactService from '@/services/ContactServices'
import { v4 as uuid } from 'uuid'
const AuditLogCard = () => import('@/pmd/components/customer/AuditLogCard.vue')

export default Vue.extend({
  components: {
    AuditLogCard,
  },
  data() {
    return {
      locationId: '',
      contactId: '',
      loading: true,
      loadingMore: false,
      records: [],
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.locationId = id
      this.fetchData()
    },
    '$route.params.contact_id': function (id) {
      this.contactId = id
      this.fetchData()
    },
  },
  async created() {
    this.locationId = this.$router.currentRoute.params.location_id
    this.contactId = this.$router.currentRoute.params.contact_id
    this.fetchData()
  },
  computed: {
    audits(): { [key: string]: any }[] {
      return this.records
        .map((record: { [key: string]: any }) => {
          record['id'] = uuid()
          return record
        })
        .reverse()
    },
  },
  methods: {
    async fetchData() {
      try {
        const response = await ContactService.getAudit(this.contactId)
        if (response) {
          this.records = response
        }
      } catch (error) {
        console.log(error)
      }

      this.loading = false
    },
    auditId() {
      return uuid()
    },
  },
  mounted() {
    this.$root.$on('updateAudit', () => {
      this.fetchData()
    })
  },
})
</script>

<style lang="scss">
.logs-list {
  height: 100%;
  overflow-x: auto;
}
</style>
