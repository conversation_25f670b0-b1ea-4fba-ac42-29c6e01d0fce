<template>
  <div class="card">
    <div class="card-header">
      <h3>complimentary Credits from HighLevel</h3>
    </div>
    <div class="card-body" v-if="loading">
      <div class="cc-wrap">
        <shimmer class="shimmer small" />
      </div>
    </div>
    <div class="card-body" v-else>
      <div class="cc-wrap">
        <div class="cc-wrap__left" :class="currentBalanceClass">{{complimentaryCredits}}</div>
        <div class="cc-wrap__right">
          <div>You got {{initialBalance}} free credits from HighLevel <span v-if="walletCreatedAt">on {{walletCreatedAt}}</span></div>
          <div>You have used {{initialBalance - complimentaryCredits}} credits so far</div>
          <div>You will be charged for usage after {{complimentaryCredits}} credits expire</div>
        </div>
      </div>
      <div class="cc-wrap__info">
        <i class="fas fa-info-circle"></i>
        You will start paying after free credits are exhausted. <a href="#" target="_blank" class="link" v-if="false">See Pricing.</a></div>
    </div>
  </div>
</template>

<script>
import Shimmer from '@/pmd/components/common/shimmers/Shimmer.vue'

export default {
  props: {
    complimentaryCredits: {
      type: Number,
      default: 100,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    walletCreatedAt: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // currentBalance: 41,
      initialBalance: 100,
    }
  },
  components: {
    Shimmer,
  },
  computed: {
    currentBalanceClass() {
      if (this.complimentaryCredits > 50) {
        return '--green';
      } else if (this.complimentaryCredits > 20) {
        return '--yellow';
      } else {
        return '--red';
      }
    }
  }
}
</script>

<style lang="scss">
.cc-wrap{
  width: 100%;
  background: #F3F8FB;
  border-radius: 6px;
  margin: 0px;
  padding: 20px;

  display: flex;
  align-content: center;
  // justify-content: space-between;
  .cc-wrap__left{
    width: 110px;
    height: 110px;
    background: #37CA37;
    border-radius: 3px;

    display: flex;
    align-items: center;
    justify-content: center;

    font-weight: 500;
    font-size: 36px;
    line-height: 42px;
    text-align: center;

    color: #FFFFFF;
    &.--yellow {
      background-color: #FFBC00;
    }
    &.--red {
      background-color: #EB5757;
    }
  }
  .cc-wrap__right{
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-left: 24px;
  }
}
.cc-wrap__info {
  text-align: center;
  margin-top: 12px;
  i {
    margin-right: 6px;
  }
  .link {
    color: #178AF6;
    font-weight: 500;
    cursor: pointer;
  }
}
</style>
