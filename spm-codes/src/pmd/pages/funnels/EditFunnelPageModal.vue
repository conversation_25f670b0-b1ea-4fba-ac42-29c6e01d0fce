<template>
  <div
    class="modal fade hl_funnel-edit-page--modal"
    id="funnel-edit-page"
    tabindex="-1"
    role="dialog"
    aria-hidden="true"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Edit Page</h2>
            <button
              @click="hideModal()"
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div class="form-group">
              <UITextInputGroup v-model="pageName" type="text" label="Name" placeholder="Name" />
              <p class="help-block">name of the page</p>
              <p
                class="help-block2"
              >Note: Page Domains are now set at the Funnel level (Funnel Settings -> Domain)</p>
            </div>
            <div class="form-group">
              <UITextInputGroup v-model="pageUrl" type="text" label="Path" placeholder="Path" />
              <p class="help-block">ending path of the url</p>
            </div>
            <span class="error" v-if="errorInEditingPage">Error while updating page.</span>
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner">
            <UIButton use="outline" type="button" data-dismiss="modal">Close</UIButton>
            <UIButton
              :disabled="updateDisabled"
              @click="updatePage()"
              type="button"
              use="primary"
            >{{ updatingPage ? 'Updating..' : 'Update Page'}}</UIButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { checkIfUserIsSettingFunnelPathAsRoot } from '../../../util/helper'

export default Vue.extend({
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    pageToEdit: {
      type: Object,
      required: true
    },
    errorInEditingPage: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  computed: {
    updateDisabled(): Boolean {
      return this.updatingPage || !this.pageName || !this.pageUrl
    }
  },
  data() {
    return {
      pageName: '',
      pageUrl: '',
      updatingPage: false
    }
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hide')
    })
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  methods: {
    hideModal() {
      this.$emit('hide')
    },
    updatePage() {
      const updates = {
        name: this.pageName.trim(),
        url: this.pageUrl.trim()
      }

      if (checkIfUserIsSettingFunnelPathAsRoot(updates.url)) {
        return
      }

      this.updatingPage = true
      this.$emit('updatedPage', updates)
    }
  },
  watch: {
    showModal(newValue, oldValue) {
      if (newValue && !oldValue) {
        // show the modal
        this.updatingPage = false
        this.pageName = this.pageToEdit.name
        this.pageUrl = this.pageToEdit.url
        $(this.$refs.modal).modal('show')
      } else if (!newValue && oldValue) {
        // hide the modal
        $(this.$refs.modal).modal('hide')
      }
    },
    'pageToEdit.name': function(newValue) {
      this.pageName = newValue
    },
    'pageToEdit.url': function(newValue) {
      this.pageUrl = newValue
    },
    errorInEditingPage(newValue) {
      if (newValue) {
        this.updatingPage = false
      }
    }
  }
})
</script>
<style scoped>
.controls {
  display: flex;
  justify-content: space-between;
}
</style>
