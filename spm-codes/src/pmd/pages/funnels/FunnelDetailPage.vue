<template>
  <div>
    <section v-if="!fetchingFunnelDetails" class="hl_wrapper">
      <section class="hl_wrapper--inner hl_funnels" id="funnels">
        <div class="hl_funnels--header">
          <div class="container-fluid">
            <div class="hl_controls">
              <div class="hl_controls--left funnel-title">
                <h2 class="inline-flex items-center">
                  <router-link :to="{ name: listingPageRouteName }" class="back">
                    <i class="icon icon-arrow-left-2"></i>
                  </router-link>
                  <div class="flex items-center">
                    {{ funnel.name }}
                    <div v-if="funnel.orderFormVersion === 2" class="inline-flex items-center ml-2 px-2 py-0.5 text-xs font-medium rounded bg-purple-100 text-purple-800">Version 2</div>
                    <!-- <div class="w-20 ml-2" v-if="allowPaymentModeOption">
                      <div class="inline-flex items-center ml-2 px-2 py-0.5 text-xs font-medium rounded-full bg-green-100 text-green-800">
                        <ToolsIcon v-if="!(funnel.isLivePaymentMode !== undefined ? funnel.isLivePaymentMode : locationConnectMode)" class="h-4 w-4" />
                        <PlayIcon v-else class="h-4 w-4" />
                        <div class="ml-1">
                          {{(funnel.isLivePaymentMode !== undefined ? funnel.isLivePaymentMode : locationConnectMode) ? 'Live' : 'Test'}}
                        </div>
                      </div>
                    </div> -->
                  </div>
                </h2>
              </div>
              <div class="hl_controls--right">
                <button
                  @click="showShareModal = !showShareModal"
                  type="button"
                  class="btn btn-light btn-sm"
                  data-tooltip="tooltip"
                  data-placement="top"
                  title="Share Funnel"
                  data-toggle="modal"
                  v-if="user && user.type === 'agency'"
                >
                  <i class="fa text-primary fa-share-alt"></i>
                  <span class="sr-only">Share Funnel</span>
                </button>
                <a
                  v-if="funnelDomain"
                  :href="funnelLink"
                  target="_blank"
                  class="btn btn-light btn-sm"
                  data-tooltip="tooltip"
                  data-placement="top"
                  title="Visit Funnel URL"
                >
                  <i class="icon icon-share-2"></i>
                  <span class="sr-only">Share</span>
                </a>
                <button
                  :disabled="!funnelDomain"
                  @click="showWhatIsFunnelModal()"
                  type="button"
                  class="btn btn-light btn-sm"
                  data-tooltip="tooltip"
                  data-placement="top"
                  title="What is the Funnel URL"
                  data-toggle="modal"
                  data-target="#whatis-funnel-url"
                >
                  <i class="icon icon-info"></i>
                  <span class="sr-only">What is Funnel</span>
                </button>
              </div>
            </div>
            <FunnelOverviewRouteTabs :route-data="routeLinks" />
          </div>
        </div>

        <div class="container-fluid">
          <div class="hl_funnels--wrap">
            <div class="tab-content">
              <router-view :funnel="funnel" :funnel-domain="funnelDomain" :is-website="funnelIsWebsite" />
            </div>
          </div>
        </div>
      </section>
      <!-- END of .funnels -->

      <!-- What is Funnel URL Modal -->
      <div
        class="modal fade hl_whatis-funnel-url--modal"
        id="whatis-funnel-url"
        tabindex="-1"
        role="dialog"
      >
        <div class="modal-dialog" role="document">
          <div class="modal-content">
            <div class="modal-header">
              <div class="modal-header--inner">
                <h2 class="modal-title">What is the {{ entityName }} URL?</h2>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
            </div>
            <div class="modal-body">
              <div class="modal-body--inner">
                <div class="url-card">
                  <h3 class="heading4">Send Traffic to {{ entityName }} URL</h3>
                  <p>Use the {{ entityName }} URL below to send traffic to your {{ entityName.toLowerCase() }}. This URL will always take visitors to the first page in your {{ entityName.toLowerCase() }}.</p>
                  <div class="form-group">
                    <div class="url-text">
                      <input
                        readonly
                        type="text"
                        class="form-control form-light"
                        placeholder="URL"
                        :value="funnelLink"
                      />
                      <p
                        class="help-block"
                      >You can change the domain name or path in the {{ entityName }} Settings.</p>
                    </div>
                    <div class="url-button">
                      <a :href="funnelLink" target="_blank" class="btn btn-success">View {{ entityName }}</a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section v-else class="hl_wrapper">
      <div style="padding: 20px">
        <moon-loader :loading="fetchingFunnelDetails" color="#188bf6" size="30px" />
      </div>
    </section>
    <ShareFunnelModal :funnelId="funnelId" :showModal="showShareModal" :entity-name="entityName" />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'

import ShareFunnelModal from '../../components/funnels/ShareFunnelModal.vue'
import { UserState } from '../../../store/state_models'
import { getValidUrl } from '@/util/helper'
import { Funnel, Domain, User, Location, EditorActiveViewer } from '../../../models'
import { FunnelType } from '../../../models/funnel'
import FunnelOverviewRouteTabs from '@/pmd/components/funnels/FunnelOverviewRoute.vue'
import PlayIcon from '@/assets/pmd/icons/play.svg'
import ToolsIcon from '@/assets/pmd/icons/tools.svg'
let unsubscribeFunnelListener: () => void
let unsubscribeEditorActiveViewer: () => void

export default Vue.extend({
  components: {
    ShareFunnelModal,
    FunnelOverviewRouteTabs,
    PlayIcon,
    ToolsIcon,
  },
  data() {
    return {
      fetchingFunnelDetails: true,
      funnel: {} as Funnel,
      currentLocationId: this.$router.currentRoute.params.location_id,
      funnelId: this.$router.currentRoute.params.funnel_id,
      funnelDomain: '',
      showShareModal: false,
      locationConnectMode: undefined as undefined | boolean,
      // allowPaymentModeOption: false
      // overviewRoute: [
      //   { path_name: 'funnel_steps', text: 'Steps' },
      //   { path_name: 'funnel_stats', text: 'Stats' },
      //   { path_name: 'funnel_sales', text: 'Sales' },
      //   { path_name: 'funnel_settings', text: 'Settings' }
      // ]
    }
  },
  async mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hide')
    })
    this.listenForEditorActiveViwerChanges()

    this.fetchFunnel(this.funnelId)

    this.funnelDomain = await this.getFunnelDomain(this.funnel.domainId)

    let location = await this.$store.dispatch('locations/getById', this.currentLocationId);
    if (!location) location = await Location.getById(this.currentLocationId)
    const currentLocation = new Location(location);
    this.locationConnectMode = currentLocation.stripeConnectMode === 'live';
    // this.allowPaymentModeOption = !currentLocation.stripe?.publishable_key;

  },
  computed: {

    entityName(): string {
      return this.funnelIsWebsite ? 'Website' : 'Funnel'
    },
    routeLinks(): object[] {
      if (!this.funnel) return []

      const routesConfig = this.funnelIsWebsite ? [
        { path_name: this.isV2SideBarEnabled ? 'website-pages-v2' : 'website_pages', text: 'Pages' },
        { path_name: this.isV2SideBarEnabled ? 'website-stats-v2' : 'website_stats', text: 'Stats' },
        { path_name: this.isV2SideBarEnabled ? 'website-sales-v2' : 'website_sales', text: 'Sales' },
        { path_name: this.isV2SideBarEnabled ? 'website-settings-v2' : 'website_settings', text: 'Settings' }
      ] : [
        { path_name: this.isV2SideBarEnabled ? 'funnel-steps-v2' : 'funnel_steps', text: 'Steps' },
        { path_name: this.isV2SideBarEnabled ? 'funnel-stats-v2' : 'funnel_stats', text: 'Stats' },
        { path_name: this.isV2SideBarEnabled ? 'funnel-sales-v2' : 'funnel_sales', text: 'Sales' },
        { path_name: this.isV2SideBarEnabled ? 'funnel-settings-v2' : 'funnel_settings', text: 'Settings' }
      ]


      const routes = routesConfig.map(route => {

        if(this.funnel.orderFormVersion === 2 && (route.path_name === 'funnel_sales' || route.path_name === 'website_sales' || route.path_name === 'funnel-sales-v2' || route.path_name === 'website-sales-v2')) return null

        return {
          name: route.path_name,
          params: {
            funnel_id: this.funnel.id,
            location_id: this.currentLocationId
          },
          text: route.text
        }
      }).filter(x => x)
      return routes
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    funnelLink(): string {
      if (!this.funnelDomain) {
        return `${window.location.origin}${this.funnel.url}`
      }
      const url = this.getFunnelURL;
      return getValidUrl(encodeURIComponent(url))
    },
    getFunnelURL(): string {
      const step = this.funnel.steps && this.funnel.steps[0];
      if(step && step.split) {
        return `${this.funnelDomain}/s${this.funnel.url}`
      }
      return `${this.funnelDomain}${this.funnel.url}`
    },
    funnelIsWebsite(): boolean {
      return this.funnel.type === FunnelType.Website
    },
    listingPageRouteName() {
      let routeName = this.funnelIsWebsite ? 'websites_funnels' : 'all_funnels'
      if (this.isV2SideBarEnabled) {
        routeName = this.funnelIsWebsite ? 'websites-funnels-v2' : 'funnels-page-v2'
      }
      return routeName
    },
    isV2SideBarEnabled() {
      return this.getSideBarVersion == 'v2'
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')

    if (unsubscribeFunnelListener) {
      unsubscribeFunnelListener()
    }
    if (unsubscribeEditorActiveViewer) unsubscribeEditorActiveViewer()

  },

  methods: {
    listenForEditorActiveViwerChanges(){
        if (unsubscribeEditorActiveViewer) unsubscribeEditorActiveViewer()
        const query = EditorActiveViewer.queryByLocation(this.$route.params.location_id)
        unsubscribeEditorActiveViewer = query.onSnapshot(snapshot => {
          if(snapshot && snapshot.docs.length > 0) {
            const items = []
            for (const funnel of snapshot.docs) {
              items.push(funnel.data())
            }
            this.$store.dispatch('funnel/SET_ACTIVE_EDITOR_VIEWER',items)
          }
        })

    },
    fetchFunnel(funnelId: string) {
      this.fetchingFunnelDetails = true
      let firstFetch = true
      if (unsubscribeFunnelListener) unsubscribeFunnelListener()
      unsubscribeFunnelListener = Funnel.collectionRef()
        .doc(funnelId)
        .onSnapshot(snapshot => {
          this.funnel = new Funnel(snapshot)
          this.fetchingFunnelDetails = false
          if (firstFetch) {
            this.loadStep()
            firstFetch = false
          }
        })
    },
    loadStep() {
      const { step_id: stepId } = this.$route.params

      if (this.funnelIsWebsite) {
        this.$router.replace({
          name: (this.isV2SideBarEnabled) ? 'website-pages-v2' : 'website_pages'
        })
        return
      }

      if (stepId) {
        this.$router.replace({
          name: (this.isV2SideBarEnabled) ? 'funnel-step-overview-v2' : 'funnel_step_overview',
          params: {
            funnel_id: this.funnel.id,
            location_id: this.currentLocationId,
            step_id: stepId
          }
        })
      } else {
        this.$router.replace({
          name: (this.isV2SideBarEnabled) ? 'funnel-steps-v2' : 'funnel_steps',
          params: {
            funnel_id: this.funnel.id,
            location_id: this.currentLocationId
          }
        })
      }
    },
    async getFunnelDomain(domainId: string) {
      const allDomains = await Domain.getAllByLocation(this.currentLocationId)
      const domain = allDomains.find(d => d.id === domainId)
      // return domain ? domain.url : window.location.origin
      return domain ? domain.url : ''
    },
    // copyFunnelLink() {
    //   console.log(this.link)
    //   const textElem = document.createElement('textarea')
    //   textElem.innerHTML = this.link
    //   document.body.appendChild(textElem)
    //   textElem.select()
    //   document.execCommand('copy')
    //   document.body.removeChild(textElem)
    // },
    showWhatIsFunnelModal() {
      $(this.$refs.modal).modal('show')
    },
    hideWhatIsFunnelModal() {
      $(this.$refs.modal).modal('hide')
    }
  },
  watch: {
    'funnel.domainId': async function(newValue) {
      this.funnelDomain = await this.getFunnelDomain(newValue)
    },
    '$route.params.location_id': function(newValue, oldValue) {
      let pageName = this.funnelIsWebsite ? 'websites_funnels' : 'all_funnels'
      if (this.isV2SideBarEnabled) {
        pageName = this.funnelIsWebsite ? 'websites-funnels-v2' : 'funnels-page-v2'
      }

      this.$router.push({
        name: pageName,
        params: { location_id: newValue }
      })
    }
  }
})
</script>
<style scoped>
.funnel-title {
  max-width: 60%;
}

.funnel-title h2 {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
