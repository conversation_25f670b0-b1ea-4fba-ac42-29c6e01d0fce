import BaseService from './BaseService'
import servicesConfig from '../config/services'

class ContactService extends BaseService {
  constructor(baseUrl: string) {
    super(baseUrl)
  }

  getDeleted = (locationId: string, startAt?: string) =>
    this.requests.get(
      `/${locationId}/deleted/${startAt ? '?startAt=' + startAt : ''}`
    )
  deleteContacts = (contacts: string[]) =>
    this.requests.post(`/bulk/delete/`, { contacts })
  restoreContacts = (contacts: string[], locationId: string) =>
    this.requests.post(`/bulk/restore/`, { contacts, locationId })
  update = (id: string, data: { [key: string]: any }) =>
    this.requests.put(`/${id}`, data)
  create = (data: { [key: string]: any }) => this.requests.post(`/`, data)
  getAudit = (id: string) => this.requests.get(`/${id}/audits`)
  Notes = {
    create: (contactId: string, data: { [key: string]: any }) =>
      this.requests.post(`/${contactId}/notes/`, data),
    read: (contactId: string, noteId: string) =>
      this.requests.get(`/${contactId}/notes/${noteId}`),
    update: (contactId: string, noteId: string, data: { [key: string]: any }) =>
      this.requests.put(`/${contactId}/notes/${noteId}`, data),
    delete: (contactId: string, noteId: string) =>
      this.requests.delete(`/${contactId}/notes/${noteId}`),
    list: (contactId: string) => this.requests.get(`/${contactId}/notes/`),
  }
  Tasks = {
    create: (contactId: string, data: { [key: string]: any }) =>
      this.requests.post(`/${contactId}/tasks/`, data),
    read: (contactId: string, taskId: string) =>
      this.requests.get(`/${contactId}/tasks/${taskId}`),
    update: (contactId: string, taskId: string, data: { [key: string]: any }) =>
      this.requests.put(`/${contactId}/tasks/${taskId}`, data),
    delete: (contactId: string, taskId: string) =>
      this.requests.delete(`/${contactId}/tasks/${taskId}`),
    list: (contactId: string) => this.requests.get(`/${contactId}/tasks/`),
  }
}

const instance = new ContactService(servicesConfig.Contacts)

export default instance
