<template>
  <div :style="getStyle">
    <WorkflowBuilder
      :key="`${$route.params.location_id}`"
      v-if="location"
      :location="location"
      :user="user"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import WorkflowBuilder from './WorkflowBuilder.vue'

export default Vue.extend({
  components: { WorkflowBuilder },
  computed: {
    locationId() {
      return this.$route.params.locationId
    },
    location() {
      const locations = this.$store.state.locations.locations
      return locations.find(
        (x: { [key: string]: any }) => x.id === this.$route.params.location_id
      )
    },
    getStyle() {
      return this.$store.getters['sidebarv2/getVersion'] == 'v2' ? 'overflow:hidden' : 'overflow-y: scroll';
    },
    user() {
      return this.$store.state.auth.user
    },
  },
})
</script>
