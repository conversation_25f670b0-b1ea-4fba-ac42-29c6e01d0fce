<template>
  <div>
      <multiselect
        v-model="values"
        :label="label"
        :track-by="trackBy"
        placeholder="Type to search"
        open-direction="bottom"
        class="hl-contact-serch"
        :preserveSearch="true"
        :options="options"
        :searchable="true"
        :loading="isLoading"
        :limit="3"
        :max-height="600"
        :show-no-results="false"
        :hide-selected="true"
        @input="val => this.$emit('change', val)"
        @search-change="asyncFind">
          <template slot="singleLabel" slot-scope="props">
            <Avatar
              :contact="props.option"
              :include_name="true"
              :include_company_name="true"
            />
            <span class="option__desc">
              <span class="option__title">{{ props.option.label }}</span>
            </span>
          </template>
          <template slot="option" slot-scope="props" class="flex">
            <Avatar
              :contact="props.option"
              :include_name="true"
              :include_company_name="true"
            />
            <div class="option__desc">
              <span class="option__title">{{ props.option.label }}</span>
            </div>
          </template>
      </multiselect>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import Multiselect from 'vue-multiselect'
import axios from 'axios'
const Avatar = () => import('@/pmd/components/Avatar.vue').then(m => m.default)

export default Vue.extend({
  components: {
    Multiselect,
    Avatar
  },
  props: {
    label: {
      type: String,
      default: 'label'
    },
    trackBy: {
      type: String,
      default: 'value'
    },
    searchUrl: {
      type: String,
      default: ''
    },
    locationId: {
      type: String,
      default: ''
    },
    isClear: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      values: [],
      options: [],
      isLoading: false
    }
  },
  watch: {
    isClear() {
      this.clearAll()
    }
  },
  methods: {
    limitText (count: number) {
      return `and ${count} other countries`
    },
    initials(name: string) {
      if (!name) {
        return
      }

      let splitStr = name.toLowerCase().split(' ')
      for (let i = 0; i < splitStr.length; i++) {
        // You do not need to check if i is larger than splitStr length, as your for does that for you
        // Assign it back to the array
        splitStr[i] = splitStr[i].charAt(0).toUpperCase()
      }
      // Directly return the joined string
      return splitStr.join('')
    },
    asyncFind (query: string) {
      this.isLoading = true
      axios
        .get(this.searchUrl, {
          params: {
            location_id: this.locationId,
            q: query,
            page: 1,
            limit: 10,
          },
        })
        .then(res => {
          this.options = res.data.hits.hits.map((contact: any) => {
            return {
              value: contact._id,
              label: contact._source.contact_name,
              name: contact._id,
              prettyName: contact._source.contact_name,
              initials: this.initials(contact._source.contact_name),
              profileColor: 'rgb(134, 117, 189)',
            }
          });

          this.isLoading = false
        })
    },
    clearAll () {
      this.values = []
    }
  }
})
</script>

<style scoped>
.hl-contact-serch .multiselect__option {
  display: flex;
  align-items: center;
}

.hl-contact-serch .multiselect__tags {
  padding-top: 5px;
}

.hl-contact-serch .multiselect__single {
  margin-bottom: 4px;
}
.hl-contact-serch .multiselect__single .avatar {
  height: 32px;
}
</style>
