<template>
	  <div class="menu-item">
      <div class="form-builder--item">
        <!--Adding descript for each field-->
        <label style="display:block;" v-if="item.description !== undefined" v-html="item.description"></label>
        <div style="display: inline-flex;" v-if="item.type !=='submit'">
          <label v-if="showFieldLabel(item)">{{item.label}}
            <span v-if="item.required">*</span>
          </label>
          <div v-if="item.hidden">
            <small style="background: #e1e1e1;padding: 2px 5px;border-radius: 2px; margin-left:5px;">Hidden</small>
          </div>
        </div>
        <component
          v-if="item.type=='text' || item.type=='email' || item.type=='date'"
          v-bind:is="getFieldInput(item.type)"
          :placeholder="item.placeholder"
          :disabled="true"
        ></component>

				<!--Heading-->
				<div v-else-if="item.tag === 'header'">
					<h1
						v-bind:style="{
						color: '#' + item.color,
						fontFamily: item.font,
						fontSize: item.size + 'px',
						fontWeight: item.weight,
						textAlign: item.align
						}">
						{{ item.label }}
					</h1>
				</div>

        <!--Free hand HTML-->
				<div v-else-if="item.tag === 'html'">
          <h5
            v-if="item.html==''"
						v-bind:style="{
						color: '#' + item.color,
						fontFamily: item.font,
						fontSize: item.size + 'px',
						fontWeight: item.weight,
						textAlign: item.align
						}">
            {{ item.placeholder }}
					</h5>
          <div v-else>
            <div>
              <small style="background: #e1e1e1;padding: 2px 5px;border-radius: 2px;">Script Added!</small>
            </div>
            <div v-html="item.html"></div>
          </div>
				</div>
        <!--Source Name-->
				<div v-else-if="item.tag === 'source'">
          <div>
              <small style="background: #e1e1e1;padding: 2px 5px;border-radius: 2px;">Source Added!</small>
            </div>
          <h6
						v-bind:style="{
						color: '#' + item.color,
						fontFamily: item.font,
						fontWeight: item.weight,
						textAlign: item.align
						}">
            {{item.value}}
					</h6>
				</div>

        <!--Image-->

		    <moon-loader class="mt-5" color="#188bf6" size="20px" v-if="loading"/>
        <div v-else-if="item.tag === 'image'" >
          <input
            v-if="!('url' in item)"
            type="file"
            class="form-control"
            placeholder="Image"
            v-bind:name="item.tag"
            @change="onFileChange"
            accept="image/*"
          >

          <div class="form-builder--item form-builder--image" v-else-if="('url' in item)"
          :align="!item.align ? 'center' : item.align">
            <img
              :src="item.url"
              :alt="item.alt"
              v-bind:style="{
                width:(!item.width || item.width<=0) ? '100%' : item.width + 'px',
                height:(!item.height || item.height<=0) ? '100%': item.height + 'px'
              }"
            />
            <!-- <v-lazy-image
            :src="item.url"
            :alt="item.alt"
            v-bind:style="{
              width:(!item.width || item.width<=0) ? '100%' : item.width + 'px',
              height:(!item.height || item.height<=0) ? '100%': item.height + 'px'
            }"
            /> -->
          </div>
        </div>

        <!--Captcha-->
        <div v-else-if="item.tag === 'country'">
					<SingleOptionsField 
					:placeholder="item.placeholder"
          :disabled="true"/>
        </div>

				<!--Captcha-->
        <div v-else-if="item.tag === 'captcha'">
          <img
            class="captcha-wrap-img"
            src="../../../assets/pmd/img/captcha.png"
            alt="Avatar Name"
          >
        </div>

				<!-- Gender -->
				<div v-else-if="item.tag === 'gender'">
					<div
						class="option-radio"
						v-for='(pickerOption, index) in item.picklistOptions'
						:key="index">
					
						<div>
							<input
								type="radio"
								name="gender"
								:disabled="true"
								:value="pickerOption"
								@keyup="(e) => $emit('keyup', e)"
								@change="(e) => $emit('change', e)"
							>
							<label> {{pickerOption }}</label>
						</div>
					</div>
				</div>

        <!--Button-->
        <div
          v-else-if="item.tag === 'button'"
          v-bind:style="{
            textAlign: item.align
          }">
          <button
            type="button"
            class="btn btn-dark"
            v-bind:style="{
            backgroundColor: '#' + buttonStyle.bgColor,
            color: '#' + buttonStyle.color,
            border: buttonStyle.border + 'px solid #0f1010',
            borderRadius: buttonStyle.radius + 'px',
            padding: buttonStyle.padding + 'px',
            marginTop: '15px',
            width: (item.fullwidth) ? '100%' : 'auto',
          }"
          >{{item.label}}</button>
        </div>

      </div>
      <a class="close-icon" @mousedown="removeField(item.tag)" v-if="item.active">
        <i class="icon-close"></i>
      </a>
      <add-field-dropzone :index="index" v-if="showDropZones"></add-field-dropzone>
    </div>
</template>

<script lang="ts">
import Vue from "vue";

import { isoToState, countries } from "@/util/state_helper";
import VLazyImage from "v-lazy-image";
import {
	Location,
	Contact
} from "@/models";

import { v4 as uuid } from "uuid";
const path = require('path');

/*--Firebase--*/
import firebase from "firebase/app";

import libphonenumber from "google-libphonenumber";
const TextField = () => import("../util/TextField.vue");
const DateField = () => import("../util/DateField.vue");
const RadioField = () => import("../util/RadioField.vue");
const SingleOptionsField = () => import("../util/SingleOptionsField.vue");
const AddFieldDropzone = () => import("../../components/marketing/AddFieldDropzone.vue");
import { FileAttachment } from "@/store/state_models";

const MoonLoader = () => import("../../components/MoonLoader").then(m => m.default);

import ImageTools from "../../../util/image_tools";
const imageTools = new ImageTools();

export enum FieldType {
	text = 'Text',
  email = 'Text',
	date = 'Date',
	radio = 'Radio'
}

export default Vue.extend({
	props: {
		propImageFileInStore: {} as any,
		currentLocationId: {} as any,
		builderId: {} as any,
    item: {} as any,
    index: {} as any,
    showDropZones: {} as any,
    formLabelVisible: {} as any,
    formButtonStyle: {} as any
	},
	components: {
		MoonLoader,
		TextField,
    "add-field-dropzone": AddFieldDropzone,
    VLazyImage,
		DateField,
		SingleOptionsField,
		RadioField
	},
	data() {
		return {
		  loading: false,
      contactCustomFieldsData: {},
      buttonStyle: this.formButtonStyle,

			//File upload
			attachments: [] as string[],
			filesAdded: [] as FileAttachment[],
			isImageFileInStore: this.propImageFileInStore,
		};
	},
	computed: {

	},
	watch: {

	},
	updated() {
		const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
	},
	mounted() {

	},
	methods: {
    showFieldLabel(item:any){
	  if(this.formLabelVisible && (item.type=='text' || item.type=='email' || item.type=='captcha' || item.type=='date' || item.type=='select' || item.type=='radio'))
        return true
      else
        return false;
    },
		getFieldInput(fieldType: string) {
			return FieldType[fieldType] ? FieldType[fieldType].replace(/ /g, '') + "Field" : "";
		},
		removeField(fieldId: string) {
			this.$bus.$emit("remove-standard-field", {
				id: fieldId
			});
		},

		/* IMAGE UPLOAD CODE */
		async onFileChange(e: any) {
      this.loading = true;
      this.filesAdded = [] as FileAttachment[]; //clear first and then upload
			const element = <HTMLInputElement>e.target;
			if (!element.files) return;
			for (let i = 0; i < element.files.length; i++) {
				this.vfileAdded(element.files[i]);
			}
			element.files = null;
		},
		async vfileAdded(file: File) {
			const response = <File | Blob>(
				await imageTools.resize(file, { height: 1000, width: 1000 })
			);
			this.filesAdded.push({
				name: file.name,
				type: file.type,
				url: URL.createObjectURL(response),
				data: response
			});
			this.isImageFileInStore = true; //Show image to form now/dont wait for image upload
      this.send();
		},
		send: async function () {
			let el = this;
			const urls = [] as string[];
			if (this.filesAdded.length > 0) {
				const basePath =
					"location/" +
					this.currentLocationId +
					"/form/" +
					this.builderId +
					"/";
				const newURLs: string[] = await Promise.all(
					this.filesAdded.map(async attachment => {
						let imagePath = basePath + uuid() + path.extname(attachment.name);
						var uploadPath = firebase.storage().ref(imagePath);
						const snapshot = await uploadPath.put(attachment.data, {
              contentType: attachment.type,
              contentDisposition: `inline; filename="${attachment.name}"`,
							customMetadata: { name: attachment.name }
						});
						return await snapshot.ref.getDownloadURL();
					})
				);
				urls.push.apply(urls, newURLs.filter(url => url));

				//Set uploaded image url to ItemDropable
				if (urls.length > 0) {
					this.$emit("saveUploadPath", urls, this.index);
				} else {
					this.isImageFileInStore = false; //If image is not uploaded
				}
			}
			this.loading = false;
		},
	}
});
</script>

<style scoped>
.v-lazy-image {
  filter: blur(5px);
  transition: filter 0.7s;
}
.v-lazy-image-loaded {
  filter: blur(0);
}
</style>
