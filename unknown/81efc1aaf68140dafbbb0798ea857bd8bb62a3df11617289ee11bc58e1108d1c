<template>
  <div :class="['drop-zone-wrap', {'hover': hover }]">
    <div class="triangle"></div>
    <drop
      class="add-dropzone-builder"
      @dragenter="hover = true"
      @dragleave="hover = false"
      @drop="handleDropZone(index)"
    ></drop>
  </div>
</template>

<script>
import { Drop } from "vue-drag-drop";

export default {
  props: ["index"],
  data() {
    return {
      hover: false
    };
  },
  components: {
    Drop
  },
  methods: {
    handleDropZone(data) {
      this.hover = false;
      this.$bus.$emit("add-field-to-drozone", {
        data: data
      });
    }
  }
};
</script>
