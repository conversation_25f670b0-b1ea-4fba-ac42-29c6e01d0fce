<template>
  <div
    class="tab-pane fade show active"
    id="additional-info"
    role="tabpanel"
    aria-labelledby="additional-info-tab"
  >
    <div class="mb-2">
      <UICheckbox
        name="empty_check"
        :checked="emptyCheck"
        v-model="emptyCheck"
      /> <UITextLabel class="">Hide Empty Fields</UITextLabel>
    </div>
    <div v-for="(field,index) in customFields" :key="index">
      <div
        class="form-group"
        v-if="
          ((contactCustomFieldsData[field.id] != null &&
            contactCustomFieldsData[field.id] != '' && !contactCustomFieldsData[field.id].meta) || ( getUndeletedFile(field.id) )  ) ||
            !emptyCheck || (isFileType(contactCustomFieldsData[field.id])  && isFilesNotEmpty(contactCustomFieldsData[field.id]) )
        "
      >
        <div v-if="customFields.hidden">
          <small
            style="background: #e1e1e1;padding: 2px 5px;border-radius: 2px;"
            >Hidden</small
          >
        </div>
        <UITextLabel class="capitalize">{{ field.name }}</UITextLabel>

<section v-if="isFileType(contactCustomFieldsData[field.id]) || ((field.multiple_files_allowed === true || field.multiple_files_allowed === false))">
<FileDownloadFieldV2 :files="contactCustomFieldsData[field.id]" @setEditedTrue="setEdited" @setToBeDeleted="setToBeDeletedFilesV2" @setToBeUploadedFiles="setToBeUploadedFilesV2" :id="field.id" :field="field" />

</section>


        <component
          v-else
          v-bind:is="getFieldInput(field.dataType)"
          :placeholder="field.placeholder"
          v-model="contactCustomFieldsData[field.id]"
          :picklistOptions="field.picklistOptions"
          :picklistOptionsImage="field.picklistOptionsImage"
          :field="field"
          @keyup="setEdited"
          @change="setEdited"
          :currentLocationId="currentLocationId"
          :file="getUndeletedFile(field.id)"
          :isToBeDeleted="isToBeDeleted(field.id)"
          :toBeUploadedFile="getToBeUploadedFile(field.id)"
          @delete-file="toBeDeleted(field.id)"
          @change-file="addToBeUploadedFile"
          :isTW="true"
        ></component>
        <!-- <PhoneNumber
					class="form-control msgsndr1"
					placeholder="Phone numbers"
					v-model="phone"
					v-validate="'phone'"
					name="msgsndr1"
          autocomplete="msgsndr1"
					data-vv-as="phone"
					:currentLocationId="currentLocationId"
					@keyup="setEdited"
        />-->
        <!-- <PhoneField/>  -->
      </div>
    </div>

    <div class="form-footer save" v-if="edited">
      <UIButton use="outline" @click.prevent="reset">
        Cancel
      </UIButton>
      <div style="display: inline-block; position: relative; margin-left: 10px">
        <UIButton
          @click.prevent="save"
          :loading="loading"
        >
          Save
        </UIButton>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import firebase from 'firebase/app'
import { v4 as uuid } from 'uuid'
import {
  Location,
  Contact,
  CustomField,
  ICustomField,
  FieldType,
  CustomDateField
} from '@/models'
import libphonenumber from 'google-libphonenumber'
import { isoToState, countries } from '@/util/state_helper'
import TextField from '../util/TextField.vue'
import LargeTextField from '../util/LargeTextField.vue'
import NumericalField from '../util/NumericalField.vue'
import PhoneField from '../util/PhoneField.vue'
import MonetaryField from '../util/MonetaryField.vue'
import SingleOptionsField from '../util/SingleOptionsField.vue'
import CheckboxField from '../util/CheckboxField.vue'
import MultipleOptionsField from '../util/MultipleOptionsField.vue'
import RadioField from '../util/RadioField.vue'
import DateField from '../util/DateField.vue'
import TextboxListField from '../util/TextboxListField.vue'
import FileDownloadField from '../util/FileDownloadField.vue'
import FileDownloadFieldV2 from '../util/FileDownloadFieldV2.vue'
import { UxMessage } from '@/util/ux_message'
import lodash from 'lodash'

import moment from 'moment-timezone'
let unsubscribeCustomFields: () => void
const phoneUtil = libphonenumber.PhoneNumberUtil.getInstance()
declare var $: any

const store = require('store')

export default Vue.extend({
  components: {
    TextField,
    LargeTextField,
    NumericalField,
    PhoneField,
    MonetaryField,
    SingleOptionsField,
    CheckboxField,
    MultipleOptionsField,
    RadioField,
    DateField,
    TextboxListField,
    FileUploadField: FileDownloadField,
    SignatureField: FileDownloadField,
    FileDownloadFieldV2
  },
  props: {
    location: Location,
    contact: Contact
  },
  inject: ['uxmessage'],
  data() {
    return {
      loading: false,
      edited: false,
      customFields: [] as CustomField[],
      currentLocationId: '',
      contactCustomFieldsData: {},
      emptyCheck: true,
      toBeDeletedFields: [],
      toBeUploadedFiles: [],
      ToBeUploadedFilesV2: {},
      toBeDeletedFilesV2: {}
    }
  },
  watch: {
    customFields(value) {
      value.forEach(v => {
        Vue.set(this.contactCustomFieldsData, v.id, null)
      })

      if (this.contact.customFields) {
        this.contact.customFields.forEach(field => {
          Vue.set(this.contactCustomFieldsData, field.id, field.field_value)
        })
      }
    },
    emptyCheck: {
      handler(val, oldVal) {
        store.set(
          `additional-fields-${this.currentLocationId}`,
          this.emptyCheck
        )
      }
    }
  },
  mounted() {
    this.reset()
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.emptyCheck =
      store.get(`additional-fields-${this.currentLocationId}`) || false
    this.fetchData()
  },
  methods: {
    setEdited() {
      this.edited = true
    },
    fetchData() {
      if (unsubscribeCustomFields) unsubscribeCustomFields()

      unsubscribeCustomFields = CustomField.getByLocationId(
        this.currentLocationId
      ).onSnapshot(snapshot => {
        this.customFields = snapshot.docs.map(d => new CustomField(d))
      })
    },
    getFieldInput(fieldType: FieldType) {
      return FieldType[fieldType]
        ? FieldType[fieldType].replace(/ /g, '') + 'Field'
        : ''
    },
    reset() {
      this.$bus.$emit("multifile/clear" );
      this.loading = false
      this.edited = false
      this.toBeDeletedFields = []
      this.toBeUploadedFiles = []
      if (this.contact.customFields && this.contactCustomFieldsData) {
        this.contact.customFields.forEach(field => {
          Vue.set(this.contactCustomFieldsData, field.id, field.field_value)
        })
      }
    },
    async save() {
      const files = [];
      if (this.loading) return

      this.loading = true
      let response

      if (this.contact && this.customFields.length > 0) {

        this.toBeDeletedFields.forEach((field: any) => {
          this.contactCustomFieldsData[field].meta.deleted = true
        })


        let test = []
        for (var field in this.contactCustomFieldsData) {
          let customField = lodash.find(this.customFields, { id: field })
          if (
            customField &&
            FieldType[customField.dataType] === FieldType.DATE &&
            !lodash.isEmpty(this.contactCustomFieldsData[field])
          ) {
            try {
              let customDateField = await CustomDateField.getByContactIdAndCustomFieldId(
                this.contact.id,
                customField.id,
                this.currentLocationId
              )
              if (!customDateField) {
                customDateField = new CustomDateField()
              }
              customDateField.locationId = this.currentLocationId
              customDateField.contactId = this.contact.id
              customDateField.customFieldId = customField.id
              customDateField.dateValue = moment.utc(
                this.contactCustomFieldsData[field]
              )
              console.log(moment.utc(this.contactCustomFieldsData[field]))
              await customDateField.save()
            } catch (e) {
              console.log(e)
            }
          }
          const file = this.getToBeUploadedFile(field)

          if (file) {
            files.push(  {field , file: file.file});
          }

          test.push({
            id: field,
            field_value: this.contactCustomFieldsData[field]
          })
        }
        this.contact.customFields = test
        if(files.length) {
          await this.uploadFiles(files)
        }
        if(this.isUploadRequired(this.ToBeUploadedFilesV2))
          await this.uploadFilesV2(this.ToBeUploadedFilesV2);

        this.contact.customFields.forEach((field) => {
          if(field && this.toBeDeletedFilesV2[field.id]) {

            this.toBeDeletedFilesV2[field.id].forEach((id) => {
              Vue.set(field.field_value[id].meta , 'deleted'  , true);
            })
          }
        })

        response = await this.contact.save()
        if (response instanceof Contact || response === undefined) {
          this.edited = false
          this.$root.$emit('updateAudit');
          this.reset();
        } else {
          this.uxmessage(UxMessage.errorType(lodash.capitalize(response)), true)
        }
      }
      this.loading = false
    },
    toBeDeleted(id) {
      this.edited = true
      this.toBeDeletedFields.push(id)
    },
    isToBeDeleted(id) {
      return this.toBeDeletedFields.indexOf(id) > -1 ? true : false
    },
    addToBeUploadedFile(id, file) {
      this.edited = true
      this.toBeDeletedFields = this.toBeDeletedFields.filter(el => el != id)
      if (!lodash.find(this.toBeUploadedFiles, { id })) {
        this.toBeUploadedFiles.push({
          id,
          file
        })
      } else {
        this.toBeUploadedFiles = this.toBeUploadedFiles.filter(
          el => el.id != id
        )
        this.toBeUploadedFiles.push({
          id,
          file
        })
      }
    },
    async uploadFiles(files) {


      var bodyFormData = new FormData()
      files.forEach((obj) => {

        bodyFormData.set(obj.field, obj.file)
      })

      bodyFormData.set("contact"  , JSON.stringify({id: this.contact.id , data: {
        location_id: this.contact.data.location_id
      }}));

     const {data} = await this.$http.post("/form/custom-files-upload" , bodyFormData)

data.forEach((obj) => {
this.contact.customFields.push(obj);
} )

    },
    getToBeUploadedFile(id) {
      const file = this.toBeUploadedFiles.find(obj => {
        if (obj.id === id) {
          return true
        }
      })

      return file
    },
    getUndeletedFile(id) {
      return this.contactCustomFieldsData[id] && this.contactCustomFieldsData[id].meta && !this.contactCustomFieldsData[id].meta.deleted ? this.contactCustomFieldsData[id] : undefined
    },
    setToBeUploadedFilesV2(files , id) {
      this.ToBeUploadedFilesV2[id] = files;
    },async uploadFilesV2(files) {
            var bodyFormData = new FormData()

            Object.keys(files).forEach((key) => {
                files[key].forEach((file) => {

                  bodyFormData.append(`${key}_${file.id}`, file.file);

                })

            })

      bodyFormData.set("contact"  , JSON.stringify({id: this.contact.id , data: {
        location_id: this.contact.data.location_id
      }}));

     const {data} = await this.$http.post("/v2/form/custom-files-upload" , bodyFormData)


     this.contact.customFields.forEach((field) => {
       if(field && data[field.id]) {
         let {field_value , id} = field;
         field.field_value = { ...field_value , ...data[id]} ;
       }
     })



    } ,
    setToBeDeletedFilesV2(files , id) {
      this.toBeDeletedFilesV2[id] = files;
    },isUploadRequired(files) {

      let flag = false;
                 Object.keys(files).forEach((key) => {
                files[key].forEach((file) => {

                 flag = true;

                })

            })
  return flag;
    },isFileType(value) {

      if( !(value && typeof value === 'object')) {
        return false;
      }

      let flag = false;
      Object.keys(value).forEach((id) =>{

            if(value[id].hasOwnProperty('meta')) {
              flag = true;
          }
      })
      return flag;
    }, isFilesNotEmpty(value) {
      let flag = false;
        Object.keys(value).forEach((id) => {

          if(value[id].meta.deleted === false ) {
            flag = true;
          }

        })
      return true;
    }
  },

  beforeDestroy(): void {
    if (unsubscribeCustomFields) unsubscribeCustomFields()
  }
})
</script>
