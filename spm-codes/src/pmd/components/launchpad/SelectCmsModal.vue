<template>
  <div class="chat-modal-containter">
    <div class="chat-instractions">
      <h5>Which website builder do you use?</h5>
      <div class="chat-cms-select">
        <div
          class="chat-cms"
          :class="{ 'chat-selected-cms': newCms === 'WordPress' }"
          @click.prevent="newCms = 'WordPress'"
        >
          <label class="label">
            <input
              type="radio"
              class="input-radio"
              value="WordPress"
              v-model="newCms"
            />
          </label>
          <div class="chat-cms-img">
            WordPress
            <img src="@/assets/pmd/img/launchpad/wordpress.png" />
          </div>
        </div>
        <!-- <div
          class="chat-cms"
          :class="{ 'chat-selected-cms': newCms === 'Wix' }"
          @click.prevent="newCms = 'Wix'"
        >
          <label class="label">
            <input
              type="radio"
              class="input-radio"
              value="Wix"
              v-model="newCms"
            />
          </label>
          <div class="chat-cms-img">
            Wix
            <img src="@/assets/pmd/img/launchpad/wix.svg" />
          </div>
        </div> -->
        <div
          class="chat-cms"
          :class="{ 'chat-selected-cms': newCms === 'SquareSpace' }"
          @click.prevent="newCms = 'SquareSpace'"
        >
          <label class="label">
            <input
              type="radio"
              class="input-radio"
              value="SquareSpace"
              v-model="newCms"
            />
          </label>
          <div class="chat-cms-img">
            SquareSpace
            <img src="@/assets/pmd/img/launchpad/squarespace.png" />
          </div>
        </div>
        <div
          class="chat-cms"
          :class="{ 'chat-selected-cms': newCms === 'Joomla' }"
          @click.prevent="newCms = 'Joomla'"
        >
          <label class="label">
            <input
              type="radio"
              class="input-radio"
              value="Joomla"
              v-model="newCms"
            />
          </label>
          <div class="chat-cms-img">
            Joomla
            <img src="@/assets/pmd/img/launchpad/joomla.png" />
          </div>
        </div>
        <!-- <div
          class="chat-cms"
          :class="{ 'chat-selected-cms': newCms === 'Dealer' }"
          @click.prevent="newCms = 'Dealer'"
        >
          <label class="label">
            <input
              type="radio"
              class="input-radio"
              value="Dealer"
              v-model="newCms"
            />
          </label>
          <div class="chat-cms-img">
            Dealer.com
            <img src="@/assets/pmd/img/launchpad/dealer.png" />
          </div>
        </div> -->
        <div
          class="chat-cms"
          :class="{ 'chat-selected-cms': newCms === 'Duda' }"
          @click.prevent="newCms = 'Duda'"
        >
          <label class="label">
            <input
              type="radio"
              class="input-radio"
              value="Duda"
              v-model="newCms"
            />
          </label>
          <div class="chat-cms-img">
            Duda
            <img
              class="chat-img-circle"
              src="@/assets/pmd/img/launchpad/duda.png"
            />
          </div>
        </div>
        <div
          class="chat-cms"
          :class="{ 'chat-selected-cms': newCms === 'Shopify' }"
          @click.prevent="newCms = 'Shopify'"
        >
          <label class="label">
            <input
              type="radio"
              class="input-radio"
              value="Shopify"
              v-model="newCms"
            />
          </label>
          <div class="chat-cms-img">
            Shopify
            <img src="@/assets/pmd/img/launchpad/shopify.png" />
          </div>
        </div>
        <div
          class="chat-cms"
          :class="{ 'chat-selected-cms': newCms === 'Weebly' }"
          @click.prevent="newCms = 'Weebly'"
        >
          <label class="label">
            <input
              type="radio"
              class="input-radio"
              value="Weebly"
              v-model="newCms"
            />
          </label>
          <div class="chat-cms-img">
            Weebly
            <img src="https://static.msgsndr.com/img/weebly.svg" />
          </div>
        </div>
        <!-- <div
          class="chat-cms"
          :class="{ 'chat-selected-cms': newCms === 'GoDaddy' }"
          @click.prevent="newCms = 'GoDaddy'"
        >
          <label class="label">
            <input
              type="radio"
              class="input-radio"
              value="GoDaddy"
              v-model="newCms"
            />
          </label>
          <div class="chat-cms-img">
            GoDaddy
            <img src="@/assets/pmd/img/launchpad/godaddy.png" />
          </div>
        </div> -->
        <div
          class="chat-cms"
          :class="{ 'chat-selected-cms': newCms === 'Other' }"
          @click.prevent="newCms = 'Other'"
        >
          <label class="label">
            <input
              type="radio"
              class="input-radio"
              value="Other"
              v-model="newCms"
            />
          </label>
          <div class="chat-cms-img">
            Other / I don't know
            <div class="other-cms">?</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: ['cms'],
  data() {
    return {
      newCms: this.cms,
    }
  },
  methods: {
    setCms() {
      this.$root.$emit('set-cms', this.newCms)
    },
  },
  watch: {
    newCms: async function () {
      this.setCms()
    },
  },
})
</script>
<style scopes>
.chat-instractions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: start;
  padding: 20px;
}
.chat-instractions .chat-step-img {
  height: 340px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  align-items: center;
  width: 100%;
}
.chat-instractions .chat-cms-select {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin: 0px auto;
  overflow: none;
}
.chat-instractions .chat-cms-select .chat-cms {
  margin: 8px 8px;
  padding: 0px 16px;
  border: 1px solid rgb(204, 204, 204);
  border-radius: 4px;
  cursor: pointer;
  width: 210px;
  height: 75px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.chat-selected-cms {
  border: 1px solid rgb(76, 118, 224) !important;
}
.chat-instractions .chat-cms-select .chat-cms .label {
  margin-top: 12px;
  margin-right: 10px;
}
.chat-instractions .chat-cms-select .chat-cms .chat-cms-img {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}
.chat-instractions .chat-cms-select .chat-cms .chat-cms-img img {
  height: 40px;
  width: 40px;
}
.chat-instractions .chat-cms-select .chat-cms .chat-cms-img .chat-img-circle {
  border-radius: 50%;
}
.other-cms {
  height: 40px;
  width: 40px;
  display: flex;
  -webkit-box-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  align-items: center;
  border-radius: 50%;
  background-color: rgb(228, 233, 240);
  color: rgb(76, 118, 224);
  font-size: 20px;
}
.chat-modal-containter {
  max-width: 600px;
  max-height: 500px;
  overflow: hidden;
  overflow: hidden;
}
</style>
