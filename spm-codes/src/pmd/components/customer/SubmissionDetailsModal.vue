<template>
  <div
    class="modal fade hl_sms-template--modal"
    id="submission-detail-modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog modal-xl modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title" v-if="! isPrefetchedData">{{ activity.type  == 'survey' ? 'Survey' : 'Form' }} Submission Detail</h2>
            <h2 class="modal-title" v-else>Booking Details</h2>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <h6 class="text-muted px-3">{{ activity.pageTitle }}</h6>
            <JotboxCard
              :contactNumber="0"
              :contactId="contactId"
              :title="title"
              :submitDate="submitDate"
              :selectedValues="selectedValues"
              :loading="loading"
              :isContactPage="true"
            />
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner nav">
            <UIButton use="outline" data-dismiss="modal">
              Close
            </UIButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">

import Vue from 'vue'
import axios from "axios";
import config from '@/config'
import JotboxCard from '../../pages/marketing/jotbox/JotboxCard.vue'
import {
  CustomField
} from '@/models'

declare var $: any
const standradField = [
  'full_name',
  'first_name',
  'last_name',
  'phone',
  'address',
  'city',
  'postal_code',
  'organization',
  'state',
  'email',
  'website',
  'source',
  'date_of_birth'
]
const deletedField = ['contact_id', 'formId', 'location_id', 'funneEventData']
export default Vue.extend({
  props: ['values'],
  components: {
    JotboxCard
  },
  data() {
    return {
      fetchedLocationId: undefined,
      customFields: [] as CustomField [],
      contactId: 0,
      loading: true,
      isPrefetchedData: false,
      activity: {},
      locationId: null,
      submitDate: null,
      title: null,
      selectedValues: []
    }
  },
  methods: {
    async fetchCustomField() {
      this.loading = true;
      if (this.fetchedLocationId !== this.locationId) {
        const snapshot = await CustomField.getByLocationId(this.locationId).get()
        if (! snapshot.empty) {
          this.customFields = snapshot.docs.map(d => new CustomField(d))
        }
        this.fetchedLocationId = this.locationId
      }

      if (! this.isPrefetchedData) {
        this.fetchData()
      } else {
        this.getSubmittedDetails({
          others: this.activity
        })
      }

    },
    async fetchData() {
      let url;
      if (this.activity.type == 'survey') {
        url = `${config.attributionUrl}/survey/${this.locationId}/submission/${this.activity.marketingId}`
      } else {
        url = `${config.attributionUrl}/form/${this.locationId}/submission/${this.activity.marketingId}`
      }
      this.contactId++
      axios.get(url)
        .then(({ data }) => {
          if (data.records) {
            this.getSubmittedDetails(data.records)
          } else {
            this.loading = false
          }
        }).catch(err => {
          this.loading = false
        })
    },
    getSubmittedDetails(item: any) {
      const dateAdded = item.createdAt
      const selectedData = Object.assign({}, item.others)
      for (var key in selectedData) {
        if (!(standradField.includes(key) || deletedField.includes(key))) {
          this.customFields.forEach(item => {
            if (item.id === key) {
              selectedData[item.id] = {
                tag: item.data.field_name,
                value: this.parseValue(
                  selectedData[key]
                )
              }
            }
          })
        } else if (deletedField.includes(key)) {
          delete selectedData[key]
        }
      }

      this.contactId = item.contactId
      this.selectedValues = selectedData

      this.loading = false;
    },
    isFileType(value) {
      let flag = false;
      Object.keys(value).forEach((id) => {
        if(value[id].hasOwnProperty('meta')) {
          flag = true;
        }
      })
      return flag;
    },
    parseValue(value: any) {
      let parseVal = ''
      if (!value) {
        return parseVal;
      }

      if (typeof value === 'object' && !Array.isArray(value)) {
        if (value.hasOwnProperty('meta')) {//Old File type or Signature
          if(value.meta.isSignature) {
            return value;
          }
          let obj = {}
          obj["id"] = value;
          obj.fileType = true;

          return obj;
        } else if (this.isFileType(value)) {
          value.fileType = true;
          return value;
        }

        for (const key in value) {
          if (value[key] !== '') {
            parseVal += value[key] + ' '
          }
        }
        return parseVal
      } else if (Array.isArray(value)) {
        value.forEach(item => {
          if (typeof item === 'object') {
            parseVal += this.parseValue(item)
          } else {
            parseVal += item + ' '
          }
        })
        return parseVal
      } else {
        return value
      }
    }
  },
  watch: {
    values(values: { [key: string]: any }) {
      if (values.visible) $(this.$refs.modal).modal('show')
      else $(this.$refs.modal).modal('hide')
      if (values.visible) {
        this.activity = values.activity
        this.locationId = values.locationId
        this.isPrefetchedData = values.isPrefetchedData
        this.fetchCustomField()
      }
    }
  },
  updated() {
    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hidden')
    })

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  }
})
</script>

<style lang="scss">
  #submission-detail-modal {
    .modal-header {
      .modal-header--inner {
        margin-left: 0;
        margin-right: 0;
      }
    }
    .modal-body {
      padding: 20px 0;

      .modal-body--inner {
        max-width: 100%;

        .row {
          .user-info-area {
            padding-left: 5px;
            padding-right: 5px;
            .user-info-header {
              padding-left: 10px;
            }
            .user-info-submission-header {
              display: none;
            }
          }
        }

      }
    }
  }
</style>
