<template>
  <div
    id="chat-connect"
    class="launchpad-item-sub-container bg-white my-1"
    :class="{ 'launchpad-bottom-border': isAdmin }"
  >
    <div class="items-tiles mx-3 my-2">
      <img
        class="integration-icon mx-4"
        :class="{ hl_disable_action: isStripeConnected }"
        src="https://static.msgsndr.com/img/stripe_logo.5a4dee84.png"
      />
      <div class="item-info">
        <div class="font-normal text-sm text-gray-500" v-if="reconnect">
          Stripe Connect appears to be broken.
        </div>
        <div
          class="font-normal text-sm text-gray-500"
          v-else-if="isStripeConnected"
        >
          Congratulations! Stripe is connected to your account.
        </div>
        <div
          class="font-normal text-sm text-gray-500"
          v-else-if="!isStripeConnected"
        >
          Connect your Stripe account to start accepting payments.<br />
          <span style="font-size: 12px; font-style: italic; color: #718096"
            >(Existing stripe API integration will continue to work, but it is
            advised to use Stripe Connect for more security)</span
          >
        </div>
      </div>
      <div class="flex flex-col justify-center item-end content-end w-40">
        <div
          class="flex flex-row justify-end item-end content-end w-full pr-4 pl-4"
        >
          <button
            id="launchpad-stripe-connect"
            v-if="!sending"
            class="green-btn"
            :class="{
              'green-filled-button green-btn connetGoogle': !isStripeConnected,
              'btn btn-circle success-green-btn': isStripeConnected,
            }"
            @click="connect"
          >
            <span v-if="!isStripeConnected">{{ btnText }}</span>
            <i v-else class="fa fa-check"></i>
          </button>
          <moon-loader
            v-if="sending"
            :loading="sending"
            color="#37ca37"
            size="30px"
          />
        </div>
      </div>
    </div>
    <AlterTriangle
      v-if="reconnect"
      width="30px"
      height="30px"
      style="position: absolute; right: -44px"
      src="https://static.msgsndr.com/img/alert-triangle.0e06f0da.svg"
    />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { Location, LocationStripeConnect, User } from '@/models'
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'
import {
  updateCachingIndex,
  cacheUpdateEvents,
} from '../../../util/caching.helper'
import { PaymentServices } from '@/services'

let stripeConnectWindow: Window | null
// import AlterTriangle from '@/assets/pmd/img/launchpad/alert-triangle.svg'
import { defineComponent } from '@vue/composition-api'
export default defineComponent({
  props: ['currentLocationId', 'userid'],
  // components:{AlterTriangle},
  data() {
    return {
      sending: false,
      location: {} as Location,
      disableHandler: {
        action: 'stripe',
        disable: false,
      },
    }
  },
  async mounted() {
    this.location = new Location(
      await this.$store.dispatch(
        'locations/getCurrentLocation',
        this.currentLocationId
      )
    )
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    isStripeConnected() {
      return (
        this.$store.state.stripeConnect.accountId &&
        !this.location?.launchpad?.is_stripe_disconnected
      )
    },
    reconnect():boolean | undefined {
      return this.location?.launchpad?.is_stripe_disconnected
    },
    btnText(): string {
      let btnTxt = 'Connect'
      if (this.reconnect) {
        btnTxt = 'Reconnect'
      }
      return this.sending ? 'Connecting...' : btnTxt
    },
    isAdmin() {
      return this.user && this.user.role == User.ROLE_ADMIN
    },
  },
  methods: {
    async connect() {
      this.disableHandler.disable = true
      this.$root.$emit(
        'disable-launchpad-action',
        JSON.stringify(this.disableHandler)
      )
      this.sending = true
      try {
        const { data } = await PaymentServices.connectStripe({
          locationId: this.location?.id
        })
        stripeConnectWindow = window.open(data.link, '_blank')
        const timer = setInterval(() => {
          if (stripeConnectWindow?.closed) {
            clearInterval(timer)
            this.refreshData()
            this.disableHandler.disable = false
            this.$root.$emit(
              'disable-launchpad-action',
              JSON.stringify(this.disableHandler)
            )
          }
        }, 1000)
      } catch (err) {
        this.disableHandler.disable = false
        this.$root.$emit(
          'disable-launchpad-action',
          JSON.stringify(this.disableHandler)
        )
        this.sending = false
      }
    },
    async refreshData() {
      await updateCachingIndex({
        index_type: cacheUpdateEvents.STRIPE_SETTINGS,
        event_origin: this.location?.id,
      })
      this.$store.dispatch('stripeConnect/syncAll', this.location.id)
      const stripeAccount = await LocationStripeConnect.forLocation(
        this.location.id
      )
      if (stripeAccount) {
        await this.location.ref.update({
          'launchpad.is_stripe_disconnected': false,
        })
        this.location = new Location(
          await this.$store.dispatch(
            'locations/getCurrentLocation',
            this.currentLocationId
          )
        )
      }
      this.sending = false
      this.$root.$emit('launchpad-status')
    },
  },
})
</script>
