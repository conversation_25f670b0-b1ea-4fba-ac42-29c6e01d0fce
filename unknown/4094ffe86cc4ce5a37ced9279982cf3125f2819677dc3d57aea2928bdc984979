<template>
	<div>
		<div class="form-group">
			<label>Notes</label>
      <div class="custom-dropdown-menu">
        <NestedMenu
          class="my-2"
          v-show="displayMenu"
          :items="menuItems"
          :title="'Merge fields'"
          v-on:MenuSelected="menuItemSelected"
          :fromAction="true"
        />
			<editor
				api-key="a62fc67jmelptppyvtbxwvtesdksxsumzvj5l3q16kalszes"
				:init="editorOptions"
				v-model="tinymcehtml"
        :id='getRandomId'
        ref='editor'
        name="notesEditor"
				v-validate="'handlebars'"
        data-vv-validate-on="input"
			></editor>
      </div>
      <span v-show="errors.has('notesEditor')" class="--red">{{errors.first('notesEditor')}}</span>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
import Editor from '@tinymce/tinymce-vue';
import { mapState } from 'vuex';
import { EventBus } from '@/models/event-bus'
import { FilterMergeTags } from '../../../util/filter_merge_tags'
import {
	CustomField
} from "@/models";

import NestedMenu from '@/pmd/components/NestedMenu.vue'

export default Vue.extend({
	props: ['action', 'editorOptions', 'triggerType', 'displayMenu'],
	components: { 'editor': Editor, NestedMenu },
	data() {
		return {
      tinymcehtml: '',
			body: '',
			currentLocationId: '',
      menuItems: [] as any[],
      customFields: [] as { [key: string]: any }[]
		}
	},
	methods: {
    setValue(field: string, value: string) {
      Vue.set(this.action, field, value)
      this.$emit('update:action', this.action)
    },
    menuItemSelected(item) {
      console.log("From method" + item);
      EventBus.$emit('customFieldSelected', item);
    }
  },
  computed: {
    getRandomId() {
      return 'add_note_' + Math.random().toString(36).substring(7);
    }
  },
  watch: {
    tinymcehtml(value: string) {
      var div = document.createElement("div");
      value = value.replace(new RegExp('<br/>', 'g'), '\n');
      value = value.replace(new RegExp('<br />', 'g'), '\n');
			div.innerHTML = value;
			this.body = (div.textContent || div.innerText || "").replaceAll(String.fromCharCode(160), ' '); // to remove &nbsp
			this.setValue('html', this.body);
    },
    async triggerType() {
      this.menuItems = await FilterMergeTags.resyncList(this.menuItems, this.customFields, this.triggerType);
    },
    action() {
      if (this.action.html && this.body !== this.action.html) {
        const editor = tinymce.get(this.$refs.editor.id);
        editor.setContent(this.action.html.replace(/\n/g, '<br/>'));
      }
    }
  },
	async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id;

    const fields = await CustomField.getByLocationIdAndType(this.currentLocationId, 'contact');
    this.customFields = fields.map(field => {
      return { text: field.name, value: `{{${field.fieldKey}}}` }
    });
    this.menuItems = await FilterMergeTags.syncList(this.currentLocationId, this.customFields, this.menuItems)
    this.menuItems = await FilterMergeTags.resyncList(this.menuItems, this.customFields, this.triggerType)

		if (this.action.html) this.tinymcehtml = this.body = this.action.html.replace(/\n/g, '<br/>');
	}
})
</script>

