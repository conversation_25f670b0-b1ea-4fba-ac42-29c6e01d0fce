<template>
  <textarea
    class="hl-text-area-input  text-gray-800 shadow-sm block w-full focus:outline-none focus:ring-offset-curious-blue-500 focus:border-curious-blue-500 sm:text-sm border-gray-300 rounded-md disabled:opacity-50"
    :value="value"
    placeholder="Product Description"
    name="description"
    @keyup="$emit('keyup', $event.target.value)"
    @change="$emit('change', $event.target.value)"
    @input="$emit('input',$event.target.value)"
    @blur="$emit('blur',  $event.target.value)"
    @focus="$emit('focus',  $event.target.value)"
    @keyup.enter.exact="$emit('enter',  $event.target.value)"
    @keydown.enter.shift.exact="$emit('shift-enter', $event.target.value)"
    :class="{'resize-none' : !expandable }"
  />
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  $_veeValidate: {
    value() {
      return this.value
    },
  },
  props: {
    value: String,
    expandable: {
      type: Boolean,
      default: true
    }
  },
})
</script>

<style scoped></style>
