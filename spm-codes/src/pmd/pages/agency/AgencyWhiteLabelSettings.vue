<template>
	<div class="hl_settings--body">
		<div class="container-fluid">
			<div class="row">
				<div class="hl_settings--main col-lg-8">
					<div class="card">
						<div class="card-header">
							<h3>White Label Settings</h3>
						</div>
						<div class="card-body" style="max-width: 100%;">
							<div class="personal-logo">
								<div class="col-sm-9">
									<p>We've tried to make white labeling as easy as possible with HighLevel. We really only have one step!</p>
									<br>Simply setup a CNAME for your preferred domain to point to "whitelabel.gohighlevel.com". For example you could use "app.yourdomain.com" and point this to "whitelabel.gohighlevel.com". That's it! We will automatically generate
									the SSL certificates when the first request is sent from your CNAME and renew all certificates automatically going forward. Welcome to white label!
								</div>
							</div>
							<form @submit.prevent="validateBeforeSubmit" data-vv-scope="form-2">
								<div class="form-group">
									<label>
										Step 1: Enter domain prefix (ex. https://
										<b>prefix</b>.mydomain.com):
									</label>
									<div class="form-input-group-item input-group mb-2 mr-sm-2">
										<input
											type="text"
											class="form-control"
											id="inlineFormInputGroupUsername2"
											placeholder="Username"
										>
										<div class="input-group-prepend">
											<div class="input-group-text">{{company.website}}</div>
										</div>
									</div>
								</div>
								<div class="form-group">
									<label>
										Step 1: Enter domain prefix (ex. https://
										<b>prefix</b>.mydomain.com):
									</label>
									<input type="text" data-lpignore="true" class="form-control" placeholder="Address" v-model="company.address">
									<div class="adress-preview"></div>
								</div>
								<div class="form-group">
									<label>Step 1: Enter domain name:</label>
									<input type="text" data-lpignore="true" class="form-control" placeholder="Address" v-model="company.address">
									<div class="adress-preview"></div>
								</div>
								<div class="form-group">
									<button type="submit" class="btn btn-primary">Update Address</button>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang='ts'>
import Vue from 'vue';
import libphonenumber from 'google-libphonenumber';
import { Company, Location, User, Notification, NotificationType } from '@/models';
const Avatar = () => import( '../../components/Avatar.vue');
const EditTeamMemberModal = () => import( '../../components/EditTeamMemberModal.vue');

var phoneUtil = libphonenumber.PhoneNumberUtil.getInstance();
var PNF = libphonenumber.PhoneNumberFormat;
declare var $: any;
let cancelCompanySubscription: () => void;


export default Vue.extend({
	components: { Avatar, EditTeamMemberModal },
	data() {
		return {
			company: {} as Company,
			currentLocationId: '' as string,
		}
	},
	watch: {
		'$route.params.location_id': function (id) {
			this.currentLocationId = id;
		},
	},
	updated() {
		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker('refresh');
		}
	},
	async created() {
		var _self = this;
		const authUser = await this.$store.dispatch('auth/get');
		this.company = await Company.getById(authUser.companyId);
	},
	beforeDestroy() {
		if (cancelCompanySubscription) cancelCompanySubscription();

	},
	async mounted() {
		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker('refresh');
		}
	},
	computed: {
	},
	methods: {
	}
});
</script>
