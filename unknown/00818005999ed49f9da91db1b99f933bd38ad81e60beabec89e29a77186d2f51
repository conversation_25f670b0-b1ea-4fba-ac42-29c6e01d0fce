<template>
	<Filters
		:conditions="conditions"
		:filterMaster="filterMaster"
		v-on:update:conditions="$emit('update:conditions', $event)"
	/>
</template>

<script lang="ts">
import Vue from 'vue'
import { Campaign, Formsurvey, Tag } from '@/models';
import { Condition } from '@/models/trigger';
import moment from 'moment-timezone';

const Filters = () => import( './Filters.vue');

export default Vue.extend({
	props: ['conditions'],
	components: { Filters },
	data() {
		return {
      tags: [],
			filterMaster: [
				{
          id: 'month-value',
					placeHolder: 'Select month',
					title: 'Month is',
					value: 'contact.birthMonth',
					valueType: 'text',
					type: 'select',
          options: [] as { [key: string]: any }[]
        },
        {
          id: 'day-value',
					placeHolder: 'Select day',
					title: 'Day is',
					value: 'contact.birthDay',
					valueType: 'text',
					type: 'select',
					options: [] as { [key: string]: any }[]
        },
        {
          id: 'before-days-in-number',
					placeHolder: 'Days in number',
					title: 'Before no. of days',
					value: 'contact.dateOfBirth',
          valueType: 'number',
          operator: 'time-diff-now-lte',
					type: 'input',
        },
        {
          id: 'after-days-in-number',
					placeHolder: 'Days in number',
					title: 'After no. of days',
					value: 'contact.dateOfBirth',
          valueType: 'number',
          operator: 'time-diff-now-gte',
					type: 'input',
				},
			],
		}
  },
  watch: {
    conditions: {
      handler: function(conditions) {
        this.conditionsChanged(conditions);
      },
      deep: true
    }
  },
  methods: {
    conditionsChanged: function (condition) {
      const monthOptions = lodash.find(condition, { field: 'contact.birthMonth' });
      if (
          monthOptions &&
          monthOptions.value &&
          lodash.some(this.filterMaster, {
            value: "contact.birthDay"
          })
        ) {
          const daysOptions = lodash.find(this.filterMaster, { value: 'contact.birthDay' });
          if (daysOptions) {
            daysOptions.options = [];
            for(let i = 1; i <= moment(monthOptions.value + 1, 'M').daysInMonth(); i++) {
              daysOptions.options.push({value: i, title: `${i}`});
            }
          }
        }
        else {
          const daysOptions = lodash.find(this.filterMaster, { value: 'contact.birthDay' });
          if(daysOptions) {
            daysOptions.options = [];
            for(let i = 1; i < 32; i++) {
              daysOptions.options.push({value: i, title: `${i}`});
            }
          }
        }

        if (
          lodash.find(condition, { id: 'before-days-in-number' })
        ) {
          if (lodash.find(this.filterMaster, { id: 'after-days-in-number' })) {
            let existingIndex = lodash.findIndex(this.filterMaster, { id: 'after-days-in-number' });
            if(existingIndex !== -1) {
              this.filterMaster.splice(existingIndex, 1);
            }
          }
        } else {
          let existingIndex = lodash.findIndex(this.filterMaster, { id: 'after-days-in-number' });
          if(existingIndex === -1) {
            this.filterMaster.push({
              id: 'after-days-in-number',
              placeHolder: 'Days in number',
              title: 'After no. of days',
              value: 'contact.dateOfBirth',
              valueType: 'number',
              operator: 'time-diff-now-gte',
              type: 'input',
            });
          }
        }

        if (
          lodash.find(condition, { id: 'after-days-in-number' })
        ) {
          if (lodash.find(this.filterMaster, { id: 'before-days-in-number' })) {
            let existingIndex = lodash.findIndex(this.filterMaster, { id: 'before-days-in-number' });
            if(existingIndex !== -1) {
              this.filterMaster.splice(existingIndex, 1);
            }
          }
        } else {
          let existingIndex = lodash.findIndex(this.filterMaster, { id: 'before-days-in-number' });
          if(existingIndex === -1) {
            this.filterMaster.push({
              id: 'before-days-in-number',
              placeHolder: 'Days in number',
              title: 'Before no. of days',
              value: 'contact.dateOfBirth',
              valueType: 'number',
              operator: 'time-diff-now-lte',
              type: 'input',
            });
          }
        }
    },
    async addTagsFilter() {
      this.tags = (await Tag.getByLocationId(this.$route.params.location_id)).map((tag)=>{
        return {
          title: tag.name,
          value: tag.name
        }
      })
      this.filterMaster.push({
        id: "has-tag",
        placeHolder: "Has Tag",
        title: "Has Tag",
        value: "contact.tags",
        valueType: "text",
        operator: "index-of-true",
        type: "select",
        options: this.tags
      })
    }
  },
	async created() {
		const currentLocationId = this.$router.currentRoute.params.location_id;
    const monthOptions = lodash.find(this.filterMaster, { value: 'contact.birthMonth' });
    if(monthOptions) {
      for(let i = 0; i < 12; i++) {
        monthOptions.options.push({value: i, title: moment(i+1, 'MM').format('MMMM')});
      }
    }
    this.conditionsChanged(this.conditions);
    this.addTagsFilter()
	}
})
</script>

