<template>
  <div
    id="gmb-connect"
    class="launchpad-item-sub-container launchpad-bottom-border bg-white my-1"
  >
    <div class="items-tiles mx-3 my-2">
       <img
        class="integration-icon mx-4"
        :class="{ hl_disable_action: isGMBConnected }"
        src="https://static.msgsndr.com/img/gmb.90d0fb06.svg"
      />
      <div class="item-info">
        <div
          class="font-normal text-sm text-gray-500"
          v-if="!isGMBConnected && reconnect && !slideShow"
        >
          Your GMB connection seems to have an issue, please reconnect to bring
          your GMB leads and engage with them and manage customer reviews!
        </div>
        <div
          class="font-normal text-sm text-gray-500"
          v-else-if="isGMBConnected"
        >
          Great! Leads and reviews from Google My Business will now show up in
          the system. You’ll be able to engage with them on the conversations
          page.
        </div>
        <div class="font-normal text-sm text-gray-500" v-else-if="!gmbAuth">
          Generate more leads from Google My Business! Monitor and reply to GMB
          conversations & reviews.
        </div>
        <div class="font-normal text-sm text-gray-500" v-else-if="gmbAuth">
          Complete Google My Business Connection.
        </div>
      </div>
      <div class="flex flex-col justify-center item-end content-end w-40">
        <div
          class="flex flex-row justify-end item-end content-end w-full pr-4 pl-4"
        >
          <button
            id="launchpad-gmb-connect"
            v-if="!processing"
            class="green-btn"
            :class="{
              'green-filled-button green-btn connetGoogle':
                !isGMBConnected && !slideShow,
              'btn btn-circle ligh-green-btn': slideShow,
              'btn btn-circle success-green-btn': isGMBConnected,
            }"
            @click="perfromAction"
            :style="disableButton"
          >
            <span v-show="!slideShow && !isGMBConnected">{{ btnText }}</span>
            <i class="fa fa-chevron-down" v-show="slideShow"></i>
            <i v-show="isGMBConnected" class="fa fa-check"></i>
          </button>
          <moon-loader
            v-if="processing"
            :loading="processing"
            color="#37ca37"
            size="30px"
          />
        </div>
      </div>
    </div>
    <transition name="slide">
      <div v-show="slideShow" class="gmb-mailer-grp mx-5 my-2">
        <div class="form-group gmb-mailer-from-group">
          <div class="form-group dropdown">
            <label class="gmb-input-label"
              >Which business do you want to link to this location?</label
            >
            <select
              class="selectpicker googleBusinessAccount"
              v-model="googleBusinessAccount"
              name="googleBusinessAccount"
              data-size="5"
            >
              <option value>Select Page</option>
              <option
                v-for="page in googleLocations"
                v-bind:key="page.id"
                :value="page"
                :selected="page.id === googleBusinessAccount.id"
              >
                {{ page.locationName }} -
                {{ page.address && page.address.locality }},
                {{ page.address && page.address.administrativeArea }}
              </option>
            </select>
            <span v-show="errors.has('googleBusinessAccount')" class="error">{{
              errors.first('googleBusinessAccount')
            }}</span>
          </div>
          <div class="form-group dropdown" v-if="analyticsAccounts.length">
            <label class="gmb-input-label">Select Analytics Account?</label>
            <select
              class="selectpicker analyticsAcc"
              v-model="analyticsAcc"
              name="analyticsAcc"
              data-size="5"
            >
              <option value>Choose one..</option>
              <option
                v-for="account in analyticsAccounts"
                v-bind:key="account.id"
                :value="account.id"
                v-text="account.name"
              ></option>
            </select>
            <span v-show="errors.has('analyticsAcc')" class="error">{{
              errors.first('analyticsAcc')
            }}</span>
          </div>
        </div>
        <div class="gmb-button-grp" @click.stop="">
          <button
            id="launchpad-gmb-link"
            class="green-filled-button green-btn"
            @click="linkBusinessPage"
            :class="{ invisible: sending }"
          >
            Done
          </button>
          <moon-loader
            class="loader-postion"
            :loading="sending"
            color="#37ca37"
            size="30px"
          />
        </div>
      </div>
    </transition>
    <AlertTriangleIcon
      v-if="reconnect && !isGMBConnected"
      width="30px"
      height="30px"
      src="https://static.msgsndr.com/img/alert-triangle.0e06f0da.svg"
      style="position: absolute; right: -44px"
    />
    <GoogleMyBusinessMakePrimaryModal
      :currentLocationId="currentLocationId"
      :modalShow="makePrimaryAccount"
      :googleConnection="makeGoogleConnectionPrimary"
    />
    <GoogleMyBusinessNoGmbPageModal :modalShow="noGmbAccount" />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import firebase from 'firebase/app'
import { Location, OAuth2 } from '@/models'
import config from '@/config'
import { trackGaEvent } from '@/util/helper'
import GmbIcon from '@/assets/pmd/img/gmb.svg'
// import AlertTriangleIcon from '@/assets/pmd/img/launchpad/alert-triangle.svg'
import GoogleMyBusinessMakePrimaryModal from './GoogleMyBusinessMakePrimaryModal.vue'
import GoogleMyBusinessNoGmbPageModal from './GoogleMyBusinessNoGmbPageModal.vue'

declare var $: any
let openedWindow: Window | null
let cancelGoogleSubscription: () => void

// Create IE + others compatible event handler
const eventMethod: string = window.addEventListener
  ? 'addEventListener'
  : 'attachEvent'
const win: any = window
const eventer = win[eventMethod]
const messageEvent = eventMethod == 'attachEvent' ? 'onmessage' : 'message'

// Listen to message from child window
eventer(
  messageEvent,
  function (e) {
    if (e.data && e.data.page === 'integrations_settings') {
      if (e.data.actionType === 'close' && openedWindow) {
        openedWindow.close()
      }
    }
  },
  false
)

export default Vue.extend({
  props: ['currentLocationId', 'userid'],
  components: {
    GmbIcon,
    // AlertTriangleIcon,
    GoogleMyBusinessMakePrimaryModal,
    GoogleMyBusinessNoGmbPageModal,
  },
  data() {
    return {
      googleLocations: [] as any,
      analyticsAccounts: [] as any,
      googleBusinessAccount: '',
      analyticsAcc: '',
      slideShow: false,
      sending: false,
      location: {} as Location,
      gmbAuth: false,
      googleConnections: [] as OAuth2[],
      connectLoader: false,
      disableHandler: {
        action: 'google',
        disable: false,
      },
      processing: false,
      noGmbAccount: false,
      makePrimaryAccount: false,
      makeGoogleConnectionPrimary: {} as OAuth2,
    }
  },
  watch: {
    '$route.params.location_id': async function (id) {
      this.location = new Location(
        await this.$store.dispatch('locations/getCurrentLocation', id)
      )
      if (!this.isGMBConnected) {
        this.processing = true
        try {
          await this.fetchGoogleData()
        } catch (err) {}
        this.processing = false
      }
      if (this.googleLocations.length > 0 && !this.location.gmb.name) {
        this.gmbAuth = true
      }
    },
  },
  computed: {
    isGMBConnected(): boolean | undefined {
      return this.location.gmb?.name && this.googleConnections.length ? true : false
    },
    btnText(): string {
      let btnTxt = 'Connect'
      if (this.reconnect) {
        btnTxt = 'Reconnect'
      }
      return this.connectLoader ? 'Connecting...' : btnTxt
    },
    reconnect(): boolean {
      return this.location.isGmbDisconnected
    },
    disableButton(): string {
      return this.slideShow ? 'pointer-events:none !important' : ''
    },
  },
  async mounted() {
    this.location = new Location(
      await this.$store.dispatch(
        'locations/getCurrentLocation',
        this.currentLocationId
      )
    )
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
    if (!this.isGMBConnected) {
      this.processing = true
      try {
        await this.fetchGoogleData()
      } catch (err) {}
      this.processing = false
    }
    if (this.googleLocations.length > 0 && !this.location.gmb.name) {
      this.gmbAuth = true
    }
    this.$root.$on('close-nogmb-modal', () => {
      this.noGmbAccount = false
    })
    this.$root.$on('google-connect', () => {
      this.connectGoogle()
    })
    this.$root.$on('close-modal', async () => {
      try {
        this.disableHandler.disable = false
        this.$root.$emit(
          'disable-launchpad-action',
          JSON.stringify(this.disableHandler)
        )
        await this.location.ref.update({
          recent_google_connection_id: firebase.firestore.FieldValue.delete(),
        })
        this.location = new Location(
          await this.$store.dispatch(
            'locations/getCurrentLocation',
            this.currentLocationId
          )
        )
        this.makePrimaryAccount = false
        this.noGmbAccount = false
        await this.fetchGoogleData()
      } catch (err) {}
    })
    this.$root.$on('gmb-primary-change', async () => {
      this.makePrimaryAccount = false
      this.noGmbAccount = false
      this.location = new Location(
        await this.$store.dispatch(
          'locations/getCurrentLocation',
          this.currentLocationId
        )
      )
      this.processing = true
      try {
        await this.fetchGoogleData()
        if (
          (this.googleLocations.length > 1 || this.analyticsAccounts > 1) &&
          !this.isGMBConnected
        ) {
          this.slideShow = true
          this.gmbAuth = true
        } else {
          this.disableHandler.disable = false
          this.$root.$emit(
            'disable-launchpad-action',
            JSON.stringify(this.disableHandler)
          )
        }
      } catch (err) {
        this.disableHandler.disable = false
        this.$root.$emit(
          'disable-launchpad-action',
          JSON.stringify(this.disableHandler)
        )
      }
      this.processing = false
    })
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
      if (
        this.company &&
        this.company.country == 'US' &&
        this.$refs.stateSelect
      ) {
        $(this.$refs.stateSelect).selectpicker('val', this.company.state)
      }
    }
  },
  methods: {
    fetchGoogleData(connect?: boolean) {
      return new Promise((resolve, reject) => {
        try {
          this.analyticsAccounts = []

          if (cancelGoogleSubscription) {
            cancelGoogleSubscription()
          }
          cancelGoogleSubscription = OAuth2.getAllByLocationIdAndType(
            this.currentLocationId,
            OAuth2.TYPE_GOOGLE
          ).onSnapshot(async (snapshot: firebase.firestore.QuerySnapshot) => {
            this.googleConnections = snapshot.docs.map(
              (d: firebase.firestore.QueryDocumentSnapshot) => new OAuth2(d)
            )
            let googleAccountId = this.location.primaryGoogleConnectionId
            if (connect && this.location.recentGoogleConnectionId) {
              googleAccountId = this.location.recentGoogleConnectionId
            }
            let googleConnection = this.googleConnections.find(
              x => x.id === googleAccountId
            )

            if (googleConnection) {
              try {
                let response = await this.$http.get('/gmb/get_locations', {
                  params: {
                    location_id: this.currentLocationId,
                    launchpad: connect ? true : false,
                  },
                })
                this.googleLocations = response.data
                if (!this.googleLocations.length && connect) {
                  this.noGmbAccount = true
                }
                if (
                  this.googleLocations.length &&
                  connect &&
                  googleAccountId !== this.location.primaryGoogleConnectionId
                ) {
                  this.noGmbAccount = false
                  this.makePrimaryAccount = true
                  this.makeGoogleConnectionPrimary = googleConnection
                }
              } catch (err) {
                console.log(err)
              }

              try {
                let { data } = await this.$http.get(
                  '/google/get_analytics_accounts',
                  {
                    params: {
                      oauth_id: googleConnection.id,
                    },
                  }
                )
                this.analyticsAccounts = data.data['items']
              } catch (err) {
                this.analyticsAccounts = []
              }
              if (this.googleLocations.length == 1)
                this.googleBusinessAccount = this.googleLocations[0]
              if (this.analyticsAccounts.length == 1) {
                this.analyticsAcc = this.analyticsAccounts[0].id
              }
              if (
                this.googleLocations.length == 1 &&
                this.analyticsAccounts.length <= 1 &&
                !this.isGMBConnected
              ) {
                this.disableHandler.disable = true
                this.$root.$emit(
                  'disable-launchpad-action',
                  JSON.stringify(this.disableHandler)
                )
                await this.linkBusinessPage()
              }
            }
            resolve(true)
          })
        } catch (error) {
          reject(error)
        }
      })
    },
    connectGoogle() {
      trackGaEvent(
        'Launchpad',
        'Initiate GMB connect',
        `Init GMB connect for location: ${this.currentLocationId}`
      )
      this.disableHandler.disable = true
      this.$root.$emit(
        'disable-launchpad-action',
        JSON.stringify(this.disableHandler)
      )
      this.connectLoader = true
      this.noGmbAccount = false
      this.makePrimaryAccount = false
      openedWindow = window.open(
        `${config.baseUrl}/api/gmail/start_oauth?location_id=${this.currentLocationId}&user_id=${this.userid}&launchpad=true`,
        'MyWindow',
        'toolbar=no, menubar=no,scrollbars=no,resizable=no,location=no,directories=no,status=no'
      )
      const timer = setInterval(async () => {
        if (openedWindow.closed) {
          clearInterval(timer)
          try {
            this.location = new Location(
              await this.$store.dispatch(
                'locations/getCurrentLocation',
                this.currentLocationId
              )
            )
            await this.fetchGoogleData(true)
          } catch (err) {
            // console.log(err)
          }
          this.connectLoader = false
          if (
            (this.googleLocations.length > 1 || this.analyticsAccounts > 1) &&
            !this.isGMBConnected &&
            !this.makePrimaryAccount
          ) {
            this.slideShow = true
            this.gmbAuth = true
          } else if (!this.googleLocations.length && !this.makePrimaryAccount) {
            this.disableHandler.disable = false
            this.$root.$emit(
              'disable-launchpad-action',
              JSON.stringify(this.disableHandler)
            )
          }
        }
      }, 500)
    },
    async linkBusinessPage() {
      if (this.sending) return
      this.sending = true
      let error = false
      if (!this.googleBusinessAccount) {
        this.$validator.errors.add({
          field: 'googleBusinessAccount',
          msg: 'Business required.',
        })
        error = true
      }

      if (error) {
        this.sending = false
        return false
      }

      try {
        // Moved from Created to Set function
        await this.$http.get('/gmb/delete_reviews', {
          params: {
            location_id: this.currentLocationId,
          },
        })
      } catch (err) {
        //
      }
      try {
        let name = this.googleBusinessAccount.locationName
        if (this.googleBusinessAccount.address) {
          name += ' - '
          if (this.googleBusinessAccount.address.locality)
            name += this.googleBusinessAccount.address.locality
          if (
            this.googleBusinessAccount.address.locality &&
            this.googleBusinessAccount.address.administrativeArea
          )
            name += ', '
          if (this.googleBusinessAccount.address.administrativeArea)
            name += this.googleBusinessAccount.address.administrativeArea
        }
        await this.$http.post('/gmb/link', {
          location_id: this.currentLocationId,
          gmb_location_id: this.googleBusinessAccount.name,
          gmb_location_name: name,
          gmb_places_id: this.googleBusinessAccount.locationKey.placeId,
          gmb_location_only_name: this.googleBusinessAccount.locationName,
          is_verified: this.googleBusinessAccount.locationState.isVerified,
          enable_call_tracking: false,
          launchpad: true,
        })
        const location = await Location.getById(this.currentLocationId)
        location.isGmbDisconnected = false
        if (this.analyticsAcc)
          location.googleAnalyticsAccountId = this.analyticsAcc

        if (location.deleted === undefined) {
          trackGaEvent(
            'Error',
            'Location save issue on gmb launchpad',
            location.id,
            1
          )
        }

        const updatedData = {
          is_gmb_disconnected: false,
          // google_analytics_account_id: this.analyticsAcc,
          date_updated: firebase.firestore.FieldValue.serverTimestamp(),
          last_updated_by: {
            source: 'gmb',
            channel: 'web_app',
          },
        } as any

        if (this.analyticsAcc) {
          updatedData.google_analytics_account_id = this.analyticsAcc
        }

        await location.ref.update(updatedData)

        this.location = new Location(
          await this.$store.dispatch(
            'locations/getCurrentLocation',
            this.currentLocationId
          )
        )
        trackGaEvent(
          'Launchpad',
          'Successfully GMB connected',
          `Successfully GMB connected for location: ${this.currentLocationId}`
        )
        this.$root.$emit('launchpad-status')
      } catch (err) {
        console.log(err)
      }
      this.sending = false
      this.disableHandler.disable = false
      this.$root.$emit(
        'disable-launchpad-action',
        JSON.stringify(this.disableHandler)
      )
      this.slideShow = false
    },
    connectPage() {
      this.disableHandler.disable = !this.disableHandler.disable
      this.$root.$emit(
        'disable-launchpad-action',
        JSON.stringify(this.disableHandler)
      )
      this.slideShow = !this.slideShow
    },
    perfromAction() {
      if (this.isGMBConnected) {
        return false
      } else if (this.gmbAuth) {
        this.connectPage()
      } else if (!this.gmbAuth) {
        this.connectGoogle()
      }
    },
  },
})
</script>
