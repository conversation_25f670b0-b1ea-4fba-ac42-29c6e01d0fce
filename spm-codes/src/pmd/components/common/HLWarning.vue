<template>
  <transition name="alert">
    <div
      v-if="this.showAlert && this.visibilityApproved"
      v-bind:class="[{ root: this.modal, transitionRoot: !this.modal }]"
    >
      <div
        v-bind:class="[
          { 'center-upgrade-warning': this.modal },
          `upgrade-warning`,
        ]"
      >
        <div class="alert-close-icon" @click="closeAlert" title="Dismiss">
          <i class="fa fa-times"></i>
        </div>
        <div
          class=""
          v-bind:class="[
            { 'center-billing-alert': this.modal },
            `billing-alert`,
          ]"
        >
          <h4 class="alert-heading">
            {{ !this.heading ? 'Hi ' + user.name : this.heading }}
          </h4>
          {{ this.title }}
          <br />
          <div class="info-message">
            {{ this.description }}
          </div>

          <div class="alert-actions">
            <slot name="action"></slot>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
import { User } from '@/models'

export default {
  props: [
    'showAlert',
    'locationId',
    'closeWarning',
    'heading',
    'title',
    'description',
    'modal',
  ],
  data() {
    return {
      visibilityApproved: false,
    }
  },

  watch: {
    showAlert: function() {
      if (this.showAlert) {
        this.showMe && this.showMe(this)
      }
    },
  },
  mounted() {
    if (this.showAlert) {
      this.showMe && this.showMe(this)
    }
  },
  computed: {
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
  },
  methods: {
    closeAlert() {
      this.$emit('closeWarning', 'someValue')
    },
  },
}
</script>

<style scoped>
.transitionRoot {
  transform: translateX(0);
  transition: all 0.8s ease-in-out;
  position: fixed;
  bottom: 100px;
  left: 100px;
  z-index: 1050;
}
.root {
  transform: translateY(0);
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1050;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
  background-color: rgba(0, 0, 0, 0.4);
}
.upgrade-warning {
  background-color: #ffffff;
  padding: 8px 50px 8px 8px;
  z-index: 1000;
  box-shadow: 0px 5px 10px 6px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  /* opacity: 0.9; */
  max-width: 500px;
}
.center-upgrade-warning {
  position: relative;
  transform: translate(-50%, -50%);
  top: 50%;
  left: 50%;
}

.alert-heading {
  /* text-transform: uppercase; */
  font-size: 22px;
}
.billing-alert {
  background-color: #ffffff;
  border-left: 4px solid #e93d3d;
  padding: 12px 12px 12px 20px;
  line-height: 20px;
}
.center-billing-alert {
  border-left: none;
}

.upgrade-btn {
  margin-right: 20px;
}
.alert-close-icon {
  position: absolute;
  top: 20px;
  right: 20px;
  cursor: pointer;
  font-size: 20px;
  line-height: 20px;
  color: #aaa;
  z-index: 5;
}
.alert-close-icon:hover {
  color: #777;
}
.alert-actions {
  margin: 20px 0px;
}

.info-message {
  /* border-left: 4px solid #198bf5;
  padding: 8px;
  background-color: #eaf3fe; */
  font-style: italic;
  opacity: 0.8;
}
/*
 * The following styles are auto-applied to elements with
 * transition="modal" when their visibility is toggled
 * by Vue.js.
 *
 * You can easily play with the modal transition by editing
 * these styles.
 */

.alert-enter {
  opacity: 0;
  transform: translateX(-100%);
}

.alert-leave-active {
  opacity: 0;
  transform: translateX(-100%);
}

.alert-enter .upgrade-warning,
.alert-leave-active .upgrade-warning {
  opacity: 0;
  /* transform: translateX(-100%); */
}
</style>
