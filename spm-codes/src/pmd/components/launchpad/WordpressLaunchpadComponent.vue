<template>
  <div
    id="chat-connect"
    class="launchpad-item-sub-container bg-white my-1 launchpad-bottom-border"
    v-if="showWordpressOption"
  >
    <div class="items-tiles mx-3 my-2">
      <img src="/pmd/img/icon-wordpress-logo.jpg" class="integration-icon mx-4" />
      <div class="item-info">
        <div class="font-normal text-sm text-gray-500">
          Move your WordPress site to our blazing fast and affordable hosting
          <div>
            <b v-if="wpInstalled"> Congratulations! Your wordpress website is on our servers</b>
          </div>
        </div>
      </div>
      <div class="flex flex-col justify-center item-end content-end w-40">
        <div
          class="
            flex flex-row
            justify-end
            item-end
            content-end
            w-full
            pr-4
            pl-4
          "
        >
          <button
            id="launchpad-wordpress"
            class="green-btn green-filled-button green-btn"
            @click="gotoWordpressPitch"
          >
            <span v-if="!wpInstalled">Connect</span>
            <i v-else class="fa fa-check"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: {
    location: {
      type: Object,
    },
    company: {
      type: Object,
    },
    user: {
      type: Object,
    },
  },
  computed: {
    showWordpressOption() {
      try {
        if (
          this.company &&
          this.location &&
          this.company.stripeConnectId &&
          this.location.wordpressReseller &&
          this.location.wordpressReseller.location_enabled &&
          this.company.wordpressReseller.valve 
        ) {
          return true
        }
      } catch (err) {
        console.error(err)
      }
      return false
    },

    wpInstalled() {
    return this.location &&  this.location.wordpressReseller.wp_id
    },
  },
  methods: {
    gotoWordpressPitch() {
      this.$router.push({ name: 'wordpress_dashboard' })
    },
  },
})
</script>
