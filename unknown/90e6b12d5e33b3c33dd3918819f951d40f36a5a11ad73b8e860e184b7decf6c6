<template>
  <Filters
    :conditions="conditions"
    :filterMaster="filterMaster"
    v-on:update:conditions="$emit('update:conditions', $event)"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import { Tag } from '@/models';
import { Condition } from '@/models/trigger';
const Filters = () => import( './Filters.vue');

export default Vue.extend({
  props: ['conditions'],
  components: { Filters },
  data() {
    return {
      filterMaster: [
        {
          placeHolder: 'Select Product',
          title: 'Select Product',
          value: 'product.id',
          valueType: 'text',
          type: 'select',
          options: [] as { [key: string]: any }[]
        }
      ]
    }
  },
  watch: {
    '$route.params.location_id': async function(id) {
      await this.$store.dispatch('membership/syncAll', {
        locationId: id,
        forceRefresh: false
      })
    }
  },
  async created() {
    const currentLocationId = this.$router.currentRoute.params.location_id
    await this.$store.dispatch('membership/syncAll', {
      locationId: currentLocationId,
      forceRefresh: false
    });
    const productOption = lodash.find(this.filterMaster, { value: 'product.id' });
    productOption.options = await this.$store.state.membership.products.map(product => {
      return {
        title: product.value,
        value: product.key
      }
    });
  }
})
</script>

