<template>
  <div class="form-control-group">
    <div class="dropdown my-2" style="width:100%">
      <a
        id="dropdownMenuButton"
        @click.stop.prevent="moreDispToggler.toggle()"
        data-toggle="dropdown"
        aria-haspopup="true"
        aria-expanded="false">
        Select {{ (option && option.filterName) || 'option' }}
        <i class="fas fa-caret-down --light"></i>
      </a>
      <div class="dropdown-menu"
        :id="moreDispToggler.id"
        aria-labelledby="dropdownMenuButton"
        style="min-height:50px; max-height: 300px; overflow: auto; max-width: 220px">
        <div class="dropdown-item pointer text-wrap text-break"
              style="max-width:200px"
              v-model="cached" v-for="s in cached"
              @click.stop.prevent="select(s)">
          <span class="text align-middle text-wrap text-break space-wrap-preline">
            {{
            s.value
            }}
          </span>
        </div>
      </div>
    </div>
    <div v-if="!selectedItems || selectedItems.length <= 0">
      <span class="--red">* Atleast one {{ filterName }} required</span>
    </div>
    <div class="tag-group py-2" v-if="selectedItems && selectedItems.length > 0">
      <div class="tag" v-for="tag in selectedItems">
        {{ tag.value }}
        <a @click="remove(tag)">
          <i class="icon icon-close"></i>
        </a>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { FilterOption, IKeyVal } from './vm/option_vm'
import { Tag } from '@/models'
import { SafeDropDownToggle } from './safe_drop_down_toggle'

export default Vue.extend({
  name: 'ArrayInputDropDown',
  props: {
    bus: Vue,
    option: FilterOption,
    listFetchParams: {}
  },
  data() {
    return {
      cached: [] as IKeyVal[],
      selectedItems: [] as IKeyVal[],
      locationId: '',
      filterName: this.option ? this.option.filterName.toLowerCase() : '',
      moreDispToggler: new SafeDropDownToggle(this, 'arrayDispToggler')
    }
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.locationId = id
      this.fetchData()
    },
    listFetchParams(v: any) {
      this.fetchData()
      this.$nextTick(() => {
        this.selectedItems = []
        this.publishChange()
      })
    }
  },
  methods: {
    async select(item: IKeyVal) {
      this.moreDispToggler.toggle()
      if (!item || this.selectedItems.includes(item)) return
      if (this.option.limitItems) this.selectedItems = []
      this.selectedItems.push(item)
      this.publishChange()
    },
    async fetchData() {
      if (this.option.listFetch) {
        this.cached = await this.option.listFetch({
          locationId: this.locationId,
          ...this.listFetchParams
        })
      }
    },
    remove(item: IKeyVal) {
      if (!this.selectedItems) return
      let idx = this.selectedItems.findIndex(a => a.key === item.key)
      if (idx !== -1) this.selectedItems.splice(idx, 1)
      this.publishChange()
    },
    publishChange() {
      this.option.firstValue = [...this.selectedItems]
      this.$emit('inputChange')
    }
  },
  async mounted() {
    this.locationId = this.$router.currentRoute.params.location_id
    await this.fetchData()
    if (this.option.firstValue) this.selectedItems = [...this.option.firstValue]
  }
})
</script>
