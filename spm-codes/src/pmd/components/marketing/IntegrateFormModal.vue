<template>
  <div class="tab-wrapper">
    <p class="sub-heading ml-3">
      Use the following options to integrate your custom form into your website
    </p>

    <section class="mt-3" id="tabs">
      <div class="container">
        <div class="row">
          <div class="col-xs-12 col-sm-12 col-md-12">
            <nav>
              <div class="nav nav-tabs nav-fill" id="nav-tab" role="tablist">
                <a
                  class="nav-item nav-link active"
                  id="nav-embed-tab"
                  data-toggle="tab"
                  href="#nav-embed"
                  role="tab"
                  aria-controls="nav-embed"
                  aria-selected="true"
                  >Embed</a
                >
                <a
                  class="nav-item nav-link"
                  id="nav-link-tab"
                  data-toggle="tab"
                  href="#nav-link"
                  role="tab"
                  aria-controls="nav-link"
                  aria-selected="false"
                  >Link</a
                >
              </div>
            </nav>
            <div class="tab-content py-3 px-3 px-sm-0" id="nav-tabContent">
              <div
                class="tab-pane fade show active"
                id="nav-embed"
                role="tabpanel"
                aria-labelledby="nav-embed-tab"
              >
                <div class="form-group">
                  <label for="embed" class="d-flex align-items-center"
                    >Iframe Embed
                    <svg
                      width="1.7em"
                      height="1.7em"
                      viewBox="0 0 16 16"
                      class="ml-2 bi bi-clipboard"
                      fill="currentColor"
                      xmlns="http://www.w3.org/2000/svg"
                      @click="copyToClipBoard"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M4 1.5H3a2 2 0 0 0-2 2V14a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V3.5a2 2 0 0 0-2-2h-1v1h1a1 1 0 0 1 1 1V14a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V3.5a1 1 0 0 1 1-1h1v-1z"
                      />
                      <path
                        fill-rule="evenodd"
                        d="M9.5 1h-3a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h3a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5zm-3-1A1.5 1.5 0 0 0 5 1.5v1A1.5 1.5 0 0 0 6.5 4h3A1.5 1.5 0 0 0 11 2.5v-1A1.5 1.5 0 0 0 9.5 0h-3z"
                      />
                    </svg>
                  </label>

                  <textarea
                    class="shadow-sm block w-full focus:outline-none focus:ring-offset-curious-blue-500 focus:border-curious-blue-500 sm:text-sm border-gray-300 rounded-md disabled:opacity-50"
                    rows="5"
                    id="embed"
                    readonly
                    >{{ iframeUrl }}

										</textarea>
                </div>
              </div>
              <div
                class="tab-pane fade"
                id="nav-link"
                role="tabpanel"
                aria-labelledby="nav-link-tab"
              >
                <div class="form-group">
                  <label for="link" class="d-flex align-items-center"
                    >Copy the link below and easily share it anywhere you wish.
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1.7em"
                      height="1.7em"
                      fill="currentColor"
                      class="bi bi-arrow-up-right-square"
                      viewBox="0 0 16 16"
                      @click="openLinkInNewTab"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M15 2a1 1 0 0 0-1-1H2a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V2zM0 2a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v12a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V2zm5.854 8.803a.5.5 0 1 1-.708-.707L9.243 6H6.475a.5.5 0 1 1 0-1h3.975a.5.5 0 0 1 .5.5v3.975a.5.5 0 1 1-1 0V6.707l-4.096 4.096z"
                      />
                    </svg>
                  </label>
                  <input
                    type="text"
                    class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800 disabled:opacity-50"
                    id="link"
                    readonly
                    :value="embedUrl"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import {
  Location,
  Contact,
  CustomField,
  ICustomField,
  FieldType,
  Company,
} from '@/models'
import config from '../../../config'
import { UxMessage } from '@/util/ux_message'
export default Vue.extend({
  components: {},
  props: {
    isSurvey: {
      default: false,
      type: Boolean,
    },
  },
  inject: ['uxmessage'],
  data() {
    return {
      baseUrl: '',
      embedUrl: '',
      iframeUrl: '',
      locationId: '',
      formId: '',
      surveyId: '',
    }
  },
  computed: {
    widgetId() {
      if (this.isSurvey) {
        return this.surveyId
      } else {
        return this.formId
      }
    },
    widgetName() {
      if (this.isSurvey) {
        return 'survey'
      } else {
        return 'form'
      }
    },
  },
  watch: {},
  updated() {},
  mounted() {},
  methods: {
    getUrlParams() {
      this.locationId = this.$router.currentRoute.params.location_id
      this.formId = this.$router.currentRoute.params.builder_id
      this.surveyId = this.$router.currentRoute.params.survey_id
    },
    createEmbedUrl() {
      if (config.mode === 'dev') {
        this.embedUrl = `http://localhost:3344/widget/${this.widgetName}/${this.widgetId}`
      } else {
        this.embedUrl =
          this.baseUrl + `/widget/${this.widgetName}/${this.widgetId}`
      }
      this.iframeUrl = `<iframe src="${this.embedUrl}" style="border:none;width:100%;" scrolling="no" id="${this.widgetId}"></iframe>\n<script src="${this.baseUrl}/js/form_embed.js"><\/script>`
    },
    copyToClipBoard() {
      navigator.clipboard.writeText(this.iframeUrl).then(
        () => {
          this.uxmessage(
            UxMessage.infoType('Copied to Clipboard', '', undefined),
            true
          )
        },
        err => {
          console.error('Async: Could not copy text: ', err)
        }
      )
    },
    openLinkInNewTab() {
      var win = window.open(this.embedUrl, '_blank')
      win.focus()
    },
  },
  created() {
    this.getUrlParams()
    this.$store.dispatch('company/getWhitelabelDomain').then(data => {
      this.baseUrl = data
      this.createEmbedUrl()
    })
  },
})
</script>

<style scoped>
.bi {
  position: absolute;
  cursor: pointer;
  font-weight: 900 !important;
}
.bi-arrow-up-right-square {
  left: 36rem;
  bottom: 2.8rem;
}
.bi-clipboard {
  left: 36rem;
  bottom: 3rem;
}
</style>
