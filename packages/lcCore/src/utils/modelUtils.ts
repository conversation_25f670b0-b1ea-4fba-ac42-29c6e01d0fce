/**
 * Model Utilities
 * 
 * Utility functions for model operations, data transformation, and validation
 * Based on patterns from SPM Model class and ModelUtils
 */

// Use individual lodash functions to avoid dependency issues
const isEqual = (a: any, b: any): boolean => {
  return JSON.stringify(a) === JSON.stringify(b);
};

const cloneDeep = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};

const pickBy = <T extends Record<string, any>>(
  obj: T,
  predicate: (value: any, key: string) => boolean
): Partial<T> => {
  const result: Partial<T> = {};
  for (const [key, value] of Object.entries(obj)) {
    if (predicate(value, key)) {
      result[key as keyof T] = value;
    }
  }
  return result;
};

/**
 * Date handling utilities
 * Based on SPM ModelUtils and Model class date handling
 */
export class DateUtils {
  /**
   * Convert various date formats to ISO string
   * Handles Firestore Timestamps, moment objects, Date objects, and numbers
   */
  static toISOString(date: any): string | undefined {
    if (!date) return undefined;
    
    try {
      // Handle Firestore Timestamp-like objects
      if (date && typeof date === 'object' && date._seconds) {
        return new Date(date._seconds * 1000 + (date._nanoseconds || 0) / 1000000).toISOString();
      }
      
      // Handle moment-like objects
      if (date && typeof date.toISOString === 'function') {
        return date.toISOString();
      }
      
      // Handle Date objects
      if (date instanceof Date) {
        return date.toISOString();
      }
      
      // Handle timestamp numbers
      if (typeof date === 'number') {
        return new Date(date).toISOString();
      }
      
      // Handle ISO strings
      if (typeof date === 'string') {
        return new Date(date).toISOString();
      }
      
      return undefined;
    } catch (error) {
      console.warn('Error converting date to ISO string:', date, error);
      return undefined;
    }
  }

  /**
   * Convert ISO string to Date object
   */
  static fromISOString(isoString: string | undefined): Date | undefined {
    if (!isoString) return undefined;
    
    try {
      return new Date(isoString);
    } catch (error) {
      console.warn('Error converting ISO string to Date:', isoString, error);
      return undefined;
    }
  }

  /**
   * Get current timestamp as ISO string
   */
  static now(): string {
    return new Date().toISOString();
  }

  /**
   * Check if a date is valid
   */
  static isValid(date: any): boolean {
    if (!date) return false;
    
    try {
      const converted = this.toISOString(date);
      return converted !== undefined && !isNaN(new Date(converted).getTime());
    } catch {
      return false;
    }
  }
}

/**
 * Data validation utilities
 * Based on SPM Model class data filtering and validation
 */
export class ValidationUtils {
  /**
   * Remove null and undefined values from an object
   * Based on SPM Model.data getter using lodash.pickBy
   */
  static cleanData<T extends Record<string, any>>(data: T): Partial<T> {
    return pickBy(data, (value) => value !== null && value !== undefined) as Partial<T>;
  }

  /**
   * Validate required fields are present
   */
  static validateRequired<T extends Record<string, any>>(
    data: T, 
    requiredFields: (keyof T)[]
  ): { isValid: boolean; missingFields: string[] } {
    const missingFields: string[] = [];
    
    for (const field of requiredFields) {
      const value = data[field];
      if (value === null || value === undefined || value === '') {
        missingFields.push(String(field));
      }
    }
    
    return {
      isValid: missingFields.length === 0,
      missingFields
    };
  }

  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone number format (basic)
   */
  static isValidPhone(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
    return phoneRegex.test(phone);
  }
}

/**
 * Change tracking utilities
 * Based on SPM Model class change detection using lodash.isEqual
 */
export class ChangeTracker<T extends Record<string, any>> {
  private originalData: T;
  private currentData: T;

  constructor(initialData: T) {
    this.originalData = cloneDeep(initialData);
    this.currentData = cloneDeep(initialData);
  }

  /**
   * Update the current data
   */
  updateData(newData: Partial<T>): void {
    this.currentData = { ...this.currentData, ...newData };
  }

  /**
   * Get the changes between original and current data
   * Returns only the fields that have changed
   */
  getChanges(): Partial<T> {
    const changes: Partial<T> = {};
    
    for (const key in this.currentData) {
      const currentValue = this.currentData[key];
      const originalValue = this.originalData[key];
      
      if (!isEqual(currentValue, originalValue)) {
        changes[key] = currentValue;
      }
    }
    
    return changes;
  }

  /**
   * Check if any changes have been made
   */
  hasChanges(): boolean {
    return Object.keys(this.getChanges()).length > 0;
  }

  /**
   * Get the original value of a specific field
   */
  getOriginalValue(key: keyof T): T[keyof T] {
    return this.originalData[key];
  }

  /**
   * Reset changes and update the original data
   */
  commitChanges(): void {
    this.originalData = cloneDeep(this.currentData);
  }

  /**
   * Discard changes and revert to original data
   */
  revertChanges(): void {
    this.currentData = cloneDeep(this.originalData);
  }

  /**
   * Get the current data
   */
  getCurrentData(): T {
    return cloneDeep(this.currentData);
  }

  /**
   * Get the original data
   */
  getOriginalData(): T {
    return cloneDeep(this.originalData);
  }
}

/**
 * Entity utilities for common model operations
 */
export class EntityUtils {
  /**
   * Generate a profile color based on entity data
   * Based on SPM User model's profileColor getter
   */
  static generateProfileColor(data: { name?: string; email?: string; phone?: string }): string {
    let str = data.name || '';
    if (data.phone) str += data.phone;
    if (data.email) str += data.email;
    
    let hash = 0;
    if (str.length === 0) return '#afb8bc';
    
    for (let i = 0; i < str.length; i++) {
      hash = str.charCodeAt(i) + ((hash << 5) - hash);
      hash = hash & hash; // Convert to 32bit integer
    }
    
    const shortened = Math.abs(hash) % 360;
    return `hsl(${shortened}, 35%, 60%)`;
  }

  /**
   * Create audit info object
   */
  static createAuditInfo(userId?: string, source = 'web_app', channel = 'lcCore'): {
    userId?: string;
    source: string;
    channel: string;
    timestamp: string;
  } {
    return {
      userId,
      source,
      channel,
      timestamp: DateUtils.now()
    };
  }

  /**
   * Prepare entity for creation
   * Adds required timestamps and audit info
   */
  static prepareForCreate<T extends Record<string, any>>(
    data: T,
    userId?: string
  ): T & {
    dateAdded: string;
    dateUpdated: string;
    deleted: boolean;
    createdBy: ReturnType<typeof EntityUtils.createAuditInfo>;
    lastUpdatedBy: ReturnType<typeof EntityUtils.createAuditInfo>;
  } {
    const now = DateUtils.now();
    const auditInfo = this.createAuditInfo(userId);
    
    return {
      ...data,
      dateAdded: now,
      dateUpdated: now,
      deleted: false,
      createdBy: auditInfo,
      lastUpdatedBy: auditInfo
    };
  }

  /**
   * Prepare entity for update
   * Updates timestamp and audit info
   */
  static prepareForUpdate<T extends Record<string, any>>(
    changes: Partial<T>,
    userId?: string
  ): Partial<T> & {
    dateUpdated: string;
    lastUpdatedBy: ReturnType<typeof EntityUtils.createAuditInfo>;
  } {
    return {
      ...changes,
      dateUpdated: DateUtils.now(),
      lastUpdatedBy: this.createAuditInfo(userId)
    };
  }

  /**
   * Prepare entity for soft delete
   */
  static prepareForDelete(userId?: string): {
    deleted: boolean;
    dateUpdated: string;
    deletedBy: ReturnType<typeof EntityUtils.createAuditInfo>;
  } {
    return {
      deleted: true,
      dateUpdated: DateUtils.now(),
      deletedBy: this.createAuditInfo(userId)
    };
  }
}
