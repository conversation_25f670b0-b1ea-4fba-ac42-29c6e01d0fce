const plans = [
  {
    saasProducts: [
      '2-way-text-messaging',
      'gmb-messaging',
      'web-chat',
      'reputation-management',
      'facebook-messenger',
      'gmb-call-tracking',
      'missed-call-text-back',
      'text-to-pay',
    ],
    snapshotId: '',
    stripePlans: [
      {
        id: 'price_1IfiZfGl2ry3wFNB1wFpCNhP',
        billingInterval: 'month',
        amount: 19700,
        currency: 'usd',
        symbol: '$',
        active: true,
        created: 1618306223,
        min: 9700,
      },
      {
        id: 'price_1IfiZfGl2ry3wFNB9AAeDGgd',
        billingInterval: 'year',
        amount: 197000,
        currency: 'usd',
        symbol: '$',
        active: true,
        created: 1618306223,
        min: 97000,
      },
    ],
    trialPeriod: 0,
    setupFee: 0,
    _id: '607564afeacbda16a7566e57',
    companyId: 'aj0N7hRGoHgOuWUP9vP4',
    title: 'Standard',
    description: 'Standard Subscription Plan',
    complementaryCredits: { amount: 5, type: 'oneTime' },
    twilioRebilling: { markup: 50, enabled: true },
    planLevel: 0,
    stripeProductId: 'prod_JIJCJYjST0USFE',
    createdAt: '2021-04-13T09:30:23.545Z',
    updatedAt: '2021-04-13T09:30:23.545Z',
    __v: 0,
  },
  {
    saasProducts: [
      '2-way-text-messaging',
      'gmb-messaging',
      'web-chat',
      'reputation-management',
      'facebook-messenger',
      'gmb-call-tracking',
      'missed-call-text-back',
      'text-to-pay',
      'calendar',
      'crm',
      'opportunities',
      'email-marketing',
    ],
    snapshotId: '',
    stripePlans: [
      {
        id: 'price_1IfiZfGl2ry3wFNB5t2nPWuW',
        billingInterval: 'month',
        amount: 39700,
        currency: 'usd',
        symbol: '$',
        active: true,
        created: 1618306223,
        min: 29700,
      },
      {
        id: 'price_1IfiZfGl2ry3wFNBuN67jdUS',
        billingInterval: 'year',
        amount: 397000,
        currency: 'usd',
        symbol: '$',
        active: true,
        created: 1618306223,
        min: 297000,
      },
    ],
    trialPeriod: 0,
    setupFee: 0,
    _id: '607564afeacbda16a7566e58',
    companyId: 'aj0N7hRGoHgOuWUP9vP4',
    title: 'Professional',
    description: 'Professional Subscription Plan',
    complementaryCredits: { amount: 10, type: 'oneTime' },
    twilioRebilling: { markup: 50, enabled: true },
    planLevel: 1,
    stripeProductId: 'prod_JIJCXbdV4qrJvh',
    createdAt: '2021-04-13T09:30:23.604Z',
    updatedAt: '2021-04-13T09:30:23.604Z',
    __v: 0,
  },
  {
    saasProducts: [
      '2-way-text-messaging',
      'gmb-messaging',
      'web-chat',
      'reputation-management',
      'facebook-messenger',
      'gmb-call-tracking',
      'missed-call-text-back',
      'text-to-pay',
      'calendar',
      'crm',
      'opportunities',
      'email-marketing',
      'funnels',
      'membership',
      'websites',
      'workflow',
      'all-reports',
    ],
    snapshotId: '',
    stripePlans: [
      {
        id: 'price_1IfiZfGl2ry3wFNBKrJgUO2I',
        billingInterval: 'month',
        amount: 59700,
        currency: 'usd',
        symbol: '$',
        active: true,
        created: 1618306223,
        min: 49700,
      },
      {
        id: 'price_1IfiZfGl2ry3wFNB98C1UEVa',
        billingInterval: 'year',
        amount: 597000,
        currency: 'usd',
        symbol: '$',
        active: true,
        created: 1618306223,
        min: 497000,
      },
    ],
    trialPeriod: 0,
    setupFee: 0,
    _id: '607564afeacbda16a7566e56',
    companyId: 'aj0N7hRGoHgOuWUP9vP4',
    title: 'Premium',
    description: 'Premium Subscription Plan',
    complementaryCredits: { amount: 100, type: 'oneTime' },
    twilioRebilling: { markup: 50, enabled: true },
    planLevel: 2,
    stripeProductId: 'prod_JIJCA6Z4lmWY05',
    createdAt: '2021-04-13T09:30:23.532Z',
    updatedAt: '2021-04-13T09:30:23.532Z',
    __v: 0,
  },
]

export default plans
