<template>
  <div>
    <div class="hl_conversations--message">
      <div class="hl_conversations--message-header">
        <div class="message-header-actions contact-detail-actions">
          <div
            class="pointer"
            style="display:inline-block;margin-right: 15px;"
            v-if="!$store.state.agencyTwilio.locationTwilioEmpty"
          >
            <i
              class="fa fa-phone"
              v-if="contact && contact.phone"
              @click.stop.prevent="makeCall"
            ></i>
          </div>

          <div>
            <div class="dropdown --no-caret">
              <a
                class="dropdown-toggle"
                data-toggle="dropdown"
                aria-haspopup="true"
                aria-expanded="false"
              >
                <template v-if="assignedUser">
                  {{ getAssignedUser }}
                  <i class="fa fa-caret-down"></i>
                </template>
                <template v-else>
                  <i class="icon icon-plus-circle"></i> Assign to
                </template>
              </a>
              <ul
                class="dropdown-menu"
                data-toggle="dropdown"
                style="min-height:50px; max-height:500px; overflow: auto;"
              >
                <li
                  v-if="assignedUser"
                  class="dropdown-item"
                  @click="assignedUser = null"
                >
                  <span style="color: #e03131">Remove assigned user</span>
                </li>
                <li
                  class="dropdown-item"
                  v-for="(user, userIndex) in assignees"
                  :key="userIndex"
                  @click="assignedUser = user.id"
                >
                  {{ user.fullName }}
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="message-header-actions">
          <button
            type="button"
            class="btn btn-light btn-sm"
            @click="toggleRead()"
            v-if="conversation"
          >
            <i
              class="fa fa-envelope-open"
              v-if="conversation.unreadCount > 0"
            ></i>
            <i class="fa fa-envelope" v-else></i>
            <span v-if="conversation.unreadCount > 0">&nbsp;Mark as Read</span>
            <span v-else>&nbsp;Mark as Unread</span>
          </button>
        </div>
      </div>
      <div class="hl_conversations--message-body">
        <conversation-component
          :conversation="conversation"
          :contact="contact"
          :messages="messages"
          :moreAvailable="moreAvailable"
          :fetching="fetching"
          :company="company"
          :manualSMS="manualSMS"
        ></conversation-component>
      </div>
    </div>
    <UseTemplateModal
      :showModal.sync="showTemplateModal"
      :currentLocationId="locationId"
      :type="templateType"
      @hidden="showTemplateModal = false"
      @selected="templateSelected"
    />
    <RequestPaymentModal
      :showModal.sync="showRequestPaymentModal"
      :currentLocationId="locationId"
      :contact="contact"
      @hidden="showRequestPaymentModal = false"
      @invoiceCreated="invoiceCreated"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'

import {
  Message,
  Conversation,
  Contact,
  Opportunity,
  Template,
  User,
  Company,
  Activity,
  ActivityType,
  ActionType
} from '@/models'
import SendMessageComponent from '../../components/conversations/SendMessageComponent.vue'
import UseTemplateModal from '../../components/UseTemplateModal.vue'
import RequestPaymentModal from '../../components/RequestPaymentModal.vue'
import moment from 'moment-timezone'
import firebase from 'firebase/app'
import lodash from 'lodash'
import { UserState, FileAttachment, CompanyState } from '@/store/state_models'
import PhoneNumber from '../../components/util/PhoneNumber.vue'
import ConversationComponent from './ConversationComponent.vue'
import ProfileComponent from './ProfileComponent.vue'
declare var $: any
import { EventBus } from '@/models/event-bus'

let cancelSubscription: () => void

export default Vue.extend({
  props: [
    'locationId',
    'conversation',
    'showprofile',
    'headerProperty',
    'optionForActions',
    'contact',
    'users',
    'manualSMS'
  ],
  components: {
    UseTemplateModal,
    SendMessageComponent,
    PhoneNumber,
    ConversationComponent,
    ProfileComponent,
    RequestPaymentModal
  },
  data() {
    return {
      assignedUser: '',
      opportunity: undefined as Opportunity | undefined,
      modalData: {},
      messages: [] as Message[],
      currentConversationId: '',
      fileSearchText: '',
      opportunityModalValues: {
        visible: false,
        contact: undefined as Contact | undefined,
        currentLocationId: '',
        tab: ''
      },
      showTemplateModal: false,
      templateType: 'sms',
      moreAvailable: false,
      fetching: false,
      isFirstSnapshot: true,
      isFirstLoadComplete: false,
      showRequestPaymentModal: false,
      messageType: ''
    }
  },
  watch: {
    async conversation(current, previous) {
      if (current && previous && current.id === previous.id) return
      if (cancelSubscription) cancelSubscription()
      this.isFirstLoadComplete = false
      this.isFirstSnapshot = true
      this.moreAvailable = false
      this.messages = []

      if (!current) return
      this.currentConversationId = current.id
      // await this.loadInitialMessages()
      await this.listenForUpdates()
    },
    contact() {
      if (this.contact && this.contact.assignedTo)
        this.assignedUser = this.contact.assignedTo
      else this.assignedUser = undefined // for issue reported by Tony Clubhouse: https://app.clubhouse.io/gohighlevel/story/3442
    },
    assignedUser(current, previous) {
      this.assignUser()
    }
  },
  computed: {
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      }
    }),
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    isAdmin(): boolean {
      return this.user && this.user.role === 'admin'
    },
    getConversationType(): string | undefined {
      if (!this.conversation) return
      switch (this.conversation.type) {
        case Conversation.TYPE_PHONE:
          return 'SMS'
        case Conversation.TYPE_EMAIL:
          return 'Email'
      }
    },
    getAssignedUser() {
      const user = <User | undefined>(
        lodash.find(this.users, { id: this.assignedUser })
      )
      if (user) return user.fullName
      else return ''
    },
    assignees() {
      if (this.onlyOwnData)
        return this.users.filter(
          user => user.role === 'admin' || user.id === this.user.id
        )
      else return this.users || this.localUsers
    },
    onlyOwnData() {
      return (
        this.user &&
        this.user.role === 'user' &&
        this.user.permissions.assigned_data_only === true
      )
    }
  },
  methods: {
    invoiceCreated(url: string) {
      if (this.messageType) {
        switch(this.messageType) {
          case 'sms':
            EventBus.$emit('sms_invoice_created', url);
            break;
          case 'email':
            EventBus.$emit('email_invoice_created', url);
            break;
          case 'fb':
            EventBus.$emit('fb_invoice_created', url);
            break;
          case 'gmb':
            EventBus.$emit('gmb_invoice_created', url);
            break;
          case 'insta':
            EventBus.$emit('insta_invoice_created', url);
            break;
        }
      }
    },
    async assignUser() {
      if (this.contact && this.assignedUser !== this.contact.assignedTo) {
        // update contact
        await this.contact.ref.update({
          assigned_to: this.assignedUser,
          date_updated: firebase.firestore.FieldValue.serverTimestamp()
        })
        this.contact.assignedTo = this.assignedUser
        // update activity log
        await this.addActivityLog(this.assignedUser)
      }
    },
    async addActivityLog(userId: string) {
      const activity = new Activity()
      activity.locationId = this.locationId
      activity.userId = this.user.id
      activity.contactId = this.contact.id
      activity.type = ActivityType.NOTE
      activity.actionType = ActionType.UPDATED
      activity.activityData = {
        body: `${this.contact.fullName} ${this.contact.type} assigned to ${this.getAssignedUser ? this.getAssignedUser : this.assignedUser }`
      }
      activity.dateUpdated = moment().utc()
      await activity.save()
    },
    makeCall() {
      if (this.contact && this.contact.phone)
        this.$root.$emit('makeCall', { phone_number: this.contact.phone })
    },
    scrollToBottom() {
      var container = this.$el.querySelector('.messages-group')
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },
    templateSelected(template: Template) {
      if (template && template.template) {
        template.template.id = template.id // append an identifier
      }
      if (template.type == Template.TYPE_SMS || !template.type) {
        EventBus.$emit('selected_sms_template', template.template)
      } else if (template.type == Template.TYPE_EMAIL) {
        EventBus.$emit('selected_email_template', template.template)
      }
    },
    scheduleAppointment() {
      if (!this.conversation || !this.contact) return
      this.modalData = {
        visible: true,
        contactId: this.contact.id
      }
    },
    moreOptions() {
      if (!this.conversation || !this.contact) return
      this.opportunityModalValues = {
        visible: true,
        contact: this.contact,
        currentLocationId: this.conversation.locationId,
        tab: 'OpportunityComponent'
      }
    },
    async loadMoreMessages(limit?: number) {
      if (!this.isFirstLoadComplete) return
      if (!this.currentConversationId) return

      this.moreAvailable = false
      this.fetching = true
      let query = Message.getAllMessages(
        this.currentConversationId,
        this.locationId
      )
      if (this.messages.length > 0) {
        let lastMessageSnapshot = this.messages[0].snapshot
        if (!lastMessageSnapshot) {
          const lastMessage = await Message.getById(this.messages[0].id)
          if (lastMessage && lastMessage.snapshot) {
            lastMessageSnapshot = lastMessage.snapshot
          }
        }
        if (!lastMessageSnapshot) {
          this.fetching = false
          return
        }
        query = query.startAfter(lastMessageSnapshot)
      }
      if (!limit) {
        limit = 21
      }
      query = query.limit(limit)
      const snapshot = await query.get()
      const newMessages = snapshot.docs
        .map((d: firebase.firestore.QueryDocumentSnapshot) => new Message(d))
        .reverse()
      if (newMessages.length === limit) {
        this.moreAvailable = true
        newMessages.splice(0, 1)
      }
      this.messages.unshift.apply(this.messages, newMessages)
      if (this.messages.length <= 21 || limit === 15) {
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
      this.fetching = false
    },
    async loadInitialMessages() {
      return
      this.fetching = true
      try {
        const { data } = await this.$http.get(
          `conversation/${this.currentConversationId}/messages`
        )
        if (data && data.messages) {
          this.messages = lodash
            .uniqBy(
              lodash.filter(data.messages, {
                conversation_id: this.currentConversationId
              }),
              'id'
            )
            .map(m => new Message(m))
            .reverse()

          if (data.messages.length >= 25) {
            this.moreAvailable = true
          } else {
            this.moreAvailable = false
          }
          this.$nextTick(() => {
            this.scrollToBottom()
          })
        }
      } catch (err) {
        this.messages = []
        this.moreAvailable = true
      }

      this.fetching = false
      this.isFirstLoadComplete = true
    },
    async listenForUpdates() {
      if (cancelSubscription) {
        cancelSubscription()
      }
      if (!this.currentConversationId) return
      if (this.isFirstSnapshot) {
        this.fetching = true
      }
      const conversationId = this.conversation.id
      cancelSubscription = Message.getAllMessages(
        this.currentConversationId,
        this.locationId
      )
        .limit(10)
        .onSnapshot((snapshot: firebase.firestore.QuerySnapshot) => {
          if (this.isFirstSnapshot) {
            this.messages = snapshot.docs.map(m => new Message(m)).reverse()

            this.isFirstLoadComplete = true

            if (this.messages.length >= 10) {
              this.moreAvailable = true
              this.loadMoreMessages(15)
            } else {
              this.moreAvailable = false
            }

            this.$nextTick(() => {
              this.scrollToBottom()
            })
            this.isFirstSnapshot = false
            this.fetching = false
            return
          }

          for (let i = snapshot.docChanges().length - 1; i >= 0; i--) {
            const docChange = snapshot.docChanges()[i]
            const newMessage = new Message(docChange.doc)
            let oldMessage = <Message | undefined>lodash.find(this.messages, {
              id: newMessage.id
            })

            if (oldMessage) {
              this.messages.splice(this.messages.indexOf(oldMessage), 1)
            }
            if (docChange.type === 'added' || docChange.type === 'modified') {
              const sortedIndex = lodash.sortedIndexBy(
                this.messages,
                newMessage,
                value => value.dateAdded.valueOf()
              )
              this.messages.splice(sortedIndex, 0, newMessage)
            }
          }
        })
    },
    async toggleRead() {
      if (this.conversation) {
        await this.conversation.toggleRead()
      }
    }
  },
  mounted() {
    EventBus.$on('select_sms_template', () => {
      this.templateType = 'sms'
      this.showTemplateModal = true
    })
    EventBus.$on('select_email_template', () => {
      this.templateType = 'email'
      this.showTemplateModal = true
    })
    EventBus.$on('request_payment', (type: string) => {
      this.messageType = type;
      this.showRequestPaymentModal = true
    })
  },
  beforeDestroy() {
    if (cancelSubscription) cancelSubscription()
    EventBus.$off('select_sms_template')
    EventBus.$off('request_payment')
  }
})
</script>
