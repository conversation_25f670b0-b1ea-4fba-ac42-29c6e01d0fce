<template>
  <modal
    v-if="modalShow"
    @close="closeModal"
    :maxWidth="500"
    :showCloseIcon="true"
    :noBackdropClose="true"
  >
    <div class="primary-container" style="padding-top: 40px">
      <div class="sub-title mt-2 mb-5">
        For you to connect GMB page, the connected account has to be primary. Do
        you want to make this account primary?
      </div>
      <div class="primary-btn">
        <button
          v-if="!loader"
          id="launchpad-gmb-primary-yes"
          class="btn btn-primary active"
          type="button"
          @click.once="makeItPrimary()"
        >
          Yes, continue
        </button>
        <button
          v-if="!loader"
          id="launchpad-gmb-primary-no"
          class="btn btn-primary"
          type="button"
          @click.once="tryagain"
        >
          No, connect a different account
        </button>
        <moon-loader
          v-if="loader"
          :loading="loader"
          color="#37ca37"
          size="20px"
        />
      </div>
    </div>
  </modal>
</template>
<script lang="ts">
import Vue from 'vue'
import Modal from '@/pmd/components/common/Modal.vue'
import { Location } from '@/models'
import firebase from 'firebase/app'

export default Vue.extend({
  props: ['modalShow', 'googleConnection', 'currentLocationId'],
  components: {
    Modal,
  },
  data() {
    return {
      popupWidth: 500,
      loader: false,
      location: {} as Location,
    }
  },
  async mounted() {
    this.location = new Location(
      await this.$store.dispatch(
        'locations/getCurrentLocation',
        this.currentLocationId
      )
    )
  },
  methods: {
    async closeModal() {
      this.$root.$emit('close-modal')
    },
    async makeItPrimary() {
      this.loader = true
      if (this.googleConnection.id) {
        this.location.primaryGoogleConnectionId = this.googleConnection.id
        this.location.googleAnalyticsViewId = undefined
        this.location.googleAnalyticsViewId = undefined
        this.location.googleAdwordsId = undefined
        await this.location.ref.update({
          primary_google_connection_id: this.googleConnection.id,
          google_analytics_view_id: firebase.firestore.FieldValue.delete(),
          google_adwords_id: firebase.firestore.FieldValue.delete(),
          date_updated: firebase.firestore.FieldValue.serverTimestamp(),
        })
        this.location = new Location(
          await this.$store.dispatch(
            'locations/getCurrentLocation',
            this.currentLocationId
          )
        )
      }
      this.$root.$emit('gmb-primary-change')
    },
    tryagain() {
      this.$root.$emit('google-connect')
    },
  },
})
</script>
<style>
.primary-container {
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: start;
  padding: 27px 30px;
  text-align: start;
  width: 100%;
}
.primary-container .sub-title {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 157.19%;
  color: #111827;
}
.primary-btn {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;
  width: 100%;
}
</style>
