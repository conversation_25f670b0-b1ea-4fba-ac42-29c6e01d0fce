/**
 * Base Entity Types and Interfaces
 * 
 * This file defines the foundational types and interfaces for all model entities
 * in the lcCore package, based on patterns from the SPM codebase.
 */

/**
 * Base properties that all entities should have
 * Matches the common patterns from SPM Model class
 */
export interface IBaseEntity {
  /** Unique identifier for the entity */
  id: string;
  /** Timestamp when the entity was created */
  dateAdded: string | Date;
  /** Timestamp when the entity was last updated */
  dateUpdated: string | Date;
  /** Soft delete flag */
  deleted: boolean;
  /** Additional metadata that might be present */
  [key: string]: any;
}

/**
 * Audit trail information for tracking entity changes
 * Based on SPM User model's CreatedOrUpdatedOrDeletedBy interface
 */
export interface IAuditInfo {
  /** ID of the user who performed the action */
  userId?: string;
  /** Source of the action (web_app, public_api, etc.) */
  source: string;
  /** Channel through which the action was performed */
  channel: string;
  /** Timestamp of the action */
  timestamp: string | Date;
}

/**
 * Extended base entity with audit trail information
 */
export interface IAuditableEntity extends IBaseEntity {
  /** Information about who created the entity */
  createdBy?: IAuditInfo;
  /** Information about who last updated the entity */
  lastUpdatedBy?: IAuditInfo;
  /** Information about who deleted the entity (if deleted) */
  deletedBy?: IAuditInfo;
}

/**
 * Firestore-specific metadata
 * Based on SPM Model class's Firestore integration
 */
export interface IFirestoreMetadata {
  /** Firestore document reference path */
  ref?: string;
  /** Whether this is a new record not yet saved */
  newRecord?: boolean;
  /** Type of model (firestore, es, raw) */
  modelType?: 'firestore' | 'es' | 'raw';
}

/**
 * Complete entity interface combining all base features
 */
export interface IEntity extends IAuditableEntity, IFirestoreMetadata {}

/**
 * Query options for entity retrieval
 * Based on common query patterns in SPM models
 */
export interface IQueryOptions {
  /** Maximum number of results to return */
  limit?: number;
  /** Number of results to skip (for pagination) */
  skip?: number;
  /** Search term for text-based filtering */
  search?: string;
  /** Whether to include deleted entities */
  includeDeleted?: boolean;
  /** Sort field */
  sortBy?: string;
  /** Sort direction */
  sortOrder?: 'asc' | 'desc';
}

/**
 * Standard response format for paginated queries
 */
export interface IPaginatedResponse<T> {
  /** Array of entities */
  data: T[];
  /** Total count of entities (before pagination) */
  total: number;
  /** Number of results returned */
  count: number;
  /** Current page/offset */
  skip: number;
  /** Page size */
  limit: number;
  /** Whether there are more results available */
  hasMore: boolean;
}

/**
 * Standard API response format
 */
export interface IApiResponse<T = any> {
  /** Whether the request was successful */
  success: boolean;
  /** Response data */
  data?: T;
  /** Error message if unsuccessful */
  message?: string;
  /** Additional error details */
  error?: any;
}

/**
 * Mutation response format for create/update/delete operations
 */
export interface IMutationResponse<T = any> extends IApiResponse<T> {
  /** The affected entity (for create/update operations) */
  entity?: T;
  /** Number of entities affected (for bulk operations) */
  affected?: number;
}

/**
 * Filter operators for query building
 * Based on Firestore query capabilities
 */
export type FilterOperator = 
  | '==' | '!=' | '<' | '<=' | '>' | '>=' 
  | 'in' | 'not-in' | 'array-contains' | 'array-contains-any';

/**
 * Query filter definition
 */
export interface IQueryFilter {
  /** Field to filter on */
  field: string;
  /** Filter operator */
  operator: FilterOperator;
  /** Value to filter by */
  value: any;
}

/**
 * Complex query definition
 */
export interface IQuery extends IQueryOptions {
  /** Array of filters to apply */
  filters?: IQueryFilter[];
  /** Fields to include in the response */
  select?: string[];
  /** Related entities to include */
  include?: string[];
}

/**
 * Real-time subscription options
 * For implementing Firestore listeners in RTK Query
 */
export interface ISubscriptionOptions {
  /** Whether to enable real-time updates */
  realtime?: boolean;
  /** Callback for handling real-time updates */
  onUpdate?: (data: any) => void;
  /** Callback for handling errors */
  onError?: (error: any) => void;
}

/**
 * Cache tag types for RTK Query invalidation
 * Based on entity relationships in SPM models
 */
export type CacheTagType = 
  | 'User' | 'Company' | 'Location' | 'Contact' | 'Opportunity'
  | 'Campaign' | 'Workflow' | 'CustomField' | 'Notification'
  | 'Team' | 'Permission' | 'Integration';

/**
 * Cache tag definition for RTK Query
 */
export interface ICacheTag {
  type: CacheTagType;
  id?: string | 'LIST';
}

/**
 * Base configuration for API slices
 */
export interface IApiSliceConfig {
  /** Reducer path for the API slice */
  reducerPath: string;
  /** Base URL for the API */
  baseUrl?: string;
  /** Cache tag types this slice manages */
  tagTypes: CacheTagType[];
  /** Whether to enable real-time subscriptions */
  enableSubscriptions?: boolean;
}
