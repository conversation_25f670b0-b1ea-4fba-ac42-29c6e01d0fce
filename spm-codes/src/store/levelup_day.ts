import { Module } from 'vuex'
import { LevelUpDayState, CompanyState, RootState } from '../store/state_models'

/*
Usage
-----

In your component/page add a computed variable

computed: {
    isEnabled(){
        return this.$store.getters['LevelUpDayFlag/isfeatursActive']
    }
}
*/

export const LevelUpDayFlag: Module<LevelUpDayState, RootState> = {
  namespaced: true,
  state: { 
    featuresActive: false,
    levelupDate: new Date("2021-10-21T19:00:00.000+03:00")
  },
  getters: {
    isfeatursActive(state: LevelUpDayState, getters: any, rootState: RootState, rootGetters: any){
        const now = new Date()
        const excemptedAccounts = [
            "YuTUZlUtrwBtvmgByZDW", //Production CompanyId, 
            "5DP4iH6HLkQsiKESj6rh" //Staging CompanyId
        ]
        const company = rootGetters['company/get']
        if (company?.id){
            if (now >= state.levelupDate || excemptedAccounts.indexOf(company.id)>-1){
                if (!state.featuresActive){
                    state.featuresActive = true
                }
            }
            return state.featuresActive
        }
        else{
            return false
        }
    }
  }
}
