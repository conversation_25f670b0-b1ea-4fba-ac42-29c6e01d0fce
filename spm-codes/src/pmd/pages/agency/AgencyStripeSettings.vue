<template>
  <div class="hl_settings--body">
    <div class="container-fluid">
      <div class="row">
        <div class="hl_settings--main col-lg-12">
          <div class="card hl_settings--team-management">
            <div class="card-header" style="padding: 20px 30px">
              <h3>Stripe Integration</h3>
              <div
                class="dashboard-visible-to__wrap"
                v-if="
                  company &&
                  company.data &&
                  company.data.stripe_connect_id &&
                  (company.saasSettings &&
                  company.saasSettings.stripe_connect_initiated_by
                    ? company.saasSettings.stripe_connect_initiated_by ===
                      user.id
                    : company.saasSettings &&
                      company.saasSettings.agency_dashboard_visible_to ===
                        'individual'
                    ? user.saasSettings &&
                      user.saasSettings.agency_dashboard_visible
                    : user.role === 'admin')
                "
              >
                Agency Dashboard visible to &nbsp;
                <b-dropdown
                  :text="selectedDashboardVisibleTo"
                  variant="light"
                  size="sm"
                >
                  <b-dropdown-item @click="setDashboardVisibleTo('individual')">
                    Only me
                  </b-dropdown-item>
                  <b-dropdown-item @click="setDashboardVisibleTo('admin')">
                    All Agency Admins
                  </b-dropdown-item>
                </b-dropdown>
              </div>
            </div>
            <div
              class="card-body"
              style="margin: unset; max-width: unset"
              v-if="company && company.data && !company.data.stripe_connect_id"
            >
              <div
                class="row"
                v-if="company && company.data && company.stripeSecretKey"
              >
                <div class="col-md-6">
                  <div class="form-group">
                    <label>Stripe Secret key</label>
                    <div class="stripe-secret-key__row">
                      {{ company.stripeSecretKey }}
                    </div>
                    <div class="stripe-secret-key__warning">
                      Will be deprecated soon. Switch to Stripe Connect
                    </div>
                    <!-- <input
                      type="text"
                      class="form-control"
                      placeholder="Stripe Secret key"
                      v-model="company.stripeSecretKey"
                      v-validate="'required'"
                      name="publishable-key"
                    >
                    <div v-show="errors.has('form-1.publishable-key')" class="invalid-feedback">
                      Publishable stripe key is required.
                    </div> -->
                  </div>
                  <!-- <div style="display: inline-block;position: relative;">
                    <button
                      :class="{invisible: processing}"
                      type="button"
                      class="btn btn-success"
                      @click="saveAccount"
                    >Save</button>
                    <div
                      style="position: absolute;left: 50%;transform: translate(-50%, -50%);top: 50%;"
                      v-show="processing"
                    >
                      <moon-loader :loading="processing" color="#37ca37" size="30px"/>
                    </div>
                  </div> -->
                </div>
                <div class="col-md-6">
                  <div class="stripe-connect__wrap">
                    <div class="stripe-connect__header">
                      <div class="stripe-connect__logo-wrap">
                        <StripeConnectIcon
                          class="stripe-connect__logo"
                        />
                      </div>
                      <div class="stripe-connect__info">
                        We recommend using stripe connect for better security
                      </div>
                    </div>
                    <div class="stripe-connect__body">
                      <button
                        type="button"
                        class="btn btn-primary"
                        disabled
                        v-if="connecting"
                      >
                        <moon-loader
                          :loading="connecting"
                          color="#ffffff"
                          size="16px"
                        />
                      </button>
                      <button
                        type="button"
                        class="btn btn-primary"
                        @click="connectAccount"
                        v-else
                      >
                        <i
                          class="fas fa-arrow-right"
                          style="margin-right: 12px"
                        ></i>
                        Switch to Stripe Connect
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else>
                <div class="stripe-connect__wrap">
                  <div class="stripe-connect__header">
                    <div class="stripe-connect__logo-wrap">
                      <StripeConnectIcon
                        class="stripe-connect__logo"
                      />
                    </div>
                    <div class="stripe-connect__info">
                      HighLevel will have access to your data, and can create
                      payments and customers on your behalf
                    </div>
                  </div>
                  <div class="stripe-connect__body">
                    <button
                      type="button"
                      class="btn btn-primary"
                      disabled
                      v-if="connecting"
                    >
                      <moon-loader
                        :loading="connecting"
                        color="#ffffff"
                        size="20px"
                      />
                    </button>
                    <button
                      type="button"
                      class="btn btn-primary"
                      @click="connectAccount"
                      v-else
                    >
                      Connect to your Stripe account
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div
              class="card-body"
              style="margin: unset; max-width: unset"
              v-if="company && company.data && company.data.stripe_connect_id"
            >
              <agency-stripe-connect-settings />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import { CompanyState } from '@/store/state_models'
import { Company, User } from '@/models'
import { trackGaEvent } from '@/util/helper'

const MoonLoader = () => import('@/pmd/components/MoonLoader.vue')
import AgencyStripeConnectSettings from './AgencyStripeConnectSettings.vue'
import StripeConnectIcon from '@/assets/pmd/img/billing/stripe-connect.svg'

export default Vue.extend({
  components: { MoonLoader, AgencyStripeConnectSettings, StripeConnectIcon },
  data() {
    return {
      // company: {} as Company,
      processing: false,
      publishableStripeKey: '',
      secretStripeKey: '',
      stripeConnectId: '',
      connecting: false,
      dashboardVisibleTo: 'admin',
    }
  },
  computed: {
    ...mapState('company', {
      company: s => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    selectedDashboardVisibleTo() {
      let value =
        this.company.saasSettings &&
        this.company.saasSettings.agency_dashboard_visible_to
          ? this.company.saasSettings.agency_dashboard_visible_to
          : this.dashboardVisibleTo
      return value === 'admin' ? 'All Agency Admins' : 'Only me'
    },
  },
  async created() {
    const authUser = await this.$store.dispatch('auth/get')
    // this.company = await Company.getById(authUser.companyId)
    if (
      this.company &&
      this.company.data &&
      this.company.data.stripe_connect_id
    ) {
      this.stripeConnectId = this.company.data.stripe_connect_id
      console.log(this.stripeConnectId)
    }
  },
  methods: {
    // async saveAccount() {
    //   this.processing = true
    //   await this.company.save() // avoid directly updating company document from store.
    //   this.processing = false
    // },
    async connectAccount() {
      this.connecting = true
      try {
        trackGaEvent('StripeConnect', this.company.id, 'Start OAuth', 1)
        const { data } = await this.saasService.get(
          `/connect/start_oauth?company_id=${this.company?.id}`
        )
        window.open(data.link, '_blank')
        await Company.collectionRef().doc(this.company.id).update({
          saas_settings: {
            ...this.company?.saasSettings,
            stripe_connect_initiated_by: this.user.id,
            agency_dashboard_visible_to: 'admin',
          },
        })
        await User.collectionRef().doc(this.user.id).update({
          saas_settings: {
            ...this.user?.saasSettings,
            stripe_connect_initiated: true,
          },
        })
      } catch (err) {
        //
      }
      this.connecting = false
    },
    async setDashboardVisibleTo(newValue: string) {
      this.dashboardVisibleTo = newValue
      if (newValue === 'individual') {
        await User.collectionRef().doc(this.user.id).update({
          saas_settings: {
            ...this.user?.saasSettings,
            agency_dashboard_visible: true,
          },
        })
      }
      await Company.collectionRef().doc(this.company.id).update({
        saas_settings: {
          ...this.company?.saasSettings,
          agency_dashboard_visible_to: newValue,
        },
      })
    },
  },
})
</script>

<style lang="scss">
.stripe-connect__wrap {
  background-color: #f7fafc;
  border: 1px solid #edf2f7;
  border-radius: 4px;
  padding: 24px;
}
.stripe-connect__header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}
.stripe-connect__logo-wrap {
  min-width: 64px;
  max-width: 64px;
  height: 64px;
  border-radius: 50%;
  box-shadow: 0px 4px 9px rgba(0, 0, 0, 0.03);
  background-color: #ffffff;
  margin-right: 24px;

  display: flex;
  justify-content: center;
  align-items: center;
}

.stripe-secret-key__row {
  background: #f0f5f8;
  border: 2px solid #e93d3d;
  box-sizing: border-box;
  border-radius: 3px;

  padding: 18px;

  color: #2d3748;
  opacity: 0.35;

  font-size: 14px;
  line-height: 16px;
}
.stripe-secret-key__warning {
  font-family: Roboto;
  font-style: italic;
  font-weight: normal;
  font-size: 12px;
  line-height: 14px;

  margin-top: 16px;

  color: #f56565;
}
.dashboard-visible-to__wrap {
  display: flex;
  align-items: center;
  .btn.btn-light {
    background-color: #ffffff !important;
  }
}
</style>
