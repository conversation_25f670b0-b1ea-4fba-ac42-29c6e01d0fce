<template>
  <div class="hl_settings--body">
    <div class="container-fluid">
      <div class="row">
        <div class="hl_settings--main col-lg-6">
          <div class="card">
            <div class="card-header">
              <h3>Company Data</h3>
              <div style="display:flex" v-if="company.relationshipNumber" >
                <UITextLabel>
                  Relationship Number
                  <span
                    style="margin-left: 5px"
                    class="input-group-addon"
                    v-b-tooltip.hover
                    title="Company Relationship Number"
                  >
                    <i class="fas fa-question-circle"></i>
                  </span>
                </UITextLabel>
                <span class="cursor-pointer ml-2" @click.prevent.stop="copyRelationshipNumber(company.relationshipNumber)">{{company.relationshipNumber}}</span>

                <i class="far fa-copy ml-2 --dark copier zoomable" style="align-self:center" id="copy-clipboard"  @click.prevent.stop="copyRelationshipNumber(company.relationshipNumber)"></i>
                <b-tooltip
                  triggers="click"
                  target="copy-clipboard"
                  :show.sync="copiedKey"
                >
                  Copied
                </b-tooltip>
              </div>

            </div>
            <div class="card-body">
              <div class="personal-logo">
                <vue2Dropzone
                  v-show="showDropZone"
                  class="picture drag_drop"
                  @vdropzone-file-added="vfileAdded"
                  :options="dropzoneOptions"
                  :include-styling="false"
                  id="profile_photo_dropzone"
                  ref="account_logo_file_drop_zone"
                  style="width: 350px; background-size: 24px"
                ></vue2Dropzone>
                <div
                  v-show="!showDropZone"
                  class="picture"
                  id="preview_container"
                  ref="preview_container"
                  :style="getPreviewImage"
                ></div>
                <div class="picture-text">
                  <h4>Company Logo</h4>
                  <p>The proposed size is 350px * 180px</p>
                  <p>no bigger than 2.5mb</p>
                  <div class="flex py-2">
                    <UIButton
                      id="profileChangePhoto"
                      use="secondary"
                      :disabled="!isAdmin"
                    >
                      Change
                    </UIButton>
                    <UIButton
                      type="button"
                      @click.stop="removeImage"
                      class="ml-2"
                      use="outline"
                      :disabled="!isAdmin"
                    >
                      Remove
                    </UIButton>
                  </div>
                </div>
              </div>
              <div class="form-group">
                <UITextInputGroup
                  label="Company Name"
                  type="text"
                  placeholder="Company Name"
                  v-model="company.name"
                  :disabled="!isAdmin"
                />
              </div>
              <form
                @submit.prevent="validateBeforeSubmit('company')"
                data-vv-scope="form-1"
              >
                <div class="form-group">
                  <UITextInputGroup
                    type="email"
                    label="Company Email"
                    placeholder="Company Email"
                    v-model="company.email"
                    :disabled="!isAdmin"
                  />
                </div>
                <div class="form-group">
                  <UITextLabel>Company Phone</UITextLabel>
                  <PhoneNumber
                    placeholder="Company Phone"
                    v-model="company.phone"
                    name="msgsndr1"
                    autocomplete="msgsndr1"
                    :disabled="!isAdmin"
                  />
                </div>
                <div class="form-group">
                  <UITextInputGroup
                    type="text"
                    label="Company Website"
                    placeholder="Company Website"
                    v-model="company.website"
                    :disabled="!isAdmin"
                  />
                </div>
                <div class="form-group">
                  <div style="display: inline-block; width: 96%">
                    <UITextInputGroup
                      label="Whitelabel Domain"
                      type="text"
                      placeholder="Whitelabel Domain"
                      v-model="company.domain"
                      :disabled="!isAdmin"
                    />
                  </div>
                  <span
                    v-if="user.type == 'agency'"
                    style="margin-left: 5px"
                    class="input-group-addon hl_help-article"
                    v-b-tooltip.hover
                    title="Click here for setup instructions"
                  >
                    <a
                      href="https://help.gohighlevel.com/support/solutions/articles/48000982207-white-label-the-desktop-app"
                      target="_blank"
                    >
                      <i class="fas fa-question-circle"></i>
                    </a>
                  </span>
                </div>
                <div class="form-group">
                  <div style="display: inline-block; width: 96%">
                    <UITextInputGroup
                      type="text"
                      label="API Domain"
                      placeholder="Whitelabel Domain"
                      v-model="company.sparedomain"
                      :disabled="!isAdmin"
                    />
                  </div>
                  <span
                    v-if="user.type == 'agency'"
                    style="margin-left: 5px"
                    class="input-group-addon hl_help-article"
                    v-b-tooltip.hover.html="
                      `Make sure to have cname record pointing to <b>${cnameRedirectURL}</b>`
                    "
                  >
                    <i class="fas fa-question-circle"></i>
                  </span>
                </div>
                <div class="form-group">
                  <div style="display: inline-block; width: 96%">
                    <UITextInputGroup
                      type="text"
                      label="Privacy Policy URL"
                      placeholder="Privacy Policy URL"
                      v-model="company.privacyPolicy"
                      :disabled="!isAdmin"
                    />
                  </div>
                  <span
                    style="margin-left: 5px"
                    class="input-group-addon"
                    v-b-tooltip.hover
                    title="A link to your custom privacy policy for your agency."
                  >
                    <i class="fas fa-question-circle"></i>
                  </span>
                </div>
                <div class="form-group">
                  <div style="display: inline-block; width: 96%">
                    <UITextArea
                      label="Custom Javascript"
                      type="text"
                      rows="5"
                      placeholder="Custom Javascript"
                      v-model="company.customJavascript"
                      :disabled="!isAdmin"
                    ></UITextArea>
                  </div>
                  <span
                    style="margin-left: 5px"
                    class="input-group-addon"
                    v-b-tooltip.hover
                    title="Custom javascript to run on your agency account."
                  >
                    <i class="fas fa-question-circle"></i>
                  </span>
                </div>
                <div class="form-group">
                  <div style="display: inline-block; width: 96%">
                    <UITextArea
                      type="text"
                      label="Custom CSS"
                      rows="5"
                      placeholder="Custom CSS"
                      v-model="company.customCSS"
                      :disabled="!isAdmin"
                    ></UITextArea>
                  </div>
                  <span
                    style="margin-left: 5px"
                    class="input-group-addon"
                    v-b-tooltip.hover
                    title="Custom css to run on your agency account. Don't add in <style> tags."
                  >
                    <i class="fas fa-question-circle"></i>
                  </span>
                </div>
                <div class="form-group">
                  <UITextLabel>Theme (Beta)</UITextLabel>
                  <select
                    class="selectpicker"
                    v-model="theme"
                    data-size="5"
                    :disabled="!isAdmin"
                  >
                    <option value="default">Default</option>
                    <option value="default-dark-v1">Default Dark v1</option>
                  </select>
                </div>

                <div class="form-group">
                  <UIButton type="submit" :loading="saving.company">
                    Update Company
                  </UIButton>
                </div>
              </form>
            </div>
          </div>
        </div>
        <div class="hl_settings--main col-lg-6">
          <div class="card">
            <div class="card-header">
              <h3>Company Address</h3>
            </div>
            <div class="card-body">
              <form
                @submit.prevent="validateBeforeSubmit('address')"
                data-vv-scope="form-2"
              >
                <div class="form-group">
                  <UITextInputGroup
                    type="text"
                    label="Address"
                    placeholder="Address"
                    v-model="company.address"
                    :disabled="!isAdmin"
                  />
                  <div class="adress-preview"></div>
                </div>
                <div class="row">
                  <div class="col-sm-8">
                    <div class="form-group">
                      <UITextInputGroup
                        type="text"
                        label="City"
                        placeholder="City"
                        v-model="company.city"
                        :disabled="!isAdmin"
                      />
                    </div>
                  </div>
                  <div class="col-sm-4">
                    <div class="form-group">
                      <UITextInputGroup
                        type="text"
                        label="Zip Code"
                        placeholder="Zip Code"
                        v-model="company.postalCode"
                        :disabled="!isAdmin"
                      />
                    </div>
                  </div>
                </div>
                <div class="form-group" v-show="company.country === 'US'">
                  <UITextLabel>State / Prov / Region *</UITextLabel>
                  <select
                    class="selectpicker"
                    title="State / Prov / Region"
                    v-model="company.state"
                    name="state"
                    data-size="5"
                    :disabled="!isAdmin"
                  >
                    <option value>Choose one..</option>
                    <option value="AL">Alabama</option>
                    <option value="AK">Alaska</option>
                    <option value="AZ">Arizona</option>
                    <option value="AR">Arkansas</option>
                    <option value="CA">California</option>
                    <option value="CO">Colorado</option>
                    <option value="CT">Connecticut</option>
                    <option value="DE">Delaware</option>
                    <option value="DC">District Of Columbia</option>
                    <option value="FL">Florida</option>
                    <option value="GA">Georgia</option>
                    <option value="HI">Hawaii</option>
                    <option value="ID">Idaho</option>
                    <option value="IL">Illinois</option>
                    <option value="IN">Indiana</option>
                    <option value="IA">Iowa</option>
                    <option value="KS">Kansas</option>
                    <option value="KY">Kentucky</option>
                    <option value="LA">Louisiana</option>
                    <option value="ME">Maine</option>
                    <option value="MD">Maryland</option>
                    <option value="MA">Massachusetts</option>
                    <option value="MI">Michigan</option>
                    <option value="MN">Minnesota</option>
                    <option value="MS">Mississippi</option>
                    <option value="MO">Missouri</option>
                    <option value="MT">Montana</option>
                    <option value="NE">Nebraska</option>
                    <option value="NV">Nevada</option>
                    <option value="NH">New Hampshire</option>
                    <option value="NJ">New Jersey</option>
                    <option value="NM">New Mexico</option>
                    <option value="NY">New York</option>
                    <option value="NC">North Carolina</option>
                    <option value="ND">North Dakota</option>
                    <option value="OH">Ohio</option>
                    <option value="OK">Oklahoma</option>
                    <option value="OR">Oregon</option>
                    <option value="PA">Pennsylvania</option>
                    <option value="RI">Rhode Island</option>
                    <option value="SC">South Carolina</option>
                    <option value="SD">South Dakota</option>
                    <option value="TN">Tennessee</option>
                    <option value="TX">Texas</option>
                    <option value="UT">Utah</option>
                    <option value="VT">Vermont</option>
                    <option value="VA">Virginia</option>
                    <option value="WA">Washington</option>
                    <option value="WV">West Virginia</option>
                    <option value="WI">Wisconsin</option>
                    <option value="WY">Wyoming</option>
                  </select>

                  <span v-show="errors.has('state')" class="error">{{
                    errors.first('state')
                  }}</span>
                </div>
                <div class="form-group" v-show="company.country !== 'US'">
                  <UITextInputGroup
                    type="text"
                    label="State / Prov / Region *"
                    placeholder="State"
                    v-model="company.state"
                    name="state"
                    :disabled="!isAdmin"
                  />
                </div>
                <div class="form-group">
                  <UITextLabel>Country</UITextLabel>
                  <select
                    class="selectpicker"
                    v-model="company.country"
                    data-size="5"
                    :disabled="!isAdmin"
                  >
                    <option value>Choose country...</option>
                    <option
                      v-for="(country, value) in countries"
                      :value="value"
                      v-text="country"
                    ></option>
                  </select>
                </div>
                <div class="form-group">
                  <UITextLabel>Time Zone *</UITextLabel>
                  <select
                    class="selectpicker"
                    v-model="company.timezone"
                    name="timezone"
                    data-size="5"
                    :disabled="!isAdmin"
                    v-validate="'required'"
                  >
                    <option value disabled>Choose one..</option>
                    <option
                      v-for="timezone in timezones"
                      :key="timezone.value"
                      :value="timezone.value"
                      v-text="timezone.label"
                    ></option>
                  </select>

                  <span v-show="errors.has('timezone')" class="error">{{
                    errors.first('timezone')
                  }}</span>
                </div>
                <div class="form-group">
                  <UIButton type="submit" :loading="saving.address">
                    Update Address
                  </UIButton>
                </div>
              </form>
            </div>
          </div>
          <AgencyOptimisationCard />
          <SidebarSettingsV2 v-if="isV2SidebarSettingsEnable" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import vue2Dropzone from 'vue2-dropzone'
import 'vue2-dropzone/dist/vue2Dropzone.min.css'
import AgencyOptimisationCard from "@/pmd/components/agency/AgencyOptimisationCard.vue";
import SidebarSettingsV2 from './SidebarSettingsV2.vue';
import firebase from 'firebase/app'
import {
  Company,
  Location,
  User,
  Notification,
  NotificationType,
} from '@/models'
const PhoneNumber = () => import('../../components/util/PhoneNumber.vue')
import timezones from '@/util/timezones.ts'
import countries from '@/util/countries'
import { UxMessage } from '@/util/ux_message'
import { mapState } from 'vuex'
import { UserState } from '../../../store/state_models'
import config from '@/config'

declare var $: any

interface IResizeImageOptions {
  maxSize: number
  file: File
}
const resizeImage = (settings: IResizeImageOptions) => {
  const file = settings.file
  const maxSize = settings.maxSize
  const reader = new FileReader()
  const image = new Image()
  const canvas = document.createElement('canvas')
  const dataURItoBlob = (dataURI: string) => {
    const bytes =
      dataURI.split(',')[0].indexOf('base64') >= 0
        ? atob(dataURI.split(',')[1])
        : unescape(dataURI.split(',')[1])
    const mime = dataURI.split(',')[0].split(':')[1].split(';')[0]
    const max = bytes.length
    const ia = new Uint8Array(max)
    for (var i = 0; i < max; i++) ia[i] = bytes.charCodeAt(i)
    let data = new Blob([ia], { type: mime })
    data['name'] = file.name
    return data
  }
  const resize = () => {
    let width = image.width
    let height = image.height

    if (width > height) {
      if (width > maxSize) {
        height *= maxSize / width
        width = maxSize
      }
    } else {
      if (height > maxSize) {
        width *= maxSize / height
        height = maxSize
      }
    }

    canvas.width = width
    canvas.height = height
    canvas.getContext('2d').drawImage(image, 0, 0, width, height)
    let dataUrl = canvas.toDataURL('image/png')
    return dataURItoBlob(dataUrl)
  }

  return new Promise((ok, no) => {
    if (!file.type.match(/image.*/)) {
      no(new Error('Not an image'))
      return
    }

    reader.onload = (readerEvent: any) => {
      image.onload = () => ok(resize())
      image.src = readerEvent.target.result
    }
    reader.readAsDataURL(file)
  })
}

export default Vue.extend({
  components: { vue2Dropzone, PhoneNumber, AgencyOptimisationCard, SidebarSettingsV2 },
  inject: ['uxmessage'],
  data() {
    return {
      dropzoneOptions: {
        autoQueue: false,
        url: '/file/post',
        addRemoveLinks: true,
        maxFilesize: 100,
        clickable: '#profileChangePhoto',
        dictDefaultMessage: '',
        dictRemoveFileConfirmation:
          'Are you sure you want to delete this file?',
        dictResponseError: 'Error uploading file!',
        previewsContainer: '#preview_container',
        previewTemplate: '<img data-dz-thumbnail/>',
      },
      authUser: {} as any,
      company: {} as Company,
      userInfo: {} as User,
      logoURL: null as File | null,
      currentLocationId: '',
      timezones: timezones,
      countries: countries,
      saving: {
        company: false,
        address: false,
        features: false
      },
      cnameRedirectURL: config.cnameWhitelabelURL,
      theme: '',
      copiedKey:false
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
    },
    'company.domain': function (val: string, before: string) {
      if (val && val.length > 0) {
        this.company.domain = val.toLowerCase().split(' ').join('')
      }
    },
    theme: function (val: string, before: string) {
      document.body.dataset.theme = val
      this.company.theme = val
    },
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  async created() {
    var _self = this
    this.authUser = await this.$store.dispatch('auth/get')
    this.userInfo = await User.getById(this.authUser.userId)
    this.company = await Company.getById(this.userInfo.companyId)
    this.theme = this.company.theme
  },
  async mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    showDropZone() {
      if (this.company && this.company.logoURL) {
        return false
      } else {
        return true
      }
    },
    isAdmin(): boolean {
      return this.userInfo && this.userInfo.role === 'admin'
    },
    getPreviewImage() {
      const styles: any = {}

      if (this.company.logoURL) {
        styles['background-image'] = 'url("' + this.company.logoURL + '")'
        // styles["background-size"] = "180px 350px"
      }

      styles['width'] = '350px'

      return styles
    },
    isV2SidebarSettingsEnable() {
      return this.$store.getters['sidebarv2/isSidebarSettingsEnable']
    }
  },
  methods: {
    async vfileAdded(file: any) {
      if (!this.isAdmin)
        return this.uxmessage(
          UxMessage.warningType(
            "You don't have permission to upload file. Check your permissions with your agency admin."
          )
        )
      const dropZone: any = this.$refs.account_logo_file_drop_zone
      dropZone.removeAllFiles()

      console.log('Added file.', file.dataURL)

      var _self = this
      var file_name = this.company.id

      var ref_string =
        '/companyPhotos/' + file_name + '.' + file.name.split('.').pop()
      var upload_path = firebase.storage().ref(ref_string)

      var fileType = file['type']
      var ValidImageTypes = ['image/gif', 'image/jpeg', 'image/png']

      var dataURL = URL.createObjectURL(file)

      file = await resizeImage({
        file: file,
        maxSize: 350,
      })
      let snapshot = await upload_path.put(file, {
        contentDisposition: `inline; filename="${file.name}"`,
      })
      console.log('Got snapshot:', snapshot)
      snapshot.ref.getDownloadURL().then(async function (downloadURL) {
        console.log('File available at', downloadURL)
        if (downloadURL) {
          _self.company.logoURL = downloadURL
          await _self.company.save()
        }
      })
    },

    async removeImage() {
      this.uxmessage(
        UxMessage.confirmationType(
          'Are you sure you want to delete this image?',
          async resp => {
            if (resp === 'ok') {
              this.logoURL = null

              var upload_path = firebase
                .storage()
                .refFromURL(this.company.logoURL)
              await upload_path.delete()
              this.company.logoURL = ''
              this.company.save()
            }
          }
        )
      )
    },
    async checkForValidCNAME(
      domain: string,
      urlType: string
    ): Promise<boolean> {
      let url
      if (urlType === 'API')
        url = `${config.baseUrl}/check-domain-linked/${domain}?whitelabel=true`
      else if (urlType === 'frontend')
        url = `${config.baseUrl}/check-allow-domain/frontend/${domain}`

      if (url) {
        try {
          const response = await this.$http.get(url)
          const { data, status } = response
          if (status === 200) {
            return true
          }
          return false
        } catch (error) {
          return false
        }
      } else return false
    },
    async validateBeforeSubmit(loaderType) {
      if (this.company.domain) {
        if (
          this.company.domain.includes('http') ||
          this.company.domain.includes('/')
        )
          return this.$uxMessage(
            'warning',
            "Whitelabel Domain must not have the full URL, just the domain. > Correct: app.gohighlevel.com | Wrong: https://app.gohighlevel.com/. Please remove any http declarations or /'s in the provided Whitelabel domain."
          )
        let duplicated
        try {
          const response = await this.$http.get(
            `${config.baseUrl}/check-duplicated-domain/`,
            {
              params: {
                domain: this.company.domain,
                company_id: this.company.id,
              },
            }
          )
          if (response.data.exists)
            return this.$uxMessage(
              'warning',
              `This Whitelabel Domain is used in ${
                response.data.multiple
                  ? 'multiple other companies.'
                  : 'another company.'
              }`
            )
        } catch (err) {
          // Issue with the endpoint, but might not affect anything here
          return console.error(err)
        }
        let domainError = `Couldn't find cname record pointing ${this.company.domain} to (${config.frontEndCNameRedirectURL})`
        try {
          const cnameValidated = await this.checkForValidCNAME(
            this.company.domain,
            'frontend'
          )
          if (!cnameValidated)
            return this.uxmessage(UxMessage.warningType(domainError))
        } catch (err) {
          console.error('error while checking cname --> ', err)
          return this.uxmessage(UxMessage.warningType(domainError))
        }
      }
      if (this.company.sparedomain) {
        if (
          this.company.sparedomain.includes('http') ||
          this.company.sparedomain.includes('/')
        ) {
          return this.uxmessage(
            UxMessage.warningType(
              "Whitelabel Domain must not have the full URL, just the domain. > Correct: app.gohighlevel.com | Wrong: https://app.gohighlevel.com/. Please remove any http declarations or /'s in the provided Whitelabel domain."
            )
          )
        }
        let domainError = `Couldn't find cname record pointing ${this.company.sparedomain} to (${config.cnameWhitelabelURL})`
        try {
          const cnameValidated = await this.checkForValidCNAME(
            this.company.sparedomain,
            'API'
          )
          if (!cnameValidated)
            return this.uxmessage(UxMessage.warningType(domainError))
        } catch (err) {
          console.error('error while checking cname --> ', err)
          return this.uxmessage(UxMessage.warningType(domainError))
        }
      }

      if (!this.isAdmin)
        return this.uxmessage(
          UxMessage.warningType(
            "You don't have permission to upload file. Check your permissions with your agency admin."
          )
        )

      this.$validator.errors.clear()
      let result = null
      result = await this.$validator.validateAll()
      if (!this.company.timezone) {
        this.$validator.errors.add({
          field: 'timezone',
          msg: 'Timezone is required',
        })
        return false
      }
      if (!result) {
        console.error('Correct them errors!', this.$validator.errors)
        return false
      }
      this.saving[loaderType] = true
      await this.company.save()
      this.saving[loaderType] = false
    },
    copyRelationshipNumber(key: string) {
      this.copiedKey = true;
      this.clipboardCopy(key)
      setTimeout(() => this.copiedKey = false, 2000);
    },
  },
})
</script>
