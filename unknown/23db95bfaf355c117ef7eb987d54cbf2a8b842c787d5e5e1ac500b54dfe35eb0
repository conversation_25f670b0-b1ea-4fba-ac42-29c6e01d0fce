<template>
  <div class="form-group">
    <div class="form-input-dropdown dropdown">
      <i class="icon icon-arrow-down-1"></i>
      <input
        type="text"
        class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
        data-toggle="dropdown"
        placeholder="Select Action"
        :value="selectedAction"
      />
      <div class="dropdown-menu">
        <a
          class="dropdown-item trigger-type"
          href="javascript:void(0);"
          @click.prevent="setValue('enable')"
        >
          <p>Enable Contact DND</p>
        </a>
        <a
          class="dropdown-item trigger-type"
          href="javascript:void(0);"
          @click.prevent="setValue('disable')"
        >
          <p>Disable Contact DND</p>
        </a>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: ['action'],
  computed: {
    selectedAction(): string {
      if (this.action.dnd_contact === undefined) return ''
      return this.action.dnd_contact === 'enable' ? 'Enable DND' : 'Disable DND'
    }
  },
  methods: {
    setValue(value: string) {
      Vue.set(this.action, 'dnd_contact', value)
      this.$emit('update:action', this.action)
    }
  }
})
</script>
