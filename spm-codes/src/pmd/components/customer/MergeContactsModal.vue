<template>
  <div
    class="modal fade hl_sms-template--modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
    @click.self="close"
  >
    <div class="modal-dialog merge-modal" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h2 class="modal-title">Merge Contacts</h2>
          <small
            >Select relevant information from different contacts and merge them
            as one.</small
          >
          <button
            type="button"
            class="close"
            data-dismiss="modal"
            aria-label="Close"
            @click="close"
          >
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div
            v-if="loading"
            class="spinner my-4 d-flex align-items-center flex-column"
          >
            <moon-loader :loading="loading" color="#188bf6" size="30px" />
            <small class="mt-2">Fetching Contacts Details...</small>
          </div>
          <div
            v-else-if="saving"
            class="spinner my-4 d-flex align-items-center flex-column"
          >
            <moon-loader :loading="saving" color="#188bf6" size="30px" />
            <small class="mt-2">Resolving Duplicates...</small>
          </div>
          <div
            v-else-if="error.hasError"
            class="my-4 d-flex align-items-center flex-column"
          >
            <span class="mt-2 --red">{{ error.message }}</span>
            <button type="button" class="btn btn-link2 mt-2" @click="close">
              Back
            </button>
          </div>
          <div v-else>
            <table class="table merge-table">
              <thead>
                <tr>
                  <td></td>
                  <td
                    v-for="(id, index) in contacts"
                    :key="index"
                    v-bind:id="`master-${index}`"
                    v-bind:class="
                      `master-${index}` === selected_master
                        ? 'master-record-selected'
                        : ''
                    "
                  >
                    <div
                      class="record-selector d-flex justify-content-between align-items-center"
                    >
                      <div class="radio-input d-flex align-items-center mr-3">
                        <input
                          type="radio"
                          v-bind:id="`radio-master-${index}`"
                          class="mr-2"
                          v-bind:checked="`master-${index}` === selected_master"
                          v-on:click="setSelectedMaster(index)"
                        />
                        <span>MASTER RECORD</span>
                      </div>
                      <span
                        class="select-all cursor-pointer"
                        @click="handleSelectAll(index)"
                        >Select All</span
                      >
                    </div>
                  </td>
                </tr>
              </thead>
              <tbody>
                <tr v-for="record in filteredRecords" :key="record.field">
                  <td>{{ record.name }}</td>
                  <td
                    v-for="(value, index) in record.columns"
                    :key="index"
                    v-bind:id="`${record.field}-${index}`"
                    v-bind:class="getClasses(record, index)"
                  >
                    <div v-if="record.name === 'DND'">
                      <UIToggle
                        type="checkbox"
                        class="tgl tgl-light"
                        id="account-buffer-tgl-conv"
                        v-model="dnd"
                      />
                    </div>
                    <div v-else-if="record.onlyReference">
                      <span>{{ getValueReference(value) }}</span>
                    </div>
                    <div
                      class="checkbox-input d-flex flex-column mr-3"
                      v-else-if="record.customValueType === 'FILE_UPLOAD'"
                    >
                      <div v-for="el in value" :key="el.meta.uuid">
                        <input
                          type="checkbox"
                          v-bind:id="`radio-${record.field}-${index}`"
                          class="mr-2"
                          v-bind:checked="
                            isSelected(
                              record.selected_value,
                              el.meta.uuid,
                              'FILE_UPLOAD'
                            )
                          "
                          @change="handleCheckbox($event, el, record.field)"
                        />
                        <span
                          ><a :href="el.url" target="_blank"
                            >{{ el.url }}
                          </a></span
                        >
                      </div>
                    </div>
                    <div
                      class="radio-input d-flex align-items-center"
                      v-else-if="record.type === 'customValue'"
                    >
                      <input
                        type="radio"
                        v-bind:id="`radio-${record.field}-${index}`"
                        class="mr-2"
                        v-bind:checked="
                          `${record.field}-${index}` ===
                          getSelectedRecord(record.field)
                        "
                        v-on:click="setSelectedRecord(record.field, index)"
                      />
                      <div
                        v-if="record.customValueType === 'TEXTBOX_LIST'"
                        class="d-flex flex-column"
                      >
                        <span v-for="el in value" :key="el">
                          {{ el }}
                        </span>
                      </div>
                      <span v-else-if="Array.isArray(value)">
                        {{ value.join(', ') }}
                      </span>
                      <span v-else> {{ value }} </span>
                    </div>
                    <div
                      class="checkbox-input d-flex flex-column mr-3"
                      v-else-if="Array.isArray(value)"
                    >
                      <div
                        v-for="el in value"
                        :key="record.type === 'extra' ? el.id : el"
                      >
                        <input
                          type="checkbox"
                          v-bind:id="`radio-${record.field}-${index}`"
                          class="mr-2"
                          v-bind:checked="
                            record.type === 'extra'
                              ? isSelected(record.selected_value, el.id)
                              : isSelected(record.selected_value, el)
                          "
                          @change="
                            record.type === 'extra'
                              ? handleCheckbox($event, el.id, record.field)
                              : handleCheckbox($event, el, record.field)
                          "
                        />
                        <span v-if="record.field === 'tags'">{{ el }}</span>
                        <span v-else>{{ el.name }}</span>
                      </div>
                    </div>
                    <div
                      class="radio-input d-flex align-items-center mr-3"
                      v-else-if="record.type === 'conversation'"
                    >
                      <input
                        type="checkbox"
                        v-bind:id="`radio-${record.field}-${index}`"
                        class="mr-2"
                        v-bind:checked="
                          isSelected(record.selected_value, value)
                        "
                        v-on:click="handleCheckbox($event, value, record.field)"
                      />
                      <span> Merge?</span>
                    </div>
                    <div
                      class="radio-input d-flex align-items-center mr-3"
                      v-else
                    >
                      <input
                        type="radio"
                        v-bind:id="`radio-${record.field}-${index}`"
                        class="mr-2"
                        v-bind:checked="
                          `${record.field}-${index}` ===
                          getSelectedRecord(record.field)
                        "
                        v-on:click="setSelectedRecord(record.field, index)"
                      />
                      <span v-if="integrationFields.includes(record.field)">{{
                        checkIntegrations(value, record.field)
                      }}</span>
                      <span v-else>{{ value }}</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
            <div class="d-flex align-items-center flex-column mt-3 mb-2">
              <span v-if="showAll">Showing All Fields.</span>
              <span v-else>Showing Fields With Different Values.</span>
              <small
                v-if="showAll"
                class="select-all cursor-pointer"
                @click="handleShowAll"
                >Show Fields With Different Values.</small
              >
              <small
                v-else
                class="select-all cursor-pointer"
                @click="handleShowAll"
                >Show All Field</small
              >
            </div>
          </div>
        </div>
        <div
          v-if="!loading && !saving && !error.hasError"
          class="modal-footer d-flex flex-column align-items-end"
        >
          <small class="text-warning pl-2 text-center mb-1"
            >This operation is irreversible, please type 'CONFIRM' to confirm
            and resolve.</small
          >
          <div class="d-flex justify-content-end">
            <UITextInput
              id="confirm"
              name="confirm"
              v-model="confirm"
              placeholder="CONFIRM"
            />
            <UIButton use="outline" class="mx-2" @click="close">
              Back
            </UIButton>
            <UIButton
              :disabled="confirm.toLowerCase() !== 'confirm'"
              @click="resolve"
            >
              Resolve
            </UIButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import moment from 'moment-timezone'
import vSelect from 'vue-select'
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'
import { Contact, User } from '@/models'
import * as lodash from 'lodash'
import { toInteger } from 'lodash'
import { ContactMembershipService } from '@/util/contact_membership'
import restAgent from '@/restAgent'
declare var $: any

interface MergeRecord {
  field: string
  name: string
  columns: []
  selected: string | null | undefined
  selected_value: string | []
  equal: boolean | undefined
  type: string
  onlyReference: boolean | undefined
  customValueType: string | undefined
  customValueId: string | undefined
}

export default Vue.extend({
  props: ['currentLocationId', 'selectedContacts'],
  components: {
    vSelect,
  },
  data() {
    return {
      records: [] as MergeRecord[],
      selected_master: 'master-0',
      contact_master_id: '',
      showAll: false,
      contacts: [] as string[],
      loading: true,
      saving: false,
      confirm: '',
      error: {
        hasError: false,
        message:
          'Unfortunately something went wrong, please contact support to resolve this issue.',
      },
      dnd: true,
      integrationFields: ['facebookId', 'googleData'],
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    filteredRecords() {
      return this.showAll
        ? this.records
        : this.records.filter(record => !record.equal || record.onlyReference)
    },
    selectedMasterIndex() {
      return toInteger(this.selected_master.split('master-')[1])
    },
  },
  methods: {
    close() {
      this.$emit('closed')
    },
    setSelectedMaster(index: number) {
      this.selected_master = `master-${index}`
      this.contact_master_id = this.contacts[index]
    },
    getSelectedRecord(field: string) {
      const _record = lodash.find(this.records, record => {
        return record.field === field
      })
      return _record?.selected
    },
    setSelectedRecord(field: string, index: number) {
      const index_record = lodash.findIndex(this.records, record => {
        return record.field === field
      })
      this.records[index_record].selected = `${field}-${index}`
      const value = this.records[index_record].columns[index]
      if (
        this.records[index_record].type === 'customValue' &&
        Array.isArray(value)
      ) {
        this.records[index_record].selected_value = [...value]
      } else {
        this.records[index_record].selected_value = value
      }
    },
    async fetchRecords(contacts: string[]) {
      try {
        const response = await this.$http.post(
          'contact/merge_contact_details/',
          {
            contactIds: contacts,
            locationId: this.currentLocationId,
          }
        )
        this.records = response.data.records
        this.contacts = response.data.contacts
        this.contact_master_id = this.contacts[0]

        const _record = lodash.find(this.records, record => {
          return record.name === 'DND'
        })
        if (_record) this.dnd = _record.columns.some(el => el)
      } catch (error) {
        console.log(error)
        this.error.hasError = true
        this.error.message = error
      }
      this.loading = false
    },
    handleShowAll() {
      this.showAll = !this.showAll
    },
    handleSelectAll(index: number) {
      this.setSelectedMaster(index)
      this.records.forEach(record => {
        if (record.type === 'extra') {
          if (record.columns[index].length)
            record.selected_value = record.columns[index].map(el => {
              return el.id
            })
          else record.selected_value = []
        } else if (record.type === 'conversation') {
          record.selected_value = [record.columns[index]]
        } else {
          this.setSelectedRecord(record.field, index)
        }
      })
    },
    async resolve() {
      try {
        this.saving = true

         await restAgent.Contact.merge({
          locationId: this.currentLocationId,
          masterRecord: this.contact_master_id,
          recordIds: this.selectedContacts,
          records: this.records,
          dnd: this.dnd,
        })
        const contact = await Contact.getById(this.contact_master_id)
        await this.mergeOffers(contact)
        await this.$store.dispatch('contacts/update', this.contact_master_id)
        this.$emit('finishMerge', this.contact_master_id)
      } catch (error) {
        console.log(error)
        this.error.hasError = true
        this.error.message = error
      }
      this.saving = false
    },
    removeMultiSelect(value: string | Object, field: string) {
      const index_record = lodash.findIndex(this.records, record => {
        return record.field === field
      })

      let index
      if (this.records[index_record].customValueType === 'FILE_UPLOAD') {
        index = lodash.findIndex(
          this.records[index_record].selected_value,
          record => {
            return record.meta.uuid === value.meta.uuid
          }
        )
      } else {
        index = this.records[index_record].selected_value.indexOf(value)
      }

      this.records[index_record].selected_value.splice(index, 1)
    },
    addMultiSelect(value: string | Object, field: string) {
      const index_record = lodash.findIndex(this.records, record => {
        return record.field === field
      })
      this.records[index_record].selected_value.push(value)
    },
    handleCheckbox(
      event: { target: HTMLInputElement },
      el: string | Object,
      field: string
    ) {
      event.target.checked
        ? this.addMultiSelect(el, field)
        : this.removeMultiSelect(el, field)
    },
    isSelected(value: [] | Object, element: string | Object, type: string) {
      if (type === 'FILE_UPLOAD') {
        return lodash.find(value, x => {
          return x.meta.uuid === element
        })
          ? true
          : false
      }
      if (Array.isArray(value) && value.includes(element)) return true
      return false
    },
    getClasses(record: Object, index: number) {
      let classes = []

      if (record.customValueType === 'FILE_UPLOAD') {
        if (
          record.columns[index] &&
          record.columns[index].some(el =>
            this.isSelected(record.selected_value, el.meta.uuid, 'FILE_UPLOAD')
          )
        )
          classes.push('item-record-selected')
      } else if (record.field === 'conversation') {
        if (this.isSelected(record.selected_value, record.columns[index]))
          classes.push('item-record-selected')
      } else if (record.type === 'extra') {
        if (
          record.columns[index].some(el =>
            record.selected_value.includes(el.id)
          )
        )
          classes.push('item-record-selected')
      } else if (
        Array.isArray(record.columns[index]) &&
        record.type !== 'customValue' &&
        record.columns[index].some(el => record.selected_value.includes(el))
      ) {
        classes.push('item-record-selected')
      } else if (record.onlyReference) {
        if (this.selectedMasterIndex === index) {
          classes.push('item-record-selected')
        }
      } else if (
        `${record.field}-${index}` === this.getSelectedRecord(record.field)
      ) {
        classes.push('item-record-selected')
      }

      if (`master-${index}` === this.selected_master)
        classes.push('body-master-selected')

      return classes.join(' ')
    },
    getValueReference(value: string) {
      if (moment(value).isValid())
        return moment(value).format('MMMM Do YYYY, h:mm a')
      else return value ? 'ON' : 'OFF'
    },
    checkIntegrations(value: string, type: string) {
      let integrated = false
      if (type === 'googleData') {
        if (value && value.id) integrated = true
      }
      if (type === 'facebookId') {
        if (value) integrated = true
      }
      return integrated ? 'Integrated' : ''
    },
    async mergeOffers(master: Contact) {
      const contacts_ids = this.selectedContacts.filter(
        (id: string) => id !== this.contact_master_id
      )

      const offers = []

      const contacts = await Contact.getByIds(contacts_ids)

      for (const contact of contacts) {
        offers.push(...contact.offers)
        if (
          (contact.offers && contact.offers.length) ||
          (contact.products && contact.products.length)
        ) {
          await ContactMembershipService.deleteContact(contact.id)
        }
      }

      lodash
        .uniq(lodash.difference(offers, master.offers))
        .forEach(async (offer_id: string) => {
          await ContactMembershipService.addOffer(master.id, offer_id)
        })
    },
  },
  async mounted() {
    $(this.$refs.modal).modal('show')
    this.fetchRecords(this.selectedContacts)
  },
  beforeDestroy() {
    $(this.$refs.modal).modal('hide')
  },
})
</script>

<style scoped>
.master-record-selected {
  background-color: #071f3e !important;
  border: 2px solid #071f3e !important;
}

.master-record-selected::after {
  content: ' ';
  width: 100%;
  height: 112px;
  background-color: #071f3e !important;
  position: absolute;
  left: 0px;
  top: 43px;
  -webkit-clip-path: polygon(50% 7%, -7% 0%, 107% 0%);
  clip-path: polygon(50% 8%, 1% 0%, 100% 0%);
}

.master-record-selected span {
  color: #f6f7f8;
}

.select-all {
  color: #83aee4;
}

.master-record-selected span.select-all {
  color: #5d8cc7;
}

.item-record-selected {
  background-color: #f3f8fb !important;
  color: black;
}

.record-selector span {
  font-size: 0.6rem;
}

.merge-table thead td {
  background-color: #f6f6f6;
  position: relative;
}

.merge-table td {
  min-width: 200px;
  max-width: 300px;
  background-color: white;
  border: 1px solid #cccccc;
}

.modal-body {
  padding: 0 !important;
  overflow-x: auto;
}

.merge-table thead tr td:nth-child(1) {
  background-color: white;
  border-bottom: 0px;
}

.merge-table tbody tr td:nth-child(1) {
  text-transform: capitalize;
}

.merge-table tbody tr:nth-child(1) td div {
  padding-top: 5px;
}

.merge-table tbody tr:nth-child(1) td:nth-child(1) {
  border-top: 0px;
}

input[type='radio']::before {
  background-color: white;
}

.cursor-pointer {
  cursor: pointer;
}
.checkbox-input > div {
  display: flex;
  align-items: center;
  margin: 2px 0;
}

.body-master-selected {
  border-right: 2px solid #3c8bee !important;
  border-left: 2px solid #3c8bee !important;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
}

.merge-table tbody tr:last-child td.body-master-selected {
  border-bottom: 2px solid #3c8bee !important;
}

.merge-table span {
  word-break: break-all;
}

.merge-modal {
  max-width: 80% !important;
}

.merge-table a:hover,
.merge-table a:focus {
  color: #071f3e !important;
}
</style>
