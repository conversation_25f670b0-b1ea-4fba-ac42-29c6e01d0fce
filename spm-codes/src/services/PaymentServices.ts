import { requests } from '.'
import config from '../config'

interface RetrieveStripeAccount {
  accountId?: string
  liveMode: boolean
  locationId?: string
}

interface StripeConnect {
  locationId: string
}

export interface CreateInvoice {
  items:string
  contactId:string
  expirationDate:string
  invoiceNumber:string
  locationId:string
  liveMode:boolean
}

export interface AccountsDetailsByLocation {
  location: string
}

export interface PaypalConnectOptions {
  locationId: string
  liveMode: boolean
  redirectURL: string
}

export interface GetPaypalAccounts {
  locationId:string
  liveMode: boolean
}

export interface PaypalConnectWithKeyOptions {
  locationId: string
  liveMode: boolean
  merchantClientId: string
  merchantSecretId: string
}

const PaymentServices = {
  connectPaypal: (params:PaypalConnectOptions) =>
    requests(config.paymentsApiURL).get('/payments/paypal/connect', { params }),
  connectPaypalWithKey: (params:PaypalConnectWithKeyOptions) =>
    requests(config.paymentsApiURL).post('/payments/paypal/connect', params),
  connectPaypalCallback: (params) =>
    requests(config.paymentsApiURL).post('/payments/paypal/callback', params),
  disconnectPaypal: (params:GetPaypalAccounts) =>
    requests(config.paymentsApiURL).post(`/payments/paypal/disconnect`, params),
  connectStripe: (params: StripeConnect) =>
    requests(config.paymentsApiURL).get('/payments/stripe/connect', { params }),
  disconnectStripe: (locationId:string) =>
    requests(config.paymentsApiURL).post(`/payments/stripe/disconnect`, { locationId }),
  retrieveStripeAccount: (params: RetrieveStripeAccount) =>
    requests(config.paymentsApiURL).get('/payments/stripe/retrieveAccount', { params }),
  findStripeIntegration: (locationId:string) =>
    requests(config.paymentsApiURL).get(`/payments/integrations/stripe/`,{
      params:{
        location_id: locationId
      }
    }),
  createInvoice: (params:CreateInvoice) =>
    requests(config.paymentsApiURL).post('/payments/stripe/create_invoice', params),
  getIntegrations: (locationId: string) =>
    requests(config.paymentsApiURL).get(`/payments/integrations/${locationId}`),
  getIntegrationsDetails: (locationId: string) =>
    requests(config.paymentsApiURL).get(`/payments/integrations/details/${locationId}`),
}

export default PaymentServices
