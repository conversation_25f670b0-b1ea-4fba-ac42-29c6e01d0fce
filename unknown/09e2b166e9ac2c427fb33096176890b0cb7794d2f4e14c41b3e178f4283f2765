<template>
  <div class="card billing-history">
    <div class="card-header">
      <h3>Billing History</h3>
      <p class="refresh" @click="$emit('retry')">
        <i class="fa fa-redo"></i>&nbsp;&nbsp;Refresh
      </p>
      <!-- <div class="sort-by dropdown --no-caret">
        <a
          href="#"
          role="button"
          data-toggle="dropdown"
          aria-haspopup="true"
          aria-expanded="false"
          class="dropdown-toggle"
        >
          Sort By <i class="fa fa-angle-down" aria-hidden="true"></i>
        </a>
        <div class="dropdown-menu dropdown-menu-right">
          <div
            class="dropdown-menu-item"
            v-for="filter in filters"
            :key="filter.id"
            @click="currentFilter = filter.id"
          >
            {{ filter.title }}
          </div>
        </div>
      </div> -->
    </div>
    <div v-if="loading" class="card-body loading">
      <upgrade-modal-shimmer style="max-width: 100%" />
    </div>
    <div v-else class="card-body">
      <div v-if="error" class="error invoice-card" @click="$emit('retry')">
        Error while fetching invoices, click to retry
      </div>
      <!-- <div
        class="invoice-card"
        :class="[
          invoice.status === 'paid' ? 'success' : 'failed',
          invoiceCardOpen(invoice.id) ? 'open' : 'closed',
        ]"
        v-for="invoice in invoices"
        :key="invoice.id"
      >
        <div class="date">
          <p class="title">Date</p>
          <p class="data">{{ formatDate(invoice.created) }}</p>
        </div>
        <div class="name" style="padding: 0px 4px">
          <p class="title">Invoice No.</p>
          <p class="data">
            <i class="fa fa-link" aria-hidden="true"></i>
            {{ invoice.number }}
          </p>
          <a
            v-if="invoiceCardOpen(invoice.id)"
            :href="invoice.hosted_invoice_url"
            target="_blank"
            >VIEW</a
          >
        </div>
        <div class="card-details">
          <p class="title">Card No.</p>
          <p
            class="data"
            v-if="
              invoice.charge_data &&
              invoice.charge_data.payment_method_details.card
            "
          >
            {{ invoice.charge_data.payment_method_details.card.brand }} ••••
            {{ invoice.charge_data.payment_method_details.card.last4 }}
          </p>
          <p v-else class="data">---- ----</p>
        </div>
        <div class="payment-status">
          <p class="title">Status</p>
          <p class="data">
            <i class="fa fa-check-circle" aria-hidden="true"></i>
            {{ invoice.status }}
          </p>
        </div>
        <div class="details" @click="switchToggleStatus(invoice.id)">
          <i class="fa fa-angle-right" aria-hidden="true"></i>
        </div>
        <div v-if="invoiceCardOpen(invoice.id)" class="invoice-no">
          <p class="title">Invoice Description</p>
          <p class="data">
            <span
              v-if="invoice.charge_data && invoice.charge_data.description"
              >{{ invoice.charge_data.description }}</span
            >
            <span v-else>{{ invoice.number }}</span>
          </p>
        </div>
        <div v-if="invoiceCardOpen(invoice.id)" class="amount">
          <p class="title">Amount</p>
          <p class="data">
            {{ invoice.total / 100 }} {{ invoice.currency.toUpperCase() }}
          </p>
        </div>
      </div> -->

      <!-- charges ===== -->
      <div
        class="invoice-card"
        :class="[
          charge.paid ? 'success' : 'failed',
          invoiceCardOpen(charge.id) ? 'open' : 'closed',
        ]"
        v-for="charge in charges"
        :key="charge.id"
      >
        <div class="date">
          <p class="title">Date</p>
          <p class="data">{{ formatDate(charge.created) }}</p>
        </div>
        <div class="name" style="padding: 0px 4px">
          <p class="title">Invoice No.</p>
          <p class="data">
            <i class="fa fa-link" aria-hidden="true"></i>
            {{
              charge.invoice
                ? charge.invoice.substr(0, 8)
                : charge.id.substr(0, 8)
            }}
            ...
          </p>
          <a
            v-if="invoiceCardOpen(charge.id)"
            :href="charge.receipt_url"
            target="_blank"
            >VIEW</a
          >
        </div>
        <div class="card-details">
          <p class="title">Card No.</p>
          <p class="data" v-if="charge && charge.payment_method_details.card">
            {{ charge.payment_method_details.card.brand }} ••••
            {{ charge.payment_method_details.card.last4 }}
          </p>
          <p v-else class="data">---- ----</p>
        </div>
        <div class="payment-status">
          <p class="title">Status</p>
          <p class="data">
            <i class="fa fa-check-circle" aria-hidden="true"></i>
            {{ charge.status }}
          </p>
        </div>
        <div class="details" @click="switchToggleStatus(charge.id)">
          <i class="fa fa-angle-right" aria-hidden="true"></i>
        </div>
        <div v-if="invoiceCardOpen(charge.id)" class="invoice-no">
          <p class="title">Invoice Description</p>
          <p class="data">
            <span>{{
              charge.description
                ? charge.description.substr(0, 12) + '...'
                : charge.id.substr(0, 8)
            }}</span>
          </p>
        </div>
        <div v-if="invoiceCardOpen(charge.id)" class="amount">
          <p class="title">Amount</p>
          <p class="data">
            {{ charge.amount / 100 }} {{ charge.currency.toUpperCase() }}
          </p>
        </div>
      </div>
      <div
        v-if="!error && invoices.length === 0 && charges.length === 0"
        class="invoice-card"
      >
        No Invoices
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import * as moment from 'moment-timezone'
// import Shimmer from '../../common/shimmers/Shimmer.vue'
import UpgradeModalShimmer from '@/pmd/components/common/shimmers/UpgradeModalShimmer.vue'

export default Vue.extend({
  components: {
    // Shimmer,
    UpgradeModalShimmer,
  },
  props: {
    invoices: {
      type: Array,
      default: () => {
        return []
      },
    },
    charges: {
      type: Array,
      default: () => {
        return []
      },
    },
    loading: {
      type: Boolean,
      default: false,
    },
    error: {
      type: Error,
      default: null,
    },
  },
  data() {
    return {
      toggleStatus: {} as { [key: string]: boolean },
      currentFilter: -1,
      filters: [
        {
          id: 1,
          title: 'Recent Date',
        },
        {
          id: 2,
          title: 'Amount',
        },
        {
          id: 3,
          title: 'Invoice No.',
        },
      ],
    }
  },
  mounted() {
    console.log(this.charges)
  },
  methods: {
    formatDate(timestamp: number) {
      return moment.unix(timestamp).format('MMM DD, YYYY')
    },
    switchToggleStatus(invoiceId: string) {
      const toggleStatus: any = { ...this.toggleStatus }
      toggleStatus[invoiceId] = toggleStatus[invoiceId] === true ? false : true
      this.toggleStatus = toggleStatus
    },
    invoiceCardOpen(invoiceId: string): boolean {
      return !!this.toggleStatus[invoiceId]
    },
  },
})
</script>
<style scoped>
.billing-history .card-header .refresh {
  cursor: pointer;
  font-size: 12px;
  font-style: italic;
}

.billing-history .card-body {
  height: calc(100vh - 63px);
  overflow-y: auto;
}

.sort-by .dropdown-menu {
  padding: 0;
}

.sort-by .dropdown-menu-item {
  color: #2d3748;
  font-size: 14px;
  line-height: 16px;
  margin: 6px 0;
  cursor: pointer;
  padding: 12px;
  border-radius: 3px 3px 0 0;
}

.sort-by .dropdown-menu-item:hover {
  background-color: lightgray;
}

.card-body.loading .invoice-card > * {
  margin: 0 6px;
}

.card-body.loading .shimmer {
  width: 100%;
  height: 24px;
}

.invoice-card.error {
  cursor: pointer;
  color: red;
}

.invoice-card {
  display: grid;
  grid-template-columns: auto auto 30% 20% 20px;
  grid-row-gap: 16px;
  padding: 16px;
  margin-bottom: 8px;
  background: white;
  border: 1px solid #edf2f7;
  border-radius: 5px;
  font-size: 14px;
}

.invoice-card .title {
  text-transform: capitalize;
  color: #a0aec0;
  font-size: 11px;
  user-select: none;
}

.invoice-card .data {
  color: #4a5568;
  white-space: nowrap;
}

.invoice-card.closed .title {
  display: none;
}

.invoice-card.failed {
  background: #fff5f5;
  border: 1px solid #fed7d7;
}

.invoice-card .date {
  color: #4a5568;
}

.invoice-card .name .data {
  color: #a0aec0;
  white-space: nowrap;
  margin: 0 3px;
}

/* .invoice-card .name i {
  margin-right: 6px;
} */

.invoice-card .name a {
  background: #63b3ed;
  border-radius: 45px;
  color: white !important;
  padding: 5px 8px;
  font-size: 11px;
  letter-spacing: 0.055em;
}

.invoice-card .card-details {
  color: #4a5568;
}

.invoice-card .card-details .data {
  text-transform: uppercase;
}

.invoice-card.success .payment-status .data {
  color: #48bb78;
  text-transform: uppercase;
}

.invoice-card.failed .payment-status .data {
  color: #f56565;
}

.invoice-card .details {
  background: #ffffff;
  box-shadow: 0px 1px 7px rgba(0, 0, 0, 0.11);
  color: #4299e1;
  background: white;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  cursor: pointer;
  border-radius: 100%;
  transition: transform 0.25s ease;
  transform: none;
}

.invoice-card.open .details {
  color: white;
  background: #4299e1;
  transform: rotate(90deg);
}

.invoice-card .amount .data {
  font-weight: bold;
}

.invoice-card p {
  margin: 0;
}
</style>
