<template>
  <div>
    <div class="form-group">
      <div class="form-input-dropdown dropdown">
        <div data-toggle="dropdown">
          <i class="icon icon-arrow-down-1"></i>
          <input type="text" class="form-control" placeholder="Select Eliza" :value="agentName" />
        </div>
        <div class="dropdown-menu">
          <a
            class="dropdown-item trigger-type"
            href="javascript:void(0);"
            v-for="agent in agents"
            :key="agent.title"
            @click.prevent="setValue('agent', agent)"
          >
            <p>{{agent.title}}</p>
          </a>
        </div>
      </div>
    </div>
    <div class="form-group">
      <div class="form-input-dropdown dropdown">
        <div data-toggle="dropdown">
          <i class="icon icon-arrow-down-1"></i>
          <input type="text" class="form-control" data-lpignore="true" placeholder="Select Calendar" :value="calendarName" />
        </div>
        <div class="dropdown-menu">
          <a
            class="dropdown-item trigger-type"
            href="javascript:void(0);"
            v-for="calendar in calendars"
            :key="calendar.name"
            @click.prevent="setValue('calendarId', calendar.id)"
          >
            <p>{{calendar.name}}</p>
          </a>
        </div>
      </div>
    </div>
    <div class="form-group" v-if="filteredTemplates.length > 0">
      <div class="form-input-dropdown dropdown">
        <div data-toggle="dropdown">
          <i class="icon icon-arrow-down-1"></i>
          <input type="text" class="form-control" data-lpignore="true" placeholder="Select Welcome Template" :value="selectedWelcomeTemplate" />
        </div>
        <div class="dropdown-menu">
          <a
            class="dropdown-item trigger-type"
            href="javascript:void(0);"
            v-for="template in filteredTemplates"
            :key="template.name"
            @click.prevent="setValue('welcomeTemplate', template.id)"
          >
            <div class="clearfix text-template-option" style="padding: 10px;">
              <div style="float:left;">
                <strong>{{template.name | truncate(80, '...')}}</strong>
                <p>{{template.template.body | truncate(80, '...')}}</p>
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>
    <div class="form-group" v-if="filteredTemplates.length > 0">
      <div class="form-input-dropdown dropdown">
        <div data-toggle="dropdown">
          <i class="icon icon-arrow-down-1"></i>
          <input type="text" class="form-control" data-lpignore="true" placeholder="Select Thankyou Template" :value="selectedThankyouTemplate" />
        </div>
        <div class="dropdown-menu">
          <a
            class="dropdown-item trigger-type"
            href="javascript:void(0);"
            v-for="template in filteredTemplates"
            :key="template.name"
            @click.prevent="setValue('thankyouTemplate', template.id)"
          >
            <div class="clearfix text-template-option" style="padding: 10px;">
              <div style="float:left;">
                <strong>{{template.name | truncate(80, '...')}}</strong>
                <p>{{template.template.body | truncate(80, '...')}}</p>
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>
    <div class="form-group">
      <input
        type="text"
        class="form-control"
        placeholder="Enter a Timeout (Hours)"
        v-model="timeout"
        name="timeout"
        @input="setValue('timeout', timeout)"
      />
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import vSelect from 'vue-select'
import { Template, LogicalEliza } from '@/models'

export default Vue.extend({
  props: ['action'],
  components: {
    vSelect
  },
  data() {
    return {
      currentLocationId: '',
      agents: [] as any[],
      templates: [] as { [key: string]: any }[],
      calendars: [] as any[],
      timeout: '',
    }
  },
  computed: {
    agentName(): string {
      const agent = lodash.find(this.agents, { title: this.action.agent })
      if (agent) return agent.title

      return ''
    },
    filteredTemplates(): { [key: string]: any }[] {
      return this.templates.filter(t => t.type === 'sms');
    },
    calendarName(): string {
      const calendar = lodash.find(this.calendars, { id: this.action.calendarId })
      if (calendar) return calendar.name

      return ''
    },
    selectedWelcomeTemplate() {
      const template = lodash.find(this.filteredTemplates, { id: this.action.welcomeTemplate });
      if (template) return template.name

      return ''
    },
    selectedThankyouTemplate() {
      const template = lodash.find(this.filteredTemplates, { id: this.action.thankyouTemplate });
      if (template) return template.name

      return ''
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchTemplates();
    this.calendars = this.$store.state.calendars.calendars
    this.agents = (await LogicalEliza.fetchByLocationId(this.currentLocationId)).map(logicalEliza => {
        return {
          id: logicalEliza.id,
          projectId: logicalEliza.elizaId,
          title: logicalEliza.logicalName
        }
      });

  },
  methods: {
    async fetchTemplates() {
      const snapshot = await Template.fetchByLocationId(
        this.currentLocationId
      ).get()

      this.templates = snapshot.docs.map(d => {
        return { id: d.id, ...d.data() }
      })
      if (this.action.timeout)
        this.timeout = this.action.timeout
    },
    setValue(field: string, value: { [key: string]: string }) {
      if (field === 'agent') {
        Vue.set(this.action, 'agent', value.title)
        Vue.set(this.action, 'projectId', value.projectId)
        Vue.set(this.action, 'logicalElizaId', value.id)
      } else {
        Vue.set(this.action, field, value)
      }
      this.$emit('update:action', this.action)
    },

  }
})
</script>
