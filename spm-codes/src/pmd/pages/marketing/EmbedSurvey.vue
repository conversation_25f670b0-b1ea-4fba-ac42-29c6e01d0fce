<template>
	<div class="form-builder-integrate-wrapper">
		<div class="top-header col-md-12 dark-bg">
			<button class="back-btn btn" @click="back">Back</button>
			<button class="save-exit btn btn-success" @click="exit">Save and exit</button>
		</div>
		<div class="tab-wrapper">
			<h2>Integrate Your Survey</h2>
			<p class="sub-heading">Use the following options to integrate your custom form into your website</p>

			<section id="tabs">
				<div class="container">
					<div class="row">
						<div class="col-xs-12 col-sm-12 col-md-12">
							<nav>
								<div class="nav nav-tabs nav-fill" id="nav-tab" role="tablist">
									<a
										class="nav-item nav-link active"
										id="nav-embed-tab"
										data-toggle="tab"
										href="#nav-embed"
										role="tab"
										aria-controls="nav-embed"
										aria-selected="true"
									>Embed</a>
									<a
										class="nav-item nav-link"
										id="nav-link-tab"
										data-toggle="tab"
										href="#nav-link"
										role="tab"
										aria-controls="nav-link"
										aria-selected="false"
									>Link</a>
								</div>
							</nav>
							<div class="tab-content py-3 px-3 px-sm-0" id="nav-tabContent">
								<div
									class="tab-pane fade show active"
									id="nav-embed"
									role="tabpanel"
									aria-labelledby="nav-embed-tab"
								>
									<div class="form-group">
										<label for="embed">Iframe Embed</label>
										<textarea class="form-control" rows="5" id="embed">{{iframeUrl}}</textarea>
									</div>
								</div>
								<div class="tab-pane fade" id="nav-link" role="tabpanel" aria-labelledby="nav-link-tab">
									<div class="form-group">
										<label for="link">Copy the link below and easily share it anywhere you wish.</label>
										<input type="text" data-lpignore="true" class="form-control" id="link" :value="embedUrl">
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</section>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from "vue";
import {
	Location,
	Contact,
	CustomField,
	ICustomField,
  FieldType,
  Company
} from "@/models";
import config from '../../../config';

export default Vue.extend({
	components: {},
	data() {
		return {
			baseUrl: "",
			embedUrl: "",
			iframeUrl: "",
			locationId: "",
			surveyId: ""
		};
	},
	computed: {

	},
	watch: {

	},
	updated() {

	},
	mounted() {

	},
	methods: {
		back() {
			this.$router.push({
				name: "survey_manage",
				params: {
					location_id: this.locationId,
					survey_id: this.surveyId
				}
			});
		},
		exit() {
			this.$router.push({
				name: "list_survey",
				params: {
					location_id: this.locationId
				}
			});
		},
		getUrlParams() {
			this.locationId = this.$router.currentRoute.params.location_id;
			this.surveyId = this.$router.currentRoute.params.survey_id;
		},
		createEmbedUrl() {
			if (config.mode === 'dev') {
        //this.embedUrl = this.baseUrl + `/html/survey_widget_v2.html?survey=${this.surveyId}`;
        this.embedUrl = `http://localhost:3344/widget/survey/${this.surveyId}`;
			} else if (config.mode === 'staging') {
        this.embedUrl = `${this.baseUrl}/widget/survey/${this.surveyId}`;
      } else {
        //this.embedUrl = this.baseUrl + `/widget/survey/${this.surveyId}`;
        this.embedUrl = `${this.baseUrl}/widget/survey/${this.surveyId}`;
      }
			this.iframeUrl = `<iframe src="${this.embedUrl}" style="border:none;width:100%;" scrolling="no" id="${this.surveyId}"></iframe>\n<script src="${this.baseUrl}/js/form_embed.js"><\/script>`;
		}
	},
	async created() {
    this.getUrlParams();
    this.baseUrl = await this.$store.dispatch('company/getWhitelabelDomain');
		this.createEmbedUrl();
	}
});
</script>
