<template>
  <div
    class="modal fade hl_funnel-add-new-step--modal"
    id="add-new-step"
    tabindex="-1"
    role="dialog"
    aria-hidden="true"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">
              {{
                addingWebsitePage
                  ? 'New Page for Website'
                  : 'New Step in Funnel'
              }}
            </h2>
            <button
              @click="hideModal()"
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div class="form-group">
              <UITextInputGroup
                v-model="stepName"
                type="text"
                placeholder="Name for Page"
                label="Name for Page"
              />
            </div>
            <!-- <div class="form-group">
              <label>Show In Funnel?</label>
              <div class="toggle">
                <input type="checkbox" class="tgl tgl-light" id="show-funnel" checked>
                <label class="tgl-btn" for="show-funnel"></label>
              </div>
            </div>-->
            <div class="form-group">
              <UITextInputGroup
                v-model="stepUrl"
                @input="lowerCaseStepPaths"
                type="text"
                placeholder="Path"
                label="Path"
              />
              <p class="help-block">the path for this page</p>
            </div>

            <div class="form-group" v-if="allowToImportFunnel">
              <UITextInputGroup
                v-model="cfUrl"
                type="url"
                label="Import from ClickFunnels (Optional)"
                placeholder="ClickFunnels Url"
              />
              <p class="help-block">
                If you want to import your page data from ClickFunnels.
              </p>
              <span class="text-danger" v-if="hasInvalidError"
                >Please enter a valid ClickFunnels URL and try again.</span
              >
              <span class="text-danger" v-if="hasHTTPError">
                Please enter a ClickFunnels URL starting with https
              </span>
            </div>

            <div class="form-group" v-if="cfUrl && allowToImportFunnel">
              <div class="option-md">
                <UICheckbox
                  v-model="acceptTermsToImportFunnel"
                  id="accept-terms-for-import"
                />
                <UITextLabel for="accept-terms-for-import"
                  >I confirm that this page either belongs to me or I have
                  permission to use these assets.</UITextLabel
                >
              </div>
            </div>

            <!-- <div class="form-group">
              <label>Page Type</label>
              <select
                class="selectpicker"
                title="Select Page Type"
                data-width="fit"
                name="filterType"
                v-model="selectedPageType"
              >
                <option v-for="pageType in allPageTypes" :key="pageType.id" :value="pageType.id">
                  {{ pageType.title }}
                </option>
              </select>
            </div>-->
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner">
            <UIButton type="button" use="outline" data-dismiss="modal">
              Close
            </UIButton>
            <UIButton
              type="button"
              :disabled="submitDisabled"
              @click="addStep()"
              :loading="addingNewStep"
              use="primary"
            >
              {{
                addingNewStep
                  ? cfUrl
                    ? 'Importing...'
                    : 'Creating...'
                  : addingWebsitePage
                  ? 'Create New Page'
                  : 'Create Funnel Step'
              }}
            </UIButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { FUNNEL_PAGE_TYPES } from '../../../util/funnel_consts'
import { UserState } from '../../../store/state_models'
import { User } from '../../../models'
import { mapState } from 'vuex'

export default Vue.extend({
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    addingWebsitePage: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hide')
    })

    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }
  },
  updated() {
    const _self = this
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  computed: {
    newStepName(): String {
      return this.stepName.trim()
    },
    allowToImportFunnel() {
      return (
        this.user &&
        this.user.permissions &&
        this.user.permissions.funnels_enabled === true
      )
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    submitDisabled(): Boolean {
      return (
        !this.newStepName ||
        !this.selectedPageType ||
        this.addingNewStep ||
        (this.cfUrl !== '' && !this.acceptTermsToImportFunnel)
      )
    }
  },
  data() {
    return {
      cfUrl: '',
      stepName: '',
      stepUrl: '',
      hasInvalidError: false,
      hasHTTPError: false,
      acceptTermsToImportFunnel: false,
      // selectedPageType: '',
      // setting default page type
      selectedPageType: FUNNEL_PAGE_TYPES[0].id,
      addingNewStep: false,
      allPageTypes: FUNNEL_PAGE_TYPES
    }
  },
  methods: {
    hideModal() {
      this.$emit('hide')
    },
    lowerCaseStepPaths() {
      this.stepUrl = this.stepUrl ? this.stepUrl.toLowerCase() : ''
    },
    async addStep() {
      this.hasInvalidError = false
      this.hasHTTPError = false
      this.addingNewStep = true
      const newStepDetails = {
        name: this.newStepName,
        url: this.stepUrl.trim(),
        cfUrl: this.cfUrl,
        type: this.selectedPageType,
        html: null
      }

      if (this.cfUrl) {
        if(this.cfUrl.indexOf('http:') === 0) {
          this.addingNewStep = false
          this.hasHTTPError = true;
          return;
        }
        try {
          const html = await this.fetchFunnelHtml(this.cfUrl)
          if (
            !html ||
            !html.includes('containerWrapper') ||
            !html.includes('app.clickfunnels.com')
          ) {
            this.addingNewStep = false
            this.hasInvalidError = true
          } else {
            newStepDetails.html = html
            this.$emit('newStepDetails', newStepDetails)
          }
        } catch (err) {
          console.error(err)
          this.addingNewStep = false
          this.hasInvalidError = true
        }
      } else {
        this.$emit('newStepDetails', newStepDetails)
      }
    },
    fetchFunnelHtml(url) {
      return fetch(url).then(function(response) {
        return response.text()
      })
    }
  },
  watch: {
    showModal(newValue, oldValue) {
      if (newValue && !oldValue) {
        // show the modal
        // reset form
        this.stepName = ''
        this.stepUrl = ''
        this.cfUrl = ''
        this.addingNewStep = false
        this.acceptTermsToImportFunnel = false
        $(this.$refs.modal).modal('show')
      } else if (!newValue && oldValue) {
        // hide the modal
        $(this.$refs.modal).modal('hide')
      }
    }
  }
})
</script>
