import Vue from 'vue';
import { Modu<PERSON>} from 'vuex';
import { IncomingCallState, IncomingCall, RootState } from '@/store/state_models';

export const incomingCall: Module<IncomingCallState, RootState> = {
    namespaced: true,
    state: {
        available: false,
    },
    getters: {
        get: (state: IncomingCallState, getters: any, rootState: RootState, rootGetters: any) => {
            return state;
        },
    },
    mutations: {
        set: (state: IncomingCallState, payload: IncomingCall) => {
            state.available = true;
            Vue.set(state, 'incomingCall', payload);
        },
        delete: (state: IncomingCallState) => {
            state.available = false;
            Vue.set(state, 'incomingCall', {});
        },
    },
};
