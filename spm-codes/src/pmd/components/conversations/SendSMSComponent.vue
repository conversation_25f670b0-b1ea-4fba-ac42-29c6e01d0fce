<template>
  <div
    class="tab-pane fade show active overflowdisabled"
    id="sms"
    role="tabpanel"
    aria-labelledby="sms-tab"
    data-tour-step="3"
  >
    <div
      class="message-box relative"
      @drop="handleDrag"
      @dragover="handleDragover"
      @dragleave="handleDragleave"
      :class="{focused:isFocus, dragoverd: isDrogover}"
    >
      <div class="form-control mx-0 my-0 px-0 py-0">
        <UITextArea
          v-model="msg"
          rows="4"
          class="pr-12 shadow-sm block w-full focus:outline-none focus:ring-offset-curious-blue-500 focus:border-curious-blue-500 sm:text-sm border-gray-300 rounded-md disabled:opacity-50 resize-none"
          placeholder="Type a message"
          v-validate="'handlebars'"
          data-vv-validate-on="input"
          name="editor"
          @keydown.enter.exact.prevent
          @enter="send"
        ></UITextArea>
        <picker
          v-if="showEmojis"
          set="emojione"
          :style="{ position: 'absolute', bottom: '178px', right: '0px' }"
          title
          @select="insertEmoji"
        />
        <div style="display:inline-block" v-if="allAttachments && allAttachments.length > 0">
          <div
            class="float-md-right mx-2 py-2"
            v-for="attachment in allAttachments"
            :key="attachment"
          >
            <div style="position:relative">
              <GHLFileAttachment :fileUrl="attachment" />
              <a
                @click="removeAttachment(attachment)"
                style="position: absolute;right: -11px;top: -13px;padding: 4px;color: #e93d3d !important;"
                class="pointer"
              >
                <i class="fas fa-minus-circle"></i>
              </a>
            </div>
          </div>
        </div>
      </div>
      <div class="message-action-top">
        <NestedMenu
          class="my-2"
          v-show="menuItems && menuItems.length > 0 && !isOnboarding"
          :items="menuItems"
          :title="'Merge fields'"
          v-on:MenuSelected="menuItemSelected"
        />
        <div class="file-open" :class="{ 'onboarding-hide-component' : isOnboarding }">
          <input type="file" id="file-open" ref="fileupload" @change="onFileChange" multiple />
          <label for="file-open">
            <i class="icon icon-file"></i>
          </label>
        </div>
        <a href="javascript:void(0);" class="emoji-open" @click.stop="toggleEmojis">
          <i class="icon icon-emoji"></i>
        </a>
      </div>
      <div class="message-action-bottom"></div>
      <div v-if="errorMsg" class="--red" style="text-align: center;">{{errorMsg}}</div>
      <span v-show="errors.has('editor')" class="--red">There are issues in your custom variables, please fix them before sending.</span>
      <div class="message-action-bottom">
        <p>
          <!--<button class="btn btn-link py-0 px-2 m-0" v-if="selectedTemplateId"
                  @click.prevent="removeTemplate">
                  <i class="icon icon-minus-circle"></i>
                  Remove Template
          </button>-->
          <a @click.prevent="pickTemplate" class="use-template py-0 px-2" :class="{ 'onboarding-hide-component' : isOnboarding }" >
            <i class="icon icon-document-text" v-b-tooltip.hover title="Use a template"></i>
          </a>
          <a @click.prevent="requestPayment" class="use-template py-0 px-2" :class="{ 'onboarding-hide-component' : isOnboarding }" >
            <i class="icon icon-request" v-b-tooltip.hover title="Request Payment"></i>
          </a>
        </p>
        <div class="email-action-bottom-right items-center flex space-x-2">
          <UIButton
            :class="{invisible: sending}"
            @click.prevent="clear"
            use="outline"
          >Clear</UIButton>

          <div>
            <UIButton
              data-tour-step="4"
              @click.prevent="send"
              type="submit"
              :loading="sending"
              :disabled="sending || loadingManualSMS || errors.has('editor')"
            >
              <SendWhiteIcon class="mr-2"/>
              <span>Send</span>
            </UIButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import restAgent from '../../../restAgent'
import Vue from 'vue'
import ImageTools from '../../../util/image_tools'
import { v4 as uuid } from 'uuid'
import 'emoji-mart-vue-fast/css/emoji-mart.css'
// import { Picker } from 'emoji-mart-vue-fast'
const Picker = () => import( /* webpackChunkName: "lib-emojimart" */ 'emoji-mart-vue-fast').then(m => m.Picker)
import { EventBus } from '@/models/event-bus'
import { MessageTemplate } from '@/models/campaign'
import NestedMenu from '@/pmd/components/NestedMenu.vue'
import { getTagOptions } from '@/util/merge_tags'
import { CustomFields } from '../../../util/custom_fields'
const GHLFileAttachment = () => import('../GHLFileAttachment.vue')
import { blackListedTwilioErrors } from '../../../util/twilio_error_blacklist'
import SendWhiteIcon from '/public/pmd/img/icon-send-white.svg'

import {
  Message,
  Conversation,
  Contact,
  MessageType,
  ManualQueue,
  MessageContentType,
  Template,
  User,
  SMSTemplate,
  Link
} from '../../../models'
import { mapState } from 'vuex'
import { UserState, FileAttachment, CompanyState } from '@/store/state_models'
import * as lodash from 'lodash'
import firebase from 'firebase/app'
const path = require('path');
import { trackGaEvent } from "@/util/helper";

const imageTools = new ImageTools()
const menuItems = [] as any[]
export default Vue.extend({
  props: ['conversation', 'contact', 'manualSMS'],
  components: {
    Picker,
    NestedMenu,
    GHLFileAttachment,
    SendWhiteIcon
  },
  watch: {
    manualSMS: function(data:any){
      this.setManualSMS(data);
    },
    conversation: function(newConv, oldConv) {
      if (newConv && newConv.id && (!oldConv || oldConv.id !== newConv.id)) {
        const data: () => object = <(() => object)>this.$options.data
        if (data) Object.assign(this.$data, data.apply(this))
      }
    },
    msg(val: string) {
      if (this.firstLetter && val && val.length) {
        this.firstLetter = false
        this.msg = val.charAt(0).toUpperCase() + val.slice(1)
      }
    },
  },
  data() {
    return {
      errorMsg: '',
      menuItems,
      showTemplateModal: false,
      firstLetter: true,
      attachments: [] as string[],
      filesAdded: [] as FileAttachment[],
      msg: '',
      showEmojis: false,
      isFocus: false,
      isDrogover: false,
      sending: false,
      //selectedTemplateId: undefined,
      props: {
        pack: { type: Array, required: true },
        labelSearch: { type: String, default: 'Pesquisar...' },
        showCategory: { type: Boolean, default: true },
        emojisByRow: { type: Number, default: 5 },
        showSearch: { type: Boolean, default: () => true }
      },
      loadingManualSMS: false,
    }
  },
  async mounted() {
    EventBus.$on('selected_sms_template', this.templateSelected)
    EventBus.$on('sms_invoice_created', this.invoiceCreated)
    if (this.manualSMS && this.manualSMS.body) this.setManualSMS(this.manualSMS);
    await this.loadCustomFieldItems()
    await this.loadTriggerLinks();
    this.removeOnboardingWantedComponent()
  },
  beforeDestroy() {
    EventBus.$off('selected_sms_template')
    EventBus.$off('sms_invoice_created')
  },
  computed: {
    allAttachments(): string[] {
      if (this.filesAdded.length === 0) return this.attachments
      return this.filesAdded.map(f => f.url).concat(this.attachments)
    },
    isOnboarding(): boolean {
      if (this.$store.state.conversation.onboarding) {
        return true
      }
      return false
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    })
  },
  methods: {
    invoiceCreated(invoice_url: string) {
      this.msg = invoice_url + '\n' + this.msg
    },
    async loadTriggerLinks() {
      if (menuItems && !menuItems.find(item => item.text === 'Trigger links')) {
        let obj = this.$store.getters['conversation/getTriggerLinksMenuItem']
        if (!obj || !obj.text) {
          obj = await this.$store.dispatch('conversation/fetchTriggerLinks', {locationId: this.$route.params.location_id})
        }
        delete obj.location_id
        menuItems.push(obj)
      }
    },
    async loadCustomFieldItems() {
      if (!this.contact || !this.contact.locationId) return
      try {
        menuItems.splice(0,menuItems.length)
        const options = await CustomFields.getList(this.contact.locationId, true)
        menuItems.push(...options)
      } catch (err) {
        console.log(err)
      }
    },
    menuItemSelected(item) {
      if (item) {
        this.msg = this.msg ? `${this.msg} ${item}` : item
      }
    },
    pickTemplate() {
      EventBus.$emit('select_sms_template')
    },
    requestPayment() {
      trackGaEvent('Text to Pay', 'Request button clicked (Text)', this.$route.params.location_id, 1);
      EventBus.$emit('request_payment', 'sms')
    },
    clear() {
      //this.selectedTemplateId = undefined
      this.msg = ''
      this.attachments = []
      this.filesAdded = []
      this.firstLetter = true
      this.showEmojis = false
      this.errorMsg = ''
      this.sending = false
    },
    async getAttachmentsList(locationId, contactId) {
      const urls = [] as string[]
      if (this.filesAdded && this.filesAdded.length > 0) {
        const basePath = `location/${locationId}/contact/${contactId}/`
        const newURLs: string[] = await Promise.all(
          this.filesAdded.map(async attachment => {
            let imagePath = basePath + uuid() + path.extname(attachment.name)
            var uploadPath = firebase.storage().ref(imagePath)
            const snapshot = await uploadPath.put(attachment.data, {
              contentType: attachment.type,
              contentDisposition: `inline; filename="${attachment.name}"`,
              customMetadata: { name: attachment.name }
            })
            return await snapshot.ref.getDownloadURL()
          })
        )
        urls.push.apply(urls, newURLs.filter(url => url))
      }
      urls.push.apply(urls, this.attachments)
      return urls
    },
    send: async function() {
      this.tooltipAction = true;
      if (this.errors.has('editor')) return
      this.errorMsg = ''
      this.showEmojis = false
      if (this.sending) return
      if (!this.contact) {
        this.errorMsg = 'The contact not selected.'
        return
      }
      if (!this.contact.id || !this.contact.locationId) {
        this.errorMsg = 'Cannot identify contact and its location.'
        return
      }
      //const locationId = this.$route.params.location_id
      //const contactId = this.$route.params.contact_id
      // let conv
      // if (this.conversation) {
      //   conv = await Conversation.getById(this.conversation.id)';'
      // }

      if (!this.contact.phone) {
        this.errorMsg = 'The contact does not have an phone number.'
        return
      }

      if (
        this.msg.trim().length == 0 &&
        /*!this.selectedTemplateId &&*/
        lodash.isEmpty(this.attachments) &&
        lodash.isEmpty(this.filesAdded)
      ) {
        this.errorMsg =
          'Make sure you have entered a message or a selected template .'
        return
      }

      this.sending = true
      try {
        let urls = await this.getAttachmentsList(
          this.contact.locationId,
          this.contact.id
        )
        let messageToSend = this.msg
        //const templateId =  this.selectedTemplateId;
        // let messageToSend = '';
        // if (!templateId) {
        //   urls = await this.getAttachmentsList();
        //   messageToSend = this.msg;
        // }

        const data = {
          contactId: this.contact.id,
          type: 'SMS',
          message: messageToSend,
          messageType: MessageType.TYPE_SMS,
          //templateId: templateId,
          attachments: urls
        } as {[key: string]: any};

        if (this.manualSMS && !lodash.isEmpty(this.manualSMS)) { // Adds campaign and campaign status so it shows up in Message object + changes type of message
          data.messageType = MessageType.TYPE_CAMPAIGN_MANUAL_SMS
          data.campaignId = this.manualSMS.campaignId
          data.campaignStatusId = this.manualSMS.campaignStatusId
          data.campaignStepId= this.manualSMS.campaignStepId
          data.preDeterminedMessageId = this.manualSMS.preDeterminedMessageId
        }
        let response = await restAgent.Conversations.message(data)
        this.$emit('messageSent')
        if (!lodash.isEmpty(this.manualSMS)) {
          this.$root.$emit('dialer:queueNextAction')
        }
        this.clear()
      } catch (err) {
        console.log(err.response, err.response.data)
        const errorCode = err.response && err.response.data && err.response.data.code
        if (errorCode === 21408) {
          this.$uxMessage('error', err.response.data.message)
        } else if (!blackListedTwilioErrors.includes(errorCode)) {
          const message = `An error has occured. Please try again or check the Twilio integration. Error message: ${err.response && err.response.data.msg ? err.response.data.msg : err.message}`
          if(this.$route.query.manualId) {
            this.$uxMessage('confirmation', message, async response => {
              if(response === 'ok') {
                console.log('skipped')
                await ManualQueue
                  .collectionRef()
                  .doc(this.$route.query.manualId)
                  .update({
                    skipped: true,
                    date_updated: firebase.firestore.FieldValue.serverTimestamp()
                  })

                this.$emit('messageSent')
                this.$root.$emit('dialer:queueNextAction')
                this.clear()
              }
            }, { okButton: 'Skip', cancelButton: 'Stay'})
          } else {
            this.$uxMessage('error', message)
          }
        }
        console.error(err)
      } finally {
        this.sending = false
      }
    },
    toggleEmojis() {
      this.showEmojis = !this.showEmojis
    },
    insertEmoji(emoji: any) {
      this.toggleEmojis()
      console.log(emoji)
      this.msg += ' ' + emoji.native
    },
    handleDrag(e: any) {
      var fileList = e.dataTransfer.files
      if (fileList && fileList.length > 0) {
        this.vfileAdded(fileList[0])
      }
      this.isDrogover = false
      e.preventDefault()
    },
    handleDragover(e: any) {
      this.isDrogover = true
      e.preventDefault()
    },
    handleDragleave(e: any) {
      this.isDrogover = false
      e.preventDefault()
    },
    async onFileChange(e: any) {
      const element = <HTMLInputElement>e.target
      if (!element.files) return
      for (let i = 0; i < element.files.length; i++) {
        this.vfileAdded(element.files[i])
      }
      element.files = null
    },
    async vfileAdded(file: File) {
      if (!this.conversation) return
      const response = <File | Blob>(
        await imageTools.resize(file, { height: 1000, width: 1000 })
      )
      this.filesAdded.push({
        name: file.name,
        type: file.type,
        url: URL.createObjectURL(response),
        data: response
      })

      this.$refs.fileupload.value = ''
    },
    async deleteFile(file) {
      console.log('Got file:', file)
      if (confirm('Are you sure you want to delete ' + file.name + '?')) {
        var fileRef = firebase.storage().ref(file.fullPath)
        await fileRef.delete()
        if (this.contact) {
          this.contact.remove_attachment(file.downloadURL)
          this.contact.save()
        }
      }
    },
    setManualSMS(data: any) {
      this.loadingManualSMS = true
      this.attachments = []
      this.filesAdded = []
      this.msg = data && data.body || '';
      if (data && !lodash.isEmpty(data.attachments)) {
        this.attachments = lodash.clone(data.attachments) || []
      }
      this.loadingManualSMS = false
    },
    templateSelected(template: MessageTemplate) {
      this.attachments = []
      this.filesAdded = []
      // this.selectedTemplateId = undefined;
      // this.selectedTemplateId = template ? template.id : undefined;
      console.log('Using template:', template)
      const temp = <SMSTemplate>template
      // let extras = {} as { contact: Contact; user?: User }
      // if (this.contact) {
      //   extras.contact = this.contact
      // }
      // if (this.user) {
      //   extras.user = this.user
      // }
      //this.msg = Template.format(temp.body, extras)
      this.msg = temp.body
      //alert(this.selectedTemplateId);
      if (!lodash.isEmpty(temp.attachments)) {
        this.attachments.push(...lodash.clone(temp.attachments))
      }
      if (!lodash.isEmpty(temp.urlAttachments)) {
        this.attachments.push(...lodash.clone(temp.urlAttachments))
      }
    },
    removeAttachment(attachment: string) {
      if (this.attachments.indexOf(attachment) !== -1)
        this.attachments.splice(this.attachments.indexOf(attachment), 1)
      console.log(attachment, this.filesAdded)
      let exists = lodash.find(this.filesAdded, { url: attachment })
      console.log(exists)
      if (exists) {
        this.filesAdded.splice(this.filesAdded.indexOf(exists), 1)
      }
    },
    removeOnboardingWantedComponent() {
      if (this.$store.state.conversation.onboarding) {
        const removeEleme = setInterval(() => {
          const elem = document.getElementsByClassName('use-template')[0];
          if (elem) {
            elem.remove();
            clearInterval(removeEleme)
          }
        },400);
      }
    }
  }
})
</script>

<style scoped>
  textarea.form-control {
    padding-right: 35px;
  }
  .hl_conversations--message-body .message-box .message-action-top, .dropdown.my-2, .file-open, a.emoji-open {
    display: block;
  }
  .message-action-top {
    top: 5px !important;
    right: 15px !important;
  }
  .message-action-top > div, .message-action-top > a {
    margin: 3px 0px 0px 0px !important;
}
.message-action-top > a {
  margin-left: -2px !important;
}
.onboarding-hide-component {
  display: none !important;
}
</style>
