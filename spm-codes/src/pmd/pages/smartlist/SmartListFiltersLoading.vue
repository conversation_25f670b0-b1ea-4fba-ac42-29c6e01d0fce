<template>
  <div>
     <div class="optionsbar" style="height:40px;"></div>
    <!-- class="workinprogress optionsbar" -->
    <div class="card">
    <div class="header">
      <span class="workinprogress" style="height:40px"></span>
      </div>
      <div class="header">
        <div><span class="workinprogress"></span></div>
        <div><span class="workinprogress"></span></div>
      </div>
      <div class="header">
        <div><span class="workinprogress"></span></div>
        <div><span class="workinprogress"></span></div>
      </div>
    </div>
   </div>
</template>

<script>

import Vue from 'vue';
import { SmartList } from '@/models'
export default Vue.extend({name:'SmartListFiltersLoading'});

</script>


<style scoped>
  .optionsbar {
       background:#E8F3FE;
      /* margin-bottom: 10px; */
      height: 40px;
      padding: 10px;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      /* border-bottom: 2px solid #dee5e8; */
  }

</style>
