import { Module } from 'vuex'
import { RootState, PhoneNumbers } from './state_models'

export const numbers: Module<PhoneNumbers, RootState> = {
  namespaced: true,
  state: {
    dialOutNumber: '',
    numbers: []
  },
  getters: {
    get: (
      state: PhoneNumbers,
      getters: any,
      rootState: RootState,
      rootGetters: any
    ) => {
      return state
    }
  },
  mutations: {
    setDialOutNumber: (state: PhoneNumbers, dialOutNumber: string) => {
      state.dialOutNumber = dialOutNumber
    },
    setNumbers: (state: PhoneNumbers, numbers: string[]) => {
      state.numbers = numbers
    }
  }
}
