
import {IFilterOption, FilterOption} from './option_vm';
import { v4 as uuid } from "uuid";
import {Utils} from '@/util/utils'

export interface ISerializationSpecs{
  isForPersistence: boolean;
}

export interface IFilter {
  id: string;
  filterName: string;
  selectedOption: IFilterOption;
  title: string;
  hasChanges: boolean;
  expandedUX: boolean ;
  extras?: {[key:string]: any};
  cannotPersist?:boolean;
  hidden?:boolean;
  readonly isLocked?: boolean;
  updateTitle();
  copy(): IFilter;
  captureState(): void;
  serialize(options?: ISerializationSpecs): any;
  onQueryTransformation?: (payload: any) => any;
  onPeristentTransformation?: (payload:any) => any;
  clearValues();
}

// tslint:disable-next-line: max-classes-per-file
export class Filter implements IFilter {
  public id: string;
  public filterName: string;
  public options: IFilterOption[] = [];
  public title: string;
  public expandedUX: boolean = false;
  public extras ?: {[key:string]: any};
  public hidden:boolean = false;
  public cannotPersist:boolean = false;
  public onQueryTransformation: (filter:IFilter) => any;
  public onPeristentTransformation: (filter:IFilter) => any;
  public get isLocked(){
    return this.extras && this.extras.isLocked === true;
  }

  //private _callbacks: Array< (string) => void > = [];

  public get selectedOption(): IFilterOption {
    if (!this.options || !this.options.length) return null;
    for(let i=0; i < this.options.length; i++) {
        if (this.options[i].selected === true) {
            return this.options[i];
        }
    }
    return null;
    // const selections = this.options.reduce((acm, itm) => {
    //     if ( itm.selected === true) acm.push(itm);
    //     return acm;
    // }, []);
    // if (!selections || selections.length > 1) throw new Error('Select only one option');
    // return selections[0];
  }

  public set selectedOption(val: IFilterOption) {
    if (!val) return;
    let found;
    this.options.forEach((a) => {
      if (a.filterName === val.filterName && a.condition === val.condition) {
        a.selected = true;
        a.firstValue = val.firstValue || a.defaultFirstVal;
        a.secondValue = val.secondValue || a.defaultSecondVal;
        let to = a.nextFilters;
        let from = val.nextFilters;
        if (to && to.length && from && from.length){
          to.forEach(target => {
            const source = from.find(a=> a.filterName === target.filterName);
            if (source) target.selectedOption = source.selectedOption
          });
        }
        found = true;
      } else {
         a.selected = false;
         a.firstValue = undefined;
         a.secondValue = undefined;
         if(a.nextFilters) a.nextFilters.map(a=> a.clearValues());
      }
    });
    if (found === false) throw new Error(`${val.condition} option not found in ${this.filterName} filter`);
    this.updateTitle();
  }

  public get hasChanges(): boolean {
    if (this._originalOptionsCount > 0 && !this.options ) return true;
    if (this._originalOptionsCount !== this.options.length) return true;
    if (this.options) {
      let i = 0;
      for (; i < this.options.length; i++) {
         if (this.options[i].hasChanges) return true;
      }
    }
    return false;
  }

  private _originalOptionsCount: number = 0;

  public static newInstance(name : string, id: string = null, extras : {[key:string] : any} = null): Filter{
    return new Filter(name,id,extras);
  }

  private constructor(filterName : string, id: string = null, extras : {[key:string] : any} = null){
    if (!filterName || filterName.trim() === '') throw new Error('Filter name required to create');
    //console.log(`constructing filter ${filterName} ${id}`);
    this.filterName = filterName;
    this.id = !Utils.isEmptyStr(id) ? id: uuid();
    if (extras) {
      //if (extras.isLocked === true) this.isLocked = true;
      this.extras = extras;
    }
    //console.log(this.id);
    //setTimeout(() => this.updateTitle(), 1500);
  }

  public clearValues(){
    this.options.forEach((a) => {
        a.selected = false;
        a.firstValue = undefined;
        a.secondValue = undefined;
        if(a.nextFilters && a.nextFilters.length > 0) a.nextFilters.map(a=> a.clearValues());
    });
  }

  public hasOption(condition: string): boolean {
      if (!condition || condition.trim() === '') return false ;
      condition = condition.toLowerCase();
      if (this.options) return this.options.findIndex((a) => a.condition.toLowerCase() === condition) !== -1;
  }

  public serialize(options?: ISerializationSpecs): any {
    const isForPersistence = options && options.isForPersistence === true;

    if (isForPersistence && this.cannotPersist === true) return null;

    const obj : any =  {
      id: this.id,
      filterName: this.filterName ,
      filterName_lc: Utils.serverReady(this.filterName)
    };
    if (this.extras) obj.extras =  Object.assign({},this.extras);
    if(this.selectedOption) obj.selectedOption = this.selectedOption.serialize(options);

    if (isForPersistence == true && this.onPeristentTransformation)
      return this.onPeristentTransformation(obj);
    else if (!isForPersistence && this.onQueryTransformation)
      return this.onQueryTransformation(obj)
    else return obj;
  }

  public updateTitle() {
    const s = this.selectedOption;
    const opt = (s ? s.Formatter.format(s) : '');
    let titles = [];
    if (this.filterName === 'Campaign' && opt && opt.length > 0) {
        titles.push(`${opt[0].toUpperCase()}${opt.slice(1)}`);
    } else if (s && s.Formatter && s.Formatter.titleGenerator) {
      titles.push(s.Formatter.titleGenerator(s));
    } else titles.push(`${this.filterName} ${opt}`);

    if (s && s.nextFilters && s.nextFilters.length > 0) {
       s.nextFilters.forEach(a=> {
        a.updateTitle();
        if(a.title) titles.push(a.title);
      });
    }
    this.title = titles.join(', ');
  }

  public copy(): IFilter {
    if (!parent) return;
    const f = new Filter(this.filterName);
    f.cannotPersist = this.cannotPersist;
    if (this.extras) f.extras = Object.assign({},this.extras);
    f.options = [];
    this.options.forEach((a) => f.options.push(a.copy(f)));
    f.updateTitle();
    return f;
  }

  public captureState() {
    if (this.options) {
      this._originalOptionsCount = this.options.length;
      this.options.forEach((a) => a.captureState());
    }
  }

}
