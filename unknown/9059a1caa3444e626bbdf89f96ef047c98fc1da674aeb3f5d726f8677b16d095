<template>
	<!-- Edit Event Modal -->
	<div
		class="modal fade hl_add-opportunities--modal"
		id="edit-event-modal"
		tabindex="-1"
		role="dialog"
		ref="modal"
	>
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<div class="modal-header--inner">
						<h2 class="modal-title" v-if="editMode">
							<i class="icon icon-edit"></i> Edit Bulk Request
						</h2>
						<h2 class="modal-title" v-else>
							<i class="icon icon-plus"></i> Add Bulk Request
						</h2>
					</div>
				</div>
				<div class="modal-body">
					<div class="modal-body--inner">
						<div class="row">
							<div class="col-12">
								<h3>Bulk request</h3>
							</div>
							<div class="col-sm-12">
								<div class="form-group">
									<label>Name</label>
									<input
										type="text"
										class="form-control msgsndr2"
										placeholder="Name"
										v-model.trim="name"
										v-validate="'required'"
										name="msgsndr2"
										data-lpignore="true"
										autocomplete="msgsndr2"
									>
									<span v-show="errors.has('msgsndr2')" class="error">{{ errors.first('msgsndr2') }}</span>
								</div>
							</div>

							<div class="col-sm-6">
								<div class="form-group" id="campaign-picker">
									<label>Campaign</label>
									<select
										class="selectpicker"
										title="Select Campaign"
                    @change="campaignSelect()"
										v-model="campaignId"
										name="campaign"
										v-validate="'required'"
										data-size="5"
										:disabled="editMode || initializing"
									>
										<option v-for="campaign in campaigns" :value="campaign.id">{{campaign.name | toTitleCase}}</option>
									</select>
									<span v-show="errors.has('campaign')" class="error">{{ errors.first('campaign') }}</span>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label>Scheduled Date
									<span
										style="margin-left: 5px;"
										class="input-group-addon"
										v-b-tooltip.hover
										:title="`This date is based on this company's timezone, which is ${timezone}`"
									>
										<i class="fas fa-question-circle"></i>
									</span>
									</label>
									<!-- <datepicker
                    v-model="scheduledDate"
                    :input-class="'form-control date-picker'"
                    :format="'D, MMM dsu'"
                    :placeholder="'Scheduled Date'"
                    :disabled="disabledDates"
                    :typeable="true"
                    name="dueDate"
									/>-->
									<vue-ctk-date-time-picker
                    :locale="getCountryInfo('locale')"
										v-model="scheduledDate"
										:noClearButton="true"
										:minute-interval="5"
										color="#188bf6"
										enable-button-validate
										:input-class="'form-control date-picker'"
										name="scheduledDate"
									/>
									<span v-show="errors.has('scheduledDate')" class="error">Scheduled date is required</span>
								</div>
							</div>
							<div class="col-12">
								<h3>Who should it run for?</h3>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label>Contact Type</label>
									<select
										class="selectpicker"
										title="Pick type"
										v-model="contactType"
										name="contactType"
										v-validate="'required'"
										:disabled="editMode || initializing"
										data-vv-as="contact type"
									>
										<option value="all">All</option>
										<option value="customers">Customers</option>
										<option value="leads">Leads</option>
									</select>
									<span v-show="errors.has('contactType')" class="error">{{ errors.first('contactType') }}</span>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label>Tag</label>
									<select
										class="selectpicker"
										title="Tags"
										name="Tag picker"
										v-model="tag"
										data-size="5"
										:disabled="editMode || initializing"
									>
										<option v-for="tag in existingTags" :value="tag">{{tag}}</option>
									</select>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<div class="error" style="text-align: center;margin-bottom: 10px;">{{errorMessage}}</div>
					<div class="modal-footer--inner nav">
						<button
							:class="{invisible: saving}"
							type="button"
							class="btn btn-light2"
							data-dismiss="modal"
						>Cancel</button>
						<div style="display: inline-block;position: relative;">
							<button
								:class="{invisible: saving}"
								type="button"
								class="btn btn-success"
								@click.prevent="save"
							>
								<span v-if="editMode">Update</span>
								<span v-else>Add</span>
							</button>
							<div style="position: absolute;top: 21%;left: 33%;" v-show="saving">
								<moon-loader :loading="saving" color="#37ca37" size="30px"/>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
import { Campaign, BulkRequest, Location, Tag, getCountryInfo } from '@/models';
import moment from 'moment-timezone';

declare var $: any;

export default Vue.extend({
	props: ['values', 'timezone'],
	data() {
		return {
      getCountryInfo,
			currentLocationId: "",
			errorMessage: '',
			initializing: true,
			editMode: false,
			saving: false,
			campaigns: [] as Campaign[],
			campaignId: '',
			contactType: '' as 'all' | 'customers' | 'leads',
			name: '',
			tag: '',
			scheduledDate: undefined as undefined | string,
			disabledDates: {
				from: moment()
					.add(4, 'weeks')
					.toDate(),
				to: moment()
					.subtract(1, 'day')
					.toDate(),
			},
			bulkRequest: new BulkRequest(),
			existingTags: [] as string[],
		}
	},
	methods: {
		async save() {
        const result = await this.$validator.validateAll();
        if (!result) {
          return false;
		}
		if (!this.timezone) {
			this.$uxMessage('warning', 'Bulk Campaign Requests are based off the Location Timezone, please add one before creating Bulk Requests.')
			return;
		}
        this.saving = true;

        this.bulkRequest.name = this.name;
        this.bulkRequest.contactType = this.contactType;
        this.bulkRequest.campaignId = this.campaignId;
        this.bulkRequest.tags = this.tag ? [this.tag] : [];

        if(this.scheduledDate) this.bulkRequest.scheduledTime = moment(this.scheduledDate, "YYYY-MM-DD hh:mm a").tz(this.timezone, true);
        await this.bulkRequest.save();
        if(this.editMode && this.bulkRequest.status !== 'new') {
          try {
            await this.$http.delete(`/bulk/${this.bulkRequest.id}/pause`);
          } catch (err) {
            console.error(err);
          };
        }
			this.$emit('hidden');
    },
    campaignSelect() {
      $('#campaign-picker a.dropdown-item').tooltip('dispose');
    },
		toggleVisibility() {
			if (this.values && this.values.visible) {
				$(this.$refs.modal).modal('show');
			} else {
				$(this.$refs.modal).modal('hide');
			}
    }
	},
	beforeDestroy() {
		$(this.$refs.modal).off('hidden.bs.modal');
	},
	watch: {
		async values(values: { [key: string]: any }) {
			const data: (() => object) = <(() => object)>this.$options.data;
			if (data) Object.assign(this.$data, data.apply(this));
			this.toggleVisibility();
			if (values.visible) {
				this.currentLocationId = this.$router.currentRoute.params.location_id;
				const location = new Location(await this.$store.dispatch('locations/getById', this.currentLocationId));
				this.existingTags = (await Tag.getByLocationId(this.currentLocationId)).map(tag => tag.name);
				const toDate = moment.tz(this.timezone);
				const fromDate = moment.tz(this.timezone).add(4, 'weeks');
				this.disabledDates = {
					from: new Date(fromDate.year(), fromDate.month(), fromDate.date()),
					to: new Date(toDate.year(), toDate.month(), toDate.date()),
				};

				this.campaigns = await Campaign.getWithLocationId(this.currentLocationId);
				if (values.bulkRequest) {
					this.bulkRequest = values.bulkRequest;
					this.name = this.bulkRequest.name;
					this.contactType = this.bulkRequest.contactType;
					this.campaignId = this.bulkRequest.campaignId;
					this.tag = this.bulkRequest.tags.length > 0 ? this.bulkRequest.tags[0] : '';
					const scheduled = this.bulkRequest.scheduledTime;
					if (scheduled) {
						this.scheduledDate = moment.tz(scheduled, this.timezone).format("YYYY-MM-DD HH:mm:ss");
					}
					this.editMode = true;
				} else {
					this.bulkRequest = new BulkRequest();
          this.bulkRequest.locationId = this.currentLocationId;
          this.bulkRequest.status = 'new';
				}
				this.initializing = false;
			}

		}
	},
	updated() {
		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker('refresh');
    }
    $('#campaign-picker a.dropdown-item').each( function() {
       if($(this).text().length >= 30) {
          $(this).attr('title', $(this).text());
          $(this).tooltip();
       }
    });
		this.toggleVisibility();
	},
	mounted() {
		const _self = this;
		$(this.$refs.modal).on('hidden.bs.modal', function () {
			_self.$emit('hidden');
		});

		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker();
    }
  this.toggleVisibility();
	},
})
</script>

<style>
#campaign-picker .bootstrap-select .dropdown-menu {
  max-width: 125% !important;
}
#campaign-picker .dropdown-item span.text {
  width: 250px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
