<template>
  <div class="transaction-status">
    <SuccessIcon class="transaction-status__icon" />
    <div class="transaction-status__label">Transaction Successful</div>
    <div class="transaction-status__message">
      Your account has been {{type==='reactivate'?'activated' :'upgraded'}}
      <br/>
      An invoice will be emailed to you shortly
    </div>
    <div class="transaction-status__action-btn btn btn-success" @click="$emit('close')" v-if="showCloseBtn">Close</div>
  </div>
</template>

<script>
import SuccessIcon from '@/assets/pmd/img/billing/upgrade-modal/success-icon.svg'

export default {
  props:['showCloseBtn','type'],
  components: {
    SuccessIcon
  }
}
</script>

<style>
.transaction-status{
  padding: 50px;
  text-align: center;
  margin: auto;
  width: fit-content;
}
.transaction-status__icon{
  /* font-size: 100px; */
  width: 80px;
  height: 80px;
  margin: auto;
}
.transaction-status__label{
  line-height: 20px;
  font-size: 16px;
  color: #4F4F4F;
  margin-top: 20px;
  font-weight: 700;
}
.transaction-status__message{
  margin-top: 80px;

  border: 1px solid #E0E0E0;
  box-sizing: border-box;
  border-radius: 9px;

  width: 333px;
  padding: 22px;

  font-size: 14px;
  line-height: 16px;
  text-align: center;

  color: #4F4F4F;
}
.transaction-status__action-btn{
  width: 140px;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  font-weight: 500;
  padding: 12px;
  margin: 50px auto 0px;

  background-color: #27AE60;
  color: #ffffff;
  transition: all 0.3s ease-in-out;
}
.transaction-status__action-btn:hover{
  background-color: #249151;
}
</style>
