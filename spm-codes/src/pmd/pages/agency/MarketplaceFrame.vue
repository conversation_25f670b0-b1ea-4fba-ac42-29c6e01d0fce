<template>
  <div>
    <SideBarV2 v-if="getSideBarVersion === 'v2'"/>
    <SideBar v-else />
    <TopBar />
    <section class="hl_wrapper">
      <section
        class="hl_agency hl_agency-sales-resources"
        style="background-color: white"
      >
        <iframe
          :src="marketplaceURL"
          style="height: calc(100vh - 84px); width: 100%; border: none"
        ></iframe>
      </section>
      <IframeFunnelPayment />
    </section>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import TopBar from '../../components/TopBar.vue'
import SideBar from '../../components/agency/SideBar.vue'
import SideBarV2 from '../../components/sidebar/SideBar.vue'
import { User } from '@/models'
import IframeFunnelPayment from '@/pmd/components/agency/billing/FunnelIframePayment/IframeFunnelPaymentModal.vue';

export default Vue.extend({
  components: { TopBar, SideBar, SideBarV2, IframeFunnelPayment },
  data() {
    return {
      pathExtension: ''
    }
  },
  computed:{
    marketplaceURL(){
      let baseURL = 'https://mp.gohighlevel.com'
      if(this.pathExtension) baseURL = `${baseURL}/${this.pathExtension}`
      const companyId = this.$store.state?.company?.company?.id
      baseURL = baseURL + `?marketplace=${companyId}`
      return baseURL
    },
    user() {
        const user = this.$store.state.user.user
        return user ? new User(user) : undefined
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion']
		},
  },
  created() {
    switch (this.$route.query.t) {
      case 'hippa-compliance': {
        this.pathExtension = 'hippa-compliance'
        break
      }
      case 'priority-support': {
        this.pathExtension = 'priority-support'
        break
      }
      default: this.pathExtension = ''
    }
  }
})
</script>
