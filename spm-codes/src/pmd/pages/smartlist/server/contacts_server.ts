import { Contact } from '@/models';
import axios from 'axios';
import firebase from 'firebase/app';
import { ISortDefintion } from '../vm/interfaces';
import defaults from '@/config'
export interface IContactsQuery {
  location_id: string;
  user_id: string;
  assigned_to: string;
  page: number;
  filters: any[];
  sort: ISortDefintion[];
  page_limit: number;
  only_ids?: boolean;
  search_after: any;
  just_ids: boolean;
}

export interface IContactsResult {
  pageItems: Contact[];
  pageLimit: number;
  page: number;
  allRecordsCount: number;
  totalPages: number;
  searchAfter: any;
}

export interface IContactsServer {
  fetch(query: IContactsQuery): Promise<IContactsResult>;
}

export class ContactsServer implements IContactsServer {

  private apiURL: string;

  constructor(apiURL: string) {
    if (!apiURL || apiURL.trim() === '') throw new Error('Specify right url to intialize smart list fetch');
    this.apiURL = apiURL;
  }

  // tslint:disable-next-line: no-shadowed-variable
  public async fetch(query: IContactsQuery): Promise<IContactsResult> {

    if (!query) throw new Error('Specify query');

    //query.limit = query.pageLimit;

    let response = null
    try {
      response = await axios.post(this.apiURL, query);
    } catch(err) {
      err.serverMessage = err?.response?.data?.msg || err?.response?.data?.message;
      throw err;
    }
    if (!response) {
      return Promise.reject('No response. Error fetching contacts for query');
    }
    if (response.status !== 200) {
      //console.log(response);
      return Promise.reject(response);
    }
    const data: Array<{ [key: string]: any }> = response?.data?.hits?.hits;
    const allRecordsCount = response?.data?.hits?.total.value;
    let totalPages = 0;

    if (allRecordsCount <= 0) totalPages = 0;
    else if (allRecordsCount <= query.page_limit) totalPages = 1;
    else totalPages = Math.ceil(allRecordsCount / query.page_limit);
    // console.log('Data in contacts server');
    // console.log(data);
    if (data) {
      const returnthis = {
        pageItems: data.map((ec) => new Contact({ id: ec._id, ...ec._source })),
        pageLimit: query.page_limit,
        page: query.page,
        allRecordsCount,
        totalPages,
        searchAfter: data && data.length > 0 ? data[data.length - 1].sort : null
      } as IContactsResult;
      // Switch alert on to track paging issues
      const log = `- Smart List Fetch Size / Query Page Limit - ${query.page_limit}`+ '\r\n' +
                  `- Current Query Page - ${query.page}`+ '\r\n' +
                  `- TotalPages ${totalPages}` + '\r\n' +
                  `- next searchAfter - ${JSON.stringify(returnthis.searchAfter)}` + '\r\n' +
                  `- Total Records Count - ${allRecordsCount}`+ '\r\n';
                  `- Query - ${JSON.stringify(query)}`+ '\r\n';
      console.log(log);
      //console.log(returnthis);
      return returnthis;
    } else {
      return {
        pageItems: [], pageLimit: query.page_limit,
        page: query.page, allRecordsCount: 0,
        totalPages: 0
      } as IContactsResult;
    }
  }

}


// tslint:disable-next-line: max-classes-per-file
export class ContactChnageListener {

  public static subscribe(locationId: string) {
    ContactChnageListener.unsubscribe();
    //firebase.firestore().collection('contacts').doc.do
    //firebase.firestore().collection('contacts')
    ContactChnageListener._disconn =
      firebase.firestore().collection('contacts')
        .where('location_id', '==', locationId)
        .onSnapshot(snapshot => {
          snapshot.docChanges().forEach(function (change) {
            if (change.type === "added") {
              console.log("New contact: ", change.doc.data());
            }
            if (change.type === "modified") {
              console.log("Modified contact: ", change.doc.data());
            }
            if (change.type === "removed") {
              console.log("Removed contact: ", change.doc.data());
            }
          });
          // snapshot.docs.map(d =>  {
          // const x = new Contact(d);
          // console.log(x.firstName);
          // console.log(x);
          // });
        });
  }

  public static unsubscribe() {
    if (ContactChnageListener._disconn) ContactChnageListener._disconn();
  }

  private static _disconn;

}


export class DisplayContactFetcher {
  public async fetch(params: {locationId: string; contactIds: string[]}): Promise<Contact>{
    if (!params || !params.locationId || !params.contactIds || !params.contactIds.length) return [];
    let contactsData = await axios.post(
      `${defaults.baseUrl}/getContactDetails`,
      {
        allDetails: true,
        locationId: params.locationId,
        contactIds: params.contactIds
      }
    )
    if(contactsData && contactsData.data && contactsData.data.hits) {
      return contactsData.data.hits.map(element =>
        new Contact(element)
      )
    }
    return [];
  }
}
