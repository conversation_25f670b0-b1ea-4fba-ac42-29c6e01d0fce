<template>
  <i :class="['icon', funnelStepIcon]"></i>
</template>
<script lang="ts">
import Vue from 'vue'
import { SALES_FUNNEL_PAGE, OPTIN_FUNNEL_PAGE, MISC_FUNNEL_PAGE } from '../../../util/funnel_consts'

export default Vue.extend({
  props: {
    funnelStepType: {
      type: String,
      required: false
    }
  },
  computed: {
    funnelStepIcon(): string {
      let stepIcon = ''

      switch (this.funnelStepType) {
        case SALES_FUNNEL_PAGE: {
          stepIcon = 'icon-cart'
          break
        }
        case OPTIN_FUNNEL_PAGE: {
          stepIcon = 'icon-mail'
          break
        }
        case MISC_FUNNEL_PAGE: {
          stepIcon = 'icon-list'
          break
        }
        default:
          stepIcon = 'icon-info'
      }

      return stepIcon
    }
  }
})
</script>

