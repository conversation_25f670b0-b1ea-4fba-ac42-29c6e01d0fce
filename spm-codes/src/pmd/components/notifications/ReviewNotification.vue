<template>
	<div :class="{'-notification': !notification.read}">
		<div class="avatar --sm">
			<div class="avatar_img">
				<img :src="imgUrl">
			</div>
		</div>
		<p>
			<strong>{{notification.notificationData.reviewerName}}</strong>
			<font v-if="notification.type=='review'"> has left a new review.</font>
			<font v-if="notification.type=='reviewReply'"> replied to the review.</font>
		</p>
		<p class="location">{{getLocationName}}</p>
		<p class="time-date">{{notification.dateAdded.calendar()}}</p>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
import ListingHelper from '../../../util/listing_helper';

export default Vue.extend({
	props: ['notification'],
	computed: {
		imgUrl(): string | undefined {
			if (this.notification.notificationData && this.notification.notificationData.source) {
				const listing = ListingHelper.getNetworkInfo(parseInt(this.notification.notificationData.source));
				if (listing) return listing.imgPath;
			}
		},
		location(): { [key: string]: any } {
			return this.$store.getters['locations/getById'](this.notification.locationId);
		},
		getLocationName(): string {
			if (!this.location) return '';
			let name = this.location.name;
			if (this.location.city || this.location.state) name += ', ';
			if (this.location.city) name += this.location.city;
			if (this.location.city && this.location.state) name += ', ';
			if (this.location.state) name += this.location.state;
			return name;
		}
	},
});
</script>
