<template>
  <div
    class="chat-instractions"
    v-if="instruction.steps && steps.length > 0 && currentStep >= 0"
  >
    <h3>{{ instruction.heading }}</h3>
    <div
      v-for="(step, index) in instruction.steps"
      :key="index"
      style="width: 100%"
    >
      <p v-show="currentStep == index" v-html="step.text"></p>
      <div
        v-show="currentStep == index"
        class="chat-step-img"
        :class="{
          'flex-start': !step.logo,
          'flex-center': step.logo,
        }"
      >
        <!-- <moon-loader
            v-if="!screenshot && currentStep >= steps.length - 1"
            class="chat-cms-width"
            :loading="!screenshot && currentStep >= steps.length - 1"
            color="#37ca37"
            size="300px"
          /> -->
        <div v-if="step.screenshot" class="chat-screenshot">
          <img
            v-if="screenshot"
            :src="screenshot"
            style="width: 100%; height: 100%"
          />
          <img
            v-else
            src="@/assets/pmd/img/launchpad/Other2-min.png"
            style="width: 100%; height: 100%"
          />
          <div
            class="chat-screeshot-notification"
            v-if="chatIntegrated === false"
          >
            <svg
              viewBox="0 0 24 24"
              stroke="none"
              width="20px"
              height="20px"
              focusable="false"
              color="#E73E51"
              style="
                stroke: currentcolor;
                fill: currentcolor;
                stroke-width: 1.5;
              "
            >
              <path
                d="M20.1 2c1 0 1.8.8 1.9 1.7V10c0 .3-.3.6-.6.6s-.6-.2-.6-.5V3.9c0-.3-.2-.6-.5-.6H3.9c-.3 0-.6.2-.6.5v12.6c0 .3.2.6.5.6h1.3c.3 0 .6.2.6.5v2.6l2.7-2c.2-.2.6-.2.8 0l.1.1c.2.2.2.6 0 .8l-.1.1-3.7 2.8c-.4.3-.9 0-1-.4v-3.2h-.6c-1 0-1.8-.8-1.9-1.7V3.9c0-1 .8-1.8 1.7-1.9H20.1zm-6.3 9.4c1.7-.9 3.7-.8 5.4.1 1.7 1 2.8 2.9 2.8 4.9s-1.1 3.9-2.8 4.9c-1.7 1-3.9 1-5.6 0-1.7-1-2.8-2.9-2.8-4.9s1.1-3.9 2.8-4.9l.2-.1zm6.1 2.4l-6.1 6.1c.1.1.3.2.4.3 1.4.8 3 .8 4.4 0 1.4-.8 2.2-2.2 2.2-3.8-.1-1-.4-1.9-.9-2.6zm-1.3-1.2c-1.4-.8-3-.8-4.4 0-1.4.8-2.2 2.2-2.2 3.8 0 1 .3 1.9.9 2.6l6.1-6.1c-.1-.1-.3-.2-.4-.3z"
                stroke="none"
              ></path>
            </svg>
            <p style="font-size: 14px; font-weight: bold">Not Installed</p>
            <p style="margin-top: 0px">
              We suggest forwarding the instructions to your web admin, or
              contacting our support team.
            </p>
          </div>
          <div class="chat-screeshot-notification" v-else-if="chatIntegrated">
            <CelebrationIcon
              width="20px"
              height="20px"
            />
            <p style="font-size: 14px; font-weight: bold">Installed</p>
            <p style="margin-top: 0px">
              Congratulations, webchat widget is successfully installed!
            </p>
          </div>
        </div>
        <img
          v-else-if="step.img"
          :src="getImgUrl(step.img)"
          :class="{
            'img-default': !step.logo,
            'img-logo': step.logo,
          }"
        />
        <div v-else-if="step.widgetCode" class="chat-widget-code">
          <div class="chat-widget-header">
            <h6>Your Widget Code</h6>
            <button
              id="copy-clipboard"
              class="btn btn-primary active next-btn"
              @click="copyToClipboard"
            >
              Copy
            </button>
            <b-tooltip
              triggers="click"
              target="copy-clipboard"
              :show.sync="showCopiedCodeToolTip"
            >
              Copied
            </b-tooltip>
          </div>
          <div class="chat-script">{{ getScriptCode() }}</div>
        </div>
        <div v-else-if="step.apiKey" class="chat-widget-code">
          <div class="chat-widget-header">
            <h6>Your API Key</h6>
            <button
              id="copy-clipboard"
              class="btn btn-primary active next-btn"
              @click="copyToClipboard"
            >
              Copy
            </button>
            <b-tooltip
              triggers="click"
              target="copy-clipboard"
              :show.sync="showCopiedCodeToolTip"
            >
              Copied
            </b-tooltip>
          </div>
          <div class="chat-script" style="height: 70px !important">
            {{ location.apiKey }}
          </div>
        </div>
      </div>
    </div>
    <div class="chat-step-count">
      Step {{ currentStep + 1 }} of {{ steps.length }}
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { Location } from '@/models'

const DEPLOYMENT_BASE_URL = 'https://widgets.leadconnectorhq.com/'
const TEXT_WIDGET_DEPLOYMENT = `${DEPLOYMENT_BASE_URL}loader.js`
const TEXT_WIDGET_IE_DEPLOYMENT = `${DEPLOYMENT_BASE_URL}chat-widget/loader.js`
const DEFAULT_LEGAL_MESSAGE =
  'By submitting you agree to receive SMS or e-mails for the provided channel. Rates may be applied.'
const DEFAULT_REVISIT_MSG = 'Welcome back {{name}}, How can we help you today?'
const DEFAULT_HEADING = 'Have a question?'
const DEFAULT_SUB_HEADING =
  'Enter your question below and a representative will get right back to you.'
const DEFAULT_SUCCESS_MESSAGE =
  'One of our representatives will contact you shortly.'
const DEFAULT_THANKS_MESSAGE = 'Thank You!'
const DEFAULT_PROMPT_MESSAGE = 'Hi there, have a question? Text us here.'
import CelebrationIcon from '@/assets/pmd/img/celebration.svg'
export default Vue.extend({
  components:{CelebrationIcon},
  props: [
    'cms',
    'instruction',
    'currentStep',
    'screenshot',
    'chatIntegrated',
    'widgetSettings',
  ],
  data() {
    return {
      showCopiedCodeToolTip: false,
      loaderScriptURL: TEXT_WIDGET_DEPLOYMENT,
      IELoaderScriptURL: TEXT_WIDGET_IE_DEPLOYMENT,
      widgetPrimaryColor: '#188bf6',
      steps: [] as any,
      locationId: '',
      location: {} as Location,
    }
  },
  async mounted() {
    this.locationId = this.$router.currentRoute.params.location_id
    this.location = new Location(
      await this.$store.dispatch(
        'locations/getCurrentLocation',
        this.locationId
      )
    )
    if (this.cms) this.steps = this.instruction.steps
  },
  watch: {
    cms: async function () {
      this.steps = this.instruction.steps
    },
  },
  methods: {
    copyToClipboard() {
      this.showCopiedCodeToolTip = true
      if (this.steps[this.currentStep].widgetCode) {
        this.clipboardCopy(this.getScriptCode())
      } else if (this.steps[this.currentStep].apiKey) {
        this.clipboardCopy(this.location.apiKey)
      }
      setTimeout(() => (this.showCopiedCodeToolTip = false), 2000)
    },
    getImgUrl(pic: string) {
      const images = require.context(
        '../../../assets/pmd/img/launchpad/',
        false,
        /(\.png|\.svg)$/
      )
      return images('./' + pic)
    },
    getScriptCode() {
      const elementName = 'chat-widget'
      const script = 'script'
      const attrPrefix = ''
      const revisitMessage = this.widgetSettings.revisitPromptMsg
      const importScriptCode = `<${script} src="${this.loaderScriptURL}"
          data-resources-url="${this.IELoaderScriptURL}" ></${script}>`

      let scriptCode = `    <${elementName}
            data-chat-widget
            ${attrPrefix}style="--chat-widget-primary-color: ${
        this.widgetPrimaryColor
      }; --chat-widget-active-color:${
        this.widgetPrimaryColor
      } ;--chat-widget-bubble-color: ${this.widgetPrimaryColor}"
            ${attrPrefix}location-id="${this.locationId}"
            ${attrPrefix}heading="${this.widgetSettings.heading}"
            ${attrPrefix}sub-heading="${this.widgetSettings.subHeading}"
            ${attrPrefix}prompt-msg="${this.widgetSettings.promptMsg}"
            ${attrPrefix}legal-msg="${this.widgetSettings.legalMsg}"
            ${attrPrefix}show-prompt="${this.widgetSettings.showPrompt}"
            ${attrPrefix}use-email-field="${this.widgetSettings.useEmailField}"
            ${attrPrefix}enable-revisit-message="${
        this.widgetSettings.enableRevisitMessage
      }"
            ${attrPrefix}revisit-prompt-msg="${revisitMessage}"
            ${attrPrefix}support-contact="${this.widgetSettings.supportContact}"
            ${attrPrefix}success-msg="${this.widgetSettings.successMsg}"
            ${attrPrefix}thank-you-msg="${this.widgetSettings.thankYouMsg}"
            ${attrPrefix}prompt-avatar="${this.widgetSettings.promptAvatar}"
            ${attrPrefix}auto-country-code="${
        this.widgetSettings.autoCountryCode
      }"
            ${attrPrefix}country-code="${
        this.widgetSettings.countryCode ? this.widgetSettings.countryCode : 'US'
      }"
      ></${elementName}>
       ${importScriptCode}`

      let startIndex = scriptCode.indexOf('data-chat-widget')
      let endIndex = scriptCode.indexOf('\n', startIndex)
      scriptCode = scriptCode.replace(
        scriptCode.substring(startIndex, endIndex + 13),
        ''
      )
      if (
        DEFAULT_PROMPT_MESSAGE === this.widgetSettings.promptMsg ||
        !this.widgetSettings.showPrompt
      ) {
        let startIndex = scriptCode.indexOf(`${attrPrefix}prompt-msg="`)
        let endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }
      if (DEFAULT_HEADING === this.widgetSettings.heading) {
        let startIndex = scriptCode.indexOf(`${attrPrefix}heading="`)
        let endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }

      if (DEFAULT_SUB_HEADING === this.widgetSettings.subHeading) {
        let startIndex = scriptCode.indexOf(`${attrPrefix}sub-heading="`)
        let endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }
      if (
        this.widgetSettings.enableRevisitMessage ||
        !this.widgetSettings.showPrompt
      ) {
        let startIndex = scriptCode.indexOf(
          `${attrPrefix}enable-revisit-message="`
        )
        let endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
        if (
          DEFAULT_REVISIT_MSG === this.widgetSettings.revisitPromptMsg ||
          !this.widgetSettings.showPrompt
        ) {
          startIndex = scriptCode.indexOf(`${attrPrefix}revisit-prompt-msg="`)
          endIndex = scriptCode.indexOf('\n', startIndex)
          scriptCode = scriptCode.replace(
            scriptCode.substring(startIndex, endIndex + 13),
            ''
          )
        }
      } else {
        const startIndex = scriptCode.indexOf(
          `${attrPrefix}revisit-prompt-msg="`
        )
        const endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }

      if (this.widgetSettings.legalMsg === DEFAULT_LEGAL_MESSAGE) {
        const startIndex = scriptCode.indexOf(`${attrPrefix}legal-msg="`)
        const endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }

      if (this.widgetSettings.showPrompt) {
        const startIndex = scriptCode.indexOf(`${attrPrefix}show-prompt="`)
        const endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }
      if (!this.widgetSettings.useEmailField) {
        const startIndex = scriptCode.indexOf(`${attrPrefix}use-email-field="`)
        const endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }
      if (!this.widgetSettings.supportContact) {
        const startIndex = scriptCode.indexOf(`${attrPrefix}support-contact="`)
        const endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }
      if (
        !this.widgetSettings.successMsg ||
        DEFAULT_SUCCESS_MESSAGE === this.widgetSettings.successMsg
      ) {
        const startIndex = scriptCode.indexOf(`${attrPrefix}success-msg="`)
        const endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }

      if (
        !this.widgetSettings.thankYouMsg ||
        DEFAULT_THANKS_MESSAGE === this.widgetSettings.thankYouMsg
      ) {
        const startIndex = scriptCode.indexOf(`${attrPrefix}thank-you-msg="`)
        const endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }
      if (
        !this.widgetSettings.promptAvatar ||
        !this.widgetSettings.showPrompt
      ) {
        const startIndex = scriptCode.indexOf(`${attrPrefix}prompt-avatar="`)
        const endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }
      if (this.widgetSettings.autoCountryCode) {
        let startIndex = scriptCode.indexOf(`${attrPrefix}auto-country-code="`)
        let endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
        startIndex = scriptCode.indexOf(`${attrPrefix}country-code="`)
        endIndex = scriptCode.indexOf('"', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 4),
          ''
        )
      }
      return scriptCode
    },
  },
})
</script>
<style scopes>
.chat-instractions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: start;
  padding: 20px;
}
.chat-instractions .chat-step-img {
  height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  /* align-items: start; */
  width: 100%;
}
.chat-instractions .chat-step-img img {
  border-radius: 12px;
}
.chat-instractions .chat-step-img .img-margin {
  margin-top: 30px;
}
.chat-instractions .chat-step-img .chat-widget-code {
  background: rgb(244, 246, 249);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  height: 370px;
  justify-content: center;
  margin: 0px auto;
  padding: 0px 32px;
  width: 100%;
  box-sizing: border-box;
  margin-top: 10px;
}
.chat-instractions .chat-step-img .chat-widget-code .chat-widget-header {
  align-items: center;
  color: rgb(99, 114, 125);
  display: flex;
  justify-content: space-between;
}
.chat-instractions .chat-step-img .chat-widget-code .chat-widget-header h4 {
  margin: 22px 0px;
  font-weight: 500;
}
.chat-instractions .chat-step-count {
  margin: 10px;
  color: rgb(99, 114, 125);
  font-size: 12px;
  text-align: center;
  width: 100%;
}
.chat-script {
  white-space: pre-wrap;
  text-align: start;
  background: rgb(255, 255, 255);
  border-radius: 12px;
  border: 2px solid rgb(173, 182, 190);
  color: rgb(99, 114, 125);
  display: flex;
  font-family: Regular, Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 13px;
  justify-content: center;
  margin: 0px auto;
  padding: 22px;
  width: 100%;
  box-sizing: border-box;
  overflow: auto;
  margin-top: 20px;
  height: 150px;
}
.chat-cms-width {
  width: 450px;
}
.chat-screenshot-container {
  display: flex;
  justify-content: center;
  align-items: center;
}
.chat-screenshot {
  display: flex;
  justify-content: center;
  height: 370px;
  position: relative;
  width: 100%;
  margin: 0px auto;
  border-radius: 12px;
}
.chat-screeshot-notification {
  transform: translateY(-50%);
  align-items: center;
  background: rgb(255, 255, 255);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 0px auto;
  min-height: 170px;
  padding: 12px;
  position: absolute;
  text-align: center;
  top: 50%;
  width: 290px;
  box-shadow: 0 1em 2em -0.5em rgba(0, 0, 0, 0.35);
}
.flex-start {
  align-items: start;
}
.flex-center {
  align-items: center;
}
.img-default {
  width: 100%;
  max-height: 370px;
}
.img-logo {
  width: 200px;
  height: 200px;
}
</style>
