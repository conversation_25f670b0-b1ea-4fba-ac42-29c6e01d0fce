<template>
  <modal
    v-if="modalShow"
    :showCloseIcon="false"
    :noBackdropClose="true"
    :maxWidth="700"
    modalMaskBgColor="white"
    id="launchpadSubAccount"
  >
    <div class="hl_settings--body">
      <div class="container-fluid">
        <div class="row">
          <div class="hl_settings--main col-lg-12">
            <div class="card">
              <div class="card-body" style="padding: 0.5rem 1.5rem !important">
                <div class="text-center mb-4">
                  <div class="popup-title">Let us setup your first client</div>
                </div>
                <form
                  @submit.prevent="validateBeforeSubmit()"
                  data-vv-scope="form-subaccount"
                  name="form-subaccount"
                >
                  <div class="row">
                    <div class="col-sm-12">
                      <div class="form-group">
                        <UITextInputGroup
                          type="text"
                          placeholder="Client Name"
                          v-model="clientName"
                          v-validate="'required'"
                          name="clientName"
                          data-lpignore="true"
                          autocomplete="clientName"
                          data-vv-as="Client name"
                          label="Enter name of one of your clients"
                          :error="errors.has('form-subaccount.clientName')"
                          :errorMsg="errors.first('form-subaccount.clientName')"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-sm-6">
                      <div class="form-group">
                        <UITextInputGroup
                          type="text"
                          placeholder="First Name"
                          v-model="firstName"
                          v-validate="'required'"
                          name="firstName"
                          data-lpignore="true"
                          autocomplete="firstName"
                          data-vv-as="First name"
                          label="First Name of the Business Owner"
                          :error="errors.has('form-subaccount.firstName')"
                          :errorMsg="errors.first('form-subaccount.firstName')"
                        />
                      </div>
                    </div>
                    <div class="col-sm-6">
                      <div class="form-group">
                        <UITextInputGroup
                          type="text"
                          placeholder="Last Name"
                          v-model="lastName"
                          v-validate="'required'"
                          name="lastName"
                          data-lpignore="true"
                          autocomplete="lastName"
                          data-vv-as="Last name"
                          label="Last Name"
                          :error="errors.has('form-subaccount.lastName')"
                          :errorMsg="errors.first('form-subaccount.lastName')"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-sm-6">
                      <div class="form-group">
                        <UITextInputGroup
                          type="text"
                          placeholder="Email"
                          v-model="email"
                          v-validate="{
                            required: true,
                            email: true,
                            uniqueEmail: [''],
                          }"
                          name="email"
                          data-lpignore="true"
                          autocomplete="email"
                          label="Email Address"
                          :error="errors.has('form-subaccount.email')"
                          :errorMsg="errors.first('form-subaccount.email')"
                        />
                      </div>
                    </div>
                    <div class="col-sm-6">
                      <div class="form-group">
                        <UITextLabel>Mobile / Business Phone</UITextLabel>
                        <PhoneNumber
                          placeholder="Phone Number"
                          v-model="phone"
                          name="phone"
                          autocomplete="phone"
                          v-validate="{
                            required: true,
                            phone: true,
                            twilioPhone: incomingPhoneNumbers.map(
                              phone => phone.phone_number
                            ),
                          }"
                          data-vv-as="phone"
                          data-vv-validate-on="input"
                          :error="errors.has('form-subaccount.phone')"
                          :errorMsg="errors.first('form-subaccount.phone')"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-sm-12">
                      <div class="form-group">
                        <UITextInputGroup
                          type="text"
                          placeholder="http://example.com"
                          v-model="website"
                          name="website"
                          :disabled="hasWebsite"
                          v-validate="{
                            validUrl: true,
                          }"
                          data-vv-as="website"
                          data-vv-validate-on="input"
                          label="Website"
                          :error="errors.has('form-subaccount.website')"
                          :errorMsg="errors.first('form-subaccount.website')"
                        />
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-sm-12">
                      <div class="form-group">
                        <div
                          class="form-check-inline"
                          style="margin-right: 50px"
                        >
                          <label class="form-check-label">
                            <UICheckbox
                              class="form-check-input"
                              name="hasWebsite"
                              v-model="hasWebsite"
                            />I don't have a website
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-sm-12">
                      <div class="form-group text-center" style="float: right">
                        <UIButton
                          type="submit"
                          :disabled="creating"
                          :loading="creating"
                        >
                          <span>Create</span>
                        </UIButton>
                        <div
                          class="launchpad-do-later"
                          @click.prevent="createDefaultLocation"
                        >
                          I'll do this later
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </modal>
</template>
<script lang="ts">
import Vue from 'vue'
import Modal from '@/pmd/components/common/Modal.vue'
import { Company, User, PrivateUser, TwilioAccount, Location } from '@/models'
import { mapState } from 'vuex'
import { UserState, CompanyState } from '../../../store/state_models'
import PhoneNumber from '../util/PhoneNumber.vue'
import { Permissions, UserSource, UserSourceChannel } from '@/models/user'
import * as lodash from 'lodash'
import depreciatedFeatures from '@/util/depreciated_features';

export default Vue.extend({
  props: ['userId', 'modalShow'],
  components: {
    Modal,
    PhoneNumber,
  },
  data() {
    return {
      accepting: false,
      clientName: '',
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      creating: false,
      hasWebsite: false,
      website: '',
      twilioAccount: {} as TwilioAccount,
      meetingLocation: undefined,
      timezone: undefined,
      openHours: [],
      userCalendar: '' as string,
      userTwilio: '' as undefined | string,
      existingLocations: {} as { [key: string]: string },
      incomingPhoneNumbers: [] as Array<{ [key: string]: any }>,
      locations: [] as Location[],
      passwordText: '',
      autofillSignature: false,
      replySignature: false,
      tinymcehtml: '' as string,
      userPermissions: {
        campaigns_enabled: true,
        campaigns_read_only: false,
        workflows_enabled: true,
        workflows_read_only: false,
        contacts_enabled: true,
        triggers_enabled: true,
        opportunities_enabled: true,
        settings_enabled: true,
        tags_enabled: true,
        lead_value_enabled: true,
        dashboard_stats_enabled: true,
        bulk_requests_enabled: true,
        appointments_enabled: true,
        reviews_enabled: true,
        online_listings_enabled: true,
        phone_call_enabled: true,
        conversations_enabled: true,
        assigned_data_only: false,
        funnels_enabled: true,
        websites_enabled: true,
        marketing_enabled: true,
        adwords_reporting_enabled: true,
        facebook_ads_reporting_enabled: true,
        attributions_reporting_enabled: true,
        membership_enabled: true,
        bot_service: false,
        agent_reporting_enabled: true,
      } as Permissions,
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
  },
  methods: {
    async validateBeforeSubmit() {
      let result = await this.$validator.validateAll('form-subaccount')

      if (!this.hasWebsite && !this.website) {
        this.$validator.errors.add({
          field: 'form-subaccount.website',
          msg: 'The Website field is required',
        })
        result = false
      }

      if (!result) {
        return false
      }
      this.creating = true
      const body = {
        businessName: this.clientName,
        phone: this.phone,
        email: this.email,
        firstName: this.firstName,
        lastName: this.lastName,
        website: '',
      }
      if (!this.hasWebsite) {
        body.website = this.website
      }
      const location = await this.createLocation(body)
      try {
        await this.createUser(location.data.id)
        await this.$http.post('/launchpad/schedule', {
          locationId: location.data.id,
          types: ['gmb', 'facebook'],
          delay: { key: 'days', value: 2 },
        })
      } catch (err) {
        console.log(err)
      }
      this.creating = false
      await Location.collectionRef().doc(location.data.id).update({
        depreciated_features: depreciatedFeatures
      });
      this.$root.$emit('redirect-launchpad', location.data.id)
    },
    async createDefaultLocation() {
      this.creating = true
      const body = {
        businessName: this.company.name || 'Default account',
        phone: this.user.phone,
        email: this.user.email,
        isContactCreate: true,
      }
      await this.createLocation(body)
      this.$router.push({ name: 'dashboard' })
      this.creating = false
    },
    async createLocation(body: any) {
      try {
        const options = {
          headers: {
            'Content-Type': 'application/json',
            'internal-channel': 'web',
          },
        }
        const location = await this.publicApi.post(
          '/v1/locations/onboarding',
          body,
          options
        )
        if (location.status == 200) {
          const company = await Company.getById(this.company.id)
          company.onboardingInfo.location = false
          await company.save()
          return location
        }
        return false
      } catch (error) {
        console.log(error)
        this.$router.push({ name: 'dashboard' })
      }
    },
    async createUser(locationId: string) {
      try {
        const user = new User()
        user.role = User.ROLE_ADMIN
        user.companyId = this.company.id
        user.type = User.TYPE_ACCOUNT
        user.permissions = this.userPermissions
        user.firstName = this.firstName
        user.lastName = this.lastName
        user.email = this.email.trim()
        user.phone = this.phone
        user.extension = ''
        user.isPasswordPending = false
        user.locations[locationId] = ''
        user.userEmailSignature[locationId] = {
          signature: this.tinymcehtml,
          autofill: this.autofillSignature,
          reply_signature: this.replySignature,
        }

        const defaultOpenHours = []

        if (this.user.twilioPhone && !lodash.isEmpty(this.twilioAccount)) {
          this.twilioAccount.save()
        }

        for (var i = 0; i < 7; i++) {
          defaultOpenHours.push({
            days_of_the_week: [i],
            hours: [
              {
                open_hour: 0,
                open_minute: 0,
                close_hour: 0,
                close_minute: 0,
              },
            ],
          })
        }

        user.locationWiseMeetingLocation[locationId] =
          this.meetingLocation || ''
        user.locationWiseZoomAdded[locationId] = this.isZoomAdded || false
        user.locationWiseTimezone[locationId] = this.timezone || ''
        user.locationWiseOpenHours[locationId] = defaultOpenHours
        user.userCalendar[locationId] = this.userCalendar
        await user.setCreatedByAndOrLastUpdatedBy(true, UserSource.LaunchPad, UserSourceChannel.WEB_APP)
        const admin = await user.save()
        await admin.syncGlobalListsForLocation(locationId)
        try {
          let response = this.$http.get('/resetBadgeNumber?user_id=' + admin.id)
        } catch (err) {
          console.error(err)
        }
        this.$emit('created')
        await this.sendMagicLink(admin.email)
      } catch (err) {
        console.log(err)
      }
    },
    async sendMagicLink(email: string) {
      try {
        await this.$http.post('/send/login_credentials', {
          email: email,
        })
      } catch (err) {
        console.log(err)
      }
    }
  },
})
</script>
<style scoped>
.terms-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.highlevel-logo {
  width: 9rem;
  height: 2rem;
}
#launchpadSubAccount .popup-title {
  font-weight: bold;
  font-size: 1.5rem;
  margin-top: 5px;
  color: #000;
}
.popup-subtitle {
  font-weight: normal;
  font-size: 0.75rem;
  color: rgb(168, 164, 164);
}
.form-container {
  display: flex;
  flex-direction: row;
  margin-top: 100px;
}
.form-element {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.checkbox-label {
  margin-left: 10px;
  margin-top: 10px;
  font-size: 0.75rem;
}
.button-grp {
  margin-bottom: 20px;
  margin-top: 10px;
}
.button-grp button {
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
}
.button-grp button i {
  margin-left: 11px;
  margin-right: 10px;
}
.form-body {
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: start;
  width: 80%;
}
.form-body .form-group {
  width: 100%;
  margin-top: 10px;
}
.select-style {
  -webkit-appearance: auto !important;
  -moz-appearance: auto !important;
  appearance: auto !important;
  font-size: 0.75rem !important;
  height: calc(1.85rem + 2px) !important;
  padding: 0.3rem 1.5rem !important;
  line-height: 1;
}
.disalbed_btn {
  cursor: not-allowed;
}
#launchpadSubAccount .card {
  margin-bottom: 0px !important;
  font-size: 0.75rem !important;
}
#launchpadSubAccount .form-group {
  margin-bottom: 0.75rem;
}
#launchpadSubAccount .popup-notice {
  margin-top: 0.2rem;
  font-size: 0.8rem;
  font-weight: bold;
  color: #ffbc00;
}
#launchpadSubAccount .tos_remind_later {
  color: blue;
  cursor: pointer;
}
#launchpadSubAccount input.form-control {
  /* padding: 0.5rem 1.5rem !important;
  line-height: 1; */
}
.launchpad-filled-button {
  margin-left: 0.75rem;
  display: inline-flex;
  justify-content: center;
  padding: 0.65rem 1.5rem;
  border-color: transparent;
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  font-size: 0.75rem;
  line-height: 1.25rem;
  font-weight: 500;
  font-family: 'Roboto';
  border-radius: 3px;
  cursor: pointer;
  white-space: nowrap;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-left: auto;
  margin-right: auto;
}
.launchpad-filled-button:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.launchpad-blue-btn {
  color: #fff;
  background-color: #4263eb;
}
.launchpad-blue-btn:hover {
  background-color: #4263eb;
}
.launchpad-do-later {
  color: #4263eb;
  text-decoration: underline;
  cursor: pointer;
}
</style>
