import { Location, AuthUser, User } from '../models'
import { Module, ActionContext } from 'vuex'
import { RootState, LocationState } from './state_models'
import router from '../routes'
import localStorage from 'store'
import {
  clone,
  difference,
  remove,
  filter,
  every,
  find,
  sortedIndexBy
} from 'lodash'
import { ISaasSettings } from '../models/location'
import restAgent from '../restAgent'
import { trackPendoEvent } from '../util/helper'

let unsubscribe: () => void
let unsubscribeLocation: () => void
export const locations: Module<LocationState, RootState> = {
  namespaced: true,
  state: { locationsLoaded: false, locations: [], activeLocations: [], notificationLocations: [], currentLocation: null },
  getters: {
    getById: (
      state: LocationState,
      getters: any,
      rootState: RootState,
      rootGetters: any
    ) => {
      return (locationId: string) => find(state.locations, { id: locationId })
    },
    searchByName: (
      state: LocationState,
      getters: any,
      rootState: RootState,
      rootGetters: any
    ) => {
      return async (name: string) => {
        let response = await Location.getByCompanyIdQuery(name.toLowerCase())

        return response.locations.map((location) => {
          return { ...location, id: location._id }
        })
      }
    },
    getCurrentLocation: (state: LocationState,
      getters: any,
      rootState: RootState,
      rootGetters: any) => {
      return state.currentLocation;
     },
     getCurrentLocationTimeZone: (state: LocationState,
      getters: any,
      rootState: RootState,
      rootGetters: any) => {
      return state.currentLocation && state.currentLocation.timezone;
    },
    twilioRebillingEnabledForCurrentLocation: (state: LocationState): boolean => {
      try {
        const saasSettings: ISaasSettings = state.currentLocation.settings.saas_settings
        return (
          saasSettings.saas_mode === 'activated' &&
          saasSettings.twilio_rebilling.enabled
        )
      } catch (error) {
        return false
      }
    },
  },
  mutations: {
    set: (state: LocationState, payload: Array<{ [key: string]: any }>) => {
      state.locations = payload.sort((a, b) => {
        if (!a.name) return -1;
        if (!b.name) return 1;
        const nameA = a.name.toLowerCase();
        const nameB = b.name.toLowerCase();
        if (nameA < nameB) return -1;
        if (nameA > nameB) return 1;
        return 0;
      });
      state.locationsLoaded = true
    },
    add: (state: LocationState, payload: { [key: string]: any }) => {
      if (find(state.locations, { id: payload.id })) {
        const location = Array<{ [key: string]: any }>(
          find(state.locations, { id: payload.id })
        );
        location.forEach(l => {
          state.locations.splice(state.locations.indexOf(l), 1, payload);
        })
      } else {
        const sortedIndex = sortedIndexBy(
          state.locations,
          payload,
          value => value.name
        )
        state.locations.splice(sortedIndex, 0, payload)
      }
    },
    clearAll: (state: LocationState) => {
      state.locations = []
      state.activeLocations = []
      state.notificationLocations = []
    },
    addActiveLocation: (state: LocationState, payload: []) => {
      state.activeLocations = payload
    },
    setNotificationLocation: (state: LocationState, payload: []) => {
      state.notificationLocations = payload
    },
    setCurrentLocation(state: LocationState, payload: { [key: string]: any }) {
      // state.currentLocation = state.locations.find(x=> x.id === locId);
      state.currentLocation = payload
      console.log(
        `locVX - current location set to ${
          state.currentLocation && state.currentLocation.id
        }`
      )
    },
  },
  actions: {
    async resetCurrentLocation(
      context: ActionContext<LocationState, RootState>,
      params: { locationId: string; forceRefresh?: boolean }
    ) {
      if (unsubscribeLocation) unsubscribeLocation()
      unsubscribeLocation = (
        await Location.getByIdRealtime(params.locationId)
      ).onSnapshot(async snapshot => {
        context.commit('setCurrentLocation', {
          ...snapshot.data(),
          id: snapshot.id,
        })
      })
    },
    async getAll(context: ActionContext<LocationState, RootState>,) {
        return new Promise(async (resolve, reject) => {
            if(context.state.locationsLoaded){
                return resolve(context.state.locations)
            } else {
                let credentials: AuthUser
                try {
                  credentials = new AuthUser(
                    await context.dispatch('auth/get', null, { root: true })
                  );
                } catch (e) {
                  return;
                }
                const user = await User.getById(credentials.userId)
                if (user.type === User.TYPE_AGENCY) {
                  const snapshot = await (await Location.fetchAllLocationsRealtimeFirestore()).get()
                  const locations = snapshot.docs.map(doc => ({ ...doc.data(), id: doc.id }))
                  context.commit('set', locations)
                  return resolve(locations)
                } else {
                  unsubscribe = User.getStreamById(credentials.userId).onSnapshot(
                    async (snapshot) => {
                      Promise.all(
                        Object.keys(user.locations).map(async (id: string) => {
                          const location = await Location.getById(id)
                          return { ...location.data, id: location.id }
                        })
                      ).then(async (locationsList: Array<{[key: string]: any}>) => {
                        context.commit('set', locationsList);
                        return resolve(locationsList)
                      });
                    }
                  );
                }
            }
        })
    },
    getById: {
      handler: (
        context: ActionContext<LocationState, RootState>,
        locationId: string
      ): Promise<Location> => {
        return new Promise(async (resolve, reject) => {
          try {
            let location = context.getters['getById'](locationId)
            if (location) {
              return resolve(location)
            }

            trackPendoEvent('perf_get_location', {
              source: 'location_vuex_getById'
            })

            const obj = await Location.getById(locationId)
            location = { ...obj.data, id: obj.id }
            context.commit('add', location)
            resolve(location)
          } catch (e) {
            reject()
          }
        })
      }
    },
    getCurrentLocation: {
      handler: (
        context: ActionContext<LocationState, RootState>,
        locationId: string
      ): Promise<Location> => {
        return new Promise(async (resolve, reject) => {
          let location = context.getters['getCurrentLocation']
          if (location) {
            return resolve(location)
          }

          try {
            const obj = await Location.getById(locationId)
            location = { ...obj.data, id: obj.id }
            context.commit('setCurrentLocation', location)
            resolve(location)
          } catch (e) {
            reject()
          }
        })
      },
    },
    syncAll: {
      root: true,
      handler: async (context: ActionContext<LocationState, RootState>) => {
      }
    },
    logout: {
      root: true,
      handler: async (context: ActionContext<LocationState, RootState>) => {
        context.commit('clearAll')
        localStorage.remove('activeLocations')
        localStorage.remove('notificationLocations')
        if (unsubscribe) {
          unsubscribe();
        }
        if (unsubscribeLocation) {
          unsubscribeLocation();
        }
      }
    },
    addToActiveLocation: async (
      context: ActionContext<LocationState, RootState>,
      payload: []
    ) => {
      // if (!payload.length) return [];
      const activeLocations = clone(await context.dispatch('getActiveLocations'))
      const newLocations = difference(payload, activeLocations)
      const credentials = new AuthUser(
        await context.dispatch('auth/get', null, { root: true })
      );
      await Promise.all(
        newLocations.map(async locationId => {
          const location = await context.dispatch('getById', locationId);
          if (location && !location.deleted && location.company_id === credentials.companyId) return locationId;
          else return '';
        })
      )
      const locationToDelete = [];
      if (newLocations.length) {
        activeLocations.push(...newLocations)
        if (activeLocations.length > 100) {
          const notificationLocations = await context.dispatch(
            'getNotificationLocations'
          );

          for(let i = 0; i < activeLocations.length; i++) {
            if (activeLocations.length - locationToDelete.length <= 100) {
              break;
            }
            const locationId = activeLocations[i];
            if (notificationLocations.indexOf(locationId) < 0) {
              locationToDelete.push(locationId);
            }
          }

          remove(activeLocations, l => {
            return locationToDelete.includes(l);
          });
        }
        context.commit('addActiveLocation', activeLocations);
        localStorage.set(
          'activeLocations',
          btoa(JSON.stringify(activeLocations))
        );
      }
      return { activeLocations, isChanged: newLocations.length }
    },
    getActiveLocations: (
      context: ActionContext<LocationState, RootState>,
      payload: []
    ) => {
      return new Promise((resolve, reject) => {
        if (context.state.activeLocations.length) {
          return resolve(context.state.activeLocations);
        }

        const b64String = localStorage.get('activeLocations');
        if (!b64String) return resolve([]);

        const activeLocations = JSON.parse(atob(b64String));
        context.commit('addActiveLocation', activeLocations);
        return resolve(activeLocations);
      });
    },
    setNotificationLocations: (
      context: ActionContext<LocationState, RootState>,
      payload: []
    ) => {
      context.commit('setNotificationLocation', payload);
      localStorage.set('notificationLocations', btoa(JSON.stringify(payload)));;
    },
    getNotificationLocations: (
      context: ActionContext<LocationState, RootState>,
      payload: []
    ) => {
      return new Promise((resolve, reject) => {
        if (context.state.notificationLocations) {
          return resolve(context.state.notificationLocations);
        }

        const b64String = localStorage.get('notificationLocations');
        if (!b64String) return resolve([]);

        const setNotificationLocation = JSON.parse(atob(b64String));
        context.commit('setNotificationLocation', setNotificationLocation);
        return resolve(setNotificationLocation);
      });
    }
  }
}
