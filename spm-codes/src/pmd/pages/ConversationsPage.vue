<template>
    <section class="hl_wrapper" :class="{'hl_force-top': isV2SideBarEnabled}">
      <section class="hl_wrapper--inner hl_conversations" id="conversations">
        <div class="hl_conversations--wrap">
          <div class="hl_conversations--messages-list relative">
            <ul class="nav nav-tabs" id="myTab" role="tablist">
              <li class="nav-item" @click.prevent="changeToUnread">
                <a
                  :class="{ active: currentTab === 'unread' }"
                  class="nav-link"
                  id="unread-tab"
                  role="tab"
                  aria-controls="unread"
                  aria-selected="false"
                  >Unread</a
                >
              </li>
              <li class="nav-item" @click.prevent="currentTab = 'inbox'">
                <a
                  :class="{ active: currentTab === 'inbox' }"
                  class="nav-link"
                  id="inbox-tab"
                  role="tab"
                  aria-controls="inbox"
                  aria-selected="true"
                  >Recents</a
                >
              </li> 
              <li class="nav-item" @click.prevent="currentTab = 'all'">
                <a
                  :class="{ active: currentTab === 'all' }"
                  class="nav-link"
                  id="marketing-tab"
                  role="tab"
                  aria-controls="marketing"
                  aria-selected="false"
                  >All</a
                >
              </li>
            </ul>
            <div class="messages-list-header">
              <h3>Messages</h3>
              <div class="messages-list-header-actions">
                <div class="dropdown show" id="messages-filter-dropdown">
                  <a
                    class="dropdown-toggle"
                    :class="
                      filters.chat !== 'all_chats' ? 'filter-enabled' : null
                    "
                    href="#"
                    role="button"
                    data-toggle="dropdown"
                    aria-haspopup="true"
                    aria-expanded="false"
                  >
                    <i class="icon icon-settings-2"></i>
                    <span class="sr-only">Filter</span>
                  </a>
                  <div class="dropdown-menu">
                    <form>
                      <div class="dropdown-menu-filter">
                        <div class="dropdown-menu-filter-header">
                          <h4>Filters</h4>
                          <a
                            href="#"
                            @click.stop="clearFilters"
                            v-if="
                              filters.chat !== 'all_chats' &&
                              !assignedDataOnlyUser
                            "
                            ><i class="icon icon-close"></i> Clear filters</a
                          >
                        </div>
                        <div class="messages-filter-item">
                          <h5>Chats</h5>
                          <div class="the-option">
                            <input
                              type="radio"
                              :disabled="assignedDataOnlyUser"
                              name="chat"
                              id="chat2"
                              value="all_chats"
                              :checked="filters.chat === 'all_chats'"
                              v-model="filters.chat"
                              @change="submitFilters"
                            />
                            <label
                              for="chat2"
                              :style="
                                assignedDataOnlyUser
                                  ? { cursor: 'not-allowed', opacity: '0.54' }
                                  : null
                              "
                              >All chats</label
                            >
                          </div>
                          <div class="the-option">
                            <input
                              type="radio"
                              name="chat"
                              id="chat1"
                              value="my_chats"
                              :checked="filters.chat === 'my_chats'"
                              v-model="filters.chat"
                              @change="submitFilters"
                            />
                            <label for="chat1">My chats</label>
                          </div>
                        </div>

                        <!-- <div class="messages-filter-item">
													<h5>Date</h5>
													<div class="the-option">
														<input type="radio" name="date" id="date1">
														<label for="date1">Today</label>
													</div>
													<div class="the-option">
														<input type="radio" name="date" id="date2">
														<label for="date2">This week</label>
													</div>
													<div class="the-option">
														<input type="radio" name="date" id="date3" checked>
														<label for="date3">This month</label>
													</div>
													<div class="the-option">
														<input type="radio" name="date" id="date4">
														<label for="date4">All</label>
													</div>
												</div> -->
                        <!-- <div class="messages-filter-item">
													<h5>Type</h5>
													<div class="the-option">
														<input type="radio" name="type" id="type1">
														<label for="type1">My chats</label>
													</div>
													<div class="the-option">
														<input type="radio" name="type" id="type2" checked>
														<label for="type2">All chats</label>
													</div>
												</div> -->
                      </div>
                    </form>
                  </div>
                </div>
                <a
                  href="javascript:void(0);"
                  data-toggle="modal"
                  data-target="#new-chat-modal"
                  @click.prevent="showNewMessageModal = true"
                >
                  <i class="icon icon-edit"></i>
                </a>
              </div>
            </div>
            <div class="messages-list-search">
              <UITextInputGroup
                type="text"
                icon="icon-loupe"
                placeholder="Search"
                v-model="searchTerm"
              />
              <div style="position: absolute; top: 19%; right: 32px">
                <moon-loader :loading="searching" color="#1ca7ff" size="15px" />
              </div>
            </div>
            <ul class="messages-list list-unstyled" data-tour-step="1">
              <ConversationTile
                v-for="conversation in conversations"
                :key="conversation.id"
                :conversation="conversation"
              />
              <li class="no-conversations" v-if="conversations.length === 0">
                No conversations to show
              </li>

              <li></li>
              <li
                v-if="conversations.length"
                style="padding: 8px; text-align: center"
              >
                <a
                  href="javascript:void(0);"
                  @click.prevent="loadMore"
                  v-if="!currentTabMeta.done && !fetching && !searching"
                  >Load more</a
                >
                <div
                  v-if="fetching"
                  style="text-align: center; margin-bottom: 30px"
                >
                  <moon-loader
                    :loading="fetching"
                    color="#1ca7ff"
                    size="15px"
                  />
                </div>
              </li>
            </ul>
          </div>

          <ConversationBody
            v-if="!hideSidebar && conversationId"
            @addAppointment="modalData = { visible: true, contactId: $event }"
            @editAppointment="
              modalData = { visible: true, appointmentId: $event }
            "
            :conversation="conversation"
            :locationId="currentLocationId"
          />
        </div>
      </section>
      <NewMessageModal
      :showModal.sync="showNewMessageModal"
      @hidden="showNewMessageModal = false"
    />
    <tour :steps="steps" :options="options" ref="tour" />
    </section>
</template>

<script lang="ts">
import Vue from 'vue'
import Postmate from 'postmate'
import { mapState, mapGetters } from 'vuex'
import {
  Contact,
  MessageType,
  Conversation,
  User,
  AuthUser,
} from '../../models'
import { UserState, ConversationState } from '../../store/state_models'
import Tour from '../components/common/Tour.vue'

const ConversationTile = () =>
  import('../components/ConversationTile.vue').then(m => m.default)
const ConversationBody = () =>
  import('../components/ConversationBody.vue').then(m => m.default)
const EditAppointmentModal = () =>
  import('../components/EditAppointmentModal.vue').then(m => m.default)
const NewMessageModal = () =>
  import('../components/NewMessageModal.vue').then(m => m.default)

let cancelConversationsSubscription: () => void
export default Vue.extend({
  name: 'LocationConversationsPage',
  components: {
    ConversationTile,
    ConversationBody,
    EditAppointmentModal,
    NewMessageModal,
    Tour,
  },
  data() {
    return {
      filters: {
        chat: 'all_chats' as string,
      },
      hideSidebar: true,
      currentLocationId: '',
      // conversation: undefined as undefined | Conversation,
      conversationId: '' as string | undefined,
      modalData: {},
      showNewMessageModal: false,
      threshold: 15 * 81,
      limit: 20,
      searchDebouncer: undefined as NodeJS.Timer | undefined,
      assignedDataOnlyUser: false as boolean,
      handshake: {} as any,
      steps: [
        {
          element: '[data-tour-step="1"]',
          title: '',
          body: 'All the conversations will be listed here',
          showCount: true,
          next: 'next',
          prev: '',
          position: 'right',
          currentStep: 1,
          totalStep: 3,
          skip: true,
          skipLabel: 'Skip the Conversations demo',
        },
        {
          element: '[data-tour-step="3"]',
          title: '',
          body: 'In the text box below, you can type out a message to respond',
          showCount: true,
          next: 'next',
          prev: 'prev',
          position: () => {
            const ele = document.getElementsByClassName(
              'hl_conversations--messages-list'
            )[0]
            const messageBox = document.getElementsByClassName('message-box')[0]
            const card = document.getElementById('tour-card')
            const arrow = document.getElementById('tour-card-arrow')
            if (card && ele && messageBox) {
              card.style.bottom = Math.abs(messageBox.offsetHeight + 20) + 'px'
              card.style.left =
                ele.offsetWidth - Math.abs(card.offsetWidth / 2) + 50 + 'px'
              card.classList.add('show-tour-card')
            }
            if (arrow && card) {
              arrow?.classList.add('tour-card-arrow-bottom')
              arrow.style.left = card.offsetWidth / 2 - 12 + 'px'
            }
          },
          currentStep: 2,
          totalStep: 3,
          skip: true,
          skipLabel: 'Skip the Conversations demo',
        },
        {
          element: '[data-tour-step="4"]',
          title: '',
          body: 'You can click on send to send out your text message',
          showCount: true,
          next: 'Done',
          prev: 'prev',
          position: () => {
            const card = document.getElementById('tour-card')
            const arrow = document.getElementById('tour-card-arrow')
            if (card) {
              card.style.bottom = '81px'
              card.style.right = '30px'
              card.classList.add('show-tour-card')
            }
            if (arrow) {
              arrow.classList.add('tour-card-arrow-bottom')
              arrow.style.right = '20px'
            }
          },
          currentStep: 3,
          totalStep: 3,
          skip: true,
          skipLabel: 'Skip the Conversations demo',
        },
      ],
      options: {},
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.conversationId =
      this.$router.currentRoute.params.conversation_id !== 'none'
        ? this.$router.currentRoute.params.conversation_id
        : undefined
    this.hideSidebar = false

    const user: User = new User(await this.$store.dispatch('user/get'))
    if (user.isAssignedTo) {
      this.filters.chat = 'my_chats'
      this.assignedDataOnlyUser = true
    }

    const assignedTo = user.isAssignedTo ? user.id : ''
    await this.$store.dispatch('conversation/syncAll', { locationId: this.$route.params.location_id, assignedTo })

    this.pickDefaultConversation()
  },
  watch: {
    '$route.params.location_id': async function (id) {
      this.currentLocationId = id
      
      const user: User = new User(await this.$store.dispatch('user/get'))
      const assignedTo = user.isAssignedTo ? user.id : ''
      await this.$store.dispatch('conversation/syncAll', { locationId: this.$route.params.location_id, assignedTo })

      this.pickDefaultConversation()
    },
    '$route.params.conversation_id': function (id) {
      if (id !== 'none') this.conversationId = id
    },
    conversations() {
      if (this.searchTerm) return
      this.pickDefaultConversation()
    },
    conversationId(val) {
      this.hideSidebar = false
      if (this.getSideBarVersion == 'v2') {
        this.$router.push({
          name: 'conversations-id-v2',
          params: { conversation_id: val || 'none' },
        })
      } else {
        this.$router.push({
          name: 'conversations',
          params: { conversation_id: val || 'none' },
        })
      }
      this.setConversation()
    },
  },
  computed: {
    conversation() {
      return this.conversationVuex
        ? new Conversation(lodash.cloneDeep(this.conversationVuex))
        : undefined
    },
    conversationVuex() {
      return this.conversations.find(x => x.id === this.conversationId)
    },
    searchTerm: {
      get() {
        return this.$store.state.conversation.searchTerm
      },
      set(term: string) {
        this.$store.dispatch('conversation/setSearchTerm', term)
        this.conversationId = undefined
      },
    },
    currentTab: {
      get() {
        return this.$store.state.conversation.currentTab
      },
      set(tab: string) {
        this.$store.dispatch('conversation/setCurrentTab', tab)
        this.conversationId = undefined
      },
    },
    onboarding: {
      get() {
        return this.$store.state.conversation.onboarding
      },
      set(onboarding: boolean) {
        this.$store.dispatch('conversation/setOnboarding', onboarding)
      },
    },
    popupStep: {
      get() {
        return this.$store.state.conversation.popupStep
      },
      set(popupStep: number) {
        this.$store.dispatch('conversation/setPopupStep', popupStep)
      },
    },
    onboarding: {
      get() {
        return this.$store.state.conversation.onboarding
      },
      set(onboarding: boolean) {
        this.$store.dispatch('conversation/setOnboarding', onboarding)
      },
    },
    popupStep: {
      get() {
        return this.$store.state.conversation.popupStep
      },
      set(popupStep: number) {
        this.$store.dispatch('conversation/setPopupStep', popupStep)
      },
    },
    ...mapGetters('conversation', {
      currentTabMeta: 'meta',
      conversations: 'conversations',
    }),
    ...mapState('conversation', {
      fetching: (s: ConversationState) => {
        return s.fetching
      },
      searching: (s: ConversationState) => {
        return s.searching
      },
    }),
    isV2SideBarEnabled() {
      return this.getSideBarVersion == 'v2'
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  methods: {
    clearFilters() {
      this.filters = {
        chat: 'all_chats',
      }
      this.submitFilters()
    },
    async submitFilters() {
      const filterObject = {}
      if (this.filters.chat === 'my_chats') {
        const auth: AuthUser = new AuthUser(
          await this.$store.dispatch('auth/get')
        )
        filterObject.user = auth.userId
      }
      await this.$store.dispatch('conversation/submitFilters', filterObject)
    },
    scrollToTop() {
      var container = this.$el.querySelector('.messages-list')
      if (container) {
        container.scrollTop = 0
      }
    },
    loadMore() {
      this.$store.dispatch('conversation/loadMore')
    },
    pickDefaultConversation() {
      const conversation = this.conversations.find(
        x => x.id === this.conversationId
      )
      if (
        (!conversation || !this.conversationId) &&
        this.conversations.length
      ) {
        const firstConversation = this.conversations[0]
        if (firstConversation.location_id === this.currentLocationId) {
          this.conversationId = firstConversation.id
          this.$nextTick(() => {
            this.scrollToTop()
          })
        }
        this.showStepPopup()
      }
    },
    async setConversation() {
      // if (this.conversationId) {
      //   // const conversation = this.conversations.find(x => x.id === this.conversationId);
      //   // if (conversation) {
      //   //   this.conversation = conversation;
      //   // } else {
      //   // }
      //   this.conversation = await Conversation.getById(this.conversationId);
      // } else {
      //   this.conversation = undefined;
      // }
    },
    changeToUnread() {
      this.$store.dispatch('conversation/refreshUnread')
      this.currentTab = 'unread'
    },
    hideOnboardingUnwantedComponent() {
      this.onboarding =
        this.$router.currentRoute.query.onboarding &&
        this.$router.currentRoute.query.onboarding == 'true'
          ? true
          : false
      if (this.onboarding) {
        this.currentTab = 'all'
        const comp1 = document.getElementsByClassName(
          'messages-list-header-actions'
        )[0]
        if (comp1) comp1.remove()
        const comp2 = document.getElementById('myTab')
        if (comp2) comp2.style.visibility = 'hidden'
        const comp3 = document.getElementsByClassName('hl_wrapper')[0]
        if (comp3) {
          comp3.style.paddingTop = '0px'
          comp3.style.paddingLeft = '0px'
        }
        const comp4 = document.getElementsByClassName(
          'hl_conversations--wrap'
        )[0]
        if (comp4) comp4.style.height = 'calc(100vh)'
        this.$refs.tour.showOverlay()
        try {
          this.$store?.state?.iframe?.handshake?.then((parent: any) => {
            parent.emit('overlay-show')
          })
        } catch (err) {}
        setTimeout(() => {
          const tabAll = document.getElementById('marketing-tab')
          if (tabAll) tabAll.click()
        }, 1000)
      }
    },
    showStepPopup() {
      if (this.popupStep == 1 && this.onboarding && this.currentTab == 'all') {
        const inter = setInterval(() => {
          const popup = document.getElementById(this.tooltipId)
          const top = document.getElementsByClassName('messages-list')[0]
            .offsetTop
          if (popup && top > 0) {
            popup.style.top =
              document.getElementsByClassName('messages-list')[0].offsetTop +
              'px'
            popup.classList.add('show-step-popup')
            clearInterval(inter)
          }
        }, 500)
      }
    },
    tourEnd() {
      this.onboarding = false
      this.$store?.state?.iframe?.handshake?.then((parent: any) => {
        parent.emit('onboarding-redirect-end')
      })
    },
    async onboardingRedirect() {
      this.$store?.state?.iframe?.handshake?.then((parent: any) => {
        parent.emit('onboarding-dashboard')
      })
    },
    async sendDemoSms() {
      try {
        if (!this.conversations.length) {
          const contact = await Contact.getByLocationId(this.currentLocationId, 1)
          if (contact.length) {
            const response = await this.$http.post('/message/sendsms', {
              contactId: contact[0].id,
              message: `Hi ${contact[0].firstName}, we've sent this message to the phone number you used to signup. Can you check your phone and respond to this message?`,
            })
          }
        }
      } catch (err) {
        console.log('$$$$$$$', err)
      }
    },
  },
  mounted() {
    this.hideOnboardingUnwantedComponent()
    this.$root.$on('redirect', () => {
      this.onboardingRedirect()
    })
    this.$root.$on('tour-end', () => {
      this.tourEnd()
    })
    this.$root.$on('spm-ts-end', () => {
      this.$store?.state?.iframe?.handshake?.then((parent: any) => {
        parent.emit('onboarding-tooltip-end')
      })
    })
    this.$root.$on('onboarding-tour-start', async () => {
      await this.sendDemoSms()
      setTimeout(() => {
        this.$refs.tour.start()
        this.$store?.state?.iframe?.handshake?.then((parent: any) => {
          parent.emit('tour-started')
        })
      },3000)
    })
  },
})
</script>
<style>
.dropdown-menu-filter {
  user-select: none;
}
.filter-enabled::before {
  content: '';
  display: block;
  width: 8px;
  height: 9px;
  border-radius: 50%;
  background: #ffbc00;
  position: absolute;
  top: 2px;
  right: -4px;
  z-index: 3;
}
.no-conversations {
  font-size: 0.8rem;
  text-align: center;
  opacity: 0.87;
}
</style>
