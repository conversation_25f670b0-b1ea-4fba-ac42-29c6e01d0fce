<template>
  <div
    class="modal fade hl_sms-template--modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Add to {{ automationString }}</h2>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div class="form-group">
              <UITextLabel>{{ automationString }}</UITextLabel>
              <vSelect
                :options="availableAutomations"
                class="mt-1"
                label="name"
                v-model="selectedCampaign"
                :clearable="false"
                v-validate="'required'"
                name="campaign"
                :loading="loading.campaigns"
              >
                <template #spinner="{ loading }">
									<div v-if="loading" style="border-left-color: rgba(88,151,251,0.71)" class="vs__spinner"></div>
								</template>
              </vSelect>
              <span v-show="errors.has('campaign')" class="error"
                >{{ automationString }} is required.</span
              >
            </div>
            <div class="form-group">
              <UITextLabel>Event Start Date</UITextLabel>

              <div class="mt-1">
                <vue-ctk-date-time-picker
                  :locale="getCountryInfo('locale')"
                  v-model="eventDate"
                  :noClearButton="true"
                  :minute-interval="5"
                  color="#188bf6"
                  enable-button-validate
                  style="width: 350px;margin: 0px;display: inline-block;"
                  class="border shadow-sm order-gray-300 rounded"
                />

                <i
                  class="icon icon-trash --gray pointer"
                  v-if="eventDate"
                  @click.stop="eventDate = null"
                  style="margin-left: 10px;display: inline-block;"
                ></i>
              </div>
            </div>
            <div v-if="error" class="help --red" style="text-align: center;">
              {{ error }}
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner nav">
            <UIButton use="outline" data-dismiss="modal">
              Cancel
            </UIButton>
            <div style="display: inline-block; position: relative;">
              <UIButton
                :loading="saving"
                @click.prevent="addToAutomation"
              >
                Add
              </UIButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import * as moment from 'moment-timezone'
import vSelect from 'vue-select'
import { mapState } from "vuex";
import { UserState } from "@/store/state_models";
import {
  Campaign,
  BulkRequest,
  Location,
  Contact,
  User,
  getCountryInfo,
  Workflow
} from '@/models'
import config from '@/config';
import { orderBy } from 'lodash';

declare var $: any

export default Vue.extend({
  props: ['values'],
  components: {
    vSelect
  },
  data() {
    return {
      selectedCampaign: undefined as Campaign | undefined,
      saving: false,
      contact: {} as Contact,
      activeCampaigns: [] as Campaign[],
      availableAutomations: [] as { [key: string]: any }[],
      eventDate: undefined as undefined | string,
      timezone: 'UTC',
      error: '',
      getCountryInfo,
      loading: {
        campaigns: false
      }
    }
  },
  computed: {
    ...mapState('user', {
			user: (s: UserState) => {
				return s.user ? new User(s.user) : undefined;
			},
		}),
    automationString() {
      return this.user.automationString(false) || 'Campaign / Workflow'
    }
  },
  methods: {
    async fetchData() {
      if (this.user?.automationPermission.campaigns) await this.fetchCampaigns()
      if (this.user?.automationPermission.workflows) await this.fetchWorkflows()
     
      const location = new Location(
        await this.$store.dispatch('locations/getById', this.contact.locationId)
      )
      this.timezone = await location.getTimeZone()
    },
    async fetchCampaigns() {
      await this.$store.dispatch('campaigns/syncAll', this.$route.params.location_id)
      const campaigns = this.$store.state.campaigns.campaigns
      const availableCampaigns = lodash.filter(campaigns, campaign => {
        return !lodash.find(this.activeCampaigns, { id: campaign.id })
      })
      this.availableAutomations.push.apply(this.availableAutomations, availableCampaigns);
    },
    async fetchWorkflows() {
      await this.$store.dispatch('workflows/clearAll'); // Clear workflows to get updated
      await this.$store.dispatch('workflows/syncAll', this.contact.locationId); // Fetch workflows to get updated
      const activeWorkflows = this.$store.state.workflows.workflows.filter(workflow => workflow.status === 'published').map(workflow => { return { name: `Workflow - ${workflow.name}`, id: `workflow_${workflow.id}` } });

      let orderedWorkflows = activeWorkflows
      try {
        orderedWorkflows = orderBy(activeWorkflows, [workflow => workflow?.name?.toLowerCase()])
      } catch (err) {}

      this.availableAutomations.push.apply(this.availableAutomations, orderedWorkflows);
    },
    async addToAutomation() {
      this.error = ''
      const result = await this.$validator.validateAll()
      if (!result || !this.selectedCampaign) {
        return false
      }
      this.saving = true
      const params: { [key: string]: any } = {}
      if (this.eventDate) {
        params['event_time'] = moment(this.eventDate, ['YYYY-MM-DD hh:mm a'])
          .tz(this.timezone, true)
          .format()
        params['start_time'] = moment(this.eventDate, ['YYYY-MM-DD hh:mm a'])
          .tz(this.timezone, true)
          .format()
      }
      params['internalSource'] = {
				type: `${this.$route.name}_page`,
        id: this.user.id,
				userName: this.user.name // Saved to data so it doesnt spend time loading it
			}
      try {
        if (this.selectedCampaign.id.includes('workflow_')) {
          const workflowId = this.selectedCampaign.id.replace('workflow_', '')

          const startWorkflowBody = {
            actionFrom: {
              userId: this.user?.id,
              source: 'add_to_campaign_modal',
              channel: 'web_app'
            },
            contactId: this.contact.id
          } as { actionFrom: { userId: string, source: string, channel: string }, contactId: string, eventStartTime: string }
          if (this.eventDate) {
            startWorkflowBody.eventStartTime = moment(this.eventDate, ['YYYY-MM-DD hh:mm a'])
              .tz(this.timezone, true)
              .format()
          }

          let response = await this.$http.post(
            `${config.workflowServiceURL}/${this.contact.locationId}/${workflowId}/start-workflow`,
            startWorkflowBody
          )
          this.$root.$emit('addedToWorkflow')
        } else {
          let response = await this.$http.post(
            `/contact/${this.contact.id}/campaign/${this.selectedCampaign.id}/start`,
            params
          )
        }
        this.saving = false
        this.$emit('hidden')
      } catch (err) {
        this.saving = false
        console.error('Error adding to campaign:', err)
        this.error = lodash.get(err, 'response.data.msg', 'Bad request')
      }
    }
  },
  watch: {
    '$route.params.location_id': async function(id) {
      await Promise.all([
        this.$store.dispatch('campaigns/syncAll', id),
        this.$store.dispatch('workflows/syncAll', id)
      ])
    },
    values(values: { [key: string]: any }) {
      const data: () => object = <() => object>this.$options.data
      if (data) Object.assign(this.$data, data.apply(this))
      if (values.visible) {
        this.contact = values.contact
        this.activeCampaigns = values.activeCampaigns
        this.fetchData()
        $(this.$refs.modal).modal('show')
      }
      else $(this.$refs.modal).modal('hide')
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hidden')
    })

    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  }
})
</script>
