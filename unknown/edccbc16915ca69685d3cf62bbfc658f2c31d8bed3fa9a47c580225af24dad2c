<template>
	<Filters
		:conditions="conditions"
		:filterMaster="filterMaster"
		v-on:update:conditions="$emit('update:conditions', $event)"
	/>
</template>

<script lang="ts">
import Vue from 'vue'
import { Campaign, Formbuilder } from '@/models';
import { Condition } from '@/models/trigger';

const Filters = () => import( './Filters.vue');

export default Vue.extend({
	props: ['conditions'],
	components: { Filters },
	data() {
		return {
			filterMaster: [
				{
					placeHolder: 'Select form',
					title: 'Form is',
					value: 'form.id',
					valueType: 'text',
					type: 'select',
					options: [] as { [key: string]: any }[]
				},
			],
		}
	},
	async created() {
		const currentLocationId = this.$router.currentRoute.params.location_id;
		const filter = lodash.find(this.filterMaster, { value: 'form.id' });
		if (filter) {
			const forms = await Formbuilder.getByLocationId(currentLocationId);
			filter.options = forms.map(form => {
				return {
					title: form.name,
					value: form.id
				}
			});
		}
	}
})
</script>

