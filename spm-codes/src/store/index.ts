import Vue from 'vue'
import Vuex, { ActionContext, ModuleTree } from 'vuex'
import { auth } from './auth_user'
import { user } from './user'
import { company } from './company'
import localStorage from 'store'
import { RootState } from './state_models'
import { AuthUser } from '../models'
import { reviewAggregate } from './review_aggregate'
import { reviewRequestAggregate } from './review_request_aggregate'
import { imagePreview } from './image_preview'
import { contactsPage } from './contact'
import { campaigns } from './campaigns'
import { workflows } from './workflows'
import { pipelines } from './pipelines'
import { users } from './users'
import { agencyUsers } from './agency_users'
import { locations } from './location'
import { teams } from './teams'
import { calendarProviders } from './calendar_providers'
import { calendars } from './calendars'
import { userCalendars } from './user_calendars'
import { linkedCalendars } from './linked_calendars'
import { numbers } from './default_number'
import { agencyTwilio } from './agency_twilio';
import phoneCall from './phone_call'
import { funnel } from './funnel'
import router from '../../src/routes';
import { contacts } from './contacts'
import { oauth2 } from './oauth2'
import { conversation } from './conversation'
import affiliate from './affiliate'
import { opportunities } from './opportunities'
import { filters } from './filters'
import { defaultEmailService } from './default_email_service'
import { trigger_folder } from './trigger_folder'
import { campaign_folder } from './campaign_folder'
import { smtpServices } from './smtp_services'
import { mailgunServices } from './mailgun_services'
import {membership} from './membership'
import {locationCustomFields} from './custom_fields'
import { manualCallStatus } from './manual_call_status'
import { iframe } from './iframe'
import { products  } from './products'
import { stripeConnect } from './stripe_connect'
import { integrations } from './integrations'
import { LevelUpDayFlag } from './levelup_day'
import { sidebarv2 } from './sidebarv2'

Vue.use(Vuex)

const modules: ModuleTree<RootState> = {
  auth,
  user,
  company,
  reviewAggregate,
  reviewRequestAggregate,
  imagePreview,
  contactsPage,
  locations,
  teams,
  calendarProviders,
  calendars,
  userCalendars,
  linkedCalendars,
  numbers,
  users,
  agencyUsers,
  campaigns,
  workflows,
  pipelines,
  agencyTwilio,
  phoneCall,
  funnel,
  contacts,
  oauth2,
  conversation,
  affiliate,
  opportunities,
  filters,
  defaultEmailService,
  trigger_folder,
  campaign_folder,
  smtpServices,
  mailgunServices,
  membership,
  locationCustomFields,
  manualCallStatus,
  iframe,
  products,
  stripeConnect,
  integrations,
  LevelUpDayFlag,
  sidebarv2
};

const createStore = () => new Vuex.Store<RootState>({
  state: {
    initialized: false,
    collapseSideBar: false,
    manualCollapseSidebar: false,
    twilioClientReady: false,
    menuTabs: {},
  },
  strict: process.env.NODE_ENV === 'development',
  modules,
  getters: {
    getManualCollapseSidebar(state: RootState) {
      if(state.manualCollapseSidebar) return true;
      else return state.collapseSideBar;
    },
    getMenuTabs(state: RootState) {
      return state.menuTabs
    },
  },
  mutations: {
    setTwilioClientReady: (state: RootState, ready: boolean) => {
      state.twilioClientReady = ready
    },
    setDeviceId: (state: RootState, deviceId: string) => {
      state.deviceId = deviceId
    },
    setInitialized: (state: RootState, initialized: boolean) => {
      state.initialized = initialized
    },
    setSideBarState: (state: RootState, collapseSideBar: boolean) => {
      state.collapseSideBar = collapseSideBar
    },
    setManualCollapseSidebar: (state: RootState, manualCollapseSidebar: boolean) => {
      state.manualCollapseSidebar = manualCollapseSidebar
    },
    setMenuTabs: (state: RootState, menuTabs: { [key: string]: any }) => {
      state.menuTabs = menuTabs
    },
  },
  actions: {
    getDeviceId: (context: ActionContext<RootState, RootState>) => {
      return new Promise((resolve, reject) => {
        if (context.state.deviceId) {
          return resolve(context.state.deviceId)
        }

        let deviceId = localStorage.get('deviceId')
        if (!deviceId) {
          const crypto = window.crypto || window.msCrypto
          deviceId = (String(1e7) + -1e3 + -4e3 + -8e3 + -1e11).replace(
            /[018]/g,
            (c: string) => {
              const randomChars = crypto.getRandomValues(
                new Uint8Array(1)
              ) as ArrayLike<number>
              return (
                Number(c) ^
                (randomChars[0] & (15 >> (Number(c) / 4)))
              ).toString(16)
            }
          )
          localStorage.set('deviceId', deviceId)
        }
        context.commit('setDeviceId', deviceId)
        resolve(deviceId)
      })
    },
    init: async (context: ActionContext<RootState, RootState>) => {
      try {
        const credentials: AuthUser = new AuthUser(
          await context.dispatch('auth/get')
        )
        context.dispatch('smtpServices/companySyncAll', credentials.companyId)
        context.dispatch('mailgunServices/companySyncAll', credentials.companyId)
      } catch (e) {
        return
      }

      if (!context.state.initialized) {
        context.commit('setInitialized', true)
        context.dispatch('syncAll')
      }
    },
    logout: (context: ActionContext<RootState, RootState>) => {
      context.commit('setInitialized', false)
      localStorage.set('recentVisitedLocations', [])
    }
  }
})


const store = createStore()
export default store
export const useStore = () => store
