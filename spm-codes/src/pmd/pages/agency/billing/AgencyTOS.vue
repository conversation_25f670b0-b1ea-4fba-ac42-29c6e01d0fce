<template>
  <div>
    <div class="billing-alert">
      <div class="">
        HighLevel terms of service is accepted by
        <b>
          {{ tosAcceptedBy }}
        </b>
        on
        <b>{{ tosAcceptedDate }}</b>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import { Company, User, UserState, CompanyState } from '@/models'
import moment from 'moment-timezone'

export default Vue.extend({
  data() {
    return {
      tosAcceptedBy: '',
      tosAcceptedDate: '',
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
  },
  methods: {
    async setTosData() {
      try {
        const tosAcceptedBy: string | any = this.company
          ?.termsOfServiceAcceptedBy
        const user = await User.getById(tosAcceptedBy)
        if (user) {
          this.tosAcceptedBy = user.fullName
        }
        const tosAcceptedDate = this.company?.termsOfServiceAcceptedDate
        this.tosAcceptedDate = moment(tosAcceptedDate).tz(this.company?.timezone).format('MMM. D, YYYY [at] h:mm A')
      } catch (error) {
        console.log(error)
      }
    },
  },
  mounted() {
    this.setTosData()
  },
})
</script>
<style>
.shimmer-card {
  padding: 20px 20px 0px;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
}
.billing-alert {
  background-color: #ffffff;
  padding: 20px;
  margin-bottom: 30px;
  line-height: 20px;
}
</style>
