<template>
  <Filters
    :conditions="conditions"
    :filterMaster="filterMaster"
    v-on:update:conditions="$emit('update:conditions', $event)"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import { Campaign, Tag } from '@/models';
import { Condition } from '@/models/trigger';
const Filters = () => import( './Filters.vue');

export default Vue.extend({
    props: ['conditions'],
    components: { Filters },
    data() {
        return {
            filterMaster: [
                {
                    placeHolder: 'Select campaign',
          title: 'In campaign',
          value: 'campaign.id',
          valueType: 'text',
          type: 'select',
          options: [] as { [key: string]: any }[]
                },
                {
                  id: "has-tag",
                  placeHolder: "Has Tag",
                  title: "Has Tag",
                  value: "contact.tags",
                  valueType: "text",
                  operator: "index-of-true",
                  type: "select",
                  options: [] as { [key: string]: any }[]
                },
                {
                  id: 'doesnot-have-tag',
                  placeHolder: "Doesn't Have Tag",
                  title: "Doesn't Have Tag",
                  value: 'contact.tags',
                  valueType: 'text',
                  operator: 'index-of-false',
                  type: 'select',
                  options: [] as { [key: string]: any }[]
                }
            ],
        }
    },
    async created() {
        let currentLocationId = this.$router.currentRoute.params.location_id;
        const campaignIdOption = lodash.find(this.filterMaster, { value: 'campaign.id' });
    if (campaignIdOption) {
      const campaigns = await Campaign.getWithLocationId(currentLocationId);
      campaignIdOption.options = campaigns.map(campaign => {
        return {
          title: campaign.name,
          value: campaign.id
        }
      });
      const hasTagIdOption = lodash.find(this.filterMaster, {
        id: "has-tag"
      });
      const doesnotHaveTagIdOption = lodash.find(this.filterMaster, {
        id: 'doesnot-have-tag'
      })
      let tagOptions = (await Tag.getByLocationId(currentLocationId)).map((tag)=>{
        return {
          title: tag.name,
          value: tag.name
        }
      });
      hasTagIdOption.options = tagOptions;
      doesnotHaveTagIdOption.options = tagOptions;
    }
    },
})
</script>

