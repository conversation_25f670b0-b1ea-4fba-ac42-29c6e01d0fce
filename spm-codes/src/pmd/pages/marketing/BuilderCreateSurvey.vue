<template>
  <section class="hl_wrapper d-flex survey-wrap-container">
    <section class="hl_wrapper--inner form-builder" id="form-builder">
      <moon-loader class="mt-5" color="#188bf6" size="20px" v-if="loading" />
      <section
        v-if="!loading"
        class="hl_form-builder--main"
        @click.self="setSlideActiveFalse"
      >
        <form
          name="builder-form"
          id="_builder-form"
          @click.self="setSlideActiveFalse"
        >
          <div
            class="form-child-wrapper"
            ref="formSurveyReferance"
            v-bind:style="{
              maxWidth: parseFloat(formStyle.width) + 30 + 'px',
            }"
          >
            <Container @drop="onSortableSlide">
              <Draggable
                v-for="(itemSlide, indexSlide) in surveySlides"
                :key="indexSlide"
                draggable=".survey-form-container"
                handle=".survey-form-container"
              >
                <div
                  class="survey-form-container"
                  v-bind:class="itemSlide.active ? 'active' : ''"
                  @click="setSlideActiveOnClick($event, indexSlide)"
                >
                  <a
                    class="close-icon-survey"
                    @click="removeSlide(indexSlide)"
                    v-if="itemSlide.active && surveySlides.length > 1"
                  >
                    <i class="icon-close"></i>
                  </a>

                  <div
                    class="builder-form-name text-center  flex justify-center justify-items-center"
                    @click="setSlideActiveOnClick($event, indexSlide)"
                    v-if="itemSlide.slideName != ''"
                  >
                    <div class="text-lg font-semibold">
                    {{ itemSlide.slideName }}
                    </div>
                      <svg
                      v-if="hasLogicConflict(indexSlide)"
                      v-b-tooltip.hover
                      title="This slide contains multiple questions that have a logic set to them. This can cause some unintended behavior"
                      width="0.8em"
                      height="0.8em"
                      viewBox="0 0 16 16"
                      class="bi bi-exclamation-circle"
                      fill="currentColor"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"
                      />
                      <path
                        d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z"
                      />
                    </svg>
                  </div>
                  <div
                    v-bind:class="
                      dragOptions.isDragEntered
                        ? 'drag-enter form-builder--wrap'
                        : 'form-builder--wrap'
                    "
                    id="the-form-builder"
                    v-bind:style="{
                      backgroundColor: '#' + formStyle.bgColor,
                      //color: '#' + formStyle.color,
                      border: formStyle.border + 'px dashed #cde0ec',
                      borderRadius: formStyle.radius + 'px',
                      maxWidth: formStyle.width + 'px',
                    }"
                  >
                    <drop
                      v-if="isSideBarDrag"
                      v-bind:class="
                        !formStyle.inlineForm
                          ? 'drop form-builder-drop builder-padding-remove'
                          : 'drop form-builder-drop builder-inline'
                      "
                      @drop="(d, e) => handleDrop(d, e, indexSlide)"
                    >
                      <Container
                        group-name="col"
                        @drop="onSortableDrop($event, indexSlide)"
                        :get-child-payload="getChildPayload(indexSlide)"
                        :drag-begin-delay="1"
                        behaviour="move"
                        :remove-on-drop-out="false"
                        v-bind:class="
                          formStyle.inlineForm
                            ? 'smooth-dnd-container vertical row'
                            : 'smooth-dnd-container vertical'
                        "
                      >
                        <Draggable
                          v-for="(item, index) in itemSlide.slideData"
                          :key="item.tag + '_' + index"
                          v-bind:class="
                            formStyle.inlineForm &&
                            !fieldFullWidth(item.type) &&
                            !checkFieldOwnLine(item.tag)
                              ? 'menu-field-wrap col-md-6 col-sm-6 col-lg-6'
                              : 'menu-field-wrap col-12'
                          "
                          draggable=".survey-form-container"
                          handle=".survey-form-container"
                        >
                          <div
                            :id="'field-' + index"
                            v-bind:class="item.active ? 'active' : ''"
                          >
                            <!--Standard fields-->
                            <FormbuilderAddStandardFields
                              v-if="item.id == undefined"
                              :propImageFileInStore="isImageFileInStore"
                              :currentLocationId="currentLocationId"
                              :builderId="builderId"
                              :item="item"
                              :index="index"
                              :showDropZones="showDropZones"
                              :formLabelVisible="formStyle.formLabelVisible"
                              :formButtonStyle="buttonStyle"
                              v-on:saveUploadPath="saveUploadPath"
                              :class="index % 2 == 0 ? 'f-even' : 'f-odd'"
                            />
                            <!--Standrard Fields-->

                            <!--Custom Form Start-->
                            <FormbuilderAddCustomFields
                              v-if="item.id != undefined"
                              :customFields="item"
                              :index="index"
                              :showDropZones="showDropZones"
                              :formLabelVisible="formStyle.formLabelVisible"
                              :class="index % 2 == 0 ? 'f-even' : 'f-odd'"
                            />
                            <!--Custom Form End-->
                          </div>
                        </Draggable>
                      </Container>
                      <!--Blank dropzone (will be added at the bottom)-->
                      <div class="blank-dropzone">
                        <add-field-dropzone :index="-1"></add-field-dropzone>
                      </div>
                    </drop>
                    <div
                      v-else
                      v-bind:class="
                        !formStyle.inlineForm
                          ? 'drop form-builder-drop builder-padding-remove'
                          : 'drop form-builder-drop builder-inline'
                      "
                    >
                      <Container
                        class="dnd-container-sort"
                        group-name="col"
                        @drop="onSortableDrop($event, indexSlide)"
                        :get-child-payload="getChildPayload(indexSlide)"
                        :drag-begin-delay="1"
                        behaviour="move"
                        :remove-on-drop-out="false"
                        v-bind:class="
                          formStyle.inlineForm
                            ? 'smooth-dnd-container vertical row'
                            : 'smooth-dnd-container vertical'
                        "
                      >
                        <Draggable
                          v-for="(item, index) in itemSlide.slideData"
                          :key="item.tag + '_' + index"
                          v-bind:class="
                            formStyle.inlineForm &&
                            !fieldFullWidth(item.type) &&
                            !checkFieldOwnLine(item.tag)
                              ? 'menu-field-wrap col-md-6 col-sm-6 col-lg-6'
                              : 'menu-field-wrap col-12'
                          "
                          draggable=".survey-form-container"
                          handle=".survey-form-container"
                        >
                          <div
                            :id="'field-' + index"
                            v-bind:class="item.active ? 'active' : ''"
                          >
                            <!--Standard fields-->
                            <FormbuilderAddStandardFields
                              v-if="item.id == undefined"
                              :propImageFileInStore="isImageFileInStore"
                              :currentLocationId="currentLocationId"
                              :builderId="builderId"
                              :item="item"
                              :index="index"
                              :showDropZones="showDropZones"
                              :formLabelVisible="formStyle.formLabelVisible"
                              :formButtonStyle="buttonStyle"
                              v-on:saveUploadPath="saveUploadPath"
                              :class="index % 2 == 0 ? 'f-even' : 'f-odd'"
                            />
                            <!--Standrard Fields-->

                            <!--Custom Form Start-->
                            <FormbuilderAddCustomFields
                              v-if="item.id != undefined"
                              :customFields="item"
                              :index="index"
                              :showDropZones="showDropZones"
                              :formLabelVisible="formStyle.formLabelVisible"
                              :class="index % 2 == 0 ? 'f-even' : 'f-odd'"
                            />
                            <!--Custom Form End-->
                          </div>
                        </Draggable>
                      </Container>
                      <hr />
                    </div>
                    <!--Branding text-->
                    <FormbuilderBranding
                      :isBrandingActive="formStyle.isBrandingActive"
                      :company="company"
                    />
                    <!--End of Branding -->
                  </div>
                </div>
              </Draggable>
            </Container>
          </div>
        </form>

        <!--Add New Page-->
        <div class="custom-field" v-if="!this.formAction.fieldSettingEnable">
          <br />
          <button
            type="button"
            class="hl_navbar--button btn btn-success btn-block"
            @click="addNewSlide"
          >
            Add New Slide
          </button>
        </div>
      </section>

      <!-- END of .hl_form-builder--main -->
      <FormbuilderSidebar
        ref="fieldSettingsRefs"
        :isSurvey="true"
        :itemDragable="itemDragable"
        :customFields="customFields"
        :fieldSettings="fieldSettings"
        :slideSettings="slideSettings"
        :allSlides="surveySlides"
        :buttonStyle="buttonStyle"
        :formStyle="formStyle"
        :formAction="formAction"
        :logicSkipStore="logicSkipStore"
        :currentSlide="currentSlide"
        v-on:startDraggingItem="startDraggingItem"
        v-on:dragEndItem="dragEndItem"
        v-on:showAddCustomFieldModal="showAddCustomFieldModal"
        v-on:showIntegratePage="showIntegratePage"
        v-on:showIntegratePageSurvey="showIntegratePageSurvey"
        v-on:saveSurvey="saveSurveyChanges"
      />
      <!-- END of .hl_form-builder--sidebar -->
    </section>
    <!-- END of .form-builder -->
    <AddCustomFieldModal
      v-if="drawComponent"
      :values="addCustomFieldModal"
      @hidden="addCustomFieldModal.visible"
      v-on:hide="closeAddCustomFieldModal"
    />

    <AddFooterHtml
      v-if="drawComponentHtmlFooter"
      :values="footerHtmlModalVisible"
      @hidden="footerHtmlModalVisible.visible"
      v-on:hide="closeFooterHtmlModal"
    />

    <AddCustomHtml
      v-if="drawComponentCustomHtml"
      :values="customHtmlModalVisible"
      @hidden="customHtmlModalVisible.visible"
      v-on:hide="closeCustomHtmlModal"
    />

    <AddDescriptionText
      :values="descriptionModalVisible"
      @hidden="descriptionModalVisible.visible"
      @save="fieldSettingInput"
      v-on:hide="closeDesriptionModal"
    />

    <b-modal
      centered
      size="sm"
      id="survey-embed-modal"
      hide-footer
      title="Integrate Survey"
    >
      <IntegrateFormModal :isSurvey="true" />
    </b-modal>
  </section>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import { Drag, Drop } from 'vue-drag-drop'
import { Container, Draggable } from 'vue-smooth-dnd'
import { v4 as uuid } from 'uuid'

/*---Component---*/
import TopBar from '../../components/TopBar.vue'
import SideBar from '../../components/SideBar.vue'
const AddFieldDropzone = () =>
  import('../../components/marketing/AddFieldDropzone.vue').then(m => m.default)
const MoonLoader = () =>
  import('../../components/MoonLoader').then(m => m.default)
const AddCustomFieldModal = () =>
  import('../../components/agency/AddCustomFieldModal.vue').then(m => m.default)
const FormbuilderAddCustomFields = () =>
  import('../../components/marketing/FormbuilderAddCustomFields.vue').then(
    m => m.default
  )
const FormbuilderAddStandardFields = () =>
  import('../../components/marketing/FormbuilderAddStandardFields.vue').then(
    m => m.default
  )
const FormbuilderBranding = () =>
  import('../../components/marketing/FormbuilderBranding.vue')
const FormbuilderSidebar = () =>
  import('../../components/marketing/FormbuilderSidebar.vue')
const AddFooterHtml = () =>
  import('../../components/marketing/AddFooterHtml.vue').then(m => m.default)
const AddCustomHtml = () =>
  import('../../components/marketing/AddCustomHtml.vue').then(m => m.default)
const AddDescriptionText = () =>
  import('../../components/marketing/AddDescriptionText.vue').then(
    m => m.default
  )
const IntegrateFormModal = () =>
  import('../../components/marketing/IntegrateFormModal.vue')

/*---Model---*/
import { CompanyState } from '../../../store/state_models'
import { Company, Formsurvey, CustomField } from '../../../models'

/*--Firebase--*/
import firebase from 'firebase/app'
import {
  updateCachingIndex,
  cacheUpdateEvents,
} from '../../../util/caching.helper'

declare var $: any

let unsubscribeCustomFields: () => void
const fullWidthFields = [
  'img',
  'submit',
  'h1',
  'html',
  'captcha',
  'large_text',
  'source',
]
let logicHandler = {}

export default Vue.extend({
  components: {
    TopBar,
    SideBar,
    Drag,
    Drop,
    Container,
    Draggable,
    'add-field-dropzone': AddFieldDropzone,
    MoonLoader,
    AddCustomFieldModal,
    FormbuilderAddCustomFields,
    FormbuilderAddStandardFields,
    FormbuilderBranding,
    FormbuilderSidebar,
    AddFooterHtml,
    AddCustomHtml,
    AddDescriptionText,
    IntegrateFormModal,
  },
  data() {
    return {
      publishedSurveyData: {},
      loading: true,
      isMultiSlide: true,
      itemDragable: [
        {
          label: 'Full Name',
          tag: 'full_name',
          type: 'text',
          placeholder: 'Full Name',
          required: false,
        },
        {
          label: 'First Name',
          tag: 'first_name',
          type: 'text',
          placeholder: 'First Name',
          required: false,
        },
        {
          label: 'Last Name',
          tag: 'last_name',
          type: 'text',
          placeholder: 'Last Name',
          required: false,
        },
        {
          label: 'Phone',
          tag: 'phone',
          type: 'text',
          placeholder: 'Phone',
          required: true,
        },
        {
          label: 'Email',
          tag: 'email',
          type: 'email',
          placeholder: 'Email',
          required: true,
        },
        {
          label: 'Organization',
          tag: 'organization',
          type: 'text',
          placeholder: 'Organization',
          required: false,
        },
        {
          label: 'Address',
          tag: 'address',
          type: 'text',
          placeholder: 'Address',
          required: false,
        },
        {
          label: 'City',
          tag: 'city',
          type: 'text',
          placeholder: 'City',
          required: false,
        },
        {
          label: 'State',
          tag: 'state',
          type: 'text',
          placeholder: 'State',
          required: false,
        },
        {
          label: 'Country',
          tag: 'country',
          type: 'select',
          placeholder: 'Country',
          required: false,
        },
        {
          label: 'Postal code',
          tag: 'postal_code',
          type: 'text',
          placeholder: 'Postal Code',
          required: false,
        },
        {
          label: 'Text',
          tag: 'header',
          type: 'h1',
          placeholder: 'header',
        },
        {
          label: 'Html',
          tag: 'html',
          type: 'html',
          placeholder: 'The Custom HTML goes here',
          html: '',
        },
        {
          label: 'Image',
          tag: 'image',
          type: 'img',
          placeholder: 'Image',
        },
        {
          label: 'Website',
          tag: 'website',
          type: 'text',
          placeholder: 'Web URL goes here',
        },
        {
          label: 'Captcha',
          tag: 'captcha',
          type: 'captcha',
          placeholder: 'Captcha',
        },
        {
          label: 'Source',
          tag: 'source',
          type: 'source',
          value: '',
        },

        {
          label: 'Date of birth',
          tag: 'date_of_birth',
          type: 'date',
          placeholder: 'Date of birth',
        },
        // {
        // 	label: "I WANT TO JOIN NOW!",
        // 	tag: "button",
        // 	type: "submit",
        // 	placeholder: "Button"
        // }
      ],

      attrName: '',

      formStyle: {
        bgColor: 'FFFFFF',
        color: '000000',
        border: 1,
        radius: 4,
        width: 550,
        showBgColorPicker: false,
        showColorPicker: false,
        inlineForm: false,
        formLabelVisible: true,
        isBrandingActive: true,
        emebedHtml: '',
        formLabelColor: '',
        bgImage: '',
      },

      buttonStyle: {
        bgColor: '2A3135',
        color: 'FFFFFF',
        border: 0,
        radius: 0.3125,
        padding: 11,
      },
      dragOptions: {
        isDragEntered: false,
      },
      styleNode: {},
      styleNodeFormColor: {},
      showDropZones: false,
      fieldPushIndex: -1,
      //Form data
      formsurvey: {} as Formsurvey,
      currentLocationId: '',
      builderId: '',
      surveyBuilderCount: 0 as number,
      customFields: [] as CustomField[],
      //Custom fields
      drawComponent: false as boolean,
      drawComponentHtmlFooter: false as boolen,

      //File upload
      isImageFileInStore: false,
      formAction: {
        redirectURL: '',
        thankyouText: 'Thank you for taking the time to complete this survey.',
        actionType: 2,
        formName: '',
        fbPixelId: '',
        pageViewEvent: 'PageView',
        formSubmissionEvent: 'SubmitApplication',
        fieldSettingEnable: false,
        fieldPerPage: 1,
        source: 'survey',
        disqualifiedText:
          'Thank you for taking the time to complete this survey.',
        disqualifiedType: 2,
        disqualifiedUrl: '',
        endsurveyText: 'Thank you for taking the time to complete this survey.',
        endsurveyType: 2,
        endsurveyUrl: '',
        stickyContact: false,
        footerHtml: '',
        headerImageSrc: '',
        headerFullWidthEnable: false,
        isBackButtonEnable: true,
        disableAutoNavigation: false,
        isProgressBarEnabled: true,
        isAnimationDisabled: false,
        isSurveyScrollEnabled: false
      },
      fieldSettings: {
        enable: false,
        fieldInfo: {},
      },
      slideSettings: {
        enable: false,
        slideInfo: {},
        slidePosition: 0,
      },
      selectedFieldIndex: 0,
      // Slides
      currentSlide: 0,
      surveySlides: [] as any,
      logicSkipStore: [] as any,
      footerHtmlModalVisible: { visible: false } as { [key: string]: any },
      addCustomFieldModal: { visible: false } as { [key: string]: any },
      // Custom html modal
      drawComponentCustomHtml: false as boolen,
      customHtmlModalVisible: { visible: false } as { [key: string]: any },

      // Description Modal
      drawComponentDescriptionText: false as boolean,
      descriptionModalVisible: { visible: false } as { [key: string]: any },
      isSideBarDrag: false,
    }
  },
  methods: {
    async saveSurveyChanges() {
      let tempObj = JSON.parse(
        JSON.stringify({ ...this.formsurvey.formData, ...this.formAction })
      )
      tempObj.slides.forEach((slide, ind) => {
        delete tempObj.slides[ind].active

        slide.slideData.forEach((field, ind1) => {
          delete tempObj.slides[ind].slideData[ind1].active
        })
      })

      this.publishedFormData = JSON.parse(JSON.stringify(tempObj))
      await this.formsurvey.save()
      await updateCachingIndex({
        index_type: cacheUpdateEvents.SURVEY_UPDATE,
        event_origin: this.currentLocationId,
      })
      this.$bus.$emit('survey-has-unsaved-data', false)
    },
    async initialData() {
      this.currentLocationId = this.$router.currentRoute.params.location_id
      this.builderId = this.$router.currentRoute.params.survey_id
      // console.log("Builder ID", this.builderId);
      this.surveyBuilderCount = await Formsurvey.getCountByLocationId(
        this.currentLocationId
      )

      if (!this.builderId) {
        //this.saveForm();
        this.$router.push({
          name: 'list_survey',
          params: this.$route.params,
        })
        this.loading = false
      } else {
        this.formsurvey = await Formsurvey.getById(this.builderId)
        // Probably has wrong ID in url, redirect it workflows page
        if (!this.formsurvey) {
          this.$router.push({
            name: 'list_survey',
            params: this.$route.params,
          })
        }
        //Migrate old surveys & Load form from json
        this.migrateOldSurveyWithId()
        this.loadSurveyFromJson()
      }
    },
    hasLogicConflict(slideIndex) {
      if (
        this.surveySlides[slideIndex].logic &&
        Object.keys(this.surveySlides[slideIndex].logic).length > 1
      ) {
        return true
      }
      return false
    },
    async fetchCustomFields() {
      if (unsubscribeCustomFields) unsubscribeCustomFields()

      unsubscribeCustomFields = CustomField.getByLocationId(
        this.currentLocationId
      ).onSnapshot(snapshot => {
        this.customFields = snapshot.docs.map(d => new CustomField(d))
        //console.log("customField",this.customFields);
      })
    },
    isFieldExist(itemAttribName: any, attrKey: any) {
      let exceptStore = ['header', 'image', 'html']
      let store: any = this.surveySlides
      var response = { status: false }
      store.forEach(function (slides: any, index: any) {
        slides.slideData.find(function (item: any, si: any) {
          if (
            item.tag == itemAttribName &&
            exceptStore.indexOf(itemAttribName) < 0
          ) {
            response = { status: true, index: si }
          }
        })
      })
      return response
    },
    camelize(str: string) {
      return str.replace(/\W+(.)/g, function (match, chr) {
        return chr.toUpperCase()
      })
    },
    handleStandredFields(data: any) {
      this.attrName = data.item.tag
      var el = this

      //Check if item Exist then return
      var isFieldExist = this.isFieldExist(this.attrName, 'tag')
      // console.log(isFieldExist);
      if (isFieldExist.status) {
        this.fieldPushIndex = -1
        return false
      }
      var createDOM: any = data.item
      if (this.fieldPushIndex < 0) {
        this.surveySlides[this.currentSlide].slideData.push(createDOM)
      } else {
        this.surveySlides[this.currentSlide].slideData.splice(
          this.fieldPushIndex,
          0,
          createDOM
        )
      }
      return true
    },
    handleCustomFields(data: any) {
      //Check if item Exist then return
      var isFieldExist = this.isFieldExist(data.item.id, 'id')
      if (isFieldExist.status) {
        this.fieldPushIndex = -1
        return false
      }

      //Common fields for both standrard and custom
      var createDOM: any = {
        id: data.item.id,
        name: data.item.name,
        type: data.item.dataType.toLowerCase(),
        required: false,
        label: data.item.name,
        placeholder: data.item.placeholder
          ? data.item.placeholder
          : data.item.name,
        tag: data.item.id,
      }

      //Adding more fields on the basis of field type -> Accept camelcase only
      for (var key in data.item.data) {
        var camelkey = this.camelize(key.replace(/_/g, ' '))
        createDOM[camelkey] = data.item.data[key]
      }

      //Insert to itemDrop array
      if (this.fieldPushIndex < 0) {
        this.surveySlides[this.currentSlide].slideData.push(createDOM)
      } else {
        this.surveySlides[this.currentSlide].slideData.splice(
          this.fieldPushIndex,
          0,
          createDOM
        )
      }
      return true
    },
    handleDrop(data: any, event: any, indexSlide: number) {
      this.setSlideActive(indexSlide)
      data = lodash.cloneDeep(data)
      var isFieldAllowedToAdd = false
      if (data.item.id) {
        console.log('Droped custom fields')
        isFieldAllowedToAdd = this.handleCustomFields(data)
      } else {
        console.log('Drop Standered fields')
        isFieldAllowedToAdd = this.handleStandredFields(data)
      }

      if (isFieldAllowedToAdd) {
        this.dragOptions.isDragEntered = false
        this.fieldPushIndex = -1 //reset field index
        this.saveForm()
      }
    },
    removeField(tag: string) {
      //Check if item Exist then return
      /*var isFieldExist: any = this.isFieldExist(tag.id, 'tag');
			console.log(isFieldExist);
      var index: number = isFieldExist.index;*/
      this.surveySlides[this.currentSlide].slideData.splice(
        this.selectedFieldIndex,
        1
      )
      this.saveForm()
    },
    isSlideIdUsedInLogic(slideId: any) {
      let slideNames = [] as string[]
      this.surveySlides.map(function (item: any, index: any) {
        if (item.logic !== undefined) {
          for (var key in item.logic) {
            const logicField = item.logic[key]
            for (var logicKey in logicField) {
              if (logicField[logicKey].id === slideId) {
                slideNames.push(item.slideName)
              }
            }
          }
        }
      })
      return slideNames.join(', ')
    },
    removeSlide(index: number) {
      let confirmation
      const slideId = this.surveySlides[index].id
      const messages = []
      const usedInSlideNames = this.isSlideIdUsedInLogic(slideId)
      if (this.surveySlides.length - 1 !== index) {
        messages.push('rearrange the other slides')
      }
      if (usedInSlideNames) {
        messages.push(`break the logic in ${usedInSlideNames}`)
      }
      if (messages.length > 0) {
        confirmation =
          'This will ' +
          messages.join(' and ') +
          ', are you sure you want to delete this slide?'
      } else {
        confirmation = 'Are you sure you want to delete this slide?'
      }

      //Check if item Exist then return
      if (confirm(confirmation)) {
        this.surveySlides.splice(index, 1)
        if (this.surveySlides.length === 1) {
          this.currentSlide = 0
        } else {
          this.currentSlide = this.surveySlides.length - 1
        }
        //set slide searial wise after deleting a slide
        //this.setSquenceOfSlide();
        this.surveySlides.map(function (item, index) {
          item.slideName = item.slideName
            ? item.slideName
            : 'slide ' + parseInt(index + 1)
          if (item.logic !== undefined) {
            for (var key in item.logic) {
              const logicField = item.logic[key]
              for (var logicKey in logicField) {
                if (logicField[logicKey].id === slideId) {
                  delete logicField[logicKey]
                }
              }
            }
          }
        })
        this.setSlideActive(this.currentSlide)
        this.saveForm()

        //Setting up localstorage
        let tempSlides = 'survey_temp_slides_' + this.builderId
        localStorage.setItem(tempSlides, JSON.stringify(this.surveySlides))
      }
    },
    setSquenceOfSlide() {
      let i = 1
      let store = this.surveySlides
      store.map(function (value: any, key: any) {
        value.slideName = 'Slide ' + i
        i++
      })
    },
    removeCustomField(field: any) {
      // console.log('custom data', field.id);
      //Check if item Exist then return
      // var isFieldExist: any = this.isFieldExist(field.id, 'id');
      // var index: number = isFieldExist.index;
      if ('logic' in this.surveySlides[this.currentSlide]) {
        for (var key in this.surveySlides[this.currentSlide].logic) {
          if (key === field.id) {
            delete this.surveySlides[this.currentSlide].logic[key]
          }
        }
      }
      this.surveySlides[this.currentSlide].slideData.splice(
        this.selectedFieldIndex,
        1
      )
      this.saveForm()
    },
    applyDrag(arr: any, dragResult: any) {
      const { removedIndex, addedIndex, payload } = dragResult
      if (removedIndex === null && addedIndex === null) return arr

      const result = [...arr]
      let itemToAdd = payload

      if (removedIndex !== null) {
        itemToAdd = result.splice(removedIndex, 1)[0]
      }

      if (addedIndex !== null) {
        result.splice(addedIndex, 0, itemToAdd)
      }
      return result
    },
    onSortableDrop(dropResult: any, slideIndex) {
      console.log(dropResult, slideIndex)

      if (dropResult.removedIndex !== null && dropResult.addedIndex === null) {
        let tempArr1 = lodash.cloneDeep(this.surveySlides[slideIndex].slideData)
        tempArr1.splice(dropResult.removedIndex, 1)
        this.surveySlides[slideIndex].slideData = lodash.cloneDeep(tempArr1)
      } else if (
        dropResult.removedIndex !== null &&
        dropResult.addedIndex !== null
      ) {
        //If rearrange within slide

        let tempArr = lodash.cloneDeep(this.surveySlides[slideIndex].slideData)
        tempArr.splice(dropResult.removedIndex, 1)
        tempArr.splice(dropResult.addedIndex, 0, dropResult.payload)
        this.surveySlides[slideIndex].slideData = lodash.cloneDeep(tempArr)
      } else if (
        dropResult.addedIndex !== null &&
        dropResult.removedIndex === null
      ) {
        let toSlideIndex = slideIndex
        let tempArr2 = lodash.cloneDeep(
          this.surveySlides[toSlideIndex].slideData
        )
        tempArr2.splice(dropResult.addedIndex, 0, dropResult.payload)
        this.surveySlides[toSlideIndex].slideData = lodash.cloneDeep(tempArr2)
      }

      if (dropResult.removedIndex !== null) {
        console.log(dropResult.addedIndex, slideIndex)
        this.setFieldActive(dropResult.addedIndex, slideIndex)
      }

      this.saveForm()
    },
    onSortableSlide(dropResult: any) {
      this.surveySlides = lodash.cloneDeep(
        this.applyDrag(this.surveySlides, dropResult)
      )
      this.saveForm()
    },
    startDraggingItem() {
      this.isSideBarDrag = true
      this.showDropZones = true
    },
    dragEndItem() {
      this.isSideBarDrag = false
      this.showDropZones = false
    },
    addFieldAfter(emitData: any) {
      this.fieldPushIndex = emitData.data
    },

    fieldSettingImage(data: any) {
      let imgStore = this.surveySlides[this.currentSlide].slideData
      switch (data.type) {
        case 'width':
          this.$set(imgStore[this.selectedFieldIndex], 'width', data.value)
          break
        case 'height':
          this.$set(imgStore[this.selectedFieldIndex], 'height', data.value)
          break
        case 'alt':
          this.$set(imgStore[this.selectedFieldIndex], 'alt', data.value)
          break
        case 'img-align':
          this.$set(imgStore[this.selectedFieldIndex], 'align', data.value)
          break
      }
      this.saveForm()
    },

    formToJson() {
      let fields: any = []
      let el = this
      this.surveySlides.map(function (value: any, key: any) {
        value.button = {
          background: el.buttonStyle.bgColor,
          color: el.buttonStyle.color,
          border: {
            border: el.buttonStyle.border,
            radius: el.buttonStyle.radius,
            padding: el.buttonStyle.padding,
          },
        }
        fields.push(value)
      })

      let formJsonObj = {
        form: {
          fbPixelId: this.formAction.fbPixelId,
          pageViewEvent: this.formAction.pageViewEvent,
          formSubmissionEvent: this.formAction.formSubmissionEvent,
          layout: this.formStyle.inlineForm,
          width: this.formStyle.width,
          formLabelVisible: this.formStyle.formLabelVisible,
          stickyContact: this.formAction.stickyContact,
          isBackButtonEnable: this.formAction.isBackButtonEnable,
          disableAutoNavigation: this.formAction.disableAutoNavigation,
          isProgressBarEnabled: this.formAction.isProgressBarEnabled,
          isAnimationDisabled: this.formAction.isAnimationDisabled,
          isSurveyScrollEnabled: this.formAction.isSurveyScrollEnabled,
          style: {
            ac_branding: this.formStyle.isBrandingActive,
            background: this.formStyle.bgColor,
            color: this.formStyle.color,
            bgImage: this.formStyle.bgImage,
            border: {
              border: this.formStyle.border,
              radius: this.formStyle.radius,
              style: 'dashed',
              color: 'CDE0EC',
            },
          },
          customStyle: this.formStyle.emebedHtml,
          company: {
            logoURL: this.company.logoURL ? this.company.logoURL : '',
            domain: this.company.domain ? this.company.domain : '',
            name: this.company.name ? this.company.name : '',
          },
          formAction: {
            redirect_url: this.formAction.redirectURL,
            thankyouText: this.formAction.thankyouText,
            actionType: this.formAction.actionType,
            fieldSettingEnable: this.formAction.fieldSettingEnable,
            fieldPerPage: this.formAction.fieldPerPage,
            disqualifiedText: this.formAction.disqualifiedText,
            disqualifiedType: this.formAction.disqualifiedType,
            disqualifiedUrl: this.formAction.disqualifiedUrl,
            endsurveyText: this.formAction.endsurveyText,
            endsurveyType: this.formAction.endsurveyType,
            endsurveyUrl: this.formAction.endsurveyUrl,
            footerHtml: this.formAction.footerHtml,
            headerFullWidthEnable: this.formAction.headerFullWidthEnable,
            headerImageSrc: this.formAction.headerImageSrc,
          },
        },
        slides: fields,
        surveyLogicLinkById: true,
      }
      return formJsonObj
    },
    async saveForm() {
      let jsonObj = this.formToJson()
      this.formsurvey.name = this.formAction.formName
      //Block for Insert
      if (!this.formsurvey) {
        console.log('SaveForm - If block Executed')
        this.formsurvey = new Formsurvey()
        this.formsurvey.name = 'Survey ' + (this.surveyBuilderCount + 1)
      }

      this.formsurvey.locationId = this.currentLocationId
      this.formsurvey.formData = jsonObj

      let tempObj = JSON.parse(
        JSON.stringify({ ...this.formsurvey.formData, ...this.formAction })
      )
      tempObj.slides.forEach((slide, ind) => {
        delete tempObj.slides[ind].active

        slide.slideData.forEach((field, ind1) => {
          delete tempObj.slides[ind].slideData[ind1].active
        })
      })

      if (!lodash.isEqual(this.publishedFormData, tempObj)) {
        this.$bus.$emit('survey-has-unsaved-data', true)
      } else {
        this.$bus.$emit('survey-has-unsaved-data', false)
      }

      console.log('Entry Saved')
    },
    migrateOldSurveyWithId() {
      const el = this
      const survey = this.formsurvey.formData
      if (!survey.surveyLogicLinkById) {
        if (!survey.form.formAction.fieldSettingEnable) {
          // Multiple slides
          survey.slides.forEach(function (item: any, index: number) {
            if (!item.id) {
              const id = new Date().getTime() + '-' + index
              el.$set(item, 'id', id)
            }
          })

          survey.slides.forEach(function (item: any, index: number) {
            if ('logic' in item) {
              for (var logicKey in item.logic) {
                for (var optionsKey in item.logic[logicKey]) {
                  const optStore = item.logic[logicKey][optionsKey]
                  if (optStore) {
                    const optIdStore = survey.slides[optStore.index]
                    if (optIdStore) {
                      optStore.id = optIdStore.id // Assign Right ID of the slide
                    }
                  }
                }
              }
            }
          })
        } else {
          // Single slide
          const id = new Date().getTime() + '-0'
          const surveySlides = survey.slides[0]
          el.$set(surveySlides, 'id', id)
          if ('logic' in surveySlides) {
            for (var logicKey in surveySlides.logic) {
              for (var optionsKey in surveySlides.logic[logicKey]) {
                const optStore = surveySlides.logic[logicKey][optionsKey]
                if (optStore) {
                  const optIdStore = surveySlides.slideData[optStore.index]
                  if (optIdStore) {
                    // Assign Right ID of the slide
                    optStore.id =
                      'id' in optIdStore ? optIdStore.id : optIdStore.tag
                  }
                }
              }
            }
          }
        }
      }
    },
    loadSurveyFromJson() {
      const el = this
      const surveyDataSet = this.formsurvey.formData.form
      const slide = this.formsurvey.formData.slides
      // if(surveyDataSet == undefined){
      // 	this.loading = false;
      // 	return false;
      // }

      if ('slides' in this.formsurvey.formData) {
        this.surveySlides = this.formsurvey.formData.slides
        this.initWithFirstSlideSelected()
      }

      // For Slides -- on refresh set active slide in-active
      this.setSlideActive(this.currentSlide)
      this.fieldSettingBack()

      if ('style' in surveyDataSet) {
        this.formStyle.bgColor = surveyDataSet.style.background //BG color
        this.formStyle.color = surveyDataSet.style.color //Font color
        this.formStyle.border = surveyDataSet.style.border.border //Border
        this.formStyle.radius = surveyDataSet.style.border.radius //Corner Radius
        this.formStyle.isBrandingActive = surveyDataSet.style.ac_branding //AC Branding
        this.formStyle.bgImage = surveyDataSet.style.bgImage
          ? surveyDataSet.style.bgImage
          : ''
      }

      if ('button' in this.surveySlides) {
        this.buttonStyle.bgColor = this.surveySlides.button.background // BG color
        this.buttonStyle.color = this.surveySlides.button.color //Font Color
        this.buttonStyle.border = this.surveySlides.button.border.border //Button Border
        this.buttonStyle.radius = this.surveySlides.button.border.radius //Button Radius
        this.buttonStyle.padding = this.surveySlides.button.border.padding //Button Padding
      }
      if ('formAction' in surveyDataSet) {
        this.formAction.redirectURL = surveyDataSet.formAction.redirect_url
        this.formAction.thankyouText = surveyDataSet.formAction.thankyouText
        this.formAction.actionType = surveyDataSet.formAction.actionType
        this.formAction.fieldSettingEnable =
          surveyDataSet.formAction.fieldSettingEnable
        this.formAction.fieldPerPage = surveyDataSet.formAction.fieldPerPage

        this.formAction.disqualifiedType =
          surveyDataSet.formAction.disqualifiedType
        this.formAction.disqualifiedText =
          surveyDataSet.formAction.disqualifiedText
        this.formAction.disqualifiedUrl =
          surveyDataSet.formAction.disqualifiedUrl

        this.formAction.endsurveyType = surveyDataSet.formAction.endsurveyType
        this.formAction.endsurveyText = surveyDataSet.formAction.endsurveyText
        this.formAction.endsurveyUrl = surveyDataSet.formAction.endsurveyUrl
        this.formAction.footerHtml =
          'footerHtml' in surveyDataSet.formAction
            ? surveyDataSet.formAction.footerHtml
            : ''
        this.formAction.headerImageSrc = surveyDataSet.formAction.headerImageSrc
          ? surveyDataSet.formAction.headerImageSrc
          : ''
        this.formAction.headerFullWidthEnable = surveyDataSet.formAction
          .headerFullWidthEnable
          ? surveyDataSet.formAction.headerFullWidthEnable
          : false
      } else if ('redirect_url' in surveyDataSet) {
        this.formAction.redirectURL = surveyDataSet.redirect_url
        this.formAction.thankyouText = ''
        this.formAction.actionType = 1
      }

      this.formStyle.width = surveyDataSet.width //Width
      this.formStyle.inlineForm = surveyDataSet.layout //Form Layout
      this.formAction.fbPixelId = surveyDataSet.fbPixelId
      this.formAction.pageViewEvent = surveyDataSet.pageViewEvent
        ? surveyDataSet.pageViewEvent
        : 'PageView'
      this.formAction.formSubmissionEvent = surveyDataSet.formSubmissionEvent
        ? surveyDataSet.formSubmissionEvent
        : 'SubmitApplication'

      this.formAction.formName = this.formsurvey.name
      this.formAction.stickyContact = surveyDataSet.stickyContact
        ? surveyDataSet.stickyContact
        : false
      this.formAction.isBackButtonEnable = surveyDataSet.isBackButtonEnable
        ? surveyDataSet.isBackButtonEnable
        : false
      this.formAction.disableAutoNavigation = surveyDataSet.disableAutoNavigation
        ? surveyDataSet.disableAutoNavigation
        : false
      this.formAction.isAnimationDisabled = surveyDataSet.isAnimationDisabled
        ? surveyDataSet.isAnimationDisabled
        : false
      this.formAction.isSurveyScrollEnabled = surveyDataSet.isSurveyScrollEnabled
        ? surveyDataSet.isSurveyScrollEnabled
        : false
      this.formAction.isProgressBarEnabled = surveyDataSet.hasOwnProperty(
        'isProgressBarEnabled'
      )
        ? surveyDataSet.isProgressBarEnabled
        : true

      if (
        'customStyle' in surveyDataSet &&
        surveyDataSet.customStyle.trim('').length > 0
      ) {
        this.formStyle.emebedHtml = surveyDataSet.customStyle
      }

      if (this.formStyle.color) {
        this.formStyle.formLabelColor =
          'label{color:#' + this.formStyle.color + '!important;}'
      }

      if ('formLabelVisible' in surveyDataSet) {
        this.formStyle.formLabelVisible = surveyDataSet.formLabelVisible
      }

      //Fill logic skip store
      this.fillSkipLogicStore()
      this.loading = false

      if (this.formsurvey || this.formAction) {
        let tempObj = JSON.parse(
          JSON.stringify({ ...this.formsurvey.formData, ...this.formAction })
        )
        tempObj.slides.forEach((slide, ind) => {
          delete tempObj.slides[ind].active

          slide.slideData.forEach((field, ind1) => {
            delete tempObj.slides[ind].slideData[ind1].active
          })
        })
        this.publishedFormData = JSON.parse(JSON.stringify(tempObj))
        this.$bus.$emit('survey-has-unsaved-data', false)
      }
    },

    fillSkipLogicStore() {
      this.logicSkipStore = []
      let el = this
      if (this.surveySlides.length > 0) {
        let skipStore = {}
        if (this.formAction.fieldSettingEnable) {
          let keyIndex = 0
          this.surveySlides[0].slideData.map(function (value: any, key: any) {
            if (
              value.tag == 'image' ||
              value.tag == 'header' ||
              value.tag == 'html'
            ) {
              return
            }
            let fieldName =
              'name' in value && value.name != undefined
                ? value.name
                : value.label
            skipStore = {
              name: 'Q' + (keyIndex + 1) + ': ' + fieldName,
              id: value.id ? value.id : value.tag,
              index: keyIndex,
            }
            if (
              el.surveySlides.length > 0 &&
              el.surveySlides[0].logic !== undefined
            ) {
              skipStore.logic = el.surveySlides[0].logic
            }
            el.logicSkipStore.push(skipStore)
            keyIndex++
          })
        } else {
          this.surveySlides.map(function (value: any, key: any) {
            skipStore = {
              id: value.id,
              name: 'P' + (key + 1) + ' - ' + value.slideName,
              index: key,
            }
            if (value && value.logic !== undefined) {
              skipStore.logic = value.logic
            }
            el.logicSkipStore.push(skipStore)
          })
        }
        this.logicSkipStore.push({
          name: 'Disqualify immediately',
          id: 'disqualify',
          index: -1,
        })
        this.logicSkipStore.push({
          name: 'Disqualify after submit',
          id: 'endsurvey',
          index: -2,
        })
      }
    },
    showAddCustomFieldModal() {
      this.reset()
      this.addCustomFieldModal = {
        visible: true,
        location_id: this.currentLocationId,
      }
    },
    reset() {
      var vm = this
      vm.drawComponent = false
      Vue.nextTick(function () {
        vm.drawComponent = true
      })
    },
    closeAddCustomFieldModal() {
      this.addCustomFieldModal = {
        visible: false,
      }
    },
    showIntegratePage() {
      this.$router.push({
        name: 'form_builder_integrate',
        params: {
          location_id: this.currentLocationId,
          builder_id: this.builderId,
        },
      })
    },
    async showIntegratePageSurvey() {
      this.$bvModal.show('survey-embed-modal')
      await this.saveSurveyChanges()
    },
    redirectToListPage() {
      this.$router.push({
        name: 'list_survey',
        params: {
          location_id: this.currentLocationId,
        },
      })
    },

    setSlideActive(index: number) {
      this.currentSlide = index
    },
    setSlideActiveFalse(e) {
      if (e) {
        e.preventDefault()
        e.stopPropagation()
      }
      this.slideSettings.enable = false
      this.slideSettings.slidePosition = 0
      this.slideSettings.slideInfo = {}
      this.surveySlides.forEach((slide, ind) => {
        slide.active = false
        slide.slideData.forEach((fld, index) => {
          this.setFieldActiveFalse(index, ind)
        })
      })
    },
    setSlideActiveOnClick(e, index) {
      let eventSource = e.target || e.srcElement
      if (
        !(
          eventSource.classList.contains('drop') ||
          eventSource.classList.contains('builder-form-name')
        )
      )
        return
      e.preventDefault()
      e.stopPropagation()
      this.setSlideActiveFalse(null)
      this.currentSlide = index
      let store = this.surveySlides
      this.slideSettings.enable = true
      this.slideSettings.slidePosition = index
      this.slideSettings.slideInfo = store[index]
      store.map((value: any, key: any) => {
        if (key === index) this.$set(value, 'active', true)
        else this.$set(value, 'active', false)
      })
    },

    setFieldActive(index: number, slideIndex: number) {
      this.setSlideActiveFalse(null)
      logicHandler = {} //reset logichan
      this.selectedFieldIndex = index
      var el = this
      this.fieldSettingBack()
      this.setSlideActive(slideIndex) //for active slide
      let store = this.surveySlides[this.currentSlide].slideData
      if (store[index] != undefined) {
        el.$set(store[index], 'active', true)
        this.fieldSettings.enable = true
        this.fieldSettings.fieldInfo = store[index]
      }
      if ('logic' in this.surveySlides[this.currentSlide]) {
        logicHandler['logic'] = this.surveySlides[this.currentSlide].logic
      }
      this.fillSkipLogicStore()
    },
    setFieldActiveFalse(index: number, slideIndex: number) {
      let el = this
      let store = this.surveySlides[slideIndex].slideData
      if (store[index] != undefined) {
        el.$set(store[index], 'active', false)
        this.fieldSettings.enable = false
        this.fieldSettings.fieldInfo = {}
      }
      if ('logic' in this.surveySlides[slideIndex]) {
        logicHandler['logic'] = this.surveySlides[slideIndex].logic
      }
    },

    fieldSettingInput(data: any) {
      let store = this.surveySlides[this.currentSlide].slideData
      switch (data.type) {
        case 'label':
          store[this.selectedFieldIndex].label = data.value
          break
        case 'placeholder':
          store[this.selectedFieldIndex].placeholder = data.value
          break
        case 'required':
          store[this.selectedFieldIndex].required = data.value
          break
        case 'submit':
          store[this.selectedFieldIndex].label = data.value
          break
        case 'deselect-slide':
          this.setSlideActiveFalse(null)
          break
        case 'text-size':
          this.$set(store[this.selectedFieldIndex], 'size', data.value)
          break
        case 'text-weight':
          this.$set(store[this.selectedFieldIndex], 'weight', data.value)
          break
        case 'text-color':
          this.$set(store[this.selectedFieldIndex], 'color', data.value)
          break
        case 'text-font':
          this.$set(store[this.selectedFieldIndex], 'font', data.value)
          break
        case 'text-label':
          store[this.selectedFieldIndex].label = data.value
          break
        case 'text-align':
          this.$set(store[this.selectedFieldIndex], 'align', data.value)
          break
        case 'allow-country-selection':
          this.$set(
            store[this.selectedFieldIndex],
            'allowCountrySelection',
            data.value
          )
          break
        case 'html':
          const scriptString = data.value
            .replace(/[‘’]/g, "'")
            .replace(/[“”]/g, '"')
          this.$set(store[this.selectedFieldIndex], 'html', scriptString)
          break
        case 'text-description':
          this.$set(store[this.selectedFieldIndex], 'description', data.value)
          break
        case 'source-name':
          store[this.selectedFieldIndex].value = data.value
          break
        case 'hidden':
          store[this.selectedFieldIndex].hidden = data.value
          break
        case 'hidden-value':
          store[this.selectedFieldIndex].hiddenFieldValue = data.value
          break
        case 'hidden-query-key':
          store[this.selectedFieldIndex].hiddenFieldQueryKey = data.value
          break
      }
      this.saveForm()
    },
    slideSettingsInput(data: any) {
      let slide = this.surveySlides[data.index]
      switch (data.type) {
        case 'slide-name':
          slide.slideName = data.value
          break
        case 'slide-jump-to':
          slide.jumpTo = data.value
      }
      this.saveForm()
    },
    fieldSettingButton(data: any) {
      let store = this.surveySlides[this.currentSlide].slideData
      switch (data.type) {
        case 'bgcolor':
          this.buttonStyle.bgColor = data.value
          break
        case 'color':
          this.buttonStyle.color = data.value
          break
        case 'border':
          this.buttonStyle.border = data.value
          break
        case 'radius':
          this.buttonStyle.radius = data.value
          break
        case 'padding':
          this.buttonStyle.padding = data.value
          break
        case 'btn-align':
          this.$set(store[this.selectedFieldIndex], 'align', data.value)
          break
        case 'btn-fullwidth':
          this.$set(store[this.selectedFieldIndex], 'fullwidth', data.value)
          break
      }
      this.saveForm()
    },
    fieldSettingForm(data: any) {
      switch (data.type) {
        case 'bgcolor':
          this.formStyle.bgColor = data.value
          break
        case 'color':
          this.formStyle.color = data.value
          this.formStyle.formLabelColor =
            'label{color:#' + data.value + '!important;}'
          break
        case 'border':
          this.formStyle.border = data.value
          break
        case 'radius':
          this.formStyle.radius = data.value
          break
        case 'width':
          this.formStyle.width = data.value
          break
        case 'width':
          this.formStyle.width = data.value
          break
        case 'layout':
          this.formStyle.inlineForm = data.value
          break
        case 'label-visible':
          this.formStyle.formLabelVisible = data.value
          break
        case 'branding-active':
          this.formStyle.isBrandingActive = data.value
          break
        case 'custom-style':
          this.formStyle.emebedHtml = data.value
          break
        case 'bg-img':
          this.formStyle.bgImage = data.value
          break
        case 'bg-img-delete':
          if ('bgImage' in this.formStyle) {
            this.formStyle.bgImage = ''
          }
          break
      }
      this.saveForm()
    },
    fieldSettingFormAdvance(data: any) {
      switch (data.type) {
        case 'form':
          this.formAction.formName = data.value
          break
        case 'redirect':
          this.formAction.redirect_url = data.value
          break
        case 'thankyou':
          this.formAction.thankyouText = data.value
          break
        case 'fb-pixel-id':
          this.formAction.fbPixelId = data.value
          break
        case 'fb-pixel-page-view-event':
          this.formAction.pageViewEvent = data.value
          break
        case 'fb-pixel-form-submission-event':
          this.formAction.formSubmissionEvent = data.value
          break
        case 'field-per-page':
          this.formAction.fieldPerPage = data.value
          break
        case 'field-setting-enable':
          this.formAction.fieldSettingEnable = data.value
          this.toggleSlider()
          break
        case 'dis-qualified-text':
          this.formAction.disqualifiedText = data.value
          break
        case 'dis-qualified-type':
          this.formAction.disqualifiedType = data.value
          break
        case 'dis-qualified-url':
          this.formAction.disqualifiedUrl = data.value
          break
        case 'end-survey-text':
          this.formAction.endsurveyText = data.value
          break
        case 'end-survey-type':
          this.formAction.endsurveyType = data.value
          break
        case 'end-survey-url':
          this.formAction.endsurveyUrl = data.value
          break
        case 'logic-skip':
          let currentSlideStore = this.surveySlides[this.currentSlide]
          // Less than 0 = Remove , Greater than 0 is for add
          if (!data.value.status) {
            //---Logic form current slide
            if (
              'logic' in currentSlideStore &&
              Object.keys(currentSlideStore.logic).length > 0 &&
              data.value.fieldId in currentSlideStore.logic
            ) {
              if (
                Object.keys(currentSlideStore.logic[data.value.fieldId])
                  .length <= 1
              ) {
                delete currentSlideStore.logic[data.value.fieldId]
              } else {
                delete currentSlideStore.logic[data.value.fieldId][
                  'option_' + data.value.optionIndex
                ]
              }
              //Remove logic if nothing found
              if (Object.keys(currentSlideStore.logic).length <= 0) {
                delete currentSlideStore['logic']
              }
            }
          } else {
            let currentFieldstore =
              currentSlideStore.slideData[this.selectedFieldIndex]
            let logicItemKey = currentFieldstore.id
            if (currentFieldstore.tag == 'gender') {
              logicItemKey = 'gender'
            }

            //check if logic exist in current slide
            if ('logic' in logicHandler == false) {
              logicHandler['logic'] = {}
            }
            if (logicItemKey in logicHandler.logic == false) {
              logicHandler.logic[logicItemKey] = {}
            }

            let skipOptionVal = 'option_' + data.value.optionIndex
            if (skipOptionVal in logicHandler.logic[logicItemKey] == false) {
              logicHandler.logic[logicItemKey][skipOptionVal] = {}
            }
            logicHandler.logic[logicItemKey][skipOptionVal] = data.value
            //Setting up value into logic selected

            logicHandler = lodash.cloneDeep(logicHandler)
            this.$set(
              this.surveySlides[this.currentSlide],
              'logic',
              logicHandler['logic']
            )
            //console.log(this.surveySlides);
          }
          break
        case 'sticky-contact':
          this.formAction.stickyContact = data.value
          break
        case 'auto-navigation':
          this.formAction.disableAutoNavigation = data.value
          break

        case 'full-width':
          this.formAction.headerFullWidthEnable = data.value
          break
        case 'footer-html':
          this.formAction.footerHtml = data.value
          break
        case 'header-img':
          this.formAction.headerImageSrc = data.value
          break
        case 'header-img-delete':
          if ('headerImageSrc' in this.formAction) {
            this.formAction.headerImageSrc = ''
          }
          break
        case 'back-buttion':
          this.formAction.isBackButtonEnable = data.value
          break
        case 'progress-button':
          this.formAction.isProgressBarEnabled = data.value
          break
        case 'disable-animation':
          this.formAction.isAnimationDisabled = data.value
          break
        case 'enabled-survey-scroll':
          this.formAction.isSurveyScrollEnabled = data.value
          break
        default:
          this.formAction.actionType = data.value
          break
      }
      this.saveForm()
    },
    toggleSlider() {
      //removing other slides fromcurrent
      let tempSlides = 'survey_temp_slides_' + this.builderId
      if (
        this.formAction.fieldSettingEnable &&
        this.surveySlides.length === 1
      ) {
        if (this.surveySlides[0].slideData) {
          this.isMultiSlide = false
          localStorage.setItem(tempSlides, JSON.stringify(this.surveySlides))
        }
      }
      //code for merge all slides
      if (this.formAction.fieldSettingEnable && this.surveySlides.length > 1) {
        localStorage.setItem(tempSlides, JSON.stringify(this.surveySlides))
        this.isMultiSlide = true
        const mergeSurveyData = []
        for (let i = 0; i < this.surveySlides.length; i++) {
          this.surveySlides[i].slideData.map(function (value: any, key: any) {
            mergeSurveyData.push(value)
          })
        }
        this.surveySlides.splice(1, this.surveySlides.length)
        this.surveySlides[0].slideData = mergeSurveyData
      } else if (!this.formAction.fieldSettingEnable && this.isMultiSlide) {
        if (localStorage.getItem(tempSlides)) {
          let store: any = localStorage.getItem(tempSlides)
          this.surveySlides = JSON.parse(store)
        }
      }
    },
    fieldSettingBack() {
      var el = this
      this.surveySlides.map(function (value: any, key: any) {
        let store = value.slideData
        store.map(function (valueField: any, keyField: any) {
          el.$set(valueField, 'active', false)
        })
      })
    },
    onClick(e: any) {
      let el: any = this.$refs.formSurveyReferance
      let target = e.target
      let elOpt: any = this.$refs.fieldSettingsRefs.$el
      if (
        el != undefined &&
        el !== target &&
        !el.contains(target) &&
        elOpt != undefined &&
        elOpt !== target &&
        !elOpt.contains(target)
      ) {
        this.fieldSettingBack()
        this.fieldSettings.enable = false
      }
    },
    saveUploadPath(urls: any, idx?: number) {
      let el = this
      let store = this.surveySlides[this.currentSlide].slideData
      if (idx != undefined && store[idx] && store[idx].tag == 'image') {
        el.$set(store[idx], 'url', urls[0])
      } else {
        store.map(function (value: any, index: any) {
          if ('tag' in value && value.tag == 'image') {
            el.$set(store[index], 'url', urls[0])
          }
        })
      }

      el.saveForm() //Save entry for modified dropItems
    },
    checkFieldOwnLine(tag: string) {
      let fieldCount = 0
      let lastFieldIndex = 0
      let store = this.surveySlides[this.currentSlide].slideData
      store.map(function (value: any, key: any) {
        if (fullWidthFields.indexOf(value.type) < 0) {
          fieldCount++
          lastFieldIndex = key
        }
      })

      if (fieldCount % 2 != 0 && store[lastFieldIndex].tag == tag) return true
      else return false
    },
    fieldFullWidth(type: string) {
      if (fullWidthFields.indexOf(type) >= 0) return true
      else return false
    },
    getSuccessorIndex() {
      return 'Slide ' + (this.surveySlides.length + 1)
    },
    async addNewSlide() {
      this.surveySlides.push({
        slideName: this.getSuccessorIndex(),
        slideData: [],
        id: new Date().getTime() + '-' + this.surveySlides.length,
      })
      this.formAction.fieldPerPage = this.surveySlides.length
      this.currentSlide = this.surveySlides.length - 1
      this.saveForm()
      this.setSlideActive(this.currentSlide)
    },
    showFooterHtmlModal(inputText: any) {
      this.resetHtmlFooter()
      this.footerHtmlModalVisible = {
        visible: true,
        text: inputText,
      }
    },
    resetHtmlFooter() {
      var vm = this
      vm.drawComponentHtmlFooter = false
      Vue.nextTick(function () {
        vm.drawComponentHtmlFooter = true
      })
    },
    closeFooterHtmlModal() {
      this.footerHtmlModalVisible = {
        visible: false,
      }
    },
    showCustomHtmlModal(inputText: any) {
      this.resetCustomHtml()
      this.customHtmlModalVisible = {
        visible: true,
        text: inputText,
      }
    },
    resetCustomHtml() {
      var vm = this
      vm.drawComponentCustomHtml = false
      Vue.nextTick(function () {
        vm.drawComponentCustomHtml = true
      })
    },
    closeCustomHtmlModal() {
      this.customHtmlModalVisible = {
        visible: false,
      }
    },
    initWithFirstSlideSelected() {
      this.setSlideActiveFalse(null)
      this.currentSlide = 0
      let store = this.surveySlides
      this.slideSettings.enable = true
      this.slideSettings.slidePosition = 0
      this.slideSettings.slideInfo = store[0]
      store.map((value: any, key: any) => {
        if (key === 0) this.$set(value, 'active', true)
        else this.$set(value, 'active', false)
      })
    },
    showDescriptionModal(inputText: any) {
      this.resetDescriptionText()
      this.descriptionModalVisible = {
        visible: true,
        text: inputText,
      }
    },
    closeDesriptionModal() {
      this.descriptionModalVisible = {
        visible: false,
      }
    },
    resetDescriptionText() {
      var vm = this
      vm.drawComponentDescriptionText = false
      Vue.nextTick(function () {
        vm.drawComponentDescriptionText = true
      })
    },
    getChildPayload(slideIndex) {
      return fieldIndex => {
        return this.surveySlides[slideIndex].slideData[fieldIndex]
      }
    },
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  mounted() {
    let style = document.createElement('style')
    style.type = 'text/css'
    style.appendChild(document.createTextNode(''))
    this.styleNode = style.childNodes[0] // a reference I store in the data hash
    document.head.appendChild(style)

    let styleColor = document.createElement('style')
    styleColor.type = 'text/css'
    styleColor.appendChild(document.createTextNode(''))
    this.styleNodeFormColor = styleColor.childNodes[0] // a reference I store in the data hash
    document.head.appendChild(styleColor)

    //On background click
    document.addEventListener('click', this.onClick)
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.redirectToListPage()
    },
    'formStyle.emebedHtml': function (val) {
      let styleStore = val.split('}')
      let styleString: string = ''
      styleStore.map(function (value: any, key: any) {
        if (value != '' && value.length > 0) {
          styleString += '#_builder-form ' + value + '} '
        }
      })
      this.styleNode.textContent = styleString
    },
    'formStyle.formLabelColor': function (val) {
      this.styleNodeFormColor.textContent = '#_builder-form ' + val
    },
  },
  computed: {
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
  },

  beforeRouteLeave(to, from, next) {
    if (!this.formsurvey?.formData?.slides) {
      next()
    }

    let tempObj = JSON.parse(
      JSON.stringify({ ...this.formsurvey.formData, ...this.formAction })
    )
    tempObj.slides.forEach((slide, ind) => {
      delete tempObj.slides[ind].active

      slide.slideData.forEach((field, ind1) => {
        delete tempObj.slides[ind].slideData[ind1].active
      })
    })

    if (!lodash.isEqual(this.publishedFormData, tempObj)) {
      this.$uxMessage(
        'confirmation',
        `The Survey Has unsaved work, Are you sure you want to leave and discard all unsaved work?`,
        async res => {
          if (res === 'ok') {
            next()
          } else {
            next(false)
          }
        }
      )
    } else {
      next()
    }
  },

  created() {
    this.$bus.$on('add-field-to-drozone', this.addFieldAfter)
    this.$bus.$on('remove-custom-field', this.removeCustomField)
    this.$bus.$on('remove-standard-field', this.removeField)
    this.$bus.$on('field-setting-input', this.fieldSettingInput)
    this.$bus.$on('slide-setting-input', this.slideSettingsInput)
    this.$bus.$on('field-setting-button', this.fieldSettingButton)
    this.$bus.$on('field-setting-form', this.fieldSettingForm)
    this.$bus.$on('field-setting-form-advance', this.fieldSettingFormAdvance)
    this.$bus.$on('field-setting-image', this.fieldSettingImage)
    this.$bus.$on('showFooterHtmlModal', this.showFooterHtmlModal)
    this.$bus.$on('showCustomHtmlModal', this.showCustomHtmlModal)
    this.$bus.$on('showDescriptionModal', this.showDescriptionModal)
    this.initialData()
    this.fetchCustomFields()
  },
  beforeDestroy() {
    this.$bus.$off('add-field-to-drozone', this.addFieldAfter)
    this.$bus.$off('remove-custom-field', this.removeCustomField)
    this.$bus.$off('remove-standard-field', this.removeField)
    this.$bus.$off('field-setting-input', this.fieldSettingInput)
    this.$bus.$off('slide-setting-input', this.slideSettingsInput)
    this.$bus.$off('field-setting-button', this.fieldSettingButton)
    this.$bus.$off('field-setting-form', this.fieldSettingForm)
    this.$bus.$off('field-setting-form-advance', this.fieldSettingFormAdvance)
    this.$bus.$off('field-setting-image', this.fieldSettingImage)
    this.$bus.$off('showFooterHtmlModal', this.showFooterHtmlModal)
    this.$bus.$off('showCustomHtmlModal', this.showCustomHtmlModal)
    this.$bus.$off('showDescriptionModal', this.showDescriptionModal)
    document.removeEventListener('click', this.onClick)
    this.styleNode.textContent = '' //Remove css
    this.styleNodeFormColor.textContent = ''
    if (unsubscribeCustomFields) unsubscribeCustomFields()
    let tempSlides = 'survey_temp_slides_' + this.builderId
    localStorage.removeItem(tempSlides)
  },
})
</script>

<style scoped lang="scss">
  .dnd-container-sort {
    min-height: 300px;
  }

  .bi-exclamation-circle {
    color: red;
  }

  .form-control {
    border-radius: 0.3125rem;
    padding: 15px 20px;
    font-size: 0.875rem;
    &:disabled, [readonly] {
      background-color: #e9ecef;
      opacity: 1;
    }
  }

  .vs__search, .vs__search:focus {
    padding: 12px 18px !important
  }

  .form-builder--item {
    .v-select {
      width: 100%;
      background: #f3f8fb;
      border-radius: 4px !important;
      height: auto;
      border: transparent;
      .vs__dropdown-toggle {
        height: 50px;
      }
    }
  }
  .add-custom-opt {
    padding: 3px;
    font-size: 14px;
  }
</style>
