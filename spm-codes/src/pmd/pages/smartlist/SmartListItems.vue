<template>
     <div style="width:100%" @click.ctrl.shift="openPod">
      <h3 class="mx-3">Smart Lists</h3>
      <div class="mx-1 my-3">
        <span class="mx-2"><i class="fa fa-globe --light mx-2" aria-hidden="true"></i>Global List</span>
        <span class="mx-1"><i class="fa fa-user-plus --light mx-2" aria-hidden="true"></i>Shared By You</span>
        <span class="mx-1"><i class="fa fa-users --light mx-2" aria-hidden="true"></i>Shared With You</span>
      </div>
      <draggable
        class="list-group"
        tag="ul"
        v-model="items"
        v-bind="dragOptions"
        @start="drag = true"
        @end="drag = false">
        <transition-group type="transition" :name="!drag ? 'flip-list' : null">
          <li v-for="l in items" :key="l.id"
              class="list-group-item" @dragend="reordered()">
            <div class="d-flex" >
              <div><i class="fa fa-align-justify handle mx-3"></i></div>
              <div style="min-width:46px">
                 <span class="mx-2" v-if="l.isSharedList && !l.userId"><i class="fa fa-globe --light mx-2" aria-hidden="true"></i></span>
                 <span class="mx-2" v-else-if="l.isSharedList && user && l.userId === user.id"><i class="fa fa-user-plus --light mx-2" aria-hidden="true"></i></span>
                 <span class="mx-1" v-else-if="l.masterListId"><i class="fa fa-users --light mx-2" aria-hidden="true"></i></span>
              </div>
              <div class="pointer" style="flex-grow:1" @click.stop.prevent="launch(l.id)">
                <span class="ml-3 mr-2 linehieght14px">{{l.listName}}</span>
              </div>
              <div style="align-self:center">
                <!--  -->
                <i @click.stop.prevent="deleteList(l)" v-if="l.canUserDelete(user)"
                    class="fas fa-trash fa-align-justify zoomable mx-2  float-right  --blue pointer"
                    v-b-tooltip.hover
                    title="Delete this smart list">
                </i>
                <i  v-else class="fas fa-trash fa-align-justify float-right mx-2 --light"></i>
                <i @click.stop.prevent="editList(l)"  v-if="l.canUserEdit(user)"
                    class="icon icon-pencil fa-align-justify zoomable mx-2  float-right  --blue pointer"
                    v-b-tooltip.hover
                    title="Edit smart list name">
                </i>
                <i v-else class="icon icon-pencil  fa-align-justify float-right mx-2 --light"></i>
                <i @click.stop.prevent="shareList(l)" v-if="l.canUserShare(user)"
                    class="fas fa-share-alt fa-align-justify zoomable mx-2  float-right  --blue pointer"
                    v-b-tooltip.hover
                    title="Share smart list">
                </i>
                <i  v-else class="fas fa-share-alt fa-align-justify float-right mx-2 --light"></i>
                <i @click.stop.prevent="copyList(l)"
                    class="fas fa-clone fa-align-justify zoomable mx-2  float-right  --blue pointer"
                    v-b-tooltip.hover
                    title="Copy smart list">
                </i>
              </div>
            </div>
            </li>
        </transition-group>
      </draggable>

    <SmartListSettingsModal :smartList="selected" :defaultName="emptyListName"
                            :allowCopy='isCopying'
                            v-on:saved="modalSaved"
                            v-on:itemAdded="itemAdded"
                            v-on:closed="modalClosed"
                            :smartListNames="smartListNames"
                            v-if="showSettingsModal"></SmartListSettingsModal>


    <SmartListSharingModal :smartListId="selected.id"
                            v-on:saved="sharingModalSaved"
                            v-on:closed="modalClosed"
                            :smartListNames="smartListNames"
                            v-if="showSharingModal"></SmartListSharingModal>
  </div>

</template>

<script lang="ts">

import Vue from 'vue';
import {FilterOption} from './vm/option_vm'
import draggable from "vuedraggable";
import {SmartList} from '@/models'
import SmartListSettingsModal from './SmartListSettingsModal.vue'
import SmartListSharingModal from './SmartListSharingModal.vue'
import {UxMessage} from '@/util/ux_message'
import { Utils } from '@/util/utils';

/* based on https://sortablejs.github.io/Vue.Draggable/#/transition-example-2 */
/* based on https://github.com/SortableJS/Vue.Draggable/blob/master/example/components/transition-example-2.vue*/

export default Vue.extend({
  name: 'SmartListItems',
  props: ['lists','emptyListName','smartListNames', 'user', 'changeListener'],
  //display: "Transitions",
  inject: ['uxmessage'],
  components: {
    draggable,
    SmartListSettingsModal,
    SmartListSharingModal
  },
  data(){
    return {
      items: [] as any[],
      drag: false,
      showSettingsModal: false,
      showSharingModal: false,
      selected: {} as SmartList,
      showTestingPod: false,
      isCopying: false,
    }
  },
  computed: {
    dragOptions() {
      return {
        animation: 200,
        group: "description",
        disabled: false,
        ghostClass: "ghost"
      };
    }
  },
  watch: {
    lists(items: any[]){
      this.updateItems(items);
    }
  },
  mounted(){
    this.updateItems(this.lists);
  },
  methods: {
    updateItems(items: any[]){
      this.items = [...items];
    },
    reordered(){
      this.items.forEach((element, index) => {
				if (element.displayOrder !== index) {
					element.displayOrder = index;
					element.save();
				}
      });
      this.$emit('sorted');
    },
    shareList(list:SmartList){
      if (!list || !list.filterSpecs || !list.filterSpecs.filters || list.filterSpecs.filters.length <= 0){
        this.uxmessage(UxMessage.infoType('A list without any filters cannot be shared'));;
        return;
      }
      const unallowedFilters = ['assigned'];
      if (list.filterSpecs.filters.filter((a) => unallowedFilters.includes(Utils.safeLC(a.filterName))).length > 0){
        this.uxmessage(UxMessage.infoType(`A list having ${unallowedFilters.join(', or ')} filter cannot be shared`));;
        return;
      }

      //if(this.changeListener) this.changeListener.suspendListen();
      this.selected = list;
      this.showSettingsModal = false;
      this.showSharingModal = true;
      //this.selected = list;
      //this.showSettingsModal = true;
    },
    editList(list:SmartList){
      //if(this.changeListener) this.changeListener.suspendListen();
      this.isCopying = false;
      this.selected = list;
      this.showSettingsModal = true;
      this.showSharingModal = false;
    },
    copyList(list:SmartList){
      if (!list || !list.filterSpecs || !list.filterSpecs.filters || list.filterSpecs.filters.length <= 0){
        this.uxmessage(UxMessage.infoType('A list without any filters cannot be copied'));;
        return;
      }
      if(this.changeListener) this.changeListener.suspendListen();
      this.isCopying = true;
      this.selected = list;
      this.showSettingsModal = true;
      this.showSharingModal = false;
    },
    sharingModalSaved(name:string){
      this.modalSaved(name);
      //this.$emit('listChange');
    },
    modalSaved(name:string){
      this.showSettingsModal = false;
      this.showSharingModal = false;
      this.selected = {} as SmartList;
      this.isCopying = false;
      // this.$emit('listChange',name);
    },
    itemAdded(smartList: SmartList){
      this.$emit('itemAdded',smartList);
    },
    modalClosed(){
      this.selected = {} as SmartList;
      this.showSettingsModal = false;
      this.showSharingModal = false;
      //if(this.changeListener) this.changeListener.resumeListen();
    },
    async launch(n: string){
      this.$emit('navigateTo', n);
    },
    async deleteList(list:SmartList){

      //if (!this.slVM || !this.slVM.listName || this.slVM.listName.trim() === '') return;
      let msg = `Do you want to delete list titled ${list.listName} ?`;
      let func = async (resp:string) => {
            if (resp === 'ok'){
              try {
                //if(this.changeListener) this.changeListener.suspendListen();
                await list.delete();
                let idx = this.items.findIndex(a=> a === list);
                if (idx !== -1) this.items.splice(idx,1);
                this.$emit('itemDeleted',list.id);
                //if(this.changeListener) this.changeListener.resumeListen();
              }
              catch (exp) { console.log(exp)}
              finally{ this.loading = false ;}
            }
            return;
      };
      this.uxmessage(UxMessage.confirmationType(msg,func));
    },
    closePod(){
      this.showTestingPod = false;
    },
    openPod(){
     this.showTestingPod = true;
    }
  },
})
</script>

<style scoped>
.flip-list-move {
  transition: transform 0.5s;
}
.no-move {
  transition: transform 0s;
}
.ghost {
  opacity: 0.5;
  background: rgb(232, 243, 254);
  /* background: #c8ebfb; */
}
.list-group {
  min-height: 20px;
}
.list-group-item .handle {
  cursor: move;
}
/* .list-group-item {
  cursor: pointer;
} */
span.linehieght14px {
  line-height: 14px; /* to keep it same with fa line-height*/
}
</style>
