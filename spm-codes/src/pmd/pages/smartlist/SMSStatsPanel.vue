<!-- prettier-ignore -->
<template>
  <div class="more-info more-detail-animation d-flex w-100" :class="{'justify-content-end': !v2, 'justify-content-center': v2}">

    <template v-if="eventStats && eventStats.count > 0">
      <div class="more-info-data text-center space-apart" :class="{'stats-clickable': !v2}" @click="openSmsStatsDetailModal('total')">
         <div><span><wbr></span></div>
         <div class="h5 my-0 py-0" :class="{'--purple': !v2, '--gray': v2}">
           {{eventStats.count | formatNumberNoDecimal}}
          </div>
        <div class="--dark" v-if="v2">Attempted</div>
        <div class="--dark" v-else>Total</div>
      </div>
      <div class="more-info-data text-center space-apart " :class="{'stats-clickable': !v2}" @click="openSmsStatsDetailModal('delivered')">
        <div>
          <span class="--green h5 " :class="{'stats-clickable': !v2}">{{getPercentage(eventStats.delivered, eventStats.count) + '%'}}</span>
        </div>
        <div  class=""  :class="{'--purple': !v2, '--gray': v2}">{{eventStats.delivered | formatNumberNoDecimal}}</div>
        <div class="--dark" >Delivered </div>
      </div>
      <div class="more-info-data text-center space-apart " :class="{'stats-clickable': !v2}" @click="openSmsStatsDetailModal('clicked')">
        <div><span class="--yellow stats-clickable h5" :class="{'stats-clickable': !v2}">{{getPercentage(eventStats.clicked, eventStats.count) + '%'}}</span></div>
        <div  class=""  :class="{'--purple': !v2, '--gray': v2}">{{eventStats.clicked | formatNumberNoDecimal}}</div>
        <div class="--dark" >
          <span>Clicked</span>
          <span
            style="margin-top: -7px"
            class="input-group-addon mx-1"
            v-b-tooltip.hover
            title="Trigger links (embedded in message) clicked as % of messages sent">
            <i class="fas fa-question-circle"></i>
          </span>
        </div>
      </div>
      <div class="more-info-data text-center space-apart" :class="{'stats-clickable': !v2}" @click="openSmsStatsDetailModal('failed')">
        <div class="--red">
          <span class="h5">{{getPercentage(eventStats.failed, eventStats.count) + '%'}}</span>
        </div>
        <div  class=""  :class="{'--purple': !v2, '--gray': v2}">{{eventStats.failed | formatNumberNoDecimal}}</div>
        <div class="--dark" >Failed</div>
      </div>
    </template>

      <SmsStatsDetailModal
        v-if="showSmsStatsDetailModal"
        :option="filter"
        @closeSmsStatsDetailModal="showSmsStatsDetailModal = !showSmsStatsDetailModal"
        modalTitle="SMS Stats"
        :bulk_req_id="bulkReqId"/>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import SmsStatsDetailModal from '../../components/SmsStatsDetailModal.vue'

export default Vue.extend({
  name: 'SMSStatsPanel',
  props: ['eventStats', 'bulkReqId', "v2"],
  data() {
    return {
      showSmsStatsDetailModal: false,
      filter: 'total',
    }
  },
  components: {
    SmsStatsDetailModal,
  },
  watch: {},
  methods: {
    async openSmsStatsDetailModal(filter: string) {
      if (this.v2) return
      this.filter = filter
      this.showSmsStatsDetailModal = true
    },
    getPercentage(value: number, total: number) {
      return value ? ((value / total) * 100).toFixed(2) : 0
    },
  },
  // async created() {},
  // async mounted() {},
  // async beforeDestroy() {},
})
</script>

<style scoped>
.space-apart {
  margin-right: 45px;
  margin-left: 45px;
}
.fa-phone {
  color: #188bf6;
  margin-right: 10px;
  font-size: 20px;
}
.email-stats__single {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 1rem;
  color: #2a3135;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}

.more-detail-animation {
  max-height: 87px;
  overflow: hidden;
  transition: max-height 0.5s ease-out;
}

.stats-clickable {
  cursor: pointer;
}
</style>
