import { SmartListColumn } from './smart_list_column'
import { CustomField } from '@/models'
import store from '@/store'
import { IColDefinition } from './interfaces'
import { Utils } from '@/util/utils'
import lodash from 'lodash'
import { Contact } from '../../../../models'
import { IContactOpportunity } from '../../../../models/contact'

export class SmartListColumnsVM {
  public static readonly ContactIdColumn = {
    key: 'id',
    value: 'Contact Id',
    order: 1,
    isRequired: false,
    isChecked: false,
    isSortable: true,
    sortName: 'id',
  }
  public static readonly FullNameColumn = {
    key: 'name',
    value: 'Name',
    order: 1,
    isRequired: true,
    exportFormatter: Utils.capitalize,
    isSortable: true,
    sortName: 'contact_name',
  }
  public static readonly CompanyNameColumn = {
    key: 'companyName',
    value: 'Business Name',
    order: 1000,
    isRequired: false,
    isChecked: false,
    isSortable: true,
    exportFormatter: Utils.capitalize,
    sortName: 'company_name',
  } // sorting business name needs some work as it is not keyword type in Elastic
  public static readonly FirstNameColumn = {
    key: 'firstName',
    value: 'First Name',
    order: 1000,
    isRequired: false,
    isChecked: false,
    exportFormatter: Utils.capitalize,
  }
  public static readonly LastNameColumn = {
    key: 'lastName',
    value: 'Last Name',
    order: 1000,
    isRequired: false,
    isChecked: false,
    exportFormatter: Utils.capitalize,
  }
  public static readonly LastActivityColumn = {
    key: 'lastActivity',
    value: 'Last Activity',
    order: 7,
    isChecked: true,
    isSortable: true,
    sortName: 'last_activity',
  }

  public static defaultSorts = ['date_added']

  public static readonly DEFAULT_COLUMNS = [
    /*** very important key name should match contact property name ***/
    SmartListColumnsVM.FullNameColumn,
    SmartListColumnsVM.CompanyNameColumn,
    { key: 'dnd', value: 'DND', order: 2, isChecked: false, isSortable: true },
    {
      key: 'phone',
      value: 'Phone',
      order: 3,
      isChecked: true,
      isSortable: true,
    },
    {
      key: 'email',
      value: 'Email',
      order: 4,
      isChecked: true,
      isSortable: true,
      sortName: 'email',
    },
    SmartListColumnsVM.LastActivityColumn,
    {
      key: 'lastAppointment',
      value: 'Last Appointment',
      order: 8,
      isSortable: true,
      sortName: 'last_appointment',
    },
    { key: 'assignedToDisplay', value: 'Assigned', order: 9 }, // assigned to stores a key so cant give sorting isSortable: true, sortName: 'assigned_to'
    {
      key: 'dateAdded',
      value: 'Created',
      order: 5,
      isSortable: true,
      sortName: 'date_added',
      isChecked: true,
    },
    {
      key: 'dateUpdated',
      value: 'Updated',
      order: 6,
      isSortable: true,
      sortName: 'date_updated',
    },
    { key: 'type', value: 'Type', order: 5 },
    {
      key: 'source',
      value: 'Source',
      order: 5,
      isSortable: true,
      sortName: 'source',
    },
    // {key: 'internalSource', value: 'Origin', order: 17, isSortable: false, isChecked : true},
    {
      key: 'opportunities',
      value: 'Opportunity',
      order: 10,
      exportFormatter: (payload: IContactOpportunity[]) => {
        const opps = Contact.formatOpportunities(payload) || []
        return (
          opps.map(a => `${a.status} ${a.pipeline} ${a.stage}`) || []
        ).join(',')
      },
    },
    { key: 'fullAddressLine', value: 'Address (full)', order: 11 },
    { key: 'addressLine1', value: 'Address (1st Line)', order: 12 },
    { key: 'city', value: 'City', order: 13, isSortable: true },
    { key: 'state', value: 'State', order: 13, isSortable: true },
    {
      key: 'postalCode',
      value: 'Postal Code',
      order: 13,
      isSortable: true,
      sortName: 'postal_code',
    },
    {
      key: 'dateOfBirth',
      value: 'Birth Date',
      order: 14,
      isSortable: true,
      sortName: 'date_of_birth',
    },
    { key: 'tags', value: 'Tags', order: 10, isChecked: true },
    { key: 'activeWorkflowsDisplay', value: 'Workflows Active', order: 15, isChecked: false },
    { key: 'finishedWorkflowsDisplay', value: 'Workflows Finished', order: 16, isChecked: false },
    { key: 'offersDisplay', value: 'Offers', order: 15, isChecked: false },
    { key: 'productsDisplay', value: 'Products', order: 16, isChecked: false },
    { key: 'lastNote', value: 'Last Note', order: 2000 },
  ] as IColDefinition[]

  public static async getColumnOptions(
    locationId: string
  ): Promise<IColDefinition[]> {
    const list = [
      ...SmartListColumnsVM.DEFAULT_COLUMNS.map(a => lodash.cloneDeep(a)),
    ]
    //let fields = await CustomField.getByLocationIdAndType(locationId, 'contact')
    let  fields = await (store.getters['locationCustomFields/getByLocationAndType'])(locationId, 'contact');
    if (fields && fields.length > 0) {
      let i = list.length
      let other_types = fields.filter(
        a => a.dataType && !['TEXTBOX_LIST'].includes(a.dataType)
      )
      other_types.forEach(x => {
        const temp = {
          key: x.id,
          value: `${Utils.cropDisplay(x.name, 30)}`,
          exportName: x.name,
          order: i + 99,
          isCustomField: true,
          isSortable: !['MULTIPLE_OPTIONS', 'CHECKBOX'].includes(x.dataType),
        } as IColDefinition
        list.push(temp)
        i++
      })
      const text_lists = fields.filter(a =>
        ['TEXTBOX_LIST'].includes(a.dataType)
      )
      text_lists.forEach(setting => {
        if (!Array.isArray(setting.picklistOptions)) return
        setting.picklistOptions.forEach(x => {
          const name = `${Utils.cropDisplay(
            setting.name,
            10
          )} - ${Utils.cropDisplay(x.label, 30)}`
          const temp = {
            key: x.id,
            parentCustomFieldId: setting.id,
            value: name,
            exportName: `${x.label} | ${setting.name} - ${x.label}`,
            order: i + 99,
            isCustomField: true,
            isSortable: true,
          } as IColDefinition
          list.push(temp)
          i++
        })
      })
    }

    return Promise.resolve(list.sort((a, b) => (a.order > b.order ? 1 : -1)))
  }

  public static async getInstance(
    locationId: string,
    selectedColumns?: IColDefinition[]
  ) {
    const newItem = new SmartListColumnsVM(locationId, selectedColumns)
    if (selectedColumns && selectedColumns.length > 0) {
      await newItem.someSelected(selectedColumns)
    } else {
      await newItem.defaultSelected()
    }
    return newItem
  }


  public static getInstanceForAllQuery(
    locationId: string,
    selectedColumns?: IColDefinition[]
  ) {
    const newItem = new SmartListColumnsVM(locationId, selectedColumns)
    const list = [
      ...SmartListColumnsVM.DEFAULT_COLUMNS.map(a => lodash.cloneDeep(a)),
    ]
    newItem.options = list.map(
      a =>
        new SmartListColumn(
          newItem,
          a,
          !a.isCustomField && (a.isRequired || a.isChecked) ? true : false
        )
    )
    newItem.allOptionKeys = newItem.options.map(a => a.key)
    newItem.buildView()
    return newItem
  }

  public allOptionKeys: string[] = []
  public options: SmartListColumn[] = []
  public selected: SmartListColumn[] = []
  private locationId: string

  private constructor(locationId: string, selectedColumns?: IColDefinition[]) {
    this.locationId = locationId
  }

  public buildView() {
    // console.log('buildview called')
    let showCols = this.options.filter(a => a.checked === true)
    let hideCols = this.options.filter(a => a.checked !== true)
    showCols = showCols.sort((a, b) => a.order - b.order)
    hideCols = hideCols.sort((a, b) => a.order - b.order)
    // console.log(hideCols);
    // console.log(showCols);
    this.options = lodash.orderBy(
      [...showCols, ...hideCols],
      ['isRequired', 'checked', 'name_lc'],
      ['desc', 'desc', 'asc']
    )
    this.selected = [...showCols]
    // console.log(this.selected);
  }

  public getSelectedColDefinitions(): IColDefinition[] {
    return this.selected ? this.selected.map(a => a.serialize()) : []
  }

  public setSortOrder(sortNames: string[]) {
    if (!this.options || this.options.length <= 0) return
    const items = lodash.uniq([
      ...(sortNames && sortNames.length ? sortNames : []),
      ...SmartListColumnsVM.defaultSorts,
    ])
    let i = 0
    items.forEach(a => {
      const item = this.options.find(c => c.sortName === a)
      if (item) {
        item.sortOrder = ++i
      }
    })
  }

  public removeSort(sortName: string) {
    if (Utils.isEmptyStr(sortName)) return
    const item = this.options.find(c => c.sortName === sortName)
    if (item) {
      item.sortDirection = null
      item.sortOrder = 0
    }
  }

  private async defaultSelected() {
    const items = await SmartListColumnsVM.getColumnOptions(this.locationId)
    this.options = items.map(
      a =>
        new SmartListColumn(
          this,
          a,
          !a.isCustomField && (a.isRequired || a.isChecked) ? true : false
        )
    )
    this.allOptionKeys = this.options.map(a => a.key)
    this.buildView()
  }

  // private toggleSelectAll() {
  //   if (this.options) {
  //      const temp = this.options;
  //      for(let i=0; i < temp.length; i++) {
  //       const a = temp[i];
  //       if (a.checked === true) a.disable();
  //       else a.enable();
  //     }
  //     setTimeout(() => {
  //       this.buildView();
  //     }, 0);
  //  }
  // }

  private async someSelected(slCols: IColDefinition[]) {
    let i = 1
    const items = (await SmartListColumnsVM.getColumnOptions(
      this.locationId
    )) as IColDefinition[]
    items.forEach(x => {
      x.order = i
      const idx = slCols.findIndex(a => a.key === x.key)
      if (idx !== -1) {
        const itm = slCols[idx] as IColDefinition
        if (itm.sortDirection) x.sortDirection = itm.sortDirection
        const sel = new SmartListColumn(this, x, true)
        this.selected.push(sel)
        this.options.push(sel)
      } else {
        this.options.push(new SmartListColumn(this, x, false))
      }
      i++
    })

    this.allOptionKeys = this.options.map(a => a.key)
    this.buildView()
  }
}
