<template>
  <div v-if="schedule">
    <template v-if="!inputDisabled">
      <div v-if="showTitles" class="py-1 text-nowrap">
        <UITextLabel>{{ startOnLabel }}</UITextLabel>
      </div>
      <div class="form-row">
        <div class="form-group col-12">
          <vue-ctk-date-time-picker
            :disabled="inputDisabled"
            :locale="getCountryInfo('locale')"
            v-model="schedule.startOn"
            :noClearButton="true"
            :no-button-now="true"
            :max-date="maxDate"
            minute-interval="1"
            color="#188bf6"
            enable-button-validate
            v-validate="'required'"
            input-class="'form-control date-picker'"
            label=""
            name="date"
            class="border shadow-sm"
          >
          </vue-ctk-date-time-picker>
          <span v-show="errors.has('date')" class="error"
            >Please select a date
          </span>
          <!-- no-button-now -->
        </div>
      </div>
    </template>
    <!-- <div class="d-block"><span v-if="!firstValue" class="--red">* Enter a value</span></div>
    <div><span v-if="!secondValue" class="--red">* Select an option</span></div>
    <div class="my-2" v-if="example" ><label style="font-size:1.1em !important;">{{example}}</label></div> -->
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import {
  Pipeline,
  Stage,
  Status,
  CustomField,
  CustomValue,
  getCountryInfo,
} from '@/models'
import { BulkSchedule } from './server/bulk_schedule'
import { SafeDropDownToggle } from './safe_drop_down_toggle'
const CommonSelector = () => import('./CommonSelector.vue')
import { KeyVal } from './CommonSelector.vue'
import * as moment from 'moment'
export default Vue.extend({
  name: 'AllAtScheduler',
  inject: ['parentValidator'],
  props: {
    modes: { type: Array, default: [] },
    schedule: { type: BulkSchedule },
    startOnLabel: { type: String, default: 'Start On' },
    dripQuantityLabel: { type: String, default: 'Batch Quantity' },
    dripGapLabel: { type: String, default: 'Repeat After' },
    buttonProcesAllLabel: { type: String, default: 'Process all at once' },
    buttonAllAtScheduleLabel: {
      type: String,
      default: 'Process all at schedule',
    },
    buttonProcessScheduleLabel: {
      type: String,
      default: 'Process in drip mode',
    },
  },
  components: {
    CommonSelector,
  },
  data() {
    return {
      getCountryInfo,
      scheduleMode: null as { key: string; display: string } | null,
      delayTypes: [
        new KeyVal('days'),
        new KeyVal('hours'),
        new KeyVal('minutes'),
        new KeyVal('seconds'),
      ],
      weekDays: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      selectedMode: {} as { [key: string]: any } | null,
      showTitles: true,
      //skipDaysToggle: new SafeDropDownToggle(this, 'skipDaysToggle'),
      delayTypeToggle: new SafeDropDownToggle(this, 'delayTypeToggle'),
      dripDelayType: 'days',
      maxDate: '' /*moment().startOf('day').add(720, 'hours').toString() limit check no longer required*/,
    }
  },
  watch: {
    schedule(newVal: BulkSchedule) {},
    dripDelayType: function (val: string) {
      //console.log('change - ' + val);
      this.schedule.dripDelayType = val
    },
  },
  computed: {
    inputDisabled() {
      return this.schedule && this.schedule.dripMode === 'none'
    },
  },
  async mounted() {
    if (this.schedule && !this.schedule.sendDays) this.schedule.sendDays = []

    // const item = this.modes.find(a => a.key === this.schedule.dripMode)
    // if (item) this.scheduleMode = item
  },
  async created() {
    this.$validator = this.parentValidator
  },
  methods: {
    // async addSkipDay(item: KeyVal) {
    //   if (!item) return
    //   if (!this.schedule) return
    //   if (!this.schedule.sendDays) this.schedule.sendDays = []
    //   if (!this.schedule.sendDays.includes(item.value))
    //     this.schedule.sendDays = [...this.schedule.sendDays, item.value]
    // },
    // async removeSkipDay(item: string) {
    //   if (!this.schedule) return
    //   if (!this.schedule.sendDays) return
    //   this.schedule.sendDays = this.schedule.sendDays.filter(a => a !== item)
    // },
    async delayTypeChange(event: KeyVal) {
      this.dripDelayType = event.value
    },
    async delayTypeChange2(val: string) {
      this.delayTypeToggle.toggle()
      this.dripDelayType = val
    },
  },
})
</script>
<style lang="scss" scoped>
.header-underline {
  display: inline-block;
  border-bottom: 1px solid #d5dde1;
  list-style: none;
  width: 100% !important;
  margin-bottom: 20px;
}
.customized-header {
  display: inline-block;
  list-style: none;
  padding: 0;
  width: 100% !important;
  padding-right: 15px;
  padding-left: 15px;
  margin: 0px;
}
</style>
