<template>
  <div class="style-wrap">
    <div class="option-card">
      <div class="option-card-header">
        <h3 v-if="formAction.source=='survey'">Survey name</h3>
        <h3 v-else>Form name</h3>
      </div>
      <div class="option-card-body">
        <div class="form-group">
          <!-- <small v-if="formAction.source=='survey'">{{formAction.formName}}</small> -->
          <UITextInputGroup type="text" v-model="formAction.formName"/>
        </div>
      </div>
    </div>
    <div class="option-card">
      <div class="option-card-header">
        <h3>On Submit</h3>
      </div>
      <div class="option-card-body">
        <div class="form-group">
          <select class="selectpicker" @change="alterFormAction($event)">
            <option :selected="formAction.actionType == '1'" value="1">Open URL</option>
            <option :selected="formAction.actionType == '2'" value="2">Message</option>
          </select>
        </div>
        <div class="form-group">
          <UITextInputGroup
            v-if="formAction.actionType == 1"
            type="text"
            v-model="formAction.redirectURL"
            @change="$bus.$emit('field-setting-form-advance',{'type': 'redirect', 'value': formAction.redirectURL})"
            placeholder="Enter URL Here"
          />
          <UITextInputGroup
            v-if="formAction.actionType == 2"
            type="text"
            v-model="formAction.thankyouText"
            @change="$bus.$emit('field-setting-form-advance',{'type': 'thankyou', 'value': formAction.thankyouText})"
            placeholder="Enter Thankyou Message"
          />
        </div>
      </div>
    </div>

    <div class="option-card" v-if="formAction.source=='survey'">
      <div class="option-card-header">
        <h3>Disqualify immediately</h3>
      </div>
      <div class="option-card-body">
        <div class="form-group">
          <select class="selectpicker" @change="alterDisQualifyAction($event)">
            <option :selected="formAction.disqualifiedType == '1'" value="1">URL</option>
            <option :selected="formAction.disqualifiedType == '2'" value="2">Message</option>
          </select>
        </div>
        <div class="form-group">
          <UITextInputGroup
            v-if="formAction.disqualifiedType == 1"
            type="text"
            v-model="formAction.disqualifiedUrl"
            @change="$bus.$emit('field-setting-form-advance',{'type': 'dis-qualified-url', 'value': formAction.disqualifiedUrl})"
            placeholder="Url Goes Here"
          />
          <UITextInputGroup
            v-if="formAction.disqualifiedType == 2"
            type="text"
            v-model="formAction.disqualifiedText"
            @change="$bus.$emit('field-setting-form-advance',{'type': 'dis-qualified-text', 'value': formAction.disqualifiedText})"
            placeholder="Message Goes Here"
          />
        </div>
      </div>
    </div>

    <div class="option-card" v-if="formAction.source=='survey'">
      <div class="option-card-header">
        <h3>Disqualify after submit</h3>
      </div>
      <div class="option-card-body">
        <div class="form-group">
          <select class="selectpicker" @change="alterEndSurveyAction($event)">
            <option :selected="formAction.endsurveyType == '1'" value="1">URL</option>
            <option :selected="formAction.endsurveyType == '2'" value="2">Message</option>
          </select>
        </div>
        <div class="form-group">
          <UITextInputGroup
            v-if="formAction.endsurveyType == 1"
            type="text"
            v-model="formAction.endsurveyUrl"
            @change="$bus.$emit('field-setting-form-advance',{'type': 'end-survey-url', 'value': formAction.endsurveyUrl})"
            placeholder="Url Goes Here"
          />
          <UITextInputGroup
            v-if="formAction.endsurveyType == 2"
            type="text"
            v-model="formAction.endsurveyText"
            @change="$bus.$emit('field-setting-form-advance',{'type': 'end-survey-text', 'value': formAction.endsurveyText})"
            placeholder="Message Goes Here"
          />
        </div>
      </div>
    </div>

    <div class="option-card">
      <div class="option-card-header">
        <h3>Facebook Pixel ID</h3>
      </div>
      <div class="option-card-body">
        <div class="form-group">
          <UITextInputGroup
            type="text"
            v-model="formAction.fbPixelId"
            @change="$bus.$emit('field-setting-form-advance',{'type': 'fb-pixel-id', 'value': formAction.fbPixelId})"
            placeholder="Enter Facebook Pixel ID"
          />
        </div>

      <div id="pixel-helper">
        <p class="text-muted">Ignore this field if you plan to use this form/survey inside a funnel</p>
      </div>
      </div>
    </div>

    <div class="option-card">
      <div class="option-card-header">
        <h3>Facebook Pixel Events</h3>
      </div>
      <div class="option-card-body">
        <div class="form-group">
      <label for="sel1">On Page View</label>
      <select style="width:100%" v-model="formAction.pageViewEvent"   id="sel1" @change="$bus.$emit('field-setting-form-advance',{'type': 'fb-pixel-page-view-event', 'value': formAction.pageViewEvent})" >
        <option v-for="(event,index) in fbEvents" :key="index"> {{event}} </option>
      </select>
        </div>

        <div class="form-group">
      <label for="sel2">On Form Submission</label>
      <select style="width:100%" v-model="formAction.formSubmissionEvent"  id="sel2"  @change="$bus.$emit('field-setting-form-advance',{'type': 'fb-pixel-form-submission-event', 'value': formAction.formSubmissionEvent})">
        <option v-for="(event,index) in fbEvents" :key="index"> {{event}} </option>
      </select>
        </div>

      </div>
    </div>

    <!-- <div class="option-card">
        <div class="option-card-header">
          <h3>Slide name</h3>
        </div>
        <div class="option-card-body">
          <div class="form-group">
            <input class="form-control" type="text" v-model="formAction.formName">
          </div>
        </div>
    </div>-->

    <div class="option-card" v-if="formAction.source=='survey'">
      <div class="option-card-header">
        <h3>Survey fields settings</h3>
      </div>
      <div class="option-card-body">
        <div class="d-flex justify-content-between">
          <p>One question at a time</p>
          <div class="toggle">
            <UIToggle
              id="fpp-toggle"
              :value="formAction.fieldSettingEnable"
              @input="changePageSettings"
            />
            <label class="tgl-btn" for="fpp-toggle"></label>
          </div>
        </div>
        <br>
      </div>
    </div>

    <div class="option-card-body">
      <div class="d-flex justify-content-between" style="margin-bottom:10px;">
        <p>
          Sticky Contact
          <span
            style="margin-left: 5px;"
            class="input-group-addon"
            v-b-tooltip.hover
            title="Sticky contact will autofill contact information previously entered so you don't have to enter it twice."
          >
            <i class="fas fa-question-circle"></i>
          </span>
        </p>
        <div class="toggle">
          <UIToggle
            id="sticky-toggle"
            :value="formAction.stickyContact"
            @input="alterStickyContact"
          />
          <label class="tgl-btn" for="sticky-toggle"></label>
        </div>
      </div>
    </div>

    <div class="option-card-body" v-if="formAction.source=='survey'">
      <div class="d-flex justify-content-between" style="margin-bottom:10px;">
        <p>
          Back Button
          <span
            style="margin-left: 5px;"
            class="input-group-addon"
            v-b-tooltip.hover
            title="Back button will help user to move the previous slide."
          >
            <i class="fas fa-question-circle"></i>
          </span>
        </p>
        <div class="toggle">
          <UIToggle
            id="back-button-toggle"
            :value="formAction.isBackButtonEnable"
            @input="alterBackButton"
          />
          <label class="tgl-btn" for="back-button-toggle"></label>
        </div>
      </div>
    </div>

    <div class="option-card-body" v-if="formAction.source=='survey'">
      <div class="d-flex justify-content-between" style="margin-bottom:10px;">
        <p>
          Disable Auto Navigation
          <span
            style="margin-left: 5px;"
            class="input-group-addon"
            v-b-tooltip.hover
            title="If turned on, The survey will not automatically go to the next slide"
          >
            <i class="fas fa-question-circle"></i>
          </span>
        </p>
        <div class="toggle">
          <UIToggle
            id="auto-nav-toggle"
            :value="formAction.disableAutoNavigation"
            @input="alterAutoNavigation"
          />
          <label class="tgl-btn" for="auto-nav-toggle"></label>
        </div>
      </div>
    </div>




    <div class="option-card-body" v-if="formAction.source=='survey'">
      <div class="d-flex justify-content-between" style="margin-bottom:10px;">
        <p>
          Progress Bar
          <span
            style="margin-left: 5px;"
            class="input-group-addon"
            v-b-tooltip.hover
            title="Turning this Off will remove the Progress Bar from the survey"
          >
            <i class="fas fa-question-circle"></i>
          </span>
        </p>
        <div class="toggle">
          <UIToggle
            id="progress-button-toggle"
            :value="formAction.isProgressBarEnabled"
            @input="alterProgressButton"
          />
          <label class="tgl-btn" for="progress-button-toggle"></label>
        </div>
      </div>
    </div>

    <div class="option-card-body" v-if="formAction.source=='survey'">
      <div class="d-flex justify-content-between" style="margin-bottom:10px;">
        <p>
          Disable Animation
          <span
            style="margin-left: 5px;"
            class="input-group-addon"
            v-b-tooltip.hover
            title="Turning this on will disable the slide transition animation"
          >
            <i class="fas fa-question-circle"></i>
          </span>
        </p>
        <div class="toggle">
          <UIToggle
            id="animation-button-toggle"
            :value="formAction.isAnimationDisabled"
            @input="alterAnimationButton"
          />
          <label class="tgl-btn" for="animation-button-toggle"></label>
        </div>
      </div>
    </div>

    <div class="option-card-body" v-if="formAction.source=='survey'">
      <div class="d-flex justify-content-between" style="margin-bottom:10px;">
        <p>
          Scroll to Top
          <span
            style="margin-left: 5px;"
            class="input-group-addon"
            v-b-tooltip.hover
            title="If turned on, the survey will scroll to top on the next slide"
          >
            <i class="fas fa-question-circle"></i>
          </span>
        </p>
        <UIToggle
          :value="formAction.isSurveyScrollEnabled"
          id="scroll-survey-button-toggle"
          @change="alterSurveyScrollButton"
         />
      </div>
    </div>



    <!--Banner Section-->
    <div class="option-card" v-if="formAction.source=='survey'">
      <div class="option-card-header position-relative">
        <div >
            <h3>Banner Image</h3>
            <moon-loader class="mt-2" color="#188bf6" size="20px" v-if="loading"/>
        </div>
      </div>
      <div class="option-card-body">
        <div class="form-group">
          <input
            :style="formAction.headerImageSrc.trim().length > 0 || isBannerFileInputVisible == false ? 'display:none;' : ''"
            ref= 'fileInput'
            type="file"
            class="form-control-bg-image"
            placeholder="Image"
            name="banner-image"
            @change="onFileChange"
            style="width:100%;"
            accept="image/*"
          >
          <img
            :src="(filesAdded.length > 0) ? filesAdded[0].url : formAction.headerImageSrc"
            style="width:100%;margin-top:5px;"
            @click="fetchImage"
          />
          <div style="text-align:right;">
            <a class="btn btn-link" @click="removeField" v-if="formAction.headerImageSrc">
              Remove image
            </a>
          </div>
          <div class="d-flex justify-content-between" style="margin-bottom:5px; margin-top:15px;">
            <p>Full width</p>
            <div class="toggle">
              <UIToggle
                id="full-width-toggle"
                :value="formAction.headerFullWidthEnable"
                @input="bannerFullWidth"
              />
              <label class="tgl-btn" for="full-width-toggle"></label>
             </div>
           </div>
        </div>
      </div>
    </div>

    <!--Footer HTML-->
    <div class="option-card" v-if="formAction.source=='survey'">
      <div class="option-card-header">
        <h3>Footer HTML</h3>
      </div>
      <div class="option-card-body">
        <div class="form-group">
          <small style="line-height:20px;display:block;margin-bottom:10px;">
            You can write custom CSS (using
            <b>style</b> tag) and HTML inside the same box
          </small>
          <button
            type="button"
            class="btn btn-warning btn-block"
            @click="editFooterHtml"
          >Edit Footer</button>
          <!-- <button @click="editFooterHtml">Edit Html</button> -->
          <!-- <textarea
              class="form-control"
              rows="4"
              v-model="formAction.footerHtml"
              @change="$bus.$emit('field-setting-form-advance',{'type': 'footer-html', 'value': formAction.footerHtml})"
          ></textarea>-->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { FileAttachment } from "@/store/state_models";
import firebase from "firebase/app";
import ImageTools from "../../../util/image_tools";
import { v4 as uuid } from "uuid";
const imageTools = new ImageTools();
const path = require('path');

export default Vue.extend({
  props: {
    optFormAction: {} as any,
    formStyle : {} as any
  },
  components: {},
  data() {
    return {
      formAction: this.optFormAction,
      modalVisible: false,
      loading: false,
      filesAdded: [] as FileAttachment[],
      currentLocationId: '',
      builderId: '',
      isBannerFileInputVisible: true,
      fbEvents: [
      "AddPaymentInfo",
      "AddToCart",
      "AddToWishlist",
      "CompleteRegistration",
      "Contact",
      "CustomizeProduct",
      "Donate",
      "FindLocation",
      "InitiateCheckout",
      "Lead",
      "PageView",
      "Purchase",
      "Schedule",
      "Search",
      "StartTrial",
      "SubmitApplication",
      "Subscribe",
      "ViewContent",
      "None"
    ]
    }
  },
  computed: {},
  watch: {
    'formAction.formName'(newVal) {
      this.$bus.$emit('field-setting-form-advance', {
        type: 'form',
        value: newVal
      })
    }
  },
  updated() {},
  mounted() {},
  created(){
    this.currentLocationId = this.$router.currentRoute.params.location_id;
    this.builderId = this.$router.currentRoute.params.survey_id;
  },
  methods: {
    alterFormAction(event: any) {
      this.formAction.actionType = event.target.value
      this.$bus.$emit('field-setting-form-advance', {
        type: 'action',
        value: this.formAction.actionType
      })
    },
    changePageSettings() {
      this.formAction.fieldSettingEnable = !this.formAction.fieldSettingEnable
      this.$bus.$emit('field-setting-form-advance', {
        type: 'field-setting-enable',
        value: this.formAction.fieldSettingEnable
      })
    },
    alterDisQualifyAction(event: any) {
      this.formAction.disqualifiedType = event.target.value
      this.$bus.$emit('field-setting-form-advance', {
        type: 'dis-qualified-type',
        value: this.formAction.disqualifiedType
      })
    },
    alterEndSurveyAction(event: any) {
      this.formAction.endsurveyType = event.target.value
      this.$bus.$emit('field-setting-form-advance', {
        type: 'end-survey-type',
        value: this.formAction.endsurveyType
      })
    },
    alterStickyContact() {
      this.formAction.stickyContact = !this.formAction.stickyContact
      this.$bus.$emit('field-setting-form-advance', {
        type: 'sticky-contact',
        value: this.formAction.stickyContact
      })
    },
    alterBackButton() {
      this.formAction.isBackButtonEnable = !this.formAction.isBackButtonEnable
      this.$bus.$emit('field-setting-form-advance', {
        type: 'back-buttion',
        value: this.formAction.isBackButtonEnable
      })
    },
    alterAutoNavigation() {
      this.formAction.disableAutoNavigation = !this.formAction.disableAutoNavigation
      this.$bus.$emit('field-setting-form-advance', {
        type: 'auto-navigation',
        value: this.formAction.disableAutoNavigation
      })
    },
    alterProgressButton() {
      this.formAction.isProgressBarEnabled = !this.formAction.isProgressBarEnabled
      this.$bus.$emit('field-setting-form-advance', {
        type: 'progress-button',
        value: this.formAction.isProgressBarEnabled
      })
    },
 alterAnimationButton() {
      this.formAction.isAnimationDisabled = !this.formAction.isAnimationDisabled
      this.$bus.$emit('field-setting-form-advance', {
        type: 'disable-animation',
        value: this.formAction.isAnimationDisabled
      })
    },
     alterSurveyScrollButton() {
      this.formAction.isSurveyScrollEnabled = !this.formAction.isSurveyScrollEnabled
      this.$bus.$emit('field-setting-form-advance', {
        type: 'enabled-survey-scroll',
        value: this.formAction.isSurveyScrollEnabled
      })
    },
    bannerFullWidth() {
     this.formAction.headerFullWidthEnable = !this.formAction.headerFullWidthEnable
      this.$bus.$emit('field-setting-form-advance', {
        type: 'full-width',
        value: this.formAction.headerFullWidthEnable
      })
    },
    editFooterHtml() {
      this.$bus.$emit('showFooterHtmlModal', this.formAction.footerHtml)
    },
    async onFileChange(e: any) {
      this.filesAdded = [] as FileAttachment[]; //clear first and then upload
			const element = <HTMLInputElement>e.target;
			if (!element.files) return;
			for (let i = 0; i < element.files.length; i++) {
				this.vfileAdded(element.files[i]);
			}
			element.files = null;
		},
		async vfileAdded(file: File) {
			const response = <File | Blob>(
				await imageTools.resize(file, { height: 1000, width: 1000 })
			);
			this.filesAdded.push({
				name: file.name,
				type: file.type,
				url: URL.createObjectURL(response),
				data: response
      });
      this.send();
		},
		send: async function () {
      this.loading= true;
      let el = this;
			const urls = [] as string[];
			if (this.filesAdded.length > 0) {
				const basePath =
					"location/" +
					this.currentLocationId +
					"/form/" +
					this.builderId +
					"/header-image/";
				const newURLs: string[] = await Promise.all(
					this.filesAdded.map(async attachment => {
						let imagePath = basePath + uuid() + path.extname(attachment.name);
						var uploadPath = firebase.storage().ref(imagePath);
						const snapshot = await uploadPath.put(attachment.data, {
              contentType: attachment.type,
              contentDisposition: `inline; filename="${attachment.name}"`,
							customMetadata: { name: attachment.name }
						});
						return await snapshot.ref.getDownloadURL();
					})
				);
        urls.push.apply(urls, newURLs.filter(url => url));
        if(urls.length > 0){
          this.isBannerFileInputVisible = false;
          this.$bus.$emit('field-setting-form-advance', {
            type: "header-img",
            value: urls[0]
          });
        }
      }
      this.loading = false;
    },
    removeField(){
     this.formAction.headerFullWidthEnable ='';
      this.filesAdded[0]='';
      this.$refs.fileInput.value = null;
      this.isBannerFileInputVisible = true;
      this.$bus.$emit('field-setting-form-advance', {
            type: "header-img-delete"
          });
    },
    fetchImage(){
      this.$refs.fileInput.click();
    }
  }
})
</script>

<style scoped>

#pixel-helper{
  font-size: 11px;
}

</style>
