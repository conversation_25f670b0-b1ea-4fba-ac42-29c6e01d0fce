<template>
  <div id="invites-goal" class="card">
    <div class="card-header card-header--compact space-between">
      <h2>Invites Goal</h2>
      <div>
        <span>{{ filter }}</span>
        <select class="selectpicker more-select" v-model="filter">
          <option>This Week</option>
          <option>Last Week</option>
          <option>This Month</option>
          <option>Last 6 Months</option>
          <option>This Year</option>
        </select>
      </div>
    </div>
    <div class="card-body">
      <div class="invites-goal_progress">
        <div class="semi-progress" data-progress="45">
          <div class="bar-wrap">
            <div class="bar" :style="barStyle"></div>
          </div>
        </div>
        <h3>{{ currentReviewRequestsAggregate.totalRequests }}</h3>
        <h4 v-if="currentReviewRequestsAggregate.totalRequests !== 0">
          You are on Track!
        </h4>
        <p>Your current goal is to send {{ reviewsGoal }} review requests!</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { ReviewRequestAggregateData } from '@/store/state_models'
import UserAvatar from '../../UserAvatar.vue'

declare var $: any

export default Vue.extend({
  components: { UserAvatar },
  data() {
    return {
      filter: 'Last 6 Months',
      currentLocationId: '',
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.filter = 'Last 6 Months'
    },
    filter: function () {
      console.log('Filter executed form arc')
      if (this.filter === 'This Week') {
        this.$store.dispatch(
          'reviewRequestAggregate/fetchThisWeek',
          this.currentLocationId
        )
      } else if (this.filter === 'Last Week') {
        this.$store.dispatch(
          'reviewRequestAggregate/fetchLastWeek',
          this.currentLocationId
        )
      } else if (this.filter === 'This Month') {
        this.$store.dispatch(
          'reviewRequestAggregate/fetchThisMonth',
          this.currentLocationId
        )
      } else if (this.filter === 'Last 6 Months') {
        this.$store.dispatch(
          'reviewRequestAggregate/fetchLast6Months',
          this.currentLocationId
        )
      } else if (this.filter === 'This Year') {
        this.$store.dispatch(
          'reviewRequestAggregate/fetchThisYear',
          this.currentLocationId
        )
      }
    },
  },
  computed: {
    currentReviewRequestsAggregate() : ReviewRequestAggregateData{
      return this.$store.getters['reviewRequestAggregate/current'](this.filter)
    },
    percent(): number {
      return (
        (this.currentReviewRequestsAggregate.totalRequests / this.reviewsGoal) *
        100
      )
    },
    barStyle(): string {
      return 'transform: rotate(' + (45 + 1.8 * this.percent) + 'deg)'
    },
    reviewsGoal(): number {
      if (this.currentReviewRequestsAggregate.totalRequests < 20) {
        return 20
      } else if (this.currentReviewRequestsAggregate.totalRequests < 30) {
        return 30
      } else if (this.currentReviewRequestsAggregate.totalRequests < 50) {
        return 50
      } else if (this.currentReviewRequestsAggregate.totalRequests < 70) {
        return 70
      } else if (this.currentReviewRequestsAggregate.totalRequests < 90) {
        return 90
      } else if (this.currentReviewRequestsAggregate.totalRequests < 1000) {
        let reached = this.currentReviewRequestsAggregate.totalRequests % 100
        let lastHundred =
          this.currentReviewRequestsAggregate.totalRequests - reached
        return lastHundred + 100
      } else {
        let reached = this.currentReviewRequestsAggregate.totalRequests % 100 as number
        let lastHundred = this.currentReviewRequestsAggregate.totalRequests - reached as number
        let inThousands =  Math.floor(lastHundred/1000);
        return lastHundred + (inThousands*100)
      }
    },
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
})
</script>
<style lang="scss" scoped>
#invites-goal {
  .space-between {
    display: flex;
    justify-content: space-between;
  }

  .card-header--compact {
    padding: 12px 50px 10px 30px;
  }

  .card-header {
    span {
      color: #afb8bc;
    }
    .more-select {
      top: 53%;
      transform: translateY(-53%);
    }
  }

  .card-body {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .invites-goal_progress {
    width: 100%;
    max-width: 300px;
    text-align: center;
    .semi-progress {
      position: relative;
      .bar-wrap {
        position: relative;
        overflow: hidden;
        width: 250px;
        height: 100px;
        margin-left: auto;
        margin-right: auto;
      }

      .bar {
        position: absolute;
        top: 0;
        left: 0;
        width: 250px;
        height: 250px;
        border-radius: 50%;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        border: 10px solid #e8e8e8;
        border-bottom-color: #188bf6;
        border-right-color: #188bf6;
      }
    }

    h3 {
      font-size: 2.5rem;
      font-weight: 500;
      position: relative;
      margin-top: -32px;

      &:before {
        content: '';
        display: block;
        width: 32px;
        height: 27px;
        background: url(/pmd/img/icon-send.svg) no-repeat center;
        background-size: 32px 32px;
        position: absolute;
        margin: auto;
        top: -40px;
        left: 0;
        right: 0;
        transform: rotate(324deg);
      }
    }
  }
}

.invites-goal_progress {
  width: 100%;
  max-width: 300px;
  text-align: center;
  .semi-progress {
    position: relative;
    .bar-wrap {
      position: relative;
      overflow: hidden;
      width: 250px;
      height: 100px;
      margin-left: auto;
      margin-right: auto;
    }

    .bar {
      position: absolute;
      top: 0;
      left: 0;
      width: 250px;
      height: 250px;
      border-radius: 50%;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      border: 10px solid #f2f7fa;
      border-bottom-color: #188bf6;
      border-right-color: #188bf6;
    }
  }

  h3 {
    font-size: 2.5rem;
    font-weight: 500;
    position: relative;
    margin-top: -32px;

    &:before {
      content: '';
      display: block;
      width: 32px;
      height: 27px;
      background: url(/pmd/img/icon-send.svg) no-repeat center;
      background-size: 32px 32px;
      position: absolute;
      margin: auto;
      top: -40px;
      left: 0;
      right: 0;
      transform: rotate(324deg);
    }
  }
  p {
    color: #afb8bc;
    font-size: 0.8rem;
  }
}
</style>


