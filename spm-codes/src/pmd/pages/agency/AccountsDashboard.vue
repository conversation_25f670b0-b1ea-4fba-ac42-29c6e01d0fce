<template>
    <div>

        <SideBar/>
        <TopBar />

        <section class="hl_wrapper">
            <section class="hl_wrapper--inner hl_agency hl_agency-dashboard" id="agency_dashboard">
                <div class="container-fluid">
                    <div class="hl_controls">
                        <div class="hl_controls--left">
                            <h1>Dashboard</h1>
                        </div>
                    </div>
                    <div class="card-group hl_agency-dashboard--cards --wider-gutter">
                        <div class="card">
                            <CardPinIcon class="card-bg" />
                            <div class="card-body">
                                <div class="icon">
                                    <PinIcon />
                                </div>
                                <h4>Total locations</h4>
                                <p>{{locations.length}}</p>
                            </div>
                        </div>
                        <div class="card">
                            <CardFireIcon class="card-bg" />
                            <div class="card-body">
                                <div class="icon">
                                    <FireIcon />
                                </div>
                                <h4>Hot leads</h4>
                                <p>{{total_hot_leads}}</p>
                            </div>
                        </div>
                        <div class="card">
                            <CardMoneyIcon class="card-bg" />
                            <div class="card-body">
                                <div class="icon">
                                    <MoneyIcon />
                                </div>
                                <h4>Revenue per month</h4>
                                <p>${{total_revenue|formatNumber}}</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-group">
                        <div class="card hl_agency-dashboard--table">
                            <div class="card-body --no-padding">
                                <div class="table-wrap">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th>Locations</th>
                                                <th>Revenue</th>
                                                <!-- <th>
                                                    
                                                </th> -->
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>
                                                    <div class="table_a-product">
                                                        <div class="icon --green">
                                                            <ListingIcon />
                                                        </div>
                                                        <p>Listings</p>
                                                    </div>
                                                </td>
                                                <td>
                                                    {{total_listings}}
                                                </td>
                                                <td>
                                                    <div class="table_a-progress">
                                                        <p>${{listing_revenue|formatNumber}}</p>
                                                        <div class="progress --green">
                                                            <div class="progress-bar" role="progressbar" :style="'width: ' + listings_percent + '%'"></div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <!-- <td>
                                                    <select class="selectpicker more-select">
                                                        <option>Option 1</option>
                                                        <option>Option 2</option>
                                                        <option>Option 3</option>
                                                    </select>
                                                </td> -->
                                            </tr>
                                            <tr>
                                                <td>
                                                    <div class="table_a-product">
                                                        <div class="icon --yellow">
                                                            <StarIcon />
                                                        </div>
                                                        <p>Reputation management</p>
                                                    </div>
                                                </td>
                                                <td>
                                                    {{total_reviews}}
                                                </td>
                                                <td>
                                                    <div class="table_a-progress">
                                                        <p>${{reviews_revenue|formatNumber}}</p>
                                                        <div class="progress --green">
                                                            <div class="progress-bar" role="progressbar" :style="'width: ' + reviews_percent + '%'"></div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <!-- <td>
                                                    <select class="selectpicker more-select">
                                                        <option>Option 1</option>
                                                        <option>Option 2</option>
                                                        <option>Option 3</option>
                                                    </select>
                                                </td> -->
                                            </tr>
                                            <tr>
                                                <td>
                                                    <div class="table_a-product">
                                                        <div class="icon --blue">
                                                            <ConversationIcon />
                                                        </div>
                                                        <p>Conversations</p>
                                                    </div>
                                                </td>
                                                <td>
                                                    {{total_conversations}}
                                                </td>
                                                <td>
                                                    <div class="table_a-progress">
                                                        <p>${{conversations_revenue|formatNumber}}</p>
                                                        <div class="progress --green">
                                                            <div class="progress-bar" role="progressbar" :style="'width: ' + conversations_percent+ '%'"></div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <!-- <td>
                                                    <select class="selectpicker more-select">
                                                        <option>Option 1</option>
                                                        <option>Option 2</option>
                                                        <option>Option 3</option>
                                                    </select>
                                                </td> -->
                                            </tr>
                                            <!-- <tr>
                                                <td>
                                                    <div class="table_a-product">
                                                        <div class="icon --pink">
                                                            <img src="../../../assets/pmd/img/icon-social.svg">
                                                        </div>
                                                        <p>Social media</p>
                                                    </div>
                                                </td>
                                                <td>
                                                    {{total_social}}
                                                </td>
                                                <td>
                                                    <div class="table_a-progress">
                                                        <p>${{social_revenue|formatNumber}}</p>
                                                        <div class="progress --green">
                                                            <div class="progress-bar" role="progressbar" :style="'width: ' + social_percent+ '%'"></div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <select class="selectpicker more-select">
                                                        <option>Option 1</option>
                                                        <option>Option 2</option>
                                                        <option>Option 3</option>
                                                    </select>
                                                </td>
                                            </tr> -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-group">
                        <TaskListComponent/>
                    </div>
                </div>
            </section>
            <!-- END of .hl_agency-dashboard -->
        </section>

    </div>
</template>

<script lang="ts">
import Vue from 'vue';
import { Location, LocationStatus } from '@/models';
import TopBar from '../../components/TopBar.vue';
import SideBar from '../../components/agency/SideBar.vue';
import CardPinIcon from '@/assets/pmd/img/icon-card-pin.svg';
import PinIcon from '@/assets/pmd/img/icon-pin.svg';
import CardFireIcon from '@/assets/pmd/img/icon-card-fire.svg';
import FireIcon from '@/assets/pmd/img/icon-fire.svg';
import CardMoneyIcon from '@/assets/pmd/img/icon-card-money.svg';
import MoneyIcon from '@/assets/pmd/img/icon-money.svg';
import ListingIcon from '@/assets/pmd/img/icon-listing.svg'
import StarIcon from '@/assets/pmd/img/icon-star.svg';
import ConversationIcon from '@/assets/pmd/img/icon-conversation.svg';

const TaskListComponent = () => import( '@/pmd/components/agency/TaskListComponent.vue');

let unsubscribeLocations: () => void;

export default Vue.extend({
    components: {
        TopBar,
        SideBar,
        TaskListComponent,
        CardPinIcon,
        PinIcon,
        CardFireIcon,
        FireIcon,
        CardMoneyIcon,
        MoneyIcon,
        ListingIcon,
        StarIcon,
        ConversationIcon,
    },
    data() {
        return {
            locations: [] as Location[]
        }
    },
    async created() {
        unsubscribeLocations = (await Location.getByCompanyIdRealtime()).onSnapshot(snapshot => {
            this.locations = snapshot.docs.map(d => new Location(d));
        });
    },
    computed: {
        total_listings(): number {
            return lodash.filter(this.locations, { productStatus: {listings: true}}).length;
        },
        total_reviews(): number {
            return lodash.filter(this.locations, { productStatus: {reviews: true}}).length;
        },
        total_social(): number {
            return lodash.filter(this.locations, { productStatus: {social: true}}).length;
        },
        total_conversations(): number {
            return lodash.filter(this.locations, { productStatus: {conversations: true}}).length;
        },
        total_hot_leads(): number {
            return lodash.filter(this.locations, { status: LocationStatus.PROSPECT }).length
        },
        listing_revenue(): number {
            return this.total_listings * 100;
        },
        reviews_revenue(): number {
            return this.total_reviews * 300;
        },
        social_revenue(): number {
            return this.total_social * 50;
        },
        conversations_revenue(): number {
            return this.total_conversations * 200;
        },
        total_revenue(): number {
            return this.listing_revenue + this.reviews_revenue + this.social_revenue + this.conversations_revenue;
        },
        listings_percent(): string {
            return ((this.listing_revenue / this.total_revenue) * 100).toFixed(0)
        },
        reviews_percent(): string {
            return ((this.reviews_revenue / this.total_revenue) * 100).toFixed(0)
        },
        social_percent(): string {
            return ((this.social_revenue / this.total_revenue) * 100).toFixed(0)
        },
        conversations_percent(): string {
            return ((this.conversations_revenue / this.total_revenue) * 100).toFixed(0)
        }
    },
    beforeDestroy() {
        if (unsubscribeLocations)
            unsubscribeLocations();
    }
});
</script>