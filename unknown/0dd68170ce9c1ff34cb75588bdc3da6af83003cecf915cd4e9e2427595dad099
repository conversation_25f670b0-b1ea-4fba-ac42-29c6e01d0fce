<template>
  <div
    class="hl_opportunities--set"
    :class="{ 'is-loading': isLoading }"
    :id="stage ? stage.id : ''"
  >
    <div class="hl_opportunities--set-header">
      <h4>{{ stage.name }}</h4>
      <p>
        <strong
          >{{
            stageData && stageData.totalCount ? stageData.totalCount : 0
          }}
          Leads</strong
        >
        <span v-if="user && user.permissions.lead_value_enabled !== false"
          >{{ location.country | symbole
          }}{{ (stageData ? stageData.revenues : 0) | formatNumber }}</span
        >
      </p>
    </div>

    <ul
      :ref="stage.id"
      :style="{ height: (isLoading ? height - 30 : height) + 'px' }"
    >
      <Container
        :group-name="'opps'"
        @drop="onDrop"
        :get-child-payload="getChildPayload"
        @drag-start="dragStart"
        @drag-end="dragEnd"
        drag-class="card-ghost"
        drop-class="card-ghost-drop"
        :drop-placeholder="dropPlaceholderOptions"
        :ref="stage.id + 'container'"
      >
        <Draggable
          v-for="opportunity in xOpp"
          :key="opportunity.id"
          class="opportunity-card-block"
        >
          <OpportunityCard
            :columns="filters.columns"
            :searchText="filters.text"
            :color="color"
            :id="opportunity.id"
            :opportunity="opportunity"
            @click="a => $emit('click', a)"
            @clickNotes="a => $emit('clickNotes', a)"
            @clickAppointment="a => $emit('clickAppointment', a)"
            @clickTask="a => $emit('clickTask', a)"
          />
          <!--
          <div class="opportunities-control btn-group mb-4 justify-content-between w-100">
            <button class="btn btn-xs btn-success">Won</button>
            <button class="btn btn-xs btn-secondary">Abandoned</button>
            <button class="btn btn-xs btn-danger">Lost</button>
          </div> -->
        </Draggable>
      </Container>
    </ul>

    <div
      v-if="isLoading"
      style="position: absolute;bottom: 0px;left: 50%;z-index: 2;transform: translate(-50%, 0);"
    >
      <moon-loader :loading="true" color="#37ca37" size="20px" />
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Contact, Location, User, Opportunity, CampaignStatus } from '@/models'
import OpportunityCard from './OpportunityCard.vue'
import { Container, Draggable } from 'vue-smooth-dnd'
import config from '../../../config'
import moment from 'moment-timezone'
import { UserState } from '../../../store/state_models'
import { mapState } from 'vuex'

let statusTimeouts = {}
let cancelOpportunitiesSubscription

export default Vue.extend({
  props: ['stage', 'filters', 'bus', 'color', 'isLoaded'],
  components: {
    OpportunityCard,
    Container,
    Draggable
  },
  data() {
    return {
      loadingFilters: {
        date: '',
        endDate: ''
      },
      currentLocationId: '',
      location: {} as Location,
      // opportunities: [] as Opportunity[],
      dragging: false,
      height: 0,
      disableDrop: false,
      firstSnapshot: true,
      seqCalling: 0,
      aggrSeqCalling: 0,
      // totalValue: 0,
      limit: 20,
      // totalLength: 0,
      startAfter: '',
      isMounted: false,
      dropPlaceholderOptions: {
        className: 'drop-preview',
        animationDuration: '500',
        showOnTop: true
      }
    }
  },
  computed: {
    xOpp() {
      return this.$store.state.opportunities.opportunities.filter(
        x => x.pipeline_stage_id === this.stage.id
      )
    },
    stageData() {
      return (
        this.$store.state.opportunities.opportunitiesStageData[this.stage.id] ||
        {}
      )
    },
    listHeight(): string {
      return this.height + 'px'
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user
      }
    }),
    isLoading() {
      return !this.dragging && this.stageData.isLoading
    }
  },
  watch: {
    isLoading(val) {
      this.setHeight()
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.location = new Location(
      await this.$store.dispatch('locations/getById', this.currentLocationId)
    )
    this.bus.$on('disableDrop', (disable: boolean) => {
      this.disableDrop = disable
    })
    this.$nextTick(() => {
      this.isMounted = true
    })
  },
  mounted() {
    this.addScrollEvent()
  },
  beforeDestroy() {
    this.bus.$off('disableDrop')
    // this.bus.$off('updateOpportunityStatus');
    window.removeEventListener('resize', this.setHeight)
    this.removeScrollEvent()
  },
  methods: {
    // Scroll Event
    addScrollEvent() {
      this.$nextTick(() => {
        window.addEventListener('resize', this.setHeight)
        let selector = this.$refs[this.stage.id]
        if (selector) {
          this.$refs[this.stage.id].addEventListener(
            'scroll',
            this.handleScroll
          )
        }
      })
      this.setHeight()
    },
    handleScroll(e) {
      if (
        this.stageData &&
        !this.stageData.isLoaded &&
        !this.stageData.isLoading &&
        500 >=
          e.target.scrollHeight - e.target.scrollTop - e.target.clientHeight
      ) {
        this.loadMoreData()
      }
      // if (this.stageData.isLoaded) {
      //   this.removeScrollEvent()
      // }
    },
    removeScrollEvent() {
      let selector = this.$refs[this.stage.id]
      if (selector) {
        this.$refs[this.stage.id].removeEventListener(
          'scroll',
          this.handleScroll
        )
      }
    },
    setHeight() {
      let height = document.body.getBoundingClientRect().height
      const listElement = <any>this.$refs[this.stage.id]
      if (listElement) {
        var viewportOffset = listElement.getBoundingClientRect()
        height -= viewportOffset.top + 8
      }
      if (this.dragging) {
        height -= 80
      }
      this.height = height
      const container = <any>this.$refs[this.stage.id + 'container']
      if (container) {
        const containerElement = container.$el
        containerElement.style.maxHeight = this.height + 'px'
      }
    },
    // Data Load
    loadMoreData: lodash.debounce(function(e) {
      const lastOpp = this.xOpp[this.xOpp.length - 1]
      this.$store.dispatch('opportunities/loadMoreData', {
        pipelineStageId: this.stage.id,
        startAfter:
          typeof lastOpp.date_added === 'number'
            ? lastOpp.date_added
            : lastOpp.date_added.toMillis()
        // startAfterId: lodash.last(this.opportunities).data.id
      })
    }, 1000),
    // Drag & Drop events
    async onDrop(dropResult: any) {
      if (dropResult.removedIndex === dropResult.addedIndex) return //nothing happened return
      if (this.disableDrop) return
      console.log(dropResult)
      if (dropResult.removedIndex !== null) {
        // console.log('removed index')
        return this.$store.commit(
          'opportunities/removeChild',
          dropResult.payload.id
        )
      }
      if (dropResult.addedIndex !== null) {
        const payload = Object.assign({}, dropResult.payload)
        const pipeline_stage_id_old = payload.pipeline_stage_id
        payload.pipeline_stage_id = this.stage.id
        // updateOpportunityLocally
        // this.$store.commit('opportunities/addUpdateChild', {
        //   opportunity: payload,
        //   nextOpportunityId: this.xOpp[dropResult.addedIndex]
        //     ? this.xOpp[dropResult.addedIndex].id
        //     : -1,
        //   localUpdate: true
        // })
        // this.opportunities.splice(dropResult.addedIndex, 0, payload);
        // const opportunity = new Opportunity(payload)
        // await Opportunity.collectionRef()
        //   .doc(payload.id)
        //   .update({
        //     pipeline_stage_id: this.stage.id,
        //     date_updated: firestore.FieldValue.serverTimestamp()
        //   })

        if (statusTimeouts[payload.id]) {
          clearTimeout(statusTimeouts[payload.id])
          delete statusTimeouts[payload.id]
        } else {
          payload.index_version = payload.index_version
            ? payload.index_version + 1
            : 1
        }

        await this.$store.dispatch('opportunities/updateStatus', {
          opportunity: Opportunity.createNewWithVuex({...payload,keepESFields:true}),
          nextOpportunityId: this.xOpp[dropResult.addedIndex]
            ? this.xOpp[dropResult.addedIndex].id
            : -1,
          dontFetchContact: true
        })

        statusTimeouts[payload.id] = setTimeout(async () => {
          delete statusTimeouts[payload.id]
          await Opportunity.updateIndex(payload, payload.status)
          // await Opportunity.updateStats({opportunity: payload, pipeline_stage_id_old, event_type: 'drag', user: this.user });
        }, 1000)
        this.$store.dispatch('opportunities/updateAggregates')
      }
    },
    dragStart(dragResult: any) {
      console.log('dragStart')
      this.dragging = true
      this.setHeight()
      this.$emit('dragStart', dragResult)
    },
    dragEnd(dragResult: any) {
      this.dragging = false
      this.setHeight()
      this.$emit('dragEnd', dragResult)
    },
    getChildPayload(index: number) {
      return this.xOpp[index]
    }
  }
})
</script>

<style>
.hl_opportunities--set {
  max-width: 350px;
  position: relative;
}
.hl_opportunities--set.is-loading {
  padding-bottom: 30px;
}
/* .hl_opportunities--set .opportunities-control {
  display: none;
  opacity: 0;
}
.hl_opportunities--set .opportunities-control button {
  flex: 1;
  width: 100%;
}
.hl_opportunities-item:hover {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.hl_opportunities--set .opportunity-card-block:hover .opportunities-control {
  opacity: 1;
  display: flex;
  transition: visibility 0s linear 0.33s, opacity 0.33s linear;
}
.hl_opportunities--set .opportunity-card-block:hover .hl_opportunities-item {
  margin-bottom: 0;
} */
.card-ghost {
  transition: transform 0.18s ease;
  transform: rotateZ(5deg);
}
.card-ghost-drop {
  transition: transform 0.18s ease-in-out;
  transform: rotateZ(0deg);
}
.opacity-ghost {
  transition: all 0.18s ease;
  opacity: 0.8;
  /* transform: rotateZ(5deg); */
  background-color: cornflowerblue;
  box-shadow: 3px 3px 10px 3px rgba(0, 0, 0, 0.3);
}
.opacity-ghost-drop {
  opacity: 1;
  /* transform: rotateZ(0deg); */
  background-color: white;
  box-shadow: 3px 3px 10px 3px rgba(0, 0, 0, 0);
}
.drop-preview {
  background-color: rgba(150, 150, 200, 0.1);
  border: 1px dashed #abc;
  margin: 5px;
}
.cards-drop-preview {
  background-color: rgba(150, 150, 200, 0.1);
  border: 1px dashed #abc;
  margin: 5px 45px 5px 5px;
}
</style>
