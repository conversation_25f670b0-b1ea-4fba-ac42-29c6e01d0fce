/**
 * SubAccount Selectors
 * 
 * Memoized selectors for SubAccount state with computed properties
 * Based on SPM Location store getters and lcCore User selector patterns
 */

import { createSelector } from '@reduxjs/toolkit';
import type { RootState } from '../../index';
import type { ISubAccount, SubAccountStatus } from '../../../types/subAccount';
import { SubAccountComputedProperties } from '../../../utils/subAccountUtils';

/**
 * Base selectors
 */
export const selectSubAccountState = (state: RootState) => state.subAccount;

export const selectSubAccounts = (state: RootState) => state.subAccount.subAccounts;

export const selectActiveSubAccounts = (state: RootState) => state.subAccount.activeSubAccounts;

export const selectNotificationSubAccounts = (state: RootState) => state.subAccount.notificationSubAccounts;

export const selectCurrentSubAccount = (state: RootState) => state.subAccount.currentSubAccount;

export const selectSubAccountsLoaded = (state: RootState) => state.subAccount.subAccountsLoaded;

export const selectSubAccountLoading = (state: RootState) => state.subAccount.isLoading;

export const selectSubAccountUpdating = (state: RootState) => state.subAccount.isUpdating;

export const selectSubAccountCreating = (state: RootState) => state.subAccount.isCreating;

export const selectSubAccountDeleting = (state: RootState) => state.subAccount.isDeleting;

export const selectSubAccountError = (state: RootState) => state.subAccount.error;

export const selectSearchQuery = (state: RootState) => state.subAccount.searchQuery;

export const selectStatusFilter = (state: RootState) => state.subAccount.statusFilter;

export const selectSelectedSubAccountIds = (state: RootState) => state.subAccount.selectedSubAccountIds;

export const selectSorting = (state: RootState) => ({
  sortBy: state.subAccount.sortBy,
  sortOrder: state.subAccount.sortOrder,
});

export const selectSubAccountSubscriptions = (state: RootState) => state.subAccount.subscriptions;

/**
 * Computed selectors
 */

/**
 * Get sub-account by ID
 */
export const selectSubAccountById = createSelector(
  [selectSubAccounts, (state: RootState, subAccountId: string) => subAccountId],
  (subAccounts, subAccountId) => subAccounts.find(sa => sa.id === subAccountId) || null
);

/**
 * Get filtered and sorted sub-accounts
 * Based on current search query and status filter
 */
export const selectFilteredSubAccounts = createSelector(
  [selectSubAccounts, selectSearchQuery, selectStatusFilter],
  (subAccounts, searchQuery, statusFilter) => {
    let filtered = subAccounts;

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(sa => sa.status === statusFilter);
    }

    // Apply search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(sa =>
        sa.name.toLowerCase().includes(query) ||
        sa.city?.toLowerCase().includes(query) ||
        sa.state?.toLowerCase().includes(query) ||
        sa.email?.toLowerCase().includes(query)
      );
    }

    return filtered;
  }
);

/**
 * Get sorted sub-accounts
 */
export const selectSortedSubAccounts = createSelector(
  [selectFilteredSubAccounts, selectSorting],
  (subAccounts, { sortBy, sortOrder }) => {
    const sorted = [...subAccounts].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'dateAdded':
          aValue = new Date(a.dateAdded);
          bValue = new Date(b.dateAdded);
          break;
        case 'dateUpdated':
          aValue = new Date(a.dateUpdated);
          bValue = new Date(b.dateUpdated);
          break;
        case 'city':
          aValue = a.city?.toLowerCase() || '';
          bValue = b.city?.toLowerCase() || '';
          break;
        case 'state':
          aValue = a.state?.toLowerCase() || '';
          bValue = b.state?.toLowerCase() || '';
          break;
        default:
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    return sorted;
  }
);

/**
 * Get sub-accounts by status
 */
export const selectSubAccountsByStatus = createSelector(
  [selectSubAccounts, (state: RootState, status: SubAccountStatus) => status],
  (subAccounts, status) => subAccounts.filter(sa => sa.status === status)
);

/**
 * Get prospect sub-accounts
 */
export const selectProspectSubAccounts = createSelector(
  [selectSubAccounts],
  (subAccounts) => subAccounts.filter(sa => SubAccountComputedProperties.isProspect(sa))
);

/**
 * Get account sub-accounts (active)
 */
export const selectAccountSubAccounts = createSelector(
  [selectSubAccounts],
  (subAccounts) => subAccounts.filter(sa => SubAccountComputedProperties.isActive(sa))
);

/**
 * Get selected sub-accounts
 */
export const selectSelectedSubAccounts = createSelector(
  [selectSubAccounts, selectSelectedSubAccountIds],
  (subAccounts, selectedIds) => subAccounts.filter(sa => selectedIds.includes(sa.id))
);

/**
 * Get sub-accounts count by status
 */
export const selectSubAccountsCountByStatus = createSelector(
  [selectSubAccounts],
  (subAccounts) => {
    const counts = {
      total: subAccounts.length,
      prospect: 0,
      account: 0,
      active: 0,
      deleted: 0,
    };

    subAccounts.forEach(sa => {
      if (sa.deleted) {
        counts.deleted++;
      } else if (SubAccountComputedProperties.isProspect(sa)) {
        counts.prospect++;
      } else if (SubAccountComputedProperties.isActive(sa)) {
        counts.account++;
        counts.active++;
      }
    });

    return counts;
  }
);

/**
 * Get current sub-account with computed properties
 */
export const selectCurrentSubAccountWithComputed = createSelector(
  [selectCurrentSubAccount],
  (currentSubAccount) => {
    if (!currentSubAccount) return null;

    return {
      ...currentSubAccount,
      // Add computed properties
      formattedName: SubAccountComputedProperties.getFormattedName(currentSubAccount),
      fullAddress: SubAccountComputedProperties.getFullAddress(currentSubAccount),
      isActive: SubAccountComputedProperties.isActive(currentSubAccount),
      isProspect: SubAccountComputedProperties.isProspect(currentSubAccount),
      displayTimezone: SubAccountComputedProperties.getDisplayTimezone(currentSubAccount),
      isSaasActivated: SubAccountComputedProperties.isSaasActivated(currentSubAccount),
      isSaasSetupPending: SubAccountComputedProperties.isSaasSetupPending(currentSubAccount),
      enabledProducts: SubAccountComputedProperties.getEnabledProducts(currentSubAccount),
      contactInfo: SubAccountComputedProperties.getContactInfo(currentSubAccount),
    };
  }
);

/**
 * Get sub-accounts with computed properties
 */
export const selectSubAccountsWithComputed = createSelector(
  [selectSortedSubAccounts],
  (subAccounts) => subAccounts.map(sa => ({
    ...sa,
    // Add computed properties
    formattedName: SubAccountComputedProperties.getFormattedName(sa),
    fullAddress: SubAccountComputedProperties.getFullAddress(sa),
    isActive: SubAccountComputedProperties.isActive(sa),
    isProspect: SubAccountComputedProperties.isProspect(sa),
    displayTimezone: SubAccountComputedProperties.getDisplayTimezone(sa),
    isSaasActivated: SubAccountComputedProperties.isSaasActivated(sa),
    isSaasSetupPending: SubAccountComputedProperties.isSaasSetupPending(sa),
    enabledProducts: SubAccountComputedProperties.getEnabledProducts(sa),
    contactInfo: SubAccountComputedProperties.getContactInfo(sa),
  }))
);

/**
 * Check if any sub-account operation is in progress
 */
export const selectSubAccountOperationInProgress = createSelector(
  [selectSubAccountLoading, selectSubAccountUpdating, selectSubAccountCreating, selectSubAccountDeleting],
  (isLoading, isUpdating, isCreating, isDeleting) => 
    isLoading || isUpdating || isCreating || isDeleting
);

/**
 * Get sub-accounts grouped by status
 */
export const selectSubAccountsGroupedByStatus = createSelector(
  [selectSubAccounts],
  (subAccounts) => {
    const grouped = {
      prospect: [] as ISubAccount[],
      account: [] as ISubAccount[],
    };

    subAccounts.forEach(sa => {
      if (!sa.deleted) {
        if (SubAccountComputedProperties.isProspect(sa)) {
          grouped.prospect.push(sa);
        } else if (SubAccountComputedProperties.isActive(sa)) {
          grouped.account.push(sa);
        }
      }
    });

    return grouped;
  }
);

/**
 * Get sub-accounts by timezone
 */
export const selectSubAccountsByTimezone = createSelector(
  [selectSubAccounts],
  (subAccounts) => {
    const byTimezone: { [timezone: string]: ISubAccount[] } = {};

    subAccounts.forEach(sa => {
      const timezone = SubAccountComputedProperties.getDisplayTimezone(sa);
      if (!byTimezone[timezone]) {
        byTimezone[timezone] = [];
      }
      byTimezone[timezone].push(sa);
    });

    return byTimezone;
  }
);

/**
 * Get sub-accounts with SAAS activated
 */
export const selectSaasActivatedSubAccounts = createSelector(
  [selectSubAccounts],
  (subAccounts) => subAccounts.filter(sa => SubAccountComputedProperties.isSaasActivated(sa))
);

/**
 * Get sub-accounts with specific product enabled
 */
export const selectSubAccountsWithProduct = createSelector(
  [selectSubAccounts, (state: RootState, product: keyof ISubAccount['productStatus']) => product],
  (subAccounts, product) => subAccounts.filter(sa => SubAccountComputedProperties.isProductEnabled(sa, product))
);
