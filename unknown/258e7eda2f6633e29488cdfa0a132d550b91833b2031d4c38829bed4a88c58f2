<template>
  <Filters
    :conditions="conditions"
    :filterMaster="filterMaster"
    v-on:update:conditions="$emit('update:conditions', $event)"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import lodash from 'lodash'
import { Campaign, Tag, TwilioAccount } from '@/models';
import { Condition } from '@/models/trigger';
const Filters = () => import( './Filters.vue');

export default Vue.extend({
  props: ['conditions'],
  components: { Filters },
  data() {
    return {
      filterMaster: [
        {
          placeHolder: 'Select campaign',
          title: 'Replied to campaign',
          value: 'campaign.id',
          valueType: 'text',
          type: 'select',
          options: [] as { [key: string]: any }[]
        },
        {
          placeHolder: 'Select reply type',
          title: 'Reply channel',
          value: 'message.type',
          valueType: 'number',
          type: 'select',
          options: [{ title: 'Call', value: 1 }, { title: 'SMS', value: 2 }, { title: 'Email', value: 3 }, { title: 'Chat Widget', value: 5 }, { title: 'Facebook Messenger', value: 11 }, { title: 'GMB Messaging', value: 15 }, { title: 'Instagram DM', value: 18 }]
        },
        {
          id: "message-contains-phrase",
          placeHolder: 'Type word/phrase',
          title: 'Contains phrase',
          value: 'message.body',
          operator: 'contains',
          valueType: 'text',
          type: 'input',
        },
        {
          id: "message-exact-phrase",
          placeHolder: 'Type word/phrase',
          title: 'Exact match phrase',
          value: 'message.body',
          operator: 'exact',
          valueType: 'text',
          type: 'input',
        },
        {
          id: "message-contains-intent",
          placeHolder: 'Select intent',
          title: 'Intent type',
          value: 'message.body',
          operator: 'matches_intent',
          valueType: 'text',
          type: 'select',
          options: [{ title: 'Positive/Yes', value: 'schedule-yes' }, { title: 'Negative/No', value: 'schedule-no' }]
        },
        {
          id: "has-tag",
          placeHolder: "Has Tag",
          title: "Has Tag",
          value: "contact.tags",
          valueType: "text",
          operator: "index-of-true",
          type: "select",
          options: [] as { [key: string]: any }[]
        },
        {
          id: 'doesnot-have-tag',
          placeHolder: "Doesn't Have Tag",
          title: "Doesn't Have Tag",
          value: 'contact.tags',
          valueType: 'text',
          operator: 'index-of-false',
          type: 'select',
          options: [] as { [key: string]: any }[]
        }
      ],
      addedInboundNumberFilter: false
    }
  },
  computed: {
    phoneNumbers() {
      return this.$store.state.numbers.numbers;
    }
  },
  watch: {
    conditions(val) {
      if (!this.addedInboundNumberFilter) {
        const replyChannelCondition = val.find(condition => condition.field === 'message.type')
        if (replyChannelCondition && [1, 2].includes(replyChannelCondition.value)) {
          this.addedInboundNumberFilter = true
          this.addInboundNumberFilterOption()
        }
      } else {
        const replyChannelCondition = val.find(condition => condition.field === 'message.type')
        if (replyChannelCondition && ![1, 2].includes(replyChannelCondition.value)) { // is not call nor sms anymore
          this.addedInboundNumberFilter = false
          this.removeInboundNumberFilterOption()
        }
      }
    }
  },
  methods: {
    removeInboundNumberFilterOption() {
      let index = this.filterMaster.findIndex(filter => filter.id === "inbound-twilio-number")
      if (index > -1) {
        this.filterMaster.splice(index, 1)
        let conditionIndex = this.conditions.findIndex(condition => condition.id === "inbound-twilio-number")
        if (conditionIndex > -1) {
          let newConditions = this.conditions
          newConditions.splice(conditionIndex, 1)
          this.$emit('update:conditions', newConditions)
        }
      }
    },
    async addInboundNumberFilterOption() {
      let index = this.filterMaster.findIndex(filter => filter.id === "inbound-twilio-number")
      if (index === -1) {
        const twilioAccount = await TwilioAccount.getByLocationId(this.$route.params.location_id)
        if (twilioAccount) {
            let phoneNumbers = this.phoneNumbers.map(number => {
              const display_name = twilioAccount.numberName[number.phone_number] || number.friendly_name
              return { value: number.phone_number, title: display_name }
            })
            phoneNumbers = lodash.orderBy(phoneNumbers, 'title')
            this.filterMaster.unshift({
              id: "inbound-twilio-number",
              placeHolder: 'Select phone',
              title: 'In Phone Number',
              operator: 'contains-any',
              value: 'inbound_number',
              valueType: 'text',
              type: 'select',
              allowMultiple: true,
              options: phoneNumbers
            })
        }
      }
    }
  },
  async created() {
    const currentLocationId = this.$router.currentRoute.params.location_id;
    const campaignIdOption = lodash.find(this.filterMaster, { value: 'campaign.id' });
    if (campaignIdOption) {
      const campaigns = await Campaign.getWithLocationId(currentLocationId);
      campaignIdOption.options = campaigns.map(campaign => {
        return {
          title: campaign.name,
          value: campaign.id
        }
      });
    }
    const hasTagIdOption = lodash.find(this.filterMaster, {
      id: "has-tag"
    });
    const doesnotHaveTagIdOption = lodash.find(this.filterMaster, {
      id: 'doesnot-have-tag'
    })
    let tagOptions = (await Tag.getByLocationId(currentLocationId)).map((tag)=>{
      return {
        title: tag.name,
        value: tag.name
      }
    });
    hasTagIdOption.options = tagOptions;
    doesnotHaveTagIdOption.options = tagOptions;
    if (!this.addedInboundNumberFilter && this.conditions.length > 0) {
      const replyChannelCondition = this.conditions.find(condition => condition.field === 'message.type')
      if (replyChannelCondition && [1, 2].includes(replyChannelCondition.value)) {
        this.addedInboundNumberFilter = true
        this.addInboundNumberFilterOption()
      }
    }
  }
})
</script>

