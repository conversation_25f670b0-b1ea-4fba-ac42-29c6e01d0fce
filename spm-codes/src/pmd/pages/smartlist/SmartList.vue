<template>
  <div class="hl_wrapper">
    <section class="hl_wrapper--inner smartlists" id="smartlists">
      <div class="customized-header">
        <div class="header-underline">
          <div class="d-inline-block">
            <ul class="header-ul">
              <li :class="{ active: highlightListId === emptyListName }">
                <a
                  @click.prevent="checkAndRoute(null, emptyListName)"
                  :class="{
                    'light-color-wait':
                      changingList && highlightListId !== emptyListName,
                  }"
                  >{{ emptyListName }}</a
                >
              </li>
              <li
                v-for="(s, index) in smartLists.slice(0, allowTabCount)"
                :key="index"
                :class="{ active: highlightListId === s.id }"
              >
                <a
                  @click.prevent="checkAndRoute(s.id, null)"
                  :class="{
                    'light-color-wait':
                      changingList && highlightListId !== s.id,
                  }"
                  >{{ s.listName }}</a
                >
              </li>
            </ul>
          </div>
          <div
            class="dropdown mt-0 mb-0 mx-3"
            v-if="smartLists && smartLists.length > allowTabCount"
          >
            <a
              id="dropdownMenuButton"
              @click.prevent="moreDispToggler.toggle()"
              :class="{ 'light-color-wait': changingList }"
              data-toggle="dropdown"
              aria-haspopup="true"
              aria-expanded="false"
            >
              More <i class="fas fa-caret-down --light"></i>
            </a>
            <div
              aria-labelledby="dropdownMenuButton"
              style="max-height: 500px; overflow: auto"
              class="dropdown-menu"
              :id="moreDispToggler.id"
            >
              <div
                class="dropdown-item my-1"
                v-for="(s, index) in smartLists.slice(
                  allowTabCount,
                  smartLists.length
                )"
                :key="index"
                @click.prevent="checkAndRoute(s.id, s.listName)"
              >
                <span class="text align-middle">{{ s.listName }}</span>
              </div>
            </div>
          </div>
          <div class="float-right ml-3 mr- d-inline-block" v-if="!isV2SidebarEnable">
            <button
              type="button"
              :disabled="changingList || (vm && vm.inProgress)"
              @click="showBulkActionsView()"
              class="btn btn-blue py-1 mx-2"
              style="height: 35px"
            >
              <span> Bulk Actions </span>
              <span class="px-1" v-if="!showBulkActions"
                >({{ schedBulkActionsCount }})
              </span>
            </button>
            <button
              type="button"
              :disabled="changingList || (vm && vm.inProgress)"
              @click="showSettingsView()"
              class="btn btn-blue py-1"
              style="height: 35px; width: 130px"
            >
              <span
                >{{ settingsName }}<i class="fa fa-cog ml-2 mr-0 --white"></i
              ></span>
            </button>
          </div>
        </div>
        <!-- <button v-if="vm.smartList && vm.smartList.listName == emptyListName"
                class="btn btn-success" @click.stop.prevent="showPopup()">
                <i class="icon icon-plus"></i> New Smartlist
        </button> -->
      </div>

      <div
        class="hl_controls hl_smartlists--controls"
        v-if="showListSettings === true && !changingList">
        <SmartListItems
          v-on:listChange="onlistChange"
          v-on:navigateTo="loadRoute"
          v-on:itemDeleted="itemDeleted"
          v-on:itemAdded="smartListAdded"
          v-on:sorted="reordered()"
          :changeListener="locationListSubscriber"
          :smartListNames="smartListNames"
          :lists="smartLists"
          :user="user"
          :emptyListName="emptyListName"
        ></SmartListItems>
      </div>
      <div class="hl_controls" v-if="showBulkActions === true && !changingList">
        <BulkActionsView
          v-on:updated="fetchScheduledBulkActionsLength()"
        ></BulkActionsView>
      </div>

      <div
        class="hl_controls hl_smartlists--controls"
        v-if="!showListSettings && !showBulkActions && vm"
      >
        <div class="hl_controls--left">
          <!-- v-if="!vm.smartList || vm.smartList.listName === emptyListName" -->
          <button
            type="button"
            class="btn btn-light btn-sm"
            data-tooltip="tooltip"
            data-placement="top"
            v-b-tooltip.hover
            title="Add Contact"
            @click.stop.prevent="addNewContact"
            data-original-title="Add"
          >
            <i class="icon icon-plus"></i>
          </button>
          <button
            type="button"
            class="btn btn-light btn-sm"
            data-tooltip="tooltip"
            data-placement="top"
            v-b-tooltip.hover
            title="Pipeline Change"
            @click.stop.prevent="bulkActions('change-pipeline')"
            data-original-title="Pipeline Modal"
          >
            <i class="fa fa-filter"></i>
          </button>
          <button
            type="button"
            class="btn btn-light btn-sm"
            data-tooltip="tooltip"
            data-placement="top"
            v-b-tooltip.hover
            :title="`Add to ${automationString}`"
            @click.stop.prevent="bulkActions('apply-campaign')"
            data-original-title="Campaign Selection Modal"
          >
            <i class="fa fa-robot" style="font-size: 17px"></i>
          </button>
          <button
            type="button"
            class="btn btn-light btn-sm"
            data-tooltip="tooltip"
            data-placement="top"
            v-b-tooltip.hover
            title="Send SMS"
            @click.stop.prevent="bulkActions('bulk-sms')"
            data-original-title="Bulk SMS Modal"
          >
            <i class="icon-sms-gray"></i>
          </button>
          <button
            type="button"
            class="btn btn-light btn-sm"
            data-tooltip="tooltip"
            data-placement="top"
            v-b-tooltip.hover
            title="Send Email"
            @click.stop.prevent="bulkActions('bulk-email')"
            data-original-title="Bulk Email Modal"
          >
            <i class="icon-email-svg-gray"></i>
          </button>
          <button
            type="button"
            class="btn btn-light btn-sm"
            data-tooltip="tooltip"
            data-placement="top"
            v-b-tooltip.hover
            title="Add Tag"
            @click.stop.prevent="bulkActions('add-tag')"
            data-original-title="Tag"
          >
            <i class="icon icon-tag-add"></i>
          </button>
          <button
            type="button"
            class="btn btn-light btn-sm"
            data-tooltip="tooltip"
            data-placement="top"
            v-b-tooltip.hover
            title="Remove Tag"
            @click.stop.prevent="bulkActions('remove-tag')"
            data-original-title="Tag"
          >
            <i class="icon icon-tag-delete"></i>
          </button>
          <!-- v-if="vm.smartList && vm.smartList.listName !== emptyListName" -->
          <button
            type="button"
            v-if="isAdmin"
            class="btn btn-light btn-sm"
            data-tooltip="tooltip"
            data-placement="top"
            v-b-tooltip.hover
            title="Delete Contacts"
            @click.stop.prevent="bulkActions('delete-contact')"
            data-original-title="Delete"
          >
            <i class="icon icon-trash"></i>
          </button>
          <button
            v-if="isAdmin"
            type="button"
            class="btn btn-light btn-sm"
            data-tooltip="tooltip"
            data-placement="top"
            v-b-tooltip.hover
            title="Send Review Requests"
            @click.stop.prevent="bulkActions('review-request')"
            data-original-title="ReviewRequest">
            <i class="fas fa-star"></i>
          </button>
          <button
            v-if="isAdmin"
            type="button"
            class="btn btn-light btn-sm"
            data-tooltip="tooltip"
            data-placement="top"
            v-b-tooltip.hover
            title="Export Contacts"
            @click.stop.prevent="bulkActions('export-contact')"
            data-original-title="Export"
          >
            <i class="icon icon-download"></i>
          </button>
          <button
            type="button"
            v-if="isAdmin"
            :disabled="!temporaryFlag"
            class="btn btn-light btn-sm"
            data-tooltip="tooltip"
            data-placement="top"
            v-b-tooltip.hover
            title="Import Contacts"
            @click.prevent="showImportModalClicked"
            data-original-title="Import"
          >
            <i class="icon icon-share-1"></i>
          </button>
          <span
            v-if="isAdmin"
            title="Merge up to 10 Contacts"
            v-b-tooltip.hover
            data-tooltip="tooltip"
            data-placement="top"
          >
            <button
              type="button"
              :disabled="blockMergeButton"
              class="btn btn-light btn-sm"
              @click.prevent="toogleMergeModal"
              data-original-title="Merge"
            >
              <i class="fas fa-object-group"></i>
            </button>
          </span>
          <div class="d-inline-block">
            <h3 v-show="!vm || !vm.totalContacts" class="mx-1">
              <template v-if="vm.inProgress">Searching...</template>
              <!-- <template v-else-if="hideFilters && vm.listName === emptyListName">Add filters and start building smart list</template> -->
              <template
                v-else-if="!vm.displayModels || vm.displayModels.length <= 0"
                >* Add filters and save this as a smart list</template
              >
              <template v-else>* No items found for selected filters</template>
            </h3>
            <!-- <h3 v-show="vm && !vm.inProgress && vm.totalContacts" class="mx-2">
              <template>Total {{vm.totalContacts}} records found.</template>
            </h3> -->
          </div>
        </div>

        <div class="hl_controls--right">
          <div class="dropdown bootstrap-select fit-width">
            <button
              id="colViewButton"
              type="button"
              class="btn dropdown-toggle bs-placeholder btn-light"
              data-toggle="dropdown"
              aria-haspopup="true"
              aria-expanded="false"
              @click.prevent="colDispToggler.toggle()"
              role="button"
              title="Columns"
            >
              <div class="filter-option">
                <div class="filter-option-inner">Columns</div>
              </div>
            </button>
            <ul
              class="dropdown-menu"
              :id="colDispToggler.id"
              aria-labelledby="colViewButton"
              v-if="vm.cols && vm.cols.options"
              style="max-height: 500px; overflow-y: auto"
            >
              <li
                v-for="(c, index) in vm.cols.options"
                :key="index + '-' + c.key"
                @click="$event.stopPropagation()"
              >
                <div class=" mx-3">
                  <UICheckbox
                    :id="c.key"
                    :disabled="c.isRequired"
                    :value="c.name"
                    :checked="c.checked"
                    v-model="c.checked"
                    />
                  <label
                    :disabled="c.isRequired === true ? true : false"
                    :for="c.key"
                  >
                    {{ c.name }}</label
                  >
                </div>
              </li>
              <!-- <li @click="$event.stopPropagation()">
                <div class="mx-3" @dblclick="toggleSelectAllColumns" >
                  <label> Select / Unselect All Columns</label>
                </div>
              </li> -->
            </ul>
          </div>
          <FilterInput
            v-if="genericFilter"
            v-on:inputChange="genericFilterChange"
            v-b-tooltip.hover
            :title="'Search by contact name, email, business name or tag. You can also search by the phone number (minimum 3 digits).'"
            :bus="bus"
            :option="genericFilter.selectedOption"
          ></FilterInput>
          <button
            type="button"
            class="btn btn-light btn-sm hl_reviews--filter-btn"
            :class="{ '--open': !hideFilters }"
            id="hl_smartlists--filter-btn"
            @click.prevent="toggleFilterDisplay()"
          >
            <span>{{ !hideFilters ? 'Hide' : 'More' }} Filters</span>
            <i
              class="icon --blue"
              :class="{
                'icon-settings-2': hideFilters === true,
                'icon-close': !hideFilters,
              }"
            ></i>
          </button>
        </div>
      </div>
      <div
        class="mx-1 my-3"
        v-if="
          !changingList &&
          !showListSettings &&
          !showBulkActions &&
          vm &&
          vm.smartList
        "
        :set="(l = vm.smartList)"
      >
        <span class="mx-2" v-if="l.isSharedList && !l.userId"
          ><i class="fa fa-globe --light mx-2" aria-hidden="true"></i>Global
          List</span
        >
        <span
          class="mx-2"
          v-else-if="l.isSharedList && user && l.userId === user.id"
          ><i class="fa fa-user-plus --light mx-2" aria-hidden="true"></i>Shared
          By You</span
        >
        <span class="mx-1" v-else-if="l.masterListId"
          ><i class="fa fa-users --light mx-2" aria-hidden="true"></i>Shared
          With You</span
        >
      </div>
      <SmartListLoading
        v-if="changingList && !showListSettings && !showBulkActions"
        :showFilters="!hideFilters"
      ></SmartListLoading>
      <div
        class="hl_smartlists--wrap"
        v-if="
          !changingList &&
          !showListSettings &&
          !showBulkActions &&
          vm &&
          vm.smartList
        "
      >
        <aside
          class="hl_smartlists--filters"
          id="hl_smartlists-filters"
          v-show="!hideFilters"
        >
          <div class="card">
            <div class="body" v-if="changingList !== true">
              <SmartListFilters
                v-on:listChange="onlistChange"
                :ref="`g-${vm.smartList.listName}`"
                v-on:listUpdate="refreshItems"
                v-on:itemDeleted="itemDeleted"
                v-on:itemAdded="newListAddition"
                :slVM="vm"
                :smartListNames="smartListNames"
                :emptyListName="emptyListName"
                :bus="bus"
              >
              </SmartListFilters>
            </div>
          </div>
        </aside>
        <section
          class="hl_smartlists--main"
          :class="{ '--open': !hideFilters }"
          id="hl_smartlists-main"
        >
          <div class="card-like">
            <PageNumBar
              v-on:goToPage="jumpTo"
              v-on:pageSizeChange="pageSizeChange"
              v-on:selectAllPages="selectAllPages"
              v-on:unselectAllPages="unSelectAllPages"
              :currentSelectedCount="currentSelectedCount"
              :totalItems="vm.totalContacts"
              :disabled="vm.inProgress"
              :fetcherror="vm.fetcherror"
              :totalPages="vm.totalPages"
              :areAllPagesSelected="vm.areAllPagesSelected"
              :isCurrentPageSelected="vm.isCurrentPageCheck"
              :currentPage="vm.pageNumber"
              :pageSize="vm.pageLimit"
            ></PageNumBar>
          </div>

          <div
            class="card no-border-radius"
            :class="{ 'min-height-150': vm.contacts && vm.contacts.length > 0 }"
          >
            <!-- min height required as for subcontext menu showing up property where there is just one item in list-->
            <div class="card-body --no-padding">
              <table ref="disp-tab" class="table table-sort" v-if="vm.cols">
                <thead v-if="vm.inProgress !== true">
                  <tr v-if="vm.contacts && vm.contacts.length > 0">
                    <th></th>
                    <th>
                      <div class="">
                        <!-- <input
                          :disabled="vm.areAllPagesSelected"
                          type="checkbox"
                          id="all"
                          :checked="vm.isCurrentPageCheck"
                          @change="togglePageSelection"
                        /> -->
                        <UICheckbox
                          id="all"
                          :disabled="vm.areAllPagesSelected"
                          :checked="vm.isCurrentPageCheck"
                          :value="vm.isCurrentPageCheck"
                          @input="togglePageSelection"
                          />
                        <label for="all"></label>
                      </div>
                      <div></div>
                    </th>
                    <th v-for="(c, index) in vm.cols.selected" :key="index">
                      <div
                        style="min-width: 40px"
                        class="mr-1 wide-header"
                        v-if="c.isSortable !== true"
                      >
                        {{ cropDisplay(c.name, 20) }}
                      </div>
                      <div
                        v-else
                        v-bind:class="{ 'text-nowrap': c.isSortable }"
                        style="min-width: 40px"
                        class="mr-1 pointer close-content"
                        @click.stop.prevent="sort(c)"
                      >
                        <span
                          :class="{ '--blue': c.ascending || c.descending }"
                          >{{ cropDisplay(c.name, 20) }}</span
                        >
                        <div
                          class="position-parent chevron-hover mx-1"
                          v-if="c.isSortable"
                        >
                          <div class="position-stacked">
                            <i
                              class="fas fa-sort-up"
                              :class="{
                                '--blue': c.ascending,
                                'chevron-light':
                                  !c.ascending && !c.descending && c.isSortable,
                              }"
                            ></i>
                          </div>
                          <div class="position-stacked">
                            <i
                              class="fas fa-sort-down"
                              :class="{ '--blue': c.descending }"
                            ></i>
                          </div>
                        </div>
                        <span v-if="showSortOrder && c.sortOrder" class="mx-1"
                          >({{ c.sortOrder }})</span
                        >
                      </div>
                    </th>
                    <!-- <i class="icon icon-arrow-down-1"></i> -->
                  </tr>
                  <tr v-else>
                    <th style="height: 40px"></th>
                  </tr>
                </thead>
                <tbody v-if="vm.inProgress === true">
                  <tr v-for="index in 10" :key="index" class="loading">
                    <td class="td-check">
                      <span></span>
                    </td>
                    <td
                      class="td-loading-short"
                      v-for="index2 in 5"
                      :key="index2"
                    >
                      <span></span>
                    </td>
                  </tr>
                </tbody>
                <tbody v-if="vm.inProgress !== true">
                  <tr
                    v-for="(contact, contact_index) in vm.contacts"
                    :key="contact.id"
                    :class="{ loading: reloadingContacts.includes(contact.id) }"
                  >
                    <template v-if="reloadingContacts.includes(contact.id)">
                      <td
                        class="td-loading-short"
                        v-for="index2 in 4"
                        :key="index2"
                      >
                        <span></span>
                      </td>
                      <td class="td-loading-short" colspan="80">
                        <!-- v-model="vm.cols.selected" v-for="c in vm.cols.selected" -->
                        <span></span>
                      </td>
                    </template>
                    <template v-else>
                      <td data-title style="max-width: 10px">
                        <div class="dropdown bootstrap-select more-select-left">
                          <button
                            type="button"
                            class="btn dropdown-toggle more-select"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="false"
                          ></button>
                          <div class="dropdown-menu">
                            <a
                              class="mx-1 my-1 pointer dropdown-item"
                              href="javascript:void(0);"
                              v-if="canViewAppointments"
                              @click.prevent="
                                dialogLaunch('appointment', contact)
                              "
                              >Schedule appointment</a
                            >
                            <a
                              class="mx-1 my-1 pointer dropdown-item"
                              href="javascript:void(0);"
                              v-if="canViewOpportunities"
                              @click.prevent="
                                dialogLaunch('create_opportunity', contact)
                              "
                              >Create opportunity</a
                            >
                            <a
                              v-if="canCreateReviewRequest"
                              class="mx-1 my-1 pointer dropdown-item"
                              href="javascript:void(0);"
                              @click.prevent="dialogLaunch('review', contact)"
                              >Send Review Request</a
                            >
                            <a
                              class="mx-1 my-1 pointer dropdown-item"
                              href="javascript:void(0);"
                              @click.prevent="showBulkOperations(contact)"
                              >Bulk Operations</a
                            >
                          </div>
                        </div>
                      </td>
                      <td data-title>
                        <div>
                          <!--@todo update with UICheckbox -->
                          <input
                            :disabled="vm && vm.areAllPagesSelected"
                            type="checkbox"
                            :id="contact.id"
                            :value="contact.id"
                            @change="contactChecked($event.target.checked)"
                            v-model="vm.selectedContactIds"
                            class="focus:ring-curious-blue-500 h-5 w-5 text-curious-blue-600 border-gray-300 rounded mr-2 disabled:opacity-50"
                          />
                          <label :for="contact.id"></label>
                        </div>
                      </td>
                      <template v-for="(c, index) in vm.cols.selected">
                        <td
                          :key="'name_' + index"
                          data-title="Name"
                          v-if="c.name === 'Name'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <Avatar
                            :contact="contact"
                            :include_name="true"
                            :include_company_name="true"
                          />
                        </td>
                        <td
                          :key="'b-name_' + index"
                          data-title="Business-Name"
                          v-else-if="c.name === 'Business Name'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <div>{{ contact.companyName }}</div>
                        </td>
                        <td
                          :key="'dnd_' + index"
                          data-title="DND"
                          v-else-if="c.name === 'DND'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <div
                            v-if="contact.dnd === true"
                            class="table_tag d-inline-block"
                          >
                            DND
                          </div>
                        </td>
                        <td
                          :key="'phone_' + index"
                          data-title="Phone"
                          v-else-if="c.name === 'Phone'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <div
                            class="phone copy-me clipboard-holder"
                            v-if="contact.phone"
                          >
                            <i class="icon icon-phone-svg mx-2"></i>
                            <PhoneNumber
                              type="display"
                              v-model="contact.phone"
                            ></PhoneNumber>
                            <i
                              class="fas fa-clipboard --dark copier zoomable"
                              @click.prevent.stop="clipboardCopy(contact.phone)"
                            ></i>
                          </div>
                        </td>
                        <td
                          :key="'email_' + index"
                          data-title="Email"
                          v-else-if="c.name === 'Email'"
                          class="pointer copy-me"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <div class="email copy-me" v-if="contact.email">
                            <i class="icon icon-email-svg mx-2"></i>
                            <!-- <i class="fas fa-envelope"></i> -->
                            {{ contact.email }}
                            <i
                              class="fas fa-clipboard --dark copier zoomable"
                              @click.prevent.stop="clipboardCopy(contact.email)"
                            ></i>
                          </div>
                        </td>
                        <!-- <td data-title="Value"></td> -->
                        <td
                          :key="'type_' + index"
                          data-title="Type"
                          v-else-if="c.name === 'Type'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <div v-if="contact.type" class="table_tag">
                            {{ contact.type }}
                          </div>
                        </td>
                        <td
                          :key="'created_' + index"
                          data-title="Created"
                          v-else-if="c.name === 'Created'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <div
                            v-if="contact.dateAdded"
                            style="align-items: normal !important"
                            :set="
                              (parts = getDateParts(
                                contact.dateAdded,
                                locationTimeZone
                              ))
                            "
                          >
                            <div class="date-display">{{ parts[0] }}</div>
                            <div class="date-display --blue">
                              {{ parts[1] }} <small>({{ parts[2] }})</small>
                            </div>
                          </div>
                        </td>
                        <td
                          :key="'update_' + index"
                          data-title="Updated"
                          v-else-if="c.name === 'Updated'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <div
                            v-if="contact.dateUpdated"
                            style="align-items: normal !important"
                            :set="
                              (parts = getDateParts(
                                contact.dateUpdated,
                                locationTimeZone
                              ))
                            "
                          >
                            <div class="date-display">{{ parts[0] }}</div>
                            <div class="date-display --blue">
                              {{ parts[1] }} <small>({{ parts[2] }})</small>
                            </div>
                          </div>
                        </td>
                        <td
                          :key="'source_' + index"
                          data-title="Source"
                          v-else-if="c.name === 'Source'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <div class="break-word" v-if="contact.source">
                            {{ contact.source }}
                          </div>
                        </td>
                        <td
                          :key="'opportunity_' + index"
                          data-title="Opportunity"
                          v-else-if="c.name === 'Opportunity'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <div
                            v-if="
                              contact.opportunities &&
                              contact.opportunities.length > 0
                            "
                            style="max-width: 250px"
                          >
                            <ListPopup
                              :items="
                                contact
                                  .getDisplayOpportunities()
                                  .map(
                                    a =>
                                      `${a.status}: ${cropDisplay(
                                        a.pipeline,
                                        25
                                      )} - ${cropDisplay(a.stage, 25)}`
                                  )
                              "
                              :uid="`opp-${contact.id}`"
                              :header="`More Opportunities on ${contact.fullName}`"
                            ></ListPopup>
                          </div>
                        </td>
                        <td
                          :key="'offers_' + index"
                          data-title="Offers"
                          v-else-if="c.name === 'Offers'"
                          class="pointer"
                          @click.stop.prevent="
                            å
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <div
                            v-if="contact.offers && contact.offers.length > 0"
                            style="max-width: 250px"
                          >
                            <ListPopup
                              :items="contact.offersDisplay"
                              :uid="`offer-${contact.id}`"
                              :header="`More Offers on ${contact.fullName}`"
                            ></ListPopup>
                          </div>
                        </td>
                        <td
                          :key="'activeWorkflows_' + index"
                          data-title="Workflows Active"
                          v-else-if="c.name === 'Workflows Active'"
                          class="pointer"
                          @click.stop.prevent="loadDetailPage(contact.id, contact_index)">
                          <div v-if="contact.activeWorkflows && contact.activeWorkflows.length > 0"
                               style="max-width: 250px">
                            <ListPopup
                              :items="contact.activeWorkflowsDisplay"
                              :uid="`activeWorkFlows-${contact.id}`"
                              :header="`More Active Workflows for ${contact.fullName}`">
                            </ListPopup>
                          </div>
                        </td>
                        <td
                          :key="'finishedWorkflows_' + index"
                          data-title="Workflows Finished"
                          v-else-if="c.name === 'Workflows Finished'"
                          class="pointer"
                          @click.stop.prevent="loadDetailPage(contact.id, contact_index)">
                          <div v-if="contact.finishedWorkflows && contact.finishedWorkflows.length > 0"
                               style="max-width: 250px">
                            <ListPopup
                              :items="contact.finishedWorkflowsDisplay"
                              :uid="`finishedWorkFlows-${contact.id}`"
                              :header="`More Finished Workflows for ${contact.fullName}`">
                            </ListPopup>
                          </div>
                        </td>
                        <td
                          :key="'products' + index"
                          data-title="Products"
                          v-else-if="c.name === 'Products'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <div
                            v-if="
                              contact.products && contact.products.length > 0
                            "
                            style="max-width: 250px"
                          >
                            <ListPopup
                              :items="contact.productsDisplay"
                              :uid="`product-${contact.id}`"
                              :header="`More Products on ${contact.fullName}`"
                            ></ListPopup>
                          </div>
                        </td>
                        <td
                          :key="'dob_' + index"
                          data-title="Birth Date"
                          v-else-if="c.name === 'Birth Date'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <div class="created" v-if="contact.dateOfBirth">
                            {{
                              contact.dateOfBirth.utc().format('MMM Do YYYY')
                            }}
                          </div>
                        </td>
                        <td
                          :key="'last_activity_' + index"
                          data-title="Last Activity"
                          v-else-if="c.name === 'Last Activity'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <div
                            v-if="contact.lastActivity"
                            class="email-activity"
                            style="align-items: normal !important"
                          >
                            <template
                              v-if="contact.lastConversationMessageType"
                            >
                              <i
                                class="icon icon-email-svg mx-2"
                                v-if="
                                  isEmailType(
                                    contact.lastConversationMessageType
                                  )
                                "
                              ></i>
                              <i
                                class="icon icon-phone-svg mx-2"
                                v-else-if="
                                  isCallType(
                                    contact.lastConversationMessageType
                                  )
                                "
                              ></i>
                              <i class="icon icon-sms2 mx-2" v-else></i>
                            </template>
                            <div class="text">
                              {{
                                relativeTime(
                                  tzTime(contact.lastActivity, locationTimeZone)
                                )
                              }}
                              <!-- {{tzTime(contact.lastActivity, locationTimeZone)}} -->
                            </div>
                          </div>
                        </td>
                        <td
                          :key="'last_appointment_' + index"
                          data-title="Last Appointment"
                          v-else-if="c.name === 'Last Appointment'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <div
                            v-if="contact.lastAppointment"
                            style="align-items: normal !important"
                            :set="
                              (parts = getDateParts(
                                contact.lastAppointment,
                                locationTimeZone
                              ))
                            "
                          >
                            <div class="date-display">{{ parts[0] }}</div>
                            <div class="date-display --blue">
                              {{ parts[1] }} <small>({{ parts[2] }})</small>
                            </div>
                          </div>
                          <!-- <div class="created" v-if="contact.lastAppointment" style="align-items: normal !important;">

                          <i class="icon icon-clock"></i>
                          {{relativeTime(tzTime(contact.lastAppointment, locationTimeZone))}}
                        </div> -->
                        </td>
                        <td
                          :key="'assigned_' + index"
                          data-title="Assigned"
                          v-else-if="c.name === 'Assigned'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <!-- {{getUserName(contact.assignedTo)}} -->
                          {{ contact.assignedToDisplay }}
                        </td>
                        <td
                          :key="'tags_' + index"
                          data-title="Tags"
                          v-else-if="c.name === 'Tags'"
                          style="min-width: 150px !important"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <ListPopup
                            :items="getTagOrderList(vm, contact.tags)"
                            :uid="contact.id"
                            :header="`More Tags on ${contact.fullName}`"
                          ></ListPopup>
                        </td>
                        <td
                          :key="'full_address_' + index"
                          data-title="Address_Full"
                          v-else-if="c.key === 'fullAddressLine'"
                          class="pointer"
                          style="min-width: 200px !important"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <div class="created" v-if="contact.fullAddressLine">
                            {{ contact.fullAddressLine }}
                          </div>
                        </td>
                        <td
                          :key="'address_line_1_' + index"
                          data-title="Address_1"
                          v-else-if="c.key === 'addressLine1'"
                          class="pointer"
                          style="min-width: 200px !important"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <div class="created" v-if="contact.addressLine1">
                            {{ contact.addressLine1 }}
                          </div>
                        </td>
                        <td
                          :key="'state_' + index"
                          data-title="State"
                          v-else-if="c.name === 'State'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <!-- {{getUserName(contact.assignedTo)}} -->
                          <div class="created" v-if="contact.state">
                            {{ contact.state }}
                          </div>
                        </td>
                        <td
                          :key="'postal_code_' + index"
                          data-title="PostalCode"
                          v-else-if="c.name === 'Postal Code'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <!-- {{getUserName(contact.assignedTo)}} -->
                          <div class="created" v-if="contact.postalCode">
                            {{ contact.postalCode }}
                          </div>
                        </td>
                        <td
                          :key="'city_' + index"
                          data-title="City"
                          v-else-if="c.name === 'City'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <!-- {{getUserName(contact.assignedTo)}} -->
                          <div class="created" v-if="contact.city">
                            {{ contact.city }}
                          </div>
                        </td>
                        <td
                          :key="'custom_field_' + index"
                          class="pointer"
                          v-else-if="
                            c.isCustomField === true &&
                            contact.customFieldsFormatter.canShowSmartListColumn(
                              c.key
                            )
                          "
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <span class="mr-1">{{
                            contact.customFieldsFormatter.showSmartListValue(
                              c.key
                            )
                          }}</span>
                        </td>
                        <td
                          :key="'last_note_' + index"
                          data-title="Last Note"
                          v-else-if="c.name === 'Last Note'"
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        >
                          <div style="width: 150px">{{ contact.lastNote }}</div>
                        </td>
                        <td
                          :key="'delatil_page_action_' + index"
                          v-else
                          class="pointer"
                          @click.stop.prevent="
                            loadDetailPage(contact.id, contact_index)
                          "
                        ></td>
                      </template>
                    </template>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </section>
        <!-- <div class="my-3">Time Zone: {{ this.locationTimeZone }}</div> -->
      </div>
    </section>
    <template v-if="importDlgState">
    <ImportCustomerModal
      v-if="newImportFlow"
      :visible="importDlgState.show"
      v-on:on_import_request="processImportRequest($event)"
      @hidden="doneBulkAction"
      :currentLocationId="locationId"
      type="contacts"
      :isAdmin="isAdmin"
      :user="user"
      v-on:onError="bulkActionError"
      @open_smart_list="refreshOnDialogClose($event)"
      @close="doneBulkAction()"
    />
    <ImportCustomerModalOld
      v-else
      :visible="importDlgState.show"
      v-on:on_import_request="processImportRequest($event)"
      @hidden="doneBulkAction"
      :currentLocationId="locationId"
      type="contacts"
      :isAdmin="isAdmin"
      :user="user"
      v-on:onError="bulkActionError"
      @close="doneBulkAction()"
    />
    </template>
    <MergeContactsModal
      v-if="showMergeModal"
      :currentLocationId="locationId"
      :selectedContacts="vm.selectedContactIds"
      :visible="showMergeModal"
      v-on:closed="toogleMergeModal"
      v-on:finishMerge="resolveFinishMerge"/>

    <!-- <TagsModal
      v-if="tagOperation"
      :operation="tagOperation"
      :contacts="tagOperation.displayContacts"
      :totalContacts="tagOperation.totalContacts"
      v-on:closed="doneBulkAction"
      v-on:onError="bulkActionError"
    >
    </TagsModal> -->
    <!-- <ContactOperationModal
      v-if="delContactOperation"
      type="Delete"
      :operation="delContactOperation"
      :contacts="delContactOperation.displayContacts"
      :totalContacts="delContactOperation.totalContacts"
      v-on:closed="doneBulkAction"
      v-on:onError="bulkActionError">
    </ContactOperationModal>-->
    <ContactOperationModal
      v-if="exportContactOperation"
      type="Export"
      :operation="exportContactOperation"
      :contacts="exportContactOperation.displayContacts"
      :totalContacts="exportContactOperation.totalContacts"
      v-on:closed="doneBulkAction"
      v-on:onError="bulkActionError"
    >
    </ContactOperationModal>
    <EditCustomerModal
      :contact="newContact"
      v-if="showContactDialog"
      @closed2="hideEditWindow"
      :isAdmin="isAdmin"
      :user="user"
    />
    <!-- All pages selected: {{vm.areAllPagesSelected}} -->

    <!--tackle this case later allowScheduling = vm && ( vm.areAllPagesSelected == true || vm.selectedContactIds.length === vm.totalContacts -->
    <BulkActionModal
      v-if="tagAddOperation2"
      showRateWarning="true"
      showDisclaimer="true"
      header="Add Tags"
      header2="Add Tags"
      submitHeader="Add Tags"
      buttonProcesAllLabel="Add all at once"
      buttonProcessScheduleLabel="Add in drip mode"
      buttonAllAtScheduleLabel="Add all at schedule time"
      startOnLabel="When should we start sending ?"
      dripQuantityLabel="How many people to send in a batch ?"
      dripGapLabel="What should be the delay between batches ?"
      :allowScheduling="false"
      :action="tagAddOperation2.actionSpecs"
      :operation="tagAddOperation2"
      :contacts="tagAddOperation2.displayContacts"
      :totalContacts="tagAddOperation2.totalContacts"
      v-on:closed="doneBulkAction"
      v-on:onError="bulkActionError"
    >
      <!-- use allowScheduling to show hide scheduling modes :allowScheduling="vm && ( vm.areAllPagesSelected == true ) " -->
      <template v-slot:action-slot>
        <TagsSelection
          placeholder="Search / Add Tag"
          :action="tagAddOperation2.actionSpecs"
          @change="value => (tagAddOperation2.actionSpecs = value)">
        </TagsSelection>
      </template>
    </BulkActionModal>
    <BulkActionModal
      v-if="tagRemoveOperation2"
      showRateWarning="true"
      showDisclaimer="true"
      header="Remove Tags"
      header2="Remove Tags"
      submitHeader="Remove Tags"
      buttonProcesAllLabel="Remove all at once"
      buttonProcessScheduleLabel="Remove in drip mode"
      buttonAllAtScheduleLabel="Remove all at schedule time"
      startOnLabel="When should we start sending ?"
      dripQuantityLabel="How many people to send in a batch ?"
      dripGapLabel="What should be the delay between batches ?"
      :allowScheduling="false"
      :action="tagRemoveOperation2.actionSpecs"
      :operation="tagRemoveOperation2"
      :contacts="tagRemoveOperation2.displayContacts"
      :totalContacts="tagRemoveOperation2.totalContacts"
      v-on:closed="doneBulkAction"
      v-on:onError="bulkActionError"
    >
      <!-- use allowScheduling to show hide scheduling modes :allowScheduling="vm && ( vm.areAllPagesSelected == true ) " -->
      <template v-slot:action-slot>
        <TagsSelection
          placeholder="Search for tag to remove"
          :action="tagRemoveOperation2.actionSpecs"
          @change="value => (tagRemoveOperation2.actionSpecs = value)">
        </TagsSelection>
      </template>
    </BulkActionModal>
    <BulkActionModal
      v-if="contactDeleteOperation2"
      showRateWarning="true"
      showDisclaimer="true"
      header="Delete Contacts"
      header2="Delete Contacts"
      submitHeader="Delete Contacts"
      buttonProcesAllLabel="Delete all at once"
      buttonProcessScheduleLabel="Delete in drip mode"
      buttonAllAtScheduleLabel="Delete all at schedule time"
      startOnLabel="When should we start sending ?"
      dripQuantityLabel="How many people to send in a batch ?"
      dripGapLabel="What should be the delay between batches ?"
      :allowScheduling="false"
      :action="contactDeleteOperation2.actionSpecs"
      :operation="contactDeleteOperation2"
      :contacts="contactDeleteOperation2.displayContacts"
      :totalContacts="contactDeleteOperation2.totalContacts"
      v-on:closed="doneBulkAction"
      v-on:onError="bulkActionError">
      <template v-slot:action-slot>
        <div></div>
      </template>
    </BulkActionModal>
    <BulkActionModal
      v-if="pipelineOperation"
      header="Add/Update Opportunity"
      header2="Apply opportunity to"
      submitHeader="Add/Update Opportunity"
      :allowScheduling="false"
      :action="pipelineOperation.actionSpecs"
      :operation="pipelineOperation"
      :contacts="pipelineOperation.displayContacts"
      :totalContacts="pipelineOperation.totalContacts"
      v-on:closed="doneBulkAction"
      v-on:onError="bulkActionError"
    >
      <template v-slot:action-slot>
        <PipelineAction
          showTitles="true"
          :action="pipelineOperation.actionSpecs"
          :hideAllowBackward="true"
          :hideAllowDuplicates="true"
        ></PipelineAction>
      </template>
    </BulkActionModal>
    <BulkActionModal
      v-if="campaignOperation"
      showTitles="true"
      :header="`Add to ${automationString}`"
      showDisclaimer="true"
      header2="Add the"
      :submitHeader="`Add to ${automationString}`"
      buttonProcesAllLabel="Add all at once"
      buttonProcessScheduleLabel="Add in drip mode"
      buttonAllAtScheduleLabel="Add all at schedule time"
      :startOnLabel="`When should we start assigning contacts to this ${automationString}?`"
      :dripQuantityLabel="`How many contacts in a batch should be assigned this ${automationString}?`"
      dripGapLabel="What should be the delay between batches ?"
      :allowScheduling="true"
      :action="campaignOperation.actionSpecs"
      :operation="campaignOperation"
      :contacts="campaignOperation.displayContacts"
      :totalContacts="campaignOperation.totalContacts"
      v-on:closed="doneBulkAction"
      v-on:onError="bulkActionError"
    >
      <!-- use allowScheduling to show hide scheduling modes :allowScheduling="vm && ( vm.areAllPagesSelected == true ) " -->
      <template v-slot:action-slot>
        <CampaignSelection
          :action="campaignOperation.actionSpecs"
          :locationId="locationId"
        ></CampaignSelection>
      </template>
    </BulkActionModal>
    <BulkActionModal
      v-if="reviewReqOperation"
      showTitles="true"
      header="Send review request"
      showDisclaimer="true"
      header2="Add the"
      submitHeader="Send review request"
      buttonProcesAllLabel="Add all at once"
      buttonProcessScheduleLabel="Add in drip mode"
      buttonAllAtScheduleLabel="Add all at schedule time"
      startOnLabel="When should we start sending review requests to contacts ?"
      dripQuantityLabel="How many contacts in a batch should be sent review request ?"
      dripGapLabel="What should be the delay between batches ?"
      :allowScheduling="true"
      :action="reviewReqOperation.actionSpecs"
      :operation="reviewReqOperation"
      :contacts="reviewReqOperation.displayContacts"
      :totalContacts="reviewReqOperation.totalContacts"
      v-on:closed="doneBulkAction"
      v-on:onError="bulkActionError"
    >
      <!-- use allowScheduling to show hide scheduling modes :allowScheduling="vm && ( vm.areAllPagesSelected == true ) " -->
      <template v-slot:action-slot>
        <ReviewRequestAction
          :action="reviewReqOperation.actionSpecs"
          :locationId="locationId">
        </ReviewRequestAction>
      </template>
    </BulkActionModal>
    <BulkActionModal
      v-if="smsOperation"
      header="Send SMS"
      showRateWarning="true"
      showDisclaimer="true"
      header2="Send sms to"
      submitHeader="Send SMS"
      buttonProcesAllLabel="Send all at once"
      buttonProcessScheduleLabel="Send in drip mode"
      buttonAllAtScheduleLabel="Send all at schedule time"
      startOnLabel="When should we start sending ?"
      dripQuantityLabel="How many people to send in a batch ?"
      dripGapLabel="What should be the delay between batches ?"
      :allowScheduling="true"
      :action="smsOperation.actionSpecs"
      :operation="smsOperation"
      :contacts="smsOperation.displayContacts"
      :totalContacts="smsOperation.totalContacts"
      v-on:closed="doneBulkAction"
      v-on:onError="bulkActionError"
    >
      <!-- use allowScheduling to show hide scheduling modes :allowScheduling="vm && ( vm.areAllPagesSelected == true ) " -->
      <template v-slot:action-slot
        ><SMSAction :sms="smsOperation.actionSpecs"></SMSAction
      ></template>
    </BulkActionModal>
    <BulkActionModal
      v-if="emailOperation"
      showRateWarning="true"
      showDisclaimer="true"
      header="Send Email"
      header2="Send email to"
      submitHeader="Send Email"
      buttonProcesAllLabel="Send all at once"
      buttonProcessScheduleLabel="Send in drip mode"
      buttonAllAtScheduleLabel="Send all at schedule time"
      startOnLabel="When should we start sending ?"
      dripQuantityLabel="How many people to send in a batch ?"
      dripGapLabel="What should be the delay between batches ?"
      :allowScheduling="true"
      :action="emailOperation.actionSpecs"
      :operation="emailOperation"
      :contacts="emailOperation.displayContacts"
      :totalContacts="emailOperation.totalContacts"
      v-on:closed="doneBulkAction"
      v-on:onError="bulkActionError"
    >
      <!-- use allowScheduling to show hide scheduling modes :allowScheduling="vm && ( vm.areAllPagesSelected == true ) " -->
      <template v-slot:action-slot
        ><EmailAction
          :emailOperation="emailOperation.actionSpecs"
          @change="value => (emailOperation.actionSpecs = value)"
        ></EmailAction
      ></template>
    </BulkActionModal>

    <EditAppointmentModal
      :values="appointmentData"
      v-on:hidden="hideAppointmentModal"
      v-on:updatedContact="refreshContactView"
    />
    <OpportunitiesModal
      :values="opportunityModalValues"
      v-on:hidden="hideOppModal"
      :users="locationUsers"
    />
    <SendReviewModal
      @hidden="hideReviewModal"
      :contact="reviewModalContact"
      :showModal.sync="showReviewModal"
    />
    <ContactBulkActionsReport
      v-on:closed="closeBulkOperations"
      :contactId="bulkOperationContactId"
      v-if="bulkOperationContactId"
    />

    <BulkRequestModal
      v-if="delContactOperation"
      :currentLocationId="locationId"
      :total="delContactOperation.totalContacts"
      :visible="showBulkModal"
      v-on:closed="closeBulkModal"
      v-on:confirmBulk="deleteContacts"
      :operation="'delete'"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import {
  SmartList,
  User,
  Contact,
  MessageType,
  Location,
  Opportunity,
  Pipeline,
  Company
} from '@/models'
import SmartListFilters from './SmartListFilters.vue'
import SmartListMigration from './SmartListMigration.vue'
import SmartListLoading from './SmartListLoading.vue'
import { SmartListVM } from './vm/smart_list_vm'
import { ScheduledBulkAction } from './server/bulkreq_api'
import { UxMessage } from '@/util/ux_message'
import PageNumBar from './PageNumBar.vue'
import SmartListItems from './SmartListItems.vue'
import { SafeDropDownToggle } from './safe_drop_down_toggle'
import { ContactsServer } from './server/contacts_server'
import { setTimeout } from 'timers'
import {
  TagAddition,
  TagDeletion,
  ContactDeletion,
  ContactExports,
} from './vm/action_vm'
import TagsModal from './TagsModal.vue'
import ContactOperationModal from './ContactOperationModal.vue'
import { Utils } from '@/util/utils'
import FilterInput from './FilterInput.vue'
import { FilterBuilder } from './vm/filter_vm_builder'
import ListPopup from '../../components/ListPopup.vue'
import { SmartListColumn } from './vm/smart_list_column'
import InternalSourceLink from '../../components/util/InternalSourceLink.vue'
import {
  LocationSmartListSubscriber,
  SmartListChangeType,
} from './server/location_smartlist_changes'
import CampaignSelection from './CampaignSelection.vue'
import SMSAction from './SMSAction.vue'
import EmailAction from './EmailAction.vue'
import ReviewRequestAction from './ReviewRequestAction.vue'
import { ScheduledBulkActions } from './server/bulkreq_api'
import BulkActionsList from './BulkActionsList.vue'
import BulkActionsView from './BulkActionsView.vue'
import TagsSelection from './TagsSelection.vue'
import {BulkRequestV2Subscriber, BulkRequestChangeType} from './server/bulk_request_changes'
import {BulkRequestTracking} from './server/bulk_request_tracking'
const ImportCustomerModal = () =>
  import(
    /* webpackChunkName: "import-customer-m" */ '@/pmd/components/customer/ImportCustomerModalTailwind.vue'
  )

  const ImportCustomerModalOld = () =>
  import(
    /* webpackChunkName: "import-customer-m" */ '@/pmd/components/customer/ImportCustomerModal.vue'
  )

const MergeContactsModal = () =>
  import(
    /* webpackChunkName: "import-customer-m" */ '@/pmd/components/customer/MergeContactsModal.vue'
  )

const BulkRequestModal = () => import('@/pmd/components/customer/BulkRequestModal.vue')

const EditCustomerModal = () =>
  import(/* webpackChunkName: "edit-customer-m" */ './ContactInputModal.vue')


import Avatar from '../../components/Avatar.vue'
import PhoneNumber from '../../components/util/PhoneNumber.vue'
import NotificationHelper from '@/util/notification_helper'
import {
  IBulkCampaignSpecs,
  IBulkEmailSpecs,
  IBulkOpportunitySpecs,
  IBulkSMSSpecs,
  IReviewRequestSpecs,
  ITagOperationSpecs,
  IContactDeleteSpecs
} from './server/bulkreq_interfaces'
import restAgent from '../../../restAgent'
import { BulkRequestV2 } from './server/bulk_req_v2'
const BulkActionModal = () => import('./BulkActionModal.vue')
const ContactBulkActionsReport = () => import('./ContactBulkActionsReport.vue')
const PipelineAction = () =>
  import('@/pmd/components/triggers/PipelineAction.vue')
const EditAppointmentModal = () =>
  import('@/pmd/components/EditAppointmentModal.vue')
const OpportunitiesModal = () =>
  import('@/pmd/components/opportunities/OpportunitiesModal.vue')
const SendReviewModal = () => import('@/pmd/components/SendReviewModal.vue')

declare var $: any

export default Vue.extend({
  name: 'SmartList',
  components: {
    Avatar,
    PhoneNumber,
    SmartListFilters,
    SmartListLoading,
    SmartListMigration,
    PageNumBar,
    SmartListItems,
    ImportCustomerModal,
    ImportCustomerModalOld,
    TagsModal,
    ContactOperationModal,
    EditCustomerModal,
    FilterInput,
    ListPopup,
    BulkActionModal,
    EditAppointmentModal,
    OpportunitiesModal,
    SendReviewModal,
    MergeContactsModal,
    ContactBulkActionsReport,
    PipelineAction,
    CampaignSelection,
    SMSAction,
    EmailAction,
    InternalSourceLink,
    BulkActionsList,
    BulkRequestModal,
    ReviewRequestAction,
    BulkActionsView,
    TagsSelection
  },
  inject: ['uxmessage'],
  data() {
    return {
      locationId: '',
      userId: '',
      vm: {} as SmartListVM | undefined,
      smartLists: [] as SmartList[],
      contacts: [] as Contact[],
      emptyListName: SmartListVM.DEFAULT_LIST_NAME,
      settingsName: SmartListVM.LIST_SETTINGS_VIEW_NAME,
      showListSettings: false,
      currentListName: '',
      showBulkActions: false,
      loading: false as boolean,
      changingList: false as boolean,
      bus: new Vue(),
      hideFilters: true,
      isCurrentPageCheck: false,
      showColumnOptions: false,
      colDispToggler: new SafeDropDownToggle(this, 'colDispToggler'),
      moreDispToggler: new SafeDropDownToggle(this, 'moreDispToggler'),
      allowTabCount: 5, // show max 5 tabs + 1 for all and 1 for more
      //esURL: `${config.baseUrl}/search/smartlist`,
      esURL: FilterBuilder.ES_SEARCH_ENDPOINT,
      importDlgState: null as { show: boolean } | null,
      tagOperation: null as TagAddition | TagDeletion | null,
      delContactOperation: null as ContactDeletion | null,
      exportContactOperation: null as ContactExports | null,
      tagAddOperation2: null as any,
      tagRemoveOperation2: null as any,
      contactDeleteOperation2: null as any,
      pipelineOperation: null as any,
      campaignOperation: null as any,
      reviewReqOperation: null as any,
      smsOperation: null as any,
      emailOperation: null as any,
      MessageType: MessageType,
      newContact: null as Contact | null,
      showContactDialog: false as boolean,
      genericFilter: null as any,
      ignoreGenericFilterTextChange: false,
      showSortOrder: false,
      locationUsers: [] as User[] | null,
      showReviewModal: false,
      reviewModalContact: null as Contact | null | undefined,
      bulkOperationContactId: null as string | null,
      locationListSubscriber: new LocationSmartListSubscriber(
        this.listChangeHandler
      ),
      appointmentData: {
        visible: false,
        contactId: '',
      },
      opportunityModalValues: {
        visible: false,
        opportunity: undefined as Opportunity | undefined,
        currentLocationId: '',
        tab: '',
        pipeline: undefined as Pipeline | undefined,
        contact: undefined as Contact | undefined,
      },
      showAppointmentModal: false,
      showOppModal: false,
      reloadingContacts: [] as string[],
      showMergeModal: false,
      schedBulkActionsCount: 0,
      showBulkModal: false,
      newImportFlow:false,
      bulkActionChangeListener: null as BulkRequestV2Subscriber | null,
      bulkRequestTracking: null as BulkRequestTracking | null,
      temporaryFlag: false // Javier - https://www.loom.com/share/843c64f84a464d4c97803c508d0b575a
    }
  },
  computed: {
    user(): User | undefined {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    automationString() {
      return this.user?.automationString(false) || 'Campaign / Workflow'
    },
    isAdmin(): boolean {
      return this.user && this.user.role === 'admin'
    },
    showSubActions(): boolean {
      return this.isAdmin || this.canCreateReviewRequest
    },
    canViewOpportunities(): boolean {
      return this.user && this.user.canViewOpportunities
    },
    canCreateReviewRequest(): boolean {
      return this.user && this.user.canCreateReviewRequest
    },
    canViewAppointments(): boolean {
      return this.user && this.user.canViewAppointments
    },
    smartListNames(): Array<string> {
      //alert(`${this.smartLists.map(a=>  Utils.safeLC(a.listName))}`)
      if (!this.smartLists || this.smartLists.length <= 0) return []
      else return this.smartLists.map(a => Utils.safeLC(a.listName))
    },
    currentSelectedCount(): number {
      return this.vm && this.vm.selectedContactIds
        ? this.vm.selectedContactIds.length
        : 0
    },
    locationTimeZone(): string {
      return this.$store.getters[`locations/getCurrentLocationTimeZone`]
    },
    highlightListId(): string {
      if (this.showBulkActions) return 'Bulk Actions'
      if (this.showListSettings) return this.settingsName
      return this.currentListName || this.emptyListName
    },
    blockMergeButton(): boolean {
      return this.currentSelectedCount < 2 || this.currentSelectedCount > 10
    },
    company(): Company {
      return this.$store.state?.company?.company
    },
    isV2SidebarEnable() {
      return this.$store.getters['sidebarv2/getVersion'] == 'v2'
    },
  },
  watch: {
    'vm.fetcherror': async function(s){
      if(s) this.uxmessage(UxMessage.errorType('Retry after some time',s),true)
    },
    '$route.params.location_id': async function (locationId: string) {
      try {
        this.locationId = locationId
        this.smartLists = []
        this.currentListName = 'All';
        await this.loadRoute('All')
        await this.resetsForLocation()
      } catch(err) {
        console.log(`Issue loading smart list for location ${locationId}`)
        console.log(err)
      }
    },
    '$route.params.business_name': async function () {
      if (this.$router.currentRoute.params.business_name)
        this.loadCustomFilter(
          { bizName: this.$router.currentRoute.params.business_name}
        )
    },
    '$route.params.import_filter': async function () {
      if (this.$router.currentRoute.params.import_filter)
        this.loadCustomFilter(
          { importId: this.$router.currentRoute.params.import_filter}
        )
    },
    '$route.params.smart_list': async function (n: string) {
      const params = this.$router.currentRoute.params
      if (this.$route.name == 'smart-list-settings-v2') n = 'Settings'
      console.log('Smart List Route', n, this.$route.name)
      if (!params) return
      if (params.business_name) return
      if (params.import_filter) return
      this.currentListName = n;
      //allert(this.currentListName)
      if (this.locationId === params.location_id) {
        this.showList(n)
      }
    },
  },
  methods: {
    async deleteContacts() {
      try {
        const authUser = await this.$store.dispatch('auth/get')
        await this.delContactOperation.action(authUser.userId)
        this.doneBulkAction(true)
      } catch (e) {
        console.log(e);
      }
    },
    closeBulkModal() {
      this.showBulkModal = false
      this.delContactOperation = null
    },
    async resolveFinishMerge(master_id: string) {
      this.showMergeModal = false
      this.$router.push({
        name: 'contact_detail',
        params: { contact_id: master_id },
      })
    },
    async contactChecked(isChecked: boolean) {
      console.log(isChecked)
      if (! isChecked) {
        this.$nextTick(() => {
          if (this.vm) {
            this.vm.resetCurrentPageSelected()
          }
        })
      }
    },
    async resetsForLocation() {
      try {
        const locationId = this.locationId
        await Promise.allSettled([
          this.setupListNames(),
          this.$store.dispatch('locationCustomFields/syncAll', {  locationId }),
          this.$store.dispatch('pipelines/syncAll', locationId),
          this.$store.dispatch('membership/syncAll', { locationId, forceRefresh: true })
        ])
        await FilterBuilder.resetForUser(this.user, locationId) // call after populating custom fields in vuex store
        if (this.vm && this.vm.reset) this.vm.reset() // required
        this.schedBulkActionsCount = 0
        this.fetchScheduledBulkActionsLength()
        this.unplug()
        this.plug()
      } catch(err){
        console.log(err)
      }
    },
    async onlistChange(listId?: string) {
      await this.setupListNames()
      const params = this.$router.currentRoute.params
      if (params && params.smart_list !== this.settingsName) {
        this.showList(listId)
      }
    },
    async setupListNames() {
      try {
        //if (!this.locationListSubscriber) this.locationListSubscriber = new LocationSmartListSubscriber(this.listChangeHandler);
        // this.changingList = true
        await this.locationListSubscriber.suspendListen()
        this.smartLists = []
        let items = await SmartList.getForUserLocation(
          this.user?.id as string,
          this.locationId
        )
        if (this.user && this.user.isAdmin) {
          let adminLists = await SmartList.getGlobalLists(this.locationId)
          if (adminLists) {
            items.push.apply(
              items,
              adminLists.filter(a => a.userId != this.user.id)
            )
          }
        }
        if (items && items.length > 0) {
          items = items.filter(a => a.listName && a.listName.trim() !== '')
          items = items.sort((a, b) => a.displayOrder - b.displayOrder)
          this.smartLists = items
        }
        this.locationListSubscriber.resumeListen()
      } finally {
        // this.changingList = false
      }
    },
    async refreshOnDialogClose(listName){
      const sl= this.smartLists.find(l=>l.listName===listName)
      this.loadRoute(sl.id)
    },
    listChangeHandler(changeType: SmartListChangeType, sl: SmartList) {
      // Do not use async handler
      const browserNotif = false
      const toastNotif = false

      if (!sl || !sl.id || !this.smartLists) return
      if (sl.locationId !== this.locationId) return
      const idx = this.smartLists.findIndex((a: SmartList) => a.id === sl.id)

      if (browserNotif) NotificationHelper.requestBrowserPermission()

      if (changeType === SmartListChangeType.ADDED && idx === -1) {
        this.smartLists = [...[sl], ...this.smartLists]

        if (browserNotif) {
          NotificationHelper.handleMessage({
            data: {
              type: 'smartlist',
              title: 'Smart List Update',
              body: `${sl.listName} shared with you. Click to view this list.`,
              listId: sl.id,
            },
          })
        } else if (toastNotif) {
          const callback = (listId: any) => this.loadRoute(listId)
          const options = {
            link: 'Open List',
            callbackWithInfo: callback,
            returnInfo: sl.id,
          }
          this.uxmessage(
            UxMessage.infoType(
              'New Smart List Shared',
              `Open ${sl.listName}`,
              options
            ),
            true
          )
        }
      } else if (changeType === SmartListChangeType.REMOVED && idx !== -1) {
        this.smartLists.splice(idx, 1)
        if (this.$route.params.smart_list === sl.id) this.loadRoute('All')
        if (browserNotif) {
          NotificationHelper.handleMessage({
            data: {
              type: 'smartlist',
              title: 'Smart List Update',
              body: `${sl.listName} no longer shared with you`,
            },
          })
        } else if (toastNotif) {
          this.uxmessage(
            UxMessage.warningType(
              'Shared Smart List Removed',
              `${sl.listName}`
            ),
            true
          )
        }
      } else if (
        changeType === SmartListChangeType.MODIFIED &&
        this.showListSettings &&
        idx !== -1
      ) {
        sl.displayOrder = this.smartLists[idx].displayOrder
        this.smartLists.splice(idx, 1, sl)
      } else if (
        changeType === SmartListChangeType.MODIFIED &&
        this.vm &&
        this.vm.smartList &&
        this.vm.smartList.id === sl.id
      ) {
        if (browserNotif) {
          NotificationHelper.handleMessage({
            data: {
              type: 'smartlist',
              title: 'Smart List Modified',
              body: `${sl.listName} modified. Click this to refresh.`,
              listId: sl.id,
            },
          })
        } else if (toastNotif) {
          const callback = (listId: any) => this.onlistChange(listId)
          const options = {
            link: 'Open List',
            callbackWithInfo: callback,
            returnInfo: sl.id,
          }
          this.uxmessage(
            UxMessage.infoType(
              'Smart List Modified',
              `Open ${sl.listName}`,
              options
            ),
            true
          )
        }
      }
    },
    async newListAddition(listId?: string) {
      await this.setupListNames()
      this.loadRoute(listId)
    },
    showSettingsView() {
      if (
        this.vm &&
        this.vm.listName !== this.emptyListName &&
        this.vm.hasChanges
      ) {
        this.askToSaveChanges(this.vm, this.settingsName)
      } else this.loadRoute(this.settingsName)
    },
    showBulkActionsView() {
      if (
        this.vm &&
        this.vm.listName !== this.emptyListName &&
        this.vm.hasChanges
      ) {
        this.askToSaveChanges(this.vm, 'bulk_actions')
      } else this.loadRoute('bulk_actions')
    },
    checkAndRoute(goToListId: string, goToListName?: string) {
      if (!goToListId && goToListName) goToListId = goToListName
      if (
        this.changingList !== true &&
        this.vm &&
        this.vm.listName !== this.emptyListName &&
        this.vm.hasChanges
      ) {
        this.askToSaveChanges(this.vm, goToListId)
      } else {
        this.loadRoute(goToListId)
      }
    },
    clearGenericFilter() {
      if (!this.genericFilter) return
      if (this.genericFilter.selectedOption)
        this.genericFilter.selectedOption.firstValue = ''
      if (!this.vm) return
      this.vm.unSelectAll()
      this.vm.removeFilters([this.genericFilter], () => {
        if (this.vm) this.vm.first()
      })
    },
    loadRoute(listId?: string, showRecentImport?: boolean, contact?: Contact) {
      this.clearGenericFilter()
      let v2SideBarPath = 'smart-list-v2'
      if (listId == this.settingsName) v2SideBarPath = 'smart-list-settings-v2'
      const r: { [key: string]: any } = {
        name: this.isV2SidebarEnable ? v2SideBarPath : 'smart_list',
        params: { smart_list: listId },
      }
      if (showRecentImport) {
        r.params.show_recent_import = showRecentImport
        r.params.recent_import_id = '' // this will be set on event handler processImportRequest
        this.$router.push(r)
        return
      }
      if (contact) {
        r.params.show_recent_contact = true
        r.params.recent_first_name = contact.firstName
        r.params.recent_last_name = contact.lastName
        this.$router.push(r)
        return
      }

      if (this.currentListName !== listId) {
        this.currentListName = listId;
        this.$router.push(r)
      } else this.showList(listId)
    },
    async showList(id?: string, previousState?: any) {
      if (id === 'bulk_actions') {
        this.showListSettings = false
        this.showBulkActions = true
        return
      } else if (id === this.settingsName) {
        console.log('Show Settings')
        this.showBulkActions = false
        this.showListSettings = true
        return
      }
      if (!this.smartLists) return
      if (!id || id === this.emptyListName) {
        await this.loadVMForId('', previousState)
        return
      }
      let showables = this.smartLists.length
      if (showables > this.allowTabCount) showables = this.allowTabCount
      for (let i = 0; i < showables; i++) {
        if (this.smartLists[i].id === id) {
          await this.loadVMForId(this.smartLists[i].id, previousState)
          return
        }
      }
      let idx = this.smartLists.findIndex(a => a.id === id)

      if (idx !== -1) {
        const item = this.smartLists[idx]
        this.smartLists.splice(idx, 1)
        this.smartLists.splice(this.allowTabCount - 1, 0, item)
        await this.loadVMForId(item.id, previousState)
      }
    },
    async loadVMForId(id?: string, previousState?: any) {
      this.vm = undefined
      this.showListSettings = false
      this.showBulkActions = false
      this.changingList = true
      this.genericFilter = null
      this.reloadingContacts = []
      // await this.$nextTick();
      try {
        let genericVal = null
        let selectedFilters = previousState
          ? previousState.currentFilters
          : null
        const selectedColumns = previousState
          ? previousState.currentColumns
          : null
        if (selectedFilters) {
          let genericIndex = selectedFilters.findIndex(
            a => a.filterName === FilterBuilder.GENERIC_FILTER
          )
          if (genericIndex !== -1) {
            genericVal = selectedFilters[genericIndex].selectedOption.firstValue
            previousState.currentFilters.splice(genericIndex, 1)
          }
        }

        if (id) {
          let smartListDefn: SmartList | undefined
          if (previousState) {
            smartListDefn = this.smartLists.find(a => a.id === id)
            if (!previousState.currentFilters && smartListDefn)
              selectedFilters = smartListDefn.filterSpecs.filters
          } else {
            smartListDefn = await SmartList.getById(id)
            if (!smartListDefn) throw new Error(`Smart list not found`)
            selectedFilters = smartListDefn.filterSpecs.filters
            let idx = this.smartLists.findIndex(a => a.id === id)
            if (idx !== -1) {
              this.smartLists[idx] = smartListDefn
            }
          }
          this.vm = await SmartListVM.fromExisting(
            new ContactsServer(this.esURL),
            smartListDefn as SmartList
          )
        }

        if (!this.vm) {
          this.vm = await SmartListVM.fromNew(
            new ContactsServer(this.esURL),
            this.user as User,
            this.locationId
          )
        }

        if (!this.vm) return

        if (selectedFilters || selectedColumns) {
          await this.vm.reset(selectedFilters, selectedColumns) // don't forget this.
        }

        this.genericFilter = await this.buildGenericFilter(genericVal)
        if (!Utils.isEmptyStr(genericVal))
          this.vm.addDisplayModelItem(this.genericFilter)

        if (previousState) {
          this.vm.hasUnsavedFilters = previousState.hasUnsavedFilters
          if (previousState.paginations)
            this.vm.paginations = previousState.paginations
          if (previousState.sortDefinitions)
            this.vm.resetColumnSortDisplay(previousState.sortDefinitions)
        }

        if (
          this.vm &&
          this.$route &&
          this.$route.params &&
          this.$route.params.show_recent_import
        ) {
          this.$route.params.show_recent_import = ''
          this.vm.clearFilters()
          this.vm.setRecentImport(this.$route.params.recent_import_id)
        } else if (
          this.vm &&
          this.$route &&
          this.$route.params &&
          this.$route.params.show_recent_contact
        ) {
          this.$route.params.show_recent_contact = ''
          const first = this.$route.params.recent_first_name
          const last = this.$route.params.recent_last_name
          this.$route.params.recent_first_name = ''
          this.$route.params.recent_last_name = ''
          this.vm.clearFilters()
          this.vm.setNewContactFilters(first, last, async () => {
            this.hideFilters = false
            setTimeout(() => this.vm?.first(), 500)
          })
        } else if (!previousState) {
          await this.vm.first()
        } else if (previousState) {
          const sl = previousState
          if (sl.allPageItems === true) this.vm.selectAll()
          if (sl.fewSelected === true){
            this.vm.selectedContactIds = sl.selectedContactIds || []
          }
          this.vm.pageLimit = sl.pageLimit || this.vm.pageLimit
          if (sl.page > 0) {
            await this.vm.jumpTo(sl.page)
          } else {
            await this.vm.first()
          }
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.changingList = false
      }
    },
    reordered() {
      if (!this.smartLists) return
      this.smartLists = this.smartLists.sort(
        (a, b) => a.displayOrder - b.displayOrder
      )
    },
    async genericFilterChange() {
      if (!this.vm || !this.genericFilter) return
      this.vm.unSelectAll()
      if (
        this.genericFilter.selectedOption &&
        !Utils.isEmptyStr(this.genericFilter.selectedOption.firstValue)
      ) {
        if (!this.vm.hasFilter(this.genericFilter.filterName))
          this.vm.addDisplayModelItem(this.genericFilter)
        await this.vm.first()
      } else {
        this.clearGenericFilter()
      }
    },
    async refreshItems(listId: string) {
      if (this.vm && this.vm.smartList.id === listId) await this.vm.first()
    },
    async itemDeleted(listId: string) {
      if (!listId) return
      let idx = this.smartLists.findIndex(a => a.id === listId)
      if (idx !== -1) this.smartLists.splice(idx, 1)
      const params = this.$router.currentRoute.params
      if (params && params.smart_list !== this.settingsName) {
        this.loadVMForId() // refresh only if the we current page is other than settings page
      }
    },
    async smartListAdded(smartList: SmartList) {
      if (!smartList || !smartList.id) return
      let idx = this.smartLists.findIndex(
        (a: SmartList) => a.id === smartList.id
      )
      if (idx !== -1) return
      let temp = [...[smartList], ...this.smartLists]
      this.smartLists = temp.sort((a, b) => a.displayOrder - b.displayOrder)
      const params = this.$router.currentRoute.params
      if (params && params.smart_list !== this.settingsName) {
        this.loadRoute(smartList.id) // refresh only if the we current page is other than settings page
      }
    },
    toggleFilterDisplay() {
      this.hideFilters = !this.hideFilters
    },
    async exportToCSV() {
      if (!this.vm?.smartList) return
      if (this.vm.totalContacts <= 0) return
      if (
        this.vm.areAllPagesSelected !== true &&
        !this.vm.selectedContactIds.length
      ) {
        this.uxmessage(
          UxMessage.infoType('Only selected contacts can be exported')
        )
        return
      }
      const exporter = new ContactExports(this.vm)
      const count = await exporter.exportToCSV()
    },
    async jumpTo(num: number) {
      if (this.vm) await this.vm.jumpTo(num)
    },
    async pageSizeChange(num: number) {
      if (this.vm) {
        this.vm.pageLimit = num
        this.vm.first()
      }
    },
    async askToSaveChanges(
      sm: SmartListVM,
      listId?: string,
      callback?: () => void
    ) {
      if (!sm) return
      let phrases = []
      if (sm.hasFilterChanges) phrases.push('filters')
      if (sm.hasColumnChanges) phrases.push('columns')
      if (sm.hasSortChanges) phrases.push('sorting preferences')
      let changedItem = phrases.join(', ').replace(/,(?=[^,]*$)/, ' and')
      let msg = `The ${changedItem} have changed in ${sm.listName} smart list. Do you want to save changes to ${changedItem} selected?`
      let func = async (resp: string) => {
        if (resp === 'ok') {
          try {
            this.loading = true
            await sm.saveList()
            if (listId) this.loadRoute(listId)
          } catch (exp) {
            console.log(exp)
          } finally {
            this.loading = false
          }
        }
        if (listId) this.loadRoute(listId)
        else if (callback) callback()
      }
      this.uxmessage(
        UxMessage.confirmationType(msg, func, {
          okButton: 'Ok, save changes',
          cancelButton: `Don't save changes`,
        })
      )
    },
    toggleSelectAllColumns() {
      if (!this.vm || !this.vm.cols) return
      else this.vm.cols.toggleSelectAll()
    },
    togglePageSelection() {
      if (!this.vm?.isCurrentPageCheck) this.vm?.selectCurrentPage()
      else this.vm.unSelectCurrentPage()
    },
    selectAllPages() {
      this.vm?.selectAll()
    },
    unSelectAllPages() {
      this.vm?.unSelectAll()
    },
    toggleColumnDisplay() {
      this.showColumnOptions = !this.showColumnOptions
    },
    // loadDetailPage(goToId: string, idx: number) {
    //   if (!this.vm || !this.vm.contacts || this.vm.contacts.length <=0 ) return;
    //   if(this.vm && this.vm.listName !== this.emptyListName && this.vm.hasChanges) {
    //       this.askToSaveChanges(this.vm, null, async ()=> this.showContactPage(goToId, idx));
    //       return;
    //   } else this.showContactPage(goToId, idx)
    // },
    loadDetailPage(goToId: string, idx: number) {
      if (!this.vm || !this.vm.contacts || this.vm.contacts.length <= 0) return
      let ids = []
      let fewSelected = false
      if (
        this.vm.selectedContactIds &&
        this.vm.selectedContactIds.length > 0 &&
        this.vm.areAllPagesSelected !== true
      ) {
        idx = this.vm.selectedContactIds.findIndex(a => a === goToId)
        if (idx === -1) {
          this.uxmessage(
            UxMessage.infoType(
              'Only selected items can be browsed when one or more items are selected'
            )
          )
          return
        }
        ids = [...this.vm.selectedContactIds]
        fewSelected = true
      } else ids = this.vm.contacts.map(a => a.id)

      const sl = {
        listId: this.vm.listId,
        listName: this.vm.listName,
        currentFilters: this.vm.currentFilters,
        currentColumns: this.vm.cols.getSelectedColDefinitions(),
        selectedContactIds: ids,
        currentIndex: idx >= 0 ? idx : 0,
        fewSelected: fewSelected,
        page: this.vm.pageNumber,
        pageLimit: this.vm.pageLimit,
        totalPages: this.vm.getTotalPageCount(),
        totalContacts:
          fewSelected === true ? ids.length : this.vm.totalContacts,
        allPageItems: this.vm.areAllPagesSelected,
        paginations: this.vm.paginations,
        hasUnsavedFilters: this.vm.hasChanges,
        searchApi: fewSelected === true ? '' : this.esURL,
        originRoute: this.$route,
        sortDefinitions: this.vm.sortDefinitions,
      }
      // const contactIds = this.vm.contacts.map((a) => a.id)
      // const id = contactIds[0];
      const push = {
        name: 'contact_detail',
        params: { contact_id: goToId, smart_list_specs: sl },
      }
      this.$router.push(push)
    },
    async buildGenericFilter(firstVal?: any) {
      const loc = new Location(
        await this.$store.dispatch(
          `locations/getById`,
          this.$router.currentRoute.params.location_id
        )
      )
      return FilterBuilder.getGenericFilter(loc ? loc.country : '', firstVal)
    },
    async showImportModalClicked() {
      // if (this.vm) this.vm.clearFilters()
      // this.loadRoute('All', true)
      // this.hideFilters = false
      this.importDlgState = { show: true }
    },
    async processImportRequest(requestId: string) {
      const params: any = this.$route.params
      params.show_recent_import = true
      params.recent_import_id = requestId // this will be set on event handler processImportRequest
      this.vm?.clearFilters()
      await this.$nextTick() // very important wait for dom to update
      this.vm?.setRecentImport(requestId)
      await this.$nextTick() // very important wait for dom to update
      this.hideFilters = false
    },
    async doneBulkAction(refresh: boolean) {
      if (this.delContactOperation
          || this.contactDeleteOperation2
          || this.tagAddOperation2
          || this.tagRemoveOperation2
          || this.importDlgState){
        this.bulkRequestTracking.start(false)
      }
      this.tagOperation = null
      this.delContactOperation = null
      this.exportContactOperation = null
      this.pipelineOperation = null
      this.campaignOperation = null
      this.reviewReqOperation = null
      this.smsOperation = null
      this.emailOperation = null
      this.tagAddOperation2 = null
      this.tagRemoveOperation2 = null
      this.contactDeleteOperation2 = null
      if (this.importDlgState) this.importDlgState.show = false
      await this.$nextTick() // wait for dom to update
      this.importDlgState = null // nullify it to remove import dialog portion from dom.
      await this.$nextTick() // wait for dom to update
      // this.$store.dispatch('contacts/resetAll');
      if (this.vm && refresh === true) {
        this.vm.unSelectAll()
        await this.$nextTick() // wait for dom to update
        this.hideFilters = false
        //this.vm.first();
        this.vm.first()
      }
      this.fetchScheduledBulkActionsLength()
    },
    bulkActionError(error: any) {
      const msg = error ? error.msg || '' : ''
      this.uxmessage(UxMessage.errorType(msg, error))
      this.doneBulkAction(true)
    },
    async bulkActions(
      op:
        | 'add-tag'
        | 'remove-tag'
        | 'delete-contact'
        | 'export-contact'
        | 'change-pipeline'
        | 'bulk-sms'
        | 'bulk-email'
        | 'apply-campaign'
    ) {
      if (!this.vm || !this.vm.selectedContactIds) return

      const total = this.vm.selectedContactIds.length
      if (total <= 0) {
        this.uxmessage(
          UxMessage.infoType(
            'Select one or more contact to start this operation'
          )
        )
        return
      }

      let showContacts: Contact[] = await this.vm.selectedForDisplayOnly() // don't use vm.selectedContacts(); this is an optimization to avoid fetching contacts unnecessarily for Avatar control
      const totContacts = this.vm.areAllPagesSelected
        ? this.vm.totalContacts
        : this.vm.selectedContactIds.length
      if (op === 'review-request') {
        const specs = { type: 'review_request', opType: 'bulk-review-request'} as IReviewRequestSpecs
        this.reviewReqOperation = this.vm.areAllPagesSelected
          ? await ScheduledBulkAction.forQuery(specs,await this.vm.getServerQuery())
          : await ScheduledBulkAction.forSelectedRecords(specs, this.vm.selectedContactIds)
        this.reviewReqOperation.displayContacts = showContacts
        this.reviewReqOperation.totalContacts = totContacts
      } else if (op === 'change-pipeline') {
        const specs = {
          type: 'create_opportunity',
          opType: 'bulk-ops',
          allow_backward: true,
          allow_multiple: false,
        } as IBulkOpportunitySpecs
        this.pipelineOperation = this.vm.areAllPagesSelected
          ? await ScheduledBulkAction.forQuery(
              specs,
              await this.vm.getServerQuery()
            )
          : await ScheduledBulkAction.forSelectedRecords(
              specs,
              this.vm.selectedContactIds
            )
        this.pipelineOperation.displayContacts = showContacts
        this.pipelineOperation.totalContacts = totContacts
      } else if (op === 'bulk-sms') {
        const specs = {
          opType: 'bulk-sms',
          message: '',
          attachments: [],
        } as IBulkSMSSpecs
        this.smsOperation = this.vm.areAllPagesSelected
          ? await ScheduledBulkAction.forQuery(
              specs,
              await this.vm.getServerQuery()
            )
          : await ScheduledBulkAction.forSelectedRecords(
              specs,
              this.vm.selectedContactIds
            )
        this.smsOperation.displayContacts = showContacts
        this.smsOperation.totalContacts = totContacts
      } else if (op === 'bulk-email') {
        const specs = {
          opType: 'bulk-email',
          html: '',
          subject: '',
          attachments: [],
        } as IBulkEmailSpecs
        this.emailOperation = this.vm.areAllPagesSelected
          ? await ScheduledBulkAction.forQuery(
              specs,
              await this.vm.getServerQuery()
            )
          : await ScheduledBulkAction.forSelectedRecords(
              specs,
              this.vm.selectedContactIds
            )
        this.emailOperation.displayContacts = showContacts
        this.emailOperation.totalContacts = totContacts
      } else if (op === 'apply-campaign') {
        const specs = { opType: 'bulk-campaign' } as IBulkCampaignSpecs
        this.campaignOperation = this.vm.areAllPagesSelected
          ? await ScheduledBulkAction.forQuery(
              specs,
              await this.vm.getServerQuery()
            )
          : await ScheduledBulkAction.forSelectedRecords(
              specs,
              this.vm.selectedContactIds
            )
        this.campaignOperation.displayContacts = showContacts
        this.campaignOperation.totalContacts = totContacts
      } else if (op === 'export-contact') {
        this.exportContactOperation = new ContactExports(this.vm)
        this.exportContactOperation.displayContacts = showContacts
        this.exportContactOperation.totalContacts = totContacts
      } else if (op === 'delete-contact') {
        this.delContactOperation = new ContactDeletion(this.vm)
        this.delContactOperation.displayContacts = showContacts
        this.delContactOperation.totalContacts = totContacts
      } else if (op === 'delete-contact-new') {
        const specs = { opType: 'bulk-contact-delete' } as IBulkCampaignSpecs
        this.contactDeleteOperation2 = this.vm.areAllPagesSelected
          ? await ScheduledBulkAction.forQuery(
              specs,
              await this.vm.getServerQuery()
            )
          : await ScheduledBulkAction.forSelectedRecords(
              specs,
              this.vm.selectedContactIds
            )
        this.contactDeleteOperation2.displayContacts = showContacts
        this.contactDeleteOperation2.totalContacts = totContacts
      } else if (op === 'add-tag') {
        const specs = { opType: 'bulk-tag-add' } as ITagOperationSpecs
        this.tagAddOperation2 = this.vm.areAllPagesSelected
          ? await ScheduledBulkAction.forQuery(
              specs,
              await this.vm.getServerQuery()
            )
          : await ScheduledBulkAction.forSelectedRecords(
              specs,
              this.vm.selectedContactIds
            )
        this.tagAddOperation2.displayContacts = showContacts
        this.tagAddOperation2.totalContacts = totContacts
      } else if (op === 'remove-tag') {
        const specs = { opType: 'bulk-tag-remove' } as ITagOperationSpecs
        this.tagRemoveOperation2 = this.vm.areAllPagesSelected
          ? await ScheduledBulkAction.forQuery(
              specs,
              await this.vm.getServerQuery()
            )
          : await ScheduledBulkAction.forSelectedRecords(
              specs,
              this.vm.selectedContactIds
            )
        this.tagRemoveOperation2.displayContacts = showContacts
        this.tagRemoveOperation2.totalContacts = totContacts
      }

      // else if (op === 'delete-contact') {
      //   this.delContactOperation = new ContactDeletion(this.vm)
      //   this.delContactOperation.displayContacts = showContacts
      //   this.delContactOperation.totalContacts = totContacts
      // } else if (op === 'add-tag') {
      //   this.tagOperation = new TagAddition(this.vm)
      //   this.tagOperation.displayContacts = showContacts
      //   this.tagOperation.totalContacts = totContacts
      // } else if (op === 'remove-tag') {
      //   this.tagOperation = new TagDeletion(this.vm)
      //   this.tagOperation.displayContacts = showContacts
      //   this.tagOperation.totalContacts = totContacts
      // }
    },
    addNewContact() {
      this.newContact = new Contact()
      this.newContact.locationId = this.locationId
      this.showContactDialog = true
    },
    async hideEditWindow(created: boolean) {
      this.showContactDialog = false
      if (!created) return
      await this.$nextTick()
      this.newContact = null
    },
    showNewContactFilter(contact: Contact) {
      if (!contact) return
      if (this.vm?.listName === this.emptyListName) {
        this.changingList = true
        this.vm.clearFilters()
        this.vm.setNewContactFilters(
          contact.firstName,
          contact.lastName,
          () => {
            this.hideFilters = false
            setTimeout(async () => {
              await this.vm?.first()
              this.changingList = false
            }, 1000)
          }
        )
      } else {
        setTimeout(() => this.loadRoute('All', false, contact), 500)
      }
    },
    async sort(col?: SmartListColumn) {
      if (!this.vm) return
      await this.vm.sort(col)
    },
    async dialogLaunch(option: string, optionSelectedContact: Contact) {
      if (option === 'review') {
        this.showReviewModal = true
        this.reviewModalContact = optionSelectedContact
      } else if (option === 'appointment') {
        this.showAppointmentModal = true
        this.$nextTick()
        this.appointmentData = {
          visible: true,
          contactId: optionSelectedContact.id,
        }
      } else if (option === 'create_opportunity') {
        this.showOppModal = true
        this.$nextTick()
        this.opportunityModalValues = {
          opportunity: undefined,
          visible: true,
          currentLocationId: this.locationId,
          tab: 'OpportunityComponent',
          pipeline: undefined,
          contact: await this.$store.dispatch(
            'contacts/syncGet',
            optionSelectedContact.id
          ),
        }
      }
    },
    async hideOppModal(opportunity: Opportunity) {
      this.showOppModal = false
      this.opportunityModalValues = {
        visible: false,
        opportunity: undefined as Opportunity | undefined,
        currentLocationId: '',
        tab: '',
        pipeline: undefined as Pipeline | undefined,
        contact: undefined as Contact | undefined,
      }
      if (!opportunity) return
      if (this.canViewOpportunities) {
        this.$router.push({
          name: 'opportunities',
          params: { location_id: this.locationId },
        })
      } else if (opportunity && opportunity.contactId) {
        this.refreshContactView(opportunity.contactId)
      }
    },
    async showBulkOperations(contact: Contact) {
      if (!contact || !contact.id) return
      this.bulkOperationContactId = contact.id
    },
    closeBulkOperations() {
      this.bulkOperationContactId = null
    },
    async hideAppointmentModal(data: any) {
      this.showAppointmentModal = false
      this.appointmentData = {
        visible: false,
        contactId: '',
      }
      await this.$nextTick()
    },
    hideReviewModal() {
      this.showReviewModal = false
      this.reviewModalContact = undefined
    },
    async refreshContactView(contactId: string) {
      try {
        if (!this.vm || this.vm.inProgress) return
        this.reloadingContacts.push(contactId)
        await this.vm.syncSingleContact(
          contactId,
          new ContactsServer(this.esURL)
        )
      } catch (err) {
        console.error(
          'Server side error in fetching single contact. See next log item'
        )
        console.error(err)
      } finally {
        if (this.reloadingContacts || this.reloadingContacts.length) {
          let idx = this.reloadingContacts.findIndex(a => a === contactId)
          if (idx !== -1) this.reloadingContacts.splice(idx, 1)
        }
      }
    },
    getTagOrderList(vm: SmartListVM, tags: string[]) {
      if (!tags || tags.length <= 0) return []
      let searchStrs: string[] = []
      if (
        this.genericFilter?.selectedOption?.condition === 'is' &&
        this.genericFilter?.selectedOption?.firstValue
      ) {
        searchStrs.push(this.genericFilter.selectedOption.firstValue)
      }
      if (vm.displayModels) {
        vm.displayModels.forEach(filter => {
          if (
            filter.filterName === 'Tag' &&
            filter.selectedOption.condition === 'is' &&
            Array.isArray(filter.selectedOption.firstValue)
          ) {
            searchStrs.push(
              ...filter.selectedOption.firstValue.map(a => a.value)
            )
          }
        })
      }
      searchStrs = searchStrs.map((a: string) => a.toLowerCase())
      const matchingTags: string[] = []
      tags.forEach((a: string) => {
        searchStrs.forEach(x => {
          if (a.toLowerCase() === x) matchingTags.push(a)
        })
      })
      return [...matchingTags, ...tags.filter(a => !matchingTags.includes(a))]
    },
    toogleMergeModal() {
      this.showMergeModal = !this.showMergeModal
    },
    async loadCustomFilter(params: {bizName?: string; importId?: string}) {
      this.vm = null
      this.showListSettings = false
      this.showBulkActions = false
      this.changingList = true
      this.genericFilter = null
      this.reloadingContacts = []
      this.smartLists = []
      this.clearGenericFilter()
      try {
        this.changingList = true
        this.locationId = this.$router.currentRoute.params.location_id
        if (!this.locationId) {
          alert('Location Id not found')
          return
        }
        await FilterBuilder.resetForUser(this.user as User, this.locationId)
        this.vm = await SmartListVM.fromNew(
          new ContactsServer(this.esURL),
          this.user as User,
          this.locationId
        )
        if (params && params.bizName) {
          this.vm.addFilterByName(FilterBuilder.BusinessFilterName, null, {
            value: params.bizName,
          })
        } else if (params && params.importId) {
          this.vm.addFilterByName('Import', null, {
            value:  params.importId,
          })
        }
        this.hideFilters = false
        await this.vm.run()
        await this.setupListNames()
        this.genericFilter = await this.buildGenericFilter(null)
      } catch (error) {
        console.log(error)
      } finally {
        this.changingList = false
      }
    },
    async fetchScheduledBulkActionsLength(status: string = 'Scheduled') {
      try {
        const status = { filterAgent: 'legacy', status: 'scheduled'}
        const filters = { status, location_id: this.locationId }
        const items = await ScheduledBulkActions.getList(filters)
        this.schedBulkActionsCount = (items && items.length) || 0
      } catch (err) {
        console.log(err)
      }
    },
    async plug() {
      try  {
        const locationId = this.$router?.currentRoute?.params?.location_id
        this.bulkRequestTracking = new BulkRequestTracking(
          locationId,
          this.user?.id,
          this.bulkActionNotification
        )
        this.bulkRequestTracking.start()
      } catch(err){
        console.log('Error in plug()')
        console.log(err)
      }
      // this.bulkActionChangeListener = new BulkRequestV2Subscriber(
      //   this.locationId,
      //   this.user?.id,
      //   this.bulkActionNotification
      // )
      // this.bulkActionChangeListener?.resumeListen(5000)
    },
    async unplug() {
      try {
      console.log('unplug')
        if (this.bulkRequestTracking)  {
          this.bulkRequestTracking.stop()
        }
        // if (this.bulkActionChangeListener) {
        //   await this.bulkActionChangeListener.terminate()
        // }
        if (this.locationListSubscriber) {
          await this.locationListSubscriber.terminate()
        }
      } catch(err){
        console.log('Error in unplug()')
        console.log(err)
      }
    },
    async bulkActionNotification() {
      if (!this.vm) return
      if (this.vm?.inSilentUpdate) return
      if (this.vm?.inProgress) return
      try {
        //console.log('vm silent update start')
        await this.vm?.run({retainTillFetch: true, silentUpdate: true}) // run hidden refresh of list - no loading
        console.log('vm silent update complete')
      } catch(err){
        console.log(err)
      } finally {
      }
    },
    async decideImportType(){
      try {
        const company = new Company(await this.$store.getters['company/get'])
        this.newImportFlow = company?.newImportFlow
        this.temporaryFlag = true
      } catch(err) {
        console.log(err)
      }
    },
  },
  async mounted() {
    const params = this.$router.currentRoute.params
    if (this.$route.name == 'smart-list-settings-v2') {
      params['smart_list'] = this.settingsName
    }
    this.locationUsers = this.$store.state.users.users.map(
      (user: User) => new User(Object.assign({}, user))
    )

    if (params.location_id) {
      this.locationId = params.location_id
    }

    const smartListName = params.smart_list
    if (smartListName === 'bulk_actions') {
      this.resetsForLocation() // load other details first like custom fields because it may be used by named list
      this.showListSettings = false
      this.showBulkActions = true
    } else if (smartListName === this.settingsName) {
      this.resetsForLocation() // load other details first like custom fields because it may be used by named list
      console.log('Show Settings')
      this.showBulkActions = false
      this.showListSettings = true
    } else if (params.import_filter){
      this.hideFilters = false
      await this.loadCustomFilter(
        { importId: params.import_filter}
      )
    } else if (params.business_name) {
      this.hideFilters = false
      await this.loadCustomFilter(
        { bizName: params.business_name}
      )
    } else if (params.smart_list && params.smart_list !== SmartListVM.DEFAULT_LIST_NAME ) {
      this.hideFilters = false
      this.currentListName = params.smart_list
      await this.resetsForLocation() // load other details first like custom fields because it may be used by named list
      await this.showList(params.smart_list)
    }
     else {
      this.hideFilters = true
      this.currentListName =SmartListVM.DEFAULT_LIST_NAME
      await this.showList( SmartListVM.DEFAULT_LIST_NAME ,params.previous_state)
      await this.resetsForLocation()
    }
    try
    {
      const $selectpicker = $(this.$el).find('.selectpicker')
      if ($selectpicker) $selectpicker.selectpicker()
      this.decideImportType()
    }
    catch(error){
      console.log(error)
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  async beforeDestroy() {
    console.log('before destroy')
    await this.unplug()
  },
})
</script>

<style lang="scss">
/*<!-- this is not scoped so it can be used by others -->*/
tr.loading {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  td {
    height: 50px;
    vertical-align: middle;
    padding: 8px;
    span {
      display: block;
    }
    &.td-check {
      width: 20px;
      span {
        width: 20px;
        height: 20px;
        background-color: rgba(0, 0, 0, 0.15);
      }
    }
    &.td-loading-short {
      max-width: 30px;
    }
    &.td-loading-long {
      max-width: 75px;
    }
    &.td-loading-long,
    &.td-loading-short {
      // padding-right: 100px;
      span {
        height: 20px;
        background: linear-gradient(to right, #eee 20%, #ddd 50%, #eee 80%);
        background-size: 700px 100px;
        animation-name: moving-gradient;
        animation-duration: 1.5s;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.hl_smartlists--main .table tbody tr td .avatar {
  min-width: 50px !important;
}

.table tbody tr td {
  padding: 4px 10px !important;
}

.table thead tr th:first-child {
  padding-left: 15px !important;
}

.table tbody tr td:first-child {
  padding-left: 15px !important;
}

.form-control {
  padding-top: 6px;
  padding-bottom: 6px;
}

.header-underline {
  display: inline-block;
  border-bottom: 1px solid #d5dde1;
  list-style: none;
  width: 100% !important;
  margin-bottom: 20px;
}
.customized-header {
  display: inline-block;
  list-style: none;
  padding: 0;
  width: 100% !important;
  padding-right: 15px;
  padding-left: 15px;
  margin: 0px;
}

@media (min-width: 768px) {
  ul.header-ul {
    display: inline-flex;
    align-items: center;
    margin-bottom: 0;
    overflow-x: auto;
  }

  ul.header-ul li a {
    padding-left: 5px;
    padding-right: 5px;
    border-bottom: 4px solid transparent;
  }
}

ul.header-ul li a {
  display: block;
  color: #607179;
  height: 40px;
  min-width: 40px;
  text-align: center;
}

ul.header-ul li.active a {
  color: #2a3135;
  border-color: #188bf6;
}

ul.header-ul {
  list-style: none;
  padding: 0;
}

ul.header-ul li:not(:last-child) {
  margin-right: 20px;
}

.dropdown {
  /* don't remove this it is very important */
  display: inline-block !important;
}

.bootstrap-select .dropdown-menu li a span.check-mark {
  display: inline-block !important;
  margin-right: 8px !important;
}

div.no-border-radius {
  border-radius: 0px !important;
}

.option > input[type='checkbox']:disabled + label::after {
  background: #dddddd;
  border-color: #dddddd;
}

.copier {
  visibility: hidden;
}

.copy-me:hover .copier {
  visibility: visible;
  display: inline-block;
  margin: 0px 4px;
}

.position-parent {
  position: relative;
  display: inline-flex;
}

.position-stacked {
  font-size: 18px;
  position: absolute;
  line-height: 0 !important;
  padding: 0px 2px !important;
}

.chevron-hover {
  color: lightgray;
}
.chevron-hover:hover {
  color: darkslategray;
}

.chevron-light {
  color: lightgray;
}

.wide-header {
  display: flex;
  justify-content: space-between;
}
.close-content {
  display: flex;
  justify-content: normal;
}
.date-display {
  white-space: pre;
  font-size: small;
}
.min-height-150 {
  min-height: 150px;
}

.list-name-inactive {
  padding-left: 5px !important;
  padding-right: 5px !important;
  color: lightgray !important;
  height: 40px !important;
  min-width: 40px !important;
}

.light-color-wait {
  color: lightgray !important;
  cursor: none;
}

.phone {
  white-space: nowrap;
}

.break-word {
  overflow-wrap: break-word;
  white-space: unset;
  max-width: 60px;
}

.btn-light:disabled {
  background-color: #efefef !important;
  padding-left: 15px !important;
}

.btn[disabled] {
  pointer-events: auto;
}

.--grayfill {
  fill: gray;
}
</style>
