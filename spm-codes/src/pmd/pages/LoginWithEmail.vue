<template>
  <div>
    <section class="hl_login">
      <div class="hl_login--header">
        <div class="container-fluid" style="min-height: 28px;">
          <a href="./">
            <!-- <svg xmlns="http://www.w3.org/2000/svg" width="28" height="26">
                            <image width="28" height="26" xlink:href="data:img/png;base64,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" />
            </svg>-->
            <svg
              v-if="logoURL === 'default'"
              xmlns="http://www.w3.org/2000/svg"
              width="28"
              height="26"
            >
              <image
                width="28"
                height="26"
                xlink:href="data:img/png;base64,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"
              ></image>
            </svg>
            <img v-else-if="logoURL" style="max-height:75px" :src="logoURL">
          </a>
          <!-- <p>Don’t have an account?
                        <a href="/signup">Sign up</a>
          </p>-->
        </div>
      </div>
      <div class="hl_login--body">
        <div class="container-fluid">
          <div class="card" v-if="isOtpScreen === false">
            <div class="card-body">
              <div class="login-card-heading">
                <h2 class="heading2">Sign into your account</h2>
                <h4
                  class="h6"
                  style="color: #000; font-weight: bold"
                  v-show="resetPasswordMsg"
                >
                  A new password is sent to your email.
                </h4>
              </div>
              <div class="form-group">
                <UITextInputGroup
                  id="email"
                  v-model="email"
                  type="text"
                  label="Email"
                  name="email"
                  placeholder="Your email address"
                  v-validate="'required|email'"
                  @keyup.native.enter="submit()"
                  :error="errors.has('email')"
                  :errorMsg="errors.first('email')"
                  :ignoreLastPass="false"
                  :disabled="loading"
                />
              </div>
              <div class="form-group">
                <UITextInputGroup
                  id="password"
                  name="password"
                  label="Password"
                  v-model="password"
                  type="password"
                  placeholder="The password you picked"
                  v-validate="'required'"
                  @keyup.native.enter="submit()"
                  :error="errors.has('password')"
                  :errorMsg="errors.first('password')"
                  :ignoreLastPass="false"
                  :disabled="loading"

                />
                <!-- <a href="javascript:void(0);" class="forgot-password">Forgot password?</a> -->
                <router-link
                  id="forgot_passowrd_btn"
									tag="a"
									class="forgot-password text-curious-blue-500 hover:text-curious-blue-800 font-semibold"
                  :to="{
                    name: 'forgot_password',
                    query: { email: email.trim() },
                  }"
                  >Forgot password?</router-link
                >
              </div>
              <div class="form-group button-holder">
                <div class="global-error">
                  <p v-show="response" stye="text-align:center">
                    <span class="error">{{ response }}</span>
                  </p>
                </div>
                <div style="display: inline-block;position: relative;width: 100%;">
                  <UIButton
                    use="tertiary"
                    @click.prevent="submit()"
                    :loading="loading"
                    class="justify-center w-full"
                  >Sign in</UIButton>
                </div>
              </div>

              <div class="mt-6" v-if="isLoginWithGoogleAllowed">
                <div class="relative">
                  <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-300"></div>
                  </div>
                  <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-white text-gray-500">
                      Or continue with
                    </span>
                  </div>
                </div>
              </div>

              <div class="form-group button-holder mt-3">
                <div id="g_id_signin" class="g_id_signin" data-width="370"></div>
              </div>
              <p class="foot-note">
                By signing in you agree to our
                <a
                  class="text-curious-blue-500 hover:text-curious-blue-800 font-semibold"
                  :href="privacyURL"
                  target="_blank"
                >Terms and Conditions</a>
              </p>
            </div>
          </div>
          <div v-else>
            <LoginOTPScreen :email="email" :password="password" :emailMask="emailMask" :phoneMask="phoneMask" :selectedCompanyId="selectedCompanyId" />
          </div>
        </div>
      </div>
    </section>

    <MultipleUsersLogin :values="multipleUsersFound" @hidden="multipleUsersFound = null" @submit="selectAgencyMultipleUser" />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { AxiosResponse } from 'axios'
import NotificationHelper from '../../util/notification_helper'
import { getHostAndDomain } from '../../util/helper'
import { Company, User } from '@/models'
import firebase from 'firebase/app';
import ContactSupport from '@/pmd/components/common/ContactSupport.vue'
import MultipleUsersLogin from '../components/MultipleUsersLogin.vue'
import restAgent from '@/restAgent'
import LoginOTPScreen from './LoginOTPScreen.vue'
import config from '@/config'

const MoonLoader = () => import('@/pmd/components/MoonLoader.vue')
export default Vue.extend({
  metaInfo: {
    title: 'Login'
  },
  components: {
    MoonLoader, ContactSupport, MultipleUsersLogin, LoginOTPScreen
  },
  data() {
    return {
      response: '',
      email: '',
      password: '',
      loading: false,
      logoURL: '',
      privacyURL: '',
      domainCompanyId: null,
      multipleUsersFound: null,
      resetPasswordMsg: false,
      googleAuth: undefined,
      emailMask: '',
      phoneMask: '',
      isOtpScreen: false,
      selectedCompanyId: '',
      isLoginWithGoogleAllowed: false
    }
  },
  created() {
    this.$http
      .get('/api/brand', { params: getHostAndDomain() })
      .then((response: AxiosResponse) => {
        this.logoURL = response.data.logoURL
        if (response.data.privacyURL) {
          this.privacyURL = response.data.privacyURL
        } else {
          this.privacyURL = 'https://www.gohighlevel.com/privacy'
        }
        this.domainCompanyId = response.data.companyId
        if(response.data.customCSS) {
          let customCSS = document.createElement('style')
          customCSS.type = 'text/css'
          customCSS.media = 'screen'
          customCSS.appendChild(document.createTextNode(response.data.customCSS))
          document.head.appendChild(customCSS)
        }
        if(response.data.theme) {
          document.body.dataset.theme = response.data.theme
        }
      })
      .catch(err => {
        this.logoURL = 'default'
        this.privacyURL = 'https://www.gohighlevel.com/privacy'
      })
  },
  mounted() {
    // const checkAndInitGoogle = setTimeout(() => {
    //   if (google && google.accounts) {
    //     this.googleLoginInit();
    //     clearTimeout(checkAndInitGoogle)
    //   } else {
    //     checkAndInitGoogle();
    //   }
    // }, 1000);

    this.isOtpScreen = false;

    // if (['dev', 'staging'].includes(config.mode) || getHostAndDomain()?.domain?.includes('gohighlevel')) {
    //   this.checkAndInitGoogleLogin();
    // }

    if (this.$router.currentRoute?.query?.email) {
      this.email = this.$router.currentRoute?.query?.email
    }
    if (this.$router.currentRoute?.query?.reset_password) {
      this.resetPasswordMsg = this.$router.currentRoute?.query?.reset_password
    }
  },
  methods: {
    checkAndInitGoogleLogin(interval = 1) {
      if (window.google && google.accounts) {
        this.googleLoginInit();
      } else {
        setTimeout(() => this.checkAndInitGoogleLogin(interval + 1), interval * 500)
      }
    },
    googleLoginInit() {
      google.accounts.id.initialize({
        client_id: config.googleAuthId,
        callback: (params) => this.submit(false, params)
        // login_uri: 'http://localhost:5001/getTestUrl',
        // ux_mode: 'redirect'
      });
      google.accounts.id.renderButton(
        document.getElementById("g_id_signin"),
        { theme: "outline", size: "large", width: 370 }  // customization attributes
      );
      google.accounts.id.prompt(); // also display the One Tap dialog
      this.isLoginWithGoogleAllowed = true
    },
    async submit(selectedAgencyMultipleUsers?: string, googleAuth: any) {
      if (selectedAgencyMultipleUsers && this.googleAuth) {
        googleAuth = this.googleAuth
      }
      this.googleAuth = googleAuth
      const _self = this
      if (!googleAuth) {
        await this.$validator.validateAll()
        if (this.errors.any()) {
          return Promise.resolve(true)
        }
      }

      this.loading = true
      this.response = ''

      const params = {
        ...getHostAndDomain(),
        email: this.email.trim(),
        password: this.password.trim(),
        deviceId: await _self.$store.dispatch('getDeviceId'),
        deviceType: 'web',
        deviceName: navigator.userAgent,
        token: googleAuth?.credential,
        version: 2
      } as { [key: string]: any }
      this.selectedCompanyId = undefined

      if (this.domainCompanyId && this.domainCompanyId !== 'YuTUZlUtrwBtvmgByZDW') {
        params.companyId = this.domainCompanyId // When logging in from White Label domain
        this.selectedCompanyId = this.domainCompanyId
      } else if (selectedAgencyMultipleUsers) {
        params.companyId = selectedAgencyMultipleUsers // When selecting an Agency because of Multiple Users
        this.selectedCompanyId = selectedAgencyMultipleUsers
      }

      restAgent.Oauth.loginWithEmail(params, googleAuth ? 'google' : 'email').then(
        (response: AxiosResponse) => {
          if (response.multipleUsers) {
            this.multipleUsersFound = response.multipleUsers
            this.loading = false
          } else {
            if (!googleAuth && !response.token) {
              this.emailMask = response.email;
              this.phoneMask = response.phone;
              this.isOtpScreen = true;
              return
            }

            this.$store
            .dispatch('auth/set', {
              apiKey: response.apiKey,
              userId: response.userId,
              companyId: response.companyId
            })
            .then(() => {
              return this.$auth.signInWithCustomToken(response.token)
            })
            .then(async () => {
              this.$store.dispatch('auth/set', {
                firebaseToken: response.token,
                apiKey: response.apiKey,
                userId: response.userId,
                companyId: response.companyId,
              });
              NotificationHelper.requestPermission()
              this.$store.dispatch('init')
              const loginCount = firebase.firestore.FieldValue.increment(1)
              await User.collectionRef()
              .doc(response.userId).update({ login_count: loginCount })
              const url = this.$route.query.url
              if (url) {
                this.$router.push({
                  path: decodeURIComponent(url),
                  replace: true
                })
              } else {
                console.info('Redirect to dashboard')
                this.$router.push({ name: 'dashboard', replace: true })
              }
            })
          }
        }
      ).catch(reason => {
        this.response = reason.response
          ? reason.response.data.message
          : 'Unable to log you in at this time.'
        this.loading = false
      })
    },
    async selectAgencyMultipleUser(selectedAgencyMultipleUsers) {
      this.multipleUsersFound = null
      await this.submit(selectedAgencyMultipleUsers)
    }
  }
})
</script>

<style scoped>
.error {
  color: #a94442;
}
/*.container-fluid .card .card-body*/
.hl_login--body .spinner {
  display: inline-block;
  margin: 40px 0px 0px 0px;
  width: 100%;
}
.has-error .form-control,
.has-error .form-control:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.global-error {
  margin-bottom: 10px;
}
.hl_login--body .v-spinner .v-moon1 {
  margin: auto;
}
</style>

