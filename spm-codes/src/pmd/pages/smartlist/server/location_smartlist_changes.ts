import { SmartList, User, Location} from "@/models";
import store from '@/store'
import { subtract } from "lodash";
import { unregisterHelper } from "handlebars";

export enum SmartListChangeType{
  ADDED = 'added',
  MODIFIED = 'modified',
  REMOVED = 'removed'
}

export class LocationSmartListSubscriber{

  public constructor(callback: (changeType: SmartListChangeType, sl :SmartList) =>{}){
    this.callback = callback;
  }

  private locUpdateUnsubscribe = [];
  private callback: (changeType: SmartListChangeType, sl :SmartList) =>{};

  public async resumeListen(){
    await this.suspendListen();
    if (!this.callback) return;
    setTimeout(()=> this.listen(), 1000)
  }

  public async suspendListen(){
    for(let unsub of this.locUpdateUnsubscribe){
      console.log('unbindLocation');
      await unsub();
    }
    this.locUpdateUnsubscribe = [];
  }

  public terminate(){
    this.suspendListen();
    this.callback = null;
  }

  private async listen(){
    const currentUser = new User(await store.dispatch('user/get'));
    const currentLocation = await new Location(store.getters['locations/getCurrentLocation']);
    if (!currentUser || !currentLocation) return;
    const onSnapshotHandler =  (querySnapshot) => { // do not use async
      if (querySnapshot.metadata.hasPendingWrites) return;
      //console.log('global')
      querySnapshot.docChanges().forEach(change => {
        // console.log(change.type);
        const sl = new SmartList(change.doc)
        // console.log(`${change.type} - ${sl.listName} - slid: ${sl.id} - user: ${sl.userId} - isGlobal: ${sl.global}`);
        if (this.callback) this.callback(change.type as SmartListChangeType, sl);
        else this.suspendListen();
      });
    };

    this.locUpdateUnsubscribe.push(
      SmartList.collectionRef()
        .where('user_id', '==', currentUser.id)
        .where('location_id', '==', currentLocation.id)
        .onSnapshot(onSnapshotHandler)
    );
    if (currentUser.isAdmin === true) {
      this.locUpdateUnsubscribe.push(
        SmartList.collectionRef()
        .where('location_id', '==', currentLocation.id)
        .where('global', '==', true)
        .onSnapshot(onSnapshotHandler)
      );
    }
  }


}
