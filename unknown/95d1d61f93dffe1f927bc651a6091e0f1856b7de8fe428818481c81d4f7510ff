<template>
  <div id="review-link" class="card">
    <div class="card-header --no-right-padding">
      <h3 @click="fetchData()">Leave a Review Link</h3>
    </div>
    <div class="card-body">
      <div class="form-group review-link-input">
        <div>
          <div class="review-link-item --link">
            <UITextInputGroup
              type="text"
              icon="icon-link"
              label="Review Link"
              placeholder="google.com/places/"
              v-model="reviewLink"

            />
          </div>
        </div>

        <div>
          <UIButton
            @click.prevent="generateLink"
            use="secondary"
            id="generate_link_btn"
          >
            Generate Link
          </UIButton>
          <template v-if="placeIdErrorDialogData">
            <ReputationGenericModal
              :modelContent="placeIdErrorDialogData"
              @closed="modalClosed()"
            />
          </template>
        </div>
      </div>
      <UIButton
        type="button"
        use="primary"
        @click.stop="saveCustomLink"
        :disabled="saving"
         id="generate_link_save_btn"
      >
        <i class="icon mr-2" :class="saving ? 'icon-clock' : 'icon-ok'"></i>
        Save
      </UIButton>
    </div>
  </div>
</template>

<script lang="ts">
// @ts-nocheck
import ReputationGenericModal from './ReputationGenericModal.vue'
import Vue from 'vue'
import { Location, User } from '@/models'
import { ready } from 'node_modules/@types/jquery'
import firebase from 'firebase/app'

export default Vue.extend({
  data() {
    return {
      googlePlacesId: null,
      placeIdError: null,
      reviewLink: '',
      location: undefined as undefined | Location,
      saving: false as Boolean,
    }
  },
  components: {
    ReputationGenericModal,
  },
  props: {},
  computed: {
    placeIdErrorDialogData: function () {
      return this.placeIdError
    },
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.fetchData(id)
    },
  },

  methods: {
    async saveCustomLink() {
      this.saving = true
      if (this.location.settings && this.location.settings.sms) {
        this.location.settings.sms.review_link = this.reviewLink
      }
      if (this.location.settings && this.location.settings.email) {
        this.location.settings.email.review_link = this.reviewLink
      }
      await this.location.ref.update({
        'settings.email.review_link':this.reviewLink,
        'settings.sms.review_link':this.reviewLink,
         date_updated: firebase.firestore.FieldValue.serverTimestamp(),
      })
      this.saving = false
    },

    modalClosed() {
      this.placeIdError = null
    },
    async generateLink() {
      if (this.googlePlacesId) {
        this.saving = true
        let url = `https://search.google.com/local/writereview?placeid=${this.googlePlacesId}`
        this.reviewLink = url
        if (this.location.settings && this.location.settings.sms) {
          this.location.settings.sms.review_link = this.reviewLink
        }
        if (this.location.settings && this.location.settings.email) {
          this.location.settings.email.review_link = this.reviewLink
        }
        await this.location.ref.update({
          'settings.email.review_link':this.reviewLink,
          'settings.sms.review_link':this.reviewLink,
          date_updated: firebase.firestore.FieldValue.serverTimestamp(),
        })
        this.saving = false
      } else {
        this.placeIdError = {
          title: 'Place ID missing',
          bodyHtml: `You need to connect Google My Business first. You can connect it from
              <a target="_blank" href="../settings/integrations">Integration Settings</a>
            `,
          type: 'info',
        }
      }
    },
    async fetchData() {
      const currentLocationId = this.$route.params.location_id
      this.location = new Location({
        ...(await this.$store.dispatch('locations/getCurrentLocation', currentLocationId)),
      })
      this.googlePlacesId =
        this.location.googlePlacesId || this.location.gmb.places_id || false

      try {
        this.reviewLink =
          this.location.settings.sms.review_link ||
          this.location.settings.email.review_link
      } catch {
        this.reviewLink = ''
      }
    },
  },

  async created() {},
  mounted() {
    this.fetchData()
  },
  updated() {},
})
</script>
<style lang="scss" scoped>
#review-link {
  .card-body {
    max-width: unset;
  }

  .review-link-item {
    margin: unset;
  }

  .hl-primary-btn-width {
    width: 180px;
    padding: 15px;
  }
  .review-link-input {
    display: grid;
    grid-template-columns: auto auto;
    align-items: end;
    grid-column-gap: 10px;
    .gen-link-btn {
      margin: 0 0 0 0;
    }
  }
  .btn-success {
    width: 200px;
  }
}
</style>
