<template>
  <b-modal ref="modal-sm-d" size="sm" @hide="close" hide-footer>
      <template v-slot:modal-title>
        <h5 class="modal-title">
            {{type}} Contacts
        </h5>
      </template>
      <template>
        <div class="modal-body">
          <div class="modal-body--inner">
          <h4>{{type}} following contacts</h4>
            <div class="avatar-group">
              <template v-if="!contacts || contacts.length <= 0">
                <span style="font-size:15px; color:#188bf6">No contact selected</span>
              </template>
              <template v-else v-for="(contact,index) in contacts">
                <Avatar v-if="index < 10" :contact="contact" :include_name="false"/>
                <template v-if="totalContacts >=10 ">
                  <span
                    v-if="index === contacts.length - 1"
                    style="margin-top:10px; font-size:19px,margin-left:10px; color:#188bf6"
                  >{{ totalContacts - 10 }} &nbsp;more contacts...</span>
                </template>
              </template>
            </div>

            <div v-if="type == 'Delete'" style="margin-top: 30px">
              Please be aware that deleting any contact will also remove them from: <br/>Conversation, Notes, Opportunities, Tasks, Appointments, Manual Actions, Campaigns and Workflows.
            </div>

          </div>
        </div>
       <div class="modal-footer">
        <div class="modal-footer--inner">
          <div class="modal-footer--inner nav" v-if="!sending">
            <UIButton
              use="outline"
              class="mx-2"
              @click="close">
              Cancel
            </UIButton>
            <div style="display: inline-block;position: relative;" v-if="contacts.length > 0">
              <UIButton
                v-if="operation"
                use="secondary"
                @click.prevent="operate"
                :loading="sending"
              >
                {{type}} Contacts
              </UIButton>
            </div>
          </div>
          <div class="modal-footer--inner nav" v-else>
            <div class="uploading" style="text-align: center;">
              <div class="progress">
                <div class="progress-bar progress-bar-striped bg-success progress-bar-animated"
                     :style="percent">
                </div>
              </div>
              <p>{{status.updated}}/{{status.total}}</p>
              <!-- <p>{{operation.updated}}/{{total}}</p>
              <p>{{updated}}/{{total}}</p> -->
            </div>
          </div>
        </div>
      </div>
      </template>
  </b-modal>
</template>

<script lang="ts">
import Vue from 'vue'
import { Contact, User, Tag, ImportRequest, ImportType, ImportStatus } from '@/models'
import {IBulkAction, ContactDeletion, ContactExports, StatusCheck} from './vm/action_vm'
import { UxMessage } from '@/util/ux_message'

const Avatar = () => import('../../components/Avatar.vue')
const MoonLoader = () => import('@/pmd/components/MoonLoader.vue')

let unsubscribeImportRequest: () => void;

export default Vue.extend({
  name:"ContactOperationModal",
  props: {
    type : String ,
    contacts: Array as () => Contact[] ,
    operation:  Object as () => IBulkAction ,
    totalContacts: Number
  },
  components: {
    Avatar,
    MoonLoader,
  },
  inject: ['uxmessage'],
  data() {
    return {
      currentLocationId: '',
      sending: false,
      status: this.operation.status || new StatusCheck()
    }
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
  },
  async mounted(){
    await this.$nextTick();
    this.showPopup();
  },
  methods: {
    async operate() {
      try {
        this.sending = true
        const authUser = await this.$store.dispatch('auth/get')
        await this.operation.action(authUser.userId, undefined , (status: string) =>{
          this.sending = false
          if (status !== 'success'){
            this.showError(status);
          } else {
            this.close(true);
          }
        })
      } catch (e) {
        this.sending = false
        console.log(e);
        this.showError(e && e.message || 'unknown error',e);
        this.$emit('onError',e);
      }
    },
    showError(err:string, stack?:string){
      this.uxmessage(UxMessage.errorType(err,stack))
    },
    close(processed: boolean = false) {
      this.hidePopup();
      this.$emit('closed', processed)
    },
    showPopup(){
      if(this.$refs['modal-sm-d']) this.$refs['modal-sm-d'].show();
    },
    hidePopup(){
      if(this.$refs['modal-sm-d']) this.$refs['modal-sm-d'].hide();
    }
  },
  computed: {
    percent(): { [key: string]: any } {
      return { width: (this.status.updated / this.status.total ) * 100 + '%' }
    },
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.currentLocationId = id
    },
  },
})
</script>
<style>
</style>
