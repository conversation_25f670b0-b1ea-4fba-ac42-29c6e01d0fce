import Vue from 'vue'
import { Mo<PERSON><PERSON>, ActionContext } from 'vuex'
import { RootState, CalendarProviderState } from './state_models'
import CalendarProvider from '../models/calendar_provider'

let calendarProviderListner
export const calendarProviders: Module<CalendarProviderState, RootState> = {
  namespaced: true,
  state: { calendarProviders: [], locationId: undefined },
  getters: {
    getById: (state: CalendarProviderState) => {
      return (id: string) => state.calendarProviders.find(x => x.id === id)
    },
    calendarsWithErrors(state: CalendarProviderState) {
      return state.calendarProviders.filter(
        c =>
          c.linked_calendars &&
          c.linked_calendars.google &&
          c.linked_calendars.google.error
      )
    },
    errorMsg: (state: CalendarProviderState, getters) => {
      if (getters.calendarsWithErrors.length === 0) return null

      let msg =
        'There is a problem with the integration on these calendarProviders'
      getters.calendarsWithErrors.forEach(cal => {
        msg += cal.name + ', '
      })
      msg = lodash.trimEnd(msg, ', ')
      msg += '"'
      return msg
    }
  },
  mutations: {
    set: (
      state: CalendarProviderState,
      payload: { calendarProviders: any[]; locationId: string }
    ) => {
      state.calendarProviders = payload.calendarProviders
      if (payload.locationId) {
        state.locationId = payload.locationId
      }
    },
    clearAll(state: CalendarProviderState) {
      if (calendarProviderListner) {
        calendarProviderListner()
      }

      state.calendarProviders = []
      state.locationId = undefined
    }
  },
  actions: {
    async syncAll(
      context: ActionContext<CalendarProviderState, RootState>,
      locationId: string
    ) {
      if (!locationId) {
        context.commit('clearAll')
        return
      }


      if (context.state.locationId === locationId) {
        return context.state.calendarProviders
      }

      return new Promise((resolve, reject) => {

        if (calendarProviderListner) {
          calendarProviderListner()
        }

        calendarProviderListner = CalendarProvider.fetchAllByLocation(
          locationId
        ).onSnapshot(snapshot => {
          context.commit('set', {
            locationId: locationId,
            calendarProviders: snapshot.docs.map(d => {
              return {
                id: d.id,
                ...d.data()
              }
            })
          })

          resolve(context.state.calendarProviders)
        })
      })
    },
    logout: {
      root: true,
      handler: (context: ActionContext<CalendarProviderState, RootState>) => {
        context.commit('clearAll')
      }
    },
    clearAll: {
      handler: (context: ActionContext<CalendarProviderState, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
