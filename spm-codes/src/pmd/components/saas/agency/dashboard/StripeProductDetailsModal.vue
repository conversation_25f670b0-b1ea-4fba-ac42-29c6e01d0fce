<template>
    <modal v-if="show" @close="$emit('close')" maxWidth="500" :showCloseIcon="true">
        <div >
            <div class="modal-heading-wrap">
                <div class="modal-heading-content">
                    <h4>Stripe Product Details</h4>
                </div>
            </div>
            <div v-if="loading" style="padding: 30px;">
              <upgrade-modal-shimmer/>
            </div>
            <div class="modal-body --wide" v-else>
              <div class="product-details__wrap">
                <div class="product-details__row">
                  <div class="product-details__label">Product Name:</div>
                  <div class="product-details__value">{{plan.title}}</div>
                </div>
                <!-- <div class="product-details__row">
                  <div class="product-details__label">Product Id:</div>
                  <div class="product-details__value">
                    <div class="product-details__id-row">
                      {{plan.stripeProductId}}
                      <i class="far fa-copy" @click="copyText(plan.stripeProductId)"></i>
                    </div>
                  </div>
                </div> -->
              </div>
              <div class="product-details__wrap --price" v-for="(price,i) in activePrices" :key="price.id">
                <div class="product-details__row">
                  <div class="product-details__label">{{price.billingInterval === 'month' ? 'Monthly' : 'Annual'}} Plan Details:</div>
                  <div class="product-details__value">{{price.symbol || '$'}}{{price.amount/100}}/{{price.billingInterval}}</div>
                  <!-- <div class="product-details__copy"></div> -->
                </div>
                <div class="product-details__row">
                  <div class="product-details__label">{{price.billingInterval === 'month' ? 'Monthly' : 'Annual'}} Plan Id:</div>
                  <div class="product-details__value">
                    <div class="product-details__id-row">
                      {{price.id}}
                      <i class="far fa-copy" @click="copyText(price.id)"></i>
                    </div>
                    <div class="product-details__copied-overlay" v-if="copyId === price.id">
                      <div class="product-details__id-row">Copied to clipboard
                        <!-- <i class="fas fa-copy"></i> -->
                      </div>

                    </div>
                  </div>
                  <!-- <div class="product-details__copy"><i class="far fa-copy" @click="copyText(plan.stripeProductId)"></i></div> -->
                </div>
              </div>
            </div>
        </div>
    </modal>
</template>

<script>
import Modal from '@/pmd/components/common/Modal.vue'
import UpgradeModalShimmer from '@/pmd/components/common/shimmers/UpgradeModalShimmer.vue'
import copyToClipboard from '@/util/copy-to-clipboard'

export default {
  props: ['show', 'plan'],
  components: { Modal, UpgradeModalShimmer },
  computed:{
    company() {
      return this.$store.state.company.company
    },
    activePrices() {
      if (this.plan.stripePlans) {
        return this.plan.stripePlans.filter( price => price.active === true)
      } else {
        return this.plan.prices.filter( price => price.active === true)
      }
    },
  },
  created(){
  },
  data(){
    return {
      loading: false,
      snapshotTab: 'own',
      copyId: ''
    }
  },
  methods: {
    copyText(text) {
      copyToClipboard(text)
      this.copyId = text;
      setTimeout( ()=>{
        this.copyId = ''
      },1000)
    },
  }
}
</script>

<style scoped lang="scss">
.modal-body.--wide{
  padding: 30px;
}
.modal-heading-wrap {
    padding: 30px 50px 0px;
}
.modal-heading-content {
	text-align: center;
	padding-bottom: 20px;
	border-bottom: 2px solid #f2f7fa;
}

.product-details__wrap{
  display: table;
  &.--price{
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #e7e7e7;
  }
  .product-details__row{
    display: table-row;
    .product-details__label{
      display: table-cell;
      font-weight: 600;
      min-width: 150px;
    }
    .product-details__value{
      display: table-cell;
      width: 100%;
      position: relative;
      // min-width: 200px;
      .product-details__id-row {
        display: flex;
        align-items: center;
        justify-content: space-between;

        background-color: rgba(221,223,224, 0.50);
        border-radius: 2px;
        padding: 4px 12px;
        color: #575757;
        // margin-top: 8px;

        i{
          cursor: pointer;
          margin-left: 24px;
          opacity: 0.7;
          &:hover {
            opacity: 1;
          }
        }
      }
      .product-details__copied-overlay{
        position: absolute;
        width: calc(100% - 30px);
        top: 4px;
        left: 0px;
        background-color: #fff;
        font-size: 11px;
        z-index: 2;
        .product-details__id-row{
          justify-content: flex-end;
        }
      }
    }
    // .product-details__copy{
    //   display: table-cell;
    //   padding-left: 24px;
    //   cursor: pointer;
    // }
  }
}
</style>
