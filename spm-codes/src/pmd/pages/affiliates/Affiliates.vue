<template>
    <div>
        <SideBarV2 v-if="getSideBarVersion === 'v2'"/>
        <SideBar v-else />
        <TopBar />
    <router-view/>
    </div>
</template>

<script lang="ts">
    import Vue from 'vue'
    import TopBar from '../../components/TopBar.vue'
    import SideBar from '../../components/agency/SideBar.vue'
    import SideBarV2 from '../../components/sidebar/SideBar.vue'
    import { User } from '@/models'

    export default Vue.extend({
       components: {
           TopBar,
           SideBar,
           SideBarV2
       }, 
       computed: {
           user() {
                const user = this.$store.state.user.user
                return user ? new User(user) : undefined
            },
            getSideBarVersion(): string {
                return this.$store.getters['sidebarv2/getVersion'] 
            },
       }
    })
</script>

<style scoped>

</style>