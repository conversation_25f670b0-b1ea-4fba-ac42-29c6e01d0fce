<template>

  <div class="filter-item"  v-if='isLocked'>
    <!-- <div> Locked : {{isLocked}} </div> -->
       <div class="d-inline-flex" style="width:100%">
          <div class="avatar_img --blue float-right ml-0 mr-2" style="margin-top:3px;" ><span><i class="fa fa-filter --white" style="font-size:x-small"></i></span></div>
          <div class="float-left ml-1 mr-2 overflow-break" :class="{'--gray': (filter && filter.isLocked)}" style="width:85%">{{filter.title}}</div>
          <div><i class="fa fa-lock --gray mx-1" style="font-size:small"></i></div>
       </div>
  </div>
  <div class="filter-item" v-else>
    <!-- <div> Locked : {{isLocked}} </div> -->
    <a ref="refcol" data-toggle="collapse"
       @click.stop.prevent="toggle()"
       :aria-expanded="expand  ? 'true' : 'false'"
       role="button">
       <div class="d-inline-flex" style="width:100%">
            <div class="avatar_img --blue float-right ml-0 mr-2" style="margin-top:3px;" ><span><i class="fa fa-filter --white" style="font-size:x-small"></i></span></div>
            <div class="float-left mx-2 overflow-break" :class="{'--gray': (filter && filter.isLocked)}" style="width:85%">{{filter.title}}</div>
            <div class="float-right pointer ml-1 mr-2" @click.stop.prevent="filterRemove" >
              <i class="fa fa-times fa-mods zoomable"></i>
            </div>
            <div class="float-right pointer mx-1"> <i class="fa fa-chevron-down fa-mods zoomable"></i></div>
       </div>
    </a>
    <div class="collapse" v-bind:class="{show : value}">
      <div class="filter-option">
          <expander-options :filter="filter"
                             v-on:filterUpdate="filterUpdate">
          </expander-options>
      </div>
    </div>
  </div>
</template>

<script lang="ts">

import Vue from 'vue';
import { IFilter } from './vm/filter_vm';
import FilterOptions from './FilterOptions.vue'


export default Vue.extend({
  name: 'FilterExpander',
	props: {
    bus : Vue,
    filter : IFilter,
    isLocked: Boolean,
		value: Boolean
  },
  components:{
    'expander-options': FilterOptions
  },
	computed: {
		expand: {
			get() { return this.value },
      set(v) {
          //alert(v);
          this.$emit("input", v);
          this.$emit('expanding',this.group);
       }
		}
  },
  methods: {
    async filterUpdate(item: IFilter){
       this.$emit('filterUpdate',this.filter);
    },
    async filterRemove(){
       this.$emit('filterRemove',[this.filter]);
    },
    async toggle(){
      this.expand = !this.expand;
    },
  },
})
</script>

<style scoped>
 .avatar_img {
   min-width: 20px !important;
   width: 20px !important;
   min-height: 20px !important;
   height: 20px !important;
   padding: 2px;
 }

 .overflow-break {
   overflow-wrap: anywhere;
 }

</style>
