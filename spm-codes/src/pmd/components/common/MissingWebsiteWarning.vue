<template>
  <HLWarning
    heading="Missing website"
    :modal="true"
    :showAlert="showComapnyWebsiteWarning"
    title="Please enter your business website"
    description="We Noticed you haven't added your company website in settings. It helps us in improving lead generation for you"
    @closeWarning="closeAlert"
  >
    <template v-slot:action>
      <div class="d-flex justify-content-between">
        <input
          type="text"
          placeholder="enter website"
          class="form-control mr-4"
          v-validate="'required|validUrl'"
          v-model="locationWebsite"
          name="websiteURL"
        />
        <button
          type="button"
          class="btn btn-primary"
          role="button"
          title="Lead"
          @click.stop="saveWebsite"
        >
          <moon-loader
            v-if="saving"
            :loading="saving"
            color="#188bf6"
            size="21px"
          />
          <span v-else>Save</span>
        </button>
      </div>
      <span v-show="errors.has('websiteURL')" class="--red">{{
        errors.first('websiteURL') === 'Invalid url.'
          ? 'Invalid website URL.'
          : 'The website requires an URL.'
      }}</span>
    </template>
  </HLWarning>
</template>

<script>
import { User, Location } from '@/models'
import HLWarning from '@/pmd/components/common/HLWarning.vue'
const LAST_OPEN_WIDGET_GAP_HOURS = 7 * 24 // gap of 7 days is required to show this dialog next time
const DEFAULT_URL = 'https://'

export default {
  props: ['locationId'],
  components: {
    HLWarning,
  },
  data() {
    return {
      location: {},
      currentLocationId: '',
      showComapnyWebsiteWarning: false,
      locationWebsite: DEFAULT_URL,
      saving: false,
    }
  },
  created() {
    this.currentLocationId =
      this.locationId || this.$router.currentRoute.params.location_id
  },

  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.showComapnyWebsiteWarning = false
      this.fetchData()
    },
    locationId: function (id) {
      this.currentLocationId = id
    },
  },

  mounted() {
    this.fetchData()
  },
  computed: {
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
  },

  methods: {
    async fetchData() {
      this.location = new Location(
        await this.$store.dispatch('locations/getById', this.currentLocationId)
      )
      setTimeout(async () => {
        if (this.location && !this.location.website) {
          if (
            !this.location.data.missing_webiste_alert_dismissed ||
            (this.location.data.missing_webiste_alert_dismissed.seconds +
              60 * 60 * LAST_OPEN_WIDGET_GAP_HOURS) *
              1000 <
              +new Date()
          )
            this.showComapnyWebsiteWarning = true
        }
      }, 5000)
    },
    async closeAlert() {
      await Location.collectionRef().doc(this.currentLocationId).update({
        missing_webiste_alert_dismissed: new Date(),
      })
      this.showComapnyWebsiteWarning = false
    },
    async saveWebsite() {
      if (this.errors.has('websiteURL')) {
        return
      }
      this.saving = true
      await this.location.ref.update({
        website: this.locationWebsite,
      })
      this.showComapnyWebsiteWarning = false
      this.saving = false
      this.locationWebsite = DEFAULT_URL
    },
  },
}
</script>

<style scoped>
.moon-loader {
  color: var(--blue);
}
</style>
