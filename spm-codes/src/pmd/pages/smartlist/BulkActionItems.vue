<!-- prettier-ignore -->
<template>
    <section >
      <div  class="container-fluid">
        <!-- User - {{selectedUserId}} ---- Type - {{selectedType}} ----- Filter - {{selectedFilter}} -->
        <div class="card hl_dashboard--latest-review-requests" >
          <div class="card-body --no-padding">
            <div class="table-wrap" v-if="isLoading">
              <table class="table table-sort">
                <tbody>
                <tr  v-for="index in 7" :key="index">
                  <td v-for="index2 in 7" :key="index2">
                    <span class="workinprogress"></span>
                  </td>
                </tr>
                </tbody>
              </table>
             </div>
            <div class="table-wrap" v-else >
              <table class="table table-sort">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Bulk Operation <small>(Type)</small></th>
                    <th>Status</th>
                    <th><div class="date-display text-nowrap">Created</div><div class="date-display text-nowrap"><small>(Date and Time)</small></div></th>
                    <th>User</th>
                    <th><div class="date-display text-nowrap">Completed</div><div class="date-display text-nowrap"><small>(Date and Time)</small></div></th>
                    <th>Statistics</th>
                    <th style="max-width:100px">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <template v-for="request in items">
                    <template v-if="request.loading">
                      <tr :key="request.id">
                        <td v-for="index22 in 8" :key="index22">
                        <span class="workinprogress"></span>
                        </td>
                      </tr>
                      <!-- <tr :key="request.id" style="border: 0px solid !important;">
                        <td style="border: 0px solid !important;" colspan="100" >
                          <div  class="h5 --light d-flex justify-content-end px-4 py-2"> Loading .... </div>
                        </td>
                      </tr> -->
                    </template>
                    <template v-else>
                    <tr :key="request.id">
                      <td>
                        <div class="align-self-center" style="max-width:200px">
                          <div v-if="request.operationNote" class="py-1">{{request.operationNote}}</div>
                          <!-- <div><span class="my-1" v-if="request.operationDescription">{{ request.operationDescription }}</span></div> -->
                        </div>
                      </td>
                      <td >
                        <div class="align-self-center">
                          <div class="text-nowrap capitalize">
                            {{request.operationType ? request.operationType.replaceAll('-', ' ') : '' }}
                          </div>
                          <!-- <div class="tag my-1" v-if="!request.selectedRecordsCount && !request.hasFrozenList"><small>uses dynamic list</small></div> -->
                        </div>
                      </td>
                      <td style="width:130px;">
                          <!-- <span v-if="request.currentStatus=='pending' || request.currentStatus=='resumed'" class="--dark text-capitalize">{{request.currentStatus}}</span>
                          <span v-else-if="request.currentStatus=='complete'" class="--green text-capitalize">{{request.currentStatus}}</span>
                          <span v-else-if="request.currentStatus=='waiting'" class="--yellow text-capitalize">Queueing</span>
                          <span v-else-if="request.currentStatus=='processing' || request.currentStatus=='queueing'" class="--yellow text-capitalize">{{request.currentStatus}}</span>
                          <span v-else-if="request.currentStatus=='cancelled'" class="--red text-capitalize">{{request.currentStatus}}</span>
                          <span v-else-if="request.error" class="--red text-capitalize"
                                v-b-tooltip.hover :title="`${request.error}`">{{request.currentStatus}}</span>
                          <span v-else class="-orange text-capitalize">{{request.currentStatus}}</span> -->
                          <div class="capitalize"
                              :class="{ '--green':  request.simplifiedStatus === 'complete',
                                        '--blue':  request.simplifiedStatus === 'scheduled',
                                        '--yellow':  request.simplifiedStatus === 'processing',
                                        '--purple':  request.simplifiedStatus === 'paused',
                                        '--red':  request.simplifiedStatus === 'cancelled' || request.simplifiedStatus === 'error'
                                      }">
                              <span>{{request.simplifiedStatus}}</span>
                              <span v-if="request.simplifiedStatus === 'processing'" class="mx-1">{{request.getPercentComplete()}}</span>
                          </div>
                          <div v-if="request.advancedStatus" class="my-1">
                            <span class="--gray capitalize">{{request.advancedStatus}}</span>
                          </div>
                          <div>
                            <a class="my-1 capitalize --blue" href="javascript:void()"
                                                    @click.prevent.stop="openDescriptionReport(request)">
                                                    (View Details)
                            </a>
                          </div>
                      </td>
                      <td style="width:130px;">
                          <div v-if="request.dateAdded" style="align-items: normal !important;"
                              :set="parts = getDateParts(request.dateAdded, locationTimeZone)">
                              <!--getDateParts(request.processingStartedOn, locationTimeZone)-->
                            <div class="date-display text-nowrap">{{parts[0]}}</div>
                            <div class="date-display --gray text-nowrap">{{parts[1]}} <small>({{parts[2]}})</small></div>
                          </div>
                      </td>
                      <td>{{ request.byUserName || findUsername(request.byUserId) }}</td>
                      <td style="width:130px;">
                        <div v-if="request.simplifiedStatus === 'complete' && request.processingCompletedOn"
                            style="align-items: normal !important;"
                            :set="parts = getDateParts(request.processingCompletedOn, locationTimeZone)">
                            <!--getDateParts(request.processingStartedOn, locationTimeZone)-->
                          <div class="date-display text-nowrap">{{parts[0]}}</div>
                          <div class="date-display --gray text-nowrap">{{parts[1]}} <small>({{parts[2]}})</small></div>
                        </div>
                        <div v-if="request.simplifiedStatus === 'cancelled' && request.processingCancelledOn"
                            style="align-items: normal !important;"
                            :set="parts = getDateParts(request.processingCancelledOn, locationTimeZone)">
                            <!--getDateParts(request.processingStartedOn, locationTimeZone)-->
                          <div class="date-display text-nowrap">{{parts[0]}}</div>
                          <div class="date-display --gray text-nowrap">{{parts[1]}} <small>({{parts[2]}})</small></div>
                        </div>
                        <!-- {{request.nextProcessingOn}} -->
                         <!-- <div v-if="request.currentStatus !=='processing' && request.currentStatus !=='queueing' && request.currentStatus !=='waiting'">
                          <div v-if="request.nextProcessingOn" style="align-items: normal !important;"
                              :set="parts = getDateParts(request.nextProcessingOn, locationTimeZone)">
                            <div class="date-display text-nowrap">{{parts[0]}}</div>
                            <div class="date-display --gray text-nowrap">{{parts[1]}} <small>({{parts[2]}})</small></div>
                          </div>
                         </div> -->
                      </td>
                      <td>
                        <div v-if="canShowStats(request) && !request.showStats">
                            <a class="my-1 capitalize" href="javascript:void()"
                                                  @click.prevent.stop="getStats(request.id)"> Show Stats
                           </a>
                        </div>
                        <div v-if="request.showStats">
                            <a class="my-1 capitalize" href="javascript:void()"
                                                  @click.prevent.stop="request.showStats = false"> Hide Stats
                           </a>
                        </div>
                      </td>
                      <td style="max-width:60px" >
                        <template v-if="!isLoading
                                        && request.operationType !== 'bulk-import'
                                        && !(request.simplifiedStatus === 'complete' || request.simplifiedStatus === 'cancelled' || request.simplifiedStatus === 'error')">
                          <div class="d-flex justify-content-center">
                            <div class="dropdown bootstrap-select more-select-left">
                              <button
                                type="button"
                                class="btn dropdown-toggle more-select"
                                data-toggle="dropdown"
                                aria-haspopup="true"
                                aria-expanded="false"></button>
                              <div class="dropdown-menu dropdown-menu-right" style="min-height:60px;">
                                <a v-if="request.currentStatus === 'paused'"
                                  @click.prevent="action('resume', request)"
                                  class="mx-1 my-1 pointer dropdown-item"
                                  href="javascript:void(0);">
                                  <i class="icon icon-play-filled fa-align-justify mx-2 --blue"></i>Resume
                                </a>
                                <a v-if="request.canPause"
                                  @click.prevent="action('pause', request)"
                                  class="mx-1 my-1 pointer dropdown-item"
                                  href="javascript:void(0);">
                                  <i class="icon icon-pause-filled fa-align-justify mx-2 --blue"></i>Pause
                                </a>
                                <a v-if="request.canCancel"
                                  @click.prevent="action('cancel', request)"
                                  class="mx-1 my-1 pointer dropdown-item"
                                  href="javascript:void(0);">
                                  <i class="icon icon-close-circle-filled fa-align-justify mx-2 --blue"></i>Cancel
                                </a>
                                <a v-if="request.canEditSprintLimit"
                                    @click.stop.prevent="editSprintLimit(request)"
                                    class="mx-1 my-1 pointer dropdown-item"
                                    href="javascript:void(0);">
                                <i class="icon icon-pencil fa-align-justify mx-2 --blue"></i>
                                  Edit Batch Quantity
                                </a>
                                <a v-if="request.canEditSchedule"
                                    @click.stop.prevent="editSchedule(request)"
                                    class="mx-1 my-1 pointer dropdown-item"
                                    href="javascript:void(0);">
                                <i class="icon icon-pencil fa-align-justify mx-2 --blue"></i>
                                  Edit
                                </a>
                              </div>
                            </div>
                          </div>
                        </template>
                        <template v-else-if="!isLoading
                                              && request.operationType === 'bulk-import'">
                          <div class="d-flex justify-content-center">
                            <div class="dropdown bootstrap-select more-select-left">
                              <button
                                type="button"
                                class="btn dropdown-toggle more-select"
                                data-toggle="dropdown"
                                aria-haspopup="true"
                                aria-expanded="false"></button>
                              <div class="dropdown-menu dropdown-menu-right" style="min-height:60px">
                                <a v-if="(request.simplifiedStatus !== 'complete' && request.simplifiedStatus !== 'cancelled' && request.simplifiedStatus !== 'error')"
                                  @click.prevent="cancelBulkImport(request)"
                                  class="mx-1 my-1 pointer dropdown-item"
                                  href="javascript:void(0);">
                                  <i class="icon icon-close-circle-filled fa-align-justify mx-2 --blue"></i>Cancel
                                </a>
                                <a @click.prevent="revert(request)"
                                  v-if="(request.simplifiedStatus === 'complete' && !request.advancedStatus)"
                                  class="mx-1 my-1 pointer dropdown-item"
                                  href="javascript:void(0);">
                                  <div class="d-inline-flex justify-content-start" style="width: 100%">
                                    <i class="fa fa-undo fa-align-justify mx-2 pt-1 --blue"></i>
                                    <span>Revert</span>
                                  </div>
                                </a>
                                <a @click.prevent="downloadErrorLog(request)"
                                  v-if="(request.simplifiedStatus === 'complete' || request.simplifiedStatus === 'cancelled' || request.simplifiedStatus === 'error')"
                                  class="mx-1 my-1 pointer dropdown-item"
                                  href="javascript:void(0);">
                                  <div class="d-inline-flex justify-content-start" style="width: 100%">
                                    <i class="icon icon-play-filled fa-align-justify mx-2 pt-1 --blue"></i>
                                    <span>Download Error Log</span>
                                  </div>
                                </a>
                                <a @click.prevent="showSmartList({import_filter: request.operationImportId })"
                                    class="mx-1 my-1 pointer dropdown-item"
                                    href="javascript:void(0);">
                                  <div class="d-inline-flex justify-content-start text-break" style="width: 100%">
                                    <i class="icon icon-edit fa-align-justify mx-2 pt-1 --blue"></i>
                                    <span>Show Imported</span>
                                  </div>
                                </a>
                              </div>
                            </div>
                          </div>
                        </template>
                      </td>
                    </tr>
                    <!-- <tr v-else-if="request.showStats === true && request.operationType === 'bulk-sms'"
                        :key="`${request.id}-1`"
                        style="border: 0px solid !important;">
                      <td style="border: 0px solid !important;" colspan="100">
                        <SMSStatsPanel :eventStats="request.eventStats" :bulkReqId="request.id"></SMSStatsPanel>
                      </td>
                    </tr>
                    <tr v-else-if="request.showStats === true && request.operationType === 'bulk-email'"
                        :key="`${request.id}-2`"
                        style="border: 0px solid !important;">
                      <td style="bporder: 0px solid !important;" colspan="100">
                        <EmailStatsPanel :emailStats="request.eventStats" :bulkReqId="request.id"></EmailStatsPanel>
                      </td>
                    </tr> -->
                    <tr v-if="canShowStats(request) && request.showStats"
                        :key="`${request.id}-2`"
                        style="border: 0px solid !important;">
                      <td style="border: 0px solid !important;" colspan="100">
                        <BulkActionStatsPanel :bulkReq="request"></BulkActionStatsPanel>
                      </td>
                    </tr>
                    <tr v-else-if="request.showStats"
                        :key="`${request.id}-2`"
                        style="border: 0px solid !important;">
                      <td style="border: 0px solid !important;" colspan="100">
                        <div class="h5 --light d-flex justify-content-end py-2 my-2">No Stats Found</div>
                      </td>
                    </tr>
                    </template>
                  </template>
                </tbody>
              </table>
            </div>
          <div class="d-flex justify-content-end my-2 mx-8" v-if="!isLoading">
            <button
              @click="prev"
              type="button" class="btn override-to-btn-class-override btn-outline-primary mx-2"
              v-if="hasPrevious" >
              Previous
            </button>
            <button
              @click="next"
              type="button" class="btn override-to-btn-class-override btn-outline-primary mx-2"
              v-if="hasNext">
              Next
            </button>
          </div>
          </div>
        </div>
      </div>

      <BulkActionDescription v-if="selectedBulReq && showDetails"
                            :v2='true'
                            :allowEdit ="allowScheduleEdit"
                            :allowSprintChange="allowSprintChange"
                            :request="selectedBulReq"
                             v-on:closed="closeListReport"/>
    </section>

</template>

<script lang="ts">
import { UxMessage } from '@/util/ux_message'
import Vue from 'vue'
import { ImportRequest } from '../../../models'
import { ScheduledBulkActions } from './server/bulkreq_api'
import {
  BulkRequestChangeType,
  BulkRequestV2Subscriber,
} from './server/bulk_request_changes'
import BulkActionDescription from './BulkActionDescription.vue'
import SMSStatsPanel from './SMSStatsPanel.vue'
import EmailStatsPanel from './EmailStatsPanel.vue'
import BulkActionStatsPanel from './BulkActionStatsPanel.vue'
import { BulkRequestV2 } from './server/bulk_req_v2'
import { KeyVal } from './CommonSelector.vue'
import * as moment from 'moment-timezone'
import BulkActionItemsReport from './BulkActionItemsReport.vue'
import { IBulkImportSpecs, IBulkReq, IExportSpecs } from './server/bulkreq_interfaces'
import restAgent from '@/restAgent'
import NotificationHelper from '@/util/notification_helper'

export default Vue.extend({
  name: 'BulkActionItems',
  inject: ['uxmessage'],
  props: ['selectedFilter', 'selectedDateRange', 'users', 'selectedUserId', 'selectedType'],
  data() {
    return {
      showDetails: false,
      allowScheduleEdit: false,
      allowSprintChange: false,
      requests: [] as ImportRequest[],
      currentLocationId: '',
      changeTracker: null as BulkRequestV2Subscriber | null,
      isLoading: false,
      isFetching: false,
      processingId: '',
      items: [] as BulkRequestV2[],
      selectedBulReq: null as BulkRequestV2 | null,
      showError: false as boolean,
      showSuccess: false as boolean,
      filters: [new KeyVal('Scheduled'), new KeyVal('Completed')],
      reportRefreshTimer: undefined as number | undefined,
      hasPrevious: false as boolean,
      hasNext: false as boolean,
      refreshStartAfter: null as null | any,
      refreshEndBefore: null as null | any,
      debouncer: null as NodeJS.Timer | null,
    }
  },
  components: {
    BulkActionItemsReport,
    BulkActionDescription,
    SMSStatsPanel,
    EmailStatsPanel,
    BulkActionStatsPanel,
  },
  // async created() {
  //   this.currentLocationId = this.$router.currentRoute.location_id
  //   await this.fetchData()
  // },
  watch: {
    '$route.params.location_id': async function (id) {
      this.currentLocationId = id
      this.debounceFetchData('loc id change')
    },
    selectedFilter: async function () {
      this.debounceFetchData('selectedFilter')
    },
    selectedDateRange: async function () {
      this.debounceFetchData('selectedDateRange')
    },
    selectedUserId: async function () {
      this.debounceFetchData('selectedUserId')
    },
    selectedType: async function () {
      this.debounceFetchData('selectedType')
    },
  },
  computed: {
    locationTimeZone() {
      return this.$store.getters[`locations/getCurrentLocationTimeZone`]
    },
  },
  methods: {
    canShowStats(req: IBulkReq){
      if (!req) return false
      if (req.opSpecs?.opType === 'bulk-export' || req.opSpecs?.opType === 'bulk-merge-contact') return true
      if (req.opSpecs?.opType === 'bulk-import') return true
      if (req.opSpecs?.opType === 'bulk-contact-delete') return true
      if (!req.queuedCount) return false
      return true;
    },
    clearRefreshTimer() {
      if (this.reportRefreshTimer) clearTimeout(this.reportRefreshTimer)
    },
    startRefreshTimer(ms: number = 5 /*min*/ * 60 * 1000) {
      this.reportRefreshTimer = setTimeout(this.refreshSamePage, ms)
    },
    debounceFetchData(source: string, params : null | {endBefore?: any, startAfter?: any} = null) {
      if (this.debouncer) clearTimeout(this.debouncer)
      this.debouncer = setTimeout(() => this.fetchData(source,params),500);
    },
    async fetchData(source: string, params : null | {endBefore?: any, startAfter?: any} = null) {
      try {
        console.log(`Source of fetch data request ${source}`)
        // alert(`Source of fetchData request ${source}`)
        if (this.isFetching) return
        this.clearRefreshTimer()
        const previousCount = (this.items && this.items.length) || 0

        this.isLoading = true
        this.isFetching = true
        const filters: { [key: string]: any } = {}

        if (this.selectedUserId !== 'all') filters.userId = `${this.selectedUserId}`
        if (this.selectedType !== 'all') filters.opType = `${this.selectedType}`.toLowerCase()
        if (this.selectedFilter !== 'all') filters.status = { filterAgent: 'smv2', status: `${this.selectedFilter}`.toLowerCase() }

        if (this.selectedDateRange) {
          if (this.selectedDateRange.start instanceof Date)
            filters.from = moment(
              this.selectedDateRange.start.toISOString()
            ).format('YYYY-MM-DD HH:mm:ss')
          else if (typeof this.selectedDateRange.start === 'string')
            filters.from = moment(
              this.selectedDateRange.start,
              'YYYY-MM-DD hh:mm:ss am'
            ).format('YYYY-MM-DD HH:mm:ss')

          if (this.selectedDateRange.end instanceof Date) {
            let end = moment(this.selectedDateRange.end.toISOString())
            filters.to = end.format('YYYY-MM-DD HH:mm:ss')
          } else if (typeof this.selectedDateRange.end === 'string') {
            filters.to = moment(
              this.selectedDateRange.end,
              'YYYY-MM-DD hh:mm:ss am'
            ).format('YYYY-MM-DD HH:mm:ss')
          }
          // filters.from = this.selectedDateRange.start ? this.selectedDateRange.start.format('YYYY-MM-DD') : '';
          // filters.to = this.selectedDateRange.end ? this.selectedDateRange.end.format('YYYY-MM-DD') : '';
          filters.timezone = this.locationTimeZone
        }
        //console.log(filters);
        const pageLimit = 10
        filters.limit = pageLimit + 1
        filters.startAfter = params && params.startAfter
        filters.endBefore = params && params.endBefore
        this.$emit('loading', this.isLoading)
        const data = await ScheduledBulkActions.getList(filters)

        this.hasPrevious = false
        this.hasNext = true
        this.refreshStartAfter = null
        this.refreshEndBefore = null
        if (filters.startAfter) {
          this.refreshStartAfter = filters.startAfter
          this.hasPrevious = true
          if (data.length > pageLimit) this.hasNext = true
          else this.hasNext = false
        } else if (filters.endBefore) {
          this.refreshEndBefore = filters.endBefore
          this.hasNext = true
          if (data.length > pageLimit) this.hasPrevious = true
          else this.hasPrevious = false
        } else if (
          !filters.startAfter &&
          !filters.endBefore &&
          data.length <= pageLimit
        ) {
          this.hasNext = false
        }

        if ((!filters.startAfter && !filters.endBefore) || filters.startAfter) {
          this.items = data.slice(
            0,
            data.length > pageLimit ? data.length - 1 : data.length
          )
        } else if (filters.endBefore) {
          this.items = data.slice(
            data.length > pageLimit ? 1 : 0,
            pageLimit + 1
          )
        }

        if (previousCount !== ((this.items && this.items.length) || 0)) {
          this.$emit('updated')
        }
      } catch (err) {
        console.log(err)
      } finally {
        this.isLoading = false
        this.isFetching = false
        this.$emit('loading', this.isLoading)
        this.startRefreshTimer()
      }
    },
    next() {
      if (this.items && this.items.length) {
        this.fetchData('next-1',{ startAfter: this.items[this.items.length - 1].id })
      } else this.fetchData('next-2',)
    },
    prev() {
      if (this.items && this.items.length) {
        this.fetchData('prev-1',{ endBefore: this.items[0].id })
      } else this.fetchData('prev-2',)
    },
    refreshSamePage() {
      this.debounceFetchData('refreshSamePage', {
        startAfter: this.refreshStartAfter,
        endBefore: this.refreshEndBefore
      })
    },
    async getStats(reqId: string) {
      if (!reqId) return
      let idx = this.items.findIndex(a=> a.id === reqId)
      if (idx === -1) return
      let req = this.items[idx]
      if (req.opSpecs?.opType === 'bulk-export') {
        const stats = {
          total: (req?.opSpecs as IExportSpecs)?.total,
          queued: 0,
          success: (req?.opSpecs as IExportSpecs).total,
          error: 0,
        }
        req.eventStatsV2 = { ...stats, noDrillDown: true}
        req.showStats = true
        return
        // req.showStats = true
        // return
      }
      if (req.opSpecs?.opType === 'bulk-merge-contact') {
        const stats = {
          total: req?.opSpecs?.contactIds?.length,
          queued: 0,
          success: req?.opSpecs?.contactIds?.length,
          error: 0,

        }
        req.eventStatsV2 = { ...stats, noDrillDown: true}
        req.showStats = true
        return
        // req.showStats = true
        // return
      }
      try {
        req.loading = true
        const promises: Promise<any>[] = []
        promises.push(ScheduledBulkActions.getItemDetails({bulkReqId: req.id}))
        if (req.operationType === 'bulk-sms') {
          promises.push(ScheduledBulkActions.getSMSEventStats(req))
        } else if (req.operationType === 'bulk-email') {
          promises.push(ScheduledBulkActions.getEmailEventStats(req))
        }
        const results = await Promise.all(promises)
        // console.log(`0 - ${results[0].id}`)
        // console.log(`1 - ${JSON.stringify(results[1])}` )
        const x = results[0]
        req = x|| req
        const providerStats = results[1] || {}
        const stats = {
          total: req.totalCount,
          queued: req.queuedCount,
          success: req.successCount,
          updated: req.updatedCount,
          created: req.createdCount,
          error: req.errorCount,
        }
        req.eventStatsV2 = { ...stats, providerStats }
        if (req.operationType === 'bulk-import' || req.operationType === 'bulk-contact-delete') {
          req.eventStatsV2.noDrillDown = true
        }
        req.showStats = true
        if (x) this.items.splice(idx,1,x)
        this.items = [...this.items]
      } catch (err) {
        console.log(err)
        req.showStats = true
      } finally {

      }
      // alert(JSON.stringify(stats))
    },
    async itemUpdate(
      changeType: BulkRequestChangeType,
      reqUpdate: BulkRequestV2
    ) {
      if (!reqUpdate || !reqUpdate.id) return
      const index = this.items.findIndex(a => a.id === reqUpdate.id)
      if (changeType === BulkRequestChangeType.MODIFIED && index !== -1) {
        this.items.splice(index, 1, reqUpdate)
      } else if (changeType === BulkRequestChangeType.REMOVED && index !== -1) {
        this.items.splice(index, 1)
      }
    },
    async clearChangeTracking() {
      if (this.changeTracker) await this.changeTracker.terminate()
    },
    async downloadErrorLog(br: BulkRequestV2){
      if (!br || !br.id) return;
      if (!br.operationType || br.operationType !== 'bulk-import') return;
      if (!br.opSpecs) return;
      if (!(br.opSpecs as IBulkImportSpecs).importId) return;
      const response =  await restAgent.Contact.getImportLog(`${(br.opSpecs as IBulkImportSpecs).importId}`);
      if (response === 'No records found') {
        this.showToast('No errors found in CSV', 'success')
      } else {
        const url = window.URL.createObjectURL(new Blob([response]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', 'import_log.csv');
        document.body.appendChild(link);
        link.click();
      }
    },
    async revert(br: BulkRequestV2){
      if (!br || !br.id) return;
      if (!br.operationType || br.operationType !== 'bulk-import') return;
      if (!br.opSpecs) return;
      if (!(br.opSpecs as IBulkImportSpecs).importId) return;
      const response =  await restAgent.Contact.revertImport(`${(br.opSpecs as IBulkImportSpecs).importId}`);
      await this.refreshItem(br.id)
    },
    async cancelBulkImport(br: BulkRequestV2){
      if (this.isLoading || this.processingId) return
      if (!br) return
      try {
        this.isLoading = true
        br.suspendUIAction = true
        if (!br || !br.id) return;
        if (!br.operationType || br.operationType !== 'bulk-import') return;
        if (!br.opSpecs) return;
        if (!(br.opSpecs as IBulkImportSpecs).importId) return;
        const response =  await restAgent.Contact.cancelImport(`${(br.opSpecs as IBulkImportSpecs).importId}`);
        this.$nextTick()
        await this.fetchData()
        this.showToast(`Please note it may take some time to cancel an import`)
      } catch (err) {
        this.processingId = ''
        console.log(err)
      } finally {
        br.suspendUIAction = false
        this.processingId = ''
      }
    },
    async showSmartList(params: any){
      if (params.import_filter) {
        this.$router.push({name:(`import_filter${this.$router.currentRoute.name?.endsWith('v2') ? '-v2' : ''}`), params})
      }
    },
    async action(
      action: 'pause' | 'cancel' | 'resume',
      bulkRequest: BulkRequestV2
    ) {
      if (this.isLoading || this.processingId) return
      if (!bulkRequest) return
      try {
        this.isLoading = true
        bulkRequest.suspendUIAction = true
        this.$nextTick()
        this.processingId = bulkRequest.id
        await ScheduledBulkActions.action({
          action,
          bulkRequestId: bulkRequest.id,
        })
        this.$nextTick()
        await this.fetchData(action)
        this.showToast(`It may take some time to ${action} an action.`)
      } catch (err) {
        this.processingId = ''
        console.log(err)
      } finally {
        this.processingId = ''
        bulkRequest.suspendUIAction = false
      }
    },
    async editSprintLimit(req: any) {
      this.allowScheduleEdit = false
      this.allowSprintChange = true
      this.selectedBulReq = req
      this.showDetails = true
    },
    async editSchedule(req: any) {
      this.allowScheduleEdit = true
      this.allowSprintChange = false
      this.selectedBulReq = req
      this.showDetails = true
    },
    async closeListReport() {
      const requiresRefresh = this.allowScheduleEdit || this.allowSprintChange
      this.allowScheduleEdit = false
      this.allowSprintChange = false
      this.showSuccess = this.showError = false
      this.showDetails = false
      const id = this.selectedBulReq?.id
      this.selectedBulReq = null
      await this.refreshItem(id)
      // this.$emit('actionUpdated', selectedBulReqId);
    },
    async refreshItem(selectedBulReqId?: string){
      if (!selectedBulReqId) return;
      let idx = -1
      let selectedBulReq: BulkRequestV2| null = null
      try {
        idx =  this.items.findIndex(a=> a.id === selectedBulReqId)
        if (idx !== -1) selectedBulReq = this.items[idx]
        if (selectedBulReq) selectedBulReq.loading = true;
        if (selectedBulReqId){
          selectedBulReq = await ScheduledBulkActions.getItemDetails({bulkReqId: selectedBulReqId})
          idx = this.items.findIndex(a=> a.id === selectedBulReqId)
          if (selectedBulReq && idx !== -1) {
            this.items.splice(idx,1,selectedBulReq)
            this.items = [...this.items]
          }
        }
      } catch(err){
        console.log(err)
      } finally {
        if (selectedBulReq) {
          selectedBulReq.loading = false
        }
      }
    },
    async openErrorReport(bulkReq: BulkRequestV2) {
      this.showError = true
      this.showSuccess = false
      this.showDetails = false
      this.selectedBulReq = bulkReq
    },
    async openSuccessReport(bulkReq: BulkRequestV2) {
      this.showSuccess = true
      this.showError = false
      this.showDetails = false
      this.selectedBulReq = bulkReq
    },
    async openQueuedReport(bulkReq: BulkRequestV2) {
      this.showError = false
      this.showSuccess = false
      this.selectedBulReq = bulkReq
    },
    openDescriptionReport(bulkReq: BulkRequestV2) {
      this.showError = false
      this.showSuccess = false
      this.showDetails = true
      this.selectedBulReq = bulkReq
    },
    findUsername(userId: string) {
      if (userId) {
        const item = this.users.find(a => a.id === userId)
        if (item) return item.name
      }
      return ''
    },
    showToast(message: string, type: string = 'info'){
      try {
        setTimeout(() => {
          if (type === 'success') this.uxmessage(UxMessage.successType('',message), true)
          else this.uxmessage(UxMessage.infoType('',message), true)
        }, 1200);
      } catch(err){

      }
    }
  },
  async beforeDestroy() {
    this.clearRefreshTimer()
    await this.clearChangeTracking()
  },
})
</script>
<style>
.override-to-btn-class-override {
  color: #178af6;
  border:#178af6 0.5px solid;
}
.override-to-btn-class-override:focus {
  color: white;
  background: #178af6;
  border:#178af6 0.5px solid;
}
.sort {
  margin-left: 20px;
  margin-bottom: 10px;
  cursor: pointer;
}

.form-control:disabled,
.form-control[readonly] {
  background-color: #f7fafc;
}
.vdp-datepicker__calendar .cell:not(.blank):not(.disabled).day:hover,
.vdp-datepicker__calendar .cell:not(.blank):not(.disabled).month:hover,
.vdp-datepicker__calendar .cell:not(.blank):not(.disabled).year:hover {
  border: 1px solid #188bf6 !important;
}
.vdp-datepicker__calendar .cell.selected,
.vdp-datepicker__calendar .cell.selected.highlighted,
.vdp-datepicker__calendar .cell.selected:hover {
  background: #188bf6 !important;
  color: white;
}
#back {
  margin-right: 10px;
  cursor: pointer;
  color: #188bf6;
}
.dropdown-background {
  background: rgb(242 247 250);
}
.campaign_new_header .hl_datepicker {
  background: rgb(242 247 250);
  padding: 0px 8px;
  border-radius: 0.3125rem;
  -webkit-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  cursor: pointer;
  margin-right: 0;
  height: 40px;
}
.hl_tasks-list.bulk-actions-header {
  display: flex;
  align-items: center;
}
.hl_tasks-list.bulk-actions-header h2 {
  margin-right: 16px;
}
.hl_tasks-list button.dropdown-toggle {
  background-color: #ffffff !important;
  color: #607179 !important;
}
.hl_tasks-list .dropdown .dropdown-menu {
  max-height: 250px;
  overflow: auto;
}
.dropdown .dropdown-menu .dropdown-item {
  width: auto !important;
}
.--gray {
  color: #818587
}
</style>
