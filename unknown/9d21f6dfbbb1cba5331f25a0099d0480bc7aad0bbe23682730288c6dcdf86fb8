<template>
  <div>
    <div class="form-group">
      <UITextInputGroup
        type="text"
        placeholder="Title"
        v-validate="'required|handlebars'"
        data-vv-validate-on="input"
        v-model="title"
        name="title"
        @input="setValue('title', title)"
        :error="errors.has('title')"
        :errorMsg="errors.first('title')"
      />
    </div>
    <div class="form-group">
			<label>Message</label>
      <div class="custom-dropdown-menu">
        <NestedMenu
          class="my-2"
          v-show="displayMenu"
          :items="menuItems"
          :title="'Merge fields'"
          v-on:MenuSelected="menuItemSelected"
          :fromAction="true"
        />
			<editor
				:init="editorOptions"
				v-model="tinymcehtml"
				v-validate="'required|handlebars'"
        name="notificationEditor"
        data-vv-validate-on="input"
        :id='getRandomId'
        ref="editor"
			></editor>
      </div>
      <span v-show="errors.has('notificationEditor')" class="--red">{{errors.first('notificationEditor')}}</span>
		</div>
    <div class="form-group">
      <label>Select Redirect Page</label>
      <div class="form-input-dropdown dropdown">
          <div data-toggle="dropdown">
              <i class="icon icon-arrow-down-1"></i>
              <input type="text" class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800" data-lpignore="true" placeholder="Select user type" :value="redirectPage">
          </div>
          <div class="dropdown-menu">
              <a class="dropdown-item trigger-type" href="javascript:void(0);" v-for="list in pageList" :key="list.name" @click.prevent="setValue('redirectPage', list.value)">
                  <p>{{list.name}}</p>
              </a>
          </div>
      </div>
    </div>
    <div class="form-group">
      <label>Select User Type</label>
      <div class="form-input-dropdown dropdown">
          <div data-toggle="dropdown">
              <i class="icon icon-arrow-down-1"></i>
              <input type="text" class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800" data-lpignore="true" placeholder="Select user type" :value="userType">
          </div>
          <div class="dropdown-menu">
              <a class="dropdown-item trigger-type" href="javascript:void(0);" v-for="list in userTypeList" :key="list.name" @click.prevent="setValue('userType', list.value)">
                  <p>{{list.name}}</p>
              </a>
          </div>
      </div>
    </div>
    <div class="form-group" v-if="userType == 'Select User'">
        <label>Select User</label>
        <div class="form-input-dropdown dropdown">
          <div data-toggle="dropdown">
              <i class="icon icon-arrow-down-1"></i>
              <input type="text" class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800" data-lpignore="true" placeholder="Select user type" :value="selectedUser">
          </div>
          <div class="dropdown-menu">
              <a class="dropdown-item trigger-type" href="javascript:void(0);" v-for="user in users" :key="user.id" @click.prevent="setValue('selectedUser', user.id)">
                  <p>{{user.fullName}}</p>
              </a>
          </div>
      </div>
    </div>
	</div>
</template>

<script lang="ts">
import Vue from 'vue'
import Editor from '@tinymce/tinymce-vue';
import {
	CustomField, User
} from "@/models";
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'
declare var $: any
let unsubscribeUsers: () => void
import { EventBus } from '@/models/event-bus'
import { FilterMergeTags } from '../../../util/filter_merge_tags'

import NestedMenu from '@/pmd/components/NestedMenu.vue'

export default Vue.extend({
  props: ['action', 'editorOptions', 'triggerType', 'displayMenu'],
  components: { 'editor': Editor, NestedMenu },
  data() {
    return {
      title: '',
      currentLocationId: '',
      tinymcehtml: '',
      body: '',
      edited: false,
      userTypeList: [{name: 'All User', value: 'all'}, {name: 'User assigned to the contact', value: 'assign'}, {name: 'Select User', value: 'user'}],
      pageList: [{name: 'Contact', value: 'contact'}, {name: 'Conversation', value: 'conversation'}, {name: 'Opportunity', value: 'opportunity'}],
      users: [] as User[],
      menuItems: [] as any[],
      customFields: [] as { [key: string]: any }[]
    }
  },
  computed: {
    userType(): string {
      const userType = lodash.find(this.userTypeList, {value: this.action.userType});
      if(userType) return userType.name;
      return '';
    },
    selectedUser(): string {
      const selectedUser = lodash.find(this.users, {id: this.action.selectedUser});
      if(selectedUser) return selectedUser.fullName;
      return '';
    },
    redirectPage(): string {
      const redirectPage = lodash.find(this.pageList, {value: this.action.redirectPage});
      if(redirectPage) return redirectPage.name;
      return '';
    },
    getRandomId() {
      return 'send_notification_' + Math.random().toString(36).substring(7);
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id;

    const fields = await CustomField.getByLocationIdAndType(this.currentLocationId, 'contact');
    this.customFields = fields.map(field => {
      return { text: field.name, value: `{{${field.fieldKey}}}` }
    });
    this.menuItems = await FilterMergeTags.syncList(this.currentLocationId, this.customFields, this.menuItems)
    this.menuItems = await FilterMergeTags.resyncList(this.menuItems, this.customFields, this.triggerType)

    if (this.action.body) this.tinymcehtml = this.body = this.action.body;
    if (this.action.title) this.title = this.action.title;

    if (this.currentLocationId) {
      this.users = this.$store.state.users.users.map(u => new User(u))
    }
	},
  methods: {
    setValue(field: string, value: string) {
      Vue.set(this.action, field, value)
      this.$emit('update:action', this.action)
    },
    menuItemSelected(item) {
      console.log("From method" + item);
      EventBus.$emit('customFieldSelected', item);
    }
  },
  watch: {
		tinymcehtml(value: string) {
      var div = document.createElement("div");
      value = value.replace(new RegExp('<br/>', 'g'), '\n');
      value = value.replace(new RegExp('<br />', 'g'), '\n');
			div.innerHTML = value;
      this.body = (div.textContent || div.innerText || "").replaceAll(String.fromCharCode(160), ' '); // to remove &nbsp
      this.setValue('body', this.body);
    },
    async triggerType() {
      this.menuItems = await FilterMergeTags.resyncList(this.menuItems, this.customFields, this.triggerType);
    },
    action() {
      if (this.action.body && this.body !== this.action.body) {
        const editor = tinymce.get(this.$refs.editor.id);
        editor.setContent(this.action.body.replace(/\n/g, '<br/>'));
      }
      if (this.action.title) this.title = this.action.title;
    }
	}
})
</script>
