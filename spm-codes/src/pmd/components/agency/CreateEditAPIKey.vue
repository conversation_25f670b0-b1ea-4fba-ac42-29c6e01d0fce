<template>
  <div
    class="modal fade hl_sms-template--modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">
              {{
                editMode
                  ? `Edit API Key`
                  : `Create API Key`
              }}
            </h2>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">

            <div class="form-group">
              <UITextInputGroup
                label="Name"
                type="text"
                placeholder="Name"
                v-model="name"
                maxlength="40"
                v-validate="'required'"
                name="name"
                :error="errors.has('name')"
                :errorMsg="'The name require field.'"
              />
            </div>

            <div class="form-group" v-if="editMode">
              <label>
                
              </label>
              <UITextInputGroup
                type="text"
                placeholder="API Key"
                label="API Key"
                :disabled="true"
                v-model="apiKey"
                name="api_key"
              />
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <div class="modal-footer--inner nav">
            <UIButton use="outline" data-dismiss="modal">
              Cancel
            </UIButton>
            <div style="display: inline-block;position: relative;">
              <UIButton
                @click.prevent="
                  () =>
                    editMode
                      ? editAPIKey()
                      : createAPIKey()
                "
                :disabled="processing"
                :loading="processing"
              >
                Save
              </UIButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { v4 as uuid } from 'uuid'
import IconPicker from '@/pmd/components/common/IconPicker.vue'
import { Company } from '@/models'
import axios from 'axios'

declare var $: any

export default Vue.extend({
  components: { IconPicker },
  props: ['values', 'userId'],
  data() {
    return {
      company: {} as Company,
      name: '' as string,
      apiKey: '' as string,
      processing: false
    }
  },
  computed: {
    editMode(): boolean {
      return Boolean(this.values.apiKey)
    }
  },
  methods: {
    reset() {
      this.$validator.reset()
      this.name = ''
      this.apiKey = ''
      this.processing = false
    },
    async createAPIKey() {
      const result = await this.$validator.validateAll();
      if (!result) return false

      try {
        this.processing = true
        axios.post(
          `agency/api-key/${this.company.id}`,
          {
            name: this.name,
            action: 'add',
            userId: this.userId
          }
        ).then(({ data }) => {
          this.reset()
          this.$emit('hidden')
        }).catch(error => {
          this.$uxMessage('error', 'Sorry! Something went wrong! Try agian.');
        });

      } catch (error) {
        console.log(error)
      }
    },
    async editAPIKey() {
      const result = await this.$validator.validateAll()
      const { linkId } = this.values
      if (!result) return false
      try {
        this.processing = true
        axios.post(
          `agency/api-key/${this.company.id}`,
          {
            name: this.name,
            apiKey: this.apiKey,
            action: 'edit',
            userId: this.userId
          }
        ).then(({ data }) => {
          this.reset()
          this.$emit('hidden')
        }).catch(error => {
          this.$uxMessage('error', 'Sorry! Something went wrong! Try agian.');
        });

      } catch (error) {
        console.log(error)
      }
    }
  },
  beforeDestroy() {
    this.reset()
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  watch: {
    async values(values: { [key: string]: any }) {
      const data: () => object = <() => object>this.$options.data
      if (data) Object.assign(this.$data, data.apply(this))
      if (values.visible) $(this.$refs.modal).modal('show')
      else $(this.$refs.modal).modal('hide')

      if (values.visible) {
        this.company = await Company.getById(this.values.companyId)
        if (values.apiKey) {
          const index = this.company.apiKeys.findIndex(
            item => item === values.apiKey
          )
          if (index > -1) {
            this.name = this.company.apiKeyTitles[index]
            this.apiKey = this.company.apiKeys[index]
          } else {
            this.name = ''
          }

        }
      }
    }
  },
  updated() {
    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.reset()
      _self.$emit('hidden')
    })

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  }
})
</script>
<style scoped>
.sm-button::before {
  font-family: var(--ff);
  font-weight: 900;
  content: var(--fa);
  font-style: normal;
}
</style>
