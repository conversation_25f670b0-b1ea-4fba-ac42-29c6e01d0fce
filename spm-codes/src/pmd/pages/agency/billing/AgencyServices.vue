<template>
    <div class="hl_billing--services">
        <div class="card">
            <div class="card-header">
                <h3 style="margin-bottom:0px"> Services </h3>
            </div>
            <div class="card-body">
            <div class="no-active-service" v-if="!formatedLocations.length" @click="$router.push({ name: 'marketplace-frame'})">
                You have no services yet. Find a service in our Marketplace &nbsp;
                <i class="fas fa-cart-plus"></i>
            </div>
            <div class="service-card" v-for="(location,i) in formatedLocations" :key="location.id">
                <div class="service-card-header">
                <div class="avatar">
                    <div :class="`avatar_img --${getAvtarBgClass(i)}`">
                    {{ getAvtarName(location.name) }}
                    </div>
                    <h4>{{location.name}}</h4>
                </div>
                </div>
                <div class="service-card-body">
                <div class="table-wrap">
                    <table class="table">
                    <tbody>
                        <tr v-for="service in location.services" :key="service.plan.plan_level + '_' + service.id" >
                            <td>
                                <div :class="`plan-icon-wrap --${service.icon_bgcolor_name}`">
                                    <img :src="service.icon_url" class="plan-icon">
                                </div>
                                {{service.name}}
								<span class="label --warning" v-if="service.status === 'in-process'" :title="`Service is ${service.status}`">{{service.status}}</span>
                            </td>
                            <td class="plan-name">
                                <span :class="`label --${$options.planClasses[service.plan.plan_level-1]}`">{{service.plan.name}}</span>
                            </td>
                            <td class="plan-price">
                                ${{service.plan.wholesale_recur_price}}/month
                            </td>
                            <!-- <td class="plan-remove" title="Deactivate service" @click="removeService(location.id,service.id,service.plan.plan_level )">
                                <moon-loader color="#37ca37" size="16px" v-if="isUnloading.locationId === location.id && isUnloading.serviceId === service.id && isUnloading.planLevel === service.plan.plan_level"/>
                                <i class="fas fa-minus-circle" v-else></i>
                            </td> -->
                        </tr>
                    </tbody>
                    </table>
                </div>
                </div>
            </div>

            </div>
        </div>
    </div>
</template>
<script lang='ts'>
import Vue from 'vue';
import { AgencyService } from '@/models';

export default Vue.extend({
    data() {
        return {
            services: [] as AgencyService[],
            isUnloading: {},
            loading: {
                locations: false
            }
		}
    },
    planClasses : ['basic','plus','premium','elite','basic','plus','premium','elite'],
    mounted(){
        this.fetchServices();
    },
    methods:{
        async fetchServices() {
            this.services = await AgencyService.getAllServices();
        },
        getAvtarBgClass(index){
            var colors = ['orange', 'green', 'purple'];
            return colors[index % 3];
        },
        getAvtarName(name: String){
            let nameWords = name.split(' ');
            return nameWords.map( word=>{
                return word[0];
            }).join('').toUpperCase().substr(0,2);
        },
        async removeService( locationId,serviceId,planLevel){
            this.isUnloading = {locationId,serviceId,planLevel};
            try {
                const response = await this.$http.post(`/service/${serviceId}/unload/${locationId}`, {
                    servicePlan: planLevel
                })
                this.isUnloading={};
            } catch(err) {
                this.isUnloading={};
                console.error(err);
            }
        },
    },
    computed:{
        async formatedLocations() {
            this.loading.locations = true
            var locations = await this.$store.dispatch('locations/getAll')
            this.loading.locations = false

            locations = lodash.filter(locations, location => {
                return (location.services);
            })

            locations = [...locations]
            var allServices = [...this.services];
            var newArr = [];

            locations.forEach(location => {

				var services = [];
				Object.entries(location.services).forEach( ([key, service]) => {
                // location.services.forEach(service => {
                    var serviceIndex = allServices.findIndex( x=> x._id === service.service_id);
                    if(serviceIndex !== -1){
                        var currentService = allServices[serviceIndex]._data

                        var planIndex = currentService.plans.findIndex( x=> x.plan_level === service.plan_level)
                        if(planIndex !== -1){
                            currentService['plan'] = { ...currentService.plans[planIndex] };
						}
						if(service.enabled === true)
                        	services.push({...currentService, id: service.service_id, status: service.status })
                    }
				});
				if(services.length){
	                newArr.push({...location, services })
				}
            });

            return newArr;
        }
    },
})
</script>
<style scoped>
.hl_billing--services .service-card .table .label.--warning{
    background-color:rgba(255,188,0,0.9);
	color: #ffffff;
	border-left: 2px solid #ff870d;
	margin-left: 10px;
}
.hl_billing--services .service-card .table .label.--basic{
    background-color:#ff870d
}
.hl_billing--services .service-card .table .label.--plus{
    background-color:#6e22ff
}
.plan-icon-wrap{
    background-color: #a7a7a7;
    display: inline-block;
    border-radius: 4px;
    height: 24px;
    width: 24px;
    margin-right: 5px;
}
.plan-icon-wrap.--blue{background-color:#1b23fc}
.plan-icon-wrap.--magenta{background-color:#ff1342}
.plan-icon-wrap.--lt-blue{background-color:#00b2ff}
.plan-icon-wrap.--green{background-color:#00da58}
.plan-icon-wrap.--purple{background-color:#ac42ff}
.plan-icon-wrap.--orange{background-color:#ff8913}
.plan-icon{
    height: 16px;
    width: 16px;
    margin: 4px;
}
.plan-name{
    text-align: center;
}
.plan-price{
    text-align: right;
}
.plan-remove{
    color: #ffc3c3;
    font-size: 14px;
    cursor: pointer;
    padding-left: 0px;
    text-align: center;
    width: 18px;
}
.no-active-service{
    text-align: center;
    cursor: pointer;
    padding: 20px;
    color: #979797;
}
</style>
