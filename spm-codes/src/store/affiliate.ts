const affiliate = {
  namespaced: true,
  state: {
    industry_served: '',
    service_provided: '',
    partner_status: '',
    partner_name: '',
    affiliateList: [],
  },
  mutations: {
    updateIndustryServed(state, value) {
      state.industry_served = value;
    },
    updateServiceProvided(state, value) {
      state.service_provided = value;
    },
    updatePartnerStatus(state, value) {
      state.partner_status = value;
    },
    updatePartnerName(state, value) {
      state.partner_name = value;
    },
    updateAffiliateList(state, value) {
        state.affiliateList = value;
    }
  },
  actions: {}
}

export default affiliate;
