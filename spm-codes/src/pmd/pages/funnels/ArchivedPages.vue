<template>
  <div>
    <hr>
    <h3 class="archived-header">Archived Pages</h3>
    <table class="table table-sm">
      <thead>
        <th>Name</th>
        <th>Created</th>
        <th>Archived</th>
        <th></th>
      </thead>
      <tbody>
        <tr v-for="page in pages" v-if="page.deleted">
          <td>{{page.name}}</td>
          <td>{{page.dateAdded.format('lll')}}</td>
          <td>{{page.dateUpdated.format('lll')}}</td>
          <td>
            <a class="btn btn-sm restore-link" @click="restorePage(page)">Restore</a>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
export default Vue.extend({
  name: "ArchivedPages",
  props: {
    pages: {
      type: Array
    }
  },
  methods: {
    restorePage(page) {
      this.$emit('restorePage', page)
    }
  }
})
</script>

<style scoped>

.table tbody tr td {
  padding: 0.3rem !important;
  color: #3E474F !important;
}

.restore-link:hover {
  color: var(--custom-primary) !important;
  text-decoration: underline !important;
}
</style>
