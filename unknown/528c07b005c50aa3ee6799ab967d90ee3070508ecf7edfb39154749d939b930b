<template>
  <div id="yext_payment">
    <template v-if="loading">
      <div class="loader-backdrop">
        <div>
          <moon-loader :loading="loading" color="#188bf6" size="30px" />
          <p v-if="paymentStarted">
            Payment is processing. Please do not reload or press back button
          </p>
        </div>
      </div>
    </template>
    <div class="card">
      <div class="card-body">
        <div class="two_part">
          <div class="billing part_common">
            <div id="invoice_container">
              <div class="back_link">
                <a
                  href="#"
                  @click.prevent="$emit('cancel')"
                  id="yext_go_back_order_summary"
                >
                  <i class="fa fa-chevron-left" aria-hidden="true"></i>
                </a>
              </div>
              <div class="agency_details">
                <div class="agency_logo_container">
                  <template v-if="company.logoURL">
                    <div
                      class="agency_logo"
                      :style="{ backgroundImage: `url(${company.logoURL})` }"
                    ></div>
                  </template>
                  <template v-else>
                    <i class="fas fa-store"></i>
                  </template>
                </div>

                <label class="agency_name">{{ company.name }}</label>
              </div>
              <div id="invoice_body">
                <label class="section_header"
                  >Subscribe to Yext Online Listing Subscription
                </label>
                <div class="amount">
                  <span class="price">${{ salePrice }}.00</span>
                  <div class="unit">
                    <span>per month</span>
                    <span>(3 months commitment)</span>
                  </div>
                </div>
                <label class="section_header" style="margin-top: 16px"
                  >Knowledge Engine Professional
                </label>

                <div class="product_logo"></div>
                <!-- <footer>
                  <a
                    class="powered_by"
                    href="https://stripe.com"
                    target="_blank"
                    >Powered by <b>stripe</b></a
                  >
                  <a href="https://stripe.com/checkout/terms" target="_blank"
                    >Terms</a
                  >
                  <a href="https://stripe.com/privacy" target="_blank"
                    >Privacy</a
                  >
                </footer> -->
                <!--                <div class="table_container">
                  <table>
                    <tr>
                      <td>Plan</td>
                      <td>Professional</td>
                    </tr>
                    <tr>
                      <td>Billing Cycle</td>
                      <td>
                        {{ today.format('DD-MMM-YY') }} -
                        {{ today.clone().add(1, 'month').format('DD-MMM-YY') }}
                      </td>
                    </tr>
                    <tr>
                      <td class="last_data_row">Contract Term</td>
                      <td class="last_data_row">
                        3 months (recurring monthly)
                      </td>
                    </tr>
                    <tr>
                      <td class="separator"></td>
                      <td class="separator"></td>
                    </tr>
                    <tr>
                      <td>Total</td>
                      <td>${{ salePrice }}/mo</td>
                    </tr>
                  </table>
                </div> -->
              </div>
            </div>
          </div>

          <div class="cc_card_info part_common" style="justify-content: start">
            <div id="cc_card_container">
              <label class="section_header">Pay with card</label>
              <div class="cc_card">
                <div class="cc_card_body">
                  <form action="/charge" method="post" id="add-card-form">
                    <div>
                      <template>
                        <UITextInputGroup
                          label="Email"
                          type="email"
                          name="contact_email"
                          id="contact_email"
                          :disabled="!!location.email"
                          v-model="contactEmail"
                        />
                      </template>
                    </div>
                    <div class="card_details">
                      <template v-if="primaryCard">
                        <div
                          :class="{
                            card_type: true,
                            [primaryCard.cardBrand]: true,
                          }"
                        >
                          <VisaIcon
                            v-if="primaryCard.cardBrand === 'visa'"
                            alt="VISA"
                          />
                          <MastercardIcon
                            v-else-if="primaryCard.cardBrand === 'mastercard'"
                            alt="MasterCard"
                          />
                          <AmexIcon
                            v-else-if="primaryCard.cardBrand === 'amex'"
                            alt="AMEX"
                          />
                          <DinersClubIcon
                            v-else-if="primaryCard.cardBrand === 'diners_club'"
                            alt="Diners Club"
                          />
                        </div>
                      </template>

                      <div class="stripe_element stripe_element_number">
                        <label> Card information </label>
                        <div class="card_group_inputs shadow-sm">
                          <div>
                            <input
                              v-if="primaryCard"
                              class="card-input-raw"
                              style="border: unset"
                              type="text"
                              :value="
                                '* * * *   * * * *   * * * *   ' +
                                primaryCard.cardNumber
                              "
                              disabled
                            />
                            <div
                              v-else
                              id="card-number"
                              class="card-input"
                              style="border: unset"
                            ></div>
                            <div class="second_row">
                              <div class="stripe_element stripe_element_expiry">
                                <template v-if="primaryCard">
                                  <input
                                    v-show="primaryCard"
                                    class="card-input-raw"
                                    style="border-right: 1px solid #c8c8c8"
                                    type="text"
                                    :value="`${primaryCard.expiryMonth}/${primaryCard.expiryYear}`"
                                    disabled
                                  />
                                </template>
                                <div
                                  v-else
                                  class="card-input"
                                  style="border-right: 1px solid #c8c8c8"
                                >
                                  <div id="card-expiry"></div>
                                </div>
                              </div>

                              <div class="stripe_element stripe_element_cvv">
                                <div v-if="primaryCard">
                                  <input
                                    v-show="primaryCard"
                                    class="card-input-raw"
                                    type="text"
                                    value="***"
                                    disabled
                                  />
                                </div>
                                <div class="card-input" v-else>
                                  <div id="card-cvc"></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <div class="stripe_element stripe_element_name">
                        <div id="card-name">
                          <UITextInputGroup
                            v-if="primaryCard"
                            label="Name On Card"
                            type="text"
                            name="name_on_card"
                            id="name_on_card"
                            :value="primaryCard.nameOnCard"
                            :disabled="true"
                          />

                          <UITextInputGroup
                            v-else
                            label="Name On Card"
                            type="text"
                            name="name_on_card"
                            id="name_on_card"
                            v-model="nameOnCard"
                          />
                          <!--   <label> Name </label> -->
                          <!--           <input
                            v-if="primaryCard"
                            class="card-input"
                            type="text"
                            disabled
                          />
                          <input
                            v-else
                            class="card-input"
                            type="text"
                            v-model="nameOnCard"
                          /> -->
                        </div>
                      </div>
                    </div>
                    <div v-if="!primaryCard">
                      <UITextInputGroup
                        label="zip"
                        type="text"
                        id="zip"
                        name="zip"
                        v-model="postalCode"
                      />
                    </div>
                    <!--                     <div class="fourth_row" v-if="!primaryCard">
                      <div class="title">
                        {{ 'Make this card primary' }}
                      </div>
                      <div class="toggle">
                        <input
                          type="checkbox"
                          class="tgl tgl-light"
                          id="primary_card"
                          v-model="makePrimaryCard"
                        />
                        <label class="tgl-btn" for="primary_card"></label>
                      </div>
                    </div> -->
                  </form>
                </div>
              </div>

              <div class="actions">
                <button
                  class="btn btn-success hl_calltoaction-btn"
                  @click="confirmPayment()"
                  id="yext_payment_confirm_btn"
                >
                  Confirm
                </button>
              </div>
              <p class="consent">
                By confirming your subscription, you allow {{ company.name }}
                to charge your card for this payment and future payment for this
                subscriptions
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import config from '@/config'
import moment from 'moment-timezone'
import firebase from 'firebase/app'

import VisaIcon from '@/assets/pmd/img/saas/visa.svg'
import MastercardIcon from '@/assets/pmd/img/saas/mastercard.svg'
import AmexIcon from '@/assets/pmd/img/saas/amex.svg'
import DinersClubIcon from '@/assets/pmd/img/saas/diners.svg'

declare var Stripe: any

export default Vue.extend({
  components: {
    VisaIcon,
    MastercardIcon,
    AmexIcon,
    DinersClubIcon,
  },
  props: {
    location: {
      type: Object,
    },
    company: {
      type: Object,
    },
    user: {
      type: Object,
    },
  },
  data() {
    return {
      contactEmail: this.location.email || this.user.email,
      addingFirstCard: false,
      stripe: undefined as any,
      cardNumber: undefined as any,
      cardNumberError: undefined,
      cardCvc: undefined as any,
      cardCvcError: undefined,
      cardExpiry: undefined as any,
      cardExpiryError: undefined,
      makePrimaryCard: true,
      nameOnCard: this.user.name || '',
      postalCode: this.location.postalCode || '',
      loading: false,
      paymentStarted: false,
      error: '',
      addCardSuccessful: false,
      primaryCard: null,
      today: moment(),
      newlyCreatedStripeCustomerId: null,
    }
  },
  created() {},
  async mounted() {
    this.init()
  },
  beforeDestroy() {},
  methods: {
    async init() {
      try {
        await this.updateExistingCardDetails()
        if (!this.primaryCard) {
          this.setupStripe()
        }
      } catch (err) {
        console.error(err)
        this.setupStripe()
      }
    },
    async confirmPayment() {
      if (!this.location.email) {
        // in any condition location email is must
        let error = this.validateEmail()
        if (!error) {
          if (!this.location.email) {
            this.location.email = this.contactEmail
            await this.location.ref.update({
              email: this.location.email,
              date_updated: firebase.firestore.FieldValue.serverTimestamp(),
            })
          }
        } else {
          if (this.primaryCard) {
            this.$uxMessage('error', `<ol>${error}</ol>`, null, {
              isMessageInRawHTML: true,
            })
          } else {
            this.$uxMessage('error', `<ol>${this.inputErrors()}</ol>`, null, {
              isMessageInRawHTML: true,
            })
          }
          // until email is fixed we are not allowing anything
          return
        }
      }
      if (this.primaryCard) {
        // using existing primary card
        this.startSubscription()
      } else {
        let error = this.inputErrors()
        if (!error) {
          try {
            await this.createCustomerIfNotExists()
          } catch (err) {
            console.error(err)
            this.$uxMessage('error', `Unable to create customer`, null, {
              isMessageInRawHTML: true,
            })
          }
          if (this.stripeCustomerId) {
            let result = null
            try {
              result = await this.addCard()
            } catch (err) {
              console.error(err)
              this.$uxMessage('error', `Unable to Add card`, null, {
                isMessageInRawHTML: true,
              })
            }
            if (result) {
              this.startSubscription()
            }
          }
        } else {
          this.$uxMessage('error', `<ol>${error}</ol>`, null, {
            isMessageInRawHTML: true,
          })
        }
      }
    },
    setupStripe() {
      let recaptchaScript = document.getElementById('stripeV3')

      if (recaptchaScript) {
        console.log('stripe already setup')
        this.stripe = Stripe(config.stripeKey, {
          stripeAccount: this.stripeAccountId,
        })
        this.initCardElement()
        return
      }
      recaptchaScript = document.createElement('script')
      recaptchaScript.setAttribute('id', 'stripeV3')

      recaptchaScript.onload = () => {
        this.stripe = Stripe(config.stripeKey, {
          stripeAccount: this.stripeAccountId,
        })
        this.initCardElement()
      }

      recaptchaScript.setAttribute('src', 'https://js.stripe.com/v3/')
      document.head.appendChild(recaptchaScript)
    },
    initCardElement() {
      const elements = this.stripe.elements()
      const style = {
        base: {
          color: '#000000',
          padding: '15px 20px',
          fontFamily:
            'Roboto,system,-apple-system,BlinkMacSystemFont,".SFNSDisplay-Regular","Helvetica Neue",Helvetica,Arial,sans-serif',
          fontSmoothing: 'antialiased',
          fontSize: '13px',
          letterSpacing: '8px',
          '::placeholder': {
            color: '#b0b0b0',
          },
        },
        invalid: {
          color: '#cc0000',
          iconColor: '#f8a3d4',
        },
      }

      this.cardNumber = elements.create('cardNumber', { style: style })
      this.cardNumber.mount('#card-number')
      this.cardNumber.addEventListener('change', (event: any) => {
        if (event.error) {
          this.cardNumberError = event.error.message
        } else {
          this.cardNumberError = null
        }
      })

      this.cardCvc = elements.create('cardCvc', { style: style })
      this.cardCvc.mount('#card-cvc')
      this.cardCvc.addEventListener('change', (event: any) => {
        if (event.error) {
          this.cardCvcError = event.error.message
        } else {
          this.cardCvcError = null
        }
      })

      this.cardExpiry = elements.create('cardExpiry', { style: style })
      this.cardExpiry.mount('#card-expiry')
      this.cardExpiry.addEventListener('change', (event: any) => {
        if (event.error) {
          this.cardExpiryError = event.error.message
        } else {
          this.cardExpiryError = null
        }
      })
    },
    async createCustomerIfNotExists() {
      return new Promise(async (resolve, reject) => {
        try {
          if (this.stripeCustomerId) {
            return resolve(this.stripeCustomerId)
          }
          console.log(`Creating new customer`)
          this.loading = true
          const resp = await this.saasService.post(
            `/location-payment-methods/${this.location.id}/create_customer_if_not_exists`,
            {
              locationId: this.location.id,
              name: this.location.name || this.nameOnCard,
              email: this.location.email || this.location.prospectInfo.email,
              description: 'Customer created for buying YEXT',
            }
          )
          this.newlyCreatedStripeCustomerId = resp.data.customer_id
          this.location.settings.saas_settings.stripe_customer_id =
            resp.data.customer_id
          await this.location.ref.update({
            'settings.saas_settings.stripe_customer_id': resp.data.customer_id,
            date_updated: firebase.firestore.FieldValue.serverTimestamp(),
          })
          this.loading = false
          return resolve(resp.data.customer_id)
        } catch (err) {
          this.loading = false
          console.error(err)
          return reject(null)
        }
      })
    },
    async addCard() {
      return new Promise(async (resolve, reject) => {
        this.loading = true
        this.error = ''
        try {
          const intent = await this.saasService.post(
            `/saas-config/${this.location.id}/setup-stripe-intent`,
            {
              stripeCustomerId: this.stripeCustomerId,
              stripeAccountId: this.stripeAccountId,
            }
          )

          const { error, setupIntent } = await this.stripe.confirmCardSetup(
            intent.data.client_secret,
            {
              payment_method: {
                card: this.cardNumber,
                billing_details: {
                  name: this.nameOnCard,
                  address: {
                    postal_code: this.postalCode,
                  },
                },
              },
            }
          )
          if (error) {
            console.error(error)
            this.$uxMessage(
              'error',
              `${error.message ? error.message : `Unable to save the card`}`
            )
            return reject(false)
          }

          const existingPaymentMethod = await this.cardAlreadyExists(
            setupIntent.payment_method
          )
          if (!existingPaymentMethod.exists) {
            const useExistingPaymentSource =
              !!existingPaymentMethod.paymentSource
            const paymentMethodId = useExistingPaymentSource
              ? existingPaymentMethod.paymentSource.id
              : setupIntent.payment_method
            await this.updateCard(paymentMethodId, useExistingPaymentSource)
            console.log(`Card already successfully`)
            return resolve(true)
          } else {
            console.log(`Card already added`)
            return resolve(true)
          }
        } catch (error) {
          console.error(error)
          this.error = error.message || 'Error while adding card!'
          this.loading = false
          return reject(false)
        }
      })
    },
    async cardAlreadyExists(
      paymentMethodId: string
    ): Promise<{ exists: boolean; paymentSource: any | null }> {
      const {
        data: { exists, paymentSource },
      } = await this.saasService.post(
        `/location-payment-methods/${this.location.id}/payment-method-exists`,
        {
          paymentMethodId,
          stripeCustomerId: this.stripeCustomerId,
          stripeAccountId: this.stripeAccountId,
        }
      )

      return { exists, paymentSource }
    },
    async updateCard(
      paymentMethodId: string,
      useExistingPaymentSource: boolean
    ) {
      try {
        console.log('update card with payment method id --> ', paymentMethodId)
        const resp = await this.saasService.post(
          `/location-payment-methods/${this.location.id}`,
          {
            companyId: this.company.id,
            paymentMethodId,
            stripeCustomerId: this.stripeCustomerId,
            stripeAccountId: this.stripeAccountId,
            setAsPrimary: this.makePrimaryCard,
            nameOnCard: this.nameOnCard,
            useExistingPaymentSource,
          }
        )

        console.log('got resp for add payment method --> ', resp)
        this.addCardSuccessful = true
      } catch (error) {
        console.log('error while adding card --> ', error)
        error = error?.response?.data?.msg || 'Failed to add card!'
        this.$uxMessage('error', `${error}`)
      } finally {
        this.loading = false
        this.cardNumber.clear()
        this.cardCvc.clear()
        this.cardExpiry.clear()
      }
    },

    async updateExistingCardDetails() {
      try {
        this.loading = true
        this.primaryCard = null;
        const { data: paymentMethods } = await this.saasService.get(
          `/location-payment-methods/${this.location.id}`
        )
        let defaultCard = paymentMethods.find(card => card.defaultPayment)
        this.primaryCard = defaultCard
        try {
          if (this.primaryCard.expiryMonth.toString().match(/^\d$/)) {
            this.primaryCard.expiryMonth = `0${this.primaryCard.expiryMonth.toString()}`
          }
          this.primaryCard.expiryYear = this.primaryCard.expiryYear
            .toString()
            .replace(/^\d\d/, '')
        } catch {}
      } catch (error) {
        console.error('error while fetching payment methods --> ', error)
      } finally {
        this.loading = false
      }
    },

    async startSubscription() {
      try {
        this.loading = true
        this.paymentStarted = true
        let response = await this.saasService.post(`/yext`, {
          locationId: this.location.id,
        })
        console.log(response.data)
        this.$emit('paymentComplete', response.data)
      } catch (err) {
        console.error(err.response)
        if (err.response.status === 460 && err?.response?.data?.message) {
          this.$uxMessage('error', err?.response?.data?.message)
        } else if (err.response.status === 461) {
          await this.init();
          this.$uxMessage('error', err.response.data.message);
        } else {
          this.$uxMessage(
            'error',
            `Unable to add subscription, contact support`
          )
        }
      } finally {
        this.loading = false
        this.paymentStarted = false
      }
    },

    inputErrors() {
      let errors = ''
      if (!this.contactEmail) {
        errors += '<li>Please enter Contact email address</li>'
      } else if (!this.contactEmail.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
        errors += '<li>Please enter valid contact email</li>'
      }
      if (this.cardNumberError) {
        errors += `<li>${this.cardNumberError}</li>`
      }
      if (this.cardNumberError === undefined) {
        errors += `<li>Please Enter your card number</li>`
      }
      if (this.cardCvcError) {
        errors += `<li>Your card's CVV is incomplete.</li>`
      }
      if (this.cardCvcError === undefined) {
        errors += `<li>Please enter your card CVV</li>`
      }
      if (this.cardExpiryError) {
        errors += `<li>${this.cardExpiryError}</li>`
      }
      if (this.cardExpiryError === undefined) {
        errors += `<li>Please enter your Card Expiry details</li>`
      }
      if (!this.nameOnCard || this.nameOnCard.trim().length == 0) {
        errors += '<li>Please enter the card holder name</li>'
      }
      if (!this.postalCode || this.postalCode.trim().length == 0) {
        errors += '<li>Please enter ZIP Code </li>'
      }
      if (errors) {
        return errors
      } else {
        return null
      }
    },
    validateEmail() {
      let errors = ''
      if (!this.contactEmail) {
        errors += '<li>Contact email address required</li>'
      } else if (!this.contactEmail.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
        errors += '<li>Invalid contact email</li>'
      }
      if (errors) {
        return errors
      } else {
        return null
      }
    },
  },
  computed: {
    salePrice: function () {
      if (this.location && this.location.yextReseller.location_price) {
        return this.location.yextReseller.location_price
      } else if (this.company && this.company.yextReseller.agency_price) {
        return this.company.yextReseller.agency_price
      }
    },
    stripeCustomerId: function () {
      if (
        this.location.settings.saas_settings &&
        this.location.settings.saas_settings.stripe_customer_id
      ) {
        return this.location.settings.saas_settings.stripe_customer_id
      }
      if (this.newlyCreatedStripeCustomerId) {
        return this.newlyCreatedStripeCustomerId
      }
      return null
    },
    stripeAccountId: function () {
      return this.company.stripeConnectId
    },
  },
  watch: {
    location: function (oldLoc, newLoc) {
      if (oldLoc.id && oldLoc.id != newLoc.id) {
        this.init()
      }
    },
  },
})
</script>
<style scoped lang="scss">
#yext_payment {
  position: relative;
  height: calc(100vh - 115px);
  background: #fff;
  overflow: hidden;
  .card {
    height: 100%;
    .card-body {
      padding: unset;

      .two_part {
        display: grid;
        grid-template-columns: 1fr 1fr;
        height: 100%;
        .billing {
          #invoice_container {
            display: grid;
            justify-content: left;
            position: relative;
            .back_link {
              text-align: center;
              position: absolute;
              left: -4rem;
              font-size: 1.5rem;
              a {
                color: #7b7976;
                display: block;
                width: 40px;
                height: 40px;
                background: #fff;
                border-radius: 50px;
                display: flex;
                justify-content: center;
                align-items: center;
                transition: all 0.4s;
              }
              a:hover {
                background: #f2f0ef;
              }
            }
            .agency_details {
              display: grid;
              grid-template-columns: auto 1fr;
              align-items: center;
              grid-gap: 10px;
              .agency_logo_container {
                background: white;
                width: 40px;
                height: 40px;
                display: grid;
                justify-content: center;
                align-items: center;
                border-radius: 50%;
                overflow: hidden;
                box-shadow: 0px 1px 6px 0px #00000038;
                .agency_logo {
                  width: 30px;
                  height: 30px;
                  /* border-radius: 50%; */
                  background-size: contain;
                  background-repeat: no-repeat;
                  background-position: center;
                }
              }
              .agency_name {
                font-size: 0.9rem;
                font-weight: 500;
                margin: 0;
                color: #1a1a1ae6;
              }
            }
            #invoice_body {
              display: grid;
              grid-template-rows: auto 1fr auto;
              margin-top: 32px;

              .section_header {
                font-size: 0.9rem;
                font-weight: 500;
                color: #1a1a1a99;
              }
              .amount {
                display: grid;
                grid-template-columns: auto 1fr;
                grid-gap: 5px;
                margin: 3px 0;
                .price {
                  font-size: 2.3rem;
                  color: #1a1a1ae6;
                  font-weight: 500;
                }
                .unit {
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  padding: 9px 0 0 0;
                  font-size: 0.8rem;
                  font-weight: 500;
                  font-size: 0.9rem;
                  color: #1a1a1a99;
                }
              }
              .product_logo {
                background-image: url('/pmd/img/icon-yext-logo.svg');
                width: 18vh;
                height: 18vh;
                max-height: 224px;
                max-width: 224px;
                background-size: cover;
                margin: 8vh 0;
              }
              footer {
                display: grid;
                grid-template-columns: auto auto 1fr;
                grid-gap: 10px;
                .powered_by {
                  padding: 0 10px 0 0;
                  border-right: 1px solid #607179;
                  color: #607179;
                }
                a {
                  color: #607179;
                }
              }
              /* 
              .table_container {
                display: grid;
                align-items: center;
                table {
                  font-weight: 500;
                  tr {
                    td {
                      padding: 10px;
                    }
                    td + td {
                      color: #27ae60;
                    }
                    .separator {
                      border-top: 1px solid #bababa;
                      padding: unset;
                      padding-top: 10px;
                    }
                    .last_data_row {
                      padding-bottom: 20px;
                    }
                  }
                }
              } */
            }
          }
        }
        .cc_card_info {
          box-shadow: 15px 0 30px 0 #0000002e;
          position: relative;
          #cc_card_container {
            display: grid;
            grid-row-gap: 30px;
            overflow: hidden;
            width: 400px;
            margin-left: 4vw;
            .section_header {
              margin: unset;
              font-size: 1.4rem;
              font-weight: 500;
              color: #1a1a1ae6;
            }
            .cc_card {
              margin: auto;
              width: 400px;
              padding: 0 10px;
              .cc_card_body {
                form {
                  display: grid;
                  grid-gap: 16px;

                  .card_details {
                    position: relative;
                    .card_type {
                      display: grid;
                      justify-content: center;
                      align-items: center;
                      position: absolute;
                      background: #27ae60;
                      right: 1px;
                      top: 22px;
                      height: 39px;
                      padding: 0 10px;
                      border-top-right-radius: 5px;
                    }
                    .visa {
                      background: #27ae60;
                    }
                    .mastercard {
                      background: #ffffff;
                    }
                    .amex {
                      background: #016fd0;
                    }
                    .diners_club {
                      background: #ffffff;
                    }
                    .card_group_inputs {
                      border: 1px solid #d1d5db;
                      border-radius: 5px;
                      .second_row {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        border-top: 1px solid #c8c8c8;
                      }
                    }
                  }
                }

                label {
                  font-weight: 500;
                  margin: 00;
                }
                .card-input {
                  padding: 0 8px;
                  min-height: 36px;
                  min-width: 50px;
                  display: grid;
                  /* -webkit-box-align: center; */
                  -ms-flex-align: center;
                  align-items: center;
                  outline: none;
                  border: unset;
                  /* color: #fff; */
                  letter-spacing: 2px;
                  letter-spacing: 2px;
                  .card-input {
                    /*  background-color: unset; */
                  }
                }

                .card-input-raw {
                  width: 100%;
                  border: unset;
                  letter-spacing: 3px;
                  color: #b5b8bb;
                }

                .third_row {
                  display: grid;
                  grid-template-columns: 125px;
                  justify-content: flex-end;
                  padding-top: 10px;
                }

                .fourth_row {
                  display: grid;
                  grid-template-columns: auto auto;
                  justify-content: right;
                  align-items: center;
                  grid-gap: 10px;
                  padding: 15px 0 0 0;
                  color: #fff;
                  margin-bottom: -8px;

                  .tgl-light + .tgl-btn:after,
                  .tgl-light:checked + .tgl-btn:after {
                    background: #ffffff;
                  }
                  .tgl-light:checked + .tgl-btn {
                    background: #4c51bf;
                  }
                }
              }
            }

            .actions {
              display: grid;
              grid-template-columns: 1fr;
              justify-content: right;
              grid-gap: 1px;
              padding: 0 10px;
              .back_btn {
                background: #7b7976;
                color: #ffffff;
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
              }
              #yext_payment_confirm_btn {
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
              }
            }
            .consent {
              text-align: center;
              color: #1a1a1a99;
              padding: 0px 10px;
              margin-top: -15px;
              font-size: 0.8rem;
            }
          }
        }
        .part_common {
          padding: 10vh 4vw 0 40px;
          display: grid;
          justify-content: end;
          align-items: baseline;
        }
      }
    }
  }
  .loader-backdrop {
    background: #ffffffc7;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    justify-content: center;
    align-items: center;
    z-index: 2;
    p {
      margin-top: 10px;
      padding: 10px;
      color: #009000;
      font-weight: 500;
    }
  }
}
</style>
