<template>
  <div class="pricing-table__wrap">
    <!-- Pricing Table -->
    <div class="pricing-table">
      <div
        class="pricing-table__plan"
        v-for="plan in saasPlans"
        :key="plan.planLevel"
      >
        <div
          class="pricing-table__plan-tag"
          v-if="currentLevel === plan.planLevel"
        >
          Current Plan
        </div>
        <div class="pricing-table__plan-info">
          <div class="pricing-table__plan-info-main">
            <div class="pricing-table__plan-name">{{ plan.title }}</div>
          </div>
        </div>
        <ul class="pricing-table__plan-highlights">
          <li><i class="fa fa-check text-success" /> {{ plan.description }}</li>
        </ul>
        <div
          class="pricing-table__price-card"
          v-for="price in activePrices(plan.stripePlans)"
          :key="price.id"
          @click="selectPrice(price.id, plan._id)"
          :class="{ '--selected': selectedPriceId === price.id }"
        >
          {{ price.symbol || '$' }} {{ price.amount / 100 }}/{{
            price.billingInterval
          }}
          <div class="pricing-table__price-id">{{ price.id }}</div>
          <div
            class="pricing-table__price-radio"
            :class="{ '--selected': selectedPriceId === price.id }"
          ></div>
        </div>
        <ul class="pricing-table__plan-features-list">
          <li class="list-heading"><b>Features</b></li>
        </ul>
        <div class="pricing-table__plan-features">
          <ul class="pricing-table__plan-features-list">
            <li v-if="plan.planLevel !== 0 && saasPlans.length">
              <span class="plan-feature__name"
                >Everything in
                <b>{{ saasPlans[plan.planLevel - 1].title }}+</b></span
              >
            </li>
            <li
              v-for="feature in planExclusiveFeatures(
                plan.saasProducts,
                plan.planLevel
              ).slice(0, 6)"
              :key="feature"
            >
              <span class="plan-feature__name">{{
                saasProductDetails[feature]
                  ? saasProductDetails[feature].title
                  : feature.split('-').join(' ')
              }}</span>
            </li>
          </ul>
          <ul
            class="pricing-table__plan-features-list --expanded"
            v-show="expandedPlans.includes(plan.planLevel)"
          >
            <li
              v-for="feature in planExclusiveFeatures(
                plan.saasProducts,
                plan.planLevel
              ).slice(6)"
              :key="feature"
            >
              <span class="plan-feature__name">{{
                saasProductDetails[feature]
                  ? saasProductDetails[feature].title
                  : feature.split('-').join(' ')
              }}</span>
            </li>
          </ul>
          <div
            class="pricing-table__plan-features__view-more"
            v-if="
              planExclusiveFeatures(plan.saasProducts, plan.planLevel).length >
              6
            "
          >
            <span
              v-if="!expandedPlans.includes(plan.planLevel)"
              @click="expandedPlans.push(plan.planLevel)"
            >
              View More (+{{
                planExclusiveFeatures(plan.saasProducts, plan.planLevel)
                  .length - 6
              }})
              <i class="icon icon-arrow-down-1"></i>
            </span>
            <span v-else @click="viewLess(plan.planLevel)">
              View Less
              <i class="icon icon-arrow-up-1"></i>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="save-btn__row">
      <button class="btn btn-light" v-if="skippable" @click="$emit('skip')">Skip for now</button>
      <div v-else></div>
      <div class="ghl-action-error" v-if="actionError">
        {{ actionError }}
      </div>
      <button
        class="btn btn-success"
        :class="{'btn-danger': addStatus === 'failed'}"
        :disabled="selectedPriceId === '' || addStatus !== ''"
        @click="addPlan()"
        style="min-width: 160px"
      >
        <span v-if="addStatus === 'adding'">
          <moon-loader color="#ffffff" size="16px" />
        </span>
        <span v-else-if="addStatus === 'failed'">Failed</span>
        <span v-else-if="addStatus === 'success' && actionError" @click="emitUpgrade"> Done</span>
        <span v-else-if="addStatus === 'success'"><i class="fas fa-check"/> Success</span>
        <span v-else>Confirm &amp; Proceed</span>
      </button>
    </div>
    <!-- End of Pricing Table -->
    <div class="no-payment-method-overlay" v-if="!paymentMethodsExist">
      <!-- <moon-loader color="#37ca37" size="24px" v-if="paymentMethodsStatus === 'fetching'"/> -->
      <div class="">
        <i class="fas fa-exclamation-triangle"></i>
        <br /><br />
        Location needs to add payment method first.
        <br />
        Please ask your client to
        <a target="_blank" :href="cardSetupLink">add credit card here</a>.
      </div>
    </div>
    <div class="no-payment-method-overlay" v-else-if="plansStatus !== 'exist'">
      <moon-loader
        color="#37ca37"
        size="24px"
        v-if="plansStatus === 'fetching'"
      />
      <div class="" v-if="plansStatus === 'not-exist'">
        <i class="fas fa-exclamation-triangle"></i>
        <br /><br />
        You have no SaaS plan configured.
        <br />
        Please
        <a target="_blank" :href="saasSetupLink">
          create your SaaS plans here.
        </a>
        .
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Location } from '@/models'
let unsubscribeLocation: () => void

export default Vue.extend({
  props: [
    'currentLevel',
    'company',
    'saasPlans',
    'saasProductDetails',
    'locationId',
    'plansStatus',
    'skippable',
  ],
  data() {
    return {
      expandedPlans: [],
      selectedPriceId: '',
      selectedPlanId: '',
      addStatus: '',
      // paymentMethodsStatus: 'fetching', // 'fetching', 'exist', 'not-exist'
      location: undefined as Location | undefined,
      actionError: '',
      // recommendedLevel: 2
    }
  },
  created() {
    // this.getPaymentMethods()
    this.fetchLocationData();
  },
  computed: {
    // company() {
    //   return this.$store.state.company.company
    // },
    domain() {
      return this.company.domain
        ? `https://${this.company.domain}`
        : window.location.origin || 'https://app.gohighlevel.com'
    },
    cardSetupLink() {
      return `${this.domain}/location/${this.locationId}/settings/billing`
    },
    saasSetupLink() {
      return `${this.domain}/saas_dashboard`
    },
    paymentMethodsExist() {
      if (
        this.location &&
        this.location.settings.saas_settings &&
        this.location.settings.saas_settings.saas_mode === 'activated'
      ) return true
      else return false
    }
  },
  methods: {
    async fetchLocationData() {
      if (!this.locationId) return

      if (unsubscribeLocation) unsubscribeLocation()
      unsubscribeLocation = Location.getByIdRealtime(
        this.locationId
      ).onSnapshot(snapshot => {
        this.location = new Location(snapshot)
      })
    },
    // async getPaymentMethods() {
    //   try {
    //     this.paymentMethodsStatus = 'fetching'
    //     const { data: paymentMethods } = await this.saasService.get(
    //       `/location-payment-methods/${this.locationId}`
    //     )
    //     // console.log('got payment methods --> ', paymentMethods)
    //     if (paymentMethods.length > 0) {
    //       this.paymentMethodsStatus = 'exist'
    //     } else {
    //       this.paymentMethodsStatus = 'not-exist'
    //     }
    //   } catch (err) {
    //     this.paymentMethodsStatus = 'not-exist'
    //     console.error(err.message)
    //   }
    // },

    viewLess(planLevel) {
      this.expandedPlans = this.expandedPlans.filter(plan => {
        return plan === planLevel ? false : true
      })
    },
    activePrices(stripePlans) {
      if (stripePlans)
        return stripePlans
          .filter(price => price.active === true)
          .sort((a,b) => (a.amount < b.amount ? -1 : 1))
      else return []
    },
    planExclusiveFeatures(saasProducts, planLevel) {
      return saasProducts.filter(feature => {
        if (planLevel !== 0) {
          if (
            this.saasPlans[planLevel - 1] &&
            this.saasPlans[planLevel - 1].saasProducts &&
            this.saasPlans[planLevel - 1].saasProducts.includes(feature)
          ) {
            return false
          } else {
            return true
          }
        } else {
          return true
        }
      })
    },
    selectPrice(priceId, planId) {
      this.selectedPlanId = planId
      this.selectedPriceId = priceId
    },
    async addPlan() {
      // const payload = {
      //   // stripeAccountId: this.company.stripe_connect_id,
      //   companyId: this.company.id,
      //   saasPlanId: this.selectedPlanId,
      //   stripePriceId : this.selectedPriceId
      // }
      this.addStatus = 'adding';
      try{
        const resp = await this.saasService.post(
          `/saas-config/${this.locationId}/attach-saas-plan/${this.selectedPlanId}`,
          {
            stripePriceId: this.selectedPriceId,
            companyId: this.company.id,
            stripeAccountId: this.company.stripe_connect_id,
          }
        )
        console.log('got resp --> ', resp)
        this.addStatus = 'success'
        if (resp.twilioRebillingSetupComplete === false) {
          this.actionError = `Subscription successfully added, please check the twilio config for this account to enable rebilling.`
        } else {
          setTimeout(() => {
            this.emitUpgrade()
          }, 1000)
        }
      } catch (err) {
        this.addStatus = 'failed';
        setTimeout( ()=>{
          this.addStatus = '';
        }, 2000)
      }
    },
    emitUpgrade() {
      this.$emit('upgraded', {
        saasPlanId: this.selectedPlanId,
        stripePriceId: this.selectedPriceId
      })
    },
  },
  beforeDestroy() {
    if (unsubscribeLocation) unsubscribeLocation()
  },
})
</script>

<style scoped lang="scss">
.pricing-table__wrap {
  padding: 0px 16px 16px;
  max-height: calc(100vh - 64px);
  overflow-y: auto;
}
/* ===================> Pricing Table Duration */

/* Pricing Table Plan ===================> */
.pricing-table {
  display: flex;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}
.pricing-table__plan {
  padding: 32px 20px;
  position: relative;
  flex: 1 1;
  border-right: 1px solid #e0e0e0;
  text-align: left;
}
.pricing-table__plan:last-child {
  border-right: none;
}
.pricing-table__plan-tag {
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  padding: 6px;
  width: 180px;
  position: absolute;
  top: 0px;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  font-size: 14px;
  line-height: 16px;
  font-weight: 400;

  background-color: #ffffff;
  color: #4f4f4f;
}
.pricing-table__plan-info {
  display: flex;
  justify-content: space-between;
  height: unset;
}
.pricing-table__plan-info-main {
  color: #373737;
}
.pricing-table__plan-name {
  font-size: 18px;
  line-height: 20px;
  font-weight: 500;
  /* margin-bottom: 16px; */
  margin-bottom: 24px;
}

.pricing-table__plan-tagline {
  color: #bdbdbd;
  font-size: 14px;
  line-height: 16px;
  font-weight: 400;
  max-width: 150px;
}
.plan-info-quick__item {
  cursor: pointer;
  display: flex;
  align-items: stretch;
  justify-content: space-between;

  margin-bottom: 12px;
}
.plan-info-quick__item-icon {
  max-height: 20px;
  max-width: 20px;
}
.plan-info-quick__item-value {
  max-height: 24px;
  max-width: 20px;
  margin-left: 8px;
  border-bottom: 1px dashed #bdbdbd;
  color: #828282;
}
.pricing-table__plan-highlights {
  list-style: none;
  padding-inline-start: 0px;
  font-size: 14px;
  line-height: 16px;
  color: #4f4f4f;
  height: 24px; /* As per design */
}
.pricing-table__plan-highlights li {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}
.pricing-table__plan-highlights li i {
  margin-right: 8px;
}

.pricing-table__plan-features {
  height: 280px;
  overflow-y: auto;
  position: relative;
}
.pricing-table__plan-features-list {
  list-style: none;
  padding-inline-start: 0px;
  font-size: 14px;
  line-height: 16px;
  color: 828282;
}
.pricing-table__plan-features-list li.list-heading {
  color: #4f4f4f;
  font-weight: 500;
}
.pricing-table__plan-features-list li {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.pricing-table__plan-features__view-more {
  color: #2f80ed;
  font-weight: 14px;
  line-height: 16px;
  font-weight: 500;
  cursor: pointer;
}
.pricing-table__plan-features__view-more i {
  font-size: 10px;
  margin-left: 8px;
}
/* ===================> Pricing Table Plan */
.plan-feature__name {
  margin-right: 8px;
}
.plan-feature__upcomming-tag {
  background-color: #ffe59d;
  padding: 1px 8px;
  border-radius: 10px;
  font-size: 12px;
  /* margin: 0px 0px 0px 8px; */
  /* text-transform: lowercase; */
  white-space: nowrap;
}
.plan-feature__new-tag {
  background-color: #a6f5ad;
  padding: 1px 8px;
  border-radius: 10px;
  font-size: 12px;
  /* margin: 0px 0px 0px 8px; */
  /* text-transform: lowercase; */
  white-space: nowrap;
}

.pricing-table__price-card {
  background-color: #c2d8fa3b;
  border: 1px solid #4e90ef4f;
  border-radius: 5px;
  margin-bottom: 16px;
  padding: 12px 16px 12px 32px;
  box-sizing: border-box;
  cursor: pointer;
  color: #373737;
  line-height: 17px;
  position: relative;
  .pricing-table__price-id {
    font-size: 10px;
    opacity: 0.6;
  }
  .pricing-table__price-radio {
    position: absolute;
    top: 50%;
    left: 8px;
    height: 16px;
    width: 16px;
    border-radius: 50%;
    background-color: #fff;
    transform: translateY(-50%);
    border: 1px solid #6aa1f2;
    transition: all 0.3s ease-in-out;
    &.--selected {
      background-color: #ffffff;
      border: 5px solid #36c937;
    }
  }
  &.--selected {
    border: 1px solid #36c937;
    // font-weight: 500;
    background-color: #99e1941a;
  }
}
.save-btn__row {
  padding-top: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.no-payment-method-overlay {
  background-color: rgba($color: #ffffff, $alpha: 0.9);
  height: 100%;
  width: 100%;
  position: absolute;
  top: 0px;
  left: 0px;
  // z-index: 5;
  font-size: 20px;
  line-height: 24px;

  display: flex;
  align-items: center;
  justify-content: center;
  i {
    color: #f77d7d;
    font-size: 44px;
    margin: auto;
    text-align: center;
    display: block;
  }
  a {
    color: #2f80ed;
    text-decoration: none;
    font-weight: 500;
  }
}
.ghl-action-error {
  text-align: center;
  line-height: 18px;
  font-size: 14px;
  color: #fa7777;
}
</style>
