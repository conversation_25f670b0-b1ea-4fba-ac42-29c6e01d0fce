<template>
  <div
    id="delete-confirm-modal"
    ref="modal"
    aria-hidden="true"
    aria-labelledby="client-checkin--modalLabel"
    class="modal fade"
    role="dialog"
    tabindex="-1"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 id="delete-confirm-modal-label" class="modal-title">
            Delete selected item?
          </h5>
          <button aria-label="Close" class="close" type="button"
                  @click="closeModal">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <form>
              <div class="form-group">
                <div class="alert alert-warning" role="alert"
                     style="color: #856404;">
                  <i class="fas fa-exclamation-triangle"></i> This action cannot
                  be reversed.
                </div>
              </div>
              <div class="form-group">
                <p>The selected item will be permanently deleted from your
                  account.</p>
              </div>
              <div class="form-group">
                <UITextInputGroup
                  id="delete-confirm"
                  v-model="deleteConfirmation"
                  :class="deleteConfirmation.toLowerCase() === 'delete' ? 'is-valid' : 'is-invalid'"
                  label="Confirm deletion by typing 'DELETE' below:"
                  type="text"
                />
                <div
                  v-if="deleteConfirmation && deleteConfirmation !== 'delete'"
                  id="validation-feedback"
                  class="invalid-feedback">
                  Enter <strong>DELETE</strong> to confirm
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer--inner">
            <UIButton use="outline" type="button" @click="closeModal">
              Cancel, Keep
            </UIButton>
            <UIButton
              :disabled="deleteDisabled"
              use="danger"
              type="button"
              @click="deleteConfirmed"
            >Yes, Delete
            </UIButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: {
    showModal: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      deleteConfirmation: '',
      deleteDisabled: true
    }
  },
  watch: {
    showModal(status: Boolean) {
      this.deleteConfirmation = ''
      var _self = this
      if (status) {
        $(this.$refs.modal)
          .modal('show')
          .on('hidden.bs.modal', function() {
            _self.$emit('hidden', true)
          })
      } else {
        $(this.$refs.modal).modal('hide')
      }
    },
    deleteConfirmation(value: string) {
      this.deleteDisabled = !(value && value === 'DELETE')
    }
  },
  methods: {
    deleteConfirmed() {
      this.$emit('delete', true)
    },
    closeModal() {
      this.$emit('showDeleteModal', false)
      $(this.$refs.modal).modal('hide')
    }
  }
})
</script>
<style scoped>
.modal .modal-dialog {
  max-width: 500px;
}
</style>
