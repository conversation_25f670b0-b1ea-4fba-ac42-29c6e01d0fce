<template>
  <modal
    class="wallet-transaction__container"
    v-if="show"
    @close="closeModal"
    title="Export Transactions"
    :showCloseIcon="true"
  >
    <template v-slot:body>
      <div class="wallet-transaction__body" v-if="loading">
        <moon-loader size="30px" />
      </div>
      <div class="wallet-transaction__body" v-else>
        <div v-if="!sent">
          We will send a CSV file to your email. It usually takes 7 - 10
          minutes.
          <div class="req-card__tags-info">
            <i class="fas fa-exclamation-circle"></i>
            Use commas to separate multiple emails.
          </div>
          <UIMultiSelect
            dataKey="name"
            dataValue="email"
            :allowEmpty="false"
            label="Enter Email Address"
            v-model="selectedEmails"
            :itemList="allEmails"
            :validationFn="isValidEmail"
            @inputRaw="handleListUpdate"
          />
        </div>
        <div v-else>
          Please check your email ({{ sendEmailList }}).
          <br />
          <br />
          It usually arrives in 10 minutes.
        </div>
      </div>
    </template>
    <template v-slot:footer>
      <div v-if="!sent" class="wallet-transaction__footer">
        <button
          @click="sendRequestForTransactionMail"
          :disabled="isExportClicked || selectedEmails.length <= 0"
          class="btn btn-primary"
        >
          Export &amp; Send CSV
        </button>
      </div>
      <div v-else class="wallet-transaction__footer">
        <button @click="closeModal" class="btn btn-primary">Done</button>
      </div>
    </template>
  </modal>
</template>
<script lang="ts">
import Vue from 'vue'
import Modal from '@/pmd/components/common/Modal.vue'
import UIMultiSelect from '../../UIMultiSelect.vue'
import { User } from '@/models'
import { UserSource } from '@/models/user'

export default Vue.extend({
  props: {
    show: {
      default: false,
      type: Boolean,
    },
    accountId: {
      type: String,
      required: true,
    },
    endpoint: {
      type: String,
      default: 'location-wallet'
    },
    view: {
      type: String,
      default: 'location'
    },
    source: String
  },
  components: {
    Modal,
    UIMultiSelect,
  },
  data() {
    return {
      sent: false,
      isExportClicked: false,
      allEmails: [],
      selectedEmails: [],
      selectedEmailsRaw: [],
      loading: false
    }
  },
  mounted() {
    if(this.view === 'company') {
      User.getAgencyUsersByCompanyId(
        this.company.id
      ).then(users => {
        this.allEmails = users
      })
    } else if (this.view === 'companyAndLocation') {
      this.getLocationAndCompanyUsers()
    } else {
      User.getByLocationCompany(
        this.accountId,
        this.company.id // TODO lookup from the
      ).then(users => {
        this.allEmails = users
      })
    }
  },
  computed: {
    sendEmailList() {
      return this.selectedEmails.join(', ')
    },
    company() {
      return this.$store.state.company.company
    },
    tz() {
      const tz = Intl.DateTimeFormat().resolvedOptions().timeZone
      return tz
    },
  },
  methods: {
    async getLocationAndCompanyUsers () {
      const users = []
      const agencyUsers = await User.getAgencyUsersByCompanyId(
        this.company.id
      )
      users.push(...agencyUsers)
      const locationUsers = await User.getByLocationCompany(
        this.accountId,
        this.company.id // TODO lookup from the
      )
      users.push(...locationUsers)
      users.forEach((val, index) => {
        for (let i = index + 1; i < users.length; i++ ) {
          if(val._id === users[i]._id) {
            users.splice(i, 1)
          }
        }
      })
      this.allEmails = users
    },
    clearFields() {
      this.selectedEmails = []
      this.selectedEmailsRaw = []
    },
    closeModal() {
      this.clearFields()
      this.$emit('close')
      this.contact = null
    },
    async sendRequestForTransactionMail() {
      let url = this.source === 'PhoneBilling' ? `/${this.endpoint}/${this.company.id}/mail-transactions?locationId=${this.accountId}&timezone=${this.tz}` : `/${this.endpoint}/${this.accountId}/mail-transactions?timezone=${this.tz}`
      this.isExportClicked = true
      await this.saasService
        .post(url, {
          source: this.source,
          // users: this.selectedEmails,
          users: this.selectedEmailsRaw.map( user => { return {email: user.email, name: user.name, id: user.id}}),
        })
        .then(() => (this.sent = true))
        .finally(() => (this.isExportClicked = false))
    },
    isValidEmail(email) {
      const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      return re.test(String(email).toLowerCase())
    },
    handleListUpdate(users) {
      console.log(users);
      this.selectedEmailsRaw = users;
    }
  }
})
</script>

<style lang="scss" scoped>
.wallet-transaction__header {
  padding: 16px 32px;
  border-bottom: 1px solid #c4c4c4;

  .wallet-transaction__header-title {
    font-size: 24px;
    line-height: 28px;
    color: #737373;
  }
}

.wallet-transaction__body {
  padding: 32px;
  // max-height: calc(100vh - 240px);
  // overflow: auto;

  .metadata {
    margin-bottom: 25px;
  }

  .body {
    background: #f3f8fb;
    padding: 26px;
    margin: 26px 0;
    font-size: 12px;
    color: #737373;
    border-radius: 6px;
  }

  .details {
    margin-top: 25px;
    display: flex;
    justify-content: space-between;

    .transaction-amount {
      .credit {
        color: green;
      }
      .debit {
        color: red;
      }
    }

    .link-to-conversation {
      a:hover {
        text-decoration: underline;
      }
    }
  }

  .data {
    font-size: 14px;
    color: black;

    span:first-child {
      color: #737373;
      margin-right: 4px;
    }

    span:last-child {
      font-weight: bold;
    }
  }
}

.wallet-transaction__footer {
  padding-bottom: 30px;
  text-align: right;
  padding-right: 32px;

  button {
    padding: 9px 34px;
  }
}
.req-card__tags-info {
  font-size: 14px;
  line-height: 16px;
  color: #8a939f;
  margin-top: 8px;
  margin-bottom: 12px;
  i {
    color: #b5b8bb;
    margin-right: 6px;
  }
}
</style>
