
import { MessageType, SmartList } from "../../../../models";
import { FilterBuilder } from "./filter_vm_builder";
import { ReadOnlySmartListVM} from './smart_list_vm'
import { SmartListColumnsVM } from "./smart_list_columns";


export async function getLastActivityByType(messageType: MessageType, locationId: string,  assignedTo?:string ) {
  if (!locationId) throw new Error ('Location id required');
  if (!messageType) throw new Error ('Message type required');
  await FilterBuilder.loadForLocation(locationId);
  const lastFilter = FilterBuilder.getFilterByName(FilterBuilder.LastActivityTypeFilterName);
  if (!lastFilter || !lastFilter.selectedOption) {
    console.log(`Filter Not Found or No Default Option: ${FilterBuilder.LastActivityTypeFilterName}`)
    return []
  }
  lastFilter.selectedOption.firstValue = [{ key: messageType, value: messageType }];
  const vm = await ReadOnlySmartListVM.forFilters([lastFilter],locationId, assignedTo);
  vm.resetColumnSortDisplay([{ fieldName: SmartListColumnsVM.LastActivityColumn.sortName, direction: 'desc' }])
  await vm.first();
  return vm.contacts;
}

export async function getCommonBusinessName(businessName: string, locationId: string,  assignedTo?:string ) {
  if (!locationId) throw new Error ('Location id required');
  if (!businessName) throw new Error ('Business name equired');
  await FilterBuilder.loadForLocation(locationId);
  const lastFilter = FilterBuilder.getFilterByName(FilterBuilder.BusinessFilterName);
  if (!lastFilter || !lastFilter.selectedOption) {
    console.log(`Filter Not Found or No Default Option: ${FilterBuilder.BusinessFilterName}`)
    return []
  }
  lastFilter.selectedOption.firstValue = businessName;
  const vm = await ReadOnlySmartListVM.forFilters([lastFilter],locationId, assignedTo);
  vm.resetColumnSortDisplay([{ fieldName: SmartListColumnsVM.LastActivityColumn.sortName, direction: 'desc' }])
  await vm.first();
  return vm.contacts;
}
