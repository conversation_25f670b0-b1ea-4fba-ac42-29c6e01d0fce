<template>
  <div
    id="workflowBuilder"
    ref="workflowBuilder"
    style="height: 100vh; width: 100vw; border: none"
    :class="{
      'hl_sidebar-v2-collapse-container':
        $store.state.manualCollapseSidebar && isV2SideBarEnable,
      'hl_sidebar-v2-open-container':
        !$store.state.manualCollapseSidebar && isV2SideBarEnable,
    }"
  ></div>
</template>

<script lang="ts">
import Vue from 'vue'
import Postmate from 'postmate'
import config from '../../../config'
import firebase from 'firebase/app'
import { AuthUser, Location } from '../../../models'
import store from '../../../store'

const setElementDisplay = (
  element: HTMLElement | null,
  displayValue: string,
  isMounted = true
) => {
  try {
    if (element) {
      if (element.hasAttribute('data-workflow-display')) {
        element.removeAttribute('data-workflow-display')
        return
      }
      const computedStyle = window.getComputedStyle(element)
      if (
        isMounted &&
        (element.style?.display === displayValue ||
          computedStyle?.display === displayValue)
      )
        element.setAttribute('data-workflow-display', displayValue)
      else element.style.display = displayValue
    }
  } catch (err) {
    console.warn(err, '')
  }
}

export default Vue.extend({
  props: {
    location: {
      type: Object,
      required: true,
    },
    user: {
      type: Object,
      required: true,
    },
  },
  mounted() {
    this.loadMicroApp()
    this.previousSideBarState = this.$store.getters.getManualCollapseSidebar
    if (!this.isV2SideBarEnable)
      this.$store.commit('setManualCollapseSidebar', true)
    this.toggelCustomJsVisibility('none')
  },
  computed: {
    company() {
      return this.$store.state.company?.company
    },
    isV2SideBarEnable() {
      return this.$store.getters['sidebarv2/getVersion'] == 'v2'
    },
  },
  beforeDestroy() {
    this.childApp.then((child: any) => {
      child.destroy()
      this.childApp = undefined
    }) // closing the child connection
    if (!this.isV2SideBarEnable)
      this.$store.commit('setManualCollapseSidebar', this.previousSideBarState)
    this.toggelCustomJsVisibility('inline', false)
  },
  data: () => {
    return {
      childApp: undefined as any,
      previousSideBarState: undefined as boolean | undefined,
      showSmtpWarning: false,
    }
  },
 
  methods: {
    async showFromSmtpWarning() {
      if (
        this.location?.default_email_service &&
        this.location?.default_email_service !== 'mailgun'
      ) {
        this.showSmtpWarning = await this.$store.dispatch(
          'defaultEmailService/checkFromVisibility',
          this.location.default_email_service
        )
      }
    },
    toggelCustomJsVisibility(value: string, isMounted = true) {
      // agency custom js breaks workflow builder drag and drop feature
      // hide that DOM temprorly when user opens workflow builder

      // do not hide it in list page
      if (value === 'none' && this.$route.name === 'workflows') return

      for (let element of [
        'customJS',
        'freshworks-container',
        'beacon-container',
      ]) {
        const customJsEl = document.getElementById(element)
        setElementDisplay(customJsEl, value, isMounted)
      }

      //@ts-expect-error dom operation
      document.body.children.forEach(e => {
        const child = e.children
        if (e?.id?.includes('pendo')) {
          // Do nothing as this is from pendo
        } else if (
          window.getComputedStyle(e).position === 'fixed' ||
          (e.id !== 'app' &&
            child.length > 0 &&
            window.getComputedStyle(child[0]).position === 'fixed')
        ) {
          setElementDisplay(e, value)
        }
      })
    },
    async getTokenId(): Promise<string> {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const tokenId = await firebase.auth().currentUser.getIdToken()
      return tokenId
    },

    async setChildApp() {
      const workflowURL = config.workflowAppURL
      const tokenId = await this.getTokenId()

      await this.showFromSmtpWarning()

      const auth: AuthUser = new AuthUser(await store.dispatch('auth/get'))
      const userPermission = this.$store.state.user?.user?.permissions

      const user = {
        id: firebase.auth().currentUser?.uid,
        type: this.$store.state.user?.user?.type,
        role: this.$store.state.user?.user?.role,
        permission: {
          workflows_enabled:
            userPermission?.workflows_read_only !== undefined
              ? userPermission?.workflows_enabled
              : userPermission?.campaigns_enabled,
          workflows_read_only:
            userPermission?.workflows_read_only !== undefined
              ? userPermission?.workflows_read_only
              : userPermission?.campaigns_read_only,
        },
        first_name: this.$store.state.user?.user?.first_name,
        last_name:this.$store.state.user?.user?.last_name,
        profile_photo:this.$store.state.user?.user?.profile_photo,
        email: this.$store.state.user?.user?.email,
        phone: this.$store.state.user?.user?.phone,
        date_update: ''
      }

      const location = new Location(
        await this.$store.dispatch('locations/getById', this.location.id)
      )

      const handshake = new Postmate({
        container: this.$refs.workflowBuilder,
        url: `${workflowURL}${this.$route.fullPath}`,
        name: 'workflow-builder',
        model: {
          locationId: location.id,
          internalUser: this.$store.state.user?.internalUser || false,
          tokenId: tokenId,
          apiKey: location.apiKey,
          authApiKey: auth.apiKey,
          timeZone: this.location.timezone,
          smtpWarningFlag: this.showSmtpWarning, // to show warning in case of some of the SMTPs.
          company: {
            plan: this.$store.state.company?.company?.stripe_active_plan,
            id: this.$store.state.company?.company?.id,
          },
          user,
          users: this.$store.state.users.users
        },
      })

      handshake
        .then((child: any) => {
          console.log('Handshake with Child complete.')
          child.on('route-change', (data: any) => {
            console.log(data)

            if (data.inNewPage) {
              let routeData = this.$router.resolve({
                path: data.path,
              })
              window.open(routeData.href, '_blank')
            } else {
              this.$router.push(data.path)
            }
          })
          child.on('getRefreshedTokenId', async () => {
            const tokenId = await this.getTokenId()
            child.call('setTokenId', tokenId)
          })
        })
        .catch(async (err: Error) => {
          console.error({ handledError: err })
          this.$refs.workflowBuilder.innerHTML = ''
          await this.setChildApp()
        })
      this.childApp = handshake
    },

    async loadMicroApp() {
      await this.setChildApp()
    },
  },
})
</script>
<style>
#workflowBuilder iframe {
  width: 100%;
  height: 100%;
  border: none;
}
.workflow-builder {
  width: 100%;
  background: #ffffff;
}
</style>
