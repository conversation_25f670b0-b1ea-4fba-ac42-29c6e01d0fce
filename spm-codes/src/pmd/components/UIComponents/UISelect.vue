<template>
  <select
    :value="value"
    @change="$emit('change',$event.target.value)"
    @input="val => this.$emit('input', val)"
    class="mt-1 block w-full pl-3 pr-10 py-2 text-base text-gray-800 border-gray-300 focus:outline-none focus:ring-curious-blue-500 focus:border-curious-blue-500 sm:text-sm rounded-md"
  >
    <option disabledfo value="" v-if="placeholder">{{placeholder}}</option>
    <option
      v-for="(type, index) in options"
      :key="index"
      :value="type.value"
    >
      {{ type.label }}
    </option>
  </select>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: {
    value:String,
    options: {
      type: Array,
    },
    placeholder:{
      type:String,
      default:''
    }

  },
})
</script>

<style scoped></style>
