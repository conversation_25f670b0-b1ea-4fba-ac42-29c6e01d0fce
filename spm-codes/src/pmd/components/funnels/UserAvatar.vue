<template>
  <div
    class="avatar"
    :class="{ 'idle-user': checkUserIsIdle() }"
    data-tooltip="tooltip"
    data-placement="top"
    aria-haspopup="true"
    v-b-tooltip.hover
    :title="userFullName()"
    :style="{ backgroundColor: profileColor() }"
  >
    <div v-if="user.profile_photo" class="avatar_img">
      <img :src="user.profile_photo" />
    </div>
    <div v-else-if="initials()" class="avatar_img">
      {{ initials() }}
    </div>
    <div v-else class="avatar_img --gray">?</div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import moment from 'moment'
export default Vue.extend({
  props: {
    user: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    profileColor(): string {
      let str = this.user.first_name + ' ' + this.user.last_name
      if (this.user.email) str += this.user.email
      if (this.user.phone) str += this.user.phone
      let hash = 0
      if (str.length == 0) return '#afb8bc'
      for (let i = 0; i < str.length; i++) {
        hash = str.charCodeAt(i) + ((hash << 5) - hash)
        hash = hash & hash // Convert to 32bit integer
      }
      // var colourIndex = Math.abs(hash) % 10;
      // return User.colours[colourIndex];
      const shortened = Math.abs(hash) % 360
      return 'hsl(' + shortened + ',35%, 60%)'
    },
    initials(): string {
      const { last_name: lastName, first_name: firstName } = this.user
      let initials = ''
      if (firstName) initials += firstName.substring(0, 1).toUpperCase()
      if (lastName) initials += lastName.substring(0, 1).toUpperCase()
      return initials
    },
    userFullName() {
      return this.user.first_name + ' ' + this.user.last_name
    },
    checkUserIsIdle() {
       return moment(this.user.date_update.toMillis()).isBefore(
       moment().subtract(5, 'minutes'))
    }
  }
})
</script>

<style scoped>
.idle-user {
  opacity: 0.5;
  background-color: #fff;
}
.avatar {
  display: flex;
  -webkit-box-align: center;
  align-items: center;
  height: 35px;
  width: 35px;
  border-radius: 50%;
  padding: 2px;
  justify-items: center;
  justify-content: center;
  border: 1px solid #ddeefe;
}

.avatar_img {
  min-width: 30px;
  width: 30px;
  height: 30px;
  line-height: 30px;
  border-radius: 50%;
  color: #fff;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  overflow: hidden;
}

.avatar_img > img {
  max-width: 35px;
  max-height: 35px;
  border-radius: 50%;
}
</style>
