<template>
    <vue-final-modal
      classes="flex justify-center items-center"
      content-class="relative flex flex-col max-h-full mx-4 border dark:border-gray-800 rounded bg-white dark:bg-gray-900"
      :value="showDeleteModal"
      @input="closeModal"
      :clickToClose="false"
    >
      <div class="max-w-lg mx-auto px-4">
        <div class="border-b flex items-center">
            <i class="fa fa-trash text-red-600 text-lg" aria-hidden="true"></i>
          <div class="text-lg leading-6 font-medium text-gray-900 px-3 py-4">
            Delete {{ name }}
          </div>
        </div>
        <UIAlert type="warning" >
            This action cannot be reversed.
        </UIAlert>
        <div class="text-left my-3">
            {{warningText}}
        </div>
        <span class="text-gray-700 font-bold">
          Confirm deletion by typing 'DELETE' below.
        </span>
        <UITextInputGroup
          type="text"
          placeholder="Type here."
          v-model="confirmText"
          v-validate="'required'"
          name="confirmText"
          autocomplete="off"
          :value="confirmText"
          class="my-2"
        />
        <div class="pb-4 px-2 flex justify-end space-x-2">
        <UIButton use="outline" @click="closeModal" :id="cancelBtnId"> Cancel, Keep </UIButton>
        <UIButton use="danger" :disabled="confirmText !== 'DELETE'" @click="$emit('click'), confirmText=''" :id="deleteBtnId"> Yes, Delete </UIButton>
      </div>
      </div>
    </vue-final-modal>
</template>

<script>
import { defineComponent } from '@vue/composition-api'

export default defineComponent({
    props: {
        showDeleteModal: Boolean,
        name: String,
        loading: Boolean,
        warningText: String,
        deleteProduct: Function,
        cancelBtnId: String,
        deleteBtnId: String,
    },
    data() {
      return {
        confirmText: ''
      }
    },
    methods: {
      closeModal() {
          this.$emit('close')
      },
    }
})
</script>
