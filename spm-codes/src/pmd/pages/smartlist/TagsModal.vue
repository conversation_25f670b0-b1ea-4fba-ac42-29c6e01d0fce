<template>
  <b-modal ref="modal-sm-d" size="sm" @hide="close" hide-footer>
      <template v-slot:modal-title>
        <h5 class="modal-title">
          <i class="icon" :class="{'icon-plus' : type === 'add', 'icon-minus' : type === 'remove'}" ></i>
          {{ type | capitalize }} Tags
        </h5>
      </template>
      <template>
        <div class="modal-body">
          <div class="modal-body--inner">
            <h4>
              {{ type | capitalize }} tags to the following contacts
            </h4>
            <div class="avatar-group">
              <template v-if="!contacts || contacts.length <= 0">
                <span style="font-size:15px; color:#188bf6">No contact selected</span>
              </template>
              <template v-else v-for="(contact,index) in contacts">
                <Avatar v-if="index < 10" :contact="contact" :include_name="false"/>
                <template v-if="totalContacts >=10">
                  <span v-if="index === contacts.length - 1"
                        style="margin-top:10px; font-size:19px,margin-left:10px; color:#188bf6">
                    {{totalContacts - 10}} &nbsp;more contacts...
                  </span>
                </template>
              </template>
            </div>
            <TagComponent v-model="selectedTags" v-if="type !== 'delete'"/>
            <div class="my-1" v-if="errMessage"><span class="--red">{{errMessage}}</span></div>
          </div>
        </div>
       <div class="modal-footer">
        <div class="modal-footer--inner">
          <div class="modal-footer--inner nav" v-if="!sending">
            <UIButton use="outline" @click.prevent="close">
              Cancel
            </UIButton>
            <div style="display: inline-block;position: relative;" v-if="contacts.length > 0">
              <UIButton
                v-if="operation"
                @click.prevent="operate"
                :loading="sending"
              >
                {{ type === 'add'? 'Add': 'Remove' }} &nbsp;Tags
              </UIButton>
            </div>
          </div>
          <div class="modal-footer--inner nav" v-else>
            <div class="uploading" style="text-align: center;">
              <div class="progress">
                <div class="progress-bar progress-bar-striped bg-success progress-bar-animated"
                     :style="percent">
                </div>
              </div>
              <p>{{status.updated}}/{{status.total}}</p>
            </div>
          </div>
        </div>
      </div>
      </template>
  </b-modal>
</template>

<script lang="ts">
import Vue from 'vue'
import { Contact, User, Tag, ImportRequest, ImportType, ImportStatus } from '@/models'
import { StatusCheck } from './vm/action_vm'
import { UxMessage } from '@/util/ux_message'

const Avatar = () => import('../../components/Avatar.vue')
const MoonLoader = () => import('@/pmd/components/MoonLoader.vue')
const TagComponent = () => import('@/pmd/components/customer/TagComponent.vue')

let unsubscribeImportRequest: () => void;

export default Vue.extend({
  name:"TagsModal",
  props: ['contacts','operation','totalContacts'],
  components: {
    Avatar,
    MoonLoader,
    TagComponent
  },
  inject: ['uxmessage'],
  data() {
    return {
      // tagSearch: '',
      tags: [] as string[],
      currentLocationId: '',
      disabled: false,
      selectedTags: [],
      sending: false,
      status : this.operation.status || new StatusCheck(),
      errMessage: ''
    }
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchTags();
  },
  async mounted(){
    await this.$nextTick();
    this.showPopup();
  },
  methods: {
    async fetchTags() {
      this.tags = (await Tag.getByLocationId(this.currentLocationId))
      .map(tag => tag.name)
    },
    async operate() {
      try {
        if (!this.selectedTags || this.selectedTags.length <= 0){
          this.errMessage = "Select atleast one tag";
          return;
        }
        this.sending = true
        const authUser = await this.$store.dispatch('auth/get')
        await this.operation.action(authUser.userId, {tags: this.selectedTags },(status:string)=>{
          this.sending = false
          if (status !== 'success'){
            this.showError(status);
          } else {
            this.close(true);
          }
        })
      } catch (e) {
        this.sending = false
        console.error(e);
        this.showError(e && e.message || 'unknown error',e);
        this.$emit('onError',e);
      }
    },
    showError(err:string, stack?:string){
      this.uxmessage(UxMessage.errorType(err,stack))
    },
    close(processed: boolean = false) {
      this.cleanUp();
      this.hidePopup();
      this.$emit('closed', processed)
    },
    cleanUp() {
      this.selectedTags = []
    },
    showPopup(){
      if(this.$refs['modal-sm-d']) this.$refs['modal-sm-d'].show();
    },
    hidePopup(){
      if(this.$refs['modal-sm-d']) this.$refs['modal-sm-d'].hide();
    }
  },
  computed: {
    type(){
      return this.operation.getActionType() || '';
    },
    percent(): { [key: string]: any } {
      return { width: (this.status.updated / this.status.total ) * 100 + '%' }
    },
    exactMatch(): boolean {
      return this.tags.indexOf(this.tagSearch) !== -1
    },
    filteredTags(): string[] {
      let filtered = <string[]>lodash.difference(this.tags, this.selectedTags)
      filtered = lodash.filter(
        filtered,
        tag => tag.indexOf(this.tagSearch) !== -1
      )
      return filtered
    }
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.currentLocationId = id
      this.fetchTags()
    },
    selectedTags: function(n){
      if (!n || n.length<0) this.errMessage = 'Select atleast one tag';
      else this.errMessage = '';
    }
  },
  filters: {
    capitalize: function(value) {
      if (!value) return ''
      value = value.toString()
      return value.charAt(0).toUpperCase() + value.slice(1)
    },

  }
})
</script>
<style>
</style>
