<template>
  <div>
    <div class="container-fluid">
      <div class="hl_marketing--header">
        <div class="container-fluid">
          <div class="hl_marketing--header-inner">
            <h2>Account Snapshot</h2>
            <UIButton
              use="primary"
              type="button"
              @click.prevent="createSnapshot"
              v-if="isAdmin"
            >
              <i class="icon icon-plus mr-2" style="color:white;margin-right: 10px;"></i>
              Create New Snapshot
            </UIButton>
          </div>
          <ul v-if="getSideBarVersion != 'v2'" class="hl_marketing--nav">
            <li @click.prevent="type = 'own'" :class="{ active: type === 'own' }">
              <a
                class="nav-link"
                id="own-tab"
                role="tab"
                aria-controls="own"
                aria-selected="true"
              >My Snapshots</a>
            </li>
            <li @click.prevent="type = 'imported'" :class="{ active: type === 'imported' }">
              <a
                class="nav-link"
                id="imported-tab"
                role="tab"
                aria-controls="imported"
                aria-selected="false"
              >Imported Snapshots</a>
            </li>
            <li @click.prevent="type = 'default'" :class="{ active: type === 'default' }">
              <a
                class="nav-link"
                id="default-tab"
                role="tab"
                aria-controls="default"
                aria-selected="false"
              >Default Snapshots</a>
            </li>
          </ul>
        </div>
      </div>
      <div class="hl_settings--controls">
        <div class="hl_settings--controls-left">
          <ul class="hl_marketing--nav"></ul>
        </div>
        <div class="hl_settings--controls-right"></div>
      </div>
      <div class="card">
        <div class="--no-padding">
          <div class="table-wrap">
            <table class="table">
              <thead>
                <tr>
                  <th>Snapshot Name</th>
                  <th v-if="type === 'own'">Snapshot Date</th>
                  <th v-if="type === 'own'">Account Name</th>
                  <th style="width: 1px;" v-if="type === 'own'"></th>
                  <th style="width: 1px;" v-if="type === 'own' && isAdmin"></th>
                  <th style="width: 1px;" v-if="type === 'own' && isAdmin"></th>
                  <th style="width: 1px;" v-if="type !== 'default' && isAdmin"></th>
                  <th style="width: 1px;" v-if="type !== 'default' && isAdmin"></th>
                </tr>
              </thead>
              <tbody>
                <SnapshotAccount
                  v-for="account in accounts"
                  :key="account.id"
                  :account="account"
                  :isProcessingPush="isProcessingPush(account.id)"
                  :isProcessingDehydrate="isProcessingDehydrate(account.id)"
                  :isProcessingDelete="isProcessingDelete(account.id)"
                  :type="type"
                  :is-admin="isAdmin"
                  @shareSnapshot="shareSnapshot(account.id)"
                  @dehydrateAccount="dehydrateAccount(account.id)"
                  @pushToAllAccounts="pushToAllAccounts(account.id)"
                  @editAccountSnapshot="editAccountSnapshot(account.id)"
                  @deleteAccountSnapshot="deleteAccountSnapshot(account.id)"
                />
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <CreateEditCloneAccount
      :values="createEditModal"
      @hidden="createEditModal = { visible: false }"
    />
    <PushCloneAccount :values="pushCloneModal" @hidden="pushCloneModal = { visible: false }" />
    <ShareCloneAccount :values="shareModal" @hidden="shareModal = { visible: false }" />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import {
  CloneAccount,
  AuthUser,
  Location,
  getCountryDateFormat,
  User
} from '@/models'
import config from '../../../config'
import * as moment from 'moment-timezone'
import { UserState } from '@/store/state_models'
import { mapState } from 'vuex'
import UIButton from '@/pmd/components/UIComponents/UIButton.vue'
const CreateEditCloneAccount = () =>
  import(
    /* webpackChunkName: "edit-clone-account" */ '@/pmd/components/agency/CreateEditCloneAccount.vue'
  )
const ShareCloneAccount = () =>
  import(
    /* webpackChunkName: "share-clone-account" */ '@/pmd/components/agency/ShareCloneAccount.vue'
  )

const PushCloneAccount = () =>
  import('@/pmd/components/agency/PushCloneAccount.vue')

const SnapshotAccount = () =>
  import('@/pmd/components/agency/snapshots/SnapshotAccount.vue')

let unsubscribeCloneAccounts: () => void

export default Vue.extend({
  components: {
    CreateEditCloneAccount,
    ShareCloneAccount,
    PushCloneAccount,
    SnapshotAccount,
    UIButton
  },
  data() {
    return {
      authUser: {} as AuthUser,
      currentLocationId: '' as string,
      accounts: [] as CloneAccount[],
      processingDelete: {} as { [key: string]: boolean },
      processingDehydrate: {} as { [key: string]: boolean },
      processingPush: {} as { [key: string]: boolean },
      type: 'own',
      createEditModal: { visible: false } as { [key: string]: any },
      pushCloneModal: { visible: false } as { [key: string]: any },
      shareModal: { visible: false } as { [key: string]: any },
      seqCalling: 0,
      getCountryDateFormat
    }
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.currentLocationId = id
      this.fetchData()
    },
    '$route.params.type': function(id) {
      this.type = id
      this.fetchData()
    },
    type() {
      this.$router.push({ name: 'snapshot', params: { type: this.type } })
      if (!lodash.isEmpty(this.authUser)) {
        this.fetchData()
      }
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    isAdmin(): boolean {
      return this.user && this.user.role === 'admin'
    },
    getSideBarVersion(): string {
        return this.$store.getters['sidebarv2/getVersion'] 
    },
  },
  async created() {
    if (this.$router.currentRoute.params.type)
      this.type = this.$router.currentRoute.params.type
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.authUser = await this.$store.dispatch('auth/get')
    this.fetchData()
  },
  beforeDestroy() {
    if (unsubscribeCloneAccounts) unsubscribeCloneAccounts()
  },
  methods: {
    async dehydrateAccount(accountId: string) {
      if (confirm('Are you sure you want to update this snapshot?')) {
        Vue.set(this.processingDehydrate, accountId, true)
        try {
          await this.$http.post(`/snapshot/${accountId}/refresh`)
        } catch (error) {
          console.error('Error while refreshing snapshot --> ', error)
          alert('Error while refreshing snapshot, please try again!')
        } finally {
          Vue.set(this.processingDehydrate, accountId, false)
        }
      }
    },
    async pushToAllAccounts(accountId: string) {
      this.pushCloneModal = {
        visible: true,
        companyId: this.authUser.companyId,
        snapshotId: accountId
      }

      // if (
      //   confirm(
      //     'Are you sure you want to push this update to all linked accounts?'
      //   )
      // ) {
      //   Vue.set(this.processingPush, account.id, true)
      //   const response = await this.$http.post(`/snapshot/${account.id}/load_all`)
      //   Vue.set(this.processingPush, account.id, false)
      // }
    },
    // getByLocationId(locationId: string): Location | undefined {
    //   return this.$store.getters['locations/getById'](locationId)
    // },
    // getLocationName(locationId: string) {
    //   const location = this.getByLocationId(locationId)
    //   if (location) return location.name
    // },
    async fetchData() {
      if (unsubscribeCloneAccounts) unsubscribeCloneAccounts()
      this.accounts = []
      this.seqCalling++
      let seqCalling = this.seqCalling

      if (this.type === 'own' || this.type === 'imported') {
        unsubscribeCloneAccounts = CloneAccount.getByCompanyIdAndTypeRealtime(
          this.authUser.companyId,
          this.type
        ).onSnapshot(snapshot => {
          if (this.seqCalling !== seqCalling) return
          this.accounts = snapshot.docs.map(d => new CloneAccount(d))
          // console.log(JSON.stringify(snapshot.docs.map(d => { return {account_data: d.data().account_data, name: d.data().name};})));
        })
      } else {
        const url = '/snapshot/templates'
        const response = await this.$http.get(url)
        if (this.seqCalling !== seqCalling) return
        this.accounts = response.data
      }
    },
    isProcessingPush(accountId: string) {
      return this.processingPush[accountId]
    },
    isProcessingDehydrate(accountId: string) {
      return this.processingDehydrate[accountId]
    },
    isProcessingDelete(accountId: string) {
      return this.processingDelete[accountId]
    },
    editAccountSnapshot(accountId: string) {
      this.createEditModal = {
        visible: true,
        companyId: this.authUser.companyId,
        snapshotId: accountId
      }
    },
    createSnapshot() {
      this.createEditModal = {
        visible: true,
        companyId: this.authUser.companyId
      }
    },
    shareSnapshot(accountId: string) {
      this.shareModal = {
        visible: true,
        snapshotId: accountId,
        userId: this.authUser.userId
      }
    },
    async deleteAccountSnapshot(accountId: string) {
      if (confirm('Are you sure you want to delete this snapshot?')) {
        Vue.set(this.processingDelete, accountId, true)
        try {
          await this.$http.delete(`/snapshot/${accountId}`)
        } catch (error) {
          console.error('Error while deleting snapshot --> ', error)
          alert('Error while deleting snapshot, please try again.')
        } finally {
          Vue.set(this.processingDelete, accountId, false)
        }
      }
    }
  }
})
</script>
