<template>
  <div>
    <div class="form-group">
      <UITextInputGroup
        type="text"
        placeholder="Webhook URL"
        v-validate="'handlebars'"
        data-vv-validate-on="input"
        name="webhook_url"
        :value="webhookUrl"
        @input="setValue('webhook_url', $event)"
        :error="errors.has('webhook_url')"
        :errorMsg="errors.first('webhook_url')"
      />
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Pipeline, Stage } from '@/models'

export default Vue.extend({
  props: ['action'],
  data() {
    return {
      currentLocationId: ''
    }
  },
  computed: {
    webhookUrl(): string {
      if (this.action) return this.action.webhook_url
      return ''
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
  },
  methods: {
    setValue(field: string, value: string) {
      Vue.set(this.action, field, value)
      Vue.set(this.action, 'webhook_request_type', 'POST')
      this.$emit('update:action', this.action)
    }
  }
})
</script>
