<template>
  <div v-if="schedule">
    <template v-if="!inputDisabled">
      <div v-if="showTitles" class="py-1 text-nowrap">
        <UITextLabel>{{ startOnLabel }}</UITextLabel>
      </div>
      <div class="form-row">
        <div class="form-group col-12">
          <vue-ctk-date-time-picker
            :disabled="inputDisabled"
            :locale="getCountryInfo('locale')"
            v-model="schedule.startOn"
            :noClearButton="true"
            :no-button-now="true"
            :max-date="maxDate"
            minute-interval="1"
            color="#188bf6"
            enable-button-validate
            v-validate="'required'"
            label=""
            name="date"
            class="border shadow-sm order-gray-300 rounded"
          >
          </vue-ctk-date-time-picker>
          <span v-show="errors.has('date')" class="error"
            >Please select a date
          </span>
          <!-- no-button-now -->
        </div>
      </div>
      <div v-if="showTitles" class="py-1 text-nowrap">
        <UITextLabel>{{ dripQuantityLabel }}</UITextLabel>
      </div>
      <div class="form-row">
        <div class="form-group col-12">
          <UITextInput
            :disabled="inputDisabled"
            type="text"
            v-model="schedule.dripQuantity"
            v-validate="'required'"
            name="quantity"
          />
          <span v-show="errors.has('quantity')" class="error">
            Please enter a quantity
          </span>

        </div>
      </div>
      <div v-if="showTitles" class="py-1 text-nowrap">
        <UITextLabel>{{ dripGapLabel }}</UITextLabel>
      </div>
      <div class="form-row">
        <div class="form-group col-6">
          <UITextInput
            :disabled="inputDisabled"
            type="text"
            v-model="schedule.dripDelay"
            v-validate="'required'"
            name="delay"
          />
          <span v-show="errors.has('delay')" class="error">
            Please enter a delay
          </span>
        </div>
        <div class="form-group col-6">
          <div class="dropdown bootstrap-select form-control">
            <button
              type="button"
              class="btn dropdown-toggle btn-light"
              style="height: 40px;"
              data-toggle="dropdown"
              role="button"
              title="Lead"
              aria-expanded="true"
              @click.stop.prevent="delayTypeToggle.toggle()"
            >
              <div class="filter-option">
                <div class="filter-option-inner">
                  <div class="filter-option-inner-inner capitalize">
                    {{ dripDelayType }}
                  </div>
                </div>
              </div>
            </button>
            <div class="dropdown-menu" :id="delayTypeToggle.id" role="combobox">
              <div
                class="inner show"
                role="listbox"
                aria-expanded="true"
                tabindex="-1"
                style="max-height: 749px; overflow-y: auto; min-height: 0px"
              >
                <ul class="dropdown-menu inner show">
                  <li>
                    <a
                      @click.prevent.stop="delayTypeChange2('days')"
                      role="option"
                      class="dropdown-item"
                      aria-disabled="false"
                      tabindex="0"
                      aria-selected="false"
                    >
                      <span class="text">days</span></a
                    >
                  </li>
                  <li>
                    <a
                      @click.prevent.stop="delayTypeChange2('hours')"
                      role="option"
                      class="dropdown-item"
                      aria-disabled="false"
                      tabindex="0"
                      aria-selected="false"
                    >
                      <span class="text">hours</span></a
                    >
                  </li>
                  <li>
                    <a
                      @click.prevent.stop="delayTypeChange2('minutes')"
                      role="option"
                      class="dropdown-item"
                      aria-disabled="false"
                      tabindex="0"
                      aria-selected="false"
                    >
                      <span class="text">minutes</span></a
                    >
                  </li>
                  <li>
                    <a
                      @click.prevent.stop="delayTypeChange2('seconds')"
                      role="option"
                      class="dropdown-item"
                      aria-disabled="false"
                      tabindex="0"
                      aria-selected="false"
                    >
                      <span class="text">seconds</span></a
                    >
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="form-row">
        <div class="form-group col-12">
          <UITextLabel>Send On</UITextLabel>
          <div class="form-group-controls hl_campaign-configuration">
            <div class="week-select">
              <div class="week-select-day" v-for="w in weekDays" :key="w">
                <input
                  type="checkbox"
                  :id="`${w}-wait-event`"
                  v-model="schedule.sendDays"
                  :value="w"
                  @change="dayError = false"
                />
                <label :for="`${w}-wait-event`">{{ w }}</label>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="form-group">
        <div class="form-row">
          <div class="d-inline-block mx-2">
            <UITextLabel>Process between hours (optional)</UITextLabel>
            <a
              @click.prevent.stop="clearProcessWindow()"
              v-if="schedule && (windowEndLabel || windowStartLabel)"
            >
              <span class="text px-4">Clear time selection</span></a
            >
          </div>
        </div>

        <div class="form-row">
          <div class="col-6">
            <div class="d-inline-block mx-2 my-1">
              <UITextLabel>Start From</UITextLabel>
            </div>
            <div class="dropdown bootstrap-select">
              <button
                type="button"
                class="btn dropdown-toggle btn-light"
                data-toggle="dropdown"
                role="button"
                title="Lead"
                aria-expanded="true"
                @click.stop.prevent="startTimeToggle.toggle()"
              >
                <div class="filter-option">
                  <div class="filter-option-inner">
                    <div class="filter-option-inner-inner">
                      <span v-if="windowStartLabel">{{
                        windowStartLabel
                      }}</span>
                    </div>
                  </div>
                </div>
              </button>
              <div
                class="dropdown-menu"
                :id="startTimeToggle.id"
                role="combobox"
              >
                <div
                  class="inner show"
                  role="listbox"
                  aria-expanded="true"
                  tabindex="-1"
                  style="max-height: 100px; overflow-y: auto; min-height: 0px"
                >
                  <ul class="dropdown-menu inner show">
                    <li
                      v-for="slot in timeSlots"
                      :key="'start-' + slot.valueOf()"
                      :value="slot.valueOf()"
                    >
                      <div
                        class="dropdown-item"
                        @click.stop.prevent="setStartTime(slot)"
                      >
                        {{ slot.format('hh:mm a') }}
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div class="form-group col-6">
            <div class="d-inline-block mx-2 my-1"><UITextLabel>End At</UITextLabel></div>
            <div class="dropdown bootstrap-select">
              <button
                type="button"
                class="btn dropdown-toggle btn-light"
                data-toggle="dropdown"
                role="button"
                title="Lead"
                aria-expanded="true"
                @click.stop.prevent="endTimeToggle.toggle()"
              >
                <div class="filter-option">
                  <div class="filter-option-inner">
                    <div class="filter-option-inner-inner">
                      <span v-if="windowEndLabel">{{ windowEndLabel }}</span>
                    </div>
                  </div>
                </div>
              </button>
              <div class="dropdown-menu" :id="endTimeToggle.id" role="combobox">
                <div
                  class="inner show"
                  role="listbox"
                  aria-expanded="true"
                  tabindex="-1"
                  style="max-height: 100px; overflow-y: auto; min-height: 0px"
                >
                  <ul class="dropdown-menu inner show">
                    <li
                      v-for="slot in timeSlots"
                      :key="'start-' + slot.valueOf()"
                      :value="slot.valueOf()"
                    >
                      <div
                        class="dropdown-item"
                        @click.stop.prevent="setEndTime(slot)"
                      >
                        {{ slot.format('hh:mm a') }}
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <span v-show="errors.has('dispatchEndTime')" class="error">
              {{ errors.first('dispatchEndTime') }}
            </span>
          </div>
        </div>
      </div>
    </template>
    <!-- <div class="d-block"><span v-if="!firstValue" class="--red">* Enter a value</span></div>
    <div><span v-if="!secondValue" class="--red">* Select an option</span></div>
    <div class="my-2" v-if="example" ><label style="font-size:1.1em !important;">{{example}}</label></div> -->
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import {
  Pipeline,
  Stage,
  Status,
  CustomField,
  CustomValue,
  getCountryInfo,
} from '@/models'
import { BulkSchedule } from './server/bulk_schedule'
import { SafeDropDownToggle } from './safe_drop_down_toggle'
const CommonSelector = () => import('./CommonSelector.vue')
import { KeyVal } from './CommonSelector.vue'
import * as moment from 'moment'

export default Vue.extend({
  name: 'BulkScheduler',
  inject: ['parentValidator'],
  props: {
    schedule: { type: BulkSchedule },
    startOnLabel: { type: String, default: 'Start On' },
    dripQuantityLabel: { type: String, default: 'Batch Quantity' },
    dripGapLabel: { type: String, default: 'Repeat After' },
    buttonProcesAllLabel: { type: String, default: 'Process all at once' },
    buttonAllAtScheduleLabel: {
      type: String,
      default: 'Process all at schedule',
    },
    buttonProcessScheduleLabel: {
      type: String,
      default: 'Process in drip mode',
    },
  },
  components: {
    CommonSelector,
  },
  data(): any {
    return {
      getCountryInfo,
      maxDate: '',
      scheduleMode: null as { key: string; display: string } | null,
      // modes: null as { value: string; text: string }[]| null,
      delayTypes: [
        new KeyVal('days'),
        new KeyVal('hours'),
        new KeyVal('minutes'),
        new KeyVal('seconds'),
      ],
      weekDays: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      selectedMode: {} as { [key: string]: any } | null,
      showTitles: true,
      //skipDaysToggle: new SafeDropDownToggle(this, 'skipDaysToggle'),
      delayTypeToggle: new SafeDropDownToggle(this, 'delayTypeToggle'),
      startTimeToggle: new SafeDropDownToggle(this, 'startTimeToggle'),
      endTimeToggle: new SafeDropDownToggle(this, 'endTimeToggle'),
      dripDelayType: 'days',
      windowStartLabel: '',
      windowEndLabel: '',
      timeSlots: [],
    }
  },
  watch: {
    schedule(newVal: BulkSchedule) {},
    dripDelayType: function (val: string) {
      //console.log('change - ' + val);
      this.schedule.dripDelayType = val
    },
    windowStartLabel: function (val: string) {
      this.validate()
    },
    windowEndLabel: function (val: any) {
      this.validate()
    },
  },
  computed: {
    inputDisabled() {
      return this.schedule && this.schedule.dripMode === 'none'
    },
  },
  async mounted() {
    this.timeSlots = []
    let start = moment().startOf('day')
    let end = moment().startOf('day').add(1, 'day')
    for (; start.isBefore(end); start.add(30, 'minutes')) {
      this.timeSlots.push(start.clone())
    }
    if (this.schedule && !this.schedule.sendDays) this.schedule.sendDays = []
    if (this.schedule && this.schedule.processWindowStart) {
      this.setStartTime(this.schedule.processWindowStart)
      this.startTimeToggle.hide()
    }
    if (this.schedule && this.schedule.processWindowEnd) {
      this.setEndTime(this.schedule.processWindowEnd)
      this.endTimeToggle.hide()
    }
    this.dripDelayType = this.schedule.dripDelayType
    // this.modes = [
    //     { value: 'none', text: this.buttonProcesAllLabel },
    //     { value: 'drip', text: this.buttonProcessScheduleLabel },
    //     { value: 'allatschedule', text: this.buttonAllAtScheduleLabel }
    //   ];
    // const item = this.modes.find(a => a.key === this.schedule.dripMode)
    // if (item) this.scheduleMode = item
  },
  async created() {
    this.$validator = this.parentValidator
  },
  methods: {
    clearProcessWindow() {
      this.setEndTime(null)
      this.setStartTime(null)
    },
    setEndTime(slot: any) {
      if (slot) this.endTimeToggle.toggle()
      this.schedule.processWindowEnd = slot ? slot.clone() : null
      this.windowEndLabel = this.schedule.processWindowEnd
        ? this.schedule.processWindowEnd.format('hh:mm a')
        : ''
    },
    setStartTime(slot: any) {
      if (slot) this.startTimeToggle.toggle()
      this.schedule.processWindowStart = slot ? slot.clone() : null
      this.windowStartLabel = this.schedule.processWindowStart
        ? this.schedule.processWindowStart.format('hh:mm a')
        : ''
      // console.log(`Moment put back to getther ${moment( this.windowStartLabel,'hh:mm a')}`);
      // console.log(`Moment put back to getther ${moment(moment().add(1, 'Days').format('yyyy-mm-dd') + ' ' +  this.windowStartLabel,'yyyy-mm-dd hh:mm a')}`);
    },
    validate() {
      // console.log(this.dispatchEndTime.diff(this.dipatchStartTime, 'minutes') )
      if (
        this.schedule.processWindowStart &&
        this.schedule.processWindowEnd &&
        this.schedule.processWindowEnd.diff(
          this.schedule.processWindowStart,
          'minutes'
        ) <= 0
      ) {
        this.$validator.errors.add({
          field: 'dispatchEndTime',
          msg: `* Should be after selected ${this.schedule.processWindowStart.format(
            'hh:mm a'
          )}`,
        })
      } else if (
        this.schedule.processWindowStart &&
        !this.schedule.processWindowEnd
      ) {
        this.$validator.errors.add({
          field: 'dispatchEndTime',
          msg: `* Select end time`,
        })
      } else if (
        !this.schedule.processWindowStart &&
        this.schedule.processWindowEnd
      ) {
        this.$validator.errors.add({
          field: 'dispatchEndTime',
          msg: `* Select start time`,
        })
      } else {
        this.$validator.errors.remove('dispatchEndTime')
      }
    },
    // async addSkipDay(item: KeyVal) {
    //   if (!item) return
    //   if (!this.schedule) return
    //   if (!this.schedule.sendDays) this.schedule.sendDays = []
    //   if (!this.schedule.sendDays.includes(item.value))
    //     this.schedule.sendDays = [...this.schedule.sendDays, item.value]
    // },
    // async removeSkipDay(item: string) {
    //   if (!this.schedule) return
    //   if (!this.schedule.sendDays) return
    //   this.schedule.sendDays = this.schedule.sendDays.filter(a => a !== item)
    // },
    async delayTypeChange(event: KeyVal) {
      this.dripDelayType = event.value
    },
    async delayTypeChange2(val: string) {
      this.delayTypeToggle.toggle()
      this.dripDelayType = val
    },
  },
})
</script>
<style lang="scss" scoped>
.header-underline {
  display: inline-block;
  border-bottom: 1px solid #d5dde1;
  list-style: none;
  width: 100% !important;
  margin-bottom: 20px;
}
.customized-header {
  display: inline-block;
  list-style: none;
  padding: 0;
  width: 100% !important;
  padding-right: 15px;
  padding-left: 15px;
  margin: 0px;
}

</style>
