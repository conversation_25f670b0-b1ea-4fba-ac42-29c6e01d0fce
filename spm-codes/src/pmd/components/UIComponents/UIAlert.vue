<template>
  <div
    class="px-4 py-2 rounded relative text-gray-600 text-left"
    :class="theme"
    role="alert"
    v-if="!closeAlert"
  >
    <strong class="text-sm leading-4 font-medium" v-if="title">{{
      title
    }}</strong>
    <span class="block sm:inline text-sm leading-5"><slot></slot></span>
    <span class="absolute top-0 bottom-0 right-0 px-2 py-2" v-if="closeable">
      <svg
        class="fill-current h-6 w-6"
        role="button"
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 20 20"
        @click="close"
      >
        <title>Close</title>
        <path
          d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"
        />
      </svg>
    </span>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref } from '@vue/composition-api'
export interface StyleMap {
  [key: string]: string
}
const types: StyleMap = {
  danger: 'bg-red-50 border border-red-300 ',
  info: 'bg-blue-50 border border-blue-300 ',
  warning: 'bg-yellow-50 border border-yellow-300',
  tip: 'border border-blue-300',
  success: 'bg-green-50 border border-green-300',
}

export default defineComponent({
  name: 'UIAlert',
  props: {
    title: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: 'danger',
    },
    closeable: {
      type: Boolean,
      default: false,
    },
    alertName: {
      type: String,
    },
    expireTime: {
      type: [String, Number],
      default: '1D',
    }
  },
  setup(props, ctx) {
    const theme = types[props.type]
    const closeAlert = ref(false);

    const close = () => {
      closeAlert.value = true;
      ctx.root.$cookie.set(props.alertName, true, { expires: props.expireTime })
    }

    onMounted(() => {
      closeAlert.value = ctx.root.$cookie.get(props.alertName) || false
    })

    return {
      theme,
      closeAlert,
      close,
    }
  },
})
</script>
