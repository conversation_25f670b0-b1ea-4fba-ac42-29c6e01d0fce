<template>
  <tr>
    <td>
      <router-link
        :to="{name: 'form_builder_manage', params: {location_id: currentLocationId, builder_id: formbuilder.id}}"
        tag="a"
      >{{ formbuilder.name }}</router-link>
    </td>
    <td v-text="formbuilder.id"/>
    <td v-text="formbuilder.deleted ? 'Inactive' : 'Active'"/>
    <td>
      <div class="btn-group">
          <router-link
            tag="button"
            class="btn btn-primary"
            :to="{name: 'form_builder_manage', params: {location_id: currentLocationId, builder_id: formbuilder.id}}"
          >Edit</router-link>
          <button
            type="button"
            class="btn btn-primary dropdown-toggle dropdown-toggle-split"
            data-toggle="dropdown"
            aria-haspopup="true"
            aria-expanded="true"
          ></button>
          <div class="dropdown-menu dropdown-menu-right">
            <a
              class="dropdown-item"
              href="javascript:void(0);"
              @click.prevent="$emit('clone', formbuilder)"
            >Copy</a>
            <a
              class="dropdown-item"
              href="javascript:void(0);"
              @click.prevent="deleteBuilderItem"
            >Delete</a>
          </div>
        </div>
    </td>
  </tr>
</template>

<script lang="ts">
import Vue from "vue";
import { mapState } from "vuex";
import { UserState } from "../../../store/state_models";
import { Formbuilder, User } from "../../../models/";

export default Vue.extend({
  props: ["formbuilder"],
  data() {
    return {
      currentLocationId: ""
    };
  },
  watch: {
    "$route.params.location_id": function(id) {
      this.currentLocationId = id;
    }
  },
  computed: {
    allowEdit(): boolean {
      //this.user.permissions.campaigns_read_only !== true;
      return this.user ? true : false;
    },
    ...mapState("user", {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined;
      }
    })
  },
  methods: {
    async deleteBuilderItem() {
      if (confirm("Are you sure you want to delete this form?")) {
        let formStore = await Formbuilder.getById(this.formbuilder.id);
        formStore.deleted = true;
        await formStore.save();
        this.$emit('updated')
      }
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id;
  }
});
</script>
