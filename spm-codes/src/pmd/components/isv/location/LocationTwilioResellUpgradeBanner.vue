<template>
  <div>
    <div class="hl-banner__isv-upgrade" :class="{'--success':variant === 'success'}" v-if="user && user.type === 'agency' && user.role === 'admin' && company && company.stripe_active_plan && company.stripe_active_plan.split('_')[2] !== '497'">
      <i class="fas fa-dollar-sign" style="margin-right: 8px;"></i>
      <div class="text-center">
        <span style="font-weight: 500;">Want to rebill this to your clients?</span>
        <br />
        <span style="color:#4F4F4F">Agencies on pro plan make about $363 per month by rebilling phone costs to their clients</span>
      </div>
      <div class="hl-agency-banner__actions">
        <div
          class="btn hl-alert__action-btn"
          :class="{'--success':variant === 'success'}"
          @click="showUpgradeModal = true"
        >
          Upgrade
        </div>
      </div>
    </div>
    <upgrade-modalv2
      :company="company"
      v-if="showUpgradeModal"
      :show="showUpgradeModal"
      source="billing-page"
      @close="showUpgradeModal = false"
    />
  </div>
</template>

<script>
import { User } from '@/models'
import UpgradeModalv2 from "@/pmd/components/agency/billing/UpgradeModalv2.vue";

export default {
  props: {
    showCloseIcon: {
      type: Boolean,
      default: false,
    },
    variant: {
      type: String,
      default: 'success',
    },
  },
  components: {
    UpgradeModalv2,
  },
  computed: {
    company() {
      return this.$store.state.company.company
    },
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    // showUpgradeBanner() {
    //   if (
    //     this.showUpgradeBannerDefault &&
    //     this.user &&
    //     this.user.type === 'agency' &&
    //     this.user.role === 'admin' &&
    //     this.company &&
    //     this.company.stripe_active_plan &&
    //     this.company.stripe_active_plan.includes('_monthly_297') &&
    //     this.company.free_upgrade_alert_dismissed !== true
    //   ) {
    //     return true
    //   } else return false
    // },
  },
  data() {
    return {
      showPromoteFreeUpgradeModal: false,
      showUpgradeModal: false,
      // showUpgradeBannerDefault: true,
    }
  },
  created() {
    // let dismissedDate = window.localStorage.getItem('free_upgrade_alert_dismissed');
    // if (dismissedDate) {
    //   let currentTime = + new Date();
    //   if (currentTime - parseInt(dismissedDate) < 24 * 60 * 60 * 1000) {
    //     this.showUpgradeBannerDefault = false
    //   }
    // }
  },
  methods: {
    hideTemporary() {
      // this.showPromoteFreeUpgradeModal = false
      // this.showUpgradeBannerDefault = false
    },
  }
}
</script>

<style lang="scss">
.hl-banner__isv-upgrade {
  background-color: #e3f2fd;
  padding: 16px;
  border-left: 3px solid #158bf5;
  color: #158bf5;
  border-radius: 8px;
  // margin-bottom: 16px;
  margin: 16px;

  display: flex;
  justify-content: space-between;
  align-items: center;
  &.--success {
    background-color: #DDF6DD;
    border-left: 3px solid #27AE60;
    color: #27AE60;
  }
  i {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    line-height: 38px;
    text-align: center;
    color: #ffffff;
    font-size: 20px;
    background-color: #27AE60;
  }
  .hl-alert__close-icon {
    // color: #158bf5;
    cursor: pointer;
    font-weight: 500;
    // height: 30px !important;
    // width: 30px !important;
    // border-radius: 50% !important;
    opacity: 0.7;
    margin-left: 16px;
    width: 38px;
    height: 38px;
    color: #ffffff;

    &:hover {
      opacity: 1;
    }
    &.--success {
      background-color: #27AE60;
    }
  }
  .hl-alert__action-btn {
    padding: 4px 12px;
    background-color: rgba(187 ,222 ,251, 0.50);
    color: #158bf5 !important;
    margin-left: 16px;
    cursor: pointer;
    &.--success {
      // background-color: #b2dfdb73;
      // color: #009688 !important;
      background-color: #27AE60;
      color: #e0f2f1 !important;
    }
  }
  .hl-agency-banner__actions {
    display: flex;
    align-items: center;
  }
}

</style>
