<template>
  <div :class="{'-notification': !notification.read}">
    <div class="avatar --sm">
      <div class="avatar_img --gray">GHL</div>
    </div>
    <p>{{ notification.notificationData.snapshotName || 'Snapshot' }} {{ notification.notificationData.loadStatus && notification.notificationData.loadStatus === 'success' ? 'Loaded' : 'Failed' }}!</p>
    <p class="location">{{accountName}}</p>
    <p class="time-date">{{notification.dateAdded.calendar()}}</p>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Contact, Location } from '../../../models'
const Avatar = () => import('../Avatar.vue')

export default Vue.extend({
  props: ['notification'],
  components: { Avatar },
  data() {
    return {
      contact: undefined as Contact | undefined,
      accountName: ''
    }
  },
  mounted() {
    this.getLocationName(this.notification?.locationId)
  },
  methods: {
    async getByLocationId(locationId: string): Promise<Location | undefined> {
      try {
        return await this.$store.dispatch('locations/getById', locationId)
      } catch(error) {
        console.error(error)
      }
    },
    async getLocationName(locationId: string) {
      const location = await this.getByLocationId(locationId)
      if (location) {
        let name = location.name
        if (location.city || location.state) name += ', '
        if (location.city) name += location.city
        if (location.city && location.state) name += ', '
        if (location.state) name += location.state
        this.accountName = name
      }
    }
  }
  // computed: {
  //   location(): { [key: string]: any } {
  //     return this.$store.getters['locations/getById'](
  //       this.notification.locationId
  //     )
  //   },
  //   getLocationName(): string {
  //     if (!this.location) return ''
  //     let name = this.location.name
  //     if (this.location.city || this.location.state) name += ', '
  //     if (this.location.city) name += this.location.city
  //     if (this.location.city && this.location.state) name += ', '
  //     if (this.location.state) name += this.location.state
  //     return name
  //   }
  // }
  // async created() {
  //   if (this.notification.notificationData.userId) {
  //     this.contact = await this.$store.dispatch(
  //       'contacts/syncGet',
  //       this.notification.notificationData.userId
  //     )
  //   }
  // }
})
</script>
