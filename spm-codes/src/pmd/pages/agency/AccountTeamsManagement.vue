<template>
  <div class="hl_settings--body" :class="{ 'hl_v2_stettings--body': isV2SideBarEnabled}">
    <div class="container-fluid container--teams">
      <div class="hl_controls">
        <div class="hl_controls--left">
          <h2>{{ teams.length }} Teams</h2>
        </div>
        <div class="hl_controls--right">
          <UIButton
            use="primary"
            @click.prevent="modalValues = { visible: true }"
          >
            <i class="icon icon-plus mr-2"></i> Add Team
          </UIButton>
        </div>
      </div>

      <div class="card hl_settings--team-management">
        <div class="card-body --no-padding" style="max-width:100%;">
          <div class="table-wrap">
            <div
              v-if="isCreatedFromSnapshot"
              class="alert alert-primary text-dark alert-dismissible fade show"
              role="alert"
            >
              It appears that some teams are imported from a snapshot. Team
              members need to be added
              <strong>in order to activate</strong> those teams.
              <strong>Edit > Team Info > Add Team Members</strong>

              <button
                type="button"
                class="close"
                data-dismiss="alert"
                aria-label="Close"
              >
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <table class="table table-sort">
              <thead>
                <tr>
                  <th data-sort="string">Name</th>
                  <th data-sort="string" v-if="!currentLocationId">Action</th>
                  <th></th>
                </tr>
              </thead>

              <tbody>
                <tr v-for="team in teams" :index="team.id" :key="team.id">
                  <td
                    :class="{
                      disabled: team.userIds && team.userIds.length === 0
                    }"
                  >
                    {{ team.name }}
                  </td>
                  <td
                    :class="{
                      disabled: team.userIds && team.userIds.length === 0
                    }"
                  >
                    <user-avatar
                      v-for="userId in team.userIds"
                      :key="userId"
                      :userId="userId"
                      size="sm"
                      class="mr-2"
                    />
                  </td>
                  <td>
                    <div
                      class="btns"
                      :style="
                        logInUser && logInUser.role == 'admin'
                          ? 'display:inline-block;width:150px;'
                          : 'display:inline-block;width:70px;'
                      "
                    >
                      <button
                        type="button"
                        class="btn btn-primary"
                        @click.prevent="
                          modalValues = { visible: true, teamId: team.id }
                        "
                      >
                        Edit
                      </button>

                      <i
                        v-if="logInUser && logInUser.role == 'admin'"
                        style="margin-left:20px"
                        :class="{
                          invisible: providerIdwiseIsDeleting[team.id]
                        }"
                        class="icon icon-trash --gray"
                        @click.prevent="deleteTeam(team)"
                      ></i>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <EditTeamsModal
      :visible="modalValues.visible"
      :teamId="modalValues.teamId"
      :currentLocationId="currentLocationId"
      @hidden="modalValues = { visible: false }"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { User, Team, Calendar } from '@/models'
import { UserState } from '@/store/state_models'
import { mapState } from 'vuex'
import UserAvatar from '@/pmd/components/UserAvatar.vue'
const EditTeamsModal = () =>
  import(
    /* webpackChunkName: "team-edit" */ '../../components/settings/EditTeamsModal.vue'
  )

export default Vue.extend({
  components: { UserAvatar, EditTeamsModal },
  data() {
    return {
      currentLocationId: '' as string,
      modalValues: {
        visible: false,
        teamId: undefined as undefined | string
      },
      loadingTeams: true,
      filters: {
        search: null,
        role: null,
        type: null,
        locationId: null
      },
      providerIdwiseIsDeleting: {}
    }
  },
  watch: {
    '$route.params.location_id': async function(id) {
      this.currentLocationId = id
      await this.$store.dispatch('teams/syncAll', id)
    }
  },
  computed: {
    ...mapState('user', {
      logInUser: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    vuexTeams() {
      return this.$store.state.teams.teams
    },
    teams(): Team[] {
      return this.vuexTeams.map(t => new Team(lodash.cloneDeep(t)))
    },
    isCreatedFromSnapshot() {
      return (
        this.teams.filter(x => x.userIds && x.userIds.length === 0).length > 0
      )
    },
    vuexCalendars() {
      return this.$store.state.calendars.calendars
    },
    calendars() {
      const _calendars = this.vuexCalendars.map(
        s => new Calendar(lodash.cloneDeep(s))
      )
      return _calendars
    },
    providerIdwiseCalendarsCount() {
      const _providerIdwiseCalendarsCount = {}
      this.teams &&
        this.teams.forEach(p => {
          _providerIdwiseCalendarsCount[p.id] = this.calendars.reduce(
            (total, x) => (x.providerId === p.id ? total + 1 : total),
            0
          )
        })
      return _providerIdwiseCalendarsCount
    },
    isV2SideBarEnabled() {
      return this.getSideBarVersion == 'v2'
    },
    getSideBarVersion(): string {
        return this.$store.getters['sidebarv2/getVersion'] 
    },
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    await this.$store.dispatch('teams/syncAll', this.$route.params.location_id)
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }
  },
  updated() {
    const _self = this
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  methods: {
    async deleteTeam(team: Team) {
      const calendarsCount = this.providerIdwiseCalendarsCount[team.id]
      const calendarText =
        calendarsCount > 1
          ? `all the ${calendarsCount} calendars`
          : `the one and only calendar`
      const confirmMessage = `Are you sure you want to delete this team? ${
        calendarsCount > 0
          ? `\n\nNote: It's also going to delete ${calendarText} which tied with this team and all the appointments associated with it.`
          : ''
      }`

      if (confirm(confirmMessage)) {
        // TODO: (MP) - Animate Delete Process and so
        // this.$set(this.providerIdwiseIsDeleting, team.id, true)
        const providerCalendars = this.calendars.filter(
          x => x.providerId === team.id
        )
        if (providerCalendars && providerCalendars.length > 0) {
          await Promise.allSettled(
            providerCalendars.map(x => this.deleteCalendar(x, false))
          )
        }
        team.deleted = true
        await team.save()
        // this.$set(this.providerIdwiseIsDeleting, team.id, false)
      }
    },
    // Duplicate method between CalendarV3Setting and this component
    async deleteCalendar(calendar: Calendar, showConfirmMessage = true) {
      const confirmMessage =
        "Are you sure you want to delete this calendar? \n\nNote: It's also going to delete all the appointments of this calendar."
      if (!showConfirmMessage || confirm(confirmMessage)) {
        // Not needed
        // this.$set(this.calendarIdwiseIsDeleting, calendar.id, true)
        await calendar.deleteCalendarAndEvents()
        // this.$set(this.calendarIdwiseIsDeleting, calendar.id, false)
      }
    }
  }
})
</script>

<style lang="scss">
.container--teams {
  .disabled {
    opacity: 0.5;
  }
}
</style>
