/**
 * SubAccount Slice
 * 
 * Redux slice for SubAccount state management
 * Based on SPM Location store patterns and lcCore User slice patterns
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { ISubAccount, SubAccountStatus } from '../../../types/subAccount';

/**
 * SubAccount state interface
 * Based on SPM LocationState interface
 */
export interface ISubAccountState {
  /** All sub-accounts loaded */
  subAccounts: ISubAccount[];
  
  /** Active sub-accounts only */
  activeSubAccounts: ISubAccount[];
  
  /** Sub-accounts for notifications */
  notificationSubAccounts: ISubAccount[];
  
  /** Currently selected sub-account */
  currentSubAccount: ISubAccount | null;
  
  /** Loading states */
  isLoading: boolean;
  isUpdating: boolean;
  isCreating: boolean;
  isDeleting: boolean;
  
  /** Error state */
  error: string | null;
  
  /** Data loaded flag */
  subAccountsLoaded: boolean;
  
  /** Search and filter state */
  searchQuery: string;
  statusFilter: SubAccountStatus | 'all';
  
  /** Real-time subscription status */
  subscriptions: {
    subAccountUpdates: boolean;
    statusChanges: boolean;
  };
  
  /** UI state */
  selectedSubAccountIds: string[];
  sortBy: 'name' | 'dateAdded' | 'dateUpdated' | 'city' | 'state';
  sortOrder: 'asc' | 'desc';
}

/**
 * Initial state
 */
const initialState: ISubAccountState = {
  subAccounts: [],
  activeSubAccounts: [],
  notificationSubAccounts: [],
  currentSubAccount: null,
  isLoading: false,
  isUpdating: false,
  isCreating: false,
  isDeleting: false,
  error: null,
  subAccountsLoaded: false,
  searchQuery: '',
  statusFilter: 'all',
  subscriptions: {
    subAccountUpdates: false,
    statusChanges: false,
  },
  selectedSubAccountIds: [],
  sortBy: 'name',
  sortOrder: 'asc',
};

/**
 * SubAccount slice
 */
export const subAccountSlice = createSlice({
  name: 'subAccount',
  initialState,
  reducers: {
    /**
     * Set all sub-accounts
     * Based on SPM location store 'add' mutation patterns
     */
    setSubAccounts: (state: ISubAccountState, action: PayloadAction<ISubAccount[]>) => {
      state.subAccounts = action.payload;
      state.subAccountsLoaded = true;
      state.error = null;
    },

    /**
     * Add or update a single sub-account
     * Based on SPM location store 'add' mutation
     */
    addOrUpdateSubAccount: (state: ISubAccountState, action: PayloadAction<ISubAccount>) => {
      const subAccount = action.payload;
      const existingIndex = state.subAccounts.findIndex(sa => sa.id === subAccount.id);
      
      if (existingIndex >= 0) {
        state.subAccounts[existingIndex] = subAccount;
      } else {
        // Insert in sorted order by name
        const insertIndex = state.subAccounts.findIndex(sa => sa.name > subAccount.name);
        if (insertIndex >= 0) {
          state.subAccounts.splice(insertIndex, 0, subAccount);
        } else {
          state.subAccounts.push(subAccount);
        }
      }
      
      state.error = null;
    },

    /**
     * Remove sub-account
     */
    removeSubAccount: (state: ISubAccountState, action: PayloadAction<string>) => {
      const subAccountId = action.payload;
      state.subAccounts = state.subAccounts.filter(sa => sa.id !== subAccountId);
      state.activeSubAccounts = state.activeSubAccounts.filter(sa => sa.id !== subAccountId);
      state.notificationSubAccounts = state.notificationSubAccounts.filter(sa => sa.id !== subAccountId);
      
      if (state.currentSubAccount?.id === subAccountId) {
        state.currentSubAccount = null;
      }
      
      state.selectedSubAccountIds = state.selectedSubAccountIds.filter(id => id !== subAccountId);
    },

    /**
     * Clear all sub-accounts
     * Based on SPM location store 'clearAll' mutation
     */
    clearAllSubAccounts: (state: ISubAccountState) => {
      state.subAccounts = [];
      state.activeSubAccounts = [];
      state.notificationSubAccounts = [];
      state.currentSubAccount = null;
      state.subAccountsLoaded = false;
      state.selectedSubAccountIds = [];
    },

    /**
     * Set active sub-accounts
     * Based on SPM location store 'addActiveLocation' mutation
     */
    setActiveSubAccounts: (state: ISubAccountState, action: PayloadAction<ISubAccount[]>) => {
      state.activeSubAccounts = action.payload;
    },

    /**
     * Set notification sub-accounts
     * Based on SPM location store 'setNotificationLocation' mutation
     */
    setNotificationSubAccounts: (state: ISubAccountState, action: PayloadAction<ISubAccount[]>) => {
      state.notificationSubAccounts = action.payload;
    },

    /**
     * Set current sub-account
     * Based on SPM location store 'setCurrentLocation' mutation
     */
    setCurrentSubAccount: (state: ISubAccountState, action: PayloadAction<ISubAccount | null>) => {
      state.currentSubAccount = action.payload;
      state.error = null;
    },

    /**
     * Set loading state
     */
    setLoading: (state: ISubAccountState, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
      if (action.payload) {
        state.error = null;
      }
    },

    /**
     * Set updating state
     */
    setUpdating: (state: ISubAccountState, action: PayloadAction<boolean>) => {
      state.isUpdating = action.payload;
      if (action.payload) {
        state.error = null;
      }
    },

    /**
     * Set creating state
     */
    setCreating: (state: ISubAccountState, action: PayloadAction<boolean>) => {
      state.isCreating = action.payload;
      if (action.payload) {
        state.error = null;
      }
    },

    /**
     * Set deleting state
     */
    setDeleting: (state: ISubAccountState, action: PayloadAction<boolean>) => {
      state.isDeleting = action.payload;
      if (action.payload) {
        state.error = null;
      }
    },

    /**
     * Set error state
     */
    setError: (state: ISubAccountState, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.isLoading = false;
      state.isUpdating = false;
      state.isCreating = false;
      state.isDeleting = false;
    },

    /**
     * Set search query
     */
    setSearchQuery: (state: ISubAccountState, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },

    /**
     * Set status filter
     */
    setStatusFilter: (state: ISubAccountState, action: PayloadAction<SubAccountStatus | 'all'>) => {
      state.statusFilter = action.payload;
    },

    /**
     * Set subscription status
     */
    setSubscriptionStatus: (
      state: ISubAccountState,
      action: PayloadAction<{ type: keyof ISubAccountState['subscriptions']; active: boolean }>
    ) => {
      state.subscriptions[action.payload.type] = action.payload.active;
    },

    /**
     * Set selected sub-account IDs
     */
    setSelectedSubAccountIds: (state: ISubAccountState, action: PayloadAction<string[]>) => {
      state.selectedSubAccountIds = action.payload;
    },

    /**
     * Toggle sub-account selection
     */
    toggleSubAccountSelection: (state: ISubAccountState, action: PayloadAction<string>) => {
      const subAccountId = action.payload;
      const index = state.selectedSubAccountIds.indexOf(subAccountId);
      
      if (index >= 0) {
        state.selectedSubAccountIds.splice(index, 1);
      } else {
        state.selectedSubAccountIds.push(subAccountId);
      }
    },

    /**
     * Set sorting
     */
    setSorting: (
      state: ISubAccountState,
      action: PayloadAction<{ sortBy: ISubAccountState['sortBy']; sortOrder: ISubAccountState['sortOrder'] }>
    ) => {
      state.sortBy = action.payload.sortBy;
      state.sortOrder = action.payload.sortOrder;
    },

    /**
     * Update current sub-account data
     * For optimistic updates
     */
    updateCurrentSubAccount: (state: ISubAccountState, action: PayloadAction<Partial<ISubAccount>>) => {
      if (state.currentSubAccount) {
        state.currentSubAccount = {
          ...state.currentSubAccount,
          ...action.payload,
        };
      }
    },

    /**
     * Clear error state
     */
    clearError: (state: ISubAccountState) => {
      state.error = null;
    },

    /**
     * Clear sub-account state
     */
    clearSubAccountState: (state: ISubAccountState) => {
      return { ...initialState };
    },
  },
});

/**
 * Export actions
 */
export const {
  setSubAccounts,
  addOrUpdateSubAccount,
  removeSubAccount,
  clearAllSubAccounts,
  setActiveSubAccounts,
  setNotificationSubAccounts,
  setCurrentSubAccount,
  setLoading,
  setUpdating,
  setCreating,
  setDeleting,
  setError,
  setSearchQuery,
  setStatusFilter,
  setSubscriptionStatus,
  setSelectedSubAccountIds,
  toggleSubAccountSelection,
  setSorting,
  updateCurrentSubAccount,
  clearError,
  clearSubAccountState,
} = subAccountSlice.actions;

/**
 * Export reducer
 */
export default subAccountSlice.reducer;

/**
 * Export types
 */
export type SubAccountState = ISubAccountState;
