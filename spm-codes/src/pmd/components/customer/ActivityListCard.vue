<template>
  <div class="hl_tasks--item hl_activity--item" v-if="activity">
    <div class="option" v-html="icon" :class="optionClass"></div>
    <div class="hl_tasks--item-header">
      <h4
        style="cursor: pointer"
        class="d-flex align-items-center justify-content-center"
      >
        {{ activity.title }}
        <span
          class="ml-1"
          :class="optionClass"
          v-if="isShowSubmissionDetails"
          @click.prevent="$emit('showDetail', activity)"
        >
          <svg
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            stroke="currentColor"
            viewBox="0 0 24 24"
            height="20px"
          >
            <path
              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
            ></path>
          </svg>
        </span>

        <span
          class="ml-1"
          v-if="isPageVisit"
          @click.prevent="$emit('showPageVisit', activity)"
        >
          <svg
            fill="none"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            stroke="currentColor"
            viewBox="0 0 24 24"
            height="20px"
          >
            <path
              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"
            ></path>
          </svg>
        </span>
      </h4>
      <div class="task-actions">
        <p>
          <strong>{{ createdDate }}</strong>
        </p>
      </div>
    </div>
    <div class="hl_products--item-body">
      <div class="hl_products--item-text">
        <p>
          <a :href="activity.pageUrl" target="_blank">{{
            activity.pageTitle
          }}</a>
        </p>
        <ActivityListSource
          v-if="isFBLeadForm && isShowSource"
          :activity="activity"
        />
      </div>
      <div class="task-actions">
        <p>
          {{ createdTime }}
        </p>
      </div>
    </div>
    <div
      class="hl_products--item-footer"
      v-if="isShowSource"
      :class="{ 'activity-fb-lead-footer': isFBLeadForm }"
    >
      <ActivityListSource v-if="!isFBLeadForm" :activity="activity" />

      <span
        v-if="activity.campaign"
        class="badge-campaign"
        :class="badgeCampaignClass"
      >
        <strong>Campaign:</strong> {{ activity.campaign }}
        <i
          class="fas fa-question-circle"
          :id="`activity-tooltip-${activity.id}`"
        ></i>
      </span>
    </div>

    <b-tooltip :target="`activity-tooltip-${activity.id}`" placement="bottom">
      <template v-if="activity.campaign">
        <strong>Campaign:</strong> {{ activity.campaign }} <br />
      </template>

      <template v-if="activity.utm_medium">
        <strong>UTM Medium:</strong> {{ activity.utm_medium }} <br />
      </template>

      <template v-if="activity.utm_content">
        <strong>UMT Content:</strong> {{ activity.utm_content }}
      </template>
    </b-tooltip>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { getCountryDateFormat, SessionType } from '@/models/index'
import * as moment from 'moment-timezone'
const ActivityListSource = () =>
  import('@/pmd/components/customer/ActivityListSource.vue')

export default Vue.extend({
  props: {
    activity: Object,
  },
  components: {
    ActivityListSource,
  },
  data() {
    return {
      getCountryDateFormat: getCountryDateFormat,
    }
  },
  computed: {
    icon() {
      if (this.activity.type == SessionType.PAGE_VISIT) {
        return '<svg fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" class="w-5 h-4"><path d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path></svg>'
      } else if (
        [
          SessionType.FORM,
          SessionType.SURVEY,
          SessionType.CHAT_WIDGET_FORM,
        ].indexOf(this.activity.type) != -1
      ) {
        return '<svg fill="currentColor" viewBox="0 0 20 20" class="w-5 h-5"><path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path><path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path></svg>'
      } else if (this.activity.type == SessionType.APPOINTMENT) {
        return '<svg fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" class="w-5 h-5"><path d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>'
      } else if (this.activity.type == SessionType.TRIGGER_LINK) {
        return '<svg fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" class="w-5 h-5"><path d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path></svg>'
      } else if (this.activity.type == SessionType.CONTACT_CREATED) {
        return '<svg fill="currentColor" viewBox="0 0 20 20"><path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"></path></svg>'
      } else if (this.activity.type == SessionType.CALL) {
        return '<svg fill="currentColor" viewBox="0 0 20 20"><path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path></svg>'
      } else if (this.activity.type == SessionType.TWO_STEP_CONTACT || this.activity.type == SessionType.ONE_STEP_CONTACT) {
        return '<svg fill="currentColor" viewBox="0 0 20 20"><path d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clip-rule="evenodd" fill-rule="evenodd"></path></svg>'
      } else if (this.activity.type == SessionType.TWO_STEP_PAY || this.activity.type == SessionType.ONE_STEP_PAY) {
        return '<svg fill="currentColor" viewBox="0 0 20 20"><path d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd" fill-rule="evenodd"></path><path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path></svg>'
      } else {
        return '<svg fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" class="w-5 h-5"><path d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path></svg>'
      }
    },
    optionClass() {
      return [
        SessionType.PAGE_VISIT,
        SessionType.CONTACT_CREATED,
        SessionType.CALL,
      ].indexOf(this.activity.type) != -1
        ? 'bg-success'
        : 'null'
    },
    createdDate() {
      return moment(this.activity.createdAt).format(
        getCountryDateFormat('month date, year')
      )
    },
    createdTime() {
      return moment(this.activity.createdAt).format('hh:mm a')
    },
    isShowSubmissionDetails() {
      if (
        this.activity.marketingId &&
        [
          SessionType.FORM,
          SessionType.SURVEY,
          SessionType.FB_LEAD_FORM,
          SessionType.CHAT_WIDGET_FORM,
        ].includes(this.activity.type)
      ) {
        return true
      }
      if (
        this.activity.marketingId &&
        [SessionType.APPOINTMENT, SessionType.APPOINTMENT_V3].includes(
          this.activity.type
        ) &&
        this.activity.isBookingCustomForm
      ) {
        return true
      }

      return false
    },

    isPageVisit() {
      if ([SessionType.PAGE_VISIT].includes(this.activity.type)) {
        return true
      }

      return false
    },

    isShowSource() {
      if (
        (this.isPageVisit || this.activity.type == SessionType.FB_LEAD_FORM) &&
        this.activity.source &&
        [
          'Paid Search',
          'Paid Social',
          'Social media',
          'Organic Search',
        ].includes(this.activity.source)
      ) {
        return true
      }

      return false
    },

    isFBLeadForm() {
      return this.activity.type == SessionType.FB_LEAD_FORM
    },
    badgeClass() {
      if (this.isShowSource) {
        if (
          this.activity.adSource == 'Facebook' ||
          this.activity.source == 'Social media'
        ) {
          return 'badge-primary'
        } else if (
          this.activity.adSource == 'Google' ||
          this.activity.adSource == 'Adword'
        ) {
          return 'badge-success'
        }

        return 'badge-custom'
      }

      return ''
    },
    badgeCampaignClass() {
      if (this.isShowSource) {
        if (
          this.activity.adSource == 'Facebook' ||
          this.activity.source == 'Social media'
        ) {
          return 'badge-campaign-primary'
        } else if (
          this.activity.adSource == 'Google' ||
          this.activity.adSource == 'Adword'
        ) {
          return 'badge-campaign-success'
        }

        return 'badge-campaign-custom'
      }

      return ''
    },
  },
})
</script>

<style lang="scss">
.hl_activity {
  &--item {
    padding-left: 40px;
    .option {
      padding: 5px;
      border-radius: 50%;
      background: #188bf6;
      svg {
        width: 18px;
        color: white;
      }
    }
    .badge {
      line-height: inherit;
      font-size: 11px;
      padding: 2px 10px;
    }

    .badge-campaign {
      border: 1px solid #b3b3b3;
      padding: 3px 5px;
      margin-left: 10px;
      border-radius: 5px;
      display: inline-block;
    }
    .badge-campaign-primary {
      color: #188bf6;
      border-color: #188bf6;
    }

    .badge-campaign-success {
      color: #37ca37;
      border-color: #37ca37;
    }
    .badge-custom {
      background-color: rgb(189, 117, 127);
      color: white;
    }

    .badge-campaign-custom {
      color: rgb(189, 117, 127);
      border-color: rgb(189, 117, 127);
    }

    .activity-fb-lead-footer {
      margin-top: 5px;
      span {
        margin-left: 0px;
      }
    }
  }
}
</style>
