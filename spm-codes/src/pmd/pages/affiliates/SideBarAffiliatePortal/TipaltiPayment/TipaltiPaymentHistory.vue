<template>
    <!-- Load iframe component -->
    <TipaltiIframe pageName="PaymentsHistory"/>
</template>

<script>
import Vue from 'vue'
import TopBar from '../../../../components/TopBar.vue'
import SideBar from '../../../../components/agency/SideBar.vue'
import SideBarV2 from '../../../../components/sidebar/SideBar.vue'
import TipaltiIframe from './TipaltiIframe.vue'



export default {
  components: { TopBar, SideBar, SideBarV2, TipaltiIframe },
}

</script>

<style scoped lang="scss" >

  .loader-backdrop {
    background: #ffffffc7;
    width: 100%;
    height: 90vh;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;

    p {
      margin-top: 10px;
      padding: 10px;
      color: #009000;
      font-weight: 500;
    }

    .errorMsg{
      color:red;
      font-size: 20px;
      font-weight: 500;
    }

 }
</style>
