<template>
  <modal
    v-if="modalShow"
    :maxWidth="popupWidth"
    @close="closeModal"
    :showCloseIcon="true"
    :noBackdropClose="true"
  >
    <div class="gmb-container">
      <div class="fb-container">
        <div class="mt-2 fb-title">Hey {{ name }}, just a heads-up</div>
        <div class="fb-sub-title mt-2">
          Please turn on all the permissions for this business on Facebook.
          These permissions are required for our CRM to work seamlessly
        </div>
        <FbHeadsUpIcon
          class="fb-headsup-logo"
          alt="feature image"
        />
        <button
          class="btn btn-primary active yes-button mt-4"
          @click="connetFacebook"
        >
          Got it
        </button>
      </div>
    </div>
  </modal>
</template>
<script>
import Vue from 'vue'
import Modal from '@/pmd/components/common/Modal.vue'
import FbHeadsUpIcon from '@/assets/pmd/img/icon-fb-heads-up.svg'

export default Vue.extend({
  props: ['name', 'modalShow'],
  components: {
    Modal,
    FbHeadsUpIcon,
  },
  data() {
    return {
      popupWidth: 600,
    }
  },
  methods: {
    connetFacebook() {
      this.$root.$emit('fb-connect')
      this.closeModal()
    },
    closeModal() {
      this.headsup = false
      this.isCredentials = true
      this.$root.$emit('close-modal')
    },
  },
})
</script>
<style scoped>
.gmb-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 27px 10px;
}
.fb-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 450px;
}
.fb-headsup-logo {
  width: 200px;
  height: 200px;
  margin-bottom: 10px;
  margin-top: 20px;
}
.fb-title {
  font-weight: bold;
  font-size: 20px;
  color: #333333;
}
.fb-sub-title {
  font-weight: normal;
  font-size: 12px;
  color: #718096;
}
</style>
