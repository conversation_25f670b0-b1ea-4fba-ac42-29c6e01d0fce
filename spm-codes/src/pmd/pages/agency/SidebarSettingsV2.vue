<template>
    <div class="card">
        <div class="card-header">
            <h3>Navigation Sidebar Access for all the location admins and users</h3>
        </div>
        <div class="card-body">
            <div class="form-group">
                <div class="flex flex-row items-center">
                    <UISelect
                        :options="notificationType"
                        :value="selectedValue"
                        name="notification"
                        :class="{
                            input: true,
                        }"
                        @change="val => {selectedValue = val; changeDescription(val);}"
                        v-validate="'required'"
                        :disabled="!isAdmin"
                    />
                    <span
                        v-if="isAdmin"
                        style="margin-left: 5px"
                        class="input-group-addon hl_help-article"
                        v-b-tooltip.left.hover.html="getTooltip"
                    >
                        <i class="fas fa-question-circle"></i>
                    </span>
                </div>
                <span v-show="errors.has('notification')" class="error">{{
                errors.first('notification')
                }}</span>
                <span class="text-gray-400 block text-xs" v-html="selectedDesc"></span>
            </div>
            <div class="form-group">
                <UIButton :disable="!selectedValue" type="button" :loading="saving" @click="save">
                    Save Preference 
                </UIButton>
            </div>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, computed, onMounted } from '@vue/composition-api'
import { Company, User } from '@/models'
import { trackPendoEvent } from '@/util/helper'

const notificationType = [
    {
        value: 'optional',
        label: 'Optional New Sidebar Upgrade',
        description: `Optional New Sidebar Upgrade will give the location users a choice 
        between new navigation sidebar or switch to the old sidebar. The option to switch 
        from old sidebar to new or vice versa is given below profile avatar.`
    },
    {
        value: 'mandatory_v2',
        label: 'Mandatory New Sidebar Upgrade',
        description: 'Mandatory New Sidebar Upgrade will give the location users access to new navigation sidebar.'
    },
    {
        value: 'mandatory_v1',
        label: 'Mandatory Old Sidebar Downgrade',
        description: `Mandatory Old Sidebar Downgrade will revert back the location users to old navigation experience. 
        In case if you are facing troubles or have suggestion for new sidebar, Please fill this form 
        <a href='https://link.gohighlevel.com/widget/form/0cA8j6Cfs7gvoOBhpeCW' target='_blank'>here</a>.
        We are happy to hear you.`
    }
]

export default defineComponent({
    setup(_, ctx) {
        const store = ctx.root.$store 
        const saving = ref(false)
        const selectedDesc = ref('Optional New Sidebar Upgrade will give the location users a choice between new navigation sidebar or switch to the old sidebar. The option to switch from old sidebar to new or vice versa is given below profile avatar.')
        const selectedValue = ref('optional')

        const company = computed(() => store.getters['company/get'])
        const user = computed((): User | undefined => {
            const user = store.state.user.user
            return user ? new User(user) : undefined
        })
        const isAdmin = computed(() => user.value && user.value.role === 'admin')
        const selectedOption =  computed(() => store.getters['sidebarv2/getNotificationType'])
        if (selectedOption.value && selectedOption.value !== 'disable') selectedValue.value = selectedOption.value

        const getTooltip = () => {
            let html = `<ul class='flex flex-col justify-start' >`

            for (const type of notificationType) {
                html += `<li>
                        <b>${type.label}:</b> <br/>
                        ${type.description}<br/><br/>
                    </li>`
            }
            html += '</ul>'

            return html
        }
        

        const save = async () => {
            saving.value = true
            if (selectedValue.value) {
                await Company.collectionRef().doc(company.value.id).update({
                    sidebar_v2_notification_type: selectedValue.value
                })
                trackPendoEvent('sidebar_v2_choice', { option: selectedValue.value })
            }
            saving.value = false
        }

        const changeDescription = (val: string) => {
            if (val) {
                const types = notificationType.filter((t: any) => t.value == val)
                if (types.length) selectedDesc.value = types[0].description
                
            } else { selectedDesc.value = '' }
        }

        onMounted(() => {
            changeDescription(company.value.sidebar_v2_notification_type)
        })

        return {
            notificationType,
            saving,
            selectedValue,
            isAdmin,
            save,
            changeDescription,
            selectedDesc,
            getTooltip
        }
    },
})
</script>
