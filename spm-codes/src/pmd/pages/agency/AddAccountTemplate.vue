<template>
    <div>

        <SideBarV2 v-if="getSideBarVersion === 'v2'"/>
        <SideBar v-else />
        <TopBar />

        <section class="hl_wrapper">

            <section class="hl_wrapper--inner customers" id="customers">
                <div class="container-fluid">
                    <div class="hl_controls" style="height:50px">
						<div class="hl_controls--left">
							<h1>Available Snapshots</h1>
						</div>
						<div class="hl_controls--right">
                            <button class="btn btn-success" v-if="selectedTemplate._id" @click="onSelectTemplate(selectedTemplate)">
                                <i class="icon icon-plus"></i> Create New Account
                            </button>
						</div>
					</div>
                    <div class="card">
                        <div class="card-body">
                            <template-list source="sidebar" @open="handleOpenTemplate" @select="onSelectTemplate"/>
                        </div>
                    </div>
                </div>
            </section>
        </section>
    </div>
</template>

<script lang="ts">
import Vue from 'vue';
import TopBar from '../../components/TopBar.vue';
import SideBar from '../../components/agency/SideBar.vue';
import SideBarV2 from '../../components/sidebar/SideBar.vue'
import { SnapshotTemplate, User } from '@/models';
import TemplateList from "@/pmd/components/agency/snapshot_template/TemplateList.vue";
// const TemplateList = () => import('@/pmd/components/agency/snapshot_template/TemplateList.vue');

export default Vue.extend({
    components: { TopBar, SideBar, TemplateList, SideBarV2 },
    data() {
        return {
            selectedTemplate: {} as SnapshotTemplate,
        };
    },
    computed: {
        user() {
            const user = this.$store.state.user.user
            return user ? new User(user) : undefined
        },
        getSideBarVersion(): string {
            return this.$store.getters['sidebarv2/getVersion'] 
        },
    },
    methods: {
		handleOpenTemplate(snapshot){
			this.selectedTemplate = snapshot;
		},
		onSelectTemplate(snapshot){
            this.$router.push({ name: 'account_add_map', query: { snapshot: snapshot._id, s_type: snapshot.type?snapshot.type:'vertical' } });
		}
	},
});
</script>
