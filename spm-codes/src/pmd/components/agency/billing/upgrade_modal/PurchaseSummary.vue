<template>
  <div class="purchase-summary">
    <div class="purchase-summary__left">
      <div class="purchase-summary__heading">Purchase Summary</div>
      <div class="purchase-summary__plan-name">{{plan.label}} Subscription Plan</div>
      <div class="purchase-summary__duration" v-if="type !== 'annual-upgrade'">
        <div class="purchase-summary__duration-btn"
          :class="{active: selectedDuration === 'monthly'}"
          @click="selectedDuration = 'monthly'"
          >Monthly</div>
        <div class="purchase-summary__duration-btn"
          :class="{active: selectedDuration === 'annual'}"
          @click="selectedDuration = 'annual'"
          >Annually</div>
      </div>
      <br v-else />
      <div class="purchase-summary__plan-info">
        <div class="pricing-table__plan-amount --annual-summary" v-if="selectedDuration === 'annual' && plan.level !== 4">
          <span v-if="plan.level !== 4">$</span>
          {{plan.amount_annual}}
          <span class="pricing-table__plan-amount-duration">/month</span>
        </div>
        <div class="pricing-table__plan-amount" :class="{'--with-discount': selectedDuration === 'annual' && plan.level !== 4}">
          <span v-if="plan.level !== 4">$</span>
          {{plan.amount}}
          <span class="pricing-table__plan-amount-duration">/month</span>
        </div>

        <!-- <div class="purchase-summary__plan-amount">${{selectedDuration === 'annual' ? plan.amount_annual : plan.amount}}<span class="purchase-summary__plan-amount-duration">/month</span></div> -->
        <div class="purchase-summary__plan-tagline" v-if="selectedDuration === 'annual'">billed as yearly charge of ${{plan.amount}}0 / year</div>
        <div class="purchase-summary__plan-tagline" v-else>billed as monthly charge of ${{plan.amount}} / month</div>
      </div>
        <ul class="purchase-summary__plan-highlights">
          <li v-for="highlight in plan.highlights" :key="highlight">
            <i class="fa fa-check text-success" />  {{highlight}}
          </li>
        </ul>
        <div class="purchase-summary__bottom">
          <div v-if="scaPending">
            Please <span class="purchase-summary__link" @click="$emit('failedSCA')">authenticate</span> your card details again
            <span class="hl_tooltip pointer" v-b-tooltip.hover title="We have moved to a more secure billing system. For added security you need to authenticate your card again." ><i class="fa fa-info" style="font-size: 10px"></i></span>
          </div>
          <div class="purchase-summary__pay-btn btn btn-blue" @click="$emit('failedSCA')" v-if="scaPending">
            <moon-loader v-if="processing" color="#ffffff" size="20px"/>
            <span v-else>Update Payment Method</span>
          </div>
          <div class="purchase-summary__pay-btn btn btn-success" @click="upgradeNow" :class="{'--disabled': scaPending}" v-else>
            <moon-loader v-if="processing" color="#ffffff" size="20px"/>
            <span v-else>Confirm &amp; Pay</span>
          </div>
        </div>
    </div>
    <div class="purchase-summary__right">
        <ul class="purchase-summary__plan-features-list">
          <li class="list-heading"><b>What you'll get:</b></li>
        </ul>
        <div class="purchase-summary__plan-features">
          <ul class="purchase-summary__plan-features-list">
            <li v-if="upgradeLevel !== 1">
              <span class="plan-feature__name">Everything in <b>{{$options.agencyPlans[upgradeLevel -2].label}}+</b></span>
            </li>
            <li v-else>
              <span class="plan-feature__name">Workflow Builder</span>
              <span class="plan-feature__upcomming-tag">Coming Soon</span>
            </li>
            <li v-for="feature in plan.features.slice(0,6)" :key="feature.label">
              <span class="plan-feature__name">{{feature.label}}</span>
              <span class="plan-feature__upcomming-tag" v-if="feature.upcoming">Coming Soon</span>
              <span class="plan-feature__new-tag" v-if="feature.new">New</span>
            </li>
          </ul>
          <ul class="purchase-summary__plan-features-list --expanded" v-show="featuresExpanded">
            <li v-for="feature in plan.features.slice(6)" :key="feature.label">
              <span class="plan-feature__name">{{feature.label}}</span>
              <span class="plan-feature__upcomming-tag" v-if="feature.upcoming">Coming Soon</span>
              <span class="plan-feature__new-tag" v-if="feature.new">New</span>
            </li>
          </ul>
          <div class="purchase-summary__plan-features__view-more" v-if="plan.features.length > 6" >
            <span v-if="!featuresExpanded" @click="featuresExpanded = true">
              View More (+{{plan.features.length - 6}})
              <i class="icon icon-arrow-down-1"></i>
            </span>
            <span v-else @click="featuresExpanded = false">
              View Less
              <i class="icon icon-arrow-up-1"></i>
            </span>
          </div>
        </div>
    </div>
  </div>
</template>

<script>
import { agencyPlans } from "./agencyPlans";
import { trackGaEvent } from "@/util/helper";

export default {
  props:['upgradeLevel','planDuration','company','activePlan','type'],
  agencyPlans: agencyPlans,
  data(){
    return{
      selectedDuration: 'annual',
      featuresExpanded: false,
      processing: false,
      scaPending: false
    }
  },
  created() {
    this.verifySCA();
  },
  mounted() {
    if(this.planDuration){
      this.selectedDuration = this.planDuration;
    }
  },
  computed:{
    plan(){
      return this.$options.agencyPlans[this.upgradeLevel - 1]
    }
  },
  methods:{
    async verifySCA(){
      this.processing = true;
      try{
        let verificationData = await this.$http.post('/stripe/verify_sca',{
          company_id: this.company.id
        });
        if(!verificationData.data.default_payment_method){
          this.scaPending = true;
        }

      } catch (err) {
        //
      } finally{
        this.processing = false;
      }
    },
    async upgradeNow(){
      if (!this.processing){
            this.processing = true;
            let new_plan_label = `agency_${this.selectedDuration === 'monthly'?'monthly':'annual'}_${this.plan.amount}`
            trackGaEvent('BillingUpgradeInitiate', this.company.id, 'Upgrade Modal: Upgrading', 1);
            try {
                let upgradeResponse = await this.$http.post('/stripe/agency_upgrade',{
                    company_id: this.company.id,
                    plan: this.activePlan,
                    current_plan_label: this.activePlan ? this.activePlan.name : '',
                    current_plan_status: this.activePlan? this.activePlan.status : '',
                    type: this.type || '',
                    new_plan_label,
                    user_id: this.$store.state.user.user.id
                })
                trackGaEvent('BillingUpgradeInitiate', this.company.id, 'Upgrade Modal: Completed', 1);
                // if(upgradeResponse.data && response.data.plan){
                    // this.upgraded = true;
                    this.processing = false;
                    // this.$emit('success',response.data.plan);
                    // this.submitted = true;
                // }
                this.$emit('success', upgradeResponse.data.plan);
            } catch (err) {
                console.log(err);
                this.processing = false;
                if(err?.response?.data?.msg){
                  console.log(err?.response?.data?.msg);
                  this.$emit('failed',err?.response?.data?.msg);
                } else {
                  this.$emit('failed',err.message || '');
                }
                // this.showError('Something went wrong !! Please try again later.', 5000);
            }
      }
    },
  },
}
</script>

<style>
.purchase-summary{
  display: flex;
  height: 100%;
  text-align: left;
}
.purchase-summary__left{
  padding: 24px;
  border-right: 1px solid #e0e0e0;
  text-align: left;
  flex-grow: 1;
  position: relative;
}
.purchase-summary__right{
  min-width: 244px;
  max-width: 244px;
  padding: 20px;
}
.purchase-summary__heading{
  font-size: 14px;
  line-height: 16px;
  margin-bottom: 6px;

  font-weight: 500;
  color: #4F4F4F;
}
.purchase-summary__plan-name{
  font-size: 18px;
  line-height: 20px;
  color: #373737;
  font-weight: 500;
}
/* purchase-summary Duration ===================> */
.purchase-summary__duration{
  display: flex;
  align-items: center;
  /* justify-content: center; */
  border: 3px solid #2F80ED;
  border-radius: 30px;
  background-color: #2F80ED;
  /* margin: auto; */
  margin-top: 20px;
  width: fit-content;
}
.purchase-summary__duration-btn{
  font-size: 12px;
  line-height: 14px;
  font-weight: 500;
  width: 100px;
  padding: 8px 0px;
  border-radius: 30px;
  background-color: #2F80ED;
  color: #ffffff;
  text-align: center;
  /* transition: all 0.3s; */
  cursor: pointer;
  user-select: none;
}
.purchase-summary__duration-btn.active{
  background-color: #ffffff;
  color: #2F80ED;
}
/* ===================> purchase-summary Duration */

.purchase-summary__plan-features{
  height: 380px;
  overflow-y: auto;
  position: relative;
}
.purchase-summary__plan-features-list{
  list-style: none;
  padding-inline-start: 0px;
  font-size: 14px;
  line-height: 16px;
  color: 828282;
}
.purchase-summary__plan-features-list li.list-heading{
  color: #4F4F4F;
  font-weight: 500;
}
.purchase-summary__plan-features-list li {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.purchase-summary__plan-features__view-more{
  color: #2F80ED;
  font-size: 14px;
  line-height: 16px;
  font-weight: 500;
  cursor: pointer;
}
.purchase-summary__plan-features__view-more i{
  font-size: 10px;
  margin-left: 8px;
}


.purchase-summary__plan-info{
  height: 44px;
  position: relative;
  margin-top: 16px;
}
.purchase-summary__plan-amount{
  color: #373737;
  font-size: 18px;
  line-height: 20px;
  font-weight: 500;
  margin-top: 20px;
  margin-bottom: 4px;

}
.purchase-summary__plan-amount-duration{
  font-size: 14px;
  line-height: 16px;
  font-weight: 400;
  color: #828282;
}
.purchase-summary__plan-tagline{
  color: #2F80ED;
  font-size: 14px;
  line-height: 16px;
  font-weight: 400;
  /* max-width: 150px; */
}

.purchase-summary__plan-highlights{
  margin-top: 20px;
  list-style: none;
  padding-inline-start: 0px;
  font-size: 14px;
  line-height: 16px;
  color: #4F4F4F;
}
.purchase-summary__plan-highlights li{
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}
.purchase-summary__plan-highlights li i{
  margin-right: 8px;
}
.purchase-summary__bottom{
  position: absolute;
  bottom: 24px;
  left: 24px;
  width: calc(100% - 48px);
}
.purchase-summary__link{
  color: #2F80ED;
  font-weight: 500;
  cursor: pointer;
}
.purchase-summary__pay-btn{
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  font-weight: 500;
  padding: 12px;
  width: 100%;
  margin-top: 8px;

  background-color: #27AE60;
  color: #ffffff;
  transition: all 0.3s ease-in-out;
}
.purchase-summary__pay-btn:hover{
  background-color: #249151;
}
.purchase-summary__pay-btn.--disabled{
  opacity: 0.4;
  pointer-events: none;
  cursor: pointer;
}
.pricing-table__plan-amount{
  color: #373737;
}
.pricing-table__plan-amount.--annual-summary{
  /* font-size: 20px; */
  position: absolute;
  top: -12px;
  font-size: 24px;
  color: #373737;
}
.pricing-table__plan-amount.--with-discount{
  font-size: 16px;
  color: #d75046;
  margin-top: 6px;
  padding-top: 10px;
  margin-bottom: -6px;
  text-decoration: line-through;
}
</style>
