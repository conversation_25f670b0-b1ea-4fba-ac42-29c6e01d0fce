<template>
  <div class="hl_settings--body">
    <div class="container-fluid">
      <div class="hl_settings--controls" v-if="$route.meta.router_version === 'v2'">
        <div class="hl_settings--controls-left">
          <h2>Media</h2>
        </div>
        <div class="hl_settings--controls-right"></div>
      </div>
      <div
        class="bg-white py-3 rounded shadow flex justify-center items-center flex-col w-full"
        style="height: 400px"
      >
        <img src="@/assets/pmd/img/gallary-items.png" class="w-36 h-36" />
        <UIButton @click="openMediaLibrary" class="my-4">
          Open media library
        </UIButton>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from '@vue/composition-api'
export default defineComponent({
  setup(props, ctx) {
    const router = ctx.root.$router
    function openMediaLibrary() {
      router.push({ name: 'medias' })
    }

    return {
      openMediaLibrary,
    }
  },
})
</script>

<style lang="scss" scoped></style>
