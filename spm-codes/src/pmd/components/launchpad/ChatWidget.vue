<template>
  <div
    id="chat-connect"
    class="launchpad-item-sub-container launchpad-bottom-border bg-white my-1"
  >
    <div class="items-tiles mx-3 my-2">
       <img
        class="integration-icon mx-4"
        :class="{ hl_disable_action: isChatWidgetConnected }"
        src="https://static.msgsndr.com/img/icon-chat-widget-integration.ee9ce5e8.svg"
      />
      <div class="item-info">
        <div
          class="font-normal text-sm text-gray-500"
          v-if="!isChatWidgetConnected && reconnect"
        >
          Your webchat widget does not seems to work. Please reconfigure to
          generate leads from your website.
        </div>
        <div
          class="font-normal text-sm text-gray-500"
          v-else-if="isChatWidgetConnected"
        >
          Congratulations! You have successfully added webchat in your website.
          Engage with your leads in Conversations page.
        </div>
        <div
          class="font-normal text-sm text-gray-500"
          v-else-if="!isChatWidgetConnected"
        >
          Generate leads from your website by connecting webchat widget.
        </div>
      </div>
      <div class="flex flex-col justify-center item-end content-end w-40">
        <div
          class="flex flex-row justify-end item-end content-end w-full pr-4 pl-4"
        >
          <button
            id="launchpad-chatwidget-connect"
            v-if="!chatWidgetChecking"
            class="green-btn"
            :class="{
              'green-filled-button green-btn connetGoogle': !isChatWidgetConnected,
              'btn btn-circle success-green-btn': isChatWidgetConnected,
            }"
            @click="setupChatWidget"
          >
            <span v-if="!isChatWidgetConnected && reconnect">Reconfigure</span>
            <span v-else-if="!isChatWidgetConnected">Connect</span>
            <i v-show="isChatWidgetConnected" class="fa fa-check"></i>
          </button>
          <moon-loader
            v-if="chatWidgetChecking"
            :loading="chatWidgetChecking"
            color="#37ca37"
            size="30px"
          />
        </div>
      </div>
    </div>
    <ChatWebsiteUrlModal
      :currentLocationId="currentLocationId"
      :modalShow="showModal"
      :siteUrl="website"
    />
    <img
      v-if="reconnect && !isChatWidgetConnected"
      src="https://static.msgsndr.com/img/alert-triangle.0e06f0da.svg"
      width="30px"
      height="30px"
      style="position: absolute; right: -44px"
    />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { Location } from '@/models'
import ChatWebsiteUrlModal from './ChatWebsiteUrlModal.vue'
import { trackGaEvent } from '@/util/helper'
import ChatWidgetIcon from '@/assets/pmd/img/launchpad/icon-chat-widget-integration.svg'

export default Vue.extend({
  props: [
    'currentLocationId',
    'userid',
    'name',
    'website',
    'chatWidgetChecking',
    'isChatWidgetConnected',
    'reconnect',
  ],
  components: {
    ChatWidgetIcon,
    ChatWebsiteUrlModal,
  },
  data() {
    return {
      showModal: false,
    }
  },
  mounted() {
    this.$root.$on('close-modal', () => {
      this.showModal = false
    })
  },
  methods: {
    setupChatWidget() {
      this.showModal = true
      trackGaEvent(
        'Launchpad',
        'Initiate WebChat widget integration',
        `Webchat widget has been initiated for location: ${this.currentLocationId}`
      )
    },
  },
})
</script>
