<template>
  <div class="active-users-group">
    <div
      v-for="(user, index) in users"
      :key="index"
    >
      <UserAvatar :user="user" />
    </div>
    <div v-if="users.length > 4">
      <div
        class="rounded-full border h-9 w-9 flex items-center justify-center justify-items-center bg-gray-100 cursor-pointer"
      >
        +{{ users.length - 4 }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import UserAvatar from './UserAvatar.vue'

export default Vue.extend({
  components:{
    UserAvatar
  },
  props: {
    users: {
      type: Array,
      default: [],
    },
  },
})
</script>

<style scoped>

</style>
