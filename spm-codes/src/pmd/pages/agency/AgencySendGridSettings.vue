<template>
  <div class="hl_settings--body">
    <div class="container-fluid">
      <div class="row">
        <div class="hl_settings--main col-lg-12">
          <div class="card">
            <div class="card-header">
              <h3>SendGrid Settings</h3>
            </div>
            <div class="card-body">
              <div class="personal-logo">
                <div class="col-sm-3">
                  <img
                    src="/pmd/img/logo_medallions/sendgrid-medallion.png"
                    style="height:125px"
                    alt="Avatar Name"
                  />
                </div>

                <div class="col-sm-9">
                  <form
                    @submit.prevent="validateBeforeSubmit"
                    data-vv-scope="form-1"
                  >
                    <div class="row">
                      <div class="col-sm-9">
                        <div class="form-group">
                          <label>API Key</label>
                          <input
                            type="text"
                            class="form-control"
                            placeholder="API Key"
                            v-model="sendgridAccount.apiKey"
                            v-validate="'required'"
                            name="apiKey"
                          />
                          <span
                            v-show="errors.has('form-1.apiKey')"
                            class="--red"
                            >API Key Required</span
                          >
                        </div>
                      </div>
                    </div>

                    <div class="form-group">
                      <button
                        type="button"
                        class="btn btn-success"
                        @click="saveAccount()"
                      >
                        Save
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="container-fluid" v-if="locations.length > 0">
      <div class="hl_settings--controls">
        <div class="hl_settings--controls-left">
          <h2>
            SendGrid Settings for Locations
            <span>{{ locations.length }} locations</span>
          </h2>
        </div>
        <div class="hl_settings--controls-right"></div>
      </div>
      <div class="card hl_settings--team-management">
        <div class="card-body --no-padding" style="max-width:100%;">
          <div class="table-wrap">
            <table class="table">
              <thead>
                <tr>
                  <th style="width: 20%" data-sort="string">Name</th>
                  <th style="width: 35%" data-sort="string">API Key</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                <SendGridListItem
                  v-for="location in locations"
                  :key="location.id"
                  :location="location"
                />
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="container-fluid">
			<div class="row">
				<div class="card hl_settings--team-management col-lg-12">
					<div class="card-header">
						<div style="display: flex;align-items:center;">
							<img src="/pmd/img/SendGrid-Logomark.png" style="width: 30px;">
							<span style="margin-left:10px;">
								<h2>SendGrid</h2>
								<div style="position: absolute;top: 30%;left: 26%;" v-show="saving">
									<moon-loader :loading="saving" color="#188bf6" size="30px"/>
								</div>
							</span>
						</div>
					</div>

					<div class="card-body --no-padding" style="max-width:100%;">
						<div class="table-wrap">
							<table class="table" v-if="sendgridAccount">
								<thead>
									<tr>
										<th style="width: 20%" data-sort="string">Name</th>
										<th style="width: 35%" data-sort="string">SubDomain</th>
										<th style="width: 35%" data-sort="string">Domain</th>
										<th></th>
									</tr>
								</thead>
								<tbody>

									<tr v-for="location in locations" v-bind:index="location.id">
										<td>{{location.name}}</td>
										<td>
											<input
												type="text"
												class="form-control"
												placeholder="Subdomain (optional)"
												:value="getSubDomain(location.id)"
												@keyup="setSubDomainsValue($event, location)"
											>
										</td>
										<td>
											<select
												class="form-control selectpicker border"
												:value="getDomain(location.id)"
												@change="setDomainsValue($event, location)"
											>
												<option value>Select domain</option>
												<option v-for="host in hosts" :key="host.id" :value="host.domain">{{host.domain}}</option>
											</select>
										</td>
										<td>
											<button
												:class="{invisible: saving}"
												type="button"
												class="btn btn-success"
												@click.prevent="setHost($event, location.id)"
											>Save</button>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>-->
    <div class="container-fluid">
      <div class="row">
        <div class="card hl_dashboard--emails-sent col-lg-12">
          <div class="card-header">
            <div style="display: flex;align-items:center;">
              <img src="/pmd/img/SendGrid-Logomark.png" style="width: 30px;" />
              <span style="margin-left:10px;">
                <h2>
                  SendGrid
                  <span>Last 12 Months</span>
                </h2>
              </span>
            </div>

            <!-- <select class="selectpicker more-select">
							<option>Today</option>
							<option>This Week</option>
							<option>Last Week</option>
							<option>This Month</option>
							<option>Last 6 Months</option>
							<option>This Year</option>
						</select>-->
          </div>
          <div class="card-body">
            <ul class="dashboard_emails-sent-chart-stats list-inline">
              <li class="list-inline-item">
                <h4>{{ totalSends | formatNumberNoDecimal }}</h4>
                <p>Total Sent</p>
              </li>
              <li class="list-inline-item">
                <h4>{{ totalOpens | formatNumberNoDecimal }}</h4>
                <p>Opens</p>
              </li>
              <li class="list-inline-item">
                <h4>{{ totalClicks | formatNumberNoDecimal }}</h4>
                <p>Clicks</p>
              </li>
              <li class="list-inline-item">
                <h4>{{ totalBounces | formatNumberNoDecimal }}</h4>
                <p>Bounces</p>
              </li>
              <!-- <li class="list-inline-item">
                                <h4>{{totalOrganicViews| formatNumberNoDecimal}}</h4>
                                <p>Organic</p>
							</li>-->
            </ul>
            <highcharts
              :options="googleanalyticschartoptions"
              :callback="analyticsChartLoaded"
            ></highcharts>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

import firebase from 'firebase/app'
import { Chart } from 'highcharts-vue'
import moment from 'moment-timezone'
import libphonenumber from 'google-libphonenumber'
import {
  Company,
  Location,
  User,
  Notification,
  NotificationType,
  SendGridAccount
} from '@/models'

const Avatar = () => import('../../components/Avatar.vue')
const EditTeamMemberModal = () =>
  import('../../components/EditTeamMemberModal.vue')
const SendGridListItem = () =>
  import('@/pmd/components/agency/SendGridListItem.vue')

var phoneUtil = libphonenumber.PhoneNumberUtil.getInstance()
var PNF = libphonenumber.PhoneNumberFormat
declare var $: any
let unsubscribeLocations: () => void
let cancelUsersSubscription: () => void

export default Vue.extend({
  components: {
    Avatar,
    EditTeamMemberModal,
    highcharts: Chart,
    SendGridListItem
  },
  data() {
    return {
      company: {} as Company,
      currentLocationId: '' as string,
      users: [] as User[],
      userInfo: {} as User,
      showModal: false as boolean,
      selectedUserId: undefined as string | undefined,
      locations: [] as Location[],
      hosts: [],
      subdomains: [],
      domains: [],
      sendgridAccount: {} as SendGridAccount,
      subAccounts: undefined as undefined | {},
      googleanalyticschartoptions: {
        chart: {
          type: 'column',
          style: {
            fontFamily: 'Roboto'
          }
        },
        credits: {
          enabled: !1
        },
        plotOptions: {
          column: {
            groupPadding: 0.35,
            borderWidth: 0
          },
          series: {
            pointWidth: 6,
            borderRadius: 5
          }
        },
        legend: {
          enabled: false
        },
        title: {
          text: ''
        },
        xAxis: {
          lineColor: '#dfe3e4',
          tickColor: '#fff',
          categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Jul', 'Sun'],
          labels: {
            style: {
              color: '#607179',
              fontSize: '14px'
            }
          }
        },
        yAxis: {
          gridLineColor: '#dfe3e4',
          title: {
            text: ''
          },
          labels: {
            style: {
              color: '#607179',
              fontSize: '14px'
            }
          }
        },
        series: []
      },
      sendgridAnalyticsChart: undefined,
      sendgridAnalyticsData: undefined,
      saving: false as boolean
    }
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.currentLocationId = id
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  async created() {
    var _self = this
    const authUser = await this.$store.dispatch('auth/get')
    this.company = await Company.getById(authUser.companyId)
    cancelUsersSubscription = (await User.fetchAllUsers()).onSnapshot(
      (snapshot: firebase.firestore.QuerySnapshot) => {
        this.users = snapshot.docs.map(
          (d: firebase.firestore.QueryDocumentSnapshot) =>
            new User({ ...d.data(), id: d.id })
        )
      }
    )

    unsubscribeLocations = (
      await Location.fetchAllLocationsRealtimeFirestore()
    ).onSnapshot(snapshot => {
      this.locations = snapshot.docs.map(d => new Location(d))
    })

    this.sendgridAccount = await SendGridAccount.getByCompanyId(
      this.company.id,
      this.currentLocationId
    )
    if (this.sendgridAccount) {
      this.populateChart()
    } else {
      this.sendgridAccount = new SendGridAccount()
      this.sendgridAccount.companyId = this.company.id
    }

    let hostList = await this.$http.get(
      '/sendgrid/get_domains?company_id=' + this.company.id
    )
    this.hosts = hostList.data
  },
  beforeDestroy() {
    if (unsubscribeLocations) unsubscribeLocations()
    if (cancelUsersSubscription) cancelUsersSubscription()
  },
  async mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  computed: {
    totalSends() {
      let total = 0
      if (this.sendgridAnalyticsData && this.sendgridAnalyticsData.length > 0) {
        this.sendgridAnalyticsData.forEach(element => {
          total += Number(element.stats[0].metrics.requests)
        })
      }
      return total
    },
    totalOpens(): number {
      let total = 0
      if (this.sendgridAnalyticsData && this.sendgridAnalyticsData.length > 0) {
        this.sendgridAnalyticsData.forEach(element => {
          total += Number(element.stats[0].metrics.opens)
        })
      }
      return total
    },
    totalClicks(): number {
      let total = 0
      if (this.sendgridAnalyticsData && this.sendgridAnalyticsData.length > 0) {
        this.sendgridAnalyticsData.forEach(element => {
          total += Number(element.stats[0].metrics.clicks)
        })
      }
      return total
    },
    totalBounces(): number {
      let total = 0
      if (this.sendgridAnalyticsData && this.sendgridAnalyticsData.length > 0) {
        this.sendgridAnalyticsData.forEach(element => {
          total += Number(element.stats[0].metrics.bounces)
        })
      }
      return total
    }
  },
  methods: {
    async saveAccount() {
      await this.sendgridAccount.save()
      try {
        let response = await this.$http.get(
          '/sendgrid/set_webhook?location_id=' + this.location.id
        )
      } catch (err) {
        console.error('Error creating twilio account:', err)
      }
    },
    analyticsChartLoaded(chart: any) {
      this.sendgridAnalyticsChart = chart
      this.populateChart()
    },
    async populateChart() {
      let _self = this

      if (this.sendgridAccount.apiKey && this.sendgridAnalyticsChart) {
        let analytics = await this.$http.get('/sendgrid/get_analytics', {
          params: {
            company_id: _self.company.id
          }
        })

        const startMonth = moment().subtract(11, 'months')
        const endMonth = moment()

        let series = [
          {
            name: 'Sends',
            type: 'spline',
            data: [],
            color: '#ffbc00'
          },
          {
            name: 'Opens',
            data: [],
            color: '#37ca37'
          },
          {
            name: 'Clicks',
            data: [],
            color: '#188bf6'
          },
          {
            name: 'Bounces',
            data: [],
            color: '#e93d3d'
          }
        ]

        let goal_series = series[0]
        let open_series = series[1]
        let click_series = series[2]
        let bounce_series = series[3]

        let categories = []

        if (analytics && analytics.data && analytics.data.length > 0) {
          this.sendgridAnalyticsData = analytics.data

          while (
            endMonth > startMonth ||
            startMonth.format('M') === endMonth.format('M')
          ) {
            categories.push(startMonth.format('MMM'))

            let monthCount = this.findMonthCount(
              startMonth.format('MM'),
              analytics
            )

            let send_count = monthCount.stats[0].metrics.requests
            let open_count = monthCount.stats[0].metrics.opens
            let click_count = monthCount.stats[0].metrics.clicks
            let bounce_count = monthCount.stats[0].metrics.bounces

            open_series.data.push(open_count)
            click_series.data.push(click_count)
            bounce_series.data.push(bounce_count)

            goal_series.data.push(send_count)

            startMonth.add(1, 'month')
          }

          this.sendgridAnalyticsChart.xAxis[0].setCategories(categories)

          this.sendgridAnalyticsChart.addSeries(goal_series, false)
          this.sendgridAnalyticsChart.addSeries(open_series, false)
          this.sendgridAnalyticsChart.addSeries(click_series, false)
          this.sendgridAnalyticsChart.addSeries(bounce_series, false)

          this.sendgridAnalyticsChart.redraw()
        }
      }
    },
    findMonthCount(month: string, analytics: [{}]): {} {
      let obj = undefined
      if (analytics) {
        analytics.data.forEach((element: {}) => {
          if (moment(element.date).format('MM') == month) {
            obj = element
          }
        })
      }
      return obj
    },
    async setHost(event: any, locationId: string) {
      let hostname = ''

      hostname = this.sendgridAccount.domains[locationId]

      if (this.sendgridAccount.subdomains[locationId]) {
        hostname = this.sendgridAccount.subdomains[locationId] + '.' + hostname
      }

      if (hostname == '') {
        console.error('Hostname blank, something wrong')
        return false
      }

      this.saving = true

      console.log('Saving host name:', hostname)

      await this.sendgridAccount.save()

      let data = {
        hostname: hostname
      }

      try {
        let response = await this.$http.post(
          '/sendgrid/set_parse?company_id=' + this.company.id,
          data
        )
      } catch (err) {
        console.error('Error setting parse:', err)
      }

      this.saving = true
    },
    getDomain(locationId: string) {
      let host = null
      if (this.sendgridAccount.domains) {
        host = this.sendgridAccount.domains[locationId]
      }
      console.log('Hostname:', locationId, host)
      return host
    },
    getSubDomain(locationId: string) {
      let host = null
      if (this.sendgridAccount.subdomains) {
        host = this.sendgridAccount.subdomains[locationId]
      }
      return host
    },
    setDomainsValue(event: any, location: Location) {
      console.log('Got event:', event)
      this.sendgridAccount.domains[location.id] = event.target.value
    },
    setSubDomainsValue(event: any, location: Location) {
      console.log('Got event:', event)
      this.sendgridAccount.subdomains[location.id] = event.target.value
    }
  }
})
</script>
