<template>
  <div id="yext_resell" v-if="company">
    <template v-if="!company.stripeConnectId">
      <div class="backdrop saas_backdrop">
        <div
          class="stripe-connect__setup-btn btn"
          @click="gotoStripeSettings()"
        >
          Connect Stripe
          <i class="fas fa-exclamation-circle"></i>
        </div>
      </div>
    </template>
    <template v-else-if="company.country != 'US'">
      <div class="backdrop saas_backdrop">
        <div class="country_restriction">
          <i class="fas fa-exclamation-circle"></i> Yext is only available in US
          for now. We are working to support this in other countries.
        </div>
      </div>
    </template>

    <div class="card">
      <div class="card-header">
        <div class="toggle header-toggle">
          <template v-if="false && company.stripeConnectId">
            <input
              type="checkbox"
              id="yext_show_customer_tgl"
              class="tgl tgl-light"
              v-model="yextReseller.enabled"
            /><label
              for="yext_show_customer_tgl"
              id="toggle_sms_settings"
              class="tgl-btn"
            ></label>
          </template>
          <label for="yext_show_customer_tgl" class="card-header__label">
            Yext Reselling</label
          >
        </div>
      </div>
      <div class="card-body">
        <div class="logo">
          <img class="sidebar-icon" src="/pmd/img/icon-yext-logo.svg" />
        </div>
        <div class="details">
          <h3>Re-sell Yext Listing</h3>
          <a
            class="how_to_link"
            href="https://help.gohighlevel.com/a/solutions/articles/48001196389"
            target="_blank"
            >How does it work?</a
          >
          <h4 class="sub-heading">Build Your Offer</h4>
          <div class="yext_offer_table">
            <form action="#">
              <label for="hl_price">HighLevel Price</label>
              <div class="prefix_input">
                <div class="prefix">$</div>
                <input
                  disabled
                  type="text"
                  id="hl_price"
                  :value="yextReseller.hl_price"
                />
              </div>

              <label for="agency_price">Your Price</label>
              <div>
                <div class="prefix_input">
                  <div class="prefix">$</div>
                  <input
                    type="text"
                    id="agency_price"
                    v-model.number="agency_price"
                  />
                  <span class="field_error">
                    <template v-if="agency_price < yextReseller.hl_price">
                      Price must be more or equal to HighLevel price
                    </template>
                  </span>
                </div>
              </div>

              <label for="agency_profit">Your Profit</label>
              <div class="prefix_input">
                <div class="prefix">$</div>
                <input
                  disabled
                  type="text"
                  id="agency_profit"
                  :value="
                    agency_price >= yextReseller.hl_price
                      ? getFriendlyNumber(agency_price - yextReseller.hl_price)
                      : 0
                  "
                />
              </div>
            </form>
          </div>
          <div class="place-eater"></div>
        </div>
        <div class="footer-controls">
          <div class="showoff">
            HighLevel gives you a discount compared to the stock rate which is
            usually billed at $999 with yearly commitment. HighLevel's wholesale
            price is just $30/month with only 3 month's commitment
          </div>
          <button
            :disabled="agency_price == yextReseller.agency_price"
            id="yext_resell_global"
            class="btn btn-success reselling-btn"
            @click="save"
          >
            <template v-if="saving">
              <i class="icon" :class="saving ? 'icon-clock' : 'icon-ok'"></i>
              Saving
            </template>
            <template v-else> Save </template>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// @ts-nocheck

import Vue from 'vue'
import { UserState, CompanyState } from '@/store/state_models'
import { mapState } from 'vuex'
import { User, Company } from '@/models'
import { getFriendlyNumber } from '@/util/helper'

export default Vue.extend({
  components: {},
  data: function () {
    return {
      saving: false,
      agency_price: 0,
      getFriendlyNumber
    }
  },

  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
    yextReseller: function () {
      return this.company.yextReseller
    },

    agencyInSaasPlan(): boolean {
      return this.$store.getters['company/inSaasPlan']
    },
  },
  created: function () {
    this.agency_price = this.company.yextReseller.agency_price
  },
  watch: {
    
  },
  methods: {
    async save() {
      if (
        this.company.stripeConnectId &&
        this.company.country &&
        this.company.country.toLowerCase() === 'us'
      ) {
        this.saving = true
        if (this.agency_price < this.yextReseller.hl_price) {
          this.agency_price = this.yextReseller.hl_price
        }
        const company = await Company.getById(this.company.id)
        company.yextReseller.agency_price = this.agency_price
        await company.save()
        this.saving = false
      }
    },
    gotoStripeSettings() {
      this.$router.push({ name: 'stripe_settings' })
    },
  },
})
</script>
<style lang="scss">
#yext_resell {
  height: 100%;
  position: relative;
  .saas_backdrop {
    position: absolute;
    z-index: 3;
    width: 100%;
    height: 100%;
    display: grid;
    justify-content: center;
    align-items: center;
    background: #ffffffad;
    /* label {
      background: #27ae60;
      padding: 5px 15px;
      color: #fff;
      font-size: 1rem;
      margin: 0;
    } */

    .stripe-connect__setup-btn {
      background: #feebc8;
      border-radius: 3px;
      font-weight: 500;
      font-size: 1rem;
      /* line-height: 14px; */
      text-align: center;
      margin-bottom: 14px;
      color: #f6ad55;
      padding: 10px 20px;
      box-shadow: 0 0 2px 0px #00000038;
      i {
        margin-left: 6px;
      }
      &:hover {
        background: #f6ad55;
        color: #ffffff;
      }
    }

    .country_restriction {
      font-size: 1rem;
      background: #feebc8;
      padding: 10px 20px;
      max-width: 60%;
      margin: auto;
      display: grid;
      grid-template-columns: auto auto;
      grid-column-gap: 10px;
      color: #ed8936;
      border-radius: 2px;
      .fa-exclamation-circle {
        margin-top: 5px;
        font-size: 2rem;
      }
    }
  }

  .card {
    height: 100%;
    .card-header {
      padding: 10px 20px;

      .header-toggle {
        display: grid;
        grid-template-columns: auto auto;
        grid-gap: 10px;
        align-items: center;

        input {
          display: none;
        }
      }

      .card-header__label {
        margin: 0;
        font-size: 1rem;
        font-weight: 400;
        color: #2a3844;
      }
    }
    .card-body {
      display: grid;
      grid-template-columns: auto 1fr;
      grid-column-gap: 40px;
      .logo {
        max-height: 100px;
        max-width: 100px;
        overflow: hidden;
        img {
          height: 100%;
        }
      }
      .details {
        display: grid;
        grid-template-rows: auto auto auto 1fr auto;
        h3 {
          font-size: 1.5rem;
          font-weight: 600;
          color: #4f4f4f;
        }
        .sub-heading {
          font-size: 1rem;
          margin: 10px 0;
          font-weight: 600;
          color: #4f4f4f;
        }
      }

      .upcoming-product {
        position: absolute;
        width: 100%;
        height: 100%;
        background: #ffffffc7;
        top: 0;
        left: 0;
        z-index: 1;
        display: grid;
        justify-content: center;
        align-items: center;
        h3 {
          background: #feebc8;
          padding: 10px 30px;
          color: #ed8936;
          border-radius: 1px;
          font-size: 1rem;
        }
      }

      .yext_offer_table {
        form {
          display: grid;
          grid-template-columns: auto 1fr;
          grid-gap: 10px;
          label {
            font-size: 1rem;
            color: #4f4f4f;
          }
          .field_error {
            display: block;
            font-size: 0.8rem;
            color: #e93d3d;
            min-height: 14px;
          }

          .prefix_input {
            position: relative;
            .prefix {
              position: absolute;
              top: 2px;
              left: 3px;
              background: white;
              padding: 0 10px;
              display: grid;
              align-items: center;
            }
            input {
              padding: 0 0 0 30px;
              width: 90px;
            }
          }
        }
        [for='agency_profit'] {
          color: #27ae60;
        }
      }
      .footer-controls {
        grid-column: 1/-1;
        display: grid;
        justify-content: right;
        grid-gap: 15px;
        grid-template-columns: 1fr auto;
        align-items: end;
        .showoff {
          padding: 8px;
          border-left: 3px solid #73cb98;
          background: linear-gradient(45deg, #73cb981f, transparent);
        }
      }
    }
  }

  .reselling-btn {
    background: #27ae60;
    padding: 6px 10px;
    justify-self: right;
  }
  .reselling-toggle {
    display: grid;
    grid-template-columns: auto 1fr;
    justify-content: right;
    grid-gap: 10px;
    .tgl-light:checked + .tgl-btn:after {
      background: white;
    }
    .tgl-light:checked + .tgl-btn {
      background: #3182ce;
    }
  }
}
</style>
