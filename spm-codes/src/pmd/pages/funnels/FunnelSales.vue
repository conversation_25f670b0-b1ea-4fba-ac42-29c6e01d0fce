<template>
  <div
    class="tab-pane fade show active"
    id="stats-tab"
    role="tabpanel"
    aria-labelledby="stats-tab"
  >
    <div class="hl_controls">
      <div class="hl_controls--left">
        <!-- <span class="page-instructions">
          <sup>*</sup>View receipt option only available for one time products
        </span> -->
      </div>
      <div class="hl_controls--right">
        <div>
          <!-- funnel  step picker -->
          <vue-ctk-date-time-picker
            v-model="dateRange"
            :range="true"
            color="#188bf6"
            :only-date="true"
            enable-button-validate
            :noClearButton="true"
            :formatted="getCountryDateFormat(false)"
            @validate="loadOrders"
            id="date-picker-sales"
            :right="true"
          />
        </div>
        <div>
          <vSelect
            class="page-size"
            :options="pageSizeOptions"
            v-model="pageSize"
            :clearable="false"
            placeholder="Select steps"
          ></vSelect>
        </div>
        <!-- funnel  step picker -->
        <!-- <div>
          <vSelect
            class="funnel-steps-picker"
            :options="stepOptions"
            label="name"
            v-model="stepPicked"
            :clearable="false"
          ></vSelect>
        </div>-->

        <div style="display: inline-block; position: relative">
          <button
            :class="{ invisible: downloadLoading }"
            type="button"
            class="btn btn-sm btn-primary"
            @click.prevent="downloadToCsv"
          >
            <i class="icon icon-download"></i>
          </button>
          <div
            style="
              position: absolute;
              left: 50%;
              transform: translate(-50%, -50%);
              top: 50%;
            "
            v-show="downloadLoading"
          >
            <moon-loader
              :loading="downloadLoading"
              color="#1ca7ff"
              size="15px"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="container-fluid">
      <div class="card hl_customers--table">
        <div class="card-body --no-padding">
          <div class="table-wrap">
            <table class="table table-sort">
              <thead>
                <tr>
                  <th>Customer</th>
                  <th>Email</th>
                  <th>Product Name</th>
                  <th>Transaction Id</th>
                  <th>Amount</th>
                  <th>Step</th>
                  <th>Purchase Date</th>
                </tr>
              </thead>
              <template v-if="loader">
                <placeholderLoader v-for="(n, index) in 7" :key="index" />
              </template>
              <orderList :order-data="orderData" v-if="!loader" />
            </table>
          </div>
        </div>
      </div>
      <nav v-if="orderData.length > 0">
        <ul data-v-efd6b0a4 class="pagination justify-content-end">
          <li
            data-v-efd6b0a4
            class="page-item"
            :class="{ disabled: disablePrevious }"
            @click="!disablePrevious && pre()"
          >
            <a
              data-v-efd6b0a4
              href="javascript:void(0);"
              tabindex="-1"
              class="page-link"
              >Previous</a
            >
          </li>
          <li
            data-v-efd6b0a4
            class="page-item"
            :class="{ disabled: disableloadMore }"
            @click="more"
          >
            <a
              data-v-efd6b0a4
              href="javascript:void(0);"
              class="page-link"
              @click="!disableloadMore && more()"
              >Next</a
            >
          </li>
        </ul>
      </nav>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import moment from 'moment-timezone'
import vSelect from 'vue-select'

import * as json2csv from 'json2csv'
import download from 'downloadjs'
import { getCountryDateFormat, Order, Funnel } from '../../../models'
import firebase from 'firebase/app'

export default Vue.extend({
  components: {
    vSelect,
    placeholderLoader: () =>
      import('@/pmd/components/funnels/PlaceHolderTable.vue'),
    orderList: () => import('@/pmd/components/funnels/OrderDataList.vue'),
    MoonLoader: () => import('@/pmd/components/MoonLoader.vue'),
  },
  data() {
    const endDate = moment().toDate()
    const startDate = moment().subtract(1, 'months').toDate()
    const dateRange = {
      start: startDate,
      end: endDate,
    }
    return {
      dateRange,
      getCountryDateFormat,
      loader: true,
      orders: [] as Order[],
      pageSize: 10,
      pageSizeOptions: [10, 25, 100],
      disableloadMore: false,
      disablePrevious: true,
      orderData: [] as Array<{ [key: string]: any }>,
      products: [] as any,
      downloadLoading: false,
    }
  },
  mounted() {
    this.loadOrders()
    this.fetchProductDetails()
  },
  watch: {
    pageSize() {
      this.disableloadMore = false
      this.loadOrders()
    },
  },
  computed: {
    funnelId(): string {
      return this.$route.params.funnel_id as string
    },
    locationId(): string {
      return this.$route.params.location_id as string
    },
    funnelSteps(): Array<{ [key: string]: any }> {
      const steps = JSON.parse(
        JSON.stringify(this.$store.state.funnel.funnelSteps)
      )
      return steps[this.funnelId]
    },
    stepOptions(): Array<{ [key: string]: any }> {
      return [...[{ name: 'All steps', value: 'all' }], ...this.funnelSteps]
    },
  },
  methods: {
    downloadToCsv() {
      this.downloadLoading = true
      const downloadData = this.orderData
        .map((order: any) => {
          return {
            customer: this.capitalize(
              order?.customer?.contact_name || 'DELETED CONTACT'
            ),
            email: order?.customer?.email || 'DELETED CONTACT',
            product_name: order?.product?.productName || '',
            transaction_id: order?.charge_id,
            amount: order?.amount,
            purchase_date: this.formatDate(order.date_added.seconds),
          }
        })
        .filter(x => x)

      const fields: Array<string | { label: string; value: string }> = [
        { label: 'Customer', value: 'customer' },
        { label: 'Email', value: 'email' },
        { label: 'Product Name', value: 'product_name' },
        { label: 'Transaction Id', value: 'transaction_id' },
        { label: 'Amount', value: 'amount' },
        { label: 'Purchase Date', value: 'purchase_date' },
      ]
      const json2csvParser = new json2csv.Parser({ fields })
      const csv = json2csvParser.parse(downloadData)

      download(csv, 'order.csv', 'text/csv')
      this.downloadLoading = false
    },
    async fetchProductDetails() {
      const funnel: Funnel = await Funnel.getById(this.funnelId)
      let productsList: Array<{ [key: string]: any }>
      productsList = funnel.steps
        .map(step => step.products)
        .filter(a => a)
        .reduce(
          (products: Array<{ [key: string]: any }>, product: any) => [
            ...products,
            ...product,
          ],
          []
        )
      this.products = productsList.reduce(
        (obj: any, item: any) => ((obj[item.id] = item), obj),
        {}
      )
    },
    orderFetchQuery(): firebase.firestore.Query {
      const fromDate = moment(this.dateRange.start, 'YYYY-MM-DD hh:mm a')
      let toDate: string
      if (!this.dateRange.end) {
        toDate = moment(this.dateRange.start, 'YYYY-MM-DD hh:mm a').endOf('day')
      } else {
        toDate = moment(this.dateRange.end, 'YYYY-MM-DD hh:mm a')
      }
      return Order.getAllByLocationAndFunnelId({
        locationId: this.locationId,
        funnelId: this.funnelId,
        start: fromDate,
        end: toDate,
      })
    },
    extractContactFromResponse(data: any) {
      return data.hits.hits
        .map((contact: any) => contact._source)
        .reduce(
          (set: Array<{ [key: string]: any }>, contact: any) =>
            Object.assign(set, { [contact.tie_breaker_id]: contact }),
          {}
        )
    },
    async bootstrap(contactIds: Array<string>) {
      try {
        const ids = contactIds.reduce((acc, val) => acc + ',' + val)
        const { data } = await this.$http.get('/search/contact_array', {
          params: { location_id: this.locationId, contact_id: ids },
        })
        const contacts = this.extractContactFromResponse(data)
        let steps: { [key: string]: any } = {}
        for (const step of this.funnelSteps) {
          steps[step.value] = step
        }
        this.orderData = []
        for (let order of this.orders) {
          if (!steps[order.stepId]) continue
          const customer = contacts[order.contactId]
          const { ...data } = order.data
          const product = this.products[order.productId]
          this.orderData.push({
            ...data,
            customer,
            product,
            stepName: steps[order.stepId].name || '',
          })
        }
        this.loader = false
      } catch (error) {
        console.error(error)
      }
    },
    async loadOrders() {
      this.orderData = []
      let fetchAllOrderQuery = await this.orderFetchQuery()
      const snapshot = await fetchAllOrderQuery.limit(this.pageSize).get()
      let contactIds: string[] = []
      this.orders = snapshot.docs.map(
        (d: firebase.firestore.QueryDocumentSnapshot) => {
          const order = new Order(d)
          contactIds.push(order.contactId)
          return order
        }
      )
      if (!(this.orders.length > 0)) {
        this.loader = false
        this.disableloadMore = true
        this.disablePrevious = true
        return
      }

      //  disable load more btn if orderlist less than page size
      if (this.orders.length < this.pageSize) {
        this.disableloadMore = true
      }
      if (!Array.isArray(contactIds)) return

      await this.bootstrap(contactIds)
    },
    async pre() {
      if (!(this.orders.length > 0)) return
      let contactIds: string[] = []
      let fetchAllOrderQuery = await this.orderFetchQuery()
      fetchAllOrderQuery = fetchAllOrderQuery.endBefore(this.orders[0].snapshot)
      let snapshot = await fetchAllOrderQuery.limitToLast(this.pageSize).get()
      if (!(snapshot.docs.length > 0)) {
        this.disablePrevious = true
        this.loader = false
        return
      }
      this.loader = true
      this.disableloadMore = false
      this.orders = snapshot.docs.map(
        (d: firebase.firestore.QueryDocumentSnapshot) => {
          const order = new Order(d)
          contactIds.push(order.contactId)
          return order
        }
      )
      if (!Array.isArray(contactIds)) {
        this.loader = false
        return
      }
      await this.bootstrap(contactIds)
    },
    async more() {
      if (!(this.orders.length > 0)) return
      let contactIds: string[] = []
      let fetchAllOrderQuery = await this.orderFetchQuery()
      fetchAllOrderQuery = fetchAllOrderQuery.startAfter(
        this.orders[this.orders.length - 1].snapshot
      )
      let snapshot = await fetchAllOrderQuery.limit(this.pageSize).get()
      if (!(snapshot.docs.length > 0)) {
        this.disableloadMore = true
        this.loader = false
        return
      }
      this.loader = true
      this.orders = snapshot.docs.map(
        (d: firebase.firestore.QueryDocumentSnapshot) => {
          const order = new Order(d)
          contactIds.push(order.contactId)
          return order
        }
      )

      this.disablePrevious = false

      console.log(this.orders)
      if (!Array.isArray(contactIds)) {
        this.loader = false
        return
      }
      await this.bootstrap(contactIds)
    },
    capitalize(value: string) {
      if (!value) return ''
      value = value.toString()
      return value.charAt(0).toUpperCase() + value.slice(1)
    },
    formatDate(value: number) {
      if (!value) return ''
      return moment.unix(value).format('LLL')
    },
  },
})
</script>

<style scoped>
.hl_controls .hl_controls--right > * {
  margin-bottom: unset;
}
.page-instructions {
  margin-left: 20px;
}
</style>

<style>
.funnel-steps-picker .vs__dropdown-toggle {
  min-width: 150px;
}
.page-size .vs__dropdown-toggle {
  min-width: 100px;
}
.funnel-sales-container {
  display: flex;
  flex-direction: column;
}
.funnel-sales-container {
  flex: 1 1 auto;
}
.page-size .vs__dropdown-toggle,
.funnel-steps-picker .vs__dropdown-toggle {
  background: #ffffff;
  /* padding: 9px; */
}
#date-picker-sales-wrapper {
  min-width: 200px;
}
</style>
