import { Module } from 'vuex';
import { TwilioState, RootState } from '../store/state_models';

export const agencyTwilio: Module<TwilioState, RootState> = {
    namespaced: true,
    state: {
		twilioData: {},
        showTwilioAlert: true,
        showTwilioLocationAlert: true,
        locationTwilioEmpty: false,
        showResolvePrompt: false,
    },
    getters: {
        get: (state: TwilioState, getters: any, rootState: RootState, rootGetters: any) => {
            return state;
        },
    },
    mutations: {
        // update: (state: TwilioState, twilioData) => {
        //     state.twilioData = twilioData;
        // },
        // reset: (state: TwilioState) => {
        //     state.twilioData = {};
        // },
        setLocationTwilioEmpty: (state: TwilioState, isEmpty) => {
            state.locationTwilioEmpty = isEmpty;
        },
		toggleAlert: (state: TwilioState, showAlert) => {
			if (showAlert) {
				// document.getElementsByTagName('body')[0].classList.add('with-alert');
			} else {
				document.getElementsByTagName('body')[0].classList.remove('with-alert');
			}
			state.showTwilioAlert = showAlert;
        },
        toggleLocationAlert: (state: TwilioState, showAlert) => {
			if (showAlert) {
				// document.getElementsByTagName('body')[0].classList.add('with-alert');
			} else {
				document.getElementsByTagName('body')[0].classList.remove('with-alert');
			}
			state.showTwilioLocationAlert = showAlert;
        },
        toggleResolvePrompt: (state: TwilioState, showPrompt) => {
			state.showResolvePrompt = showPrompt;
		}
    },
};