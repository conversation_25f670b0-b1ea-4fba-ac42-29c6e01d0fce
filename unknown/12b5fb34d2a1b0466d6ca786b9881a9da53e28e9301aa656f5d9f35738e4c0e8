<template>
  <div class="flex items-start">
    <div class="flex items-center h-5">
      <input
        :id="id"
        :name="name"
        type="radio"
        class="group focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 cursor-pointer"
        :disabled="disabled"
        :value="val"
        v-model="radio_value"
      />
    </div>
    <div class="ml-2 text-sm">
      <label
        :for="id"
        class="block text-xm font-medium cursor-pointer"
        :class="disabled ? 'text-gray-300' : 'text-gray-700'"
      >
        {{ label }}
      </label>
    </div>
  </div>
</template>
<script>
import { v4 as uuid } from 'uuid'
export default {
  props: {
    val: {
      type: String,
      required: true,
    },
    value: {},
    name: {
      type: String,
    },
    label: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      radio_value: null,
      id:uuid()
    }
  },
  watch: {
    value: {
      handler() {
        this.radio_value = this.value
      },
      immediate: true,
    },
    radio_value() {
      this.$emit('input', this.radio_value)
    },
  },
}
</script>
