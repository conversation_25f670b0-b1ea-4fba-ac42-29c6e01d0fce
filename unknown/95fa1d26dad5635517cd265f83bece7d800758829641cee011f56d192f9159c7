<template>
  <div class="recom-plan__wrap">
    <div class="recom-plan__plan-name" :class="{'--error': invalidElementId === `${plan.id}_title`}" v-if="!editingTitle" @click="editTitle()">
      {{ plan.title }} <i class="fas fa-pencil-alt"></i>
      <div class="tooltip__invalid-value" v-if="invalidElementId === `${plan.id}_title`" style="left: 64px;">
        <span class="value" > Name can't be empty</span>
      </div>
    </div>
    <div class="recom-plan__plan-name" :class="{'--error': invalidElementId === `${plan.id}_title`}" v-else>
      <input
        ref="planTitleInput"
        type="text"
        autofocus
        :value="plan.title"
        @change="updatePlanTitle"
        @blur="editingTitle = false"
      />
      <div class="tooltip__invalid-value" v-if="invalidElementId === `${plan.id}_title`" style="left: 64px;">
        <span class="value" > Name can't be empty</span>
      </div>
    </div>
    <div class="recom-plan__durations">
      <div
        class="recom-plan__duration-row"
        v-for="duration in activePrices"
        :key="duration.id"
      >
        <div class="recom-plan__duration-curreny">
          {{ duration.symbol || '$' }}
        </div>
        <UITextInputGroup
          type="number"
          class="recom-plan__duration-input"
          :class="{'--error': invalidElementId === duration.id}"
          :min="duration.min / 100"
          :value="duration.amount / 100"
          @change="e => updatePrices(duration.id, e, duration.min)"
          @keydown="preventDecimal"
        />
        <div class="recom-plan__duration-label">/ {{ duration.billingInterval }}</div>
        <div class="tooltip__invalid-value" v-if="invalidElementId === duration.id">
          <span class="value" > Minimum must be {{ duration.symbol || '$' }}{{duration.min/100}} </span>
        </div>
        <div class="tooltip__invalid-value" v-if="invalidElementId === `${duration.id}_annual_max`" style="left: 90px;">
          <span class="value" > Must be less than Monthly Plan * 12 </span>
        </div>
      </div>
    </div>
    <div class="snapshot-row --link" style="text-align:right" @click="showStripeProductDetailsModal = true">Show Stripe Product Details</div>
    <div class="recom-plan__highlights">
      <div
        class="recom-plan__highlight-row"
        v-for="highlight in plan.highlights"
        :key="highlight"
      >
        <i class="fas fa-check"></i>
        <div class="recom-plan__highlight-label">{{ highlight }}</div>
      </div>
      <div class="recom-plan__highlight-row">
        <i class="fas fa-check"></i>
        <div class="recom-plan__highlight-label">{{plan.description}}</div>
      </div>
    </div>
    <div class="recom-plan__feature-row --bold">Features:</div>
    <div class="recom-plan__features" v-if="mode=== 'recommended'">
      <div class="recom-plan__feature-row --additonal" v-if="prevPlan">
        Everything in {{prevPlan.title}} +
      </div>
      <div
        class="recom-plan__feature-row --actual"
        v-for="feature in recommendedPlanFeatures.slice(0,6)"
        :key="feature"
        :title="saasProductDetails[feature] ? saasProductDetails[feature].description : ''"
      >
        {{ saasProductDetails[feature] ? saasProductDetails[feature].title : feature.split('-').join(' ') }}
      </div>
      <div v-if="showAllFeatures">
        <div
          class="recom-plan__feature-row --actual"
          v-for="feature in recommendedPlanFeatures.slice(6)"
          :key="feature"
          :title="saasProductDetails[feature] ? saasProductDetails[feature].description : ''"
        >
          {{ saasProductDetails[feature] ? saasProductDetails[feature].title : feature.split('-').join(' ') }}
        </div>
      </div>
      <div class="recom-plan__feature-row --view-more" v-if="recommendedPlanFeatures.length > 6">
        <span @click="showAllFeatures = true" v-if="!showAllFeatures">
          View More (+{{recommendedPlanFeatures.length - 6}}) <i class="fas fa-chevron-down"></i>
        </span>
        <span @click="showAllFeatures = false" v-else>
          View Less <i class="fas fa-chevron-up"></i>
        </span>
      </div>
    </div>

    <div class="recom-plan__features --custom light-scrollbar" v-if="mode=== 'custom'">
      <Container group-name="features" @drop="onDropFeature( $event)" :get-child-payload="getChildPayload" :drop-placeholder="dropPlaceholderOptions">
        <div class="recom-plan__feature-row__wrap" v-if="prevPlan">
          <div class="recom-plan__feature-row --additonal" >
            <i class="fas fa-lock --lock"></i>
            Everything in {{prevPlan.title}} +
          </div>
        </div>
        <div v-else>
          <div class="recom-plan__feature-row__wrap"
            v-for="feature in minProducts"
            :key="feature"
            :title="saasProductDetails[feature] ? saasProductDetails[feature].description : ''">
            <div class="recom-plan__feature-row">
              <i class="fas fa-lock --lock"></i>
              {{ saasProductDetails[feature] ? saasProductDetails[feature].title : feature }}
            </div>
          </div>
        </div>
        <draggable
          class="recom-plan__feature-row__wrap"
          v-for="feature in planFeatures"
          :key="feature"
          :title="saasProductDetails[feature] ? saasProductDetails[feature].description : ''"
        >
          <div class="recom-plan__feature-row">
            <i class="fas fa-grip-vertical"></i>
            {{ saasProductDetails[feature] ? saasProductDetails[feature].title : feature }}
          </div>
        </draggable>
      </Container>
      <!-- <div v-if="showAllFeatures">
        <div
          class="recom-plan__feature-row --actual"
          v-for="feature in planFeatures.slice(6)"
          :key="feature"
          :title="saasProductDetails[feature] ? saasProductDetails[feature].description : ''"
        >
          {{ saasProductDetails[feature] ? saasProductDetails[feature].title : feature.split('-').join(' ') }}
        </div>
      </div> -->

      <!-- <div class="recom-plan__feature-row --view-more" v-if="planFeatures.length > 6">
        <span @click="showAllFeatures = true" v-if="!showAllFeatures">
          View More (+{{planFeatures.length - 6}}) <i class="fas fa-chevron-down"></i>
        </span>
        <span @click="showAllFeatures = false" v-else>
          View Less <i class="fas fa-chevron-up"></i>
        </span>
      </div> -->
    </div>
    <div class="recom-plan__section">
      <div class="recom-plan__section-heading">Complimentary Credits</div>
      <div class="recom-plan__section-description">
        When a user signs up, you can offer them free credits to use email
        &amp; telephony services.
      </div>
      <div class="recom-plan__twilio-credits-row">
        <div class="recom-plan__twilio-credits__value">
          <div class="recom-plan__duration-curreny">$</div>
          <UITextInputGroup
            type="number"
            class="recom-plan__twilio-credits-input"
            :class="{'--error': invalidElementId === `${plan.id}_complementaryCredits`}"
            :value="plan.complementaryCredits.amount"
            @change="e => updateComplementaryCredits('amount', e)"
            @keydown="preventDecimal"
          />
          <div class="recom-plan__duration-curreny">monthly</div>
          <div class="tooltip__invalid-value" v-if="invalidElementId === `${plan.id}_complementaryCredits`" style="left: 64px;">
            <span class="value" > Must be in range of 0-{{parseInt( this.activePrices[0].amount / 200 )}} USD</span>
          </div>
        </div>

        <!-- <div class="recom-plan__twilio-credits__duration">
          <div
            class="recom-plan__twilio-credits__duration-toggle__btn"
            :class="plan.complementaryCredits.type === 'monthly' ? '--active' : ''"
            @click="updateComplementaryCredits('type', 'monthly')"
          >
            Monthly
          </div>
          <div
            class="recom-plan__twilio-credits__duration-toggle__btn"
            :class="plan.complementaryCredits.type === 'oneTime' ? '--active' : ''"
            @click="updateComplementaryCredits('type', 'oneTime')"
          >
            One-time
          </div>
        </div> -->
      </div>
      <div class="recom-plan__twilio-credits__conclusion">
        That’s about
        {{ parseInt(plan.complementaryCredits.amount / smsCharge) }} texts or
        {{ parseInt(plan.complementaryCredits.amount / incomingCallsCharge) }}
        mins in calls
        <span v-if="perEmailCost > 0"> or &nbsp; {{
          parseInt(plan.complementaryCredits.amount / Number(perEmailCost))
          }}
           emails
        </span>
      </div>
    </div>
    <div class="recom-plan__section">
      <div class="recom-plan__section-heading">Attach Snapshot</div>
      <div class="recom-plan__section-description">
        Provide pre-build snapshots to the new accounts.
      </div>
      <!-- <div class="btn btn-primary" style="margin: 8px auto 0px; display: block" >
        Select Snapshot
      </div> -->
      <div class="snapshot-row --link" v-if="!selectedSnapshotName" @click="showSelectSnapshotModal = true">
        <span ><i class="fas fa-paperclip"></i> Select Snapshot</span>
        <span>
          <!-- <div @click="selectedSnapshot = null" class="snapshot-delete-icon"><i class="far fa-trash-alt" ></i></div> -->
        </span>
      </div>
      <div class="snapshot-row" v-else>
        <span><i class="fas fa-paperclip"></i> {{selectedSnapshotName}}</span>
        <div style="display:flex; align-items: center">
          <div @click="showSelectSnapshotModal = true" class="snapshot-edit-icon"><i class="fas fa-pencil-alt"></i></div>
          <div @click="dettachSnapshot" class="snapshot-delete-icon"><i class="far fa-trash-alt" ></i></div>
        </div>
      </div>
    </div>
    <!-- <div class="recom-plan__section">
      <div class="recom-plan__section-heading">Stripe Product Details</div>
      <div class="recom-plan__section-description">
        Below is stripe product-id, for your reference.
      </div>

      <div class="recom-plan__section__product-row" v-if="plan.stripeProductId">
        <div>{{plan.stripeProductId}}</div>
        <i class="far fa-copy" @click="copyText(plan.stripeProductId)"></i>
      </div>
    </div> -->
    <select-snapshot-modal :show="showSelectSnapshotModal" v-if="showSelectSnapshotModal" @close="showSelectSnapshotModal = false" @select="attachSnapshot"/>
    <stripe-product-details-modal :plan="plan" :show="showStripeProductDetailsModal" v-if="showStripeProductDetailsModal" @close="showStripeProductDetailsModal = false"/>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import SelectSnapshotModal from "./SelectSnapshotModal.vue";
import StripeProductDetailsModal from "./StripeProductDetailsModal.vue";
import copyToClipboard from '@/util/copy-to-clipboard'
import TwilioRebillMixin from "./TwilioRebillMixin";
import { CloneAccount } from '@/models';
import { trackGaEvent } from "@/util/helper";
import { Container, Draggable } from 'vue-smooth-dnd'

export default Vue.extend({
  mixins: [TwilioRebillMixin],
  props: ['plan', 'recommendedPlan', 'prevPlan', 'prevRecommendedPlan', 'saasProductDetails', 'minProducts', 'mode', 'perEmailCost'],
  data() {
    return {
      // creditsDuration: 'monthly',
      selectedSnapshotName: '',
      showSelectSnapshotModal: false,
      showStripeProductDetailsModal: false,
      showAllFeatures: false,
      editingTitle: false,
      markup: 0,
      invalidElementId: '',
      dropPlaceholderOptions: {
        className: 'drop-preview',
        animationDuration: '150',
        showOnTop: true
      }
    }
  },
  created(){
    this.setSelectedSnapshotName();
    this.markup = this.computeMarkupAmountFromPercent(this.plan.twilioRebilling.markup || 5)
  },
  components: {
    SelectSnapshotModal,
    StripeProductDetailsModal,
    Container,
    Draggable
  },
  computed: {
    activePrices() {
      if (this.plan.stripePlans) {
        return this.plan.stripePlans
          .filter( price => price.active === true)
          .sort((a,b) => {
            return a.amount < b.amount ? -1 : 1
          })
      } else {
        return this.plan.prices
          .filter( price => price.active === true)
          .sort((a,b) => {
            return a.amount < b.amount ? -1 : 1
          })
      }
    },
    recommendedPlanFeatures() {
      return this.recommendedPlan.saasProducts.filter( feature => {
        if(this.prevRecommendedPlan && this.prevRecommendedPlan.saasProducts && this.prevRecommendedPlan.saasProducts.includes(feature)) {
          return false;
        } else { // base-plan case
          return true;
        }
      })
    },
    planFeatures() {
      return this.plan.saasProducts.filter( feature => {
        if(this.prevPlan && this.prevPlan.saasProducts && this.prevPlan.saasProducts.includes(feature)) {
          return false;
        } else if (!this.prevPlan && this.minProducts.includes(feature) ) {
          return false;
        } else { // base-plan case
          return true;
        }
      })
    },
    company() {
      return this.$store.state.company.company
    },
  },
  watch: {
    'plan.snapshotId' : async function (newId) {
      this.setSelectedSnapshotName();
		},
    'plan.twilioRebilling.markup' : function (newMarkup) {
      this.markup = this.computeMarkupAmountFromPercent(this.plan.twilioRebilling.markup)
    }
  },
  methods: {
    editTitle() {
      this.editingTitle = true
      this.$nextTick(() => {
        this.$refs.planTitleInput.focus()
      })
    },
    preventDecimal(e){
      if(e.key==='.' || e.key === 'e'){
        e.preventDefault();
      }
    },
    async setSelectedSnapshotName() {
      const snapshotId = this.plan.snapshotId;
      if(snapshotId) {
        console.log(snapshotId);
        let snapshot = await CloneAccount.getById(snapshotId);
        this.selectedSnapshotName = snapshot.name || ''
      }
    },
    attachSnapshot(snapshot) {
      this.selectedSnapshotName = snapshot.name
      this.updatePlanData('snapshotId', snapshot.id)
      this.showSelectSnapshotModal = false;
    },
    dettachSnapshot(){
      this.selectedSnapshotName = ''
      this.updatePlanData('snapshotId', '')
      // this.showSelectSnapshotModal = false;
    },
    updatePlanTitle(e){
      let newValue = e.target.value;
      if(newValue === '') {
        e.preventDefault();
        this.invalidElementId = `${this.plan.id}_title`;
        setTimeout( ()=>{
          this.invalidElementId = ''
        }, 2000);
      } else {
        this.updatePlanData('title', newValue);
      }
    },
    updatePlanData(field, value){
      this.$emit('update', {field, value});
    },
    updateComplementaryCredits(field, value) {
      let newValue = value;
      let delayDuration = 0;
      if(field === 'amount'){
        const maxLimit = parseInt( this.activePrices[0].amount / 200)
        if(value < 0) {
          newValue = 0;
          delayDuration = 1000;
          this.invalidElementId = `${this.plan.id}_complementaryCredits`;
        } else if( value > maxLimit) {
          newValue = maxLimit;
          delayDuration = 2000;
          this.invalidElementId = `${this.plan.id}_complementaryCredits`;
        }
      }
      const newComplementaryCredits = {...this.plan.complementaryCredits};
      newComplementaryCredits[field] = value;
      this.updatePlanData('complementaryCredits',newComplementaryCredits)
      setTimeout( () => {
        newComplementaryCredits[field] = newValue;
        this.updatePlanData('complementaryCredits',newComplementaryCredits)
        this.invalidElementId = '';
        trackGaEvent('SaasPlanBuilder', this.company.id, `Updating complementaryCredits of plan-level: ${this.plan.planLevel}. New Value: ${newValue}`, 1);
      }, delayDuration)
    },
    updatePrices(planId: string, value: any, min: number) {
      let planIndex = this.plan.stripePlans.findIndex( plan => plan.id === planId);
      if(planIndex === -1) {
        return; // ideally never
      }
      const newPrices = [...this.plan.stripePlans];
      let modifiedValue = JSON.parse( JSON.stringify(value) );
      let delayDuration = 0;
      if(value * 100 < min) {
        modifiedValue = min / 100;
        delayDuration = 2000;
        this.invalidElementId = planId;
      } else if (newPrices[planIndex].billingInterval === 'year') {
        let mothlyPlanIndex = this.activePrices.findIndex( plan => plan.billingInterval === 'month' );
        if(mothlyPlanIndex !== -1 && (parseFloat(value) * 100 >= this.activePrices[mothlyPlanIndex].amount * 12) ) {
          modifiedValue = this.activePrices[mothlyPlanIndex].amount * 12 / 100;
          delayDuration = 2000;
          this.invalidElementId = `${planId}_annual_max`;
        }
      }
      if(planIndex !== -1) {
        newPrices[planIndex].amount = value * 100;
        this.updatePlanData('stripePlans',newPrices)
      }
      setTimeout( ()=>{
        if(planIndex !== -1) {
          newPrices[planIndex].amount = modifiedValue * 100;
          this.updatePlanData('stripePlans',newPrices)
          trackGaEvent('SaasPlanBuilder', this.company.id, `Updating stripePlans of plan-level: ${this.plan.planLevel}. New Value: ${newPrices[planIndex].amount / 100}/${newPrices[planIndex].billingInterval} `, 1);
        }
        this.invalidElementId = '';
      }, delayDuration)
    },
    copyText(text : string) {
      copyToClipboard(text)
    },
    getChildPayload(index: number) {
      // if(this.prevPlan){
      //   return this.planFeatures[index - 1]
      // } else {
      //   return this.planFeatures[index - this.minProducts.length]
      // }
      return this.planFeatures[index - 1]
    },
    onDropFeature(event:any){
      let e = {
        ...event,
        addedIndex: event.addedIndex ? event.addedIndex -1 : event.addedIndex,
        removedIndex: event.removedIndex ? event.removedIndex -1 : event.removedIndex,
      }
      if(e.addedIndex === null && e.removedIndex === null){
        return;
      }else if(e.addedIndex !== null && e.removedIndex !== null) {
        // re-order within same group
        let newSaasProducts = [...this.planFeatures]
        let removedProduct = newSaasProducts[e.removedIndex];
        // Deleting fromIndex
        newSaasProducts.splice(e.removedIndex, 1);
        // Adding toIndex
        newSaasProducts.splice(e.addedIndex, 0, removedProduct);
        if(this.prevPlan && this.prevPlan.saasProducts){
          this.updatePlanData('saasProducts', [...this.prevPlan.saasProducts,...newSaasProducts])
        } else { // base-plan case.
          this.updatePlanData('saasProducts', [...this.minProducts, ...newSaasProducts])
        }
      } else{
        this.$emit('onDropFeature', e );
      }
    }
  }
})
</script>

<style lang="scss">
@import './lightscroll.scss';
.recom-plan__wrap {
  padding: 44px 24px 0px;
  .recom-plan__plan-name {
    position: relative;
    font-weight: 500;
    font-size: 17px;
    line-height: 20px;
    color: #2d3748;
    // text-align: center;
    margin-bottom: 16px;
    cursor: pointer;
    // text-align: center;
    &.--error{
      color: rgb(255, 34, 34) !important;
    }

    i{
      margin-left: 8px;
      font-size: 12px;
      opacity: 0.5;
    }
    &:hover{
      i{
        opacity: 1;
      }
    }

    input{
      padding: 4px 6px;
      margin: -6px;


      width: 100%;
      background: rgba(255, 255, 255, 0.85);
      border: 1px solid #d1d5db;
      box-sizing: border-box;
      border-radius: 3px;
      outline: none;
      // text-align: center;

      &:active,
      &:focus-within {
        border: 1px solid #63b3ed;
      }
    }
  }
  .recom-plan__duration-row {
    display: flex;
    align-items: center;
    position: relative;
    // justify-content: center;

    margin-bottom: 12px;
    margin-left: 16px;
    .recom-plan__duration-input {
      width: 80px;
      background: rgba(255, 255, 255, 0.85);
      // border: 1px solid #d1d5db;
      box-sizing: border-box;
      border-radius: 3px;
      outline: none;
      text-align: center;

      margin: 0px 8px;
      &:active,
      &:focus-within {
        border: 1px solid #63b3ed;
      }
      &.--error{
        border: 1px solid rgb(255, 34, 34);
      }
    }
    .recom-plan__duration-label {
      min-width: 64px;
    }
  }
  .recom-plan__highlights {
    margin: 24px 0px;
  }
  .recom-plan__highlight-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    font-size: 14px;
    font-weight: 200;

    i {
      margin-right: 8px;
      color: #27ae60;
    }
  }
  .recom-plan__features {
    height: 208px;
    overflow-y: auto;
    &.--custom {
      height: 300px;
      .smooth-dnd-container.vertical{
        // height: unset !important;
      }
      .recom-plan__feature-row__wrap{
        padding: 2px 0px;
      }
      .recom-plan__feature-row{
        background-color: #ffffff;
        padding: 4px 6px;
        width: 100%;
        // margin-bottom: 4px;
        font-size: 14px;
        line-height: 18px;
        user-select: none;
        i{
          margin-right: 8px;
          cursor: move;
          color: #C4C4C4;
          &.--lock {
            color: #6B7280;
            cursor: pointer;
          }
        }
      }
    }
  }
  .recom-plan__feature-row {
    // font-size: 14px;
    // line-height: 16px;
    &.--actual {
      text-transform: capitalize;
    }
    &.--bold {
      font-weight: 500;
    }
    &.--additonal {
      font-weight: 500;
      color: #2f80ed;
    }
    &.--view-more {
      // margin-top: 12px;
      color: #2f80ed;
      font-weight: 500;
      display: flex;
      align-items: center;

      cursor: pointer;
      i {
        margin-left: 40px;
        font-size: 12px;
        color: #bdbdbd;
      }
    }
  }

  .recom-plan__section {
    margin: 24px 0px;
    .recom-plan__section-heading {
      font-weight: 500;
      font-size: 14px;
      line-height: 16px;
      color: #2d3748;

      margin-bottom: 10px;
    }
    .recom-plan__section-description {
      font-size: 12px;
      line-height: 16px;
      color: #718096;
    }
    .recom-plan__twilio-credits__conclusion {
      margin-top: 12px;
      font-size: 12px;
      line-height: 16px;
      color: #059669;
    }
    .recom-plan__section__product-row{
      display: flex;
      align-items: center;
      justify-content: space-between;

      background-color: rgba(221,223,224, 0.50);
      border-radius: 2px;
      padding: 6px 12px;
      color: #575757;
      margin-top: 8px;

      i{
        cursor: pointer;
      }
    }
  }
  .recom-plan__twilio-credits-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 12px;
    .recom-plan__twilio-credits__value {
      position: relative;
      display: flex;
      align-items: center;
    }
    .recom-plan__twilio-credits-input {
      width: 64px;
      background: rgba(255, 255, 255, 0.85);
      // border: 1px solid #d1d5db;
      box-sizing: border-box;
      border-radius: 3px;
      outline: none;
      text-align: center;

      margin: 0px 8px;
      &:active,
      &:focus-within {
        border: 1px solid #63b3ed;
      }
      &.--error{
        border: 1px solid rgb(255, 34, 34);
      }
    }
  }
  .recom-plan__twilio-credits__duration {
    display: flex;
    margin: -16px auto;

    border-radius: 5px;
    border: solid 2px #2f80ed;
    background-color: #2f80ed;
    // width: 162px;
    z-index: 1;
    position: relative;
  }
  .recom-plan__twilio-credits__duration-toggle__btn {
    padding: 6px 12px;
    font-weight: 500;
    font-size: 12px;
    line-height: 14px;
    letter-spacing: 0.004em;
    // text-transform: uppercase;
    white-space: nowrap;

    background-color: #2f80ed;
    color: #ffffff;
    cursor: pointer;
    border-radius: 5px;
    &.--active {
      background-color: #ffffff;
      color: #2f80ed;
    }
  }
}

.snapshot-row{
  margin-top: 12px;
  font-size: 14px;

  display: flex;
  align-items: center;
  justify-content: space-between;

  i{
    margin-right: 8px;
  }
  .snapshot-delete-icon{
    cursor: pointer;
    color: red;
  }
  .snapshot-edit-icon {
    cursor: pointer;
    color: #63b3ed;
  }
  &.--link{
    cursor: pointer;
    color: #2f80ed;

  }
}
.tooltip__invalid-value {
    position: absolute;
    left: 40px;
    top: -30px;
    // width: calc(100% - 24px);
    height: 20px;
    z-index: 5;
  }
  span.value {
    position: absolute;
    text-align: center;
    background: #2d3748;
    border-radius: 5px;
    color: white;
    padding: 4px 6px;
    border-radius: 2px;
    font-size: 12px;
    line-height: 16px;
    white-space: nowrap;
    transition: opacity 0.25s ease-in;
    transform: translateX(-50%);
    opacity: 0.8;
    &:before {
      content: '';
      width: 0px;
      height: 0px;
      position: absolute;
      border-left: 5px solid #2d3748;
      border-right: 5px solid transparent;
      border-top: 5px solid #2d3748;
      border-bottom: 5px solid transparent;
      left: calc(50% - 5px);
      top: 18px;
      transform: rotate(225deg);
    }
  }
.drop-preview{
  background-color: rgba(150, 150, 200, 0.1);
  border: 1px dashed #abc;
  margin: 5px;
  width: 100%;
  // height: 80px;
}
</style>
