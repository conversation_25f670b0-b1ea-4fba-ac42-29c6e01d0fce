<template>
	<section class="hl_wrapper">
		<section class="hl_marketing--inner hl_wrapper--inner hl_marketing" id="marketing">
			<div class="hl_marketing--header" style="border-bottom:none;">
				<div class="container-fluid">
					<div class="hl_controls campaign_new_header">
						<div class="hl_controls--left flex" style="flex-grow: 1;">
							<h3 style="width: 100%;">
								<span @click="goToPreviousRoute()" id="back">
									<i class="icon icon-arrow-left-2"></i>
								</span>
								<input
									class="trigger-name"
									:disabled="!allowEdit"
									type="text"
									placeholder="Campaign name"
									v-model="campaign.name"
									@change="saveName"
									style="border-bottom: 1px solid #e0e0e0;"
								>
							</h3>
						</div>
						<div class="hl_controls--right">
							<div
							id="hl_datepicker"
							class="hl_datepicker d-flex justify-content-center align-items-center"
							>
								<i class="icon icon-calendar"></i>
								<div class="d-flex" style="width: 220px;">
									<vue-ctk-date-time-picker
                  id="event-start-time"
									v-model="opportunitiesFiltersDates"
									:right="true"
									:range="true"
									color="#188bf6"
									:only-date="true"
									enable-button-validate
									:noClearButton="true"
									name="start_time"
									min-date="2020-05-29 12:00 am"
									:formatted="getCountryDateFormat(false)"
									@validate="setSelectedDate"

									/>
								</div>
								<i class="icon icon-arrow-down-1"></i>
							</div>
							<template v-if="allowEdit">
								<div>
								<span v-if="this.showEventRequiredError" style="color: red">At least one event is required.</span>
								</div>
								<b-dropdown :text="campaign.status | capitalize"  variant="primary" size="sm">
									<b-dropdown-item
									@click.prevent="campaign.draft"
									v-if="campaign.status==='published'"
									>Draft</b-dropdown-item>
									<b-dropdown-item
									@click.prevent="updateCampaignStatus"
									v-if="campaign.status==='draft'"
									>Published</b-dropdown-item>
								</b-dropdown>
								<b-dropdown text="Actions"  variant="primary" right size="sm">
									<b-dropdown-item
									@click="copyCampaign()"
									>Copy Campaign</b-dropdown-item>
								</b-dropdown>
							</template>
						</div>
					</div>
					<!-- <ul class="hl_marketing--nav">
                        <router-link :to="{name: 'campaign_edit', params: {location_id: currentLocationId, campaign_id: campaign.id}}" tag="li" active-class="active" exact>
                            <a href="javascript:void(0);">Campaign Steps</a>
                        </router-link>
                        <router-link :to="{name: 'campaign_subscribers', params: {location_id: currentLocationId, campaign_id: campaign.id}}" tag="li" active-class="active" exact>
                            <a href="javascript:void(0);">Campaign Subscribers</a>
                        </router-link>
					</ul>-->
				</div>
			</div>

			<div class="container-fluid">
				<div class="hl_marketing--heading --info" v-if="campaign.status === 'draft' && !campaign.loopIdentified">
					<i class="icon icon-info"></i>
					<div class="hl_marketing--heading-text">
						<h3>Draft Mode</h3>
						<p>This campaign is a draft. It cannot be used until it is published.</p>
					</div>
				</div>

        <div
					class="d-flex justify-content-between align-items-center alert --red"
					role="alert"
					v-if="campaign.deleted"
				>
          <span>This Campaign is deleted! Do you wish to restore it?</span>
          <button v-if="campaign.deleted" class="btn btn-danger" type="button" @click="restore()" >Restore</button>
        </div>

        <div
					class="d-flex justify-content-between align-items-center alert --red"
					role="alert"
					v-if="campaign.loopIdentified"
				>
          <span>This Campaign has been locked in Draft status because it is causing a loop. To activate this Campaign, please contact Support.</span>
        </div>

        <div
					class="d-flex justify-content-between align-items-center alert --green"
					role="alert"
					v-if="isInternalUser || $route.query.debug"
				>
          <span>Campaign creation: {{ campaign.dateAdded }}</span>
          <span>Last update: {{ campaign.dateUpdated }}</span>
        </div>

				<div class="hl_create-campaign">
          <div class="py-1 px-2 my-2 info-blue --blue">
             <div class="mx-3 my-1">
               <span class="input-group-addon mx-1">
                 <i class="fa fa-exclamation-triangle" ></i>
              </span>
              <span class="mx-1 my-2">Email stats data is available from 29 May 2020. SMS stats data is available from 29 June 2020.</span>
            </div>
          </div>
					<div class="card card-collapsing" v-show="allowEdit">
						<a
							data-toggle="collapse"
							href="#campaignconfiguration"
							role="button"
							aria-expanded="false"
							class="collapsed"
							ref="configSection"
						>
							<div class="card-header">
								<h2>
									<div>
										<i class="icon icon-arrow-right-1"></i> Campaign Configuration
									</div>
								</h2>
							</div>
						</a>
						<div class="collapse" id="campaignconfiguration">
							<div class="card-body">
								<div class="hl_campaign-configuration">
									<div class="form-group" style="margin-bottom: 10px;">
										<label>Window</label>
										<div>
											<UIToggle
												id="account-buffer-tgl"
												v-model="window"
												:disabled="!allowEdit"
											/>
											<label class="tgl-btn" for="account-buffer-tgl"></label>
										</div>
									</div>
									<transition name="slide">
										<div class="scheduling" v-if="window">
											<hr>
											<div class="form-group condition">
												<label>Condition</label>
												<div>
													<div class="form-group-controls">
														<select
															class="selectpicker"
															v-model="condition"
															name="condition"
															:disabled="!allowEdit"
														>
															<option value="if">If</option>
															<option value="when">When</option>
														</select>

                          <span
                          v-if="condition==='when'"
                          v-b-tooltip.hover
                          title="Note: If the first step in campaigns is set to “Immediately”, then time condition would not apply for that first step."
                          >
                          <i class="fas fa-question-circle"></i>
                          </span>

													</div>
													<span
														class="explanation"
														v-if="condition==='if'"
													>Run this campaign or event if the current time falls in this window, if the current time is outside this window skip the campaign or event and move on to the next one.</span>
													<span
														class="explanation"
														v-if="condition==='when'"
													>Run the events in this campaign when the current time falls in this window, else wait until the next window to run the events.</span>
												</div>
											</div>
											<div class="form-group">
												<label>Included Days</label>
												<div class="form-group-controls">
													<div class="week-select">
														<div class="week-select-day">
															<input type="checkbox" id="mon" v-model="days" value="1" :disabled="!allowEdit">
															<label for="mon">Mon</label>
														</div>
														<div class="week-select-day">
															<input type="checkbox" id="tue" v-model="days" value="2" :disabled="!allowEdit">
															<label for="tue">Tue</label>
														</div>
														<div class="week-select-day">
															<input type="checkbox" id="wed" v-model="days" value="3" :disabled="!allowEdit">
															<label for="wed">Wed</label>
														</div>
														<div class="week-select-day">
															<input type="checkbox" id="thu" v-model="days" value="4" :disabled="!allowEdit">
															<label for="thu">Thu</label>
														</div>
														<div class="week-select-day">
															<input type="checkbox" id="fri" v-model="days" value="5" :disabled="!allowEdit">
															<label for="fri">Fri</label>
														</div>
														<div class="week-select-day">
															<input type="checkbox" id="sat" v-model="days" value="6" :disabled="!allowEdit">
															<label for="sat">Sat</label>
														</div>
														<div class="week-select-day">
															<input type="checkbox" id="sun" v-model="days" value="0" :disabled="!allowEdit">
															<label for="sun">Sun</label>
														</div>
													</div>
												</div>
											</div>
											<div class="form-group">
												<label>Start time</label>
												<div class="form-group-controls">
													<select
														class="selectpicker"
														v-model="startTime"
														name="startTime"
														data-size="5"
														:disabled="!allowEdit"
													>
														<option
															v-for="slot in startSlots"
															:key="'start-'+slot.format()"
															:value="slot.format()"
														>{{slot.format('hh:mm a')}}</option>
													</select>
												</div>
											</div>
											<div class="form-group">
												<label>End time</label>
												<div class="form-group-controls">
													<select
														class="selectpicker"
														v-model="endTime"
														name="endTime"
														data-size="5"
														:disabled="!allowEdit"
													>
														<option
															v-for="slot in endSlots"
															:key="'start-'+slot.format()"
															:value="slot.format()"
														>{{slot.format('hh:mm a')}}</option>
													</select>
												</div>
											</div>
										</div>
									</transition>
									<div class="users">
										<!-- <h3>Attributions</h3>
										<hr>-->
										<div class="form-group">
											<label>Users</label>
											<div class="form-group-controls" style="width: 350px;">
												<select
													class="selectpicker"
													@change="userSelected"
													ref="location_select_picker"
													:disabled="!allowEdit"
												>
													<option value>Add user</option>
													<option v-for="user in filteredUsers" :value="user.id">{{user.fullName}}</option>
												</select>
												<div
													v-for="(userId, key) in campaignUsers"
													v-bind:key="key"
													class="marketing-config-user"
													v-if="getUser(userId)"
												>
													<div>
														<Avatar :contact="getUser(userId)" size="sm" include_name="true"/>
														<i class="icon-close" @click.prevent="removeUser(userId)" v-if="allowEdit"></i>
													</div>
												</div>
											</div>
										</div>
									</div>
									<!-- <div class="form-group" style="margin-bottom: 10px;">
										<label>Create opportunity on response</label>
										<div>
											<input
												:disabled="!allowEdit"
												type="checkbox"
												class="tgl tgl-light"
												id="account-buffer-opportunity"
												v-model="opportunity"
											>
											<label class="tgl-btn" for="account-buffer-opportunity"></label>
										</div>
									</div>
									<transition name="slide">
										<div class="scheduling" v-if="opportunity">
											<div class="form-group">
												<label>Pipeline</label>
												<div class="form-group-controls" style="width: 200px;">
													<select
														:disabled="!allowEdit"
														class="selectpicker"
														v-model="pipelineId"
														name="pipelineId"
														data-size="5"
														title="Select pipeline"
													>
														<option
															v-for="pipeline in pipelines"
															:key="pipeline.id"
															:value="pipeline.id"
														>{{pipeline.name}}</option>
													</select>
												</div>
											</div>
											<div class="form-group" v-if="pipelineId">
												<label>Stage</label>
												<div class="form-group-controls" style="width: 200px;">
													<select
														:disabled="!allowEdit"
														title="Select stage"
														class="selectpicker"
														v-model="pipelineStageId"
														name="pipelineStageId"
														data-size="5"
													>
														<option v-for="stage in stages" :key="stage.id" :value="stage.id">{{stage.name}}</option>
													</select>
												</div>
											</div>
										</div>
									</transition>-->

									<div class="actions">
										<!-- <h3>Action</h3>
										<hr>-->
										<div class="form-group">
											<label>Next Campaign</label>
											<div class="form-group-controls" style="width: 350px;">
												<select
													class="selectpicker"
													v-model="actionNoResponse"
													data-size="5"
													:disabled="!allowEdit"
												>
													<option value>Select campaign</option>
													<option
														v-for="campaign in filteredCampaigns"
														:key="'no-response-'+campaign.id"
														:value="campaign.id"
													>{{campaign.name}}</option>
												</select>
											</div>
										</div>
									</div>
									<div class="from-email-address">
										<!-- <h3>Extras</h3>
										<hr>-->
										<div class="form-group">
											<label>
												From address
												<a class="hl_help-article" v-if="showFromWarning && user.type == 'agency'" v-b-tooltip.hover title="SMTP providers has some limitations. Click here to know more." href="https://youtu.be/eltAOMf3AUA" target="blank">
													<i class="fa fa-exclamation-triangle text-warning"></i>
												</a>
											</label>
											<div class="form-group-controls" style="width: 350px;">
												<UITextInputGroup
													:disabled="!allowEdit"
													type="text"
													v-model.trim="fromName"
													placeholder="From name"
													v-validate="'handlebars'"
													name="from_name"
													:error="errors.has('from_name')"
													:errorMsg="errors.first('from_name')"
												/>
												<UITextInputGroup
													style="margin-top: 5px;"
													:disabled="!allowEdit"
													type="email"
													name="fromEmail"
													v-model.trim="fromEmailAddress"
													placeholder="From email"
													@change="validateFromEmail"
													:error="fromEmailError"
													:errorMsg="'Please provide a valid email address or custom variable'"
												/>
											</div>
										</div>
									</div>

									<div class="lead-value">
										<!-- <h3>Extras</h3>
										<hr>-->
										<div v-if="user && user.permissions.lead_value_enabled !== false" class="form-group">
											<label>Lead value</label>
											<div class="form-group-controls">
												<UITextInputGroup
													:disabled="!allowEdit"
													type="number"
													v-model.number="leadValue"
													placeholder="$ value of lead"
												/>
											</div>
										</div>
										<div class="form-group campaign-tags">
											<label>Tags</label>
											<div class="form-group-controls">
												<TagComponent
													v-model="tags"
													:form="false"
													:disabled="!allowEdit"
												/>
												<div class="tag-group" v-if="tags.length > 0" style="margin-top: 10px;max-width:188px;">
													<div class="tag" v-for="tag in tags">
														{{tag}}
														<a @click="removeTag(tag)" v-if="allowEdit">
															<i class="icon icon-close"></i>
														</a>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div class="form-group">
										<label>Allow multiple</label>
										<div>
											<UIToggle
												id="allow-multiple"
												v-model="allowMultiple"
												:disabled="!allowEdit"
											/>
											<label class="tgl-btn" for="allow-multiple"></label>
										</div>
									</div>
									<div class="form-group">
										<label>Stop on response</label>
										<div>
											<UIToggle
												id="stop-on-reply"
												v-model="stopOnReply"
												:disabled="!allowEdit"
											/>
											<label class="tgl-btn" for="stop-on-reply"></label>
										</div>
									</div>
									<div class="form-group">
										<label>Event Start Date</label>

										<vue-ctk-date-time-picker
                      id="event-start-date"
                 			:locale="getCountryInfo('locale')"
											:noClearButton="true"
											:disabled="!allowEdit"
											v-model="eventDate"
											:minute-interval="5"
											color="#188bf6"
											enable-button-validate
											style="width: 350px;margin: 0px;"
                      class="border border-gray-300 rounded shadow"
										/>

										<i
											class="icon icon-trash --gray pointer"
											v-if="eventDate && allowEdit"
											@click.stop="eventDate=null"
											style="margin-left:10px; margin-top:14px;"
										></i>
									</div>
								</div>
							</div>
							<div class="card-footer" v-if="allowEdit">
								<a @click.prevent="saveConfig" href="javascript:void(0);" class="btn btn-blue">Save</a>
								<a @click.prevent="resetConfig" href="javascript:void(0);" class="btn btn-primary">Cancel</a>
							</div>
						</div>
					</div>

					<div class="card">
						<div class="card-body">
							<div class="add-new-event" v-if="templates.length === 0">
								<h4>Start by adding an event to this campaign.</h4>
								<a
									@click.prevent="addActionValues={visible: true}"
									href="javascript:void(0);"
									class="btn btn-success"
								>
									<i class="icon icon-plus"></i> Add Event
								</a>
							</div>
							<div v-else>
								<!-- <div class="sort" @click.prevent="sort">
									<i class="fa fa-2x fa-sort-amount-down"></i>
								</div>-->
								<Container
									:group-name="'campaign'"
									@drop="onDrop"
									:get-child-payload="getChildPayload"
									@drag-start="dragStart"
									@drag-end="dragEnd"
								>
									<Draggable
                    					v-if="user && user.permissions.campaigns_enabled && !user.permissions.campaigns_read_only"
										v-for="template in templates"
										:key="template.id"
										:class="{'calendar-day-on-drag' : dragging }"
									>
										<ActionItem
											:key="unique"
											:bus="bus"
											:template.sync="template"
											:campaignId="campaign.id"
											:selectedDate="selectedDate"
											@remove="removeAction"
											@edit="editAction"
											@update="updateAction"
											:dragging="dragging"
										/>
									</Draggable>
									<div
										v-if="user && user.permissions.campaigns_enabled && user.permissions.campaigns_read_only"
										v-for="template in templates"
										:key="template.id"
										style="margin-bottom: 30px"
									>
										<ActionItem
											:bus="bus"
											:campaignId="campaign.id"
											:selectedDate="selectedDate"
											:template.sync="template"
											@remove="removeAction"
											@edit="editAction"
											@update="updateAction"
										/>
									</div>
								</Container>
								<div class="new-event-day-item --add-new" style="margin-top: 30px;">
									<a
										v-if=" allowEdit"
										@click.prevent="addActionValues={visible: true}"
										href="javascript:void(0);"
										class="add-new-event-btn"
									>
										<i class="icon icon-plus"></i>
										<span>Add Event</span>
									</a>
                  <!-- <a v-on:click.stop.prevent="refreshAllStats" href="javascript:void(0);"  style="padding: 4px 16px;">Refresh All Stats</a> -->
								</div>
							</div>
						</div>
					</div>

					<AddActionModal
						:values="addActionValues"
						@selected="addAction($event)"
						@hidden="addActionValues={visible: false}"
					/>
					<EditEmailEventModal
						:values="editEmailEvent"
						@save="saveTemplates"
						@hidden="editEmailEvent={visible: false}"
						:templates="emailTemplates"
					/>
					<EditSMSEventModal
						:values="editSMSEvent"
						@save="saveTemplates"
						@hidden="editSMSEvent={visible: false}"
						:templates="dbTemplates"
					/>
          			<EditWebhookEventModal
						:values="editWebhookEvent"
						@save="saveTemplates"
						@hidden="editWebhookEvent={visible: false}"
					/>
          			<EditTaskNotificationModal
						:values="editTaskNotificationEvent"
						@save="saveTemplates"
						@hidden="editTaskNotificationEvent={visible: false}"
          			/>
					<EditCallEventModal
						:values="editCallEvent"
						@save="saveTemplates"
						@hidden="editCallEvent={visible: false}"
					/>
					<EditVoicemailEventModal
						:values="editVoicemailEvent"
						@save="saveTemplates"
						@hidden="editVoicemailEvent={visible: false}"
					/>
					<EditWaitEventModal
						:values="editWaitEvent"
						@save="saveTemplates"
						@hidden="editWaitEvent={visible: false}"
					/>
					<CopyCampaignModal
						:campaign="campaign"
						:showModal.sync="showCopyModal"
						:currentLocationId="currentLocationId"
						@hidden="showCopyModal=false;"
					/>
				</div>
			</div>
		</section>
		<!-- END of .hl_marketing -->
	</section>
	<!-- END of .hl_wrapper -->
</template>

<script lang="ts">
import Vue from "vue";
import * as moment from "moment-timezone";
import * as lodash from "lodash";
import { mapState } from 'vuex';
import { v4 as uuid } from "uuid";
import Datepicker from "vuejs-datepicker";
import { Container, Draggable } from "vue-smooth-dnd";
import Trigger, { CreateOpportunity } from "@/models/trigger";
import { UserState } from '../../../store/state_models';
import {UxMessage} from '@/util/ux_message'
import { EventBus } from '@/models/event-bus';
import { getCountryDateFormat } from '@/models';

import {
	Campaign,
	User,
	CampaignStatus,
	Stage,
	Pipeline,
	Location,
	Template,
  Link,
	getCountryInfo,
	Folder
} from "@/models";
import Avatar from  "../../components/Avatar.vue";
const TagComponent = () => import( /* webpackChunkName: "customer-tag" */ "../../components/customer/TagComponent.vue").then(m => m.default);
const AddActionModal = () => import(/* webpackChunkName: "marketing-add-action-m" */ "../../components/marketing/AddActionModal.vue").then(m => m.default);
const ActionItem = () => import(/* webpackChunkName: "marketing-action-item" */ "../../components/marketing/ActionItem.vue").then(m => m.default);
const EditEmailEventModal = () => import(/* webpackChunkName: "marketing-edit-email-m" */ "../../components/marketing/EditEmailEventModal.vue").then(m => m.default);
const EditSMSEventModal = () => import(/* webpackChunkName: "marketing-edit-sms-m" */ "../../components/marketing/EditSMSEventModal.vue").then(m => m.default);
const EditWebhookEventModal = () => import(/* webpackChunkName: "marketing-edit-webhook-m" */ "../../components/marketing/EditWebhookEventModal.vue").then(m => m.default);
const EditTaskNotificationModal = () => import(/* webpackChunkName: "marketing-edit-task-m" */  "../../components/marketing/EditTaskNotificationModal.vue").then(m => m.default);
// const EditFacebookMessageModal = () => import(/* webpackChunkName: "marketing-edit-fb-m" */ "../../components/marketing/EditFacebookMessageModal.vue").then(m => m.default);
const EditCallEventModal = () => import(/* webpackChunkName: "marketing-edit-call-m" */ "../../components/marketing/EditCallEventModal.vue").then(m => m.default);
const EditVoicemailEventModal = () => import(/* webpackChunkName: "marketing-edit-voicemail-m" */ "../../components/marketing/EditVoicemailEventModal.vue").then(m => m.default);
const EditWaitEventModal = () => import(/* webpackChunkName: "marketing-edit-wait-m" */ "../../components/marketing/EditWaitEventModal.vue").then(m => m.default);
const CopyCampaignModal = () => import(/* webpackChunkName: "marketing-copy-campaign-m" */ "../../components/marketing/CopyCampaignModal.vue").then(m => m.default);

declare var $: any;
let cancelTemplateSubscription: () => void;
let cancelStatusSubscription: () => void;

const bus = new Vue();
// const refreshCount = lodash.debounce(() => {
// 	bus.$emit('refresh_count')
// }, 2000, { trailing: true, maxWait: 2000 });

const getDefaultTZ = () => {
	return moment.tz.guess() || "America/Los_Angeles";
};

export default Vue.extend({
  inject: ['uxmessage'],
	components: {
		TagComponent,
		Avatar,
		AddActionModal,
		ActionItem,
		EditEmailEventModal,
		EditSMSEventModal,
		EditCallEventModal,
		EditVoicemailEventModal,
		EditWaitEventModal,
		Datepicker,
		CopyCampaignModal,
		Container,
		Draggable,
    	EditWebhookEventModal,
		EditTaskNotificationModal,
	},
	data() {
		return {
			opportunitiesFiltersDates: {
				start: moment()
					.subtract(30, 'days')
					.startOf('day')
					.toDate(),
				end: moment()
					.endOf('day')
					.toDate()
			},
			selectedDate: {
				start: moment()
					.subtract(30, 'days')
					.startOf('day')
					.toDate(),
				end: moment()
					.endOf('day')
					.toDate()
			},
			unique: 0,
			showEventRequiredError: false,
      		getCountryInfo,
			currentLocationId: "",
			actualStartTime: moment()
				.startOf("day")
				.set({ hours: 8 }),
			actualEndTime: moment()
				.startOf("day")
				.set({ hours: 17 }),
			slotDuration: 30,
			days: [1, 2, 3, 4, 5] as any[],
			campaign: {} as Campaign,
			campaignUsers: [] as string[],
			templates: [] as { [key: string]: any }[],
			tags: [] as string[],
			addActionValues: {
				visible: false
			},
			window: true,
			condition: "when" as "if" | "when" | "wait",
			newStageName: "",
			editEmailEvent: {
				visible: false,
				template: {},
				currentLocationId: "",
				campaignId: "",
			},
			editSMSEvent: {
				visible: false,
				template: {},
				currentLocationId: "",
				campaignId: ""
			},
			editWebhookEvent: {
				visible: false,
						template: {},
						currentLocationId: "",
						campaignId: ""
			},
			editTaskNotificationEvent: {
				visible: false,
						template: {},
						currentLocationId: "",
						campaignId: ""
			},
			editCallEvent: {
				visible: false,
				template: {},
				currentLocationId: "",
				campaignId: ""
			},
			editVoicemailEvent: {
				visible: false,
				template: {},
				currentLocationId: "",
				campaignId: ""
			},
			editWaitEvent: {
				visible: false,
				template: {},
				currentLocationId: "",
				campaignId: ""
			},
			publishing: false,
			actionNoResponse: "",
			leadValue: undefined as number | undefined,
			fromEmailAddress: undefined as string | undefined,
			fromName: undefined as string | undefined,
			pipelineId: "",
			pipelineStageId: "",
			// opportunity: false,
			stages: [] as Stage[],
			allowMultiple: true,
			eventDate: undefined as undefined | string,
			showCopyModal: false as boolean,
			location: {} as Location,
			timezone: getDefaultTZ(),
			dbTemplates: [] as Template[],
			emailTemplates: [] as Template[],
			stopOnReply: false,
			dragging: false,
			showFromWarning: false,
			bus,
			modal: {
				visible: false,
				title: 'Create Campaign',
				type: 'campaigns'
			},
			folders: [] as Folder [],
      folderId: '',
	  getCountryDateFormat,
        fromEmailError: false
		};
	},
	watch: {
		"$route.params.location_id": async function (id) {
			this.$router.replace({
						name: "customer_acquisition",
						params: { location_id: id }
			});
			await this.fetchSyncAll()
		},
		"$route.params.campaign_id": function (id) {
			this.reset();
			this.fetchData();
		},
		pipelineId() {
			const pipeline = lodash.find(this.pipelines, { id: this.pipelineId });
			if (!pipeline) this.stages = [];
			else this.stages = lodash.sortBy(pipeline.stages, ["position"]);
		}
	},
	created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id;
    if(moment(this.opportunitiesFiltersDates.start).isBefore(moment("2020-05-29T12:00:00"))) {
      this.opportunitiesFiltersDates.start = moment("2020-05-29T12:00:00")
    }
		this.fetchData();
	},
	methods: {
		goToPreviousRoute() {
			if (this.getSideBarVersion == 'v2') {
				this.$router.push({
					name: 'campaigns-v2',
					params: {
						location_id: this.currentLocationId
					}
				})
			} else {
				this.$router.go(-1)
			}
		},
		async fetchSyncAll() {
			await Promise.all([
				this.$store.dispatch('pipelines/syncAll', this.$route.params.location_id),
				this.$store.dispatch('campaigns/syncAll', this.$route.params.location_id)
			])
		},
		async validateFromEmail(event) {
			const email = this.fromEmailAddress
			const { valid: validEmail } = await this.$validator.verify(email, 'email')
			const { valid: validCustomValue } = await this.$validator.verify(email, 'handlebars')
			this.fromEmailError = email.includes('{') ? !validCustomValue : !validEmail
		},
		setSelectedDate() {
			this.selectedDate = this.opportunitiesFiltersDates
		},
		updateCampaignStatus() {
			if(this.templates.length > 0) {
				this.campaign.publish()
				this.showEventRequiredError = false
			} else {
				this.showEventRequiredError = true
			}
		},
		dragStart(dragResult: any) {
			this.dragging = true;
		},
		dragEnd(dragResult: any) {
			this.dragging = false;
		},
		async onDrop(dropResult: any) {
      		if (dropResult.removedIndex === dropResult.addedIndex) return; //nothing happened return

			this.templates.splice(dropResult.removedIndex, 1);
			this.templates.splice(dropResult.addedIndex, 0, dropResult.payload);
			this.saveTemplates();
		},
		getChildPayload(index: number) {
			return this.templates[index];
		},
		removeTag(tag: string) {
			this.tags.splice(this.tags.indexOf(tag), 1);
		},
		editAction(id: string) {
			const template = <{ [key: string]: any }>(
				lodash.find(this.templates, { id: id })
			);
			if (!template) return;
			if (template.type === "sms" || template.type === "manual-sms") {
				this.editSMSEvent = {
					visible: true,
					template: template,
					currentLocationId: this.currentLocationId,
					campaignId: this.campaign.id,
					eventType: 'SMS'
				};
			} else if (template.type === "messenger") {
				this.editSMSEvent = {
					visible: true,
					template: template,
					currentLocationId: this.currentLocationId,
					campaignId: this.campaign.id,
					eventType: 'Facebook Message'
				};
			} else if (template.type === "email") {
				this.editEmailEvent = {
					visible: true,
					template: template,
					currentLocationId: this.currentLocationId,
          			campaignId: this.campaign.id,

				};
			} else if (template.type === "call") {
				this.editCallEvent = {
					visible: true,
					template: template,
					currentLocationId: this.currentLocationId,
					campaignId: this.campaign.id
				};
			} else if (template.type === "voicemail") {
				this.editVoicemailEvent = {
					visible: true,
					template: template,
					currentLocationId: this.currentLocationId,
					campaignId: this.campaign.id
				};
			} else if (template.type === "wait") {
				this.editWaitEvent = {
					visible: true,
					template: template,
					currentLocationId: this.currentLocationId,
					campaignId: this.campaign.id
				};
			} else if (template.type === "webhook") {
				this.editWebhookEvent = {
					visible: true,
					template: template,
					currentLocationId: this.currentLocationId,
					campaignId: this.campaign.id
				};
			} else if (template.type === "task-notification") {
				this.editTaskNotificationEvent = {
					visible: true,
					template: template,
					currentLocationId: this.currentLocationId,
					campaignId: this.campaign.id
				};
			} else if (template.type === "gmb") {
				this.editSMSEvent = {
					visible: true,
					template: template,
					currentLocationId: this.currentLocationId,
					campaignId: this.campaign.id,
					eventType: 'GMB Message'
				};
			}
		},
		async removeAction(id: string) {
			const template = <{ [key: string]: any }>(
				lodash.find(this.templates, { id: id })
      		);
			if (template)
			  	this.templates.splice(this.templates.indexOf(template), 1);
			this.saveTemplates();
			try {
				const response = await this.$http.post(`/campaigns/${this.campaign.id}/template/${template.id}/delete`, template);
			} catch (err) {
				console.error(err);
			}
		},
		updateAction(id: string, startAfter: { [key: string]: any }) {
			const template = <{ [key: string]: any }>(
				lodash.find(this.templates, { id: id })
			);
			if (template)
				template.start_after = startAfter;
			this.saveTemplates();
		},
		// sort() {
		// 	this.templates = this.sortedTemplates;
    // },
    async refreshAllStats(){
      this.$root.$emit('refresh_campaign_stats', this.campaign && this.campaign.id);
    },
		addAction(type: string) {
			let count =
				lodash.size(lodash.filter(this.templates, { type: type })) + 1;
			let name = "";
			let attributes;
			if (type === "sms") {
				name = "SMS ";
				attributes = { body: "" };
      		} else if (type === "manual-sms") {
				name = "Manual SMS ";
				attributes = { body: "" };
			} else if (type === "email") {
				name = "Email ";
				attributes = { subject: "" };
			} else if (type === "call") {
				name = "Call ";
				attributes = {};
			} else if (type === "manual-call") {
				name = "Manual Call ";
				attributes = {};
			} else if (type === "voicemail") {
				name = "Voicemail ";
				attributes = {};
			} else if (type === "wait") {
				name = "Wait ";
				attributes = {};
			} else if (type === "messenger") {
				name = "Messenger ";
				attributes = {};
			} else if (type === "webhook") {
				name = "Webhook ";
				attributes = {};
			} else if (type === "task-notification") {
				name = "Add Task ";
				attributes = {};
			} else if (type === 'gmb') {
        name = "GMB message";
        attributes = { body: "" };
      }
			name += count;
			const id = uuid();
			this.templates.push({
				type: type,
				id: id,
				name: name,
				attributes: attributes,
				start_after: {
					value: 0,
					type: 'minutes',
					when: 'after'
				},
				window: {}
			});
			this.saveTemplates();
			this.editAction(id);
		},
		reset() {
			const data: (() => object) = <(() => object)>this.$options.data;
			if (data) Object.assign(this.$data, data.apply(this));
		},
		resetConfig() {
			const element = <HTMLElement>this.$refs.configSection;
			if (element) element.click();
			this.actualStartTime = moment()
				.startOf("day")
				.set({ hours: 8 });
			this.actualEndTime = moment()
				.startOf("day")
				.set({ hours: 17 });
			this.days = [1, 2, 3, 4, 5];
			this.setConfig();
		},
		async fetchData() {
			const campaignId = this.$router.currentRoute.params.campaign_id;
			await this.fetchSyncAll()

			this.location = new Location(await this.$store.dispatch('locations/getById', this.currentLocationId));
			if (this.location.defaultEmailService && this.location.defaultEmailService !== 'mailgun') {
				this.showFromWarning = await this.$store.dispatch('defaultEmailService/checkFromVisibility', this.location.defaultEmailService);
      		}
			this.timezone = await this.location.getTimeZone();
			if (campaignId === 'new') {
				this.campaign = new Campaign();
				this.campaign.locationId = this.currentLocationId;
				// this.campaign.name = "Campaign " + (this.campaigns.length + 1);
				this.campaign.name = this.$router.currentRoute.params.campaign_name
				this.campaign.folderId = this.$router.currentRoute.params.folder_id
			} else {
				let tmpCampaign = this.campaigns && lodash.find(this.campaigns, { id: campaignId });
				if(tmpCampaign) {
					this.campaign = new Campaign(<Campaign>(
						lodash.cloneDeep(tmpCampaign)
					));
					} else {
					this.campaign = await Campaign.getById(campaignId);
					if (this.campaign && this.campaign.locationId !== this.currentLocationId) {
						this.$router.replace({
							name: "customer_acquisition",
							params: { location_id: this.currentLocationId }
						});
					}
				}
			}
			this.setConfig();
			this.setTemplate();

			if (cancelTemplateSubscription) {
				cancelTemplateSubscription();
			}
			cancelTemplateSubscription = Template.fetchByLocationId(this.location.id).onSnapshot(async (snapshot: firebase.firestore.QuerySnapshot) => {
        	this.dbTemplates = snapshot.docs.map(d => new Template(d));

				if (this.dbTemplates && this.dbTemplates.length > 0) {
					for (let template of this.dbTemplates) {
						if (template.type == Template.TYPE_EMAIL) {
							this.emailTemplates.push(template)
						}
					}
				}
			});
			// if (cancelStatusSubscription) {
			// 	cancelStatusSubscription();
			// }
			// cancelStatusSubscription = CampaignStatus.fetchByCampaignIdRealtime(campaignId).limit(1).onSnapshot(async (snapshot: firebase.firestore.QuerySnapshot) => {
			// 	refreshCount();
      // });

		},
		async setConfig() {
			const window = this.campaign.campaignData.window;
			if (window && !lodash.isEmpty(window)) {
				this.window = true;
				this.days = window.days;
				this.condition = window.condition;
				const startParts = window.start.split(":");
				this.actualStartTime = moment()
					.startOf("day")
					.set({
						hours: parseInt(startParts[0]),
						minutes: parseInt(startParts[1])
          });

				const endParts = window.end.split(":");
				this.actualEndTime = moment()
					.startOf("day")
					.set({
						hours: parseInt(endParts[0]),
						minutes: parseInt(endParts[1])
					});
			} else {
				this.window = false;
			}
			this.campaignUsers = lodash.clone(this.campaign.campaignData.users || []);
			const noResponse = this.campaign.campaignData.no_response;
			if (
				noResponse &&
				noResponse.action_type === "campaign" &&
				noResponse.campaign_id
			) {
				this.actionNoResponse = noResponse.campaign_id;
      }
			if (this.campaign.campaignData.lead_value) {
				this.leadValue = this.campaign.campaignData.lead_value;
			}
			if (this.campaign.campaignData.from_email_address) {
				this.fromEmailAddress = this.campaign.campaignData.from_email_address;
			}
			if (this.campaign.campaignData.from_name) {
				this.fromName = this.campaign.campaignData.from_name;
			}
			this.tags = this.campaign.tagContacts;
			// if (!lodash.isEmpty(this.campaign.campaignData.responded)) {
			// 	const trigger = await Trigger.getById(
			// 		this.campaign.campaignData.responded[0]
			// 	);
			// 	const action = <CreateOpportunity>trigger.actions[0];
			// 	this.pipelineId = action.pipeline_id;
			// 	this.pipelineStageId = action.pipeline_stage_id;
			// 	this.opportunity = true;
			// }
			if (this.campaign.allowMultiple !== undefined) {
				this.allowMultiple = this.campaign.allowMultiple;
			}
			if (this.campaign.stopOnReply !== undefined) {
				this.stopOnReply = this.campaign.stopOnReply;
					}

			if (this.campaign.campaignData.event_time) {
				this.eventDate = moment.tz(this.campaign.campaignData.event_time, this.timezone).format("YYYY-MM-DD HH:mm:ss");;
			}
		},
		setTemplate() {
			this.templates = this.campaign.campaignData.templates || [];
		},
		userSelected(e: any) {
			let userId = e.target.value;
			e.target.value = "";
			if (userId) {
				this.campaignUsers.push(userId);
			}
		},
		removeUser(userId: string) {
			this.campaignUsers.splice(this.campaignUsers.indexOf(userId), 1);
		},
		getUser(userId: string) {
			return lodash.find(this.users, { id: userId });
		},
		// async publish() {
		// 	this.publishing = true;
		// 	await this.campaign.publish();
		// 	this.publishing = false;
		// },
		async saveConfig() {
			const result = await this.$validator.validateAll();
			if (!result || this.fromEmailError) {
				return false;
			}
			const element = <HTMLElement>this.$refs.configSection;
			if (element) element.click();
			if (this.window) {
				this.campaign.campaignData.window = {
					condition: this.condition,
					days: lodash.sortBy(
						this.days.map(day =>
							typeof day === "string" ? parseInt(day) : day
						)
					),
					start: this.actualStartTime.format("HH:mm"),
					end: this.actualEndTime.format("HH:mm")
				};
			} else if (this.campaign.campaignData.window) {
				delete this.campaign.campaignData.window;
			}
			// if (this.opportunity) {
			// 	if (!lodash.isEmpty(this.campaign.campaignData.responded)) {
			// 		const trigger = await Trigger.getById(
			// 			this.campaign.campaignData.responded[0]
			// 		);
			// 		const action = <CreateOpportunity>trigger.actions[0];
			// 		action.pipeline_id = this.pipelineId;
			// 		action.pipeline_stage_id = this.pipelineStageId;
			// 		await trigger.save();
			// 	} else {
			// 		const trigger = await Trigger.createOpportunityOnResponse(
			// 			this.currentLocationId,
			// 			this.campaign.id,
			// 			this.pipelineId,
			// 			this.pipelineStageId
			// 		);
			// 		this.campaign.campaignData.responded = [trigger.id];
			// 	}
			// } else {
			// 	if (!lodash.isEmpty(this.campaign.campaignData.responded)) {
			// 		const trigger = await Trigger.getById(
			// 			this.campaign.campaignData.responded[0]
			// 		);
			// 		trigger.deleted = true;
			// 		await trigger.save();
			// 		delete this.campaign.campaignData.responded;
			// 	}
			// }
			if (this.leadValue) {
				this.campaign.campaignData.lead_value = this.leadValue;
			} else {
				delete this.campaign.campaignData.lead_value;
			}
			if (this.fromEmailAddress) {
				this.campaign.campaignData.from_email_address = this.fromEmailAddress;
			} else {
				delete this.campaign.campaignData.from_email_address;
			}
			if (this.fromName) {
				this.campaign.campaignData.from_name = this.fromName;
			} else {
				delete this.campaign.campaignData.from_name;
			}
			this.campaign.allowMultiple = this.allowMultiple;
			this.campaign.stopOnReply = this.stopOnReply;
			if (this.eventDate) {
				this.campaign.campaignData.event_time = moment(this.eventDate, ['YYYY-MM-DD hh:mm a']).tz(this.timezone, true).format();
			} else {
				delete this.campaign.campaignData.event_time;
			}
			console.log(this.campaignUsers);
			this.campaign.campaignData.users = this.campaignUsers;
			if (this.actionNoResponse) {
				this.campaign.campaignData.no_response = {
					action_type: "campaign",
					campaign_id: this.actionNoResponse
				};
			} else if (this.campaign.campaignData.no_response) {
				delete this.campaign.campaignData.no_response;
			}
      this.campaign.tagContacts = this.tags;
			await this.campaign.save();
		},
    async restore() {
      this.campaign.deleted = false;
      await this.campaign.save();
    },
		async saveTemplates() {
			const now = moment().startOf("day");
			const templates = this.templates;
			for (let i = 0; i < templates.length; i++) {
        		const template = templates[i];

				if (i < templates.length - 1) {
					const nextTemplate = templates[i + 1];
					template.next = nextTemplate.id;
				} else {
					delete template.next;
				}
			}
			this.campaign.campaignData.templates = JSON.parse(
				JSON.stringify(templates)
			);
			await this.campaign.save();
		},
		copyCampaign() {
			this.showCopyModal = true;
    },
		saveName(){
			try {
				this.campaign.save();
			}catch(err){
				this.uxmessage(UxMessage.errorType(err));
			}
		}
	},
	computed: {
    isInternalUser() {
      return Boolean(this.$store.state.user?.internalUser);
    },
		users() {
		return this.$store.state.users.users.map(u => new User(u))
		},
		pipelines() {
		return this.$store.state.pipelines.pipelines
		},
		campaigns() {
		return this.$store.state.campaigns.campaigns
    },
		filteredCampaigns() {
			if (!this.campaign) return [];
			return this.campaigns.filter(c => c.id !== this.campaign.id);
		},
		// sortedTemplates() {
		// 	const now = moment();
		// 	return lodash.sortBy(this.templates, template => {
		// 		let time = now.clone();
		// 		if (!lodash.isEmpty(template.start_after)) {
		// 			if (template.start_after.when === "now")
		// 				time = now.clone().subtract(1, "year");
		// 			else if (template.start_after.when === "before")
		// 				time = now
		// 					.clone()
		// 					.subtract(template.start_after.value, template.start_after.type);
		// 			else
		// 				time = now
		// 					.clone()
		// 					.add(template.start_after.value, template.start_after.type);
		// 		}
		// 		return time;
		// 	});
		// },
		filteredUsers() {
			return lodash.filter(
				this.users,
				user => this.campaignUsers.indexOf(user.id) === -1
			);
		},
		startTime: {
			get: function (): string {
				return this.actualStartTime.format();
			},
			set: function (time: string) {
				this.actualStartTime = moment(time);
			}
		},
		endTime: {
			get: function (): string {
				return this.actualEndTime.format();
			},
			set: function (time: string) {
				this.actualEndTime = moment(time);
			}
		},
		startSlots(): moment.Moment[] {
			const times = [];
			let start = moment().startOf("day");
			let end = moment()
				.startOf("day")
				.add(1, "day");
			for (; start.isBefore(end); start.add(this.slotDuration, "minutes")) {
				times.push(start.clone());
			}
			return times;
		},
		endSlots(): moment.Moment[] {
			const times = [];
			let start = moment().startOf("day");
			let end = moment()
				.startOf("day")
				.add(1, "day");
			for (; start.isBefore(end); start.add(this.slotDuration, "minutes")) {
				times.push(start.clone());
			}
			return times;
		},
		allowEdit(): boolean {
			return this.campaign.deleted === false && !this.campaign.loopIdentified && this.user && this.user.permissions.campaigns_read_only !== true;
		},
		...mapState('user', {
			user: (s: UserState) => {
				return s.user ? new User(s.user) : undefined;
			},
    	}),
		getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion']
		},
	},
	updated() {
		const $selectpicker = $(this.$el).find(".selectpicker");
		if ($selectpicker) {
			$selectpicker.selectpicker("refresh");
		}
	},
	mounted() {
		const $selectpicker = $(this.$el).find(".selectpicker");
		if ($selectpicker) {
			$selectpicker.selectpicker("refresh");
		}
	},
	beforeDestroy() {
		if (cancelTemplateSubscription) cancelTemplateSubscription();
		if (cancelStatusSubscription) cancelStatusSubscription();
	},
});
</script>

<style scoped>
.sort {
	margin-left: 20px;
	margin-bottom: 10px;
	cursor: pointer;
}

.form-control:disabled,
.form-control[readonly] {
	background-color: #f7fafc;
}
.vdp-datepicker__calendar .cell:not(.blank):not(.disabled).day:hover,
.vdp-datepicker__calendar .cell:not(.blank):not(.disabled).month:hover,
.vdp-datepicker__calendar .cell:not(.blank):not(.disabled).year:hover {
	border: 1px solid #188bf6 !important;
}
.vdp-datepicker__calendar .cell.selected,
.vdp-datepicker__calendar .cell.selected.highlighted,
.vdp-datepicker__calendar .cell.selected:hover {
	background: #188bf6 !important;
	color: white;
}
#back {
  margin-right: 10px;
  cursor: pointer;
  color: #188bf6;
}
.campaign_new_header .hl_datepicker {
    background: rgba(24, 139, 246, 0.1);
    padding: 0px 10px;
    border-radius: 0.3125rem;
    -webkit-transition: all 0.2s ease-in-out 0s;
    transition: all 0.2s ease-in-out 0s;
    cursor: pointer;
	margin-right: 0;
	height: 40px;
}
</style>

