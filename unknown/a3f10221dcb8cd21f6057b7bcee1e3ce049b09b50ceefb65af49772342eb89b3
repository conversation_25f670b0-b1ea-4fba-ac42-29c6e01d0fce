<template>
  <div class="hl_saas--services">
    <div class="card">
      <div class="card-header">
        <div class="title">
          <h3>{{TwilioTelephonyCopy}} Resell Settings</h3>
          <p>
            This allows you to rebill {{TwilioTelephonyCopy}} costs to your clients and include a
            markup.
            <a
              href="https://help.gohighlevel.com/support/solutions/articles/***********-activate-saas-mode-request-payment-and-configure-twilio-rebilling"
              target="_blank"
              style="font-weight: 500"
            >
              See how <i class="fas fa-external-link-alt"></i
            ></a>
          </p>
        </div>
        <div
          v-if="canUpdate && !(updating || existingTwilioAccount)"
          class="toggle"
        >
          <span>Enable</span>
          <UIToggle
            id="twilio_rebilling"
            v-model="rebillingEnabled"
          />
          <label class="tgl-btn" for="twilio_rebilling"></label>
        </div>
        <div v-else-if="!existingTwilioAccount">
          <span>
            {{ updating ? 'Updating..' : 'Setup Pending' }}
          </span>
        </div>
      </div>
      <div class="card-body" :class="[!rebillingEnabled ? 'disabled' : '']">
        <div v-if="!rebillingEnabled || !canUpdate" class="mask"></div>
        <div v-if="existingTwilioAccount" class="mask existing-twilio-account">
          <h4>
            <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
            {{
              existingTwilioAccount.forceNew
                ? 'Multiple locations using same twilio account'
                : 'A twilio account is already setup for this location'
            }}
          </h4>
          <div class="details">
            <p>
              <span class="label">Account SID:</span>
              <span class="data">{{ existingTwilioAccount.accountSID }}</span>
            </p>
            <p>
              <span class="label">Token:</span>
              <span class="data">{{ existingTwilioAccount.token }}</span>
            </p>
          </div>
          <div class="controls" v-if="!updating">
            <p>
              {{
                existingTwilioAccount.forceNew
                  ? 'A new twilio account needs to be created before we can start rebilling'
                  : 'Do you want to keep the same or create a new one?'
              }}
            </p>
            <div v-if="!existingTwilioAccount.forceNew" class="radio-controls">
              <div class="radio">
                <input
                  type="radio"
                  name="use-existing"
                  id="use-existing-twilio"
                  @change="createNewTwilioSubAccount = false"
                  v-model="createNewTwilioSubAccount"
                  :value="false"
                />
                <label for="use-existing-twilio">
                  <!-- <p>This is my Twilio</p> -->
                  If you pay for this Twilio subaccount, you can keep using the
                  same one.
                </label>
              </div>
              <div class="radio">
                <input
                  type="radio"
                  name="create-new"
                  id="create-new-twilio"
                  @change="createNewTwilioSubAccount = true"
                  v-model="createNewTwilioSubAccount"
                  :value="true"
                />
                <label for="create-new-twilio">
                  <!-- <p>This is my client's Twilio</p> -->
                  If your client pays for this Twilio subaccount, you'll need to
                  create a new twilio subaccount and move phone numbers.
                  <a
                    target="_blank"
                    href="https://help.gohighlevel.com/support/solutions/articles/***********-move-twilio-numbers-from-one-twilio-account-to-another"
                  >
                    Learn How
                  </a>
                </label>
              </div>
            </div>
            <p
              v-if="existingTwilioAccount.forceNew || createNewTwilioSubAccount"
            >
              You'll need to create a new twilio account, update the credentials
              for this account under Twilio settings and move phone numbers
              before enabling rebilling.<br />
              <a
                target="_blank"
                href="https://help.gohighlevel.com/support/solutions/articles/***********-move-twilio-numbers-from-one-twilio-account-to-another"
              >
                Learn How to move phone numbers
              </a>
            </p>
            <button
              v-else
              class="btn"
              :class="createNewTwilioSubAccount ? 'create-new' : 'existing'"
              @click="
                $emit('replaceExistingAccount', createNewTwilioSubAccount)
              "
            >
              {{
                createNewTwilioSubAccount
                  ? existingTwilioAccount.forceNew ||
                    "This is my client's Twilio, create new"
                  : 'This is my Twilio, use same'
              }}
            </button>
          </div>
          <p v-else><i>Just one sec...</i></p>
        </div>
        <span class="description"
          >These are margins that are billed to your clients over the {{TwilioTelephonyCopy}}
          cost borne by you</span
        >
        <div class="profit-level">
          <h6 class="label">Rebill Amount</h6>
          <div class="slider">
            <input
              v-if="rebillingEnabled"
              v-model="markup"
              @change="unsavedChanges = true"
              type="range"
              id="markup"
              name="markup"
              :min="billRange.min"
              :max="billRange.max"
              :step="billRange.step"
            />
            <input
              v-else
              disabled
              :value="0"
              min="0"
              max="100"
              step="5"
              type="range"
              name="markup"
            />
            <span
              class="value"
              :style="{ left: `calc(${markup * 10}% - 30px)` }"
              >{{ markup }}x</span
            >
            <span>1x</span>
            <span>10x</span>
          </div>
        </div>

        <div class="profit-boxes">
          <div class="label boxes">
            <p class="title">DO SOMETHING</p>
            <p><sup>*</sup>{{TwilioTelephonyCopy}} Charge</p>
            <p :style="{ lineHeight: '16px' }">
              Charged to<br />
              customer
            </p>
            <p>${{ DOLLAR_AMOUNT }} will give you about</p>
            <p class="profit">Profit</p>
          </div>
          <div class="boxes">
            <p class="title">Making calls</p>
            <p>$&nbsp;{{ twilioCharge.outgoingCall }}&nbsp;/&nbsp;min</p>
            <p>$&nbsp;{{ outgoingCallsCharge }}&nbsp;/&nbsp;min</p>
            <p>
              {{ countEstimateFromDollarAmount(outgoingCallsCharge) }} calls
            </p>
            <p class="profit">$&nbsp;{{ outgoingCallsProfit }}</p>
          </div>
          <div class="boxes">
            <p class="title">Receiving calls</p>
            <p>$&nbsp;{{ twilioCharge.incomingCall }}&nbsp;/&nbsp;min</p>
            <p>$&nbsp;{{ incomingCallsCharge }}&nbsp;/&nbsp;min</p>
            <p>
              {{ countEstimateFromDollarAmount(incomingCallsCharge) }} calls
            </p>
            <p class="profit">$&nbsp;{{ incomingCallsProfit }}</p>
          </div>
          <div class="boxes">
            <p class="title">Text Messages</p>
            <p>$&nbsp;{{ twilioCharge.sms }}&nbsp;/&nbsp;sms</p>
            <p>$&nbsp;{{ smsCharge }}&nbsp;/&nbsp;sms</p>
            <p>{{ countEstimateFromDollarAmount(smsCharge) }} messages</p>
            <p class="profit">$&nbsp;{{ smsProfit }}</p>
          </div>
        </div>
        <div class="save-changes">
          <div class="warning">
            <i>
              Modifying these settings will change the cost incurred by your
              clients going forward.
              <br />
              Please do not modify these if you aren't sure of its effects.
              <br />
              These settings will apply to your new clients going forward
            </i>
          </div>
          <i class="reminder" v-if="unsavedChanges">Unsaved Changes</i>
          <UIButton
            @click="updateRebillingSettings"
            :disabled="updating"
            :class="unsavedChanges ? 'unsaved' : ''"
          >
            {{ updating ? 'Saving..' : 'Save' }}
          </UIButton>
        </div>
        <div class="tc">
          <sup>*</sup>Rates may differ as per region. Calculations shown as per
          twilio rates for US, please check twilio for more details.
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
// import { debounce } from 'lodash'
import TwilioTelephonyCopyMixin from "@/pmd/components/isv/agency/TwilioTelephonyCopyMixin.js";
export default Vue.extend({
  mixins: [TwilioTelephonyCopyMixin],
  props: {
    settings: Object,
    canUpdate: {
      type: Boolean,
      default: false,
    },
    updating: {
      type: Boolean,
      default: false,
    },
    existingTwilioAccount: {
      type: Object,
      default: null,
    },
  },
  mounted() {
    if (this.settings && this.settings.markup) {
      this.markup = this.computeMarkupAmountFromPercent(this.settings.markup)
    }
  },
  computed: {
    company() {
      return this.$store.state.company.company
    },
    markupPercent(): number {
      const markupAmount = parseFloat(this.markup)
      if (markupAmount === 1) {
        return 5
      }

      // if markup is set to 2.5 ie. 2.5x
      // then markup % would be 150
      return markupAmount * 100 - 100
    },
    incomingCallsProfit(): string {
      return this.computeMarkupProfit(this.twilioCharge.incomingCall)
      // % of markup * actual charge --> 10% of incoming call charge
    },
    outgoingCallsProfit(): string {
      return this.computeMarkupProfit(this.twilioCharge.outgoingCall)
    },
    smsProfit(): string {
      return this.computeMarkupProfit(this.twilioCharge.sms)
    },
    incomingCallsCharge(): string {
      return this.readableFloat(
        parseFloat(this.incomingCallsProfit) + this.twilioCharge.incomingCall
      )
    },
    outgoingCallsCharge(): string {
      return this.readableFloat(
        parseFloat(this.outgoingCallsProfit) + this.twilioCharge.outgoingCall
      )
    },
    smsCharge(): string {
      return this.readableFloat(
        parseFloat(this.smsProfit) + this.twilioCharge.sms
      )
    },
    rebillingEnabled: {
      get(): boolean {
        return this.settings && this.settings.enabled
      },
      set(value: boolean) {
        const updatedSettings = {
          enabled: value,
          markup: this.markupPercent,
          companyId: this.company.id,
        }
        this.emitUpdatedSettings(updatedSettings)
      },
    },
  },
  data() {
    return {
      markup: '1',
      billRange: {
        min: 1,
        max: 10,
        step: 0.1,
      },
      unsavedChanges: false,
      twilioCharge: {
        incomingCall: 0.0085,
        outgoingCall: 0.013,
        sms: 0.0075,
      },
      createNewTwilioSubAccount: true,
      DOLLAR_AMOUNT: 10,
    }
  },
  methods: {
    countEstimateFromDollarAmount(charge: number): number {
      const count = Math.round(this.DOLLAR_AMOUNT / charge)
      return Math.round(count / 5) * 5
    },
    computeMarkupAmountFromPercent(markupPercent: number): string {
      return `${(markupPercent * 100) / 10000 + 1}`
    },
    computeMarkupProfit(charge: number): string {
      const profit = parseFloat(
        `${((this.markupPercent * 100) / 10000) * charge}`
      )
      return this.readableFloat(profit)
    },
    readableFloat(amount: number): string {
      return parseFloat(amount.toFixed(4)).toString()
    },
    updateRebillingSettings() {
      if (!this.unsavedChanges || !this.rebillingEnabled) return

      const updatedSettings = {
        enabled: this.rebillingEnabled,
        markup: this.markupPercent,
        companyId: this.company.id
      }

      this.emitUpdatedSettings(updatedSettings)
    },
    emitUpdatedSettings(updatedSettings: any) {
      this.$emit('update', updatedSettings)
      this.unsavedChanges = false
    },
  },
  watch: {
    settings: function (newValue) {
      if (newValue.markup) {
        this.markup = this.computeMarkupAmountFromPercent(newValue.markup)
      }
    },
  },
})
</script>
<style scoped>
.card-header {
  display: flex;
  flex-wrap: nowrap;
  justify-items: space-between;
  padding: 25px 30px;
}

.card-header .title {
  max-width: 70%;
}

.card-header h3 {
  margin-bottom: 12px;
}

.card-header p {
  line-height: 16px;
  font-size: 12px;
}

.card-header .toggle {
  display: flex;
  align-items: center;
}

.card-header .toggle span {
  margin-right: 12px;
}

.card-body > span.description {
  color: lightslategray;
  font-size: 14px;
}

.card-body {
  position: relative;
  z-index: 1;
}

/* .card-body.disabled * {
  color: #4A5568 !important;
} */

.card-body .mask {
  position: absolute;
  width: 100%;
  height: 100%;
  background: lightgray;
  z-index: 2;
  top: 0;
  left: 0;
  cursor: not-allowed;
  opacity: 0.5;
}

.profit-level {
  display: flex;
  margin: 30px 0 20px 0;
  justify-content: space-between;
}

.profit-level h6.label {
  margin: 0;
  /* margin-top: 5px; */
  font-weight: bold;
}

.profit-level > span {
  width: 100px;
}

.profit-level .slider {
  width: calc(100% - 100px);
  position: relative;
}

.profit-level .slider input {
  width: 100%;
}

.profit-level .slider span:last-child {
  float: right;
}

.profit-level .slider .value {
  text-align: center;
  position: absolute;
  left: 0;
  top: -25px;
  background: #2d3748;
  border-radius: 2px;
  color: white;
  padding: 3px;
  border-radius: 2px;
  font-size: 12px;
  line-height: 16px;
  width: 40px;
  transition: opacity 0.25s ease-in;
  opacity: 0;
}

#markup:focus + span.value {
  opacity: 1;
}

.profit-level .slider .value:before {
  content: '';
  width: 0px;
  height: 0px;
  position: absolute;
  border-left: 5px solid #2d3748;
  border-right: 5px solid transparent;
  border-top: 5px solid #2d3748;
  border-bottom: 5px solid transparent;
  left: calc(50% - 5px);
  top: 14px;
  transform: rotate(225deg);
}

.profit-boxes {
  display: flex;
  justify-content: flex-end;
}

.profit-boxes .boxes {
  border: 1px solid;
  border-radius: 3px;
  text-align: center;
  padding: 12px 6px;
  margin-right: 6px;
  font-size: 13px;
}

.profit-boxes .boxes:last-child {
  margin-right: 0;
}

.profit-boxes .boxes.label {
  border-style: none;
  text-align: right;
  padding-right: 12px;
}

.profit-boxes .boxes.label .title {
  color: transparent !important;
}

.boxes .title {
  font-weight: bold;
  margin-bottom: 24px;
  text-transform: uppercase;
}

.boxes.label p {
  padding-bottom: 9px;
}

.boxes p {
  margin: 0;
  padding-bottom: 12px;
  white-space: nowrap;
  color: #4a5568;
}

.boxes p.profit {
  font-weight: bold;
  color: #38b2ac;
  padding-bottom: 0;
}

.save-changes {
  text-align: right;
}

.save-changes .warning {
  color: #fd9725;
  margin: 12px 0;
  font-size: 10px;

}

.save-changes .reminder {
  font-size: 16px;
}

.save-changes button {
  margin-left: 12px;
  background: #e2e8f0;
  color: #4a5568;
  padding: 8px 12px;
  border-radius: 3px;
}

.save-changes button.unsaved {
  background: #178af6;
  color: white;
}

.tc {
  font-style: italic;
  font-size: 11px;
  margin-top: 12px;
}

#markup {
  -webkit-appearance: none;
}

#markup:focus {
  outline: none;
}

#markup::-moz-range-thumb {
  border: 6px solid #38b2ac;
  height: 10px;
  width: 20px;
  border-radius: 25px;
  background: #b2f5ea;
}

input[type='range']#markup::-webkit-slider-thumb {
  -webkit-appearance: none !important;
  border: 6px solid #38b2ac;
  height: 19px;
  width: 26px;
  border-radius: 25px;
  background: #b2f5ea;
  margin-top: -8px;
}

#markup::-webkit-slider-runnable-track {
  height: 6px;
  cursor: pointer;
  background: #38b2ac;
  border-radius: 17px;
  border: 0.2px solid #38b2ac;
}

#markup::-moz-range-track {
  height: 6px;
  cursor: pointer;
  border-radius: 17px;
}

input[type='range']#markup::-moz-range-progress {
  background: #38b2ac;
  height: 6px;
  border-radius: 17px;
}
input[type='range']#markup::-moz-range-track {
  background: #e5e7e6;
}

.existing-twilio-account.mask {
  opacity: 1;
  text-align: center;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: rgba(235, 239, 242, 0.85);
}

/* .existing-twilio-account div {
  margin-top: 18px;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
} */

.existing-twilio-account h4 {
  color: #f59e0b;
  font-size: 18px;
  font-weight: bold;
}

.existing-twilio-account .details {
  background: rgba(255, 255, 255, 0.75);
  border: 1px solid rgba(0, 0, 0, 0.03);
  padding: 12px;
  margin-top: 18px;
  margin-bottom: 66px;
}

.existing-twilio-account .details .label {
  color: #6b7280;
  font-size: 13px;
  line-height: 15px;
  margin-right: 8px;
}

.existing-twilio-account .details .data {
  background: #f3f4f6;
  border-radius: 3px;
  color: #9ca3af;
  font-size: 14px;
  line-height: 16px;
  padding: 3px 5px;
}

.existing-twilio-account .controls .radio-controls {
  text-align: left;
}

.existing-twilio-account .controls p {
  color: #1f2937;
  font-size: 13px;
  margin-bottom: 14px;
}

.existing-twilio-account button {
  padding: 16px;
  border-radius: 3px;
  font-size: 14px;
  line-height: 16px;
}

.existing-twilio-account button.existing {
  background: #f59e0b;
  color: #374151;
  margin-right: 14px;
}

.existing-twilio-account button.create-new {
  background: #bfdbfe;
  color: #3b82f6;
}
</style>
