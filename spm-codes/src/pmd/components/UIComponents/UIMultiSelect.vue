<template>
  <div class="hl-multi-select-label-container">
    <span
      v-if="label"
      :for="name"
      class="hl-multi-select-label block text-sm font-medium text-gray-700 mb-1"
      >{{ label }}
    </span>
    <multiselect
      class="hl-multi-select"
      :name="name"
      :value="value"
      :searchable="searchable"
      :options="options"
      :multiple="multiple"
      :close-on-select="closeOnSelect"
      :placeholder="placeholder"
      :allow-empty="allowEmpty"
      track-by="value"
      label="label"
      :deselect-label="deselectLabel"
      @input="val => this.$emit('input', val)"
      :disabled="disabled"
      :loading="loading"
    >
      <span slot="noResult"
        >Oops! No elements found. Consider changing the search query.</span
      >
    </multiselect>
    <p v-if="description" class="mt-2 text-xs text-gray-500">
      {{ description }}
    </p>
    <p
      v-if="errorMsg && error"
      class="mt-2 text-sm text-red-600"
      id="email-error"
    >
      {{ errorMsg }}
    </p>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import Multiselect from 'vue-multiselect'
import 'vue-multiselect/dist/vue-multiselect.min.css'
export default Vue.extend({
  components: {
    Multiselect,
  },
  props: {
    description: {
      type: String,
      default: '',
    },
    error: {
      type: Boolean,
      default: false,
    },
    errorMsg: {
      type: String,
      default: '',
    },
    label: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '',
    },
    value: {
      type: [Object, Array, String, Number],
      default: function () {
        return []
      },
    },
    options: {
      type: Array,
    },
    placeholder: {
      type: String,
      default: 'Type to search',
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    searchable: {
      type: Boolean,
      default: true,
    },
    closeOnSelect: {
      type: Boolean,
      default: false,
    },
    allowEmpty: {
      type: Boolean,
      default: true,
    },
    deselectLabel: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
})
</script>

<style>
input.multiselect__input:focus {
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
  --tw-ring-color: none;
  --tw-ring-shadow: none;
}
</style>
