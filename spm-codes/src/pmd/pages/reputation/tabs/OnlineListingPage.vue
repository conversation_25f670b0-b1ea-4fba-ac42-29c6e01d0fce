<template>
  <!--  -->
  <section
    v-if="user && user.permissions.online_listings_enabled"
    class="hl_wrapper"
  >
    <section v-if="yextId" id="online-listing">
      <template v-if="loading">
        <div class="loader-backdrop">
          <moon-loader :loading="loading" color="#188bf6" size="30px" />
        </div>
      </template>

      <template
        v-if="
          !loading &&
          listings === null &&
          location.yextReseller &&
          (location.yextReseller.status === 'COMPLETE' ||
            location.yextReseller.status === 'SUBMITTED')
        "
      >
        <div class="loader-backdrop">
          <div class="wait_on_">
            <h2>Sit back and relax 🏝️</h2>
            <p>
              YEXT is working on your listings, it usually takes around 48 Hours
              after submission
            </p>
            <button
              class="btn btn-success hl_calltoaction-btn"
              @click="getYextData"
            >
              Reload Again
            </button>
          </div>
        </div>
      </template>

      <template
        v-else-if="
          !loading &&
          location.yextReseller &&
          listings === null &&
          location.yextReseller.status
        "
      >
        <div class="loader-backdrop">
          <div class="wait_on_">
            <h2>{{ location.yextReseller.status }}</h2>
            <p>
              {{ location.yextReseller.status_message }}
            </p>
            <template v-if="location.yextReseller.status === 'CANCELED' || location.yextReseller.status === 'CANCELLED'">
              <p>
                Yext notified us that listings already exists for this business
                name, phone number & address. Please reach out to
                <a
                  target="_blank"
                  :href="`mailto:<EMAIL>?cc=shivam%40gohighlevel.com&subject=Listing%20stuck%20in%20canceled%20state ${yextId}`"
                  ><EMAIL></a
                >
                resolve this. Your entity id is <b> {{yextId}} </b>
              </p>
            </template>
            <template v-if="location.yextReseller.status === 'REVIEW'">
              <p>
                If your listings stay in review status for more than 5 days,
                please reach out to
                <a
                  target="_blank"
                  :href="`mailto:<EMAIL>?cc=shivam%40gohighlevel.com&subject=Listing%20stuck%20in%20review%20state ${yextId}`"
                  ><EMAIL></a
                >
                resolve this.
              </p>
            </template>
            <button
              class="btn btn-success hl_calltoaction-btn"
              @click="getYextData"
            >
              Reload Again
            </button>
          </div>
        </div>
      </template>
      <template v-if="showManageListingPopup">
        <div class="loader-backdrop">
          <div class="wait_on_ manage_listing_popup">
            <h2>Manage your listing on Yext</h2>
            <div>
              <table>
                <tr>
                  <td>Username</td>
                  <td>
                    <b>{{ company.yextReseller.yext_user_email }}</b>
                  </td>
                </tr>
                <tr>
                  <td>Password</td>
                  <td>
                    <div class="password_div">
                      <div class="password_holder">
                        <span
                          ><b>{{ company.id }}</b></span
                        >
                        <div class="password_copy">
                          <span
                            id="copy-code-wrapper"
                            class="copy-code-wrapper noselect"
                            @click="clipboardCopy(company.id)"
                            v-b-tooltip.hover.right="'Copy password'"
                            ><span></span>
                            <i class="icon-copy-to-clipboard"></i>
                          </span>
                        </div>
                      </div>
                      <div class="password_info">
                        <span>unless changed by you</span>
                      </div>
                    </div>
                  </td>
                </tr>
              </table>
            </div>

            <p>
              If you are not able to login please use forgot password feature in
              yext login page.
            </p>
            <div class="action_btn_holders">
              <button
                class="btn btn-success hl_calltoaction-btn btn-cancel"
                id="open_yext_btn"
                @click.prevent="showManageListingPopup = false"
              >
                Close
              </button>
              <button
                class="btn btn-success hl_calltoaction-btn"
                id="open_yext_btn"
                @click="goToYext"
              >
                Go to Yext Login
              </button>
            </div>
          </div>
        </div>
      </template>

      <div>
        <div class="card">
          <div class="card-header card-header--compact space-between">
            <h2>Listings</h2>
            <template
              v-if="
                company &&
                company.yextReseller.yext_user_id &&
                user &&
                user.type === 'agency'
              "
            >
              <div>
                <UIButton
                  use="primary"
                  @click.prevent="showManageListingPopup = true"
                  id="manage_listing_btn"
                >
                  Manage Listing
                </UIButton>
                <span
                  class="hl_visible_to"
                  v-b-tooltip.hover.bottomleft="
                    'Manage Listing option is only visible to Agency users!'
                  "
                  title="Manage Listing option is only to visible to Agency users"
                >
                  <i class="fa fa-eye" aria-hidden="true"></i>
                </span>
              </div>
            </template>
          </div>
          <div class="card-body">
            <div class="dashboard">
              <div>
                <highcharts
                  :options="chartOptions"
                  :callback="chartLoaded"
                ></highcharts>
              </div>
              <div>
                <div class="partners">
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-google-my-business.svg"
                      style="width: 45px"
                    />
                    <div>Google My Business</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions//medallion-ALEXA.png"
                      style="width: 45px"
                    />
                    <div>Alexa</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-apple.svg"
                      style="width: 45px"
                    />
                    <div>Apple</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-bing.svg"
                      style="width: 45px"
                    />
                    <div>Bing</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-facebook.svg"
                      style="width: 45px"
                    />
                    <div>Facebook</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-yelp.svg"
                      style="width: 45px"
                    />
                    <div>Yelp</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-foursquare.svg"
                      style="width: 45px"
                    />
                    <div>Foursquare</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-superpages.svg"
                      style="width: 45px"
                    />
                    <div>Superpages</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-yahoo.svg"
                      style="width: 45px"
                    />
                    <div>Yahoo!</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-citysearch.svg"
                      style="width: 45px"
                    />
                    <div>Citysearch</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-YPCOM.png"
                      style="width: 45px"
                    />
                    <div>Yellowpages</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-if="false && !yextId" class="card-group">
          <div class="card hl_online-analysis--online-presence-errors">
            <div class="card-header">
              <h2>Online Presence Errors</h2>
            </div>
            <div class="card-body">
              <div class="card-content">
                <div class="card-heading">
                  <!-- <img src="https://www.yextstatic.com/partner/public/images/powerlistings-synced.svg"> -->
                  <h3>{{ yext_error_rate }}</h3>
                  <div class="hl_online-analysis--badge --warning">
                    <i class="icon icon-warning"></i>
                  </div>
                </div>
                <div class="hl_online-analysis--network">
                  <h4>Network Errors</h4>
                  <ul class="network_list list-inline">
                    <li
                      class="list-inline-item"
                      v-for="site_error in yext_first_three"
                      v-bind:key="site_error[0]"
                    >
                      <img
                        style="height: 35px; width: 35px"
                        :src="site_error[0]"
                      />
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div class="card hl_online-analysis--page-speed-score">
            <div class="card-header">
              <h2>Page Speed Score</h2>
            </div>
            <div class="card-body">
              <div class="card-content">
                <div class="card-heading">
                  <h3>
                    <div>{{ speed_score | formatNumber }}</div>
                  </h3>
                  <div class="avatar --sm">
                    <div class="avatar_img --shadow">
                      <img
                        src="../../../../assets/pmd/img/logo-google.png"
                        alt="Google"
                      />
                    </div>
                  </div>
                </div>
                <div class="progress">
                  <div
                    v-if="location"
                    :class="progress_bar_class"
                    role="progressbar"
                    v-bind:style="{ width: speed_score + '%' }"
                    :aria-valuenow="speed_score"
                    aria-valuemin="0"
                    aria-valuemax="100"
                  ></div>
                </div>
                <a
                  style="word-break: break-all"
                  v-if="location"
                  :href="location.website"
                  target="_blank"
                  class="link"
                  >{{ website_link }}</a
                >
              </div>
            </div>
          </div>
          <div class="card hl_online-analysis--competition-leaderboard">
            <div class="card-header">
              <h2>Competition Leaderboard</h2>
            </div>
            <div class="card-body">
              <div class="card-content">
                <div class="card-heading">
                  <h3>
                    <div v-if="scanReport">
                      {{ yext_scan.reviewScorePercentile }}
                      <span>/{{ yext_scan.reviewCountPercentile }}</span>
                    </div>
                  </h3>
                  <div class="hl_online-analysis--badge --star">
                    <i class="icon icon-star-filled"></i>
                  </div>
                </div>
                <p class="search" v-if="location && location.searchTerm">
                  {{ location.searchTerm | toTitleCase }}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div v-if="yextId" class="card hl_online-analysis--table">
          <div class="card-body --no-padding">
            <div class="table-wrap">
              <table class="table table-sort">
                <thead>
                  <tr>
                    <th data-sort="string">
                      Network
                      <!-- <i class="icon icon-arrow-down-1"></i> -->
                    </th>
                    <th data-sort="string">
                      Name
                      <!-- <i class="icon icon-arrow-down-1"></i> -->
                    </th>
                    <th data-sort="string">
                      Address
                      <!-- <i class="icon icon-arrow-down-1"></i> -->
                    </th>
                    <th data-sort="string">
                      Phone
                      <!-- <i class="icon icon-arrow-down-1"></i> -->
                    </th>
                    <th data-sort="float">
                      Reviews
                      <!-- <i class="icon icon-arrow-down-1"></i> -->
                    </th>
                    <th>No.</th>
                    <th data-sort="string">
                      Status
                      <!-- <i class="icon icon-arrow-down-1"></i> -->
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <template v-for="report in yextData">
                    <YextListingRow
                      v-if="!listings && (report.listingUrl || report.brandId)"
                      v-bind:report="report"
                      v-bind:key="report.brandId"
                      v-bind:location="location"
                    />
                  </template>
                  <template v-if="listings && listings.length > 0">
                    <template v-for="listing in listings">
                      <YextListingRow
                        v-if="
                          listings &&
                          (listing.listingUrl || listing.publisherId)
                        "
                        v-bind:yextReport="listing"
                        v-bind:key="listing.publisherId"
                        v-bind:location="location"
                        :yextLocation="yextLocation"
                      />
                    </template>
                  </template>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section v-if="yextId === null">
      <YextShop @statusUpdate="fetchLocationData" />
    </section>
  </section>
</template>

<script lang="ts">
import Vue from 'vue'
import { Location, User, ScanReport, Company } from '@/models'
import ListingHelper from '../../../../util/listing_helper'
import { Chart } from 'highcharts-vue'
import { mapState } from 'vuex'
import { CompanyState } from '@/store/state_models'

const YextListingRow = () =>
  import('../../../components/online_listing/YextListingRow.vue')

const YextShop = () =>
  import('../../../components/online_listing/YextShopComponent.vue')

let location: Location | undefined
let totalCountGraph = 0
export default Vue.extend({
  components: {
    YextListingRow,
    YextShop,
    highcharts: Chart,
  },
  data() {
    return {
      yextId: '',
      loading: false,
      showManageListingPopup: false,
      currentLocationId: '',
      location: {} as Location,
      scanReport: {} as ScanReport | undefined,
      chartOptions: {
        chart: {
          type: 'pie',
          plotBackgroundColor: null,
        },
        credits: {
          enabled: !1,
        },
        title: {
          text: '',
        },
        plotOptions: {
          pie: {
            allowPointSelect: !0,
            cursor: 'pointer',
            center: ['50%', '50%'],
            innerSize: '70%',
            startAngle: 180,
            dataLabels: {
              style: {
                fontWeight: 'normal',
              },
              formatter: function () {
                return this.point.hideLabel
                  ? null
                  : '\x3cb\x3e' +
                      this.point.name +
                      '\x3c/b\x3e ' +
                      (
                        ((this.point.labelValue || this.point.y) /
                          totalCountGraph) *
                        100
                      ).toFixed(2) +
                      '%'
              },
            },
            showInLegend: !0,
          },
        },
        legend: {
          symbolRadius: 3,
          margin: 30,
          squareSymbol: !0,
        },
        series: [
          {
            type: 'pie',
            name: 'Listings',
          },
        ],
        tooltip: {
          valueSuffix: '',
        },
      },
      theChart: {},
      totalCount: 0,
      liveCount: 0,
      processingCount: 0,
      unavCount: 0,
      optedCount: 0,
      listings: [] as { [key: string]: any }[] | undefined,
      yextLocation: null,
    }
  },
  async beforeRouteUpdate(to, from, next) {
    console.log('Going to:', to)
    console.log('Coming from:', from)
    this.currentLocationId = to.params.location_id
    await this.$store.dispatch('reviewAggregate/reset')
    await this.$store.dispatch('reviewRequestAggregate/reset')
    if (this.currentLocationId) {
      this.$store.dispatch(
        'reviewAggregate/fetchThisMonth',
        this.currentLocationId
      )
      this.$store.dispatch(
        'reviewRequestAggregate/fetchThisMonth',
        this.currentLocationId
      )
      this.fetchLocationData()
    }
    next()
  },
  watch: {
    listings() {
      if (this.theChart && this.theChart.series) {
        this.populateChart()
      }
    },
    theChart() {
      if (this.theChart && this.theChart.series) {
        this.populateChart()
      }
    },
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
  },
  async mounted() {
    await this.fetchLocationData()
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
    yext_scan(): { [key: string]: any } {
      if (this.scanReport && this.scanReport.yextData) {
        return JSON.parse(this.scanReport.yextData)
      } else {
        return {}
      }
    },
    yextData(): { [key: string]: any } {
      if (this.yext_scan && this.yext_scan.reports) {
        return this.yext_scan.reports
      } else {
        return []
      }
    },
    yext_errors(): any[] {
      var _self = this
      var errors = [] as any
      if (this.yextData) {
        lodash.each(this.yextData, function (report) {
          var a = []
          a.push(_self.imgUrl(report))
          if (!report.listingFound) {
            return a.push('listingNotFound'), a
          }
          report.matchAddress || a.push('address')
          report.matchName || a.push('name')
          report.matchPhone || a.push('phone')
          if (a.length > 1) {
            errors.push(a)
          }
        })
      }
      return errors
    },
    yext_error_rate() {
      var error_rate = 0
      if (this.yext_errors && this.yextData && this.yextData.length > 0) {
        error_rate = Math.floor(
          (this.yext_errors.length / this.yextData.length) * 100
        )
      }
      return error_rate
    },
    yext_first_three() {
      if (this.yext_errors) {
        return this.yext_errors.slice(0, 3)
      }
    },
    lighthouse_scan(): { [key: string]: any } {
      if (this.scanReport && this.scanReport.lighthouseData) {
        return JSON.parse(this.scanReport.lighthouseData)
      } else {
        return {}
      }
    },
    speed_score() {
      if (
        this.lighthouse_scan &&
        this.lighthouse_scan.audits &&
        this.lighthouse_scan.audits['speed-index-metric']
      ) {
        return this.lighthouse_scan.audits['speed-index-metric']['score']
      } else {
        return 0
      }
    },
    progress_bar_class: function () {
      if (this.speed_score && this.speed_score <= 35) {
        return {
          'progress-bar': true,
          'bg-danger': true,
        }
      } else if (
        this.speed_score &&
        this.speed_score > 35 &&
        this.speed_score <= 85
      ) {
        return {
          'progress-bar': true,
          'bg-warning': true,
        }
      } else if (this.speed_score && this.speed_score > 85) {
        return {
          'progress-bar': true,
          'bg-success': true,
        }
      } else {
        return {
          'progress-bar': true,
        }
      }
    },
    website_link(): string | undefined {
      if (this.location && this.location.website) {
        return this.location.website.slice(
          this.location.website.indexOf('http://www.') != -1 ? 11 : 0
        )
      }
    },
  },

  beforeDestroy() {
    if (this.$private.cancelBusinessListingSubscription) {
      this.$private.cancelBusinessListingSubscription()
    }
  },
  methods: {
    chartLoaded(chart: any) {
      this.theChart = chart
    },
    async fetchLocationData() {
      this.location = new Location(
        await this.$store.dispatch('locations/getCurrentLocation', this.currentLocationId)
      )
      if (this.location.yextReseller.location_id) {
        this.yextId = this.location.yextReseller.location_id
        if (this.location.yextReseller.status != 'COMPLETE') {
          await this.updateListingStatus()
        }
      } else if (this.location.yextId) {
        console.debug(`Legacy yext integration`, this.location.yextId)
        this.yextId = this.location.yextId
      } else {
        this.yextId = null
      }
      try {
        this.scanReport = await ScanReport.getByLocationId(
          this.currentLocationId
        )
      } catch (err) {
        this.scanReport = undefined
      }
      this.getYextData()
    },

    imgUrl(report: any): string | undefined {
      if (report && report.brandId) {
        const listing = ListingHelper.getNetworkInfo(report.brandId)
        if (listing) return listing.imgPath
      }
    },
    async updateListingStatus() {
      this.loading = true
      let response = await this.$http.post(`/yext/update_listing_status`, {
        location_id: this.currentLocationId,
      })
      this.loading = false
    },
    async getYextData() {
      if (this.yextId) {
        this.loading = true
        try {
          let response = await this.$http.get(
            'api/yext/' + this.currentLocationId + '/listings'
          )
          //response =[];
          let _self = this

          if (response.status === 200) {
            this.listings = response.data
            if (response.data.length == 0) {
              this.listings = null
            } else {
              this.listings = response.data.listings
              this.yextLocation = response.data.yextLocation
            }
          } else {
            this.listings = null
          }
        } catch (err) {
          this.listings = null
          console.error(err)
        }
        this.loading = false
      } else {
        this.listings = null
      }
    },
    populateChart() {
      var _self = this
      _self.liveCount = 0
      _self.unavCount = 0
      _self.optedCount = 0
      _self.processingCount = 0

      this.theChart.series[0].setData([])

      if (this.listings && this.listings.length > 0) {
        this.totalCount = this.listings.length
        totalCountGraph = this.totalCount

        for (let key in this.listings) {
          var listing = this.listings[key]
          if (!listing.status) continue
          if (listing.status == 'LIVE') {
            _self.liveCount += 1
          } else if (listing.status == 'UNAVAILABLE') {
            _self.unavCount += 1
          } else if (listing.status == 'OPTED_OUT') {
            _self.optedCount += 1
          } else if (listing.status.indexOf('WAITING') > -1) {
            _self.processingCount += 1
          }
        }

        this.theChart.series[0].addPoint({
          color: '#178ACD',
          x: 0,
          y: _self.liveCount,
          name: 'Live',
        })

        this.theChart.series[0].addPoint({
          color: '#23D2BE',
          x: 1,
          y: _self.processingCount,
          name: 'Processing',
        })

        this.theChart.series[0].addPoint({
          color: '#F2326B',
          x: 2,
          y: _self.optedCount,
          name: 'Opted Out',
        })

        this.theChart.series[0].addPoint({
          color: '#FDC131',
          x: 3,
          y: _self.unavCount,
          name: 'Unavailable',
        })
      }
    },
    async delay(time: number) {
      return new Promise(resolve => {
        setTimeout(resolve, time)
      })
    },
    async goToYext() {
      //this.showManageListingPopup = false
      window.open(
        `https://www.yext.com/users/login?username=${window.encodeURIComponent(
          this.company.yextReseller.yext_user_email
        )}`
      )
    },
  },
})
</script>
<style lang="scss" scoped>
#online-listing {
  position: relative;
  padding: 15px;

  .card {
    .card-header {
      padding: 10px 20px;
    }
  }

  .loader-backdrop {
    background: #ffffff91;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: grid;
    justify-content: center;
    align-items: baseline;
    padding-top: 190px;
    z-index: 3;

    .wait_on_ {
      background: white;
      padding: 20px;
      border: 1px solid #c5c5c5;
      border-radius: 3px;
      min-height: 200px;
      max-width: 700px;
      text-align: center;
      display: grid;
      align-content: center;
      h2 {
        color: #545453;
        font-weight: 500;
      }
      button {
        justify-self: center;
        margin: 30px 0 0 0;
      }
    }
  }
  .dashboard {
    display: grid;
    grid-template-columns: 1fr 1.5fr;

    .partners {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      align-content: center;
      height: 100%;
      min-height: 45vh;

      & > div {
        width: 120px;
        height: 150px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        padding: 10px;
      }
    }
  }

  .manage_listing_popup {
    max-width: 820px;
    table {
      min-width: 50%;
      text-align: left;
      margin: 20px auto 40px auto;
      td {
        padding: 5px 10px 5px 0;
      }

      .password_div {
        position: relative;
        .password_info {
          position: absolute;
          left: 0;
          font-style: italic;
          font-size: 0.8rem;
          margin-top: -5px;
        }
        .password_holder {
          display: grid;
          grid-template-columns: auto 1fr;
          position: relative;
          align-items: center;
        }
      }
    }
    .action_btn_holders {
      display: grid;
      grid-template-columns: auto auto;
      grid-gap: 10px;
      justify-content: center;

      .btn-cancel {
        background: #e0ecf3;
        color: #6a6c6e;
        width: 106px;
      }
    }
  }

  .hl_visible_to {
    padding: 0 0px 0 10px;
    color: #188bf6;
  }

  .copy-code-wrapper {
    display: inline-flex;
    border: none;
    // width: 32px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    background: #e5f2fe;
    cursor: pointer;
    align-items: center;
    padding: 5px;
    border-radius: 5px;

    &:hover {
      opacity: 0.7;
    }

    &:active {
      opacity: 1;
    }
    span {
      font-size: 10px;
      margin-right: 5px;
    }
    margin-left: 10px;
  }
}
</style>
