<template>
    <div class="card hl_dashboard--leaderboard-invites">
        <div class="card-header">
            <h2>Leaderboard for Invites
                <span>{{filter}}</span>
            </h2>
            <select class="selectpicker more-select" v-model="filter">
                <option>This Week</option>
                <option>Last Week</option>
                <option>This Month</option>
                <option>Last 6 Months</option>
                <option>This Year</option>
            </select>
        </div>
        <div class="card-body">
            <div class="leaderboard_item" v-for="(value, key) in currentReviewsAggregate.leaderboard" :key="key">
                <UserAvatar :userId='key' :showName="true" />
                <p>
                    <strong>{{value}}</strong> / 100</p>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import Vue from 'vue';
import { ReviewRequestAggregateData } from '@/store/state_models';
import UserAvatar from '../UserAvatar.vue';

declare var $: any;

export default Vue.extend({
    components: { UserAvatar },
    data() {
        return {
            filter: 'Last 6 Months',
            currentLocationId: '',
        };
    },
    watch: {
        '$route.params.location_id': function(id) {
            this.currentLocationId = id;
            this.filter = 'Last 6 Months';
        },
        filter: function() {
            if (this.filter === 'This Week') {
                this.$store.dispatch('reviewRequestAggregate/fetchThisWeek', this.currentLocationId);
            } else if (this.filter === 'Last Week') {
                this.$store.dispatch('reviewRequestAggregate/fetchLastWeek', this.currentLocationId);
            } else if (this.filter === 'This Month') {
                this.$store.dispatch('reviewRequestAggregate/fetchThisMonth', this.currentLocationId);
            } else if (this.filter === 'Last 6 Months') {
                this.$store.dispatch('reviewRequestAggregate/fetchLast6Months', this.currentLocationId);
            } else if (this.filter === 'This Year') {
                this.$store.dispatch('reviewRequestAggregate/fetchThisYear', this.currentLocationId);
            }
        },
    },
    computed: {
        currentReviewsAggregate(): ReviewRequestAggregateData {
            return this.$store.getters['reviewRequestAggregate/current'](this.filter);
        },
    },
    created() {
        this.currentLocationId = this.$router.currentRoute.params.location_id;
    },
    mounted() {
        const $selectpicker = $(this.$el).find('.selectpicker');
        if ($selectpicker) {
            $selectpicker.selectpicker('refresh');
        }
    },
    updated() {
        const $selectpicker = $(this.$el).find('.selectpicker');
        if ($selectpicker) {
            $selectpicker.selectpicker('refresh');
        }
    },
});
</script>

