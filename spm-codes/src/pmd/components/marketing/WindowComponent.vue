<template>
	<div>
		<div class="form-group">
			<label>Custom time</label>
			<div>
				<UIToggle :id="'window'+parent" v-model="window"/>
				<label class="tgl-btn" :for="'window'+parent"></label>
			</div>
		</div>
		<transition name="slide">
			<div class="scheduling" v-if="window">
				<div class="form-group condition">
					<label>Condition</label>
					<div>
						<div class="form-group-controls">
							<select class="selectpicker" v-model="condition" name="condition" @change="save">
								<option value="if">If</option>
								<option value="when">When</option>
								<option value="wait">Exact</option>
							</select>
						</div>
						<span
							class="explanation"
							v-if="condition==='if'"
						>Execute this action if the current time falls in this window, if the current time is outside this window skip this step and move on to the next action.</span>
						<span
							class="explanation"
							v-if="condition==='when'"
						>Execute this action if the current time falls in this window, else wait until the next window to execute this action.</span>
					</div>
				</div>
				<div class="form-group">
					<label>Pick days</label>
					<div class="form-group-controls">
						<div class="week-select">
							<div class="week-select-day">
								<input type="checkbox" :id="'mon'+parent" v-model="days" value="1" @change="save">
								<label :for="'mon'+parent">Mon</label>
							</div>
							<div class="week-select-day">
								<input type="checkbox" :id="'tue'+parent" v-model="days" value="2" @change="save">
								<label :for="'tue'+parent">Tue</label>
							</div>
							<div class="week-select-day">
								<input type="checkbox" :id="'wed'+parent" v-model="days" value="3" @change="save">
								<label :for="'wed'+parent">Wed</label>
							</div>
							<div class="week-select-day">
								<input type="checkbox" :id="'thur'+parent" v-model="days" value="4" @change="save">
								<label :for="'thur'+parent">Thu</label>
							</div>
							<div class="week-select-day">
								<input type="checkbox" :id="'fri'+parent" v-model="days" value="5" @change="save">
								<label :for="'fri'+parent">Fri</label>
							</div>
							<div class="week-select-day">
								<input type="checkbox" :id="'sat'+parent" v-model="days" value="6" @change="save">
								<label :for="'sat'+parent">Sat</label>
							</div>
							<div class="week-select-day">
								<input type="checkbox" :id="'sun'+parent" v-model="days" value="0" @change="save">
								<label :for="'sun'+parent">Sun</label>
							</div>
						</div>
					</div>
				</div>
				<div class="form-group" v-show="condition!=='wait'">
					<label>Start time</label>
					<div class="form-group-controls">
						<select
							class="selectpicker"
							v-model="startTime"
							name="startTime"
							data-size="5"
							@change="save"
						>
							<option
								v-for="slot in startSlots"
								:key="'start-'+slot.format()"
								:value="slot.format()"
							>{{slot.format('hh:mm a')}}</option>
						</select>
					</div>
				</div>
				<div class="form-group" v-show="condition!=='wait'">
					<label>End time</label>
					<div class="form-group-controls">
						<select class="selectpicker" v-model="endTime" name="endTime" data-size="5" @change="save">
							<option
								v-for="slot in endSlots"
								:key="'start-'+slot.format()"
								:value="slot.format()"
							>{{slot.format('hh:mm a')}}</option>
						</select>
					</div>
				</div>
				<div class="form-group" v-show="condition==='wait'">
					<label>Pick time</label>
					<div class="form-group-controls wait-start-time">
						<select
							class="selectpicker"
							v-model="startHour"
							name="startHour"
							data-size="5"
							@change="save"
						>
							<option v-for="hour in hours" :key="'hour-'+hour" :value="pad(hour,2)">{{pad(hour,2)}}</option>
						</select>
						<select
							class="selectpicker"
							v-model="startMinute"
							name="startMinute"
							data-size="5"
							@change="save"
						>
							<option
								v-for="minute in minutes"
								:key="'minute-'+minute"
								:value="pad(minute,2)"
							>{{pad(minute,2)}}</option>
						</select>
						<select
							class="selectpicker ampm"
							v-model="amORpm"
							name="startAMPM"
							data-size="5"
							@change="save"
						>
							<option value="AM">AM</option>
							<option value="PM">PM</option>
						</select>
					</div>
				</div>
			</div>
		</transition>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
import * as moment from 'moment-timezone';
import { ActionCondition } from '@/models';

declare var $: any;

export default Vue.extend({
	props: ['value', 'parent'],
	watch: {
		window() {
			this.save();
		},
		value() {
			const data: (() => object) = <(() => object)>this.$options.data;
			if (data) Object.assign(this.$data, data.apply(this));
			if (lodash.isEmpty(this.value)) return;
			this.window = true;
			this.days = this.value.days;
			this.condition = this.value.condition;
			if (this.value.condition === 'wait') {
				const startTime = moment(this.value.start, ["HH:mm"]);
				this.startHour = startTime.format('hh');
				this.startMinute = startTime.format('mm');
				this.amORpm = startTime.format('A');
			} else {
				const startParts = this.value.start.split(":");
				this.actualStartTime = moment().startOf('day').set({ hours: parseInt(startParts[0]), minutes: parseInt(startParts[1]) });
				const endParts = this.value.end.split(":");
				this.actualEndTime = moment().startOf('day').set({ hours: parseInt(endParts[0]), minutes: parseInt(endParts[1]) });
			}
		}
	},
	data() {
		return {
			amORpm: 'AM',
			startHour: '09',
			startMinute: '00',
			hours: Array(12).fill(0).map((item: number, index: number) => index + 1),
			minutes: Array(61).fill(0).map((item: number, index: number) => index),
			days: [0, 1, 2, 3, 4, 5, 6] as any[],
			actualStartTime: moment().startOf('day').set({ hours: 8 }),
			actualEndTime: moment().startOf('day').set({ hours: 17 }),
			slotDuration: 30,
			condition: 'when' as ActionCondition,
			window: false,
		}
	},
	methods: {
		pad(n: any, width: number, z?: string) {
			z = z || '0';
			n = n + '';
			return n.length >= width ? n : new Array(width - n.length + 1).join(z) + n;
		},
		save() {
			let window;
			if (!this.window) { this.$emit('input', {}); return; }
			if (this.condition === 'wait') {
				window = {
					condition: 'wait',
					days: lodash.sortBy(this.days.map(day => typeof day === 'string' ? parseInt(day) : day)),
					start: moment(`${this.startHour}:${this.startMinute} ${this.amORpm}`, ["h:mm A"]).format('HH:mm')
				};
			} else {
				window = {
					condition: this.condition,
					days: lodash.sortBy(this.days.map(day => typeof day === 'string' ? parseInt(day) : day)),
					start: this.actualStartTime.format('HH:mm'),
					end: this.actualEndTime.format('HH:mm'),
				};
			}
			this.$emit('input', window);
		}
	},
	computed: {
		startTime: {
			get: function (): string {
				return this.actualStartTime.format();
			},
			set: function (time: string) {
				this.actualStartTime = moment(time);
			}
		},
		endTime: {
			get: function (): string {
				return this.actualEndTime.format();
			},
			set: function (time: string) {
				this.actualEndTime = moment(time);
			}
		},
		startSlots(): moment.Moment[] {
			const times = [];
			let start = moment().startOf('day');
			let end = moment().startOf('day').add(1, 'day');
			for (; start.isBefore(end); start.add(this.slotDuration, 'minutes')) {
				times.push(start.clone());
			}
			return times;
		},
		endSlots(): moment.Moment[] {
			const times = [];
			let start = moment().startOf("day");
			let end = moment().startOf('day').add(1, 'day');
			for (; start.isBefore(end); start.add(this.slotDuration, 'minutes')) {
				times.push(start.clone());
			}
			return times;
		},
	},
	updated() {
		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker('refresh');
		}
	},
	mounted() {
		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker();
		}
	},
})
</script>

