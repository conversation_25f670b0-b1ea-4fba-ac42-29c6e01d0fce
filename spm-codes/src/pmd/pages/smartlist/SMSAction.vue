<template>
  <div
    class="hl_conversations--message-body message-input-wrap overflowdisabled"
  >
    <div class="tab-content">
      <div
        class="tab-pane fade show active overflowdisabled"
        id="sms"
        role="tabpanel"
        aria-labelledby="sms-tab"
      >
        <div class="form-group text-right">
          <!-- <label>Template (Optional)</label> -->
          <CommonSelector
            :disabled="false"
            customclass="dropdownwide"
            display="SMS Templates"
            :items="templatesDisplay"
            v-on:selectionChange="templateChanged"
          >
          </CommonSelector>
        </div>
        <div
          class="message-box"
          @drop="handleDrag"
          @dragover="handleDragover"
          @dragleave="handleDragleave"
          :class="{ focused: isFocus, dragoverd: isDrogover }"
        >
          <div class="form-control mx-0 my-0 px-0 py-0" v-if="sms">
            <textarea
              v-model="sms.message"
              class="form-control"
              rows="4"
              placeholder="Type a message"
              data-vv-validate-on="input"
              name="editor"
              v-validate="'required'"
              @keydown.enter.exact.prevent
              @keydown.enter.exact="sms.message += '\r\n'"
            ></textarea>
            <picker
              v-if="showEmojis"
              set="emojione"
              :style="{ position: 'absolute', bottom: '178px', right: '0px' }"
              title
              @select="insertEmoji"
            />
            <div
              style="display: inline-block"
              v-if="sms.attachments && sms.attachments.length > 0"
            >
              <div
                class="float-md-right mx-2 py-2"
                v-for="attachment in sms.attachments"
                :key="attachment"
              >
                <!-- <div>{{attachment}}</div>
                <div>{{sms.attachments.length}}</div> -->
                <div style="position: relative">
                  <GHLFileAttachment :fileUrl="attachment" />
                  <a
                    @click="removeAttachment(attachment)"
                    style="
                      position: absolute;
                      right: -11px;
                      top: -13px;
                      padding: 4px;
                      color: #e93d3d !important;
                    "
                    class="pointer"
                  >
                    <i class="fas fa-minus-circle"></i>
                  </a>
                </div>
              </div>
            </div>
          </div>
          <div class="message-action-top">
            <NestedMenu
              class="my-2"
              v-show="menuItems && menuItems.length > 0"
              :items="menuItems"
              :title="'Merge fields'"
              v-on:MenuSelected="menuItemSelected"
            />
            <div class="file-open">
              <input
                type="file"
                id="file-open"
                ref="fileupload"
                @change="onFileChange"
                multiple
              />
              <label for="file-open">
                <i class="icon icon-file"></i>
              </label>
            </div>
            <a
              href="javascript:void(0);"
              class="emoji-open"
              @click.stop="toggleEmojis"
            >
              <i class="icon icon-emoji"></i>
            </a>
          </div>
          <div class="message-action-bottom"></div>
          <div v-if="errorMsg" class="--red" style="text-align: center">
            {{ errorMsg }}
          </div>
          <!-- <span v-show="errors.has('editor')" class="--red">There are issues in your custom variables, please fix them before sending.</span> -->
          <span v-show="errors.has('editor')" class="error"
            >{{ errors.first('editor') }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import ImageTools from '../../../util/image_tools'
import { v4 as uuid } from 'uuid'
import 'emoji-mart-vue-fast/css/emoji-mart.css'
// import { Picker } from 'emoji-mart-vue-fast'
const Picker = () =>
  import(/* webpackChunkName: "lib-emojimart" */ 'emoji-mart-vue-fast').then(
    m => m.Picker
  )
import { EventBus } from '@/models/event-bus'
import { MessageTemplate } from '@/models/campaign'
import NestedMenu from '@/pmd/components/NestedMenu.vue'
import { getTagOptions } from '@/util/merge_tags'
import { CustomFields } from '../../../util/custom_fields'
import GHLFileAttachment from '../../components/GHLFileAttachment.vue'
import vSelect from 'vue-select'
const CommonSelector = () => import('./CommonSelector.vue')

import {
  Message,
  Conversation,
  Contact,
  MessageType,
  MessageContentType,
  Template,
  User,
  SMSTemplate,
  Link,
} from '../../../models'
import { mapState } from 'vuex'
import { UserState, FileAttachment, CompanyState } from '@/store/state_models'
import * as lodash from 'lodash'
import firebase from 'firebase/app'
import { KeyVal } from './CommonSelector.vue'
const path = require('path')

const imageTools = new ImageTools()

export interface ISMS {
  attachments: string[]
  message: String
}

export default Vue.extend({
  name: 'SMSAction',
  props: {
    sms: Object as () => ISMS,
  },
  inject: ['parentValidator'],
  components: {
    Picker,
    NestedMenu,
    GHLFileAttachment,
    vSelect,
    CommonSelector,
  },
  watch: {
    '$route.params.location_id': async function (n: string) {
      await this.loadUserOptions()
    },
    selectedTemplate(val: any) {
      this.templateSelected(val)
    },
    'sms.message'(val: string) {
      if (this.firstLetter && val && val.length) {
        this.firstLetter = false
        this.sms.message = val.charAt(0).toUpperCase() + val.slice(1)
      }
    },
  },
  data() {
    return {
      errorMsg: '',
      menuItems: [],
      showTemplateModal: false,
      firstLetter: true,
      showEmojis: false,
      isFocus: false,
      isDrogover: false,
      sending: false,
      //selectedTemplateId: undefined,
      props: {
        pack: { type: Array, required: true },
        labelSearch: { type: String, default: 'Pesquisar...' },
        showCategory: { type: Boolean, default: true },
        emojisByRow: { type: Number, default: 5 },
        showSearch: { type: Boolean, default: () => true },
      },
      loadingManualSMS: false,
      templates: [] as { [key: string]: any }[],
      selectedTemplate: undefined as { [key: string]: any } | undefined,
    }
  },
  created() {
    this.$validator = this.parentValidator
  },
  async mounted() {
    await this.loadUserOptions()
  },
  beforeDestroy() {
    EventBus.$off('selected_sms_template')
  },
  computed: {
    templatesDisplay() {
      const items = lodash.filter(this.templates, (template: Template) => {
        return template.type == Template.TYPE_SMS || !template.type
      })
      return items.map(template => new KeyVal(template.id, template.name))
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    filteredTemplates(): { [key: string]: any }[] {
      return lodash.filter(this.templates, (template: Template) => {
        return template.type == Template.TYPE_SMS || !template.type
      })
    },
  },
  methods: {
    async loadUserOptions() {
      this.loadTemplates()
      await this.loadCustomFieldItems()
      await this.loadTriggerLinks()
    },
    templateChanged(event: KeyVal) {
      if (this.templates && event.key) {
        const idx = this.templates.findIndex(a => a.id === event.key)
        this.templateSelected(this.templates[idx])
      }
    },
    async loadTemplates() {
      if (!this.$route.params.location_id) return
      const snapshot = await Template.fetchByLocationId(
        this.$route.params.location_id
      ).get()

      this.templates = snapshot.docs.map(d => {
        return { id: d.id, ...d.data() }
      })
      console.log(this.templates)
    },
    async loadTriggerLinks() {
      if (!this.$route.params.location_id) return
      await this.$store.dispatch(
        'conversation/fetchTriggerLinks',
        {locationId: this.$route.params.location_id}
      )
      let obj = this.$store.getters['conversation/getTriggerLinksMenuItem']
      if (obj) {
        delete obj.location_id
        this.menuItems = [...this.menuItems, obj]
      }
    },
    async loadCustomFieldItems() {
      if (!this.$route.params.location_id) return
      try {
        this.menuItems.push.apply(
          this.menuItems,
          await CustomFields.getList(this.$route.params.location_id, true)
        )
      } catch (err) {
        console.log(err)
      }
    },
    menuItemSelected(item: string) {
      if (item && this.sms) {
        this.sms.message = this.sms.message
          ? `${this.sms.message} ${item}`
          : item
      }
    },
    pickTemplate() {
      EventBus.$emit('select_sms_template')
    },
    clear() {
      //this.selectedTemplateId = undefined
      if (this.sms) this.sms.message = ''
      if (this.sms && this.sms.attachments) this.sms.attachments = []
      this.firstLetter = true
      this.showEmojis = false
      this.errorMsg = ''
      this.sending = false
    },
    toggleEmojis() {
      this.showEmojis = !this.showEmojis
    },
    insertEmoji(emoji: any) {
      this.toggleEmojis()
      console.log(emoji)
      this.sms.message += ' ' + emoji.native
    },
    handleDrag(e: any) {
      var fileList = e.dataTransfer.files
      if (fileList && fileList.length > 0) {
        this.vfileAdded(fileList[0])
      }
      this.isDrogover = false
      e.preventDefault()
    },
    handleDragover(e: any) {
      this.isDrogover = true
      e.preventDefault()
    },
    handleDragleave(e: any) {
      this.isDrogover = false
      e.preventDefault()
    },
    async onFileChange(e: any) {
      const element = <HTMLInputElement>e.target
      if (!element.files) return
      for (let i = 0; i < element.files.length; i++) {
        this.vfileAdded(element.files[i])
      }
      element.files = null
    },
    async vfileAdded(file: File) {
      const response = <File | Blob>(
        await imageTools.resize(file, { height: 1000, width: 1000 })
      )
      const url = await this.uploadFileToBucket({
        name: file.name,
        type: file.type,
        url: URL.createObjectURL(response),
        data: response,
      })
      if (!this.sms.attachments) this.sms.attachments = []
      if (url) this.sms.attachments.push(url)
    },
    templateSelected(template: MessageTemplate) {
      if (!template || !template.template) return
      this.sms.attachments = []
      console.log('Using template:', template)
      const temp = template.template as SMSTemplate
      this.sms.message = temp.body
      //alert(this.selectedTemplateId);
      if (!lodash.isEmpty(temp.attachments)) {
        this.sms.attachments.push(...lodash.clone(temp.attachments))
      }
      if (!lodash.isEmpty(temp.urlAttachments)) {
        this.sms.attachments.push(...lodash.clone(temp.urlAttachments))
      }
    },
    removeAttachment(attachment: string) {
      if (this.sms.attachments.indexOf(attachment) !== -1)
        this.sms.attachments.splice(this.sms.attachments.indexOf(attachment), 1)
    },
    async getImagePath(): Promise<string> {
      const locationId = this.$router.currentRoute.params.location_id
      if (!locationId)
        throw new Error('Cannot upload a file without location id')
      let imagePath = 'location/' + locationId + '/bulk_sms/' + uuid()
      return imagePath
    },
    async uploadFileToBucket(attachment: any) {
      try {
        var uploadPath = firebase.storage().ref(await this.getImagePath())
        const snapshot = await uploadPath.put(attachment.data, {
          contentType: attachment.type,
          contentDisposition: `inline; filename="${attachment.name}"`,
          customMetadata: { name: attachment.name },
        })
        return await snapshot.ref.getDownloadURL()
      } catch (err) {
        console.log(err)
      }
    },
  },
})
</script>

<style scoped>
textarea.form-control {
  padding-right: 35px;
}
.hl_conversations--message-body .message-box .message-action-top,
.dropdown.my-2,
.file-open,
a.emoji-open {
  display: block;
}
.message-action-top {
  top: 5px !important;
  right: 15px !important;
}
.message-action-top > div,
.message-action-top > a {
  margin: 3px 0px 0px 0px !important;
}
.message-action-top > a {
  margin-left: -2px !important;
}
.dropdownwide {
  width: 100%;
}
</style>
