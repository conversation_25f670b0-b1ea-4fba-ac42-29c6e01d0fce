<template>
  <modal
    @close="$emit('close')"
    maxWidth="510"
    :showCloseIcon="true"
    v-if="show"
  >
    <div class="success-modal__body">
      <div class="success-modal__icon"><i class="fas fa-check"></i></div>
      <div class="success-modal__label">
        {{ message || `Stripe customer successfully connected!` }}
      </div>
      <div class="success-modal__action-btn btn" @click="$emit('close')">
        Okay
      </div>
    </div>
  </modal>
</template>

<script>
import Modal from '@/pmd/components/common/Modal.vue'

export default {
  props: ['show', 'message'],
  components: {
    Modal,
  },
}
</script>

<style lang="scss">
.success-modal__body {
  padding: 40px;
  text-align: center;
}
.success-modal__icon {
  font-size: 48px;
  color: #68d391;
  border: 1px solid #68d391;
  border-radius: 50%;
  height: 90px;
  width: 90px;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.success-modal__label {
  margin: 24px auto 44px;
  color: #4f4f4f;
  font-weight: 500;
  font-size: 17px;
  line-height: 20px;
}
.success-modal__action-btn {
  width: 155px;
  height: 48px;
  margin: auto;
  background: #e2e8f0;
  border-radius: 3px;

  font-weight: bold;
  font-size: 14px;
  line-height: 24px;
  text-align: center;

  color: #718096 !important;
}
</style>
