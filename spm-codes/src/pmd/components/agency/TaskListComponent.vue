<template>
  <div
    :style="[fullheight ? { height: '100%', display: 'flex', flex: '1' } : {}]"
    :class="childClass"
  >
    <div
      class="card task-list-card"
      :style="getHeightBasedOnMessageBox"
      v-if="tasks.length > 0 || isAdmin"
    >
      <div
        v-show="heading"
        :class="{ 'card-header': childClass !== 'customerPageClass' }"
      >
        <h2>Tasks</h2>

        <div class="hl_tasks-list">
          <b-dropdown v-if="isAdmin" :text="userSelectDropdown">
            <b-dropdown-item v-if="locationId" @click="selectAllUsers"
              >All</b-dropdown-item
            >
            <b-dropdown-item
              v-for="(user, userIndex) in existingUsers"
              :value="user.id"
              :key="userIndex"
              @click="selectUser(user.id)"
              >{{ user.name }}</b-dropdown-item
            >
          </b-dropdown>
          <b-dropdown :text="filterBy">
            <b-dropdown-item @click="filterBy = 'All'">All</b-dropdown-item>
            <b-dropdown-item @click="filterBy = 'Completed'"
              >Completed</b-dropdown-item
            >
            <b-dropdown-item @click="filterBy = 'Pending'"
              >Pending</b-dropdown-item
            >
          </b-dropdown>
          <b-dropdown :text="getOrderByName()">
            <b-dropdown-item
              v-for="(order, orderIndex) in ordersList"
              :key="orderIndex"
              @click="orderBy = order.type"
            >
              <i class="fas fa-sort-amount-down hint-icon"></i> {{ order.name }}
            </b-dropdown-item>
          </b-dropdown>
        </div>
      </div>
      <div class="card-body" style="overflow-y: auto" v-if="tasks.length > 0">
        <div class="hl_tasks">
          <TaskListCard
            v-for="task in orderedTasks"
            :task="task"
            :key="task.id"
            @TaskDeleted="deleteTask(task.id)"
          />
        </div>
      </div>
      <div v-else class="card-body">
        <div class="text-center">
          <i class="fas fa-tasks" style="font-size: 20px; color: #bdbdbd"></i>
          <p>There are no tasks assigned for the selected user</p>
        </div>
      </div>
    </div>
    <div v-else-if="placeholder" class="card">
      <div v-show="heading" class="card-header">
        <h2>Tasks</h2>
      </div>
      <moon-loader :loading="loading" color="#188bf6" size="20px" />
      <div v-if="!loading" class="card-body">
        <div class="text-center">
          <i class="fas fa-tasks" style="font-size: 20px; color: #bdbdbd"></i>
        </div>
        <p class="text-center">
          {{ message === undefined ? defaultMessage : message }}
          <br />
        </p>
        <div class="text-center" v-if="action">
          Click
          <a
            @click="createNew"
            style="font-weight: bold; font-size: 15px; color: #188bf6"
            >here</a
          >
          to create one
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import { User, AuthUser } from '@/models'
import Task from '@/models/api/Task'
import { UserState } from '@/store/state_models'
import TaskListCard from './TaskListCard.vue'
import MoonLoader from '@/pmd/components/MoonLoader.vue'
import lodash from 'lodash'
import { UxMessage } from '@/util/ux_message'

const store = require('store')
let unsubscribeTasks: () => void

export default Vue.extend({
  props: [
    'heading',
    'action',
    'message',
    'placeholder',
    'fullheight',
    'childClass',
  ],
  inject: ['uxmessage'],
  data() {
    return {
      tasks: [] as Task[],
      authUser: {} as AuthUser,
      locationId: '',
      accountId: '',
      contactId: '',
      defaultMessage: 'No tasks for this contact',
      loading: true as boolean,
      orderBy: 'dueDate,asc',
      filterBy: 'Pending',
      selectedUserId: null,
      hideTask: {
        hide_completed_task: false,
      },
      ordersList: [
        {
          type: 'dueDate,desc',
          name: ' Due Date (DESC)',
        },
        {
          type: 'dueDate,asc',
          name: ' Due Date (ASC)',
        },
        {
          type: 'dateAdded,desc',
          name: ' Date Added (DESC)',
        },
        {
          type: 'dateAdded,asc',
          name: ' Date Added (ASC)',
        },
      ],
    }
  },
  components: { TaskListCard, MoonLoader },
  watch: {
    '$route.params.account_id': function (id) {
      this.accountId = id
      this.tasks = []
      this.fetchData()
    },
    '$route.params.location_id': function (id) {
      this.locationId = id
      this.tasks = []
      this.fetchData()
    },
    '$route.params.contact_id': function (id) {
      this.contactId = id
      this.tasks = []
      this.fetchData()
    },
    orderBy(val) {
      store.set(`task-list-orderby-${this.locationId}`, { orderBy: val })
    },
  },
  async created() {
    this.accountId = this.$router.currentRoute.params.account_id
    this.locationId = this.$router.currentRoute.params.location_id
    const storedPref = store.get(`task-list-orderby-${this.locationId}`)
    if (storedPref && storedPref.orderBy) {
      this.orderBy = storedPref.orderBy
    }

    this.contactId = this.$router.currentRoute.params.contact_id
    this.authUser = await this.$store.dispatch('auth/get')

    this.fetchData()
  },
  computed: {
    filteredTasks(): Task[] {
      let filtered = this.tasks.filter(
        (task: Task) =>
          this.filterBy === 'All' ||
          (this.filterBy === 'Completed' && task.completed) ||
          (this.filterBy === 'Pending' && !task.completed)
      )
      if (this.contactId)
        filtered = filtered.filter(
          (task: Task) => task.contactId === this.contactId
        )
      return filtered
    },
    orderedTasks(): Task[] {
      if (!this.heading) return this.tasks
      const [field, order] = this.orderBy.split(',')
      return lodash.orderBy(this.filteredTasks, field, order)
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    isAdmin(): boolean {
      return this.user && this.user.role === 'admin'
    },
    userSelectDropdown(): string {
      if (!this.selectedUserId) return 'Select user'
      if (this.selectedUserId === 'all') return 'All'
      const user: User | undefined = this.existingUsers.find(
        user => user.id === this.selectedUserId
      )
      return user ? user.name : 'Select user'
    },
    existingUsers() {
      return this.$store.state.users.users.map(
        u => new User(Object.assign({}, u))
      )
    },
    getHeightBasedOnMessageBox(): string {
      return 'max-height: calc(100vh - ' + 380 + 'px);'
    },
  },
  methods: {
    async fetchData() {
      //if (unsubscribeTasks) unsubscribeTasks()
      if (this.contactId) {

        try {
          let queryData = {isLocation:true,locationId:this.locationId, contactId:this.contactId}
          this.tasks = await Task.list(queryData)
          this.loading = false

        } catch (error) {
          this.uxmessage(UxMessage.errorType(error), true)
        }

        this.loading = false
      } else if (this.accountId) {

        try{
          let queryData = {isLocation:false,locationId:this.accountId}
          this.tasks = await Task.list(queryData)
          this.loading = false

        }catch (error) {
          this.uxmessage(UxMessage.errorType(error), true)
        }
      } else if (this.locationId) {
        this.selectedUserId = this.authUser.userId
        this.getTasksUserID()

      }
    },

    async getTasksUserID() {
      let queryData = {isLocation:true,locationId:this.locationId, userId:this.selectedUserId,contactId:this.contactId}
      this.tasks = await Task.list(queryData)
      this.loading = false
    },

    async selectUser(id) {
      this.selectedUserId = id
      this.getTasksUserID()
    },
    createNew() {
      this.$emit('createNew', true)
    },
    getOrderByName() {
      const order = this.ordersList.find(
        obj => obj.type === (this.orderBy || 'dueDate,asc')
      )
      return order ? order.name : 'Sort by'
    },
    async selectAllUsers() {
      this.selectedUserId = 'all'
      let queryData = {isLocation:true,locationId:this.locationId,contactId:this.contactId}
      this.tasks = await Task.list(queryData)
      this.loading = false
    },

    deleteTask(id){
      const i = this.tasks.findIndex(t => t.id === id)
      this.tasks.splice(i,1)
    }
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  beforeDestroy() {
    if (unsubscribeTasks) unsubscribeTasks()
  },
})
</script>

<style>
.hl_tasks-list button.dropdown-toggle {
  background-color: #ffffff !important;
  color: #607179 !important;
}
.customerPageClass .hl_tasks-list .show {
  display: inline-block;
}
.customerPageClass h2 {
  display: none;
}
.customerPageClass .hl_tasks-list .dropdown button {
  font-size: 0.825rem;
  font-weight: 500;
}
.customerPageClass .hl_tasks-list .btn.dropdown-toggle {
  padding-right: 35px !important;
}
.toggle-task {
  margin: 0px 10px -5px 0px !important;
  display: inline-block !important;
}
.toggle-task-label {
  font-weight: bold;
}
.task-list-card {
  flex: 1;
}
.hl_tasks-list .dropdown .dropdown-menu {
  max-height: 250px;
  overflow: auto;
}
</style>
