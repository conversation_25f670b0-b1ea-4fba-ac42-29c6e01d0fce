<template>
  <div id="review-email" class="card">
    <div class="card-header --no-right-padding card-header--withbutton">
      <div class="image-toggle__btn">
        <div class="toggle">
          <UIToggle
            id="tgld"
            v-model="emailSettings.enabled"
            @change="toggleSwitched"
          />
          <label class="tgl-btn" for="tgld" id="toggle_email_settings"></label>
        </div>
      </div>
      <h3 @click="fetchData()">Customize Review Email</h3>
    </div>
    <div class="card-body">
      <div class="two-part">
        <div>
          <div>
            <UITextLabel class="text-xl">Email Sent to the User</UITextLabel>
          </div>
          <div class="mt-2">
            <form
              @submit.prevent="validateBeforeSubmit"
              data-vv-scope="emailSettingsForm"
            >
              <div class="form-group">
                <UITextInputGroup
                  type="text"
                  data-lpignore="true"
                  label="Subject"
                  placeholder="Subject"
                  v-model="emailSettings.subject"
                />
              </div>
              <div class="form-group">
                <UITextInputGroup
                  type="text"
                  data-lpignore="true"
                  placeholder="Heading"
                  label="Heading"
                  v-model="emailSettings.heading"
                />
              </div>
              <div class="form-group">
                <UITextAreaGroup
                  rows="5"
                  data-lpignore="true"
                  placeholder="Message"
                  label="Message"
                  v-model="emailSettings.message"
                ></UITextAreaGroup>
              </div>
              <div class="row">
                <div class="col-sm-6">
                  <div class="form-group">
                    <UITextInputGroup
                      type="text"
                      data-lpignore="true"
                      placeholder="Positive Answer"
                      label="Positive Answer"
                      v-model="emailSettings.positive"
                    />
                  </div>
                </div>
              </div>
              <div
                class="communication"
                v-if="review_request_behaviour === 'custom_schedule'"
              >
                <form>
                  <div class="form-group">
                    <label>When to send Email after check-in?</label>
                    <div class="dropdowns">
                      <div class="dropdown">
                        <button
                          class="btn btn-ghl_lite dropdown-toggle"
                          type="button"
                          id="reviw_time"
                          data-toggle="dropdown"
                          aria-haspopup="true"
                          aria-expanded="false"
                        >
                          <template
                            v-if="
                              local_after_check_in_time.unit === 'hours' &&
                              local_after_check_in_time.value == 0 &&
                              local_after_check_in_time.type != 'custom'
                            "
                          >
                            Immediately
                          </template>
                          <template
                            v-else-if="
                              local_after_check_in_time.unit === 'hours' &&
                              local_after_check_in_time.value == 1 &&
                              local_after_check_in_time.type != 'custom'
                            "
                          >
                            1 Hour
                          </template>
                          <template
                            v-else-if="
                              local_after_check_in_time.unit === 'hours' &&
                              local_after_check_in_time.value == 2 &&
                              local_after_check_in_time.type != 'custom'
                            "
                          >
                            2 Hours
                          </template>
                          <template
                            v-else-if="
                              local_after_check_in_time.unit === 'hours' &&
                              local_after_check_in_time.value == 4 &&
                              local_after_check_in_time.type != 'custom'
                            "
                          >
                            4 Hours
                          </template>
                          <template v-else> Custom </template>
                        </button>
                        <div class="dropdown-menu" aria-labelledby="reviw_time">
                          <span
                            class="dropdown-item"
                            @click="afterChckinTimeChanged('hours', 0)"
                          >
                            Immediately</span
                          >
                          <span
                            class="dropdown-item"
                            @click="afterChckinTimeChanged('hours', 1)"
                          >
                            1 Hour</span
                          >
                          <span
                            class="dropdown-item"
                            @click="afterChckinTimeChanged('hours', 2)"
                          >
                            2 Hours</span
                          >
                          <span
                            class="dropdown-item"
                            @click="afterChckinTimeChanged('hours', 4)"
                          >
                            4 Hours</span
                          >
                          <span
                            class="dropdown-item"
                            @click="
                              afterChckinTimeChanged('hours', null, 'custom')
                            "
                          >
                            Custom</span
                          >
                          <!--   <a class="dropdown-item" href="#!">1 Hour</a>
                      <a class="dropdown-item" href="#!">2 Hours</a>
                      <a class="dropdown-item" href="#!">4 Hours</a>
                      <a class="dropdown-item" href="#!">Custom</a> -->
                        </div>
                      </div>

                      <template
                        v-if="local_after_check_in_time.type === 'custom'"
                      >
                        <div class="customCheckinTime">
                          <input
                            type="number"
                            name="when-send-email"
                            id="email_check_in_custom"
                            min="0"
                            v-model="local_after_check_in_time.value"
                            class="form-control"
                          />
                          <div class="dropdown">
                            <button
                              class="btn btn-ghl_lite dropdown-toggle"
                              type="button"
                              id="unitTypeDropDown"
                              data-toggle="dropdown"
                              aria-haspopup="true"
                              aria-expanded="false"
                            >
                              <template
                                v-if="local_after_check_in_time.unit == 'hours'"
                              >
                                Hour(s)
                              </template>
                              <template
                                v-if="local_after_check_in_time.unit == 'days'"
                              >
                                Day(s)
                              </template>
                            </button>
                            <div
                              class="dropdown-menu"
                              aria-labelledby="unitTypeDropDown"
                            >
                              <span
                                class="dropdown-item"
                                @click="
                                  afterChckinTimeChanged('days', null, 'custom')
                                "
                              >
                                Day(s)</span
                              >
                              <span
                                class="dropdown-item"
                                @click="
                                  afterChckinTimeChanged(
                                    'hours',
                                    null,
                                    'custom'
                                  )
                                "
                              >
                                Hour(s)</span
                              >
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                </form>

                <form>
                  <div class="form-group">
                    <label>Until clicked, repeat this every</label>
                    <div class="dropdowns">
                      <div class="dropdown">
                        <button
                          class="btn btn-ghl_lite dropdown-toggle"
                          type="button"
                          id="reviw_time"
                          data-toggle="dropdown"
                          aria-haspopup="true"
                          aria-expanded="false"
                        >
                          <template
                            v-if="
                              local_repeat_review_request_in.value == -1 &&
                              local_repeat_review_request_in.type != 'custom'
                            "
                          >
                            Don't Repeat
                          </template>
                          <template
                            v-else-if="
                              local_repeat_review_request_in.unit === 'days' &&
                              local_repeat_review_request_in.value == 3 &&
                              local_repeat_review_request_in.type != 'custom'
                            "
                          >
                            3 Days
                          </template>
                          <template
                            v-else-if="
                              local_repeat_review_request_in.unit === 'days' &&
                              local_repeat_review_request_in.value == 7 &&
                              local_repeat_review_request_in.type != 'custom'
                            "
                          >
                            1 Week
                          </template>
                          <template
                            v-else-if="
                              local_repeat_review_request_in.unit === 'days' &&
                              local_repeat_review_request_in.value == 15 &&
                              local_repeat_review_request_in.type != 'custom'
                            "
                          >
                            15 Days
                          </template>
                          <template
                            v-else-if="
                              local_repeat_review_request_in.unit === 'days' &&
                              local_repeat_review_request_in.value == 30 &&
                              local_repeat_review_request_in.type != 'custom'
                            "
                          >
                            1 Month
                          </template>
                          <template v-else> Custom </template>
                        </button>
                        <div class="dropdown-menu" aria-labelledby="reviw_time">
                          <span
                            class="dropdown-item"
                            @click="
                              repeateReviewRequestTimeChanged('hours', -1)
                            "
                          >
                            Don't Repeat</span
                          >
                          <span
                            class="dropdown-item"
                            @click="repeateReviewRequestTimeChanged('days', 3)"
                          >
                            3 Days</span
                          >
                          <span
                            class="dropdown-item"
                            @click="repeateReviewRequestTimeChanged('days', 7)"
                          >
                            1 Week</span
                          >
                          <span
                            class="dropdown-item"
                            @click="repeateReviewRequestTimeChanged('days', 15)"
                          >
                            15 Days</span
                          >
                          <span
                            class="dropdown-item"
                            @click="repeateReviewRequestTimeChanged('days', 30)"
                          >
                            1 Month</span
                          >
                          <span
                            class="dropdown-item"
                            @click="
                              repeateReviewRequestTimeChanged(
                                'days',
                                1,
                                'custom'
                              )
                            "
                          >
                            Custom</span
                          >
                          <!--   <a class="dropdown-item" href="#!">1 Hour</a>
                      <a class="dropdown-item" href="#!">2 Hours</a>
                      <a class="dropdown-item" href="#!">4 Hours</a>
                      <a class="dropdown-item" href="#!">Custom</a> -->
                        </div>
                      </div>

                      <template
                        v-if="local_repeat_review_request_in.type === 'custom'"
                      >
                        <div class="customCheckinTime">
                          <input
                            type="number"
                            name="when-repeat-email"
                            id="email_check_in_repeat_custom"
                            min="0"
                            v-model="local_repeat_review_request_in.value"
                            class="form-control"
                          />
                          <div class="dropdown">
                            <button
                              class="btn btn-ghl_lite dropdown-toggle"
                              type="button"
                              id="email_check_in_repeat_custom_unit_type"
                              data-toggle="dropdown"
                              aria-haspopup="true"
                              aria-expanded="false"
                            >
                              <template
                                v-if="
                                  local_repeat_review_request_in.unit == 'hours'
                                "
                              >
                                Hour(s)
                              </template>
                              <template
                                v-if="
                                  local_repeat_review_request_in.unit == 'days'
                                "
                              >
                                Day(s)
                              </template>
                            </button>
                            <div
                              class="dropdown-menu"
                              aria-labelledby="email_check_in_repeat_custom_unit_type"
                            >
                              <span
                                class="dropdown-item"
                                @click="
                                  repeateReviewRequestTimeChanged(
                                    'days',
                                    null,
                                    'custom'
                                  )
                                "
                              >
                                Day(s)</span
                              >
                              <span
                                class="dropdown-item"
                                @click="
                                  repeateReviewRequestTimeChanged(
                                    'hours',
                                    null,
                                    'custom'
                                  )
                                "
                              >
                                Hour(s)</span
                              >
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                </form>
                <form>
                  <div class="row">
                    <div class="col-sm-6">
                      <div
                        class="form-group"
                        v-if="
                          local_repeat_review_request_in.value &&
                          local_repeat_review_request_in.value != '-1'
                        "
                      >
                        <label>Maximum retries</label>
                        <div>
                          <input
                            type="number"
                            name="max-repeat-email"
                            id="email_check_in_repeat_count"
                            min="1"
                            v-model.number="emailSettings.repeat_count"
                            class="form-control"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              </div>

              <div class="save-button-holder">
                <UIButton
                  type="button"
                  use="primary"
                  @click.stop="validateBeforeSubmit"
                  class="justify-center"
                  :disabled="saving"
                  id="emil_settings_save"
                >
                  <i
                    class="icon mr-2"
                    :class="saving ? 'icon-clock' : 'icon-ok'"
                  ></i>
                  {{ saveButtonText }}
                </UIButton>
              </div>
            </form>
          </div>
        </div>
        <div>
          <EmulatorComponent :content="emulatorContent" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// @ts-nocheck
import Vue from 'vue'
import EmulatorComponent from '../EmulatorComponent.vue'
import { Location, User } from '@/models'
import { Chrome } from 'vue-color'
import libphonenumber from 'google-libphonenumber'
import firebase from 'firebase/app'
var phoneUtil = libphonenumber.PhoneNumberUtil.getInstance()
var PNF = libphonenumber.PhoneNumberFormat

export default Vue.extend({
  props:{
    location:Object as Location,
    review_request_behaviour:String
  },
  components: {
    'chrome-picker': Chrome,
    EmulatorComponent,
  },
  data() {
    return {
      local_after_check_in_time: {
        unit: 'hours',
        value: 0,
      },
      local_repeat_review_request_in: {
        unit: 'hours',
        value: -1,
      },
      authUser: {} as any,
      //  location: {} as Location,
      currentLocationId: '',
      emailSettings: {
        enabled: false,
        repeat_count: 5,
      } as { [key: string]: any },
      backgroundColor: '#FFFFFF',
      htmlInput: '',
      user: {} as User,
      saving: false as Boolean,
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.fetchData()
    },
  },
  async created() {
    var _self = this
    _self.currentLocationId = _self.$router.currentRoute.params.location_id
    this.authUser = await this.$store.dispatch('auth/get')
    this.user = await this.$store.dispatch(
      'agencyUsers/syncGet',
      this.authUser.userId
    )
    this.fetchData()
  },
  async mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  methods: {
    async toggleSwitched(){
      await this.location.ref.update({
        'settings.email.enabled':this.emailSettings.enabled,
          date_updated: firebase.firestore.FieldValue.serverTimestamp(),
      })
    },
    afterChckinTimeChanged(unit, time, type = 'generic') {
      this.local_after_check_in_time = {
        ...this.local_after_check_in_time,
        type,
        unit,
        ...(time != null ? { value: parseInt(time) } : {}),
      }
    },
    repeateReviewRequestTimeChanged(unit, time, type = 'generic') {
      this.local_repeat_review_request_in = {
        ...this.local_repeat_review_request_in,
        type,
        unit,
        ...(time != null ? { value: parseInt(time) } : {}),
      }
    },
    async fetchData() {
      if (this.location.settings && this.location.settings.email) {
        this.emailSettings = {
          ...this.emailSettings,
          ...this.location.settings.email,
        }
      }
      if (this.emailSettings.after_check_in_hours == undefined) {
        this.emailSettings.after_check_in_hours = 0
      }

      if (
        this.emailSettings.after_check_in_hours == 0 ||
        this.emailSettings.after_check_in_hours == 1 ||
        this.emailSettings.after_check_in_hours == 2 ||
        this.emailSettings.after_check_in_hours == 4
      ) {
        this.local_after_check_in_time = {
          type: 'generic',
          unit: 'hours',
          value: this.emailSettings.after_check_in_hours || 0,
        }
      } else {
        let unit =
          this.emailSettings.after_check_in_hours % 24 == 0 ? 'days' : 'hours'
        let value = this.emailSettings.after_check_in_hours || 0
        if (unit === 'days') {
          value = value / 24
        }
        this.local_after_check_in_time = {
          type: 'custom',
          unit,
          value,
        }
      }


      if(this.emailSettings.repeat_count < 1){
        this.emailSettings.repeat_count = 1;
      }

      if (this.emailSettings.repeat_review_request_in == undefined) {
        this.emailSettings.repeat_review_request_in = -1
      }

      if (
        this.emailSettings.repeat_review_request_in == -1 ||
        this.emailSettings.repeat_review_request_in == 3 * 24 ||
        this.emailSettings.repeat_review_request_in == 7 * 24 ||
        this.emailSettings.repeat_review_request_in == 15 * 24 ||
        this.emailSettings.repeat_review_request_in == 30 * 24
      ) {
        let hoursInDay =
          this.emailSettings.repeat_review_request_in != -1
            ? this.emailSettings.repeat_review_request_in / 24
            : this.emailSettings.repeat_review_request_in
        this.local_repeat_review_request_in = {
          type: 'generic',
          unit: 'days',
          value: hoursInDay || -1,
        }
      } else {
        let unit =
          this.emailSettings.repeat_review_request_in % 24 == 0
            ? 'days'
            : 'hours'
        let value = this.emailSettings.repeat_review_request_in || 0
        if (unit === 'days') {
          value = value / 24
        }
        this.local_repeat_review_request_in = {
          type: 'custom',
          unit,
          value,
        }
      }

      this.getPreviewHTML()
    },
    backgroundColorChanged(value: any) {
      this.emailSettings.backgroundColor = value.hex
    },
    async validateBeforeSubmit() {
      let result = null
      result = await this.$validator.validateAll()
      if (!result) {
        console.error('Correct them errors!', this.$validator.errors)
        return false
      }

      if (this.review_request_behaviour === 'immediate') {
        //changed in between
        this.emailSettings.after_check_in_hours = 0
        this.emailSettings.repeat_review_request_in = -1
        this.local_after_check_in_time = {
          unit: 'hours',
          value: 0,
          type: 'generic',
        }
        this.local_repeat_review_request_in = {
          unit: 'hours',
          value: -1,
          type: 'generic',
        }
      }

      this.emailSettings.after_check_in_hours = parseInt(
        this.local_after_check_in_time.value
      )
      if (this.emailSettings.after_check_in_hours < 0) {
        this.emailSettings.after_check_in_hours = 0
        this.local_after_check_in_time = {
          unit: 'hours',
          value: 0,
          type: 'generic',
        }
      }
      if (this.local_after_check_in_time.type === 'custom') {
        if (this.local_after_check_in_time.unit === 'days') {
          this.emailSettings.after_check_in_hours =
            this.emailSettings.after_check_in_hours * 24
        }
      }

      this.emailSettings.repeat_review_request_in = parseInt(
        this.local_repeat_review_request_in.value
      )
      if (this.emailSettings.repeat_review_request_in <= 0) {
        this.emailSettings.repeat_review_request_in = -1
        this.local_repeat_review_request_in = {
          unit: 'hours',
          value: -1,
          type: 'generic',
        }
      }
      if (
        this.local_repeat_review_request_in.unit === 'days' &&
        this.emailSettings.repeat_review_request_in > 0
      ) {
        this.emailSettings.repeat_review_request_in =
          this.emailSettings.repeat_review_request_in * 24
      }

      this.saving = true
      this.location.settings.email = this.emailSettings
      if (!this.location.settings.review) {
        this.location.settings.review = {}
      }
      this.location.settings.review.review_request_behaviour =
        this.review_request_behaviour
      await this.location.ref.update({
        'settings.email':this.emailSettings,
        'settings.review': this.location.settings.review,
        date_updated: firebase.firestore.FieldValue.serverTimestamp()
      })  
      this.saving = false
      this.getPreviewHTML()
    },
    changeValue(field: any, value: any) {
      var el = $('<div></div>')
      el.html(this.htmlInput)
      if(field === `body`){
        value =value.replace(/(\r\n|\n|\r)/gm, "<br>")
        value =value.replace(/\s\s/gm, "&nbsp;&nbsp;")
      }
      $('#' + field, el).html(value) // All the anchor elements
      this.htmlInput = el.html();
    },
    async getPreviewHTML() {
      let params = {
        location_id: this.location.id,
        user_id: this.user.id,
      }
      let response = await this.$http.get('/api/reviews/getEmailHtml', {
        params: params,
      })
      this.htmlInput = response.data
    },
  },
  computed: {
    saveButtonText(): string {
      return this.saving ? 'Saving' : 'Save'
    },

    emulatorContent: function () {
      let body = this.htmlInput
      return {
        type: 'iframe-src-doc',
        emulators: [
          { name: 'mobile' },
          { name: 'tablet', default: true },
          { name: 'laptop' },
        ],
        //  iframeSrc : `/api/reviews/getEmailHtml?location_id=${this.location.id}&user_id=${this.user.id}`,
        htmlBody: body,
      }
    },
  },
})
</script>
<style lang="scss" scoped>
#review-email {
  .card-body {
    max-width: unset;

    .communication {
      padding: 20px 0 0 0;

      .dropdowns {
        display: grid;
        grid-template-columns: auto 1fr;

        .btn-ghl_lite {
          color: #188bf6 !important;
          background-color: rgba(24, 139, 246, 0.2) !important;
          border-color: rgba(24, 139, 246, 0.2) !important;
        }
      }

      .customCheckinTime {
        display: inline-grid;
        grid-template-columns: auto auto 1fr;
        padding: 0 10px;
        grid-gap: 10px;
        /* height: 52px; */
        /* overflow: hidden; */

        input[type='number'] {
          padding: 10px;
          max-width: 80px;
        }
      }
    }
  }

  .card-header--withbutton {
    display: grid;
    grid-template-columns: auto 1fr;
    grid-gap: 1rem;
  }
  .secondary-header {
    font-size: 0.88rem;
  }

  .save-button-holder {
    padding: 25px 0 0 0;
    button {
      width: 200px;
    }
  }
  .secondary-header {
    font-size: 0.88rem;
  }

  .two-part {
    display: grid;
    width: 100%;
    grid-template-columns: 1fr 1fr;
    grid-column-gap: 10px;

    & > div:first-child {
      border-right: 1px solid #eaeaea;
      padding-right: 20px;
    }

    .image-config {
      display: grid;
      grid-template-columns: 1fr auto;

      .upload-box {
        min-height: 220px;
        background: #e6f2f8;
        display: grid;
        grid-template-rows: 1fr auto;
        max-width: 220px;
        padding: 3px;

        img {
          width: 100%;
          background: #fff;
        }

        button {
          margin-top: 6px;
        }
      }

      .image-toggle__btn {
        display: grid;
        grid-template-columns: auto auto;
        justify-content: right;
        grid-column-gap: 10px;
        .toggle {
          padding-top: 10px;
          padding-top: 3px;
        }
      }
    }

    .sms-text-box {
      margin-top: 2rem;
    }
  }

   input{
    background: #ecf3f8;
   }
  
      
}
</style>
