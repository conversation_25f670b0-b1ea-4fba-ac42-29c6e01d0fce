<template>
  <b-modal
    ref="modal-sm-d"
    size="sm"
    @hide="close(false)"
    hide-footer
    hide-title
  >
    <template v-slot:modal-title>
      <h5 class="modal-title">Contact</h5>
    </template>
    <template>
      <div class="modal-body">
        <div class="modal-body--inner">
          <div class="personal-logo">
            <vue2Dropzone
              v-if="!photoUrl"
              class="picture drag_drop"
              @vdropzone-file-added="vfileAdded"
              :options="dropzoneOptions"
              :include-styling="false"
              id="customer_photo_dropzone"
              ref="file_drop_zone"
            ></vue2Dropzone>
            <div
              v-else
              class="picture"
              id="preview_container"
              ref="preview_container"
              :style="getPreviewImage"
            ></div>
            <div class="picture-text">
              <h4>Personal Logo</h4>
              <p>The proposed size is 512*512px</p>
              <p>no bigger than 2.5mb</p>
              <div class="btns">
                <UIButton
                  type="button"
                  use="secondary"
                  id="profileChangePhoto"
                  class="mr-2"
                >
                  Change
                </UIButton>
                <button
                  type="button"
                  @click.stop="photoUrl = undefined"
                  class="btn btn-light2"
                >
                  Remove
                </button>
              </div>
            </div>
          </div>
          <form @submit.prevent.stop="validateBeforeSubmit">
            <div class="row">
              <div class="col-sm-6">
                <div class="form-group">
                  <UITextInputGroup
                    type="text"
                    label="First Name"
                    placeholder="First Name"
                    v-model="firstName"
                    name="msgsndr5"
                    data-lpignore="true"
                    autocomplete="msgsndr5"
                    data-vv-as="First name"
                    :class="{
                      'msgsndr5': true
                    }"
                    :error="errors.has('msgsndr5')"
                    :errorMsg="errors.first('msgsndr5')"
                  />
                </div>
              </div>
              <div class="col-sm-6">
                <div class="form-group">
                  <UITextInputGroup
                    type="text"
                    label="Last Name"
                    placeholder="Last Name"
                    v-model="lastName"
                    name="msgsndr6"
                    data-lpignore="true"
                    autocomplete="msgsndr5"
                    data-vv-as="Last name"
                    :class="{
                      'msgsndr6': true
                    }"
                    :error="errors.has('msgsndr6')"
                    :errorMsg="errors.first('msgsndr6')"
                  />
                </div>
              </div>
            </div>
            <div class="form-group">
              <UITextInputGroup
                type="text"
                label="Email"
                placeholder="Email"
                v-model="email"
                name="msgsndr3"
                data-lpignore="true"
                autocomplete="msgsndr3"
                data-vv-as="Email"
                :class="{
                  'msgsndr3': true
                }"
                :error="errors.has('msgsndr3')"
                :errorMsg="errors.first('msgsndr3')"
              />
            </div>
            <div class="form-group">
              <UITextLabel>Phone</UITextLabel>
              <PhoneNumber
                placeholder="Phone"
                v-model="phone"
                v-validate="'phone'"
                name="msgsndr1"
                autocomplete="msgsndr1"
                :currentLocationId="currentLocationId"
              />
              <span v-show="errors.has('msgsndr1')" class="error">{{
                errors.first('msgsndr1')
              }}</span>
            </div>

            <div class="form-group">
              <UITextLabel>Contact Type</UITextLabel>
              <div class="dropdown bootstrap-select">
                <button
                  type="button"
                  class="btn dropdown-toggle btn-light"
                  data-toggle="dropdown"
                  role="button"
                  title="Lead"
                  aria-expanded="true"
                  @click.stop.prevent="typeDropDownToggler.toggle()"
                >
                  <div class="filter-option">
                    <div class="filter-option-inner">
                      <div class="filter-option-inner-inner capitalize">
                        {{ type }}
                      </div>
                    </div>
                  </div>
                </button>
                <div
                  class="dropdown-menu"
                  :id="typeDropDownToggler.id"
                  role="combobox"
                >
                  <div
                    class="inner show"
                    role="listbox"
                    aria-expanded="true"
                    tabindex="-1"
                    style="max-height: 749px; overflow-y: auto; min-height: 0px"
                  >
                    <ul class="dropdown-menu inner show">
                      <li>
                        <a
                          @click.prevent.stop="selectType('customer')"
                          role="option"
                          class="dropdown-item"
                          aria-disabled="false"
                          tabindex="0"
                          aria-selected="false"
                        >
                          <span class="text">Customer</span></a
                        >
                      </li>
                      <li>
                        <a
                          @click.prevent.stop="selectType('lead')"
                          role="option"
                          class="dropdown-item"
                          aria-disabled="false"
                          tabindex="0"
                          aria-selected="false"
                        >
                          <span class="text">Lead</span></a
                        >
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              <span v-show="errors.has('type')" class="error mr-1">{{
                errors.first('type')
              }}</span>
            </div>

            <div class="form-group">
              <UITextLabel>DND</UITextLabel>
              <div class="toggle mt-1">
                <UIToggle
                  class="tgl tgl-light"
                  id="account-buffer-tgld"
                  v-model="dnd"
                />
              </div>
            </div>

            <div
              class="modal-buttons d-flex align-items-center justify-content-between"
            >
              <UIButton
                use="outline"
                data-dismiss="modal"
                @click.prevent.stop="close(false)"
              >
                Close
              </UIButton>
              <div>
                <UIButton
                  type="submit"
                  use="primary"
                  :loading="processing"
                >
                  Save
                </UIButton>
              </div>
            </div>
          </form>
        </div>
      </div>
    </template>
  </b-modal>
</template>

<script lang="ts">
import Vue from 'vue'
import { v4 as uuid } from 'uuid'
import vue2Dropzone from 'vue2-dropzone'
import firebase from 'firebase/app'
import 'vue2-dropzone/dist/vue2Dropzone.min.css'
import { UxMessage } from '@/util/ux_message'
import { Contact, User } from '@/models'
import VueRouter from 'vue-router'
import Router from '../../../routes/index'
import store from '@/store'
import { SafeDropDownToggle } from './safe_drop_down_toggle'
import lodash from 'lodash'

const PhoneNumber = () => import('../../components/util/PhoneNumber.vue')

declare var $: any

export default Vue.extend({
  name: 'CustomerInputModal',
  components: { vue2Dropzone, PhoneNumber },
  props: ['showModal', 'contact', 'isAdmin', 'user', 'operation', 'quickAction'],
  inject: ['uxmessage'],
  data() {
    return {
      dropzoneOptions: {
        url: '/file/post',
        maxFilesize: 2.5,
        clickable: '#profileChangePhoto',
        resizeWidth: 1024,
        resizeHeight: 1024,
      },
      currentLocationId: '',
      processing: false,
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      type: 'lead',
      dnd: false,
      photoUrl: undefined as String | undefined,
      selectedFile: undefined as File | undefined,
      typeDropDownToggler: new SafeDropDownToggle(this, 'typeDropDownToggler'),
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
    },
    async contact() {
      if (this.contact) {
        this.firstName = this.contact.firstName || ''
        this.lastName = this.contact.lastName || ''
        this.email = this.contact.email || ''
        this.photoUrl = this.contact.profilePhoto
        this.phone = this.contact.phone || ''
        this.dnd = this.contact.dnd || false
        this.type = this.contact.type || 'lead'
      }
    },
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
  },
  async mounted() {
    await this.$nextTick()
    this.showPopup()
  },
  computed: {
    getPreviewImage() {
      if (this.photoUrl) {
        return {
          'background-image': 'url("' + this.photoUrl + '")',
          'background-size': '180px 180px',
        }
      }
    },
    getSideBarVersion(): string {
      return this.$store.getters['sidebarv2/getVersion'] 
    },
  },
  methods: {
    async vfileAdded(file: any) {
      const dropZone: any = this.$refs.file_drop_zone
      dropZone.removeAllFiles()

      this.selectedFile = file
      this.photoUrl = URL.createObjectURL(file)
    },
    async validateBeforeSubmit() {
      if (this.processing) return

      const result = await this.$validator.validateAll()
      if (!result) {
        return false
      }

      this.processing = true

      try {
        if (this.contact.profilePhoto && this.selectedFile) {
          var path = firebase.storage().refFromURL(this.contact.profilePhoto)
          await path.delete()
          this.contact.profilePhoto = ''
        }
        if (this.selectedFile) {
          let imagePath =
            'locationFiles/' +
            this.currentLocationId +
            '/contact/' +
            this.contact.id +
            '/' +
            uuid()
          var uploadPath = firebase.storage().ref(imagePath)

          const snapshot = await uploadPath.put(this.selectedFile, {
            contentDisposition: `inline; filename="${this.selectedFile.name}"`,
          })
          this.contact.profilePhoto = await snapshot.ref.getDownloadURL()
        }

        this.contact.firstName = this.firstName
        this.contact.lastName = this.lastName
        this.contact.email = this.email
        this.contact.phone = this.phone
        this.contact.dnd = this.dnd
        this.contact.type = this.type
        this.contact.internalSource = {
          type: 'manual_addition',
          id: this.user.id,
          userName: this.user.name,
        }
        if (this.user && this.user.isAssignedTo) {
          this.contact.assignedTo = this.user.id
        }
        const response = await this.contact.save()
        if (!(response instanceof Contact)) {
          this.processing = false
          this.showError(response)
        } else {
          this.processing = false
          this.close(true)
          this.redirectToContactsPage()
        }
      } catch (err) {
        this.processing = false
        this.showError(err)
        console.log(err)
      }
    },
    close(status: boolean) {
      if (this.processing) return
      this.$emit('closed2', status)
      this.hidePopup()
    },
    showPopup() {
      if(this.$refs['modal-sm-d']) this.$refs['modal-sm-d'].show()
    },
    hidePopup() {
      this.clear()
      if(this.$refs['modal-sm-d']) this.$refs['modal-sm-d'].hide()
    },
    clear() {
      this.errors.clear()
      this.selectedFile = undefined
      this.photoUrl = undefined
      this.processing = false
    },
    showError(err: string, stack?: string) {
      this.uxmessage(UxMessage.errorType(lodash.capitalize(err)), true)
    },
    selectType(type: string = '') {
      this.typeDropDownToggler.toggle()
      this.type = type
    },
    async showToast() {
      let msg = 'Contact Created'
      let contactDescription = `${this.contact.firstName} ${this.contact.lastName}`
      if (!this.contact.isNewRecord) msg = 'Updated Contact'

      const callback = () => {
        Router.push({
          name: this.getSideBarVersion == 'v2' ? 'contact_detail-v2': 'contact_detail',
          params: { contact_id: this.contact.id },
        })
      }
      let options = undefined
      if (this.contact.isNewRecord) {
        options = {
          link: 'Open Contact',
          callbackWithInfo: callback,
          returnInfo: this.contact.id,
        }
      } else {
        let canAccess = false
        const user = new User(await store.dispatch('user/get'))
        if (user.isAdmin || !user.isAssignedTo) canAccess = true
        else if (user.id === this.contact.assigned_to) canAccess = true
        if (canAccess)
          options = {
            link: 'Open Contact',
            callbackWithInfo: callback,
            returnInfo: this.contact.id,
          }
        else contactDescription += ' - assigned to someone else'
      }
      this.uxmessage(UxMessage.infoType(msg, contactDescription, options), true)
    },
    redirectToContactsPage() {
      if (!this.quickAction) {
        Router.push({
          name: this.getSideBarVersion == 'v2' ? 'contact_detail-v2': 'contact_detail',
          params: { contact_id: this.contact.id },
        })
      }
    },
  },
})
</script>
