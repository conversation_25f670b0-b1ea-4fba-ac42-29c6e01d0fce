<template>

    <div id="checkout">
      <div class="wrapper">
        <!-- For If -Loading -->
        <template v-if="loading">
            <div class="loader-backdrop">
              <div>
                <moon-loader :loading="loading" color="#188bf6" size="30px" />
                <!-- <p v-if="paymentStarted">
                  Payment is processing. Please do not reload or press back button
                </p> -->
              </div>
            </div>
        </template>
          <!-- For if -Error Msg -->
        <template v-else-if="errorMsg">
            <div class="errorWrapper">
              <div class="img-wrapper">
                <img src="/pmd/img/error.webp" alt="error img">
              </div>
              <p>{{errorMsg}}</p>
              <button class="retry btn btn-danger" @click.prevent="retry">retry</button>
            </div>
        </template>
          <!-- For  else load checkout-->
          <!-- two sides -->
          <!-- v-show="!loading && !errorMsg" -->
        <main class="checkout_wrapper" v-show="!loading && !errorMsg">
          <!-- left side - product info -->
          <section class="invoice_details common-style">
            <!-- First Line -->
            <div class="agency_details">
              <div class="back_link" v-if="options.isBackBtn">
                <a
                  href="#"
                  @click.prevent="$emit('cancel')"
                  id="yext_go_back_order_summary"
                >
                  <i class="fa fa-chevron-left" aria-hidden="true"></i>
                </a>
              </div>
              <div class="agency_logo_container">
                <template v-if="logo_URL">
                  <div
                    class="agency_logo"
                    :style="{ backgroundImage: `url(${logo_URL})` }"
                  ></div>
                </template>
                <template v-else>
                  <i class="fas fa-store"></i>
                </template>
              </div>
              <label class="agency_name"> {{companyName}} </label>
            </div>

            <!-- Second line - product Name-->
            <div class="product_details">
              <div class="product_name">
                <!-- if else -->
                <p class="title" v-if="priceDetail.recurring">Subscribe To {{productDetail.name ?productDetail.name : ""}}</p>
                <p class="title" v-else>purchase  {{productDetail.name}}</p>
              </div>
              <div class="product_price">
                <span class="price">${{priceDetail.unit_amount / 100}}.00</span>
                <!-- if recurring if else. -->
                <span class="unit" v-if="priceDetail.recurring"> {{priceDetail.recurring ? priceDetail.recurring.interval_count : 1  }}  month(s) commitment</span>
              </div>
              <div class="product_description">
                <p> {{priceDetail.nickname ? priceDetail.nickname : productDetail.description ? productDetail.description : "" }}</p>
              </div>
              <!-- image as bg -->
              <div class="product_logo" v-if="productImageURl" :style="{backgroundImage: `url(${productImageURl})`}" ></div>
              <div class="product_logo --default" v-else></div>
            </div>
          </section>

          <!-- Rightside - payment card info -->
          <section class="payment_card_info common-style">
            <!-- Loading - if payment started -->
            <template v-if="paymentStarted">
              <div class="loader-backdrop">
                <div>
                  <moon-loader :loading="paymentLoading" color="#188bf6" size="30px" />
                  <p>
                    Payment is being processed. Please do not reload or press back button
                  </p>
                </div>
              </div>
            </template>
            <!-- Success - if payment success -->
            <template v-else-if="isPaymentSuccess">
              <Success  :successMsg="successMsg" :productName="productDetail.name" />
            </template>
            <!-- v-show(coz stripe element needs to be in DOM) - paymentNotStarted & paymentNotSuccess -->
            <div class="payment_wrapper" v-show="!paymentStarted && !isPaymentSuccess">
              <!-- Show Payment Loading -->
              <div class="payment_card_container" >
                <h2 class="title">pay with card</h2>
                <div class="payment_card_details">
                  <!-- Radio Btn to toggle between cards -->
                  <div class="card-selection">
                    <div class="form-control mb-3 card-toggle" @click="selectCardOnChange({primary: true})">
                      <div class="card-radio" :class="{'--enabled': primaryCard }" />
                      <!-- <input type="radio" name="cardSelection" id="existingCard" :value="primaryCard"> -->
                      <label class="ml-2" for="existingCard">Proceed with existing card
                        <template v-if="primaryCard_last_four_digit">
                          <span> ending</span>
                          <span class="ml-2 mt-2 existing-card-number "> **** {{primaryCard_last_four_digit}} </span>
                        </template>

                      </label>
                    </div>
                    <div class="form-control mb-3 card-toggle" @click="selectCardOnChange({primary: false})">
                      <!-- <input type="radio" name="cardSelection" id="newCard" :value="!primaryCard"> -->
                      <div class="card-radio" :class="{'--enabled': !primaryCard }"/>
                      <label class="ml-2" for="newCard">Proceed with new card</label>
                    </div>
                  </div>
                  <!-- Radio Button -->

                  <!-- New Card - Form -> only show if !primaryCard-->
                  <!-- <form  v-show="!primaryCard" action="/charge" method="post" id="add-card-form"> -->
                  <form  v-show="!primaryCard"  action="/charge" method="post" id="add-card-form">
                    <div>
                      <template>
                        <UITextInputGroup
                          label="Contact Email"
                          type="email"
                          name="contact_email"
                          id="contact_email"
                          v-model="contactEmail"
                        />
                      </template>
                      <!-- Display Contact Email Error Msg -->
                        <div class="errorMsg">
                          <p v-if="contactEmailError">{{contactEmailError}}</p>
                      </div>
                    </div>
                    <!-- Stripe Elements -->
                    <div class="card_details">
                      <div class="stripe_element stripe_element_number">
                        <label> Card information </label>
                        <div class="card_group_inputs shadow-sm">
                          <!-- :class="{addBorderForError: cardExpiryError }" -->
                          <div>
                            <div
                              id="card-number"
                              class="card-input"
                              style="border: unset"
                              :class="{addBorderForError: cardNumberError }"
                            ></div>
                            <div class="second_row">
                              <div class="stripe_element stripe_element_expiry">
                                <div
                                  class="card-input"
                                  style="border-right: 1px solid #c8c8c8"
                                  :class="{addBorderForError: cardExpiryError }"
                                >
                                  <div id="card-expiry"></div>
                                </div>
                              </div>
                              <div class="stripe_element stripe_element_cvv">
                                <div class="card-input" :class="{addBorderForError: cardCvcError }" >
                                  <div id="card-cvc"></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <!-- Display Card Error Msg -->
                        <div class="errorMsg">
                          <p v-if="cardNumberError == 'Your card number is invalid.'">{{cardNumberError}}</p>
                          <p v-else-if="cardNumberError && cardExpiryError && cardCvcError">Please Update Card Details</p>
                          <p v-else-if="cardNumberError">{{cardNumberError}}</p>
                          <p v-else-if="cardExpiryError">{{cardExpiryError}}</p>
                          <p v-else-if="cardCvcError">{{cardCvcError}}</p>
                        </div>
                      </div>
                    </div>
                    <!-- NameonCard -->
                    <div>
                      <div class="stripe_element stripe_element_name">
                        <div id="card-name">
                            <UITextInputGroup
                            label="Name On Card"
                            type="text"
                            name="name_on_card"
                            id="name_on_card"
                            v-model="nameOnCard"
                            />
                        </div>
                      </div>
                    </div>
                    <!-- ZipCode -->
                    <div v-if="!primaryCard">
                      <UITextInputGroup
                        label="zip"
                        type="text"
                        id="zip"
                        name="zip"
                        v-model="postalCode"
                      />
                    </div>

                    <div class="actions">
                      <button
                        class="btn btn-success hl_calltoaction-btn"
                        @click.prevent="createPaymentOnSubmit()"
                        id="checkout_payment_confirm_btn"
                      >
                      {{priceDetail.recurring ? "Subscribe" : !priceDetail.recurring ? "Pay" : "Confirm" }}
                      </button>
                    </div>
                    <p class="consent" v-if="priceDetail.recurring">
                      <!-- By confirming your subscription, you allow {{ company.name }} -->
                      By confirming your subscription, you allow LeadConnector
                      to charge your card for this payment and future payment for this
                      subscriptions
                    </p>
                    <p class="consent" v-if="!priceDetail.recurring">
                      By confirming, you allow LeadConnector to charge your card for this payment.
                    </p>
                  </form>
                  <template v-if="primaryCard">
                    <div class="primarycard-consent">
                      <div class="actions">
                        <button
                          class="btn btn-success hl_calltoaction-btn"
                          @click="createPaymentOnSubmit()"
                          id="checkout_payment_confirm_btn"
                        >
                        {{priceDetail.recurring ? "Subscribe" : !priceDetail.recurring ? "Pay" : "Confirm" }}
                        </button>
                      </div>
                        <p class="consent" v-if="priceDetail.recurring">
                        <!-- By confirming your subscription, you allow {{ company.name }} -->
                        By confirming your subscription, you allow LeadConnector
                        to charge your card for this payment and future payment for this
                        subscriptions
                      </p>
                      <p class="consent" v-if="!priceDetail.recurring">
                        By confirming, you allow LeadConnector to charge your card for this payment.
                      </p>
                    </div>

                  </template>
                </div>
              </div>
            </div>
          </section>
        </main>
      </div>
    </div>
</template>

<script lang="ts">
import Vue from 'vue'
import config from '@/config'
import Success from '@/pmd/components/agency/billing/FunnelIframePayment/Success.vue'

declare var Stripe: any

export default Vue.extend( {
  components: {
    Success
  },
  props: {
    customerId: {
      type: String
    },
    priceId: {
      type: String
    },
    successMsg: {
      type: String
    },
    options:{
      type: Object
    },
    requestSource: {
      type: String
    }
  },
  data() {
    return {
      loading: false,
      stripeCustomerId: this.customerId,
      paymentStarted: false,
      paymentLoading: false,
      stripe: undefined as any,
      primaryCard_last_four_digit: this.$store.state?.company?.company?.card_last_4,
      primaryCard: true,
      cardNumber: undefined as any,
      cardNumberError: undefined,
      cardCvc: undefined as any,
      cardCvcError: undefined,
      cardExpiry: undefined as any,
      cardExpiryError: undefined,
      cardSelection: "existing_card",
      contactEmail: this.$store.state?.company?.company?.email,
      contactEmailError:undefined as any,
      nameOnCard: this.$store.state?.company?.company?.name || "",
      postalCode: this.$store.state?.company?.company?.postal_code || '',
      logo_URL: this.$store.state?.company?.company?.logo_url || '',
      companyName: this.$store.state?.company?.company?.name || "",
      priceIdError: undefined as any,
      companyId: this.$store.state?.auth?.user.companyId,
      userId: this.$store.state?.auth?.user.userId,
      // priceIdError: false,
      priceDetail: {} as any,
      productDetail: {} as any,
      productImageURl: undefined as any,
      productName: undefined as any,
      isPaymentSuccess: undefined as any,
      //isPaymentSuccess:true,
      errorMsg: undefined as any
    }
  },
  created() {
  },
  async mounted() {
    this.init()
    this.getPriceDetails(this.priceId)
  },
  methods: {
    async getPriceDetails(priceId: String){
      try {
        this.loading = true
        // If stripe Customer ID is undefined or does not exist, pass this message as customerId.
        if(!this.customerId){
          this.stripeCustomerId = ""
        }
        //let { data: { price, product } } = await this.$http.get(`/billing/iframe/get-price-details/${priceId}`,);
        let { data: { price, product } } = await this.$http.get(`/billing/iframe/get-price-details?priceId=${this.priceId}&customerId=${this.stripeCustomerId}`);
        this.priceDetail = price
        this.productDetail = product
        this.productImageURl = product.images[0]
        this.productName = product.name
        this.loading = false
      } catch (error) {
        this.errorMsg = error.response && error.response.data.msg ? `404 ${error.response.data.msg}` : error.response ? error.response.data.message : error.message.startsWith('Network Error') ? "Network Error, Please try again after sometime" : "Something went wrong"
        this.loading = false
      }
    },
    async init() {
      this.setupStripe()
    },
    setupStripe() {
      let recaptchaScript = document.getElementById('stripeV3')
      if (recaptchaScript) {
        // console.log('stripe already setup')
        this.stripe = Stripe(config.stripeKey, {
          // stripeAccount: this.stripeAccountId,
        })
        this.initCardElement()
        return
      }
      recaptchaScript = document.createElement('script')
      recaptchaScript.setAttribute('id', 'stripeV3')

      recaptchaScript.onload = () => {
        this.stripe = Stripe(config.stripeKey, {
          // stripeAccount: this.stripeAccountId,
        })
        this.initCardElement()
      }

      recaptchaScript.setAttribute('src', 'https://js.stripe.com/v3/')
      document.head.appendChild(recaptchaScript)
    },
    initCardElement() {
      const elements = this.stripe.elements()
      const style = {
        base: {
          color: '#000000',
          // padding: '15px 20px',
          fontFamily:
            'Roboto,system,-apple-system,BlinkMacSystemFont,".SFNSDisplay-Regular","Helvetica Neue",Helvetica,Arial,sans-serif',
          fontSmoothing: 'antialiased',
          fontSize: '13px',
          letterSpacing: '8px',
          '::placeholder': {
            color: '#b0b0b0',
          },
        },
        invalid: {
          color: '#cc0000',
          iconColor: '#f8a3d4',
        },
        // complete:{
        //   color:"#009000"
        // }
      }
      // showIcon:true
      this.cardNumber = elements.create('cardNumber', { style: style, showIcon:true })
      this.cardNumber.mount('#card-number')
      this.cardNumber.addEventListener('change', (event: any) => {
        if (event.error) {
          this.cardNumberError = event.error.message
        } else {
          this.cardNumberError = null
        }
      })

      this.cardCvc = elements.create('cardCvc', { style: style })
      this.cardCvc.mount('#card-cvc')
      this.cardCvc.addEventListener('change', (event: any) => {
        if (event.error) {
          this.cardCvcError = event.error.message
        } else {
          this.cardCvcError = null
        }
      })

      this.cardExpiry = elements.create('cardExpiry', { style: style })
      this.cardExpiry.mount('#card-expiry')
      this.cardExpiry.addEventListener('change', (event: any) => {
        if (event.error) {
          this.cardExpiryError = event.error.message
        } else {
          this.cardExpiryError = null
        }
      })
    },
    async createPaymentOnSubmit(){
      // this.loading = true
      // PL
      this.paymentLoading = true
      this.paymentStarted = true
      // Form Query
         let query = {
            customerId: this.customerId,
            priceId: this.priceId,
            cardSelection: this.cardSelection,
            userId: this.userId,
          }
        const queryString = Object.keys(query) .map((key) => key + "=" + query[key]).join("&");
        // For new Card.
        if(this.cardSelection == "new_card"){
          /*
          1.Validate Email
          */
          let isNotValidEmail = this.validateEmail()
          if(isNotValidEmail){
            // this.loading = false
            this.paymentStarted = false
            this.paymentLoading = false
            this.contactEmailError = isNotValidEmail
          }else{
            const {token, error} = await this.stripe.createToken(this.cardNumber,{
              name:this.nameOnCard,
              address_zip:this.postalCode
            });
            // If stripe error Exist show error message.
            if(error){
              // this.loading = false
              // this.errorMsg = error.message
              this.cardNumberError = error.message
              this.paymentStarted = false
              this.paymentLoading = false
              // Error Msg will be displayed in UI.
            }
            // If token exist create payment.
            if(token){
              // Clear Card Elements Details
              this.cardNumber.clear()
              this.cardCvc.clear()
              this.cardExpiry.clear()
              try {
                await this.$http.post(`/billing/iframe/init-payment/${this.companyId}?${queryString}`, {
                  token,
                  productName: this.productName,
                  requestSource: this.requestSource
                })
                // this.loading = false
                this.paymentStarted = false
                this.paymentLoading = false
                this.isPaymentSuccess = true
              } catch (error) {
                // this.loading = false
                this.paymentStarted = false
                this.paymentLoading = false
                this.errorMsg = error.response ? error.response.data.msg : error.message
              }
            }
          }
        }
        // For existing Card
        if(this.cardSelection == "existing_card"){
        //PriceId,customerId,tokenObject
          try {
            await this.$http.post(`/billing/iframe/init-payment/${this.companyId}?${queryString}`, {
              productName: this.productName,
              requestSource: this.requestSource,
            })
            // this.loading = false
            this.paymentStarted = false
            this.paymentLoading = false
            this.isPaymentSuccess = true

            }catch (error) {
            // this.loading = false
            this.paymentStarted = false
            this.paymentLoading = false
            // this.errorMsg =  error.response?.data?.msg.includes('no active card') ? "Payment could not be processed. Please try adding a card again." : error.response ? error.response.data.msg : error.message.startsWith('Network Error') ? "Network Error, Please try again after sometime" : "Something went wrong"
            this.errorMsg =  error.response?.data?.error ? "Payment could not be processed. Please try adding a card again." : error.response ? error.response.data.msg : error.message.startsWith('Network Error') ? "Network Error, Please try again after sometime" : "Something went wrong"
          }
        }
    },
    selectCardOnChange(payload: any){
      const {primary} = payload;
      this.primaryCard = primary? true : false;
      if(this.primaryCard){
        this.cardSelection = "existing_card"
        // Clear Card Elements Details
        this.cardNumber.clear()
        this.cardCvc.clear()
        this.cardExpiry.clear()
        this.contactEmailError = ""
      }else{
        this.cardSelection = "new_card"
      }
    },
    validateEmail() {
      let errors = ''
      if (!this.contactEmail) {
        errors += 'Contact email address required'
      } else if (!this.contactEmail.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
        errors += 'Invalid contact email'
      }
      if (errors) {
        return errors
      } else {
        return null
      }
    },
    retry(){
      this.errorMsg=false
      this.getPriceDetails(this.priceId)
    }
  },
  watch: {
    paymentLoading: function(newVal, oldVal){

      if(this.paymentLoading){
        this.$emit('loading', !this.paymentLoading)
      }
      if(!this.paymentLoading){
        this.$emit('loading', !this.paymentLoading)
      }
    }
  }
})
</script>

<style scoped lang="scss">

  *,
  *::before,
  *::after{
    padding: 0;
    margin: 0;
    box-sizing: border-box;
  }

  $DetailsFontSize:15px;

  @mixin consent {
    .actions {
      display: grid;
      grid-template-columns: 1fr;
      justify-content: right;
      grid-gap: 1px;
      padding: 10px 0;
      // padding: 10px 15%;

      #checkout_payment_confirm_btn {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        padding: 10px 0;
      }
    }
    .consent {
      text-align: center;
      color: #1a1a1a99;
      padding: 0px 10px;
      margin-top: 5px;
      font-size: 0.8rem;
    }
  }

  // Loading
    .loader-backdrop {
      background: rgba(255,255,255,1);
      position: relative;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: grid;
      justify-content: center;
      align-items: center;
      z-index: 2;
      p {
        margin-top: 10px;
        padding: 10px;
        color: #009000;
        font-weight: 500;
        text-align: center;
      }
    }

  #checkout{
    overflow: hidden;
    .wrapper{
      // height: calc(100vh - 15%);
      height: calc(100vh - 115px);
      //padding: 10%;

      // Loading
      // ErrorMsg
      .priceIdError,
      .errorWrapper{
        height: 100%;
        //height: 75vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: red;
        padding: 0 5%;
        text-align:center;

        .img-wrapper{
          //border: 1px solid red;
          height: 500px;
          img{
            height: 100%;
            width: 100%;
          }
        }

        p{
          font-size: 1rem;
        }
        .retry{
          margin: 5px 0;
          padding: 10px 15px;
          text-transform: capitalize;
          //background: rgba(184, 182, 182,1);
        }
      }
      // Checkout
      .checkout_wrapper{
        display: grid;
        grid-template-columns: repeat(2,1fr);
        //border: 1px solid red;
        height: 100%;

        .common-style{
          //padding: 17.5% 15%;
        }
        .invoice_details{
            padding: 17.5% 15%;
          // Agency Details
          .agency_details{
            display: flex;
            margin-bottom: 30px;

            .back_link {
              text-align: center;
              position: absolute;
              left: 2%;
              font-size: 1.5rem;
              a {
                color: #7b7976;
                display: block;
                width: 40px;
                height: 40px;
                background: #fff;
                border-radius: 50px;
                display: flex;
                justify-content: center;
                align-items: center;
                transition: all 0.4s;
              }
              a:hover {
                background: #f2f0ef;
              }
            }

            .agency_logo_container {
              background: white;
              width: 40px;
              height: 40px;
              display: grid;
              justify-content: center;
              align-items: center;
              border-radius: 50%;
              overflow: hidden;
              box-shadow: 0px 1px 6px 0px #00000038;
            .agency_logo {
                width: 30px;
                height: 30px;
                /* border-radius: 50%; */
                background-size: contain;
                background-repeat: no-repeat;
                background-position: center;
            }
            }

            .agency_name{
              font-size: $DetailsFontSize;
              margin-left: 15px;
              text-transform: capitalize;
              align-self: center;
              color: #1a1a1ae6;
            }
          }

          // Product_details
          .product_details{
            .product_name,
            .product_price,
            .product_description{
              font-size: $DetailsFontSize;
              text-transform: capitalize;
              margin-bottom: 20px;
              color: #1a1a1a99;
            }

            .product_price{
              .price{
                font-size: 2.3rem;
                color: #1a1a1ae6;
                font-weight: 500;
              }
            }

            .product_description p {
              font-size: 0.9rem;
              font-weight: 500;
              //color: #1a1a1a99;
              text-transform: capitalize;

            }

            .product_logo {
              width: 18vh;
              height: 18vh;
              max-height: 224px;
              max-width: 224px;
              background-size: cover;
              margin: 8vh auto;
              border-radius: 50%;

              &.--default {
                background-image: url('/pmd/img/product-icon.svg');
                padding: 32px;
                background-color: rgba(247, 250, 252, 0.5);
                box-shadow: 0 0 0 1px #e3e8ee;
                background-size: 8vh;
                background-repeat: no-repeat;
                background-position: center;
              }
            }
          }
        }

        .payment_card_info{
          box-shadow: 15px 0 30px 0 #0000002e;
          //border: 1px solid red;
          padding: 17.5% 0;

          .payment_card_container{
            // border: 1px solid green;
            .title{
              font-size: 1.4rem;
              font-weight: 500;
              color: #1a1a1ae6;
              text-transform: capitalize;
              padding: 0 15%;
            }

            .payment_card_details{
              // Card Selection:

              .card-selection{
                padding: 0 15%;
                margin-top: 20px;
                  label{
                    text-transform: capitalize;
                    font-size: 12.5px;
                    cursor: pointer;
                  }
                  .existing-card-number{
                    color: green;
                  }
                .card-toggle {
                  display: flex;
                  cursor: pointer;
                  padding: 20px 15px;
                 // background: rgba(247, 250, 252, 0.5);
                }
                .card-radio{
                  height: 18px;
                  width: 18px;
                  border: 1px solid #CBD5E0;
                  border-radius: 50%;
                  cursor: pointer;
                  // margin: auto;
                  transition: all 0.3s ease-in-out;
                  &.--disabled {
                    cursor: not-allowed;
                    pointer-events: none;
                  }
                  &.--enabled{
                    border: 6px solid #158bf5;
                    &.--disabled {
                      border: 6px solid #afb8bb;
                    }
                  }
                }
              }

              // Form
              form {
                display: grid;
                grid-gap: 5px;
                // height: inherit;
                max-height: calc(100vh - 450px);
                overflow: auto;
                padding: 0 15%;
               //border: 1px solid green;


                .card_details {
                  position: relative;

                  .card_group_inputs {
                    border: 1px solid #d1d5db;
                    border-radius: 5px;
                    .second_row {
                      display: grid;
                      grid-template-columns: 1fr 1fr;
                      border-top: 1px solid #c8c8c8;
                    }

                    label {
                      font-weight: 500;
                    }
                    .card-input {
                      padding: 0 8px;
                      min-height: 36px;
                      min-width: 50px;
                      display: grid;
                      /* -webkit-box-align: center; */
                      -ms-flex-align: center;
                      align-items: center;
                      outline: none;
                      border: unset;
                      //border: 1px solid red !important;
                      /* color: #fff; */
                      letter-spacing: 2px;
                      letter-spacing: 2px;
                      .card-input {
                        /*  background-color: unset; */
                      }
                    }
                  }
                }

                .errorMsg{
                  padding: 5px 0;

                  p{
                    color: red;
                    text-align: center;
                    text-transform: capitalize;
                  }
                }

                // Add this class when error occurs
                .addBorderForError{
                  border: 1px solid red !important;
                }
              }
              .actions {
                display: grid;
                grid-template-columns: 1fr;
                justify-content: right;
                grid-gap: 1px;
                padding: 10px 0;

                #checkout_payment_confirm_btn {
                  border-top-left-radius: 0;
                  border-bottom-left-radius: 0;
                  padding: 10px 0;
                }
              }
              .consent {
                text-align: center;
                color: #1a1a1a99;
                padding: 0px 10px;
                margin-top: 5px;
                font-size: 0.8rem;
              }

              .primarycard-consent{
                @include consent;
                padding: 0 15%;
              }
            }
          }
        }
      }
    }
  }
</style>
