<template>
  <div class="table-with-collapse" style="display: contents">
    <tr
      class="table-collapse page-stat"
      :class="{'table-collapse-toggle': productsInPage, active: showingProducts, deleted: pageStat.is_deleted}"
      :key="pageStat.page_id"
      @click="toggleShowingProducts()"
    >
      <td>
        <i v-if="productsInPage" class="icon-arrow-right-1"></i>
        <i
          :class="[pageStat.is_control ? 'fas': 'far', 'fa-flag']"
          :style="{marginLeft: productsInPage ? '0' : '25px', 'icon-deleted': pageStat.is_deleted}"
        ></i>
        {{ pageStat.name || 'Page' }}
      </td>
      <td>{{pageStat.page_views_all || '-'}}</td>
      <td>{{pageStat.page_views_unique || '-'}}</td>
      <td>{{pageStat.optins_all || '-'}}</td>
      <td>{{pageStat.optins_rate ? `${pageStat.optins_rate}%` : '-'}}</td>
      <td>{{pageStat.sale_count }}</td>
      <td>{{pageStat.sale_rate ? `${pageStat.sale_rate}%` : '-'}}</td>
      <td>{{pageStat.sale_value ? `$${pageStat.sale_value}` : '-'}}</td>
      <td>{{pageStat.earnings_per_page_view_all ? `$${pageStat.earnings_per_page_view_all}` : '-' }}</td>
      <td>{{pageStat.earnings_per_page_view_unique ? `$${pageStat.earnings_per_page_view_unique}`: '-'}}</td>
    </tr>
    <tr
      v-if="showingProducts"
      v-for="prodStat in pageStat.products"
      class="table-collapse product-stat"
      :class="{deleted: prodStat.is_deleted}"
    >
      <td>
        <i class="icon icon-cart"></i>
        {{prodStat.name}}
      </td>
      <td>-</td>
      <td>-</td>
      <td>-</td>
      <td>-</td>
      <td>{{ prodStat.sale_count }}</td>
      <td>{{ prodStat.sale_rate }}%</td>
      <td>${{ prodStat.sale_value }}</td>
      <td>-</td>
      <td>-</td>
    </tr>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  name: 'FunnelPageStats',
  props: {
    funnelPageStats: {
      type: Object
    }
  },
  data() {
    const productsInPage = this.funnelPageStats.products && this.funnelPageStats.products.length;
    return {
      pageStat: this.funnelPageStats,
      showingProducts: false,
      productsInPage
    }
  },
  methods: {
    toggleShowingProducts: function() {
      this.showingProducts = !this.showingProducts
    }
  }
})
</script>

<style scoped>
.hl_funnels--stats .stats--table tbody tr.product-stat td:first-child {
  padding-left: 90px;
}

.icon-deleted {
  color: #516e86 !important;
}

tr.deleted td:first-child {
  opacity: 0.5;
}
</style>
