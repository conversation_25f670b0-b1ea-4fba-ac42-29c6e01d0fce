<template>
	<div>
		<SideBarV2 v-if="getSideBarVersion === 'v2'"/>
    	<SideBar v-else />
		<TopBar/>
		<section class="hl_wrapper">
			<section
				class="hl_wrapper--inner hl_agency hl_agency-sales-resources"
				id="ideas"
				style="background-color:white;"
			>
				<iframe
					src="https://ideas.gohighlevel.com"
					style="height: calc(100vh - 140px);width:100%;border:none;"
				></iframe>
			</section>
		</section>
	</div>
</template>



<script lang="ts">
import Vue from 'vue';
import { Location, User } from '@/models';
import TopBar from '../../components/TopBar.vue';
import SideBar from '../../components/agency/SideBar.vue';
import SideBarV2 from '../../components/sidebar/SideBar.vue'

let unsubscribeLocations: () => void;

export default Vue.extend({
	components: { TopBar, SideBar, SideBarV2 },
	data() {
		return {
			files: [],
			searchText: '' as string,
			loading: false as boolean
		}
	},
	computed: {
		user() {
			const user = this.$store.state.user.user
			return user ? new User(user) : undefined
		},
		getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
	},
	async created() {

	},
	mounted() {
		let recaptchaScript = document.createElement('script')
		recaptchaScript.setAttribute('src', 'https://secure.aha.io/assets/idea_portals/embedded/application.js')
		recaptchaScript.setAttribute('data-portal-url', 'https://gohighlevel.ideas.aha.io/')
		document.head.appendChild(recaptchaScript)
	},


	methods: {


	}
});
</script>

<style scoped>
.spinner {
	display: table;
	margin: 0 auto;
}
</style>

