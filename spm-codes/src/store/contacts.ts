import { <PERSON><PERSON><PERSON>, ActionContext } from 'vuex'
import Vue from 'vue'
import { Contact } from '../models'
import { RootState, ContactsState } from '../store/state_models'

let contactListner

class LocalCachedContacts {
  data = {}
  timeout = 3000

  remove(key) {
    delete this.data[key];
  }

  exist (contactId: string) {
    return !!this.data[contactId] && ((new Date().getTime() - this.data[contactId]._) < this.timeout);
  }

  get (contactId: string) {
    console.log('getting contact from local cache ' + contactId)
    return this.data[contactId].data ? new Contact(this.data[contactId].data) : undefined;
  }

  set(contact: Contact) {
    if (!contact) return
    this.remove(contact.id);

    this.data[contact.id] = {
      _: new Date().getTime(),
      data: {
        id: contact.id,
        ...contact.data
      }
    };
  }
}

const cachedContacts = new LocalCachedContacts

export const contacts: Module<ContactsState, RootState> = {
  namespaced: true,
  state: {
    ids: new Set(),
    contacts: {},
    deletedContacts: {},
    locationId: undefined
  },
  getters: {
    getById: (state: ContactsState) => {
      return (id: string) => state.contacts[id]
    }
  },
  mutations: {
    addToWatchList: (state: ContactsState, ids: string | string[]) => {
      if (typeof ids === 'string') {
        ids = [ids]
      }

      ids.forEach(id => {
        if (id) {
          state.ids.add(id)
        }
      })
    },
    clearAll: (state: ContactsState) => {

      if (contactListner) {
        contactListner()
      }

      state.contacts = {}
      state.deletedContacts = {}
      state.locationId = undefined
    },
    addUpdate(state: ContactsState, contact: any) {
      if (!contact.deleted) {
        Vue.set(state.contacts, contact.id, Object.freeze(contact))
        state.ids.add(contact.id)
      } else {
        if (state.contacts[contact.id]) {
          Vue.delete(state.contacts, contact.id)
        }
        Vue.set(state.deletedContacts, contact.id, true)
      }
    },
    addIfNotExists(state: ContactsState, contact: any) {
      Vue.set(state.contacts, contact.id, Object.freeze(contact))
    },
    addDeletedContact(state: ContactsState, id: any) {
      Vue.set(state.deletedContacts, id, true)
    }
  },
  actions: {
    async syncGet(context: ActionContext<ContactsState, RootState>, id: string): Promise<Contact> {
      // if (context.state.contacts[id]) {

      //   return Contact.createNewWithVuex(context.state.contacts[id])
      // }

      // if(context.state.deletedContacts[id]) {

      //   return
      // }
      // try {

        if (cachedContacts.exist(id)) {
          return cachedContacts.get(id)
        }

        const contact = await Contact.getById(id)
        if (contact) cachedContacts.set(contact)
        // if (contact) {

        //   context.commit('addUpdate', {
        //     id: contact.id,
        //     ...contact.data
        //   })
        //   context.commit('addToWatchList', id)
          return contact
      //   } else {
      //     context.commit('addDeletedContact', id)
      //     return
      //   }
      // } catch(err) {}
    },
    async update(context: ActionContext<ContactsState, RootState>, id: string): Promise<Contact> {
      try {
        const contact = await Contact.getById(id)
        if (contact) {

          context.commit('addUpdate', {
            id: contact.id,
            ...contact.data
          })
          context.commit('addToWatchList', id)
          return contact
        } else {
          context.commit('addDeletedContact', id)
          return
        }
      } catch(err) {}
    },
    async getAndWatch(context: ActionContext<ContactsState, RootState>, id: string) {
      if (context.state.contacts[id]) {

        return
      }

      if(context.state.deletedContacts[id]) {

        return
      }

      const contact = await Contact.getById(id)
      if (contact) {

        context.commit('addUpdate', {
          id: contact.id,
          ...contact.data
        })
        context.commit('addToWatchList', id)
      } else {
        context.commit('addDeletedContact', id)
      }
    },
    syncAll(
      context: ActionContext<ContactsState, RootState>,
      locationId: string
    ) {
      // if (!locationId) {
      //   context.commit('clearAll')
      //   return
      // }

      // if (context.state.locationId === locationId) {
      //   return
      // }

      // if (contactListner) {
      //   contactListner()
      // }



      // contactListner = Contact.fetchAllContactsByUpdatedDeleted(locationId)
      //   .limit(5)
      //   .onSnapshot(
      //     querySnapshot => {

      //       if (!querySnapshot.metadata.hasPendingWrites) {
      //         for (let docChange of querySnapshot.docChanges()) {
      //           if (context.state.ids.has(docChange.doc.id) || context.state.deletedContacts[docChange.doc.id]) {
      //             const doc = docChange.doc

      //             context.commit('addUpdate', {
      //               id: doc.id,
      //               ...doc.data()
      //             })
      //           }
      //         }
      //       }
      //     }, err => { return }
      //   )
    },
    logout: {
      root: true,
      handler: async (context: ActionContext<ContactsState, RootState>) => {
        context.commit('clearAll')
      }
    },
    clearAll: {
      handler: async (context: ActionContext<ContactsState, RootState>) => {
        context.commit('clearAll')
      }
    },
    resetAll: {
      handler: async (context: ActionContext<ContactsState, RootState>) => {
        // context.commit('clearAll')
        // context.commit('syncAll', context.state.locationId)
      }
    }
  }
}
