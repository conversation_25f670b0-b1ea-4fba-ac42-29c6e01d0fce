<template>
	<div :class="{'-notification': !notification.read}">
    <Avatar :contact="contact" :size="'sm'"/>
		<p v-html="notification.notificationData.title"></p>
		<p v-html="notification.notificationData.body"></p>
		<p class="location">{{getLocationName}}</p>
		<p class="time-date">{{notification.dateAdded.calendar()}}</p>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
import { Contact } from '../../../models';
const Avatar = () => import( '../Avatar.vue');

export default Vue.extend({
  props: ['notification'],
  components: { Avatar },
	data() {
		return {
			contact: undefined as Contact | undefined
		}
	},
	computed: {
		location(): { [key: string]: any } {
			return this.$store.getters['locations/getById'](this.notification.locationId);
		},
		getLocationName(): string {
			if (!this.location) return '';
			let name = this.location.name;
			if (this.location.city || this.location.state) name += ', ';
			if (this.location.city) name += this.location.city;
			if (this.location.city && this.location.state) name += ', ';
			if (this.location.state) name += this.location.state;
			return name;
		}
  },
  async created() {
		if (this.notification.notificationData.contactId) {
			this.contact = await Contact.getById(this.notification.notificationData.contactId);
		}
	}
});
</script>
