<template>
  <Filters
    :conditions="conditions"
    :filterMaster="filterMaster"
    v-on:update:conditions="$emit('update:conditions', $event)"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import { Funnel, FunnelPage } from '@/models'
const Filters = () => import('./Filters.vue')
import { find, some, cloneDeep, findIndex } from 'lodash'
import {
  ICondition,
  IFilterMaster,
  ITriggerFilterOptions,
} from '../../../types/trigger'
import { FunnelsServices } from '@/services'

export default Vue.extend({
  props: {
    conditions: {
      type: Array,
      required: true,
    },
  },
  components: { Filters },
  data() {
    return {
      filterMaster: [
        {
          placeHolder: 'Select funnel/website',
          title: 'In funnel/website',
          value: 'twoStepOrderForm.funnelId',
          valueType: 'text',
          type: 'select',
          options: [] as { [key: string]: any }[],
        },
        {
          placeHolder: 'Select type',
          title: 'Submission type',
          value: 'twoStepOrderForm.submissionType',
          valueType: 'number',
          type: 'select',
          options: [
            { title: 'OptIn', value: 1 },
            { title: 'Sale', value: 2 },
            { title: 'Upsell', value: 3 },
            { title: 'Bump', value: 4 },
          ],
        },
      ] as IFilterMaster[],
      funnels: [] as Array<Funnel>,
      currentLocationId: '',
    }
  },
  watch: {
    conditions: {
      handler: function (conditions, oldCondition) {
        this.conditionsChanged(cloneDeep(conditions))
      },
      deep: true,
    },
  },
  methods: {
    removeExistingFilter(fieldName: string) {
      const index = this.filterMaster.findIndex(
        (x: any) => x.value === fieldName
      )
      if (index > -1) {
        this.filterMaster.splice(index, 1)
      }
    },
    async conditionsChanged(condition: Array<ICondition>) {
      const funnelIdOption = find(condition, {
        field: 'twoStepOrderForm.funnelId',
      })

      const pageIdOption = find(condition, {
        field: 'twoStepOrderForm.pageId',
      })

      if (funnelIdOption && funnelIdOption.value) {
        var funnelPage: IFilterMaster = {
          placeHolder: 'Select page',
          title: 'Funnel/Website page',
          value: 'twoStepOrderForm.pageId',
          type: 'select',
          valueType: 'text',
          options: [],
        }
        var funnel = find(this.funnels, {
          id: funnelIdOption.value,
        })
        const funnelPages = await FunnelPage.getByFunnelId(
          funnelIdOption.value as string,
          this.currentLocationId
        )

        const pageDetails = funnelPages.map(page => {
          return {
            title: page.name,
            value: page.id,
          }
        })

        funnelPage.options = pageDetails

        this.removeExistingFilter('twoStepOrderForm.pageId')
        this.filterMaster.push(funnelPage)
      } else if (
        (!funnelIdOption || !funnelIdOption.value) &&
        some(this.filterMaster, {
          value: 'twoStepOrderForm.pageId',
        })
      ) {
        let refreshedCondition = cloneDeep(this.conditions)
        if (
          findIndex(refreshedCondition, {
            field: 'twoStepOrderForm.pageId',
          }) !== -1
        ) {
          refreshedCondition.splice(
            findIndex(refreshedCondition, {
              field: 'twoStepOrderForm.pageId',
            }),
            1
          )
          this.$emit('update:conditions', refreshedCondition)
        }
        this.filterMaster.splice(
          findIndex(this.filterMaster, {
            value: 'twoStepOrderForm.pageId',
          }),
          1
        )
      }

      //  products filter
      if (pageIdOption?.value && funnelIdOption?.value) {
        const funnel = find(this.funnels, {
          id: funnelIdOption.value,
        }) as Funnel | undefined

        if (!funnel) return
        const funnelStep = find(funnel?.steps, function (step) {
          return step.pages.includes(pageIdOption?.value as string)
        })
        var funnelProducts: IFilterMaster = {
          placeHolder: 'Select product',
          title: 'Product',
          value: 'twoStepOrderForm.productId',
          type: 'select',
          valueType: 'text',
          options: [],
        }
        if (funnel.orderFormVersion === 2) {
          const { data } = await FunnelsServices.listFunnelProducts({
            locationId: funnel.locationId,
            funnel: funnel.id,
            step: funnelStep?.id as string,
          })
          funnelProducts.options = data.products.map((product: any) => {
            return {
              title: product?.name,
              value: product?._id,
            }
          })
        } else {
          funnelProducts.options =
            funnelStep?.products?.map((product: any) => {
              return {
                title: product?.productName,
                value: product?.id,
              }
            }) || []
        }

        this.removeExistingFilter('twoStepOrderForm.productId')
        this.filterMaster.push(funnelProducts)
        // pull products for funnel step
      } else if (!pageIdOption?.value || !funnelIdOption?.value) {
        this.removeExistingFilter('twoStepOrderForm.productId')
      }
    },
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    const funnelIdOption = find(this.filterMaster, {
      value: 'twoStepOrderForm.funnelId',
    })
    if (funnelIdOption) {
      this.funnels = await Funnel.getAllByLocation(this.currentLocationId)
      const options = this.funnels.map(funnel => {
        return {
          title: funnel.name,
          value: funnel.id,
        }
      })

      funnelIdOption.options = options as ITriggerFilterOptions
    }
    this.conditionsChanged(this.conditions as Array<ICondition>)
  },
})
</script>
