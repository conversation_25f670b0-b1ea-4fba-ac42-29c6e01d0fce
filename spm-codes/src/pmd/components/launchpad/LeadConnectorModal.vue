<template>
  <modal
    v-if="modalShow"
    :maxWidth="popupWidth"
    @close="closeModal"
    :showCloseIcon="true"
    :noBackdropClose="true"
  >
    <div class="mobile-modal-container" style="padding-top: 20px">
      <div class="card" style="width: 100%; margin-bottom: 0px">
        <div class="card-body card-body-details">
          <div class="mobile-modal-title my-2">
            A link to download the app has been sent to
          </div>
          <div class="mobile-user-container mb-4">
            <div class="mobile-sent-info mb-2">
              <EmailArtwork
               style="margin-right: 10px"
               />
              {{ email }}
            </div>
            <div class="mobile-sent-info">
              <SmsArtwork
                style="margin-right: 10px"
              />
              {{ phone }}
            </div>
          </div>
          <div v-if="mobileError" class="mobile-sub-title mb-4">
            Please open this link from your mobile device
          </div>
          <div class="mobile-applink-title mb-2">
            You can also get the App directly
          </div>
          <div class="mobile-applink-container mb-4">
            <a
              target="_blank"
              :href="iosUrl"
            >
              <AppStoreIcon
                style="width: 120px"
              />
            </a>
            <a
              target="_blank"
              :href="androidUrl"
            >
              <PlayStoreIcon
                style="width: 120px"
              />
            </a>
          </div>
        </div>
        <div class="card-footer">
          <button id="launchpad-lc-sendlink-request" class="btn btn-primary active yes-button" @click="closeModal">
            Okay, got it!
          </button>
        </div>
      </div>
    </div>
  </modal>
</template>
<script>
import Vue from 'vue'
import Modal from '@/pmd/components/common/Modal.vue'
import EmailArtwork from '@/assets/pmd/img/launchpad/email_artwork.svg'
import SmsArtwork from '@/assets/pmd/img/launchpad/sms_artwork.svg'
import AppStoreIcon from '@/assets/pmd/img/launchpad/appstore-icon.svg'
import PlayStoreIcon from '@/assets/pmd/img/launchpad/playstore-icon.svg'
export default Vue.extend({
  props: [
    'email',
    'phone',
    'shortenedUrl',
    'modalShow',
    'mobileError',
    'company',
  ],
  components: {
    Modal,
    EmailArtwork,
    SmsArtwork,
    AppStoreIcon,
    PlayStoreIcon
  },
  data() {
    return {
      popupWidth: 500,
    }
  },
  computed: {
    androidUrl() {
      let url =
        'https://play.app.goo.gl/?link=https://play.google.com/store/apps/details?id=com.LeadConnector'
      if (this.company.whitelabelAppLinks?.android)
        url = `https://play.app.goo.gl/?link=${this.company.whitelabelAppLinks.android}`

      return url
    },
    iosUrl() {
      let url = "https://apps.apple.com/app/lead-connector/id1564302502"
      if (this.company.whitelabelAppLinks?.ios)
        url = this.company.whitelabelAppLinks?.ios

      return url
    },
  },
  methods: {
    closeModal() {
      this.$root.$emit('close-modal')
    },
  },
})
</script>
<style>
.mobile-modal-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 450px;
  text-align: center;
  width: 100%;
}
.mobile-applink-title {
  color: #374151;
  font-family: Roboto;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 16px;
}
.mobile-applink-container {
  width: 350px;
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;
}
.mobile-user-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: start;
  padding: 14px 10px;
  font-family: Roboto;
  font-style: normal;
  font-size: 14px;
  line-height: 157.19%;
  background: #eff6ff;
  border: 1px solid #eff6ff;
  box-sizing: border-box;
  border-radius: 3px;
}
.modal-details {
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: flex-start;
  padding: 0px 10px;
  width: 100%;
}
.card-body-details {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.mobile-modal-title {
  font-family: Roboto;
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 157.19%;
  color: #374151;
}
.mobile-sent-info {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  color: #3b82f6;
  font-weight: 500;
}
</style>
