<template>
  <thead>
    <tr class="placeholder">
      <td class="main-td">
        <div class="image loading"></div>
      </td>
      <td>
        <div class="td loading"></div>
      </td>
      <td>
        <div class="td2 loading"></div>
      </td>
      <td>
        <div class="td loading"></div>
      </td>
      <td>
        <div class="td0 loading"></div>
      </td>
      <td>
        <div class="td loading"></div>
      </td>
      <td>
        <div class="td loading"></div>
      </td>
      <!-- <td>
        <div class="td0 loading"></div>
      </td> -->
    </tr>
  </thead>
</template>

<style scoped>
.card {
  width: 100%;
  border: 1px solid #dddfe2;
  display: grid;
  grid-template-columns: 30% 20% 10% 20% 13%;
  grid-column-gap: 20px;
  height: 60px;
}
.main-td {
  padding-left: 30px;
}
.card .image {
  background: #e9ebee;
  width: 50px;
  height: 50px;
  border-radius: 50%;
}
.td,
.td0,
.td1,
.td2 {
  background: #e9ebee;
  height: 20px;
  border-radius: 5px;
  margin-top: 13px;
}
.card .td {
  width: 150px;
}
.card .td1 {
  width: 100px;
}
.card .td2 {
  width: 100px;
}
.card .td0 {
  width: 50px;
}

.card .bars {
  height: 85px;
}
.loading {
  position: relative;
  overflow: hidden;
}
.loading::after {
  content: '';
  display: block;
  background-color: #dddfe2;
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  transform: translateX(0);
  animation: 1.5s loading-placeholder ease-in-out infinite;
}
@keyframes loading-placeholder {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
</style>
