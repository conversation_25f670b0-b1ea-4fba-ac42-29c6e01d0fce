<template>
  <div>
    <TopBar />
    <SideBar />
    <CreateSubAccountModal v-if="user" :modalShow="true" :userId="user.id" />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import TopBar from '@/pmd/components/TopBar.vue'
import SideBar from '@/pmd/components/agency/SideBar.vue'
import CreateSubAccountModal from '@/pmd/components/launchpad/CreateSubAccountModal.vue'

import { mapState } from 'vuex'
import { User, Company } from '@/models'
import { UserState } from '@/store/state_models'
import LocationsService from '@/services/LocationsService'

export default Vue.extend({
  name: 'LocationLaunchpad',
  components: {
    TopBar,
    SideBar,
    CreateSubAccountModal,
  },
  data() {
    return {}
  },
  async created() {
    const isExists = await this.isLocationExists()
    if (isExists) this.$router.push({ name: 'dashboard' })
  },
  mounted() {
    this.$root.$on('redirect-launchpad', (locationId: any) => {
      this.$router.push({
        name: 'location_launchpad',
        params: { location_id: locationId },
        replace: true,
      })
    })
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
  },
  methods: {
    async isLocationExists() {
      try {
        let locations = []
        const auth = await this.$store.dispatch('auth/get')
        await LocationsService.search(auth.companyId, false, undefined, undefined, 50).then((response) => {
            locations = response.locations.map((location: any) => {
                return {...location, id: location._id}
            })
        }).catch(err => {
        })
        if (locations.length > 0) {
          await Company.collectionRef().doc(this.company.id).update({
            'onboarding_info.location': false,
            'onboarding_info.conversationDemo': false
          })
          return true
        }
        return false
      } catch (err) {
        return false
      }
    }
  }
})
</script>

<style scoped>
@media (min-width: 768px) {
  body {
    padding-top: 0px !important;
  }
}
</style>
