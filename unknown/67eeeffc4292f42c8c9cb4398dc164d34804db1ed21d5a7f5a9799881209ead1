<template>
	<div class="card">
		<a href="javascript:void(0);" @click.prevent="$emit('update:action')" class="close">
			<i class="icon icon-close"></i>
		</a>
		<div class="card-body">
			<div class="form-group">
				<label>What action should we perform?</label>
				<div class="form-input-group space-x-1">
					<div class="form-input-group-item dropdown">
            <div data-toggle="dropdown">
              <i class="icon icon-arrow-down-1"></i>
              <input
                type="text"
                class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
                value="Drip"
                placeholder="Destination"
                v-model="formattedActionDestination"
              >
            </div>
						<div class="dropdown-menu">
							<a
								class="dropdown-item"
								href="javascript:void(0);"
								@click.prevent="formattedActionDestination='highlevel'"
							>{{company.name || 'Internal' }}</a>
							<a
								class="dropdown-item"
								href="javascript:void(0);"
								@click.prevent="formattedActionDestination='facebook'"
							>Facebook</a>
						</div>
					</div>
					<div class="form-input-group-item dropdown">
            <div data-toggle="dropdown" style="display: flex; flex-direction: row">
              <i class="icon icon-arrow-down-1"></i>
              <input
                type="text"
                class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
                placeholder="Choose an action"
                v-model="formattedActionType"
              >
			  <i v-if="actionType === 'remove_from_all_campaigns' && triggerType === 'added_to_campaign'" class="fa fa-question-circle" v-b-tooltip.hover title='This action will remove leads from all campaigns except the current campaign' style="margin-top: 17px; margin-left: 1px; font-size: 15px;"></i>
            </div>
						<div class="dropdown-menu">
              <div v-for="option in actionTypes">
                <p class="section-heading" v-if="option.title">{{option.title}}</p>
                <a
                  class="dropdown-item trigger-type"
                  href="javascript:void(0);"
                  v-for="actionType in option.options"
                  :key="actionType.value"
                  @click.prevent="formattedActionType=actionType.value"
                >
                  <p>{{actionType.title}}</p>
                </a>
              </div>
						</div>
					</div>
				</div>
			</div>
			<component
				:is="actionComponent"
				v-if="actionComponent"
				:action="localAction"
        :triggerType="triggerType"
				v-on:update:action="localAction = $event; $emit('update:action', localAction);"
        :editorOptions="editorOptions"
        :showFromWarning="showFromWarning"
        :displayMenu="displayMenu"
			/>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
import { mapState } from 'vuex';
import { Company, User, TriggerType, CustomField, CustomValue, Location } from '@/models/';
import { CompanyState, UserState} from '../../../store/state_models';
import { getTagOptions, getTaskOptions, getFormOptions, getSurveyOptions } from '@/util/merge_tags';
import { EventBus } from '@/models/event-bus'
import { ActionMaster } from '@/util/trigger';
import { defaultTinyMceOptions } from '../../../util/tiny_mce_defaults';

const PipelineAction = () => import('./PipelineAction.vue');
const RemoveOpportunityAction = () => import('./RemoveOpportunityAction.vue')
const CampaignAction = () => import('./CampaignAction.vue');
const RemoveTagAction = () => import('./RemoveTagAction.vue');
const SendEmailAction = () => import('./SendEmailAction.vue');
const SendSMSAction = () => import('./SendSMSAction.vue');
const FacebookCustomAudience = () => import('./FacebookCustomAudience.vue');
const ExecuteWebhookAction = () => import('./ExecuteWebhookAction.vue');
const DNDAction = () => import('./DNDAction.vue');
const TaskNotification = () => import('./TaskNotification.vue');
const AddNotesAction = () => import('./AddNotesAction.vue');
const NotificationAction = () => import('./NotificationAction.vue');
const GoogleAdwordsAction = () => import('./GoogleAdwordsAction.vue');
const GoogleAnalyticsAction = () => import('./GoogleAnalyticsAction.vue');
const AssignUserAction = () => import('./AssignUserAction.vue');
const StripeOneTimeChargeAction = () => import('./StripeOneTimeChargeAction.vue');
const MemebershipOfferAction = () => import('./MemebershipOfferAction.vue');
const UpdateAppoinmentStatusAction = () => import('./UpdateAppointmentStatusAction.vue');
const ContactFieldUpdateAction = () => import('./ContactFieldUpdateAction.vue');
const AddToBotConversation = () => import('./AddToBotConversation.vue');

const triggerLinks: { [key: string]: any }[] = [];

export default Vue.extend({
	props: ['action', 'triggerType', 'showFromWarning'],
	components: { PipelineAction, RemoveOpportunityAction, CampaignAction, RemoveTagAction, SendEmailAction, SendSMSAction, FacebookCustomAudience, ExecuteWebhookAction, DNDAction, TaskNotification, AddNotesAction, NotificationAction, GoogleAdwordsAction, GoogleAnalyticsAction, AssignUserAction, StripeOneTimeChargeAction, MemebershipOfferAction, UpdateAppoinmentStatusAction, ContactFieldUpdateAction, AddToBotConversation },
	data() {
		return {
      currentLocationId: '',
			localAction: {},
			actionDestination: 'highlevel',
      actionType: '',
	 	displayMenu: false,
		  actionMaster: {},
		  actionTypes: []
		}
	},
	computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
		...mapState('company', {
			company: (s: CompanyState) => {
				return s.company ? new Company(s.company) : undefined;
			},
		}),
		actionComponent() {
			switch (this.actionType) {
				case 'create_opportunity':
					return 'PipelineAction';
				case 'remove_opportunity':
					return 'RemoveOpportunityAction'
				case 'remove_from_campaign':
				case 'add_to_campaign':
					return 'CampaignAction';
				case 'remove_contact_tag':
					return 'RemoveTagAction';
				case 'add_contact_tag':
					return 'RemoveTagAction';
				case 'send_email':
					return 'SendEmailAction';
				case 'send_sms':
          return 'SendSMSAction';
        case 'execute_webhook':
          return 'ExecuteWebhookAction';
        case 'send_notification':
          return 'NotificationAction';
				case 'facebook_add_to_custom_audience':
					return 'FacebookCustomAudience';
				case 'facebook_remove_from_custom_audience':
          return 'FacebookCustomAudience';
        case 'add_notes':
          return 'AddNotesAction';
        case 'dnd_contact':
          return 'DNDAction';
        case 'task_notification':
          return 'TaskNotification';
        case 'google_adword':
          return 'GoogleAdwordsAction';
        case 'google_analytics':
          return 'GoogleAnalyticsAction';
        case 'assign_user':
          return 'AssignUserAction';
        case 'stripe_one_time_charge':
          return 'StripeOneTimeChargeAction';
        case 'membership_grant_offer':
        case 'membership_revoke_offer':
          return 'MemebershipOfferAction';
        case 'update_contact_field':
          return 'ContactFieldUpdateAction';
        case 'add_to_bot_conversation':
          return 'AddToBotConversation';
        case 'update_appointment_status':
			    return 'UpdateAppoinmentStatusAction';
			}
			return '';
		},
		actionName(): string {
			const condition = lodash.find(this.localConditions, { field: 'opportunity.pipelineId' });
			if (condition && condition.value) {
				const pipeline = lodash.find(this.pipelines, { id: condition.value });
				if (pipeline) return pipeline.name;
			}
			return '';
		},
		formattedActionDestination: {
			get(): string {
				if (this.actionDestination === 'highlevel') return this.company.name || 'Internal';
				return this.$options.filters['toTitleCase'](this.actionDestination);
			},
			set(value: string) {
				this.actionDestination = value;
				this.actionType = '';
        this.localAction = {};
        this.reactToTriggerType();
			}
		},
		formattedActionType: {
			get(): string {
        if (!this.actionDestination || !this.actionType) return '';
        let selected;
        this.actionTypes.some(data => {
          selected = lodash.find(data.options, { value: this.actionType });
          if (selected) return true;
        });
				if (selected) return !selected.title.includes('DND') ? this.$options.filters['toTitleCase'](selected.title) : selected.title;
				return '';
			},
			set(value: string) {
				if (value !== this.actionType) {
					this.actionType = value;
					this.localAction = { 'type': value };
					this.$emit('update:action', this.localAction);
				}
			}
		},
    editorOptions() {
      var d = this;
      if (this.actionType === 'send_email') {
          return {
            ...defaultTinyMceOptions,
            height: 340,
            plugins: [
              'advlist autolink link image lists charmap hr anchor pagebreak spellchecker',
              'searchreplace wordcount visualblocks visualchars code fullscreen insertdatetime media nonbreaking',
              'table contextmenu directionality emoticons template textcolor link '
            ],
            link_list: triggerLinks,
            toolbar: 'mybutton triggerlinks | sizeselect  | fontselect |  fontsizeselect formatselect  formatpainter removeformat undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent link image forecolor backcolor code',
            setup: function (editor: any) {
              editor.addButton('mybutton', {
                text: 'Custom Values',
                onclick: function () {
                  d.toTimeHtml();
                  EventBus.$on('customFieldSelected', (value => {
                    d.displayMenu = false;
                    editor.insertContent(value);
                    EventBus.$off('customFieldSelected')
                  }))
                }
              });

              editor.addButton('triggerlinks', {
                type: 'listbox',
                text: 'Trigger Links',
                onselect: function (e: any) {
                  editor.insertContent(this.value());
                  this.value(null);
                },
                values: triggerLinks
              });

              // editor.on('init', function (e) {
              // 	editor.execCommand("fontName", false, "Verdana");
              // 	editor.execCommand("fontSize", true, "11pt");
              // });
            }
          }
      }
      else if (['send_sms', 'add_notes', 'send_notification'].indexOf(this.actionType) !== -1) {
          return {
                      format: 'text',
                      menubar: false,
                      theme: 'modern',
                      height: 340,
                      toolbar: 'mybutton | triggerlinks',
                      plugins: 'paste',
                      setup: function (editor: any) {
                        editor.addButton('mybutton', {
                          text: 'Custom Values',
                          onclick: function () {
                            d.toTimeHtml();
                            EventBus.$on('customFieldSelected', (value => {
                              d.displayMenu = false;
                              editor.insertContent(value);
                              EventBus.$off('customFieldSelected')
                            }))
                          }
						});


              editor.addButton('triggerlinks', {
                type: 'listbox',
                text: 'Trigger Links',
                onselect: function (e: any) {
                  editor.insertContent(this.value());
                  this.value(null);
                },
                values: triggerLinks
              });

						// editor.on('init', function (e) {
						// 	editor.execCommand("fontName", false, "Verdana");
						// 	editor.execCommand("fontSize", true, "11pt");
						// });
                      }

          }
      }
    },
  },
  methods: {
    async loadTriggerLinks(id: string) {
      if (!triggerLinks || triggerLinks.length < 1) {
        let obj = this.$store.getters['conversation/getTriggerLinksMenuItem']
        if (!obj || !obj.text) {
          obj = await this.$store.dispatch('conversation/fetchTriggerLinks', {locationId: this.$route.params.location_id})
        }
        delete obj.location_id
        triggerLinks.push.apply(triggerLinks, obj.menu)
      }
    },
    toTimeHtml() {
      this.displayMenu = !this.displayMenu;
      EventBus.$off('customFieldSelected');
      return '';
	},
	async reactToTriggerType() {
		if (this.actionDestination === 'highlevel') {
			const location = new Location(await this.$store.dispatch('locations/getById', this.currentLocationId));

			const addToBotActionIndex = lodash.findIndex(this.actionMaster[this.actionDestination][0].options, { value: 'add_to_bot_conversation' });
			if((location && !location.botService) || (this.user && !this.user.permissions.bot_service)){
        if (addToBotActionIndex !== -1){
					this.actionMaster[this.actionDestination][0].options.splice(addToBotActionIndex, 1);
					if (this.actionType === 'add_to_bot_conversation') this.$emit('update:action')
				}
			}

		}
		const actions: Array<{[key: string]: any}> = this.actionMaster[this.actionDestination];
    this.actionTypes = actions.map(action => {
      action.options = action.options.sort((a, b) => a.title.localeCompare(b.title))
      return action;
    });
	}
  },
	async created() {
    this.actionMaster = ActionMaster
    this.currentLocationId = this.$router.currentRoute.params.location_id;
    this.localAction = lodash.clone(this.action);
		this.actionType = this.action.type;
		switch (this.actionType) {
			case 'facebook_remove_from_custom_audience':
			case 'facebook_add_to_custom_audience':
				this.actionDestination = 'facebook';
    };
    EventBus.$on('close_nested_menu', () => {
      this.displayMenu = false
    });
	  this.reactToTriggerType()
    this.loadTriggerLinks(this.currentLocationId);
  },
  beforeDestroy() {
    EventBus.$off('close_nested_menu');
  },
	watch: {
		action() {
			this.localAction = lodash.clone(this.action);
			this.actionType = this.action.type;
		},
		triggerType() {
			console.log('trigger type watch')
			this.reactToTriggerType()
		}
	}
})
</script>
