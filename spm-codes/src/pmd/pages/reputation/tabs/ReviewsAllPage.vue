<template>
  <section v-if="user && user.permissions.reviews_enabled !== false">
    <section class="hl_reviews" id="reviews">
      <div>
        <div class="hl_controls">
          <div class="hl_controls--left">
          </div>
          <div class="hl_controls--right" v-if="reviews.length > 0 ">
          </div>
        </div>
        <div
          class="hl_reviews--group --open"
          id="hl_reviews--filter-group"
          v-if="reviews.length > 0"
        >
          <div class="hl_reviews-top-bar mb-2">
            <div class="search-form"  v-if="reviews.length > 0 ">
              <UITextInputGroup
                type="text"
                class="form-light"
                icon="icon-loupe"
                placeholder="Search Reviews"
                v-model="searchText"
              />
            </div>

            <nav
              aria-label="Page navigation example"
              v-if="filtered_reviews.length > 0 &&  ( nextId || previousId)"
            >
              <ul class="pagination justify-content-end">
                <li
                  class="page-item"
                  :class="{ disabled: !previousId || fetching }"
                >
                  <a
                    class="page-link"
                    href="javascript:void(0);"
                    tabindex="-1"
                    @click.prevent="load('previous')"
                    >Previous</a
                  >
                </li>
                <li
                  class="page-item"
                  :class="{ disabled: !nextId || fetching }"
                >
                  <a
                    class="page-link"
                    href="javascript:void(0);"
                    @click.prevent="load('next')"
                    >Next</a
                  >
                </li>
              </ul>
            </nav>
          </div>

          <div class="hl_reviews--filter --open" id="hl_reviews--filter">
            <div class="form-group">
              <UITextLabel>Network</UITextLabel>
              <select class="selectpicker" @change="set_network($event)">
                <option value>Select One</option>
                <option value="247">Google</option>
                <option value="71">Facebook</option>
              </select>
            </div>
            <div class="form-group">
              <UITextLabel>Minimum Rating</UITextLabel>
              <select class="selectpicker" @change="set_minimum_rating($event)">
                <option value>Select One</option>
                <option
                  value="1"
                  data-content="<i class='icon icon-star-filled'></i> 1"
                >
                  1
                </option>
                <option
                  value="2"
                  data-content="<i class='icon icon-star-filled'></i> 2"
                >
                  2
                </option>
                <option
                  value="3"
                  data-content="<i class='icon icon-star-filled'></i> 3"
                >
                  3
                </option>
                <option
                  value="4"
                  data-content="<i class='icon icon-star-filled'></i> 4"
                >
                  4
                </option>
                <option
                  value="5"
                  data-content="<i class='icon icon-star-filled'></i> 5"
                >
                  5
                </option>
              </select>
            </div>
            <div class="form-group">
              <UITextLabel>Max Rating</UITextLabel>
              <select class="selectpicker" @change="set_maximum_rating($event)">
                <option value>Select One</option>
                <option
                  value="1"
                  data-content="<i class='icon icon-star-filled'></i> 1"
                >
                  1
                </option>
                <option
                  value="2"
                  data-content="<i class='icon icon-star-filled'></i> 2"
                >
                  2
                </option>
                <option
                  value="3"
                  data-content="<i class='icon icon-star-filled'></i> 3"
                >
                  3
                </option>
                <option
                  value="4"
                  data-content="<i class='icon icon-star-filled'></i> 4"
                >
                  4
                </option>
                <option
                  value="5"
                  data-content="<i class='icon icon-star-filled'></i> 5"
                >
                  5
                </option>
              </select>
            </div>
            <div class="form-group">
              <UITextLabel>Start Date</UITextLabel>
              <Datepicker
                :format="getCountryDateFormat('basic-mdy-date-picker')"
                placeholder="Select date"
                v-model="startDate"
                input-class="hl-text-input shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded disabled:opacity-50 text-gray-800"
              ></Datepicker>
            </div>
            <div class="form-group">
              <UITextLabel>End Date </UITextLabel>
              <Datepicker
                :format="getCountryDateFormat('basic-mdy-date-picker')"
                placeholder="Select date"
                v-model="endDate"
                input-class="hl-text-input shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded disabled:opacity-50 text-gray-800"
              ></Datepicker>
            </div>
            <a href="#" @click="clearFilters">Clear Filters</a>
          </div>
          <component
            :is="get_component_name(review)"
            v-for="review in filtered_reviews"
            v-bind:review="review"
            v-bind:key="review.id"
            :location="location"
            :ref="review.id"
          />
          <nav aria-label="Page navigation example" v-if="filtered_reviews && filtered_reviews.length > 0 && ( nextId || previousId)">
            <ul class="pagination justify-content-end">
              <li
                class="page-item"
                :class="{ disabled: !previousId || fetching }"
              >
                <a
                  class="page-link"
                  href="javascript:void(0);"
                  tabindex="-1"
                  @click.prevent="load('previous')"
                  >Previous</a
                >
              </li>
              <li class="page-item" :class="{ disabled: !nextId || fetching }">
                <a
                  class="page-link"
                  href="javascript:void(0);"
                  @click.prevent="load('next')"
                  >Next</a
                >
              </li>
            </ul>
          </nav>
        </div>
        <div v-else-if="!fetching && reviews.length === 0">
          <div class="card">
            <div class="card-body --no-content" >
              <h3>Oops!</h3>
              <div class="flex justify-center">
                <NoReviewIcon alt="" />
              </div>
              <h5>You have no reviews yet</h5>
              <div> Start by <router-link  :to="{name:'settings_reputation_settings'}"> setting up your reviews</router-link> </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- END of .hl_reviews -->
  </section>
</template>

<script lang="ts">
import Vue from 'vue'
import Datepicker from 'vuejs-datepicker'
import firebase from 'firebase/app'
import {
  Contact,
  Estimate,
  Review,
  Location,
  getCountryDateFormat,
  ReviewStatus,
  User
} from '@/models'

import { UserState } from '@/store/state_models'
import { mapState } from 'vuex'
import NoReviewIcon from '@/assets/pmd/img/noreview.svg'

const GoogleReviewCard = () => import('../../../components/GoogleReviewCard.vue')
const FacebookReviewCard = () => import('../../../components/FacebookReviewCard.vue')

declare var $: any

export default Vue.extend({
  name: 'LocationReviewsPage',
  components: { GoogleReviewCard, FacebookReviewCard, Datepicker, NoReviewIcon },
  data() {
    return {
      reviews: [] as Review[],
      logo_url: '',
      location: {} as Location,
      currentLocationId: '',
      minRating: 0 as number,
      maxRating: 0 as number,
      startDate: null as any,
      endDate: null as any,
      network: 0 as number,
      searchText: '' as string,
      navigate: true,
      getCountryDateFormat,
      previousId: undefined as firebase.firestore.DocumentSnapshot | undefined,
      nextId: undefined as firebase.firestore.DocumentSnapshot | undefined,
      fetching: false,
      limit: 15,
      showSendReviewModal:false
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.fetchLocationData()
    },
    '$route.params.review_id': function (id) {
      this.navigate = true
      this.navigateToReview()
    },
  },
  async created() {
    this.currentLocationId = await this.$router.currentRoute.params.location_id
    this.fetchLocationData()
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  updated() {
    this.navigateToReview()
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    filtered_reviews(): Review[] {
      var filtered_reviews = this.reviews

      if (this.minRating != 0) {
        filtered_reviews = lodash.filter(
          filtered_reviews,
          o => o.starRating >= this.minRating
        )
      }

      if (this.maxRating != 0) {
        filtered_reviews = lodash.filter(
          filtered_reviews,
          o => o.starRating <= this.maxRating
        )
      }

      if (this.network != 0) {
        filtered_reviews = lodash.filter(
          filtered_reviews,
          o => o.source == this.network
        )
      }

      if (this.startDate) {
        filtered_reviews = lodash.filter(filtered_reviews, o =>
          o.dateAdded.isAfter(this.startDate)
        )
      }

      if (this.endDate) {
        filtered_reviews = lodash.filter(filtered_reviews, o =>
          o.dateAdded.isBefore(this.endDate)
        )
      }

      if (this.searchText.length > 0) {
        filtered_reviews = lodash.filter(filtered_reviews, o =>
          o.comment.toLowerCase().includes(this.searchText.toLowerCase())
        )
      }

      return filtered_reviews
    },
  },
  methods: {
    navigateToReview() {
      if (this.navigate && this.$router.currentRoute.params.review_id) {
        const element = this.$refs[this.$router.currentRoute.params.review_id]
        if (element) {
          element[0].$el.scrollIntoView(true)
          window.scrollBy(0, -100)
          this.navigate = false
        }
      }
    },
    async fetchLocationData() {
      if (this.$private.cancelBusinessListingSubscription) {
        this.$private.cancelBusinessListingSubscription()
      }

      this.location = new Location(
        await this.$store.dispatch('locations/getById', this.currentLocationId)
      )

      this.load()

      // if (this.$private.cancelReviewsSubscription) {
      //   this.$private.cancelReviewsSubscription()
      // }

      // this.$private.cancelReviewsSubscription = Review.getByLocationIdRealtime(
      //   this.currentLocationId
      // ).onSnapshot((snapshot: firebase.firestore.QuerySnapshot) => {
      //   this.reviews = snapshot.docs.map(
      //     (d: firebase.firestore.QueryDocumentSnapshot) => new Review(d)
      //   )
      // })
    },
    get_component_name(review: Review) {
      if (review.source == Review.SOURCE_GOOGLE) {
        return 'GoogleReviewCard'
      } else if (review.source == Review.SOURCE_FACEBOOK) {
        return 'FacebookReviewCard'
      }
    },
    set_minimum_rating(e: any) {
      console.log('set_minimum_rating:', e.target.value)
      this.minRating = Number(e.target.value)
    },
    set_maximum_rating(e: any) {
      this.maxRating = Number(e.target.value)
    },
    set_network(e: any) {
      this.network = Number(e.target.value)
    },
    clearFilters() {
      this.minRating = 0
      this.maxRating = 0
      this.startDate = null
      this.endDate = null
      this.network = 0
      this.searchText = ''

      $('.selectpicker').selectpicker('val', '')
    },
    async load(fetchType?: 'previous' | 'next') {
      this.fetching = true
      let query = Review.collectionRef()
        .where('deleted', '==', false)
        .where('location_id', '==', this.currentLocationId)

      if (fetchType === 'previous') {
        query = query.orderBy('date_added', 'asc').startAfter(this.previousId)
      } else if (fetchType === 'next') {
        query = query.orderBy('date_added', 'desc').startAfter(this.nextId)
      } else {
        query = query.orderBy('date_added', 'desc')
      }
      const snapshot = await query.limit(this.limit + 1).get()
      let docs = snapshot.docs
      this.nextId = undefined
      this.previousId = undefined
      if (!fetchType && docs.length === this.limit + 1) {
        docs.splice(this.limit, 1)
        this.nextId = docs[this.limit - 1]
      } else if (fetchType === 'next') {
        this.previousId = docs[0]
        if (docs.length === this.limit + 1) {
          docs.splice(this.limit, 1)
          this.nextId = docs[this.limit - 1]
        }
      } else if (fetchType === 'previous') {
        docs = docs.reverse()
        if (docs.length === this.limit + 1) {
          docs.splice(0, 1)
          this.previousId = docs[0]
        }
        this.nextId = docs[this.limit - 1]
      }
      this.reviews = docs.map(d => new Review(d))
      this.fetching = false
    },
  },
  beforeDestroy(): void {
    if (this.$private.cancelReviewsSubscription) {
      this.$private.cancelReviewsSubscription()
    }
  },
})
</script>

<style>
.hl_controls{
    margin-bottom: unset
}
.hl_reviews-top-bar {
  display: grid;
  grid-template-columns: auto auto;
  align-items: flex-start;
  padding: 5px 0;
}

.hl_reviews--filter{
  padding-top: 0px;
}
.collapse.in {
  display: block;
}

.form-control:disabled,
.form-control[readonly] {
  background-color: #fff;
}

.vdp-datepicker__calendar {
  width: 270px !important;
}

.hl_reviews--group {
  min-height: 800px;
}

/* .hl_calltoaction-btn {
  background: unset;
  padding: 0;
  border: unset;
  color: #188bf6;
} */

.--no-content{
  text-align: center;
}
</style>
