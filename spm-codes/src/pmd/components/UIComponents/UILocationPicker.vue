<template>
  <div>
    <multiselect
      :id="id"
      v-model="selectedLocations"
      label="name"
      name="name"
      key="id"
      track-by="id"
      :placeholder="placeholder"
      open-direction="bottom"
      :options="locations"
      :multiple="multiple"
      :searchable="searchable"
      :loading="isLoading"
      :internal-search="false"
      :clear-on-select="false"
      :close-on-select="true"
      :limit="3"
      :limit-text="limitText"
      :max-height="maxHeight"
      :show-no-results="false"
      :hide-selected="false"
      :max="maxSelectableLocations"
      @search-change="onAsyncSearch"
    >
      <template slot="tag" slot-scope="{ option, remove }">
        <span class="custom__tag">
          <span>{{ option.name ? option.name : '' }}</span>
          <span
            class="custom__remove cursor-pointer"
            @click.prevent.stop="remove(option)"
            >&times;</span
          >
        </span></template
      >

      <template slot="clear" slot-scope="props">
        <div
          class="multiselect__clear"
          v-if="selectedLocations.length"
          @mousedown.prevent.stop="clearAll(props.search)"
        ></div>
      </template>
      <template slot="noResult"
        >Oops! No elements found. Consider changing the search query.</template
      >

      <template slot="afterList">
        <div v-observe-visibility="reachedEndOfList" v-if="hasNextPage" />
      </template>
    </multiselect>
  </div>
</template>

<script lang="ts">
import LocationsService from '@/services/LocationsService'
import {
  computed,
  defineComponent,
  onMounted,
  ref,
} from '@vue/composition-api'
import Multiselect from 'vue-multiselect'
import { ObserveVisibility } from 'vue-observe-visibility'
import Vue from 'vue'
Vue.directive('observe-visibility', ObserveVisibility)
import 'vue-multiselect/dist/vue-multiselect.min.css'
import debounce from 'lodash/debounce'

export default defineComponent({
  components: { Multiselect },

  props: {
    value: {
      type: [Array, String, Object],
      required: true,
    },
    id: {
      type: String,
      required: true,
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    searchable: {
      type: Boolean,
      default: true,
    },
    removeCurrentLocation: {
      type: Boolean,
      default: false,
    },
    maxHeight: {
      type: Number,
      default: 300,
    },
    placeholder: {
      type: String,
      default: 'Search for locations',
    },
    maxSelectableLocations: {
      type: Number,
      default: 5,
    }
  },
  setup(props, ctx) {
    const pagniationSize = 30

    const selectedLocations = computed({
      get(){
        let selectedValues = props.multiple ? [] : {}
        if(props.value){
          selectedValues = props.value as any
        }
        return selectedValues
      },
      set(value){
        ctx.emit('input', value)
      }
    })

    const locations = ref<Array<any>>([])
    const limit = ref(pagniationSize)
    const skip = ref(0)
    const searchText = ref('')
    const isLoading = ref(false)
    const totalLocations = ref(0)
    const route = ctx.root.$route
    const enableHasNextOption = ref(false)

    const hasNextPage = computed(() => {
      return locations.value.length < totalLocations.value
    })

    async function loadLocationData() {
      try {
        isLoading.value = true
        const auth = await ctx.root.$store.dispatch('auth/get')

        let { locations: locationRawData, hit } = await LocationsService.search(
          auth.companyId,
          false,
          undefined,
          skip.value,
          limit.value,
          searchText.value
        )

        totalLocations.value = hit.length ? hit[0].count : 0
        if (!locationRawData) return

        locationRawData = await locationRawData
          .map((location: any) => {
            const { _id: id, name, ...result } = location

            if (props.removeCurrentLocation) {
              if (route.params.location_id === id) {
                return null
              }
            }

            return {
              ...result,
              name: name || 'NO NAME',
              id,
            }
          })
          .filter((location: any) => location)

        if (searchText.value) {
          locations.value = locationRawData
        } else {
          locations.value.push(...locationRawData)
        }
        enableHasNextOption.value = true
        isLoading.value = false
      } catch (error) {
        isLoading.value = false
        console.error(error)
      }
    }

    const onAsyncSearch = debounce((query: string) => {
      limit.value = pagniationSize
      skip.value = 0
      searchText.value = query
      if (!query) {
        locations.value = []
        enableHasNextOption.value = false
        loadLocationData()
        return
      }
      loadLocationData()
    }, 500)

    function reachedEndOfList(reached: any) {
      if (reached && enableHasNextOption.value) {
        skip.value += limit.value
        loadLocationData()
      }
    }
    function limitText(count: number) {
      return `and ${count} other locations`
    }

    function clearPagination() {
      skip.value = 0
      limit.value = pagniationSize
      searchText.value = ''
    }

    function clearAll() {
      selectedLocations.value = []
      clearPagination()
    }


    onMounted(() => {
      loadLocationData()
    })

    return {
      locations,
      hasNextPage,
      selectedLocations,
      isLoading,
      onAsyncSearch,
      limitText,
      totalLocations,
      reachedEndOfList,
      clearAll,
      enableHasNextOption,
    }
  },
})
</script>

<style>
fieldset[disabled] .multiselect {
  pointer-events: none;
}

.multiselect__loading-enter-active,
.multiselect__loading-leave-active {
  transition: opacity 0.4s ease-in-out;
  opacity: 1;
}
.multiselect__loading-enter,
.multiselect__loading-leave-active {
  opacity: 0;
}
.multiselect,
.multiselect__input,
.multiselect__single {
  font-family: inherit;
  font-size: 16px;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
}
.multiselect {
  box-sizing: content-box;
  display: block;
  position: relative;
  width: 100%;
  min-height: 40px;
  text-align: left;
  color: #35495e;
}
.multiselect * {
  box-sizing: border-box;
}
.multiselect:focus {
  outline: none;
}
.multiselect--disabled {
  background: #ededed;
  pointer-events: none;
  opacity: 0.6;
}
.multiselect--active {
  z-index: 3;
}
.multiselect--active:not(.multiselect--above) .multiselect__current,
.multiselect--active:not(.multiselect--above) .multiselect__input,
.multiselect--active:not(.multiselect--above) .multiselect__tags {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.multiselect--active .multiselect__select {
  transform: rotate(180deg);
}
.multiselect--above.multiselect--active .multiselect__current,
.multiselect--above.multiselect--active .multiselect__input,
.multiselect--above.multiselect--active .multiselect__tags {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.multiselect__input,
.multiselect__single {
  position: relative;
  display: inline-block;
  min-height: 20px;
  line-height: 20px;
  border: none;
  border-radius: 5px;
  background: #fff;
  padding: 0 0 0 5px;
  width: 100%;
  transition: border 0.1s ease;
  box-sizing: border-box;
  margin-bottom: 8px;
  vertical-align: top;
}
.multiselect__input:-ms-input-placeholder {
  color: #35495e;
}
.multiselect__input::placeholder {
  color: #35495e;
}
.multiselect__tag ~ .multiselect__input,
.multiselect__tag ~ .multiselect__single {
  width: auto;
}
.multiselect__input:hover,
.multiselect__single:hover {
  border-color: #cfcfcf;
}
.multiselect__input:focus,
.multiselect__single:focus {
  border-color: #a8a8a8;
  outline: none;
}
.multiselect__single {
  padding-left: 5px;
  margin-bottom: 8px;
}
.multiselect__tags-wrap {
  display: inline;
}
.multiselect__tags {
  min-height: 40px;
  display: block;
  padding: 8px 40px 0 8px;
  border-radius: 5px;
  border: 1px solid #e8e8e8;
  background: #fff;
  font-size: 14px;
}
.multiselect__tag {
  position: relative;
  display: inline-block;
  padding: 4px 26px 4px 10px;
  border-radius: 5px;
  margin-right: 10px;
  color: #fff;
  line-height: 1;
  background: #38a0db;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  max-width: 100%;
  text-overflow: ellipsis;
}
.multiselect__tag-icon {
  cursor: pointer;
  margin-left: 7px;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  font-weight: 700;
  font-style: normal;
  width: 22px;
  text-align: center;
  line-height: 22px;
  transition: all 0.2s ease;
  border-radius: 5px;
}
.multiselect__tag-icon:after {
  content: '\D7';
  color: #266d4d;
  font-size: 14px;
}
.multiselect__tag-icon:focus,
.multiselect__tag-icon:hover {
  background: #369a6e;
}
.multiselect__tag-icon:focus:after,
.multiselect__tag-icon:hover:after {
  color: #fff;
}
.multiselect__current {
  min-height: 40px;
  overflow: hidden;
  padding: 8px 12px 0;
  padding-right: 30px;
  white-space: nowrap;
  border-radius: 5px;
  border: 1px solid #e8e8e8;
}
.multiselect__current,
.multiselect__select {
  line-height: 16px;
  box-sizing: border-box;
  display: block;
  margin: 0;
  text-decoration: none;
  cursor: pointer;
}
.multiselect__select {
  position: absolute;
  width: 40px;
  height: 38px;
  right: 1px;
  top: 1px;
  padding: 4px 8px;
  text-align: center;
  transition: transform 0.2s ease;
}
.multiselect__select:before {
  position: relative;
  right: 0;
  top: 65%;
  color: #999;
  margin-top: 4px;
  border-style: solid;
  border-width: 5px 5px 0;
  border-color: #999 transparent transparent;
  content: '';
}
.multiselect__placeholder {
  color: #adadad;
  display: inline-block;
  margin-bottom: 10px;
  padding-top: 2px;
}
.multiselect--active .multiselect__placeholder {
  display: none;
}
.multiselect__content-wrapper {
  position: absolute;
  display: block;
  background: #fff;
  width: 100%;
  max-height: 240px;
  overflow: auto;
  border: 1px solid #e8e8e8;
  border-top: none;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  z-index: 3;
  -webkit-overflow-scrolling: touch;
}
.multiselect__content {
  list-style: none;
  display: inline-block;
  padding: 0;
  margin: 0;
  min-width: 100%;
  vertical-align: top;
}
.multiselect--above .multiselect__content-wrapper {
  bottom: 100%;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  border-bottom: none;
  border-top: 1px solid #e8e8e8;
}
.multiselect__content::webkit-scrollbar {
  display: none;
}
.multiselect__element {
  display: block;
}
.multiselect__option {
  display: block;
  padding: 12px;
  min-height: 40px;
  line-height: 16px;
  text-decoration: none;
  text-transform: none;
  vertical-align: middle;
  position: relative;
  cursor: pointer;
  white-space: nowrap;
}
.multiselect__option:after {
  top: 0;
  right: 0;
  position: absolute;
  line-height: 40px;
  padding-right: 12px;
  padding-left: 20px;
  font-size: 13px;
}
.multiselect__option--highlight {
  background: #74bde6;
  outline: none;
  color: #fff;
}
.multiselect__option--highlight:after {
  content: attr(data-select);
  background: #74bde6;
  color: #fff;
}
.multiselect__option--selected {
  background: #f3f3f3;
  color: #35495e;
  font-weight: 700;
}
.multiselect__option--selected:after {
  content: attr(data-selected);
  color: silver;
}
.multiselect__option--selected.multiselect__option--highlight {
  background: #ff6a6a;
  color: #fff;
}
.multiselect__option--selected.multiselect__option--highlight:after {
  background: #ff6a6a;
  content: attr(data-deselect);
  color: #fff;
}
.multiselect--disabled .multiselect__current,
.multiselect--disabled .multiselect__select {
  background: #ededed;
  color: #a6a6a6;
}
.multiselect__option--disabled {
  background: #ededed !important;
  color: #a6a6a6 !important;
  cursor: text;
  pointer-events: none;
}
.multiselect__option--group {
  background: #ededed;
  color: #35495e;
}
.multiselect__option--group.multiselect__option--highlight {
  background: #35495e;
  color: #fff;
}
.multiselect__option--group.multiselect__option--highlight:after {
  background: #35495e;
}
.multiselect__option--disabled.multiselect__option--highlight {
  background: #dedede;
}
.multiselect__option--group-selected.multiselect__option--highlight {
  background: #ff6a6a;
  color: #fff;
}
.multiselect__option--group-selected.multiselect__option--highlight:after {
  background: #ff6a6a;
  content: attr(data-deselect);
  color: #fff;
}
.multiselect-enter-active,
.multiselect-leave-active {
  transition: all 0.15s ease;
}
.multiselect-enter,
.multiselect-leave-active {
  opacity: 0;
}
.multiselect__strong {
  margin-bottom: 8px;
  line-height: 20px;
  display: inline-block;
  vertical-align: top;
}
[dir='rtl'] .multiselect {
  text-align: right;
}
[dir='rtl'] .multiselect__select {
  right: auto;
  left: 1px;
}
[dir='rtl'] .multiselect__tags {
  padding: 8px 8px 0 40px;
}
[dir='rtl'] .multiselect__content {
  text-align: right;
}
[dir='rtl'] .multiselect__option:after {
  right: auto;
  left: 0;
}
[dir='rtl'] .multiselect__clear {
  right: auto;
  left: 12px;
}
[dir='rtl'] .multiselect__spinner {
  right: auto;
  left: 1px;
}
.custom__tag {
  display: inline-block;
  padding: 0px 13px;
  font-size: 13px;
  background: #cde7f6;
  margin-right: 8px;
  margin-bottom: 8px;
  border-radius: 10px;
  cursor: pointer;
}
.custom__tag:nth-child(2n) {
  background: #cde7f6;
}
.custom__tag:hover {
  background: #afd9f1;
}
.custom__remove {
  padding: 0;
  font-size: 15px;
  font-weight: bold;
  margin-left: 5px;
}
.multiselect__clear {
  position: absolute;
  right: 41px;
  height: 40px;
  width: 40px;
  display: block;
  cursor: pointer;
  z-index: 2;
}
.multiselect__clear:after,
.multiselect__clear:before {
  content: '';
  display: block;
  position: absolute;
  width: 3px;
  height: 16px;
  background: #aaa;
  top: 12px;
  right: 4px;
}
.multiselect__clear:before {
  transform: rotate(45deg);
}
.multiselect__clear:after {
  transform: rotate(-45deg);
}
.option__image {
  max-height: 80px;
  margin-right: 10px;
}
.option__desc,
.option__image {
  display: inline-block;
  vertical-align: middle;
}
.option__desc {
  padding: rem(10px);
}
.option__title {
  font-size: rem(24px);
}
.option__small {
  margin-top: rem(10px);
  display: block;
}
.form__label {
  margin-top: 5px !important;
}
.badge__img {
  vertical-align: middle;
  float: right;
}
.badge__name {
  vertical-align: middle;
  display: inline-block;
  margin-left: 5px;
  float: left;
}
</style>
