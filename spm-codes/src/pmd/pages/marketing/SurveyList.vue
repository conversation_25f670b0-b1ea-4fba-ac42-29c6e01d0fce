<template>
  <section class="hl_wrapper d-flex">
    <section class="hl_wrapper--inner form-builder-list" id="form-builder">
      <div class="hl_marketing--header border-bottom-0 mb-0">
        <div class="container-fluid">
          <div class="hl_controls">
            <div class="hl_controls--left flex">
              <h2 class="capitalize">{{ getHeading }}</h2>
            </div>
            <div class="hl_controls--right">
              <a
                href="create-form"
                class="btn btn-success"
                v-if="tabIndex === 0"
                @click.prevent="createNew"
                >Create New Survey</a
              >
              <div
                v-if="tabIndex !== 0"
                id="hl_datepicker"
                class="hl_datepicker d-flex justify-content-center align-items-center"
              >
                <i class="icon icon-calendar"></i>
                <div class="d-flex" style="width: 230px">
                  <vue-ctk-date-time-picker
                    v-model="date"
                    :only-date="true"
                    :range="true"
                    :right="true"
                    :noClearButton="true"
                    formatted="ddd, MMM Do"
                    :maxDate="maxDate"
                    @validate="updateFilterDate"
                  />
                </div>
              </div>
              <button
                v-if="tabIndex !== 0"
                type="button"
                class="btn btn-sm btn-primary p-8"
                data-tooltip="tooltip"
                data-placement="top"
                v-b-tooltip.hover
                title="Refresh Data"
                @click="refreshData"
                data-original-title="Refresh"
              >
                <i class="icon icon-reload-1"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
      <b-tabs content-class="mt-4" v-model="tabIndex">
        <b-tab :title="getTitle(0)">
          <div class="container-fluid">
            <div class="hl_controls justify-content-end">
              <div class="hl_controls--right">
                <div class="search-form" v-if="formsurvey.length !== 0">
                  <UITextInputGroup
                    type="text"
                    class="form-light"
                    v-model="filters.text"
                    placeholder="Search Surveys"
                    icon="icon-loupe"
                  />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-lg-12">
                <moon-loader
                  class="mt-5"
                  color="#188bf6"
                  size="20px"
                  v-if="loading"
                />
                <div
                  v-else-if="formsurvey.length !== 0"
                  class="card hl_customer-acquisition--table"
                >
                  <div class="card-body --no-padding">
                    <HLTable
                      :items="items"
                      :fields="fields"
                      :currentPage="1"
                      :rows="items.length"
                      :isBusy="loading"
                      :headerOptions="headerOptions"
                      :defaultPerPage="items.length ? items.length : 10"
                      :isPerPageOption="false"
                      :detailsPagination="true"
                      :isSampleData="false"
                      :isLocalShorting="true"
                      title="Survey Builder"
                    >
                      <template v-slot:name="slotProps">
                        <router-link
                          :to="getRoute(slotProps.data.item.id)"
                          tag="a"
                          >{{ slotProps.data.value }}</router-link
                        >
                      </template>

                      <template v-slot:action="slotProps">
                        <div class="btn-group">
                          <router-link
                            tag="button"
                            class="btn btn-primary"
                            :to="getRoute(slotProps.data.item.id)"
                            >Edit</router-link
                          >
                          <button
                            type="button"
                            class="btn btn-primary dropdown-toggle dropdown-toggle-split"
                            data-toggle="dropdown"
                            aria-haspopup="true"
                            aria-expanded="true"
                          ></button>
                          <div class="dropdown-menu dropdown-menu-right">
                            <a
                              class="dropdown-item"
                              href="javascript:void(0);"
                              @click.prevent="
                                copySurvey(slotProps.data.item.id)
                              "
                              >Copy</a
                            >
                            <a
                              class="dropdown-item"
                              href="javascript:void(0);"
                              @click.prevent="
                                deleteBuilderItem(slotProps.data.item.id)
                              "
                              >Delete</a
                            >
                          </div>
                        </div>
                      </template>
                    </HLTable>
                  </div>
                </div>
                <div v-else class="nothing-found">
                  <p>
                    You do not have any survey yet.
                    <a @click.prevent="createNew" href="javascript:void(0);"
                      >Click here</a
                    >
                    to create your first survey.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </b-tab>
        <b-tab :title="getTitle(1)">
          <div>
            <survey-analyze
              :location_id="currentLocationId"
              :surveys="formsurvey"
              :date="date"
              :selectedDate="selectedDate"
              @changeTab="changeTab"
              v-if="filteredFormsurvey.length > 0 && tabIndex === 1"
            />
            <div v-else class="nothing-found">
              <p>
                You do not have any survey yet. Please create survey first to
                see analyze here.
              </p>
            </div>
          </div>
        </b-tab>
        <b-tab :title="getTitle(2)">
          <div>
            <JotboxSurvey
              :formSurvey="filteredFormsurvey"
              :date="date"
              :selectedDate="selectedDate"
              v-if="filteredFormsurvey.length > 0 && tabIndex === 2"
            />
            <div v-else class="nothing-found">
              <p>
                You do not have any survey yet. Please create survey first to
                see submission here.
              </p>
            </div>
          </div>
        </b-tab>
      </b-tabs>
    </section>
    <!-- END of .form-builder -->
    <CopySurveyModal
      :values="copyValues"
      @hidden="copyValues = { visible: false }"
      @updated="fetchData"
    />
  </section>
</template>

<script lang="ts">
import Vue from 'vue'
import * as moment from 'moment'
import { mapState } from 'vuex'
const CopySurveyModal = () =>
  import('../../components/marketing/CopySurveyModal.vue').then(m => m.default)
const MoonLoader = () =>
  import('../../components/MoonLoader').then(m => m.default)
const SurveyAnalyze = () =>
  import('@/pmd/components/marketing/SurveyAnalyze.vue').then(m => m.default)
const JotboxSurvey = () => import('./JotboxSurvey.vue').then(m => m.default)

/*---Model---*/
import { CompanyState } from '../../../store/state_models'
import { Company, Formsurvey, User } from '../../../models'
const HLTable = () =>
  import('@/pmd/components/HLTable.vue').then(m => m.default)

export default Vue.extend({
  components: {
    MoonLoader,
    CopySurveyModal,
    SurveyAnalyze,
    JotboxSurvey,
    HLTable,
  },
  data() {
    return {
      loading: true,
      currentLocationId: '',
      formsurvey: [] as Formsurvey[],
      filters: {
        text: '',
      },
      copyValues: {
        visible: false,
        survey: undefined as Formsurvey | undefined,
        currentLocationId: '',
        count: 0,
      },
      tabIndex: 0,
      date: {
        start: moment().subtract(1, 'month').toDate(),
        end: moment(),
        start_date: moment.utc().subtract(1, 'month').toDate().getTime(),
        end_date: moment.utc().toDate().getTime(),
      },
      maxDate: moment().format('YYYY-MM-DD'),
      selectedDate: '',
      tabs: ['main', 'analyze', 'submissions'],
      tabsTitle: ['Builder', 'Analyze', 'Submissions'],
      headerOptions: {
        isExportOption: false,
        isColumnModifier: false,
        isQuickSearch: false,
        isMoreFilter: false,
      },
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.fetchData()
    },
    tabIndex() {
      this.$router.push({
        path: this.tabs[this.tabIndex]
      })
    },
    '$route.name': function (name) {
      if (this.isV2SideBarEnabled) {
        let tabName = name.split("-")[2];
        this.tabIndex = this.tabs.indexOf(tabName);
      }
    }
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.selectedDate =
      moment().subtract(1, 'month').format('YYYY-MM-DD') +
      moment().format('YYYY-MM-DD')
    this.fetchData()

      let tabName = this.$router.currentRoute.name.split("_")[2];
      if (!tabName) tabName = this.$router.currentRoute.name.split("-")[2];
      this.tabIndex = this.tabs.indexOf(tabName);
      this.tabIndex = this.tabIndex === -1 ? 0 : this.tabIndex;
  },
  methods: {
    fetchData() {
      Formsurvey.getQueryWithLocationId(this.currentLocationId)
        .get()
        .then(async snapshot => {
          this.formsurvey = snapshot.docs
            .map(d => new Formsurvey(d))
            .sort((a, b) => {
              var nameA = ((a && a.name) || '').toUpperCase() // ignore upper and lowercase
              var nameB = ((b && b.name) || '').toUpperCase() // ignore upper and lowercase
              if (nameA < nameB) {
                return -1
              }
              if (nameA > nameB) {
                return 1
              }

              // names must be equal
              return 0
            })
          this.loading = false
        })
    },
    copySurvey(id) {
      const survey = this.formsurvey.find(form => form.id == id)
      this.copyValues = {
        currentLocationId: this.currentLocationId,
        survey: survey,
        visible: true,
        count: this.formsurvey.length + 1,
      }
    },
    async createNew() {
      let survey = new Formsurvey()
      survey.locationId = this.currentLocationId
      survey.name = 'Survey ' + (this.formsurvey.length + 1)
      let surveySlides = []
      surveySlides.push({
        active: false,
        button: {
          background: '2A3135',
          color: 'FFFFFF',
          border: {
            border: 0,
            padding: 11,
            radius: 0.3125,
            color: 'FFFFFF',
          },
        },
        slideName: 'Slide 1',
        slideData: [],
        id: new Date().getTime() + '-' + surveySlides.length,
      })

      let storeObjs = {
        form: {
          fbPixelId: '',
          layout: false,
          width: 550,
          formLabelVisible: true,
          style: {
            ac_branding: true,
            background: 'FFFFFF',
            color: '000000',
            border: {
              border: 1,
              radius: 4,
              style: 'dashed',
              color: 'CDE0EC',
            },
          },
          customStyle: '',
          company: {
            logoURL: this.company.logoURL ? this.company.logoURL : '',
            domain: this.company.domain ? this.company.domain : '',
            name: this.company.name ? this.company.name : '',
          },
          formAction: {
            redirect_url: '',
            thankyouText:
              'Thank you for taking the time to complete this survey.',
            actionType: 2,
            fieldSettingEnable: false,
            fieldPerPage: 1,
            disqualifiedText:
              'Thank you for taking the time to complete this survey.',
            disqualifiedType: 2,
            disqualifiedUrl: '',
            endsurveyText:
              'Thank you for taking the time to complete this survey.',
            endsurveyType: 2,
            endsurveyUrl: '',
          },
        },
        slides: surveySlides,
        surveyLogicLinkById: true,
      }
      survey.formData = storeObjs
      await survey.save()

      console.log('new ID', survey.id)
      if (this.isV2SideBarEnabled) {
        this.$router.push({
          name: 'survey-manage',
          params: {
            location_id: this.currentLocationId,
            survey_id: survey.id,
          },
        })
      } else {
        this.$router.push({
          name: 'survey_manage',
          params: {
            location_id: this.currentLocationId,
            survey_id: survey.id,
          },
        })
      }
    },
    changeTab(tab) {
      this.tabIndex = tab
    },
    updateFilterDate() {
      this.$set(
        this.date,
        'start_date',
        moment.utc(this.date.start, ['YYYY-MM-DD h:m a']).toDate().getTime()
      )
      if (!this.date.end) {
        this.$set(
          this.date,
          'end_date',
          moment
            .utc(this.date.start, ['YYYY-MM-DD h:m a'])
            .add(1, 'days')
            .toDate()
            .getTime()
        )
      } else {
        let endTime = moment.utc(this.date.end, ['YYYY-MM-DD h:m a'])
        if (endTime > moment()) {
          endTime = moment.utc(this.maxDate).set({ hour: 23, minute: 59 })
          this.$set(this.date, 'end', endTime.toDate())
        }

        this.$set(this.date, 'end_date', endTime.toDate().getTime())
      }
      this.selectedDate =
        moment(this.date.start, 'YYYY-MM-DD').format('YYYY-MM-DD') +
        moment(this.date.end, 'YYYY-MM-DD').format('YYYY-MM-DD')
    },
    refreshData() {
      this.selectedDate =
        moment(this.date.start, 'YYYY-MM-DD').toDate() + moment().format()
    },
    async deleteBuilderItem(id) {
      const callbackFunction = async (response: string) => {
        if (response === 'ok') {
          let surveyStore = await Formsurvey.getById(id)
          surveyStore.deleted = true
          await surveyStore.save()
          this.fetchData()
        }
      }

      this.$uxMessage(
        'confirmation',
        'Are you sure you want to delete this survey?',
        callbackFunction
      )
    },
    getTitle(index: number) {
      return !this.isV2SideBarEnabled ? this.tabsTitle[index] : ''
    },
    getRoute(id: string) {
      if (this.isV2SideBarEnabled) {
        return {
          name: 'survey-manage',
          params: {
            location_id: this.currentLocationId,
            survey_id: id,
          },
        }
      } else {
        return {
          name: 'survey_manage',
          params: {
            location_id: this.currentLocationId,
            survey_id: id,
          },
        }
      }
    }
  },
  computed: {
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
    filteredFormsurvey(): Formsurvey[] {
      return !this.filters.text
        ? this.formsurvey
        : this.formsurvey.filter(
            x =>
              !this.filters.text ||
              x.name.toLowerCase().includes(this.filters.text.toLowerCase())
          )
    },
    fields() {
      return [
        { key: 'name', sortable: true, checked: true, isRequired: true },
        { key: 'id', label: 'Survey Id', sortable: true, checked: true },
        { key: 'status', sortable: true, checked: true },
        { key: 'action', label: '', sortable: false, checked: true },
      ]
    },
    items() {
      return this.filteredFormsurvey.map(survey => {
        return {
          id: survey.id,
          name: survey._data.name,
          status: survey.deleted ? 'Inactive' : 'Active',
        }
      })
    },
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    isV2SideBarEnabled() {
      return this.$store.getters['sidebarv2/getVersion'] == 'v2'
    },
    getHeading() {
      let heading = 'Surveys'
      if (this.isV2SideBarEnabled) {
        heading = this.tabsTitle[this.tabIndex]
      }
      return heading
    }
  },
})
</script>
<style lang="scss">
#form-builder {
  .hl_datepicker {
    padding: 0 20px;
  }

  .date-time-picker {
    .field-input {
      background-color: white !important;
      height: 0 !important;
    }
  }
}
.hl_wrapper--inner .nav.nav-tabs {
  padding: 0px 20px;
}
.hl_wrapper--inner .nav.nav-tabs .nav-link.active {
  color: #188bf6;
  border-color: #188bf6;
}
.hl_wrapper--inner .nav.nav-tabs .nav-link {
  padding-bottom: 10px;
  border-bottom: 3px solid transparent;
  color: #607179;
  font-weight: 500;
  font-size: 14px;
}

*:focus {
  outline: none;
}
</style>
