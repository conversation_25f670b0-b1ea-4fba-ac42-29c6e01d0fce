<template>
  <modal
    v-if="modalShow"
    @close="closeModal"
    :maxWidth="500"
    :showCloseIcon="true"
    :noBackdropClose="true"
  >
    <div class="nogmb-container" style="padding-top: 40px">
      <div class="sub-title mt-2 mb-5">
        We could not find Google My Business page in the Google account that you
        have integrated. Please create a GMB page and then try to connect or
        login into the Google account that has GMB page.
      </div>
      <div class="nogmb-btn">
        <button
          v-if="!loader"
          id="launchpad-gmb-tryagain"
          class="btn btn-primary active"
          type="button"
          @click.once="tryagain"
        >
          Try again
        </button>
        <moon-loader
          v-if="loader"
          :loading="loader"
          color="#37ca37"
          size="20px"
        />
      </div>
    </div>
  </modal>
</template>
<script lang="ts">
import Vue from 'vue'
import Modal from '@/pmd/components/common/Modal.vue'

export default Vue.extend({
  props: ['modalShow'],
  components: {
    Modal,
  },
  data() {
    return {
      popupWidth: 500,
      loader: false,
    }
  },
  methods: {
    async closeModal() {
      this.$root.$emit('close-nogmb-modal')
    },
    tryagain() {
      this.$root.$emit('google-connect')
    },
  },
})
</script>
<style>
.nogmb-container {
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: start;
  padding: 27px 30px;
  text-align: start;
  width: 100%;
}
.nogmb-container .sub-title {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 157.19%;
  color: #111827;
}
.nogmb-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
}
</style>
