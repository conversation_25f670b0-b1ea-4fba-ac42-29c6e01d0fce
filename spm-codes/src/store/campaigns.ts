import { Module, ActionContext } from 'vuex'
import { RootState, CampaignState } from '../store/state_models'
import { Campaign } from '../models'

let campaignsListner

export const campaigns: Module<CampaignState, RootState> = {
  namespaced: true,
  state: { campaigns: [], locationId: undefined },
  getters: {
    getById: (state: CampaignState) => {
      return (id: string) => state.campaigns.find(x => x.id === id)
    },
    getAll: (state: CampaignState) => {
      return state.campaigns
    }
  },
  mutations: {
    addUpdate: (state: CampaignState, payload: { campaigns: any, locationId: string }) => {
      state.campaigns = payload.campaigns
      if (payload.locationId) {
        state.locationId = payload.locationId
      }
    },
    clearAll: (state: CampaignState) => {
      state.campaigns = []
      state.locationId = undefined
      if (campaignsListner) {
        campaignsListner()
      }
    }
  },
  actions: {
    async syncAll(
      context: ActionContext<CampaignState, RootState>,
      locationId: string
    ) {
      if(!locationId) {
        context.commit('clearAll')
        return
      }


      if (context.state.locationId === locationId) {
        return context.state.campaigns;
      }

      context.commit('clearAll')
      return new Promise((resolve, reject) => {

        if (campaignsListner) {
          campaignsListner()
        }

        campaignsListner = Campaign.getQueryWithLocationId(
          locationId
        ).onSnapshot(snapshot => {
          context.commit('addUpdate', {
            locationId: locationId,
            campaigns: snapshot.docs.map(d => {
              return {
                id: d.id,
                ...d.data()
              }
            })
          })

          resolve(context.state.campaigns.campaigns)
        }, err => resolve())
      })
    },
    logout: {
      root: true,
      handler: async (context: ActionContext<CampaignState, RootState>) => {
        context.commit('clearAll')
      }
    },
    clearAll: {
      handler: async (context: ActionContext<CampaignState, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
