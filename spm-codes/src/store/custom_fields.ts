
import { Modu<PERSON>, ActionContext } from 'vuex'
import { RootState, LocationCustomFieldsState } from './state_models'
import { CustomField } from '../models'
import { FieldModel, ViewOnlyCustomField } from '../models/custom_field';

export const locationCustomFields: Module<LocationCustomFieldsState, RootState> = {
  namespaced: true,
  state: { customFields: [], locationId: undefined } as LocationCustomFieldsState,
  getters: {
    getCustomFields(state: LocationCustomFieldsState) {
      return [...state.customFields];
    },
    getLocationId (state: LocationCustomFieldsState) {
      return (locationId: string) => state.customFields.filter(a=> a.locationId === locationId)
    },
    getByLocationAndType (state: LocationCustomFieldsState) {
      return (locationId: string, model: FieldModel) => state.customFields.filter(a=> a.locationId === locationId && a.model === model)
    }
  },
  mutations: {
    setCustomFields (state, customFields) {
      state.customFields = [...customFields];
    },
    setLocationId (state, locId) {
      state.locationId = locId;
    },
    clearAll(state) {
      state.customFields = [];
    }
  },
  actions: {
    async syncAll(
      context: ActionContext<LocationCustomFieldsState, RootState>,
      params: { locationId: string, forceRefresh?: boolean}
    ) {
      if(!params || !params.locationId) {
        context.commit('clearAll')
        return
      }

      const locationId = params.locationId;

      if (!params.forceRefresh && context.state.locationId === locationId) return;
      context.commit('setLocationId',params.locationId);

      const snapShotData = await CustomField.getByLocationId(locationId).get();

      if (snapShotData && context.state.locationId ===  locationId) { // some other call can changed locationid while awaiting for custom fields to load from fire store
        const data = snapShotData.docs.map(d => ViewOnlyCustomField.getInstance(d));
        context.commit('setCustomFields',data);
      }
    },
    logout: {
      root: true,
      handler: async (context: ActionContext<LocationCustomFieldsState, RootState>) => {
        context.commit('clearAll')
      }
    },
    clearAll: {
      handler: async (context: ActionContext<LocationCustomFieldsState, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
