<template>
  <div v-if="disabled">{{disabledDisplay || display}}</div>
  <div class="dropdown" :class="customclass" v-else>
    <a
      id="dropdownMenuButton"
      @click.stop.prevent="dropdownToggler.toggle()"
      data-toggle="dropdown"
      aria-haspopup="true"
      aria-expanded="false">
      <span style="color: #188bf6">{{ display }}</span>
      <i class="fas fa-caret-down --light ml-2"></i>
    </a>
    <div
      class="dropdown-menu"
      :id="dropdownToggler.id"
      aria-labelledby="dropdownMenuButton"
      style="width:100%; min-height:50px; max-height: 300px; overflow: auto;">
      <div
        class="dropdown-item pointer text-wrap text-break"
        v-for="s in items"
        :key="s.key"
        @click.stop.prevent="itemSelected(s)">
        <span class="text  text-wrap text-break space-wrap-preline">
          {{ s.value }}
        </span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { SafeDropDownToggle } from './safe_drop_down_toggle'

export class KeyVal {
  constructor(public key: string | number, public value?: any) {
    if (!this.value && this.key) this.value = this.key
  }
}

export default Vue.extend({
  name: 'CommonSelector',
  props: {
    display: String,
    disabled: Boolean,
    disabledDisplay: String,
    customclass: String,
    items: {
      type: Array,
      validator: prop => prop.every(e => e instanceof KeyVal) //https://michaelnthiessen.com/unlock-full-potential-prop-types/
    }
  },
  data() {
    return {
      dropdownToggler: new SafeDropDownToggle(this, 'skipDaysToggle')
    }
  },
  computed: {
    // display1(){
    //   return this.display;
    // },
    // disabledDisplay1(){
    //   return this.disabledDisplay;
    // }
  },
  methods: {
    async itemSelected(item: KeyVal) {
      await this.dropdownToggler.toggle()
      this.$emit('selectionChange', item)
    }
  }
})
</script>
