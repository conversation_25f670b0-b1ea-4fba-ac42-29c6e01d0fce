<template>
  <b-modal ref="modal-sm-d-1" size="sm" @hide="close" hide-footer>
    <template v-slot:modal-title>
      <moon-loader-title :title="'Bulk Action'" />
      <!-- <h5 class="modal-title"></h5> -->
    </template>
    <template>
      <div class="hl_rules--wrap" ref="modal-card">
        <div class="card">
          <div class="card-body">
            <div class="mt-1 mb-2">
              <div>
                <strong>Type:</strong>
                <span
                  class="text-capitalize"
                  @click.prevent.stop="showId = true"
                  v-b-tooltip.hover
                  :title="`Request Id ${request.id}`"
                >
                  {{
                    request.operationType
                      ? request.operationType.replace('-', ' ')
                      : ''
                  }}
                </span>
                <span v-if="showId" class="mx-4">{{ request.id }}</span>
              </div>
            </div>
            <!-- <div class="mt-1 mb-2 ">
              <span class="text-capitalize">
                <strong>Total Items:</strong> {{ request.totalCount }}
              </span>
            </div> -->
            <div class="mt-1 mb-2 " v-if="!v2">
              <span class="text-capitalize">
                <strong>Current Status:</strong> {{ request.currentStatus }}
              </span>
            </div>
            <!-- <div class="mt-1 mb-2 " v-if="v2 && showModes()">
              <span
                class="text-capitalize"
                v-if="schedule.dripMode === 'processAllAtSchedule'">
                <strong>Mode:</strong> Later
              </span>
              <span
                class="text-capitalize"
                v-else-if="schedule.dripMode === 'drip'">
                <strong>Mode:</strong> Now (Drip)
              </span> -->
              <!-- This is per wisdom seen in https://doc.clickup.com/d/h/87cpx-732/8e30181c71bafd6/87cpx-774 -->
              <!-- <span class="text-capitalize" v-else>
                <strong>Mode:</strong> Now
              </span>
            </div> -->
            <div
              class="mt-1 mb-2 "
              v-if="request.opSpecs && request.opSpecs.from">
              <div class="d-inline-flex">
                <div><strong>From:</strong></div>
                <div class="mx-1" style="max-width: 250px">
                  {{ request.opSpecs.from }}
                </div>
              </div>
            </div>
            <div
              class="mt-1 mb-2 "
              v-if="request.opSpecs && request.opSpecs.templateName">
              <div class="d-inline-flex">
                <div><strong>Template Name:</strong></div>
                <div class="mx-1" style="max-width: 250px">
                  {{ request.opSpecs.templateName }}
                </div>
              </div>
            </div>
            <div v-if="request.operationNote">
              <div class="mt-1 mb-2 d-inline-flex">
                <div style="max-width: 150px" v-if="!v2">
                  <strong>Description 1:</strong>
                </div>
                <div style="max-width: 150px" v-if="v2">
                  <strong>Name:</strong>
                </div>
                <div class="mx-1">{{ request.operationNote || '-' }}</div>
              </div>
            </div>
            <div v-if="request.operationDescription && request.operationType !== 'bulk-merge-contact'">
              <div class="mt-1 mb-2 d-inline-flex">
                <div>
                  <span class="text-capitalize">
                    <strong>Additional Details:</strong>
                  </span>
                </div>
                <div class="mx-1" style="max-width: 300px">
                  {{ request.operationDescription }}
                </div>
              </div>
            </div>

            <div class="mt-1 mb-2 " v-if="allowEdit && request.sprintSchedule">
              <AllAtScheduler
                :schedule="schedule"
                :modes="modes"
                v-if="schedule && schedule.dripMode === 'processAllAtSchedule'"
              ></AllAtScheduler>
              <BulkScheduler
                :schedule="schedule"
                :modes="modes"
                v-if="schedule && schedule.dripMode === 'drip'"
              ></BulkScheduler>
              <div
                class="modal-buttons d-flex align-items-center justify-content-between px-2">
                <UIButton use="outline" @click.prevent.stop="close" data-dismiss="modal">
                  Cancel
                </UIButton>
                <div class="d-inline-flex">
                  <UIButton
                    @click.prevent="reschedule"
                    :loading="sending">
                    Save Changes
                  </UIButton>
                </div>
              </div>
            </div>

            <template v-else>
              <div class="mt-1 mb-2" v-if="request.processingStartedOn">
                <span
                  class="text-capitalize"
                  v-if="request.processingStartedOn"
                  :set="
                    (parts = getDateParts(
                      request.processingStartedOn,
                      locationTimeZone
                    ))
                  "
                >
                  <strong>Scheduled Start:</strong>
                  <span class="date-display text-nowrap mx-1">{{
                    parts[0]
                  }}</span>
                  <span class="date-display --blue text-nowrap mx-1"
                    >{{ parts[1] }} <small>({{ parts[2] }})</small></span
                  >
                </span>
              </div>
              <div class="mt-1 mb-2 " v-if="request.nextProcessingOn && request.status !== 'complete'">
                <span
                  class="text-capitalize"
                  v-if="request.nextProcessingOn"
                  :set="
                    (parts = getDateParts(
                      request.nextProcessingOn,
                      locationTimeZone
                    ))
                  "
                >
                  <strong>Next Processing On:</strong>
                  <span class="date-display text-nowrap mx-1">{{
                    parts[0]
                  }}</span>
                  <span class="date-display --blue text-nowrap mx-1"
                    >{{ parts[1] }} <small>({{ parts[2] }})</small></span
                  >
                </span>
              </div>
              <!-- <div class="mt-1 mb-2 " v-if="request.selectedRecordsCount"> // Tony does not want to show it.
                <span class="text-capitalize">
                  <strong>Selected Records Count:</strong>
                  {{ request.selectedRecordsCount }}
                </span>
              </div> -->
              <div class="mt-1 mb-2 " v-else-if="showModes()">
                <span class="text-capitalize">
                  <strong>Mode:</strong>
                  <span class="mx-1" v-if="request.isDripMode">Process Gradually (Drip)</span>
                  <span
                    class="mx-1"
                    v-else-if="request.isProcessAllAtScheduleMode">Process All On Given Date</span>
                  <span class="mx-1" v-else>Process All As Soon As Possible</span>
                </span>
              </div>
              <div class="mt-1 mb-2" v-if="request.totalCount && !v2 && request.processingStartedOn">
                <span class="text-capitalize">
                  <strong>Progress:</strong>
                  <span
                    class="mx-1"
                    v-if="request.successCount || request.errorCount"
                    >{{
                      Math.round(
                        ((request.successCount || 0 + request.errorCount || 0) /
                          request.totalCount) *
                          100
                      ).toFixed(2)
                    }}%</span
                  >
                  <span v-else class="mx-1">{{request.currentStatus}}</span>
                </span>
              </div>
              <div class="mt-1 mb-2" v-if="request.simplifiedStatus === 'processing' && v2">
                <span class="text-capitalize">
                  <strong>Progress:</strong>
                  <span
                    class="mx-1"
                    v-if="request.successCount || request.errorCount"
                    >{{
                      Math.round(
                        ((request.successCount || 0 + request.errorCount || 0) /
                          request.totalCount) *
                          100
                      ).toFixed(2)
                    }}%</span
                  >
                  <span v-else class="mx-1">{{request.simplifiedStatus}}</span>
                </span>
              </div>
              <div class="mt-1 mb-2 " v-if="request && !request.isDripMode && (request.simplifiedStatus === 'scheduled'  || request.simplifiedStatus === 'processing' || request.simplifiedStatus === 'queued')">
                <span class="text-capitalize">
                  <strong>Time Elapsed:</strong>
                  <span class="mx-1">
                    {{getElapsedMinutes()}}
                    </span>
                </span>
              </div>
              <div
                class="mt-1 mb-2 "
                v-if="
                  request.sprintSchedule &&
                  request.sprintSchedule.processWindowStart
                ">
                <span class="text-capitalize">
                  <strong>Process During:</strong>
                  {{ request.sprintSchedule.processWindowStart }} -
                  {{ request.sprintSchedule.processWindowEnd }}
                </span>
              </div>
              <div class="mt-1 mb-2 " v-if="request.sprintSchedule && request.isDripMode">
                <span class="text-capitalize">
                  <strong>Processing Allowed On:</strong>
                  {{
                    getAllowedDays(
                      request.sprintSchedule.skipDays
                        ? request.sprintSchedule.skipDays
                        : []
                    )
                  }}
                </span>
              </div>
              <div
                class="mt-1 mb-2 "
                v-if="request.sprintSchedule && request.sprintSchedule.sprintDelay">
                <span class="text-capitalize">
                  <strong>Delay Between Batches:</strong>
                  {{ request.sprintSchedule.sprintDelay }}
                  {{ request.sprintSchedule.sprintDelayType }}
                </span>
              </div>
              <div class="mt-1 mb-2 " v-if="request.sprintSchedule && request.isDripMode">
                <template v-if="allowSprintChange">
                  <div class="d-flex justify-content-start  align-items-center">
                  <div class="text-capitalize mr-2 d-flex align-items-center">
                    <strong>Batch Quantity:</strong>
                  </div>
                  <div class="mr-2">
                    <UITextInput
                      type="text"
                      v-model="request.sprintSchedule.sprintLimit"
                      v-validate="'required'"
                      name="quantity"></UITextInput>
                  </div>
                  <div class="mr-2">
                    <UIButton
                      @click.prevent="changeSprintLimit"
                      :loading="sending">
                      Update Batch Quantity
                    </UIButton>
                  </div>
                  </div>
                  <div v-if="errors.has('quantity')" class="error">
                    <span v-show="errors.has('quantity')" class="error">
                      Please enter a quantity
                    </span>
                  </div>
                </template>
                <template v-else>
                  <span class="text-capitalize">
                    <strong>Batch Quantity:</strong>
                    {{ request.sprintSchedule.sprintLimit }}
                  </span>
                </template>
              </div>
            </template>
            <div
              v-if="(request.operationType === 'bulk-tag-add' ||
                    request.operationType === 'bulk-tag-remove') && (request.opSpecs && request.opSpecs.tags && request.opSpecs.tags.length)">
              <div class="mt-1 mb-2 d-inline-flex">
                <div style="max-width: 150px">
                  <strong>Tags ({{ request.operationType === 'bulk-tag-add'? "Add" : "Remove" }}):</strong>
                </div>
                <div v-for="item in request.opSpecs.tags" :key="item" class="mx-1 capitalize tag">{{ item}}</div>
              </div>
            </div>
          <template v-if="exportStats && !v2">
              <div class="more-info-data text-center space-apart">
                <span class="--light h5 mx-2">{{ exportStats.label }}</span>
                <span class="--green h5 my-0 py-0">
                  {{ exportStats.quantity  }}
                </span>
              </div>
          </template>
          <template v-else-if="mergedStats && !v2">
              <div class="more-info-data text-center space-apart">
                <span class="--light h5 mx-2">{{ mergedStats.label }}</span>
                <span class="--green h5 my-0 py-0">
                  {{ mergedStats.quantity }}
                </span>
              </div>
            </template>
            <div class="pad-top-10 mb-2" v-else-if="!v2">
              <div
                class="more-info more-detail-animation d-flex w-100 justify-content-center">
                <div class="more-info-data text-center space-apart">
                  <div class="--purple h5 my-0 py-0">
                    {{ request.totalCount }}
                  </div>
                  <div class="--dark">Total</div>
                </div>
                <div class="more-info-data text-center space-apart" v-if="!v2">
                  <div class="--yellow h5 my-0 py-0">
                    {{ request.queuedCount || '-' }}
                  </div>
                  <div class="--dark">Queued</div>
                </div>
                <div class="more-info-data text-center space-apart">
                  <div class="--green h5 my-0 py-0">
                    {{ request.successCount || '-' }}
                  </div>
                  <div class="--dark">Success</div>
                </div>
                <div class="more-info-data text-center space-apart">
                  <div class="--red h5 my-0 py-0">
                    {{ request.errorCount || '-' }}
                  </div>
                  <div class="--dark">Error</div>
                </div>
              </div>
            </div>
            <div
              class="mt-1 mb-2"
              v-if="!v2 && !request.selectedRecordsCount && request.hasDynamicList">
              <small>** uses dynamic list</small>
            </div>
          </div>
        </div>
      </div>
    </template>
  </b-modal>
</template>

<script lang="ts">
import Vue from 'vue'
import { BulkRequestV2 } from './server/bulk_req_v2'
import { BulkSchedule } from './server/bulk_schedule'
import BulkScheduler from './BulkScheduler.vue'
import AllAtScheduler from './AllAtScheduler.vue'
import { ScheduledBulkAction, ScheduledBulkActions } from './server/bulkreq_api'
import { UxMessage } from '@/util/ux_message'
import moment from 'moment-timezone'
import { toInteger } from 'lodash'

export default Vue.extend({
  name: 'BulkActionDescription',
  props: {
    request: BulkRequestV2,
    allowEdit: { type: Boolean, default: false },
    allowSprintChange: { type: Boolean, default: false },
    v2: { type: Boolean, default: false },
  },
  inject: ['uxmessage'],
  provide() {
    return { parentValidator: this.$validator }
  },
  components: {
    BulkScheduler,
    AllAtScheduler,
  },
  data() {
    return {
      exportStats: null as { label: string; quantity: number } | null,
      mergedStats: null as { label: string; quantity: number } | null,
      schedule: new BulkSchedule(this.request.sprintSchedule),
      sending: false,
      showId: false,
      opType: null as String | null,
      modes: [
        { value: 'none', text: 'Immediately' },
        { value: 'processAllAtSchedule', text: 'All On Given Date' },
        { value: 'drip', text: 'Drip' },
      ],
    }
  },
  async mounted() {
    document.addEventListener('keydown', this.keyDownHandler)
    this.showPopup()
    if (!this.request) return
    this.opType = this.request?.operationType?.toLowerCase()
    if (this.opType === 'bulk-export') {
      this.exportStats = {
        label: 'Total Exported',
        quantity: this.request?.opSpecs?.total,
      }
    } else if (this.opType === 'bulk-merge-contact') {
      this.mergedStats = {
        label: 'Total Merged',
        quantity: this.request?.opSpecs?.contactIds?.length,
      }
    }
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.keyDownHandler) // don't forget to remove the listener in destructor
  },
  methods: {
    close() {
      this.hidePopup()
      this.$emit('closed')
    },
    async showPopup() {
      if (this.request && this.$refs['modal-sm-d-1']) this.$refs['modal-sm-d-1'].show()
    },
    hidePopup() {
      this.$refs['modal-sm-d-1'].hide()
    },
    showError(err: string, stack?: string) {
      this.uxmessage(UxMessage.errorType(err, stack))
    },
    getAllowedDays(sendDays: string[]) {
      if (!sendDays) return BulkSchedule.WEEKDAYSLIST || []
      return (BulkSchedule.WEEKDAYSLIST || [])
        .filter(a => !sendDays.includes(a))
        .join(', ')
    },
    showModes(){
      return this.opType === 'bulk-email' || this.opType === 'bulk-sms' || this.opType === 'bulk-campaign' || this.opType === 'bulk-workflow';
    },
    getElapsedMinutes() {
      // console.log('Time Added')
      // console.log(this.request.dateAdded)
      // console.log(this.request.dateAdded.toISOString())
      // console.log(moment(this.request.dateAdded.clone().utc()).fromNow()) // `${moment().diff(this.request.dateAdded, 'minutes')} miutes`
      // console.log(moment().subtract(10,'minutes').fromNow());
      // const curtz =  moment.tz.guess();
      // console.log(curtz)
      // const c = moment.tz( this.request.dateAdded.clone(), curtz);
      // console.log(c)
      // console.log(`${moment().utc().tz(curtz).diff(c, 'minutes')}`)
      // console.log(c.fromNow()) // `${moment().diff(this.request.dateAdded, 'minutes')} miutes`
      // console.log(`${moment().diff(this.request.dateAdded, 'minutes')} mins`)
      if (!this.request) return 'NA'
      // else return this.request.dateAdded.fromNow(true)
      else return `${moment().diff(this.request.dateAdded, 'minutes')} minutes`
    },
    async changeSprintLimit(){
      if (!this.request.sprintSchedule.sprintLimit) {
        this.showError('Enter batch sprint limit')
        return
      }
      try {
        this.sending = true
        await ScheduledBulkAction.changeSprintLimit({
          bulkRequestId: this.request.id,
          sprintLimit: toInteger(this.request.sprintSchedule.sprintLimit),
        })
        this.hidePopup()
      } catch (e) {
        this.sending = false
        this.showError((e && e.message) || 'unknown error', e)
      } finally {
        this.sending = false
      }
    },
    async reschedule() {
      try {
        this.sending = true
        await ScheduledBulkAction.changeSchedule({
          bulkRequestId: this.request.id,
          sprintSchedule: this.schedule.getPersistence(),
        })
        this.hidePopup()
      } catch (e) {
        this.sending = false
        this.showError((e && e.message) || 'unknown error', e)
      } finally {
        this.sending = false
      }
    },
    keyDownHandler(e: any) {
      if (!this.request) return;
      if (e.keyCode === 72 && e.ctrlKey && e.shiftKey) {// key code for h is 72
        e.preventDefault();
        e.stopPropagation();
        ScheduledBulkActions.getItemDetails({bulkReqId: this.request.id}) // just use network tab to check history
      }
    },
  },
  computed: {
    locationTimeZone() {
      console.log(
        `Location Time Zone -- ${
          this.$store.getters[`locations/getCurrentLocationTimeZone`]
        }`
      )
      return this.$store.getters[`locations/getCurrentLocationTimeZone`]
    },
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.close()
    },
    request: function (item) {
      if (item) this.showPopup()
    },
  },
})
</script>
<style scoped>
.pad-top-10 {
  padding-top: 20px;
}
.hl_rules--wrap {
  margin-top: 5px;
}
.form-input-dropdown {
  position: absolute;
  will-change: transform;
  top: 0px;
  left: 0px;
  transform: translate3d(0px, 51px, 0px);
  min-width: 0;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
}
td.td-borderless {
  border-top: none !important;
  padding: 0px !important;
  padding-bottom: 6px !important;
}
.space-apart {
  margin-right: 45px;
  margin-left: 45px;
}
.fa-phone {
  color: #188bf6;
  margin-right: 10px;
  font-size: 20px;
}
.email-stats__single {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 1rem;
  color: #2a3135;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}

.more-detail-animation {
  max-height: 87px;
  overflow: hidden;
  transition: max-height 0.5s ease-out;
}

.stats-clickable {
  cursor: pointer;
}
.no-select {
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}
</style>
