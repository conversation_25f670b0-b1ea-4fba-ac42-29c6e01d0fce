import { Module, ActionContext } from 'vuex'
import { RootState, FiltersState } from '../store/state_models'
const store = require('store')

export const filters: Module<FiltersState, RootState> = {
  namespaced: true,
  state: { filters: {}, locationId: undefined },
  mutations: {
    addUpdateChild: (state: FiltersState, payload: { type: string, filters: { [key: string]: any } }) => {
      state.filters[payload.type] = payload.filters || {}
      store.set('filters-' + state.locationId, state.filters)
    },
    addUpdate: (state: FiltersState, payload: { [key: string]: any }) => {
      state.filters = payload.filters || {}
      if (payload.locationId) {
        state.locationId = payload.locationId
      }
    },
    clearAll: (state: FiltersState) => {
      state.filters = {}
      state.locationId = undefined
    }
  },
  actions: {
    syncAll(context: ActionContext<FiltersState, RootState>, locationId: string) {
      if (context.state.locationId === locationId) {
        return;
      }

      const filters = store.get('filters-' + locationId);
      context.commit('addUpdate', {
        filters,
        locationId
      });
    },
    logout: {
      root: true,
      handler: async (context: ActionContext<FiltersState, RootState>) => {
        context.commit('clearAll')
      }
    },
    clearAll: {
      handler: async (context: ActionContext<FiltersState, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
