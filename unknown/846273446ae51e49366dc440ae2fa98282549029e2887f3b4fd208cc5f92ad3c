<template>
  <!--Modal-->
  <div class="modal fade hl_sms-template--modal" tabindex="-1" role="dialog" ref="modalHtmlFooter">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <!-- Header - START -->
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Footer Html</h2>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <!-- Header - END -->

        <!-- Body - START -->
        <div class="modal-body pb-2">
          <div class="modal-body--inner">
            <textarea
              rows="8"
              cols="0"
              class="footer-modal-html mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
              v-model="htmltext"
            ></textarea>
          </div>
        </div>
        <!-- Body - END -->

        <!-- Footer - START -->
        <div class="modal-footer">
          <div class="modal-footer--inner nav">
            <button
              type="button"
              class="btn btn-primary"
              data-dismiss="modal"
              @click="handleSave"
            >Save</button>
          </div>
        </div>
        <!-- Footer - END -->
      </div>
    </div>
  </div>
  <!--End Modal-->
</template>

<script lang="ts">
import Vue from "vue";
declare var $: any;

export default Vue.extend({
	props: {
		values: {} as any
	},
	data() {
		return {
      htmltext: ''
		};
	},
	computed: {

	},
	watch: {
		values(values: { [key: string]: any }) {
      // Handle show/hide states of modal
			if (values.visible) $(this.$refs.modalHtmlFooter).modal("show");
			else $(this.$refs.modalHtmlFooter).modal("hide");
		}
	},
	updated() {
    // Handle show/hide states of modal
		if (this.values && this.values.visible) {
			$(this.$refs.modalHtmlFooter).modal("show");
		}
	},
	mounted() {
    if (this.values && this.values.visible) {
      this.htmltext = ("text" in this.values) ? this.values.text : "";
      $(this.$refs.modalHtmlFooter).modal("show");
		}
	},
	methods: {
    handleSave(){
      this.$bus.$emit('field-setting-form-advance',{'type': 'footer-html', 'value': this.htmltext});
    }
	},
});
</script>
