<template>
    <div :class="`hl_funnel-add-new-variation--modal${source === 'sidebar'?'':' in-modal'}` ">
        <div v-if="!selectedTemplate._id">
            <div class="hl_new-variation--header">
                <ul class="filter">
                    <li :class="snapshotTab === 'vertical'? 'active':''" @click.prevent="snapshotTab = 'vertical'">
                        <a href="#">Vertical Snapshots ({{verticalTemplates.length}})</a>
                    </li>
                    <li :class="snapshotTab === 'own'? 'active':''" @click.prevent="snapshotTab = 'own'">
                        <a href="#">Own Snapshots ({{ownTemplates.length}})</a>
                    </li>
                    <li :class="snapshotTab === 'imported'? 'active':''" @click.prevent="snapshotTab = 'imported'">
                        <a href="#">Imported Snapshots ({{importedTemplates.length}})</a>
                    </li>
                </ul>
            </div>
            <div class="hl_new-variation--templates row" v-if="snapshotTab === 'vertical'">
                <div class="col-sm-6 col-md-4 col-xl-3" v-if="source !== 'sidebar'">
                  <div class="template-card dfy-card">
                      <div class="px-3 dfy-card-img">
                        <h6 class="text-center text-blue sub-title pt-2 mb-0">DFY Account Creation</h6>
                        <img
                          src="https://storage.googleapis.com/highlevel-backend.appspot.com/templates/image_artwork.png"
                          alt="DFY Account creation"
                          style="height: 133px;object-fit: fill;"
                        >
                      </div>
                      <div class="dfy-card-info d-flex py-2">
                        <div class="align-self-center ml-2 mr-1">
                          <h2 class="text-center mb-0">$299</h2>
                        </div>
                        <div class="align-self-center p-2 dfy-card-info-description">
                          <p>Get this account setup by our team of experts</p>
                        </div>
                      </div>
                      <div class="template-card-actions">
                          <a href="#" class="btn btn-success btn-xs" @click.prevent="$emit('select', { type: 'dfy' })">
                          {{source === 'sidebar'? 'Select & Create New Account':'Know More'}}
                          <i class="icon icon-arrow-right-2"></i></a>
                      </div>
                  </div>
                </div>
                <div class="col-sm-6 col-md-4 col-xl-3" v-if="source !== 'sidebar'">
                    <div class="template-card">
                        <img src="https://firebasestorage.googleapis.com/v0/b/highlevel-backend.appspot.com/o/templates%2FBlank.jpg?alt=media&token=f936584d-65f0-4a85-9b80-cb30d96e1ea7">
                        <h4>Blank Snapshot</h4>
                        <div class="template-card-actions">
                            <a href="#" class="btn btn-success btn-xs" @click.prevent="$emit('select',{type:'blank'})">
                            {{source === 'sidebar'?'Select & Create New Account':'Select & Continue'}}
                            <i class="icon icon-arrow-right-2"></i></a>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6 col-md-4 col-xl-3" v-for="n in verticalTemplates" :key="n._id">
                    <div class="template-card">
                        <img :src="n._data.image_url">
                        <h4>{{n._data.name}}</h4>
                        <div class="template-card-actions">
                            <a href="#" class="btn btn-success btn-xs" @click.prevent="$emit('select',n)">
                            {{source === 'sidebar'?'Select & Create New Account':'Select & Continue'}}
                            <i class="icon icon-arrow-right-2"></i></a>
                            <a href="#" class="btn btn-blue btn-xs" @click.prevent="openTemplate(n)"><i class="far fa-eye"></i> Know More</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="hl_new-variation--templates hl_template-own" v-if="snapshotTab === 'own'">
                <div class="hl_template-own--tuple " v-for="snapshot in ownTemplates" :key="snapshot.id">
                    <div class="hl_template-own--info">
                        <div class="hl_template-own--avtar" >{{getAvtar(snapshot.name)}}</div>
                        <div class="hl_template-own--name" >{{snapshot.name}}</div>
                    </div>
                    <div class="hl_template-own--actions">
                        <a href="#" class="btn btn-success btn-xs" @click.prevent="$emit('select',snapshot)">
                            {{source === 'sidebar'?'Select & Create New Account':'Select & Continue'}}
                            <i class="icon icon-arrow-right-2"></i>
                        </a>
                    </div>
                </div>
            </div>
            <div class="hl_new-variation--templates hl_template-own" v-if="snapshotTab === 'imported'">
                <div class="hl_template-own--tuple " v-for="snapshot in importedTemplates" :key="snapshot.id">
                    <div class="hl_template-own--info">
                        <div class="hl_template-own--avtar" >{{getAvtar(snapshot.name)}}</div>
                        <div class="hl_template-own--name" >{{snapshot.name}}</div>
                    </div>
                    <div class="hl_template-own--actions">
                        <a href="#" class="btn btn-success btn-xs" @click.prevent="$emit('select',snapshot)">
                            {{source === 'sidebar'?'Select & Create New Account':'Select & Continue'}}
                            <i class="icon icon-arrow-right-2"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <template-details v-else :selectedTemplate="selectedTemplate" @back="onBack"/>
    </div>

</template>
<script lang="ts">
import Vue from 'vue'
import TemplateDetails from '@/pmd/components/agency/snapshot_template/TemplateDetails.vue';
// const TemplateDetails = () => import('@/pmd/components/agency/snapshot_template/TemplateDetails.vue');
import { SnapshotTemplate, CloneAccount, AuthUser } from '@/models';

export default Vue.extend({
    props: ['source'],
    components: { TemplateDetails },
    data() {
        return {
            authUser: {} as AuthUser,
			verticalTemplates: [] as SnapshotTemplate[],
			ownTemplates: [] as SnapshotTemplate[],
			importedTemplates: [] as SnapshotTemplate[],
			selectedTemplate: {} as SnapshotTemplate,
			snapshotTab: 'vertical',
		}
    },
    mounted(){
        this.fetchData();
    },
    methods:{
        async fetchData() {
          // Fetching Default Templates
          // TODO: We can fetch this list locally, directly with SDK
          const url = '/snapshot/list_templates';
          this.$http.get(url).then(response => {
              this.verticalTemplates = response.data.sort( (a, b) =>
                  a._data.name.localeCompare(b._data.name)
              );
          });

          // Fetching Self Templates
          this.authUser = await this.$store.dispatch("auth/get");
          const snapshots = await CloneAccount.getByCompanyId(this.authUser.companyId).then(snapshots => {
            this.importedTemplates = snapshots.filter(s => s.type === 'imported').sort((a, b) => a.name.localeCompare(b.name));
            this.ownTemplates = snapshots.filter(s => s.type === 'own').sort((a, b) => a.name.localeCompare(b.name));
          });
        },

		    openTemplate(template){
			  // this.activeTab = 'overview';
            this.selectedTemplate = template;
            this.$emit('open',template)
        },
        onBack(){
            this.selectedTemplate = {};
            this.$emit('open',{});
        },
        getAvtar(name: String){
            let nameWords = name.split(' ');
            return nameWords.map( word=>{
                return word[0];
            }).join('').toUpperCase();
        }
    }
})
</script>

<style scoped>
@media (min-width: 992px){
	.hl_new-variation--templates {
		height: unset !important;
	}
    .in-modal .hl_new-variation--templates{
        max-height: calc(100vh - 310px);
    }
}

.hl_new-variation--templates .dfy-card {
  background: linear-gradient(233.13deg, #E5F0FE -1.04%, #FFFFFF 108.16%);
}

.hl_new-variation--templates .dfy-card .dfy-card-img .sub-title {
  color: #3182CE;
  font-size: 17px;
}

.hl_new-variation--templates .dfy-card .dfy-card-info h2 {
  font-weight: bold;
  font-size: 27px;
  line-height: 32px;

  color: #4299E1;
}

.hl_new-variation--templates .dfy-card .dfy-card-info {
  background: #FFFFFF;
  box-shadow: 0px -4px 21px rgba(0, 0, 0, 0.07);
  border-radius: 0px 0px 3px 3px;
}

.hl_new-variation--templates .dfy-card .dfy-card-info .dfy-card-info-description p{
  font-size: 14px;
  line-height: 16px;
  color: #373F42;
}

.hl_new-variation--templates .template-card img {
    height: 200px;
    object-fit: cover;
}
.hl_new-variation--templates .template-card{
	height: unset;
}
.hl_new-variation--templates>div{
	height: fit-content;
}

.hl_template-own{
    display: flex;
    flex-wrap: wrap;
}

.hl_template-own--tuple{
    border: 2px solid #f2f7fa;
    border-radius: 4px;
    /* cursor: pointer; */
    padding: 10px;
    margin-bottom: 10px;
    margin-right: 10px;
    box-sizing: border-box;

    width: calc(50% - 10px);
    position: relative;
}
@media (max-width: 575px){
    .hl_template-own--tuple{
        width: 100%;
    }
}
.hl_template-own--actions{
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(42,49,53,0.2);
    border-radius: 4px;
    top: 0px;
    left: 0px;
    display:none;
    justify-content: flex-end;
    align-items: center;
    padding-right: 20px;
}
.hl_template-own--tuple:hover .hl_template-own--actions{
    display: flex;
}
.hl_template-own--info{
    display: flex;
}
.hl_template-own--avtar{
    height: 40px;
    width: 40px;
    border-radius: 4px;
    line-height: 40px;
    text-align: center;

    margin-right: 20px;
    color: #f7fafc;
    background-color: #118cf6;
}
.hl_template-own--name{
    line-height: 40px;

}
</style>
