<template>
  <modal
    v-if="modalShow"
    @close="closeModal"
    :maxWidth="500"
    :showCloseIcon="true"
    :noBackdropClose="true"
  >
    <div class="card">
      <div class="card-header">
        <h3 style="font-weight: bold">Add Users</h3>
      </div>
      <div class="card-body">
        <span style="font-size: 12px; color: #718096"
          >The new user(s) will have the same permissions like yours except the
          ability to add new users.</span
        >
        <br /><br />
        <form data-vv-scope="form-add-user" name="form-add-user">
          <div class="form-group">
            <label style="font-weight: bold">Enter the email addresses:</label>
            <div class="tag-input" @click.prevent="focusInput">
              <div
                v-for="(id, index) in emailIds"
                :key="id.val"
                class="tag-input__tag"
                :class="{
                  'tag-input__tag__correct': id.valid,
                  'tag-input__tag__error': !id.valid || id.exists,
                }"
              >
                <div v-if="!id.valid || id.exists">
                  <svg
                    version="1.1"
                    id="Capa_1"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlns:xlink="http://www.w3.org/1999/xlink"
                    x="0px"
                    y="0px"
                    viewBox="0 0 191.812 191.812"
                    style="
                      enable-background: new 0 0 191.812 191.812;
                      margin-right: 5px;
                      width: 1rem;
                    "
                    xml:space="preserve"
                  >
                    <g>
                      <path
                        style="fill: #e93d3d"
                        d="M95.906,121.003c6.903,0,12.5-5.597,12.5-12.5V51.511c0-6.904-5.597-12.5-12.5-12.5
		s-12.5,5.596-12.5,12.5v56.993C83.406,115.407,89.003,121.003,95.906,121.003z"
                      />
                      <path
                        style="fill: #e93d3d"
                        d="M95.909,127.807c-3.29,0-6.521,1.33-8.841,3.66c-2.329,2.32-3.659,5.54-3.659,8.83
		s1.33,6.52,3.659,8.84c2.32,2.33,5.551,3.66,8.841,3.66s6.51-1.33,8.84-3.66c2.319-2.32,3.66-5.55,3.66-8.84s-1.341-6.51-3.66-8.83
		C102.419,129.137,99.199,127.807,95.909,127.807z"
                      />
                      <path
                        style="fill: #e93d3d"
                        d="M95.906,0C43.024,0,0,43.023,0,95.906s43.023,95.906,95.906,95.906s95.905-43.023,95.905-95.906
		S148.789,0,95.906,0z M95.906,176.812C51.294,176.812,15,140.518,15,95.906S51.294,15,95.906,15
		c44.611,0,80.905,36.294,80.905,80.906S140.518,176.812,95.906,176.812z"
                      />
                    </g>
                    <g></g>
                    <g></g>
                    <g></g>
                    <g></g>
                    <g></g>
                    <g></g>
                    <g></g>
                    <g></g>
                    <g></g>
                    <g></g>
                    <g></g>
                    <g></g>
                    <g></g>
                    <g></g>
                    <g></g>
                  </svg>
                </div>
                {{ id.val }}
                <span @click="removeTag(index)">
                  <i class="fa fa-times"></i>
                </span>
              </div>
              <input
                type="text"
                :placeholder="placeholder"
                class="tag-input__text"
                v-model="emailId"
                v-validate="'required'"
                name="emailid"
                @keydown.space="addEmail"
                @keydown.enter="addEmail"
                @keydown.188="addEmail"
                @keydown.delete="removeLastEmail"
                @blur="addEmail"
                ref="emailInput"
                :disabled="emailIds.length == 10"
              />
            </div>
            <span style="font-size: 12px; color: #718096"
              >Press <b>ENTER</b> after typing each email address.</span
            ><br />
            <div v-if="errorIdsCount" class="tag-wrong-count">
              <svg
                version="1.1"
                id="Capa_1"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                x="0px"
                y="0px"
                viewBox="0 0 191.812 191.812"
                style="
                  enable-background: new 0 0 191.812 191.812;
                  margin-right: 5px;
                  width: 0.85rem;
                "
                xml:space="preserve"
              >
                <g>
                  <path
                    style="fill: #e93d3d"
                    d="M95.906,121.003c6.903,0,12.5-5.597,12.5-12.5V51.511c0-6.904-5.597-12.5-12.5-12.5
		s-12.5,5.596-12.5,12.5v56.993C83.406,115.407,89.003,121.003,95.906,121.003z"
                  />
                  <path
                    style="fill: #e93d3d"
                    d="M95.909,127.807c-3.29,0-6.521,1.33-8.841,3.66c-2.329,2.32-3.659,5.54-3.659,8.83
		s1.33,6.52,3.659,8.84c2.32,2.33,5.551,3.66,8.841,3.66s6.51-1.33,8.84-3.66c2.319-2.32,3.66-5.55,3.66-8.84s-1.341-6.51-3.66-8.83
		C102.419,129.137,99.199,127.807,95.909,127.807z"
                  />
                  <path
                    style="fill: #e93d3d"
                    d="M95.906,0C43.024,0,0,43.023,0,95.906s43.023,95.906,95.906,95.906s95.905-43.023,95.905-95.906
		S148.789,0,95.906,0z M95.906,176.812C51.294,176.812,15,140.518,15,95.906S51.294,15,95.906,15
		c44.611,0,80.905,36.294,80.905,80.906S140.518,176.812,95.906,176.812z"
                  />
                </g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
              </svg>
              <p>
                {{ errorIdsCount }} {{ errorIdsCount > 1 ? ' errors.' : ' error.' }}
                <a @click.once="removeErrorIds">Remove all items with errors</a>
              </p>
            </div>
            <div v-if="existsIdsCount" class="tag-wrong-count">
              <svg
                version="1.1"
                id="Capa_1"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                x="0px"
                y="0px"
                viewBox="0 0 191.812 191.812"
                style="
                  enable-background: new 0 0 191.812 191.812;
                  margin-right: 5px;
                  width: 0.85rem;
                "
                xml:space="preserve"
              >
                <g>
                  <path
                    style="fill: #e93d3d"
                    d="M95.906,121.003c6.903,0,12.5-5.597,12.5-12.5V51.511c0-6.904-5.597-12.5-12.5-12.5
		s-12.5,5.596-12.5,12.5v56.993C83.406,115.407,89.003,121.003,95.906,121.003z"
                  />
                  <path
                    style="fill: #e93d3d"
                    d="M95.909,127.807c-3.29,0-6.521,1.33-8.841,3.66c-2.329,2.32-3.659,5.54-3.659,8.83
		s1.33,6.52,3.659,8.84c2.32,2.33,5.551,3.66,8.841,3.66s6.51-1.33,8.84-3.66c2.319-2.32,3.66-5.55,3.66-8.84s-1.341-6.51-3.66-8.83
		C102.419,129.137,99.199,127.807,95.909,127.807z"
                  />
                  <path
                    style="fill: #e93d3d"
                    d="M95.906,0C43.024,0,0,43.023,0,95.906s43.023,95.906,95.906,95.906s95.905-43.023,95.905-95.906
		S148.789,0,95.906,0z M95.906,176.812C51.294,176.812,15,140.518,15,95.906S51.294,15,95.906,15
		c44.611,0,80.905,36.294,80.905,80.906S140.518,176.812,95.906,176.812z"
                  />
                </g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
                <g></g>
              </svg>
              <p>
                {{ existsIdsCount }} Email address(es) already exists.
                <a href="javascript:void(0)" @click.once="removeExistsIds">
                  &nbsp;Remove all</a
                >
              </p>
            </div>
            <div v-if="correctIdsCount" class="tag-correct-count">
              <i class="fa fa-check-circle"></i>
              {{ correctIdsCount }} new user(s).
            </div>
          </div>
        </form>
      </div>
      <div class="card-footer" @click.stop="">
        <div v-if="sentInviteSuccess" class="card-verify--success">
          <i class="far fa-check-circle"></i> Your request has been sent
          successfully
        </div>
        <button
          v-if="!loader"
          id="launchpad-invite-user"
          class="btn btn-primary active"
          type="button"
          :disabled="!correctIdsCount"
          @click.prevent="sendInvite()"
        >
          Send Invite
        </button>
        <moon-loader
          v-if="loader"
          :loading="loader"
          color="#37ca37"
          size="20px"
        />
      </div>
    </div>
  </modal>
</template>
<script lang="ts">
import Vue from 'vue'
import Modal from '@/pmd/components/common/Modal.vue'
import { User, TwilioAccount, Company, Location, AuthUser, PrivateUser } from '@/models'
import { mapState } from 'vuex'
import { CompanyState, UserState } from '@/store/state_models'
import { UserSource, UserSourceChannel } from '@/models/user'
import moment from 'moment'

export interface EmailIds {
  val: string
  valid: boolean
  exists: boolean
}

export default Vue.extend({
  props: ['modalShow', 'currentLocationId'],
  components: {
    Modal,
  },
  data() {
    return {
      popupWidth: 500,
      loader: false,
      location: {} as Location,
      emailId: '',
      emailIds: [] as EmailIds[],
      placeholder: '<EMAIL>',
      sentInviteSuccess: false,
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      creating: false,
      hasWebsite: false,
      website: '',
      twilioAccount: {} as TwilioAccount,
      meetingLocation: undefined,
      timezone: undefined,
      openHours: [],
      userCalendar: '' as string,
      userTwilio: '' as undefined | string,
      existingLocations: {} as { [key: string]: string },
      incomingPhoneNumbers: [] as Array<{ [key: string]: any }>,
      locations: [] as Location[],
      passwordText: '',
      autofillSignature: false,
      replySignature: false,
      tinymcehtml: '' as string,
      isZoomAdded: '' as string,
      userPermissions: {
        campaigns_enabled: true,
        campaigns_read_only: false,
        workflows_enabled: true,
        workflows_read_only: false,
        contacts_enabled: true,
        triggers_enabled: true,
        opportunities_enabled: true,
        settings_enabled: true,
        tags_enabled: true,
        lead_value_enabled: true,
        dashboard_stats_enabled: true,
        bulk_requests_enabled: true,
        appointments_enabled: true,
        reviews_enabled: true,
        online_listings_enabled: true,
        phone_call_enabled: true,
        conversations_enabled: true,
        assigned_data_only: false,
        funnels_enabled: true,
        websites_enabled: true,
        marketing_enabled: true,
        adwords_reporting_enabled: true,
        facebook_ads_reporting_enabled: true,
        attributions_reporting_enabled: true,
        membership_enabled: true,
        bot_service: false,
        agent_reporting_enabled: true,
      },
    }
  },
  async mounted() {
    this.location = new Location(
      await this.$store.dispatch(
        'locations/getCurrentLocation',
        this.currentLocationId
      )
    )
  },
  computed: {
    errorIdsCount() {
      return this.emailIds.filter(id => !id.valid).length
    },
    existsIdsCount() {
      return this.emailIds.filter(id => id.exists).length
    },
    correctIdsCount() {
      return this.emailIds.filter(id => id.valid && !id.exists).length
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
  },
  methods: {
    validateEmailId(email: string) {
      const emailRegEx = /^(([^<>()[\]\.,;:\s@\"]+(\.[^<>()[\]\.,;:\s@\"]+)*)|(\".+\"))@(([^<>()[\]\.,;:\s@\"]+\.)+[^<>()[\]\.,;:\s@\"]{2,})$/
      return emailRegEx.test(email)
    },
    closeModal() {
      this.sentInviteSuccess = false
      this.$root.$emit('close-modal')
    },
    async addEmail(event) {
      event.preventDefault()
      const val = this.emailId.trim()
      this.emailId = ''
      const exists = this.emailIds.find(
        o => o.val.toLowerCase() === val.toLowerCase()
      )
      if (val.length > 0 && !exists) {
        let valid = this.validateEmailId(val)
        let exists = false
        if (await this.emailExists(val)) {
          exists = true
        }
        this.emailIds.push({ val, valid, exists })
        this.placeholder = ''
      }
    },
    removeTag(index) {
      this.emailIds.splice(index, 1)
    },
    removeLastEmail(event) {
      if (this.emailId.length === 0) {
        this.removeTag(this.emailIds.length - 1)
      }
    },
    removeErrorIds() {
      const emailIds = this.emailIds.filter(email => email.valid)
      this.emailIds = emailIds
    },
    removeExistsIds() {
      const emailIds = this.emailIds.filter(email => !email.exists)
      this.emailIds = emailIds
    },
    focusInput() {
      this.$refs.emailInput.focus()
      this.sentInviteSuccess = false
    },
    async sendInvite() {
      this.loader = true
      try {
        for (const user of this.emailIds) {
          if (user.valid && !user.exists) await this.createUser(user.val)
        }
        this.emailIds = []
        this.placeholder = '<EMAIL>'
        this.sentInviteSuccess = true
      } catch (e) {
        console.log(e)
      }
      this.loader = false
    },
    async createUser(email: string) {
      try {
        const locationId = this.currentLocationId
        const user = new User()
        user.role = User.ROLE_USER
        user.companyId = this.location.companyId
        user.type = User.TYPE_ACCOUNT
        user.permissions = this.userPermissions
        user.firstName = this.firstName
        user.lastName = this.lastName
        user.email = email.trim()
        user.phone = this.phone
        user.extension = ''
        user.isPasswordPending = true
        user.locations[locationId] = ''
        user.userEmailSignature[locationId] = {
          signature: this.tinymcehtml,
          autofill: this.autofillSignature,
          reply_signature: this.replySignature,
        }

        const defaultOpenHours = []

        for (var i = 0; i < 7; i++) {
          defaultOpenHours.push({
            days_of_the_week: [i],
            hours: [
              {
                open_hour: 0,
                open_minute: 0,
                close_hour: 0,
                close_minute: 0,
              },
            ],
          })
        }

        user.locationWiseMeetingLocation[locationId] =
          this.meetingLocation || ''
        user.locationWiseZoomAdded[locationId] = this.isZoomAdded || ''
        user.locationWiseTimezone[locationId] = this.timezone || ''
        user.locationWiseOpenHours[locationId] = defaultOpenHours
        user.userCalendar[locationId] = this.userCalendar
        user.lastLoginTime = moment();
        await user.setCreatedByAndOrLastUpdatedBy(true, UserSource.LaunchPad, UserSourceChannel.WEB_APP)
        const admin = await user.save() as User

        /** Set default password **/
        const currentPrivateUser = new PrivateUser(admin)
        currentPrivateUser.passwordHash = Math.random().toString(36).substr(2, 12);
        await currentPrivateUser.save()

        await this.sendMagicLink(admin.id)
      } catch (err) {
        console.log(err)
      }
    },
    async sendMagicLink(userId: string) {
      try {
        await this.$http.post('/launchpad/schedule', {
          locationId: this.currentLocationId,
          types: ['magic_link'],
          userId,
          senderId: this.user.id,
        })
      } catch (err) {
        console.log(err)
      }
    },
    async emailExists(email: string) {
      try {
        const auth: AuthUser = new AuthUser(
          await this.$root.$store.dispatch('auth/get')
        )
        const response = await this.$http.post('/exists', {
          email,
          companyId: auth.companyId,
        })
        return response.status === 200 ? false : true
      } catch (err) {
        return true
      }
    },
  },
})
</script>
<style scoped>
.form-group {
  margin-bottom: 0.5rem;
}

.tag-input {
  width: 100%;
  border: 1px solid rgb(0 0 0 / 40%);
  font-size: 0.9em;
  box-sizing: border-box;
  padding: 0 10px;
  border-radius: 5px;
  min-height: 100px;
  max-height: 300px;
  font-family: Roboto;
}

.tag-input:focus-within {
  box-shadow: 0 0 5px rgba(81, 203, 238, 1);
  margin: 5px 1px 3px 0px;
  border: 1px solid rgba(81, 203, 238, 1);
}

.tag-input__tag {
  height: 30px;
  float: left;
  margin-right: 10px;
  background-color: #eee;
  margin-top: 10px;
  line-height: 30px;
  border-radius: 5px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  font-size: 0.75rem;
  font-weight: bold;
}

.tag-input__tag__correct {
  background-color: rgba(24, 139, 246, 0.1);
  padding: 0 5px;
}

.tag-input__tag__error {
  background-color: transparent;
  border: 1px dotted #e93d3d;
  padding: 0 5px 0 0;
}

.tag-input__tag__error:hover {
  background: rgb(233 61 61 / 10%);
}

.tag-input__tag__error > div {
  width: 2rem;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: column;
  background: rgb(233 61 61 / 10%);
  height: 100%;
  margin-right: 5px;
}

.tag-wrong-count {
  display: flex;
  color: #111827;
}

.tag-wrong-count > p > a {
  color: rgb(13 107 175);
  cursor: pointer;
}

.tag-wrong-count > p > a:hover {
  text-decoration: underline;
}

.tag-input__tag > span {
  cursor: pointer;
  opacity: 0.75;
  font-size: 0.75rem;
  margin-left: 5px;
  font-weight: normal;
}

.tag-input__text {
  border: none;
  outline: none;
  font-size: 0.75rem;
  line-height: 30px;
  background: none;
  width: 50%;
}
.card-header {
  margin-top: 8px;
  color: #111827;
}

.card-body {
  padding: 10px 30px 0px 30px;
}

.tag-correct-count {
  color: #111827;
}

.tag-correct-count > i {
  color: green;
}

.card-footer {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  padding: 10px 30px 10px 30px;
}

.card-verify--success {
  color: #11b735;
  margin-right: 10px;
}
</style>
