<template>
  <Filters
    :conditions="conditions"
    :filterMaster="filterMaster"
    v-on:update:conditions="$emit('update:conditions', $event)"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import { User } from '@/models';
import { Condition } from '@/models/trigger';
const Filters = () => import( './Filters.vue');

export default Vue.extend({
  props: ['conditions'],
  components: { Filters },
  data() {
    return {
      filterMaster: [
        {
          placeHolder: 'Assigned User',
          title: 'Assigned User',
          value: 'task.assignedTo',
          valueType: 'text',
          type: 'select',
          options: [] as { [key: string]: any }[]
        }
      ]
    }
  },
  async created() {
    const currentLocationId = this.$router.currentRoute.params.location_id
    const assignUserOption = lodash.find(this.filterMaster, {
      value: 'task.assignedTo'
    })
    
    if (assignUserOption) {
        let userOptions = this.$store.state.users.users.map(user => {
            return {
                title: user.first_name + ' ' + user.last_name,
                value: user.id
            }
        });
        assignUserOption.options = userOptions
    }
  }
})
</script>

