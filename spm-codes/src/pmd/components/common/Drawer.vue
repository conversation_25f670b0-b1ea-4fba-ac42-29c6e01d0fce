<template>
  <div class="drawer">
    <div class="drawer-backdrop" @click="$emit('close')" v-if="showPanel"></div>
    <transition name="slide">
      <div v-if="showPanel" class="drawer-panel" :style="`max-width:${maxWidth}px;`">
        <div class="drawer-close-icon" v-if="showCloseIcon" @click="$emit('close')">
          <i class="fa fa-times"></i>
        </div>
        <slot></slot>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  props: {
    maxWidth: {
      type: String,
      default: '420'
    },
    showCloseIcon: {
      type: Boolean,
      default: false
    },
    showPanel: {
      type: Boolean,
      default: false
    }
  },
};
</script>

<style scoped>
.drawer-backdrop {
	background-color: rgba(11,11,27,0.5);
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  right: 0;
  z-index: 999;
  /* cursor: pointer; */
}

.drawer-panel {
  /* overflow-y: auto; */
  background-color: #ffffff;
  position: fixed;
  right: 0;
  top: 0;
  width: 100%;
  max-width: 420px;
  height: 100vh;
  z-index: 1000;
  /* border-top-left-radius: 12px; */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.33);
}

.drawer-close-icon {
  position: fixed;
  top: 20px;
  right: 20px;
  cursor: pointer;
  font-size: 20px;
  line-height: 20px;
  color: #aaa;
  z-index: 5;
}
.drawer-close-icon:hover{
  color: #777;
}

/*
 * The following styles are auto-applied to elements with
 * transition="modal" when their visibility is toggled
 * by Vue.js.
 *
 * You can easily play with the modal transition by editing
 * these styles.
 */

.slide-enter-active,
.slide-leave-active
{
  transition: transform 0.3s ease;
}

.slide-enter,
.slide-leave-to {
  transform: translateX(100%);
  transition: all .2s ease-in 0s
}

</style>