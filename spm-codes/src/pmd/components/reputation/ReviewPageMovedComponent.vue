<template>
  <div id="page-moved">
    <div class="notice">
      <div class="header">This page has been moved</div>
      <div class="body">
        <p>The following pages have been moved</p>
        <ul>
          <li>SMS</li>
          <li>Email</li>
          <li>Review Widget</li>
        </ul>
        <p>
          Now you can find these pages under

          <router-link
            :to="{
              name: 'reputation_settings',
            }"
          >
            <a href="javascript:void(0);"> Settings > Reputation</a>
          </router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// @ts-nocheck
</script>
<style lang="scss" scoped>
#page-moved {
  display: flex;
  justify-content: center;
  padding: 10px;
  .notice {
      background: #FFF;
    width: 50%;
    box-shadow: 0 0 4px 1px #8c8c8c24;
    border-radius: 3px;
    .header {
      padding: 0.8rem 1rem;
      font-size: 1.2rem;
      border-bottom: 1px solid #d6d6d6;
    }
    .body {
      padding: 1rem;
    }
  }
}
</style>
