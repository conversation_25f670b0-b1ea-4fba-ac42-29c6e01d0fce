<template>
  <div class="marketplace-details--infos">
    <div class="row">
      <div class="col-lg-7 col-xl-8">
        <div class="card">
          <div class="card-body">
            <div class="marketplace-details--about mb-3">
              <h3 class="heading6">About me</h3>
              <p class="mt-2">{{ partner.description }}</p>
            </div>
            <div class="marketplace-details--moreinfo mt-3">
              <h3 class="heading6">Years in business</h3>
              <p class="mt-2 mb-2">{{ partner.experience }}</p>
              <div class="d-flex justify-content-between mt-2 mb-2">
                <div>
                  <h3 class="heading6">Industries served</h3>
                  <ul class="marketplace-details--list list-disc mt-2 space-y-1.5">
                    <li v-for="industry in partner.industries" :key="industry">
                      {{ industry }}
                    </li>
                  </ul>
                </div>
                <div class="mr-5">
                  <h3 class="heading6">Services provided</h3>
                  <ul class="marketplace-details--list list-disc mt-2 space-y-1.5">
                    <li v-for="service in partner.services" :key="service">
                      {{ service }}
                    </li>
                  </ul>
                </div>
              </div>
              <div class="row mt-4">
                <div class="col-sm-6">
                  <h3 class="heading6">Languages</h3>
                  <p class="mt-2">
                    <span class="mr-2" v-for="language in partner.languages" :key="language">{{
                      language.replace(/,/g, ' ')
                    }}</span>
                  </p>
                </div>
                <div class="col-sm-4 offset-sm-2" style="position:">
                  <h3 class="heading6">Social sites</h3>
                  <div v-if="partner.socialMedia" class="social-links d-flex mt-2">
                    <div class="mr-2" v-show="partner.socialMedia[0].website">
                      <a
                        :href="partner.socialMedia[0].website"
                        target="_blank"
                        ><i class="fas fa-link fa-lg"></i
                      ></a>
                    </div>
                    <div class="mr-2" v-show="partner.socialMedia[0].facebook">
                      <a
                        :href="partner.socialMedia[0].facebook"
                        target="_blank"
                        ><i class="fab fa-facebook-f fa-lg"></i
                      ></a>
                    </div>
                    <div class="mr-2" v-show="partner.socialMedia[0].twitter">
                      <a
                        :href="partner.socialMedia[0].twitter"
                        target="_blank"
                        ><i class="fab fa-twitter fa-lg"></i
                      ></a>
                    </div>
                    <div class="mr-2" v-show="partner.socialMedia[0].linkedin">
                      <a
                        :href="partner.socialMedia && partner.socialMedia[0].linkedin"
                        target="_blank"
                        ><i class="fab fa-linkedin-in fa-lg"></i
                      ></a>
                    </div>
                    <div class="mr-2" v-show="partner.socialMedia[0].youtube">
                      <a
                        :href="partner.socialMedia[0].youtube"
                        target="_blank"
                        ><i class="fab fa-youtube fa-lg"></i
                      ></a>
                    </div>
                    <div class="mr-2" v-show="partner.socialMedia[0].instagram">
                      <a
                        :href="partner.socialMedia[0].instagram"
                        target="_blank"
                        ><i class="fab fa-instagram fa-lg"></i
                      ></a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-5 col-xl-4">
        <div class="card">
          <div class="card-body">
            <div class="marketplace-details--message">
              <h3 class="heading6">Send a message</h3>
              <form @submit.prevent="sendMail" class="mt-2">
                <div class="form-group">
                  <UITextAreaGroup
                    rows="5"
                    label="Message"
                    name="message"
                    v-model="message"
                    v-validate="'required'"
                    placeholder="Send a message to introduce yourself to this advisor. Include details about yourself and the services you’re looking for."
                    :error="errors.has('message')"
                    :errorMsg="'Please provide a valid message.'"
                  ></UITextAreaGroup>
                </div>
                <div class="form-group">
                  <UITextInputGroup
                    type="text"
                    label="Your name"
                    placeholder="Full Name"
                    v-model="name"
                    v-validate="'required'"
                    name="name"
                    :error="errors.has('name')"
                    :errorMsg="'Please provide a valid name.'"
                  />
                </div>
                <div class="form-group">
                  <UITextInputGroup
                    type="email"
                    label="Your email"
                    placeholder="Your email"
                    v-model="email"
                    v-validate="'required'"
                    name="email"
                    :error="errors.has('email')"
                    :errorMsg="'Please provide a valid email.'"
                  />
                </div>
                <div class="form-group">
                  <UITextInputGroup
                    type="text"
                    label="Phone Number"
                    placeholder="Phone Number with Country Code"
                    v-model="phone"
                    v-validate="'required'"
                    name="phone"
                    :error="errors.has('phone')"
                    :errorMsg="'Please provide a valid Phone Number.'"
                  />
                </div>
                <div class="form-group">
                  <UIButton
                    type="submit"
                    :loading="sending"
                    :disabled="sending"
                  >
                    Send Message
                  </UIButton>
                </div>
              </form>
              <div
                v-if="success"
                style="color: green"
                class="alert alert-success"
                role="alert"
              >
                Email Sent Successfully
              </div>
              <div
                v-if="fail"
                style="color: red"
                class="alert alert-danger"
                role="alert"
              >
                Unable to send Email
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import axios from 'axios'
import defaults from '@/config'
import { Contact } from '@/models'
import MoonLoader from '../MoonLoader'

export default Vue.extend({
  components: { MoonLoader },
  props: {
    partner: {
      type: Object,
      default: function() {
        return {}
      }
    }
  },
  data() {
    return {
      name: '',
      email: '',
      message: '',
      phone: '',
      success: false,
      fail: false,
      sending: false
    }
  },
  methods: {
    async sendMail() {
      try {
        await this.$validator.validateAll()
        if (this.errors.any()) {
          return
        }

        this.sending = true;
        let response = await axios.post(
          `${defaults.baseUrl}/email/partnerProgram`,
          {
            name: this.name,
            email: this.email,
            message: this.message,
            phone: this.phone,
            sendTo: this.partner.email
          }
        )
        this.addToContact()
        this.sending = false;
        this.success = true;
        setTimeout(() => {
          this.success = false
          this.reset()
        }, 3000)
      } catch (err) {
        this.sending = false;
        console.log(err)
        this.fail = true
        setTimeout(() => {
          this.fail = false
        }, 3000)
      }
    },
    reset() {
      ;(this.name = ''),
        (this.email = ''),
        (this.message = ''),
        (this.phone = '')
        this.errors.items = [];
    },
    async addToContact() {
      let contact = await axios.post(`${defaults.baseUrl}/partners/addContact`, {
          locationId: this.partner.locationId,
          firstName: this.name.split(' ')[0],
            lastName: this.name.split(' ')[1] || '',
            email: this.email,
            phone: this.phone
      })
    }
  },
  computed: {
    isComplete() {
      return this.name && this.email && this.message && this.phone
    }
  }
})
</script>

<style scoped>
.marketplace-details--list{
  display: flex;
  flex-direction: column;
}
</style>
