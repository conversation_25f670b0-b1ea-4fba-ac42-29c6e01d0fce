import { SmartListVM, ReadOnlySmartListVM } from './smart_list_vm';
import { ImportRequest, ImportType, ImportStatus, Contact} from '../../../../models';
import * as json2csv from 'json2csv'
import { SmartListColumn } from './smart_list_column';
import { SmartListColumnsVM } from './smart_list_columns';
import { Utils } from '../../../../util/utils';
import { IOpSpecs, ISprintSchedule } from '../server/bulkreq_interfaces';
import config from '../../../../config';
const moment = require('moment-timezone');
import FileSaver from 'file-saver'
import { remove } from 'lodash';
import { ScheduledBulkAction } from '../server/bulkreq_api';
//import download from 'downloadjs'

export class StatusCheck {
  public total: number = 0;
  public updated: number  = 0;
}

export interface IBulkAction {
  name: string;
  status: StatusCheck;
  totalContacts: Number;
  action(userId: string, params ?: { opSpecs?: IOpSpecs, sprintSchedule?: ISprintSchedule | null}, taskFinishCallback?: (status: string) => {}): Promise<Boolean>;
}

// tslint:disable-next-line: max-classes-per-file
export abstract class BulkAction implements IBulkAction {
  public name: string;
  public vm: SmartListVM;
  public type: ImportType;
  public displayContacts: Contact[];
  public totalContacts: Number;
  public get status(): StatusCheck {
    return this._status;
  }
  protected taskFinishCallback: (status: string) => {};
  protected taskRunning: boolean = false;
  private unsubscribeImportRequest: any;
  private _status = new StatusCheck();


  constructor(vm: SmartListVM) {
      this.vm = vm;
  }

  public async action(userId: string, params ?:{}, taskFinishCallback ?: (status: string) => {}): Promise<Boolean> {

    const total =  this.vm.areAllPagesSelected === true ? this.vm.totalContacts : this.vm.selectedContactIds.length;
    if (total <= 0) {
      throw new Error('No contact selected');
    }

    this.status.total = total;

    if (!this.isValid(params)) {
      return false;
    }

    this.taskRunning = true;
    this.taskFinishCallback = taskFinishCallback;
    const request = new ImportRequest();
    request.userId = userId;
    request.locationId = this.vm.locationId;
    request.status = ImportStatus.PENDING;
    request.type = this.type; // ImportType.BULK_UPDATE; //BULK_DELETE
    await request.save();
    try {

      this.unsubscribeImportRequest = await ImportRequest.getByIdRealTime(request.id)
      .onSnapshot((snapshot) => this.processServerUpdate(snapshot.data()));

      if (this.vm.areAllPagesSelected === true) {
        await this.serverProcess(request.id, { ...params, smartList: (await this.vm.getServerQuery()), locationId: this.vm?.locationId })
      } else if (this.vm.selectedContactIds && this.vm.selectedContactIds.length > 0) {
        await this.serverProcess(request.id, { ...params, contactIds: this.vm.selectedContactIds, locationId: this.vm?.locationId })
      } else this.markAsComplete('Nothing to process');

    } catch (err) {
      this.markAsComplete(err.message);
    }
    // finally { // let the exception buble up to calling code.
    //     // this.unsubscribeImportRequest();
    //     // this.unsubscribeImportRequest = null;
    // }
    return true;
  }

  protected abstract isValid(params?: {}): boolean;
  protected abstract serverProcess(requestId: string, params?: {}): Promise<Boolean>;
  protected abstract getActionType(): string;

  protected markAsComplete(status: string) {
    if (this.unsubscribeImportRequest) this.unsubscribeImportRequest();
    this.taskRunning = false;
    if (this.taskFinishCallback) {
      this.taskFinishCallback(status);
      this.taskFinishCallback = null;
    }
  }

  protected processServerUpdate(serverUpdate: any) {
    // console.log(serverUpdate);
    if (!Utils.isEmptyStr(serverUpdate.error)){
      this.markAsComplete(serverUpdate.error);
    } if (serverUpdate.status === ImportStatus.SUCCESS){
      this.markAsComplete('success');
    } else if (serverUpdate.status === ImportStatus.REVERT){
      this.markAsComplete('reverting back');
    }
  }
}

// tslint:disable-next-line: max-classes-per-file
export class TagAddition extends BulkAction {

  constructor(vm: SmartListVM) {
    super(vm);
    this.type = ImportType.BULK_UPDATE;
  }

  protected isValid(params?:{tags?: [] | null}) {
    if (!params && !params.tags) throw new Error('Provide tags to add or remove');
    return true;
  }

  // tslint:disable-next-line: ban-types
  protected serverProcess(requestId: string, params?: {tags?: [] | null, smartList?: any | null, contactIds?: [] | null }): Promise<Boolean> {
    return Contact.smartListUpdateTags(requestId, params.tags, this.getActionType() , params.smartList, params.contactIds);
  }

  protected processServerUpdate(serverUpdate: any) {
    super.processServerUpdate(serverUpdate);
    console.log('Tag Add Update');
    //console.log(serverUpdate);
    this.status.updated = serverUpdate.updated || 0;
  }

  protected getActionType(): 'add' | 'remove' {
    return 'add';
  }
}

// tslint:disable-next-line: max-classes-per-file
export class TagDeletion extends TagAddition {

  protected processServerUpdate(serverUpdate: any) {
    super.processServerUpdate(serverUpdate);
    console.log('Tag Delete Update');
    //console.log(serverUpdate);
    this.status.updated = serverUpdate.updated || 0;
  }

  protected getActionType(): 'add' | 'remove' {
    return 'remove';
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ContactDeletion extends BulkAction {

  constructor(vm: SmartListVM) {
    super(vm);
    this.type = ImportType.BULK_DELETE
  }

  protected isValid(params?: {}) {
    return true;
  }

  // tslint:disable-next-line: ban-types
  protected serverProcess(requestId: string, params?: { smartList?: any | null, contactIds?: [] | null, locationId?: string }): Promise<Boolean> {
    return Contact.smartListDeleteContacts(requestId, params.smartList, params.contactIds, params.locationId);
  }

  protected processServerUpdate(serverUpdate: any) {
    super.processServerUpdate(serverUpdate);
    console.log('Contact Deletion Update');
    //console.log(serverUpdate);
    this.status.updated = serverUpdate.deleted || 0;
  }

  protected getActionType(): string {
    return 'delete-contact';
  }
}

export class OpportunityChange extends BulkAction {

  constructor(vm: SmartListVM) {
    super(vm);
    this.type = ImportType.BULK_DELETE
  }

  protected isValid(params?: {}) {
    return true;
  }

  // tslint:disable-next-line: ban-types
  protected serverProcess(requestId: string, params?: { opportunityConfig: {}, smartList?: any | null, contactIds?: [] | null }): Promise<Boolean> {

    //return Contact.opportunityUpdates(requestId, params.smartList, params.contactIds, params.opportunityConfig);
  }

  protected processServerUpdate(serverUpdate: any) {
  }

  protected getActionType(): string {
    return 'change-opportunity';
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ContactExports implements IBulkAction  {
  totalContacts: Number;

  public name: string;
  public displayContacts: Contact[];
  public get status(): StatusCheck {
    return this._status;
  }
  private vm: SmartListVM;
  private _status = new StatusCheck();

  constructor(vm: SmartListVM) {
    this.vm = vm;
    this.name = 'Export Contacts';
  }

  public async action(userId: string,  params?: {}, taskFinishCallback ?: (status: string) => {}): Promise<Boolean> {
    try {
      const total =  this.vm.areAllPagesSelected === true ? this.vm.totalContacts : this.vm.selectedContactIds.length;
      if (total <= 0) {
        throw new Error('No contact selected');
      }
      this.status.total =  total;
      const count = await this.exportToCSV();
      if(taskFinishCallback) taskFinishCallback('success');
      return true;
    } catch (error) {
      if(taskFinishCallback) taskFinishCallback(`${error}`);
      console.log('Error exporting contacts');
      console.log(error);
    }
    return false;
  }

  public async exportToCSV() {
    let selectedCols = this.vm && this.vm.cols && this.vm.cols.selected && this.vm.cols.selected.length ? [...this.vm.cols.selected] : [];
    const removeKeys = [SmartListColumnsVM.FirstNameColumn.key,SmartListColumnsVM.LastNameColumn.key,SmartListColumnsVM.CompanyNameColumn.key];
    selectedCols = selectedCols.filter(a => !removeKeys.includes(a.key));
    let cols = [new SmartListColumn(null,SmartListColumnsVM.ContactIdColumn, false),
                new SmartListColumn(null,SmartListColumnsVM.FirstNameColumn, false),
                new SmartListColumn(null,SmartListColumnsVM.LastNameColumn, false),
                new SmartListColumn(null,SmartListColumnsVM.CompanyNameColumn, false)];
    cols.push(...selectedCols);//.map((a) => { a.key, isCustomField: a.isCustomField});
    const fullNameColKey = 'name';
    cols = cols.filter(a=> a.key !== fullNameColKey);
    const headers = cols.map((a) => a.exportName);
    const values = [];
    if (this.vm.areAllPagesSelected === true) {
      let totalPages = 1;
      this.status.total = this.vm.totalContacts;
      const vm2 = await ReadOnlySmartListVM.fromVM(this.vm);
      vm2.pageLimit = 500;
      for (let i = 0 ; i < totalPages; i++) {
        //const rs = await vm2.getContactsForPage(i);
        const rs = await vm2.getNextContacts();
        totalPages = rs.totalPages;
        values.push(...this.buildContactsCSV(cols, rs.pageItems));
        this.status.updated = values.length;
      }
    } else if (this.vm.selectedContactIds && this.vm.selectedContactIds.length > 0) {
      this.status.total = this.vm.selectedContactIds.length;
      values.push(...this.buildContactsCSV(cols, await this.selectedContacts(this.vm)));
    }
    const json2csvParser = new json2csv.Parser({header: false}); // turn header: true for debugging
    const csvReadyHeaders  = headers.map(a=> `"${Utils.safeReplaceDoubleQuotes(a|| '')}"`);
    const csv = `${csvReadyHeaders.join(',')}\n${await this.getCSVLines(values)}`;
    const blob = new Blob([csv], {type: "text/csv;"});
    await FileSaver.saveAs(blob,`filesaver-${this.vm.smartList.listName}${moment().format('_MMM DD YY h_mm a')}.csv`,{autoBom: true });
    await ScheduledBulkAction.justAuditTrail('bulk-export', { total: values.length });
    return values?.length;
    // await download(csv, `downloadjs-${this.vm.smartList.listName}${moment().format('_MMM DD YY h_mm a')}.csv`, 'text/csv')
  }

  private async selectedContacts(vm: SmartListVM): Promise<Contact[]> {
    if (!vm || !vm.selectedContactIds || vm.selectedContactIds.length <= 0)  return [];
    const selectedIds = [...vm.selectedContactIds];
    const returnItems = [];
    const currentContacts = [...vm.contacts];
    for (const x of selectedIds) {
      const idx = currentContacts.findIndex((a) => a.id === x);
      if (idx > -1) returnItems.push(currentContacts[idx]);
      else {
        const item = await Contact.getById(x);
        if (item) returnItems.push(item);
      }
      this.status.updated +=1;
    };
    return returnItems;
  }

  private buildContactsCSV(selectedCols: SmartListColumn[], contacts: Contact[]) : any[] {
    if (!contacts || !contacts.length) return [];
    if (!selectedCols || !selectedCols.length) return [];
    const values = [];
    contacts.forEach((contact) => {
      const y = {};
      selectedCols.forEach((colItem) =>  {
          const col = colItem.key;
          if (typeof contact[col] === 'number') y[col] = `${contact[col]}`
          else y[col] = contact[col] || ''; // Very important to stuff a blank value before processing anything else to avoid bug. It should have some value in situation where no condition check below stuffs a value.
          // Use export formatter for specific column issues

          if(colItem.isCustomField){
            y[col] = contact.customFieldsFormatter.showSmartListValue(colItem.key);
          }

          if (colItem.exportFormatter){
            y[col] = colItem.exportFormatter(contact[col]);
          }

          if (y[col] instanceof Array) {
            const len = y[col].length || 0;
            y[col] =  len > 0 ?  y[col].join(', ') : '';
          }
      });
      values.push(y);
      this.status.updated += 1;
    });
    return values;
  }

  private async getCSVLines(values: { [key: string]: any }[]) {
    // i235wUNCN9ZWWUbBgvCS egypt location test
    if (!values || !values.length) return '';

    let lines = []
    const keys = Object.keys(values[0])
    await Promise.all(values.map(async a => {
      if (!a) return
      let line = ''
      keys.forEach(x => {
        try {
          if (a[x] && a[x].utc) {
            line += `"${a[x].format()}",`
          } else {
            // a[x] can be a non string like boolean so it is necessary to convert it to string. Otherwise it create bugs.
            line += `"${Utils.safeReplaceDoubleQuotes(a[x] || '')}",` // Make sure that .replace(...) is only operated upto a string and not a non-string like boolean. DND has boolean.
          }
        } catch(err){
          console.log(`Error processing export cell. Cannot process  ${x}: ${a[x]} in item ${JSON.stringify(a)}`)
          throw err
        }
      })
      lines.push(line)
    }))
    return lines.join('\n')
  }
}
