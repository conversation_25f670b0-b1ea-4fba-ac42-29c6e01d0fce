<template>
      <div class="hl_smartlists--wrap">
        <aside  class="hl_smartlists--filters" id="hl_smartlists-filters" v-show="showFilters">
          <div class="card" >
            <div class="body">
                <SmartListFiltersLoading></SmartListFiltersLoading>
            </div>
          </div>
        </aside>
        <section class="hl_smartlists--main" :class="{'--open': showFilters}" id="hl_smartlists-main">
          <div class="card-like" >
            <div class="row" style="background:#E8F3FE;min-height:40px !important" ></div>
          </div>
          <div class="card no-border-radius">
            <div class="card-body --no-padding">
                <table ref="disp-tab" class="table table-sort">
                  <tbody>
                    <tr v-for="index in 10" :key="index" class="loading">
                      <td class="td-check">
                        <span></span>
                      </td>
                      <td class="td-loading-short" v-for="index2 in 5" :key="index2">
                        <!-- v-model="vm.cols.selected" v-for="c in vm.cols.selected" -->
                        <span></span>
                      </td>
                    </tr>
                  </tbody>
                </table>
            </div>
          </div>
        </section>
      </div>
</template>

<script>

import Vue from 'vue';
import { SmartList } from '@/models'
import SmartListFiltersLoading from './SmartListFiltersLoading.vue'

export default Vue.extend({
    name:'SmartListLoading',
    props:['showFilters'],
    components: {
      SmartListFiltersLoading
    },
    data(){
        return {
          changingList : true,
        }
    }
  });
</script>


<style scoped>
  .optionsbar {
       background:#E8F3FE;
      /* margin-bottom: 10px; */
      height: 40px;
      padding: 10px;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      /* border-bottom: 2px solid #dee5e8; */
  }

</style>
