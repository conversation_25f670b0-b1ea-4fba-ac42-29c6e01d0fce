

import Vue from 'vue'
import { <PERSON><PERSON>le, ActionContext } from 'vuex'
import { RootState, LinkedCalendarState } from './state_models'
import { LinkedCalendar, UserCalendar} from '../models';

export const linkedCalendars: Module<LinkedCalendarState, RootState> = {
  namespaced: true,
  state: { linkedCalendars: [], userId: undefined},
  getters: {
    getById: (state: LinkedCalendarState) => {
      return (id: string) => state.linkedCalendars.find(x => x.id === id)
    },
    linkedCalendarsWithErrors(state: LinkedCalendarState) {
      return state.linkedCalendars.filter(c => c.google && c.google.error)
    },
    errorMsg: (state: LinkedCalendarState, getters) => {
      if (getters.linkedCalendarsWithErrors.length === 0) return null;

      let msg = 'There is a problem with the integration on these calendars "'
      getters.linkedCalendarsWithErrors.forEach(cal => {
        msg += cal.google.name + ', '
      })
      msg = lodash.trimEnd(msg, ', ')
      msg += '"'
      return msg
    }
  },
  mutations: {
    set: (
      state: LinkedCalendarState,
      payload: { linkedCalendars: any[]; userId: string }
    ) => {
      state.linkedCalendars = payload.linkedCalendars
      if (payload.userId) {
        state.userId = payload.userId
      }
    },
    clearAll(state: LinkedCalendarState) {
      state.linkedCalendars = []
      state.userId = undefined
    }
  },
  actions: {
    async syncAll(
      context: ActionContext<LinkedCalendarState, RootState>,
      {userId , locationId}: any
    ) {
      if (!userId) {
        context.commit('clearAll')
        return
      }



      if (context.state.userId === userId && context.state.locationId === locationId) {
        return context.state.linkedCalendars
      }


     let linkedCalendars = await LinkedCalendar.getByUserId(userId)
     let userCalendars = await UserCalendar.getByUserAndLocation(userId , locationId)

     let linkedCalendarsOfThisLocation = [];
     userCalendars.forEach((userCalendar) => {
      linkedCalendars.forEach((linkedCalendar) => {

        if(userCalendar.linkedCalendarId === linkedCalendar.id ) {
          linkedCalendarsOfThisLocation.push(linkedCalendar)
        }

      })
     })

     context.commit('set' , {
       userId: userId ,
       linkedCalendars : linkedCalendarsOfThisLocation,
       locationId: locationId
     })
    },
    logout: {
      root: true,
      handler: (context: ActionContext<LinkedCalendarState, RootState>) => {
        context.commit('clearAll')
      }
    },
    clearAll: {
      handler: (context: ActionContext<LinkedCalendarState, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
