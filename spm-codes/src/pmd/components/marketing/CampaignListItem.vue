<template>
  <div
    class="w-100 py-2 d-flex align-items-center"
    style="border-bottom: 1px solid rgba(0,0,0,0.1);"
    :class="{ 'pl-4 folder-child': isFolderCampaign }"
  >
    <div class="w-25" style="padding-left: 40px; min-width: 25%;">
      <!-- <i class="fa fa-align-justify mr-3 color-blue handle"></i> -->
      <router-link
        style="font-weight: 500;"
        @click.native="sendFolders"
        :to="{
          name: 'campaign_edit',
          params: { location_id: currentLocationId, campaign_id: campaign.id }
        }"
        tag="a"
      >
        <!--<span class="active">A</span>-->
        {{ userFriendlyName }}
      </router-link>
    </div>
    <div class="w-75 d-flex justify-content-around">
      <span class="" style="min-width: 34px;">
        <router-link
          :to="{
            name: 'campaign_accounts_list',
            params: { location_id: currentLocationId, campaign_id: campaign.id }
          }"
          tag="a"
          >{{ total }}</router-link
        >
      </span>
      <span class="text-center" style="min-width: 40.5px;">{{ active }}</span>
      <span class="text-center" style="min-width: 70px;">{{ completed }}</span>
      <span class="text-center" style="min-width: 48px;">
        <span>{{ replied }}</span>
      </span>
      <span class="text-center" style="min-width: 50px;">
        <span>{{ conversion }}%</span>
      </span>
      <span class="text-center" style="min-width: 75px;">{{
        capitalize(campaign.status)
      }}</span>
      <span class="text-center" style="min-width: 162px;">{{
        campaign.id
      }}</span>
    </div>
    <div
      class="w-25 d-flex align-items-center"
      style="min-width: 25%; justify-content: center; padding-left: 25px;"
    >
      <!-- <select class="selectpicker more-select">
				<option>Option 1</option>
				<option>Option 2</option>
				<option>Option 3</option>
			</select>-->

      <moon-loader :loading="deleting" color="#e93d3d" size="15px" />
      <div
        class="dropdown bootstrap-select more-select show"
        v-if="!deleting && allowEdit"
      >
        <button
          type="button"
          class="btn dropdown-toggle bs-placeholder btn-light"
          data-toggle="dropdown"
        ></button>
        <div class="dropdown-menu">
          <a class="dropdown-item" @click.prevent="deleteCampaign">Delete</a>
          <a
            class="dropdown-item"
            v-if="campaign.folder_id"
            @click="removeFromFolder"
            >Remove From folder</a
          >
          <span class="dropdown-submenu" v-if="availableFolders.length > 0">
            <a class="dropdown-item" tabindex="-1" href="javascript:void(0);"
              >Move to folder <i class="fas fa-angle-right hint-icon"></i
            ></a>
            <div class="dropdown-menu campaignItem">
              <span
                class="dropdown-item"
                style="cursor: pointer;"
                @click="addToFolder(folder.id)"
                v-for="(folder, index) in availableFolders"
                :key="index"
              >
                <a tabindex="-1" href="javascript:void(0);">
                  {{ folder.name }}</a
                >
              </span>
            </div>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import { AxiosResponse } from 'axios'
import { UserState } from '../../../store/state_models'
import { Campaign, CampaignStatus, User } from '../../../models/'
import config from '../../../config'
import { Utils } from '@/util/utils'
import { EventBus } from '@/models/event-bus'
import firebase from 'firebase/app'

export default Vue.extend({
  props: ['campaign', 'folders'],
  data() {
    return {
      currentLocationId: '',
      campaignStatuses: [] as CampaignStatus[],
      aggregationResults: {} as { [key: string]: any },
      deleting: false
    }
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.currentLocationId = id
    }
  },
  computed: {
    isFolderCampaign() {
      return (
        this.campaign.folder_id &&
        lodash.findIndex(this.folders, { id: this.campaign.folder_id }) !== -1
      )
    },
    availableFolders() {
      if (!this.folders) return []
      return this.folders.filter(
        (folder: any) => folder.id !== this.campaign.folder_id
      )
    },
    stopOnReply() {
      return this.campaign.stop_on_reply === undefined
        ? true
        : this.campaign.stop_on_reply
    },
    total(): any {
      if (lodash.isEmpty(this.aggregationResults)) return 0
      return this.aggregationResults.hits.total.value
    },
    active(): any {
      if (lodash.isEmpty(this.aggregationResults)) return 0
      const running = lodash.find(
        this.aggregationResults.aggregations.attribCount.buckets,
        { key: 'running' }
      )
      return running ? running.doc_count : 0
    },
    completed(): any {
      if (lodash.isEmpty(this.aggregationResults)) return 0
      // const finished = lodash.find(this.aggregationResults.aggregations.attribCount.buckets, {key: 'finished'});
      // return finished ? finished.doc_count : 0;
      return this.total - this.active
    },
    replied(): any {
      if (lodash.isEmpty(this.aggregationResults)) return 0
      const repliedStatus = lodash.find(
        this.aggregationResults.aggregations.attribCount.buckets,
        { key: 'replied' }
      ) // Old logic, preserved for old campaigns.
      const repliedBoolean = lodash.find(
        this.aggregationResults.aggregations.repliedCount.buckets,
        { key_as_string: 'true' }
      )
      if (repliedBoolean && repliedStatus) {
        return repliedBoolean.doc_count + repliedStatus.doc_count
      } else if (repliedBoolean) {
        return repliedBoolean.doc_count
      } else if (repliedStatus) {
        return repliedStatus.doc_count
      } else {
        return 0
      }
    },
    conversion(): any {
      if (lodash.isEmpty(this.aggregationResults)) return 0
      return ((this.replied / (this.total || 1)) * 100).toFixed(2)
    },
    allowEdit(): boolean {
      return this.user && this.user.permissions.campaigns_read_only !== true
    },
    userFriendlyName() {
      return !this.campaign || Utils.isEmptyStr(this.campaign.name)
        ? '<please rename>'
        : this.campaign.name
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    })
  },
  methods: {
    sendFolders() {
      EventBus.$emit('folders_list', false)
    },
    async removeFromFolder() {
      await new Campaign(this.campaign).ref.update({
        folder_id: firebase.firestore.FieldValue.delete()
      })
    },
    async addToFolder(folderId: string) {
      await new Campaign(this.campaign).ref.update({
        folder_id: folderId
      })
    },
    capitalize(value: string) {
      return lodash.capitalize(value)
    },
    async deleteCampaign() {
      if (confirm('Are you sure you want to delete this campaign?')) {
        try {
          this.deleting = true
          await this.$http.delete(`/campaign/${this.campaign.id}`)
        } catch (err) {
          this.deleting = false
          console.log(err)
        }
      }
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.$http
      .get(
        `${config.baseUrl}/aggregate/campaign_status?location_id=${this.currentLocationId}&campaign_id=${this.campaign.id}`
      )
      .then((response: AxiosResponse) => {
        this.aggregationResults = response.data
      })
      .catch((err: any) => {})
  }
})
</script>
<style lang="css" scope>
.folder-child {
  background-color: #f4f4f4;
}
.dropdown-submenu {
  position: relative;
}

.dropdown-submenu > .dropdown-menu {
  top: 0px;
  right: 2px;
  left: auto !important;
}

.dropdown-submenu:hover > .dropdown-menu {
  display: block;
}

.dropdown-item > a {
  color: #607179;
}

#dropdownMenu1::after {
  content: none;
}

.color-blue {
  color: #188bf6;
}

.campaignItem {
  max-height: 300px;
  max-width: 250px;
  overflow-y: scroll;
  bottom: -40px !important;
  top: auto !important;
}
.campaignItem > span {
  white-space: normal;
}
</style>
