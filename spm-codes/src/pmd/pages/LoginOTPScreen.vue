<template>
  <div class="card">
    <div class="card-body">
      <div class="login-card-heading">
        <h2 class="heading2">Verify Security Code</h2>
      </div>
      <div>
        <div class="flex" v-if="emailMask">
          <UIRadioButton
            label="Send code to email"
            id="otp-for-email"
            name="otp"
            :selectedValue="otpChannel"
            @input="changeotpChannel('email')"
            value="email"
          />
          <p class="ml-2">{{ emailMask }}</p>
        </div>
        <div class="flex mt-3" v-if="phoneMask">
          <UIRadioButton
            label="Send code to phone"
            id="otp-for-phone"
            name="otp"
            :selectedValue="otpChannel"
            @input="changeotpChannel('phone')"
            value="phone"
          />
          <p class="ml-2">{{ phoneMask }}</p>
        </div>
      </div>
      <div class="form-group mt-4" v-if="firstTime == false">
        <div class="flex flex-row justify-center text-center px-2">
          <OtpInput
            ref="otpInput1"
            input-classes="otp-input"
            separator="-"
            :num-inputs="6"
            :should-auto-focus="true"
            input-type="number"
            @on-change="handleOnChange"
            @on-complete="handleOnComplete"
          />
        </div>
      </div>
      <!-- <label class="text-red-500 my-2 w-full text-center" v-if="!isValidOtp"
        >Invalid OTP. Please try again</label
      > -->
      <div class="form-group button-holder">
        <div class="global-error">
          <p v-show="response" stye="text-align:center">
            <span class="error">{{ response }}</span>
          </p>
        </div>
        <div style="display: inline-block; position: relative; width: 100%">
          <UIButton
            use="tertiary"
            @click.prevent="submit()"
            :loading="loading"
            :disabled="!firstTime && !isValidOtp"
            class="justify-center w-full"
          >
            {{ firstTime ? 'Send Security Code' : 'Confirm Code' }}</UIButton
          >
        </div>
      </div>

      <div
        class=" font-semibold cursor-pointer w-full text-center"
        :class="lastOtpSent === allowOTPAfter ? 'text-curious-blue-500 hover:text-curious-blue-800' : 'text-gray-300'"
        v-if="!firstTime"
        @click="requestForOtp"
        v-text="'If you did not receive the code click here to resend'"
      >
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { AxiosResponse } from 'axios'
import NotificationHelper from '../../util/notification_helper'
import { getHostAndDomain } from '../../util/helper'
import { Company, User } from '@/models'
import firebase from 'firebase/app'
import ContactSupport from '@/pmd/components/common/ContactSupport.vue'
import MultipleUsersLogin from '../components/MultipleUsersLogin.vue'
import restAgent from '@/restAgent'
const MoonLoader = () => import('@/pmd/components/MoonLoader.vue')
import OtpInput from './OtpInput.vue';
import moment from 'moment'

export default Vue.extend({
  metaInfo: {
    title: 'LoginOTP',
  },
  components: {
    MoonLoader,
    ContactSupport,
    MultipleUsersLogin,
    OtpInput
  },
  props: {
    email: {
      type: String,
      required: true,
    },
    password: {
      type: String,
      required: true,
    },
    emailMask: {
      type: String,
    },
    phoneMask: {
      type: String,
    },
    selectedCompanyId: {
      type: String
    },
    fromShopify: {
      type: Boolean
    }
  },
  data() {
    return {
      response: '',
      loading: false,
      logoURL: '',
      privacyURL: '',
      domainCompanyId: null,
      firstTime: true,
      otpChannel: this.emailMask ? 'email' : 'phone' as 'email' | 'phone',
      otpCode: '',
      length: 6,
      token:"",
      lastOtpSent: 0,
      allowOTPAfter: 5,
      allowMaxAttempt: 10,
      otpAttempt: 0
    }
  },
  computed: {
    isValidOtp() {
      return this.otpCode.length === 6
    }
  },
  methods: {
    handleOnChange(value) {
      this.otpCode = value
    },
    handleOnComplete(value) {
      this.otpCode = value
    },
    changeCounter() {
      if (this.lastOtpSent && this.lastOtpSent < this.allowOTPAfter) {
        this.lastOtpSent++;
        setTimeout(this.changeCounter, 1000);
      }
    },
    async requestForOtp() {
      if (this.firstTime == false && this.lastOtpSent !== this.allowOTPAfter) {
        return;
      }

      this.otpCode = ""
      this.lastOtpSent = 0
      this.otpAttempt++

      const params = {
        ...getHostAndDomain(),
        email: this.email.trim(),
        password: this.password.trim(),
        deviceId: await this.$store.dispatch('getDeviceId'),
        deviceType: 'web',
        deviceName: navigator.userAgent,
        version: 2,
        otpChannel: this.otpChannel,
      } as { [key: string]: any }

      this.loading = true

      if (
        this.domainCompanyId &&
        this.domainCompanyId !== 'YuTUZlUtrwBtvmgByZDW'
      ) {
        params.companyId = this.domainCompanyId || this.selectedCompanyId
      } else {
        params.companyId = this.selectedCompanyId
      }

      restAgent.Oauth.loginWithEmail(params)
        .then((response: AxiosResponse) => {
          this.token = response.token
          this.firstTime = false
          this.loading = false
          this.lastOtpSent = 1
          this.changeCounter()
        })
        .catch(reason => {
          this.response = reason.response
            ? reason.response.data.message
            : 'Unable to log you in at this time.'
          this.loading = false
        })
    },

    async verifyOtp() {
      const _self = this

      this.loading = true
      this.response = ''

      const params = {
        ...getHostAndDomain(),
        email: this.email.trim(),
        password: this.password.trim(),
        deviceId: await this.$store.dispatch('getDeviceId'),
        deviceType: 'web',
        deviceName: navigator.userAgent,
        version: 2,
        otpChannel: this.otpChannel,
        otp: this.otpCode,
        token: this.token,
      } as { [key: string]: any }

      if (
        this.domainCompanyId &&
        this.domainCompanyId !== 'YuTUZlUtrwBtvmgByZDW'
      ) {
        params.companyId = this.domainCompanyId || this.selectedCompanyId
      } else {
        params.companyId = this.selectedCompanyId
      }

      restAgent.Oauth.loginWithEmail(params)
        .then((response: AxiosResponse) => {
          this.$store
            .dispatch('auth/set', {
              apiKey: response.apiKey,
              userId: response.userId,
              companyId: response.companyId,
            })
            .then(() => {
              return this.$auth.signInWithCustomToken(response.token)
            })
            .then(async () => {
              this.$store.dispatch('auth/set', {
                firebaseToken: response.token,
                apiKey: response.apiKey,
                userId: response.userId,
                companyId: response.companyId,
              })
              NotificationHelper.requestPermission()
              this.$store.dispatch('init')
              const loginCount = firebase.firestore.FieldValue.increment(1)
              await User.collectionRef()
                .doc(response.userId)
                .update({ login_count: loginCount })
              const url = this.$route.query.url
              if (url) {
                this.$router.push({
                  path: decodeURIComponent(url),
                  replace: true,
                })
              } else {
                if (this.fromShopify) {
                  this.$router.push({ name: 'shopify_dashboard', replace: true, query: this.$router.currentRoute?.query })
                } else {
                  this.$router.push({ name: 'dashboard', replace: true })
                }
              }
            }).finally(() => {
              this.loading = false
            })
        })
        .catch(reason => {
          this.response = reason.response
            ? reason.response.data.message
            : 'Unable to log you in at this time.'
          // this.isValidOtp = false
          this.loading = false
        })
    },

    async submit(selectedAgencyMultipleUsers?: string) {
      if (this.firstTime) {
        return this.requestForOtp()
      }

      return this.verifyOtp()
    },
    async selectAgencyMultipleUser(selectedAgencyMultipleUsers) {
      this.multipleUsersFound = null
      await this.submit(selectedAgencyMultipleUsers)
    },
    changeotpChannel(option: 'email' | 'phone') {
      this.firstTime = true
      this.otpChannel = option
    },
  },
})
</script>

<style scoped>
.error {
  color: #a94442;
}
/*.container-fluid .card .card-body*/
.hl_login--body .spinner {
  display: inline-block;
  margin: 40px 0px 0px 0px;
  width: 100%;
}
.has-error .form-control,
.has-error .form-control:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.global-error {
  margin-bottom: 10px;
}
.hl_login--body .v-spinner .v-moon1 {
  margin: auto;
}
</style>
