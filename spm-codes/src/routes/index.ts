import Vue from 'vue'
import Router from 'vue-router'
import { pmd } from './pmd'
import { v2 } from './v2'
import { getCurrentInstance } from '@vue/composition-api'
// Note: patch for duplicated routes error from vue-router
const originalPush = Router.prototype.push
// @ts-ignore
Router.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject)
    return originalPush.call(this, location, onResolve, onReject)
  return originalPush.call(this, location).catch(err => err)
}

Vue.use(Router)

const routes = [
  ...pmd,
  ...v2,
]

export default new Router({
  mode: 'history',
  routes,
  scrollBehavior(to, from, savedPosition) {
    return { x: 0, y: 0 }
  }
})

function useRoute() {
  const vm = getCurrentInstance()
  if (!vm) throw new Error('must be called in setup')

  return vm.proxy.$route
}

function useRouter() {
  const vm = getCurrentInstance()
  if (!vm) throw new Error('must be called in setup')

  return vm.proxy.$router
}
export { useRoute,useRouter }
