import { requests } from '.'
import config from '../config'
interface FetchProductLogs {
  locationId: string

  productId?: string

  limit: number

  offset: number

  success?: string

  methods?: string

  startAt?: string

  endAt?: string
}

interface ListProducts {

  locationId: string

  parentId?: string

  limit: number

  offset?: number

  search?: string
}

const ProductServices = {
  fetchProduct: productId =>
    requests(config.productsApiURL).get(`/products/${productId}`),

  createProduct: params =>
    requests(config.productsApiURL).post(`/products/`, params),

  deleteProduct: productId =>
    requests(config.productsApiURL).delete(`/products/${productId}`),

  listProducts: (params:ListProducts) =>
    requests(config.productsApiURL).get(`/products/`, { params }),

  createPrice: params =>
    requests(config.productsApiURL).post(`/products/prices`, params),

  updatePrice: (params, id) =>
    requests(config.productsApiURL).put(`/products/prices/${id}`, params),

  deletePrice: priceId =>
    requests(config.productsApiURL).delete(`/products/prices/${priceId}`),

  updateProduct: (params, id) =>
    requests(config.productsApiURL).put(`/products/${id}`, params),

  fetchPrice: priceId =>
    requests(config.productsApiURL).get(`/products/prices/${priceId}`),

  fetchProductLogs: (params: FetchProductLogs) =>
    requests(config.requestLoggerApiURL).get(`/request-logger/products`, {
      params: params,
    }),

  findByLogId: requestId =>
    requests(config.requestLoggerApiURL).get(`/request-logger/${requestId}`),

  syncProduct: params =>
    requests(config.productsApiURL).post(`/products/sync`, params),

  getProductWithIntegrations: (params) =>
    requests(config.productsApiURL).get(`/products/with-integrations`,{params}),

  getStripePrices: (params) =>
    requests(config.productsApiURL).get(`/products/prices/stripe`, { params }),

  importStripeProduct: params =>
    requests(config.productsApiURL).post(`/products/prices/stripe/import`, params),
}

export default ProductServices
