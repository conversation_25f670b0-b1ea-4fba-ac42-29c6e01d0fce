fetch("https://backend.leadconnectorhq.com/users/?locationId=ErcZjGpxMqLYjK1UwRwe", {
    "headers": {
      "accept": "application/json, text/plain, */*",
      "accept-language": "en-US,en;q=0.9",
      "authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
      "cache-control": "no-cache",
      "channel": "APP",
      "source": "WEB_USER",
      "token-id": "eyJhbGciOiJSUzI1NiIsImtpZCI6ImE0YTEwZGVjZTk4MzY2ZDZmNjNlMTY3Mjg2YWU5YjYxMWQyYmFhMjciLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LJ1OgKIXqbT20Kk6cPThB878JhE7OgDYW_xAiG46dgo9sVs0bAdWix6ir6WV1ValZXFNeXLYknxSTtxBx8N5petfdlMsayI2R_X-HZTVxhN_RVILyCYH5DdG8iaxAnrGJR8mI5osJqOtjWsal6JmrNmMQ3M9UzqXYY1k9-vLxMBHFC6R7eZY-NxejCq8ucMfp46v3ysZlN1LMpszSsGUautj2fIcyReZCFVF_qdK4NwBf4WxTqgHemrRC-ENOC3N0NUBOuMNV5-38fbrykbUTumY_hdmpC0vq-SiX70nIrIHBTqfczGwsoG-t2xwM6EtbCMUcrsM8OuNNePkMXbTXg"
    },
    "referrer": "https://app.gohighlevel.com/",
    "referrerPolicy": "strict-origin-when-cross-origin",
    "body": null,
    "method": "GET",
    "mode": "cors",
    "credentials": "include"
  });

//   const response = {"users":[],"traceId":"75bc2ed5-16bb-43e4-ae73-35f421573e76"}