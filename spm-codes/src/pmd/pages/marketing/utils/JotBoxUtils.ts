import * as lodash from 'lodash'
export class JotBoxUtils {
  protected selectedValue: any = {}

  public get selectedValues(): any {
    return this.selectedValue
  }

  public set selectedValues(val: any) {
    this.selectedValue = val
  }

  public get standardFields() {
    return {
      full_name: 'Full Name',
      name: 'Name',
      first_name: 'First Name',
      last_name: 'Last Name',
      phone: 'Phone',
      email: 'Email',
      organization: 'Organization',
      address: 'Address',
      city: 'City',
      state: 'State',
      postal_code: 'Postal Code',
      website: 'Website',
      date_of_birth: 'Date of birth',
      gender: 'Gender',
      calendar_name: 'Calendar Name',
      selected_slot: 'Selected Slot',
      selected_timezone: 'Selected Timezone',
      calendar_notes: 'Calender Notes',
      message: 'Message',
    }
  }

  public get eventsFields() {
    return {
      url: 'URL',
      source: 'Source',
      keyword: 'Keyword',
      campaign: 'Campaign',
      utm_medium: 'UTM Medium',
      utm_content: 'UTM Content',
      url_params: 'URL Params',
      utm_source: 'UTM Source',
      campaign_id: 'Campaign Id',
      referrer: 'Referrer',
      page_title: 'Page Title',
    }
  }

  public get notAllowedFields() {
    return [
      'title',
      'dateAdded',
      'form_id',
      'eventData',
      'form_name',
      'sessionId',
      'contactCreatedAt',
      'sessionFingerprint',
      'formbuilderContactId',
      'fingerprint',
      'survey_name',
      'survey_id',
      'surveyContactId',
      'adSource',
      'timestamp',
      'contactSessionIds',
      'calendar_id',
      'appointmentId',
      'calendar_service_id',
      'fieldsOriSequance',
      'calendarEvent',
      'parentId',
      'parentName',
      'pageVisitType',
      'version',
      'domain',
      'ip',
      'fbc',
      'fbp',
      'customForm',
      'internal_source',
      'attributionSource'
    ]
  }

  public filterData(): any {
    if (!this.selectedValues || this.selectedValues.length < 1) {
      return []
    }
    const results = []
    if (this.selectedValues.fieldsOriSequance) {
      let i = 0
      const orderdFields = lodash.sortBy(
        Object.keys(this.selectedValues),
        field => {
          i++

          return lodash.indexOf(this.selectedValues.fieldsOriSequance, field) !=
            -1
            ? lodash.indexOf(this.selectedValues.fieldsOriSequance, field)
            : this.selectedValues.length + i
        }
      )

      orderdFields.filter(key => {
        if (this.notAllowedFields.indexOf(key) == -1) {
          results.push({
            name: key,
            value: this.selectedValues[key],
          })
        }
      })
    } else {
      Object.keys(this.selectedValues).filter(key => {
        if (this.notAllowedFields.indexOf(key) == -1) {
          results.push({
            name: key,
            value: this.selectedValues[key],
          })
        }
      })
    }

    return results
  }

  public eventData(): any {
    if (
      !this.selectedValues ||
      this.selectedValues.length < 1 ||
      !this.selectedValues.eventData
    ) {
      return []
    }

    const results = []
    Object.keys(this.selectedValues.eventData).filter(key => {
      if (key == 'page') {
        results.push({
          name: 'url',
          value: this.selectedValues.eventData[key].url,
        })

        if (this.selectedValues.eventData[key].title) {
          results.push({
            name: 'page_title',
            value: this.selectedValues.eventData[key].title,
          })
        }
      } else if (key == 'url_params') {
        if (
          this.selectedValues.eventData[key] &&
          Object.keys(this.selectedValues.eventData[key]).length
        ) {
          const urlParam = this.selectedValues.eventData[key]

          if ({}.hasOwnProperty.call(urlParam, 'utm_medium')) {
            results.push({
              name: 'utm_medium',
              value: urlParam['utm_medium'],
            })
          }

          if ({}.hasOwnProperty.call(urlParam, 'utm_content')) {
            results.push({
              name: 'utm_content',
              value: urlParam['utm_content'],
            })
          }

          results.push({
            name: key,
            value: urlParam,
          })
        }
      } else if (
        this.notAllowedFields.indexOf(key) == -1 &&
        this.selectedValues.eventData[key]
      ) {
        results.push({
          name: key,
          value: this.selectedValues.eventData[key],
        })
      }
    })

    return results
  }

  public initials(title: string) {
    if (!title) {
      return
    }

    const splitStr = title.toLowerCase().split(' ')
    for (let i = 0; i < splitStr.length; i++) {
      // You do not need to check if i is larger than splitStr length, as your for does that for you
      // Assign it back to the array
      splitStr[i] = splitStr[i].charAt(0).toUpperCase()
    }
    // Directly return the joined string
    return splitStr.join('')
  }

  public get notAllowedPageFields() {
    return ['type', 'utm_campaign']
  }

  public decodeURIString(value: any) {
    try {
      return decodeURIComponent(value)
    } catch(error) {
      return '';
    }
  }

  public urlParamsfield() {
    const results = []
    const eventData = this.eventData()
    const urlParams = eventData.find(data => data.name == 'url_params')
    if (urlParams && urlParams.value) {
      Object.keys(urlParams.value).forEach(key => {
        const alreadyExist = eventData.find(data => data.name == key)
        if (!alreadyExist && !this.notAllowedPageFields.includes(key)) {
          results.push({
            name: this.decodeURIString(key),
            value: this.decodeURIString(urlParams.value[key]),
          })
        }
      })
    }

    return results
  }

  public pageDetails() {
    const results: { name: string; value: string }[] = []
    const eventData = this.eventData()
    const eventsFields = this.eventsFields
    eventData.forEach(data => {
      if (data.name != 'url_params' && data.name != 'type') {
        results.push({
          name: {}.hasOwnProperty.call(eventsFields, data.name)
            ? eventsFields[data.name]
            : data.name,
          value: data.value,
        })
      }
    })

    const urlParamsfields = this.urlParamsfield()
    urlParamsfields.forEach(data => {
      results.push({
        name: {}.hasOwnProperty.call(eventsFields, data.name)
          ? eventsFields[data.name]
          : data.name,
        value: data.value,
      })
    })

    return results
  }
}
