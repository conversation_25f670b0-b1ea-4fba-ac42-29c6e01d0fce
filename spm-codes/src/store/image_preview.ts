import { Module } from 'vuex';
import { ImagePreviewState } from '@/store/state_models';

export const imagePreview: Module<ImagePreviewState, RootState> = {
    namespaced: true,
    state: {
        src: ''
    },
    getters: {
        get: (state: ImagePreviewState, getters: any, rootState: RootState, rootGetters: any) => {
            return state;
        },
    },
    mutations: {
        show: (state: ImagePreviewState, src: string) => {
            state.src = src;
        },
        hide: (state: ImagePreviewState) => {
            state.src = '';
        },
    },
};
