<template>
  <div>
    <div class="saas-plan-builder__page-heading">Plans &amp; Pricing</div>
    <div class="saas-plan-builder__wrap">
      <div
        class="saas-plan-builder__plans-overlay"
        v-if="plansStatus !== 'exist' || !company.stripe_connect_id"
      >
        <div class="saas-plan-builder__plans-overlay__default-card">
          <i class="fas fa-folder-plus"></i>
          <div
            class="btn btn-warning"
            @click="$router.push({ name: 'stripe_settings' })"
            v-if="!company.stripe_connect_id"
          >
            Connect your Stripe Account first
          </div>
          <div
            class="btn btn-primary"
            @click="initDefaultPlans"
            v-else-if="plansStatus === 'not_exist'"
          >
            Create Recommended Plans on Stripe
          </div>
          <moon-loader
            v-else-if="plansStatus === 'creating'"
            color="#7F9CF5"
            size="30px"
          />
          <div class="btn btn-success" v-else-if="plansStatus === 'created'">
            ✓ Created Plan in Stripe
          </div>
        </div>
      </div>
      <div class="saas-plan-builder__overall">
        <div class="saas-plan-builder__overall-card --main">
          <div class="saas-plan-builder__overall-card__title">
            Customize Your SaaS Plans
          </div>
          <div style="margin-top: 24px; display: flex">
            <div class="saas-plan-builder__overall-card__info">
              You can either offer our recommended plans or build your own
              packages by editing it.
            </div>
          </div>
        </div>

        <div class="saas-plan-builder__overall-card">
          <div
            class="saas-plan-builder__overall-card__title"
            style="
              display: flex;
              align-items: center;
              justify-content: space-between;
            "
          >
            Trial Period
            <div class="saas-plan-builder__trial-toggle">
              <span>Enable</span>
              <UIToggle
                id="trial_period"
                v-model="trialEnabled"
              />
              <label class="tgl-btn" for="trial_period"></label>
            </div>
          </div>
          <div style="margin-top: 10px; display: flex">
            <div class="saas-plan-builder__overall-card__info">
              Enter the number of days for which the trial can be used
            </div>
          </div>
          <div class="saas-plan-builder__overall-card__trial-row">
            <div class="slider saas-plan-builder__overall-card__trial-slider">
              <input
                v-model="trialDays"
                type="range"
                id="markup"
                name="trialDays"
                :min="trialDaysConfig.min"
                :max="trialDaysConfig.max"
                :step="trialDaysConfig.step"
              />
              <div class="markup-value__wrap">
                <span
                  class="value"
                  :style="{
                    left: `${(trialDays / trialDaysConfig.max) * 100}%`,
                  }"
                  >{{ trialDays }} Days</span
                >
              </div>
            </div>
            <div class="saas-plan-builder__overall-card__trial-value">
              <UITextInputGroup
                type="number"
                class="saas-plan-builder__overall-card__trial-input"
                v-model="trialDays"
                @keydown="preventDecimal"
              />
              <div>Days</div>
            </div>
          </div>
          <!-- <div class="saas-plan-builder__overall-card__overlay"></div> -->
        </div>
        <!-- <div class="saas-plan-builder__overall-card">
          <div class="saas-plan-builder__overall-card__title">
            One time setup fee
          </div>
          <div style="margin-top: 10px; display: flex">
            <div class="saas-plan-builder__overall-card__info">
              You can charge your clients an one-time setup fee when they sign
              up for your SaaS platform
            </div>
          </div>
          <div class="saas-plan-builder__overall-card__one-time-value">
            <div>$</div>
            <input
              type="number"
              class="saas-plan-builder__overall-card__one-time-input"
              :value="297"
            />
          </div>
          <div style="color: #059669; font-size: 12px;">Your profit: $100</div>
          <div class="saas-plan-builder__overall-card__overlay">
            <i class="fas fa-wrench"></i>
            Feature under Development
          </div>
        </div> -->
        <a
          class="saas-plan-builder__overall__more-info"
          href="https://youtu.be/BqgrbB9BbZw"
          target="_blank"
        >
          Learn how to use these products in your sales funnel
          <i class="fas fa-chevron-right"></i>
        </a>
      </div>
      <div
        class="saas-plan-builder__plans"
        :class="{ '--custom': tableMode === 'custom' }"
      >
        <div class="saas-plan-builder__builder-toggle">
          <div
            class="saas-plan-builder__builder-toggle__btn"
            :class="{ '--active': tableMode === 'recommended' }"
            @click="tableMode = 'recommended'"
          >
            Recommended Plans
          </div>
          <div
            class="saas-plan-builder__builder-toggle__btn"
            :class="{ '--active': tableMode === 'custom' }"
            @click="tableMode = 'custom'"
          >
            BUILD YOUR OWN PLAN
          </div>
        </div>

        <div class="saas-plan-builder__plans-row">
          <div
            class="saas-plan-builder__plan-col"
            v-for="(plan, i) in plans"
            :key="plan._id || plan.title"
            :class="{ '--custom': tableMode === 'custom' }"
          >
            <recommended-plan
              :plan="plan"
              :recommendedPlan="recommendedPlans[i]"
              :prev-plan="i === 0 ? null : plans[i - 1]"
              :prev-recommended-plan="i === 0 ? null : recommendedPlans[i - 1]"
              @update="e => updatePlan(i, e)"
              :saasProductDetails="saasProductDetails"
              :minProducts="minProducts"
              :mode="tableMode"
              @onDropFeature="e => handleDropFeature(i, e)"
              :perEmailCost="mailgunConfig.perEmailCost"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      class="saas-plan-builder__plans__save-row"
      v-if="plansStatus === 'exist'"
    >
      <div class="btn" v-if="plansUpdated" @click="resetPlans">
        Reset to defaults
      </div>
      <button
        class="btn"
        :class="plansUpdated ? 'btn-success' : 'btn-light'"
        :disabled="!plansUpdated"
        @click="savePlans"
      >
        Save Changes
      </button>
    </div>

    <br />

    <div class="row" v-if="plansStatus === 'exist'">
      <default-twilio-rebilling
        :settings="twilioRebilling"
        @update="updateTwilioRebilling"
        class="col-lg-6"
        type="twilio"
      />
      <email-rebilling
        class="col-lg-6"
        :can-update="true"
        :auto-save="true"
        :settings="mailgunRebilling"
        :updating="mailgunConfig.updating"
        :error="mailgunConfig.error"
        @update="updateMailgunRebilling"
        @updatePerEmailCost="updatePerEmailCost"
      />
    </div>
    <br /><br />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import RecommendedPlan from './RecommendedPlan.vue'
import DefaultTwilioRebilling from './DefaultTwilioRebilling.vue'
import EmailRebilling from '../EmailRebilling.vue'
import { trackGaEvent } from '@/util/helper'
import { IMailgunRebillingSettings } from '@/models/location'

export default Vue.extend({
  components: { RecommendedPlan, DefaultTwilioRebilling, EmailRebilling },
  data() {
    return {
      trialDaysConfig: {
        min: 1,
        max: 30,
        step: 1,
      },
      plansStatus: 'none', // 'none' | 'creating' | 'created' | 'failed_create' | 'exist' | 'not_exist' | 'fetching'
      plans: [] as Array<any>,
      recommendedPlans: [] as Array<any>,
      originalPlans: [] as Array<any>,
      minProducts: [] as Array<any>,
      plansUpdated: false,
      savingPlans: false,
      saasProductDetails: {},
      tableMode: 'recommended', // 'recommended' | 'custom'
      isCustom: false,
      mailgunConfig: {
        updating: false,
        error: '',
        perEmailCost: 0.0007,
      },
    }
  },
  computed: {
    company() {
      return this.$store.state.company.company
    },
    twilioRebilling: {
      get(): any {
        if (this.plans.length) {
          const plan = this.plans[0]
          const { twilioRebilling } = plan
          return twilioRebilling
        } else {
          return {
            enabled: false,
            markup: 10,
          }
        }
      },
      set(value: any) {
        this.updateAllPlans('twilioRebilling', value)
      },
    },
    mailgunRebilling: {
      get(): IMailgunRebillingSettings {
        const defaultSetting: IMailgunRebillingSettings = {
          enabled: false,
          price: 0.0007,
          markup: 5,
        }

        if (this.plans.length) {
          const plan = this.plans[0]
          const { mailgunRebilling } = plan
          return mailgunRebilling || defaultSetting
        } else {
          return defaultSetting
        }
      },
      set(value: IMailgunRebillingSettings) {
        this.updateAllPlans('mailgunRebilling', value)
      },
    },
    trialEnabled: {
      get(): boolean {
        return this.trialDays > 0
      },
      set(value: boolean) {
        this.trialDays = value ? 10 : 0
        trackGaEvent(
          'SaasPlanBuilder',
          this.company.id,
          'Trial days enabled',
          1
        )
      },
    },
    trialDays: {
      get(): number {
        if (this.plans.length) {
          const plan = this.plans[0]
          const { trialPeriod } = plan
          return trialPeriod
        } else {
          return 0
        }
      },
      set(days: string) {
        this.updateAllPlans('trialPeriod', parseInt(days))
      },
    },
  },
  created() {
    this.getPlans()
  },
  methods: {
    preventDecimal(e: any) {
      if (e.key === '.' || e.key === 'e') {
        e.preventDefault()
      }
    },
    updateTwilioRebilling(updatedSettings: any) {
      this.twilioRebilling = updatedSettings
    },
    updateMailgunRebilling(updatedSettings: IMailgunRebillingSettings) {
      this.mailgunRebilling = updatedSettings
    },
    updatePerEmailCost(perEmailCost: any) {
      this.mailgunConfig.perEmailCost = perEmailCost
    },
    async getPlans() {
      this.plansStatus = 'loading'
      try {
        const { data } = await this.saasService.get(
          `/subscription-plans/${this.company.id}/`
        )
        // console.log(data);
        this.saasProductDetails = data.saasProductDetails
        this.plans = data.plans
        this.originalPlans = JSON.parse(JSON.stringify(data.plans))
        this.recommendedPlans = data.recommendedPlans
        this.minProducts = data.minProducts
        if (data.default === true) {
          this.plansStatus = 'not_exist'
        } else {
          this.plansStatus = 'exist'
          this.recommendedPlans.forEach((plan, i) => {
            if (
              JSON.stringify(this.plans[i].saasProducts) !==
              JSON.stringify(plan.saasProducts)
            ) {
              this.isCustom = true
              this.tableMode = 'custom'
            }
          })
        }
      } catch (err) {
        this.plansStatus = 'failed_loading'
      }
    },
    async initDefaultPlans() {
      this.plansStatus = 'creating'
      try {
        const { data } = await this.saasService.post(
          `/subscription-plans/${this.company.id}/`,
          {
            stripeAccountId: this.company.stripe_connect_id,
          }
        )
        // console.log(data);
        this.saasProductDetails = data.saasProductDetails
        this.plans = data.plans
        this.originalPlans = JSON.parse(JSON.stringify(data.plans))
        this.plansStatus = 'created'
        setTimeout(() => {
          this.plansStatus = 'exist'
        }, 1000)
        this.enableAutoCreateTwilioSubAccount()
      } catch (err) {
        this.plansStatus = 'failed_create'
        console.error(`'error while creating plans --> `, err)
        trackGaEvent(
          'SaasPlanBuilder',
          this.company.id,
          `Failed to create-plans. ERROR: ${err.message}`,
          1
        )
        alert('Failed to create plans!')
      }
    },
    updateAllPlans(field: string, value: any) {
      for (let i = 0; i < this.plans.length; i++) {
        this.updatePlan(i, { field, value })
      }
      trackGaEvent(
        'SaasPlanBuilder',
        this.company.id,
        `Updating ${field}. New Value: ${JSON.stringify(value)}`,
        1
      )
    },
    updatePlan(index: number, e: { field: string; value: any }): void {
      const { field, value } = e
      this.plans[index][field] = value

      this.plansUpdated = true
      if (field === 'saasProducts') {
        this.isCustom = true
        this.tableMode = 'custom'
      }
      if (
        ![
          'twilioRebilling',
          'trialPeriod',
          'complementaryCredits',
          'stripePlans',
          'saasProducts',
          'mailgunRebilling',
        ].includes(field)
      ) {
        trackGaEvent(
          'SaasPlanBuilder',
          this.company.id,
          `Updating ${field} of plan-level: ${
            index + 1
          }. New Value: ${JSON.stringify(value)}`,
          1
        )
      }
    },
    resetPlans() {
      this.plans = JSON.parse(JSON.stringify(this.originalPlans))
      this.plansUpdated = false
      trackGaEvent('SaasPlanBuilder', this.company.id, `Reset to Defaults`, 1)
    },
    async savePlans() {
      if (this.tableMode === 'recommended' && this.isCustom) {
        if (
          confirm(
            'You have build your custom plans. Are you sure that you want to override them with recommended plans?'
          )
        ) {
          // proceed // update plans.
          for (let i = 0; i < this.plans.length; i++) {
            this.updatePlan(i, {
              field: 'saasProducts',
              value: this.recommendedPlans[i].saasProducts,
            })
            this.tableMode = 'recommended'
          }
        } else {
          this.tableMode = 'custom'
          return
        }
      }
      try {
        this.savingPlans = true
        const { data } = await this.saasService.put(
          `/subscription-plans/${this.company.id}/`,
          {
            stripeAccountId: this.company.stripe_connect_id,
            plans: this.plans,
          }
        )
        // console.log(data)
        this.saasProductDetails = data.saasProductDetails
        this.plans = data.plans
        this.originalPlans = JSON.parse(JSON.stringify(data.plans))
        // this.plansStatus = 'created';
        // setTimeout( ()=>{
        //   this.plansStatus = 'exist';
        // }, 1000)
        this.plansUpdated = false
        trackGaEvent('SaasPlanBuilder', this.company.id, `Saved Plans`, 1)
      } catch (err) {
        // this.plansStatus = 'failed_create';
        console.error(`'error while saving plans --> `, err)
        trackGaEvent(
          'SaasPlanBuilder',
          this.company.id,
          `Failed to save-plans. ERROR: ${err.message}`,
          1
        )
        alert('Failed to save plans!')
      } finally {
        this.savingPlans = false
      }
    },
    handleDropFeature(index: number, e: any) {
      // console.log(index,e);

      for (let i = index; i < this.plans.length; i++) {
        let newSaasProducts = [...this.plans[i].saasProducts]

        let lockedProducts = (i > 0) ? this.plans[i-1].saasProducts : this.minProducts
        let unlockedProducts = newSaasProducts.filter( feature => {
          // if(i === 1 && e.removedIndex !== null) debugger;
          if(i > 0 && this.plans[i-1].saasProducts && this.plans[i-1].saasProducts.includes(feature)) {
            return false;
          } else if (i === 0 && this.minProducts.includes(feature)) {
            return false;
          } else if(e.payload === feature) { // SKIP for further products.
            return false;
          } else { // base-plan case
            return true;
          }
        })
        // console.log(index, i, lockedProducts, unlockedProducts);
        if (e.removedIndex !== null) {
          // Removing
          if(i === index && unlockedProducts[e.removedIndex] === e.payload) {
            unlockedProducts.splice(e.removedIndex, 1)
          }
        } else if (e.addedIndex !== null) {
          // Adding
          if (i === index) {
            unlockedProducts.splice(e.addedIndex, 0, e.payload)
          }
        }
        // console.log(index, i, lockedProducts, unlockedProducts);
        this.updatePlan(i, {field: 'saasProducts', value: [...lockedProducts, ...unlockedProducts] })
      }
    },
    async enableAutoCreateTwilioSubAccount() {
      try {
        // Refer to: src/pmd/pages/agency/AgencyTwilioSettings.vue
        // let res = await this.$http.get(`/twilio/accounts/${this.company.id}`)
        const body = {
          createSubAccountsAutomatically: true,
        }
        await this.$http.post('/twilio/edit?company_id=' + this.company.id, body)
        // if (res && res.status === 200) {
        //   const companyTwilioAccount = res.data.companyTwilioAccount
        //   if (companyTwilioAccount && companyTwilioAccount.account_sid) {
        //     const body = {
        //       accountSID: companyTwilioAccount.account_sid,
        //       accountToken: companyTwilioAccount.token,
        //       createSubAccountsAutomatically: companyTwilioAccount.createSubAccountsAutomatically || true,
        //     }
        //     await this.$http.post('/twilio/edit?company_id=' + this.company.id, body)
        //   }
        // }
      } catch (err) {
        console.error(err.message)
      }
    },
  },
})
</script>

<style lang="scss">
@import './slider.scss';
.saas-plan-builder__page-heading {
  font-size: 17px;
  line-height: 20px;
  color: #2d3748;
  padding-bottom: 16px;
}
.saas-plan-builder__wrap {
  background: #ffffff;
  box-shadow: 0px 3px 21px rgba(26, 13, 62, 0.04);
  border-radius: 1px;

  display: flex;
  position: relative;
  // flex-wrap: wrap;

  .saas-plan-builder__overall {
    min-width: 400px;
    .saas-plan-builder__overall-card {
      position: relative;
      padding: 16px 24px;
      border-bottom: 1px solid #e2e8f0;

      .saas-plan-builder__overall-card__overlay {
        position: absolute;
        top: 0px;
        left: 0px;
        width: 100%;
        height: 100%;
        z-index: 1;
        background-color: rgba(255, 255, 255, 0.6);

        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: rgba(0, 0, 0, 0.4);
        i {
          font-size: 24px;
          margin: 20px;
        }
      }

      .saas-plan-builder__overall-card__title {
        font-weight: 500;
        font-size: 13px;
        line-height: 15px;
        color: #4a5568;
      }
      .saas-plan-builder__overall-card__info {
        font-size: 12px;
        line-height: 16px;
        color: #718096;
      }
      &.--main {
        padding: 24px 24px;
        .saas-plan-builder__overall-card__title {
          font-size: 15px;
          line-height: 18px;
        }
        .saas-plan-builder__overall-card__info {
          font-size: 14px;
          line-height: 16px;
        }
      }
    }
    .saas-plan-builder__trial-toggle {
      display: flex;
      align-items: center;
      span {
        margin-right: 6px;
        font-weight: 400;
      }
    }
    .saas-plan-builder__overall__more-info {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      cursor: pointer;
      font-size: 13px;
      line-height: 15px;
      text-align: right;
      color: #3080ec;
      text-decoration: none;

      padding: 12px 24px;
      i {
        margin-left: 8px;
      }
    }
  }
  .saas-plan-builder__plans {
    margin-top: 44px;
    background-color: #f9fafb;
    width: 100%;
    max-width: calc(100% - 400px);
    border-top: 1px solid #e2e8f0;
    border-left: 1px solid #e2e8f0;
    position: relative;
    &.--custom {
      background-color: #eff6ff;
    }
    .saas-plan-builder__builder-toggle {
      // position: absolute;
      // transform: translateY(-50%);

      display: flex;
      margin: -16px auto;

      border-radius: 5px;
      border: solid 3px #2f80ed;
      background-color: #2f80ed;
      // width: 370px;
      width: fit-content;
      z-index: 1;
      position: relative;
    }
    .saas-plan-builder__builder-toggle__btn {
      padding: 6px 20px;
      font-weight: 500;
      font-size: 12px;
      line-height: 14px;
      letter-spacing: 0.004em;
      text-transform: uppercase;

      background-color: #2f80ed;
      color: #ffffff;
      cursor: pointer;
      border-radius: 5px;
      &.--active {
        background-color: #ffffff;
        color: #2f80ed;
      }
    }
    .saas-plan-builder__plans-row {
      display: flex;
      overflow-x: auto;
      .saas-plan-builder__plan-col {
        min-height: 500px;
        flex: 1 1 0;
        &:nth-child(even) {
          background-color: #ffffff;
          &.--custom {
            background-color: #f5faff;
          }
        }
      }
    }
  }
}
.saas-plan-builder__plans__save-row {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  position: fixed;
  bottom: 0px;
  left: 0px;
  z-index: 4;

  width: 100%;
  background: #335161;
  height: 58px;
  padding: 16px;
  color: #ffffff;
  .btn {
    &:disabled {
      padding-left: 20px;
      opacity: 0.7;
    }
    &.btn-light {
      background-color: #ffffff;
    }
    &.btn-success {
      // background-color: #3080ec !important;
      background-color: #37ca37 !important;
      color: #ffffff !important;
      // &:hover, &:active{
      //   background-color: #37ca37 !important;
      // }
    }
  }
}
.saas-plan-builder__overall-card__trial-row {
  display: flex;
  align-items: center;
  justify-content: space-between;

  margin-top: 8px;
  .saas-plan-builder__overall-card__trial-slider {
    flex-grow: 1;
    margin-right: 12px;
  }
  .saas-plan-builder__overall-card__trial-value {
    display: flex;
    align-items: center;
    .saas-plan-builder__overall-card__trial-input {
      width: 64px;
      background: rgba(255, 255, 255, 0.85);
      // border: 1px solid #d1d5db;
      box-sizing: border-box;
      border-radius: 3px;
      outline: none;
      text-align: center;

      margin: 0px 8px;
      &:active,
      &:focus-within {
        border: 1px solid #63b3ed;
      }
    }
  }
}
.saas-plan-builder__overall-card__one-time-value {
  display: flex;
  align-items: center;

  margin: 16px 0px;
  .saas-plan-builder__overall-card__one-time-input {
    width: 64px;
    background: rgba(255, 255, 255, 0.85);
    border: 1px solid #d1d5db;
    box-sizing: border-box;
    border-radius: 3px;
    outline: none;
    text-align: center;

    margin: 0px 8px;
    &:active,
    &:focus-within {
      border: 1px solid #63b3ed;
    }
  }
}

.saas-plan-builder__plans-overlay {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  background-color: rgba(245, 252, 255, 0.7);

  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  .saas-plan-builder__plans-overlay__default-card {
    background-color: #ffffff;
    padding: 64px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-width: 420px;
    min-height: 260px;
  }

  i {
    font-size: 48px;
    margin-bottom: 32px;
    color: rgba(146, 152, 162, 0.5);
  }
}

.saas-plan-builder__overall-card__trial-slider {
  position: relative;
  #markup {
    width: 100%;
  }
  .markup-value__wrap {
    position: absolute;
    left: 8px;
    top: -30px;
    width: calc(100% - 24px);
    height: 20px;
  }
  span.value {
    position: absolute;
    text-align: center;
    background: #2d3748;
    border-radius: 5px;
    color: white;
    padding: 4px 6px;
    border-radius: 2px;
    font-size: 12px;
    line-height: 16px;
    white-space: nowrap;
    transition: opacity 0.25s ease-in;
    transform: translateX(-50%);
    opacity: 0;
    &:before {
      content: '';
      width: 0px;
      height: 0px;
      position: absolute;
      border-left: 5px solid #2d3748;
      border-right: 5px solid transparent;
      border-top: 5px solid #2d3748;
      border-bottom: 5px solid transparent;
      left: calc(50% - 5px);
      top: 18px;
      transform: rotate(225deg);
    }
  }
  &:focus-within {
    span.value {
      opacity: 0.8;
    }
  }
}
</style>
