<template>
	<Filters
		:conditions="conditions"
		:filterMaster="filterMaster"
		v-on:update:conditions="$emit('update:conditions', $event)"
	/>
</template>

<script lang="ts">
import Vue from 'vue'
import * as lodash from 'lodash';
import { Campaign, Calendar } from '@/models';
import { Condition } from '@/models/trigger';

const Filters = () => import( './Filters.vue');

export default Vue.extend({
	props: ['conditions'],
	components: { Filters },
	data() {
		return {
			filterMaster: [
				{
					placeHolder: 'Select Calendar Provider',
					title: 'In Calendar Provider',
					value: 'appointment_v3.calendarProviderId',
					valueType: 'text',
					type: 'select',
					options: [] as { [key: string]: any }[]
				}
			],
		}
  },
  watch: {
    conditions: {
      handler: function(val) {
        this.setServices(val)
      },
      deep: true
    }
  },
	async created() {
		const currentLocationId = this.$router.currentRoute.params.location_id;
		const calendarProviderIdOption = lodash.find(this.filterMaster, { value: 'appointment_v3.calendarProviderId' });
    if (calendarProviderIdOption) {
			calendarProviderIdOption.options = this.$store.state.calendarProviders.calendarProviders.map(calendarProvider => {
				return {
					title: calendarProvider.name,
					value: calendarProvider.id
				}
      });
    }

    this.setServices()
  },
  methods: {
    setServices() {
      const provider = this.conditions.find(x => x.field === 'appointment_v3.calendarProviderId' && x.value)
      const serviceIndex = this.filterMaster.findIndex(x => x.value === 'appointment_v3.calendarServiceId')
      const userIndex = this.filterMaster.findIndex(x => x.value === 'appointment_v3.userId')

      if (!provider) {
        if (serviceIndex !== -1) {
          this.filterMaster.splice(serviceIndex, 1)
        }
        if (userIndex !== -1) {
          this.filterMaster.splice(userIndex, 1)
        }
      } else {
        if (userIndex === -1) {
          const users = []
          const calendarProvider = this.$store.state.calendarProviders.calendarProviders.find(x => x.id === provider.value)
          if (calendarProvider) {
            const usersIds = calendarProvider.user_ids
            if (usersIds) {
              const users = this.$store.state.users.users.filter(x => usersIds.includes(x.id))
                .map(user => {
                  return {
                    title: [user.first_name, user.last_name].filter(val => val).join(' '),
                    value: user.id
                  }
                })

              if (users) {
                const userOption = {
                  placeHolder: 'Select User',
                  title: 'Select User',
                  value: 'appointment_v3.userId',
                  valueType: 'text',
                  type: 'select',
                  options: users
                }

                this.filterMaster.splice(1, 0, userOption)
              }
            }
          }
        }

        if (serviceIndex === -1) {
          const serviceOption = {
            placeHolder: 'Select Calendar Service',
            title: 'Select Calendar Service',
            value: 'appointment_v3.calendarServiceId',
            valueType: 'text',
            type: 'select',
            options: []
          }

          this.filterMaster.splice(1, 0, serviceOption)
        }

        const providerOption = this.filterMaster.find(x => x.value === 'appointment_v3.calendarServiceId')

        providerOption.options = this.$store.state.calendars.calendars
          .filter(x => x.provider_id === provider.value)
          .map(calendarService => {
            return {
              title: calendarService.name,
              value: calendarService.id
            }
          })
      }
    }
  }
})
</script>

