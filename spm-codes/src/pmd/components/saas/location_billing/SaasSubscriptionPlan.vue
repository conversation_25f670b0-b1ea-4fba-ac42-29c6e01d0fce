<template>
  <div class="card subscription-plan">
    <div class="card-header">
      <h3>Subscription Plans</h3>
    </div>
    <div class="card-body">
      <div v-for="eachPlan of plans" v-bind:key="eachPlan.title"  class="plan-details">
        <div class="title">{{ eachPlan.title }}</div>
        <div class="price">
          <span>{{ eachPlan.symbol }}{{ eachPlan.amount }}</span
          >&nbsp;/&nbsp;{{ eachPlan.billingInterval }}
        </div>
      </div>
      <!-- <a href="#">Contact us to change plan &gt;</a> -->
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    plans: {
      type: Array,
    },
  },
})
</script>
<style scoped>
.subscription-plan .card-body {
  padding-top: 16px;
  padding-bottom: 8px;
}

.plan-details {
  display: flex;
  padding: 24px 0;
  justify-content: center;
  align-items: center;
  background: linear-gradient(90.69deg, #cee5f1 0%, #d3daf9 100%);
  border-radius: 3px;
  margin-bottom: 24px;
}

.plan-details .title {
  font-size: 15px;
  margin-right: 10px;
  color: #4a5568;
}

.plan-details .price {
  background: rgba(236, 244, 255, 0.35);
  border: 1px solid rgba(163, 186, 222, 0.27);
  box-sizing: border-box;
  border-radius: 9px;
  padding: 8.5px 12.5px;
  color: gray;
  margin-left: 10px;
}

.plan-details .price span {
  font-weight: bold;
  font-size: 16px;
  color: black;
}
</style>
