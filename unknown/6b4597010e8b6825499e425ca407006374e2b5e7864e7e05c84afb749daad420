<template>
  <div id="yext_status">
    <div class="card">
      <div class="card-body">
        <div class="hl_center hl_text_center">
          <template v-if="this.location.yextReseller.status === 'SUBMITTED'">
            <h3>🎉 Congratulations! 🎉</h3>
            <p>
              Your request for listing has been submitted to yext, once
              processing finished you will able to see all your listings here
            </p>
          </template>
          <template v-else>
            <h3>{{ this.location.yextReseller.status }}</h3>
            <p>
              {{ this.location.yextReseller.status_message }}
            </p>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { Location } from '@/models'
export default Vue.extend({
  props: ['location'],
  async created() {},
  methods: {
    callToActionClicked: function () {
      this.$emit('callToActionClicked')
    },
  },
})
</script>
<style lang="scss" scoped>
#yext_status {
  padding-top: 50px;
  display: grid;
  justify-content: center;
  align-items: center;
  position: sticky;
  top: 100px;
  left: 0;
  z-index: 10;
  margin-top: -265px;
  .card {
    border: 1px solid #e1e1e1;
    box-shadow: 0 2px 4px 0px #b1b1b13b;
    width: 650px;
    height: 200px;
    .pitch {
      color: #f59e0b;
      margin-bottom: 10px;
    }
  }
}
</style>