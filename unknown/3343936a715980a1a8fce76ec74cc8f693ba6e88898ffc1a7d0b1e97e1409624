<template>
  <div>
    <h6>
      No User available in this account.
      <!-- <span
            class="req-card__link"
            @click="$router.push({ name: 'account_team_management' })"
            >Click here</span
          > -->
      <!-- to create a new user for this account. -->
      Please add a new client below:
    </h6>
    <br />
    <div class="row">
      <div class="col-sm-6">
        <div class="form-group">
          <label>First Name</label>
          <input
            type="text"
            class="form-control msgsndr5"
            placeholder="First Name"
            v-model="firstName"
            v-validate="'required'"
            name="msgsndr5"
            autocomplete="msgsndr5"
            data-vv-as="First name"
          />
          <span v-show="errors.has('msgsndr5')" class="--red">{{
            errors.first('msgsndr5')
          }}</span>
        </div>
      </div>
      <div class="col-sm-6">
        <div class="form-group">
          <label>Last Name</label>
          <input
            type="text"
            class="form-control msgsndr6"
            placeholder="Last Name"
            v-model="lastName"
            v-validate="'required'"
            name="msgsndr6"
            data-vv-as="Last name"
            autocomplete="msgsndr6"
          />
          <span v-show="errors.has('msgsndr6')" class="--red">{{
            errors.first('msgsndr6')
          }}</span>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-6">
        <div class="form-group">
          <label>Email</label>
          <input
            type="email"
            class="form-control msgsndr3"
            placeholder="Email"
            v-model="email"
            v-validate="{
              required: true,
              email: true,
            }"
            name="msgsndr3"
            autocomplete="msgsndr3"
          />
          <span v-show="errors.has('msgsndr3')" class="--red">{{
            errors.first('msgsndr3')
          }}</span>
        </div>
      </div>
      <div class="col-sm-6">
        <div class="form-group">
          <label>Password</label>
          <input
            type="password"
            data-lpignore="true"
            class="form-control"
            name="password"
            placeholder="Password"
            v-validate="'required|strongPassword'"
            v-model="passwordText"
            autocomplete="new-password"
          />
          <span v-show="errors.has('password')" class="--red">{{
            errors.first('password')
          }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      firstName: '',
      lastName: '',
      email: '',
      passwordText: '',
    }
  },
}
</script>

<style></style>
