/**
 * SubAccount API Slice
 * 
 * RTK Query API slice for SubAccount CRUD operations and advanced queries
 * Based on SPM Location model patterns and lcCore User API patterns
 */

import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { 
  ISubAccount, 
  ICreateSubAccountPayload, 
  IUpdateSubAccountPayload,
  ISubAccountQueryParams,
  ISubAccountSearchResponse,
  SubAccountStatus
} from '../../../types/subAccount';

/**
 * SubAccount API slice with RTK Query
 */
export const subAccountApi = createApi({
  reducerPath: 'subAccountApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api',
    prepareHeaders: (headers) => {
      headers.set('Content-Type', 'application/json');
      return headers;
    },
  }),
  tagTypes: ['SubAccount', 'SubAccountList'],
  endpoints: (builder) => ({
    /**
     * Get all sub-accounts for a company
     * Based on SPM Location.getByCompanyId patterns
     */
    getSubAccounts: builder.query<ISubAccountSearchResponse, ISubAccountQueryParams>({
      query: (params = {}) => ({
        url: '/sub-accounts',
        params,
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.subAccounts.map(({ id }) => ({ type: 'SubAccount' as const, id })),
              { type: 'SubAccountList', id: 'LIST' },
            ]
          : [{ type: 'SubAccountList', id: 'LIST' }],
    }),

    /**
     * Get sub-account by ID
     * Based on SPM Location.getById patterns
     */
    getSubAccountById: builder.query<ISubAccount, string>({
      query: (id: string) => `/sub-accounts/${id}`,
      providesTags: (result, error, id) => [{ type: 'SubAccount', id }],
    }),

    /**
     * Create new sub-account
     * Based on SPM Location creation patterns
     */
    createSubAccount: builder.mutation<ISubAccount, ICreateSubAccountPayload>({
      query: (subAccountData: ICreateSubAccountPayload) => ({
        url: '/sub-accounts',
        method: 'POST',
        body: subAccountData,
      }),
      invalidatesTags: [{ type: 'SubAccountList', id: 'LIST' }],
    }),

    /**
     * Update sub-account
     * Based on SPM Location.save patterns
     */
    updateSubAccount: builder.mutation<ISubAccount, { id: string } & IUpdateSubAccountPayload>({
      query: ({ id, ...updateData }: { id: string } & IUpdateSubAccountPayload) => ({
        url: `/sub-accounts/${id}`,
        method: 'PUT',
        body: updateData,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'SubAccount', id },
        { type: 'SubAccountList', id: 'LIST' },
      ],
    }),

    /**
     * Delete sub-account (soft delete)
     * Based on SPM Location.delete patterns
     */
    deleteSubAccount: builder.mutation<void, string>({
      query: (id: string) => ({
        url: `/sub-accounts/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'SubAccount', id },
        { type: 'SubAccountList', id: 'LIST' },
      ],
    }),

    /**
     * Get active sub-accounts only
     * Based on SPM Location.fetchAllLocations patterns
     */
    getActiveSubAccounts: builder.query<ISubAccount[], string>({
      query: (companyId: string) => ({
        url: '/sub-accounts/active',
        params: { companyId },
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'SubAccount' as const, id })),
              { type: 'SubAccountList', id: 'ACTIVE' },
            ]
          : [{ type: 'SubAccountList', id: 'ACTIVE' }],
    }),

    /**
     * Search sub-accounts by name
     * Based on SPM Location.getByCompanyIdQuery patterns
     */
    searchSubAccounts: builder.query<ISubAccount[], { companyId: string; query: string }>({
      query: ({ companyId, query }: { companyId: string; query: string }) => ({
        url: '/sub-accounts/search',
        params: { companyId, query },
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'SubAccount' as const, id })),
              { type: 'SubAccountList', id: 'SEARCH' },
            ]
          : [{ type: 'SubAccountList', id: 'SEARCH' }],
    }),

    /**
     * Get sub-accounts by status
     * Based on SPM Location status filtering patterns
     */
    getSubAccountsByStatus: builder.query<ISubAccount[], { companyId: string; status: SubAccountStatus }>({
      query: ({ companyId, status }: { companyId: string; status: SubAccountStatus }) => ({
        url: '/sub-accounts/by-status',
        params: { companyId, status },
      }),
      providesTags: (result, error, { status }) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'SubAccount' as const, id })),
              { type: 'SubAccountList', id: status.toUpperCase() },
            ]
          : [{ type: 'SubAccountList', id: status.toUpperCase() }],
    }),

    /**
     * Update sub-account status
     * Based on SPM Location status management patterns
     */
    updateSubAccountStatus: builder.mutation<ISubAccount, { id: string; status: SubAccountStatus }>({
      query: ({ id, status }: { id: string; status: SubAccountStatus }) => ({
        url: `/sub-accounts/${id}/status`,
        method: 'PATCH',
        body: { status },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'SubAccount', id },
        { type: 'SubAccountList', id: 'LIST' },
        { type: 'SubAccountList', id: 'ACTIVE' },
        { type: 'SubAccountList', id: 'PROSPECT' },
        { type: 'SubAccountList', id: 'ACCOUNT' },
      ],
    }),

    /**
     * Update sub-account settings
     * Based on SPM Location settings management patterns
     */
    updateSubAccountSettings: builder.mutation<ISubAccount, { id: string; settings: Partial<ISubAccount['settings']> }>({
      query: ({ id, settings }: { id: string; settings: Partial<ISubAccount['settings']> }) => ({
        url: `/sub-accounts/${id}/settings`,
        method: 'PATCH',
        body: { settings },
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'SubAccount', id },
      ],
    }),

    /**
     * Get sub-account by snapshot ID
     * Based on SPM Location.fetchLocationBySnapshotId patterns
     */
    getSubAccountBySnapshotId: builder.query<ISubAccount[], string>({
      query: (snapshotId: string) => `/sub-accounts/by-snapshot/${snapshotId}`,
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'SubAccount' as const, id })),
              { type: 'SubAccountList', id: 'SNAPSHOT' },
            ]
          : [{ type: 'SubAccountList', id: 'SNAPSHOT' }],
    }),

    /**
     * Bulk update sub-accounts
     * For batch operations
     */
    bulkUpdateSubAccounts: builder.mutation<ISubAccount[], { ids: string[]; updateData: IUpdateSubAccountPayload }>({
      query: ({ ids, updateData }: { ids: string[]; updateData: IUpdateSubAccountPayload }) => ({
        url: '/sub-accounts/bulk-update',
        method: 'PATCH',
        body: { ids, updateData },
      }),
      invalidatesTags: (result, error, { ids }) => [
        ...ids.map(id => ({ type: 'SubAccount' as const, id })),
        { type: 'SubAccountList', id: 'LIST' },
      ],
    }),
  }),
});

// Export hooks for use in components
export const {
  useGetSubAccountsQuery,
  useGetSubAccountByIdQuery,
  useCreateSubAccountMutation,
  useUpdateSubAccountMutation,
  useDeleteSubAccountMutation,
  useGetActiveSubAccountsQuery,
  useSearchSubAccountsQuery,
  useGetSubAccountsByStatusQuery,
  useUpdateSubAccountStatusMutation,
  useUpdateSubAccountSettingsMutation,
  useGetSubAccountBySnapshotIdQuery,
  useBulkUpdateSubAccountsMutation,
  useLazyGetSubAccountsQuery,
  useLazySearchSubAccountsQuery,
  useLazyGetSubAccountsByStatusQuery,
} = subAccountApi;
