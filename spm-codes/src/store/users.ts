import { User } from '../models'
import { <PERSON><PERSON>le, ActionContext } from 'vuex'
import { RootState, UsersState } from './state_models'
import { user } from './user';

let userListner;

export const users: Module<UsersState, RootState> = {
  namespaced: true,
  state: { users: [], locationId: '' },
  getters: {
    getById: (state: UsersState) => {
      return (userId: string) => state.users.find(x => x.id === userId)
    },
    getAll: (state: UsersState) => {
      return [...state.users.map(a=> new User(a))];
    }
  },
  mutations: {
    set: (state: UsersState, payload: { users: any[], locationId: string }) => {
      state.users = payload.users
      state.locationId = payload.locationId
    },
    clearAll: (state: UsersState) => {
      if (userListner) {
        userListner();
      }

      state.users = []
      state.locationId = undefined
    }
  },
  actions: {
    async syncAll(
      context: ActionContext<UsersState, RootState>,
      locationId: string
    ) {
      if (!locationId) {
        context.commit('clearAll')
        return
      }


      if (context.state.locationId === locationId) {
        return context.state.users;
      }

      return new Promise((resolve, reject) => {

        if (userListner) {
          userListner();
        }

        userListner = User.collectionRef()
          .where('locations.' + locationId, '>=', '')
          .onSnapshot(snapshot => {
            context.commit('set', {
              locationId: locationId,
              users: lodash
              .sortBy(snapshot.docs
                .map(d => {
                  return {
                    id: d.id,
                    ...d.data()
                  }
                })
                .filter(x => !x.deleted),
                [user => user.first_name ? user.first_name.toLowerCase() : '']
              )
            })

            resolve(context.state.users)
          }, err => resolve())
      })
    },
    logout: {
      root: true,
      handler: async (context: ActionContext<UsersState, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
