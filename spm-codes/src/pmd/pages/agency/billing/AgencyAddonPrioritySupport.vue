<template>
  <div class="hl_billing--account">
    <div class="card --addons">
      <div class="card-body">
        <h3 class="agency-plan__heading">Priority Support</h3>
        <p v-if="duration === 'monthly'">$300<span>/month</span></p>
        <p v-else-if="duration === 'annual'">$3000<span>/year</span></p>
        <p class="agency-plan__description" v-else>Subscribed</p>

      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import * as moment from 'moment-timezone';

export default Vue.extend({
  props: ['company'],
  data(){
    return{
      amount: 300,
      duration: '' // 'monthly' | 'annual'
    }
  },
  methods:{
    formateDate(miliSeconds: number){
      return moment.unix(miliSeconds).format("MMM DD, YYYY");
    },
    async getPrioritySupportStatus(){
      try {
        let activePlan = await this.$http.post(`/api/stripe/is_priority_support`,
        {
          stripe_id: this.company ? this.company.stripeId : ''
        });
        if(activePlan.data && activePlan.data.plan_label){
          this.duration = activePlan.data.plan_label.split('_')[1];
        }
      } catch (e) {
        console.error(e);
      } finally {
        // this.loadingActivePlan = false;
      }
    }
  },
  created(){
    this.getPrioritySupportStatus();
  }
})
</script>
