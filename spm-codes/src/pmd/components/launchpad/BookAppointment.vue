<template>
  <div
    id="chat-connect"
    class="launchpad-item-sub-container launchpad-bottom-border bg-white my-1"
  >
    <div class="items-tiles mx-3 my-2">
      <BookAppointmentIcon
        class="integration-icon mx-4"
      />
      <div class="item-info">
        <div
          class="font-normal text-sm text-gray-500"
          v-if="isChatWidgetConnected"
        >
          Congratulations! You have successfully added HighLevel webchat in your
          website. Engage with your leads in Conversations page.
        </div>
        <div
          class="font-normal text-sm text-gray-500"
          v-else-if="!isChatWidgetConnected"
        >
          Book a call with our customer success team!
        </div>
      </div>
      <div class="flex flex-col justify-center item-end content-end w-40">
        <div
          class="flex flex-row justify-end item-end content-end w-full pr-4 pl-4"
        >
          <transition name="slide-fade" mode="out-in">
            <button
              v-if="!isChatWidgetConnected"
              class="green-filled-button green-btn connetGoogle"
            >
              <transition name="slide-fade" mode="out-in">
                <span :key="btnText">{{ btnText }}</span>
              </transition>
            </button>
          </transition>
          <transition name="slide-fade" mode="out-in">
            <button
              v-if="isChatWidgetConnected"
              class="btn btn-circle green-btn"
              style="
                border: 1px solid #38a169;
                color: rgba(56, 161, 105, 1);
                background: transparent;
                pointer-events: none;
              "
            >
              <i class="fa fa-check" aria-hidden="true"></i>
            </button>
          </transition>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import firebase from 'firebase/app'
import lodash from 'lodash'
import { Location, OAuth2, Calendar } from '@/models'
import config from '@/config'
import BookAppointmentIcon from '@/assets/pmd/img/launchpad/icon-book-appointment.svg'

export default Vue.extend({
  props: ['currentLocationId', 'userid'],
  components: {
    BookAppointmentIcon
  },
  data() {
    return {
      sending: false,
    }
  },
  computed: {
    isChatWidgetConnected(): boolean | undefined {
      return false
    },
    btnText(): string {
      return 'Book an Appointment'
    },
  },
  async mounted() {
    this.location = new Location(
      await this.$store.dispatch('locations/getById', this.currentLocationId)
    )
  },
  methods: {},
})
</script>
<style scopes>
.launchpad-item-sub-container {
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-content: start;
  background: #fff;
  width: 100%;
  min-height: 90px;
  position: relative;
}
.items-tiles {
  display: flex;
  flex-direction: row !important;
  justify-content: center;
  align-items: center;
}
.integration-icon {
  width: 3.5rem;
  height: 3.5rem;
}
.connect-icon {
  width: 1.5rem;
  height: 1.5rem;
}
.item-info {
  display: flex;
  flex-direction: row !important;
  justify-content: start;
  align-items: center;
  width: 450px;
}
.gmb-button-grp {
  margin-top: 20px;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  width: 560px;
}

.gmb-button-grp .yes-button {
  margin-left: 50px;
}
.gmb-mailer-grp {
  margin-top: 20px;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  width: 100%;
  justify-content: center;
  align-items: flex-start;
}
.gmb-mailer-from-group {
  text-align: initial;
  color: #1a202c;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 400px;
}
#gmb-connect .dropdown-toggle {
  background: #ffffff;
  border: 1px solid #cbd5e0;
  box-sizing: border-box;
  border-radius: 3px;
}
.gmb-input-label {
  font-family: Roboto;
  font-style: normal;
  font-weight: bold;
  font-size: 14px;
  line-height: 16px;
  text-align: start;
}
.gmb-form-error {
  border: 1px solid red;
}
.gmb-from-error-message {
  color: red;
  text-align: start;
  font-size: 12px;
}
.loader-postion {
  position: absolute;
}
.green-filled-button {
  margin-left: 0.75rem;
  display: inline-flex;
  justify-content: center;
  padding: 0.65rem 1.5rem;
  border-color: transparent;
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  font-size: 0.75rem;
  line-height: 1.25rem;
  font-weight: 700;
  border-radius: 3px;
  cursor: pointer;
  white-space: nowrap;
}
.green-filled-button:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.green-btn {
  color: #fff;
  background-color: #38a169;
}
.green-btn:hover {
  background-color: #38a169;
}
</style>
