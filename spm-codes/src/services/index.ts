import axios, { AxiosRequestConfig } from 'axios'
import config from '../config'
import PaymentServices from './PaymentServices'
import ProductServices from './ProductServices'
import FunnelsServices from './FunnelServices'
import EmailBuilderServices from './EmailBuilderService'
import firebase from 'firebase/app'


const getHeader = () => {
  return {
    channel: 'APP',
    source: 'WEB_USER',
    version: '2021-07-28'
  }
}


export const requests = (host?:string) => {
  const baseUrl = host || config.restApiUrl
  const axiosInstance = axios.create(
    {
      baseURL:baseUrl,
      headers: getHeader(),
    }
  )

  axiosInstance.interceptors.request.use(async (requestConfig: AxiosRequestConfig) => {
    try {
        requestConfig.headers['token-id'] = await firebase
          .auth()
          .currentUser.getIdToken()
    } catch (e) {}
    return requestConfig
  })

  return axiosInstance
}

const Conversations = {
  message: (messageBody) => requests().post('/conversations/message', messageBody)
}


const Contact = {
  getDeleted: (locationId: string, startAt?: string) =>
  requests().get(`/contacts/${locationId}/deleted/${startAt ? '?startAt=' + startAt : ''}`),
  deleteContacts: (contacts: string[]) =>
  requests().post(`/contacts/bulk/delete/`, { contacts }),
  restoreContacts: (contacts: string[]) =>
  requests().post(`/contacts/bulk/restore/`, { contacts }),
  update: (id: string, data: { [key: string]: any }) =>
  requests().put(`/contacts/${id}`, data),
  create: (data: { [key: string]: any }) => requests().post(`/contacts/`, data),
  getAudit: (id: string) => requests().get(`/contacts/${id}/audits`),
}

export default {
  Conversations,
  Contact
}

export {
  PaymentServices,
  ProductServices,
  FunnelsServices,
  EmailBuilderServices,
}

