<template>
  <div
    class="tab-pane fade show active"
    id="settings-tab"
    role="tabpanel"
    aria-labelledby="settings-tab"
  >
    <div class="hl_funnels--settings">
      <div class="hl_funnels--card row">
        <div class="col-lg-12 funnel-inner funnel-settings-grid">
          <div class="form-group">
            <UITextInputGroup
              v-model="funnelName"
              type="text"
              label="Name"
              :placeholder="`${entityName} Name`"
              @change="edited = true"
            />
          </div>
          <div class="form-group">
            <UITextLabel>Domain</UITextLabel>
            <select
              class="selectpicker"
              :title="`Select Domain for ${entityName}`"
              name="filterType"
              v-model="domainSelected"
              @change="edited = true"
            >
              <option
                v-for="domain in allDomains"
                :key="domain.id"
                :value="domain.id"
              >
                {{ domain.url }}
              </option>
            </select>
            <div class="hint-message">
              <div>
                <span>The Domain for the {{ entityName }} -</span>
                <router-link
                  tag="a"
                  :to="{
                    name: 'domain_settings',
                    params: { location_id: currentLocationId },
                  }"
                  >Add/Edit Domains</router-link
                >
              </div>
              <div
                v-if="domainSelected"
                @click="onClickRemoveDomain"
                class="remove-domain"
              >
                Remove
              </div>
            </div>
          </div>
          <div class="form-group">
            <UITextInputGroup
              v-model="funnelPath"
              type="text"
              label="Path"
              :placeholder="`${entityName} Path`"
              @change="edited = true"
            />
            <div v-if="funnelUrlError" class="hint-message">
              <span class="error-message">{{ funnelUrlError }}</span>
            </div>
          </div>
          <div class="form-group">
            <UITextInputGroup
              type="text"
              label="Favicon URL"
              v-model="faviconUrl"
              @change="edited = true"
            />
            <div class="hint-message">
              <span
                >Add a URL to your .png or .ico favicon image. For ALL your
                pages inside your {{ entityName }}.</span
              >
            </div>
          </div>
          <div class="form-group">
            <UITextAreaGroup
              type="text"
              label="Head tracking code"
              rows="2"
              max-rows="6"
              v-model="headTrackingCode"
              @change="edited = true"
            />
            <div class="hint-message">
              <span>{{ entityName }} wide tracking code for the head tag</span>
            </div>
          </div>
          <div class="form-group">
            <UITextAreaGroup
              type="text"
              label="Body tracking code"
              rows="2"
              max-rows="6"
              v-model="bodyTrackingCode"
              @change="edited = true"
            />
            <div class="hint-message">
              <span>{{ entityName }} wide tracking codes for the body tag</span>
            </div>
          </div>
          <div class="form-group" v-if="allowPaymentModeOption">
            <div class="block text-sm font-medium text-gray-700">Payment mode</div>
            <div class="flex w-full space-x-2 mt-1">
              <span> Test </span>
              <UIToggle :value="paymentMode"  @input="togglePaymentMode" />
              <span> Live </span>
            </div>
            <div class="hint-message">
              <span>Funnel payment status will be based on payment mode.</span>
            </div>
          </div>
          <div class="form-group" v-if="showChatWidgetOption && isEnabled">
            <div class="block text-sm font-medium text-gray-700">Chat widget</div>
            <div class="flex w-full space-x-2 mt-1">
              <span> Disabled </span>
              <UIToggle :value="chatWidget"  @input="toggleChatWidget" />
              <span> Enabled </span>
            </div>
          </div>
        </div>

        <div class="col-lg-12 funnel-settings-actions mt-4">
          <div class="error-message" v-if="errMsg">
            <i class="fa text-error fa-info-circle"></i> {{ errMsg }}
          </div>
          <hr />
          <div class="mt-4">
            <UIButton
              @click="askDeleteConfirmation"
              :disabled="saveButtonDisabled"
              use="danger"
            >
              {{ deleting ? 'Deleting...' : `Delete ${this.entityName}` }}
            </UIButton>
            <UIButton
              @click="checkIfDomainChanged"
              :disabled="saveButtonDisabled"
              use="primary"
            >
              {{ saving ? 'Saving...' : 'Save' }}
            </UIButton>
          </div>
        </div>
      </div>
    </div>
    <ConfirmDomainChangeModal
      :showModal="showDomainChangeModal"
      @showDomainChangeModal="(val) => showDomainChangeModal = val"
      @hidden="showDomainChangeModal = false"
      @change="stopSplitTest"
    />
    <ConfirmDomainRemoveModal
      :showModal="showDomainRemoveModal"
      @showDomainRemoveModal="(val) => showDomainRemoveModal = val"
      @hidden="showDomainRemoveModal = false"
      @remove="stopSplitTest"
    />
    <ConfirmDeleteModal
      :showModal="showDeleteModal"
      @showDeleteModal="val => (showDeleteModal = val)"
      @hidden="showDeleteModal = false"
      @delete="deleteFunnel"
    />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import defaults from '@/config'
import { Domain, FunnelLookup, Funnel, FunnelPage, Location } from '../../../models/'
import {
  getValidFunnelLookupUrl,
  updateDomainForAllFunnelLookups,
  getUniqueFunnelUrl,
  getFunnelUrl,
  checkIfUserIsSettingFunnelPathAsRoot, createFunnelLookups
} from '@/util/helper'
import ConfirmDeleteModal from '../../components/funnels/ConfirmDeleteModal.vue'
import ConfirmDomainChangeModal from '../../components/funnels/ConfirmDomainChangeModal.vue'
import ConfirmDomainRemoveModal from '../../components/funnels/ConfirmDomainRemoveModal.vue'
import {
  updateCachingIndex,
  cacheUpdateEvents,
  fetchDomainsAndPathsForFunnel,
} from '@/util/caching.helper'
import axios from 'axios'
import { FunnelsServices } from '../../../services'
declare const $: any
export default Vue.extend({
  components: {
    ConfirmDeleteModal,
    ConfirmDomainChangeModal,
    ConfirmDomainRemoveModal
  },
  props: {
    funnel: {
      type: Object,
    },
    isWebsite: {
      type: Boolean,
      default: false,
    },
  },
  async mounted() {
    this.currentLocationId = this.$route.params.location_id

    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }

    this.allDomains = await Domain.getAllByLocation(this.currentLocationId)
    this.location = await Location.getById(this.currentLocationId)

    const WidgetSettings =  this.location.settings.textwidget
    if(Object.keys(WidgetSettings).length!=0){
      this.showChatWidgetOption = true
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  computed: {
    saveButtonDisabled(): boolean {
      return (
        this.saving || !this.funnelName || !this.funnelPath || this.deleting
      )
    },
    entityName(): string {
      return this.isWebsite ? 'Website' : 'Funnel'
    },
    isEnabled(){
        return this.$store.getters['LevelUpDayFlag/isfeatursActive']
    }
  },
  data() {
    return {
      currentLocationId: '',
      funnelName: this.funnel.name,
      funnelPath: this.funnel.url,
      saving: false,
      allDomains: [] as Domain[],
      domainSelected: this.funnel.domainId || '',
      funnelUrlError: '',
      faviconUrl: this.funnel.favicon || '',
      headTrackingCode: this.funnel.trackingCodeHead || '',
      bodyTrackingCode: this.funnel.trackingCodeBody || '',
      edited: false,
      deleting: false,
      showDeleteModal: false,
      showDomainChangeModal: false,
      showDomainRemoveModal: false,
      errMsg: '',
      paymentMode: undefined as undefined | boolean,
      chatWidget: this.funnel.ischatWidgetLive || false,
      allowPaymentModeOption: false,
      showChatWidgetOption: false,
      location: {} as Location,
      domainChanged: false,
      domainRemoved: false
    }
  },
  methods: {
    onClickRemoveDomain() {
      this.domainSelected = ''
      this.edited = true
    },
    togglePaymentMode() {
      this.paymentMode = !this.paymentMode;
      this.edited = true;
    },
    toggleChatWidget() {
      this.chatWidget = !this.chatWidget;
      this.edited = true;
    },
    checkIfDomainChanged() {
      if (this.funnel?._data?.type==='website'){
        this.showDomainRemoveModal = false
        this.showDomainChangeModal = false
        this.updateFunnelSettings()
        return
      }
      if (this.domainChanged && this.funnel.domainId){
        this.showDomainChangeModal = true
        this.showDomainRemoveModal = false
      } else if (this.domainRemoved && this.funnel.domainId) {
        this.showDomainRemoveModal = true
        this.showDomainChangeModal = false
      } else {
        this.updateFunnelSettings()
      }
    },
    async stopSplitTest(){
      try {
        this.saving = true
        const params = {
          locationId: this.currentLocationId,
          funnelId: this.funnel.id,
          userId: this.$store.state.auth.user.userId
        }
        await FunnelsServices.stopAllSplitTestsAndReset(params)
        this.updateFunnelSettings()
      } catch (error) {
        console.error(error)
      }
    },
    async updateFunnelSettings() {
      const domain = this.allDomains.find(d => d.id === this.domainSelected)
      const domainName = domain ? domain.url : ''
      let domainGotUpdated = this.funnel.domainId !== this.domainSelected
      const attachingDomainForFirstTime = !this.funnel.domainId
      const domainAvailableWithFunnel = this.allDomains.find(
        d => d.id === this.funnel.domainId
      )
      let saveSuccessful = false
      this.saving = true
      try {
        if (this.edited) {
          if (!this.domainSelected && this.funnel.domainId) {
            await updateCachingIndex({
              index_type: cacheUpdateEvents.FUNNEL_SETTINGS,
              domains: [domainAvailableWithFunnel?.url],
              event_origin: this.currentLocationId,
            })
            await this.$http.delete(
              `/funnelbuilder/domain/${this.funnel.id}?updated_by=${this.$store.state.auth.user.userId}`
            )
            domainGotUpdated = false
          }
          const cleanedFunnelUrl = this.funnelPath.trim()

          if (
            cleanedFunnelUrl !== this.funnel.url &&
            checkIfUserIsSettingFunnelPathAsRoot(cleanedFunnelUrl)
          ) {
            this.saving = false
            return
          }

          if (cleanedFunnelUrl !== this.funnel.url || domainGotUpdated) {
            let funnelUrl = getFunnelUrl(cleanedFunnelUrl)
            // TAG: LOOKUPS
            if (domainName) {
              funnelUrl = await getUniqueFunnelUrl(domainName, funnelUrl)
            }
            this.funnelPath = funnelUrl
          }
          await this.updateFunnel()
        }

        if (domainGotUpdated) {
          await this.updateDomainForAllLookups(
            this.funnel.id,
            this.funnelPath,
            attachingDomainForFirstTime
          )
        } else if (domainName) {
          // TAG: LOOKUPS
          await this.updatePathForLookup(this.funnel.id, this.funnelPath)
        }

        if (domainAvailableWithFunnel) {
          const { domains, paths } = await fetchDomainsAndPathsForFunnel(
            this.funnel.id,
            this.currentLocationId
          )
          await updateCachingIndex({
            index_type: cacheUpdateEvents.FUNNEL_SETTINGS,
            domains,
            paths,
            event_origin: this.currentLocationId,
          })
        }

        saveSuccessful = true

        this.saving = false

        if (saveSuccessful) {
          // redirect to funnel steps page
          this.$router.push({
            name: this.isWebsite ? 'website_pages' : 'funnel_steps',
            params: {
              funnel_id: this.funnel.id,
              location_id: this.currentLocationId,
            },
          })
        }
      } catch (error) {
        console.error(error)
        this.errMsg = `We're sorry, we ran into a problem while updating funnel settings. Please try again. If you continue to experience the problem, please reach out to our support.`
        saveSuccessful = true
        this.saving = false
      }
    },
    async deleteFunnel() {
      this.showDeleteModal = false
      if (this.funnel) {
        const funnelId = this.$route.params.funnel_id
        this.deleting = true

        try {
          await this.$http.delete(`/funnelbuilder/delete-funnel/${funnelId}`)
          this.deleting = false

          this.$router.push({
            name: this.isWebsite ? 'websites_funnels' : 'all_funnels',
            params: {
              location_id: this.currentLocationId,
            },
          })
        } catch (error) {
          this.deleting = false
          alert('Something went wrong while deleting funnel!')
          console.error('Error while deleting funnel --> ', error)
        }
      }
    },
    async updateFunnel() {
      const funnel = await Funnel.getById(this.funnel.id)
      funnel.name = this.funnelName
      funnel.url = this.funnelPath
      funnel.domainId = this.domainSelected
      funnel.favicon = this.faviconUrl
      funnel.trackingCodeHead = this.headTrackingCode
      funnel.trackingCodeBody = this.bodyTrackingCode
      if (this.allowPaymentModeOption && this.paymentMode !== undefined) funnel.isLivePaymentMode = this.paymentMode
      funnel.ischatWidgetLive = this.chatWidget
      await funnel.save()
      await updateCachingIndex({
        index_type: cacheUpdateEvents.STRIPE_SETTINGS,
        event_origin: this.currentLocationId
      })
    },
    async updatePathForLookup(funnelId: string, funnelUrl: string) {
      try {
        const lookup = await FunnelLookup.getByTypeId(
          funnelId,
          this.currentLocationId
        )
        lookup.path = getValidFunnelLookupUrl(funnelUrl)
        await lookup.save()
        console.log('updated lookup path')
      } catch (error) {
        console.error('error while updating path for lookup:', error)
      }
    },
    async updateDomainForAllLookups(
      funnelId: string,
      funnelUrl: string,
      createNewLookups: boolean
    ) {
      try {
        if(createNewLookups){
          const domain = this.allDomains.find(x => x.id === this.domainSelected);
          const funnelPages = await FunnelPage.getByFunnelId(this.funnel.id, this.currentLocationId)
          await createFunnelLookups({funnel: this.funnel, domain, funnelPages})
        }

        const { data: { pathsUpdated } } = await axios.post(
          `${defaults.funnelServiceBaseUrl}/api/funnel/${this.funnel.id}/lookups`,
          {
            funnelPath: funnelUrl,
            domainId: this.domainSelected
          },
          {
            timeout: 600000
          }
        )
        console.log('pathsUpdated:', pathsUpdated)
        if(pathsUpdated) {
          alert(
            'Some of the URLs in the funnel might have been updated due to duplicate urls.'
          )
        }
      } catch (error) {
        console.error('error while updating lookup --> ', error)
      }
    },
    askDeleteConfirmation() {
      this.showDeleteModal = true
    }
  },
  watch: {
    funnelPath() {
      this.funnelUrlError = ''
    },
    domainSelected(newDomain) {
      const current = this.funnel.domainId
      if (newDomain!==current && current && newDomain) {
        this.domainChanged = true
        this.domainRemoved = false
      } else if (!newDomain && current && newDomain!==current) {
        this.domainRemoved = true
        this.domainChanged = false
      } else if (newDomain===current){
        this.domainRemoved = false
        this.domainChanged = false
      }
    },
    async currentLocationId() {
      if (this.currentLocationId) {
        let isLivePaymentMode;
        const funnelPaymentMode = this.funnel.isLivePaymentMode;
        if (funnelPaymentMode !== undefined) {
          isLivePaymentMode = funnelPaymentMode
          this.allowPaymentModeOption = true
        } else {
          let location = await this.$store.dispatch('locations/getById', this.currentLocationId);
          if (!location) location = await Location.getById(this.currentLocationId)
          const currentLocation = new Location(location);
          this.allowPaymentModeOption = !currentLocation.stripe?.publishable_key;
          isLivePaymentMode = currentLocation.stripeConnectMode === 'live';
        }

        if (this.allowPaymentModeOption) {
          this.funnel.isLivePaymentMode = isLivePaymentMode;
          this.paymentMode = isLivePaymentMode;
        }
      }
    },
  },
})
</script>
<style lang="scss">
.hl_funnels--settings {
  .hl_funnels--card {
    padding: 43px 0;
    .funnel-inner {
      margin: auto;
      .hint-message {
        margin: 6px 0;
        font-size: 12px;
        display: flex;
        justify-content: space-between;
        .remove-domain {
          color: var(--danger);
          cursor: pointer;
          &:hover {
            color: rgba(230, 61, 61, 0.8);
          }
        }
      }
      .error-message {
        color: rgb(230, 114, 114);
      }
    }
    .funnel-settings-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-column-gap: 50px;
      padding: 0 45px;
    }
    .funnel-settings-actions {
      text-align: right;
      button {
        margin-left: 20px;
      }
    }
    .error-message {
      color: rgb(230, 114, 114);
      font-size: 12px;
    }
  }
}
</style>
