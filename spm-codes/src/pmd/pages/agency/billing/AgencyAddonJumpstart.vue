<template>
  <div class="hl_billing--account">
    <div class="card --addons">
      <div class="card-body">
        <h3 class="agency-plan__heading">Agency Jumpstart</h3>
        <p class="agency-plan__description">Subscribed Till: <b>{{formateDate(expiry)}}</b></p>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import * as moment from 'moment-timezone';

export default Vue.extend({
  props:['expiry'],
  methods:{
    formateDate(miliSeconds: number){
      return moment.unix(miliSeconds).format("MMM DD, YYYY");
    },
  }
})
</script>
