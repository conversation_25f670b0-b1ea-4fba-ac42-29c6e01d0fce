<template>
  <notifications :group="groupName"
                 animation-type="velocity"
                 closeOnClick
                 position="bottom right">
    <template slot="body" slot-scope="props">
      <div class="notif-container">
      <div class="notif-box opacity-eight notif-left-border" :class="{'notif-green':props.item.type === 'success', 'notif-white':  props.item.type === 'info' , 'notif-orange':props.item.type === 'warning', 'notif-red':props.item.type === 'error' }" ></div>
      <div class="notif-content line-height-one mx-2 " :class="{'pointer': props.item.data  && props.item.data.callbackWithInfo }" @click="close(props.close, props.item.data)">
        <div v-if="props.item.title || props.item.data" class="d-flex justify-content-between --white mx-2 mt-2 mb-0" :class="{'--gray': props.item.type === 'info' }" >
          <span class="notif-header">{{props.item.title}}</span>
          <i v-if="props.item.data && props.item.data.callbackWithInfo" class="fas fa-link"></i>
        </div>
        <div class="--white mx-2 my-2 py-2 px-2" :class="{'--gray': props.item.type === 'info'}" >{{props.item.text}}</div>
      </div>
      </div>
    </template>
  </notifications>
</template>

<script lang="ts">
import Vue from 'vue'
import * as Ux from '@/util/ux_message'

export default Vue.extend({
  data() {
    return {
      groupName: 'bottom-bubble',
      options: null as any | null,
    }
  },
  computed: {
    error() {
      return Ux.UxMessageType.ERROR
    },
    warning() {
      return Ux.UxMessageType.WARNING
    },
    info() {
      return Ux.UxMessageType.INFO
    },
     confirmation() {
      return Ux.UxMessageType.CONFIRMATION
    }
  },
  methods: {
    show(uxMessage: Ux.UxMessage) {
      const temp = { text: uxMessage.stack, ...uxMessage.options};
      this.$notify({ group: this.groupName,
          title: uxMessage.message,
          type : uxMessage.type,
          duration:6000, // put -1 for testing
          text: uxMessage.stack,
          data: uxMessage.options
      });
    },
    clearAll() {
      this.$notify({ group: this.groupName, clean: true})
    },
    close(itemClose: any, options: any){
     if(options && options.callbackWithInfo) options.callbackWithInfo(options.returnInfo);
     if(itemClose) itemClose();
    }
  }
})
</script>

<style>
    .line-height-one{
      line-height: 1;
    }
    .notif-header{
      letter-spacing: 1px;
      font-size: 17px;
    }
    .--white{
      color: white
    }
    .opactity-five {
      opacity: 0.5;
    }
    .opactity-eight {
      opacity: 0.8;
    }
    .opactity-none{
      opacity: 1;
    }
    .notif-border{
      border: 1px solid #dee2e6 !important;
    }
    .notif-left-border {
      border: 0px;
      border-style:solid;
      border-left-width:5px;
    }
    .notif-orange { background:#ffb648; border-left-color:#f48a06 }
    .notif-red { background:#e54d42; border-left-color:#b82e24 }
    .notif-green { background:#68cd86; border-left-color:#42a85f }
    .notif-white { background:white; border-left-color:rgb(184, 180, 180);}

    .notif-container{
        max-width: 300px;
        min-width: 150px;
        min-height: 50px;
        margin:10px;
        position: relative;
    }
    .notif-box{
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
    }
    .notif-content{
        padding: 5px;
        position: relative;
    }
</style>
