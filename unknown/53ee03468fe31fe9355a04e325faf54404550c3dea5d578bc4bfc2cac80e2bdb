<template>
  <div class="new-event-day-item">
    <div class="calendar-day">
      <div class="calendar-day-inner border shadow" v-if="when === 'after'">
        <p>{{ delayValue }}</p>
        <h5>{{ singular(capitalize(delayType)) }}</h5>
      </div>
      <div class="calendar-day-inner" v-else-if="when === 'before'">
        <p>-{{ delayValue }}</p>
        <h5>{{ singular(capitalize(delayType)) }}</h5>
      </div>
      <div class="calendar-day-inner" v-else>
        <p>!!</p>
        <!-- <h5>{{singular(capitalize(delayType))}}</h5> -->
      </div>
    </div>
    <div class="event-card border shadow rounded" style="cursor: default">
      <div class="event-card--top">
        <div class="row event-card--title">
          <div class="col title">
            <h5 class="align-middle">
              <i class="action-icon align-middle" :class="icon"></i>
              <div class="align-middle d-inline-block">{{ template.name }}</div>
              <div
                class="align-middle d-inline-block px-2"
                v-if="template.attributes && template.attributes.template_id"
              >
                <span class="action-type-tag">Template</span>
              </div>
              <div
                class="align-middle d-inline-block px-2"
                v-if="
                  template.type === 'manual-sms' ||
                  template.type === 'manual-call'
                "
              >
                <span class="action-type-tag">Manual</span>
              </div>
            </h5>
            <div
              style="margin-top: 20px"
              v-if="
                template.type === 'voicemail' && template.attributes.file_url
              "
            >
              <audio-player :file="template.attributes.file_url"></audio-player>
            </div>
            <p v-else>{{ description }}</p>
            <p>
              <i>{{ runtime }}</i>
            </p>
            <p v-if="count > 0">
              <b>
                <i>{{ count }} {{ count > 1 ? 'people' : 'person' }} queued</i>
              </b>
            </p>
          </div>
          <div
            v-if="eventStats && eventStats.count > 0"
            class="col-7 d-flex justify-content-between"
          >
            <div class="email-stats__single">
              <span
                style="font-size: 1.2rem; color: black"
                class="stats-clickable"
                @click="openSmsStatsDetailModal('delivered')"
                >{{
                  getPercentage(eventStats.delivered, eventStats.count) + '%'
                }}</span
              >
              <span>Delivered</span>
            </div>
            <div class="d-flex">
              <div class="email-stats__single">
                <span
                  style="font-size: 1.2rem; color: black"
                  class="stats-clickable"
                  @click="openSmsStatsDetailModal('clicked')"
                  >{{
                    getPercentage(eventStats.clicked, eventStats.count) + '%'
                  }}</span
                >
                <span>Clicked</span>
              </div>
              <div class="ml-2 mt-1">
                <span
                  style="margin-top: -7px"
                  class="input-group-addon"
                  v-b-tooltip.hover
                  title="trigger links (embedded in message) clicked as % of messages sent"
                >
                  <i class="fas fa-question-circle"></i>
                </span>
              </div>
            </div>
            <div class="email-stats__single">
              <span
                style="font-size: 1.2rem; color: black"
                class="stats-clickable"
                @click="openSmsStatsDetailModal('failed')"
                >{{
                  getPercentage(eventStats.failed, eventStats.count) + '%'
                }}</span
              >
              <span>Failed</span>
            </div>
          </div>
          <div
            v-else-if="template.type === 'email' && emailStats.count > 0"
            class="col-7 d-flex justify-content-between"
          >
            <div class="email-stats__single">
              <span
                style="font-size: 1.2rem; color: black"
                class="stats-clickable"
                @click="openEmailStatsDetailModal('delivered', true)"
                >{{
                  haveSMTP
                    ? 'NA'
                    : getPercentage(emailStats.delivered, emailStats.count) +
                      '%'
                }}</span
              >
              <span>Delivered</span>
            </div>
            <div class="email-stats__single">
              <span
                style="font-size: 1.2rem; color: black"
                class="stats-clickable"
                @click="openEmailStatsDetailModal('permanent_fail', true)"
                >{{
                  haveSMTP
                    ? 'NA'
                    : getPercentage(
                        emailStats.permanent_fail,
                        emailStats.count
                      ) + '%'
                }}</span
              >
              <span>Bounced</span>
            </div>
            <div class="email-stats__single">
              <span
                style="font-size: 1.2rem; color: black"
                class="stats-clickable"
                @click="openEmailStatsDetailModal('opened', true)"
                >{{
                  getPercentage(emailStats.opened, emailStats.count) + '%'
                }}</span
              >
              <span>Opened</span>
            </div>
            <div class="d-flex">
              <div class="email-stats__single">
                <span
                  style="font-size: 1.2rem; color: black"
                  class="stats-clickable"
                  @click="openEmailStatsDetailModal('clicked', true)"
                  >{{
                    getPercentage(emailStats.clicked, emailStats.count) + '%'
                  }}</span
                >
                <span>Clicked</span>
              </div>
              <div class="ml-2 mt-1">
                <span
                  style="margin-top: -7px"
                  class="input-group-addon"
                  v-b-tooltip.hover
                  :title="
                    haveSMTP
                      ? `NA - Some stats are not avaiable for SMTP.\n Accuracy of metrics depend upon receiver's domain.`
                      : `Accuracy of metrics depend upon receiver's domain.`
                  "
                >
                  <i class="fas fa-question-circle"></i>
                </span>
              </div>
            </div>
          </div>
          <!-- <div v-else class="col-7 d-flex justify-content-end">
            <div class="email-stats__single">
              <span v-if="isLoadingStats === true" class="--blue">Fetching Stats........</span>
              <span v-else style="font-size: 1.2rem; color: black">No Stats Available</span>
            </div>
          </div> -->
        </div>
      </div>
      <div v-if="showMoreDetail" class="more-info more-detail-animation">
        <template v-if="eventStats && eventStats.count > 0">
          <div class="more-info-data text-right">
            <h5
              class="stats-clickable"
              @click="openSmsStatsDetailModal('total')"
            >
              {{ eventStats.count | formatNumberNoDecimal }}
            </h5>
            <p class="--dark">Total</p>
          </div>
          <div class="more-info-data text-right">
            <h5
              class="--green stats-clickable"
              @click="openSmsStatsDetailModal('delivered')"
            >
              <span>{{ eventStats.delivered | formatNumberNoDecimal }}</span>
            </h5>
            <p class="--dark">Delivered</p>
          </div>
          <div class="more-info-data text-right">
            <h5
              class="--green stats-clickable"
              @click="openSmsStatsDetailModal('clicked')"
            >
              {{ eventStats.clicked | formatNumberNoDecimal }}
            </h5>
            <p class="--dark">Clicked</p>
          </div>
          <div class="more-info-data text-right">
            <h5 class="--yellow">
              <span
                class="stats-clickable"
                @click="openSmsStatsDetailModal('failed')"
                >{{ eventStats.failed | formatNumberNoDecimal }}</span
              >
            </h5>
            <p class="--dark">Failed</p>
          </div>
        </template>
        <template v-else-if="emailStats && emailStats.count > 0">
          <div class="more-info-data ml-3">
            <h5
              class="stats-clickable"
              @click="openEmailStatsDetailModal('total')"
            >
              {{ emailStats.count | formatNumberNoDecimal }}
            </h5>
            <p class="--dark">Total</p>
          </div>
          <div class="more-info-data">
            <h5 class="--green">
              <span v-if="haveSMTP">NA</span>
              <span
                class="stats-clickable"
                @click="openEmailStatsDetailModal('delivered')"
                v-else
                >{{ emailStats.delivered | formatNumberNoDecimal }}</span
              >
            </h5>
            <p class="--dark">Delivered</p>
          </div>
          <div class="more-info-data">
            <h5
              @click="openEmailStatsDetailModal('opened')"
              class="--green stats-clickable"
            >
              {{ emailStats.opened | formatNumberNoDecimal }}
            </h5>
            <p class="--dark">Opened</p>
          </div>
          <div class="more-info-data">
            <h5
              @click="openEmailStatsDetailModal('clicked')"
              class="--green stats-clickable"
            >
              {{ emailStats.clicked | formatNumberNoDecimal }}
            </h5>
            <p class="--dark">Clicked</p>
          </div>
          <div class="more-info-data">
            <h5
              @click="openEmailStatsDetailModal('replied')"
              class="--green stats-clickable"
            >
              {{ emailStats.replied | formatNumberNoDecimal }}
            </h5>
            <p class="--dark">Replied</p>
          </div>
          <div class="more-info-data">
            <h5 class="--yellow">
              <span v-if="haveSMTP">NA</span>
              <span
                class="stats-clickable"
                @click="openEmailStatsDetailModal('permanent_fail')"
                v-else
                >{{ emailStats.permanent_fail | formatNumberNoDecimal }}</span
              >
            </h5>
            <p class="--dark">Bounced</p>
          </div>
          <div class="more-info-data">
            <h5 class="--red">
              <span v-if="haveSMTP">NA</span>
              <span
                class="stats-clickable"
                @click="openEmailStatsDetailModal('unsubscribed')"
                v-else
                >{{ emailStats.unsubscribed | formatNumberNoDecimal }}</span
              >
            </h5>
            <p class="--dark">Unsubscribed</p>
          </div>
        </template>
      </div>
      <div class="event-card--bottom">
        <ul
          class="actions"
          v-if="user && user.permissions.campaigns_read_only != true"
        >
          <li>
            <a
              v-if="isEditable"
              @click="$emit('edit', template.id)"
              href="javascript:void(0);"
              style="padding: 4px"
              >Edit</a
            >
          </li>
          <li v-if="count === 0">
            <a
              @click="$emit('remove', template.id)"
              href="javascript:void(0);"
              style="padding: 4px"
              >Remove</a
            >
          </li>
          <!-- <li>
            <a v-on:click.stop.prevent="loadStats"
              href="javascript:void(0);"
              style="padding: 4px;">Refresh Stats</a>
          </li> -->
        </ul>
        <div style="width: 100%" class="d-flex justify-content-end items-center">
          <div
          class="flex align-items-center actions"
            v-if="
              template.type !== 'wait' &&
              user &&
              user.permissions.campaigns_read_only != true
            "
          >
          <span class="mr-2 text-gray-700 font-semibold">
            Send
          </span>
            <select v-model="when" @change="update" class="focus:ring-curious-blue-500 focus:border-curious-blue-500 rounded border border-gray-300">
              <option value="now">immediately</option>
              <option value="before">before</option>
              <option value="after">after</option>
            </select>
            <input
              class="delay-value focus:ring-curious-blue-500 focus:border-curious-blue-500 rounded border border-gray-300"
              type="text"
              v-model.number="delayValue"
              @change="update"
              v-if="when !== 'now'"
            />
            <select
              v-model="delayType"
              @change="update"
              v-if="when !== 'now'"
              class="focus:ring-curious-blue-500 focus:border-curious-blue-500 rounded border border-gray-300 "
            >
              <option value="minutes">{{ singular('minutes') }}</option>
              <option value="hours">{{ singular('hours') }}</option>
              <option value="days">{{ singular('days') }}</option>
            </select>
          </div>
          <div
            class="--blue ml-3"
            v-if="hasDetails"
            @click="openShowDetailCard"
          >
            <span v-if="!showMoreDetail" class="pointer zoomable"
              >Show Detail</span
            >
            <span v-else class="pointer zoomable">Hide Detail</span>
          </div>
        </div>
      </div>
    </div>
    <EmailStatsDetailModal
      v-if="showEmailStatsDetailModal"
      @closeEmailStatsDetailModal="
        showEmailStatsDetailModal = !showEmailStatsDetailModal
      "
      :option="emailStatsDetailModalActiveTab"
      :modalTitle="template.name"
      :campaign_id="campaignId"
      :campaign_step_id="template.id"
      :selectedDate="selectedDate"
      :haveSMTP="haveSMTP"
    />
    <SmsStatsDetailModal
      v-if="showSmsStatsDetailModal"
      @closeSmsStatsDetailModal="
        showSmsStatsDetailModal = !showSmsStatsDetailModal
      "
      :option="smsStatsDetailModalActiveTab"
      :modalTitle="template.name"
      :campaign_id="campaignId"
      :campaign_step_id="template.id"
      :selectedDate="selectedDate"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import * as moment from 'moment-timezone'
import { UserState } from '../../../store/state_models'
import { User, CampaignStatus } from '@/models/'
const AudioPlayer = () => import('../AudioPlayer.vue')
import axios from 'axios'
import defaults from '@/config'
import * as lodash from 'lodash'
import { parse } from 'handlebars'
const EmailStatsDetailModal = () => import('./../EmailStatsDetailModal')
const SmsStatsDetailModal = () => import('./../SmsStatsDetailModal')
import { trackGaEvent } from '@/util/helper'
import { IEventStats } from '../../../models/evenstats'

export default Vue.extend({
  props: ['template', 'campaignId', 'bus', 'selectedDate'],
  components: { AudioPlayer, EmailStatsDetailModal, SmsStatsDetailModal },
  data() {
    return {
      delayValue: 0,
      delayType: 'minutes',
      when: 'after',
      count: 0,
      emailStats: {
        count: 0,
        opened: 0,
        clicked: 0,
        delivered: 0,
        complained: 0,
        permanent_fail: 0,
        unsubscribed: 0,
        replied: 0,
      },
      eventStats: null as IEventStats | null,
      haveSMTP: false,
      showMoreDetail: false,
      baseUrl: defaults.emailReportingUrl, //'https://staging.services.msgsndr.com'
      agentReportingUrl: `${defaults.emailReportingUrl}/agent_reporting`,
      isLoadingStats: false,
      showEmailStatsDetailModal: false,
      emailStatsDetailModalActiveTab: '',
      showSmsStatsDetailModal: false,
      smsStatsDetailModalActiveTab: '',
    }
  },
  watch: {
    template: {
      handler(template: { [key: string]: any }) {
        if (this.template && this.template.start_after) {
          this.delayValue = this.template.start_after.value
            ? this.template.start_after.value
            : 0
          this.delayType = this.template.start_after.type
            ? this.template.start_after.type
            : 'minutes'
          this.when = this.template.start_after.when
            ? this.template.start_after.when
            : 'after'
          this.fetchCount()
        }
      },
      deep: true,
    },
    selectedDate: function () {
      this.loadStats()
    },
  },
  created() {
    if (this.template && this.template.start_after) {
      this.delayValue = this.template.start_after.value
        ? this.template.start_after.value
        : 0
      this.delayType = this.template.start_after.type
        ? this.template.start_after.type
        : 'minutes'
      this.when = this.template.start_after.when
        ? this.template.start_after.when
        : 'after'
      this.fetchCount()
      this.loadStats()
    }
  },
  computed: {
    hasDetails(): boolean {
      return (
        (this.emailStats && this.emailStats.count > 0) ||
        (this.eventStats && this.eventStats.count > 0)
      )
    },
    isEditable(): boolean {
      return this.template.type !== 'manual-call'
    },
    description(): string | undefined {
      const type = this.template.type
      if (type === 'sms' || type === 'manual-sms' || type === 'messenger')
        return this.template.attributes.body
      else if (type === 'email') return this.template.attributes.subject
      else if (type === 'webhook') return this.template.attributes.url
    },
    runtime(): string | undefined {
      if (!this.template || lodash.isEmpty(this.template.window)) return

      let response = 'Send '

      if (this.template.window.condition === 'if') {
        response += ' if the time now is '
      } else if (this.template.window.condition === 'when') {
        response += ' when the time is '
      }

      if (
        this.template.window.days &&
        this.template.window.days.length > 0 &&
        this.template.window.days.length < 7
      ) {
        response +=
          ' ' +
          this.template.window.days
            .map((day: number) => {
              return moment().isoWeekday(day).format('ddd')
            })
            .join(', ')
      }

      if (['if', 'when'].indexOf(this.template.window.condition) !== -1) {
        response +=
          ' between ' +
          moment(this.template.window.start, ['HH:mm']).format('h:mm A') +
          ' and ' +
          moment(this.template.window.end, ['HH:mm']).format('h:mm A')
      } else if (this.template.window.condition === 'wait') {
        response +=
          ' at ' +
          moment(this.template.window.start, ['HH:mm']).format('h:mm A')
      }

      return response
    },
    icon(): string | undefined {
      switch (this.template.type) {
        case 'messenger':
          return 'fab fa-facebook-messenger'
        case 'sms':
          return 'icon icon-smartphone'
        case 'email':
          return 'icon icon-mail'
        case 'snapshot':
          return 'icon-document-plus'
        case 'voicemail':
          return 'icon icon-mic'
        case 'call':
          return 'fas fa-phone'
        case 'wait':
          return 'icon icon-clock'
        case 'manual-sms':
          return 'icon icon-smartphone'
        case 'manual-call':
          return 'fas fa-phone'
        case 'webhook':
          return 'fas fa-satellite-dish'
        case 'task-notification':
          return 'fas fa-tasks'
        case 'gmb':
          return 'icon icon-google'
      }
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
  },
  methods: {
    openShowDetailCard() {
      if (this.showMoreDetail) {
        this.showMoreDetail = false
        trackGaEvent(
          'CampaignEmailStats',
          this.$store.state.company.company.id,
          'Campaign Stats Hide Detail',
          1
        )
      } else {
        this.showMoreDetail = true
        trackGaEvent(
          'CampaignEmailStats',
          this.$store.state.company.company.id,
          'Campaign Stats Show Detail',
          1
        )
      }
    },
    async fetchStepId() {
      const campStatus = await CampaignStatus.getByCampaignId()
    },
    async fetchCount() {
      // try {
      // 	const resp = await this.$http.get(`/aggregate/campaign/${this.campaignId}/campaign_status/${this.template.id}/count?cache_buster=${new Date().getTime()}`);
      // 	this.count = resp.data.count;
      // } catch (err) {
      // 	this.count = -1;
      // 	console.error(err);
      // }
    },
    openEmailStatsDetailModal(
      activeTab: string,
      isPerecentValueClicked: boolean
    ) {
      this.emailStatsDetailModalActiveTab = activeTab
      this.showEmailStatsDetailModal = true
      if (isPerecentValueClicked) {
        trackGaEvent(
          'CampaignEmailStats',
          this.$store.state.company.company.id,
          `${activeTab} percentage clicked`,
          1
        )
      } else {
        trackGaEvent(
          'CampaignEmailStats',
          this.$store.state.company.company.id,
          `${activeTab} value clicked`,
          1
        )
      }
    },
    openSmsStatsDetailModal(activeTab: string) {
      this.smsStatsDetailModalActiveTab = activeTab
      this.showSmsStatsDetailModal = true
    },
    getPercentage(value: number, total: number) {
      return value ? ((value / total) * 100).toFixed(2) : 0
    },
    async loadStats() {
      try {
        this.eventStats = null
        this.emailStats = {
          count: 0,
          opened: 0,
          clicked: 0,
          delivered: 0,
          complained: 0,
          permanent_fail: 0,
          unsubscribed: 0,
          replied: 0,
        }
        this.isLoadingStats = true
        //if (!this.showMoreDetail) this.showMoreDetail = false
        if (
          this.template.type === 'sms' ||
          this.template.type === 'manual-sms' ||
          this.template.type === 'manual_sms'
        ) {
          this.eventStats = await this.getSMSStats()
        } else if (this.template.type === 'email') {
          let start = this.selectedDate.start
          let end = this.selectedDate.end

          if (typeof start === 'string' || start instanceof String) {
            start = start.replace(/-/g, '/') // for safari -- it doesn't accept YYYY-MM-DD
          }

          if (typeof end === 'string' || end instanceof String) {
            end = end.replace(/-/g, '/')
          }

          let stats = await axios.get(
            `${this.baseUrl}/email_reporting/stats_by_campaign_step_id/${
              this.$route.params.location_id
            }/${this.campaignId}/${this.template.id}/${new Date(
              start
            ).toISOString()}/${new Date(end).toISOString()}`
          )
          //console.table(stats.data.response);
          if (stats.status !== 200) {
            return 'error'
          }
          for (let row of stats.data.response) {
            //console.table(row);
            if (row['provider'] !== 'smtp') {
              this.emailStats['count'] =
                this.emailStats['count'] + parseInt(row['count'])
              this.emailStats['opened'] =
                this.emailStats['opened'] + parseInt(row['opened'])
              this.emailStats['clicked'] =
                this.emailStats['clicked'] + parseInt(row['clicked'])
              this.emailStats['complained'] =
                this.emailStats['complained'] + parseInt(row['complained'])
              this.emailStats['unsubscribed'] =
                this.emailStats['unsubscribed'] + parseInt(row['unsubscribed'])
              this.emailStats['delivered'] =
                this.emailStats['delivered'] + parseInt(row['delivered'])
              this.emailStats['permanent_fail'] =
                this.emailStats['permanent_fail'] +
                parseInt(row['permanent_fail'])
              this.emailStats['replied'] =
                this.emailStats['replied'] + parseInt(row['replied'])
            } else {
              this.haveSMTP = true
              this.emailStats['count'] =
                this.emailStats['count'] + parseInt(row['count'])
              this.emailStats['opened'] =
                this.emailStats['opened'] + parseInt(row['opened'])
              this.emailStats['clicked'] =
                this.emailStats['clicked'] + parseInt(row['clicked'])
              this.emailStats['complained'] =
                this.emailStats['complained'] + parseInt(row['complained'])
              this.emailStats['unsubscribed'] =
                this.emailStats['unsubscribed'] + parseInt(row['unsubscribed'])
              this.emailStats['replied'] =
                this.emailStats['replied'] + parseInt(row['replied'])
            }
          }
        }
      } catch (err) {
        console.error(err)
      } finally {
        this.isLoadingStats = false
      }
    },
    async getSMSStats(): Promise<IEventStats | null> {

      let start = this.selectedDate.start
      let end = this.selectedDate.end

      if (typeof start === 'string' || start instanceof String) {
        start = start.replace(/-/g, '/') // for safari -- it doesn't accept YYYY-MM-DD
      }

      if (typeof end === 'string' || end instanceof String) {
        end = end.replace(/-/g, '/')
      }

      try {
        let stats = await axios.post(`${this.agentReportingUrl}/sms_stats`, {
          location_id: this.$route.params.location_id,
          campaign_id: this.campaignId,
          campaign_step_id: this.template.id,
          from_date: new Date(start).toISOString(),
          to_date: new Date(end).toISOString(),
        })
        if (stats.status !== 200) {
          console.log(
            `No sms stats for loc:${this.$route.params.location_id} camp: ${this.campaignId} template:${this.template.id}`
          )
          return null
        }
        console.log(stats)
        const returnStats = {
          isSMSStats: true,
          count: 0,
          delivered: 0,
          clicked: 0,
          failed: 0,
        } as IEventStats
        const data = (stats && stats.data && stats.data.response) || []
        for (let row of data) {
          if (row.count) returnStats.count += parseInt(row.count)
          if (row.clicked) returnStats.clicked += parseInt(row.clicked)
          if (row.delivered) returnStats.delivered += parseInt(row.delivered)
          if (row.failed) returnStats.failed += parseInt(row.failed)
        }
        console.log(returnStats)
        return returnStats
      } catch (error) {
        console.log(
          `No sms stats for loc:${this.$route.params.location_id} camp: ${this.campaignId} template:${this.template.id}`
        )
        console.log(error)
        return null
      }
    },
    singular(type: string) {
      return this.delayValue === 1 ? type.substring(0, type.length - 1) : type
    },
    capitalize(value: string) {
      return lodash.capitalize(value)
    },
    update() {
      this.$emit('update', this.template.id, {
        when: this.when,
        type: this.delayType,
        value: this.delayValue,
      })
    },
  },
  mounted() {
    this.bus.$on('refresh_count', () => {
      this.fetchCount()
    })
    this.$root.$on('refresh_campaign_stats', (cid: string) => {
      if (cid === this.campaignId) this.loadStats()
    })
  },
  beforeDestroy() {
    this.bus.$off('refresh_count')
    this.$root.$off('refresh_campaign_stats')
  },
})
</script>

<style scoped>
.fa-phone {
  color: #188bf6;
  margin-right: 10px;
  font-size: 20px;
}
.email-stats__single {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 1rem;
  color: #2a3135;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}

.more-detail-animation {
  max-height: 87px;
  overflow: hidden;
  transition: max-height 0.5s ease-out;
}

.stats-clickable {
  cursor: pointer;
}
</style>
