<template>
    <modal v-if="show" @close="$emit('close')" maxWidth="900" :showCloseIcon="true">
        <div >
            <div class="modal-heading-wrap">
                <div class="modal-heading-content">
                    <h4>Attach Snapshot</h4>
                    <p>Selected snapshot will be automatically added to the newly created SaaS Location</p>
                </div>
            </div>
            <div v-if="loading" style="padding: 30px;">
              <upgrade-modal-shimmer/>
            </div>
            <div class="modal-body --wide" v-else>
              <div class="hl_new-variation--header">
                <ul class="filter">
                    <li :class="snapshotTab === 'own'? 'active':''" @click.prevent="snapshotTab = 'own'">
                        <a href="#">Own Snapshots ({{ownTemplates.length}})</a>
                    </li>
                    <li :class="snapshotTab === 'imported'? 'active':''" @click.prevent="snapshotTab = 'imported'">
                        <a href="#">Imported Snapshots ({{importedTemplates.length}})</a>
                    </li>
                </ul>
              </div>
              <div class="hl_template-own" v-if="snapshotTab === 'own'">
                <div class="hl_template-own--tuple " v-for="snapshot in ownTemplates" :key="snapshot.id">
                    <div class="hl_template-own--info">
                        <div class="hl_template-own--avtar" >{{getAvtar(snapshot.name)}}</div>
                        <div class="hl_template-own--name" >{{snapshot.name}}</div>
                    </div>
                    <div class="hl_template-own--actions">
                        <a href="#" class="btn btn-success btn-xs" @click.prevent="$emit('select',snapshot)">
                            Select &amp; Continue
                            <i class="icon icon-arrow-right-2"></i>
                        </a>
                    </div>
                </div>
              </div>
              <div class="hl_template-own" v-else-if="snapshotTab === 'imported'">
                <div class="hl_template-own--tuple " v-for="snapshot in importedTemplates" :key="snapshot.id">
                    <div class="hl_template-own--info">
                        <div class="hl_template-own--avtar" >{{getAvtar(snapshot.name)}}</div>
                        <div class="hl_template-own--name" >{{snapshot.name}}</div>
                    </div>
                    <div class="hl_template-own--actions">
                        <a href="#" class="btn btn-success btn-xs" @click.prevent="$emit('select',snapshot)">
                            Select &amp; Continue
                            <i class="icon icon-arrow-right-2"></i>
                        </a>
                    </div>
                </div>
              </div>
            </div>
        </div>
    </modal>
</template>

<script>
import Modal from '@/pmd/components/common/Modal.vue'
import { CloneAccount } from '@/models';
import UpgradeModalShimmer from '@/pmd/components/common/shimmers/UpgradeModalShimmer.vue'

export default {
  props: ['show'],
  components: { Modal, UpgradeModalShimmer },
  computed:{
    company() {
      return this.$store.state.company.company
    },
  },
  created(){
    this.getSnapshotList();
  },
  data(){
    return {
      importedTemplates: [],
      ownTemplates: [],
      loading: false,
      snapshotTab: 'own'
    }
  },
  methods: {
    async getSnapshotList(){
      this.loading = true;
      const snapshots = await CloneAccount.getByCompanyId(this.company.id).then(snapshots => {
        this.importedTemplates = snapshots.filter(s => s.type === 'imported').sort((a, b) => a.name.localeCompare(b.name));
        this.ownTemplates = snapshots.filter(s => s.type === 'own').sort((a, b) => a.name.localeCompare(b.name));
      });
      this.loading = false;
    },
    getAvtar(name){
      let nameWords = name.split(' ');
      return nameWords.map( word=>{
          return word[0];
      }).join('').toUpperCase();
    }
  }
}
</script>

<style scoped>
.modal-body.--wide{
  padding: 30px;
}
.modal-heading-wrap {
    padding: 30px 50px 0px;
}
.modal-heading-content {
	text-align: center;
	padding-bottom: 20px;
	border-bottom: 2px solid #f2f7fa;
}


.hl_template-own{
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  min-height: 260px;
  max-height: calc(100vh - 260px);
  overflow-y: auto;
}

.hl_template-own--tuple{
    border: 2px solid #f2f7fa;
    border-radius: 4px;
    /* cursor: pointer; */
    padding: 10px;
    margin-bottom: 10px;
    margin-right: 10px;
    box-sizing: border-box;

    width: calc(50% - 10px);
    position: relative;
}
@media (max-width: 575px){
    .hl_template-own--tuple{
        width: 100%;
    }
}
.hl_template-own--actions{
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(42,49,53,0.2);
    border-radius: 4px;
    top: 0px;
    left: 0px;
    display:none;
    justify-content: flex-end;
    align-items: center;
    padding-right: 20px;
}
.hl_template-own--tuple:hover .hl_template-own--actions{
    display: flex;
}
.hl_template-own--info{
    display: flex;
}
.hl_template-own--avtar{
    height: 40px;
    width: 40px;
    border-radius: 4px;
    line-height: 40px;
    text-align: center;

    margin-right: 20px;
    color: #f7fafc;
    background-color: #118cf6;
}
.hl_template-own--name{
    line-height: 40px;

}
</style>
