import store from '@/store'
import * as moment from 'moment'
import { ISprintSchedule } from './bulkreq_interfaces'

export class BulkSchedule {
  public static WEEKDAYSLIST = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']

  public dripMode: string
  public dripQuantity: number
  public dripDelay: number
  public dripDelayType: string = 'days'
  public sendDays: string[] = []
  public startOn: string
  public timeZone: string
  public processWindowStart: moment.Moment | null
  public processWindowEnd: moment.Moment | null

  public constructor(translateFrom?: ISprintSchedule) {
    this.sendDays = [...BulkSchedule.WEEKDAYSLIST]
    this.dripMode = 'none'
    if (translateFrom) {
      if (translateFrom.processAllAtSchedule === true)
        this.dripMode = 'processAllAtSchedule'
      else this.dripMode = 'drip'
      this.dripQuantity = translateFrom.sprintLimit
      this.dripDelay = translateFrom.sprintDelay
      this.dripDelayType = translateFrom.sprintDelayType
      this.startOn = translateFrom.scheduleStart
      this.timeZone = translateFrom.scheduleTimeZone
      if (translateFrom.skipDays)
        this.sendDays = BulkSchedule.WEEKDAYSLIST.filter(
          a => !translateFrom.skipDays.includes(a)
        )
      if (translateFrom.processWindowStart) {
        this.processWindowStart = moment(
          moment().format('YYYY-MM-DD') + ' ' + translateFrom.processWindowStart
        )
      }
      if (translateFrom.processWindowEnd) {
        this.processWindowEnd = moment(
          moment().format('YYYY-MM-DD') + ' ' + translateFrom.processWindowEnd
        )
      }
    }
  }

  public hasValidSettings() {
    if (this.dripMode === 'none') return true

    if (this.dripQuantity <= 0) {
      throw new Error('Specify valid drip quanity. A number above zero.')
    }

    if (!this.startOn) {
      throw new Error('Select schedule start date')
    }

    if (this.dripDelay < 0) {
      this.dripDelay = 1
      throw new Error('Specify a valid number to setup delay in days or hours')
    }

    if (this.dripDelayType === 'hours' && this.dripDelay > 10) {
      throw new Error('Maximum delay of 10 hours between batch runs allowed')
    }

    if (this.dripDelayType === 'days' && this.dripDelay > 31) {
      throw new Error('Maximum delay of 31 days between batch runs allowed')
    }

    return true
  }

  public getPersistence(): ISprintSchedule | null {
    if (!this.hasValidSettings()) {
      throw new Error('Provide valid settings')
    }

    if (this.dripMode === 'none') return null
    else {
      const skipDays = BulkSchedule.WEEKDAYSLIST.filter(
        a => !this.sendDays.includes(a)
      )
      const dto = {
        sprintLimit: this.dripQuantity,
        sprintDelay: this.dripDelay,
        sprintDelayType: this.dripDelayType,
        scheduleStart: this.startOn,
        scheduleTimeZone: this.timeZone,
        processAllAtSchedule: this.dripMode === 'processAllAtSchedule',
        skipDays,
      } as ISprintSchedule
      if (!dto.scheduleTimeZone) {
        dto.scheduleTimeZone =
          store.getters[`locations/getCurrentLocationTimeZone`]
      }
      if (this.processWindowStart) {
        dto.processWindowStart = this.processWindowStart.format('hh:mm a')
      }
      if (this.processWindowEnd) {
        dto.processWindowEnd = this.processWindowEnd.format('hh:mm a')
      }
      return dto
    }
  }
}
