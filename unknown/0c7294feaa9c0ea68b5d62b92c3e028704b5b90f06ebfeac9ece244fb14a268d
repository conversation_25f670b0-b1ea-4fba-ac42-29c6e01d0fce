<template>
  <div class="card">
    <div class="card-header">
      <h3>Enable / Disable Products</h3>
    </div>
    <div class="card-body">
      <div class="product-table">
        <div class="product-table-row --head">
          <div class="product-table-cell --left --head">Feature</div>
          <div class="product-table-cell --head --fixed">Disable</div>
          <div class="product-table-cell --head --fixed">Enable</div>
        </div>
        <div class="product-table-row" v-for="product in products" :key="product.id">
          <div class="product-table-cell --left --extra-padding --name" :title="product.description">{{product.title}}</div>
          <div class="product-table-cell --fixed">
            <div class="product-radio" :class="{'--enabled': !product.enabled, '--disabled': product.disabled}" @click="updateProduct(product.id, false)"/>
          </div>
          <div class="product-table-cell --fixed">
            <div class="product-radio" :class="{'--enabled': product.enabled, '--disabled': product.disabled}" @click="updateProduct(product.id, true)"/>
          </div>
        </div>
      </div>
    </div>
    <div class="card-footer-row">
      <i class="reminder" v-if="unsavedChanges">Unsaved Changes</i>
      <UIButton
        :class="unsavedChanges ? 'btn-primary' : ''"
        :disabled="!unsavedChanges || updating"
        @click="save"
      >
        {{ updating ? 'Saving..' : 'Save' }}
      </UIButton>
    </div>
    <div class="saas-card-overlay" v-if="!saasPlanId">
      <div class="no-plan-warning">
        <i class="fas fa-exclamation-triangle"></i>
        <br />
        <div class="title">No SaaS Plan Enabled</div>
        <br />
        <div class="btn btn-primary" @click="$emit('attachPlan')">Add a Subscription</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { trackGaEvent } from "@/util/helper";

export default Vue.extend({
  props: ['locationId', 'saasPlanId'],
  data(){
    return{
      unsavedChanges: false,
      updating: false,
      products:[],
      originalProducts: []
    }
  },
  // created(){
  //   this.getPlans();
  // },
  computed: {
    company() {
      return this.$store.state.company.company
    },
  },
  watch: {
    saasPlanId: {
      handler: function(){
        this.getPlans();
      },
      immediate: true
    }
  },
  methods:{
    async getPlans() {
      // this.plansStatus = 'loading';
      try {
        const { data } = await this.saasService.get(`/saas-config/${this.locationId}/features?companyId=${this.company.id}`)
        const { enabledProducts, minProducts, allProducts } = data

        let productsList = [];
        Object.entries(allProducts).forEach( ([key, value]) => {
          productsList.push({
            id: key,
            title: value.title,
            description: value.description,
            enabled: enabledProducts.includes(key),
            disabled: minProducts.includes(key)
          })
        })
        this.products = productsList;
        this.originalProducts = JSON.parse(JSON.stringify(this.products));
        // console.log(this.products);
      } catch (err) {
        // this.plansStatus = 'failed_loading'
      }
    },
    updateProduct(productId: string, isEnabled: boolean) {
      let productIndex = this.products.findIndex( product => product.id === productId)

      if(productIndex !== -1) {
        this.products[productIndex].enabled = isEnabled
        if(JSON.stringify(this.products) !== JSON.stringify(this.originalProducts)){
          this.unsavedChanges = true;
        } else {
          this.unsavedChanges = false;
        }
      }
    },
    async save(){
      try {
        this.updating = true;
        const enabledProducts = [];
        this.products.forEach(product => {
          if (product.enabled) {
            enabledProducts.push(product.id);
          }
        });
        const { data } = await this.saasService.put(`/saas-config/${this.locationId}/features`, {
          products: enabledProducts,
          companyId: this.company.id
        })

        if(data.updated) {
          this.originalProducts = JSON.parse(JSON.stringify(this.products));
          this.unsavedChanges = false;
        }
        trackGaEvent('SaasPlanBuilder', this.company.id, `Saved Location Products of locationId: ${this.locationId}`, 1);
      } catch (err) {
        // this.plansStatus = 'failed_loading'
        trackGaEvent('SaasPlanBuilder', this.company.id, `Failed to saved Location Products of locationId: ${this.locationId}`, 1);
      } finally {
        this.updating = false;
      }
    }
  }
})
</script>
<style lang="scss" scoped>
.card-footer-row{
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0px 30px 30px;
  i {
    margin-right: 12px;
  }
  .btn{
    background: #e2e8f0;
    color: #4a5568;
    padding: 8px 12px;
    border-radius: 3px;
    // .--unsaved {
    //   background-color: #178af6;
    //   color: white;
    // }
  }
}
.product-table{
  display: table;
  width: 100%;
  .product-table-row{
    display: table-row;
    &.--head{
      font-weight: 500;
      font-size: 14px;
      line-height: 16px;
      color: #4A5568;
    }
  }
  .product-table-cell{
    display: table-cell;
    text-align: center;
    padding: 12px;
    border-bottom: 1px solid #F1F7FA;
    &.--left{
      text-align: left;
    }
    &.--head{
      border-bottom: none;
    }
    &.--fixed{
      width: 80px;
    }
    &.--extra-padding{
      padding-left: 32px;
    }
    &.--name{
      text-transform: capitalize;
    }
  }
  .product-radio{
    height: 18px;
    width: 18px;
    border: 1px solid #CBD5E0;
    border-radius: 50%;
    cursor: pointer;
    margin: auto;
    transition: all 0.3s ease-in-out;
    &.--disabled {
      cursor: not-allowed;
      pointer-events: none;
    }
    &.--enabled{
      border: 6px solid #158bf5;
      &.--disabled {
        border: 6px solid #afb8bb;
      }
    }
  }
}
.saas-card-overlay {
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  background-color: rgba(242, 242, 242, 0.8);
  z-index: 2;

  display: flex;
  align-items: flex-end;
  justify-content: center;
  .no-plan-warning{
    margin-top: 100px;
    // background-color: #ffffff;
    position: sticky;
    bottom: 100px;
    display: block;
    i {
      color: #f77d7d;
      font-size: 44px;
      margin: auto;
      text-align: center;
      display: block;
    }
  }
}
</style>
