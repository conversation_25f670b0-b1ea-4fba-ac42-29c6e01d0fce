import BaseService from './BaseService'
import servicesConfig from '../config/services'

class OpportunityService extends BaseService {
  constructor(baseUrl: string) {
    super(baseUrl)
  }

  create = (data: { [key: string]: any }) =>
    this.requests.post(`/`, data)
  read = (id: string) =>
    this.requests.get(`/${id}`)
  update = (id: string, data: { [key: string]: any }) =>
    this.requests.put(`/${id}`, data)
  delete = (id: string) =>
    this.requests.delete(`/${id}`)
}

const instance = new OpportunityService(servicesConfig.Opportunities)

export default instance
