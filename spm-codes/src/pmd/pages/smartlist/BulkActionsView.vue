<!-- prettier-ignore -->
<template>
  <section id="dashboard" class="hl_wrapper--inner customers" style="min-width:100%">
    <div class="container-fluid">
          <!-- <div class="customized-header">
            <div class="header-underline">
              <div class="d-inline-block">
                <ul class="header-ul">
                  <li
                    v-for="(s, index) in bulkActionTypes"
                    :key="index"
                    :class="{
                      active: selectedType && selectedType.value === s.value,
                    }">
                    <a
                      @click.prevent="changeType(s)"
                      :class="{
                        'light-color-wait':
                          changingList &&
                          selectedType &&
                          selectedType.value !== s.value}">
                      {{ s.key }}
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div> -->
        <div class="d-flex justify-content-start">
          <!-- <div class="mx-4 my-4">
            <div class="vertical-list">
              <div class="d-block">
                <ul class="link-ul">
                  <li v-for="(s, index) in filters"
                    :key="index">
                    <div :class="{'link-highlight': selectedFilter && selectedFilter.value === s.value}" class="link-item">
                    <a class="btn btn-extended"
                      @click.prevent="filterChange(s)">
                      {{ s.key }}
                    </a>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div> -->
          <div class="container-fluid">
          <div class="card hl_dashboard--latest-review-requests">
            <div class="card-header">
                        <div class="d-flex justify-content-end" style="width: 100%">
            <div class="d-inline-flex">
              <div class="hl_tasks-list bulk-actions-header">
                <b-dropdown :text="selectedType ? selectedType.key : ''" :disabled="isLoading">
                  <b-dropdown-item
                    v-for="item in bulkActionTypes"
                    :key="item.value"
                    @click="changeType(item)">
                    {{ item.key }}
                  </b-dropdown-item>
                </b-dropdown>
              </div>
              <div class="hl_tasks-list bulk-actions-header">
                <b-dropdown :text="selectedUser ? selectedUser.key : ''" :disabled="isLoading">
                  <b-dropdown-item
                    v-for="item in users"
                    :key="item.value"
                    @click="userChange(item)">
                    {{ item.key }}
                  </b-dropdown-item>
                </b-dropdown>
              </div>
              <div class="hl_tasks-list bulk-actions-header" aria-disabled="">
                <b-dropdown :text="selectedFilter ? selectedFilter.key: ''" :disabled="isLoading">
                  <b-dropdown-item
                    v-for="filter in filters"
                    :key="filter.value"
                    @click="filterChange(filter)">
                    {{ filter.key }}
                  </b-dropdown-item>
                </b-dropdown>
              </div>
              <div class="d-flex" style="min-width: 190px">
                <vue-ctk-date-time-picker
                  v-model="selectedDateRange"
                  :right="true"
                  :range="true"
                  color="#188bf6"
                  :only-date="true"
                  :disabled="isLoading"
                  enable-button-validate
                  :noClearButton="true"
                  name="start_time"
                  min-date="2020-05-29 12:00 am"
                  :formatted="getCountryDateFormat(false)"
                />
              </div>
            </div>
          </div>
            </div>
            <div class="card-body">
            <BulkActionItems
                  v-on:loading="setLoading"
                  :selectedFilter="selectedFilter.value"
                  :selectedDateRange="selectedDateRange"
                  :selectedType="selectedType ? selectedType.value :null"
                  :selectedUserId="selectedUser ? selectedUser.value :null"
                  :users="users">
            </BulkActionItems>
            </div>
          </div>
              </div>
        </div>
        </div>
  </section>
</template>

<script lang="ts">
import Vue from 'vue'
import { ImportRequest, User } from '../../../models'
import { BulkRequestV2 } from './server/bulk_req_v2'
import { getCountryDateFormat } from '@/models'
import { KeyVal } from './CommonSelector.vue'
import * as moment from 'moment-timezone'
import BulkActionItems from './BulkActionItems.vue'
import { Key } from 'readline'

export default Vue.extend({
  name: 'BulkActionsView',
  data() {
    return {
      bulkActionTypes: [] as KeyVal[],
      statuses: [] as KeyVal[],
      highLightType: 'bulk_actions',
      changingList: false,
      showDetails: false,
      users: [] as KeyVal[],
      allowScheduleEdit: false,
      requests: [] as ImportRequest[],
      currentLocationId: '',
      isLoading: false,
      processingId: '',
      items: [] as BulkRequestV2[],
      selectedBulReq: null as BulkRequestV2 | null,
      selectedUser: null as KeyVal| null,
      showError: false as boolean,
      showSuccess: false as boolean,
      filters: [
        new KeyVal('Any Status', 'all'),
        new KeyVal('Queued', 'scheduled'),
        new KeyVal('Processing', 'processing'),
        new KeyVal('Completed', 'complete'),
        new KeyVal('Cancelled', 'cancelled'),
        new KeyVal('Paused', 'paused'),
      ],
      selectedFilter: new KeyVal('Queued', 'scheduled') as KeyVal,
      selectedType: null as null | KeyVal,
      getCountryDateFormat,
      selectedDateRange: {
        start: moment().subtract(30, 'days').startOf('day').toDate(),
        end: moment().add(30, 'days').endOf('day').toDate(),
      },
    }
  },
  components: {
    BulkActionItems,
  },
  watch: {
    '$route.params.location_id': async function () {
      await this.refresh()
    },
    '$route.query': async function () {
      const query = this.$router.currentRoute.query
      if (!query) return
      const {locationId, status, type, user} = query
      // alert(JSON.stringify(this.$router.currentRoute.query))
      let hasChange = false
      hasChange = (status && !this.selectedFilter) || this.selectedFilter.value !== status
      hasChange = (user && !this.selectedFilter) || this.selectedUser.value !== user
      hasChange = (type && !this.selectedType) || this.selectedType.value !== type
      if (hasChange) this.refresh()
    }
  },
  computed: {
    locationTimeZone() {
      //console.log(`hi -- ${this.$store.getters[`locations/getCurrentLocationTimeZone`]}`)
      return this.$store.getters[`locations/getCurrentLocationTimeZone`]
    },
  },
  methods: {
    async setLoading(val: boolean) {
      this.isLoading = val
    },
    async filterChange(event: KeyVal) {
      this.selectedFilter = event
    },
    async userChange(user: KeyVal) {
      if (user) this.selectedUser = user
    },
    async changeType(event: KeyVal) {
      this.selectedType = event
    },
    async refresh() {
      const tempUsers = await this.$store.getters['users/getAll']
      if (tempUsers && tempUsers.length) this.users = tempUsers.map(a => new KeyVal(a.name,a.id));
      else this.users = []
      const defaultUser = new KeyVal('All Users', 'all')
      this.users.unshift(defaultUser)

      const defaultType = new KeyVal('All Actions', 'all')
      const types = [
        'Email',
        'SMS',
        'Campaign',
        'Workflow',
        'Review Request'
        // 'Add Tag',
        // 'Delete Tag',
        // 'Import',
      ]

      this.bulkActionTypes = types.map(
        a =>
          new KeyVal(`Bulk ${a}`, `bulk-${a.toLowerCase().replace(' ', '-')}`)
      )

      this.bulkActionTypes.unshift(defaultType)

      types.push('Bulk Opportunities'); this.bulkActionTypes.push(new KeyVal(`Bulk Opportunities`,'bulk-ops'));
      types.push('Add Tag'); this.bulkActionTypes.push(new KeyVal(`Bulk Add Tag`,'bulk-tag-add'));
      types.push('Remove Tag');this.bulkActionTypes.push(new KeyVal(`Bulk Remove Tag`,'bulk-tag-remove'));
      types.push('Delete Contact');this.bulkActionTypes.push(new KeyVal(`Bulk Delete Contact`,'bulk-contact-delete'));
      types.push('Bulk Import'); this.bulkActionTypes.push(new KeyVal(`Import`,'bulk-import'));
      types.push('Export'); this.bulkActionTypes.push(new KeyVal(`Export`,'bulk-export'));
      types.push('Merge Contact'); this.bulkActionTypes.push(new KeyVal(`Merge Contact`,'bulk-merge-contact'));

      const {status, type, user} = this.$router.currentRoute.query;

      const idx = this.users.findIndex(a=> a.value && a.value.toLowerCase() === user) // convert user ids to lower case for comparison
      if (idx !== -1) this.selectedUser = this.users[idx]
      else this.selectedUser = defaultUser

      const idx2 = this.bulkActionTypes.findIndex(a=> a.value === type)
      if (idx2 !== -1) this.selectedType = this.bulkActionTypes[idx2]
      else this.selectedType = defaultType

      const idx3 = this.filters.findIndex(a=> a.value === status)
      if (idx3 !== -1) this.selectedFilter = this.filters[idx3]
      else this.selectedFilter = this.filters[0]
    },
    keyDownHandler(e: any) {
      if (e.keyCode === 190 && e.ctrlKey && e.shiftKey) {// e.code === "Period"
        e.preventDefault()
        let routeData = this.$router.resolve({name: 'smart_list', params: {location_id: this.$router.currentRoute.params.location_id, smart_list: 'bulk_actions_legacy'}});
        window.open(routeData.href, '_blank')
      }
      if (e.keyCode === 186 && e.ctrlKey && e.shiftKey) {// e.code === "Period"
        e.preventDefault()
        let routeData = this.$router.resolve({name: 'smart_list', params: {location_id: this.$router.currentRoute.params.location_id, smart_list: 'contact_requests_old'}});
        window.open(routeData.href, '_blank')
      }
    },
  },
  async mounted() {
    await this.refresh()
    document.addEventListener('keydown', this.keyDownHandler)
  },
  beforeDestroy() {
    document.removeEventListener('keydown', this.keyDownHandler) // don't forget to remove the listener in destructor
  }
})
</script>
<style scoped>
.sort {
  margin-left: 20px;
  margin-bottom: 10px;
  cursor: pointer;
}

.form-control:disabled,
.form-control[readonly] {
  background-color: #f7fafc;
}
.vdp-datepicker__calendar .cell:not(.blank):not(.disabled).day:hover,
.vdp-datepicker__calendar .cell:not(.blank):not(.disabled).month:hover,
.vdp-datepicker__calendar .cell:not(.blank):not(.disabled).year:hover {
  border: 1px solid #188bf6 !important;
}
.vdp-datepicker__calendar .cell.selected,
.vdp-datepicker__calendar .cell.selected.highlighted,
.vdp-datepicker__calendar .cell.selected:hover {
  background: #188bf6 !important;
  color: white;
}
#back {
  margin-right: 10px;
  cursor: pointer;
  color: #188bf6;
}
.dropdown-background {
  background: rgb(242 247 250);
}
.campaign_new_header .hl_datepicker {
  background: rgb(242 247 250);
  padding: 0px 8px;
  border-radius: 0.3125rem;
  -webkit-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  cursor: pointer;
  margin-right: 0;
  height: 40px;
}
.hl_tasks-list.bulk-actions-header {
  display: flex;
  align-items: center;
}
.hl_tasks-list.bulk-actions-header h2 {
  margin-right: 16px;
}
.dropdown .dropdown-menu .dropdown-item {
  white-space: initial;
  width: 350px;
}
.hl_tasks-list button.dropdown-toggle {
  background-color: #ffffff !important;
  color: #607179 !important;
}
.customized-header {
  display: inline-block;
  list-style: none;
  padding: 0;
  width: 100% !important;
  padding-right: 15px;
  padding-left: 15px;
  margin: 0px;
}

.hl_tasks-list .dropdown .dropdown-menu {
  max-height: 250px;
  overflow: auto;
}

.hl_smartlists--main .table tbody tr td .avatar {
  min-width: 50px !important;
}

.table tbody tr td {
  padding: 4px 10px !important;
}

.table thead tr th:first-child {
  padding-left: 15px !important;
}

.table tbody tr td:first-child {
  padding-left: 15px !important;
}

.form-control {
  padding-top: 6px;
  padding-bottom: 6px;
}

.header-underline {
  display: inline-block;
  border-bottom: 1px solid #d5dde1;
  list-style: none;
  width: 100% !important;
  margin-bottom: 20px;
}

.vertical-list {
  display: block;
  list-style: none;
  margin-bottom: 20px;
}


@media (min-width: 768px) {
  ul.header-ul {
    display: inline-flex;
    align-items: center;
    margin-bottom: 0;
    overflow-x: auto;
  }

  ul.header-ul li a {
    padding-left: 5px;
    padding-right: 5px;
    border-bottom: 4px solid transparent;
  }
}

ul.header-ul-vertical li a {
  padding-left: 5px;
  padding-right: 5px;
}

ul.header-ul li a {
  display: block;
  color: #607179;
  height: 40px;
  min-width: 40px;
  text-align: center;
}

ul.header-ul li.active a {
  color: #2a3135;
  border-color: #188bf6;
}

ul.header-ul {
  list-style: none;
  padding: 0;
}

ul.header-ul li:not(:last-child) {
  margin-right: 20px;
}

div.no-border-radius {
  border-radius: 0px !important;
}

.option > input[type='checkbox']:disabled + label::after {
  background: #dddddd;
  border-color: #dddddd;
}

.copier {
  visibility: hidden;
}

.copy-me:hover .copier {
  visibility: visible;
  display: inline-block;
  margin: 0px 4px;
}

.position-parent {
  position: relative;
  display: inline-flex;
}

.position-stacked {
  font-size: 18px;
  position: absolute;
  line-height: 0 !important;
  padding: 0px 2px !important;
}

.chevron-hover {
  color: lightgray;
}
.chevron-hover:hover {
  color: darkslategray;
}

.chevron-light {
  color: lightgray;
}

.wide-header {
  display: flex;
  justify-content: space-between;
}
.close-content {
  display: flex;
  justify-content: normal;
}
.date-display {
  white-space: pre;
  font-size: small;
}
.min-height-150 {
  min-height: 150px;
}

.list-name-inactive {
  padding-left: 5px !important;
  padding-right: 5px !important;
  color: lightgray !important;
  height: 40px !important;
  min-width: 40px !important;
}

.light-color-wait {
  color: lightgray !important;
  cursor: none;
}

.phone {
  white-space: nowrap;
}

.break-word {
  overflow-wrap: break-word;
  white-space: unset;
  max-width: 60px;
}

.btn-light:disabled {
  background-color: #efefef !important;
  padding-left: 15px !important;
}

.btn[disabled] {
  pointer-events: auto;
}

.--grayfill {
  fill: gray;
}

ul.header-ul-2 {
  list-style: none;
  padding: 0;
  cursor: pointer;
}

.link-item {
  display: flex;
  justify-content: left;
  align-items: center;
  height: 100%;
  width: 100%;
  line-height: 1pt;
  cursor: pointer;
  min-height: 40px;
  min-width: 100px;
  height: 100%;
  width: 100%;
}

.link-highlight {
  background: #188bf6;
  color: white;
  border-radius: 6px;
  color: white;
  width: 100%;
  height: 100%;
}
.link-highlight {
  background: #188bf6;
  color: white;
  border-radius: 6px;
  color: white;
  width: 100%;
  height: 100%;
}
.btn-extended {
  padding: 4px 20px 4px 8px;
  text-align: left;
}
ul.link-ul {
  list-style: none;
  padding: 0;
  cursor: pointer;
  padding: 6px;
  display: table-row;
}
.date-time-picker .field .field-input {
  height: auto !important;
  padding: 0 !important;
  color: #188bf6 !important;
  text-align: center;
  border: none !important;
  background: transparent !important;
}
</style>
