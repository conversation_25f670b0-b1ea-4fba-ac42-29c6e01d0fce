<template>
	<Filters
		:conditions="conditions"
		:filterMaster="filterMaster"
		v-on:update:conditions="$emit('update:conditions', $event)"
	/>
</template>

<script lang="ts">
import Vue from 'vue'
import { Campaign, Formsurvey } from '@/models';
import { Condition } from '@/models/trigger';

const Filters = () => import( './Filters.vue');

export default Vue.extend({
	props: ['conditions'],
	components: { Filters },
	data() {
		return {
			filterMaster: [
				{
					placeHolder: 'Select survey',
					title: 'Survey is',
					value: 'survey.id',
					valueType: 'text',
					type: 'select',
					options: [] as { [key: string]: any }[]
				},
        {
          placeHolder: "Select condition",
          title: "Disqualified",
          value: "surveySubmission.disqualified",
          valueType: "boolean",
          type: "select",
          options: [{ title: 'is true', value: true }, { title: 'is false', value: false }]
        },
			],
		}
	},
	async created() {
		const currentLocationId = this.$router.currentRoute.params.location_id;
		const filter = lodash.find(this.filterMaster, { value: 'survey.id' });
		if (filter) {
			const surveys = await Formsurvey.getByLocationId(currentLocationId);
			filter.options = surveys.map(survey => {
				return {
					title: survey.name,
					value: survey.id
				}
			});
		}
	}
})
</script>

