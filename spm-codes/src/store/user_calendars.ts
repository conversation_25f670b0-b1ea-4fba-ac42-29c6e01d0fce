import Vue from 'vue'
import { Module, ActionContext } from 'vuex'
import { RootState, CalendarState } from './state_models'
import UserCalendar from '../models/user_calendar'

let calendarListner;
export const userCalendars: Module<CalendarState, RootState> = {
  namespaced: true,
  state: { calendars: [], locationId: undefined },
  getters: {
    getById: (state: CalendarState) => {
      return (id: string) => state.calendars.find(x => x.id === id)
    }
  },
  mutations: {
    set: (
      state: CalendarState,
      payload: { calendars: any[]; locationId: string }
    ) => {
      state.calendars = payload.calendars
      if (payload.locationId) {
        state.locationId = payload.locationId
      }
    },
    clearAll(state: CalendarState) {
      if (calendarListner) {
        calendarListner();
      }

      state.calendars = []
      state.locationId = undefined
    }
  },
  actions: {
    async syncAll(
      context: ActionContext<CalendarState, RootState>,
      locationId: string
    ) {
      if (!locationId) {
        context.commit('clearAll')
        return
      }


      if (context.state.locationId === locationId) {
        return context.state.calendars
      }

      return new Promise((resolve, reject) => {

        if (calendarListner) {
          calendarListner();
        }

        calendarListner = UserCalendar.fetchAllByLocation(locationId)
          .onSnapshot(snapshot => {
            context.commit('set', {
              locationId: locationId,
              calendars: snapshot.docs.map(d => {
                return {
                  id: d.id,
                  ...d.data()
                }
              })
            })

            resolve(context.state.calendars)
          }, err => resolve())
      })
    },
    logout: {
      root: true,
      handler: (context: ActionContext<CalendarState, RootState>) => {
        context.commit('clearAll')
      }
    },
    clearAll: {
      handler: (context: ActionContext<CalendarState, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
