<template>
    <div class="form-control-group">
      <!-- {{option.option}} {{option.value}} -->
      <!-- <template v-else-if="isPhone">
          <PhoneNumber
                class="form-control"
                placeholder="Phone numbers"
                v-model="firstValue"
                v-validate="'phone'"
                name="msgsndr1"
                autocomplete="msgsndr1"
                data-vv-as="phone"/>
        <span v-show="errors.has('msgsndr1')" class="error">{{ errors.first('msgsndr1') }}</span>
      </template> -->
      <template v-if="isGeneric">
         <div class="search-form">
          <i class="icon icon-loupe"></i>
          <UITextInput
            :placeholder="'Quick search'"
            v-model="firstValue"
            :class="{
              'form-light': true
            }"
          />
          </div>
      </template>
      <template v-else>
         <UITextInput
            v-model="firstValue"
        />
        <div><span v-if="!firstValue" class="--red">* required</span></div>
      </template>
      <div class="my-2" v-if="example" ><label style="font-size:1.1em !important;">{{example}}</label></div>
    </div>
</template>

<script lang="ts">

import Vue from 'vue';
import {FilterOption} from './vm/option_vm'
import { Location } from '@/models'
import { FilterBuilder } from './vm/filter_vm_builder';

const PhoneNumber = () => import('@/pmd/components/util/PhoneNumber.vue');

export default Vue.extend({
  name: 'FilterInput',
  props : {
    bus : Vue,
    option: FilterOption
  },
  components: {
    PhoneNumber
  },
  data () {
    return {
      firstValue: this.option.firstValue,
      waitTime: 300,
      debouncer:undefined as NodeJS.Timer | undefined,
      example: this.option.example || ''
    };
  },
  watch : {
    firstValue(n,o){
     this.option.firstValue = n;
     if(this.debouncer) clearTimeout(this.debouncer);
     this.debouncer = setTimeout(() => this.publish(),this.waitTime);
    }
  },
  computed: {
    isPhone(){
      return !(!this.option || !this.option.filterName || this.option.filterName.toLowerCase().includes('phone') === false);
    },
     isGeneric(){
      return !(!this.option || !this.option.filterName || this.option.filterName.toLowerCase().includes(FilterBuilder.GENERIC_FILTER) === false);
    },
  },
  methods: {
    async publish(){
      if (this.option && this.isPhone && this.$router.currentRoute.params.location_id) {
          const loc = new Location(await this.$store.dispatch(`locations/getById`, this.$router.currentRoute.params.location_id));
          this.option.secondValue = loc.country;
      }
      this.$emit('inputChange');
    },
  },
})
</script>
