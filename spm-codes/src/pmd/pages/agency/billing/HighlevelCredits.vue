<template>
  <div class="hl_billing--services">
    <div class="card">
      <div class="card-header">
        <h3 style="margin-bottom:0px"> Credits </h3>
      </div>
      <div class="card-body">
        <div class="no-active-service">
          <p v-if="rechargeStatus === 'failed'" class="failed-message">Auto Debit failed</p>
          <p>Credits Remaining: <b>${{credits}}</b> <i @click="fetchCredits()" id="credits-reload-icon" class="icon icon-reload-1"></i></p>
          <p>You will be automatically charged <b>${{autoPurchaseCreditsAmount}}</b> when the credits goes below <b>${{autoPurchaseWhenCreditsBelow}}</b></p>
          <br>
          <div class="form-group">
            <table>
              <tr>
                <td width="80%">
                  <label>Add Credits Manually ($)
                    <!--              <i-->
                    <!--                class="fa fa-info-circle"-->
                    <!--                v-b-tooltip.hover-->
                    <!--                :title="'hello'"></i>-->
                  </label>
                  <input
                    type="text"
                    class="form-control"
                    placeholder="Recharge Credits Amount"
                    v-model="recharge_credits_amount"
                    name="recharge_credits_amount"
                    :disabled="purchasing"
                  />
                </td>
                <td>
                  <button
                    type="button"
                    class="btn btn-blue"
                    data-dismiss="modal"
                    id="eliza-credits-purchase-button"
                    :disabled="purchasing"
                    @click="purchaseCredits()"
                  >
                    <moon-loader v-if="purchasing" color="#37ca37" size="20px" />
                    <span v-if="!purchasing">Purchase</span>
                  </button>
                </td>
              </tr>
            </table>
            <p v-if="status === 'failed'" class="failed-message">{{message}}</p>
            <p v-if="status === 'success'" class="success-message">{{message}}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';
import axios from 'axios';
import config from '@/config';
import {Company} from '@/models';

export default Vue.extend({
  data() {
    return {
      recharge_credits_amount: '',
      company: Company,
      rechargeStatus: '',
      credits: '',
      purchasing: false,
      status: '',
      message: '',
      autoPurchaseWhenCreditsBelow: '',
      autoPurchaseCreditsAmount: ''
    };
  },
  async created() {
    await this.fetchCompanyDetails();
    await this.fetchCredits();
  },
  methods: {
    async fetchCompanyDetails() {
      let company = await this.$store.dispatch('company/get');
      this.company = company.data;
    },
    async fetchCredits() {
      const {data} = await this.paymentService.get(`/credits/${this.company.id}/remaining-credits`);
      this.rechargeStatus = data.rechargeStatus;
      this.credits = data.credits;
      this.autoPurchaseWhenCreditsBelow = data.autoPurchaseWhenCreditsBelow;
      this.autoPurchaseCreditsAmount = data.autoPurchaseCreditsAmount;
      this.message = '';
    },
    async purchaseCredits() {
      try {
        this.purchasing = true;
        this.message = '';
        const {data} = await this.paymentService.post(`/credits/${this.company.id}/purchase-credits`, {
          companyId: this.company.id,
          amount: Number(this.recharge_credits_amount)
        });
        const {message, status} = data;
        this.message = message;
        this.status = status;
        this.recharge_credits_amount = '';
      } catch (err) {

      } finally {
        this.purchasing = false;
      }

    }
  },
});
</script>

<style>
.hl_billing--services .service-card .table .label.--warning {
  background-color: rgba(255, 188, 0, 0.9);
  color: #ffffff;
  border-left: 2px solid #ff870d;
  margin-left: 10px;
}

.hl_billing--services .service-card .table .label.--basic {
  background-color: #ff870d
}

.hl_billing--services .service-card .table .label.--plus {
  background-color: #6e22ff
}

#eliza-credits-purchase-button {
  position: relative;
  top: 15px;
  left: 15px;
}

.failed-message {
  color: #ff0000;
}

.success-message {
  color: #008000;
}

#credits-reload-icon {
  position: relative;
  left: 16px;
  top: 1px;
  cursor: pointer;
}
</style>
