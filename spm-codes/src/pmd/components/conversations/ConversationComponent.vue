<template>
  <div class="message-body--conversation">
    <div
      style="padding-top: 0"
      class="messages-group"
      :style="getHeightBasedOnMessageBox"
      ref="messageBox"
      @scroll="scrollListener"
    >
      <div class="messages-group-inner">
        <span
          class="btn btn-light2 btn-xs message-group-date-indicator top-date-sticky"
          ref="topLabel"
        />
        <div v-if="fetching" style="text-align: center; margin-bottom: 35px">
          <moon-loader :loading="fetching" color="#1ca7ff" size="15px" />
        </div>
        <div
          v-else-if="moreAvailable"
          style="text-align: center; margin-bottom: 25px"
        >
          <a
            href="javascript:void(0);"
            style="font-size: 0.85rem; text-decoration: underline"
            @click.prevent="loadMoreMessages(21)"
            >Fetch older messages</a
          >
        </div>
        <div class="messages-group-heading" v-else>
          <Sms2Icon class="mx-auto" />
          <h3>Conversation began</h3>
          <p v-if="conversation && conversation.dateAdded">
            {{
              conversation.dateAdded.format(
                getCountryDateFormat('extended-weekday')
              )
            }}
          </p>
        </div>
        <div
          class="messages-group-inner"
          v-for="(messagesGroup, groupDate, groupIndex) in groupedMessages"
          :key="groupIndex"
        >
          <span
            id="date-label"
            class="btn btn-light2 btn-xs message-group-date-indicator"
            >{{ groupDate }}</span
          >
          <ConversationMessage
            v-for="message in messagesGroup"
            :key="message.id"
            :message="message"
            :company="company"
            :contact="contact"
            :lastMessageId="lastMessageId"
          />
          <span
            v-if="showCelebrationMessage"
            id="onboarding-celebration"
            class="btn btn-light2 onboarding-celebration message-group-date-indicator show-step-popup"
          >
            <div class="onboarding-celebration-header">
              <CelebrationIcon />
              <p>Wohoo! Conversations are flowing now</p>
            </div>
            <p class="onboarding-celebration-body">
              Things are just getting started, lets explore how GHL can help
              build your business.<br />Meanwhile, you can keep sending messages
              to your heart’s content
            </p>
            <div class="onboarding-celebration-footer">
              <!-- <div
                @click.prevent="redirectDashboard"
                class="onboarding-celebration-footer-link"
              >
                TAKE ME THERE
              </div> -->
              <div @click.prevent="hideCelebrationPopup">
                DISMISS THIS MESSAGE
              </div>
            </div>
          </span>
        </div>
      </div>
    </div>
    <i
      v-if="showScrollDownShortcut"
      class="scroll-down-shortcut fa fa-lg fa-angle-double-down"
      @click.prevent="scrollToBottom"
    ></i>

    <SendMessageComponent
      :conversation="conversation"
      :contact="contact"
      :activeTabHeight.sync="activeTabHeight"
      :manualSMS="manualSMS"
      @hideScrollDownShortcut="showScrollDownShortcut = false"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import SendMessageComponent from '../../components/conversations/SendMessageComponent.vue'
import ConversationMessage from '../../components/ConversationMessage.vue'
import { EventBus } from '@/models/event-bus'
import { getCountryDateFormat } from '../../../models'
import TopBarVue from '../TopBar.vue'
import Sms2Icon from '@/assets/pmd/img/icon-sms2.svg'
import CelebrationIcon from '@/assets/pmd/img/celebration.svg'

export default Vue.extend({
  props: [
    'conversation',
    'contact',
    'messages',
    'moreAvailable',
    'fetching',
    'company',
    'manualSMS',
  ],
  components: {
    ConversationMessage,
    SendMessageComponent,
    Sms2Icon,
    CelebrationIcon,
  },
  created() {
    EventBus.$on('scrollToBottom', this.scrollToBottom)
    EventBus.$on('update-ui', this.updateUI)
  },
  data() {
    return {
      componentKey: 0,
      activeTabHeight: 276,
      getCountryDateFormat,
      showScrollDownShortcut: false,
      showCelebrationMessage: false,
    }
  },
  watch: {
    conversation(val, oldVal) {
      this.resetPage()
    },
    '$store.state.conversation.popupStep': function () {
      const onboarding = this.$store.state.conversation.onboarding
      const popupStep = this.$store.state.conversation.popupStep
			if (onboarding && popupStep == 2) {
        this.scrollToBottom();
			}
		}
  },
  methods: {
    updateUI(query: string) {
      console.log('update UI')
      this.componentKey += 1
    },
    async loadMoreMessages(limit?: number) {
      let previousScrollHeight = -1
      if (this.$refs.messageBox) {
        previousScrollHeight = this.$refs.messageBox.scrollHeight
      }
      await this.$parent.loadMoreMessages(limit)
      this.$nextTick(() => {
        this.$refs.messageBox.scrollTop +=
          this.$refs.messageBox.scrollHeight - previousScrollHeight
      })
    },
    scrollListener() {
      this.checkAndShowScrollDownShortcut()
      this.updateStickyTopDate()
    },
    updateStickyTopDate() {
      const dateLabels = document.querySelectorAll('#date-label')
      let currentLabel = null
      dateLabels.forEach(dateLabel => {
        if (this.$refs.messageBox.scrollTop >= dateLabel.offsetTop - 42) {
          dateLabel.style.opacity = '0'
          currentLabel = dateLabel
        } else {
          dateLabel.style.opacity = '1'
        }
      })
      if (currentLabel) {
        this.$refs.topLabel.style.opacity = '1'
        this.$refs.topLabel.innerText = currentLabel.innerText
      } else {
        this.$refs.topLabel.style.opacity = '0'
      }
    },
    checkAndShowScrollDownShortcut() {
      this.showScrollDownShortcut = Boolean(
        this.$refs.messageBox.scrollHeight -
          this.$refs.messageBox.scrollTop -
          160 >
          this.$refs.messageBox.clientHeight
      )
    },
    scrollToBottom() {
      this.$refs.messageBox.scrollTop = this.$refs.messageBox.scrollHeight
    },
    resetPage() {
      this.updateStickyTopDate()
      this.scrollToBottom()
      this.checkAndShowScrollDownShortcut()
    },
    hideCelebrationPopup() {
      this.showCelebrationMessage = false
      this.$root.$emit('spm-ts-end');
    },
    redirectDashboard() {
      this.showCelebrationMessage = false
      this.$root.$emit('redirect');
    },
  },
  computed: {
    getHeightBasedOnMessageBox(): string {
      return 'max-height: calc(100vh - ' + (this.activeTabHeight + 157) + 'px);'
    },
    groupedMessages() {
      return lodash.groupBy(this.messages, message => {
        return message.dateAdded.format(
          this.getCountryDateFormat('month date, year')
        )
      })
    },
    lastMessageId() {
      return (
        this.messages &&
        this.messages[this.messages.length - 1].id
      )
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.showScrollDownShortcut = false
    this.$refs.topLabel.style.opacity = '0'
    next()
  },
  beforeDestroy() {
    EventBus.$off('update-ui', this.updateUI)
    EventBus.$off('scrollToBottom', this.scrollToBottom)
  },
  mounted() {
    this.updateStickyTopDate()
    this.$root.$on('tour-end', () => {
      this.showCelebrationMessage = true
      setTimeout(() => {
        this.scrollToBottom()
      }, 300)
    })
  },
})
</script>

<style>
.message-group-date-indicator {
  margin: 2px auto 20px;
  cursor: default !important;
  z-index: 3;
  pointer-events: none;
}
.scroll-down-shortcut {
  position: absolute;
  right: 40px;
  cursor: pointer;
  padding: 8px;
  border-radius: 100%;
  background-color: #fafafa;
  z-index: 7;
}
.scroll-down-shortcut:hover {
  box-shadow: 1px 1px 4px #ddd;
}
.top-date-sticky {
  position: sticky;
  top: 10px;
  text-align: center;
  opacity: 0; /* Intial Value */
  z-index: 3;
  pointer-events: none;
}
.onboarding-celebration {
  padding: 7px 11px;
  font-size: 0.6875rem;
  font-weight: 500;
  min-width: 0;
  min-height: 30px;
  pointer-events: all !important;
}
.onboarding-celebration-header {
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
  margin-top: 5px;
  margin-bottom: 5px;
}
.onboarding-celebration-header p {
  margin-left: 4px;
  font-size: 12px;
  font-weight: 500;
}
.onboarding-celebration-body {
  align-items: center;
  justify-content: start;
  text-align: initial;
}
.onboarding-celebration-footer {
  width: 100%;
  margin-top: 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
}
.onboarding-celebration-footer div {
  margin-top: 0px;
  margin-left: 12px;
  cursor: pointer;
}
.onboarding-celebration-footer-link {
  color: #3b82f6;
}
.show-celebration {
  visibility: visible !important;
  opacity: 1 !important;
  transition-delay: 0s;
}
</style>
