<template>
  <div>
    <SideBarV2 v-if="getSideBarVersion === 'v2'"/>
    <SideBar v-else />
    <TopBar />

    <section class="hl_wrapper">
      <section
        class="hl_wrapper--inner hl_agency hl_agency-location--details"
        id="agency_location"
      >
        <div class="container-fluid">
          <div class="hl_controls">
            <div class="hl_controls--left">
              <span @click="$router.go(-1)" class="back">
                <i class="icon icon-arrow-left-2"></i>
              </span>
              <div>
                <h1 v-if="location">{{ location.name }}</h1>
                <img
                  v-if="logoUrl"
                  :src="logoUrl"
                  style="max-width: 100px; max-height: 50px"
                />
              </div>
            </div>
            <div class="hl_controls--right" v-if="user.role == 'admin'">
              <div v-if="actionLoading">
                <moon-loader
                  :loading="actionLoading"
                  color="#188bf6"
                  size="30px"
                />
              </div>
              <div v-else class="dropdown">
                <button
                  class="btn btn-primary dropdown-toggle"
                  type="button"
                  data-toggle="dropdown"
                  aria-haspopup="true"
                  aria-expanded="false"
                >
                  Actions
                </button>
                <div class="dropdown-menu dropdown-menu-right">
                  <a
                    class="dropdown-item"
                    href="javascript:void(0);"
                    @click.prevent="loadSnapshot"
                    >Load Snapshot</a
                  >
                  <!-- <a
                    class="dropdown-item"
                    href="javascript:void(0);"
                    @click.prevent="toggleSaasMode"
                    >{{ saasModeActivated || saasModePending ? 'Disable' : 'Enable' }} Saas Mode</a
                  > -->
                  <a
                    class="dropdown-item"
                    href="javascript:void(0);"
                    @click.prevent="deleteAccount"
                    >Delete</a
                  >
                </div>
              </div>
              <!-- <div class="product-revenue">
                                <h4>Product revenue</h4>
                                <p>
                                    <p>${{totalRevenue|formatNumber}}</p>
                                </p>
							</div>-->
            </div>
          </div>
          <div class="row">
            <div class="col-lg-6">
              <div class="card" v-if="location">
                <div class="card-header">
                  <h2>Account Details</h2>
                </div>
                <div class="card-body">
                  <ul class="nav nav-tabs location-details-tab" role="tablist">
                    <li
                      class="nav-item"
                      @click.prevent="
                        currentDetailsTabComponent = 'ProspectDetailsComponent'
                      "
                    >
                      <a
                        :class="{
                          active:
                            currentDetailsTabComponent ===
                            'ProspectDetailsComponent',
                        }"
                        class="nav-link"
                        id="prospect-tab"
                        data-toggle="tab"
                        href="#prospect"
                        role="tab"
                        aria-controls="prospect"
                        aria-selected="true"
                        >Account</a
                      >
                    </li>
                    <li
                      class="nav-item"
                      @click.prevent="
                        currentDetailsTabComponent = 'GeneralDetailsComponent'
                      "
                    >
                      <a
                        :class="{
                          active:
                            currentDetailsTabComponent ===
                            'GeneralDetailsComponent',
                        }"
                        class="nav-link"
                        id="general-info-tab"
                        data-toggle="tab"
                        href="#general-info"
                        role="tab"
                        aria-controls="general-info"
                        aria-selected="false"
                        >General info</a
                      >
                    </li>
                    <!-- <li class="nav-item">
                                            <a class="nav-link" id="hours-operations-tab" data-toggle="tab" href="#hours-operations" role="tab" aria-controls="hours-operations" aria-selected="false">Hours of Operations</a>
										</li>-->
                    <li
                      class="nav-item"
                      @click.prevent="
                        currentDetailsTabComponent = 'SocialDetailsComponent'
                      "
                    >
                      <a
                        :class="{
                          active:
                            currentDetailsTabComponent ===
                            'SocialDetailsComponent',
                        }"
                        class="nav-link"
                        id="social-profiles-tab"
                        data-toggle="tab"
                        href="#social-profiles"
                        role="tab"
                        aria-controls="social-profiles"
                        aria-selected="false"
                        >Social Profiles</a
                      >
                    </li>
                  </ul>

                  <div class="tab-content">
                    <component
                      v-bind:is="currentDetailsTabComponent"
                      :location="location"
                    ></component>
                  </div>
                </div>
              </div>
              <TaskListComponent :heading="true" :action="false" ref="task_list" />
              <NotesListComponent :heading="true" :action="false" />
              <!--div
                class="card-header"
                style="padding-right: 30px;margin-bottom:10px; display: grid"
                v-if="company.elizaEnabled"
              >
                <div class="row" style="margin-bottom: 20px">
                  <div class="col-10">Enable Eliza Service</div>
                  <div class="col-2">
                    <div class="toggle">
                      <input
                        type="checkbox"
                        class="tgl tgl-light"
                        id="bot_service"
                        v-model="bot_service"
                        @change="saveBotService"
                      />
                      <label class="tgl-btn" for="bot_service"></label>
                    </div>
                  </div>
                </div>
                <div class="row">
                  <div class="col-10">Allow first shot by eliza bot</div>
                  <div class="col-2">
                    <div class="toggle">
                      <input
                        type="checkbox"
                        class="tgl tgl-light"
                        id="first_shot_eliza"
                        v-model="first_shot_eliza"
                        @change="saveFirstShotEliza"
                      />
                      <label class="tgl-btn" for="first_shot_eliza"></label>
                    </div>
                  </div>
                </div>
              </div> -->

              <!-- <div class="card mt-2" v-if="company && company.isCalendarV3On">
                <div class="card-header">
                  <h3>
                    Calendar (Beta) Preferences
                  </h3>
                </div>
                <div class="row card-body mx-0">
                  <div class="col-md-6 pl-0">
                    <div style="max-width: 348px;">
                      <div class="sub-heading">Setup Instructions</div>
                      <iframe
                        src="https://www.youtube-nocookie.com/embed/SA2nJXOtvAc"
                        frameborder="0"
                        allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
                        allowfullscreen
                      ></iframe>
                    </div>
                  </div>
                  <div
                    class="cold-md-6 d-flex align-items-center justify-content-end flex-grow-1"
                    v-if="location"
                    style="flex-grow: 1;"
                  >
                    <div class="d-flex align-items-center py-2">
                      <i
                        v-show="saving"
                        class="fa fa-snowflake fa-spin text-primary mr-2"
                      ></i>
                      <label
                        v-if="saving || !location.isCalendarV3On"
                        class="pr-2 label--tgl-calendar-v3"
                      >
                        Calendar (Beta)
                      </label>
                      <div v-else-if="!saving" style="display: flex;">
                        <img
                          src="../../../assets/pmd/img/reports/icon-check.svg"
                          style="margin-right: 5px"
                          alt="check"
                          class="icon"
                        />
                        Enabled
                      </div>
                      <div v-if="!location.isCalendarV3On" class="toggle">
                        <input
                          type="checkbox"
                          class="tgl tgl-light"
                          :id="'tgl-calendar-v3-for-location-' + location.id"
                          v-model="location.isCalendarV3On"
                          @change="saveLocation(location)"
                        />
                        <label
                          class="tgl-btn"
                          :for="'tgl-calendar-v3-for-location-' + location.id"
                        ></label>
                      </div>
                    </div>
                  </div>
                </div>
              </div> -->

              <div v-show="false" ref="confirmationContent">
                <div
                  class="card-body confirmation-content pt-0 px-3 text-justify"
                >
                  <div>
                    <div>
                      <h5>Essential Considerations</h5>
                    </div>
                    <div class="faq">
                      <div class="q">
                        What does the Calendar (Private Beta) entail?
                      </div>
                      <div class="a">
                        The ability to round-robin appointments between
                        different group of agents is the main highlight of this
                        update.
                      </div>
                    </div>
                    <div class="faq">
                      <div class="q">
                        What changes does the new version of the Calendar come
                        with?
                      </div>
                      <div class="a">
                        <ul>
                          <li>
                            Conversations &gt; Schedule Appointment (New)
                            <ul>
                              <li>
                                Ability to allocate appointments in round robin
                              </li>

                              <li>
                                Ability to choose a specific user the
                                appointment to be booked with
                              </li>
                            </ul>
                          </li>

                          <li>
                            Opportunity &gt; Add Appointment (New)
                            <ul>
                              <li>
                                Ability to allocate appointments in round robin
                              </li>

                              <li>
                                Ability to choose a specific user the
                                appointment to be booked with
                              </li>

                              <li>
                                Users on the new version will lose the ability
                                to ‘view/edit’’ Appointments (created on the
                                previous version) through the Opportunity
                                Details modal- but they can continue to perform
                                all those actions on the Appointments page
                              </li>
                            </ul>
                          </li>

                          <li>
                            Scheduling &gt; Calendar/Appointments (New Filters +
                            Toggle to switch between new and old appointments)
                            <ul>
                              <li>
                                Toggle Off (New Version)

                                <ol>
                                  <li>
                                    Admin - Ability to view all User’s Calendar
                                    Appointments
                                  </li>
                                  <li>
                                    Non-admin - Ability to only view their
                                    personal appointments (Assigned data/
                                    Calendar mapping Under TEAM MANAGEMENT has
                                    no impact)
                                  </li>
                                </ol>
                              </li>

                              <li>
                                Toggle On (Old Version)

                                <ol>
                                  <li>
                                    Admin - Ability to see every User’s Calendar
                                    Appointments
                                  </li>

                                  <li>
                                    Non-admin - Ability to only view their
                                    personal appointments
                                  </li>
                                </ol>
                              </li>
                            </ul>
                          </li>

                          <li>
                            Contact Detail &gt; Appointments (New)
                            <ul>
                              <li>
                                Ability to allocate appointments in round robin
                              </li>

                              <li>
                                Ability to choose a specific user the
                                appointment to be booked with
                              </li>

                              <li>
                                Old Appointments will be displayed alongside the
                                New Appointments in the same appointments list
                              </li>
                            </ul>
                          </li>

                          <li>
                            Funnels/Websites &gt; Calendar (New)
                            <ul>
                              <li>Only New Calendar Widget can be added</li>
                            </ul>
                          </li>

                          <li>
                            Settings &gt; Calendar (Beta)
                            <ul>
                              <li>
                                Provides for all options (Teams + Services)
                                necessary to configure the new calendar
                              </li>
                            </ul>
                          </li>

                          <li>
                            Integration (New)
                            <ul>
                              <li>
                                Admins can see all integrations irrespective of
                                who created them but Users can see integrations
                                that they created for themselves
                              </li>

                              <li>
                                Admins can delete any integrations they want,
                                but Users can only remove their own
                              </li>

                              <li>
                                Re-integrating all connections may be required
                                once the Calendar Beta is enabled for a location
                              </li>
                            </ul>
                          </li>

                          <li>
                            Profile Page for Admin (New)
                            <ul>
                              <li>Surfacing it for the first time</li>
                            </ul>
                          </li>

                          <li>
                            Team Management (Available to Admins only)
                            <ul>
                              <li>
                                Ability to choose Google Calendars to ‘Add to’ +
                                ‘Conflict’ Calendars to check to compute
                                availability for every user
                              </li>
                            </ul>
                          </li>

                          <li>
                            Profile Page (All Users)
                            <ul>
                              <li>
                                Ability to choose Google Calendars to ‘Add to’ +
                                ‘Conflict’ Calendars to check to compute
                                availability for every user
                              </li>
                            </ul>
                          </li>

                          <li>
                            Triggers
                            <ul>
                              <li>The New and Old triggers will co-exist</li>

                              <li>
                                While on the call, we’ll help reorient the
                                triggers to suit the new calendar configuration
                              </li>
                            </ul>
                          </li>
                        </ul>
                      </div>
                    </div>

                    <div class="faq">
                      <div class="q">
                        What happens to our old appointments after the
                        transition?
                      </div>
                      <div class="a">
                        The old appointments can well be viewed on the
                        appointments page via a toggle switch.
                      </div>
                    </div>

                    <div class="faq">
                      <div class="q">
                        Will our old Calendar URLs continue to work after the
                        transition?
                      </div>
                      <div class="a">
                        Yes. In fact the old settings page will continue to
                        exist and so is your ability to make any changes there.
                        The old calendar widget and the new calendar widget will
                        function independently yet be in constant sync with each
                        other to ensure accurate computation of your
                        availability across the two systems. To avoid confusion,
                        we would highly encourage you to use the new URLs
                        wherever possible, so that you are burdened to monitor
                        two separate URLs for same sets of users.
                      </div>
                    </div>

                    <div class="faq">
                      <div class="q">
                        Will the old appointments be factored in when computing
                        availability for the fresh appointments?
                      </div>
                      <div class="a">
                        Yes, since there is one-way sync on, the google calendar
                        will ensure that unavailable slots are blocked out.
                      </div>
                    </div>

                    <div class="faq">
                      <div class="q">
                        Will we get to use the new version on Mobile during
                        beta?
                      </div>
                      <div class="a">
                        Not at the moment. But we are trying hard to bring it to
                        mobile as soon as possible.
                      </div>
                    </div>

                    <div class="faq">
                      <div class="q">
                        What will we see on Mobile during Beta?
                      </div>
                      <div class="a">
                        Old Calendar Version + Old Appointments
                      </div>
                    </div>
                  </div>
                  <div class="text-left text-note">
                    If you are comfortable with the aforementioned changes,
                    please hit ‘I Agree’ and get started with Calendar (Beta) on
                    this location
                  </div>
                </div>
              </div>

              <!-- <div class="card" v-if="location">
                                <div class="card-header">
                                    <h2>Products</h2>
                                </div>
                                <div class="card-body">
                                    <div class="hl_products">
                                        <div class="hl_products--item">
                                            <div class="icon --green">
                                                <img src="../../../assets/pmd/img/icon-listing.svg">
                                            </div>
                                            <div class="hl_products--item-body">
                                                <div class="hl_products--item-text">
                                                    <h4>Listings</h4>
                                                    <ul class="list-inline">
                                                        <li class="list-inline-item">Revenue:
                                                            <strong>$100</strong>/month</li>
                                                        <li class="list-inline-item">Cost: $50/month</li>
                                                    </ul>
                                                </div>
                                                <div class="toggle">
                                                    <input type="checkbox" class="tgl tgl-light" id="product1" @click="setListing" :checked="location.productStatus.listings">
                                                    <label class="tgl-btn" for="product1"></label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="hl_products--item">
                                            <div class="icon --yellow">
                                                <img src="../../../assets/pmd/img/icon-star.svg">
                                            </div>
                                            <div class="hl_products--item-body">
                                                <div class="hl_products--item-text">
                                                    <h4>Reputation management</h4>
                                                    <ul class="list-inline">
                                                        <li class="list-inline-item">Revenue:
                                                            <strong>$300</strong>/month</li>
                                                        <li class="list-inline-item">Cost: $150/month</li>
                                                    </ul>
                                                </div>
                                                <div class="toggle">
                                                    <input type="checkbox" class="tgl tgl-light" id="product2" @click="setReviews" :checked="this.location.productStatus.reviews">
                                                    <label class="tgl-btn" for="product2"></label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="hl_products--item">
                                            <div class="icon --blue">
                                                <img src="../../../assets/pmd/img/icon-conversation.svg">
                                            </div>
                                            <div class="hl_products--item-body">
                                                <div class="hl_products--item-text">
                                                    <h4>Conversations</h4>
                                                    <ul class="list-inline">
                                                        <li class="list-inline-item">Revenue:
                                                            <strong>$200</strong>/month</li>
                                                        <li class="list-inline-item">Cost: $50/month</li>
                                                    </ul>
                                                </div>
                                                <div class="toggle">
                                                    <input type="checkbox" class="tgl tgl-light" id="product3" @click="setConversations" :checked="this.location.productStatus.conversations">
                                                    <label class="tgl-btn" for="product3"></label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="hl_products--item">
                                            <div class="icon --pink">
                                                <img src="../../../assets/pmd/img//icon-social.svg">
                                            </div>
                                            <div class="hl_products--item-body">
                                                <div class="hl_products--item-text">
                                                    <h4>Social media</h4>
                                                    <ul class="list-inline">
                                                        <li class="list-inline-item">Revenue:
                                                            <strong>$350</strong>/month</li>
                                                        <li class="list-inline-item">Cost: $100/month</li>
                                                    </ul>
                                                </div>
                                                <div class="toggle">
                                                    <input type="checkbox" class="tgl tgl-light" id="product4" @click="setSocialMedia" :checked="this.location.productStatus.social">
                                                    <label class="tgl-btn" for="product4"></label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
							</div>-->
            </div>
            <div class="col-lg-6">
              <div class="card">
                <div class="card-header">
                  <h2>Activity</h2>
                </div>
                <div class="card-body">
                  <ul class="nav nav-tabs activity-tab" role="tablist">
                    <!-- <li class="nav-item">
                                            <a class="nav-link active" id="email-tab" data-toggle="tab" href="#email" role="tab" aria-controls="email" aria-selected="true">Email</a>
                                        </li>
                                        <li class="nav-item">
                                            <a class="nav-link" id="call-tab" data-toggle="tab" href="#call" role="tab" aria-controls="call" aria-selected="false">Call</a>
										</li>-->
                    <li
                      class="nav-item"
                      @click.prevent="currentActivitiesTab = 'NoteComponent'"
                    >
                      <a
                        :class="{
                          active: currentActivitiesTab === 'NoteComponent',
                        }"
                        class="nav-link"
                        id="note-tab"
                        data-toggle="tab"
                        href="#note"
                        role="tab"
                        aria-controls="note"
                        aria-selected="false"
                        >Note</a
                      >
                    </li>
                    <li
                      class="nav-item"
                      @click.prevent="currentActivitiesTab = 'TaskComponent'"
                    >
                      <a
                        :class="{
                          active: currentActivitiesTab === 'TaskComponent',
                        }"
                        class="nav-link"
                        id="task-tab"
                        data-toggle="tab"
                        href="#task"
                        role="tab"
                        aria-controls="task"
                        aria-selected="false"
                        >Task</a
                      >
                    </li>
                  </ul>
                  <component v-bind:is="currentActivitiesTab" @task_saved="$refs.task_list.fetchData()"></component>
                  <div class="hl_activity-history" v-if="activities.length > 0">
                    <h3>History</h3>
                    <!-- <div class="hl_activity-history--item">
                                            <i class="icon icon-mail --blue"></i>
                                            <div class="hl_activity-history--item-header">
                                                <h4>Sent email</h4>
                                                <p>Subject:
                                                    <strong>Conversations</strong>
                                                </p>
                                            </div>
                                            <p>You have finished building your own website. You have introduced your company and presented your products and services. You have added propositions and…</p>
                                            <div class="hl_activity-history--item-footer">
                                                <p>By:
                                                    <strong>James Robertson</strong>
                                                </p>
                                                <p>Aug 12, 2018 at 8:30 AM</p>
                                            </div>
                                        </div>
                                        <div class="hl_activity-history--item">
                                            <i class="icon icon-phone --green"></i>
                                            <div class="hl_activity-history--item-header">
                                                <h4>Call made
                                                    <span>12:45</span>
                                                </h4>
                                            </div>
                                            <div class="hl_activity-history--item-footer">
                                                <p>By:
                                                    <strong>James Robertson</strong>
                                                </p>
                                                <p>Aug 12, 2018 at 8:30 AM</p>
                                            </div>
										</div>-->
                    <ActivityCard
                      v-for="activity in activities"
                      :key="activity.id"
                      :activity="activity"
                    />
                    <!-- <div class="hl_activity-history--item">
                                            <i class="icon icon-task --yellow"></i>
                                            <div class="hl_activity-history--item-header">
                                                <h4>Added Task</h4>
                                            </div>
                                            <p>I hate peeping Toms. For one thing they usually step all over the hedges and plants on the side of someone’s house killing them and setting back the…</p>
                                            <div class="hl_activity-history--item-footer">
                                                <p>By:
                                                    <strong>James Robertson</strong>
                                                </p>
                                                <p>Aug 12, 2018 at 8:30 AM</p>
                                            </div>
										</div>-->
                  </div>
                </div>
              </div>
              <location-active-services
                :location="location ? location.data : ''"
              />
              <div class="card">
                <div
                class="card-header"
                style="padding-right: 30px"
                v-if="
                  company &&
                  company.hipaaCompliance &&
                  user.role == 'admin' &&
                  user.type == 'agency'
                "
              >
                <img
                  src="https://www.whoa.com/wp-content/uploads/2018/08/hipaa-logo.png"
                  class="h-20"
                />
                <div class="row mr-0" v-if="location">
                  <div class="d-flex py-2" v-if="!location.hipaaCompliance">
                    <label class="pr-2"> HIPAA </label>

                    <div class="toggle">
                      <UIToggle
                        id="hipaa_enabled"
                        :value="location.hipaaCompliance"
                        @input="setHIPAAValue"
                      />
                      <label class="tgl-btn" for="hipaa_enabled"></label>
                    </div>
                  </div>
                  <div v-else style="display: flex">
                    <CheckIcon
                      style="margin-right: 5px"
                      alt="check"
                      class="icon"
                    />
                    Subscribed
                  </div>
                </div>
                </div>
              </div>
              
            </div>

          </div>
          <div class="separator">
            <span>Reselling</span>
          </div>
          <div class="row" v-if="location">
            <div class="col-lg-6"> <YextOffer  :location="location" /></div>
            <div class="col-lg-6"> <WordpressOffer :location="location" /></div>
          </div>
          <AccountDetailSaas
            v-if="locationId && location && company && agencyInSaasPlan"
            :location="location"
            :locationId="locationId"
            :company="company"
          />
        </div>
      </section>
      <!-- END of .hl_agency-location--details -->
    </section>
    <!-- <LoadSnapshotModal :values="loadSnapshotModal" @hidden="loadSnapshotModal={visible: false}"/> -->
    <PushCloneAccount
      :values="pushCloneModal"
      @hidden="pushCloneModal = { visible: false }"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import { cloneDeep } from 'lodash'
import { GenericDefaultElizaFAQ } from '@/util/generic_default_eliza_faq'

import {
  Location,
  LocationStatus,
  Activity,
  User,
  ActivityType,
  ActionType,
  Company,
  Contact,
  LogicalEliza,
  TwilioAccount
} from '@/models'
import { UserState, CompanyState } from '@/store/state_models'
// import {UxMessage} from '@/util/ux_message'

import TopBar from '../../components/TopBar.vue'
import SideBar from '../../components/agency/SideBar.vue'
import SideBarV2 from '../../components/sidebar/SideBar.vue'
import defaults from '@/config'
const ProspectDetailsComponent = () =>
  import(
    /* webpackChunkName: "account-prospect-details" */ '@/pmd/components/agency/ProspectDetailsComponent.vue'
  )
const GeneralDetailsComponent = () =>
  import(
    /* webpackChunkName: "account-general-details" */ '@/pmd/components/agency/GeneralDetailsComponent.vue'
  )
const SocialDetailsComponent = () =>
  import(
    /* webpackChunkName: "account-social-details" */ '@/pmd/components/agency/SocialDetailsComponent.vue'
  )
const NoteComponent = () =>
  import(
    /* webpackChunkName: "account-activity-notes" */ '@/pmd/components/agency/NoteComponent.vue'
  )
const TaskComponent = () =>
  import(
    /* webpackChunkName: "account-activity-task" */ '@/pmd/components/agency/TaskComponent.vue'
  )
const TaskListComponent = () =>
  import(
    /* webpackChunkName: "account-task-list" */ '@/pmd/components/agency/TaskListComponent.vue'
  )
const NotesListComponent = () =>
  import(
    /* webpackChunkName: "account-notes-list" */ '@/pmd/components/agency/NotesListComponent.vue'
  )
const ActivityCard = () =>
  import(
    /* webpackChunkName: "account-activity" */ '@/pmd/components/agency/ActivityCard.vue'
  )
const LoadSnapshotModal = () =>
  import(
    /* webpackChunkName: "account-load-snapshot" */ '@/pmd/components/agency/LoadSnapshotModal.vue'
  )
const LocationActiveServices = () =>
  import(
    /* webpackChunkName: "account-active-services" */ '@/pmd/pages/agency/billing/LocationActiveServices.vue'
  )
const YextOffer = () =>
  import(
    /* webpackChunkName: "yext-location-level-offer-component" */ '@/pmd/components/agency/reselling/YextLocationLevelOfferComponent.vue'
  )
const WordpressOffer = () =>
  import(
    /* webpackChunkName: "yext-location-level-offer-component" */ '@/pmd/components/agency/reselling/wordpress/WordpressLocationLevelOfferComponent.vue'
  )
const PushCloneAccount = () =>
  import('@/pmd/components/agency/PushCloneAccount.vue')

import AccountDetailSaas from './AccountDetailSaas.vue'
import CheckIcon from '@/assets/pmd/img/reports/icon-check.svg'

let unsubscribeLocation: () => void
let unsubscribeActivities: () => void
import firebase from 'firebase/app'
import axios from 'axios'

export default Vue.extend({
  components: {
    TopBar,
    SideBar,
    ProspectDetailsComponent,
    GeneralDetailsComponent,
    SocialDetailsComponent,
    NoteComponent,
    TaskComponent,
    ActivityCard,
    TaskListComponent,
    NotesListComponent,
    LoadSnapshotModal,
    LocationActiveServices,
    PushCloneAccount,
    AccountDetailSaas,
    YextOffer,
    WordpressOffer,
    CheckIcon,
    SideBarV2
  },

  data() {
    return {
      saving: false,
      locationId: '',
      location: undefined as Location | undefined,
      loading: false,
      currentDetailsTabComponent: 'ProspectDetailsComponent',
      currentActivitiesTab: 'NoteComponent',
      activities: [] as Activity[],
      loadSnapshotModal: { visible: false } as { [key: string]: any },
      actionLoading: false,
      pushCloneModal: { visible: false } as { [key: string]: any },
      bot_service: false,
      //first_shot_eliza: true  //Removing it, as the flow is completely changed for enabling eliza
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
    agencyInSaasPlan(): boolean {
      return this.$store.getters['company/inSaasPlan']
    },
    totalRevenue(): number {
      if (!this.location) return 0
      let total = 0
      if (this.location.productStatus.listings) total += 100
      if (this.location.productStatus.reviews) total += 300
      if (this.location.productStatus.conversations) total += 200
      return total
    },
    logoUrl(): string {
      if (this.location && this.location.logoUrl) {
        return this.location.logoUrl
      } else {
        return ''
      }
    },
    getSideBarVersion(): string {
      return this.$store.getters['sidebarv2/getVersion'] 
    },
  },
  watch: {
    '$route.params.account_id': function (id) {
      this.locationId = id
      this.location = undefined
      this.fetchData()
    },
    '$route.query.task_id': function (id) {
      if (id) {
        this.currentActivitiesTab = 'TaskComponent'
      }
    },
    '$route.query.note_id': function (id) {
      if (id) {
        this.currentActivitiesTab = 'NoteComponent'
      }
    },
  },
  async created() {
    this.locationId = this.$router.currentRoute.params.account_id
    let companyId = this.company && this.company.id
    await Promise.all([
      this.$store.dispatch('smtpServices/syncAll', this.locationId),
      this.$store.dispatch('mailgunServices/syncAll', this.locationId),
      this.$store.dispatch('smtpServices/companySyncAll', companyId),
      this.$store.dispatch('mailgunServices/companySyncAll', companyId)
    ])
    this.location = await Location.getById(this.locationId);
    if (this.location) {
      this.bot_service = this.location.botService
    }
    // if (this.location && this.location.hasOwnProperty('firstShotEnabled')) {
    //   this.first_shot_eliza = this.location.firstShotEnabled
    // }
    if (this.$route.query.task_id) {
      this.currentActivitiesTab = 'TaskComponent'
    } else if (this.$route.query.note_id) {
      this.currentActivitiesTab = 'NoteComponent'
    }

    this.fetchData()
  },
  methods: {
    async saveBotService() {
      try {
        if (this.location) {
          if (this.bot_service) {
            // Checking that default eliza exist or not for location..
            const logicalElizaRes = await LogicalEliza.getDefaultEliza(
              this.location.id
            )

            // Creating default Eliza for location...
            if (!logicalElizaRes) {
              let logicalEliza = new LogicalEliza()
              logicalEliza.elizaId = defaults.genericElizaId
              logicalEliza.locationId = this.location.id
              logicalEliza.logicalName = 'Default Eliza'
              logicalEliza.defaultEliza = true
              logicalEliza.faq = cloneDeep(GenericDefaultElizaFAQ)
              await logicalEliza.save()
              if (!this.location.defaultLogicalEliza) {
                this.location.defaultLogicalEliza = logicalEliza.id
              }
            }
            // const {data} = await this.humanRollOverService.post(`/location/highlevel`, { "locationId": this.location.id });
          } else {
            // const {data} = await this.humanRollOverService.delete(`/location/highlevel/` + this.location.id);
          }
          this.location.botService = this.bot_service
          await this.location.ref.update({
            default_logical_eliza: this.location.defaultLogicalEliza,
            bot_service: this.bot_service,
            date_updated: firebase.firestore.FieldValue.serverTimestamp(),
          })
          if (
            this.location.botService &&
            !this.company.elizaFirstLocationEnabled
          ) {
            this.company.elizaFirstLocationEnabled = true
            await this.company.ref.update({
              eliza_first_location_enabled: this.bot_service,
              date_updated: firebase.firestore.FieldValue.serverTimestamp(),
            })
          }
        }
      } catch (err) {
        console.error(err)
      }
    },
    // async saveFirstShotEliza() {
    //   if (this.location) {
    //     this.location.firstShotEnabled = this.first_shot_eliza
    //     await this.location.ref.update({
    //       first_shot_enabled: this.first_shot_eliza,
    //       date_updated: firebase.firestore.FieldValue.serverTimestamp()
    //     })
    //   }
    // },
    async saveLocation(location: Location) {
      this.$nextTick(() => {
        // location.isCalendarV3On = false // Just to make it works even accidentally modal dialog gets closed by clicking out side of the dialog
      })
      this.$uxMessage(
        'confirmation',
        this.$refs.confirmationContent.innerHTML,
        async res => {
          if (res === 'ok') {
            this.saving = true
            // location.isCalendarV3On = true
            await location.save()
            this.saving = false
          }
        },
        { isMessageInRawHTML: true, okButton: 'I Agree' }
      )
    },
    async setHIPAAValue(event) {
      if (!this.location) return
      if (
        !this.location.hipaaCompliance &&
        confirm(
          'Are you sure you want to enable HIPAA? You will not be able to disable it then.'
        )
      ) {
        this.location.hipaaCompliance = true
        await this.location.save()
      } else {
        event.preventDefault()
        event.stopPropagation()
      }
    },
    loadSnapshot() {
      console.log('loader')
      this.pushCloneModal = {
        visible: true,
        locationId: this.locationId,
        // companyId: this.authUser.companyId,
        // snapshotId: account.id
      }

      // this.loadSnapshotModal = {
      // 	visible: true,
      // 	locationId: this.locationId,
      // };
    },
    async deleteAccountConfirmed({ deleteTwilio, disableSaaS }: {
      deleteTwilio: boolean
      disableSaaS?: boolean
    }) {
      this.actionLoading = true

      if (disableSaaS) {
        try {
          await this.saasService.get(
            `/saas-config/${this.locationId}/disable-saas?debugMode=true`
          )
        } catch (error) {
          console.warn('Error from saas service', error)
        }
      }

      try {
        if (deleteTwilio)
          await this.$http.delete(
            `/twilio/account?location_id=${this.location.id}`
          )
      } catch (error) {
        console.log(error)
      }
      await this.$http.delete(`/location/${this.location.id}`)
      await this.$http.post('/stripe/sync_locations', {
        company_id: this.company.id,
      })
      this.actionLoading = false
      this.$router.go(-1)
    },
    async deleteAccount() {
      if (!this.location) return

      const inSaaSMode = this.location.settings.saas_settings?.saas_mode === 'activated'

      let confirmMessage = 'Are you sure you want to delete this account?'
      let closeAccountMessage = `Do you wish to close the Twilio subaccount too?
						"When you close a subaccount, Twilio will release all phone numbers assigned to it and shut it down completely.
						You can't ever use a closed account to make and receive phone calls or send and receive SMS messages.
						It's closed, gone, kaput – you cannot reopen a closed account." - Twilio`
      if (this.location.hipaaCompliance)
        confirmMessage =
          'This account is HIPAA Compliant. All of your contact details will be deleted permanently. Are you sure you want to delete this location?'
      this.$uxMessage('consent', confirmMessage, async response => {
        if (response === 'ok') {
          this.$nextTick(async () => {

            const twilioAccount = await TwilioAccount.getByLocationId(this.locationId)
            const disableSaaS = inSaaSMode

            // auto delete twilio account if managed by GHL
            if (twilioAccount && twilioAccount.underGHLAccount) {
              try {
                await this.$http.get(
                  `/twilio/queue-delete-account?locationId=${this.locationId}`
                )
              } catch (error) {
                console.warn(`Error while deleting telephony account for: ${this.locationId}`);
                console.error(error);
              }

              try {
                await this.saasService.get(`/isv-mode/location-deleted/${this.locationId}`)
              } catch (error) {
                console.warn(`Error while disabling location isv setup for: ${this.locationId}`);
                console.error(error);
              }

              await this.deleteAccountConfirmed({ deleteTwilio: false, disableSaaS })
            } else {
              // ask for consent
              this.$uxMessage(
                'consent',
                closeAccountMessage,
                async (res: string) => {
                  // don't delete twilio account if rebilling is on
                  const deleteTwilio = twilioAccount && res === 'ok'
                  await this.deleteAccountConfirmed({ deleteTwilio, disableSaaS })
                },
                { consentString: 'Close', cancelButton: 'No' }
              )
            }
          })
        }
      })
    },
    async setReviews(event: any) {
      const checked: boolean = event.target.checked
      if (!this.location) {
        event.target.checked = !checked
        return
      }

      if (
        checked &&
        confirm(
          'You will be billed for this product. Turn on reviews for this location?'
        )
      ) {
        this.location.productStatus.reviews = checked
        await this.location.save()
        Activity.product(
          this.location.companyId,
          this.location.id,
          this.user.id,
          'review',
          checked ? ActionType.ADDED : ActionType.DELETED
        )
        await this.convertIfProspect(checked)
        await this.updateStripeSubscriptions()
      } else {
        event.target.checked = !checked
        if (!checked) {
          alert(
            'Please email <NAME_EMAIL> to turn off this product.'
          )
        }
      }
    },
    async setListing(event: any) {
      const checked: boolean = event.target.checked
      if (!this.location) {
        event.target.checked = !checked
        return
      }
      if (!this.company.cardLast4) {
        event.target.checked = !checked
        alert(
          'You need to enable billing to turn this product on. Add a credit card in the settings page.'
        )
        return
      }

      if (
        checked &&
        confirm(
          'You will be billed for this product. Turn on listings for this location?'
        )
      ) {
        try {
          if (checked) {
            let resp = await this.$http.post('/api/yext/' + this.location.id)
          } else {
            //TODO delete yext location?
          }
          this.location.productStatus.listings = checked
          await this.location.save()
          Activity.product(
            this.location.companyId,
            this.location.id,
            this.user.id,
            'listing',
            checked ? ActionType.ADDED : ActionType.DELETED
          )
          this.convertIfProspect(checked)
          await this.updateStripeSubscriptions()
        } catch (err) {
          console.error(err)
          event.target.checked = !checked
        }
      } else {
        event.target.checked = !checked
        if (!checked) {
          alert(
            'Please email <NAME_EMAIL> to turn off this product.'
          )
        }
      }
    },
    async setConversations(event: any) {
      const checked: boolean = event.target.checked
      if (!this.location) {
        event.target.checked = !checked
        return
      }
      if (!this.company.cardLast4) {
        event.target.checked = !checked
        alert(
          'You need to enable billing to turn this product on. Add a credit card in the settings page.'
        )
        return
      }

      if (
        checked &&
        confirm(
          'You will be billed for this product. Turn on conversations for this location?'
        )
      ) {
        try {
          // if (checked) {
          //     let resp = await this.$http.post("/api/twilio/set_up_phone_number/" + this.location.id);
          // } else {
          //     //TODO delete twilio account?
          // }
          this.location.productStatus.conversations = checked
          await this.location.save()
          Activity.product(
            this.location.companyId,
            this.location.id,
            this.user.id,
            'conversation',
            checked ? ActionType.ADDED : ActionType.DELETED
          )
          this.convertIfProspect(checked)
          await this.updateStripeSubscriptions()
        } catch (err) {
          event.target.checked = !checked
        }
      } else {
        event.target.checked = !checked
        if (!checked) {
          alert(
            'Please email <NAME_EMAIL> to turn off this product.'
          )
        }
      }
    },
    async setSocialMedia(event: any) {},
    async updateStripeSubscriptions() {
      let resp = await this.$http.get('/api/stripe/updateSubscriptions')
      if (resp.status !== 200) {
        resp = await this.$http.get('/api/stripe/updateSubscriptions')
      }
    },
    async convertIfProspect(checked: boolean) {
      if (
        checked &&
        this.location &&
        this.location.status == LocationStatus.PROSPECT
      ) {
        await this.location.convertToAccount()
      }
    },
    async fetchData() {
      if (!this.locationId) return

      if (unsubscribeLocation) unsubscribeLocation()
      unsubscribeLocation = Location.getByIdRealtime(
        this.locationId
      ).onSnapshot(snapshot => {
        this.location = new Location(snapshot)
      })

      if (unsubscribeActivities) unsubscribeActivities()
      unsubscribeActivities = Activity.getAllActivitiesByAccountId(
        this.locationId
      ).onSnapshot(snapshot => {
        this.activities = snapshot.docs.map(d => new Activity(d))
      })
    },
    beforeDestroy() {
      if (unsubscribeLocation) unsubscribeLocation()
      if (unsubscribeActivities) unsubscribeActivities()
    },
    async save() {
      if (!this.location) return

      if (this.loading) return

      await this.$validator.validateAll()
      if (this.errors.any()) {
        return Promise.resolve(true)
      }

      this.loading = true
      await this.location.save()
      this.loading = false
    },
  },
})
</script>

<style lang="scss">
.hl_activity-history {
  clear: both;
}

.save {
  float: right;
}
.error {
  color: #a94442;
}
.v-moon.v-moon1 {
  margin: auto;
}
.spinner {
  width: 85px;
  margin: 10px;
}
.back {
  cursor: pointer;
  color: #188bf6;
}
.label--tgl-calendar-v3 {
  margin-bottom: 0px;
  font-size: 0.875rem;
}
.sub-heading {
  opacity: 0.7;
}
.confirmation-content {
  .faq {
    .q {
      font-size: 1.2em;
      font-weight: 500;
      padding-top: 8px;
    }
    .a {
      opacity: 0.9;
      font-size: 1.1em;
    }
  }
  .text-note {
    margin-top: 15px !important;
    font-size: 1.2em;
    font-weight: 400;
    border: 1px solid lightgray;
    border-radius: 10px;
    padding: 10px !important;
  }
}
.separator {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    padding: 16px;
    span {
      background: #f2f7fa;
      padding: 0 1rem;
      color: rgba(0, 0, 0, 0.3);
    }
    span::after{
      content: '';
      width: 100%;
      height: 1px;
      top: 50%;
      left: 0;
      background: #d9dee1;
      position: absolute;
      z-index: -1;
    }
}
</style>
