<template>
 <div class="uxmodal">
  <b-modal id="alertModal" ref="modal-sm-d" size="sm" hide-footer @hide="clear">
    <template v-slot:modal-title v-if="item">
      <div class="d-inline-block align-middle">
        <h5 class="d-inline-block mx-2 --gray">
          <i class="fa fa-times-circle-o mx-2" aria-hidden="true" v-if="item.type === error"></i>
          <i class="fa fa-exclamation-circle mx-2" aria-hidden="true" v-if="item.type === warning"></i>
          <i class="fa fa-info-circle mx-2" aria-hidden="true" v-if="item.type === info"></i>
          <i class="fa fa-check-circle mx-2" aria-hidden="true" v-if="item.type === confirmation"></i>
          <i class="fa fa-user-check mx-2" aria-hidden="true" v-if="item.type === consent"></i>
          <span class="capitalize">{{item.type}}</span>
        </h5>
      </div>
    </template>
    <template v-if="item">
      <div class="mx-5 form-group consent-action" v-if="item.type === consent">
        <div class="mx-2 mb-4" v-html="item.message"></div>
        <label>Confirm by typing <font color="#e93d3d">{{ consentString }}</font> below.</label>
        <UITextInput
          :placeholder="consentString"
          v-model="consentInput"
          name="consent"
          data-lpignore="true"
          @input="consentError = null"
        />
        <span v-show="consentError" class="error">{{ consentError }}</span>

        <button type="button" class="btn btn-sm btn-blue mx-2 mt-4 mb-2 float-right" @click.stop.prevent="ok">Confirm {{consentString}}</button>
        <button type="button" class="btn btn-sm btn-light mx-2 float-right" @click.stop.prevent="cancel">{{item.options && item.options.cancelButton ||'Cancel'}}</button>
      </div>
      <div v-else>
        <div v-if="!isMessageInRawHTML" class="mx-2">{{item.message}}</div>
        <div v-else v-html="item.message"></div>

        <div class="flex justify-end mt-4 py-2" style="width:100%" >
          <UIButton
            use="outline"
            v-if="item.type === confirmation"
            @click.stop.prevent="cancel"
          >{{item.options && item.options.cancelButton ||'Cancel'}}</UIButton>

          <UIButton
            use="tertiary"
            class="ml-2"
            @click.stop.prevent="ok"
          >
            {{item.options && item.options.okButton ||'Ok'}}
          </UIButton>
        </div>
      </div>
      <pre
        v-if="item.stack"
        class="my-4 mx-4"
        style="max-height:250px;overflow:auto;background:lightgray"
      >{{item.stack}}</pre>
    </template>
  </b-modal>
 </div>
</template>

<script lang="ts">
import Vue from 'vue'
import * as Ux from '@/util/ux_message'

export default Vue.extend({
  data() {
    return {
      item: undefined as Ux.UxMessage | undefined,
      consentInput: null as string,
      consentError: null as string,
      isMessageInRawHTML: false as boolean
    }
  },
  computed: {
    error() {
      return Ux.UxMessageType.ERROR
    },
    warning() {
      return Ux.UxMessageType.WARNING
    },
    info() {
      return Ux.UxMessageType.INFO
    },
    confirmation() {
      return Ux.UxMessageType.CONFIRMATION
    },
    consent() {
      return Ux.UxMessageType.CONSENT
    },
    consentString() {
      return this.item.options && this.item.options.consentString || 'Delete'
    }
  },
  methods: {
    show(uxMessage: Ux.UxMessage, isMessageInRawHTML: boolean = false) {
      this.item = uxMessage
      this.isMessageInRawHTML = isMessageInRawHTML
      this.$refs['modal-sm-d'].show()
    },
    clear() {
      this.item = undefined
      this.consentInput = null
      this.consentError = null
    },
    async ok(){
      if (this.item.type === this.consent) {
        if (!this.consentInput || this.consentInput.toLowerCase() !== this.consentString.toLowerCase()) {
          this.consentError = `Please type ${this.consentString} above or cancel this action.`
          return false;
        }
      }
      this.item.Ok();
      this.clear();
      this.$refs['modal-sm-d'].hide();
    },
    cancel(){
        this.item.Cancel();
        this.clear();
        this.$refs['modal-sm-d'].hide();
    },
  }
})
</script>

<style scoped>
.consent-action {
  display: flex;
  align-items: center;
  flex-direction: column;
}
</style>
