import Vue from 'vue'
import { Module, ActionContext } from 'vuex'
import { RootState, TeamState } from './state_models'
import Team from '../models/team'
import { sortBy } from 'lodash'

let calendarProviderListner
export const teams: Module<TeamState, RootState> = {
  namespaced: true,
  state: { teams: [], locationId: undefined },
  getters: {
    getById: (state: TeamState) => {
      return (id: string) => state.teams.find(x => x.id === id)
    },
    calendarProviders(state: TeamState) {
      return state.teams.filter(x => x.slug) //
    }
  },
  mutations: {
    set: (
      state: TeamState,
      payload: { teams: any[]; locationId: string }
    ) => {
      state.teams = payload.teams
      if (payload.locationId) {
        state.locationId = payload.locationId
      }
    },
    clearAll(state: TeamState) {
      if (calendarProviderListner) {
        calendarProviderListner()
      }

      state.teams = []
      state.locationId = undefined
    }
  },
  actions: {
    /**
     * This function mainly created for making sure we'll get the promise in any way to make sure we'll get the data before we start using location and team.
     * @param context  
     * @param locationId 
     * @returns boolean
     */
    async hasAnyCalendarProviders(
      context: ActionContext<TeamState, RootState>, locationId: string
    ){
        return new Promise(async(resolve, reject) => {
          if (!locationId) {
            resolve(false)
          }
          // Let's match the received location id with state location id
          if (context.state.locationId === locationId) {
            resolve(context.state.teams.length > 0)
          }
          // Seems like we don't have any data in state so let's fetch it and store it in state
          let snapshot = Team.fetchAllByLocation(locationId).get()
          snapshot.then(data => {
            const teams = data.docs.map(d => ({ id: d.id, ...d.data() }))
            const sortedTeam = sortBy(teams, (t) => t.calendar_provider_name ? t.calendar_provider_name.toUpperCase() : '')
            context.commit('set', {
              locationId: locationId,
              teams: sortedTeam
            })
             resolve(sortedTeam.length>0)
          }).catch(error =>{
            resolve(context.state.teams.length > 0) 
          })
        });
    },
    async syncAll(
      context: ActionContext<TeamState, RootState>,
      locationId: string
    ) {
      if (!locationId) {
        return
      }


      if (context.state.locationId === locationId) {
        return context.state.teams
      }

      context.commit('clearAll')

      return new Promise((resolve, reject) => {

        if (calendarProviderListner) {
          calendarProviderListner()
        }

        calendarProviderListner = Team.fetchAllByLocation(
          locationId
        ).onSnapshot(snapshot => {
          const teams = snapshot.docs.map(d => ({ id: d.id, ...d.data() }))
          const sortedTeam = sortBy(teams, (t) => t.calendar_provider_name ? t.calendar_provider_name.toUpperCase() : '')

          context.commit('set', {
            locationId: locationId,
            teams: sortedTeam
          })

          resolve(context.state.teams)
        })
      })
    },
    logout: {
      root: true,
      handler: (context: ActionContext<TeamState, RootState>) => {
        context.commit('clearAll')
      }
    },
    clearAll: {
      handler: (context: ActionContext<TeamState, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
