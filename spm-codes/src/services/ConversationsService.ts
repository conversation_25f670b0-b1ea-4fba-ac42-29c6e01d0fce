import axios from 'axios'
import servicesConfig from '../config/services'

const responseBody = res => res.data

const getHeader = () => {
    return {
        Channel: 'APP',
        Source: 'WEB_USER',
        Version: '2021-04-15',
    }
}

const requests = function (baseUrl) {
    return {
        get: (url, options = {}) =>
            axios({
                url: baseUrl + url,
                method: 'GET',
                headers: getHeader(),
                ...options
            }).then(responseBody),
        post: (url, body) =>
            axios({
                url: baseUrl + url,
                method: 'POST',
                headers: getHeader(),
                data: body,
            }).then(responseBody),
        put: (url, body) =>
            axios({
                url: baseUrl + url,
                method: 'PUT',
                headers: getHeader(),
                data: body,
            }).then(responseBody),
        delete: url =>
            axios({
                url: baseUrl + url,
                method: 'DELETE',
                headers: getHeader(),
            }).then(responseBody),
    }
}

const ConversationsService = {
  message: (body) =>
    requests(servicesConfig.Conversations).post('/message', body)
}

export default ConversationsService
