<template>
    <table style="width: 100%;">
      <tbody>
        <tr v-for='(pickerOption) in picklistOptions'  :key="pickerOption.id">
          <template v-if="isTW">
            <td>
              <label :for="pickerOption.id" class="mb-0 block text-sm">
                {{pickerOption.label}}
              </label>
            </td>
            <td>
              <div class="option col-8 pl-0 mb-2" >
                <UITextInput
                  v-if="type==='input'"
                  type="text"
                  placeholder="Fill values"
                  :id="pickerOption.id"
                  :name="name"
                  :disabled="disabled"
                  v-model="textboxModel[pickerOption.id]"
                  @input="setValues"
                  />
              </div>
            </td>
          </template>
          <template v-else>
            <td>
              <label v-bind:for="pickerOption.id">{{pickerOption.label}}</label>
            </td>
            <td>
              <div class="option col-8 pl-0 mb-2" >
                <input
                  v-if="type==='input'"
                  type="text"
                  class="form-control"
                  placeholder="Fill values"
                  :class="customClass"
                  :id="pickerOption.id"
                  :name="name"
                  :disabled="disabled"
                  v-model="textboxModel[pickerOption.id]"
                  @keyup="setValues"
                >
              </div>
            </td>
          </template>
        </tr>
      </tbody>
    </table>
</template>

<script lang="ts">
import Vue from "vue";

export default Vue.extend({
  props: {
    type: {
      default: "input"
    },
    name: String,
    value: {
      type: Object
    },
    currentLocationId: String,
    placeholder: String,
    customClass: {
      default: ""
    },
    picklistOptions: {
      type: Array
    },
    'disabled' : {
      default : false
    },
    isTW: {
      default: false
    }
  },
  computed: {
    textboxModel: {
      get: function(): [] {
       return this.value || {};
      },
      set: function(value) {
        this.$emit("update:value", value);
      }
    }
  },
  methods: {
    setValues(){
      this.$emit("keyup", true);
      this.$emit("input", this.textboxModel);
    }
  }
});
</script>

<style>
table.table.table-borderless th {
  border: 0;
}
table.table.table-borderless tr th:first-child {
  padding-right: 0;
  text-align: right;
  vertical-align: middle;
  max-width: 150px;
}
.textbox-list .option{
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0;
}
.textbox-list label{
  width: 65px;
}

</style>
