/* eslint-disable @typescript-eslint/camelcase */
// prettier-ignore
import firebase from 'firebase/app'
import * as lodash from 'lodash'
import moment, { Moment } from 'moment-timezone'
import { IOpSpecs, ISprintSchedule } from './bulkreq_interfaces'
import { IContactsQuery } from './contacts_server'
import { ICombinedStats, IEventStats } from '../../../../models/evenstats'
import { Utils } from '../../../../util/utils'

export class BulkRequestV2 {

  public static NonStandardBulkActions = ['bulk-import', 'bulk-merge-contact', 'bulk-contact-delete']

  public static collectionRef(): firebase.firestore.CollectionReference {
    return firebase.firestore().collection('bulk_requests_v2')
  }

  public async save() {
    console.log(this.id)
    await this._ref.set(this.data)
    console.log(this.id)
  }

  public static getById(id: string): Promise<BulkRequestV2> {
    return new Promise((resolve, reject) => {
      BulkRequestV2.collectionRef()
        .doc(id)
        .get()
        .then(snapshot => {
          resolve(new BulkRequestV2(snapshot))
        })
        .catch(err => {
          console.log(err)
          reject(err)
        })
    })
  }

  public static async getByLocationId(
    locationId: string
  ): Promise<BulkRequestV2[]> {
    const snapshot = await BulkRequestV2.collectionRef()
      .where('location_id', '==', locationId)
      .orderBy('date_added', 'desc')
      .get()
    return snapshot.docs.map(d => new BulkRequestV2(d))
  }

  public static async getByLocationAndUser(
    userId: string,
    locationId: string
  ): Promise<BulkRequestV2[]> {
    const snapshot = await BulkRequestV2.collectionRef()
      .where('location_id', '==', locationId)
      .where('by_user_id', '==', userId)
      .orderBy('date_added', 'desc')
      .get()
    return snapshot.docs.map(d => new BulkRequestV2(d))
  }

  public static async getByLocationUser(params: {
    userId: string
    locationId: string
    fromDate?: number
    toDate?: number
  }): Promise<BulkRequestV2[]> {
    if (!params.locationId) return []

    let query = BulkRequestV2.collectionRef().where(
      'location_id',
      '==',
      params.locationId
    )

    if (params.userId) query = query.where('by_user_id', '==', params.userId)
    if (params.fromDate) query = query.where('date_added', '>', params.fromDate)
    if (params.toDate) query = query.where('date_added', '<=', params.toDate)
    const snapshot = await query.orderBy('date_added', 'desc').get()
    const items = snapshot.docs.map(d => new BulkRequestV2(d))
    return items
  }

  public static async createBulkRequest(locationId: string) {
    const br = new BulkRequestV2()
    br.locationId = locationId
    await br.save()
    return br
  }

  private _id: string
  private _data: { [field: string]: any }
  private _ref: firebase.firestore.DocumentReference
  private _snapshot: firebase.firestore.DocumentSnapshot
  public suspendUIAction: boolean = false
  private _eventStats: IEventStats | null
  private _eventStats2: ICombinedStats | null
  private _showStats: boolean = false
  private _loading: boolean = false

  constructor(
    snapshot?:
      | firebase.firestore.QueryDocumentSnapshot
      | firebase.firestore.DocumentSnapshot
      | { [key: string]: any }
      | undefined
  ) {
    if (
      snapshot instanceof firebase.firestore.DocumentSnapshot ||
      snapshot instanceof firebase.firestore.QueryDocumentSnapshot
    ) {
      this._id = snapshot.id
      this._ref = snapshot.ref
      this._data = snapshot.data() || {}
      this._snapshot = snapshot
    } else if (snapshot) {
      this._data = Object.assign({}, snapshot)
      if (snapshot.id) {
        this._id = snapshot.id
        this._ref = BulkRequestV2.collectionRef().doc(snapshot.id)
      } else {
        this._ref = BulkRequestV2.collectionRef().doc()
        this._id = this._ref.id
      }
    } else {
      this._ref = BulkRequestV2.collectionRef().doc()
      this._id = this._ref.id
      this._data = {}
      this.dateAdded = moment()
      this.dateUpdated = moment()
      this.deleted = false
    }
  }

  get hasFrozenList(): boolean {
    return (
      this._data.contact_list_ids !== null &&
      this._data.contact_list_ids !== undefined
    )
  }

  get hasDynamicList(): boolean {
    return this._data.processing_type === 'dynamic'
  }

  get cloudTaskName() {
    return this._data.cloud_task_name
  }

  set cloudTaskName(taskName: string) {
    this._data.cloud_task_name = taskName
  }

  get scheduledStart() {
    if (this._data.scheduled_start) return moment(this._data.scheduled_start)
    else return null
  }

  set scheduledStart(startOn: moment.Moment | null) {
    if (startOn) this._data.scheduled_start = startOn.toDate()
    else this.data.scheduled_start = null
  }

  get postOp(): any {
    return this._data.post_op
  }

  get canCancel() {
    if (this.suspendUIAction) return false
    if (
      this.currentStatus === 'complete' ||
      this.currentStatus === 'cancelled'
    )
      return false

    return true
  }

  get canPause() {
    if (this.currentStatus === 'paused') return false
    const type = this.opSpecs?.opType;
    if (type !== 'bulk-email' &&  type !== 'bulk-sms' &&
        type !== 'bulk-campaign' && type !== 'bulk-workflow' &&
        type !== 'bulk-review-request') {
          return false
    }
    if (this.suspendUIAction) return false
    // allow pausing all kinds of jobs not just drip mode
    if (
      this.currentStatus === 'complete' ||
      this.currentStatus === 'cancelled' ||
      this.currentStatus === 'error'
    )
      return false
    if (this.currentStatus && this.currentStatus.startsWith('reading')) return false
    // allow pausing all kinds of jobs not just drip mode
    return true
  }

  get canEditSprintLimit() {
    if (this.suspendUIAction) return false
    if (!this.sprintSchedule) return false
    // && this.currentStatus !== 'paused'
    if (
      this.currentStatus === 'complete' ||
      this.currentStatus === 'cancelled' ||
      this.currentStatus === 'waiting' ||
      this.currentStatus === 'queueing' ||
      this.currentStatus === 'processing' ||
      this.currentStatus === 'error'
    ) {
      return false
    }
    if (this.currentStatus && this.currentStatus.startsWith('reading')) return false
    if (!this.processingStartedOn) return false
    if (this.sprintSchedule.processAllAtSchedule) return false
    return true // only if processing has started and only if it not process all at schedule
  }

  get canEditSchedule() {
    if (this.suspendUIAction) return false
    if (!this.sprintSchedule) return false
    // && this.currentStatus !== 'paused'
    if (
      this.currentStatus === 'complete' ||
      this.currentStatus === 'cancelled' ||
      this.currentStatus === 'waiting' ||
      this.currentStatus === 'queueing' ||
      this.currentStatus === 'processing' ||
      this.currentStatus === 'error'
    )
      return false
    if (this.currentStatus && this.currentStatus.startsWith('reading')) return false
    if (this.processingStartedOn) return false
    return true
  }

  set postOp(postOp: any) {
    this._data.post_op = postOp
  }

  get byUserId(): string {
    return this._data.by_user_id
  }

  set byUserId(userid: string) {
    this._data.by_user_id = userid
  }

  get description(): string {
    return this._data.description
  }

  set description(description: string) {
    this._data.description = description
  }

  get byUserName(): string {
    return this._data.by_user_name
  }

  set byUserName(userName: string) {
    this._data.by_user_name = userName
  }

  get currentStatus() {
    return this._data.status?.toLowerCase()
  }

  get error() {
    return this._data.error
  }

  get selectedRecordsCount(): number | null {
    if (this._data.record_list) return this._data.record_list.length
    else return null
  }

  get status(): string {
    return this._data.status?.toLowerCase()
  }

  public get simplifiedStatus() {
    if (this.status === RequestStatus.COMPLETE || this.status === RequestStatus.CANCELLED || this.status === RequestStatus.PAUSED || this.status === RequestStatus.ERROR) {
      return this.status;
    } else if (this.queuedCount > 0 || BulkRequestV2.NonStandardBulkActions.includes(this.operationType)) {
      return RequestStatus.PROCESSING; // once schedule begins it is in processing except if it is not completed
    } else {
      return RequestStatus.QUEUED; // this will be scheduled as statusV2
    }
  }

  public get advancedStatus() {
    return this._data.advanced_status
  }

  public getPercentComplete(){
    if (this.simplifiedStatus  !== 'processing') return '';
    if (!this.totalCount) return '';
    if (this.operationType === 'bulk-import'){
      return `(${Math.round((((this.updatedCount || 0) + (this.createdCount || 0) + (this.errorCount || 0))/this.totalCount)*100).toFixed(2) + '%'})`
    } else return `(${Math.round((((this.successCount || 0) + (this.errorCount || 0))/this.totalCount)*100).toFixed(2) + '%'})`
  }

  get totalCount(): number {
    return this._data.total_count
  }

  set totalCount(count: number) {
    this._data.total_count = count
  }

  get operationType(): string {
    if (this.opSpecs) return this.opSpecs.opType
    else return ''
  }

  get operationDescription(): string {
    if (this.opSpecs) return this.opSpecs.description
    else return ''
  }
  get operationImportId(): string {
    if (this.opSpecs){
      return this.opSpecs['importId'] || this.opSpecs['import_id'] | this.opSpecs['importid']
    } return this.opSpecs.description
  }
  get operationNote(): string {
    if (this.opSpecs) return this.opSpecs.note
    else return ''
  }

  get queuedCount(): number {
    return this._data.queued_count
  }

  set queuedCount(queued: number) {
    this._data.queued_count = queued
  }

  get successCount(): number {
    return this._data.success_count
  }

  set successCount(success: number) {
    this._data.success_count = success
  }

  get updatedCount(): number {
    return this._data.updated_count || this._data.update_count || this._data.updatedCount || this._data.updateCount
  }

  get createdCount(): number {
    return this._data.created_count || this._data.create_count || this._data.createdCount || this._data.createCount
  }

  get errorCount(): number {
    return this._data.error_count
  }

  set errorCount(errCount: number) {
    this._data.error_count = errCount
  }

  get processingStartedOn(): Moment {
    // Date and time when processing first started
    if (typeof this._data.processing_start_on === 'number')
      return moment(this._data.processing_start_on)
    else return null
  }

  get processingCompletedOn(): number | null { // Date and time when processing first started
    if (typeof this._data.processing_completed_on === 'number')
      return moment(this._data.processing_completed_on)
    else return null
  }

  get processingPausedOn(): number | null { // Date and time when processing first started
    if (typeof this._data.processing_paused_on === 'number')
      return moment(this._data.processing_paused_on)
    else return null
  }

  get processingCancelledOn(): number | null { // Date and time when processing first started
    if (typeof this._data.processing_cancelled_on === 'number')
      return moment(this._data.processing_cancelled_on)
    else return null
  }

  get nextProcessingOn(): Moment {
    // Date and time when processing first started
    if (
      this.sprintSchedule &&
      typeof this.sprintSchedule.nextResume === 'number'
    )
      return moment(this.sprintSchedule.nextResume)
    else return null
  }

  get sprintSchedule(): ISprintSchedule {
    return this._data.sprint_schedule
  }

  set sprintSchedule(sched: ISprintSchedule) {
    this._data.sprint_schedule = sched
  }

  get filters(): IContactsQuery {
    return this._data.filters
  }

  set filters(filters: IContactsQuery) {
    this._data.filters = filters
  }

  get opSpecs(): IOpSpecs {
    return this._data.op_specs
  }

  set opSpecs(opSpecs: IOpSpecs) {
    this._data.op_specs = opSpecs
  }

  get id(): string {
    return this._id
  }

  get data(): { [key: string]: any } {
    return lodash.pickBy(this._data, v => v !== null && v !== undefined)
  }

  get neverExpire(): boolean {
    return this._data.never_expire
  }

  set neverExpire(value: boolean) {
    this._data.never_expire = value
  }

  get locationId(): string {
    return this._data.location_id
  }

  set locationId(locationId: string) {
    this._data.location_id = locationId
  }

  get dateAdded() {
    return Utils.safeMoment(this._data.date_added)
  }

  set dateAdded(dateAdded: moment.Moment) {
    this._data.date_added = dateAdded.valueOf()
  }

  get dateUpdated() {
    return Utils.safeMoment(this._data.date_updated)
  }

  set dateUpdated(dt: moment.Moment) {
    this._data.date_updated = dateAdded.valueOf()
  }

  get deleted(): boolean {
    return this._data.deleted || false
  }

  set eventStats(stats: IEventStats | null) {
    this._eventStats = stats
  }

  get eventStats(): IEventStats | null {
    return this._eventStats
  }

  set eventStatsV2(stats: ICombinedStats | null) {
    this._eventStats2 = stats
  }

  get eventStatsV2(): ICombinedStats | null {
    return this._eventStats2
  }

  set loading(val: boolean) {
    this._loading = val
  }

  get loading(): boolean {
    return this._loading
  }

  set showStats(val: boolean) {
    this._showStats = val
  }

  get showStats(): boolean {
    return this._showStats
  }

  set deleted(deleted: boolean) {
    this._data.deleted = deleted
  }

  get usesFilters(): boolean {
    if (this.filters) return true
    else return false
  }

  get isDripMode(): boolean {
    if (this.sprintSchedule && !this.sprintSchedule.processAllAtSchedule)
      return true
    else return false
  }

  get isProcessAllAtScheduleMode(): boolean {
    if (
      this.sprintSchedule &&
      this.sprintSchedule.processAllAtSchedule === true
    )
      return true
    else return false
  }

  // get suspendUIAction(): boolean {
  //   return this._suspendUIAction;
  // }

  // set suspendUIAction(val: boolean){
  //   this._suspendUIAction = val;
  // }
}


export enum RequestStatus {
  NOT_STARTED = 'not-started',
  PAUSED = 'paused',
  CANCELLED = 'cancelled',
  PROCESSING = 'processing',
  RESUMED = 'resumed',
  NEXT_DRIP = 'next-drip',
  COMPLETE = 'complete',
  SUCCESS = 'success',
  ERROR = 'error',
  WAITING = 'waiting',
  QUEUED = 'queued',
  QUEUEING = 'queueing',
  READING = 'reading',
  SCHEDULED = 'scheduled',

}
