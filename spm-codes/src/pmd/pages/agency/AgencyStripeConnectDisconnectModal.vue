<template>
  <div>
    <modal v-if="show" @close="$emit('close')" maxWidth="520" :showCloseIcon="true">
      <div class="agency-stripe-disconnect-modal">
        <div class="modal-heading">Remove all SaaS Plan Configurations?</div>
        <div class="modal-description">
          You have SaaS plans created in the Stripe account!
          <br/>
          If you disconnect it, all SaaS plans will be lost and you will need to create those again.
        </div>

        <div class="option">
          <input type="checkbox" id="agreeDisconnect" v-model="isAgree"/>
          <label for="agreeDisconnect" style="line-height:20px">
            I acknowledge that all my SaaS plans will be lost and I will need to build those again
          </label>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-light" @click="$emit('close')">Cancel</button>
        <button class="btn btn-primary" :disabled="!isAgree || disconnecting" @click="removeSaasConfig">
          <moon-loader v-if="disconnecting" color="#ffffff" size="20px" />
          <span v-else> Disconnect </span>
        </button>
      </div>
    </modal>
  </div>
</template>

<script>
import Modal from '@/pmd/components/common/Modal.vue'
import { trackGaEvent } from '@/util/helper'

export default {
  components:{
    Modal
  },
  props:['show'],
  data(){
    return {
      isAgree: false,
      disconnecting: false,
    }
  },
  computed:{
    company() {
      return this.$store.state.company.company
    },
  },
  methods:{
    async removeSaasConfig(){
      this.disconnecting = true;
      try {
        const { data } = await this.saasService.delete(
          `/subscription-plans/${this.company.id}/`
        )
        console.log(data);
        await this.disconnect();
      } catch (err) {
        this.disconnecting = false;
        console.log(err);
      }
    },
    async disconnect(){
      try {
        trackGaEvent('StripeConnect', this.company.id, 'Start Disconnect', 1)
        const { data } = await this.saasService.post(
          `/connect/disconnect_account`,
          {
            account_id: this.company.stripe_connect_id,
            company_id: this.company.id,
          }
        )
        trackGaEvent('StripeConnect', this.company.id, 'Finish Disconnect', 1)
        this.$emit('close');
        // console.log(data);
      } catch (err) {
        console.log(err);
      }
      this.disconnecting = false
    },
  }
}
</script>

<style lang="scss">
.agency-stripe-disconnect-modal{
  padding: 24px;
  .modal-heading {
    font-size: 20px;
    font-weight: 500;
    padding-bottom: 8px;
    border-bottom: 1px solid #ececec;
    margin-bottom: 16px;
    // padding: 16px;
  }
  .modal-description{
    margin-bottom: 16px;
    line-height: 18px;
    background-color: rgba(233,31,98, 0.08);
    border-left: 4px solid #e91e63;
    padding: 12px;
  }
}

</style>
