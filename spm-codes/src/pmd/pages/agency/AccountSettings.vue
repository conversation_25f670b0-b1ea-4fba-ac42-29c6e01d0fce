<template>
    <div>
        <SideBarV2 v-if="getSideBarVersion === 'v2'"/>
    	<SideBar v-else />
        <TopBar />
        <section class="hl_wrapper">
            <section
                class="hl_wrapper--inner hl_settings--profile"
                id="settings"
                :class="{
                    'hl_snapshot--settings': getSideBarVersion == 'v2' && $route.name == 'snapshot', 
                    'hl_settings': $route.name !== 'api_key',
                    'hl_custommenu_settings': $route.name == 'custom_menu_link_settings' && getSideBarVersion == 'v2'
                }"
            >
                <AccountSettingsTopMenu v-if="getSideBarVersion !== 'v2'"/>
                <router-view/>
            </section>
            <!-- END of .settings-profile -->
        </section>
    </div>
</template>

<script lang='ts'>
import Vue from 'vue';
import TopBar from '../../components/TopBar.vue';
import SideBar from '../../components/agency/SideBar.vue';
import AccountSettingsTopMenu from '@/pmd/components/agency/AccountSettingsTopMenu.vue';
import SideBarV2 from '../../components/sidebar/SideBar.vue'
import { User } from '@/models';

export default Vue.extend({
    components: { TopBar, SideBar, AccountSettingsTopMenu, SideBarV2 },
    computed: {
		user() {
			const user = this.$store.state.user.user
			return user ? new User(user) : undefined
		},
        getSideBarVersion(): string {
            return this.$store.getters['sidebarv2/getVersion'] 
        },
	},
});
</script>
