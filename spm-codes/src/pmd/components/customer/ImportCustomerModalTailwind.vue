<template>
  <vue-final-modal
    classes="flex justify-center items-center"
    content-class="relative flex flex-col max-h-9/10 mx-4 border dark:border-gray-800 rounded bg-white dark:bg-gray-900 w-4/6"
    :value="visible"
    @input="$emit('close')"
    :clickToClose="true"
  >
    <div
      class="bg-white rounded-lg text-left overflow-hidden h-1/2 md:h-3/4 transform transition-all sm:align-middle"
    >
      <div class="flex flex-col">
        <div v-if="step < 4" class="px-4 pt-4">
          <nav aria-label="Progress">
            <ol
              role="list"
              class="border border-gray-300 rounded-md divide-y divide-gray-300 flex divide-y-0 h-14"
            >
              <li class="relative flex-1 flex">
                <a href="#" class="group flex items-center w-full">
                  <span class="px-6 py-4 flex items-center text-sm font-medium">
                    <span
                      class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full"
                      :class="
                        step > 1
                          ? 'bg-green-600 group-hover:bg-green-800'
                          : step === 1
                          ? 'border-green-600 border-2'
                          : 'border-2 border-gray-300 group-hover:border-gray-400'
                      "
                    >
                      <!-- Heroicon name: solid/check -->

                      <svg
                        class="w-6 h-6 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                        v-if="step > 1"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clip-rule="evenodd"
                        />
                      </svg>
                      <span
                        v-else
                        :class="
                          step === 1
                            ? 'text-green-600'
                            : 'text-gray-500 group-hover:text-gray-900'
                        "
                        >1</span
                      >
                    </span>
                    <span class="ml-4 text-sm font-medium text-gray-900"
                      >Upload</span
                    >
                  </span>
                </a>

                <!-- Arrow separator for lg screens and up -->
                <div
                  class="hidden md:block absolute top-0 right-0 h-full w-5"
                  aria-hidden="true"
                >
                  <svg
                    class="h-full w-full text-gray-300"
                    viewBox="0 0 22 80"
                    fill="none"
                    preserveAspectRatio="none"
                  >
                    <path
                      d="M0 -2L20 40L0 82"
                      vector-effect="non-scaling-stroke"
                      stroke="currentcolor"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              </li>
              <li class="relative flex-1 flex">
                <a href="#" class="group flex items-center w-full">
                  <span class="px-6 py-4 flex items-center text-sm font-medium">
                    <span
                      class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full"
                      :class="
                        step > 2
                          ? 'bg-green-600 group-hover:bg-green-800'
                          : step === 2
                          ? 'border-green-600 border-2'
                          : ' border-2 border-gray-300 group-hover:border-gray-400'
                      "
                    >
                      <!-- Heroicon name: solid/check -->

                      <svg
                        class="w-6 h-6 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                        v-if="step > 2"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clip-rule="evenodd"
                        />
                      </svg>
                      <span
                        v-else
                        :class="
                          step === 2
                            ? 'text-green-600'
                            : 'text-gray-500 group-hover:text-gray-900'
                        "
                        >2</span
                      >
                    </span>
                    <span class="ml-4 text-sm font-medium text-gray-900"
                      >Map</span
                    >
                  </span>
                </a>

                <!-- Arrow separator for lg screens and up -->
                <div
                  class="hidden md:block absolute top-0 right-0 h-full w-5"
                  aria-hidden="true"
                >
                  <svg
                    class="h-full w-full text-gray-300"
                    viewBox="0 0 22 80"
                    fill="none"
                    preserveAspectRatio="none"
                  >
                    <path
                      d="M0 -2L20 40L0 82"
                      vector-effect="non-scaling-stroke"
                      stroke="currentcolor"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              </li>
              <li class="relative flex-1 flex">
                <a href="#" class="group flex items-center w-full">
                  <span class="px-6 py-4 flex items-center text-sm font-medium">
                    <span
                      class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full"
                      :class="
                        step > 3
                          ? 'bg-green-600 group-hover:bg-green-800'
                          : step === 3
                          ? 'border-green-600 border-2'
                          : 'border-2 border-gray-300 group-hover:border-gray-400'
                      "
                    >
                      <!-- Heroicon name: solid/check -->

                      <svg
                        class="w-6 h-6 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                        v-if="step > 3"
                      >
                        <path
                          fill-rule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clip-rule="evenodd"
                        />
                      </svg>
                      <span
                        v-else
                        :class="
                          step === 3
                            ? 'text-green-600'
                            : 'text-gray-500 group-hover:text-gray-900'
                        "
                        >3</span
                      >
                    </span>
                    <span class="ml-4 text-sm font-medium text-gray-900"
                      >Details</span
                    >
                  </span>
                </a>
              </li>
            </ol>
          </nav>
        </div>
        <div v-else class="px-3 pt-3 flex justify-end">
          <span
            class="cursor-pointer text-gray-600 hover:text-gray-900"
            @click="close()"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </span>
        </div>
        <div id="flex-grow">
          <div
            v-if="loading"
            style="height: 500px"
            class="d-flex justify-content-center align-items-center"
          >
            <moon-loader :loading="loading" color="blue" size="35px" />
          </div>
          <div class="py-4 px-8" v-else-if="step === 1">
            <div class="text-center mb-2">
              <div class="h6">Upload your file</div>
              <p>
                Before you upload your file below, make sure your file is
                <a
                  href="https://doc.clickup.com/d/h/87cpx-17124/3d29cf489f722f4"
                  target="_blank"
                >
                  <span class="text-blue-600 hover:text-blue-500"
                    >ready to be imported</span
                  >
                </a>
              </p>
            </div>
            <vue2Dropzone
              :options="dropzoneOptions"
              id="dropZone"
              ref="dropZone"
              :include-styling="false"
              :useCustomSlot="true"
              @vdropzone-file-added="onFileChange"
              @vdropzone-drag-enter="hover = true"
              @vdropzone-drag-over="hover = true"
              @vdropzone-drag-leave="hover = false"
              @vdropzone-error="onError"
            >
              <div
                class="mt-1 flex justify-center px-6 pt-6 pb-8 border-2 border-dashed rounded-md"
                :class="
                  error
                    ? 'border-red-300'
                    : hover
                    ? 'border-green-300'
                    : 'border-gray-300'
                "
              >
                <div class="space-y-1 text-center py-4">
                  <svg
                    class="mx-auto h-12 w-12 text-gray-400"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"
                    />
                  </svg>
                  <div class="flex text-sm text-gray-600 justify-center">
                    <label
                      id="import-file"
                      class="relative cursor-pointer bg-white rounded-md font-medium focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                    >
                      <span class="text-blue-600 hover:text-blue-500"
                        >Upload a file</span
                      >
                    </label>
                    <p class="pl-1">or drag and drop</p>
                  </div>
                  <div>
                    <span
                      class="flex justify-center items-center font-medium py-1 px-2 bg-white"
                      v-if="selectedFile"
                    >
                      <div
                        class="relative text-sm text-gray-900 font-normal leading-none max-w-full flex-initial"
                      >
                        {{ selectedFile.name }}
                        <div
                          class="absolute text-gray-500 -right-3 -top-1 pb-2 flex flex-auto flex-row-reverse ml-1 cursor-pointer"
                        >
                          <svg
                            @click="
                              selectedFile = undefined
                              hover = false
                              error = false
                            "
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-3 w-3"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fill-rule="evenodd"
                              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        </div>
                      </div>
                    </span>
                    <div class="text-red-500" v-if="error">
                      {{ errorMessage }}
                    </div>
                  </div>
                  <p class="text-xs text-gray-500">
                    All .csv file types (upto 50MB in size) are supported.
                  </p>
                </div>
              </div>
            </vue2Dropzone>
          </div>
          <div v-else-if="step === 2">
            <div class="text-center p-3">
              <div class="h6 m-0">
                Map columns in your file to contact field properties
              </div>
            </div>
            <div
              class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8"
            >
              <div
                class="border border-gray-200 max-h-50vh overflow-scroll sm:rounded-lg"
              >
                <table
                  class="min-w-full divide-y divide-gray-200 table-fixed w-full"
                >
                  <thead class="bg-gray-100">
                    <tr class="font-weight-bold">
                      <th
                        scope="col"
                        class="px-6 py-3 text-left text-xs font-medium text-gray-900 uppercase tracking-wider max-w-1/10 w-1/10"
                      >
                        Matched
                      </th>
                      <th
                        scope="col"
                        class="px-6 py-3 text-left text-xs font-medium text-gray-900 uppercase tracking-wider max-w-3/10"
                      >
                        Column Header From File
                      </th>
                      <th
                        scope="col"
                        class="px-6 py-3 text-left text-xs font-medium text-gray-900 uppercase tracking-wider max-w-3/10"
                      >
                        Preview Information
                      </th>
                      <th
                        scope="col"
                        class="px-6 py-3 text-left text-xs font-medium text-gray-900 uppercase tracking-wider max-w-3/10"
                      >
                        Contact Fields
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200 w-full">
                    <tr
                      v-for="header in headers"
                      v-bind:key="header.fileColumn"
                      class="w-full"
                    >
                      <td
                        class="px-3 py-2 whitespace-nowrap text-sm font-medium"
                      >
                        <svg
                          v-if="map[header.fileColumn]"
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-6 w-6 text-green-600"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                        <svg
                          v-else
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-6 w-6 text-red-600"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                          />
                        </svg>
                      </td>
                      <td
                        class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900"
                      >
                        <div
                          class="font-weight-bold truncate w-min max-w-full"
                          v-b-tooltip.hover="{ delay: 1000 }"
                          :title="header.fileColumn"
                        >
                          {{ header.fileColumn }}
                        </div>
                      </td>
                      <td class="px-3 py-2 text-sm font-medium text-gray-900">
                        <div
                          v-for="(example, index) in header.examples"
                          v-bind:key="index"
                          v-b-tooltip.hover="{ delay: 1000 }"
                          :title="example"
                          class="truncate w-min max-w-full"
                        >
                          {{ example }}
                        </div>
                      </td>
                      <td
                        class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900"
                      >
                        <Select
                          label=""
                          data-size="5"
                          :items="
                            properties.filter(
                              prop =>
                                !(
                                  Object.values(map).includes(prop.value) &&
                                  map[header.fileColumn] !== prop.value
                                )
                            )
                          "
                          item_text="option"
                          item_value="value"
                          item_secondary_text="type"
                          secondary_text
                          searchable
                          @input="selectColumn($event, header.fileColumn)"
                          :value="map[header.fileColumn]"
                        >
                        </Select>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div v-if="unmatchedColumns">
              <p class="text-center my-2" style="font-size: small">
                You have {{ unmatchedColumns }} unmatched column/s
              </p>
              <div class="flex justify-center items-center mb-4">
                <Checkbox
                  name="unmatched-columns"
                  id="unmatched-columns"
                  label="Don't import data in unmatched columns"
                  v-model="dontImportUnmatchedColumns"
                />
              </div>
            </div>
          </div>
          <div v-else-if="step === 3">
            <div class="px-3 pt-3 text-center">
              <div class="h6 m-0">A few final details</div>
            </div>
            <div class="max-h-60vh overflow-scroll p-3">
              <div
                class="flex flex-column border border-gray-200 p-2 sm:rounded-lg"
              >
                <div class="bg-white p-2">
                  <Input
                    v-model="importName"
                    placeholder="Import Name"
                    label="Import Name"
                    type="text"
                    :invalid="!importNameAvailable"
                    error_messsage="Import name is not available"
                    class="mb-2"
                  />
                  <Checkbox
                    v-model="createSmartList"
                    label="Create a list of contacts from the import"
                    class="mb-2"
                  />
                </div>

                <div class="relative mb-2">
                  <div
                    class="absolute inset-0 flex items-center"
                    aria-hidden="true"
                  >
                    <div class="w-full"></div>
                  </div>
                  <div class="relative flex justify-start">
                    <span
                      type="button"
                      class="inline-flex items-center text-sm leading-5 font-medium text-blue-600 cursor-pointer"
                      @click="advanced = !advanced"
                    >
                      <!-- Heroicon name: solid/plus-sm -->
                      <span>Advanced</span>
                      <svg
                        v-if="!advanced"
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M19 9l-7 7-7-7"
                        />
                      </svg>
                      <svg
                        v-else
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </span>
                  </div>
                </div>

                <div class="bg-white p-2" v-if="advanced">
                  <label class="font-medium text-sm text-gray-700"
                    >What do you want to do with contact/s in the .csv
                    file?</label
                  >
                  <div class="flex items-start mb-6">
                    <Radio
                      val="BOTH"
                      label="Add New and Update Existing Contact Records"
                      name="conflict-resolution"
                      v-model="conflictResolution.type"
                      :disabled="findContactByPrimary === 'none'"
                      class="mr-3"
                    />
                    <Radio
                      val="CREATE"
                      label="Add New Contact Records"
                      name="conflict-resolution"
                      v-model="conflictResolution.type"
                      :disabled="
                        findContactByPrimary === 'contactId' &&
                        !findContactBySecondary
                      "
                      class="mr-3"
                    />
                    <Radio
                      val="UPDATE"
                      label="Update Existing Contact Records"
                      name="conflict-resolution"
                      v-model="conflictResolution.type"
                      :disabled="findContactByPrimary === 'none'"
                      class="mr-3"
                    />
                  </div>
                  <div class="mb-6 bg-gray-100 rounded-lg p-4">
                    <div class="flex justify-between pt-1">
                      <Select
                        :items="findByPrimaryOptions"
                        item_text="text"
                        item_value="value"
                        hideSelect
                        :label="
                          (conflictResolution.type === 'CREATE'
                            ? 'Skip'
                            : 'Find') + ' Existing Contacts Based on (Primary)'
                        "
                        v-model="findContactByPrimary"
                        class="mb-3 w-2/4"
                      />
                      <div class="flex items-center flex-grow">
                        <button
                          v-if="findContactByPrimary !== 'none'"
                          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium text-blue-600"
                          @click="addSecondaryKey = !addSecondaryKey"
                        >
                          <svg
                            v-if="!addSecondaryKey"
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-6 w-6"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                            />
                          </svg>
                          <svg
                            v-else
                            xmlns="http://www.w3.org/2000/svg"
                            class="h-6 w-6"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              stroke-linecap="round"
                              stroke-linejoin="round"
                              stroke-width="2"
                              d="M18 12H6"
                            />
                          </svg>
                          {{
                            addSecondaryKey
                              ? 'Remove Second Preference?'
                              : 'Add Second Preference?'
                          }}
                        </button>
                      </div>
                    </div>
                    <div class="flex justify-between" v-if="addSecondaryKey">
                      <Select
                        :items="findBySecondaryOptions"
                        :disabled="!findContactByPrimary"
                        item_text="text"
                        item_value="value"
                        hideSelect
                        :label="
                          (conflictResolution.type === 'CREATE'
                            ? 'Skip'
                            : 'Find') +
                          ' Existing Contacts Based on (secondary)'
                        "
                        v-model="findContactBySecondary"
                        class="mb-3 w-2/4"
                      />
                      <div class="flex-grow"></div>
                    </div>
                    <Checkbox
                      label="Don't update empty values for existing records"
                      name="update-empty-values"
                      v-if="
                        conflictResolution.type === 'UPDATE' ||
                        conflictResolution.type === 'BOTH'
                      "
                      v-model="conflictResolution.dontUpdateEmptyValues"
                    />
                  </div>

                  <TagComponent class="mb-6" v-model="tags" />

                  <div class="flex items-center justify-start my-2">
                    <span class="flex flex-col">
                      <span
                        class="text-sm font-medium text-gray-900"
                        id="availability-label"
                        >Add To Workflow/Campaign</span
                      >
                    </span>
                    <!-- Enabled: "bg-indigo-600", Not Enabled: "bg-gray-200" -->
                    <button
                      type="button"
                      :class="addToWorkflow ? 'bg-blue-600' : 'bg-gray-200'"
                      class="ml-2 relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none"
                      role="switch"
                      aria-checked="false"
                      aria-labelledby="availability-label"
                      aria-describedby="availability-description"
                      @click="addToWorkflow = !addToWorkflow"
                    >
                      <!-- Enabled: "translate-x-5", Not Enabled: "translate-x-0" -->
                      <span
                        aria-hidden="true"
                        :class="
                          addToWorkflow ? 'translate-x-5' : 'translate-x-0'
                        "
                        class="pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200"
                      ></span>
                    </button>
                  </div>
                  <Select
                    label="Workflow/Campaign"
                    :items="{
                      Workflows: getWorkflows,
                      Campaigns: getCampaigns,
                    }"
                    item_text="name"
                    item_value="id"
                    divider
                    searchable
                    v-model="campaignWorkflow"
                    class="mb-6"
                    v-if="addToWorkflow"
                  />

                  <Checkbox
                    label="Validate emails?"
                    name="validate-email"
                    v-model="validateEmail"
                    tooltip="Fires a MailGun validation for all contacts with an e-mail address. May include extra charges from MailGun."
                    :disabled="validateEmailDisabled"
                  />
                </div>
              </div>
            </div>
          </div>
          <div v-else-if="step === 4">
            <div class="modal-body mb-5">
              <div class="modal-body--import">
                <div
                  class="flex flex-column justify-center items-center text-green-600"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-16 w-16"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="1"
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                  <h5 class="my-3">Your File is being Processed...</h5>
                  <h6 class="my-3">
                    We will send you an email when your import is finished
                  </h6>
                  <h6 class="my-3">
                    <span
                      class="text-blue-600 cursor-pointer"
                      @click="viewProgress()"
                      >Click here</span
                    >
                    to view progress.
                  </h6>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="py-4 px-4 bg-gray-100 w-full flex justify-between items-center gap-3"
          v-if="step < 4"
        >
          <div v-if="step > 1">
            <button
              type="button"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm bg-white text-green-600 hover:text-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              @click="$emit('close')"
            >
              Cancel
            </button>
            <button
              type="button"
              class="inline-flex items-center ml-3 px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm bg-white text-green-600 hover:text-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              @click="prevStep"
              :disabled="loading"
            >
              Back
            </button>
          </div>
          <div v-else>
            <button
              type="button"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm bg-white text-green-600 hover:text-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              @click="$emit('close')"
            >
              Cancel
            </button>
          </div>

          <button
            type="button"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            @click="nextStep"
            :disabled="
              loading ||
              (step === 2 &&
                !dontImportUnmatchedColumns &&
                unmatchedColumns > 0) ||
              (step === 1 && selectedFile === undefined) ||
              error ||
              (step === 3 && (!importNameAvailable || !findContactByPrimary)) ||
              submitted
            "
          >
            <span v-if="step !== 3">Next </span>
            <span v-else>
              <span class="flex items-center" v-if="submitted">
                <moon-loader
                  :loading="submitted"
                  color="white"
                  size="15px"
                  class="inline-block"
                />
                <span class="inline-block ml-2">submit</span>
              </span>
              <span v-else>Submit</span>
            </span>
          </button>
        </div>
      </div>
    </div>
  </vue-final-modal>
</template>

<script lang="ts">
import {
  Campaign,
  ImportRequest,
  ImportStatus,
  ImportType,
  Workflow,
} from '@/models'
import firebase from 'firebase'
import uuid from 'uuid'
import Vue from 'vue'
import vue2Dropzone from 'vue2-dropzone'
import moment from 'moment'
import restAgent from '../../../restAgent'
import TagComponent from '../tailwind/TagsInput.vue'
import Select from '../tailwind/select.vue'
import Input from '../tailwind/input.vue'
import Checkbox from '../tailwind/checkbox.vue'
import Radio from '../tailwind/Radio.vue'
import store from '../../../store'
import router from '../../../routes'

export default Vue.extend({
  props: ['visible', 'currentLocationId', 'type'],
  components: {
    vue2Dropzone,
    TagComponent,
    Select,
    Input,
    Checkbox,
    Radio,
  },
  data() {
    return {
      dropzoneOptions: {
        url: '/file/post',
        clickable: '#import-file',
        addRemoveLinks: false,
        createImageThumbnails: false,
        acceptedFiles: '.csv',
        maxFiles: 1,
        maxFilesize: 50,
      },
      selectedFile: undefined as File | undefined,
      importRequestID: '',
      hover: false,
      error: false,
      findContactByPrimary: '',
      findContactBySecondary: '',
      errorMessage: '',
      addSecondaryKey: false,
      headers: {} as {
        fileColumn: string
        column?: string
        examples: string[]
        key: string
      }[],
      properties: {} as {
        option: string
        value: string
        tooltip?: string
      },
      map: {} as { [key: string]: string },
      loading: false,
      dontImportUnmatchedColumns: false,
      createSmartList: false,
      importName: moment().format('D_MMM_YYYY_h_mm_A').toUpperCase(),
      importNameAvailable: true,
      conflictResolution: {
        type: 'BOTH',
        findContactBy: [],
        dontUpdateEmptyValues: true,
      },
      campaignWorkflow: '',
      tags: [],
      step: 1,
      validateEmail: false,
      submitted: false,
      advanced: false,
      getCampaignWorflow: [],
      addToWorkflow: false,
    }
  },
  filters: {
    dataType(v) {
      if (v) return `(${v})`
      return ''
    },
  },
  methods: {
    async onFileChange(e: any) {
      this.error = false
      let file
      if (e instanceof Event) {
        const element = <HTMLInputElement>e.target
        if (!element.files) return
        file = element.files[0]
      } else {
        file = e
      }
      const dropZone: any = this.$refs.dropZone
      dropZone.removeAllFiles()
      this.selectedFile = file
      this.hover = true
    },
    async uploadCSV() {
      const filePath = `location/${
        this.currentLocationId
      }/contact-import/${uuid()}`

      const uploadPath = firebase.storage().ref(filePath)
      const buffer = (await this.selectedFile?.arrayBuffer()) as ArrayBuffer
      await uploadPath.put(buffer)
      return filePath
    },
    async nextStep() {
      let data
      let resp
      switch (this.step) {
        case 1:
          this.loading = true
          const filePath = await this.uploadCSV()
          data = {
            step: 1,
            filePath,
            locationId: this.currentLocationId,
          }

          const response = await restAgent.Contact.import(data)
          this.headers = response.headers
          const map = {}
          this.headers.forEach(h => {
            map[h.fileColumn] = h.column || ''
          })
          this.map = map
          this.properties = response.properties.map(p => {
            return {
              ...p,
              tooltip: p.option + (p.type ? ' ( ' + p.type + ' )' : ''),
            }
          })
          this.importRequestId = response.importRequestId
          this.loading = false
          break
        case 3:
          this.conflictResolution.findContactBy = []
          if (this.findContactByPrimary)
            this.conflictResolution.findContactBy.push(
              this.findContactByPrimary
            )
          if (this.findContactBySecondary)
            this.conflictResolution.findContactBy.push(
              this.findContactBySecondary
            )
          data = {
            step: 2,
            importRequestId: this.importRequestId,
            locationId: this.currentLocationId,
            dontImportUnmatchedColumns: this.dontImportUnmatchedColumns,
            keyMap: this.map,
            importName: this.importName,
            createSmartList: this.createSmartList,
            conflictResolution: this.conflictResolution,
            tags: this.tags,
            validateEmail: this.validateEmail,
          }

          if (this.campaignWorkflow) {
            const c = this.getCampaigns.find(
              camp => camp.id === this.campaignWorkflow
            )
            const w = this.getWorkflows.find(
              work => work.id === this.campaignWorkflow
            )
            if (c) data['campaign'] = this.campaignWorkflow
            else if (w) data['workflow'] = this.campaignWorkflow
          }
          this.submitted = true
          resp = await restAgent.Contact.import(data)
          this.submitted = false
          this.importNameAvailable = resp.success
      }
      if ((resp && resp.success) || !resp) this.step++
    },
    async prevStep() {
      this.step--
    },
    selectColumn(event, column: string) {
      this.map = Object.assign({}, this.map, { [column]: event })
    },
    onError(f, m, x) {
      this.error = true
      this.errorMessage = m
    },
    viewProgress() {
      this.$emit('close')
      router.push(
        `/location/${this.locationId}/customers/smart_list/bulk_actions`
      )
    },

    close() {
      if (this.createSmartList) {
        this.$emit('open_smart_list', this.importName)
        this.$emit('close')
      } else {
        this.$emit('on_import_request', this.importRequestId)
        this.$emit('close')
      }
    },
  },
  computed: {
    checkMatched() {
      return (column: string) => {
        return this.map[column] ? true : false
      }
    },
    unmatchedColumns() {
      return Object.keys(this.map)
        .map(key => {
          return this.map[key]
        })
        .filter(column => !column).length
    },
    getCampaigns(): Campaign[] {
      return store.getters['campaigns/getAll']
    },
    getWorkflows(): Workflow[] {
      return store.getters['workflows/getAll']
    },
    // getCampaignWorflow() {
    //   const result = {}
    //   result['Workflows'] = this.getWorkflows
    //   result['Campaigns'] = this.getCampaigns
    //   console.log('result')
    //   return result
    // },
    validateEmailDisabled() {
      if (
        this.findContactByPrimary !== 'email' &&
        this.findContactBySecondary !== 'email'
      ) {
        this.validateEmail = false
        return true
      } else return false
    },
    locationId() {
      return router.currentRoute.params.location_id
    },
    findByPrimaryOptions() {
      const mapKeys = Object.keys(this.map)
      return [
        { text: 'None', value: 'none' },
        { text: 'Email', value: 'email' },
        { text: 'Phone', value: 'phone' },
        { text: 'ContactID', value: 'contactId' },
      ].filter(i => {
        return (
          i.value !== this.findContactBySecondary &&
          (mapKeys.some(k => this.map[k] === i.value) || i.value === 'none')
        )
      })
    },
    findBySecondaryOptions() {
      const mapKeys = Object.keys(this.map)
      return [
        { text: 'Email', value: 'email' },
        { text: 'Phone', value: 'phone' },
        { text: 'ContactID', value: 'contactId' },
      ].filter(i => {
        return (
          i.value !== this.findContactByPrimary &&
          (mapKeys.some(k => this.map[k] === i.value) || i.value === 'none')
        )
      })
    },
  },
  watch: {
    map: {
      handler() {
        if (
          Object.keys(this.map).some(
            h => this.map[h]?.toLowerCase() === 'email'
          )
        )
          this.findContactByPrimary = 'email'
        else if (
          Object.keys(this.map).some(
            h => this.map[h]?.toLowerCase() === 'phone'
          )
        )
          this.findContactByPrimary = 'phone'
      },
      immediate: true,
      deep: true,
    },
    findContactByPrimary(v) {
      if (v === 'none') {
        this.conflictResolution.type = 'CREATE'
        this.addSecondaryKey = false
      }
      if (v === 'contactId' && this.conflictResolution.type === 'CREATE') {
        this.conflictResolution.type = 'BOTH'
      }
    },
    addSecondaryKey() {
      if (!this.addSecondaryKey) {
        this.findContactBySecondary = ''
        if (
          this.findContactByPrimary === 'contactId' &&
          this.conflictResolution.type === 'CREATE'
        ) {
          this.conflictResolution.type = 'BOTH'
        }
      }
    },
    importName() {
      this.importNameAvailable = true
    },
  },
  mounted() {},
})
</script>

<style scoped>
#import-modal {
  color: #2e2c2f;
}

.text-colot--light-gray {
  color: #6c8a9b !important;
}

.line {
  height: 0.1px;
  width: 100%;
  background-color: #6c8a9b;
}

.dropzone-inside {
  padding: 20px 60px;
  border: 2px dashed gray;
  border-radius: 5px;
  background-color: whitesmoke;
  margin-left: 100px;
  margin-right: 100px;
}
.dropzone-inside.hover {
  border-color: #188bf6;
}
.dropzone-inside.dropzone-error {
  border-color: #e93d3d !important;
}
.font-size {
  font-size: 18px;
}

.btn-gray {
  background-color: #2e2c2f;
}

.btn-gray:hover {
  background-color: #938f91;
}

.btn-outline-gray {
  border: 2px solid #6c8a9b;
  background-color: unset;
  color: #6c8a9b;
}

.btn-outline-gray:hover {
  background-color: #6c8a9b;
  color: white;
}

.menu-ball {
  height: 25px;
  width: 25px;
  border-radius: 50%;
  border: 3px solid #6c8a9b;
  color: #6c8a9b;
  font-weight: 500;
  display: flex;
  justify-content: center;
  align-items: center;
}

.menu-ball.active {
  background-color: #37ca37;
  border-color: #37ca37;
  color: white;
}

.menu-line {
  height: 0.1px;
  width: 100%;
  background-color: #6c8a9b;
  top: 10px;
  margin: 0 5px;
  position: relative;
}
.menu-line.active {
  background-color: #37ca37;
}
.menu-left {
  position: relative;
  left: 7px;
}

.menu-right {
  position: relative;
  right: 7px;
}

.preview-column {
  display: flex;
  flex-direction: column;
}

.import-table th,
.import-table td {
  border: 0;
}

.import-table {
  border: 2px solid #938f91;
}

.import-table thead {
  border-bottom: 2px solid #938f91;
}

.cancel-button {
  text-decoration: underline;
  cursor: pointer;
  color: #6c8a9b;
}

.cancel-button:hover,
.cancel-button:focus {
  color: #627c8b;
}

.import-table tbody {
  display: block;
  height: 300px;
  overflow: auto;
}

.import-table thead,
.import-table tbody tr {
  display: table;
  width: 100%;
}

.advanced {
  background-color: #f5f5f5;
  border: 1px solid #2e2c2f;
  padding: 0 10%;
  font-size: 1rem !important;
}

.selectContactFields > .dropdown-menu .inner .dropdown-menu li a {
  white-space: normal !important;
}

.modal-body--import {
  max-width: 100%;
  margin: 0 20px;
}

.chip {
  display: inline-block;
  padding: 0 25px;
  height: 50px;
  font-size: 17px;
  line-height: 50px;
  border-radius: 25px;
  background-color: #f1f1f1;
}
.chip .closebtn {
  padding-left: 10px;
  color: #999;
  float: right;
  font-weight: bold;
  font-size: 19px;
  cursor: pointer;
  line-height: inherit;
}
.chip .closebtn:hover {
  color: black;
}
select.form-control:not([size]):not([multiple]) {
  height: auto;
  background-color: white;
}
select.form-control:active,
select.form-control:focus {
  background-color: white;
}
input.form-control {
  border: 1px solid;
  border-color: #ced4da;
}
input.form-control.is-invalid {
  border-color: #e93d3d !important;
}
select {
  appearance: auto;
  border: 1px solid #ced4da;
}
.card {
  border: 1px solid rgba(0, 0, 0, 0.125);
}

label {
  font-size: 0.85rem;
  color: black;
}
.btn.btn-primary:focus,
.btn.btn-primary:active {
  color: #fff;
  background-color: #0871d3;
  border-color: #086bc7;
}
</style>
