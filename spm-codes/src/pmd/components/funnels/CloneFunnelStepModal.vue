<template>
  <div class="modal fade hl_clone_funnel_step--modal" tabindex="-1" role="dialog" ref="modal">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
						<h2 class="modal-title">Clone {{ stepName }}</h2>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div class="same-funnel">
              <label for="currentFunnel">Add it to same {{ entityName }}</label>
              <h4>This will add it to current {{ entityName }}</h4>
              <UIButton name="currentFunnel" @click="cloneStep()" type="button" use="secondary" class="justify-center">
                <i class="fa fa-copy mr-2"></i>&nbsp;Clone {{ funnelIsWebsite ? 'Page' : 'Step' }} in this {{ entityName }}
              </UIButton>
            </div>
            <div class="multiple-funnels">
              <div class="form-group">
                <label for="location">Select location</label>
                <UILocationPicker
                  v-model="selectedLocation"
                  id="location-picker-funnel-step"
                  :multiple="false"
                />
              </div>
              <div class="form-group ">
                <label for="funnels">Add it to multiple {{ entityName.toLowerCase() }}s</label>
                <span v-if="errorInFetchingFunnels">Error while fetching {{ entityName.toLowerCase() }}s</span>
                <span v-if="fetchingFunnels">Fetching {{ entityName }}s..</span>
                <!-- <UIMultiSelect
                  v-else
                  :disabled="!selectedLocation"
                  :options="funnels"
                  v-model="selectedFunnels"
                  name="funnels"
                  :placeholder="`Select ${entityName}s`"
                  :multiple="true"
                  :closeOnSelect="true"
                /> -->
                <UIMultiSelect
                  v-else
                  v-model="selectedFunnels"
                  :disabled="!selectedLocation"
                  :options="funnels"
                  :multiple="true"
                  :allowEmpty="true"
                  deselectLabel="Can't remove this value"
                  :closeOnSelect="true"
                  :placeholder="`Select ${entityName}s`"
                  :maxSelected="() => selectedFunnels.length <= maxSelectableFunnels"
                  :loading="loading.locations"
                >
                >
                  <template #spinner="{ loading }">
                    <div v-if="loading" style="border-left-color: rgba(88,151,251,0.71)" class="vs__spinner"></div>
                  </template>
                </UIMultiSelect>
                <span>Select upto {{ maxSelectableFunnels }} {{ entityName.toLowerCase() }}s</span>
              </div>
              <UIButton
                :disabled="cloneStepButtonDisabled"
                :style="{marginTop: '12px'}"
                @click="cloneStepAcrossFunnels()"
                type="button"
                use="primary"
                class="justify-center"
              >
                <i class="fa fa-share mr-2"></i>&nbsp;Add {{ funnelIsWebsite ? 'Page' : 'Step' }} to more {{ entityName.toLowerCase() }}s
              </UIButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { Funnel, Location } from '../../../models';
import { FunnelType } from '../../../models/funnel';

export default Vue.extend({
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    stepName: {
      type: String,
      default: 'Step'
    },
    funnelIsWebsite: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    entityName(): String {
      if (this.funnelIsWebsite) {
        return 'Website'
      }

      return 'Funnel'
    },
    cloneStepButtonDisabled(): boolean {
      return this.selectedFunnels.length === 0
    }
  },
  data() {
    return {
      allLocations: [],
      funnels: [] as any[],
      fetchingFunnels: false,
      errorInFetchingFunnels: false,
      selectedFunnels: [],
      selectedLocation: [],
      maxSelectableFunnels: 25,
      loading: {
        locations: false
      }
    }
  },
  methods: {
    hideModal() {
      this.$emit('hide');
    },
    cloneStep() {
      this.$emit('cloneStep')
    },
    cloneStepAcrossFunnels() {
      const funnelIds = this.selectedFunnels.map((f: Funnel) => f.id)
      const selectedLocationId = this.selectedLocation.id
      this.$emit('cloneStep', { funnelIds, locationId: selectedLocationId })
    },
    async fetchFunnels(locationId: string) {
      // const locationId = this.$router.currentRoute.params.location_id
      // const locationId = this.selectedLocation
      this.funnels = []
      this.fetchingFunnels = true
      this.errorInFetchingFunnels = false

      try {
        await this.$store.dispatch('auth/refreshFirebaseToken', { locationId, refresh: false } , {root: true});

        const allFunnels = await Funnel.getAllByLocation(locationId)

        this.funnels = allFunnels.reduce((agg: any[], funnel: Funnel) => {
          const { name, id } = funnel

          if (this.funnelIsWebsite && funnel.type === FunnelType.Website) {
            return [...agg, { label: name, id, value: id }]
          } else if (!this.funnelIsWebsite && (!funnel.type || funnel.type === FunnelType.Funnel)) {
            return [...agg, { label: name, id, value: id }]
          }

          return agg
        }, [])

        // const requiredFunnelType = this.funnelIsWebsite ? FunnelType.Website : FunnelType.Funnel

        // const requiredList = allFunnels.filter((funnel) => !funnel.type || funnel.type === requiredFunnelType)

        // this.funnels = requiredList.map((funnel: Funnel) => {
        //   const name = funnel.name
        //   const id = funnel.id
        //   return { label: name, id }
        // })
      } catch (error) {
        console.error('some error while fetching funnels')
        this.errorInFetchingFunnels = true
      }
      this.fetchingFunnels = false
    }
  },
  mounted() {
    const _self = this;
		$(this.$refs.modal).on('hidden.bs.modal', function () {
			_self.$emit('hide');
    });
  },
  beforeDestroy() {
		$(this.$refs.modal).off('hidden.bs.modal');
  },
  watch: {
    showModal(newValue, oldValue) {
      this.selectedFunnels = [];
      if (newValue && !oldValue) {
        // show the modal
        $(this.$refs.modal).modal('show')
      } else if(!newValue && oldValue) {
        // hide the modal
        $(this.$refs.modal).modal('hide');
      }
    },
    'selectedLocation.id'(newValue) {
      this.selectedFunnels = []
      if (newValue) {
        this.fetchFunnels(newValue)
      } else {
        this.selectedLocation = []
        this.funnels = []
      }
    }
  }
})
</script>
<style scoped>
.modal-body--inner {
  display: flex;
}

.same-funnel,
.multiple-funnels {
  flex-grow: 1;
  width: 50%;
  /* text-align: center; */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.same-funnel {
  padding-right: 10px;
}

.multiple-funnels {
  border-left: 1px solid lightgray;
  padding-left: 10px;
}
</style>
