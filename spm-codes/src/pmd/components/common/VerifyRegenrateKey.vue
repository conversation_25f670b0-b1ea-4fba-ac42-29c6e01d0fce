<template>
  <div
    id="delete-confirm-modal"
    ref="modal"
    aria-hidden="true"
    aria-labelledby="client-checkin--modalLabel"
    class="modal fade"
    role="dialog"
    tabindex="-1"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 id="delete-confirm-modal-label" class="modal-title">
            {{ title }}
          </h5>
          <button aria-label="Close" class="close" type="button"
                  @click="closeModal">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <form>
              <div class="form-group">
                <div class="alert alert-warning" role="alert"
                     style="color: #856404;">
                  <i class="fas fa-exclamation-triangle"></i> This action cannot
                  be reversed.
                </div>
              </div>
              <div class="form-group">
                <p>{{ msg }} </p>
              </div>
              <div class="form-group">
                <label>
                  Confirm reset by typing <a href="javascript:void(0)">CONFIRM</a> below:
                </label>

                <UITextInputGroup
                  id="delete-confirm"
                  v-model="regenerateConfirm"
                  :class="regenerateConfirm.toLowerCase() === 'confirm' ? 'is-valid' : 'is-invalid'"
                  type="text"
                />
                <div
                  v-if="regenerateConfirm && regenerateConfirm !== 'confirm'"
                  id="validation-feedback"
                  class="invalid-feedback">
                  Enter <strong>confirm</strong> to regenerate
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer--inner">
            <UIButton use="outline" type="button" @click="closeModal">
              Cancel, Keep
            </UIButton>
            <UIButton
              :disabled="deleteDisabled"
              use="danger"
              type="button"
              @click="regenerateConfirmed"
            >Yes, Reset
            </UIButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: 'Reset location api key?'
    },
    msg: {
      type: String,
      default: 'This locations api key will be reset.'
    }
  },
  data() {
    return {
      regenerateConfirm: '',
      deleteDisabled: true
    }
  },
  watch: {
    showModal(status: Boolean) {
      this.regenerateConfirm = ''
      var _self = this
      if (status) {
        $(this.$refs.modal)
          .modal('show')
          .on('hidden.bs.modal', function() {
            _self.$emit('hidden', true)
          })
      } else {
        $(this.$refs.modal).modal('hide')
      }
    },
    regenerateConfirm(value: string) {
      this.deleteDisabled = !(value && value.trim().toLowerCase() === 'confirm')
    }
  },
  methods: {
    regenerateConfirmed() {
      this.$emit('confirm', true)
    },
    closeModal() {
      this.$emit('close', false)
      $(this.$refs.modal).modal('hide')
    }
  }
})
</script>
<style scoped>
.modal .modal-dialog {
  max-width: 500px;
}
.modal .modal-body {
  padding: 30px 30px;
}
</style>
