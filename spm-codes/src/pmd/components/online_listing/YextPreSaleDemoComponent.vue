<template>
  <!--  -->
  <section>
    <section id="online-listing">
      <div>
        <div class="card">
          <div class="card-header card-header--compact space-between">
            <h2>Listings</h2>
          </div>
          <div class="card-body">
            <div class="dashboard">
              <div>
                <highcharts
                  :options="chartOptions"
                  :callback="chartLoaded"
                ></highcharts>
              </div>
              <div>
                <div class="partners">
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-google-my-business.svg"
                      style="width: 45px"
                    />
                    <div>Google My Business</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions//medallion-ALEXA.png"
                      style="width: 45px"
                    />
                    <div>Alexa</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-apple.svg"
                      style="width: 45px"
                    />
                    <div>Apple</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-bing.svg"
                      style="width: 45px"
                    />
                    <div>Bing</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-facebook.svg"
                      style="width: 45px"
                    />
                    <div>Facebook</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-yelp.svg"
                      style="width: 45px"
                    />
                    <div>Yelp</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-foursquare.svg"
                      style="width: 45px"
                    />
                    <div>Foursquare</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-superpages.svg"
                      style="width: 45px"
                    />
                    <div>Superpages</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-yahoo.svg"
                      style="width: 45px"
                    />
                    <div>Yahoo!</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-citysearch.svg"
                      style="width: 45px"
                    />
                    <div>Citysearch</div>
                  </div>
                  <div>
                    <img
                      src="/pmd/img/logo_medallions/medallion-YPCOM.png"
                      style="width: 45px"
                    />
                    <div>Yellowpages</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="card hl_online-analysis--table">
          <div class="card-body --no-padding">
            <div class="table-wrap">
              <table class="table table-sort">
                <thead>
                  <tr>
                    <th data-sort="string">
                      Network
                      <!-- <i class="icon icon-arrow-down-1"></i> -->
                    </th>
                    <th data-sort="string">
                      Name
                      <!-- <i class="icon icon-arrow-down-1"></i> -->
                    </th>
                    <th data-sort="string">
                      Address
                      <!-- <i class="icon icon-arrow-down-1"></i> -->
                    </th>
                    <th data-sort="string">
                      Phone
                      <!-- <i class="icon icon-arrow-down-1"></i> -->
                    </th>
                    <th data-sort="float">
                      Reviews
                      <!-- <i class="icon icon-arrow-down-1"></i> -->
                    </th>
                    <th>No.</th>
                    <th data-sort="string">
                      Status
                      <!-- <i class="icon icon-arrow-down-1"></i> -->
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <template v-for="listing in listings">
                    <YextListingRow
                      v-if="
                        listings && (listing.listingUrl || listing.publisherId)
                      "
                      v-bind:yextReport="listing"
                      v-bind:key="listing.publisherId"
                      v-bind:location="location"
                    />
                  </template>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </section>
  </section>
</template>

<script lang="ts">
import Vue from 'vue'
import { Location, User, ScanReport } from '@/models'
import ListingHelper from '../../../util/listing_helper'
import { Chart } from 'highcharts-vue'
import { mapState } from 'vuex'

const YextListingRow = () => import('./YextListingRow.vue')

let location: Location | undefined
let totalCountGraph = 0
export default Vue.extend({
  components: {
    YextListingRow,
    highcharts: Chart,
  },
  data() {
    return {
      currentLocationId: '',
      location: {} as Location,
      scanReport: {} as ScanReport | undefined,
      chartOptions: {
        chart: {
          type: 'pie',
          plotBackgroundColor: null,
        },
        credits: {
          enabled: !1,
        },
        title: {
          text: '',
        },
        plotOptions: {
          pie: {
            allowPointSelect: !0,
            cursor: 'pointer',
            center: ['50%', '50%'],
            innerSize: '70%',
            startAngle: 180,
            dataLabels: {
              style: {
                fontWeight: 'normal',
              },
              formatter: function () {
                return this.point.hideLabel
                  ? null
                  : '\x3cb\x3e' +
                      this.point.name +
                      '\x3c/b\x3e ' +
                      (
                        ((this.point.labelValue || this.point.y) /
                          totalCountGraph) *
                        100
                      ).toFixed(2) +
                      '%'
              },
            },
            showInLegend: !0,
          },
        },
        legend: {
          symbolRadius: 3,
          margin: 30,
          squareSymbol: !0,
        },
        series: [
          {
            type: 'pie',
            name: 'Listings',
          },
        ],
        tooltip: {
          valueSuffix: '%',
        },
      },
      theChart: {},
      totalCount: 0,
      liveCount: 0,
      processingCount: 0,
      unavCount: 0,
      optedCount: 0,
      listings: [] as { [key: string]: any }[] | undefined,
    }
  },

  watch: {
    listings() {
      if (this.theChart) {
        this.populateChart()
      }
    },
    theChart() {
      if (this.theChart) {
        this.populateChart()
      }
    },
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.location = new Location(
      await this.$store.dispatch('locations/getById', this.currentLocationId)
    )
    this.listings = [
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: '123LOCAL',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'https://123local.com/profile/gohighlevel',
        screenshotUrl:
          'http://a.mktgcdn.com/p/fzZ074EUeOUJnon9xlFUM7jSjxfIgV01z4uFZz7Wu3k/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: '2FINDLOCAL',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'https://www.2findlocal.com/b/********/gohighlevel-eugene-or',
        screenshotUrl:
          'http://a.mktgcdn.com/p/KhNgxDBA2rA_NCoXWJU2koCSpaTUwcqc-nOGRj62ZH8/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: '8COUPONS',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'https://www.8coupons.com/discounts/gohighlevel-eugene-97401',
        screenshotUrl:
          'http://a.mktgcdn.com/p/5wp7OhEvB9F-sjekVM8LciSzOTYx83xECOmqo5xWL4A/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'ABLOCAL',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'http://ablocal.com/us/eugene-or/**********-gohighlevel/',
        screenshotUrl:
          'http://a.mktgcdn.com/p/Dx46VVxGhCNvg91vczTA0z0MUxUnCo0alvgcQVWpYBw/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'ALEXA',
        status: 'NOT SUBMITTED ',
        screenshotUrl: 'NO SCREENSHOT FOR PARTNER',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'AMERICANTOWNSCOM',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'http://www.americantowns.com/yext/listing/yx-********',
        screenshotUrl:
          'http://a.mktgcdn.com/p/LqP-BWGOzsVLa-Fx_zuUhTj-iwLLUOly0qFOtJlfTd0/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'APPLE',
        status: 'NOT SUBMITTED ',
        screenshotUrl: 'NO SCREENSHOT FOR PARTNER',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'AROUNDME',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'http://www.yext.com/partnerpages/aroundme/gohighlevel-eugene-oregon-us-ea3eb5',
        screenshotUrl:
          'http://a.mktgcdn.com/p/5pTpofD1f-kRjeOB5aBA6rpY1xba2a09z1kB5O1OwIY/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'BAIDUGLOBAL',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'https://map.baidu.com/?shareurl=1&poiShareUid=d0144216d139f925c829153c',
        screenshotUrl: 'NO SCREENSHOT FOR PARTNER',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'BING',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'http://www.bing.com/maps?ss=ypid.YN873x8504933093538762353&mkt=en-US',
        screenshotUrl:
          'http://a.mktgcdn.com/p/tm6O7Etr7Q9rMI5trh0qr0uySwYoaxfG4EYhMyactGI/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'BROWNBOOKNET',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'https://brownbook.net/business/********/gohighlevel',
        screenshotUrl:
          'http://a.mktgcdn.com/p/xBYqEz1V6LtT65b3w97HGkSs1pw09iLQ_Zxl6rHYouE/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'CENTRALINDEXUS',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'https://us.centralindex.com/company/****************',
        screenshotUrl:
          'http://a.mktgcdn.com/p/Aw2fsrc4wK9eQ6nZUAaN8RAQHA_YOBhZzwzXzIhNUHc/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'CHAMBEROFCOMMERCECOM',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'https://www.chamberofcommerce.com/united-states/oregon/eugene/computers-peripherals-and-software/**********-gohighlevel',
        screenshotUrl:
          'http://a.mktgcdn.com/p/zJms5QWhdhOM-1kGyaEdfLplcj0mHZzKCz7Qgv-JN-s/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'CITYSEARCH',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'http://www.citysearch.com/profile/*********/eugene_or/gohighlevel.html',
        screenshotUrl:
          'http://a.mktgcdn.com/p/ZgVxCT9SnxdMAAIW4L4qdJAhIcwHUSEhbrDS2EYuvUY/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'CITYSQUARES',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'http://citysquares.com/b/gohighlevel-********',
        screenshotUrl:
          'http://a.mktgcdn.com/p/LOKxuTEuAOtgmBFKdUWDIM7JHnGAnnksDFf7XaOzxao/1280x950.png',
        alternateBrands: [],
      },
      {
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'CKMAPS',
        status: 'NOT SUBMITTED ',
        statusDetails: [
          {
            code: '14',
            message:
              'Your business is not currently categorized for this partner. Please add additional categories to this location. If you have recently added categories, please allow 24 hours for this message to reset.',
            type: 'UNAVAILABLE_REASON',
            actionable: false,
          },
        ],
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'CYLEX',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'https://www.cylex.us.com/company/gohighlevel-********.html',
        screenshotUrl:
          'http://a.mktgcdn.com/p/OVsyRN_in5a6TV61l-cYRHl1RaOkUL-K817yspcG6xA/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'DUNANDBRADSTREET',
        status: 'NOT SUBMITTED ',
        screenshotUrl: 'NO SCREENSHOT FOR PARTNER',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'ELOCAL',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'https://www.elocal.com/profile/gohighlevel-********',
        screenshotUrl:
          'http://a.mktgcdn.com/p/XPwNKsqPPS885pvMi29CpVJusyWl3fFT4ackayYg15Y/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'EZLOCAL',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'http://ezlocal.com/or/eugene/computer-software-store/**********',
        screenshotUrl:
          'http://a.mktgcdn.com/p/EPFLbSR7t_n1nYLmtuEY5pY7Hv407j3jyL355Xtgils/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'FACEBOOK',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'https://www.facebook.com/***************',
        alternateBrands: [
          {
            brandName: 'Instagram',
            listingUrlx:
              'https://www.instagram.com/explore/locations/***************/',
          },
        ],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'FINDOPEN',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'https://find-open.com/eugene/gohighlevel-********',
        screenshotUrl:
          'http://a.mktgcdn.com/p/IJ2bL3kkf6jy8kVdAozN8zvQj7sWlMDwsLgM4uClmR8/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'FOURSQUARE',
        status: 'NOT SUBMITTED ',
        screenshotUrl: 'NO SCREENSHOT FOR PARTNER',
        alternateBrands: [],
      },
      {
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'GOLOCAL247',
        status: 'NOT SUBMITTED ',
        alternateBrands: [],
      },
      {
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'GOOGLEMYBUSINESS',
        status: 'NOT SUBMITTED ',
        alternateBrands: [
          {
            brandName: 'Waze',
          },
        ],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'HERE',
        status: 'NOT SUBMITTED ',
        screenshotUrl: 'NO SCREENSHOT FOR PARTNER',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'HOTFROG',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'https://www.hotfrog.com/company/****************',
        screenshotUrl:
          'http://a.mktgcdn.com/p/9QTcvw95-AERsV4F6z-9QWE_sH508lSAr1fxU1nMcsU/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'IBEGIN',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'http://www.ibegin.com/directory/visit/********',
        screenshotUrl:
          'http://a.mktgcdn.com/p/bNBPzamBAPq6eJZA23mPvEFXxgLQ4TqRBL_lga_zw5Q/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'IGLOBAL',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'https://iglobal.co/united-states/eugene/gohighlevel',
        screenshotUrl:
          'http://a.mktgcdn.com/p/rpRXgH8NwTfrX4_g9N6XWAsoHzVs2zK455YiPnwCfKc/1280x950.png',
        alternateBrands: [],
      },
      {
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'INSIDERPAGES',
        status: 'NOT SUBMITTED ',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'LOCALCOM',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'http://www.local.com/business/details/eugene-or/gohighlevel-*********.aspx',
        screenshotUrl:
          'http://a.mktgcdn.com/p/a0RIQaHQLBkfRFK_-6RDOsS7I7T7jOnLyHlI2DQl_Q8/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'LOCALDATABASE',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'https://www.localdatabase.com/l/gohighlevel',
        screenshotUrl:
          'http://a.mktgcdn.com/p/hY7tcEN5TN9pFxvwR5hLX14aSm9hlacfWve87di0cEo/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'LOCALPAGES',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'http://localpages.com/or/eugene/lpd-********',
        screenshotUrl:
          'http://a.mktgcdn.com/p/llPXy5yEmXMFffveo_d35taM3F8t7XeWEy9Ky97VRvE/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'LOCALSTACK',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'https://localstack.com/biz/gohighlevel-eugene-or/********',
        screenshotUrl:
          'http://a.mktgcdn.com/p/R32TEGAp9b8RSSrhEKu2NhaZigVqO6tnZJyHqDSbdVg/1280x950.png',
        alternateBrands: [],
      },
      {
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'MAFENGWO',
        status: 'NOT SUBMITTED ',
        statusDetails: [
          {
            code: '14',
            message:
              'Your business is not currently categorized for this partner. Please add additional categories to this location. If you have recently added categories, please allow 24 hours for this message to reset.',
            type: 'UNAVAILABLE_REASON',
            actionable: false,
          },
        ],
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'MAPQUEST',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'http://www.mapquest.com/places/-*********/',
        screenshotUrl:
          'http://a.mktgcdn.com/p/nA-OtcMHdAph4XjInDvEMyxx21SMEnbKofXmP84Ni78/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'MAPSTR',
        status: 'NOT SUBMITTED ',
        screenshotUrl: 'NO SCREENSHOT FOR PARTNER',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'MERCHANTCIRCLE',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'https://www.merchantcircle.com/gohighlevel-eugene-or',
        screenshotUrl:
          'http://a.mktgcdn.com/p/BxIl7sd8mEZaFWlbj_bnVK8v5Gv7cd3gMfldoLn6Ri4/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'MYLOCALSERVICES',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'https://www.mylocalservices.com/Oregon/Computer_Software/********/GoHighLevel.html',
        screenshotUrl:
          'http://a.mktgcdn.com/p/KZIG73Ks54SEdgF1S8MKsZMA0d2ColNcAytJvKauNEs/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'N49CA',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'http://www.n49.com/biz/4346133/gohighlevel-or-eugene-2197-oakmont-way/',
        screenshotUrl:
          'http://a.mktgcdn.com/p/Uc2rWjOukUijVguYWx3Dn4uFDAKMRthk4aD7aDTTcmU/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'NAVMII',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'http://www.yext.com/partnerpages/navmii/gohighlevel-eugene-oregon-us-ea3eb5',
        screenshotUrl:
          'http://a.mktgcdn.com/p/c06kGS-1Lymy6O-a7SqcIvUVQbBS3hTnIpaEp8NiM2s/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'NDRIVE',
        status: 'NOT SUBMITTED ',
        screenshotUrl: 'NO SCREENSHOT FOR PARTNER',
        alternateBrands: [],
      },
      {
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'NEXTDOOR',
        status: 'NOT SUBMITTED ',
        statusDetails: [
          {
            code: '14',
            message:
              'Your business is not currently categorized for this partner. Please add additional categories to this location. If you have recently added categories, please allow 24 hours for this message to reset.',
            type: 'UNAVAILABLE_REASON',
            actionable: false,
          },
        ],
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'OPENDI',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'https://eugene-or.opendi.us/9678633.html',
        screenshotUrl:
          'http://a.mktgcdn.com/p/B0UbX7vdXexwUeqTRrgIFjLBIBvvf3jJVkS-EGRiSkg/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'PITNEYBOWES',
        status: 'NOT SUBMITTED ',
        screenshotUrl: 'NO SCREENSHOT FOR PARTNER',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'POINTCOM',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'http://www.pointcom.com/business/********',
        screenshotUrl:
          'http://a.mktgcdn.com/p/QPTuKSXnCu_ZvujqXaRQsTy0MLBX1BpFHXmMi2OSf4c/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'PROPERTYCAPSULE',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'https://maps.propertycapsule.com/internal/listing/3272117/profile',
        screenshotUrl: 'NO SCREENSHOT FOR PARTNER',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'SHOWMELOCAL',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'https://www.showmelocal.com/profile.aspx?bid=********',
        screenshotUrl:
          'http://a.mktgcdn.com/p/lkymq9MHEVdZPWjMU6nvbKRvHH2S7m3NccIbEFcVFcs/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'SNAPCHAT',
        status: 'NOT SUBMITTED ',
        screenshotUrl: 'NO SCREENSHOT FOR PARTNER',
        statusDetails: [
          {
            code: '248',
            message:
              'Your business is not currently categorized for this partner. Please add additional categories to this location. If you have recently added categories, please allow 24 hours for this message to reset.',
            type: 'UNAVAILABLE_REASON',
            actionable: false,
          },
        ],
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'SOLEO',
        status: 'NOT SUBMITTED ',
        screenshotUrl: 'NO SCREENSHOT FOR PARTNER',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'TELLOWS',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'https://www.tellows.com/num/**********',
        screenshotUrl:
          'http://a.mktgcdn.com/p/p6E2KcVedzYxMfOaYpwlY2M3II5nI2n0JEVusgokBso/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'TOMTOM',
        status: 'NOT SUBMITTED ',
        screenshotUrl: 'NO SCREENSHOT FOR PARTNER',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'TOPRATEDLOCAL',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'https://www.topratedlocal.com/v3.0/yext/publisher/profile/********',
        screenshotUrl:
          'http://a.mktgcdn.com/p/FOL5y-orDxt2ECUrG1n184O28FtNjNAixsSPR5D8NiY/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'TUPALO',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'http://www.tupalo.co/eugene-oregon/gohighlevel',
        screenshotUrl:
          'http://a.mktgcdn.com/p/sqTRBbeHz2xHQaC-MYDT-gQpvviSUEgwBfoQskVCByc/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'UBER',
        status: 'NOT SUBMITTED ',
        screenshotUrl: 'NO SCREENSHOT FOR PARTNER',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'USCITYNET',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'https://uscity.net/listing/gohighlevel-********',
        screenshotUrl:
          'http://a.mktgcdn.com/p/XnGJbomtQNI2ohcA7EO-kwVBgZz_WSjC8yjBcvSwkZA/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'VOTEFORTHEBEST',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'https://voteforthebest.com/oregon/eugene/gohighlevel/2666099.html',
        screenshotUrl:
          'http://a.mktgcdn.com/p/J042uYYRR-s4VjEBrXWvOA5jVqLF-pLMeq6eyaRAh60/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'WHERETO',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'https://wheretoapp.com/search?poi=15a64d214adaafaf016e107c92af737d',
        screenshotUrl:
          'http://a.mktgcdn.com/p/SpA1YSMi4JaAA9AYBg4lVzFlb3CG2QQJoYblQxf9gO0/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'YAHOO',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'http://local.yahoo.com/info-*********',
        screenshotUrl:
          'http://a.mktgcdn.com/p/fXIugixgt9jm_OsxLMl73UJBuw-QJX2SHkxYNbgQxrQ/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'YALWA',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'https://eugene.yalwa.com/ID_139039673/GoHighLevel.html',
        screenshotUrl:
          'http://a.mktgcdn.com/p/6BBI_2JaRlBvrQZ9clT8Y47bsy5GjLTURkSpzBrwOZo/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'YANDEX',
        status: 'NOT SUBMITTED ',
        screenshotUrl: 'NO SCREENSHOT FOR PARTNER',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'YASABE',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'https://www.yasabe.com/en/eugene-or/d-*************-gohighlevel.html',
        screenshotUrl:
          'http://a.mktgcdn.com/p/xcq8f_8_-4wsdK_IjISWgEAQHsQ09s1wV2kKpocZwIc/1280x950.png',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'YELLOWMOXIE',
        status: 'NOT SUBMITTED ',
        listingUrlx: 'http://www.yellowmoxie.com/yx-********.ym',
        screenshotUrl:
          'http://a.mktgcdn.com/p/1ZqT7Ci3555DMVJz_F63FW0G33_3dSetmRDcqi-PrI8/1280x950.png',
        alternateBrands: [],
      },
      {
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'YELLOWPAGECITYCOM',
        status: 'NOT SUBMITTED ',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'YELLOWPAGESGOESGREEN',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'https://www.yellowpagesdirectory.com/?page=show&state=OR&id=387094',
        screenshotUrl:
          'http://a.mktgcdn.com/p/joNBRqqu20-DWK4j_K4u0_5gnUReCqI1lbqThO0d4lY/1280x950.png',
        alternateBrands: [],
      },
      {
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'YELP',
        status: 'NOT SUBMITTED ',
        alternateBrands: [],
      },
      {
        id: '*********',
        locationId: '5784746003487769586',
        accountId: '1967236',
        publisherId: 'YPCOM',
        status: 'NOT SUBMITTED ',
        listingUrlx:
          'http://www.yellowpages.com/eugene-or/mip/gohighlevel-*********',
        screenshotUrl:
          'http://a.mktgcdn.com/p/3f3C3hincwwWBX3r-cE8DxijIT9NNgSNRtdw5jz2L3o/1280x950.png',
        alternateBrands: [
          {
            brandName: 'DexKnows',
            listingUrlx:
              'http://www.dexknows.com/eugene-or/bp/gohighlevel-*********',
          },
          {
            brandName: 'Superpages',
            listingUrlx:
              'http://www.superpages.com/eugene-or/bpp/gohighlevel-*********',
          },
        ],
      },
    ]
  },
  async mounted() {
    // dummy data
    this.theChart.series[0].setData([])
    this.theChart.series[0].addPoint({
      color: '#178ACD',
      x: 0,
      y: 55,
      name: 'NOT SUBMITTED ',
    })

    this.theChart.series[0].addPoint({
      color: '#23D2BE',
      x: 1,
      y: 16,
      name: 'Processing',
    })

    this.theChart.series[0].addPoint({
      color: '#F2326B',
      x: 2,
      y: 4,
      name: 'Opted Out',
    })

    this.theChart.series[0].addPoint({
      color: '#FDC131',
      x: 3,
      y: 5,
      name: 'NOT SUBMITTED ',
    })
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    yext_scan(): { [key: string]: any } {
      if (this.scanReport && this.scanReport.yextData) {
        return JSON.parse(this.scanReport.yextData)
      } else {
        return {}
      }
    },

    yext_errors(): any[] {
      var _self = this
      var errors = [] as any
      if (this.yextData) {
        lodash.each(this.yextData, function (report) {
          var a = []
          a.push(_self.imgUrl(report))
          if (!report.listingFound) {
            return a.push('listingNotFound'), a
          }
          report.matchAddress || a.push('address')
          report.matchName || a.push('name')
          report.matchPhone || a.push('phone')
          if (a.length > 1) {
            errors.push(a)
          }
        })
      }
      return errors
    },
    yext_error_rate() {
      var error_rate = 0
      if (this.yext_errors && this.yextData && this.yextData.length > 0) {
        error_rate = Math.floor(
          (this.yext_errors.length / this.yextData.length) * 100
        )
      }
      return error_rate
    },
    yext_first_three() {
      if (this.yext_errors) {
        return this.yext_errors.slice(0, 3)
      }
    },
    lighthouse_scan(): { [key: string]: any } {
      if (this.scanReport && this.scanReport.lighthouseData) {
        return JSON.parse(this.scanReport.lighthouseData)
      } else {
        return {}
      }
    },
    speed_score() {
      if (
        this.lighthouse_scan &&
        this.lighthouse_scan.audits &&
        this.lighthouse_scan.audits['speed-index-metric']
      ) {
        return this.lighthouse_scan.audits['speed-index-metric']['score']
      } else {
        return 0
      }
    },
    progress_bar_class: function () {
      if (this.speed_score && this.speed_score <= 35) {
        return {
          'progress-bar': true,
          'bg-danger': true,
        }
      } else if (
        this.speed_score &&
        this.speed_score > 35 &&
        this.speed_score <= 85
      ) {
        return {
          'progress-bar': true,
          'bg-warning': true,
        }
      } else if (this.speed_score && this.speed_score > 85) {
        return {
          'progress-bar': true,
          'bg-success': true,
        }
      } else {
        return {
          'progress-bar': true,
        }
      }
    },
    website_link(): string | undefined {
      if (this.location && this.location.website) {
        return this.location.website.slice(
          this.location.website.indexOf('http://www.') != -1 ? 11 : 0
        )
      }
    },
  },

  beforeDestroy() {
    if (this.$private.cancelBusinessListingSubscription) {
      this.$private.cancelBusinessListingSubscription()
    }
  },
  methods: {
    chartLoaded(chart: any) {
      this.theChart = chart
    },
    async fetchLocationData() {
      this.location = new Location(
        await this.$store.dispatch('locations/getById', this.currentLocationId)
      )
      console.log('Got location:', this.location.name)
      try {
        this.scanReport = await ScanReport.getByLocationId(
          this.currentLocationId
        )
        console.log('Got scanReport:', this.scanReport)
      } catch (err) {
        console.error('No scan report found!')
        this.scanReport = undefined
      }
      this.getYextData()
    },

    imgUrl(report: any): string | undefined {
      if (report && report.brandId) {
        const listing = ListingHelper.getNetworkInfo(report.brandId)
        if (listing) return listing.imgPath
      }
    },
    async getYextData() {
      if (this.location.yextId) {
        const response = await this.$http.get(
          'api/yext/' + this.currentLocationId + '/listings'
        )
        let _self = this

        if (response.status === 200) {
          this.listings = response.data
        }
      } else {
        this.listings = undefined
      }
    },
    populateChart() {
      var _self = this

      this.theChart.series[0].setData([])

      if (this.listings && this.listings.length > 0) {
        this.totalCount = this.listings.length
        totalCountGraph = this.totalCount

        for (let key in this.listings) {
          var listing = this.listings[key]
          if (!listing.status) continue
          if (listing.status == 'NOT SUBMITTED ') {
            _self.liveCount += 1
          } else if (listing.status == 'NOT SUBMITTED ') {
            _self.unavCount += 1
          } else if (listing.status == 'OPTED_OUT') {
            _self.optedCount += 1
          } else if (listing.status.indexOf('WAITING') > -1) {
            _self.processingCount += 1
          }
        }

        this.theChart.series[0].addPoint({
          color: '#178ACD',
          x: 0,
          y: _self.liveCount,
          name: 'NOT SUBMITTED ',
        })

        this.theChart.series[0].addPoint({
          color: '#23D2BE',
          x: 1,
          y: _self.processingCount,
          name: 'Processing',
        })

        this.theChart.series[0].addPoint({
          color: '#F2326B',
          x: 2,
          y: _self.optedCount,
          name: 'Opted Out',
        })

        this.theChart.series[0].addPoint({
          color: '#FDC131',
          x: 3,
          y: _self.unavCount,
          name: 'NOT SUBMITTED ',
        })
      }
    },
  },
})
</script>
<style lang="scss" scoped>
#online-listing {
  position: relative;
  .dashboard {
    display: grid;
    grid-template-columns: 1fr 1.5fr;

    .partners {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      align-content: center;
      height: 100%;
      min-height: 45vh;

      & > div {
        width: 120px;
        height: 150px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        padding: 10px;
      }
    }
  }
}
</style>
