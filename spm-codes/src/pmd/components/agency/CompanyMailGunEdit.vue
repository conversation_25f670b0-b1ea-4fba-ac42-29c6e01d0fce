<template>
  <div class="container-fluid h-100" v-if="company" @keyup="checkEdited()">
    <div class="row h-100">
      <div class="col-sm-12 col-md-auto">
        <div class="form-group">
          <div class="form-control-plaintext py-15">{{company.name}}</div>
        </div>
      </div>
      <template v-if="!showKeyDomain">
        <div class="col-sm-12 col-md-4">
          <div class="form-group">
            <UITextInputGroup
              type="text"
              label="Forwarding Address"
              :readonly="processing"
              placeholder="Forwarding addresses (comma delimited)"
              v-model="forwardingAddresses"/>
          </div>
        </div>
        <div class="col-sm-12 col-md-4">
          <div class="form-group">
            <UITextInputGroup
              type="text"
              label="BCC Address"
              :readonly="processing"
              placeholder="BCC Emails (comma delimited)"
              v-model="bccEmails"
            />
          </div>
        </div>
      </template>
      <template v-if="showKeyDomain">
        <div class="col-sm-12">
          <div class="form-group">
            <UITextInputGroup
              label="API Key"
              type="text"
              :readonly="processing"
              placeholder="API Key"
              v-model="apiKey"
            />
          </div>
        </div>
        <div class="col-sm-12">
          <div class="form-group">
            <UITextLabel>Domain</UITextLabel>
            <template v-show="domain && hosts && hosts.length > 0">
              <select
                :readonly="processing"
                class="form-control selectpicker border"
                placeholder="Select Domain"
                v-on:change="checkEdited()"
                v-model="domain">
                <option v-for="host in hosts" :key="host.id" :value="host.name">{{host.name}}</option>
              </select>
            </template>
          </div>
        </div>
      </template>
       <div class="row" style="width: 100%; margin: 0px;">
        <div class="col-sm-6">
           <UIButton use="danger" v-if="companyMailgun.id" :loading="deleteProcessing" type="button" @click.prevent="removeAccount">
              Delete connection
          </UIButton>
        </div>
        <div class="col-sm-6" style="text-align: right;">
           <UIButton
              v-show="edited || mailgunError"
              type="button"
              use="primary"
              :disabled="disabled && !mailgunError"
              :loading="processing"
              @click.prevent="saveAccount">
                <span>{{ edited ? 'Save' : 'Clear error' }}</span>
            </UIButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { MailGunAccount, Company } from '@/models'

declare var $: any

export default Vue.extend({
  props: ['showKeyDomain', 'companyId', 'companyMailgun'],
  data() {
    return {
      processing: false,
      deleteProcessing: false,
      edited: false,
      forwardingAddresses: '',
      bccEmails: '',
      apiKey: '',
      domain: '',
      hosts: [],
      mailgunAccount: undefined as MailGunAccount | undefined,
      company: undefined as Company | undefined,
      loading: true,
      saving: false,
      searchDebouncer: undefined as NodeJS.Timer | undefined,
      disabled: true,
      mailgunError: ''
    }
  },
  async created() {
      this.fetchData();
  },
  watch: {
    companyId : async function(newVal, oldVal){
        this.fetchData();
    },
    apiKey : async function(newVal,oldVal){
      if (this.showKeyDomain){
        this.disabled = true
        if (this.companyMailgun && this.companyMailgun.api_key === newVal) {
          this.domain = this.companyMailgun.domain;
        } else this.domain = undefined;

        this.clearTimer();
        this.startTimer();
      }
    },
  },
  methods: {
    clearTimer(){
      if (this.searchDebouncer) clearTimeout(this.searchDebouncer);
    },
    startTimer(){
      this.searchDebouncer = setTimeout(this.loadDomains, 700);
    },
    async checkEdited(){
      this.$nextTick(()=>{
      let ac = this.companyMailgun;
      this.edited = !(ac &&
          this.apiKey === ac.api_key &&
          this.domain === ac.domain &&
          this.forwardingAddresses === ac.forwarding_addresses &&
          this.bccEmails === ac.bcc_emails);
       });
    },
    clear() {
      this.mailgunAccount = undefined
      this.company = undefined
      this.hosts = []
      this.forwardingAddresses = ''
      this.bccEmails = ''
      this.processing = false
      this.edited = false
      this.domain = ''
    },
    notifyLoadingStart(){
      this.loading = true;
      this.$emit('refreshing');
    },
    notifyLoadingEnd(){
      this.loading = false;
      this.$emit('refreshed');
    },
    async fetchData() {
      try
      {
        this.notifyLoadingStart();
        this.clear()
        if (this.companyId) {
          this.company = await Company.getById(this.companyId)
          if (!this.company){
              console.log("Can't find the company for given company id")
              return;
          }
          if (!this.companyMailgun) {
            console.log('Nothing to set');
            return;
          }
          if (this.company.id !== this.companyMailgun.company_id){
            console.log("Trying to access other company's account")
            return;
          }
          this.copyFromMailgunAccount(this.companyMailgun);
        }
      }catch(error){
        console.log(error);
      }
      finally{
        this.notifyLoadingEnd();
      }
    },
    async loadDomains(forceLoad?:boolean){
      if (!this.showKeyDomain) return;
      let items = await this.getDomains();
      this.setDomains(items);
    },
    async getDomains(){
      if (!this.apiKey) return [];
      const response = await this.$http.get('/mailgun/get_domains?api_key=' + this.apiKey)
      if (response && response.data.status == '401') {
        alert('Invalid API key')
        return []
      } else {
        this.disabled = false;
        if (response && response.data && response.data.items) return response.data.items;
        else return [];
      }
    },
    async setDomains(items:[]){
      this.$nextTick(()=>{
        this.hosts = items;
        this.refreshPicker()
      });
    },
    copyFromMailgunAccount: function(newVal){
       if (newVal) {
          this.apiKey = newVal.api_key
          this.domain = newVal.domain
          this.forwardingAddresses = newVal.forwarding_addresses
          this.bccEmails = newVal.bcc_emails
          this.mailgunError = newVal.mailgun_error
        }
    },
    removeAccount() {
      if(this.companyMailgun.id) {
        const message = 'This action will remove the Mailgun connection.'
         this.$uxMessage('confirmation', message, async response => {
          if(response === 'ok') {
            this.deleteProcessing = true
            try {
              await this.$http.delete(`/mailgun/${this.companyMailgun.id}`)
              this.$emit('deleted', { companyId: this.companyId })
            } catch (error) {
              console.log(error)
            } finally {
              this.deleteProcessing = false
            }
          }
        })
      }
    },
    async saveAccount() {
      this.processing = true;
      let mailgunAccount;

      try {
          let body = {
            forwardingAddresses: this.forwardingAddresses,
            bccEmails: this.bccEmails,
            apiKey: this.apiKey && this.apiKey.trim(),
            domain: this.domain
          };
          this.$http.post('/mailgun/edit?company_id=' + this.company.id, body).then(async res => {
            if (res && res.status == 200) mailgunAccount = res.data;
            await this.$http.get('/mailgun/set_reply_webhook?company_id=' + this.company.id);
            this.edited = false
            this.processing = false
            this.mailgunError = ''
            this.$emit('saved', { mailgunAccount, companyId: this.company.id });
          }).catch(err => {
            this.edited = false
            this.processing = false
            console.log(err)
          })
      } catch (err) {
        this.edited = false
        this.processing = false
        console.log(err)
      }
    },
    refreshPicker(){
        const $selectpicker = $(this.$el).find('.selectpicker')
        if ($selectpicker) {
          $selectpicker.selectpicker('refresh')
        }
    }
  },
  async mounted() {
    this.refreshPicker();
  },
  async updated() {
    this.refreshPicker();
  },
})
</script>

<!--/*https://stackoverflow.com/questions/********/vertical-align-center-in-bootstrap-4*/-->
