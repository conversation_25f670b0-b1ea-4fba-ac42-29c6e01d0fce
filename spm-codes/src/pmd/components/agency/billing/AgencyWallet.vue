<template>
  <div class="card">
    <div class="card-header">
      <h3>Credits</h3>
    </div>
    <div class="card-body credits-container" v-if="loading">
      <div class="balance">
        <shimmer class="shimmer" />
      </div>
      <div class="recharge-balance">
        <shimmer class="shimmer large" />
        <shimmer class="shimmer small" />
        <shimmer class="shimmer small" />
      </div>
    </div>
    <div v-else class="card-body credits-container">
      <div class="balance">
        <div class="current-balance">
          <p>Current Balance</p>
          <h4>{{ currencySymbol }}&nbsp;{{ currentBalance }}</h4>
          <p class="refresh" @click="$emit('refresh')">
            <i class="fa fa-redo"></i>&nbsp;&nbsp;Refresh
          </p>
        </div>
        <router-link :to="{ name: 'account_wallet_transactions' }">
          See Details&nbsp;&gt;
        </router-link>
      </div>
      <div class="recharge-balance">
        <button @click="$emit('addBalance')" class="btn add-balance">
          <i class="fa fa-credit-card" aria-hidden="true"></i>Add Balance
        </button>
        <div class="auto-recharge">
          <div class="amount">
            <p>Auto recharge with</p>
            <p>
              <span>{{ currencySymbol }}&nbsp;&nbsp;</span>
              <select id="auto-recharge-amount" v-model="rechargeAmount">
                <option
                  v-for="option in autoRechargeOptions"
                  :key="option"
                  :value="option"
                >
                  {{ option }}
                </option>
              </select>
            </p>
          </div>
          <div class="balance-limit">
            <p>when balance is lower than</p>
            <p>
              <span>{{ currencySymbol }}&nbsp;&nbsp;</span>
              <select id="balance-limit-amount" v-model="rechargeThreshold">
                <option
                  v-for="option in balanceThresholdOptions"
                  :key="option"
                  :value="option"
                >
                  {{ option }}
                </option>
              </select>
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="notification"
      :class="[
        notification.show ? 'visible' : 'hidden',
        notification.success ? 'success' : 'failed',
      ]"
    >
      {{ notification.message }}
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import Shimmer from '@/pmd/components/common/shimmers/Shimmer.vue'

export default Vue.extend({
  components: {
    Shimmer,
  },
  props: {
    currentBalance: {
      type: String,
      default: '$200',
    },
    autoRechargeAmount: {
      type: Number,
      default: 0,
    },
    rechargeThresholdBalance: {
      type: Number,
      default: 10,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    currencySymbol: String,
    notification: {
      type: Object,
      default: () => {
        return {
          show: false,
          message: '',
          success: false,
        }
      },
    },
  },
  computed: {
    rechargeAmount: {
      get(): number {
        return this.autoRechargeAmount || this.DEFAULT_AUTO_RECHARGE
      },
      set(newValue: number) {
        this.$emit('updateRechargeAmount', newValue)
      },
    },
    rechargeThreshold: {
      get(): number {
        return this.rechargeThresholdBalance || this.DEFAULT_BALANCE_THRESHOLD
      },
      set(newValue: number) {
        this.$emit('updateThreshold', newValue)
      },
    },
  },
  data() {
    const autoRechargeOptions = [10, 25, 50, 75, 100, 150, 200, 250, 500]
    // const autoRechargeStep = 10
    // for (let i = 0; i < 10; i++) {
    //   const option = (i + 1) * autoRechargeStep
    //   autoRechargeOptions.push(option)
    // }

    const balanceThresholdOptions = [10, 20, 30, 40, 50]
    // const balanceThresholdStep = 10
    // for (let i = 0; i < 5; i++) {
    //   const option = (i + 1) * balanceThresholdStep
    //   balanceThresholdOptions.push(option)
    // }

    return {
      autoRechargeOptions,
      balanceThresholdOptions,
      DEFAULT_AUTO_RECHARGE: 10,
      DEFAULT_BALANCE_THRESHOLD: 10,
    }
  },
})
</script>
<style scoped>
@media only screen and (min-width: 1500px) {
  .credits-container {
    flex-direction: row !important;
  }

  .recharge-balance {
    margin-top: 0 !important;
    margin-left: 24px;
  }
}

.credits-container {
  padding-top: 16px !important;
  padding-bottom: 4px !important;
  display: flex;
  flex-direction: column;
}

.credits-container .balance .shimmer {
  width: 365px;
  height: 87px;
}

.credits-container .recharge-balance .shimmer {
  width: 100%;
  height: 32px;
}

.credits-container .recharge-balance .shimmer.large {
  margin-bottom: 32px;
}

.credits-container .recharge-balance .shimmer.small {
  height: 16px;
}

.credits-container .balance {
  flex: 1;
}

.current-balance {
  text-align: center;
  background: #c6f6d5;
  border-radius: 3px;
  padding: 16px 0 8px 0;
  margin-bottom: 32px;
  flex: 1;
}

.current-balance p {
  color: #4a5568;
  font-size: 13px;
}

.current-balance h4 {
  color: #2f855a;
  font-weight: bold;
  font-size: 27px;
}

.current-balance .refresh {
  text-align: right;
  font-size: 12px;
  margin-right: 12px;
  font-style: italic;
  cursor: pointer;
}

.recharge-balance {
  margin-top: 12px;
  flex: 1;
}

.auto-recharge > div {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
}

.auto-recharge p {
  margin: 0;
}

.auto-recharge select {
  padding: 4px;
  padding-right: 16px;
  width: 70px;
  text-align: center;
  color: #3182ce;
  font-weight: bold;
  border: 1px solid #cbd5e0;
  border-radius: 3px;
}

.recharge-balance .add-balance {
  background: #188bf6;
  width: 100%;
  margin-bottom: 14px;
}

.recharge-balance .add-balance i {
  margin-right: 6px;
}

.notification {
  align-self: flex-end;
  font-style: italic;
  margin-right: 30px;
  margin-bottom: 4px;
  transition: opacity 0.25s ease;
}

.notification.hidden {
  opacity: 0;
}

.notification.show {
  opacity: 1;
}

.notification.success {
  color: #68d391;
}

.notification.failed {
  color: #fc8181;
}
</style>
