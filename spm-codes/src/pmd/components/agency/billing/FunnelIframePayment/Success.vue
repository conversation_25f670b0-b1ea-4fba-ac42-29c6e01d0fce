<template>
  <div id="checkout_status">
    <div class="card">
      <div class="card-body">
        <div class="hl_center hl_text_center">
          <template>
            <h3>🎉 Congratulations! 🎉</h3>
            <p v-if="successMsg" >
              {{successMsg}}
            </p>
            <p v-else>
                {{productName}} has been successfully initiated
            </p>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  async created() {},
  props: {
    successMsg: {
      type: String
    },
    productName:{
      type:String
    }
  },
})
</script>

<style lang="scss" scoped>
#checkout_status {
  padding-top: 50px;
  display: grid;
  justify-content: center;
  align-items: center;
  position: sticky;
  top: 25%;
  left: 0;
  z-index: 1;
  margin-top: -265px;
  .card {
    border: 1px solid #e1e1e1;
    box-shadow: 0 2px 4px 0px #b1b1b13b;
    width: 470px;
    height: 200px;
    .pitch {
      color: #f59e0b;
      margin-bottom: 10px;
    }
  }
}
</style>
