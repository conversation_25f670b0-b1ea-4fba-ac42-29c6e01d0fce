fetch("https://backend.leadconnectorhq.com/notifications/users/UPbDRyLVrJhTxFCBRpvd?limit=25&skip=0&deleted=false", {
    "headers": {
      "accept": "application/json, text/plain, */*",
      "accept-language": "en-US,en;q=0.9",
      "authorization": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Bmupmkaf8qK8TuFSuMAPNt_zg0Uue1a_5_Rzx12PHi-41OqTj8OYdTctc1HqxAthPoAH8l7NtS9KfFrBCIFrRom1N4Fsl-PyEGaSU3_ohYB85mHCJ6qxtE5xIYDtZEfP7rqdt1HZ_7_nEt1xsRpheOT-6_nONN0NiIgN9fCpA4G4sjBSvpd4rBahI9AG5zjubdf4NQ7mH8RSn3-JufHt6Q79RRfHkz7jQqSf8uA0209Fqm-O_cZXf1FHT78uV61vCyNsvbAkjZUxmvPEDa1OqPnMzDz663c9kYojMuRJjT_sQ-mbkbpD5JBYxP5xKmjSvtImBF9S-WMXH5esuZcDDsitjL2ZE4XPkW9_zh9uXI9o1RzHSs8qI1bjbyYWHGSPlz3QM9gpsxBTVPV9fRZ-UTrvIqBWIkuDk0ZMbn4GH7Sw3qRquHEP9gbvy4sQIcqd4vkUkiuWy1PHpsR9AqKITtGtdr5ow7v28CnH0a9tmKezBVKIJSsfOadHpPrd0lJeIKe_A0KjSnPXXYYpKsIB5bLAlJHb6kVuqVYC9JpebkiOKhh7AHfC6W3ACmk8AFlF0_1IDwxV3AawyUccJo5wYasSc6NYbal6CoNUEa0Fs8BkbbT2T6m0IUhCgnuKEyLrhUzE3zBSrpsLnS_l7vAb_mYvX2hqKouSsNevILwPdfQ",
      "cache-control": "no-cache",
      "channel": "APP",
      "source": "WEB_USER",
      "token-id": "eyJhbGciOiJSUzI1NiIsImtpZCI6ImE0YTEwZGVjZTk4MzY2ZDZmNjNlMTY3Mjg2YWU5YjYxMWQyYmFhMjciLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KTUY0Tb0JYdntyRcvdiWJE4dUeZ02ivBsAw-cYUVA7waBwSpS-pIIWRkDcYsfWa8bt-Hv2bRXRVu8qIGdv5lcolLFhnz0SNLHDE6scwv2q9-VX2542SHAQEVFHjz_f9p_opJzfHQWvClu07QYy2UdXZvGnWoCfz5O-T-vLSKeouHbYXfVaVTRZVOl3CSGw7PM_TcGsRxY1cWF_oGPigcKvppAfI1hl2XAzAv3VzdM6fIvGAS1j18iwUvsQdPjAFYM89okG08K8BzIuptVkyt_sb1gK77zYMX2gdQARkxQc7wv5brf037v2-FkvMPeoLLVJNbufkDAYh8jXM8jj-UWA"
    },
    "referrer": "https://app.gohighlevel.com/",
    "referrerPolicy": "strict-origin-when-cross-origin",
    "body": null,
    "method": "GET",
    "mode": "cors",
    "credentials": "include"
  });

  fetch("https://backend.leadconnectorhq.com/reporting/notification?notificationStatus=not_viewed&companyId=o9a9azEFB9R4uO5BvCAF", {
    "headers": {
      "accept": "application/json, text/plain, */*",
      "accept-language": "en-US,en;q=0.9",
      "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzb3VyY2UiOiJXRUJfVVNFUiIsImNoYW5uZWwiOiJBUFAiLCJzb3VyY2VJZCI6IlVQYkRSeUxWckpoVHhGQ0JScHZkIiwic291cmNlTmFtZSI6IkFsIEFtaW5Jc2xhbSIsImNvbXBhbnlJZCI6Im85YTlhekVGQjlSNHVPNUJ2Q0FGIiwibWV0YSI6eyJ1c2VyUm9sZSI6ImFkbWluIiwidXNlclR5cGUiOiJhZ2VuY3kifSwicHJpbWFyeVVzZXIiOnt9LCJpYXQiOjE3NDk2ODI0MzUsImV4cCI6MTc0OTY4NjAzNSwianRpIjoiNjgzOWY5N2RhYjE0Zjg5ODcyZTI5NWJkIn0.Uzbc5Z5r_z6DulTuXCBpvENwv-LS6F_NlHUpTHJazAw",
      "cache-control": "no-cache",
      "channel": "APP",
      "source": "WEB_USER",
      "token-id": "eyJhbGciOiJSUzI1NiIsImtpZCI6ImE0YTEwZGVjZTk4MzY2ZDZmNjNlMTY3Mjg2YWU5YjYxMWQyYmFhMjciLCJ0eXAiOiJKV1QifQ.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KTUY0Tb0JYdntyRcvdiWJE4dUeZ02ivBsAw-cYUVA7waBwSpS-pIIWRkDcYsfWa8bt-Hv2bRXRVu8qIGdv5lcolLFhnz0SNLHDE6scwv2q9-VX2542SHAQEVFHjz_f9p_opJzfHQWvClu07QYy2UdXZvGnWoCfz5O-T-vLSKeouHbYXfVaVTRZVOl3CSGw7PM_TcGsRxY1cWF_oGPigcKvppAfI1hl2XAzAv3VzdM6fIvGAS1j18iwUvsQdPjAFYM89okG08K8BzIuptVkyt_sb1gK77zYMX2gdQARkxQc7wv5brf037v2-FkvMPeoLLVJNbufkDAYh8jXM8jj-UWA"
    },
    "referrer": "https://app.gohighlevel.com/",
    "referrerPolicy": "strict-origin-when-cross-origin",
    "body": null,
    "method": "GET",
    "mode": "cors",
    "credentials": "include"
  });