<template>
  <div
    class="tab-pane fade show active"
    id="steps-tab"
    role="tabpanel"
    aria-labelledby="steps-tab"
  >
    <div v-if="funnelIsWebsite" class="hl_funnels--website-pages">
      <div class="hl_controls">
        <div class="hl_controls--left">
          <!-- <h1>Website Template Categories</h1> -->
        </div>
        <div class="hl_controls--right">
          <UIButton use="primary" @click="showAddNewStep()">
            <i class="icon icon-plus mr-2"></i>&nbsp;Add New Page
          </UIButton>
        </div>
      </div>
      <div class="row">
        <div
          class="col-xs-12 col-md-6 col-lg-3"
          v-for="step in funnelSteps"
          :key="step.id"
        >
          <WebsitePageCard
            :step="step"
            :funnel-domain="funnelDomain"
            :cloning-web-page="cloningFunnelStep && stepToClone.id === step.id"
            @editWebsitePage="editWebsitePage"
            @cloneWebsitePage="cloneFunnelStep"
            @deleteWebsitePage="deleteWebsitePage"
           :orderFormVersion="funnel.orderFormVersion"
          />
        </div>
      </div>
    </div>
    <div v-else class="hl_funnels--steps">
      <div class="hl_funnels--card row">
        <FunnelStepsList
          class="col-lg-3"
          :steps="funnelSteps"
          :funnel-id="funnelId"
          :location-id="locationId"
          @showAddNewStep="showAddNewStep"
          @deleteFunnelStep="deleteFunnelStep"
          @stepsReordered="stepsReordered"
        />
        <FunnelStepDetails
          class="col-lg-9"
          v-if="currentStepHasPages"
          :funnel-domain="funnelDomain"
          :step-details="currentStep"
          :funnel="funnel"
          :delete-funnel-step="deleteFunnelStep"
          :update-funnel-step="updateFunnelStep"
          :remove-funnel-page-from-step="removeFunnelPageFromStep"
          :add-funnel-page-to-step="addFunnelPageToStep"
          :clone-funnel-step="cloneFunnelStep"
          :cloning-funnel-step="cloningFunnelStep"
          :orderFormVersion="funnel.orderFormVersion"
        />


        <div class="col-lg-9 no-steps-message" v-if="!funnelHasStepsAdded">
          Get started by adding a new step!
        </div>
      </div>
    </div>
    <AddNewStepModal
      :show-modal="addNewStepShown"
      :adding-website-page="funnelIsWebsite"
      @hide="hideNewStepModal"
      @newStepDetails="newStepSubmitted"
    />
    <CloneFunnelStepModal
      :show-modal="cloneStepShown"
      :step-name="stepToClone.name"
      :funnel-is-website="funnelIsWebsite"
      @cloneStep="processCloningStep"
      @hide="hideCloneStepModal"
    />
    <EditFunnelPageModal
      v-if="funnelIsWebsite"
      :show-modal="editWebsitePageModalShown"
      @hide="hideEditWebsitePageModal"
      :page-to-edit="websiteToEdit"
      @updatedPage="updateWebsitePage"
      @deletePage="deleteWebsitePage"
    />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import FunnelStepsList from './FunnelStepsList.vue'
import FunnelStepDetails from './FunnelStepDetails.vue'
import AddNewStepModal from './AddNewStepModal.vue'
import EditFunnelPageModal from './EditFunnelPageModal.vue'
import Funnel, { FunnelStep, FunnelType } from '../../../models/funnel'
import FunnelPage from '../../../models/funnel_page'
import FunnelLookup, { FunnelLookupType } from '../../../models/funnel_lookup'
import Domain from '../../../models/domain'
import uuid from 'uuid'
import {
  cloneControlPage,
  generateFunnelStepUrl,
  generateFunnelPageUrl,
  getValidFunnelLookupUrl,
  getUniqueFunnelUrl,
  createNewFunnelLookup
} from '../../../util/helper'
import config from '@/config'
const CloneFunnelStepModal = () =>
  import('@/pmd/components/funnels/CloneFunnelStepModal.vue')

import WebsitePageCard from '@/pmd/components/funnels/WebsitePageCard.vue'
import {
  updateCachingIndex,
  cacheUpdateEvents,
  fetchDomainsAndPathsForFunnel
} from '../../../util/caching.helper'
import firebase from 'firebase/app'

export default Vue.extend({
  components: {
    FunnelStepsList,
    FunnelStepDetails,
    AddNewStepModal,
    CloneFunnelStepModal,
    WebsitePageCard,
    EditFunnelPageModal
  },
  props: {
    funnel: {
      type: Object
    },
    funnelDomain: {
      type: String
    }
  },
  async mounted() {
    this.$store.dispatch('funnel/FETCH_FUNNEL_STEPS', { id: this.funnelId })

    if (!this.funnelIsWebsite) {
      // visit the first step by default
      const firstStep = this.funnel.getFirstStep()
      const { params } = this.$route
      const stepId = params.step_id || (firstStep && firstStep.id)

      if (stepId) {
        this.openFunnelStep(stepId)
      }
    }

    // const allDomains = await Domain.getAllByLocation(this.locationId)
    // const domain = allDomains.find(d => d.id === this.funnel.domainId)
    // this.funnelDomain = domain ? domain.url : ''
  },
  computed: {
    funnelIsWebsite(): boolean {
      return this.funnel.type === FunnelType.Website
    },
    currentStep(): FunnelStep {
      const currentStepId = this.$route.params.step_id
      const step = this.funnel.steps.find((s: Object) => s.id === currentStepId)
      return step
    },
    currentStepHasPages(): Boolean {
      if (this.currentStep && this.currentStep.pages) {
        return this.currentStep.pages.length > 0
      }
      return false
    },
    funnelSteps(): FunnelStep[] {
      return this.funnel.steps.sort(
        (stepA: FunnelStep, stepB: FunnelStep) =>
          stepA.sequence - stepB.sequence
      )
    },
    funnelHasStepsAdded(): Boolean {
      return this.funnelSteps.length > 0
    }
  },
  data() {
    return {
      locationId: this.$router.currentRoute.params.location_id,
      funnelId: this.$router.currentRoute.params.funnel_id,
      addNewStepShown: false,
      cloningFunnelStep: false,
      cloneStepShown: false,
      stepToClone: {} as FunnelStep,
      editWebsitePageModalShown: false,
      websiteToEdit: {} as FunnelStep | null
    }
  },
  methods: {
    editWebsitePage(stepId: string) {
      console.log('edit website page --> ', stepId)
      const step = this.funnelSteps.find(step => step.id === stepId)
      if (step) {
        this.websiteToEdit = step
        this.editWebsitePageModalShown = true
      }
    },
    async updateWebsitePage(updates: { name: string; url: string }) {
      if (this.websiteToEdit) {
        await this.updateFunnelStep({
          stepId: this.websiteToEdit.id,
          ...updates
        })

        try {
          const step = this.funnel.getStepById(this.websiteToEdit.id)
          const { pages } = step
          // Update the first page
          const pageId = pages[0]
          const funnelPage = await FunnelPage.getById(pageId)
          funnelPage.name = updates.name
          await funnelPage.save()
        } catch (error) {
          console.error('Error while updating page for step --> ', error)
        }

        this.hideEditWebsitePageModal()
      }
    },
    async deleteWebsitePage(step: FunnelStep) {
      await this.deleteFunnelStepConfirmed(step)
    },
    hideEditWebsitePageModal() {
      this.editWebsitePageModalShown = false
    },
    openFunnelStep(stepId: string) {
      this.$router.replace({
        name: 'funnel_steps',
        params: {
          step_id: stepId,
          funnel_id: this.funnelId,
          location_id: this.locationId
        }
      })
    },
    showAddNewStep() {
      this.addNewStepShown = true
    },
    hideNewStepModal() {
      this.addNewStepShown = false
    },
    async stepsReordered(newStepsOrder: Array<String>) {
      newStepsOrder.forEach((stepId, index) => {
        const funnelStep = this.funnel.getStepById(stepId)
        funnelStep.sequence = index + 1
        this.funnel.updateStep(funnelStep)
      })
      const steps = Array.from(this.funnel.steps)
      steps.sort((a, b) => a.sequence - b.sequence)
      this.funnel.steps = steps

      await this.funnel.save()

      const { domains, paths } = await fetchDomainsAndPathsForFunnel(
        this.funnel.id,
        this.locationId
      )
      if (domains && domains.length) {
        await updateCachingIndex({
          index_type: cacheUpdateEvents.FUNNEL_PAGE_REORDERED,
          domains,
          paths,
          event_origin: this.locationId
        })
      }
    },
    async updateFunnelStep({
      stepId,
      name,
      url
    }: {
      stepId: string
      name: string
      url: string
    }) {
      try {
        const funnelStep = this.funnel.getStepById(stepId)
        let oldStepUrl
        if (funnelStep) {
          oldStepUrl = funnelStep.url
          let newStepUrl = url
          if (oldStepUrl !== newStepUrl) {
            newStepUrl = await this.getFunnelStepUrl(name, url, funnelStep.type)
            // TAG: LOOKUPS
            if (this.funnelDomain) {
              await this.updateFunnelStepLookup(
                this.funnelDomain,
                oldStepUrl,
                newStepUrl,
                funnelStep
              )
            }
          }

          funnelStep.name = name
          funnelStep.url = newStepUrl
          this.funnel.updateStep(funnelStep)
          await this.updateFunnelPageName(funnelStep)
          await this.funnel.save()
          await updateCachingIndex({
            index_type: cacheUpdateEvents.STEP_PATH_UPDATE,
            domains: [
              this.funnelDomain,
              ...funnelStep.additional_routes?.map(r => r.domain),
              ...funnelStep.additional_routes?.map(r => `split.testing.${r.domain}`)
            ],
            event_origin: this.funnel.locationId
          })

          if (!this.funnelIsWebsite) {
            setTimeout(() => {
              this.openFunnelStep(stepId)
            }, 250)
          }
        }
      } catch (error) {
        console.error(error)
        alert('Oops, looks like we have an issue while updating step details!')
        const funnelStep = this.funnel.getStepById(stepId)
        // just reset funnel step with old data
        if (funnelStep) {
          this.funnel.updateStep(funnelStep)
          await this.funnel.save()
        }
      }
    },
    async updateFunnelPageName(funnelStep: FunnelStep) {
      try {
        const { pages } = funnelStep
        if (pages && pages.length) {
          const funnelPage = await FunnelPage.getById(pages[0])
          funnelPage.name = funnelStep.name
          await funnelPage.save()
        }
      } catch (error) {
        console.error(error)
      }
    },
    deleteFunnelStep(step: FunnelStep) {
      this.deleteFunnelStepConfirmed(step)
    },
    async deleteFunnelStepConfirmed(step: FunnelStep) {
      try {
        await this.$http.post('/funnelbuilder/delete-funnel-step', {
          funnelId: this.funnelId,
          stepId: step.id
        })
        this.funnel.removeStep(step.id)
        // // Open the first step
        // const firstStep = this.funnel.getFirstStep()
        // if (firstStep) {
        //   this.openFunnelStep(firstStep.id)
        // }
      } catch (error) {
        console.error('Error while deleting step --> ', error)
      }
    },
    async newStepSubmitted(stepDetails: Object) {
      if (stepDetails.html) {
        const fileName = uuid() + '.html'
        let filePath = 'location/' + this.locationId + '/temp/' + fileName

        let uploadPath = firebase.storage().ref(filePath)
        const snapshot = await uploadPath.putString(stepDetails.html)
        stepDetails.cfDownloadUrl = await snapshot.ref.getDownloadURL()
      }
      await this.createNewFunnelStep(stepDetails)
      this.hideNewStepModal()
    },
    async createNewFunnelStep({
      name,
      url,
      cfUrl,
      type,
      cfDownloadUrl
    }: {
      name: string
      url: string
      cfUrl: string
      type: string
      cfDownloadUrl: string
    }) {
      // Create a funnel page for the step
      const stepId = uuid()

      const stepUrl = await this.getFunnelStepUrl(name, url, type)

      // Next create new funnel step
      const newFunnelStep: FunnelStep = {
        id: stepId,
        name,
        pages: [],
        sequence: this.funnel.noOfSteps() + 1,
        url: stepUrl,
        type
      }

      await this.addNewFunnelStepLookup(newFunnelStep)

      // Add step to funnel
      this.funnel.addNewStep(newFunnelStep)

      // TAG: LOOKUPS
      const pageUrl = `${stepUrl}-page`
      const newFunnelPage = await this.createNewFunnelPage(
        name,
        pageUrl,
        type,
        stepId,
        this.funnelId,
        this.locationId,
        cfUrl,
        cfDownloadUrl
      )

      if (newFunnelPage) {
        await this.addFunnelPageToStep({ page: newFunnelPage, stepId })

        if (cfUrl) {
          if (config.mode === 'dev') {
            url = 'http://localhost:3333'
          } else if (config.mode === 'staging') {
            url = 'https://staging.gohighlevel.com'
          } else {
            url = `https://${window.location.hostname}`
          }
          window.location.href = `${url}/v2/builder/${newFunnelPage.id}`
        } else if (!this.funnelIsWebsite) {
          this.$router.replace({
            name: 'funnel_steps',
            params: { step_id: newFunnelStep.id }
          })
        }
      }
    },
    async createNewFunnelPage(
      name: string,
      url: string,
      type: string,
      stepId: string,
      funnelId: string,
      locationId: string,
      cfUrl?: string,
      cfDownloadUrl?: string
    ): Promise<FunnelPage | undefined> {
      // create new funnel step page and add property
      const newFunnelStepPage = new FunnelPage()
      newFunnelStepPage.name = name
      newFunnelStepPage.url = await this.getFunnelPageUrl(name, url, type)
      newFunnelStepPage.locationId = locationId
      newFunnelStepPage.funnelId = funnelId
      newFunnelStepPage.stepId = stepId
      newFunnelStepPage.templateType = type

      // save and open the funnel step
      await newFunnelStepPage.save()
      // this.addNewFunnelPageLookup(newFunnelStepPage)

      if (cfUrl) {
        try {
          await this.$http.get('/clone-funnel', {
            params: {
              url: cfUrl,
              downloadUrl: cfDownloadUrl,
              pageId: newFunnelStepPage.id
            }
          })

          await this.deleteCfHtml(cfDownloadUrl)
        } catch (error) {
          if (error.response && error.response.status === 422) {
            alert('Please enter the valid url and try again')
          } else {
            alert(
              'Something went wrong while importing the funnel, please try again.'
            )
          }
          await newFunnelStepPage.ref.delete()
          console.error(error)
          return
        }
      }

      return newFunnelStepPage
    },
    async deleteCfHtml(url) {
      const path = firebase.storage().refFromURL(url)
      await path.delete()
    },
    addNewFunnelPageLookup(funnelPage: FunnelPage) {
      // TAG: LOOKUPS
      if (!this.funnelDomain) return

      const newLookupPayload = {
        type: FunnelLookupType.Page,
        typeId: funnelPage.id,
        path: getValidFunnelLookupUrl(funnelPage.url),
        funnelId: this.funnelId,
        locationId: this.locationId,
        domain: this.funnelDomain
      }

      createNewFunnelLookup(newLookupPayload)
    },
    addNewFunnelStepLookup(funnelStep: FunnelStep) {
      // TAG: LOOKUPS
      if (!this.funnelDomain) return

      const newLookupPayload = {
        type: FunnelLookupType.Step,
        typeId: funnelStep.id,
        path: getValidFunnelLookupUrl(funnelStep.url),
        funnelId: this.funnelId,
        locationId: this.locationId,
        domain: this.funnelDomain
      }

      createNewFunnelLookup(newLookupPayload)
    },
    async updateFunnelStepLookup(
      domain: string,
      oldUrl: string,
      newUrl: string,
      funnelStep: FunnelStep
    ) {
      // TAG: LOOKUPS
      try {
        const lookup = await FunnelLookup.getByDomainAndPath(
          domain,
          oldUrl,
          this.locationId
        )
        lookup.path = getValidFunnelLookupUrl(newUrl)
        await lookup.save()
      } catch (e) {
        console.error(e);
        if(e.code === 404) {
          const lookups = await FunnelLookup.getAllByFunnel(this.funnelId, this.locationId)
          let lookup = lookups.find(x => x.typeId === funnelStep.id)
          if(!lookup){
            lookup = new FunnelLookup()
            lookup.domain = domain;
            lookup.locationId = this.funnel.locationId;
            lookup.funnelId = this.funnelId;
            lookup.type = FunnelLookupType.Step;
            lookup.typeId = funnelStep.id;
          }
          lookup.path = newUrl;
          await lookup.save();
        }
      }
    },
    // async deleteFunnelStepLookup(funnelStep: FunnelStep) {
    //   try {
    //     const lookup = await FunnelLookup.getByTypeId(funnelStep.id)
    //     lookup.delete()
    //     // lookup.deleted = true
    //     // lookup.save()
    //   } catch (error) {
    //     console.error('Error while deleting funnel step lookup --> ', { error })
    //   }
    // },
    // async deleteFunnelPageLookup(funnelPage: FunnelPage) {
    //   try {
    //     const lookup = await FunnelLookup.getByTypeId(funnelPage.id)
    //     lookup.delete()
    //     // lookup.deleted = true
    //     // lookup.save()
    //   } catch (error) {
    //     console.error('Error while deleting funnel page lookup --> ', { error })
    //   }
    // },
    async removeFunnelPageFromStep({
      pageId,
      stepId
    }: {
      pageId: string
      stepId: string
    }) {
      const step: FunnelStep = this.funnel.getStepById(stepId)
      const { pages } = step
      if (pages && pages.length) {
        const pagePos = pages.findIndex(p => p === pageId)
        const newPages = [
          ...pages.slice(0, pagePos),
          ...pages.slice(pagePos + 1)
        ]
        step.pages = newPages
        this.funnel.updateStep(step)
        await this.funnel.save()
      }
    },
    hideCloneStepModal() {
      this.cloneStepShown = false
      // this.stepToClone = {} as FunnelStep
    },
    cloneFunnelStep(stepId: string) {
      const step = this.funnelSteps.find(step => step.id === stepId)
      if (step) {
        this.stepToClone = step
        this.cloneStepShown = true
      } else {
        console.error('Error while finding step to clone --> ', stepId)
      }

      // console.log('clone step and pages --> ', step, pages)
      // const stepId = uuid()
      // const { name: stepName, url: stepUrl, type: stepType } = step
      // const newStepName = `${stepName} (Clone)`
      // const newStepUrl = await this.getFunnelStepUrl(newStepName, stepUrl, stepType)

      // // Next create new funnel step
      // const newFunnelStep: FunnelStep = {
      //   id: stepId,
      //   name: newStepName,
      //   pages: [],
      //   sequence: this.funnel.noOfSteps() + 1,
      //   url: newStepUrl,
      //   type: stepType
      // }

      // await this.addNewFunnelStepLookup(newFunnelStep);

      // // Add step to funnel
      // this.funnel.addNewStep(newFunnelStep);

      // const cloningPages = pages.map((page) => {
      //   page.stepId = stepId
      //   page.url = `${page.url}-clone`
      //   const clonePromise = cloneFunnelPage(page)
      //   return clonePromise
      // })

      // Promise.all((cloningPages))
      // .then((clonedPages) => {
      //   // console.log('cloned funnel pages')
      //   clonedPages.forEach((page) => {
      //     this.addFunnelPageToStep({ page, stepId })
      //   })

      //   this.$router.replace({
      //     name: 'funnel_steps',
      //     params: { step_id: newFunnelStep.id }
      //   })
      // })
    },
    async processCloningStep(params: any) {
      let cloneStepToFunnels = []
      this.cloneStepShown = false
      let locationOfFunnel = this.$router.currentRoute.params.location_id

      if (!params) {
        cloneStepToFunnels = [this.funnelId]
      } else {
        const { funnelIds, locationId } = params
        cloneStepToFunnels = funnelIds
        locationOfFunnel = locationId
      }

      try {
        this.cloningFunnelStep = true
        const { data } = await this.$http.post(
          '/funnelbuilder/clone-funnel-step',
          {
            stepId: this.stepToClone.id,
            funnelId: this.funnelId,
            funnels: cloneStepToFunnels,
            locationId: locationOfFunnel
          }
        )
        const { newStepId } = data
        if (newStepId) {
          this.$router.replace({
            name: 'funnel_steps',
            params: { step_id: newStepId }
          })
        }
      } catch (error) {
        alert('Something went wrong while cloning step!')
        console.error('error while cloning step --> ', error)
      } finally {
        this.cloningFunnelStep = false
      }
    },
    async addFunnelPageToStep({
      page,
      stepId
    }: {
      page: FunnelPage
      stepId: string
    }) {
      const step: FunnelStep = this.funnel.getStepById(stepId)
      const pageId = page.id
      const { pages } = step
      let newPages
      if (pages) {
        newPages = [...pages, pageId]
      } else {
        newPages = [pageId]
      }

      step.pages = newPages
      this.funnel.updateStep(step)
      await this.addNewFunnelPageLookup(page)
      await this.funnel.save()
    },
    async getFunnelStepUrl(
      name: string,
      url: string,
      type: string
    ): Promise<string> {
      const funnelStepUrl = generateFunnelStepUrl(name, url, type)
      // TAG: LOOKUPS
      if (this.funnelDomain) {
        const uniqueFunnelStepUrl = await getUniqueFunnelUrl(
          this.funnelDomain,
          funnelStepUrl
        )
        return uniqueFunnelStepUrl
      }
      return funnelStepUrl
    },
    async getFunnelPageUrl(
      name: string,
      url: string,
      type: string
    ): Promise<string> {
      const funnelPageUrl = generateFunnelPageUrl(name, url, type)
      // TAG: LOOKUPS
      if (this.funnelDomain) {
        const uniqueFunnelPageUrl = await getUniqueFunnelUrl(
          this.funnelDomain,
          funnelPageUrl
        )
        return uniqueFunnelPageUrl
      }
      return funnelPageUrl
    }
  },
  watch: {
    $route: function(to, from) {
      const {
        name: oldRouteName,
        params: { step_id: oldStepId, location_id: oldLocationId }
      } = from
      const {
        name: newRouteName,
        params: {
          step_id: newStepId,
          funnel_id: newFunnelId,
          location_id: newLocationId
        }
      } = to

      // If location id is changed don't load funnel step overview
      if (oldLocationId !== newLocationId) return

      // Redirect funnel_steps to funnel_step_overview or when stepId changes
      this.cloningFunnelStep = false
      if (newRouteName === 'funnel_steps') {
        this.$router.replace({
          name: 'funnel_step_overview',
          params: {
            step_id: newStepId,
            funnel_id: newFunnelId,
            location_id: newLocationId
          }
        })
      }

      if (newRouteName === 'funnel-steps-v2') {
        this.$router.replace({
          name: 'funnel-step-overview-v2',
          params: {
            step_id: newStepId,
            funnel_id: newFunnelId,
            location_id: newLocationId
          }
        })
      }

      // Condition when user clicks on back button from step overview
      // if (newRouteName === 'funnel_steps' && oldRouteName === 'funnel_step_overview' && newStepId === oldStepId) {
      //   this.$router.push({
      //     name: 'all_funnels',
      //     params: {
      //       location_id: newLocationId
      //     }
      //   })
      // }
    }
  }
})
</script>
<style scoped>
.no-steps-message {
  font-size: 36px;
  margin-top: 36px;
  text-align: center;
}
</style>
