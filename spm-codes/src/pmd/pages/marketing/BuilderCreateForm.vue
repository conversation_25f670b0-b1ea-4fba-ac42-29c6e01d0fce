<template>
  <section class="hl_wrapper d-flex">
    <section class="hl_wrapper--inner form-builder" id="form-builder">
      <moon-loader class="mt-5" color="#188bf6" size="20px" v-if="loading" />
      <section v-if="!loading" class="hl_form-builder--main">
        <form name="builder-form" id="_builder-form">
          <h5
            class="builder-form-name text-center"
            v-if="formAction.formName != ''"
          >
            {{ formAction.formName }}
          </h5>
          <div
            v-bind:class="
              dragOptions.isDragEntered
                ? 'drag-enter form-builder--wrap'
                : 'form-builder--wrap'
            "
            id="the-form-builder"
            v-bind:style="{
              backgroundColor: '#' + formStyle.bgColor,
              //color: '#' + formStyle.color,
              border: formStyle.border + 'px dashed #cde0ec',
              borderRadius: formStyle.radius + 'px',
              maxWidth: formStyle.width + 'px',
            }"
            ref="formBuilderReferance"
          >
            <drop
              v-bind:class="
                !formStyle.inlineForm
                  ? 'drop form-builder-drop builder-padding-remove'
                  : 'drop form-builder-drop builder-inline'
              "
              @drop="handleDrop"
            >
              <Container
                @drop="onDrop"
                :remove-on-drop-out="true"
                v-bind:class="
                  formStyle.inlineForm
                    ? 'smooth-dnd-container vertical row'
                    : 'smooth-dnd-container vertical'
                "
              >
                <Draggable
                  v-for="(item, index) in ItemDropable"
                  :key="item.tag + '_' + index"
                  v-bind:class="
                    formStyle.inlineForm &&
                    !fieldFullWidth(item.type) &&
                    !checkFieldOwnLine(item.tag)
                      ? 'menu-field-wrap col-md-6 col-sm-6 col-lg-6'
                      : 'menu-field-wrap col-12'
                  "
                >
                  <div
                    v-bind:class="item.active ? 'active' : ''"
                    @click="setFieldActive(index)"
                  >
                    <!--Standard fields-->
                    <FormbuilderAddStandardFields
                      v-if="item.id == undefined"
                      :propImageFileInStore="isImageFileInStore"
                      :currentLocationId="currentLocationId"
                      :builderId="builderId"
                      :item="item"
                      :index="index"
                      :showDropZones="showDropZones"
                      :formLabelVisible="formStyle.formLabelVisible"
                      :formButtonStyle="buttonStyle"
                      v-on:saveUploadPath="saveUploadPath"
                      :class="index % 2 == 0 ? 'f-even' : 'f-odd'"
                    />
                    <!--Standrard Fields-->

                    <!--Custom Form Start-->
                    <FormbuilderAddCustomFields
                      v-if="item.id != undefined"
                      :customFields="item"
                      :index="index"
                      :showDropZones="showDropZones"
                      :formLabelVisible="formStyle.formLabelVisible"
                      :class="index % 2 == 0 ? 'f-even' : 'f-odd'"
                    />
                    <!--Custom Form End-->
                  </div>
                </Draggable>

                <!--Blank dropzone (will be added at the bottom)-->
                <div class="blank-dropzone">
                  <add-field-dropzone :index="-1"></add-field-dropzone>
                </div>
              </Container>
            </drop>

            <!--Branding text-->
            <FormbuilderBranding
              :isBrandingActive="formStyle.isBrandingActive"
              :company="company"
            />
            <!--End of Branding -->
          </div>
        </form>
      </section>

      <!-- END of .hl_form-builder--main -->
      <FormbuilderSidebar
        ref="fieldSettingsRefs"
        :isSurvey="false"
        :itemDragable="itemDragable"
        :customFields="customFields"
        :fieldSettings="fieldSettings"
        :buttonStyle="buttonStyle"
        :formStyle="formStyle"
        :formAction="formAction"
        v-on:startDraggingItem="startDraggingItem"
        v-on:dragEndItem="dragEndItem"
        v-on:showAddCustomFieldModal="showAddCustomFieldModal"
        v-on:showIntegratePage="showIntegratePage"
        v-on:saveForm="saveFormChanges"
      />
      <!-- END of .hl_form-builder--sidebar -->
    </section>
    <!-- END of .form-builder -->
    <AddCustomFieldModal
      v-if="drawComponent"
      :values="addCustomFieldModal"
      @hidden="addCustomFieldModal.visible"
      v-on:hide="closeAddCustomFieldModal"
    />

    <AddCustomHtml
      v-if="drawComponentCustomHtml"
      :values="customHtmlModalVisible"
      @hidden="customHtmlModalVisible.visible"
      v-on:hide="closeCustomHtmlModal"
    />

    <b-modal
      centered
      size="sm"
      id="form-embed-modal"
      hide-footer
      title="Integrate Form"
    >
      <IntegrateFormModal />
    </b-modal>
  </section>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import { Drag, Drop } from 'vue-drag-drop'
import { Container, Draggable } from 'vue-smooth-dnd'

/*---Component---*/
import TopBar from '../../components/TopBar.vue'
import SideBar from '../../components/SideBar.vue'
const AddFieldDropzone = () =>
  import('../../components/marketing/AddFieldDropzone.vue').then(m => m.default)
const AddCustomFieldModal = () =>
  import('../../components/agency/AddCustomFieldModal.vue').then(m => m.default)
const FormbuilderAddCustomFields = () =>
  import('../../components/marketing/FormbuilderAddCustomFields.vue').then(
    m => m.default
  )
const FormbuilderAddStandardFields = () =>
  import('../../components/marketing/FormbuilderAddStandardFields.vue').then(
    m => m.default
  )
const FormbuilderBranding = () =>
  import('../../components/marketing/FormbuilderBranding.vue')
const FormbuilderSidebar = () =>
  import('../../components/marketing/FormbuilderSidebar.vue')
const AddCustomHtml = () =>
  import('../../components/marketing/AddCustomHtml.vue').then(m => m.default)
const IntegrateFormModal = () =>
  import('../../components/marketing/IntegrateFormModal.vue')

/*---Model---*/
import { CompanyState } from '../../../store/state_models'
import { Company, Formbuilder, CustomField } from '../../../models'

/*--Firebase--*/
import firebase from 'firebase/app'
import {
  updateCachingIndex,
  cacheUpdateEvents,
} from '../../../util/caching.helper'

declare var $: any

let unsubscribeCustomFields: () => void
const fullWidthFields = [
  'img',
  'submit',
  'h1',
  'html',
  'captcha',
  'large_text',
  'source',
]

export default Vue.extend({
  components: {
    TopBar,
    SideBar,
    Drag,
    Drop,
    Container,
    Draggable,
    'add-field-dropzone': AddFieldDropzone,
    AddCustomFieldModal,
    FormbuilderAddCustomFields,
    FormbuilderAddStandardFields,
    FormbuilderBranding,
    FormbuilderSidebar,
    AddCustomHtml,
    IntegrateFormModal,
  },
  data() {
    return {
      publishedFormData: {},
      loading: true,
      itemDragable: [
        {
          label: 'Full Name',
          tag: 'full_name',
          type: 'text',
          placeholder: 'Full Name',
          required: false,
        },
        {
          label: 'First Name',
          tag: 'first_name',
          type: 'text',
          placeholder: 'First Name',
          required: false,
        },
        {
          label: 'Last Name',
          tag: 'last_name',
          type: 'text',
          placeholder: 'Last Name',
          required: false,
        },
        {
          label: 'Phone',
          tag: 'phone',
          type: 'text',
          placeholder: 'Phone',
          required: true,
        },
        {
          label: 'Email',
          tag: 'email',
          type: 'email',
          placeholder: 'Email',
          required: true,
        },
        {
          label: 'Organization',
          tag: 'organization',
          type: 'text',
          placeholder: 'Organization',
          required: false,
        },
        {
          label: 'Address',
          tag: 'address',
          type: 'text',
          placeholder: 'Address',
          required: false,
        },
        {
          label: 'City',
          tag: 'city',
          type: 'text',
          placeholder: 'City',
          required: false,
        },
        {
          label: 'State',
          tag: 'state',
          type: 'text',
          placeholder: 'State',
          required: false,
        },
        {
          label: 'Country',
          tag: 'country',
          type: 'select',
          placeholder: 'Country',
          required: false,
        },
        {
          label: 'Postal code',
          tag: 'postal_code',
          type: 'text',
          placeholder: 'Postal Code',
          required: false,
        },
        {
          label: 'Text',
          tag: 'header',
          type: 'h1',
          placeholder: 'header',
        },
        {
          label: 'Html',
          tag: 'html',
          type: 'html',
          placeholder: 'The Custom HTML goes here',
          html: '',
        },

        {
          label: 'Image',
          tag: 'image',
          type: 'img',
          placeholder: 'Image',
        },
        {
          label: 'Website',
          tag: 'website',
          type: 'text',
          placeholder: 'Web URL goes here',
        },
        {
          label: 'Captcha',
          tag: 'captcha',
          type: 'captcha',
          placeholder: 'Captcha',
        },
        {
          label: 'Source',
          tag: 'source',
          type: 'source',
          value: '',
        },
        {
          label: 'Date of birth',
          tag: 'date_of_birth',
          type: 'date',
          placeholder: 'Date of birth',
        },

        {
          label: 'Button',
          tag: 'button',
          type: 'submit',
          placeholder: 'Button',
        },
      ],

      ItemDropable: [] as any,
      ItemDropableCustomField: [] as any,

      attrName: '',

      formStyle: {
        bgColor: 'FFFFFF',
        color: '000000',
        border: 1,
        radius: 4,
        width: 550,
        showBgColorPicker: false,
        showColorPicker: false,
        inlineForm: false,
        formLabelVisible: true,
        isBrandingActive: false,
        emebedHtml: '',
        formLabelColor: '',
      },

      buttonStyle: {
        bgColor: '2A3135',
        color: 'FFFFFF',
        border: 0,
        radius: 0.3125,
        padding: 11,
      },
      dragOptions: {
        isDragEntered: false,
      },
      styleNode: {},
      styleNodeFormColor: {},
      showDropZones: false,
      fieldPushIndex: -1,
      //Form data
      formbuilder: {} as Formbuilder,
      currentLocationId: '',
      builderId: '',
      formBuilderCount: 0 as number,
      customFields: [] as CustomField[],
      //Custom fields
      drawComponent: false as boolean,
      addCustomFieldModal: { visible: false } as { [key: string]: any },

      //File upload
      isImageFileInStore: false,
      formAction: {
        redirectURL: '',
        thankyouText: '',
        actionType: 1,
        formName: '',
        fbPixelId: '',
        pageViewEvent: 'PageView',
        formSubmissionEvent: 'SubmitApplication',
        stickyContact: false,
      },
      fieldSettings: {
        enable: false,
        fieldInfo: {},
      },
      selectedFieldIndex: 0,
      //Custom html modal
      drawComponentCustomHtml: false as boolen,
      customHtmlModalVisible: { visible: false } as { [key: string]: any },
    }
  },
  methods: {
    async initialData() {
      this.formBuilderCount = await Formbuilder.getCountByLocationId(
        this.currentLocationId
      )

      if (!this.builderId) {
        //this.saveForm();
        this.$router.push({
          name: 'form_builder',
          params: this.$route.params,
        })
        this.loading = false
      } else {
        this.formbuilder = await Formbuilder.getById(this.builderId)
        // Probably has wrong ID in url, redirect it workflows page
        if (!this.formbuilder) {
          this.$router.push({
            name: 'form_builder',
            params: this.$route.params,
          })
        }
        //Load form from json
        this.formLoadFromJson()
      }
    },
    async saveFormChanges() {
      let tempObj = JSON.parse(
        JSON.stringify({
          ...this.formbuilder.formData,
          ...this.formAction,
        })
      )
      tempObj.form.fields.forEach((field, ind) => {
        delete tempObj.form.fields[ind].active
      })

      this.publishedFormData = JSON.parse(JSON.stringify(tempObj))
      await this.formbuilder.save()
      await updateCachingIndex({
        index_type: cacheUpdateEvents.FORM_UPDATE,
        event_origin: this.currentLocationId,
      })
      this.$bus.$emit('form-has-unsaved-data', false)
    },
    async fetchCustomFields() {
      if (unsubscribeCustomFields) unsubscribeCustomFields()

      unsubscribeCustomFields = CustomField.getByLocationId(
        this.currentLocationId
      ).onSnapshot(snapshot => {
        this.customFields = snapshot.docs.map(d => new CustomField(d))
        //console.log("customField",this.customFields);
      })
    },
    isFieldExist(itemAttribName: any, attrKey: any) {
      const exceptStore = ['header', 'image', 'html']
      for (var i = 0; i < this.ItemDropable.length; i++) {
        if (attrKey in this.ItemDropable[i]) {
          var itemTag = this.ItemDropable[i][attrKey]
          if (
            itemTag == itemAttribName &&
            exceptStore.indexOf(itemAttribName) < 0
          ) {
            return { status: true, index: i }
          }
        }
      }
      return { status: false }
    },
    camelize(str: string) {
      return str.replace(/\W+(.)/g, function (match, chr) {
        return chr.toUpperCase()
      })
    },
    handleStandredFields(data: any) {
      this.attrName = data.item.tag
      var el = this

      //Check if item Exist then return
      var isFieldExist = this.isFieldExist(this.attrName, 'tag')
      if (isFieldExist.status) {
        this.fieldPushIndex = -1
        return false
      }

      var createDOM: any = data.item
      if (this.fieldPushIndex < 0) {
        this.ItemDropable.push(createDOM)
      } else {
        this.ItemDropable.splice(this.fieldPushIndex, 0, createDOM)
      }
      return true
    },
    handleCustomFields(data: any) {
      //Check if item Exist then return
      var isFieldExist = this.isFieldExist(data.item.id, 'id')
      if (isFieldExist.status) {
        this.fieldPushIndex = -1
        return false
      }

      //Common fields for both standrard and custom
      var createDOM: any = {
        id: data.item.id,
        name: data.item.name,
        type: data.item.dataType.toLowerCase(),
        required: false,
        label: data.item.name,
        placeholder: data.item.placeholder
          ? data.item.placeholder
          : data.item.name,
        tag: data.item.id,
      }

      //Adding more fields on the basis of field type -> Accept camelcase only
      for (var key in data.item.data) {
        var camelkey = this.camelize(key.replace(/_/g, ' '))
        createDOM[camelkey] = data.item.data[key]
      }

      //Insert to itemDrop array
      if (this.fieldPushIndex < 0) {
        this.ItemDropable.push(createDOM)
      } else {
        this.ItemDropable.splice(this.fieldPushIndex, 0, createDOM)
      }
      return true
    },
    handleDrop(data: any, event: any) {
      data = lodash.cloneDeep(data)
      var isFieldAllowedToAdd = false
      if (data.item.id) {
        console.log('Droped custom fields')
        isFieldAllowedToAdd = this.handleCustomFields(data)
      } else {
        console.log('Drop Standered fields')
        isFieldAllowedToAdd = this.handleStandredFields(data)
      }

      if (isFieldAllowedToAdd) {
        this.dragOptions.isDragEntered = false
        this.fieldPushIndex = -1 //reset field index
        this.saveForm()
      }
    },
    removeField(tag: string) {
      //Check if item Exist then return
      // var isFieldExist: any = this.isFieldExist(tag.id, 'tag');
      //var index: number = isFieldExist.index;
      const index: number = this.selectedFieldIndex
      this.ItemDropable.splice(index, 1)
      this.saveForm()
    },
    removeCustomField(field: any) {
      console.log('custom data', field.id)
      //Check if item Exist then return
      var isFieldExist: any = this.isFieldExist(field.id, 'id')
      var index: number = isFieldExist.index
      this.ItemDropable.splice(index, 1)
      this.saveForm()
    },
    applyDrag(arr: any, dragResult: any) {
      const { removedIndex, addedIndex, payload } = dragResult
      if (removedIndex === null && addedIndex === null) return arr

      const result = [...arr]
      let itemToAdd = payload

      if (removedIndex !== null) {
        itemToAdd = result.splice(removedIndex, 1)[0]
      }

      if (addedIndex !== null) {
        result.splice(addedIndex, 0, itemToAdd)
      }

      return result
    },
    onDrop(dropResult: any) {
      console.log('droped', dropResult)
      this.ItemDropable = this.applyDrag(this.ItemDropable, dropResult)
      this.saveForm()
    },

    startDraggingItem() {
      this.showDropZones = true
    },
    dragEndItem() {
      this.showDropZones = false
    },
    addFieldAfter(emitData: any) {
      this.fieldPushIndex = emitData.data
    },

    formToJson() {
      let fields: any = []
      for (let i = 0; i < this.ItemDropable.length; i++) {
        let field: any = {}
        if ('id' in this.ItemDropable[i]) {
          field = this.ItemDropable[i]
        } else if (
          'url' in this.ItemDropable[i] &&
          this.ItemDropable[i].tag == 'image'
        ) {
          field = this.ItemDropable[i]
        } else {
          field = this.ItemDropable[i]
        }
        field.hiddenFieldQueryKey = field.hasOwnProperty('allowCustomOption')
          ? field.hiddenFieldQueryKey ||
            this.getConstructedQueryKey(field.label)
          : field.hiddenFieldQueryKey || field.tag

        fields.push(field)
      }
      let formJsonObj = {
        form: {
          fbPixelId: this.formAction.fbPixelId,
          pageViewEvent: this.formAction.pageViewEvent,
          formSubmissionEvent: this.formAction.formSubmissionEvent,
          layout: this.formStyle.inlineForm,
          width: this.formStyle.width,
          formLabelVisible: this.formStyle.formLabelVisible,
          stickyContact: this.formAction.stickyContact,
          style: {
            ac_branding: this.formStyle.isBrandingActive,
            background: this.formStyle.bgColor,
            color: this.formStyle.color,
            border: {
              border: this.formStyle.border,
              radius: this.formStyle.radius,
              style: 'dashed',
              color: 'CDE0EC',
            },
          },
          button: {
            background: this.buttonStyle.bgColor,
            color: this.buttonStyle.color,
            border: {
              border: this.buttonStyle.border,
              radius: this.buttonStyle.radius,
              padding: this.buttonStyle.padding,
            },
          },
          customStyle: this.formStyle.emebedHtml,
          fields: fields,
          company: {
            logoURL: this.company.logoURL ? this.company.logoURL : '',
            domain: this.company.domain ? this.company.domain : '',
            name: this.company.name ? this.company.name : '',
          },
          formAction: {
            redirect_url: this.formAction.redirectURL,
            thankyouText: this.formAction.thankyouText,
            actionType: this.formAction.actionType,
          },
        },
      }
      return formJsonObj
    },
    async saveForm() {
      let jsonObj = this.formToJson()
      this.formbuilder.name = this.formAction.formName

      //Block for Insert
      if (!this.formbuilder) {
        console.log('SaveForm - If block Executed')
        this.formbuilder = new Formbuilder()
        this.formbuilder.name = 'Form ' + (this.formBuilderCount + 1)
      } else {
        console.log('SaveForm - Else block Executed')
      }

      this.formbuilder.locationId = this.currentLocationId
      this.formbuilder.formData = jsonObj

      let tempObj = JSON.parse(
        JSON.stringify({
          ...this.formbuilder.formData,
          ...this.formAction,
        })
      )

      tempObj.form.fields.forEach((field, ind) => {
        delete tempObj.form.fields[ind].active
      })

      if (!lodash.isEqual(this.publishedFormData, tempObj)) {
        this.$bus.$emit('form-has-unsaved-data', true)
      } else {
        this.$bus.$emit('form-has-unsaved-data', false)
      }

      // await this.formbuilder.save();
      console.log('Entry Saved')
    },
    formLoadFromJson() {
      const form = this.formbuilder.formData.form

      const el = this
      this.formAction.formName = this.formbuilder.data.name
      this.selectedFieldIndex = 0

      //If form is empty for first time
      if (form == undefined) {
        this.loading = false
        return true
      }

      form.fields.map(function (value: any, key: any) {
        if ('url' in value && value.tag == 'image') {
          el.isImageFileInStore = true
        }
        value.active = false
        el.ItemDropable.push(value)
      })

      if ('style' in form) {
        this.formStyle.bgColor = form.style.background //BG color
        this.formStyle.color = form.style.color //Font color
        this.formStyle.border = form.style.border.border //Border
        this.formStyle.radius = form.style.border.radius //Corner Radius
        this.formStyle.isBrandingActive = form.style.ac_branding //AC Branding
      }

      if ('button' in form) {
        this.buttonStyle.bgColor = form.button.background // BG color
        this.buttonStyle.color = form.button.color //Font Color
        this.buttonStyle.border = form.button.border.border //Button Border
        this.buttonStyle.radius = form.button.border.radius //Button Radius
        this.buttonStyle.padding = form.button.border.padding //Button Padding
      }
      if ('formAction' in form) {
        this.formAction.redirectURL = form.formAction.redirect_url
        this.formAction.thankyouText = form.formAction.thankyouText
        this.formAction.actionType = form.formAction.actionType
      } else if ('redirect_url' in form) {
        this.formAction.redirectURL = form.redirect_url
        this.formAction.thankyouText = ''
        this.formAction.actionType = 1
      }

      this.formStyle.width = form.width ? form.width : 550 //Width
      this.formStyle.inlineForm = form.layout ? form.layout : false //Form Layout
      this.formAction.fbPixelId = form.fbPixelId ? form.fbPixelId : ''
      this.formAction.pageViewEvent = form.pageViewEvent
        ? form.pageViewEvent
        : ''
      this.formAction.formSubmissionEvent = form.formSubmissionEvent
        ? form.formSubmissionEvent
        : ''
      this.formAction.stickyContact = form.stickyContact
        ? form.stickyContact
        : false
      if ('customStyle' in form && form.customStyle.trim('').length > 0) {
        this.formStyle.emebedHtml = form.customStyle
      }

      if (this.formStyle.color) {
        this.formStyle.formLabelColor =
          'label{color:#' + this.formStyle.color + '!important;}'
      }
      this.loading = false

      if ('formLabelVisible' in form) {
        this.formStyle.formLabelVisible = form.formLabelVisible
      }
      this.loading = false

      if (this.formbuilder || this.formAction) {
        let tempObj = JSON.parse(
          JSON.stringify({
            ...this.formbuilder.formData,
            ...this.formAction,
          })
        )

        tempObj.form.fields.forEach((field, ind) => {
          delete tempObj.form.fields[ind].active
        })
        this.publishedFormData = JSON.parse(JSON.stringify(tempObj))
      }
    },
    showAddCustomFieldModal() {
      this.reset()
      this.addCustomFieldModal = {
        visible: true,
        location_id: this.currentLocationId,
      }
    },
    reset() {
      var vm = this
      vm.drawComponent = false
      Vue.nextTick(function () {
        vm.drawComponent = true
      })
    },
    closeAddCustomFieldModal() {
      this.addCustomFieldModal = {
        visible: false,
      }
    },
    async showIntegratePage() {
      this.$bvModal.show('form-embed-modal')
      await this.saveFormChanges()
    },
    redirectToListPage() {
      this.$router.push({
        name: 'form_builder',
        params: {
          location_id: this.currentLocationId,
        },
      })
    },
    setFieldActive(index: number) {
      this.selectedFieldIndex = index
      var el = this
      this.ItemDropable.map(function (value: any, key: any) {
        if (key === index) el.$set(value, 'active', true)
        else el.$set(value, 'active', false)
      })
      if (!this.ItemDropable[index]) {
        return
      }
      //Don't show next screen for image one
      if (this.ItemDropable[index].type == 'img') {
        this.fieldSettings.enable = false
        return
      }
      this.fieldSettings.enable = true
      this.fieldSettings.fieldInfo = this.ItemDropable[index]
      // console.log(this.fieldSettings.fieldInfo);
    },
    fieldSettingInput(data: any) {
      switch (data.type) {
        case 'label':
          this.ItemDropable[this.selectedFieldIndex].label = data.value
          break
        case 'placeholder':
          this.ItemDropable[this.selectedFieldIndex].placeholder = data.value
          break
        case 'required':
          this.ItemDropable[this.selectedFieldIndex].required = data.value
          break
        case 'submit':
          this.ItemDropable[this.selectedFieldIndex].label = data.value
          break
        case 'text-size':
          this.$set(
            this.ItemDropable[this.selectedFieldIndex],
            'size',
            data.value
          )
          break
        case 'text-weight':
          this.$set(
            this.ItemDropable[this.selectedFieldIndex],
            'weight',
            data.value
          )
          break
        case 'allow-country-selection':
          this.$set(
            this.ItemDropable[this.selectedFieldIndex],
            'allowCountrySelection',
            data.value
          )

          break
        case 'text-color':
          this.$set(
            this.ItemDropable[this.selectedFieldIndex],
            'color',
            data.value
          )
          break
        case 'text-font':
          this.$set(
            this.ItemDropable[this.selectedFieldIndex],
            'font',
            data.value
          )
          break
        case 'text-label':
          this.ItemDropable[this.selectedFieldIndex].label = data.value
          break
        case 'text-align':
          this.$set(
            this.ItemDropable[this.selectedFieldIndex],
            'align',
            data.value
          )
          break
        case 'html':
          const scriptString = data.value
            .replace(/[‘’]/g, "'")
            .replace(/[“”]/g, '"')
          this.$set(
            this.ItemDropable[this.selectedFieldIndex],
            'html',
            scriptString
          )
          break
        case 'source-name':
          this.ItemDropable[this.selectedFieldIndex].value = data.value
        case 'hidden':
          this.ItemDropable[this.selectedFieldIndex].hidden = data.value
          break
        case 'hidden-value':
          this.ItemDropable[this.selectedFieldIndex].hiddenFieldValue =
            data.value
          break
        case 'hidden-query-key':
          this.ItemDropable[this.selectedFieldIndex].hiddenFieldQueryKey =
            data.value
          break
      }
      this.saveForm()
    },
    fieldSettingButton(data: any) {
      switch (data.type) {
        case 'bgcolor':
          this.buttonStyle.bgColor = data.value
          break
        case 'color':
          this.buttonStyle.color = data.value
          break
        case 'border':
          this.buttonStyle.border = data.value
          break
        case 'radius':
          this.buttonStyle.radius = data.value
          break
        case 'padding':
          this.buttonStyle.padding = data.value
          break
        case 'btn-align':
          this.$set(
            this.ItemDropable[this.selectedFieldIndex],
            'align',
            data.value
          )
          break
        case 'btn-fullwidth':
          this.$set(
            this.ItemDropable[this.selectedFieldIndex],
            'fullwidth',
            data.value
          )
          break
      }
      this.saveForm()
    },
    fieldSettingForm(data: any) {
      switch (data.type) {
        case 'bgcolor':
          this.formStyle.bgColor = data.value
          break
        case 'color':
          this.formStyle.color = data.value
          this.formStyle.formLabelColor =
            'label{color:#' + data.value + '!important;}'
          break
        case 'border':
          this.formStyle.border = data.value
          break
        case 'radius':
          this.formStyle.radius = data.value
          break
        case 'width':
          this.formStyle.width = data.value
          break
        case 'width':
          this.formStyle.width = data.value
          break
        case 'layout':
          this.formStyle.inlineForm = data.value
          break
        case 'label-visible':
          this.formStyle.formLabelVisible = data.value
          break
        case 'branding-active':
          this.formStyle.isBrandingActive = data.value
          break
        case 'custom-style':
          this.formStyle.emebedHtml = data.value
          break
      }
      this.saveForm()
    },
    fieldSettingFormAdvance(data: any) {
      switch (data.type) {
        case 'form':
          this.formAction.formName = data.value
          break
        case 'redirect':
          this.formAction.redirect_url = data.value
          break
        case 'thankyou':
          this.formAction.thankyouText = data.value
          break
        case 'fb-pixel-id':
          this.formAction.fbPixelId = data.value
          break
        case 'dis-qualified':
          this.formAction.disQualified = data.value
          break
        case 'end-of-survey':
          this.formAction.endOfSurvey = data.value
          break
        case 'sticky-contact':
          this.formAction.stickyContact = data.value
          break
        case 'fb-pixel-page-view-event':
          this.formAction.pageViewEvent = data.value
          break
        case 'fb-pixel-form-submission-event':
          this.formAction.formSubmissionEvent = data.value
          break
        default:
          this.formAction.actionType = data.value
          break
      }
      this.saveForm()
    },
    fieldSettingBack(data: any) {
      var el = this
      this.ItemDropable.map(function (value: any, key: any) {
        el.$set(value, 'active', false)
      })
      this.fieldSettings.enable = false
    },
    onClick(e: any) {
      let el: any = this.$refs.formBuilderReferance
      let target = e.target
      let elOpt: any = this.$refs.fieldSettingsRefs.$el
      if (
        el != undefined &&
        el !== target &&
        !el.contains(target) &&
        elOpt != undefined &&
        elOpt !== target &&
        !elOpt.contains(target)
      ) {
        this.fieldSettingBack(true)
      }
    },
    saveUploadPath(urls: any) {
      const index = this.selectedFieldIndex
      this.$set(this.ItemDropable[index], 'url', urls[0])
      // let el = this;
      // el.ItemDropable.map(function (value: any, index: any) {
      //   alert(el.selectedFieldIndex+'=>'+index);
      //   if ("tag" in value && value.tag == 'image' && el.selectedFieldIndex == index) {
      //     alert(urls[0]);
      //     el.$set(el.ItemDropable[index], 'url', urls[0]);
      //   }
      // });
      this.saveForm() //Save entry for modified dropItems
    },
    checkFieldOwnLine(tag: string) {
      let fieldCount = 0
      let lastFieldIndex = 0
      this.ItemDropable.map(function (value: any, key: any) {
        if (fullWidthFields.indexOf(value.type) < 0) {
          fieldCount++
          lastFieldIndex = key
        }
      })

      if (fieldCount % 2 != 0 && this.ItemDropable[lastFieldIndex].tag == tag)
        return true
      else return false
    },
    fieldFullWidth(type: string) {
      if (fullWidthFields.indexOf(type) >= 0) return true
      else return false
    },
    showCustomHtmlModal(inputText: any) {
      this.resetCustomHtml()
      this.customHtmlModalVisible = {
        visible: true,
        text: inputText,
      }
    },
    resetCustomHtml() {
      var vm = this
      vm.drawComponentCustomHtml = false
      Vue.nextTick(function () {
        vm.drawComponentCustomHtml = true
      })
    },
    closeCustomHtmlModal() {
      this.customHtmlModalVisible = {
        visible: false,
      }
    },
    getConstructedQueryKey(name) {
      return name.toLowerCase().trim().split(' ').join('_')
    },
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  mounted() {
    let style = document.createElement('style')
    style.type = 'text/css'
    style.appendChild(document.createTextNode(''))
    this.styleNode = style.childNodes[0] // a reference I store in the data hash
    document.head.appendChild(style)

    let styleColor = document.createElement('style')
    styleColor.type = 'text/css'
    styleColor.appendChild(document.createTextNode(''))
    this.styleNodeFormColor = styleColor.childNodes[0] // a reference I store in the data hash
    document.head.appendChild(styleColor)

    //On background click
    document.addEventListener('click', this.onClick)
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.redirectToListPage()
    },
    'formStyle.emebedHtml': function (val) {
      let styleStore = val.split('}')
      let styleString: string = ''
      styleStore.map(function (value: any, key: any) {
        if (value != '' && value.length > 0) {
          styleString += '#_builder-form ' + value + '} '
        }
      })
      this.styleNode.textContent = styleString
    },
    'formStyle.formLabelColor': function (val) {
      this.styleNodeFormColor.textContent = '#_builder-form ' + val
    },
  },
  computed: {
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
  },

  beforeRouteLeave(to, from, next) {
    if (!this.formbuilder?.formData?.form?.fields) {
      next()
    }

    let tempObj = JSON.parse(
      JSON.stringify({
        ...this.formbuilder.formData,
        ...this.formAction,
      })
    )
    tempObj.form.fields.forEach((field, ind) => {
      delete tempObj.form.fields[ind].active
    })

    if (!lodash.isEqual(this.publishedFormData, tempObj)) {
      this.$uxMessage(
        'confirmation',
        `The Form Has unsaved work, Are you sure you want to leave and discard all unsaved work?`,
        async res => {
          if (res === 'ok') {
            next()
          } else {
            next(false)
          }
        }
      )
    } else {
      next()
    }
  },

  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.builderId = this.$router.currentRoute.params.builder_id
    this.$bus.$on('add-field-to-drozone', this.addFieldAfter)
    this.$bus.$on('remove-custom-field', this.removeCustomField)
    this.$bus.$on('remove-standard-field', this.removeField)
    this.$bus.$on('field-setting-input', this.fieldSettingInput)
    this.$bus.$on('field-setting-button', this.fieldSettingButton)
    this.$bus.$on('field-setting-form', this.fieldSettingForm)
    this.$bus.$on('field-setting-form-advance', this.fieldSettingFormAdvance)
    this.$bus.$on('showCustomHtmlModal', this.showCustomHtmlModal)
    this.fetchCustomFields()
    this.initialData()
  },
  beforeDestroy() {
    this.$bus.$off('add-field-to-drozone', this.addFieldAfter)
    this.$bus.$off('remove-custom-field', this.removeCustomField)
    this.$bus.$off('remove-standard-field', this.removeField)
    this.$bus.$off('field-setting-input', this.fieldSettingInput)
    this.$bus.$off('field-setting-button', this.fieldSettingButton)
    this.$bus.$off('field-setting-form', this.fieldSettingForm)
    this.$bus.$off('field-setting-form-advance', this.fieldSettingFormAdvance)
    document.removeEventListener('click', this.onClick)
    this.$bus.$off('showCustomHtmlModal', this.showCustomHtmlModal)
    this.styleNode.textContent = '' //Remove css
    this.styleNodeFormColor.textContent = ''
    if (unsubscribeCustomFields) unsubscribeCustomFields()
  },
})
</script>

<style lang="scss">
  .form-control {
    border-radius: 0.3125rem;
    padding: 15px 20px;
    font-size: 0.875rem;
    &:disabled, [readonly] {
      background-color: #e9ecef;
      opacity: 1;
    }
  }

  .vs__search, .vs__search:focus {
    padding: 12px 18px !important
  }

  .form-builder--item {
    .v-select {
      width: 100%;
      background: #f3f8fb;
      border-radius: 4px !important;
      height: auto;
      border: transparent;
      .vs__dropdown-toggle {
        height: 50px;
      }
    }
  }
  .add-custom-opt {
    padding: 3px;
    font-size: 14px;
  }
</style>
