<template>
  <li
    class="hl_opportunities-item card pointer"
    @click.prevent="$emit('click', opportunity)"
    :style="{'border-top': `3px solid ${color}`}"
  >
    <div class="card-body">
      <div class="card-info">
        <div v-if="opportunity.contact_id" v-on:click.stop>
          <router-link
            tag="h5"
            :to="{name: getSideBarVersion == 'v2' ? 'contact_detail-v2': 'contact_detail', params: {contact_id: opportunity.contact_id}}"
          >
            <a style="color: black;" :href="`/location/${currentLocationId}/customers/detail/${opportunity.contact_id}`">
              {{opportunityName}}
            </a>
            <span
              v-if="opportunity.status=='won'"
              class="new"
              style="background-color:#37ca37;color:white;"
            >Won</span>
            <span
              v-if="opportunity.status=='lost'"
              class="new"
              style="background-color:#e93d3d;color:white;"
            >Lost</span>
            <span
              v-if="opportunity.status=='abandoned'"
              class="new"
              style="background-color:#607179;color:white;"
            >Abandoned</span>
          </router-link>
        </div>
        <h5 v-else>
          {{opportunityName}}
          <span
            v-if="opportunity.status=='won'"
            class="new"
            style="background-color:#37ca37;color:white;"
          >Won</span>
          <span
            v-if="opportunity.status=='lost'"
            class="new"
            style="background-color:#e93d3d;color:white;"
          >Lost</span>
          <span
            v-if="opportunity.status=='abandoned'"
            class="new"
            style="background-color:#607179;color:white;"
          >Abandoned</span>
        </h5>
        <p
          class="date"
          v-if="calendarEvent"
          @click.stop.prevent="$emit('clickAppointment', opportunity)"
        >{{calendarEvent.startTime.tz(timezone).format(getCountryDateFormat('normal'))}}</p>

        <i v-else class="fa fa-calendar-plus-o text-primary" @click.stop.prevent="$emit('clickAppointment', opportunity)"></i>
        <!-- <a
          href="#"
          class="btn btn-light4 btn-xss"
          v-else
          @click.stop.prevent="$emit('clickAppointment', opportunity)"
        >

        </a> -->
      </div>
      <p class="company-name company-name-ellipsis"  v-if="showCompanyName && opportunity.company_name" >{{opportunity.company_name}}</p>
      <div v-if="(showUser && user) || opportunity.source || opportunity.monetary_value">
        <div class="card-info">
          <Avatar :contact="user" size="sm" v-if="showUser && user" />
          <p class="product" v-if="opportunity.source" style="max-width: 110px;line-height: 1em;">{{opportunity.source}}</p>
          <p class="value" v-if="opportunity.monetary_value && (currentUser && currentUser.permissions.lead_value_enabled !== false)">{{location.country | symbole}}{{opportunity.monetary_value | formatNumber}}</p>
        </div>
      </div>

      <div class="tag-group" v-if="showTags && tags.length > 0" v-b-tooltip.hover
        :title="tags.join(', ')"
        v-bind:id="opportunity.id + 'tags'"
        placement="auto"
        data-toggle="tooltip">
          <div class="tag" v-for="tag in tags">{{tag}}</div>
      </div>

      <div class="card-info">
        <div class="opportunity-actions" style="margin-top: 5px;">
          <span @click.stop>
            <router-link
            v-if="opportunity.contact_id"
            :to="{name: getSideBarVersion == 'v2' ? 'contact_detail-v2': 'contact_detail', params: {contact_id: opportunity.contact_id}}" style="margin-right: 5px;">
            <i class="far fa-comments"></i>
          </router-link>
          </span>
          <i
            class="fa fa-phone"
            v-if="contactPhone"
            @click.stop.prevent="makeCall"
          />

          <span
            v-if="currentNote && currentNote.length"
            style="margin-left: 5px;"
            class="input-group-addon"
            v-b-tooltip.hover
            :title="currentNoteTitles(currentNote)"
            v-bind:id="opportunity.id"
            placement="top"
            data-html="true"
            data-toggle="tooltip"
          >
            <a
              href="#"
              class="btn btn-light4 btn-xss"
              @click.stop.prevent="$emit('clickNotes', opportunity)"
            >
              <i class="fas fa-info"></i>
            </a>
          </span>
        </div>
        <div>
          <a
            href="javascript:void(0);"
            class="btn btn-light4 btn-xs"
            @click.stop.prevent="$emit('clickTask', opportunity)"
            v-if="tasks && tasks.length === 0"
          >+ Task</a>
          <p @click.stop.prevent="$emit('clickTask', opportunity)" v-else>{{tasks.length}} tasks</p>
        </div>
      </div>

    </div>
  </li>
</template>
<script lang="ts">
import Vue from 'vue'
import { Container, Draggable } from 'vue-smooth-dnd'
import Avatar from '../Avatar.vue'
import { UserState } from '../../../store/state_models'

import {
  Contact,
  Location,
  User,
  Campaign,
  CampaignStatus,
  Opportunity,
  AppointmentRequest,
  Task,
  CalendarEvent,
  Note,
  getCountryDateFormat
} from '@/models'
import * as moment from 'moment-timezone'
import { mapState } from 'vuex'

export default Vue.extend({
  components: {
    Container,
    Draggable,
    Avatar
  },
  props: {
    opportunity: {
      type: Object
    },
    color: {
      type: String
    },
    columns: {
      type: Array
    },
    searchText: {
      type: String
    }
  },
  watch: {
    opportunity(campaignStatus: CampaignStatus) {
      this.fetchData()
    }
  },
  data() {
    return {
      user: undefined as undefined | User,
      currentNote: [] as Note[],
      calendarEvent: undefined as undefined | CalendarEvent,
      timezone: 'UTC',
      tasks: [] as Task[],
      currentLocationId: '',
      location: {} as Location,
      getCountryDateFormat: getCountryDateFormat
    }
  },
  computed: {
    fullName() {
      return this.opportunity && this.opportunity.contact_name
    },
    showTags() {
      return !this.columns.includes('hide_tags')
    },
    showUser() {
      return !this.columns.includes('hide_assigned_to')
    },
    tags() {
      const tags: string[] = this.opportunity && this.opportunity.tags || [];
      if (this.searchText) {
        const matchingTags: string[] = [];
        const otherTags: string[] = [];
        tags.forEach((tag: string) => {
          if (tag.toLowerCase().startsWith(this.searchText.toLowerCase())) matchingTags.push(tag);
          else otherTags.push(tag);
        });
        return [...matchingTags, ...otherTags];
      } else {
        return tags;
      }
    },
    contactPhone() {
      return this.opportunity && this.opportunity.phone
    },
    showCompanyName() {
      return this.columns.includes('company_name')
    },
    ...mapState('user', {
      currentUser: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    opportunityName(): string {
      if (this.opportunity && this.opportunity.name) {
        return this.opportunity.name
      } else if (this.fullName) {
        return this.fullName
      } else if (this.opportunity && this.opportunity.phone) {
        return this.opportunity.phone
      } else {
        return ''
      }
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchData()
  },
  methods: {
    async fetchData() {
      // if (this.opportunity.contact_id) {
      //   this.$store.dispatch('contacts/getAndWatch', this.opportunity.contact_id)
      // }

      if (this.opportunity.assigned_to) {
        this.user = new User(this.$store.getters['users/getById'](this.opportunity.assigned_to));
      } else {
        this.user = undefined
      }

      if (this.opportunity.location_id && this.opportunity.contact_id) {
        this.location = new Location(
          await this.$store.dispatch('locations/getCurrentLocation', this.currentLocationId)
        )
        this.timezone = await this.location.getTimeZone()
        if (this.columns.includes('calendar_event')) {
          this.calendarEvent = await CalendarEvent.fetchFutureEventForContact(
            this.opportunity.location_id,
            this.opportunity.contact_id,
            moment()
          )
        }
      }

      // CampaignStatus.getByContactAndStatus(this.opportunity.contact_id, 'running').then(async status => {
      // 	this.campaigns = await Promise.all(status.map(async status => {
      // 		return await Campaign.getById(status.campaignId);
      // 	}))
      // });

      if (this.opportunity.contact_id) {
        if (this.columns.includes('note')) {
          this.currentNote = await Note.getByContactId(this.opportunity.contact_id, this.currentLocationId)
        }
        if (this.columns.includes('task')) {
          this.tasks = await Task.getFrozenTasksByContactIdPending(this.opportunity.contact_id, this.currentLocationId)
        }
      }
    },
    currentNoteTitles(currentNote) {
      var html = ''
      var lastThreeNotes = currentNote.slice(0, 3)

      for (let i = 0; i < lastThreeNotes.length; i++) {
        if (
          lastThreeNotes.length == 3 &&
          lastThreeNotes[i].body.length >= 100
        ) {
          html += lastThreeNotes[i].body.substring(0, 100) + '...' + '\n\n'
        } else if (
          lastThreeNotes.length == 2 &&
          lastThreeNotes[i].body.length >= 150
        ) {
          html += lastThreeNotes[i].body.substring(0, 150) + '...' + '\n\n'
        } else if (
          lastThreeNotes.length == 1 &&
          lastThreeNotes[i].body.length >= 300
        ) {
          html += lastThreeNotes[i].body.substring(0, 300) + '...' + '\n\n'
        } else {
          html += lastThreeNotes[i].body + '\n\n'
        }
      }
      return html.replace(/<br[^>]*>/g, '\n')
    },
    makeCall() {
      if (this.contactPhone) {
        this.$root.$emit('makeCall', { phone_number: this.contactPhone });
      }
    }
  }
})
</script>
<style>
.company-name {
  max-width: 110px;
  line-height: 1em;
  color: #2a3135;
  font-weight: bold;
  line-height: 1.5em;
  padding-bottom: 5px;
}
h5:hover {
  text-decoration: underline;
}
#notesModal .modal-mask {
  position: fixed;
  z-index: 9998;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: table;
  transition: opacity 0.3s ease;
}

#notesModal .modal-wrapper {
  display: table-cell;
  vertical-align: middle;
}

#notesModal .modal-body {
  overflow-y: scroll;
}
#notesModal .modal {
  display: block !important;
}
#notesModal .modal-dialog {
  overflow-y: initial !important;
}
#notesModal .modal-body {
  padding: 10px 15px;
  height: 250px;
  overflow-y: auto;
}
#notesModal .modal-body p {
  margin-bottom: 15px;
  background: rgba(24, 139, 246, 0.1);
  padding: 10px 15px;
}
.tooltip .tooltip-inner {
  padding: 15px 10px;
  background-color: #000;
  font-size: 13px;
  max-width: 300px;
  text-align: left;
}
.tooltip {
  pointer-events: none;
}
.company-name-ellipsis {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
</style>
