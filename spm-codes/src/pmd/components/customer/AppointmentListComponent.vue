<template>
  <div style="height: 100%; overflow-y:auto;" class="pt-3">
    <div
      class="card my-0 mt-2"
      v-if="calendarEvents && calendarEvents.length > 0"
    >
      <div class="card-body py-0">
        <div class="hl_tasks">
          <AppoinmentCard
            v-for="appointment in calendarEvents"
            :calenderEvent="appointment"
            :key="appointment.id"
            :calendars="filterCalendars"
            :timezone="timezone"
            :locationId="locationId"
            :contactId="contactId"
          ></AppoinmentCard>
        </div>
      </div>
    </div>

    <template v-else>
      <div class="item-center">
        <moon-loader :loading="loading" color="#188bf6" size="20px" />
        <template v-if="!loading">
          <div class="text-center">
            <i
              class="fa fa-calendar"
              aria-hidden="true"
              style="font-size:20px; color:#BDBDBD;"
            ></i>
          </div>
          <p>
            {{ message === undefined ? defaultMessage : message }}
            <br />
          </p>
          <div class="text-center" v-if="action">
            Click
            <a
              @click="createNew"
              style="font-weight:bold; font-size:15px; color:#188bf6"
              >here</a
            >
            to create one
          </div>
        </template>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import * as moment from 'moment-timezone'
import Vue from 'vue'
import { CalendarEvent, User, Team, Calendar, Location } from '@/models/'
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'
const AppoinmentCard = () =>
  import('@/pmd/components/customer/AppointmentCard.vue')

export default Vue.extend({
  props: ['heading', 'action', 'message'],
  data() {
    return {
      calendarEvents: [] as CalendarEvent[],
      locationId: '',
      contactId: '',
      defaultMessage: 'No appointments for this contact',
      location: undefined as Location | undefined,
      timezone: 'UTC',
      loading: true as boolean,
    }
  },
  components: { AppoinmentCard },
  watch: {
    '$route.params.location_id': function(id) {
      this.locationId = id
      this.calendarEvents = []
      this.fetchData()
    },
    '$route.params.contact_id': function(id) {
      this.contactId = id
      this.calendarEvents = []
      this.fetchData()
    },
  },
  async created() {
    this.locationId = this.$router.currentRoute.params.location_id
    this.contactId = this.$router.currentRoute.params.contact_id
    this.fetchData()
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    vuexCalendarProviders() {
      return this.$store.getters['teams/calendarProviders']
    },
    calendarProviders() {
      return this.vuexCalendarProviders.map(s => new Team(lodash.cloneDeep(s)))
    },
    providerIdwiseProviderName() {
      const _providerIdwiseProviderName = {}
      this.calendarProviders.forEach(p => {
        _providerIdwiseProviderName[p.id] = p.calendarProviderName
      })
      return _providerIdwiseProviderName
    },
    vuexCalendars() {
      return this.$store.state.calendars.calendars
    },
    calendars() {
      const _calendars = lodash.orderBy(
        this.vuexCalendars.map(s => new Calendar(lodash.cloneDeep(s))),
        ['providerId'],
        ['desc']
      )
      _calendars.forEach(calendar => {
        calendar.name = this.providerIdwiseProviderName[calendar.providerId]
          ? `${calendar.name} (${
              this.providerIdwiseProviderName[calendar.providerId]
            })`
          : calendar.name
      })
      return _calendars.filter(x => x.isActive)
    },
    filterCalendars() {
      return this.calendars.filter(
        x =>
          this.user &&
          (!this.user.permissions.assigned_data_only ||
            this.user.role === 'admin' ||
            (this.user.permissions.assigned_data_only === true &&
              (this.user.userCalendar[this.locationId] === x.id ||
                (x.teamMembers &&
                  x.teamMembers.find(x => x.user_id === this.user.id)))))
      )
    },
  },
  methods: {
    async fetchData() {
      await Promise.all([
        this.$store.dispatch('teams/syncAll', this.$route.params.location_id)
      ])
      this.location = new Location(
        await this.$store.dispatch('locations/getById', this.locationId)
      )
      this.timezone = await this.location.getTimeZone()

      if (this.locationId && this.contactId) {
        CalendarEvent.fetchEventForContact(
          this.locationId,
          this.contactId
        ).then(calendarEvent => {

        if(calendarEvent) this.calendarEvents =calendarEvent.sort((a, b) => b.startTime - a.startTime)
        this.loading = false
        })

        // TODO: Delete for later
        // if (this.location.isCalendarV3On) {
        //   CalendarEvent.fetchEventForContact(
        //     this.locationId,
        //     this.contactId
        //   ).then(calendarEvent => {
        //     this.calendarEvents = calendarEvent
        //     this.loading = false
        //   })
        // }
      }
    },
    createNew() {
      this.$emit('createNew', true)
    },
  },
})
</script>
