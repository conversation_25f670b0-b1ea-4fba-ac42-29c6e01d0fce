<template>
    <div class="col-md-6">
        <div class="hl_social--post-item card">
            <div class="card-header">
                <div class="avatar">
                    <div class="avatar_img">
                        <img v-if="picture" :src="picture" alt="Avatar Name">
                        <span class="icon --facebook">
                            <i class="fab fa-facebook"></i>
                        </span>
                    </div>
                    <div class="avatar_text">
                        <h4>
                            <strong>{{name}}</strong> via
                            <span>Facebook</span>
                        </h4>
                        <p>21 Jun 2018
                            <span>9.30 AM</span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <p>{{post.message}}
                    <br/>
                    <img v-if="post.picture" :src="post.picture">
                </p>
                <div class="hl_social--post-item-details">
                    <ul class="list-inline">
                        <li class="list-inline-item">
                            <i class="fas fa-thumbs-up"></i>{{postStats.likes.summary.total_count}}</li>
                        <li class="list-inline-item">
                            <i class="fas fa-comment"></i>{{postStats.comments.summary.total_count}}
                        </li>
                        <!-- <li class="list-inline-item">
                            <i class="fas fa-retweet"></i>
                            </i> 000</li> -->
                    </ul>
                    <a href="#">Details</a>
                </div>
            </div>
            <div class="card-footer">
                <div class="hl_social--post-footer-item">
                    <h4>Potential Reach</h4>
                    <p>{{postImpressions}}</p>
                </div>
                <div class="hl_social--post-footer-item">
                    <h4>Engagement</h4>
                    <p>{{postEngagement}}</p>
                </div>
                <div class="hl_social--post-footer-item">
                    <h4>Stories Created</h4>
                    <p>{{postShares}}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import Vue from 'vue';
import { Company, Location, User, Notification, NotificationType, OAuth2 } from '@/models';

export default Vue.extend({
    props: ['post_id', 'postStats', 'name', 'picture'],
    data() {
        return {
            currentLocationId: '',
            postInsights: [],
            post: {}
        };
    },
    watch: {
        '$route.params.location_id': function (id) {
            this.currentLocationId = id;
        },
    },
    computed: {
        postImpressions(): number {
            if (this.postInsights && this.postInsights.length > 0) {
                let postImp = lodash.find(this.postInsights, { name: 'post_impressions' })
                if (postImp && postImp.values && postImp.values.length > 0) {
                    return postImp.values[0].value
                } else {
                    return 0
                }
            } else {
                return 0
            }
        },
        postEngagement(): number {
            if (this.postInsights && this.postInsights.length > 0) {
                let postImp = lodash.find(this.postInsights, { name: 'post_engaged_users' })
                if (postImp && postImp.values && postImp.values.length > 0) {
                    return postImp.values[0].value
                } else {
                    return 0
                }
            } else {
                return 0
            }
        },
        postShares(): number {
            if (this.postStats && this.postStats.shares) {
                return this.postStats.shares.count;
            } else {
                return 0
            }
        }

    },
    async created() {
        var _self = this;
        _self.currentLocationId = _self.$router.currentRoute.params.location_id;
    },
    async mounted() {
        this.get_post_data();
    },
    methods: {
        async get_post_data() {
            var _self = this;
            if (_self.post_id) {
                let resp = await this.$http.get('/api/facebook/get_post?location_id=' + _self.currentLocationId + "&post_id=" + _self.post_id);
                if (resp) {
                    _self.post = resp.data;
                }

                resp = await this.$http.get('/api/facebook/get_post_insights?location_id=' + _self.currentLocationId + "&post_id=" + _self.post_id);
                if (resp) {
                    _self.postInsights = resp.data.data;
                }
            }
        }
    },
});
</script>

    

