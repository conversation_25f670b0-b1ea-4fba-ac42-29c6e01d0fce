<template>
	<div>
		<section class="hl_login">
			<div class="hl_login--header">
				<div class="container-fluid">
					<a href="javascript:void(0);">
						<svg xmlns="http://www.w3.org/2000/svg" width="28" height="26">
							<image
								width="28"
								height="26"
								xlink:href="data:img/png;base64,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"
							></image>
						</svg>
					</a>
					<!-- <p>Already have an account?
                        <a href="/">Sign in</a>
					</p>-->
				</div>
			</div>
			<div class="hl_login--body">
				<div class="container-fluid">
					<div class="card">
						<div class="card-body">
							<div class="login-card-heading">
								<h2 class="heading2">Get started for free!</h2>
								<p>
									Get a 14 day
									<strong>free</strong> trial by signing up right now.
								</p>
							</div>
							<div class="form-group">
								<UITextInputGroup
									id="company_name"
									v-model="company_name"
									type="text"
									name="company_name"
									autocomplete="company_name"
									label="Company Name"
									placeholder="Your company name"
									v-validate="'required'"
									data-vv-as="Company name"
									:error="errors.has('company_name')"
									:errorMsg="errors.first('company_name')"
								/>
							</div>
							<div class="form-group">
								<UITextInputGroup
									id="name"
									v-model="name"
									type="text"
									name="name"
									label="Full Name"
									autocomplete="name"
									placeholder="Your full name"
									v-validate="'required'"
									:error="errors.has('name')"
									:errorMsg="errors.first('name')"
								/>
							</div>
							<div class="form-group">
								<UITextInputGroup
									id="email"
									v-model="email"
									type="text"
									name="email"
									label="Email"
									placeholder="Your email address"
									v-validate="{required: true, email: true}"
									autocomplete="email"
									:error="errors.has('email')"
									:errorMsg="errors.first('email')"
								/>
							</div>
							<div class="form-group">
								<UITextInputGroup
									id="password"
									name="password"
									v-model="password"
									type="password"
                  data-lpignore="true"
				  					label="Password"
									placeholder="Pick a password"
									v-validate="'required|strongPassword'"
									autocomplete="new-password"
									:error="errors.has('password')"
									:errorMsg="errors.first('password')"
								/>
							</div>
							<div class="form-group">
								<UITextLabel>Phone Number</UITextLabel>
								<PhoneNumber
									placeholder="Phone number"
									v-model="phone"
									v-validate="'required|phone'"
									name="phone"
									autocomplete="phone"
									data-vv-as="phone"
									:error="errors.has('phone')"
									:errorMsg="errors.first('phone')"
								/>
							</div>
							<div class="form-group">
								<UITextLabel>Account type</UITextLabel>
								<select class="selectpicker" v-model="planType">
									<option value="97">Starter Account ($97/month)</option>
									<option value="297">Freelancer Account ($297/month)</option>
									<option value="497">Agency Pro Account ($497/month)</option>
									<option value="test">Agency Test Account</option>
								</select>
							</div>
							<div v-if="!loading" class="form-group button-holder">
								<div class="global-error">
									<p v-show="response" stye="text-align:center">
										<span class="error">{{ response }}</span>
									</p>
								</div>
								<UIButton :loading="loading" @click.prevent="submit()" type="submit" use="secondary">Sign up</UIButton>
							</div>
							<p class="foot-note">
								By signing up, you agree to our
								<a
									href="https://www.gohighlevel.com/privacy"
									target="_blank"
								>Terms and Conditions</a>
							</p>
						</div>
					</div>
				</div>
			</div>
		</section>
	</div>
</template>
<script lang="ts">
import Vue from 'vue';
import VueAnalytics from 'vue-analytics';
import NotificationHelper from '../../util/notification_helper';
import { AxiosResponse } from 'axios';

Vue.use(VueAnalytics, {
	id: 'UA-*********-1',
	checkDuplicatedScript: true
})

const PhoneNumber = () => import('@/pmd/components/util/PhoneNumber.vue')
declare var $: any;

export default Vue.extend({
	components: {
      PhoneNumber,
  	},
	data() {
		return {
			response: '',
			email: '',
			password: '',
			loading: false,
			name: '',
			company_name: '',
			type: 'direct',
			planType: '97',
			phone: ''
		};
	},
	created() {
		this.$ga.page({
			page: '/sign_up',
			title: 'Sign Up Page',
			location: window.location.href
		});

		// if (this.$route.query.type === 'agency') this.type = 'agency';


	},
	methods: {
		async submit() {
      this.signout();

			const _self = this;
			this.loading = true;

			await this.$validator.validateAll();
			if (this.errors.any()) {
				this.loading = false;
				return Promise.resolve(true);
			}

			this.response = '';

			let firstName = '';
			let lastName = '';
			const parts = this.name.match(/\S+/g) || [];
			if (parts.length >= 1) {
				firstName = parts[0];
			}
			if (parts.length >= 2) {
				parts.splice(0, 1);
				lastName = parts.join(' ');
			}

			let localeString = "en-US";

			if (navigator.languages != undefined && navigator.languages.length > 0) {
				localeString = navigator.languages[0];
			}

			const params = {
				company_name: this.company_name,
				first_name: firstName.trim(),
				last_name: lastName.trim(),
				full_name: this.name.trim(),
				email: this.email.trim(),
				password: this.password.trim(),
				customer_type: "agency",
				plan : this.planType,
				locale_string: localeString,
				device_id: await _self.$store.dispatch('getDeviceId'),
				device_type: 'web',
				device_name: navigator.userAgent,
				timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        		version: 2,
				phone: this.phone.trim()
			};


			this.$http.post('/api/signup', params).then(
				(response: AxiosResponse) => {
					this.$store
						.dispatch('auth/set', {
							apiKey: response.data.apiKey,
							userId: response.data.userId,
							companyId: response.data.companyId,
						})
						.then(() => {
							return this.$auth.signInWithCustomToken(response.data.token);
						})
						.then(() => {
              this.$store
                .dispatch('auth/set', {
                  firebaseToken: response.data.token,
                  apiKey: response.data.apiKey,
                  userId: response.data.userId,
                  companyId: response.data.companyId,
                })
							this.createLeadInFirstPromoter();
							NotificationHelper.requestPermission();
							this.$store.dispatch('init');
							this.$router.push({ name: 'dashboard', replace: true });
						});
				},
				(reason: any) => {
					this.response = reason.response
						? reason.response.data.message
						: 'Unable to log you in at this time.';
					this.loading = false;
				},
			);
    },
    async signout() {
      try {
        await this.$store.dispatch('logout')
        await this.$auth.signOut()
        const response = await this.$http.post('/signout')
      } catch (err) {}
    },
		async createLeadInFirstPromoter() {

			$FPROM.trackSignup({
				email: this.email.trim(),
			},
				function () { console.log('Callback received!') });
			// let tid = this.$cookie.get('_fprom_code');

			// console.log("Got tid:", tid)

			// if (tid) {

			// 	let data = {
			// 		email: this.email.trim(),
			// 		tid: tid,
			// 	}

			// 	const response = await axios.post("/firstpromoter/add_lead", data, {
			// 		headers: {
			// 			ContentType: 'application/json',
			// 		},
			// 	});
			// 	console.log("Got response:", response.data);
			// }
		}
	},
	updated() {
		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker('refresh');
		}
	}

});
</script>

<style scoped>
.error {
	color: #a94442;
}
/*.container-fluid .card .card-body*/
.hl_login--body .spinner {
	display: inline-block;
	margin: 40px 0px 0px 0px;
	width: 100%;
}
.has-error .form-control,
.has-error .form-control:focus {
	-webkit-box-shadow: none;
	box-shadow: none;
}
.global-error {
	margin-bottom: 10px;
}
.hl_login--body .v-spinner .v-moon1 {
	margin: auto;
}
</style>


