<template>
  <div v-if="!showLoader && records.length > 0" class="container-fluid">
    <div class="card-group --wide-gutter">
      <div class="card hl_dashboard--pipeline-value">
        <highcharts :options="chart" class="pt-2"></highcharts>
      </div>
    </div>

    <h5 class="card-title pt-3">Forms</h5>
    <div class="card-group --wide-gutter shadow-sm rounded">
      <div class="card hl_dashboard--pipeline-value">
        <HLTable
            :items="items"
            :fields="fields"
            :currentPage="1"
            :rows="items.length"
            :isBusy="showLoader"
            :headerOptions="headerOptions"
            :defaultPerPage="items.length ? items.length : 10"
            :isPerPageOption="false"
            :detailsPagination="true"
            :isSampleData="false"
            :isLocalShorting="true"
            title="Form Analyze">

            <template v-slot:name="slotProps">
              <a class="pg-form-builder__link--show-submissions" @click.prevent="showSubmission(slotProps.data.item)">{{ slotProps.data.value }}</a>
            </template>

            <template v-slot:action="slotProps">
              <a href="#" class="btn btn-link pg-form-builder__link--show-submissions" @click.prevent="showSubmission(slotProps.data.item)">
                View Submission
              </a>
            </template>
        </HLTable>
      </div>
    </div>
  </div>

  <div v-else-if="!showLoader && records.length < 1">
    <div class="nothing-found">
      <p>
        No records found.
      </p>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import axios from 'axios'
import config from '@/config'
import * as moment from 'moment'
const HLTable = () => import('@/pmd/components/HLTable.vue').then(m => m.default)

export default Vue.extend({
  props: {
    location_id: {
      type: String,
      required: true
    },
    forms: {
      type: Array
    },
    date: {
      date: Object,
      required: true
    },
    selectedDate: {
      type: String
    }
  },
  components: {
    HLTable
  },
  data() {
    return {
      showLoader: true,
      records: [],
      headerOptions: {
        isExportOption: false,
        isColumnModifier: false,
        isQuickSearch: false,
        isMoreFilter: false
      },
    }
  },
  computed: {
    dates() {
      if (!this.records || !this.records.length) {
        return []
      }

      let dates = []
      for (let i = 0; i < this.records.length; i++) {
        if (!dates.includes(this.records[i].date)) {
          dates.push(this.records[i].date)
        }
      }

      return dates
    },
    counts() {
      if (!this.records || !this.records.length) {
        return []
      }

      let dates = []
      let records = []
      for (let i = 0; i < this.records.length; i++) {
        if (records.find(count => count.date == this.records[i].date)) {
          let index = records.findIndex(
            count => count.date == this.records[i].date
          )

          records[index].count = records[index].count + this.records[i].count
        } else {
          records.push({
            date: this.records[i].date,
            count: this.records[i].count
          })
        }
      }

      return records.map(record => record.count)
    },
    series() {
      let series = [
        {
          name: 'Submissions',
          type: 'column',
          data: this.counts
        }
      ]
      this.forms.forEach((form: { id: any; name: any }) => {
        series.push({
          id: form.id,
          name: form.name,
          type: 'spline',
          data: []
        })
      })

      this.dates.forEach(date => {
        series.forEach((data, index) => {
          let formCount = this.records.find(record => {
            return record.date == date && record.formId == data.id
          })

          if (data.id) {
            series[index].data.push(formCount ? formCount.count : 0)
          }
        })
      })

      return series.filter(row => {
        if (row.data && row.data.length) {
          return row.data.reduce((a, c) => a + c) > 0;
        }
      })
    },
    chart() {
      let $el = this
      return {
        chart: {
          zoomType: 'xy'
        },
        title: {
          text: 'Total Submission'
        },
        xAxis: {
          categories: this.dates
        },
        yAxis: {
          title: {
            text: 'Submission'
          }
        },
        tooltip: {
          shared: true
        },
        series: this.series,
        credits: {
          enabled: false
        }
      }
    },
    fields() {
      return [
        { key: 'id', sortable: true, checked: false },
        { key: 'name', sortable: true, checked: true, isRequired: true },
        { key: 'submission', sortable: true, checked: true, isRequired: true },
        { key: 'action', label: '', sortable: false, checked: true },
      ]
    },
    items() {
      return this.forms.map(form => {
        return {
          id: form.id,
          name: form._data.name,
          submission: this.fillupCont(form),
        }
      })
    }
  },
  watch: {
    location_id() {
      this.fetchData()
    },
    selectedDate() {
      this.fetchData()
    }
  },
  async mounted() {
    this.fetchData()
  },
  methods: {
    fillupCont(form) {
      let formData = this.records
        .filter(record => record.formId == form._id)
        .map(record => record.count)

      if (formData && formData.length) {
        return formData.reduce((a, c) => {
          return a + c
        })
      }

      return 0
    },
    fetchData() {
      this.showLoader = true
      axios
        .get(`${config.attributionUrl}/form/${this.location_id}`, {
          params: {
            startAt: this.date.start_date,
            endAt: this.date.end_date
          }
        })
        .then(({ data }) => {
          this.showLoader = false
          this.records = data
        })
    },
    showSubmission(form) {
      this.$router.push({ query: { ...this.$route.query, id: form.id } })
      this.$emit('changeTab', 2)
    }
  }
})
</script>

<style lang="scss">
/*<!-- this is not scoped so it can be used by others -->*/
tr.loading {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  td {
    height: 50px;
    vertical-align: middle;
    padding: 8px;
    span {
      display: block;
    }
    &.td-check {
      width: 20px;
      span {
        width: 20px;
        height: 20px;
        background-color: rgba(0, 0, 0, 0.15);
      }
    }
    &.td-loading-short {
      max-width: 30px;
    }
    &.td-loading-long {
      max-width: 75px;
    }
    &.td-loading-long,
    &.td-loading-short {
      // padding-right: 100px;
      span {
        height: 20px;
        background: linear-gradient(to right, #eee 20%, #ddd 50%, #eee 80%);
        background-size: 700px 100px;
        animation-name: moving-gradient;
        animation-duration: 1.5s;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
      }
    }
  }
}
</style>
