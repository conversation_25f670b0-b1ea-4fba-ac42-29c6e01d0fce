<template>
  <div class="col-md-6 mb-3">
    <div class="card">
      <div class="card-header">
        <div class="d-flex">
          <h2 class="mr-0">Channel Usage</h2>
          <div class="ml-2 mt-1">
            <span
              style="margin-top: -7px;"
              class="input-group-addon"
              v-b-tooltip.hover
              title="trigger links (embedded in message) clicked as % of messages sent"
            >
              <i class="fas fa-question-circle"></i>
            </span>
          </div>
        </div>
      </div>
      <div v-if="!isLoading" class="card-body">
        <table class="table">
          <tbody>
            <tr>
              <td>Sale</td>
              <td>45</td>
              <td>
                <span>
                  <i class="fas fa-arrow-down text-danger"/> 5%
                </span>
              </td>
            </tr>
            <tr>
              <td>Call</td>
              <td>{{data && data.call ? data.call.count || 0 : 0}}</td>
              <td>
                <span v-html="getPercentHTML(data && data.call ? data.call.percentage || 0 : 0)"/>
              </td>
            </tr>
            <tr>
              <td>Email</td>
              <td>{{data && data.email ? data.email.count || 0 : 0}}</td>
              <td>
                <span v-html="getPercentHTML(data && data.email ? data.email.percentage || 0 : 0)"/>
              </td>
            </tr>
            <tr>
              <td>Text</td>
              <td>{{data && data.sms ? data.sms.count || 0 : 0}}</td>
              <td>
                <span v-html="getPercentHTML(data && data.sms ? data.sms.changePercent || 0 : 0)"/>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div v-else class="card-body">
        <MoonLoader size="30px" radius="50%" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import MoonLoader from '../MoonLoader'
import get_percent_change_html from '../../../util/get_percent_change_html'

export default Vue.extend({
  components: {
    MoonLoader,
  },
  props: {
    isLoading: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default(){
        return {}
      }
    }
  },
  methods: {
    getPercentHTML : get_percent_change_html
  },
})
</script>

<style scoped>

</style>
