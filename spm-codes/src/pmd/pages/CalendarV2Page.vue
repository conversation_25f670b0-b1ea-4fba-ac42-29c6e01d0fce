<template>
  <section class="hl_wrapper">
    <section class="hl_wrapper--inner hl_calenderv2" id="calender-v2">
      <div class="container-fluid">
        <div class="d-flex justify-content-between mb-3">
          <div class="d-flex align-items-center">
            <b-dropdown :text="selectedItemText" variant="primary" size="sm" id="pg-calendar-v2__drpdwn--calendars-select">
              <template v-if="hasAnyCalendarProviders">
                <b-dropdown-header id="dropdown-header-label"
                >Primary</b-dropdown-header
                >
                <b-dropdown-item-button
                    @click="fetchAndSetMyCalendarEvents('My Calendar')"
                >My Calendar</b-dropdown-item-button
                >
                <!-- <b-dropdown-divider></b-dropdown-divider> -->

                <b-dropdown-header
                    v-if="filteredUsers.length > 0"
                    id="dropdown-header-label"
                >Users</b-dropdown-header
                >
                <b-dropdown-item
                    v-for="user in filteredUsers"
                    :key="user.id"
                    @click="fetchAndSetUserEvents(user.id, user.name)"
                >{{ user.name }}</b-dropdown-item
                >
                <!-- <b-dropdown-divider></b-dropdown-divider> -->

                <b-dropdown-header
                    v-if="filteredCalendarProviders.length > 0"
                    id="dropdown-header-label"
                >Teams</b-dropdown-header
                >
                <b-dropdown-item
                    v-for="calendarProvider in filteredCalendarProviders"
                    :key="calendarProvider.id"
                    @click="
                    fetchAndSetCalendarProviderEvents(
                      calendarProvider.id,
                      calendarProvider.calendarProviderName
                    )
                  "
                >{{ calendarProvider.calendarProviderName }}</b-dropdown-item
                >
              </template>

              <!-- <b-dropdown-divider></b-dropdown-divider> -->
              <b-dropdown-header
                  v-if="filterCalendars.length > 0"
                  id="dropdown-header-label"
              >Calendars</b-dropdown-header
              >
              <b-dropdown-item
                  v-for="calendar in filterCalendars"
                  :key="calendar.id"
                  @click="fetchAndSetCalendarEvents(calendar.id, calendar.name)"
              >{{ calendar.name }}</b-dropdown-item
              >
            </b-dropdown>

            <b-dropdown
                :text="calendarDefaultViews[defaultView]"
                variant="primary"
                class="mx-2"
                size="sm"
                id="pg-calendar-v2__drpdwn--calendars-view"
            >
              <b-dropdown-item
                  v-for="(calendarView, val) in calendarDefaultViews"
                  :key="val"
                  @click="defaultView = val"
              >{{ calendarView }}</b-dropdown-item
              >
            </b-dropdown>

            <b-dropdown
                :text="calendarEventFilters[eventFilter]"
                variant="primary"
                size="sm"
                id="pg-calendar-v2__drpdwn--calendars-filters"
            >
              <b-dropdown-item
                  v-for="(calendarEvent, val) in calendarEventFilters"
                  :key="val"
                  @click="eventFilter = val"
              >{{ calendarEvent }}</b-dropdown-item
              >
            </b-dropdown>

            <button class="btn btn-light btn-sm mx-2" @click="resetPage" id="pg-calendar-v2__btn--reset-page">
              Today
            </button>

            <b-button v-b-modal.my-modal size="sm" variant="link" id="pg-calendar-v2__btn--event-guide">
              <i class="fas fa-question-circle"></i>
            </b-button>
          </div>

          <div class="d-flex align-items-center">
            <button
                class="btn btn-primary btn-sm mr-2"
                @click="calendarPrevious"
                id="pg-calendar-v2__btn--pagination-prev"
            >
              <
            </button>
            <button class="btn btn-primary btn-sm" @click="calendarNext"  id="pg-calendar-v2__btn--pagination-next">
              >
            </button>
            <span class="mx-2" v-if="startDay && endDay"
            >{{ startDay }} &nbsp;-&nbsp; {{ endDay }}</span
            >
          </div>
        </div>

        <loader-slot v-if="isLoading" :loading="true" />

        <div v-if="this.filterCalendars.length === 0" class="container-fluid">
          <div class="card">
            <div class="card-header">
              <h3>
                No calendar found for your user, please create a new one or ask
                the admin to assign you to an existing calendar.
              </h3>
              <button
                  type="button"
                  class="btn btn-light3"
                  @click.prevent="$router.push({ name: 'calendar_settings' })"
              >
                Go to calendar settings
              </button>
            </div>
          </div>
        </div>
        <vue-tuicalendar
            v-else-if="initCalendar"
            ref="calendar"
            :options="calendarOptions"
            :schedules="schedules"
            @click-schedule="handleClickSchedule"
            @before-create-schedule="addEvent"
            @before-update-schedule="updateEvent"
            @before-delete-schedule="deleteEvent"
            @after-render-schedule="handleAfterRenderSchedule"
            :class="['calender-wrapper', { loading: isLoading }]"
        />
      </div>
    </section>

    <EditAppointmentModal @hidden="modalData = {}" :values="modalData" />
    <EditCalendarEventModal
        @update-event="eventUpdated"
        @delete-event="eventDeleted"
        @hidden="calendarModalData = {}"
        :values="calendarModalData"
        :calendars="filterCalendars"
    />

    <b-modal hide-footer id="my-modal" title="Event Guide">
      <b-list-group class="events-guide">
        <div class="general">Empty slot with no bookings.</div>
        <div class="blocked-slot">
          The blocked slot sync from google when you have turned on one way sync
          - Not Editable.
        </div>
        <div class="blocked-slot free">
          The blocked slot sync from google (which marked as free) when you have
          turned on one way sync - Not Editable.
        </div>
        <div class="recursive">
          Recurring event with busy status, to allow mutliple bookings set
          appointments per slot to more than 1 - Not Editable.
        </div>
        <div class="recursive free">
          Recurring event marked as free - Not Editable.
        </div>
        <div class="booked">
          Booked appointment (with contact) with busy status. Busy event color
          is darker than Free event and based on the value set in respective
          Calendar Service. To allow mutliple bookings set appointments per slot
          to more than 1.
        </div>
        <div class="booked free">
          Appointment (with contact) booked but marked as free. Free event color
          is lighter than Busy event and based on the value set in respective
          Calendar Service.
        </div>
        <!-- <div
          class="booked"
          :style="{ backgroundColor: eventColorBookedStatusBusy }"
        >
          Booked appointment (with contact) with busy status, to allow mutliple
          bookings set appointments per slot to more than 1.
        </div>
        <div
          class="booked free"
          :style="{ backgroundColor: eventColorBookedStatusFree }"
        >
          Appoinment (with contact) booked but marked as free.
        </div> -->
        <div class="booked cancelled">
          Booked appointment (with contact) but marked as cancelled, invalid,
          no-show.
        </div>
        <div class="booked new">
          New appointment (with contact) booked but required your action.
        </div>
        <div class="na">
          Slot marked unavailable in our app, will not show up in the widget,
          will not sync back to google.
        </div>
        <div class="na free">
          Slots fetched from Google Calendar (without contact) which is marked
          free.
        </div>
      </b-list-group>
    </b-modal>
  </section>
</template>

<script lang="ts">
import Vue from 'vue'
import { VueTuicalendar } from '@lkmadushan/vue-tuicalendar'
import moment, { Moment } from 'moment-timezone'
import {
  UserCalendar,
  CalendarEvent,
  Location,
  Team,
  Calendar,
  User,
  EventStatus,
  Contact,
  getCountryDateFormat
} from '../../models'
import { UserState } from '../../store/state_models'
import { mapState } from 'vuex'
import config from '../../config'
import 'tui-calendar/dist/tui-calendar.min.css'
import 'tui-date-picker/dist/tui-date-picker.css'
import 'tui-time-picker/dist/tui-time-picker.css'
import { parse } from 'handlebars'
import {
  AppoinmentEventStatus,
  AppointmentSource,
  AppointmentSourceChannel
} from '@/models/calendar_event'
const store = require('store')
import _,{ sortBy } from 'lodash'

const EditAppointmentModal = () =>
    import('../components/EditAppointmentModal.vue')
const EditCalendarEventModal = () =>
    import('../components/EditCalendarEventModal.vue')

class TZDate {
  public getTime(): number
  public toDate(): Date
  public toUTCString(): Date
}

interface CalendarInfo {
  id: string
  name: string
  color?: string
  bgColor?: string
  dragBgColor?: string
  borderColor?: string
}

interface EventScheduleObject {
  calendar: CalendarInfo
  event: MouseEvent
  schedule: Schedule
}

interface Event {
  start: Date
  end: Date
  schedule: Schedule
}

interface Schedule {
  id: string | undefined
  calendarId: undefined | string
  title: string
  category: string
  dueDateClass: string
  // state: 'Busy',
  location: string
  raw: {
    id: string | undefined
    location: string
    status: string
  }
  bgColor: string
  color: string
  start: TZDate
  end: TZDate
  isReadOnly: boolean
}

const templates = {
  popupIsAllDay: function() {
    return 'All Day'
  },
  popupStateFree: function() {
    return 'Free'
  },
  popupStateBusy: function() {
    return 'Busy'
  },
  titlePlaceholder: function() {
    return 'Subject'
  },
  locationPlaceholder: function() {
    return 'Location'
  },
  startDatePlaceholder: function() {
    return 'Start date'
  },
  endDatePlaceholder: function() {
    return 'End date'
  },
  popupSave: function() {
    return 'Save'
  },
  popupUpdate: function() {
    return 'Update'
  },
  popupDetailDate: function(isAllDay: boolean, start: TZDate, end: TZDate) {
    var isSameDate = moment(start).isSame(end)
    var endFormat = (isSameDate ? '' : 'YYYY.MM.DD ') + 'hh:mm a'

    if (isAllDay) {
      return (
          moment(start).format('YYYY.MM.DD') +
          (isSameDate ? '' : ' - ' + moment(end).format('YYYY.MM.DD'))
      )
    }

    return (
        moment(start).format('YYYY.MM.DD hh:mm a') +
        ' - ' +
        moment(end).format(endFormat)
    )
  },
  popupDetailLocation: function(schedule: Schedule) {
    return 'Location : ' + schedule.location
  },
  popupDetailUser: function(schedule: Schedule) {
    return ''
    // return 'User : ' + (schedule.attendees || []).join(', ');
  },
  popupDetailState: function(schedule: Schedule) {
    return 'Status : ' + schedule.raw.status
  },
  popupDetailRepeat: function(schedule: Schedule) {
    return ''
    // return 'Repeat : ' + schedule.recurrenceRule;
  },
  popupDetailBody: function(schedule: Schedule) {
    return 'Body : ' + schedule.body
  },
  popupEdit: function() {
    return 'Edit'
  },
  popupDelete: function() {
    return 'Delete'
  },
  timegridDisplayPrimaryTime: function(time) {
    let meridiem = time.hour < 12 ? 'am' : 'pm'

    return `${(time.hour > 12 ? time.hour - 12 : time.hour)} ${meridiem}`
  },
  timegridDisplayTime: function(time) {
    let meridiem = time.hour < 12 ? 'am' : 'pm'

    return `${(time.hour > 12 ? time.hour - 12 : time.hour)} ${meridiem}`
  },
  timegridCurrentTime: function(timezone) {
    const templates = [];

    if (timezone.dateDifference) templates.push('[' + timezone.dateDifferenceSign + timezone.dateDifference + ']<br>');

    templates.push(moment(timezone.hourmarker.toUTCString()).format('hh:mm a'));

    return templates.join('');
  }
}

let eventSubscription: () => void
// let cancelUsersSubscription: () => void

export default Vue.extend({
  name: 'calendar-v2',
  components: {
    VueTuicalendar,
    EditAppointmentModal,
    EditCalendarEventModal
  },
  data() {
    return {
      filterParams: {} as {
        user_id?: string
        calendar_provider_id?: string
        calendar_id?: string
      },
      selectedItemText: 'My Calendar' as string,
      masterCalendar: {},
      // users: [] as User[],
      calendarModalData: {},
      modalData: {},
      initCalendar: false,
      timezone: '',
      currentLocationId: '',
      currentLocation: {} as Location,
      eventFilter: 'all',
      defaultView: 'week',
      events: [] as CalendarEvent[],
      calendarStartDate: '',
      calendarEndDate: '',
      startDayOfWeek: 0,
      calendarOptions: {
        defaultView: 'week',
        template: templates,
        // useCreationPopup: true,
        // useDetailPopup: true,
        taskView: false,
        week: {
          narrowWeekend: false,
          startDayOfWeek: 1
        },
        month: {
          narrowWeekend: false,
          startDayOfWeek: 1
        },
        timezone: {
          offsetCalculator: function(timezoneName: string, timestamp: number){
            // e.g. +09:00 => -540, -04:00 => 240
            return moment.tz.zone(timezoneName).utcOffset(timestamp);
          },
        }
      },
      calendarDefaultViews: {
        month: 'Monthly',
        week: 'Weekly',
        day: 'Daily'
      },
      calendarEventFilters: {
        all: 'All',
        booked: 'Appointment',
        unavailable: 'Unavailable'
      },
      calendarId: undefined as undefined | string,
      schedules: [] as Schedule[],
      isLoading: false,
      isFirstScreenshot: true,
      initializeStartDate: '',
      initializeQuery: [],
    }
  },
  computed: {
    isMyCalendarSelected() {
      // User Master Calendar
      return this.filterParams.user_id === this.user.id
    },
    isV2CalendarSelected() {
      return this.filterParams.calendar_id && !this.selectedCalendar.providerId
    },
    filteredUsers() {
      return this.user.role === 'admin'
          ? // ? this.locationTeamMembers.filter(x => x.id !== this.user.id)
          this.locationTeamMembers
          : []
    },
    vuexCalendarProviders() {
      return this.$store.getters['teams/calendarProviders']
    },
    calendarProviders() {
      return this.vuexCalendarProviders.map(p => new Team(lodash.cloneDeep(p)))
    },
    hasAnyCalendarProviders() {
      return this.calendarProviders.length > 0
    },
    filteredCalendarProviders() {
      return this.user.role === 'admin'
          ? this.calendarProviders
          : this.calendarProviders.filter(x => x.userIds.includes(this.user.id))
    },
    providerIdwiseProviderName() {
      const _providerIdwiseProviderName = {}
      this.calendarProviders.forEach(p => {
        _providerIdwiseProviderName[p.id] = p.calendarProviderName
      })
      return _providerIdwiseProviderName
    },
    vuexCalendars() {
      return this.$store.state.calendars.calendars
    },
    calendars() {
      const _calendars = this.vuexCalendars.map(s => new Calendar(lodash.cloneDeep(s)));

      const v2Calendars = _calendars.filter(key => !key.providerId);
      const v3Calendars = _calendars.filter(key => key.providerId);
      v3Calendars.forEach(cal => { cal.name = `${cal.name} (${this.providerIdwiseProviderName[cal.providerId]})`});

      const sortedv3Calendars = sortBy(v3Calendars, (cal) => this.providerIdwiseProviderName[cal.providerId] ? this.providerIdwiseProviderName[cal.providerId].toUpperCase() : '')

      return [...v2Calendars, ...sortedv3Calendars].filter(x => x.isActive)
    },
    filterCalendars() {
      if (this.user) {
        if (this.user.role === 'admin' || !this.user.permissions.assigned_data_only) return this.calendars
        if (this.user.permissions.assigned_data_only) return this.calendars.filter(cal => this.user.userCalendar[this.currentLocationId] === cal.id || (cal.teamMembers && cal.teamMembers.find(m => m.selected && m.user_id === this.user.id)))
      }
      return []
    },
    // eventColorBookedStatusFree() {
    //   return this.calendar && this.calendar.event_color
    //     ? this.calendar.event_color + '99'
    //     : 'rgba(24, 139, 246, 0.6)'
    // },
    // eventColorBookedStatusBusy() {
    //   return this.calendar && this.calendar.event_color
    //     ? this.calendar.event_color + 'E6'
    //     : 'rgba(24, 139, 246, 0.9)'
    // },
    selectedCalendar() {
      return this.filterCalendars.find(x => x.id === this.calendarId) || {}
    },
    filteredEvents(): CalendarEvent[] {
      return this.events.filter(
          x =>
              x.status !== EventStatus.AVAILABLE &&
              (this.eventFilter === 'all' || x.status === this.eventFilter) &&
              (!this.user.permissions.assigned_data_only ||
                  this.user.role === 'admin' ||
                  x.createdByUserId === this.user.id ||
                  (this.user.permissions.assigned_data_only === true &&
                      (this.user.id === x.assignedUserId ||
                          this.user.userCalendar[this.currentLocationId] ===
                          x.calendarId)))
      )
    },
    startDay(): string {
      return this.calendarStartDate
          ? this.calendarStartDate.format(getCountryDateFormat(false))
          : ''
    },
    endDay(): string {
      return this.calendarEndDate
          ? this.calendarEndDate.format(getCountryDateFormat(false))
          : ''
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    locationTeamMembers() {
      return this.$store.state.users.users.map(u => new User(u))
    }
  },
  watch: {
    '$route.params.location_id': async function(id) {
      this.currentLocationId = id
      this.isFirstScreenshot = true
      await this.initializeData()
    },
    '$route.query.event_id': function(id) {
      this.fetchCalendars()
    },
    '$route.query.calendar': function(id) {
      if (id) {
        store.set(`calendar-${this.currentLocationId}`, {
          id: this.calendarId
        })

        if (!this.isFirstScreenshot) {
          this.fetchEvents()
        }
      }
    },
    '$route.query.startDate': function(id) {
      if (!this.isFirstScreenshot) {
        this.fetchEvents()
      }
    },
    '$route.query.user_id': function(id) {
      this.fetchEvents()
    },
    '$route.query.calendar_provider_id': function(id) {
      this.fetchEvents()
    },
    '$route.query.calendar_id': function(id) {
      this.fetchEvents()
    },
    defaultView(val) {
      this.$refs.calendar.fireMethod('changeView', val, true)
      this.fetchCalendars()
    },
    filteredEvents() {
      this.schedules = this.filteredEvents.map(e =>
          this.getScheduleDataFromEvent(e)
      )
    },
    calendarId() {
      this.updateQueryString()
    },
    '$refs.calendar': function() {
      if (!this.initCalendar) {
        this.fetchCalendars()
      }
    },
    startDayOfWeek(newValue) {
      //Default Start Day is Sunday
      this.calendarOptions.week.startDayOfWeek = Number.parseInt(newValue) || 0

      this.calendarOptions.month.startDayOfWeek = Number.parseInt(newValue) || 0
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id

    // this.fetchCalendars()

    window.addEventListener('resize', this.render)
  },
  async mounted() {
    await this.initializeData() 
  },
  methods: {
    async initializeData() {
      /**
       * Here actually we've to make sure that our calendar is loaded with team that's why have to call
       * This method
       */
      await this.$store.dispatch('teams/hasAnyCalendarProviders',this.$route.params.location_id)
      const query = this.$route.query;
      this.initializeQuery = query;

      if (query.startDate && query.return_view) {
        this.initializeStartDate = query.startDate;
      }

      if (query.eventFilter && query.return_view){
        this.eventFilter = query.eventFilter;
      }
      if (query.defaultView && query.return_view){
        this.defaultView = query.defaultView;
        this.calendarOptions.defaultView = query.defaultView;
      }

      if ((query.calendar_provider_id || query.calendar_id  || query.user_id)  && (query.return_view && query.selectedItemText))
      {

        if (query.calendar_provider_id){
          this.filterParams = { calendar_provider_id: query.calendar_provider_id }
          this.calendarId = query.calendar_id
          this.selectedItemText = query.selectedItemText
        }
        if (query.calendar_id){
          this.filterParams = { calendar_id: query.calendar_id }
          this.calendarId = query.calendar_id
          this.selectedItemText = query.selectedItemText
        }

        if (query.user_id){
          this.filterParams = { user_id: query.user_id }
          this.calendarId = query.user_id
          this.selectedItemText = query.selectedItemText
        }

      }else{
        if (this.hasAnyCalendarProviders) {
          // Show default 'My Calendar'
          this.filterParams = { user_id: this.user.id }
          this.selectedItemText = 'My Calendar'
        } else if (this.filterCalendars.length > 0) {
          // Show default first v2 calendar
          const defaultCalendar = this.filterCalendars[0] // TODO: This actually should be previously selected calendar (saved state) if any
          this.selectedItemText = defaultCalendar.name
          this.filterParams = { calendar_id: defaultCalendar.id }
          this.calendarId = defaultCalendar.id
        }
      }
      await this.fetchCalendars()
      await this.fetchData()
    },
    getReturnCalendarConfig(returnAsQuery: boolean = false){
      let query = this.filterParams;
      query['eventFilter'] = this.eventFilter
      query['defaultView'] = this.defaultView;
      query['selectedItemText'] = this.selectedItemText;
      if(returnAsQuery){
        return Object.keys(query).map(key => key + '=' + query[key]).join('&');
      }else{
        return query;
      }
    },
    getEventColorForBookedStatusBusy(calendarId: string) {
      const _calendar = this.calendars.find(x => x.id === calendarId)
      return _calendar && _calendar.eventColor
          ? _calendar.eventColor + 'E6'
          : 'rgba(24, 139, 246, 0.9)'
    },
    getEventColorForBookedStatusFree(calendarId: string) {
      const _calendar = this.calendars.find(x => x.id === calendarId)
      return _calendar && _calendar.eventColor
          ? _calendar.eventColor + '99'
          : 'rgba(24, 139, 246, 0.6)'
    },
    async fetchData() {
      if (this.currentLocationId) {
        // cancelUsersSubscription = (
        //   await User.fetchAllLocationUsers(this.currentLocationId)
        // ).onSnapshot(snapshot => {
        //   this.users = snapshot.docs.map(d => new User(d))
        // })
        // TODO: (MP) - Refactor
        const locationCalendars = await UserCalendar.getByUserAndLocation(
            this.user.id,
            this.currentLocationId
        )
        this.masterCalendar = locationCalendars.find(x => x.isMasterCalendar)
      }
    },
    subscribeRealtimeUpdate() {
      if (
          !this.filterParams.user_id &&
          !this.filterParams.calendar_provider_id &&
          !this.filterParams.calendar_id
      ) {
        return
      }

      if (eventSubscription) {
        eventSubscription()
        this.isFirstScreenshot = true
      }

      const fireStoreQuery = this.filterParams.user_id
          ? CalendarEvent.fetchAllUserEventsBetween(
              this.filterParams.user_id,
              this.calendarStartDate
                  .clone()
                  .subtract(1, 'day')
                  .startOf('day'),
              this.calendarEndDate
                  .clone()
                  .add(1, 'day')
                  .utc(),
              this.currentLocationId
          )
          : this.filterParams.calendar_provider_id
              ? CalendarEvent.fetchAllProviderEventsBetween(
                  this.filterParams.calendar_provider_id,
                  this.calendarStartDate
                      .clone()
                      .subtract(1, 'day')
                      .startOf('day'),
                  this.calendarEndDate
                      .clone()
                      .add(1, 'day')
                      .utc(),
                  this.currentLocationId
              )
              : CalendarEvent.fetchAllEventsBetween(
                  this.filterParams.calendar_id,
                  this.calendarStartDate
                      .clone()
                      .subtract(1, 'day')
                      .startOf('day'),
                  this.calendarEndDate
                      .clone()
                      .add(1, 'day')
                      .utc(),
                  this.currentLocationId
              )

      eventSubscription = fireStoreQuery.onSnapshot(async snapshot => {
        if (!this.isFirstScreenshot) {
          for (let i = snapshot.docChanges().length - 1; i >= 0; i--) {
            const docChange = snapshot.docChanges()[i]
            const calenderEvent = new CalendarEvent(docChange.doc)
            if (calenderEvent.isRecurring) {
              continue
            }

            calenderEvent.startTime = moment.tz(
                calenderEvent.startTime,
                this.timezone
            )
            calenderEvent.endTime = moment.tz(
                calenderEvent.endTime,
                this.timezone
            )

            if (docChange.type === 'added') {
              if (calenderEvent.contactId) {
                const contact = await this.$store.dispatch(
                    'contacts/syncGet',
                    calenderEvent.contactId
                )
                if (contact) {
                  calenderEvent.data.contact_name = contact.name
                }
              }
              let existingIndex = this.events.findIndex(
                  x => x.id === calenderEvent.id
              )

              if (existingIndex === -1) {
                this.events.push(calenderEvent)
              }
            } else if (docChange.type === 'modified') {
              if (calenderEvent.contactId) {
                const contact = await this.$store.dispatch(
                    'contacts/syncGet',
                    calenderEvent.contactId
                )
                if (contact) {
                  calenderEvent.data.contact_name = contact.name
                }
              }
              let existingIndex = this.events.findIndex(
                  x => x.id === calenderEvent.id
              )

              if (existingIndex !== -1) {
                this.$set(this.events, existingIndex, calenderEvent)
              }
            } else if (calenderEvent.deleted || docChange.type === 'removed') {
              let existingIndex = this.events.findIndex(
                  x => x.id === calenderEvent.id
              )

              this.events.splice(existingIndex, 1)
            }
          }
        }

        this.isFirstScreenshot = false
      })
    },
    async addEvent(event: Event) {
      if (!this.isMyCalendarSelected && !this.isV2CalendarSelected) {
        return
      }

      if (moment().isAfter(moment(event.start.getTime()))) {
        event.guide.clearGuideElement()
        return
      }
      // Here we'll subtract one hour if we're in DST
      const eventStart = moment.tz(event.start.getTime(),this.timezone)
      const eventEnd = moment.tz(event.end.getTime(),this.timezone)
      if (eventStart.isDST()) {
        eventStart.subtract(1, 'hour')
      }
      if (eventEnd.isDST()) {
        eventEnd.subtract(1, 'hour')
      }
      const newEvent = this.isMyCalendarSelected
          ? CalendarEvent.createUserCalendarEvent(
              this.currentLocationId,
              this.user.id,
              this.masterCalendar.id,
              eventStart,
              eventEnd,
              EventStatus.UNAVAILABLE,
              {
                name: event.schedule ? event.schedule.title : ''
              }
          )
          : CalendarEvent.createV2CalendarEvent(
              this.currentLocationId,
              this.selectedCalendar.id,
              eventStart,
              eventEnd,
              EventStatus.UNAVAILABLE,
              {
                name: event.schedule ? event.schedule.title : ''
              }
          )

      newEvent.isFree = false
      newEvent.isFullDay = event.isAllDay
      newEvent.userId = this.user.id // created by user

      newEvent.setCreatedByAndOrLastUpdatedBy(
          true,
          this.user.id,
          AppointmentSource.CALENDAR_PAGE,
          AppointmentSourceChannel.WEB_APP
      )
      await newEvent.save()

      if (newEvent.editable) {
        this.openCalendarModal(
            newEvent.id,
            newEvent.startTime,
            newEvent.endTime
        )
      }
    },
    async updateEvent(event: Event) {
      let eventIndex = this.events.findIndex(x => x.id === event.schedule.id)
      const calendarEvent = <CalendarEvent>(
          await CalendarEvent.getById(event.schedule.id)
      )
      if (eventIndex !== -1 && calendarEvent) {
        // calendarEvent.title = event.schedule.title;
        calendarEvent.startTime = event.start
        calendarEvent.endTime = event.end

        calendarEvent.setCreatedByAndOrLastUpdatedBy(
            false,
            this.user.id,
            AppointmentSource.CALENDAR_PAGE,
            AppointmentSourceChannel.WEB_APP
        )
        await calendarEvent.save()

        if (calendarEvent.isRecurring) {
          this.fetchEvents()
        } else {
          // this.$set(this.events, eventIndex, calendarEvent);
        }
      }
    },
    async deleteEvent({ schedule }: { schedule: Schedule }) {
      const calendarEvent = <CalendarEvent>(
          await CalendarEvent.getById(schedule.id)
      )
      if (calendarEvent) {
        calendarEvent.delete()
      }
    },
    // { schedule } : { schedule : Schedule }
    handleClickSchedule(e: { schedule: Schedule; event: MouseEvent }) {
      let { schedule, event } = e

      // event.preventDefault();
      // event.stopPropagation();

      let calendarEvent = this.events.find(x => x.id === schedule.id)

      if (calendarEvent.editable) {
        this.openCalendarModal(
            schedule.id,
            calendarEvent.startTime,
            calendarEvent.endTime
        )
      }
    },
    handleAfterRenderSchedule({ schedule }: { schedule: Schedule }) {
      // this.$refs.calendar.fireMethod('setTimezoneOffset', moment().tz('America/Boise').utcOffset());
    },
    calendarPrevious() {
      this.$refs.calendar.fireMethod('prev')

      this.setStartAndEndDate()
    },
    calendarNext() {
      this.$refs.calendar.fireMethod('next')

      this.setStartAndEndDate()
    },
    setStartAndEndDate() {
      this.calendarStartDate = moment(
          this.$refs.calendar.fireMethod('getDateRangeStart').toDate()
      )
          .tz(this.timezone, true)
          .startOf('day')
      this.calendarEndDate = moment(
          this.$refs.calendar.fireMethod('getDateRangeEnd').toDate()
      )
          .tz(this.timezone, true)
          .endOf('day')

      this.updateQueryString()
    },
    updateQueryString() {
      const filterParams = this.filterParams
      if (
          !(this.calendarStartDate && moment.isMoment(this.calendarStartDate))
      ) {
        this.calendarStartDate = moment().startOf('day')
      }
      this.$router.push({
        query: {
          ...filterParams,
          location_id: this.currentLocationId,
          startDate: this.calendarStartDate.format('YYYY-MM-DD')
        }
      })
    },
    openCalendarModal(eventId: string, startTime: Moment, endTime: Moment) {
      this.calendarModalData = {
        visible: true,
        eventId: eventId,
        calendarId: this.masterCalendar
            ? this.masterCalendar.id
            : this.selectedCalendar.id,
        startTime: startTime,
        endTime: endTime
      }
    },
    async fetchCalendars() {
      const location = await this.$store.dispatch(
          'locations/getById',
          this.currentLocationId
      )

      this.currentLocation = location

      location.timezone != '' ? this.timezone= location.timezone : this.$uxMessage('warning', 'Please set timezone for your location in Company Settings to view your appointments.')


      this.startDayOfWeek = location.start_day_of_week

      const { startDate } = this.$route.query

      if (startDate) {
        this.calendarStartDate = moment(startDate, 'YYYY-MM-DD')
            .tz(this.timezone, true)
            .startOf('day')
        this.calendarOptions.timezone.zones = [
          {
            timezoneName: this.timezone,
            displayLabel: this.timezone,
            tooltip: 'Location timezone'
          }
        ]
      } else {
        this.calendarStartDate = moment()
            .tz(this.timezone, true)
            .startOf('day')
        this.calendarOptions.timezone.zones = [
          {
            timezoneName: this.timezone,
            displayLabel: this.timezone,
            tooltip: 'Location timezone'
          }
        ]
      }

      this.events = []

      if (this.filterCalendars.length === 0) {
        return
      }

      this.initCalendar = true

      this.$nextTick(async () => {
        try {
          // if (this.$refs.calendar && this.calendarStartDate) {
          //   this.render()
          //   this.$refs.calendar.fireMethod(
          //     'setDate',
          //     this.calendarStartDate.toDate()
          //   )
          // }
          // let index = 0
          // let calendar = this.$route.query.calendar
          // if (!calendar) {
          //   const storedCal = store.get(`calendar-${this.currentLocationId}`)
          //   if (storedCal && storedCal.id) {
          //     calendar = storedCal.id
          //   }
          // }
          // if (calendar) {
          //   let findIndex = this.filterCalendars.findIndex(
          //     x => x.id === calendar
          //   )
          //   if (findIndex !== -1) {
          //     index = findIndex
          //   }
          // }

          // this.calendarId = this.filterCalendars[index].id

          if (
              !this.filterParams.user_id &&
              !this.filterParams.calendar_provider_id &&
              !this.filterParams.calendar_id
          ) {
            this.filterParams = { user_id: this.user.id }
          }

          //to get the date from url
          const query = this.initializeQuery;
          //check if have start date and it's coming from the contact details page
          if (query.startDate && query.return_view){
            //check if the date is a valid date
            //if (this.initializeStartDate.isValid()){
              //set the calendar start date
            const _startDate = moment(query.startDate, 'YYYY-MM-DD').tz(this.timezone, true).startOf('day')
              this.$refs.calendar.fireMethod(
                  'setDate',
                  _startDate.format('YYYY-MM-DD')
              )
            if (query.defaultView == 'month' && _startDate.date() > 25/* && !!query.calendar_id || !!query.calendar_provider_id*/){
                this.$refs.calendar.fireMethod('next')
              }
            //}
          }

          this.setStartAndEndDate()
          await this.fetchEvents()
        } catch (e) {
          console.warn('Error in fetching calendars, retrying on render')
          console.warn(e)
        }
      })
    },
    async fetchEvents() {
      this.events = []

      if (
          !this.filterParams.user_id &&
          !this.filterParams.calendar_provider_id &&
          !this.filterParams.calendar_id
      )
        return

      if (
          !(this.calendarStartDate && moment.isMoment(this.calendarStartDate))
      ) {
        this.calendarStartDate = moment().startOf('day')
      }

      if (!(this.calendarEndDate && moment.isMoment(this.calendarEndDate))) {
        this.calendarEndDate = moment()
            .add(7, 'days')
            .endOf('day')
      }

      if (
          !this.calendarOptions.timezone.zones ||
          this.calendarOptions.timezone.zones.length === 0 ||
          this.timezone !== this.calendarOptions.timezone.zones[0].timezoneName
      ) {
        this.$refs.calendar.fireMethod('clear')
        this.$nextTick(() => {
          this.calendarOptions.timezone.zones = [
            {
              timezoneName: this.timezone,
              displayLabel: this.timezone,
              tooltip: 'Location timezone'
            }
          ]

          this.$refs.calendar.fireMethod('setOptions', this.calendarOptions)
          this.fetchAndSetEvents()
        })
      } else {
        this.fetchAndSetEvents()
      }
    },
    fetchAndSetMyCalendarEvents(selectedItemText: string) {
      this.fetchAndSetUserEvents(this.user.id, selectedItemText)
    },
    fetchAndSetUserEvents(user_id: number, selectedItemText: string) {
      this.selectedItemText = selectedItemText
      this.filterParams = { user_id }
      this.updateQueryString()
    },
    fetchAndSetCalendarProviderEvents(
        calendar_provider_id: string,
        selectedItemText: string
    ) {
      this.selectedItemText = selectedItemText
      this.filterParams = { calendar_provider_id }
      this.updateQueryString()
    },
    fetchAndSetCalendarEvents(calendar_id: string, selectedItemText: string) {
      this.selectedItemText = selectedItemText
      this.filterParams = { calendar_id }
      this.calendarId = calendar_id
      this.updateQueryString()
    },
    fetchAndSetEvents() {
      const filterParams = this.filterParams
      this.isLoading = true
      let filters = {
        ...filterParams,
        location_id: this.currentLocationId,
        startDate: this.calendarStartDate,
        endDate: this.calendarEndDate,
        includeAll: true
      }

      this.$http
          .get(
              `${config.baseUrl}/calendar_events?` +
              Object.keys(filters)
                  .map(key => key + '=' + filters[key])
                  .join('&')
          )
          .then(({ data }) => {
            this.events = data.map(
                d =>
                    new CalendarEvent(
                        Object.assign(
                            {
                              ...d
                            },
                            {
                              start_time: moment.tz(d.start_time, this.timezone),
                              end_time:
                                  moment().tz(this.timezone).utcOffset() !== 0
                                      ? moment.tz(d.end_time, this.timezone)
                                      : moment.tz(d.end_time, this.timezone).add(1, 'milliseconds') // Workaround for TUI Calendar component issue with 1 hour events viewing in UTC timezone
                            }
                        )
                    )
            )

            this.$nextTick(() => {
              this.isLoading = false
            })
          })

      this.subscribeRealtimeUpdate()
    },
    render() {
      this.$refs.calendar.fireMethod('render')
    },
    getScheduleDataFromEvent(event: CalendarEvent): Schedule {
      // const timezoneDiff = this.calendarStartDate.utcOffset() - moment.tz(this.timezone).utcOffset();

      // Let's generate our schedule title before adding extra hour as it's only for fixing the UI
      const eventTitle = this.getCellLabel(event)

      //To fix the DST UI issue we'll add here fake one extra hour if it's a full-day event
      const eventStart = moment.tz(event.startTime,this.timezone)
      const eventEnd = moment.tz(event.endTime,this.timezone)
      if(event.isFullDay){
        if (eventStart.isDST()) {
          eventStart.add(1, 'hour')
        }
        if (eventEnd.isDST()) {
          eventEnd.add(1, 'hour')
        }
        // This one second is to make sure we're within the day
        eventEnd.subtract(1, 'second')
      }

      return {
        id: event.id,
        calendarId: undefined,
        title: eventTitle,
        category: 'time',
        dueDateClass: '',
        // state: 'Busy',
        location: 'time',
        raw: {
          id: event.id,
          location: 'time',
          status: lodash.capitalize(event.status)
        },
        bgColor: this.getCellBgColor(event),
        color: '#fff',
        start: eventStart.toDate(),
        end: eventEnd.toDate(),
        isAllDay: event.isFullDay,
        isReadOnly: event.isRecurring || event.toIgnore
      }
    },
    getCellLabel(event: CalendarEvent) {
      let startTimeStr = event.startTime
          .clone()
          .tz(this.timezone)
          .format('hh:mm A')
      let endTimeStr = event.endTime
          .clone()
          .tz(this.timezone)
          .format('hh:mm A')

      const title = event.title ? event.title : event.contactName || ''

      if (event.status === EventStatus.UNAVAILABLE) {

        if(event?.google?.data?.visibility === "private") {
          return `Busy&nbsp;(${startTimeStr} - ${endTimeStr})`
        }

        if (event.isRecurring) {
          return `${title}&nbsp;(${startTimeStr} - ${endTimeStr})`
        }

        if (event.isFree) {
          return `${title + ' - Free'} (${startTimeStr} - ${endTimeStr})`
        }

        return `${title || 'Not available'} (${startTimeStr} - ${endTimeStr})`
      } else if (event.status === EventStatus.BOOKED) {
        // if (event.appoinmentStatus === AppoinmentEventStatus.STATUS_NEW) {
        // 	return '&nbsp;Action Required';
        // }

        return `${title} (${startTimeStr} - ${endTimeStr})`
      }
    },
    getCellBgColor(event: CalendarEvent) {
      if (event.isRecurring) {
        if (event.isFree) {
          return 'rgba(141, 117, 189, 0.5)'
        }

        return 'rgb(141, 117, 189)'
      } else if (event.toIgnore) {
        if (event.isFree) {
          return 'rgba(160, 160, 160, 0.5)'
        }
        return '#a0a0a0'
      } else if (
          event.appoinmentStatus === AppoinmentEventStatus.STATUS_CONFIRMED ||
          event.appoinmentStatus === AppoinmentEventStatus.STATUS_SHOWED
      ) {
        if (event.isFree) {
          return this.getEventColorForBookedStatusFree(event.calendarId)
        }

        return this.getEventColorForBookedStatusBusy(event.calendarId)
      } else if (event.appoinmentStatus === AppoinmentEventStatus.STATUS_NEW) {
        return '#ffbc00'
      } else if (event.appoinmentStatus) {
        return '#e93d3db8'
      } else if (event.isFree) {
        return 'rgba(55, 202, 55, 0.68)'
      } else if (!event.local && event.status === EventStatus.UNAVAILABLE) {
        return 'rgba(55, 202, 55, 0.68)'
      }

      return '#37ca37'
    },
    resetPage() {
      // this.eventFilter = 'all';
      // this.defaultView = 'week';
      // this.$refs.calendar.fireMethod('changeView', 'week', true);
      // this.calendarStartDate = moment();
      // this.calendarEndDate = moment().add(7, 'days');

      // this.$router.replace({
      //   query: undefined
      // })

      // this.fetchEvents();

      // window.location.reload();
      this.$refs.calendar.fireMethod('today')
      this.setStartAndEndDate()
    },
    eventUpdated(payload) {
      const index = this.events.findIndex(x => x.id === payload.id)
      if (index !== -1) {
        const event = this.events[index]

        if (event.editable) {
          this.fetchEvents()
        } else {
          // this.$set(this.events, index, payload.event);
        }
      }
    },
    eventDeleted(id: string) {
      const index = this.events.findIndex(x => x.id === id)
      if (index !== -1) {
        const event = this.events[index]

        if (event.editable) {
          this.fetchEvents()
        } else {
          // this.events.splice(index, 1);
        }
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.render)

    if (eventSubscription) {
      eventSubscription()
    }

    // if (cancelUsersSubscription) cancelUsersSubscription()
  }
})
</script>

<style>
.previous-version-page-flag-wrapper {
  border: 1px solid #ebebeb;
  box-sizing: border-box;
  border-radius: 3px;
}
.dropdown-header {
  margin-top: 14px;
  padding-left: 14px;
  font-size: 0.8rem;
  font-weight: bold;
  line-height: 16px;
  text-transform: uppercase;
  color: #92a0b0;
}

.hl_calenderv2 .loader-wrapper {
  position: absolute !important;
  top: 50%;
  left: 50%;
  z-index: 1;
}
.calender-wrapper {
  height: calc(100vh - 200px);
  z-index: 9999;
}
.calender-wrapper.loading::after {
  background-color: rgba(255, 255, 255, 0.6);
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}
.events-guide .general {
  color: #000 !important;
}
.events-guide .list-group-item:hover {
  color: #fff;
}
.events-guide .blocked-slot {
  background: #a0a0a0 !important;
  color: #fff;
}
.events-guide .blocked-slot.free {
  background: rgba(160, 160, 160, 0.5) !important;
}
.events-guide .recursive {
  background: rgba(141, 117, 189) !important;
  color: #fff;
}
.events-guide .recursive.free {
  background: rgba(141, 117, 189, 0.5) !important;
}
.events-guide .na {
  background: #37ca37 !important;
  color: #fff;
}
.events-guide .na.free {
  background: rgba(55, 202, 55, 0.68) !important;
  color: #fff;
}
.events-guide .booked {
  background: rgba(24, 139, 246, 0.9);
  color: #fff;
}
.events-guide .booked.cancelled {
  background: #e93d3db8 !important;
  color: #fff;
}
.events-guide .booked.new {
  background: #ffbc00 !important;
  color: #fff;
}
.events-guide .booked.free {
  background: rgba(24, 139, 246, 0.6);
}
.events-guide > div {
  padding: 10px;
}
</style>
