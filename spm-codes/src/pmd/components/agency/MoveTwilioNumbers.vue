<template>
  <div class="modal fade hl_sms-template--modal" tabindex="-1" role="dialog" ref="modal">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <!-- Header - START -->
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Move twilio numbers</h2>
            <button type="button" class="close" @click.prevent="closeModal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <!-- Header - END -->

        <!-- Body - START -->
        <div class="modal-body pb-2">
          <div class="modal-body--inner">
            <div>
              <h5>Step 1 - Choose the source account</h5>
              <vSelect
                class="mt-2"
                :options="accountsOptions"
                label="name"
                v-model="source"
                :clearable="false"
                placeholder="Select source account"
                v-validate="'required'"
                name="accounts-options"
                @input="getNumbers"
                :loading="loading.locations"
              >
                <template #spinner="{ loading }">
                  <div v-if="loading" style="border-left-color: rgba(88,151,251,0.71)" class="vs__spinner"></div>
                </template>
              </vSelect>
            </div>

            <div>
              <h5 class="mt-4" :style="!source && 'color: #777'">Step 2 - Select the phone number</h5>
              <vSelect
                class="mt-2"
                :disabled="!source"
                :options="phoneNumbers"
                label="phone_number"
                v-model="phoneNumber"
                :clearable="false"
                placeholder="Select phone number"
                v-validate="'required'"
                name="phone-numbers"
              ></vSelect>
            </div>

            <div class="mb-4">
              <h5
                class="mt-4"
                :style="!phoneNumber && 'color: #777'"
              >Step 3 - Choose the destination account</h5>
              <vSelect
                class="mt-2"
                :disabled="!phoneNumber"
                :options="accountsOptions"
                label="name"
                v-model="destination"
                :clearable="false"
                placeholder="Select destination account"
                v-validate="'required'"
                name="accounts-options"
              ></vSelect>
            </div>
          </div>
        </div>
        <!-- Body - END -->

        <!-- Footer - START -->
        <div class="modal-footer">
          <div v-if="errorMessage" class="ml-4 mb-4">
            <p class="error">{{ errorMessage }}</p>
          </div>
          <div class="modal-footer--inner nav">
            <button type="button" class="btn btn-light2" @click.prevent="closeModal">Cancel</button>
            <div class="ml-2" style="display: inline-block;position: relative;">
              <button
                :class="{invisible: movingNumbers}"
                :disabled="(!source || !phoneNumber || !destination)"
                :style="(!source || !phoneNumber || !destination) && 'cursor: not-allowed'"
                type="button"
                class="btn btn-success"
                @click.prevent="moveNumbersTwilio"
              >Move numbers</button>
              <div
                style="position: absolute;left: 50%;transform: translate(-50%, -50%);top: 50%;"
                v-show="movingNumbers"
              >
                <moon-loader :loading="movingNumbers" color="#37ca37" size="30px" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
const TwilioListItem = () =>
  import('@/pmd/components/agency/TwilioListItem.vue')
import { Location, TwilioAccount } from '@/models'
import axios from 'axios'
import vSelect from 'vue-select'
import { mapState } from 'vuex'
declare var $: any

export default {
  data() {
    return {
      companyId: null,
      source: null,
      destination: null,
      phoneNumber: null,
      phoneNumbers: [],
      movingNumbers: false,
      errorMessage: null,
      locations: [],
      loading: {
        locations: false
      }
    }
  },
  components: { TwilioListItem, vSelect },
  computed: {
    accountsOptions() {
      let options = this.locations.filter(location => location !== this.source)
      options.unshift({ name: 'Master account', id: 'master' })
      return options
    }
  },
  props: {
    toggle: Boolean,
    twilioAccounts: {}
  },
  watch: {
    toggle: function(value) {
      $(this.$refs.modal).modal(value ? 'show' : 'hide')
    }
  },
  methods: {
    async getNumbers() {
      this.errorMessage = null

      this.phoneNumbers = []
      this.destination = null

      let sourceAccount
      if (this.source.id === 'master')
        sourceAccount = this.twilioAccounts.companyTwilioAccount
      else sourceAccount = lodash.find(this.twilioAccounts.locationTwilioAccounts, { 'location': { 'id': this.source.id }}).twilioAccount;

      if (sourceAccount.id) {
        let resp = await this.$http.get(
          '/twilio/get_inbound_numbers/' + sourceAccount.id
        )
        if (resp && resp.data && resp.data.incoming_phone_numbers) {
          this.phoneNumbers = resp.data.incoming_phone_numbers
        }
      }
    },
    async moveNumbersTwilio() {
      this.errorMessage = null

      const result = await this.$validator.validateAll()
      if (!result) {
        return false
      }
      if ((this.destination.id === 'master' && !this.twilioAccounts.companyTwilioAccount) || (this.destination.id !== 'master' && !lodash.find(this.twilioAccounts.locationTwilioAccounts, { 'location': { 'id': this.destination.id }}).twilioAccount.account_sid)) {
         this.errorMessage = 'Destination account do not have twilio integrated.';
         return false
      }

      this.movingNumbers = true;
      try {
        const data = {
          companyId: this.companyId,
          source: this.source.id,
          destination: this.destination.id,
          phoneNumberSID: this.phoneNumber.sid
        }
        const response = await this.$http.post('/twilio/move_number', data)
        console.log(response)
        this.closeModal()
      } catch (e) {
        if (e.response.status === 401) {
          this.errorMessage =
            "Twilio didn't authorize this number switch. Could you please check the twilio credentials?"
        } else if(e.response.status === 500) {
          this.errorMessage = e.response.data
            && e.response.data.msg
              ? e.response.data.msg : "Server encountered an error"
        } else {
          console.error(e)
          this.closeModal()
        }
      }

      this.movingNumbers = false
    },
    closeModal() {
      this.errorMessage = null
      this.source = null
      this.destination = null
      this.phoneNumber = null
      this.$emit('update:toggle', false)
    }
  },
  async mounted() {
    const company = await this.$store.dispatch('company/get')
    this.companyId = company.id

    this.loading.locations = true
    this.locations = await this.$store.dispatch('locations/getAll')
    this.loading.locations = false
  }
}
</script>
