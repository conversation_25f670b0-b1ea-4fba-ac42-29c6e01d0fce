<template>
  <transition name="modal">
    <div
      class="modal-mask"
      :class="{
        'modal-mask-bg-white': modalMaskBgColor == 'white',
        'modal-mask-default-bg': !modalMaskBgColor,
      }"
      @click="handleClickBackdrop"
    >
      <div class="modal-wrapper">
        <div
          class="modal-container"
          @click.stop
          :style="`max-width:${maxWidth}px;`"
        >
          <div
            :id="showCloseIcon"
            class="modal-close-icon"
            v-if="showCloseIcon || !title"
            @click="$emit('close')"
          >
            <i class="fa fa-times"></i>
          </div>
          <slot>
            <!-- Header - START -->
            <div class="modal-header">
              <div class="modal-header--inner">
                <h2 class="modal-title">{{ title }}</h2>
                <button
                  v-if="showCloseIcon"
                  tabindex="0"
                  type="button"
                  class="close modal-close-icon"
                  data-dismiss="modal"
                  aria-label="Close"
                  @click="$emit('close')"
                >
                  <span aria-hidden="true"></span>
                </button>
              </div>
            </div>
            <!-- Header - END -->

            <!-- Body - START -->
            <div class="modal-body pb-2">
              <div class="modal-body--inner">
                <slot name="body"> Pass Body content here </slot>
              </div>

              <!-- <div class="col-md-12 mt-3 mb-3"></div> -->
            </div>
            <!-- Body - END -->

            <!-- Footer - START -->
            <div class="modal-footer">
              <div class="modal-footer--inner nav">
                <slot name="footer">
                  <button
                    type="button"
                    class="btn btn-light2"
                    data-dismiss="modal"
                    @click="closeModal()"
                  >
                    Cancel
                  </button>
                  <div style="display: inline-block; position: relative">
                    <button
                      type="button"
                      class="btn btn-success"
                      @click.prevent="onSubmit"
                    >
                      Done
                    </button>
                  </div>
                </slot>
              </div>
            </div>
            <!-- Footer - END -->
          </slot>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
/* eslint-disable no-undef */
export default {
  props: [
    'maxWidth',
    'showCloseIcon',
    'noBackdropClose',
    'modalMaskBgColor',
    'title',
  ],
  methods: {
    handleClickBackdrop() {
      if (!this.noBackdropClose) {
        this.$emit('close')
      }
    },
  },
}
</script>

<style scoped>
.modal-mask {
  position: fixed;
  z-index: 1000;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: table;
  transition: opacity 0.3s ease;
}

.modal-wrapper {
  display: table-cell;
  vertical-align: middle;
}

.modal-container {
  width: 80%;
  max-width: 800px;
  margin: 0px auto;
  /* padding: 20px 30px; */
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.33);
  transition: all 0.3s ease;
  position: relative;
}

.modal-close-icon {
  position: absolute;
  top: 30px;
  right: 30px;
  cursor: pointer;
  font-size: 20px;
  line-height: 20px;
  color: #aaa;
  z-index: 5;
}
.modal-close-icon:hover {
  color: #777;
}

/*
 * The following styles are auto-applied to elements with
 * transition="modal" when their visibility is toggled
 * by Vue.js.
 *
 * You can easily play with the modal transition by editing
 * these styles.
 */

.modal-enter {
  opacity: 0;
}

.modal-leave-active {
  opacity: 0;
}

.modal-enter .modal-container,
.modal-leave-active .modal-container {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}
.modal-mask-default-bg {
  background-color: rgba(11, 11, 27, 0.5);
}
.modal-mask-bg-white {
  background-color: rgba(253, 253, 255, 0.9);
}
</style>
