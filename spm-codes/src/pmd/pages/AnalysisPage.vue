<template>
	<section class="hl_wrapper" :class="{'hl_analysis--padding': getSideBarVersion == 'v2'}">
		<section class="hl_wrapper--inner hl_online-analysis" id="online-analysis">
			<div class="container-fluid">
				<div v-show="location.yextId" class="card-group --wide-gutter">
					<div class="card hl_dashboard--sentiment">
						<div class="card-header">
							<h2>
								Listings
								<span></span>
							</h2>
							<select class="selectpicker more-select">
								<option>This Week</option>
								<option>Last Week</option>
								<option>This Month</option>
								<option>Last 6 Months</option>
								<option>This Year</option>
							</select>
						</div>
						<div class="card-body">
							<div class="row">
								<div class="col-sm-6">
									<highcharts
										:options="chartOptions"
										:callback="chartLoaded"
										style="height:100%;width:100%;position:absolute;padding-right:20px;"
									></highcharts>
								</div>
								<div class="col-sm-6">
									<div class="row" style="padding-top:20px;">
										<div class="col-sm-12" style="text-align:center">
											<h3 style="font-size: 3.125rem; font-weight:300;">{{totalCount}}</h3>
											<p>All Location Listings</p>
										</div>
									</div>
									<div class="row" style="padding-top:20px;text-align:center; ">
										<div class="col-sm-6" style="text-align:center ">
											<h4>{{liveCount}}</h4>
											<p>Live Listings</p>
										</div>
										<div class="col-sm-6" style="text-align:center ">
											<h4>{{processingCount}}</h4>
											<p>Processing</p>
										</div>
									</div>
									<div class="row" style="padding-top:20px;text-align:center; ">
										<div class="col-sm-6" style="text-align:center ">
											<h4>{{unavCount}}</h4>
											<p>Unavaliable</p>
										</div>
										<div class="col-sm-6" style="text-align:center ">
											<h4>{{optedCount}}</h4>
											<p>Opted Out</p>
										</div>
									</div>
									<div class="row" style="padding-top:20px;text-align:center;overflow: hidden;">
										<div
											class="col-sm-12"
											style="text-align:center;padding-left:0px;padding-right:0px;display:inherit;"
										>
											<!-- <div v-for="listing in listings" :key="listing.id" style="width: 67px;margin-right: 10px;">
                                                <div class="avatar --sm">
                                                    <div class="avatar_img --shadow">
                                                        <img v-bind:src="imgUrl(listing)" :alt="publisherTitle(listing)">
                                                    </div>

                                                </div>
                                                <div style="width: 60px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{{publisherTitle(listing)}}</div>
											</div>-->
											<div style="display:inline-block;padding-right:10px;">
												<MedallionGmbIcon
													style="width: 45px;"
												/>
												<div>Google My...</div>
											</div>
											<div style="display:inline-block;padding-right:10px; ">
												<MedallionAppleIcon style="width: 45px;" />
												<div>Apple</div>
											</div>
											<div style="display:inline-block; padding-right:10px;">
												<MedallionBingIcon style="width:45px;" />
												<div>Bing</div>
											</div>
											<div style="display:inline-block; padding-right:10px;">
												<MedallionFacebookIcon style="width:45px;" />
												<div>Facebook</div>
											</div>
											<div style="display:inline-block;padding-right:10px; ">
												<MedallionYelpIcon style="width:45px;" />
												<div>Yelp</div>
											</div>
											<div style="display:inline-block;padding-right:10px; ">
												<MedallionFoursquareIcon style="width:45px;" />
												<div>Foursquare</div>
											</div>
										</div>
									</div>
									<div class="row" style="padding-top:20px;text-align:center; ">
										<div class="col-sm-12" style="text-align:center;padding-left:0px;padding-right:0px">
											<div style="display:inline-block; padding-right:10px;">
												<MedallionSuperpagesIcon style="width:45px;" />
												<div>Superpages</div>
											</div>
											<div style="display:inline-block;padding-right:10px; ">
												<MedallionYahooIcon style="width:45px;" />
												<div>Yahoo!</div>
											</div>
											<div style="display:inline-block; padding-right:10px;">
												<MedallionCitysearch style="width:45px;" />
												<div>Citysearch</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div v-if="!location.yextId" class="card-group">
					<div class="card hl_online-analysis--online-presence-errors">
						<div class="card-header">
							<h2>Online Presence Errors</h2>
						</div>
						<div class="card-body">
							<div class="card-content">
								<div class="card-heading">
									<!-- <img src="https://www.yextstatic.com/partner/public/images/powerlistings-synced.svg"> -->
									<h3>{{yext_error_rate}}</h3>
									<div class="hl_online-analysis--badge --warning">
										<i class="icon icon-warning"></i>
									</div>
								</div>
								<div class="hl_online-analysis--network">
									<h4>Network Errors</h4>
									<ul class="network_list list-inline">
										<li
											class="list-inline-item"
											v-for="site_error in yext_first_three"
											v-bind:key="site_error[0]"
										>
											<img style="height:35px;width:35px;" :src="site_error[0]">
										</li>
									</ul>
								</div>
							</div>
						</div>
					</div>
					<div class="card hl_online-analysis--page-speed-score">
						<div class="card-header">
							<h2>Page Speed Score</h2>
						</div>
						<div class="card-body">
							<div class="card-content">
								<div class="card-heading">
									<h3>
										<div>{{speed_score | formatNumber}}</div>
									</h3>
									<div class="avatar --sm">
										<div class="avatar_img --shadow">
											<img src="../../assets/pmd/img/logo-google.png" alt="Google">
										</div>
									</div>
								</div>
								<div class="progress">
									<div
										v-if="location"
										:class="progress_bar_class"
										role="progressbar"
										v-bind:style="{ width: speed_score + '%'}"
										:aria-valuenow="speed_score"
										aria-valuemin="0"
										aria-valuemax="100"
									></div>
								</div>
								<a
									style="word-break:break-all;"
									v-if="location"
									:href="location.website"
									target="_blank"
									class="link"
								>{{website_link}}</a>
							</div>
						</div>
					</div>
					<div class="card hl_online-analysis--competition-leaderboard">
						<div class="card-header">
							<h2>Competition Leaderboard</h2>
						</div>
						<div class="card-body">
							<div class="card-content">
								<div class="card-heading">
									<h3>
										<div v-if="scanReport">
											{{yext_scan.reviewScorePercentile}}
											<span>/{{yext_scan.reviewCountPercentile}}</span>
										</div>
									</h3>
									<div class="hl_online-analysis--badge --star">
										<i class="icon icon-star-filled"></i>
									</div>
								</div>
								<p
									class="search"
									v-if="location && location.searchTerm"
								>{{location.searchTerm | toTitleCase}}</p>
							</div>
						</div>
					</div>
				</div>
				<div class="card hl_online-analysis--table">
					<div class="card-body --no-padding">
						<div class="table-wrap">
							<table class="table table-sort">
								<thead>
									<tr>
										<th data-sort="string">
											Network
											<!-- <i class="icon icon-arrow-down-1"></i> -->
										</th>
										<th data-sort="string">
											Name
											<!-- <i class="icon icon-arrow-down-1"></i> -->
										</th>
										<th data-sort="string">
											Address
											<!-- <i class="icon icon-arrow-down-1"></i> -->
										</th>
										<th data-sort="string">
											Phone
											<!-- <i class="icon icon-arrow-down-1"></i> -->
										</th>
										<th data-sort="float">
											Reviews
											<!-- <i class="icon icon-arrow-down-1"></i> -->
										</th>
										<th>No.</th>
										<th data-sort="string">
											Status
											<!-- <i class="icon icon-arrow-down-1"></i> -->
										</th>
										<th>
											<!-- View Profile -->
										</th>
									</tr>
								</thead>
								<tbody>
									<AnalysisListingCard
										v-if="!listings"
										v-for="report in yextData"
										v-bind:report="report"
										v-bind:key="report.brandId"
										v-bind:location="location"
									/>
									<AnalysisListingCard
										v-if="listings"
										v-for="listing in listings"
										v-bind:yextReport="listing"
										v-bind:key="listing.publisherId"
										v-bind:location="location"
									/>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</section>
		<!-- END of .online-analysis -->
	</section>
</template>

<script lang="ts">
import Vue from 'vue';
import { Location, Company, ScanReport, User } from '@/models';
import ListingHelper from '../../util/listing_helper';
import { Chart } from 'highcharts-vue'
import MedallionGmbIcon from '/public/pmd/img/logo_medallions/medallion-google-my-business.svg'
import MedallionAppleIcon from '/public/pmd/img/logo_medallions/medallion-apple.svg'
import MedallionBingIcon from '/public/pmd/img/logo_medallions/medallion-bing.svg'
import MedallionFacebookIcon from '/public/pmd/img/logo_medallions/medallion-facebook.svg'
import MedallionYelpIcon from '/public/pmd/img/logo_medallions/medallion-yelp.svg'
import MedallionFoursquareIcon from '/public/pmd/img/logo_medallions/medallion-foursquare.svg'
import MedallionSuperpagesIcon from '/public/pmd/img/logo_medallions/medallion-superpages.svg' 
import MedallionYahooIcon from '/public/pmd/img/logo_medallions/medallion-yahoo.svg'
import MedallionCitysearch from '/public/pmd/img/logo_medallions/medallion-citysearch.svg'

const AnalysisListingCard = () => import('../components/AnalysisListingCard.vue');

let location: Location | undefined;
let totalCountGraph = 0;
export default Vue.extend({
	components: {
		AnalysisListingCard,
		highcharts: Chart,
		MedallionGmbIcon,
		MedallionAppleIcon,
		MedallionBingIcon,
		MedallionFacebookIcon,
		MedallionYelpIcon,
		MedallionFoursquareIcon,
		MedallionSuperpagesIcon,
		MedallionYahooIcon,
		MedallionCitysearch,
	},
	data() {
		return {
			currentLocationId: '',
			location: {} as Location,
			scanReport: {} as ScanReport | undefined,
			chartOptions: {
				chart: {
					type: "pie",
					plotBackgroundColor: null
				},
				credits: {
					enabled: !1
				},
				title: {
					text: ""
				},
				plotOptions: {
					pie: {
						allowPointSelect: !0,
						cursor: "pointer",
						center: ["50%", "50%"],
						innerSize: "70%",
						startAngle: 180,
						dataLabels: {
							style: {
								fontWeight: "normal"
							},
							formatter: function ()  {
								return this.point.hideLabel ? null : "\x3cb\x3e" + this.point.name + "\x3c/b\x3e " + (((this.point.labelValue || this.point.y) / totalCountGraph)*100).toFixed(2) + "%";
							}
						},
						showInLegend: !0
					}
				},
				legend: {
					symbolRadius: 3,
					margin: 30,
					squareSymbol: !0
				},
				series: [{
					type: "pie",
					name: "Listings"
				}],
				tooltip: {
					valueSuffix: "%"
				}
			},
			theChart: {},
			totalCount: 0,
			liveCount: 0,
			processingCount: 0,
			unavCount: 0,
			optedCount: 0,
			listings: [] as { [key: string]: any }[] | undefined,
		};
	},
	async beforeRouteUpdate(to, from, next) {
		console.log("Going to:", to)
		console.log("Coming from:", from)
		this.currentLocationId = to.params.location_id;
		await this.$store.dispatch('reviewAggregate/reset');
		await this.$store.dispatch('reviewRequestAggregate/reset');
		if (this.currentLocationId) {
			this.$store.dispatch('reviewAggregate/fetchThisMonth', this.currentLocationId);
			this.$store.dispatch('reviewRequestAggregate/fetchThisMonth', this.currentLocationId);
			this.fetchLocationData();
		}
		next();
	},
	watch: {
		listings() {
			if (this.theChart) {
				this.populateChart();
			}
		},
		theChart() {
			if (this.theChart) {
				this.populateChart()
			}
		}
	},
	async created() {
		console.log("Created")
		this.currentLocationId = this.$router.currentRoute.params.location_id;

	},
	async mounted() {
		console.log("Mounted:", this.location)
		await this.fetchLocationData();
	},
	computed: {
		yext_scan(): { [key: string]: any } {
			if (this.scanReport && this.scanReport.yextData) {
				return JSON.parse(this.scanReport.yextData);
			} else {
				return {};
			}
		},
		yextData(): { [key: string]: any } {
			if (this.yext_scan && this.yext_scan.reports) {
				return this.yext_scan.reports;
			} else {
				return [];
			}
		},
		yext_errors(): any[] {
			var _self = this;
			var errors = [] as any;
			if (this.yextData) {
				lodash.each(this.yextData, function (report) {
					var a = [];
					a.push(_self.imgUrl(report));
					if (!report.listingFound) {
						return a.push('listingNotFound'), a;
					}
					report.matchAddress || a.push('address');
					report.matchName || a.push('name');
					report.matchPhone || a.push('phone');
					if (a.length > 1) {
						errors.push(a);
					}
				});
			}
			return errors;
		},
		yext_error_rate() {
			var error_rate = 0;
			if (this.yext_errors && this.yextData && this.yextData.length > 0) {
				error_rate = Math.floor(this.yext_errors.length / this.yextData.length * 100);
			}
			return error_rate;
		},
		yext_first_three() {
			if (this.yext_errors) {
				return this.yext_errors.slice(0, 3);
			}
		},
		lighthouse_scan(): { [key: string]: any } {
			if (this.scanReport && this.scanReport.lighthouseData) {
				return JSON.parse(this.scanReport.lighthouseData);
			} else {
				return {};
			}
		},
		speed_score() {
			if (
				this.lighthouse_scan &&
				this.lighthouse_scan.audits &&
				this.lighthouse_scan.audits['speed-index-metric']
			) {
				return this.lighthouse_scan.audits['speed-index-metric']['score'];
			} else {
				return 0;
			}
		},
		progress_bar_class: function () {
			if (this.speed_score && this.speed_score <= 35) {
				return {
					'progress-bar': true,
					'bg-danger': true,
				};
			} else if (this.speed_score && this.speed_score > 35 && this.speed_score <= 85) {
				return {
					'progress-bar': true,
					'bg-warning': true,
				};
			} else if (this.speed_score && this.speed_score > 85) {
				return {
					'progress-bar': true,
					'bg-success': true,
				};
			} else {
				return {
					'progress-bar': true,
				};
			}
		},
		website_link(): string | undefined {
			if (this.location && this.location.website) {
				return this.location.website.slice(this.location.website.indexOf('http://www.') != -1 ? 11 : 0);
			}
		},
		user() {
			const user = this.$store.state.user.user
			return user ? new User(user) : undefined
		},
		getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
	},

	beforeDestroy() {
		if (this.$private.cancelBusinessListingSubscription) {
			this.$private.cancelBusinessListingSubscription();
		}
	},
	methods: {
		chartLoaded(chart: any) {
			console.log("Chart:", chart)
			this.theChart = chart;
		},
		async fetchLocationData() {
			this.location = new Location(await this.$store.dispatch('locations/getById', this.currentLocationId));
			console.log("Got location:", this.location.name)
			try {
				this.scanReport = await ScanReport.getByLocationId(this.currentLocationId);
				console.log("Got scanReport:", this.scanReport)
			} catch (err) {
				console.error('No scan report found!');
				this.scanReport = undefined;
			}
			this.getYextData();
		},

		imgUrl(report: any): string | undefined {
			if (report && report.brandId) {
				const listing = ListingHelper.getNetworkInfo(report.brandId);
				if (listing) return listing.imgPath;
			}
		},
		async getYextData() {
			if (this.location.yextId) {
				const response = await this.$http.get('api/yext/' + this.currentLocationId + '/listings');
				console.log("Got Response:", response)
				let _self = this;

				if (response.status === 200) {
					this.listings = response.data;



				}
			} else {
				this.listings = undefined;
			}
		},
		populateChart() {
			var _self = this;

			this.theChart.series[0].setData([]);

			if (this.listings && this.listings.length > 0) {


        this.totalCount = this.listings.length;
        totalCountGraph = this.totalCount;

				for (let key in this.listings) {
					var listing = this.listings[key];
					if (!listing.status) continue;
					if (listing.status == "LIVE") {
						_self.liveCount += 1;
					} else if (listing.status == "UNAVAILABLE") {
						_self.unavCount += 1;
					} else if (listing.status == "OPTED_OUT") {
						_self.optedCount += 1;
					} else if (listing.status.indexOf("WAITING") > -1) {
						_self.processingCount += 1;
					}
				}



				this.theChart.series[0].addPoint({
					"color": "#178ACD",
					"x": 0,
					"y": _self.liveCount,
					"name": "Live"
				});

				this.theChart.series[0].addPoint({
					"color": "#23D2BE",
					"x": 1,
					"y": _self.processingCount,
					"name": "Processing"
				});

				this.theChart.series[0].addPoint({
					"color": "#F2326B",
					"x": 2,
					"y": _self.optedCount,
					"name": "Opted Out"
				});

				this.theChart.series[0].addPoint({
					"color": "#FDC131",
					"x": 3,
					"y": _self.unavCount,
					"name": "Unavailable"
				});

			}
		}
	},
});
</script>
