<template>
  <div
    class="modal fade hl_sms-template--modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Add Eliza</h2>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div class="form-group">
              <label>Eliza</label>
              <vSelect
                :options="availableBots"
                label="title"
                v-model="selectedBots"
                :clearable="false"
                v-validate="'required'"
                name="eliza"
              ></vSelect>
              <span v-show="errors.has('eliza')" class="error">Eliza is required.</span>
            </div>
            <div class="form-group">
              <label>Logical Name</label>
              <input
                type="text"
                class="form-control"
                placeholder="Enter a logical name"
                v-model="logicalName"
                v-validate="'required'"
                name="logicalName"
              />
              <span v-show="errors.has('logicalName')" class="error">Logical name is required.</span>
            </div>
            <div v-if="error" class="help --red" style="text-align: center;">
              {{ error }}
            </div>

          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner nav">
            <button type="button" class="btn btn-light2" data-dismiss="modal">
              Cancel
            </button>
            <div style="display: inline-block;position: relative;">
              <button
                :class="{ invisible: saving }"
                type="button"
                class="btn btn-success"
                @click.prevent="addLogicalEliza"
              >
                Add
              </button>
              <div
                style="position: absolute;left: 50%;transform: translate(-50%, -50%);top: 50%;"
                v-show="saving"
              >
                <moon-loader :loading="saving" color="#37ca37" size="30px" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import * as moment from 'moment-timezone'
import vSelect from 'vue-select'
import { mapState } from "vuex";
import { cloneDeep } from 'lodash';
import { DefaultElizaFAQ } from '@/util/default_eliza_faq';
import { GenericDefaultElizaFAQ } from '@/util/generic_default_eliza_faq';

import {
  Location,
  Contact,
  LogicalEliza
} from '@/models'

declare var $: any

export default Vue.extend({
  props: ['values'],
  components: {
    vSelect
  },
  data() {
    return {
      currentLocationId: '',
      selectedBots: undefined,
      saving: false,
      contact: {} as Contact,
      availableBots: [] as { [key: string]: any }[],
      error: '',
      logicalName: '',
    }
  },
  created(){
    this.currentLocationId = this.$router.currentRoute.params.location_id;
    this.fetchData();
  },
  methods: {
    async fetchData() {
      this.currentLocationId = this.$router.currentRoute.params.location_id;
        this.$http.get('/dialogflow/agents/' + this.currentLocationId)
        .then(res => {
          if (res && res.data) {
            this.availableBots = res.data;
          }
        }).catch(err => {
          console.log(err);
        });
    },
    async addLogicalEliza() {
      this.error = ''
      const result = await this.$validator.validateAll()
      if (!result || !this.selectedBots || !this.logicalName)
        return false

      this.saving = true
      try {
        // Add Eliza
        if(this.selectedBots){
          let logicalEliza = new LogicalEliza();
          logicalEliza.locationId = this.currentLocationId;
          logicalEliza.elizaId = this.selectedBots.projectId;
          logicalEliza.logicalName = this.logicalName;
          logicalEliza.faq = this.selectedBots.title.toLowerCase().includes('generic') ? cloneDeep(GenericDefaultElizaFAQ) : cloneDeep(DefaultElizaFAQ);
          await logicalEliza.save()
          this.$emit('saved', logicalEliza.id);
        }
        this.$emit('hidden')
      } catch (err) {
        this.saving = false
        console.error('Error adding eliza:', err)
        this.error = lodash.get(err, 'response.data.msg', 'Bad request')
      }
    }
  },
  watch: {
    values(values: { [key: string]: any }) {
      const data: () => object = <() => object>this.$options.data
      if (data) Object.assign(this.$data, data.apply(this))
      if (values.visible) $(this.$refs.modal).modal('show')
      else $(this.$refs.modal).modal('hide')
      if (values.visible) {
        this.contact = values.contact
        this.fetchData();
      }
    },
  },
  updated() {
    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hidden')
    })

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  }
})
</script>
