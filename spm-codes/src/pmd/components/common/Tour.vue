<template>
  <div class="tour" id="tour" v-if="tour">
    <div id="tour-card" v-if="step">
      <div v-if="step.skip" class="tour-skip-action" @click.once="goToDashboard">
        {{step.skipLabel}}
        <i style="margin-left: 8px" class="caret icon-arrow-right-1"></i>
      </div>
      <div id="tour-card-arrow"></div>
      <p class="tour-card-title">{{ step.title }}</p>
      <p class="tour-card-body">{{ step.body }}</p>
      <div
        class="tour-card-footer"
        :class="{
          'flex-item-center': !step.showCount,
        }"
      >
        <p
          v-if="step.showCount"
          class="tour-card-footer-text"
          :class="{
            'display-inline': step.showCount,
            'float-left': step.showCount,
          }"
        >
          {{ step.currentStep }} of {{ step.totalStep }}
        </p>
        <div
          class="action"
          @click.prevent="next"
          :class="{
            'display-inline': step.showCount,
            'float-right': step.showCount,
          }"
        >
          {{ step.next }}
        </div>
        <div
          v-if="step.prev != ''"
          class="action mr-8"
          @click.prevent="prev"
          :class="{
            'display-inline': step.showCount,
            'float-right': step.showCount,
          }"
          style="margin-right: 20px"
        >
          {{ step.prev }}
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  name: 'Tour',
  props: ['steps', 'options'],
  data() {
    return {
      tour: false as boolean,
      step: {} as any,
      currentStep: 0 as number,
      displayStep: 0 as number,
    }
  },
  methods: {
    async showOverlay() {
      this.tour = true
    },
    async start() {
      if (this.steps.length > 0) {
        this.step = this.steps[this.currentStep]
        await this.setCardPostion(this.step.element, this.step.position)
        this.tour = true
      }
    },
    async next() {
      await this.hideCurrentCard()
      if (this.currentStep < this.steps.length - 1) {
        this.currentStep += 1
        this.step = this.steps[this.currentStep]
        await this.setCardPostion(this.step.element, this.step.position)
        this.tour = true
      } else {
        this.tour = false
        this.$root.$emit('tour-end')
      }
    },
    async prev() {
      await this.hideCurrentCard()
      if (this.currentStep > 0) {
        this.currentStep -= 1
        this.step = this.steps[this.currentStep]
        await this.setCardPostion(this.step.element, this.step.position)
        this.tour = true
      }
    },
    async hideCurrentCard() {
      const ele = document.getElementById('tour-card')
      if (ele) ele.classList.remove('show-tour-card')
      this.resetPosition()
    },
    async setCardPostion(element: string, position: string) {
      const interval = setInterval(() => {
        const ele = document.querySelectorAll(element)[0]
        if (ele) {
          clearInterval(interval)
          const offset = {
            left: ele.offsetLeft,
            top: ele.offsetTop,
            right: ele.offsetLeft + ele.offsetWidth,
            bottom: ele.offsetTop + ele.offsetHeight,
          }
          if (typeof this.step.position === 'function') {
            this.step.position()
          } else {
            this.cardPositions(offset, position, ele)
          }
          return true
        }
      }, 200)
    },
    cardPositions(offset: any, position: string, element: HTMLElement) {
      const ele = document.getElementById('tour-card')
      if (ele) {
        switch (position) {
          case 'left':
            ele.style.left = offset.left - 22 + 'px'
            ele.style.top = offset.top + 'px'
            ele.classList.add('show-tour-card')
            break
          case 'right':
            ele.style.left = offset.right + 'px'
            ele.style.top = offset.top - 20 + 'px'
            ele.classList.add('show-tour-card')
            break
          case 'top':
            ele.style.bottom = Math.abs(offset.top + ele.offsetHeight) + 'px'
            ele.style.right =
              element.offsetWidth - Math.abs(ele.offsetWidth / 2) + 'px'
            ele.classList.add('show-tour-card')
            break
          case 'bottom':
            ele.style.top = offset.bottom + 'px'
            ele.style.left = offset.left + 'px'
            ele.classList.add('show-tour-card')
            break
        }
        this.arrowPosition(ele, position)
      }
    },
    arrowPosition(card: HTMLElement, position: string) {
      const ele = document.getElementById('tour-card-arrow')
      const offset = {
        left: card.offsetLeft,
        top: card.offsetTop,
        right: card.offsetLeft + card.offsetWidth,
        bottom: card.offsetTop + card.offsetHeight,
      }
      if (ele) {
        switch (position) {
          case 'left':
            ele?.classList.add('tour-card-arrow-right')
            ele.style.top = card.offsetHeight / 2 - 12 + 'px'
            break
          case 'right':
            ele?.classList.add('tour-card-arrow-left')
            ele.style.top = card.offsetHeight / 2 - 12 + 'px'
            break
          case 'top':
            ele?.classList.add('tour-card-arrow-bottom')
            ele.style.left = card.offsetWidth / 2 - 12 + 'px'
            break
          case 'bottom':
            ele?.classList.add('tour-card-arrow-top')
            ele.style.left = card.offsetWidth / 2 - 12 + 'px'
            break
        }
      }
    },
    resetPosition() {
      const card = document.getElementById('tour-card')
      const arrow = document.getElementById('tour-card-arrow')
      if (card) {
        card.style.removeProperty('left')
        card.style.removeProperty('right')
        card.style.removeProperty('top')
        card.style.removeProperty('bottom')
      }
      if (arrow) {
        arrow.style.removeProperty('left')
        arrow.style.removeProperty('right')
        arrow.style.removeProperty('top')
        arrow.style.removeProperty('bottom')
        arrow.removeAttribute('class')
      }
    },
    goToDashboard() {
      this.$root.$emit('redirect')
    }
  },
})
</script>
<style scoped>
#tour {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 20;
  background-color: rgba(0, 0, 0, 0.5);
  top: 0px;
  bottom: 0px;
  left: 0px;
  right: 0px;
}
#tour-card {
  position: absolute;
  max-width: 300px;
  min-height: 30px;
  background-color: #ffffff;
  border-radius: 3px;
  box-shadow: 1px 1px 11px #06060626;
  display: flex;
  flex-direction: column;
  padding: 12px;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0s linear 0.33s;
}
#tour-card .tour-card-title {
  font-size: 14px;
  font-weight: 500 !important;
  color: #4a5568;
  text-align: left;
}
#tour-card .tour-card-body {
  color: #4a5568;
  font-size: 12px;
  text-align: left;
}
#tour-card .tour-card-footer {
  margin-top: 15px;
  width: 100%;
  position: relative;
  font-size: 12px;
}
#tour-card .tour-card-footer-text {
  font-weight: 500 !important;
  color: #4a5568;
}
#tour-card .action {
  color: #3b82f6;
  cursor: pointer;
  font-weight: 500;
  text-transform: uppercase;
}
#tour-card .display-inline {
  display: inline-block;
}
#tour-card .float-left {
  float: left;
}
#tour-card .float-right {
  float: right;
}
#tour-card .flex-row {
  display: flex;
  flex-direction: row;
}
#tour-card .flex-item-center {
  justify-content: center;
  justify-items: center;
  align-items: center;
  display: flex;
}
.show-tour-card {
  visibility: visible !important;
  opacity: 1 !important;
  transition-delay: 0s;
}
#tour-card-arrow {
  width: 0;
  height: 0;
  position: absolute;
}
.tour-card-arrow-left {
  border-top: 10px solid transparent;
  border-right: 10px solid white;
  border-bottom: 10px solid transparent;
  left: -7px;
}
.tour-card-arrow-right {
  border-top: 10px solid transparent;
  border-left: 10px solid #ffffff;
  border-bottom: 10px solid transparent;
  right: -7px;
}
.tour-card-arrow-top {
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid white;
  top: -7px;
}
.tour-card-arrow-bottom {
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 10px solid white;
  bottom: -7px;
}
#tour-card .tour-skip-action {
  color: #ffffff;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  background: #1A202C;
  border-radius: 37px;
  padding: 5px 20px;
  position: absolute;
  top: -35px;
  font-size: 10px;
  width: 95%;
  cursor: pointer;
}
</style>
