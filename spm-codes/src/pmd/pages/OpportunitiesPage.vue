<template>
  <section class="hl_wrapper">
    <section class="hl_wrapper--inner opportunities" id="opportunities">
      <div class="container-fluid">
        <div class="hl_controls">
          <div class="hl_controls--left">
            <div class="d-flex" style="width: 270px;">
              <vue-ctk-date-time-picker
                style="background-color: white"
                :locale="getCountryInfo('locale')"
                v-model="opportunitiesFiltersDates"
                :range="true"
                :custom-shortcuts="customShortcuts"
                color="#188bf6"
                :only-date="true"
                enable-button-validate
                :noClearButton="false"
                name="start_time"
                @validate="updateOpportunitiesFilters"
                :formatted="getCountryDateFormat('month date year')"
              />
            </div>
          </div>
          <div class="hl_controls--right">
            <!-- <div class="d-flex align-items-center" style="width: 250px;">
              <vue-ctk-date-time-picker
                :locale="getCountryInfo('locale')"
                v-model="opportunitiesFiltersDates"
                :range="true"
                color="#188bf6"
                :only-date="true"
                enable-button-validate
                :noClearButton="false"
                name="start_time"
                @validate="updateOpportunitiesFilters"
                :formatted="getCountryDateFormat(false)"
              />
            </div> -->

            <select
              class="selectpicker"
              title="OrderBy"
              data-width="fit"
              v-model="filters.orderBy"
            >
              <option value="added_desc">
                <i class="fas fa-sort-amount-down hint-icon"></i>Date Added
                (DESC)
              </option>
              <option value="added_asc">
                <i class="fas fa-sort-amount-up hint-icon"></i>Date Added (ASC)
              </option>
              <!-- <option value="contact_name">Contact Name</option> -->
            </select>

            <select
              class="selectpicker"
              title="Pipeline"
              data-width="fit"
              v-model="filters.pipelineId"
            >
              <option
                v-for="pipeline in pipelines"
                :value="pipeline.id"
                :key="pipeline.id"
                >{{ pipeline.name }}</option
              >
            </select>

            <div v-if="accessAll">
              <select
                class="selectpicker"
                title="Owner"
                data-width="fit"
                v-model="filters.user"
                name="filterUser"
              >
                <option value>All</option>
                <option v-for="user in users" :value="user.id" :key="user.id">{{
                  user.fullName
                }}</option>
              </select>
            </div>

            <select
              class="selectpicker"
              title="Campaign"
              data-width="fit"
              v-model="filters.campaignId"
              name="filterCampaign"
            >
              <option value>All</option>
              <option
                v-for="campaign in campaigns"
                :value="campaign.id"
                :key="campaign.id"
                >{{ campaign.name }}</option
              >
            </select>
            <select
              class="selectpicker"
              data-width="fit"
              v-model="filters.status"
              title="Status"
            >
              <option value="all">All</option>
              <option value="open">Open</option>
              <option value="won">Won</option>
              <option value="lost">Lost</option>
              <option value="abandoned">Abandoned</option>
            </select>
            <div class="search-form" style="min-width: 250px;">
              <UITextInputGroup
                type="text"
                icon="icon-loupe"
                class="form-light"
                placeholder="Search"
                v-model.trim="filters.text"
              />
              <div style="position: absolute;top: 33%;right: 10px;">
                <moon-loader :loading="isLoading" color="#1ca7ff" size="15px" />
              </div>
            </div>
            <UIButton
              use="primary"
              type="button"
              @click.prevent="addOpportunity"
            >
              <i class="icon icon-plus mr-2"></i> New
            </UIButton>
            <button
              v-if="isAdmin"
              :class="{ invisible: downloadLoading }"
              class="btn btn-sm btn-primary"
              type="button"
              @click.prevent="exportOpportunities"
            >
              <i class="icon icon-download"></i>
            </button>
            <div v-show="downloadLoading">
              <moon-loader
                :loading="downloadLoading"
                color="#1ca7ff"
                size="15px"
              />
            </div>

            <b-dropdown
              id="dropdown-columns"
              variant="primary"
              right
              text="C"
              ref="dropdown"
              class="m-2 mb-3"
            >
              <template v-slot:button-content>
                <i class="fas fa-columns"></i>
              </template>

              <h5 class="ml-2">Additional Info</h5>

              <b-form-checkbox-group
                v-model="filters.columns"
                :options="columOptions"
                name="flavour-2a"
                class="ml-2"
                stacked
              ></b-form-checkbox-group>
            </b-dropdown>
          </div>
        </div>
        <OpportunitiesModal
          :values="opportunityModalValues"
          @hidden="opportunityModalValues = { visible: false }"
          :users="users"
        />
        <div class="hl_opportunities">
          <div class="row flex-row flex-nowrap scroll-area" v-if="!isLoading">
            <OpportunitiesList
              :color="colors[index]"
              :filters="filters"
              v-for="(stage, index) in stages"
              :stage="stage"
              :bus="bus"
              :key="stage.id"
              @click="editOpportunity"
              @clickAppointment="editOpportunityAppointment"
              @clickTask="editOpportunityTask"
              @clickNotes="editOpportunityNotes"
              @dragStart="dragStart"
              @dragEnd="dragEnd"
            />
          </div>
          <div v-else class="hl_opportunities--placeholder">
            <moon-loader color="#1ca7ff" size="30px" />
            <!-- <GridLoader color="#37ca37" size="15px" /> -->
          </div>

          <div
            :class="[
              'hl_opportunities--drag-actions active',
              dragging ? 'visible' : null
            ]"
          >
            <div class="hl_opportunities--drag-actions-inner">
              <Container
                :group-name="'opps'"
                @drop="e => onDrop('lost', e)"
                :behaviour="'drop-zone'"
                @drag-enter="e => onDragEnter('lost')"
                @drag-leave="e => onDragLeave('lost')"
                :class="{
                  'drag-action lo --lost': true,
                  'lost-hover': hoverLost
                }"
              >
                <h4>Lost</h4>
              </Container>
              <Container
                :group-name="'opps'"
                @drop="e => onDrop('abandoned', e)"
                :behaviour="'drop-zone'"
                @drag-enter="e => onDragEnter('abandoned')"
                @drag-leave="e => onDragLeave('abandoned')"
                :class="{
                  'drag-action --abandoned': true,
                  'abandoned-hover': hoverAbandoned
                }"
              >
                <h4>Abandoned</h4>
              </Container>
              <Container
                :group-name="'opps'"
                @drop="e => onDrop('won', e)"
                :behaviour="'drop-zone'"
                @drag-enter="e => onDragEnter('won')"
                @drag-leave="e => onDragLeave('won')"
                :class="{ 'drag-action --won': true, 'won-hover': hoverWon }"
              >
                <h4>Won</h4>
              </Container>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- END of .hl_marketing -->
  </section>
  <!-- END of .hl_wrapper -->
</template>

<script lang="ts">
import Vue from 'vue'
import * as json2csv from 'json2csv'
import config from '../../config'
import download from 'downloadjs'
import Datepicker from 'vuejs-datepicker'
import moment from 'moment-timezone'
import { Container, Draggable } from 'vue-smooth-dnd'

import { mapState } from 'vuex'
import {
  Contact,
  Location,
  User,
  Campaign,
  CampaignStatus,
  Stage,
  Pipeline,
  Opportunity,
  Note,
  getCountryDateFormat,
  getCountryInfo
} from '@/models'
import { UserState } from '../../../store/state_models'
// import GridLoader from 'vue-spinner/src/GridLoader.vue'
// import HLSeeder from './../../util/HLSeeder'
const bus = new Vue()

const OpportunityCard = () =>
  import('../components/opportunities/OpportunityCard.vue')
const OpportunitiesModal = () =>
  import('../components/opportunities/OpportunitiesModal.vue')
const OpportunitiesList = () =>
  import('../components/opportunities/OpportunitiesList.vue')

const store = require('store')
const Json2csvParser = json2csv.Parser
let searchDebouncer: any

export default Vue.extend({
  name: 'LocationOpportunitiesPage',
  components: {
    OpportunityCard,
    OpportunitiesModal,
    OpportunitiesList,
    Container,
    Draggable,
    Datepicker
    // GridLoader
  },
  data() {
    return {
      currentLocation: {} as Location,
      bus,
      getCountryInfo,
      opportunitiesFiltersDates: {
        start: null,
        end: null
      },
      customShortcuts: [
        {
          key: 'customValue',
          label: 'All Time',
          value: () => {
            return {
              // start: moment(new Date(2016, 0, 1)),
              start: this.pipelineCreatedDate,
              end: moment()
            }
          }
        },

        { key: 'thisWeek', label: 'This week', value: 'isoWeek' },
        { key: 'lastWeek', label: 'Last week', value: '-isoWeek' },
        { key: 'last7Days', label: 'Last 7 days', value: 7 },
        { key: 'last30Days', label: 'Last 30 days', value: 30 },
        { key: 'thisMonth', label: 'This month', value: 'month' },
        { key: 'lastMonth', label: 'Last month', value: '-month' },
        { key: 'thisYear', label: 'This year', value: 'year' },
        { key: 'lastYear', label: 'Last year', value: '-year' }
      ],
      users: [] as User[],
      pipelineCreatedDate: '',
      filters: {
        date: null,
        endDate: null,
        status: 'open',
        campaignId: '',
        user: '',
        text: '',
        pipelineId: '',
        orderBy: 'added_desc',
        columns: []
      },
      sqlCalling: 0,
      currentLocationId: '',
      opportunities: {} as any,
      opportunityModalValues: {
        visible: false,
        opportunity: undefined as Opportunity | undefined,
        currentLocationId: '',
        tab: '',
        pipeline: undefined as Pipeline | undefined
      },
      dragging: false,
      hoverLost: false,
      hoverWon: false,
      hoverAbandoned: false,
      colors: [
        '#F5AF29',
        '#0088B9',
        '#9A89B5',
        '#A6B12E',
        '#407887',
        '#9A89B5',
        '#5A8770',
        '#D33F33',
        '#A2B01F',
        '#F0B126',
        '#0087BF',
        '#F18636',
        '#0087BF',
        '#B2B7BB',
        '#72ACAE',
        '#9C8AB4',
        '#5A8770',
        '#EEB424',
        '#407887'
      ],
      downloadLoading: false as boolean,
      loadingProcess: 0,
      getCountryDateFormat: getCountryDateFormat,
      columOptions: [
        { text: 'Task', value: 'task' },
        { text: 'Calendar Event', value: 'calendar_event' },
        { text: 'Note', value: 'note' },
        { text: 'Company Name', value: 'company_name' },
        { text: 'Hide Tags', value: 'hide_tags' },
        { text: 'Hide Assigned To', value: 'hide_assigned_to' }
      ]
    }
  },
  watch: {
    '$route.params.location_id': async function(id) {
      await this.fetchSyncAll()
      this.setFilters()
      await this.refreshLocationData()
    },
    filters: {
      handler: function(filters, oldVal) {
        this.$store.commit('filters/addUpdateChild', {
          type: 'opportunities',
          filters: Object.assign(
            {
              currentLocationId: this.currentLocationId
            },
            filters
          )
        })
        if (searchDebouncer) clearTimeout(searchDebouncer)
        searchDebouncer = setTimeout(() => {
          this.$store.dispatch('opportunities/syncAll', this.currentLocationId)
        }, 500)
        // this.fetchOpportunities()
        // store.set(`pipeline-${this.currentLocationId}`, {
        //   id: this.filters.pipelineId,
        //   orderBy: this.filters.orderBy,
        //   columns: this.filters.columns
        // })
      },
      deep: true
    },
    'filters.pipelineId': function (val, oldVal) {
        if (val !== oldVal) this.getPipeline()
    },
    user() {
      if (!this.accessAll && this.user) {
        this.filters.user = this.user.id
      }
    },
    accessAll() {
      if (!this.accessAll && this.user) {
        this.filters.user = this.user.id
      } else {
        this.filters.user = undefined
      }
    },
    opportunitiesFiltersDates: {
      handler(val) {
        if (val === null) {
          this.filters = {
            ...this.filters,
            ...{
              date: null,
              endDate: null
            }
          }
        }
      },
      deep: true
    }
  },
  computed: {
    isLoading() {
      return !this.$store.state.opportunities.isReady
    },
    pipeline() {
      return this.pipelines
        ? this.pipelines.find(x => x.id === this.filters.pipelineId)
        : undefined
    },
    stages() {
      return this.pipeline && this.pipeline.stages
        ? lodash.sortBy(this.pipeline.stages, ['position'])
        : []
    },
    pipelines() {
      return this.$store.state.pipelines.pipelines
    },
    campaigns() {
      return this.$store.state.campaigns.campaigns
    },
    accessAll(): boolean {
      return this.user && this.user.permissions.assigned_data_only !== true
    },
    isAdmin(): boolean {
      return this.user && this.user.role === 'admin'
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    })
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    await this.fetchSyncAll()
    this.setFilters()
  },
  async mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
    await this.refreshLocationData()
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  methods: {
    async fetchSyncAll() {
      await Promise.all([
        this.$store.dispatch('pipelines/syncAll', this.$route.params.location_id),
        this.$store.dispatch('campaigns/syncAll', this.$route.params.location_id)
      ])
    },
    async refreshLocationData() {
      this.currentLocationId = this.$router.currentRoute.params.location_id
      this.currentLocation = new Location(
        await this.$store.dispatch('locations/getById', this.currentLocationId)
      )
    },
    updateOpportunitiesFilters() {
      var dateFilters = {}
      if (!this.opportunitiesFiltersDates.end) {
        this.opportunitiesFiltersDates.end = this.opportunitiesFiltersDates.start
        dateFilters['endDate'] = moment(this.opportunitiesFiltersDates.end, [
          'YYYY-MM-DD h:m a'
        ])
          .add(1, 'days')
          .toDate()
          .getTime()
      } else {
        dateFilters['endDate'] = moment(this.opportunitiesFiltersDates.end, [
          'YYYY-MM-DD h:m a'
        ])
          .toDate()
          .getTime()
      }
      dateFilters['date'] = moment(this.opportunitiesFiltersDates.start, [
        'YYYY-MM-DD h:m a'
      ])
        .toDate()
        .getTime()
      this.filters = { ...this.filters, ...dateFilters }
    },
    async getPipeline() {
      const pipelineDate = await Pipeline.getById(this.filters.pipelineId)
      this.pipelineCreatedDate = pipelineDate.dateAdded

      if (!this.filters.date && !this.filters.endDate) {
        this.opportunitiesFiltersDates = {
          start: null,
          end: null
        }
      }

      return this.pipelineCreatedDate
    },
    onDragEnter(name: string) {
      this.bus.$emit('disableDrop', true)
      if (name === 'won') {
        this.hoverWon = true
      } else if (name === 'lost') {
        this.hoverLost = true
      } else if (name === 'abandoned') {
        this.hoverAbandoned = true
      }
    },
    onDragLeave(name: string) {
      this.bus.$emit('disableDrop', false)
      if (name === 'won') {
        this.hoverWon = false
      } else if (name === 'lost') {
        this.hoverLost = false
      } else if (name === 'abandoned') {
        this.hoverAbandoned = false
      }
    },
    addOpportunity() {
      this.opportunityModalValues = {
        opportunity: undefined,
        visible: true,
        currentLocationId: this.currentLocationId,
        tab: 'OpportunityComponent',
        pipeline: this.pipeline
      }
    },
    editOpportunity(opportunity: Opportunity) {
      this.opportunityModalValues = {
        visible: true,
        opportunity: opportunity,
        currentLocationId: this.currentLocationId,
        tab: 'OpportunityComponent',
        pipeline: this.pipeline
      }
    },
    editOpportunityAppointment(opportunity: Opportunity) {
      this.opportunityModalValues = {
        visible: true,
        opportunity: opportunity,
        currentLocationId: this.currentLocationId,
        tab: 'AppointmentComponent',
        pipeline: this.pipeline
      }
    },
    editOpportunityTask(opportunity: Opportunity) {
      this.opportunityModalValues = {
        visible: true,
        opportunity: opportunity,
        currentLocationId: this.currentLocationId,
        tab: 'TaskComponent',
        pipeline: this.pipeline
      }
    },
    editOpportunityNotes(opportunity: Opportunity) {
      this.opportunityModalValues = {
        visible: true,
        opportunity: opportunity,
        currentLocationId: this.currentLocationId,
        tab: 'NoteComponent',
        pipeline: this.pipeline
      }
    },
    setFilters() {
      this.users = this.$store.state.users.users.map(u => new User(u))
      const filters = this.$store.state.filters.filters.opportunities || {}

      if (filters.orderBy) {
        this.filters.orderBy = filters.orderBy
      } else {
        this.filters.orderBy = 'added_desc'
      }

      if(this.pipelines?.length){
        let index = 0
        if (filters.pipelineId) {
          let _index = this.pipelines.findIndex(x => x.id === filters.pipelineId)
          if (_index !== -1) {
            index = _index
          }
        }
        this.filters.pipelineId = this.pipelines[index].id
      }

      if (filters.columns) {
        this.filters.columns = filters.columns
      } else {
        this.filters.columns = []
      }

      if (filters.status) {
        this.filters.status = filters.status
      } else {
        this.filters.status = 'open'
      }

      if (!this.accessAll && this.user) {
        this.filters.user = this.user.id
      } else if (filters.user) {
        this.filters.user = filters.user
      }

      if (filters.campaignId) {
        this.filters.campaignId = filters.campaignId
      }

      if (filters.text) {
        this.filters.text = filters.text
      }
      if (filters.date !== undefined && filters.endDate !== undefined) {
        this.filters.date = filters.date
        this.filters.endDate = filters.endDate

        if (filters.date && filters.endDate) {
          this.opportunitiesFiltersDates = {
            start: moment(filters.date).toDate(),
            end: moment(filters.endDate).toDate()
          }
        } else {
          this.opportunitiesFiltersDates = null
        }
      } else {
          this.opportunitiesFiltersDates = {
            start: null,
            end: null
          }
      }
    },
    async onDrop(status: string, dropResult: any) {
      if (dropResult.addedIndex !== null) {
        // const opportunity = await Opportunity.getById(dropResult.payload.id)
        // // this.bus.$emit('updateOpportunityStatus', {
        // //   id: dropResult.payload.id,
        // //   status: status
        // // })
        // opportunity.status = <any>status
        // await opportunity.save()

        const payload = Object.assign({}, dropResult.payload)

        payload.index_version = payload.index_version
            ? payload.index_version + 1
            : 1
        const oldStatus = payload.status
        const oldPipelineId = payload.pipeline_stage_id
        const opportunity = { ...payload, status }
        await this.$store.dispatch('opportunities/updateStatus', {
          opportunity: Opportunity.createNewWithVuex(opportunity),
          nextOpportunityId: -1,
          dontFetchContact: true
        })
        await Opportunity.updateIndex(opportunity, oldStatus)
        // await Opportunity.updateStats({opportunity: opportunity, pipeline_stage_id_old: oldPipelineId, event_type: 'on-drop', user: this.user });
      }
      if (status === 'won') {
        this.hoverWon = false
      } else if (status === 'lost') {
        this.hoverLost = false
      } else if (status === 'abandoned') {
        this.hoverAbandoned = false
      }
      setTimeout(() => {
        this.bus.$emit('disableDrop', false)
      }, 300)
    },
    dragStart(dragResult: any) {
      this.dragging = true
    },
    dragEnd(dragResult: any) {
      this.dragging = false
    },
    shouldAccept() {
      return true
    },
    async exportOpportunities() {
      if (!this.pipeline) return
      if (config.googleAnalyticId && ga) {
        ga(
          'send',
          'event',
          'DownloadOpportunityStarted',
          this.currentLocationId
        )
      }
      this.downloadLoading = true
      const fields = [
        'opportunity name',
        'contact name',
        'phone',
        'email',
        'pipeline',
        'stage',
        'value',
        'source',
        'assigned',
        'date added',
        'date updated',
        'tags',
        'notes',
        'status'
      ]
      let values = [] as any[]
      const pageLimit = 10000

      try {
        let startAfter = ''
        do {
          let apiEndpoint = `${config.baseUrl}/search/opportunity?limit=${pageLimit}&pipeline_id=${this.filters.pipelineId}&location_id=${this.currentLocationId}&startAfter=${startAfter}`
          if (!this.accessAll && this.user) {
            apiEndpoint += '&assigned_to=' + this.user.id
          }
          let response = await this.$http.get(apiEndpoint)
          if (response.data.hits.hits && response.data.hits.hits.length > 0) {
            for (
              let index = 0;
              index < response.data.hits.hits.length;
              index++
            ) {
              const opportunityES = response.data.hits.hits[index]
              const opportunity = new Opportunity({
                id: opportunityES._id,
                ...opportunityES._source,
                keepESFields: true
              })
              let row = {} as any

              row['opportunity name'] = opportunity.name
              row['tags'] = ''

              if (opportunity.contactId) {
                const oppdata = opportunity.data
                if (oppdata) {
                  row['contact name'] =
                    oppdata.contact_name &&
                    oppdata.contact_name !== oppdata.phone &&
                    oppdata.contact_name !== opportunity.data.email
                      ? oppdata.contact_name
                      : ''
                  row['phone'] = opportunity.data.phone
                  row['email'] = opportunity.data.email
                  row['tags'] = opportunity.data.tags
                    ? opportunity.data.tags.join()
                    : ''

                  if (this.filters.columns.includes('note')) {
                    let notes = await Note.getByContactId(
                      opportunity.contactId,
                      this.currentLocationId
                    )
                    if (notes && notes.length > 0) {
                      let noteText = ''

                      for (let note of notes) {
                        noteText += note.body + '\n'
                      }

                      row['notes'] = noteText
                    }
                  }
                }
              }

              if (this.pipeline) {
                row['pipeline'] = this.pipeline.name
              }

              let stage = lodash.find(this.stages, {
                id: opportunity.pipelineStageId
              })
              if (stage) {
                row['stage'] = stage.name
              }

              row['value'] = opportunity.monetaryValue
              row['source'] = opportunity.source

              if (opportunity.assignedTo) {
                const user = new User(
                  this.$store.getters['users/getById'](opportunity.assignedTo)
                )
                if (user) {
                  row['assigned'] = user.name
                }
              }
              row['date added'] = opportunity.dateAdded.format()
              row['date updated'] = opportunity.dateUpdated.format()

              row['status'] = opportunity.status

              values.push(row)
            }

            if (response.data.hits.hits.length === pageLimit) {
              startAfter =
                response.data.hits.hits[response.data.hits.hits.length - 1]
                  ._source.date_added
            } else {
              startAfter = undefined
            }
          } else {
            startAfter = undefined
          }
        } while (startAfter !== undefined)
      } catch (err) {
        console.error(err)
      }

      const json2csvParser = new Json2csvParser({ fields })
      const csv = json2csvParser.parse(values)
      if (config.googleAnalyticId && ga) {
        ga(
          'send',
          'event',
          'DownloadOpportunity',
          this.currentLocationId,
          values.length
        )
      }

      download(csv, 'opportunities.csv', 'text/csv')
      this.downloadLoading = false
    },
    loadingStarted() {
      // this.loadingProcess++
    },
    loadingCompleted() {
      // if (this.loadingProcess === 0) return
      // this.loadingProcess--
    }
  }
})
</script>

<style>
#opportunities .hl_controls {
  margin-bottom: 0 !important;
}
.hl_wrapper--inner.opportunities {
  padding-top: 10px;
}
#opportunities
  .hl_controls--right
  .date-time-picker
  input.field-input::placeholder {
  color: rgb(24, 139, 246);
  opacity: 1;
}
#opportunities input#undefined-input::-webkit-input-placeholder,
#opportunities input#undefined-input::placeholder {
  color: #188bf6;
}
#opportunities .hl_controls--right .date-time-picker input.field-input {
  margin-right: 10px;
}
.hl_opportunities--drag-actions {
  box-sizing: border-box;
  position: fixed !important;
  opacity: 0 !important;
  visibility: hidden;
  /* display: none !important; */
  bottom: -25px !important;
  width: calc(100% - 70px) !important;
  left: 70px !important;
  transition: bottom 0.3s ease, opacity 0.3s ease !important;
  z-index: 99 !important;
  padding: 10px 0;
}
.hl_opportunities--drag-actions.visible {
  /* display: block !important; */
  visibility: visible;
  bottom: 0 !important;
  opacity: 1 !important;
  transition: bottom 0.3s ease, opacity 0.3s ease !important;
}
.hl_opportunities-item {
  margin-bottom: 5px !important;
}
.hl_opportunities--set {
  margin-top: 5px;
}
.hl_opportunities--set-header {
  margin-bottom: 7px !important;
}
.hl_opportunities--set ul {
  padding-right: 0 !important;
}
.hl_opportunities .row > div {
  padding-left: 0 !important;
}
.hl_opportunities--drag-actions .drag-action {
  padding: 25px 0;
  background-color: transparent !important;
}
.hl_opportunities--drag-actions .drag-action h4 {
  padding: 25px;
}
.hl_opportunities--drag-actions-inner {
  padding: 0 15px;
  /* padding-left: 250px; */
}
.hl_opportunities--drag-actions .drag-action.\--abandoned h4 {
  background-color: #fff;
}
.hl_opportunities--drag-actions .drag-action.abandoned-hover h4,
.hl_opportunities--drag-actions .drag-action.abandoned-hover h4:hover {
  background-color: #cde0ec !important;
}
.hl_opportunities--drag-actions .drag-action.\--lost h4 {
  background-color: red;
}
.hl_opportunities--drag-actions .drag-action.lost-hover h4,
.hl_opportunities--drag-actions .drag-action.lost-hover h4:hover {
  background-color: #da1919 !important;
}
.hl_opportunities--drag-actions .drag-action.\--won h4 {
  background-color: #4bcf4b;
}
.hl_opportunities--drag-actions .drag-action.won-hover h4,
.hl_opportunities--drag-actions .drag-action.won-hover h4:hover {
  background-color: #2ba32b !important;
}
.hl_opportunities .row {
  overflow-x: scroll !important;
}
#opportunities .hl_opportunities .hl_opportunities--placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}
@media (min-width: 1200px) {
  .hl_opportunities--drag-actions {
    left: 150px;
  }
}
</style>
