<template>
	<tr>
		<td>
			<router-link
				:to="{name: 'email_builder_dashboard', params: {location_id: currentLocationId, builder_id: emailBuilder.id}}"
				tag="a"
			>{{ emailBuilder.name }}</router-link>
		</td>
		<td style="width:1px;">
			<i class="icon icon-trash --light" @click.prevent="deleteBuilderItem" v-if="allowEdit"></i>
		</td>
	</tr>
</template>

<script lang="ts">
import Vue from "vue";
import { mapState } from "vuex";
import { UserState } from "../../../store/state_models";
import { EmailBuilder, User } from "../../../models/";

export default Vue.extend({
	props: ["emailBuilder"],
	data() {
		return {
			currentLocationId: ""
		};
	},
	watch: {
		"$route.params.location_id": function (id) {
			this.currentLocationId = id;
		}
	},
	computed: {
		allowEdit(): boolean {
			//this.user.permissions.campaigns_read_only !== true;
			return this.user ? true : false;
		},
		...mapState("user", {
			user: (s: UserState) => {
				return s.user ? new User(s.user) : undefined;
			}
		})
	},
	methods: {
		async deleteBuilderItem() {
			if (confirm("Are you sure you want to delete this email template?")) {
				let formStore = await EmailBuilder.getById(this.emailBuilder.id);
				formStore.deleted = true;
				await formStore.save();
			}
		}
	},
	async created() {
		this.currentLocationId = this.$router.currentRoute.params.location_id;
	}
});
</script>
