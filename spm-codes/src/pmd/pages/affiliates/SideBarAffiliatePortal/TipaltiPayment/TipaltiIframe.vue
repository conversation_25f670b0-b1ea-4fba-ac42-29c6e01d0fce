<template>
      <div>
        <template>
        <!-- Show Loading -->
        <div  v-if="loading" class="loader-backdrop">
          <div>
            <moon-loader  color="#188bf6" size="30px" />
            <p>Please wait...</p>
          </div>
        </div>
        <!-- Show Error Msg -->
        <div  v-else-if="errorMsg" class="loader-backdrop">
          <div>
            <p class="errorMsg" >{{errorMsg}}</p>
          </div>
        </div>
        <!-- Tipalti iFrame -->
        <section v-else
          class="hl_agency hl_agency-sales-resources"
          style="background-color: white"
        >
        <!-- Load iframe component -->
        <iframe
          :src="iframeRequestURL"
          style="height: calc(100vh - 84px); width: 100%; border: none"
        ></iframe>
        </section>
      </template>
    </div>
</template>

<script>
import Vue from 'vue'
import crypto from 'crypto'
import config from '@/config'
import { Company } from '@/models'


export default {
  props:{
    pageName:String,
  },
  data(){
    return {
      loading: false,
      errorMsg:"",
      payer:'highlevel',
      state:"",
      country:"",
      promoterId:"",
      // userEmail:"",
    }
  },
  created(){
    this.initData();
    //this.getFpPromoterId()
  },
  computed:{
    iframeRequestURL(){
      let currentTimestamp = Math.round(+ new Date / 1000)
      let query = {
        idap:this.promoterId,
        ts:currentTimestamp,
        payer:this.payer,
        // email:this.userEmail,
      }

      if(this.country.toLowerCase() === 'ca'){
        query['state'] = this.state
      }
      const queryStr = Object.keys(query).map(key => key + '=' + query[key]).join('&');
      // console.log(queryStr,"querycheck")
      let hashkey = crypto.createHmac('sha256',config.tipalti.masterKey)
      .update(encodeURI(queryStr))
      .digest('hex');

      let baseURL = config.tipalti.baseURL
      let url = `${baseURL}${this.pageName}?${queryStr}&hashkey=${hashkey}`

      return url
    }
  },
  methods:{
    initData(){
      this.companyId = this.$store.state?.company?.company?.id,
      this.state = this.$store.state?.company?.company?.state,
      this.promoterId =this.$store.state?.company?.company?.promoter_id
      this.country =this.$store.state?.company?.company?.country
      // this.userEmail = this.$store.state?.user?.user?.email,
      if(!this.promoterId){
        this.getFpPromoterId()
      }
    },
    async getFpPromoterId(){
      try {
        if(!this.promoterId){
          this.loading = true
          const { data } = await this.$http.get(`/firstpromoter/get_promoter_details?company_id=${this.companyId}`)
          this.promoterId = data.id
          let company = await Company.getById(this.companyId);
          await company.ref.update({ promoter_id: data.id });
          this.loading = false
        }
      } catch (error) {
        this.loading = false
        //this.errorMsg = error.message
        this.errorMsg = 'Something went wrong'
      }
    }
  }

}

</script>

<style scoped lang="scss" >

  .loader-backdrop {
    background: #ffffffc7;
    width: 100%;
    height: 90vh;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;

    p {
      margin-top: 10px;
      padding: 10px;
      color: #009000;
      font-weight: 500;
    }

    .errorMsg{
      color:red;
      font-size: 20px;
      font-weight: 500;
    }

 }


// Adjustment for newside bar. coz in tipalti iframe "next" button not showing.so need this height adjjustment,
.hl_agency{
  height: calc(100vh - 125px) !important;
}
</style>
