import { AuthUser } from '../models'
import { ActiveUser } from '../models/editor_active_viewer'
import Team from '../models/team'

export interface UserState {
  user?: { [key: string]: any }
}
export interface CalendarState {
  calendars: { [key: string]: any }[],
  locationId: undefined | string
}

export interface LinkedCalendarState {
  linkedCalendars: { [key: string]: any }[],
  userId: undefined | string,
  locationId: undefined | string
}

export interface SMTPServiceState {
  locationSMTPServices: { [key: string]: any }[],
  companySMTPServices: { [key: string]: any }[],
  locationId: undefined | string,
  companyId: undefined | string
}
export interface MailGunServiceState {
  mailgunLocationWithErrors: string,
  mailgunCompanyWithErrors: string,
  locationId: undefined | string,
  companyId: undefined | string
}
export interface DefaultEmailService {
  defaultEmailService: { [key: string]: any }
}
export interface Step {
  funnelId: string
  url: string
  value: string
  sequence: number
  name: string
}
export interface FunnelSteps {
  [key: number]: Step
}
export interface FunnelState {
  funnelSteps: FunnelSteps
  editorActiveViewers:Array<ActiveUser>
}
export interface CompanyState {
  company?: { [key: string]: any },
  whiteLabelUrl?: string;
}
export interface RootState {
  deviceId?: string
  initialized: boolean
  collapseSideBar: boolean
  manualCollapseSidebar: boolean,
  twilioClientReady: boolean,
  menuTabs?: { [key: string]: any }
}
export interface AuthState {
  user?: AuthUser,
  locationId: undefined | string
}
export interface EstimateState {
  estimates: { [key: string]: any }
}
export interface ContactsState {
  contacts: { [key: string]: any },
  deletedContacts: { [key: string]: any },
  ids: Set<string>
  locationId: undefined | string
}
export interface LocationState {
  locationsLoaded?: boolean,
  locations: { [key: string]: any }[],
  activeLocations: string[],
  notificationLocations: string[],
  currentLocation: any
}

export interface UsersState {
  locationId: string | undefined
  users: any[]
}

export interface VuexConversation {
  done: boolean;
  last_document_date: string | number;
  documents: Array<{ [key: string]: any }>;
}

export interface ConversationState {
  all: VuexConversation;
  inbox: VuexConversation;
  unread: VuexConversation;
  search: VuexConversation;
  currentTab: string;
  searchTerm: string;
  fetching: boolean;
  searching: boolean;
  triggerLinksMenuItem: any;
  filters: any;
  onboarding: boolean;
  popupStep: number;
}

export interface CampaignState {
  campaigns: { [key: string]: any }
  locationId: undefined | string
}

export interface WorkflowState {
  workflows: { [key: string]: any }
  locationId: undefined | string
}

export interface PipelineState {
  pipelines: { [key: string]: any }
  locationId: undefined | string
}

export interface AgencyUsersState {
  users: { [key: string]: any }
  locationId: undefined | string
}

export interface ReviewAggregateData {
  totalReviews: number
  totalRating: number
  breakdown: {
    '1': number
    '2': number
    '3': number
    '4': number
    '5': number
  }
  positiveReviews: 0
  negativeReviews: 0
  sources: number[]
}
export interface ReviewComponentData {
  previous: ReviewAggregateData
  current: ReviewAggregateData
  fetched: boolean
}
export interface ReviewAggregateState {
  thisWeek: ReviewComponentData
  lastWeek: ReviewComponentData
  thisMonth: ReviewComponentData
  last6Months: ReviewComponentData
  thisYear: ReviewComponentData
}
export interface ReviewRequestAggregateData {
  totalRequests: number
  leaderboard: { [key: string]: number }
}
export interface ReviewRequestComponentData {
  current: ReviewRequestAggregateData
  fetched: boolean
}
export interface ReviewRequestAggregateState {
  thisWeek: ReviewRequestComponentData
  lastWeek: ReviewRequestComponentData
  thisMonth: ReviewRequestComponentData
  last6Months: ReviewRequestComponentData
  thisYear: ReviewRequestComponentData
}
export interface ImagePreviewState {
  src: string
}
export interface ManualCallStatusState {
  manualCallId: string
}
export interface PhoneNumbers {
  dialOutNumber: string
  numbers: string[]
}
export interface IncomingCall {
  from: string
  to: string
  contactId: string
  newCustomer: boolean
  appointmentId?: string
  locationId: string
}
export interface IncomingCallState {
  incomingCall?: IncomingCall
  available: boolean
}
export interface JobsState {
  jobs: { [key: string]: any }
}
export interface ContactState {
  estimates: Array<{ [key: string]: any }>
}

export interface ContactsState {
  contacts: { [key: string]: any }
  locationId: undefined | string
  ids: Set<string>
}

export interface Oauth2State {
  oauth2: any[],
  locationId: undefined | string,
  isReady: boolean
}

export interface OpportunityState {
  opportunities: any[],
  locationId: undefined | string,
  filters: any,
  isReady: boolean,
  opportunitiesStageData: any,
  loadingAggregates: boolean
}

export interface FiltersState {
  filters: any,
  locationId: undefined | string
}

export interface AppointmentState {
  estimates: Array<{ [key: string]: any }>
}
export interface JobState {
  estimates: Array<{ [key: string]: any }>
}
export interface FileAttachment {
  name: string
  type: string
  url: string
  data?: File | Blob
}

export interface TwilioState {
  twilioData: { [key: string]: any }
  showTwilioAlert: boolean
  showTwilioLocationAlert: boolean
  locationTwilioEmpty: boolean
  showResolvePrompt: boolean
}

export interface Folder {
  folderId: string
  locationId: string
}

export interface FolderState {
  FolderItems: Array<{ [key: string] : Folder }>
}

export interface MembershipState {
  locationId: string;
  offers: Array<{ [key: string] : string }>;
  products: Array<{ [key: string] : string }>;
}

export interface LocationCustomFieldsState {
  locationId: string;
  customFields: Array<{ [key: string] : string }>;
}

export interface TeamState {
  teams: Team[],
  locationId: undefined | string
}

export interface IframeState {
  handshake: { [key: string]: any };
  onboardingPassword: boolean
}


export interface ProductsState {
  products: { [key: string]: any };
}
export interface StripeConnectState {
  accountId: undefined | string,
  locationId: undefined | string
}

export interface IntegrationsState {
  integrations: { [key: string]: any };
}
export interface LevelUpDayState {
  featuresActive:  boolean,
  levelupDate: Date
}
export interface SideBarV2 {
  version: string,
  notification: string
}
