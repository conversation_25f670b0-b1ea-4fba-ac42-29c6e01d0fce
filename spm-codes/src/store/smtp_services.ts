import Vue from 'vue'
import { Module, ActionContext } from 'vuex'
import { RootState, SMTPServiceState } from './state_models'
import { SMTPService } from '../models';

let smtpServiceListener;
let companySMTPServiceListener;
export const smtpServices: Module<SMTPServiceState, RootState> = {
  namespaced: true,
  state: { locationSMTPServices: [], locationId: undefined, companySMTPServices: [], companyId: undefined },
  getters: {
    getById: (state: SMTPServiceState) => {
      return (id: string) => state.locationSMTPServices.find(x => x.id === id)
    },
    smtpLocationWithErrors(state: SMTPServiceState) {
      return state.locationSMTPServices.filter(s => s.smtp_service_expired && s.smtp_service_error);
    },
    smtpCompanyWithErrors(state: SMTPServiceState) {
      return state.companySMTPServices.filter(s => s.smtp_service_expired && s.smtp_service_error);
    },
    locationSMTPErrorMsg: (state: SMTPServiceState, getters) => {
      if (getters.smtpLocationWithErrors.length === 0) return null;

      let msg = 'There is a problem with the integration on these smtp services "'
      getters.smtpLocationWithErrors.forEach(service => {
        msg += service.email + ', '
      })
      msg = lodash.trimEnd(msg, ', ')
      msg += '"'
      return msg
    },
    companySMTPErrorMsg: (state: SMTPServiceState, getters) => {
      if (getters.smtpCompanyWithErrors.length === 0) return null;

      let msg = 'There is a problem with the integration on these smtp services "'
      getters.smtpCompanyWithErrors.forEach(service => {
        msg += service.email + ', '
      })
      msg = lodash.trimEnd(msg, ', ')
      msg += '"'
      return msg
    }
  },
  mutations: {
    setLocationServices: (
      state: SMTPServiceState,
      payload: { smtpServices: any[]; locationId: string }
    ) => {
      state.locationSMTPServices = payload.smtpServices
      if (payload.locationId) {
        state.locationId = payload.locationId
      }
    },
    setCompanyServices: (
      state: SMTPServiceState,
      payload: { smtpServices: any[]; companyId: string }
    ) => {
      state.companySMTPServices = payload.smtpServices
      if (payload.companyId) {
        state.companyId = payload.companyId
      }
    },
    clearLocation(state: SMTPServiceState) {
      if (smtpServiceListener) {
        smtpServiceListener();
      }

      state.locationSMTPServices = []
      state.locationId = undefined
    },
    clearCompany(state: SMTPServiceState) {
      if (companySMTPServiceListener) {
        companySMTPServiceListener();
      }

      state.companySMTPServices = []
      state.companyId = undefined
    }
  },
  actions: {
    async syncAll(
      context: ActionContext<SMTPServiceState, RootState>,
      locationId: string
    ) {
      if (!locationId) {
        context.commit('clearLocation')
        return
      }


      if (context.state.locationId === locationId) {
        return context.state.locationSMTPServices
      }
      return new Promise((resolve, reject) => {

        if (smtpServiceListener) {
          smtpServiceListener();
        }

        smtpServiceListener = SMTPService.fetchByLocationIdRealtime(locationId)
          .onSnapshot(snapshot => {
            context.commit('setLocationServices', {
              locationId: locationId,
              smtpServices: snapshot.docs.map(d => {
                return {
                  id: d.id,
                  ...d.data()
                }
              })
            })
            resolve(context.state.locationSMTPServices)
          }, err => resolve())
      })
    },
    async companySyncAll(
      context: ActionContext<SMTPServiceState, RootState>,
      companyId: string
    ) {
      if (!companyId) {
        context.commit('clearCompany')
        return
      }


      if (context.state.companyId === companyId) {
        return context.state.companySMTPServices
      }

      return new Promise((resolve, reject) => {

        if (companySMTPServiceListener) {
          companySMTPServiceListener();
        }

        companySMTPServiceListener = SMTPService.fetchByCompanyIdRealtime(companyId)
          .onSnapshot(snapshot => {
            context.commit('setCompanyServices', {
              companyId: companyId,
              smtpServices: snapshot.docs.map(d => {
                return {
                  id: d.id,
                  ...d.data()
                }
              })
            })

            resolve(context.state.companySMTPServices)
          }, err => resolve())
      })
    },
    logout: {
      root: true,
      handler: (context: ActionContext<SMTPServiceState, RootState>) => {
        context.commit('clearLocation')
        context.commit('clearCompany')
      }
    },
    clearAll: {
      handler: (context: ActionContext<SMTPServiceState, RootState>) => {
        context.commit('clearLocation')
        context.commit('clearCompany')
      }
    }
  }
}
