<template>
	<div class="tab-pane fade show active" id="tab3" role="tabpanel" aria-labelledby="tab3-tab">
		<div class="row">
			<div class="col-xl-6">
				<div class="hl_tasks">
					<div
						class="hl_tasks--item"
						:class="taskClass(task.id)"
						v-for="task in tasks"
						:task="task"
						:key="task.id"
					>
						<div class="flex items-center position-absolute left-0 mt-1">
							<UICheckbox
								:id="task.id"
								v-model="task.completed"
								@change="saveTask(task.id)"
							/>
							<label :for="task.id"></label>
						</div>
						<div class="hl_tasks--item-header">
							<h4 style="cursor:pointer;" @click.prevent="view">{{task.title}}</h4>
							<p v-if="taskClass(task.id)>=1 && taskClass(task.id) <=3">{{ dueLateNote(id) }}</p>
						</div>
						<div class="hl_products--item-body">
							<div class="hl_products--item-text">
								<p>{{task.body}}</p>
							</div>
						</div>
						<div class="hl_tasks--item-footer">
							<p v-if="task.dueDate">
								Due:
								<strong>{{ task.dueDate.format(getCountryDateFormat('month date, year')) }}</strong>
							</p>
							<p v-if="getUser(task.assignTo)">
								Assigned to:
								<strong>{{ getUser(task.assignTo).fullName }}</strong>
							</p>
							<div class="task-actions ml-auto">
								<i
									class="icon icon-edit --blue"
									style="margin-right: 10px;"
									@click.prevent="editTask(task.id)"
								></i>
								<i class="icon icon-trash --gray" @click.prevent="removeTask(task.id)"></i>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="col-xl-6">
				<div class="form-group">
					<UITextInputGroup
						type="text"
						placeholder="Task Title"
						label="Title"
						v-model="title"
						v-validate="'required'"
						name="title"
						:error="errors.has('title')"
						:errorMsg="errors.first('title')"
					/>
				</div>
				<div class="form-group">
					<UITextAreaGroup rows="5" label="Description" placeholder="Task Description" v-model="taskBody"></UITextAreaGroup>
				</div>
				<div class="form-group">
					<UITextLabel>Assign To</UITextLabel>
					<select class="selectpicker" title="Not assigned" data-width="100%" v-model="assignTo">
						<option>Not assigned</option>
						<option v-for="user in users" :value="user.id">{{user.fullName}}</option>
					</select>
				</div>
				<div class="form-group">
					<UITextLabel>Due Date</UITextLabel>
					<datepicker
						v-model="dueDate"
						:input-class="'date-picker mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800'"
						:format="getCountryDateFormat('weekday, month dateth')"
						:placeholder="'Due Date'"
						name="dueDate"
					/>
				    <!-- :disabled-dates="disabledDates" Removed regarding console errors, if necessary, need to add the logic and the data -->
					<span v-show="errors.has('dueDate')" class="error">Due date is required</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
import { Opportunity, Task, User, getCountryDateFormat } from '@/models';
import Datepicker from 'vuejs-datepicker';
import * as moment from 'moment-timezone';
import { mapState } from 'vuex';
import { UserState } from '@/store/state_models';

declare var $: any;
let unsubscribeTasks: () => void;

export default Vue.extend({
	props: ['bus', 'users', 'meta'],
	components: { Datepicker },
	watch: {
		meta(meta) {
			this.errors.clear();
			const data: (() => object) = <(() => object)>this.$options.data;
			if (data) Object.assign(this.$data, data.apply(this));
			if (this.meta.contactId) {
				this.fetchData();
			}
		},
	},
	data() {
		return {
			tasks: [] as Task[],
			taskBody: '',
			dueDate: moment().add(1, 'days').set({hour: 8, minute: 0, seconds: 0}).format(),
			assignTo: '',
			title: '',
      currentTask: undefined as Task | undefined,
      getCountryDateFormat: getCountryDateFormat
		}
	},
	computed: {
		...mapState('user', {
			user: (s: UserState) => {
				return s.user ? new User(s.user) : undefined;
			},
		}),
	},
	methods: {
		taskStatus(id: string): number {
			const task = <Task>lodash.find(this.tasks, { id: id });
			if (task.completed) return 0;
			if (task.dueDate) {
				if (task.dueDate.isBefore(moment())) return 1;
				if (task.dueDate.isSame(moment())) return 2;
				if (task.dueDate.subtract(3, 'days').isSameOrBefore(moment())) return 3;
			}
			return 4;
		},
		taskClass(id: string): { [key: string]: boolean } {
			const task = <Task>lodash.find(this.tasks, { id: id });
			const response: { [key: string]: boolean } = {};
			response[['--done', '--late', '--due', '--due', ''][this.taskStatus(id)]] = true;
			return response;
		},
		dueLateNote(id: string): string {
			const task = <Task>lodash.find(this.tasks, { id: id });
			const status = this.taskStatus(id);
			if (status === 2) return 'Due today';
      let note = status === 1 ? 'Late ' : 'Due in ';
      const days = Math.ceil((status === 1 ? moment().diff(task.dueDate, 'hours') : task.dueDate.diff(moment(), 'hours')) / 24);
			note += days;
			note += days > 1 ? ' days' : ' day';
			return note;
		},
		getUser(userId: string) {
			return lodash.find(this.users, { id: userId });
		},
		async saveTask(id: string) {
			const task = <Task>lodash.find(this.tasks, { id: id });
			await task.save();
		},
		editTask(id: string) {
			const task = <Task>lodash.find(this.tasks, { id: id });
			this.currentTask = task;
			this.taskBody = task.body;
			this.dueDate = task.dueDate.toDate();
			this.assignTo = task.assignedTo;
			this.title = task.title;
		},
		async removeTask(id: string) {
			const task = <Task>lodash.find(this.tasks, { id: id });
			if (confirm('Are you sure you want to delete this task?')) {
				task.deleted = true;
				await task.save();
			}
		},
		async submit(contactId: string) {
			this.errors.clear();

			if (!this.taskBody && !this.assignTo && !this.title) {
				this.bus.$emit('complete');
				return;
			}

			await this.$validator.validateAll();
			if (this.errors.any()) {
				this.bus.$emit('complete', { tab: 'TaskComponent' });
				return;
			}

			if (!this.currentTask) {
				this.currentTask = new Task();
				this.currentTask.locationId = this.meta.currentLocationId;
				this.currentTask.contactId = this.meta.contactId ? this.meta.contactId : contactId;
				this.currentTask.userId = this.user.id;
			}

			this.currentTask.body = this.taskBody;
			this.currentTask.title = this.title;
			this.currentTask.dueDate = this.dueDate ? moment(this.dueDate) : undefined;
			this.currentTask.assignedTo = this.assignTo;
			await this.currentTask.save();

			this.bus.$emit('complete');
		},
		async fetchData() {
			if (!this.meta.contactId) return;
			if (unsubscribeTasks) unsubscribeTasks();
			unsubscribeTasks = (Task.getTasksByContactId(this.meta.contactId, this.meta.currentLocationId)).onSnapshot(snapshot => {
				this.tasks = snapshot.docs.map(d => new Task(d));
			});
		},

	},
	mounted() {
		this.bus.$on('save_task', this.submit);
	},
	beforeDestroy() {
		if (unsubscribeTasks) unsubscribeTasks();
		this.bus.$off('save_task');
	},
	updated() {
		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker('refresh');
		}
	},
});
</script>
