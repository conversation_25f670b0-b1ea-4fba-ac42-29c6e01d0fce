<template>
  <div class="modal annual-upgrade-modal" ref="modal">
		<div class="modal-dialog" role="document" style="margin-top:90px;">
			<div class="modal-content">
				<div class="modal-header">
					<div class="modal-header--inner">

						<h5 class="modal-title" id="client-checkin--modalLabel">
							Agency Annual Upgrade
						</h5>
						<p> Upgrade now to the annual plan and get two months free <span class="hl_tooltip pointer" v-b-tooltip.hover title="Your payment for this month will be adjusted in the annual amount">i</span></p>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</div>
				<div class="modal-body">
					<div class="modal-body--inner" v-if="submitted">
						<p class="success-msg">Your agency is now upgraded to the annual plan !!</p>
					</div>
					<div class="modal-body--inner" v-else>
						<!-- <div class="row">
							<div class="form-group col-sm-12">
								<label>Name </label>
								<input
									type="email"
									class="form-control"
									placeholder="Full Name"
									v-model="contactName"
								/>
							</div>
							<div class="form-group col-sm-12" >
								<label>Contact email</label>
								<input
									type="email"
									class="form-control"
									placeholder="Email Address"
									v-model="contactEmail"
								/>
							</div>
						</div> -->
						<div>

						</div>
						<div class="plan-row" v-if="activePlan">
							<div class="plan-info">
								<!-- <i class="icon --yellow"><img src="./img/icon-refresh.svg" alt="icon"></i> -->
								<div class="text">
									<h5 style="font-size:16px;margin-bottom:0px;">Recurring Annual Payment</h5>
									<p>{{planDetails.planName}} <span aria-hidden="true">&times;</span> 12 months</p>
								</div>
							</div>
							<div class="plan-price">
								<div style="text-decoration:line-through;">${{price.originalPrice}}/year</div> 
								<p style="font-weight:600">${{price.discountedPrice}}/year</p>
							</div>
						</div>
						<div class="option" style="margin-top:15px;">
							<input type="checkbox" id="accept" v-model="accepted" />
							<label for="accept" style="line-height:30px">
								I understand this annual payment is non-refundable and will be auto-renewed after 1 year							
							</label>
						</div>
						<!-- <div class="option">
							<input type="checkbox" id="acceptedRenew" v-model="acceptedRenew" />
							<label for="acceptedRenew" style="line-height:30px">
								I understand that my subscription will be auto-renewed after 1 year							
							</label>
						</div> -->
					</div>
				</div>
				<div class="modal-footer">
					<div class="modal-footer--inner">
						<div>
							<button
								type="button"
								class="btn btn-primary"
								data-dismiss="modal"
								@click="closeModal"
							>
								Close
							</button>
						</div>
						<div style="display: inline-block;position: relative;" v-if="!submitted">
							<button
								:class="{invisible: processing}"
								type="button"
								class="btn btn-success"
								:disabled="!accepted"
								@click="upgrade"
							>Upgrade Now</button>
							<div
								style="position: absolute;left: 50%;transform: translate(-50%, -50%);top: 50%;"
								v-show="processing"
							>
								<moon-loader :loading="processing" color="#37ca37" size="30px"/>
							</div>
						</div>
						
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from 'vue'
import {
  Company, User
} from '@/models'
import { trackGaEvent } from "@/util/helper";

declare var $: any
export default Vue.extend({
	props: ['values','activePlan','planDetails','company'],
	data() {
		return {
			processing: false,
			submitted: false,
			isAgree: false,
			accepted: false,
			// acceptedRenew: false,
			// contactName: '',
			// contactEmail: '',
		}
	},
  	watch: {
    	values(values : { [key: string]: any }) {
      		const data: (() => object) = <(() => object)>this.$options.data;
			if (data) 
				Object.assign(this.$data, data.apply(this));
			if (values.visible) 
				$(this.$refs.modal).modal('show');
			else 
				$(this.$refs.modal).modal('hide');
    	}
  	},
  	methods: {
		closeModal(){
			// $(this.$refs.modal).modal('hide');
			trackGaEvent('Billing',this.company.id,'Annual upgrade cancelled');
			this.$emit('hidden');
		},
		async upgrade() {
			if (!this.accepted) {
				return;
			}
			this.processing = true;

			const authUser = await this.$store.dispatch("auth/get");
			try {
				let response = await this.$http.post('/stripe/annual_upgrade',{
					company_id: this.company.id,
					plan: this.activePlan,
					user_id: authUser.userId
				})
				if(response.data && response.data.plan){
					this.$emit('success',response.data.plan);
					this.submitted = true;
					trackGaEvent('Billing',this.company.id,'Annual upgraded successful');
				}
			} catch (e) {

			}
			this.processing = false;
		}
  	},
  	computed:{
		price() {
			let originalPrice = 'NA';
			let discountedPrice = 'NA';
			switch(this.planDetails.amount) {
				case '97':
					originalPrice = '1,164';
					discountedPrice = '970';
					break;
				case '297':
					originalPrice = '3,564';
					discountedPrice = '2,970';
					break;
				case '237':
					originalPrice = '2,844';
					discountedPrice = '2,370';
					break;
				default:
					originalPrice = 'NA';
					discountedPrice = 'NA';
			}
			return {originalPrice, discountedPrice};
	  	}
  	},
  	updated() {
		if (this.values && this.values.visible) {
			$(this.$refs.modal).modal('show');
		}
	},
	mounted() {
		const _self = this;
			$(this.$refs.modal).on('hidden.bs.modal', function () {
				_self.$emit('hidden');
			});
			if (this.values && this.values.visible) {
				$(this.$refs.modal).modal('show');
			}
		}
	})
    //      You can get 2 months free by upgrading to the annual plan
    //          Currently                                                     If you switch
    //  $297 x 12 months                                               $297 x 12 months (10 months)
    //                                                                      + 2 months free!
    //    = $3564 / year                                                     = $2970 / year
    //   (charged monthly)                                                 (charged annually)

</script>

<style scoped>
.annual-upgrade-modal .modal-footer, .annual-upgrade-modal .modal-header{
	border: none;
}
.annual-upgrade-modal .modal-body{
	padding: 0px 15px;
}
.btn-success[disabled='disabled'] {
	cursor: not-allowed;
	/* pointer-events: none; */
}
.plan-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px 0px;
	border-top: 1px solid #e2e6ed;
	border-bottom: 1px solid #e2e6ed;
}
.plan-price{
	text-align: right;
}
.success-msg{
    padding: 20px 0px 40px;
    border-top: 1px solid #35ca38;

}
</style>

