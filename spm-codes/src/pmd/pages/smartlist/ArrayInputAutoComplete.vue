<template>
    <div class="form-control-group">
      <div class="dropdown" style="width:100%" v-if="allowSearch" >
          <UITextInput
            :placeholder="`Search ${option.filterName}`"
            v-model="search"
          />
          <div class="dropdown-menu show" v-show="search"
              aria-labelledby="dropdownMenuButton"
              style="min-height:50px; max-height: 300px; overflow: auto; max-width:220px">
            <template v-if="shortListed && shortListed.length>0">
              <div class="dropdown-item d-inline-block pointer text-wrap text-break"
                  style="max-width:200px"
                  v-model="shortListed" v-for="s in shortListed"
                  @click.stop.prevent="select(s)">
                <span class="text align-middle text-wrap text-break space-wrap-preline" >{{s.value}}</span>
              </div>
            </template>
              <div v-else class="dropdown-item">
                <span class="text align-middle --blue">searching...</span>
              </div>
          </div>
    </div>
    <div  v-if="!selectedItems || selectedItems.length <= 0">
      <span class="--red">* Atleast one {{filterName}} required</span>
    </div>
    <div class="tag-group py-2" v-if="selectedItems && selectedItems.length > 0">
      <div class="tag" v-for="tag in selectedItems">
        {{tag.value}}
        <a @click="remove(tag)">
          <i class="icon icon-close"></i>
        </a>
      </div>
    </div>
    </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {FilterOption, IKeyVal} from './vm/option_vm'
import {Tag} from '@/models'


export default Vue.extend({
  name: 'ArrayInputAutoComplete',
  props : {
    bus : Vue,
    option: FilterOption
  },
  data () {
    return {
      cached: [] as IKeyVal[],
      shortListed: [] as IKeyVal[],
      search:'',
      selectedItems:[] as IKeyVal[],
      locationId:'',
      filterName: (this.option ? this.option.filterName.toLowerCase():''),
      waitTime:300,
      debouncer:undefined as NodeJS.Timer | undefined,
    };
  },
  watch : {
     search(n,o){
      if(this.debouncer) clearTimeout(this.debouncer);
      this.debouncer = setTimeout(() => this.find(n),this.waitTime);
     },
    '$route.params.location_id': function(id) {
      this.locationId = id;
      this.fetchData();
    }
  },
  computed : {
      allowSearch(){
        if (!this.selectedItems || this.selectedItems.length === 0) return true;
        return this.selectedItems.length !== this.option.limitItems;
      }
  },
  methods: {
    async find(search:string) {
      this.shortListed = [];
      if (!search || search.trim() === '') return;
      search = search.toLowerCase();
      this.shortListed = this.cached.reduce((accumulator, current) => {
        if (current.value.toLowerCase().includes(search)) accumulator.push(current);
        return accumulator;
      }, []);
    },
    async select(item: IKeyVal){
      this.search = undefined;
      await this.$nextTick();
      if (this.selectedItems.length ===  this.option.limitItems) return;
      if(!item || this.selectedItems.includes(item)) return;
      this.selectedItems.push(item);
      this.publishChange();
    },
    async fetchData() {
      if(this.option.listFetch) {
        this.cached =  await this.option.listFetch({locationId: this.locationId});
      }
      //  (await Tag.getByLocationId(
      //   this.locationId
      // )).map(tag => tag.name.toLowerCase())
    },
    remove(item: IKeyVal) {
      if(!this. selectedItems) return;
      let idx = this.selectedItems.findIndex((a)=> a.key === item.key);
      if (idx !== -1) this.selectedItems.splice(idx,1);
      this.publishChange();
    },
    publishChange(){
      this.option.firstValue = [...this.selectedItems];
      this.$emit('inputChange');
    }
  },
  async mounted(){
    this.locationId = this.$router.currentRoute.params.location_id;
    this.fetchData();
    if (this.option.firstValue) this.selectedItems = [...this.option.firstValue];
  }
})
</script>


