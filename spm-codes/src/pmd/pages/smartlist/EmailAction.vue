<template>
  <div
    class="hl_conversations--message-body message-input-wrap overflowdisabled"
  >
    <div class="tab-content">
      <div
        class="tab-pane fade show active overflowdisabled"
        id="email"
        role="tabpanel"
        aria-labelledby="email-tab"
      >
        <div class="form-group text-right">
          <vSelect
            :options="templateListOptions"
            label="value"
            v-model="selectedTemplate"
            placeholder="Email Templates"
            :clearable="true"
            name="template"
          />
        </div>
        <div class="email-box">
          <div class="form-group inline-form-name">
            <UITextInput
              type="text"
              placeholder="From Name"
              v-model="email.name"
              name="msgsndr2"
              autocomplete="msgsndr2"
              :class="{
                'msgsndr2': true
              }"
            />
            <!-- Hidden span so that the alignment is correct when there's an error -->
            <span class="hidden-span" v-if="errors.first('msgsndr3')"
              >Hidden</span
            >
          </div>
          <div class="form-group inline-form-email">
            <UITextInput
              type="text"
              placeholder="From email"
              v-model="email.from"
              v-validate="'email'"
              name="msgsndr3"
              autocomplete="msgsndr3"
              :class="{
                'msgsndr3': true
              }"
            />
            <span
              v-if="showFromWarning && user.type == 'agency'"
              class="hl_help-article"
              style="position: absolute; top: 62px; right: 40px"
              v-b-tooltip.hover
              title="SMTP providers has some limitations. Click here to know more."
            >
              <a href="https://youtu.be/eltAOMf3AUA" target="blank">
                <i
                  class="fa fa-exclamation-triangle text-warning"
                  id="fa-question-circle"
                ></i>
              </a>
            </span>
            <span style="color: red">{{ errors.first('msgsndr3') }}</span>
          </div>
        </div>
        <div class="form-group">
          <UITextInput
            type="text"
            placeholder="Email Subject"
            v-model="email.subject"
            v-validate="'required'"
            name="subject"
          />
          <span v-show="errors.has('subject')" class="error"
            >{{ errors.first('subject') }}
          </span>
        </div>
        <div class="form-group mb-2" v-if="email && delayLoadTinyMCE">
          <editor
            v-if="delayLoadTinyMCE && editorName && !email.downloadUrl"
            :id="editorName"
            :init="editorOptions"
            api-key="a62fc67jmelptppyvtbxwvtesdksxsumzvj5l3q16kalszes"
            v-model="email.html"
            name="body"
            v-validate="{ handlebars: [true], required: true }"
            ref="tinymce"
          >
          </editor>
          <span v-show="errors.has('body')" class="error"
            >{{ errors.first('body') }}
          </span>
        </div>
        <div
          class="message-box"
          v-if="email"
          @drop="handleDrag"
          @dragover="handleDragover"
          @dragleave="handleDragleave"
          :class="{ focused: isFocus }"
        >
          <div class="file-open py-1">
            <input
              type="file"
              id="file-open"
              ref="fileupload"
              @change="onFileChange"
              multiple
            />
            <label for="file-open">
              <i class="icon icon-file"></i>
              <span class="mx-1">Attach Files</span>
            </label>
          </div>
          <div
            style="display: inline-block"
            v-if="allAttachments && allAttachments.length > 0"
          >
            <div
              class="float-md-right mx-2 py-2"
              v-for="attachment in allAttachments"
              :key="attachment"
            >
              <div style="position: relative">
                <GHLFileAttachment :fileUrl="attachment" />
                <a
                  @click="removeAttachment(attachment)"
                  style="
                    position: absolute;
                    right: -11px;
                    top: -13px;
                    padding: 4px;
                    color: #e93d3d !important;
                  "
                  class="pointer"
                >
                  <i class="fas fa-minus-circle"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="message-action-bottom"></div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import ImageTools from '../../../util/image_tools'
import { v4 as uuid } from 'uuid'

import { EventBus } from '@/models/event-bus'
import { CustomFields } from '../../../util/custom_fields'
import GHLFileAttachment from '../../components/GHLFileAttachment.vue'
import vSelect from 'vue-select'
import Editor from '@tinymce/tinymce-vue'

import { Template, User, EmailTemplate, EmailBuilder } from '@/models'
import { FileAttachment } from '@/store/state_models'

import filter from 'lodash/filter'
import isEmpty from 'lodash/isEmpty'
import clone from 'lodash/clone'
import find from 'lodash/find'
import debounce from 'lodash/debounce'

import firebase from 'firebase/app'
import { KeyVal } from './CommonSelector.vue'

const imageTools = new ImageTools()
let triggerLinks: { [key: string]: any }[] = []
let customFieldValues = [] as { [key: string]: any }

export default Vue.extend({
  name: 'EmailAction',
  props: {
    emailOperation: Object as () => EmailTemplate,
  },
  inject: ['parentValidator'],
  components: {
    GHLFileAttachment,
    vSelect,
    Editor,
  },
  watch: {
    '$route.params.location_id': async function (n: string) {
      await this.loadUserOptions()
    },
    selectedTemplate(value: any) {
      if (!value) this.clear()
      else this.templateSelected(value)
    },
    email: {
      deep: true,
      handler: debounce(function (value) {
        this.$emit('change', value)
      }, 1000),
    },
  },
  data() {
    return {
      editorName: null as null | string,
      delayLoadTinyMCE: false as boolean | null /* very important*/,
      showTemplateModal: false,
      filesAdded: [] as FileAttachment[],
      isFocus: false,
      isDrogover: false,
      sending: false,
      errorMsg: '',
      from: '',
      fromName: '',
      name: '',
      showFromWarning: false,
      templates: [] as { [key: string]: any }[],
      selectedTemplate: undefined as { [key: string]: any } | undefined,
      editorOptions: {
        menubar: false,
        theme: 'modern',
        height: 100,
        fontsize_formats:
          '8pt 9pt 10pt 11pt 12pt 13pt 14pt 15pt 16pt 17pt 18pt 19pt 20pt 21pt 22pt 23pt 24pt 25pt 26pt 27pt 29pt 30pt 31pt 32pt 33pt 34pt 35pt 36pt 37pt 38pt 39pt 40pt 41pt 42pt 43pt 44pt 45pt 46pt 47pt 48pt',
        plugins: [
          'advlist autolink link image lists charmap hr anchor pagebreak spellchecker',
          'searchreplace wordcount visualblocks visualchars code fullscreen insertdatetime media nonbreaking',
          'table contextmenu directionality emoticons template textcolor link paste',
        ],
        toolbar:
          'bold italic bullist numlist  link image forecolor backcolor fontselect | fontsizeselect styleselect  | mybutton templates | triggerlinks',
        //init_instance_callback: this.initEditor,
        setup: function (editor: any) {
          editor.addButton('mybutton', {
            type: 'listbox',
            text: 'Custom Values',
            icon: false,
            onselect: function (e: any) {
              const val = this.value()
              editor.insertContent(val)
            },
            values: customFieldValues,
            onPostRender: function () {
              // Select the second item by default
              this.value('&nbsp;<em>Some italic text!</em>')
            },
          })

          editor.addButton('triggerlinks', {
            type: 'listbox',
            text: 'Trigger Links',
            icon: false,
            onselect: function () {
              editor.insertContent(this.value())
              this.value(null)
            },
            values: triggerLinks,
          })
        },
        paste_data_images: true,
        images_upload_handler: this.editorImagePath,
      },
      emailTemplates: [] as EmailBuilder[],
      email: {} as any,
    }
  },
  created() {
    this.$validator = this.parentValidator
  },
  async mounted() {
    this.email = this.emailOperation
    /*very important load tinymce after some delay*/
    await this.loadUserOptions()
    const location = this.$store.getters['locations/getCurrentLocation']
    if (
      location &&
      location.defaultEmailService &&
      location.defaultEmailService !== 'mailgun'
    ) {
      this.showFromWarning = await this.$store.dispatch(
        'defaultEmailService/checkFromVisibility',
        location.defaultEmailService
      )
    }
    this.fetchEmailTemplates()
  },
  beforeDestroy() {
    EventBus.$off('selected_sms_template')
    if (this.$refs.tinymce && this.$refs.tinymce.editor) {
      this.$refs.tinymce.editor.editorCommands.execCommand(
        'mceRemoveEditor',
        true,
        this.editorName
      )
      this.$refs.tinymce.editor.destroy()
    }
  },
  computed: {

    templateListOptions(): Array<any> {
      const items = filter(this.templates, (template: Template) => {
        return template.type == Template.TYPE_EMAIL || !template.type
      })
      const tinymceTemplates = items.map(
        (template: any) => new KeyVal(template.id, template.name)
      )

      const htmlTemplates  = this.emailTemplates.reduce((filteredTemplate:any, template: EmailBuilder)=> {
          if (!template.isArchived) {
            const newValue = new KeyVal(template.id, template.name)
            filteredTemplate.push(newValue);
          }
          return filteredTemplate;
      }, []);

      return [...tinymceTemplates, ...htmlTemplates]
    },
    allAttachments(): Array<string> {
      if (!this.filesAdded) return []
      if (this.filesAdded.length === 0)
        return this.email.attachments as Array<string>
      return this.filesAdded.map(f => f.url).concat(this.email.attachments)
    },
    user(): User | undefined {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
  },
  methods: {
    async fetchEmailTemplates() {
      const locationId = this.$route.params.location_id
      const allTemplates = await EmailBuilder.getByLocationId(locationId)
      this.emailTemplates = allTemplates.filter(
        (x: EmailBuilder) => x.builderVersion === '2'
      )
    },
    async loadTemplates() {
      if (!this.$route.params.location_id) return
      const snapshot = await Template.fetchByLocationId(
        this.$route.params.location_id
      ).get()

      this.templates = snapshot.docs.map(d => {
        return { id: d.id, ...d.data() }
      })
    },
    async loadTriggerLinks() {
      triggerLinks = []
      if (!this.$route.params.location_id) return
      await this.$store.dispatch(
        'conversation/fetchTriggerLinks',
        {locationId: this.$route.params.location_id}
      )
      let obj = this.$store.getters['conversation/getTriggerLinksMenuItem']
      if (obj) {
        delete obj.location_id
        triggerLinks.push(obj)
      }
    },
    async loadCustomFieldItems() {
      customFieldValues = []
      if (!this.$route.params.location_id) return
      try {
        customFieldValues.push.apply(
          customFieldValues,
          await CustomFields.getList(this.$route.params.location_id, true)
        )
      } catch (err) {
        console.error(err)
      }
    },
    menuItemSelected(item: string) {
      if (item && this.email) {
        this.email.html = this.email.html ? `${this.email.html} ${item}` : item
      }
    },
    clear() {
      if (this.email) {
        this.email.html = ''
        this.email.subject = ''
        this.email.downloadUrl = ''
      }
      if (this.email?.attachments) this.email.attachments = []
      this.filesAdded = []
      this.sending = false
    },
    handleDrag(e: any) {
      var fileList = e.dataTransfer.files
      if (fileList && fileList.length > 0) {
        this.vfileAdded(fileList[0])
      }
      this.isDrogover = false
      e.preventDefault()
    },
    handleDragover(e: any) {
      this.isDrogover = true
      e.preventDefault()
    },
    handleDragleave(e: any) {
      this.isDrogover = false
      e.preventDefault()
    },
    async onFileChange(e: any) {
      const element: HTMLInputElement = e.target
      if (!element.files || !element.files.length) return
      for (let i = 0; i < element.files.length; i++) {
        this.vfileAdded(element.files[i])
      }
      element.files = null
    },
    async vfileAdded(file: File) {
      const response: File | Blob = await imageTools.resize(file, {
        height: 1000,
        width: 1000,
      })
      const url = await this.uploadFileToBucket({
        name: file.name,
        type: file.type,
        url: URL.createObjectURL(response),
        data: response,
      })
      if (!this.email.attachments) this.email.attachments = []
      if (url) this.email.attachments.push(url)
    },
    async getImagePath(): Promise<string> {
      const locationId = this.$router.currentRoute.params.location_id
      if (!locationId)
        throw new Error('Cannot upload a file without location id')
      let imagePath = 'location/' + locationId + '/bulk_email/' + uuid()
      return imagePath
    },
    async uploadFileToBucket(attachment: any) {
      try {
        var uploadPath = firebase.storage().ref(await this.getImagePath())
        const snapshot = await uploadPath.put(attachment.data, {
          contentType: attachment.type,
          contentDisposition: `inline; filename="${attachment.name}"`,
          customMetadata: { name: attachment.name },
        })
        return await snapshot.ref.getDownloadURL()
      } catch (err) {
        console.error(err)
      }
    },
    templateSelected(value: KeyVal) {
      if (!value.value || !value.key) return
      this.email.attachments = []
      this.filesAdded = []
      const allTemplates = [...this.templates, ...this.emailTemplates]

      const emailTemplate: any = allTemplates.find(x => x.id === value.key)
      const tinymceTemplate = emailTemplate.template
      if (tinymceTemplate) {
        this.email.html = tinymceTemplate.html
        if (!isEmpty(tinymceTemplate.attachments)) {
          this.email.attachments.push(...clone(tinymceTemplate.attachments))
        }
        if (!isEmpty(tinymceTemplate.urlAttachments)) {
          this.email.attachments.push(...clone(tinymceTemplate.urlAttachments))
        }
      } else {
        this.email.downloadUrl = emailTemplate.htmlDownloadUrl
        this.email.name = emailTemplate.fromName
        this.email.from = emailTemplate.fromAddress
      }
      this.email.subject = tinymceTemplate?.subject || emailTemplate.subjectLine
    },
    removeAttachment(attachment: string) {
      const indexOfAttachment = this.email?.attachments
        ? this.email?.attachments.indexOf(attachment)
        : -1
      if (indexOfAttachment > -1) {
        this.email?.attachments?.splice(indexOfAttachment, 1)
      }

      let exists = find(this.filesAdded, { url: attachment })
      if (exists) {
        this.filesAdded.splice(this.filesAdded.indexOf(exists), 1)
      }
    },
    async editorImagePath(blobInfo: any, success: any, failure: any) {
      let attachment = blobInfo.blob()
      if (!attachment) return
      var uploadPath = firebase.storage().ref(await this.getImagePath())
      const snapshot = await uploadPath.putString(blobInfo.base64(), 'base64', {
        contentType: attachment.type,
        contentDisposition: `inline; filename="${attachment.name}"`,
        customMetadata: { name: attachment.name },
      })
      let url = await snapshot.ref.getDownloadURL()
      // if (!this.email.attachments) this.email.attachments = [];
      // if (url) this.email.attachments.push(url);
      success(url)
    },
    async loadUserOptions() {
      this.delayLoadTinyMCE = null
      this.editorName = null
      this.loadTriggerLinks()
      this.loadCustomFieldItems()
      this.loadTemplates()
      this.fetchEmailTemplates()
      setTimeout(async () => {
        this.editorName = 'editor' + Math.floor(Math.random() * 100)
        this.delayLoadTinyMCE = true
      }, 100)
    },
  },
})
</script>

<style scoped>
textarea.form-control {
  padding-right: 35px;
}
.hl_conversations--message-body .message-box .message-action-top,
.dropdown.my-2,
.file-open,
a.emoji-open {
  display: block;
}
.message-action-top {
  top: 5px !important;
  right: 15px !important;
}
.message-action-top > div,
.message-action-top > a {
  margin: 3px 0px 0px 0px !important;
}
.message-action-top > a {
  margin-left: -2px !important;
}
.dropdownwide {
  width: 100%;
}
.inline-form-name,
.inline-form-email {
  display: inline-block;
}
.inline-form-name {
  width: 39%;
  margin-right: 2%;
}
.inline-form-email {
  width: 59%;
}
</style>
