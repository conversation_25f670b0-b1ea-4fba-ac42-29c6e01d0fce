<template>
  <div class="modal" ref="modal">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h5 class="modal-title" id="exampleModalLabel">
              {{this.values.title}}
            </h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body" v-if="values.action === 'new_trigger_campaign'">
          <div class="modal-body--inner">
            <div class="form-group">
              <UITextInputGroup
                type="text"
                :placeholder="this.values.type === 'triggers' ? 'New Trigger Name' : 'New Campaign Name' "
                v-model="name"
                :label="`${this.values.type === 'triggers' ? 'Trigger' : 'Campaign' } Name`"
                v-validate="'required'"
                :data-vv-as="`${values.type === 'triggers' ? 'Trigger' : 'Campaign'} name`"
                name="trigger_name"
                :error="errors.has('trigger_name')"
                :errorMsg="errors.first('trigger_name')"
                />
            </div>
          </div>
          <div class="modal-body--inner" v-if="values.folders && values.folders.length > 0">
            <div class="form-group">
              <UITextLabel>Folder Name</UITextLabel>
              <vSelect
								label="name"
								:options="folderNames"
								v-model="folder"
								placeholder="Select folder"
								name="select_folder"
								data-vv-as="Folder name"
							></vSelect>
							<span v-show="errors.has('select_folder')" class="--red">Must select a folder.</span>
            </div>
          </div>
        </div>
        <div v-else class="modal-body">
          <div class="modal-body--inner">
            <div class="form-group">
              <UITextInputGroup
                type="text"
                placeholder="New folder name"
                v-model="folderName"
                v-validate="'required'"
                data-vv-as="folder name"
                name="folder_name"
                label="Folder name"
                :error="errors.has('folder_name')"
                :errorMsg="errors.first('folder_name')"
              />
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner nav">
            <UIButton type="button" use="outline" @click="hide()">Close</UIButton>
            <UIButton type="button" use="primary" @click="save(values.folder)">Save</UIButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
declare var $: any;

import vSelect from "vue-select";
import Folder from '@/models/folder'
import { EventBus } from '@/models/event-bus';

export default Vue.extend({
  props: ["values"],
  components: { vSelect },
  data() {
    return {
      currentLocationId: '',
      name: '',
      folderName: '',
      folder: undefined as any | undefined,
    }
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
        if(this.values.action === 'rename_folder') {
      this.folderName = this.values.name
    }
  },
  watch: {
    values(values) {
      if (values.visible) $(this.$refs.modal).modal("show");
    },
    '$route.params.location_id': function (id) {
      this.currentLocationId = id;
		},
  },
  computed: {
    folderNames() {
      return this.values.folders.map((f: any) => {
        return { name: f.name, id: f.id }
      })
    }
  },
  methods: {
    reset() {
      this.name = ''
      this.folder = {
        id: '',
        name: ''
      }
    },
    async save(renameFolder: Folder) {
      const result = await this.$validator.validateAll(); // or validateAll
      if (!result) { return false; }

      if (this.values.action === 'new_trigger_campaign') {
        if (this.values.type === 'triggers') {
        this.$router.push({
          name: 'triggers_detail', params: { location_id: this.currentLocationId, trigger_id: 'new', folder_id: this.folder ? this.folder.id : undefined, trigger_name: this.name }
        });
        } else if(this.values.type === 'campaigns') {
          this.$router.push({
            name: 'campaign_edit', params: { location_id: this.currentLocationId, campaign_id: 'new', folder_id: this.folder ? this.folder.id : undefined, campaign_name: this.name }
          });
        }
      } else if(this.values.action === 'new_folder') {
        this.hide()
        const folder = new Folder();
        folder.locationId = this.currentLocationId
        folder.name = this.folderName
        folder.type = this.values.type
        await folder.save()
        this.reset()
      } else {
        this.hide()
        renameFolder.name = this.folderName
        await renameFolder.save()
        this.reset()
      }
      this.hide(true)
      this.reset()
    },
    hide(sameRoute: boolean = false) {
      this.$emit('hidden', sameRoute)
    },
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker('refresh');
		}
		if (this.values && this.values.visible) {
			$(this.$refs.modal).modal("show");
    }
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker();
    }

		const _self = this;
		$(this.$refs.modal).on("hidden.bs.modal", async function () {
      _self.hide()
		});

		if (this.values && this.values.visible) {
			$(this.$refs.modal).modal("show");
		}
  },
  beforeDestroy() {
    this.name = ''
    this.selectedFolder = ''
  }
})
</script>

<style lang="css" scoped>
  .dropdown-custom-width {
    width: 88%;
  }
</style>

<style>
  .selectItem .dropdown-menu.show {
    right: 0;
    left: auto;
    width: auto;
  }
  .selectItem .dropdown-menu.show > li .dropdown-item {
    white-space: normal !important;
  }
</style>
