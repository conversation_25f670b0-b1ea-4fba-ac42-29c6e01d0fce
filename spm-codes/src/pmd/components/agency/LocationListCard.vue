<template>
  <div class="card hl_location--item">
    <div class="hl_location--item-inner">
      <div class="hl_location--logo">
        <img v-if="logoUrl" :src="logoUrl" />
      </div>
      <div class="card-body">
        <div class="hl_location--header">
          <div class="hl_location--header-left">
            <h3>
              <router-link
                tag="a"
                :to="{
                  name: 'account_detail',
                  params: { account_id: location.id },
                }"
                >{{ location.name }}</router-link
              >
              <span
                v-if="location.status === LocationStatus.PROSPECT"
                class="label --green"
                >Ready to sell</span
              >
              <span
                v-else-if="location.status === LocationStatus.ACCOUNT"
                class="label --blue"
                >Closed (Won)</span
              >
            </h3>
            <!-- <div v-if="isCalendarV3OnAtCompanyLevel" class="d-flex align-items-center py-2">
              <div class="toggle">
                <input
                  type="checkbox"
                  class="tgl tgl-light"
                  :id="'tgl-calendar-v3-for-location-' + location.id"
                  v-model="location.isCalendarV3On"
                  @change="saveLocation(location)"
                />
                <label class="tgl-btn" :for="'tgl-calendar-v3-for-location-' + location.id"></label>
              </div>
              <label class="mb-0 pl-2 label--tgl-calendar-v3">
                New Calendar
              </label>
              <i v-show="saving" class="fa fa-snowflake fa-spin text-primary ml-2"></i>
            </div> -->
          </div>
          <div class="hl_location--header-right">
            <div
              v-if="products.indexOf('listings') > -1"
              class="hl_icon --green"
              v-b-tooltip.hover
              title="Listings"
            >
              <ListIcon />
            </div>
            <div
              v-if="products.indexOf('reviews') > -1"
              class="hl_icon --yellow"
              v-b-tooltip.hover
              title="Reviews"
            >
              <StartIcon />
            </div>
            <div
              v-if="products.indexOf('conversations') > -1"
              class="hl_icon --blue"
              v-b-tooltip.hover
              title="Conversations"
            >
              <ConversationIcon />
            </div>
            <div
              v-if="products.indexOf('social') > -1"
              class="hl_icon --pink"
              v-b-tooltip.hover
              title="Social"
            >
              <SocialIcon />
            </div>
            <b-dropdown
              variant="light"
              size="sm"
              no-caret
              class="more-options-dropdown"
              v-if="showSaasMode && saasMode !== 'activated'"
            >
              <template #button-content>
                <i class="fas fa-ellipsis-h"></i>
              </template>
              <!-- <b-dropdown-item> Delete Sub-Account </b-dropdown-item> -->
              <b-dropdown-item
                @click="switchToSaaS"
                v-if="
                  showSaasMode &&
                  saasMode !== 'activated' &&
                  saasMode === 'not_activated'
                "
              >
                Switch to SaaS
              </b-dropdown-item>
              <b-dropdown-item
                @click="switchToSaaS"
                v-else-if="showSaasMode && saasMode !== 'activated'"
              >
                Request Payment Again
              </b-dropdown-item>
            </b-dropdown>
          </div>
        </div>
        <div class="flex--space-between">
          <ul class="hl_location--info-list list-unstyled">
            <li>
              <p>
                <i class="icon icon-a-pin"></i>
                {{ location.address }} {{ location.city }}, {{ location.state }}
                {{ location.postalCode }}
              </p>
            </li>
            <li>
              <p>
                <i class="icon icon-a-phone"></i>
                <PhoneNumber type="display" v-model="location.phone" />
              </p>
            </li>
          </ul>
          <div class="addons">
            <div class="imgHolder">
              <WordpressActiveIcon
                title="Wordpress active"
                v-if="wordpressInstalled"
                v-b-tooltip.hover.top="'WordPress sale successful'"
              />
              <WordpressInactiveIcon
                title="Wordpress inactive"
                v-b-tooltip.hover.top="'WordPress not sold yet'"
                v-else
              />
            </div>
            <div class="imgHolder">
              <YextActiveIcon
                title="Yext active"
                v-if="yextActive"
                v-b-tooltip.hover.top="'Yext sale successful'"
              />
              <YextInactiveIcon
                title="Yext inactive"
                v-else
                v-b-tooltip.hover.top="'Yext not sold yet'"
              />
            </div>
            <div v-if="showSaasMode" class="saas-mode">
              <div
                v-if="saasMode === 'activated'"
                style="display: flex; align-items: center"
              >
                <div class="imgHolder">
                  <ActivePlanIcon
                    title="SaaS subscription active"
                    v-b-tooltip.hover.top="'SaaS subscription plan is running'"
                    v-if="location.settings.saasSettings.saasPlanId"
                  />
                  <InactivePlanIcon
                    v-b-tooltip.hover.top="
                      'SaaS subscription plan is not enabled'
                    "
                    title="SaaS subscription inactive"
                    v-else
                  />
                </div>
                <div class="imgHolder">
                  <ActiveEmailIcon
                    title="Email rebilling active"
                    v-if="emailRebillingEnabled"
                    v-b-tooltip.hover.top="
                      'Email usage is being rebilled to your client'
                    "
                  />
                  <InactiveEmailIcon
                    title="Email rebilling inactive"
                    v-else
                    v-b-tooltip.hover.top="'Email usage is not being rebilled'"
                  />
                </div>

                <div class="imgHolder">
                  <ActiveTwilioIcon
                    v-b-tooltip.hover.top="
                      'Telephone usage is being rebilled to your client'
                    "
                    title="Twilio rebilling active"
                    v-if="twilioRebillingEnabled"
                  />
                  <InactiveTwilioIcon
                    v-b-tooltip.hover.top="
                      'Telephone usage is not being billed'
                    "
                    title="Twilio rebilling inactive"
                    v-else
                  />
                </div>
              </div>

              <div class="imgHolder">
                <ActiveSaasIcon
                  v-b-tooltip.hover.top="'SaaS mode is activated'"
                  title="SaaS mode is activated"
                  v-if="saasMode === 'activated'"
                  style=""
                />
                <PendingSaasIcon
                  v-b-tooltip.hover.top="'Payment is Pending'"
                  title="Payment is Pending"
                  v-else-if="saasMode === 'setup_pending'"
                />
                <InactiveSaasIcon
                  v-b-tooltip.hover.top="'SaaS mode is not activated'"
                  title="SaaS mode is not activated"
                  v-else-if="saasMode === 'not_activated'"
                />
              </div>
              <!-- <EmailRebillingIcon
              title="Email rebilling active"
              v-if="emailRebillingEnabled"
            /> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="hl_location--footer card-footer">
      <div class="hl_location--footer-left">
        <ul class="list-inline">
          <li
            class="list-inline-item saas-wallet__balance"
            :class="
              parseInt(walletBalance[location.id]) < 0
                ? '--negative'
                : '--positive'
            "
            v-if="
              showSaasMode &&
              saasMode === 'activated' &&
              walletBalance[location.id]
            "
          >
            <i class="fas fa-wallet wallet-icon"></i>
            ${{ walletBalance[location.id] }}
            <span
              class="hl_tooltip"
              v-b-tooltip.hover
              title="Remaining wallet balance of this SaaS Location"
              ><i class="fas fa-info-circle"></i
            ></span>
          </li>
          <li class="list-inline-item">
            <!-- <a href="javascript:void(0);" @click.stop="startScanReport" v-if="!location.scanReportId">
							<i class="icon icon-a-report"></i> Create snapshot report
						</a>
						<p v-if="location.scanReportId && location.scanReportId != 'in_progress'">
							<a :href="'/scan_report/' + location.scanReportId" target="_blank">
								<i class="icon icon-a-report"></i> Download snapshot report
							</a>
							<a href="javascript:void(0);" @click.stop="startScanReport">
								<i class="icon icon-a-refresh"></i>
							</a>
						</p>
						<a
							href="javascript:void(0);"
							@click.stop="startScanReport"
							v-if="location.scanReportId == 'in_progress'"
						>
							<i class="icon icon-a-refresh"></i> Snapshot report in progress...
						</a>-->
          </li>
          <!-- <li class="list-inline-item ">
                        <p>
                            <i class="icon icon-a-mail "></i> Claris Care email campaign
                            <i class="icon icon-a-refresh "></i>
                        </p>
					</li>-->
        </ul>
      </div>
      <div class="hl_location--footer-right">
        <a
          href="javascript:void(0);"
          class="switch-loction-btn"
          @click="swtchLocation(location.id)"
          >Switch to Account</a
        >
        <router-link
          tag="a"
          :to="{ name: 'account_detail', params: { account_id: location.id } }"
        >
          View details
          <i class="icon icon-arrow-right-1"></i>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Location, LocationStatus } from '@/models/'
import PhoneNumber from '../util/PhoneNumber.vue'
import ListIcon from '@/assets/pmd/img/icon-listing.svg'
import StarIcon from '@/assets/pmd/img/icon-star.svg'
import ConversationIcon from '@/assets/pmd/img/icon-conversation.svg'
import SocialIcon from '@/assets/pmd/img/icon-social.svg'
import ActivePlanIcon from '@/assets/pmd/img/saas/icons-v2/plan-active.svg'
import InactivePlanIcon from '@/assets/pmd/img/saas/icons-v2/plan-inactive.svg'
import ActiveEmailIcon from '@/assets/pmd/img/saas/icons-v2/email-active.svg'
import InactiveEmailIcon from '@/assets/pmd/img/saas/icons-v2/email-inactive.svg'
import ActiveTwilioIcon from '@/assets/pmd/img/saas/icons-v2/twilio-active.svg'
import InactiveTwilioIcon from '@/assets/pmd/img/saas/icons-v2/twilio-inactive.svg'
import ActiveSaasIcon from '@/assets/pmd/img/saas/icons-v2/saas-active.svg'
import PendingSaasIcon from '@/assets/pmd/img/saas/icons-v2/saas-pending.svg'
import InactiveSaasIcon from '@/assets/pmd/img/saas/icons-v2/saas-inactive.svg'
import EmailRebillingIcon from '@/assets/pmd/img/saas/icon_email_rebilling.svg'
import WordpressActiveIcon from '@/assets/pmd/img/saas/wp_enable.svg'
import WordpressInactiveIcon from '@/assets/pmd/img/saas/wp_disable.svg'
import YextActiveIcon from '@/assets/pmd/img/saas/yxt_enable.svg'
import YextInactiveIcon from '@/assets/pmd/img/saas/yxt_disable.svg'

export default Vue.extend({
  props: {
    source: Object,
    showSaasMode: {
      type: Boolean,
      default: false,
    },
    walletBalance: Object,
    // isCalendarV3OnAtCompanyLevel: Boolean
  },
  components: {
    PhoneNumber,
    ListIcon,
    StarIcon,
    ConversationIcon,
    SocialIcon,
    ActivePlanIcon,
    InactivePlanIcon,
    ActiveEmailIcon,
    InactiveEmailIcon,
    ActiveTwilioIcon,
    InactiveTwilioIcon,
    ActiveSaasIcon,
    PendingSaasIcon,
    InactiveSaasIcon,
    EmailRebillingIcon,
    WordpressInactiveIcon,
    WordpressActiveIcon,
    YextActiveIcon,
    YextInactiveIcon,
  },
  created() {
    this.getLogoUrl()
  },
  computed: {
    allowBetaAccess() {
      return this.location.allowBetaAccess
    },
    company() {
      return this.$store.state.company.company
    },
    location() {
      return this.source
    },
    products(): string[] {
      return lodash.filter(
        lodash.map(this.location.productStatus, (value, key) => {
          return value === true ? key : ''
        })
      )
    },
    saasMode(): string {
      const saasMode = this.location.settings?.saasSettings?.saasMode
      return saasMode || 'not_activated'
    },
    twilioRebillingEnabled(): boolean {
      return (
        this.saasMode === 'activated' &&
        this.location.settings.saasSettings.twilioRebilling &&
        !!this.location.settings.saasSettings.twilioRebilling.enabled
      )
    },
    emailRebillingEnabled(): boolean {
      return (
        this.saasMode === 'activated' &&
        this.location.settings.saasSettings.mailgunRebilling &&
        !!this.location.settings.saasSettings.mailgunRebilling.enabled
      )
    },
    wordpressInstalled() {
      return (
        this.location.reseller &&
        this.location.reseller.wordpress &&
        this.location.reseller.wordpress.paymentStatus === 'COMPLETE'
      )
    },
    yextActive() {
      return (
        this.location.reseller &&
        this.location.reseller.yext &&
        this.location.reseller.yext.paymentStatus === 'COMPLETE'
      )
    },
  },
  data() {
    return {
      LocationStatus,
      logoUrl: '',
      saving: false,
    }
  },
  methods: {
    enableWorkflowBuilder() {
      if (this.location.allowBetaAccess)
        this.location.allowBetaAccess.workflow = true
      else {
        this.location.allowBetaAccess = {
          workflow: true,
        }
      }
      this.location.save()
    },
    dispatch(componentName, eventName, ...rest) {
      let parent = this.$parent || this.$root
      let name = parent.$options.name
      while (parent && (!name || name !== componentName)) {
        parent = parent.$parent
        if (parent) {
          name = parent.$options.name
        }
      }
      if (parent) {
        parent.$emit.apply(parent, [eventName].concat(rest))
      }
    },
    switchToSaaS() {
      this.dispatch('ListAccounts', 'switchToSaaS', this.location.id)
    },
    async saveLocation(location: Location) {
      this.saving = true
      await this.location.save()
      this.saving = false
    },
    stripWebsite(website: string) {
      if (!website) return
      let url = website
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'http://' + website
      }
      return new URL(url).hostname.replace('www.', '')
    },
    testImageLoad(src: string): Promise<string> {
      return new Promise((resolve, reject) => {
        let img = new Image()
        img.onload = function () {
          resolve(src)
        }
        img.onerror = () => {
          reject()
        }
        img.onabort = () => {
          reject()
        }
        img.src = src
      })
    },
    async getLogoUrl() {
      if (this.location.logoUrl) {
        this.logoUrl = this.location.logoUrl
      } else if (this.location.website) {
        let website = this.stripWebsite(this.location.website)
        let url = 'https://logo.clearbit.com/' + website
        try {
          this.logoUrl = await this.testImageLoad(url)
        } catch (err) {}
      }
    },
    async startScanReport() {
      let confirmMsg =
        'Are you sure you want to run a scan report for this location?'

      if (this.location.scanReportId) {
        confirmMsg =
          'Are you sure you want to re-run a scan report for this location?'
      }

      if (confirm(confirmMsg)) {
        this.location.scanReportId = 'in_progress'
        await this.location.save()

        let response = await this.$http.get('/scan/create_scan_task', {
          params: {
            location_id: this.location.id,
          },
        })
      }
    },

    swtchLocation(locationId: string) {
      this.$router.push({
        name: 'dashboard',
        params: { location_id: locationId },
      })
    },
  },
})
</script>
<style lang="scss">
.flex--space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.switch-loction-btn {
  margin-right: 20px;
}
.more-options-dropdown {
  .btn.dropdown-toggle {
    background-color: #ffffff !important;
    padding: 0px !important;
    color: #718096;
    margin-top: -16px;
    &:after {
      display: none;
    }
  }
}
.saas-mode {
  padding-right: 24px;
  display: flex;
  align-items: flex-start;
}
.saas-wallet__balance {
  font-weight: 600;
  font-size: 17px;
  line-height: 20px;
  color: #48bb78;
  &.--negative {
    color: #f56565;
  }
  .wallet-icon {
    // color: #ccd8ed;
    color: #26a69a;
  }
  .hl_tooltip {
    background-color: #ffffff;
    color: #718096;
  }
}

.imgHolder {
  display: grid;
  justify-items: center;
  margin: 0 10px;
  position: relative;
  img,
  svg {
    width: 30px;
    height: 30px;
    margin-bottom: 10px;
  }
  .status_indicator {
    position: absolute;
    top: -10px;
    right: 3px;
    color: #68d391;
  }
  span {
    text-align: center;
    color: #2f2b2b;
    font-weight: 700;
    display: block;
    line-height: 16px;
    white-space: nowrap;
  }
}
.imgHolder:last-child {
  margin-right: 0;
}
.--inactive-label {
  opacity: 0.4;
}

.addons {
  display: flex;
}
</style>
