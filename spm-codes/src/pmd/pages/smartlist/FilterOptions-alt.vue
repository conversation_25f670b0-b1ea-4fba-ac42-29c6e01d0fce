<template>
  <div class="filter-item">
    <a ref="refcol" data-toggle="collapse" @click.stop.prevent="toggle()"
       :aria-expanded="expand  ? 'true' : 'false'"
       role="button">
       <div class="d-inline-block" style="width:100%">
          <!-- <div class="d-inline-block" style="width:90%"><slot name="title"></slot></div> -->
          <div class="d-inlin-block container-flex" style="width:100%" slot="title">
            <span class="float-left text-wrap" style="max-width:85%">{{group.title}}</span>
            <span class="float-right pointer mx-2" @click.stop.prevent="removeFilter">
              <i class="fa fa-times fa-mods zoomable"></i>
            </span>
          </div>
          <div class="float-right zoomable"> <i class="fa fa-chevron-down fa-mods"></i></div>
       </div>
    </a>
    <div class="collapse" v-bind:class="{show : value}" >
      <div class="filter-option">
          <!-- <slot name="filter"></slot> -->
        <div class="form-control-group" v-if="group" slot="filter">
          <b-form-group v-if="group.options">
              <b-form-radio
                  v-for="o in group.options"
                  v-bind:key="o.id"
                  v-model="selected" :value="o"
                  :name="o.filterName">
                  {{o.conditionUX}} <!-- - {{o.selected}} -->
                  <div class="py-1" v-if="o === selected && o.listFetch && o.isAutoComplete">
                    <ArrayInputAutoComplete v-on:inputChange="publish" :bus="bus" :option="o"></ArrayInputAutoComplete>
                  </div>
                  <div class="py-1" v-else-if="o === selected && o.listFetch && !o.isAutoComplete">
                    <ArrayInputDropDown v-on:inputChange="publish" :bus="bus" :option="o"></ArrayInputDropDown>
                  </div>
                  <div class="py-1" v-else-if="o === selected && selected.firstValueMandatory && !selected.secondValueMandatory">
                    <FilterInput v-on:inputChange="publish" :bus="bus" :option="o"></FilterInput>
                  </div>
                  <div class="py-1" v-else-if="o === selected && selected.secondValueMandatory">
                    <FilterMultiInput  v-on:inputChange="publish" :bus="bus" :option="o"></FilterMultiInput>
                  </div>
              </b-form-radio>
          </b-form-group>
      </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">

import Vue from 'vue';
import {IFilterOption} from './vm/option_vm'
import {IFilter} from './vm/filter_vm'
import FilterInput from './FilterInput.vue'
import FilterMultiInput from './FilterMultiInput.vue'
import FilterExpander from './FilterExpander.vue'
import ArrayInputDropDown from './ArrayInputDropDown.vue'
import ArrayInputAutoComplete from './ArrayInputAutoComplete.vue'

export default Vue.extend({
  name: 'FilterOption2',
  props : {
    bus : Vue,
    group : IFilter,
    value: Boolean,
  },
  components :  {
    FilterInput,
    FilterMultiInput,
    FilterExpander,
    ArrayInputDropDown,
    ArrayInputAutoComplete
  },
  data () {
    return {
      raiseSelectionEvent:false,
      waitTime:500,
      selected:undefined,
      debouncer:undefined as NodeJS.Timer | undefined,
    };
  },
  computed: {
     isExpanded(): Boolean{
       return false; //this.group.expandedUX || false ;
     },
     expand: {
			get() { return this.value },
      set(v) {
          //alert(v);
          this.$emit("input", v);
          this.expandChange();
       }
		}
  },
  watch : {
    selected(n) {
      if(this.raiseSelectionEvent === false) return; //otherwise it runs es search two times when list changes.
      if (this.group && n) {
        this.group.selectedOption = n;
        this.publish();
      }
    },
  },
  mounted(){
    if (this.group) {
      this.selected = this.group.selectedOption;
      this.$nextTick(()=> this.raiseSelectionEvent = true);
    }
  },
  methods: {
    async expandChange(){
      this.$emit('expanding',this.group)
    },
    async publish(){
       this.$emit('filterUpdate',this.group);
    },
    removeFilter(){
       this.$emit('filterRemove',[this.group]);
    },
    async toggle(){
      this.expand = !this.expand;
    },
  },
})
</script>

<style scoped>

  .filter-option {
    padding:10px !important;
    padding-top :10px !important;
    padding-right:10px !important;
    padding-left:10px !important;
  }

  .bootstrap-select .btn.dropdown-toggle .filter-option {
    padding:10px !important;
    padding-top :10px !important;
    padding-right:10px !important;
    padding-left:10px !important;
  }

</style>
