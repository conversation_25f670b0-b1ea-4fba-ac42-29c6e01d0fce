<template>
  <div id="onboardingV2" ref="onboardingV2"></div>
</template>

<script lang="ts">
import Vue from 'vue'
import Postmate from 'postmate'
import { trackGaPageView } from '@/util/helper'
import axios from 'axios'
import { mapState } from 'vuex'
import moment from 'moment-timezone'
import {
  Company,
  User,
  UserState,
  Location,
  PrivateUser,
  CloneAccount,
  Contact,
} from '@/models'
import defaults from '@/config'
import firebase from 'firebase/app'
import depreciatedFeatures from '@/util/depreciated_features';
import LocationsService from '@/services/LocationsService'

export default Vue.extend({
  data() {
    return {
      handshake: {} as any,
      onboardingData: {},
      baseUrl: defaults.baseUrl,
      newOnboardingFrontend: defaults.newOnboardingFrontend,
      firstName: '',
      childLoaded: false,
      authKey: '',
      importing: false,
      appLoader: false,
    }
  },
  mounted() {
    if (this.company && this.user) {
      this.loadMicroApp()
      this.childLoaded = true
    }
    window.addEventListener('popstate', () => {
      console.log('popstate called', this.direction)
      this.handshake.then(child => {
        child.call('routeChange', { path: this.$route.path, direction: 'back' })
      })
    })
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
  },
  watch: {
    company: function (newValue, oldValue) {
      if (!oldValue && newValue && this.user && !this.childLoaded) {
        this.loadMicroApp()
      }
    },
  },
  methods: {
    async loadMicroApp() {
      if (
        this.company &&
        !this.company.onboardingInfo.pending &&
        !this.company.onboardingInfo.location &&
        !this.company.onboardingInfo.conversationDemo
      ) {
        this.$router.replace({ name: 'login' })
        return false
      }
      if (this.company?.onboardingInfo.location || this.company?.onboardingInfo.conversationDemo) {
        const isExists = await this.isLocationExists()
        if (isExists) {
          return this.$router.replace({ name: 'dashboard', replace: true })
        } else if (this.company.onboardingInfo.customerCount !== 'I don’t have any customers yet' && 
        this.company?.onboardingInfo.location) {
          return this.$router.replace({ name: 'launchpad', replace: true })
        } else {
          return this.createDefaultLocation('')
        }
      }
      this.handshakeIframe()
    },
    async handshakeIframe() {
      try {
        let snapshotName, snapshotType
        this.firstName = this.user?.firstName
        const locationId = this.company?.onboardingInfo?.locationId
        this.authKey = await firebase.auth().currentUser.getIdToken()

        if (process.env.NODE_ENV === 'development') Postmate.debug = true
        this.handshake = new Postmate({
          container: this.$refs.onboardingV2 as HTMLElement,
          url: `${this.newOnboardingFrontend}${this.$route.path}`,
          name: 'onboarding-v2-home',
          classListArray: ['onboarding-iframe'],
          model: {
            websiteSignup: this.user?.token ? true : false,
            firstName: this.user?.firstName,
            authKey: this.authKey,
            isPasswordPending: this.user?.isPasswordPending,
            locationId: locationId,
            snapshotId: this.company.onboardingInfo.snapshotId,
            snapshotName: snapshotName,
            snapshotType: snapshotType,
            isAgencyUser: this.user?.type == User.TYPE_AGENCY
          },
        })
        this.handshake.then(child => {
          child.on('spm-ts', (data: any) => {
            if (data.newTab) {
              let routeUrl = this.$router.resolve(data.router)
              window.open(routeUrl.href, '_blank')
              console.log(routeUrl.href)
            } else {
              this.$router.push(data.router)
            }
          })
          child.on('ga-event', (data: any) => {
            trackGaPageView(data)
          })
          child.on('load', data => {
            document.getElementById('app').classList.remove('loading')
          })
          child.on('onboarding-data', data => {
            this.onboardingData = JSON.parse(data)
            this.$nextTick(() => {
              this.updateData()
            })
          })
          child.on('onboarding-dashboard', () => {
            this.goToDashboard()
          })
          child.on('onboarding-launchpad', data => {
            if (data) {
              const payload = JSON.parse(data)
              this.onboardingData.locationId = payload.locationId
              this.updateLocation(payload)
            }
            this.goToLaunchpad()
          })
          child.on('onboarding-location', data => {
            const payload = JSON.parse(data)
            this.onboardingData.locationId = payload.locationId
            this.onboardingLocation()
          })
          child.on('onboarding-complete', () => {
            this.onboardingComplete()
          })
          child.on('default-location', () => {
            this.createDefaultLocation()
          })
          child.on('default-location-launchpad', () => {
            this.createDefaultLocation('launchpad')
          })
        })
      } catch (err) {}
    },
    async addProfitwellTrail(category: string, bucket: string) {
      try {
        await this.$http.post('/profitwell/add_trait', {
          customer_id: this.company.stripeId,
          customer_email: this.company.email,
          category: category,
          bucket: bucket,
        })
      } catch (err) {
        console.error(err)
      }
    },
    async createDefaultLocation(redirect: string) {
      this.showHideLoader()
      // Creating new blank location
      const body = {
        businessName: this.company.name || 'Default account',
        phone: this.user.phone,
        email: this.user.email,
        isContactCreate: true,
        firstName: this.user.firstName || '',
        lastName: this.user.lastName || '',
        address: this.company.address,
        city: this.company.city,
        state: this.company.state,
        postalCode: this.company.postalCode,
        website: this.company.website || ''
      }

      const options = {
        headers: {
          'Content-Type': 'application/json',
          'internal-channel': 'web',
        },
      }

      try {
        const location = await this.publicApi.post(
          '/v1/locations/onboarding',
          body,
          options
        )
        if (location.status == 200) {
          this.showHideLoader()
          await Location.collectionRef().doc(location.data.id).update({
            depreciated_features: depreciatedFeatures
          });
          const company = await Company.getById(this.company.id)
          company.onboardingInfo.locationId = location.data.id
          company.onboardingInfo.location = false
          company.onboardingInfo.conversationDemo = true
          await company.save()
          // Initializing default location with default snapshot
          try {
            const snapshotType = 'default'
            await this.$http.post(
              `/snapshot/${snapshotType}/load_bg/${location.data.id}`,
              {
                snapshotType,
              }
            )

            this.handshake.then(child => {
              child.call('conversationPage', {
                locationId: location.data.id,
              })
            })
            this.$router.push({ name: 'onboarding_micro_app_conversation' })
          } catch (err) {
            this.showHideLoader()
            this.$router.push({ name: 'dashboard' })
          }
        }
      } catch (error) {
        this.showHideLoader()
        console.log(error)
      }
    },
    async isLocationExists() {
      let isLocation = false
      this.showHideLoader()
      try {
        if (this.company) {
          let locations = []
          await LocationsService.search(this.company.id, false, undefined, undefined, 50).then((response) => {
              locations = response.locations.map((location: any) => {
                  return {...location, id: location._id}
              })
          }).catch(err => {
          })
          if (locations.length > 0) { 
            isLocation = true
            await Company.collectionRef().doc(this.company.id).update({
              'onboarding_info.location': false,
              'onboarding_info.conversationDemo': false
            })
          }
        }
      } catch (err) { }
      this.showHideLoader()
      return isLocation
    },
    async createDefaultCampaign(locationId: string) {
      try {
        // Adding user to a default onboarding campaign.
        let response = await this.$http.post('/onboarding/campaign', {
          location_id: locationId,
          first_name: this.user.firstName,
          last_name: this.user.lastName,
          name: this.user.fullName || 'New User',
          email: this.user.email,
          phone: this.user.phone,
        })
      } catch (err) {
        console.error(err)
      }
    },
    async updateLocation(payload: any) {
      let company = await Company.getById(this.company.id);
      company.onboardingInfo.locationId = payload.locationId
      company.onboardingInfo.location = false
      await company.save()
    },
    async updateData() {
      let company = await Company.getById(this.company.id)
      company.onboardingInfo = {
        industryServed: this.onboardingData.industryServed,
        customerCount: this.onboardingData.customerCount,
        haveWebsite: this.onboardingData.haveWebsite,
        websiteUrl: this.onboardingData.websiteUrl,
        tools: this.onboardingData.tools,
        pending: false,
        location: true,
        snapshotId: this.company?.onboardingInfo.snapshotId || '',
      }
      if (this.onboardingData.address) {
        if (this.onboardingData.addressLine) {
          this.onboardingData.address =
            this.onboardingData.address + ', ' + this.onboardingData.addressLine
        }
        company.address = this.onboardingData.address
        company.city = this.onboardingData.city
        company.country = this.onboardingData.country
        company.postalCode = this.onboardingData.zipcode
        company.state = this.onboardingData.region
        company.termsOfService = this.onboardingData.tandc
        company.termsOfServiceAcceptedBy = this.user.id
        company.termsOfServiceAcceptor = this.user.id
        company.termsOfServiceAcceptedDate = moment().utc()
        company.isReselling = this.onboardingData.isReselling
      }
      if (this.onboardingData.address) {
        if (this.onboardingData.addressLine) {
          this.onboardingData.address =
            this.onboardingData.address + ', ' + this.onboardingData.addressLine
        }
        company.address = this.onboardingData.address
        company.city = this.onboardingData.city
        company.country = this.onboardingData.country
        company.postalCode = this.onboardingData.zipcode
        company.state = this.onboardingData.region
        company.termsOfService = this.onboardingData.tandc
        company.termsOfServiceAcceptedBy = this.user.id
        company.termsOfServiceAcceptor = this.user.id
        company.termsOfServiceAcceptedDate = moment().utc()
      }
      await company.save()
      try {
        if (this.onboardingData.password) {
          this.$store.commit('user/updatePasswordUpdatedInOnboarding', true)
          let user = await User.getById(this.user.id)
          let currentPrivateUser = new PrivateUser(
            await user.privateDataRef.get()
          )
          currentPrivateUser.passwordHash = this.onboardingData.password
          await currentPrivateUser.save()
          await user.passwordChanged()
        }

        const p1 = this.addProfitwellTrail(
          'Number of clients',
          this.onboardingData.customerCount
        )
        const p2 = this.addProfitwellTrail(
          'Industry Served',
          this.onboardingData.industryServed
        )
        const p3 = this.addProfitwellTrail(
          'Website',
          this.onboardingData.websiteUrl || `Don't have Website.`
        )
        const p4 = this.addProfitwellTrail(
          'Previously used tools',
          JSON.stringify(this.onboardingData.tools)
        )
        const onboardingInfo = this.updateOnboardingInfo()
        const stripeUpdate = this.updateStripeCustomer(company)

        await Promise.all([p1, p2, p3, p4, onboardingInfo, stripeUpdate])

        let _self = this
        //adding delay so vuex store get time to updated
        setTimeout(function () {
          _self.gotToSearch()
        }, 2000)
      } catch (error) {
        console.log('Erorr updating onboarding data', error)
      }
    },
    async gotToSearch() {
      if (
        this.onboardingData.customerCount !== 'I don’t have any customers yet'
      ) {
        this.$router.push({
          name: 'launchpad',
          replace: true,
        })
      } else {
        await this.createDefaultLocation('')
      }
    },
    async goToLaunchpad() {
      let _self = this
      await this.onboardingComplete()
      _self.$router.push({
        name: 'location_launchpad',
        params: {
          location_id: this.onboardingData.locationId,
        },
      })
    },
    async goToDashboard() {
      let _self = this
      await this.onboardingComplete()
      setTimeout(function () {
        _self.$router.push({ name: 'dashboard', replace: true })
      }, 2000)
    },
    async onboardingComplete() {
      let company = await Company.getById(this.company.id)
      company.onboardingInfo.conversationDemo = false
      await company.save()
      let user = await User.getById(this.user.id)
      user.token = ''
      await user.save()
    },
    async onboardingLocation() {
      let company = await Company.getById(this.company.id)
      company.onboardingInfo.location = false
      company.onboardingInfo.locationId = this.onboardingData.locationId
      company.onboardingInfo.conversationDemo = true
      await company.save()
      await this.updateOnboardingInfo()
      const cloneAccount = await CloneAccount.getByCompanyId(this.company.id)
      if (cloneAccount) {
        await this.$http.post(
          `/snapshot/${cloneAccount[0].id}/load_bg/${this.onboardingData.locationId}`,
          {
            snapshotType: cloneAccount[0].type,
          }
        )
      }
    },
    async updateOnboardingInfo() {
      try {
        // might fail due to 401 [an-authorized]
        const customFields = await this.$http.post('/onboarding', {
          client_count: this.onboardingData.customerCount,
          niche: this.onboardingData.industryServed,
          previous_product: this.onboardingData.tools,
          website: this.onboardingData.websiteUrl,
          is_reselling: this.onboardingData.isReselling ? 'Yes' : 'No',
        })
      } catch (err) {
        console.error('Failed to update onboarding-info in smart-list !!', err)
      }
    },
    async preventPasswrodChangeLogout() {
      await this.user.passwordChanged()
    },
    async updateStripeCustomer(company: Company) {
      try {
        if (this.onboardingData.address && company.stripeId) {
          await this.$http.put('/onboarding/customer_update', {
            metadata: {
              reseller: this.onboardingData.isReselling ? 'Yes' : 'No',
              tos_accepted_by: this.user?.fullName,
              tos_accepted_date: company.termsOfServiceAcceptedDate
                .utc()
                .format('MMM. D, YYYY [at] h:mm A z'),
            },
          })
        }
      } catch (error) {
        console.log(error)
      }
    },
    async importSnapshot() {
      if (this.importing) return

      try {
        this.importing = true
        const response = await this.$http.post('/share/redeem', {
          share_id: this.company?.onboardingInfo.snapshotId,
        })

        this.importing = false
      } catch (error) {
        this.importing = false
      }
    },
    showHideLoader() {
      const element = document.getElementById('app')
      const htmlTag = document.getElementsByTagName('html')[0]
      if (!this.appLoader) {
        element.className = 'loading'
        htmlTag.className = 'overflow-hidden'
      } else if (element) {
        element.className = ''
        htmlTag.className = ''
      }
      this.appLoader = !this.appLoader
    },
    destroy() {
      //@ts-ignore
      this.handshake.then((child: any) => {
        child.destroy()
      })
    },
  },
  beforeDestroy() {
    this.destroy()
    window.removeEventListener('popstate', () => console.log('remove popstate'))
  },
})
</script>

<style>
@media (min-width: 768px) {
  body {
    padding-top: 0px !important;
  }
}
.onboarding-iframe {
  border: none;
  width: 100%;
  height: 100vh;
  overflow: auto;
}
#app + .app-loader {
  display: none;
}
</style>
