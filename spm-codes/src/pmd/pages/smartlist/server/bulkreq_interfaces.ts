import { IContactsQuery } from './contacts_server'

export type IKeyedObject = { [key: string]: any }

export interface IReqFilters {
  filters: any[]
}

export interface IPostBulkOp {
  sendAtSprintEnd: false
  sendAtOpEnd: true
  emailTo: string
  smsTo: string
}

export interface IOpSpecs {
  opType: 'bulk-ops' | 'bulk-email' | 'bulk-sms' | 'bulk-campaign' | 'bulk-sms' |
          'bulk-workflow' | 'bulk-tag-add' | 'bulk-tag-remove' | 'bulk-contact-delete' |
          'bulk-merge-contact' | 'bulk-export' | 'bulk-import'
  contactIds: []
  postOpTagId?: string
  description?: string
  note?: string
  getOpDesc(): string // One line description of action. Can be used in logging or else where.
}

export interface ISprintSchedule {
  scheduleStart: string
  scheduleTimeZone: string
  sprintLimit: number //number of records to be processed in each sprint.
  sprintDelay: number
  sprintDelayType: 'days' | 'hours' | 'minutes'
  skipDays?: string[]
  processAllAtSchedule?: boolean
  //skipDates?: number[];
  processWindowStart: string
  processWindowEnd: string
}

export interface IBulkOpportunitySpecs extends IOpSpecs {
  // specialization
  opType: 'bulk-ops'
  pipeline_id: string
  pipeline_stage_id: string
  type: string
  allow_backward: boolean
  allow_multiple: boolean
  monetary_value?: string
  opportunity_source: string
  opportunity_status?: string
}

export interface IBulkImportSpecs extends IOpSpecs {
  // specialization
  opType: 'bulk-import'
  importId?:string
}
export interface IBulkCampaignSpecs extends IOpSpecs {
  // specialization
  opType: 'bulk-campaign' | 'bulk-workflow'
  campaign_id?: string
  workflow_id?: string
}

export interface ITagOperationSpecs extends IOpSpecs {
  // specialization
  opType: 'bulk-tag-add' | 'bulk-tag-remove'
  tags?: [] | null
}

export interface IContactDeleteSpecs  extends IOpSpecs {
  // specialization
  opType: 'bulk-contact-delete'
  tags?: [] | null
}

export interface IReviewRequestSpecs extends IOpSpecs {
  review_type: 'sms' | 'email' | 'all'
}

export interface IBulkSMSSpecs extends IOpSpecs {
  // specialization
  opType: 'bulk-sms'
  message: ''
  attachments: []
}

export interface IBulkEmailSpecs extends IOpSpecs {
  opType: 'bulk-email'
  templateId?: any
  attachments?: any[]
  html?: string
  subject?: string
  thread_id?: any
  from?: string
  fromName?: string
  assignedTo?: string
  reply_message_id?: any
}

export interface IExportSpecs extends IOpSpecs {
  opType: 'bulk-export'
  exportStats: any
  total?:number
}

export interface IMergeContactSpecs extends IOpSpecs {
  opType: 'bulk-merge-contact'
  contactIds: []
}

export interface IBulkEmailSpecsUIOnly extends IBulkEmailSpecs {
  name?: string
}

export interface IBulkReq {
  id?: string
  byUserId?: string
  byUserName?: string
  locationId: string // required
  sprintSchedule?: ISprintSchedule // Optional if it is not given it means execute at once.
  filters?: IContactsQuery
  recordIds?: any[]
  opSpecs: IOpSpecs
  postOp?: IPostBulkOp
  status?: IBulkReqStatus
  startOn?: number // timestamp same as in status.startOn
  opSource?: string
  freezeList?: boolean
  processingType?: string
}

export interface IBulkReqStatus {
  status:
    | 'not-started'
    | 'processing'
    | 'complete'
    | 'paused'
    | 'cancelled'
    | 'completed'
    | 'queued'
    | 'queueing'
  queuedCount?: number
  pendingCount?: number
  errorCount?: number
  successCount?: number
  startOn?: number //  timestamp
  completeOn?: number // timestamp
  pausedOn?: number // timestamp
  cancelledOn?: number //timestamp
  batchStatusItems?: IBatchStatus[]
}

export interface IBatchStatus {
  bulkReqId: string
  batchId: string
  //startedOn: number;
  //completeOn ?: number;
  successCount?: number
  errorCount?: number
}

export interface IRecordOperationStatus {
  recordId: string // mostly contact id depends on type of action
  actionDate: string | number // datetime timestamp
  status: 'success' | 'err'
  error?: string
}

export interface IRecordOperationLog
  extends IRecordOperationStatus,
    IKeyedObject {
  bulkReqId: string
  batchId: string
  actionType: string
  opDesc?: string
  recordId: string // mostly contact id depends on type of action
  actionDate: string | number // datetime timestamp
  status: 'success' | 'err'
  error?: string
}

export interface IOperationBatch {
  batchQueuedOn: number
  bulkReqId: string
  batchId: string
  records: any[]
  status?: IRecordOperationStatus[]
}
