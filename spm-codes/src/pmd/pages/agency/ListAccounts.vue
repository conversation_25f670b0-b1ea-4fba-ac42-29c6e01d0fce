<template>
  <div>
    <SideBarV2 v-if="getSideBarVersion === 'v2'"/>
    <SideBar v-else />
    <TopBar />

    <section class="hl_wrapper">
      <section
        class="hl_wrapper--inner hl_agency hl_agency-location"
        id="agency_location"
      >
        <div class="container-fluid">
          <div class="hl_controls">
            <div class="hl_controls--left">
              <h1>Accounts</h1>
            </div>
            <div class="hl_controls--right">
              <div class="search-form">
                <UITextInputGroup
                  type="text"
                  icon="icon-loupe"
                  placeholder="Search Location"
                  v-model="searchText"
                  @keyup.native="updateSearchText"
                />
              </div>
              <UIButton
                use="primary"
                v-on:click="loadSnapshotTemplate"
                v-if="user && user.role == 'admin'"
                :loading="loadingLocationsCount"
              >
                <i class="icon icon-plus mr-2" v-if="!loadingLocationsCount"></i>
                Add Account
              </UIButton>
            </div>
          </div>
          <VirtualList
            v-if="getSideBarVersion === 'v2'"
            style="overflow-y: auto"
            class="hl_v2-height"
            :data-sources="locations"
            :data-key="'id'"
            :data-component="locationListCard"
            :estimate-size="200"
            :extra-props="{ showSaasMode: agencyInSaasPlan, walletBalance: walletBalance }"
            :bottom-threshold="1500"
            v-on:tobottom="loadMoreOnScroll"
          />
          <VirtualList
            v-else
            style="overflow-y: auto"
            :data-sources="locations"
            :data-key="'id'"
            :data-component="locationListCard"
            :estimate-size="200"
            :extra-props="{ showSaasMode: agencyInSaasPlan, walletBalance: walletBalance }"
            page-mode
            :bottom-threshold="1500"
            @tobottom="loadMoreOnScroll"
          />

          <div class="text-center" v-if="locations && locations.length && !searchText">
              <moon-loader v-if="loadMoreLoading" color="#188bf6" size="24px" radius="50%" margin="2px" />
          </div>
        </div>
      </section>
      <!-- END of .hl_agency-location -->
    </section>
    <LoadSnapshotTemplateModal
      :values="loadSnapshotTemplateModal"
      @hidden="loadSnapshotTemplateModal = { visible: false }"
      @select="handleSelectTemplate"
    />
    <upgrade-modal
      :company="company"
      v-if="showUpgradeModal"
      :show="showUpgradeModal"
      source="billing-page"
      @close="showUpgradeModal = false"
    />
    <request-card-modal
      v-if="agencyInSaasPlan && showRequestCardModal"
      :show="showRequestCardModal"
      @close="showRequestCardModal = false"
      :location-id="requestCardLocationId"
      @success="e => handleRequestCardSuccess(e)"
      @createUser="showCreateUserModal"
    />
    <request-card-success-modal
      v-if="agencyInSaasPlan && showRequestCardSuccessModal"
      :show="showRequestCardSuccessModal"
      :message="requestCardSuccessMessage"
      @close="showRequestCardSuccessModal = false"
    />
    <EditTeamMemberModal
      :values="teamMemberModalValues"
      :defaults="{
        role: 'admin',
        type: 'account',
        locationId: requestCardLocationId,
      }"
      @hidden="teamMemberModalValues = { visible: false }"
      @created="newUserCreated"
    />
    <ManageLocationSubscription
      :locationId="requestCardLocationId"
      v-if="showManageLocationSubscriptionModal"
      :show="showManageLocationSubscriptionModal"
      @close="handleSuccessAttachPlan"
      @success="handleSuccessAttachPlan"
      @skip="handleSuccessAttachPlan"
      :skippable="true"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import TopBar from '../../components/TopBar.vue'
import SideBarV2 from '../../components/sidebar/SideBar.vue'
import SideBar from '../../components/agency/SideBar.vue'
import { UserState } from '@/store/state_models'
import { mapState } from 'vuex'
import LocationListCard from '@/pmd/components/agency/LocationListCard.vue'
import UpgradeModal from '@/pmd/components/agency/billing/UpgradeModalv2.vue'
import VirtualList from 'vue-virtual-scroll-list'
import RequestCardModal from '@/pmd/components/saas/agency/RequestCardModal.vue'
import RequestCardSuccessModal from '@/pmd/components/saas/agency/RequestCardSuccessModal.vue'
import ManageLocationSubscription from '@/pmd/components/saas/saas_account_details/ManageLocationSubscriptionModal.vue'
import { trackGaEvent } from '@/util/helper'
import * as _ from 'lodash'

const LoadSnapshotTemplateModal = () =>
  import(
    /* webpackChunkName: "snapshots" */ '@/pmd/components/agency/snapshot_template/LoadSnapshotTemplateModal.vue'
  )
const EditTeamMemberModal = () =>
  import(
    /* webpackChunkName: "team-edit" */ '@/pmd/components/EditTeamMemberModal.vue'
  )
import { Location, Company, User } from '@/models'
import LocationsService from '@/services/LocationsService'

let unsubscribeLocations: () => void

export default Vue.extend({
  name: 'ListAccounts',
  components: {
    VirtualList,
    TopBar,
    SideBar,
    LoadSnapshotTemplateModal,
    UpgradeModal,
    RequestCardModal,
    RequestCardSuccessModal,
    EditTeamMemberModal,
    ManageLocationSubscription,
    SideBarV2
  },
  data() {
    return {
      locationListCard: LocationListCard,
      locations: [] as Location[],
      searchText: '' as string,
      // twilioAccount: {} as TwilioAccount,
      company: {} as Company,
      loadSnapshotTemplateModal: { visible: false } as { [key: string]: any },
      limit: 50,
      skip: 0,
      searchDebouncer: undefined as NodeJS.Timer | undefined,
      loadMoreLoading: false,
      showUpgradeModal: false,
      showRequestCardModal: false,
      showRequestCardSuccessModal: false,
      loadingLocationsCount: false,
      showManageLocationSubscriptionModal: false,
      requestCardLocationId: '',
      requestCardSuccessMessage: '',
      teamMemberModalValues: {},
      walletBalance: {}
    }
  },
  async created() {
    const auth = await this.$store.dispatch('auth/get')
    LocationsService.search(auth.companyId, false, undefined, undefined, this.limit).then((response) => {
      this.locations = response.locations.map((location) => {
        return {...location, id: location._id}
      })
      this.skip = response.locations.length
    })

    const authUser = await this.$store.dispatch('auth/get')
    this.company = await Company.getById(authUser.companyId) // can we fetch this info from state?

    // this.twilioAccount = await TwilioAccount.getByCompanyId(this.company.id);

    // detecting change checked value from item component event.
    this.$on('switchToSaaS', (locationId: string) => {
      // console.log(locationId)
      this.requestCardLocationId = locationId
      this.showRequestCardModal = true
      trackGaEvent( 'SaaS Mode', locationId,'Init switch To SaaS',1)
    })

  },
  mounted(){
    setTimeout( ()=> {
      this.getLocationWalletBalance();
    }, 1000)
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    agencyInSaasPlan(): boolean {
      return this.$store.getters['company/inSaasPlan']
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  methods: {
    async refetchLocationData(locationId: string) {
      let locationIndex = this.locations.findIndex(location => location.id === locationId);
      if (locationIndex !== -1) {
        try {
          // let response = await Location.getById(locationId)
          let response = await LocationsService.ids([locationId])
          let newLocationData = {...response.locations[0], id: response.locations[0]._id }
          // console.log(newLocationData, this.locations[locationIndex])
          this.locations[locationIndex] = newLocationData
        } catch (err) {
          console.error("Failed to update location's info");
        }
      }
    },
    async loadMoreOnScroll() {
      console.log('Load on scroll')
       if(!this.loadMoreLoading && this.skip % 50 === 0) {
        await this.loadMore()
      }
    },
    updateSearchText: function (event) {
      console.log(`event`, event.target.value);
      
        let name = event.target.value
        if (this.searchDebouncer) clearTimeout(this.searchDebouncer)
        this.searchDebouncer = setTimeout(() => {
            this.runSearch(name)
        }, 300)
    },
    runSearch: async function(name){
        console.log('Search Query', name)
        let locations = await this.$store.getters['locations/searchByName'](name)
        console.log('Search Results', locations)
        this.locations = locations
        this.skip = locations.length
    },
    async getLocationWalletBalance() {
      try {
        const { data } = await this.saasService.get(`/company/${this.company.id}/location-wallet-balances`);
        // console.log(data);
        this.walletBalance = data.balances;
      } catch (err) {
        console.log(err);
      }
    },
    async loadMore(){
      this.loadMoreLoading = true
      const auth = await this.$store.dispatch('auth/get')
      return LocationsService.search(auth.companyId, false, undefined, this.skip, this.limit, this.searchText).then((response) => {
        this.locations = this.locations.concat(response.locations.map((location) => {
            return { ...location, id: location._id }
        }))
        this.skip += response.locations?.length
        this.loadMoreLoading = false
      })
    },
    async loadSnapshotTemplate() {
      this.loadingLocationsCount = true
      try {
        let locationsCount = await this.$http.post('/stripe/sync_locations', {
          company_id: this.company.id,
        })
        if (
          locationsCount.data.totalActiveLocations >=
          locationsCount.data.allowedLocations
        ) {
          this.showUpgradeModal = true
        } else {
          this.loadSnapshotTemplateModal = {
            visible: true,
          }
        }
      } catch (err) {
        console.error(err)
      } finally {
        this.loadingLocationsCount = false
      }
    },
    handleSelectTemplate(templateId, snapshotType, locationType) {
      this.loadSnapshotTemplateModal = {
        visible: false,
      }
      window.localStorage.setItem('templateId', templateId)
      let query = {
        snapshot: templateId,
        s_type: snapshotType,
      }
      if (locationType === 'saas') {
        query['l_type'] = 'saas';
        trackGaEvent( 'SaaS Mode', this.company.id ,'Init creation of new SaaS Location',1)
      }
      this.$router.push({
        name: 'account_add_map',
        query: query,
      })
    },
    async handleRequestCardSuccess(e: any) {
      const { msg, response, locationId } = e
      this.showRequestCardModal = false
      this.requestCardSuccessMessage = msg
      if (response.paymentMethodAdded) {
        this.showManageLocationSubscriptionModal = true
      } else {
        this.showRequestCardSuccessModal = true
      }
      trackGaEvent('SaaS Mode', this.requestCardLocationId, 'Converted to a SaaS Location.', 1)
      // console.log(response)
      await this.refetchLocationData(locationId)
    },
    async handleSuccessAttachPlan(e: any) {
      // const { saasPlanId, stripePriceId } = e
      this.showManageLocationSubscriptionModal = false
      this.showRequestCardSuccessModal = true
      if (e.locationId) {
        await this.refetchLocationData(e.locationId)
      }
    },
    showCreateUserModal(locationId: string) {
      this.showRequestCardModal = false
      this.teamMemberModalValues = {
        visible: true,
        companyId: this.company.id,
        isEditing: false,
      }
      trackGaEvent( 'SaaS Mode', locationId,'Init creating new User',1)
    },
    newUserCreated(locationId) {
      this.teamMemberModalValues = { visible: false }
      setTimeout(() => {
        this.showRequestCardModal = true
      }, 500)
      trackGaEvent( 'SaaS Mode', locationId,'Created new User',1)
    },
  },
  beforeDestroy() {
    if (unsubscribeLocations) unsubscribeLocations()
  },
})
</script>
