<template>
  <div
    class="modal fade hl_website-add-new-website-template-category--modal"
    id="new-website-template-category"
    tabindex="-1"
    role="dialog"
    aria-hidden="true"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Add New {{ templateCategoryTitle }} Template Category</h2>
            <button
              @click="hideModal()"
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div class="form-group">
              <UITextInputGroup
                v-model="name"
                type="text"
                label="Category Name"
                :placeholder="`Name for the ${templateCategoryTitle} template category`"
              />
            </div>
            <div class="form-group">
              <UITextInputGroup
                v-model="description"
                type="text"
                label="Description"
                placeholder="Enter some description"
              />
            </div>
            <div class="form-group">
              <UITextInputGroup
                v-model="image"
                type="text"
                label="Hero Image URL"
                placeholder="URL for the hero image"
              />
            </div>
            <div class="form-group">
              <UITextInputGroup
                v-model="video"
                type="text"
                label="Youtube or Vimeo Video URL"
                placeholder="URL for Youtube or Vimeo Video"
              />
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner">
            <UIButton
              type="submit"
              :disabled="disableSubmit"
              @click="createTemplateCategory()"
              use="primary"
            >{{ creatingNewTemplateCategory ? 'Creating...' : 'Create New Category' }}</UIButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { isURL } from '../../../util/helper'
export default Vue.extend({
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    templateCategoryTitle: {
      type: String
    }
  },
  computed: {
    disableSubmit(): Boolean {
      const data = this.getCleanData()
      const { name, description, image, video } = data
      if (
        !name ||
        !description ||
        (image && !isURL(image)) ||
        (video && !isURL(video))
      )
        return true

      return false
    }
  },
  data() {
    return {
      name: '',
      description: '',
      image: '',
      video: '',
      creatingNewTemplateCategory: false
    }
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hide')
    })
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  methods: {
    hideModal() {
      this.$emit('hide')
    },
    getCleanData() {
      const cleanedName = this.name.trim()
      const cleanedDescription = this.description.trim()
      const cleanedImage = this.image.trim()
      const cleanedVideo = this.video.trim()

      return {
        name: cleanedName,
        description: cleanedDescription,
        image: cleanedImage,
        video: cleanedVideo
      }
    },
    createTemplateCategory() {
      const newTemplateCategoryDetails = this.getCleanData()

      this.creatingNewTemplateCategory = true
      this.$emit('newTemplateCategoryDetails', newTemplateCategoryDetails)
    }
  },
  watch: {
    showModal(newValue, oldValue) {
      if (newValue && !oldValue) {
        // show the modal
        // reset form
        this.name = ''
        this.description = ''
        this.image = ''
        this.video = ''
        this.creatingNewTemplateCategory = false
        $(this.$refs.modal).modal('show')
      } else if (!newValue && oldValue) {
        // hide the modal
        $(this.$refs.modal).modal('hide')
      }
    }
  }
})
</script>
