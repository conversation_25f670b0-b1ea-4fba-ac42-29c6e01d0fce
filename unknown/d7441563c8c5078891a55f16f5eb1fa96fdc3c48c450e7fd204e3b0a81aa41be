<template>
  <div id="review-received" class="card">
    <div class="card-header card-header--compact space-between">
      <h2>Reviews Received</h2>
      <div>
        <span>{{ filter }}</span>
        <select class="selectpicker more-select" v-model="filter">
          <!-- <option>Today</option> -->
          <option>This Week</option>
          <option>Last Week</option>
          <option>This Month</option>
          <option>Last 6 Months</option>
          <option>This Year</option>
        </select>
      </div>
    </div>
    <div class="card-body">
      <div class="review-summary">
        <svg
          width="32"
          height="32"
          viewBox="0 0 32 32"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M18 26H26V28H18V26Z" fill="#FFBC00" />
          <path d="M18 22H30V24H18V22Z" fill="#FFBC00" />
          <path d="M18 18H30V20H18V18Z" fill="#FFBC00" />
          <path
            d="M20.549 11.217L16 2L11.451 11.217L1.28003 12.695L8.64003 19.87L6.90203 30L14 26.269V24.009L9.55903 26.344L10.611 20.208L10.789 19.171L10.036 18.438L5.57803 14.091L11.739 13.196L12.779 13.045L13.245 12.102L16 6.519L18.755 12.102L19.221 13.045L20.261 13.196L27.715 14.281L28 12.3L20.549 11.217Z"
            fill="#FFBC00"
          />
        </svg>

        <h3 v-if="currentReviewsAggregate">
          {{ currentReviewsAggregate.totalReviews }}
        </h3>
        <h3 v-else>0</h3>

        <h4 v-bind:class="{ nigative: growth < 0 }">
          <i v-if="growth >= 0" class="icon icon-arrow-up-2"></i>
          <i v-else class="icon icon-arrow-down-2"></i>
          <span v-if="growth >= 0">+</span>{{ growth }}%
        </h4>

        <!-- 
          
           <div></div>
        <p>
          <span v-if="growth >= 0">growth</span>
          <span v-else>drop</span> in reviews over the last
          {{ getWeekMonthYear }}
        </p>
        
        <div class="col-sm-6">
          <h4>
            <i class="icon-positive"></i>
            <span v-if="positiveReviewChange > 0">+</span
            >{{ positiveReviewChange }}
          </h4>
          <p v-if="positiveReviewChange >= 0">
            more positive reviews than the last {{ getWeekMonthYear }}
          </p>
          <p v-if="positiveReviewChange < 0">
            less positive reviews than the last {{ getWeekMonthYear }}
          </p>
        </div> -->
      </div>

      <div
        class="reviews-sources"
        v-if="
          currentReviewsAggregate && currentReviewsAggregate.sources.length > 0
        "
      >
        <h4>Sources</h4>
        <ul class="sources_list list-inline">
          <li
            v-for="source in currentReviewsAggregate.sources"
            :key="source"
            class="list-inline-item"
          >
            <a :style="{ 'background-image': sourceImageUrl(source) }">{{
              sourceName(source)
            }}</a>
          </li>
          <!-- <li class="list-inline-item">
                        <a href="#" class="facebook">Facebook</a>
                    </li> -->
          <!-- <li class="list-inline-item">
                        <a href="#" class="twitter">Twitter</a>
                    </li>
                    <li class="list-inline-item">
                        <a href="#" class="add">Add</a>
                    </li> -->
        </ul>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import ListingHelper from './../../../../util/listing_helper'
import { ReviewAggregateData } from '@/store/state_models'

declare var $: any

export default Vue.extend({
  data() {
    return {
      filter: 'Last 6 Months',
      currentLocationId: '',
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.filter = 'Last 6 Months'
    },
    filter: function () {
      if (this.filter === 'This Week') {
        this.$store.dispatch(
          'reviewAggregate/fetchThisWeek',
          this.currentLocationId
        )
      } else if (this.filter === 'Last Week') {
        this.$store.dispatch(
          'reviewAggregate/fetchLastWeek',
          this.currentLocationId
        )
      } else if (this.filter === 'This Month') {
        this.$store.dispatch(
          'reviewAggregate/fetchThisMonth',
          this.currentLocationId
        )
      } else if (this.filter === 'Last 6 Months') {
        this.$store.dispatch(
          'reviewAggregate/fetchLast6Months',
          this.currentLocationId
        )
      } else if (this.filter === 'This Year') {
        this.$store.dispatch(
          'reviewAggregate/fetchThisYear',
          this.currentLocationId
        )
      }
    },
  },
  computed: {
    currentReviewsAggregate(): ReviewAggregateData {
      return this.$store.getters['reviewAggregate/current'](this.filter)
    },
    previousReviewsAggregate(): ReviewAggregateData {
      return this.$store.getters['reviewAggregate/previous'](this.filter)
    },
    getWeekMonthYear(): string | undefined {
      switch (this.filter) {
        case 'This Week':
        case 'Last Week':
          return 'week'
        case 'This Month':
          return 'month'
        case 'Last 6 Months':
          return '6 months'
        case 'This Year':
          return 'year'
      }
    },
    growth(): string {
      if (!this.currentReviewsAggregate && !this.previousReviewsAggregate)
        return '0'
      let baseline =
        this.previousReviewsAggregate &&
        this.previousReviewsAggregate.totalReviews
          ? this.previousReviewsAggregate.totalReviews
          : 1
      let current = this.currentReviewsAggregate
        ? this.currentReviewsAggregate.totalReviews
        : 0
      let previous = this.previousReviewsAggregate
        ? this.previousReviewsAggregate.totalReviews
        : 0
      return (((current - previous || 0) / baseline) * 100).toFixed(2)
    },
    positiveReviewChange(): string {
      if (!this.currentReviewsAggregate && !this.previousReviewsAggregate)
        return '0'
      let current = this.currentReviewsAggregate
        ? this.currentReviewsAggregate.positiveReviews
        : 0
      let previous = this.previousReviewsAggregate
        ? this.previousReviewsAggregate.positiveReviews
        : 0
      return (current - previous || 0).toFixed(0)
    },
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
  },
  methods: {
    sourceImageUrl(source: string): string | undefined {
      const listing = ListingHelper.getNetworkInfo(source)
      if (listing) return 'url(' + listing.imgPath + ')'
    },
    sourceName(source: string): string | undefined {
      const listing = ListingHelper.getNetworkInfo(source)
      if (listing) return listing.name
    },
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
})
</script>

<style lang="scss" scoped>
#review-received {
  .space-between {
    display: flex;
    justify-content: space-between;
  }

  .card-header--compact {
    padding: 12px 50px 10px 30px;
  }

  .card-header {
    span {
      color: #afb8bc;
    }
    .more-select {
      top: 53%;
      transform: translateY(-53%);
    }
  }
  .card-body {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
  }

  .review-summary {
    display: flex;
    flex-direction: column;
    align-items: center;

    h3 {
      font-size: 2rem;
      font-weight: 500;
    }

    h4 {
      font-size: 1rem;
      font-weight: 500;
      color: #27ae60;
    }

    .nigative {
      color: #f2994a;
    }
  }

  .reviews-sources {
    padding-top: 20px;
    width: 100%;
    h4 {
      font-weight: 400;
      font-size: 0.875rem;
      color: #607179;
      margin-bottom: 15px;
    }

    .sources_list li a {
      display: block;
      width: 35px;
      height: 35px;
      border-radius: 50%;
      background-color: #eee;
      background-repeat: no-repeat;
      background-position: center;
      background-size: 35px;
      text-indent: -999999px;
    }

    .sources_list li a.google {
      background-image: url('/pmd/img/icon-g+.svg');
    }
    .sources_list li a.facebook {
      background-image: url('/pmd/img/icon-facebook.svg');
    }
    .sources_list li a.twitter {
      background-image: url('/pmd/img/icon-twitter.svg');
    }
    .sources_list li a.add {
      background-image: url('/pmd/img/icon-add.svg');
    }
  }
}
</style>


