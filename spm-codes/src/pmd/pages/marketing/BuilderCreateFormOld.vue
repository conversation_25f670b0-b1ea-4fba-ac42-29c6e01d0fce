<template>
	<section class="hl_wrapper d-flex">
		<section class="hl_wrapper--inner form-builder" id="form-builder">
			<moon-loader class="mt-5" color="#188bf6" size="20px" v-if="loading"/>
			<section v-if="!loading" class="hl_form-builder--main">
				<form name="builder-form" id="_builder-form">
					<h5 class="builder-form-name text-center" v-if="formName!=''">{{formName}}</h5>
					<div
						v-bind:class="dragOptions.isDragEntered ? 'drag-enter form-builder--wrap' : 'form-builder--wrap'"
						id="the-form-builder"
						v-bind:style="{
              backgroundColor: '#' + formStyle.bgColor,
              //color: '#' + formStyle.color,
              border: formStyle.border + 'px dashed #cde0ec',
              borderRadius: formStyle.radius + 'px',
              maxWidth: formStyle.width + 'px'
            }"
						ref="formBuilderReferance"
					>
						<drop v-bind:class="(!inlineForm) ? 'drop form-builder-drop builder-padding-remove' : 'drop form-builder-drop builder-inline'" @drop="handleDrop">
							<Container
								@drop="onDrop"
								:remove-on-drop-out="true"
								v-bind:class="inlineForm ? 'smooth-dnd-container vertical row' : 'smooth-dnd-container vertical'"
							>
								<Draggable
									v-for="(item, index) in ItemDropable"
									:key="index"
									v-bind:class="(inlineForm && item.type!='img' && item.type!='submit' && item.type!='h1' && item.type!='captcha') ? 'menu-field-wrap col-md-6 col-sm-6 col-lg-6' : 'menu-field-wrap col-12'"
								>
									<div v-bind:class="(item.active) ? 'active' : ''" @click="setFieldActive(index)">
										<!--Full Name-->
										<div v-if="item.tag === 'full_name'">
											<div class="form-builder--item">
												<label v-if="formLabelVisible">{{ item.label }} <span v-if="item.required">*</span></label>
												<input
													type="text"
													class="form-control"
													v-bind:placeholder="item.placeholder"
													v-bind:name="item.tag"
													disabled="disabled"
												>
											</div>
											<a class="close-icon" @click="removeField(index)" v-if="(item.active)">
												<i class="icon-close"></i>
											</a>
											<add-field-dropzone :index="index" v-if="showDropZones"></add-field-dropzone>
										</div>

										<!--First Name-->
										<div v-else-if="item.tag === 'first_name'" class="menu-item">
											<div class="form-builder--item">
												<label v-if="formLabelVisible">{{ item.label }} <span v-if="item.required">*</span></label>
												<input
													type="text"
													class="form-control"
													v-bind:placeholder="item.placeholder"
													v-bind:name="item.tag"
													disabled="disabled"
												>
											</div>
											<a class="close-icon" @click="removeField(index)" v-if="(item.active)">
												<i class="icon-close"></i>
											</a>
											<add-field-dropzone :index="index" v-if="showDropZones"></add-field-dropzone>
										</div>

										<!--Last Name-->
										<div v-else-if="item.tag === 'last_name'" class="menu-item">
											<div class="form-builder--item">
												<label v-if="formLabelVisible">{{ item.label }} <span v-if="item.required">*</span></label>
												<input
													type="text"
													class="form-control"
													v-bind:placeholder="item.placeholder"
													v-bind:name="item.tag"
													disabled="disabled"
												>
											</div>
											<a class="close-icon" @click="removeField(index)" v-if="(item.active)">
												<i class="icon-close"></i>
											</a>
											<add-field-dropzone :index="index" v-if="showDropZones"></add-field-dropzone>
										</div>

										<!--Email-->
										<div v-else-if="item.tag === 'email'" class="menu-item">
											<div class="form-builder--item">
												<label v-if="formLabelVisible">{{ item.label }} <span v-if="item.required">*</span></label>
												<input
													type="email"
													class="form-control"
													v-bind:placeholder="item.placeholder"
													v-bind:name="item.tag"
													disabled="disabled"
												>
											</div>
											<a class="close-icon" @click="removeField(index)" v-if="(item.active)">
												<i class="icon-close"></i>
											</a>
											<add-field-dropzone :index="index" v-if="showDropZones"></add-field-dropzone>
										</div>

										<!--Phone-->
										<div v-else-if="item.tag === 'phone'" class="menu-item">
											<div class="form-builder--item">
												<label v-if="formLabelVisible">{{ item.label }} <span v-if="item.required">*</span></label>
												<input
													type="text"
													class="form-control"
													v-bind:placeholder="item.placeholder"
													v-bind:name="item.tag"
													disabled="disabled"
												>
											</div>
											<a class="close-icon" @click="removeField(index)" v-if="(item.active)">
												<i class="icon-close"></i>
											</a>
											<add-field-dropzone :index="index" v-if="showDropZones"></add-field-dropzone>
										</div>

										<!--Organization-->
										<div v-else-if="item.tag === 'organization'" class="menu-item">
											<div class="form-builder--item">
												<label v-if="formLabelVisible">{{ item.label }} <span v-if="item.required">*</span></label>
												<input
													type="text"
													class="form-control"
													v-bind:placeholder="item.placeholder"
													v-bind:name="item.tag"
													disabled="disabled"
												>
											</div>
											<a class="close-icon" @click="removeField(index)" v-if="(item.active)">
												<i class="icon-close"></i>
											</a>
											<add-field-dropzone :index="index" v-if="showDropZones"></add-field-dropzone>
										</div>

										<!--Header-->
										<div v-else-if="item.tag === 'header'" class="menu-item">
											<div class="form-builder--item">
												<h1
													v-bind:style="{
													color: '#' + item.color,
													fontFamily: item.font,
													fontSize: item.size + 'px',
													fontWeight: item.weight,
													textAlign: item.align
													}">
														{{ item.label }}
													</h1>
											</div>
											<a class="close-icon" @click="removeField(index)" v-if="(item.active)">
												<i class="icon-close"></i>
											</a>
											<add-field-dropzone :index="index" v-if="showDropZones"></add-field-dropzone>
										</div>

										<!--Textarea-->
										<div v-else-if="item.tag === 'textarea'" class="menu-item">
											<div class="form-builder--item">
												<label v-if="formLabelVisible">{{ item.label }} <span v-if="item.required">*</span></label>
												<textarea class="form-control" placeholder=" Html here..." v-bind:name="item.tag" disabled="disabled"></textarea>
											</div>
											<a class="close-icon" @click="removeField(index)" v-if="(item.active)">
												<i class="icon-close"></i>
											</a>
											<add-field-dropzone :index="index" v-if="showDropZones"></add-field-dropzone>
										</div>

										<!--Image-->
										<div v-else-if="item.tag === 'image'" class="menu-item">
											<input
                        v-if="!('url' in item)"
                        type="file"
                        class="form-control"
                        placeholder="Image"
                        v-bind:name="item.tag"
                        @change="onFileChange"
                      >
											<div class="form-builder--item form-builder--image" v-else-if="('url' in item)"
                      :align="!item.align ? 'center' : item.align">
                          <img
                            :src="(filesAdded.length > 0 && 'url' in filesAdded[0]) ? filesAdded[0].url : item.url"
                            :alt="item.alt"
                            v-bind:style="{
                              width:(!item.width || item.width<=0) ? '100%' : item.width + 'px',
                              height:(!item.height || item.height<=0) ? '100%': item.height + 'px'
                            }"
                          >
                        </div>
											<a class="close-icon" @click="removeField(index)" v-if="(item.active)">
												<i class="icon-close"></i>
											</a>
											<add-field-dropzone :index="index" v-if="showDropZones"></add-field-dropzone>
										</div>

										<!--Captcha-->
										<div v-else-if="item.tag === 'captcha'" class="menu-item">
											<div class="form-builder--item">
												<label v-if="formLabelVisible">{{item.label}} <span>*</span></label>
												<img
													class="captcha-wrap-img"
													src="../../../assets/pmd/img/captcha.png"
													alt="Avatar Name"
												>
											</div>
											<a class="close-icon" @click="removeField(item.tag)" v-if="(item.active)">
												<i class="icon-close"></i>
											</a>
											<add-field-dropzone :index="index" v-if="showDropZones"></add-field-dropzone>
										</div>

										<!--Button-->
										<div v-else-if="item.tag === 'button'" class="menu-item">
											<div class="form-builder--item"
												v-bind:style="{
													textAlign: item.align
												}">
												<button
													type="button"
													class="btn btn-dark"
													v-bind:style="{
													backgroundColor: '#' + buttonStyle.bgColor,
													color: '#' + buttonStyle.color,
													border: buttonStyle.border + 'px solid #0f1010',
													borderRadius: buttonStyle.radius + 'px',
													padding: buttonStyle.padding + 'px',
													marginTop: '15px',
													width: (item.fullwidth) ? '100%' : 'auto',
												}"
												>{{item.label}}</button>
											</div>
											<a class="close-icon" @click="removeField(index)" v-if="(item.active)">
												<i class="icon-close"></i>
											</a>
											<add-field-dropzone :index="index" v-if="showDropZones"></add-field-dropzone>
										</div>
										<!--Custom Form Start-->
										<FormbuilderAddCustomFields
											v-if="item.id!=undefined"
											:customFields="item"
											:index="index"
											:showDropZones="showDropZones"
											:formLabelVisible="formLabelVisible"
										/>
										<!--Custom Form End-->
									</div>
								</Draggable>
								<div class="blank-dropzone">
									<add-field-dropzone :index="-1"></add-field-dropzone>
								</div>
							</Container>
						</drop>

						<div class="branding-wrap" id="_branding-wrap" v-if="isBrandingActive">
							<!-- <p>Marketing by</p> -->
							<a
								v-if="company && company.logoURL"
								:href="'//'+company.domain"
								class="branding-url"
								target="_blank"
							>
								<img :src="company.logoURL">
								<span class="cmp-name">{{company.name}}</span>
							</a>
							<a v-else-if="company" href="https://gohighlevel.com" class="branding-url" target="_blank">
								<svg data-v-1e92268e xmlns="http://www.w3.org/2000/svg" width="150" height="26">
									<image
										data-v-1e92268e
										width="28"
										height="26"
										xlink:href="data:img/png;base64,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"
									></image>
									<text x="30" y="22" fill="#607179">Highlevel</text>
								</svg>
							</a>
						</div>
					</div>
				</form>
			</section>

			<!-- END of .hl_form-builder--main -->
			<aside class="hl_form-builder--sidebar" ref="fieldSettingsRefs">
				<ul class="nav nav-tabs top-nav-wrapper-customfield" role="tablist">
					<li class="nav-item">
						<a
							class="nav-link active"
							id="fields-tab"
							data-toggle="tab"
							href="#fields"
							role="tab"
							aria-controls="fields"
							aria-selected="true"
						>Fields</a>
					</li>
					<li class="nav-item">
						<a
							class="nav-link"
							id="styles-tab"
							data-toggle="tab"
							href="#styles"
							role="tab"
							aria-controls="styles"
							aria-selected="false"
						>Styles</a>
					</li>
					<li class="nav-item">
						<a
							class="nav-link"
							id="options-tab"
							data-toggle="tab"
							href="#options"
							role="tab"
							aria-controls="options"
							aria-selected="false"
						>Options</a>
					</li>
				</ul>
				<div class="custom-field">
					<button
						type="button"
						class="btn btn-success btn-block"
						@click="showIntegratePage"
					>Integrate Form</button>
				</div>
				<div class="tab-content">
					<div class="tab-pane active" id="fields" role="tabpanel" aria-labelledby="fields-tab">
						<div class="dragdrop-wrap">
							<ul class="nav nav-tabs" id="myTab" role="tablist">
								<li class="nav-item">
									<a
										class="nav-link active"
										id="standard-tab"
										data-toggle="tab"
										href="#standard"
										role="tab"
										aria-controls="standard"
										aria-selected="false"
									>Standard</a>
								</li>
								<li class="nav-item">
									<a
										class="nav-link"
										id="myfields-tab"
										data-toggle="tab"
										href="#custom-fields"
										role="tab"
										aria-controls="custom-fields"
										aria-selected="true"
									>Custom Fields</a>
								</li>
							</ul>
							<div class="tab-content" id="myTabContent">
								<div
									class="tab-pane fade show active"
									id="standard"
									role="tabpanel"
									aria-labelledby="standard-tab"
								>
									<ul class="dragArea dragdrop-items">
										<li v-for="(item, index) in itemDragable" :key="index" class="menu-item">
											<Drag
												class="drag"
												:transfer-data="{ item }"
												@dragstart="startDraggingItem"
												@dragend="dragEndItem"
											>
												<span>{{ item.label }}</span>
												<i class="icon icon-resize-plus-2"></i>
											</Drag>
										</li>
									</ul>
								</div>
								<div
									class="tab-pane fade"
									id="custom-fields"
									role="tabpanel"
									aria-labelledby="custom-fields-tab"
								>
									<ul class="dragArea dragdrop-items">
										<li v-for="(item, index) in customFields" :key="index" class="menu-item">
											<Drag
												class="drag"
												:transfer-data="{ item }"
												@dragstart="startDraggingItem"
												@dragend="dragEndItem"
											>
												<span>{{ item.name }}</span>
												<i class="icon icon-resize-plus-2"></i>
											</Drag>
										</li>
									</ul>
									<div class="custom-field-wrap-btn">
										<button
											type="button"
											class="btn btn-warning btn-block"
											@click="showAddCustomFieldModal"
										>Add Custom Field</button>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="tab-pane" id="styles" role="tabpanel" aria-labelledby="styles-tab">
						<div class="style-wrap">
							<div class="option-card">
								<div class="option-card-header">
									<h3>Layout</h3>
								</div>
								<div class="option-card-body">
									<div class="d-flex justify-content-between">
										<p>Inline Forms</p>
										<div class="toggle">
											<input
												type="checkbox"
												class="tgl tgl-light"
												id="misc-toggle"
												v-bind:checked="inlineForm ? 'checked': '' "
												@change="changeLayout"
											>
											<label class="tgl-btn" for="misc-toggle"></label>
										</div>
									</div>
									<br/>
									<div class="d-flex justify-content-between">
										<p>Show Label</p>
										<div class="toggle">
											<input
												type="checkbox"
												class="tgl tgl-light"
												id="label-toggle"
												v-bind:checked="formLabelVisible ? 'checked': '' "
												@change="layoutFormLabelVisible"
											>
											<label class="tgl-btn" for="label-toggle"></label>
										</div>
									</div>
								</div>
							</div>
							<div class="option-card">
								<div class="option-card-header">
									<h3>Form Style</h3>
								</div>
								<div class="option-card-body">
									<div class="style-group">
										<label>Background</label>
										<div class="color-picker">
											<span class="hash">#</span>
											<input
												type="text"
												class="form-control"
												v-bind:value="formStyle.bgColor"
												@change="setFormBackground($event.target.value)"
											>
											<span
												class="the-color"
												v-bind:style="{backgroundColor: '#' + formStyle.bgColor}"
												@click="pickerOnFormBgClick"
											></span>

											<chrome-picker
												:value="formStyle.bgColor"
												v-show="formStyle.showBgColorPicker"
												style="position: absolute;top: 42px;right: 0px;z-index:99"
												@input="pickerFormBgColor"
											/>
										</div>
									</div>
									<div class="style-group">
										<label>Font Color</label>
										<div class="color-picker">
											<span class="hash">#</span>
											<input
												type="text"
												class="form-control"
												v-bind:value="formStyle.color"
												@change="setFormColor($event.target.value)"
											>
											<span
												class="the-color"
												v-bind:style="{backgroundColor: '#' + formStyle.color}"
												@click="pickerOnFormColorClick"
											></span>

											<chrome-picker
												:value="formStyle.color"
												v-show="formStyle.showColorPicker"
												style="position: absolute;top: 50px;right: 0px;z-index:99"
												@input="pickerFormColor"
											/>
										</div>
									</div>
									<div class="style-group">
										<label>Border</label>
										<div class="border-picker">
											<input
												type="text"
												class="form-control"
												v-bind:value="formStyle.border"
												@change="setFormBorder($event.target.value)"
											>
											<span class="px">px</span>
											<div class="pixel-count-btns">
												<button type="button" class="pixel-up" @click="clickBorderPlus">
													<i class="icon icon-arrow-up-1"></i>
												</button>
												<button type="button" class="pixel-down" @click="clickBorderMinus">
													<i class="icon icon-arrow-down-1"></i>
												</button>
											</div>
											<span class="the-color" style="background: #ddd"></span>
										</div>
									</div>
									<div class="style-group">
										<label>Corner Radius</label>
										<div class="pixel-count">
											<input
												type="text"
												class="form-control"
												v-bind:value="formStyle.radius"
												@change="setFormBorderRadius($event.target.value)"
											>
											<span class="px">px</span>
											<div class="pixel-count-btns">
												<button type="button" class="pixel-up" @click="clickRadiusPlus">
													<i class="icon icon-arrow-up-1"></i>
												</button>
												<button type="button" class="pixel-down" @click="clickRadiusMinus">
													<i class="icon icon-arrow-down-1"></i>
												</button>
											</div>
										</div>
									</div>
									<div class="style-group">
										<label>Width</label>
										<div class="pixel-count">
											<input
												type="text"
												class="form-control"
												v-bind:value="formStyle.width"
												@change="setFormWidth($event.target.value)"
											>
											<span class="px">px</span>
											<div class="pixel-count-btns">
												<button type="button" class="pixel-up" @click="clickWidthPlus">
													<i class="icon icon-arrow-up-1"></i>
												</button>
												<button type="button" class="pixel-down" @click="clickWidthMinus">
													<i class="icon icon-arrow-down-1"></i>
												</button>
											</div>
										</div>
									</div>
								</div>
							</div>
							<!--Custom CSS-->
							<div class="option-card">
								<div class="option-card-header">
									<h3>Custom CSS</h3>
								</div>
								<div class="option-card-body">
									<div class="form-group">
										<textarea class="form-control" rows="4" v-model="emebedHtml" @blur="recordCustomStyle"></textarea>
									</div>
									<div class="form-group d-flex justify-content-end">
										<button class="btn btn-primary btn-sm">Inspector</button>
									</div>
								</div>
							</div>
							<div class="option-card">
								<div class="option-card-header">
									<h3>Miscellaneous</h3>
								</div>
								<div class="option-card-body">
									<div class="d-flex justify-content-between">
										<p>Agency Branding</p>
										<div class="toggle">
											<input
												type="checkbox"
												class="tgl tgl-light"
												id="branding-toggle"
												v-bind:checked="isBrandingActive ? 'checked': '' "
												@change="changeBrandingStatus"
											>
											<label class="tgl-btn" for="branding-toggle"></label>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="tab-pane" id="options" role="tabpanel" aria-labelledby="options-tab">
						<div class="style-wrap">
							<div class="option-card">
								<div class="option-card-header">
									<h3>Form name</h3>
								</div>
								<div class="option-card-body">
									<div class="form-group">
										<input class="form-control" type="text" v-model="formName" @change="saveForm">
									</div>
								</div>
							</div>
							<div class="option-card">
								<div class="option-card-header">
									<h3>On Submit</h3>
								</div>
								<div class="option-card-body">
									<div class="form-group">
										<select class="selectpicker" @change="alterFormAction($event)">
											<option :selected="formAction.actionType == '1'" value="1">Open URL</option>
											<option :selected="formAction.actionType == '2'" value="2">Show Thank You</option>
										</select>
									</div>
									<div class="form-group">
										<input v-if="formAction.actionType == 1" class="form-control" type="text" v-model="formAction.redirectURL" @change="saveForm" placeholder="Enter URL Here">
										<input v-if="formAction.actionType == 2" class="form-control" type="text" v-model="formAction.thankyouText" @change="saveForm" placeholder="Enter Thankyou Message">
									</div>
								</div>
							</div>

							<!-- <div class="option-card">
                <div class="option-card-header">
                  <h3>Form Action</h3>
                </div>
                <div class="option-card-body">
                  <div class="form-group">
                    <input type="text" class="form-control" value="Add a tag">
                  </div>
                  <div class="form-group">
                    <button class="btn btn-primary btn-block">
                      <i class="icon-plus"></i>
                      Add Action
                    </button>
                  </div>
                </div>
							</div> -->
							<!-- <div class="option-card">
								<div class="option-card-header">
									<h3>Advanced</h3>
								</div>
								<div class="option-card-body">
									<div class="form-group">
										<div class="toggle">
											<input type="checkbox" class="tgl tgl-light" id="advanced-toggle" checked>
											<label class="tgl-btn" for="advanced-toggle"></label>
										</div>
									</div>
									<p>
										Allow blank fields to overwrite existing field data.
										<a
											href="#"
											class="hl_tooltip"
											data-toggle="tooltip"
											data-placement="top"
											title
											data-original-title="Some text goes here."
										>?</a>
									</p>
								</div>
							</div> -->
						</div>
					</div>
				</div>
				<!--Overlay Settings-->
				<formFieldsOverlaySettings
				v-if="fieldSettings.enable"
				:fieldInfo="fieldSettings.fieldInfo"
				:formButtonStyle="buttonStyle"
				/>
				<!--Overlay settings end-->
			</aside>
			<!-- END of .hl_form-builder--sidebar -->
		</section>
		<!-- END of .form-builder -->
		<AddCustomFieldModal
			v-if="drawComponent"
			:values="addCustomFieldModal"
			@hidden="addCustomFieldModal.visible"
			v-on:hide="closeAddCustomFieldModal"
		/>
	</section>
</template>

<script lang="ts">
import Vue from "vue";
import { mapState } from "vuex";
import { Drag, Drop } from "vue-drag-drop";
import { Container, Draggable } from "vue-smooth-dnd";
import { Chrome } from "vue-color";
import ImageTools from "../../../util/image_tools";
import { v4 as uuid } from "uuid";

/*---Component---*/
import TopBar from "../../components/TopBar.vue";
import SideBar from "../../components/SideBar.vue";
const AddFieldDropzone = () => import("../../components/marketing/AddFieldDropzone.vue").then(m => m.default);
const MoonLoader = () => import("../../components/MoonLoader").then(m => m.default);
const AddCustomFieldModal = () => import("../../components/agency/AddCustomFieldModal.vue").then(m => m.default);
const FormbuilderAddCustomFields = () => import("../../components/marketing/FormbuilderAddCustomFields.vue").then(m => m.default);
const formFieldsOverlaySettings = () => import( "../../components/marketing/formFieldsOverlaySettings.vue");

/*---Model---*/
import { CompanyState } from "../../../store/state_models";
import { Company, Formbuilder, CustomField} from "../../../models";
import { FileAttachment } from "@/store/state_models";

/*--Firebase--*/
import firebase from "firebase/app";

declare var $: any;
const path = require('path');

const imageTools = new ImageTools();

let unsubscribeCustomFields: () => void;
export default Vue.extend({
	components: {
		TopBar,
		SideBar,
		Drag,
		Drop,
		Container,
		Draggable,
		"chrome-picker": Chrome,
		"add-field-dropzone": AddFieldDropzone,
		MoonLoader,
		AddCustomFieldModal,
		FormbuilderAddCustomFields,
		formFieldsOverlaySettings
	},
	data() {
		return {
			loading: true,
			itemDragable: [
				{
					label: "Full Name",
					tag: "full_name",
					type: "text",
					placeholder: "Full Name",
					required: false
				},
				{
					label: "First Name",
					tag: "first_name",
					type: "text",
					placeholder: "First Name",
					required: false
				},
				{
					label: "Last Name",
					tag: "last_name",
					type: "text",
					placeholder: "Last Name",
					required: false
				},
				{
					label: "Phone",
					tag: "phone",
					type: "text",
					placeholder: "Phone",
					required: true,
				},
				{
					label: "Email",
					tag: "email",
					type: "email",
					placeholder: "Email",
					required: true,
				},
				{
					label: "Organization",
					tag: "organization",
					type: "text",
					placeholder: "Organization",
					required: false
				},
				{
					label: "Text",
					tag: "header",
					type: "h1",
					placeholder: "header"
				},
				/*{
				  label: "Textarea",
				  tag: "textarea",
				  type: "textarea",
				  placeholder: "Textarea"
				},*/
				{
					label: "Image",
					tag: "image",
					type: "img",
					placeholder: "Image"
				},
				{
					label: "Captcha",
					tag: "captcha",
					type: "captcha",
					placeholder: "Captcha"
				},
				{
					label: "Button",
					tag: "button",
					type: "submit",
					placeholder: "Button"
				}
			],

			ItemDropable: [] as any,
			ItemDropableCustomField: [] as any,

			attrName: "",

			formStyle: {
				bgColor: "FFFFFF",
				color: "000000",
				border: 1,
				radius: 4,
				width: 550,
				showBgColorPicker: false,
				showColorPicker: false,
				addCustomFieldModal: { visible: false } as { [key: string]: any },
			},

			buttonStyle: {
				bgColor: "2A3135",
				color: "FFFFFF",
				border: 0,
				radius: 0.3125,
				padding: 11
			},
			dragOptions: {
				isDragEntered: false
			},
			inlineForm: false,
			formLabelVisible: true,
			isBrandingActive: true,
			styleNode: {},
			emebedHtml: "",
			showDropZones: false,
			fieldPushIndex: -1,
			//Form data
			formbuilder: {} as Formbuilder,
			currentLocationId: "",
			builderId: "",
			formBuilderCount: 0 as number,
			formName: "",
			customFields: [] as CustomField[],
			//Custom fields
			drawComponent: false as boolean,
			addCustomFieldModal: { visible: false } as { [key: string]: any },

			//File upload
			attachments: [] as string[],
			filesAdded: [] as FileAttachment[],
			isImageFileInStore: false,
			formAction: {
				redirectURL: "",
				thankyouText: "",
				actionType : 1
			},
			fieldSettings:{
				enable: false,
				fieldInfo: {}
			},
			selectedFieldIndex: 0
		};
	},
	methods: {
		async initialData() {
			this.currentLocationId = this.$router.currentRoute.params.location_id;
			this.builderId = this.$router.currentRoute.params.builder_id;
			console.log("Builder ID", this.builderId);
			this.formBuilderCount = await Formbuilder.getCountByLocationId(
				this.currentLocationId
			);

			if (!this.builderId) {
				//this.saveForm();
				this.$router.replace({
					name: "form_builder",
					params: this.$route.params
				});
				this.loading = false;
			} else {
				this.formbuilder = await Formbuilder.getById(this.builderId);
				// Probably has wrong ID in url, redirect it workflows page
				if (!this.formbuilder) {
					this.$router.replace({
						name: "form_builder",
						params: this.$route.params
					});
				}
				//Load form from json
				this.formLoadFromJson();
			}
		},
		async fetchCustomFields() {
			if (unsubscribeCustomFields) unsubscribeCustomFields();

			unsubscribeCustomFields = CustomField.getByLocationId(this.currentLocationId).onSnapshot(snapshot => {
				this.customFields = snapshot.docs.map(d => new CustomField(d));
				//console.log("customField",this.customFields);
			});
		},
		isFieldExist(itemAttribName: any, attrKey: any) {
      const exceptStore = ['header','image'];
			for (var i = 0; i < this.ItemDropable.length; i++) {
				if (attrKey in this.ItemDropable[i]) {
					var itemTag = this.ItemDropable[i][attrKey];
					if (itemTag == itemAttribName && exceptStore.indexOf(itemAttribName) < 0) {
						return { status: true, index: i };
					}
				}
			}
			return { status: false };
		},
		camelize(str:string) {
      return str.replace(/\W+(.)/g, function(match, chr){
				return chr.toUpperCase();
			});
    },
		handleStandredFields(data: any) {
			this.attrName = data.item.tag;
			var el = this;

			//Check if item Exist then return
			var isFieldExist = this.isFieldExist(this.attrName, 'tag');
			if (isFieldExist.status) {
				this.fieldPushIndex = -1;
				return false;
			}

			var createDOM: any = data.item;
			if (this.fieldPushIndex < 0) {
				this.ItemDropable.push(createDOM);
			} else {
				this.ItemDropable.splice(this.fieldPushIndex, 0, createDOM);
			}
			return true;
		},
		handleCustomFields(data: any) {
			//Check if item Exist then return
			var isFieldExist = this.isFieldExist(data.item.id, 'id');
			if (isFieldExist.status) {
				this.fieldPushIndex = -1;
				return false;
			}

			//Common fields for both standrard and custom
			var createDOM: any = {
				id: data.item.id,
				name: data.item.name,
				type: (data.item.dataType).toLowerCase(),
				required: false,
				label: data.item.name,
				placeholder: (data.item.placeholder) ? data.item.placeholder : data.item.name,
				tag: data.item.id
			}

			//Adding more fields on the basis of field type -> Accept camelcase only
			for(var key in data.item.data){
				var camelkey = this.camelize(key.replace(/_/g, ' '));
				createDOM[camelkey] = data.item.data[key];
			}

			//Insert to itemDrop array
			if (this.fieldPushIndex < 0) {
				this.ItemDropable.push(createDOM);
			} else {
				this.ItemDropable.splice(this.fieldPushIndex, 0, createDOM);
			}
			return true;
		},
		handleDrop(data: any, event: any) {
      data = lodash.cloneDeep(data);
			var isFieldAllowedToAdd = false;
			if (data.item.id) {
				console.log("Droped custom fields");
				isFieldAllowedToAdd = this.handleCustomFields(data);
			} else {
				console.log("Drop Standered fields");
				isFieldAllowedToAdd = this.handleStandredFields(data);
			}

			if(isFieldAllowedToAdd){
				this.dragOptions.isDragEntered = false;
				this.fieldPushIndex = -1; //reset field index
				this.saveForm();
			}
		},
		removeField(index: number) {
			//Check if item Exist then return
			this.ItemDropable.splice(index, 1);
			this.saveForm();
		},
		removeCustomField(field: any) {
			console.log('custom data', field.id);
			//Check if item Exist then return
			var isFieldExist: any = this.isFieldExist(field.id, 'id');
			var index: number = isFieldExist.index;
			this.ItemDropable.splice(index, 1);
			this.saveForm();
		},
		applyDrag(arr: any, dragResult: any) {
			const { removedIndex, addedIndex, payload } = dragResult;
			if (removedIndex === null && addedIndex === null) return arr;

			const result = [...arr];
			let itemToAdd = payload;

			if (removedIndex !== null) {
				itemToAdd = result.splice(removedIndex, 1)[0];
			}

			if (addedIndex !== null) {
				result.splice(addedIndex, 0, itemToAdd);
			}

			return result;
		},
		onDrop(dropResult: any) {
			console.log("droped", dropResult);
			this.ItemDropable = this.applyDrag(this.ItemDropable, dropResult);
			this.saveForm();
		},
		isValidHexColor(color: string) {
			let isValidColor: boolean = /(^[0-9A-F]{8}$)|(^[0-9A-F]{6}$)|(^[0-9A-F]{3}$)/i.test(
				color
			);
			return isValidColor;
		},
		isColorPickerOpen(which: string) {
			if (which === "form-bg-color") {
				this.formStyle.showColorPicker = false;
			} else if (which === "form-color") {
				this.formStyle.showBgColorPicker = false;
			}
		},

		//---Form Style----
		setFormBackground(color: string) {
			if (this.isValidHexColor(color)) {
				this.formStyle.bgColor = color;
				this.saveForm();
			}
		},
		setFormColor(color: string) {
			if (this.isValidHexColor(color)) {
				this.formStyle.color = color;
				this.emebedHtml = "label{color:#" + color + "!important;}";
				this.saveForm();
			}
		},
		setFormBorder(value: any) {
			if (!isNaN(value)) {
				this.formStyle.border = value;
				this.saveForm();
			}
		},
		setFormBorderRadius(value: any) {
			if (!isNaN(value)) {
				this.formStyle.radius = value;
				this.saveForm();
			}
		},
		setFormWidth(value: any) {
			if (!isNaN(value)) {
				this.formStyle.width = value;
				this.saveForm();
			}
		},
		clickBorderPlus() {
			let border =
				this.formStyle.border >= 100 ? 100 : this.formStyle.border + 1;
			this.setFormBorder(border);
		},
		clickBorderMinus() {
			let border = this.formStyle.border <= 0 ? 0 : this.formStyle.border - 1;
			this.setFormBorder(border);
		},
		clickRadiusPlus() {
			let radius =
				this.formStyle.radius >= 100 ? 100 : this.formStyle.radius + 1;
			this.setFormBorderRadius(radius);
		},
		clickRadiusMinus() {
			let radius = this.formStyle.radius <= 0 ? 0 : this.formStyle.radius - 1;
			this.setFormBorderRadius(radius);
		},
		clickWidthPlus() {
			let width = this.formStyle.width + 1;
			this.setFormWidth(width);
		},
		clickWidthMinus() {
			let width = this.formStyle.width <= 0 ? 0 : this.formStyle.width - 1;
			this.setFormWidth(width);
		},
		//---Color picker form
		pickerFormBgColor(newColor: any) {
			var color = newColor.hex8.replace("#", "");
			this.setFormBackground(color);
		},
		pickerOnFormBgClick() {
			this.isColorPickerOpen("form-bg-color");
			this.formStyle.showBgColorPicker = !this.formStyle.showBgColorPicker;
		},
		pickerFormColor(newColor: any) {
			var color = newColor.hex8.replace("#", "");
			this.setFormColor(color);
		},
		pickerOnFormColorClick() {
			this.isColorPickerOpen("form-color");
			this.formStyle.showColorPicker = !this.formStyle.showColorPicker;
		},

		//---Layout change
		changeLayout() {
			this.inlineForm = !this.inlineForm;
			this.saveForm();
		},
		layoutFormLabelVisible(){
			this.formLabelVisible = !this.formLabelVisible;
			this.saveForm();
		},
		changeBrandingStatus() {
			this.isBrandingActive = !this.isBrandingActive;
			this.saveForm();
		},
		startDraggingItem() {
			this.showDropZones = true;
		},
		dragEndItem() {
			this.showDropZones = false;
		},
		addFieldAfter(emitData: any) {
			this.fieldPushIndex = emitData.data;
		},
		recordCustomStyle() {
			this.saveForm();
		},
		getStandardItemList(niddle: string) {
			for (let j = 0; j < this.itemDragable.length; j++) {
				if (this.itemDragable[j].tag == niddle) {
					var itemObjString = JSON.stringify(this.itemDragable[j]);
					return JSON.parse(itemObjString);
				}
			}
		},
		formToJson() {
			let fields: any = [];
			for (let i = 0; i < this.ItemDropable.length; i++) {
				let field: any = {};
				if ("id" in this.ItemDropable[i]) {
					field = this.ItemDropable[i];
				} else if ("url" in this.ItemDropable[i] && this.ItemDropable[i].tag == "image") {
					field = this.ItemDropable[i];
				} else {
					// field = this.getStandardItemList(this.ItemDropable[i].tag);
					field = this.ItemDropable[i];
				}
				fields.push(field);
			}
			let formJsonObj = {
				form: {
					layout: this.inlineForm,
					width: this.formStyle.width,
					formLabelVisible : this.formLabelVisible,
					style: {
						ac_branding: this.isBrandingActive,
						background: this.formStyle.bgColor,
						color: this.formStyle.color,
						border: {
							border: this.formStyle.border,
							radius: this.formStyle.radius,
							style: "dashed",
							color: "CDE0EC"
						}
					},
					button: {
						background: this.buttonStyle.bgColor,
						color: this.buttonStyle.color,
						border: {
							border: this.buttonStyle.border,
							radius: this.buttonStyle.radius,
							padding: this.buttonStyle.padding
						}
					},
					customStyle: this.emebedHtml,
					fields: fields,
					company: {
						logoURL: this.company.logoURL ? this.company.logoURL : "",
						domain: this.company.domain ? this.company.domain : "",
						name: this.company.name ? this.company.name : ""
					},
					formAction:{
						redirect_url: this.formAction.redirectURL,
						thankyouText: this.formAction.thankyouText,
						actionType: this.formAction.actionType,
					}
				}
			};
			return formJsonObj;
		},
		async saveForm() {
			let jsonObj = this.formToJson();
			this.formbuilder.name = this.formName;

			//Block for Insert
			if (!this.formbuilder) {
				console.log("SaveForm - If block Executed");
				this.formbuilder = new Formbuilder();
				this.formbuilder.name = "Form " + (this.formBuilderCount + 1);
			} else {
				console.log("SaveForm - Else block Executed");
			}

			this.formbuilder.locationId = this.currentLocationId;
			this.formbuilder.formData = jsonObj;
			await this.formbuilder.save();
			console.log("Entry Saved");
		},
		formLoadFromJson() {
			const form = this.formbuilder.formData.form;
			const el = this;
			this.formName = this.formbuilder.data.name;
			this.selectedFieldIndex = 0;

			//If form is empty for first time
			if (form == undefined) {
				this.loading = false;
				return true;
			}

			form.fields.map(function (value: any, key: any) {
				if ("url" in value && value.tag == "image") {
					el.isImageFileInStore = true;
				}
				value.active = false;
				el.ItemDropable.push(value);
			});

			if ("style" in form) {
				this.formStyle.bgColor = form.style.background; //BG color
				this.formStyle.color = form.style.color; //Font color
				//this.emebedHtml = "label{color:#" + this.formStyle.color + "!important;}";
				this.formStyle.border = form.style.border.border; //Border
				this.formStyle.radius = form.style.border.radius; //Corner Radius
				this.isBrandingActive = form.style.ac_branding; //AC Branding
			}

			if ("button" in form) {
				this.buttonStyle.bgColor = form.button.background; // BG color
				this.buttonStyle.color = form.button.color; //Font Color
				this.buttonStyle.border = form.button.border.border; //Button Border
				this.buttonStyle.radius = form.button.border.radius; //Button Radius
				this.buttonStyle.padding = form.button.border.padding; //Button Padding
			}
			if ("formAction" in form) {
				this.formAction.redirectURL = form.formAction.redirect_url;
				this.formAction.thankyouText = form.formAction.thankyouText;
				this.formAction.actionType = form.formAction.actionType;
			}else if ("redirect_url" in form){
				this.formAction.redirectURL = form.redirect_url;
				this.formAction.thankyouText = "";
				this.formAction.actionType = 1;
			}

			this.formStyle.width = form.width; //Width
			this.inlineForm = form.layout; //Form Layout

			if ("customStyle" in form && form.customStyle.trim("").length > 0) {
				this.emebedHtml = form.customStyle;
			}else {
				this.emebedHtml = "label{color:#" + this.formStyle.color + "!important;}";
			}

			if("formLabelVisible" in form){
				this.formLabelVisible = form.formLabelVisible;
			}
			this.loading = false;
		},
		showAddCustomFieldModal() {
			this.reset();
			this.addCustomFieldModal = {
				visible: true,
				location_id: this.currentLocationId
			};
		},
		reset() {
			var vm = this;
			vm.drawComponent = false;
			Vue.nextTick(function () {
				vm.drawComponent = true;
			});
		},
		closeAddCustomFieldModal() {
			this.addCustomFieldModal = {
				visible: false
			};
		},
		async onFileChange(e: any) {
			const element = <HTMLInputElement>e.target;
			if (!element.files) return;
			for (let i = 0; i < element.files.length; i++) {
				this.vfileAdded(element.files[i]);
			}
			element.files = null;
		},
		async vfileAdded(file: File) {
			const response = <File | Blob>(
				await imageTools.resize(file, { height: 1000, width: 1000 })
			);
			this.filesAdded.push({
				name: file.name,
				type: file.type,
				url: URL.createObjectURL(response),
				data: response
			});
			this.isImageFileInStore = true; //Show image to form now/dont wait for image upload

			this.send();
			//console.log("File added", this.filesAdded[0].url);
		},
		send: async function () {
			let el = this;
			const urls = [] as string[];
			if (this.filesAdded.length > 0) {
				const basePath =
					"location/" +
					this.currentLocationId +
					"/form/" +
					this.builderId +
					"/";
				const newURLs: string[] = await Promise.all(
					this.filesAdded.map(async attachment => {
						let imagePath = basePath + uuid() + path.extname(attachment.name);
						var uploadPath = firebase.storage().ref(imagePath);
						const snapshot = await uploadPath.put(attachment.data, {
              contentType: attachment.type,
              contentDisposition: `inline; filename="${attachment.name}"`,
							customMetadata: { name: attachment.name }
						});
						return await snapshot.ref.getDownloadURL();
					})
				);
				urls.push.apply(urls, newURLs.filter(url => url));

				//Set uploaded image url to ItemDropable
				if (urls.length > 0) {
					// el.ItemDropable.map(function (value: any, index: any) {
					// 	if ("tag" in value && value.tag == 'image') {
					// 		el.$set(el.ItemDropable[index], 'url', urls[0]);
					// 		// el.$set(el.itemDragable[6], 'url', urls[0]);
					// 	}
          // });
          this.$set(this.ItemDropable[this.selectedFieldIndex], 'url', urls[0]);
					el.saveForm(); //Save entry for modified dropItems
				} else {
					this.isImageFileInStore = false; //If image is not uploaded
				}
			}
		},
		showIntegratePage() {
			this.$router.push({
				name: "form_builder_integrate",
				params: {
					location_id: this.currentLocationId,
					builder_id: this.builderId
				}
			});
		},
		redirectToListPage() {
			this.$router.push({
				name: "form_builder",
				params: {
					location_id: this.currentLocationId
				}
			});
		},
		alterFormAction(event:any){
			this.formAction.actionType = event.target.value;
			this.saveForm();
		},
		setFieldActive(index:number){
			this.selectedFieldIndex = index;
			var el = this;
			this.ItemDropable.map(function (value: any, key: any) {
				if(key === index)
					el.$set(value, 'active', true);
				else
					el.$set(value, 'active', false);
			});
			//Don't show next screen for image one
			if(this.ItemDropable[index].type=='img'){
				this.fieldSettings.enable = false;
				return;
			}
			this.fieldSettings.enable = true;
			this.fieldSettings.fieldInfo = this.ItemDropable[index];
			// console.log(this.fieldSettings.fieldInfo);
		},
		fieldSettingInput(data:any){
			switch(data.type){
				case 'label':
					this.ItemDropable[this.selectedFieldIndex].label = data.value;
					break;
				case 'placeholder':
					this.ItemDropable[this.selectedFieldIndex].placeholder = data.value;
					break;
				case 'required':
					this.ItemDropable[this.selectedFieldIndex].required = data.value;
					break;
				case 'submit':
					this.ItemDropable[this.selectedFieldIndex].label = data.value;
					break;
				case 'text-size':
					this.$set(this.ItemDropable[this.selectedFieldIndex],'size',data.value);
					break;
				case 'text-weight':
					this.$set(this.ItemDropable[this.selectedFieldIndex],'weight',data.value);
					break;
				case 'text-color':
					this.$set(this.ItemDropable[this.selectedFieldIndex],'color',data.value);
					break;
				case 'text-font':
					this.$set(this.ItemDropable[this.selectedFieldIndex],'font',data.value);
					break;
				case 'text-label':
					this.ItemDropable[this.selectedFieldIndex].label = data.value;
					break;
				case 'text-align':
					this.$set(this.ItemDropable[this.selectedFieldIndex],'align',data.value);
					break;
			}
			this.saveForm();
		},
		fieldSettingButton(data:any){
			switch(data.type){
				case 'bgcolor':
					this.buttonStyle.bgColor = data.value;
					break;
				case 'color':
					this.buttonStyle.color = data.value;
					break;
				case 'border':
					this.buttonStyle.border = data.value;
					break;
				case 'radius':
					this.buttonStyle.radius = data.value;
					break;
				case 'padding':
					this.buttonStyle.padding = data.value;
					break;
				case 'btn-align':
					this.$set(this.ItemDropable[this.selectedFieldIndex],'align',data.value);
					break;
				case 'btn-fullwidth':
					this.$set(this.ItemDropable[this.selectedFieldIndex],'fullwidth',data.value);
					break;
			}
			this.saveForm();
		},
		fieldSettingBack(data:any){
			var el = this;
			this.ItemDropable.map(function (value: any, key: any) {
				el.$set(value, 'active', false);
			});
			this.fieldSettings.enable = false;
		},
		onClick(e: any) {
			let el: any = this.$refs.formBuilderReferance;
			let target = e.target;
			let elOpt: any = this.$refs.fieldSettingsRefs;
			if (el != undefined && (el !== target) && !el.contains(target) &&
					elOpt != undefined && (elOpt !== target) && !elOpt.contains(target)) {
				this.fieldSettingBack(true);
			}
		}
	},
	updated(){
		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker('refresh');
		}
	},
	mounted() {
		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker();
		}

		let style = document.createElement("style");
		style.type = "text/css";
		style.appendChild(document.createTextNode(""));
		this.styleNode = style.childNodes[0]; // a reference I store in the data hash
		document.head.appendChild(style);

		//On background click
		document.addEventListener('click', this.onClick)
	},
	watch: {
		"$route.params.location_id": function (id) {
			this.currentLocationId = id;
			this.redirectToListPage();
		},
		emebedHtml: function (val) {
			let styleStore = val.split("}");
			let styleString: string = "";
			styleStore.map(function (value: any, key: any) {
				if (value != "" && value.length > 0) {
					styleString += "#_builder-form " + value + "} ";
				}
			});
			this.styleNode.textContent = styleString;
		}
	},
	computed: {
		...mapState("company", {
			company: (s: CompanyState) => {
				return s.company ? new Company(s.company) : undefined;
			}
		})
	},
	created() {
		this.$bus.$on("add-field-to-drozone", this.addFieldAfter);
		this.$bus.$on("remove-custom-field", this.removeCustomField);
		this.$bus.$on("field-setting-input", this.fieldSettingInput);
		this.$bus.$on("field-setting-button", this.fieldSettingButton);
		this.initialData();
		this.fetchCustomFields();
	},
	beforeDestroy() {
		this.$bus.$off("add-field-to-drozone", this.addFieldAfter);
		this.$bus.$off("remove-custom-field", this.removeCustomField);
		this.$bus.$off("field-setting-input", this.fieldSettingInput);
		this.$bus.$off("field-setting-button", this.fieldSettingButton);
		document.addEventListener('click', this.onClick)
    if (unsubscribeCustomFields) unsubscribeCustomFields();
	}
});
</script>
