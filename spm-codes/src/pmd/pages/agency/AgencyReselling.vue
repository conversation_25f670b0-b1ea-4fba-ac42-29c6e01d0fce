<template>
  <div>
    <SideBarV2 v-if="getSideBarVersion === 'v2'"/>
    <SideBar v-else />
    <TopBar />
    <section class="hl_wrapper">
      <div id="agency-reselling">
        <div id="tab-header">Reselling</div>
        <div id="products">
          <div class="each_product">
            <YextResellingComponent />
          </div>
          <div class="each_product">
            <WordpressResellingComponent />
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import TopBar from '../../components/TopBar.vue'
import SideBar from '../../components/agency/SideBar.vue'
import SideBarV2 from '../../components/sidebar/SideBar.vue'
import { User } from '@/models'

const YextResellingComponent = () =>
  import(
    /* webpackChunkName: "yext-agency-level-offer-component" */ '@/pmd/components/agency/reselling/YextResellingComponent.vue'
  )

const WordpressResellingComponent = () =>
  import(
    /* webpackChunkName: "yext-agency-level-offer-component" */ '@/pmd/components/agency/reselling/wordpress/WordpressResellingComponent.vue'
  )

export default Vue.extend({
  components: {
    TopBar,
    SideBar,
    YextResellingComponent,
    WordpressResellingComponent,
    SideBarV2
  },
  computed: {
    agencyInSaasPlan(): boolean {
      return this.$store.getters['company/inSaasPlan']
    },
    user() {
        const user = this.$store.state.user.user
        return user ? new User(user) : undefined
    },
    getSideBarVersion(): string {
        return this.$store.getters['sidebarv2/getVersion'] 
    },
  },
})
</script>
<style lang="scss">
#agency-reselling {
  #tab-header {
    background: #fff;
    font-size: 1rem;
    color: #000;
    padding: 10px 20px;
  }
  #products {
    padding: 10px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 10px;
    max-width: 1700px;
    margin: auto;
/*     .each_product {
      .card {
        height: 100%;
        .card-header {
          padding: 10px 20px;

          .header-toggle {
            display: grid;
            grid-template-columns: auto auto;
            grid-gap: 10px;
            align-items: center;
          }

          .card-header__label {
            margin: 0;
            font-size: 1rem;
            font-weight: 400;
            color: #2a3844;
          }
        }
        .card-body {
          display: grid;
          grid-template-columns: auto 1fr;
          grid-column-gap: 40px;
          .logo {
            max-height: 100px;
            max-width: 100px;
            overflow: hidden;
            img {
              height: 100%;
            }
          }
          .details {
            display: grid;
            grid-template-rows: auto auto auto 1fr auto;
            h3 {
              font-size: 1.5rem;
              font-weight: 600;
              color: #4f4f4f;
            }
            .sub-heading {
              font-size: 1rem;
              margin: 10px 0;
              font-weight: 600;
              color: #4f4f4f;
            }
          }

          .upcoming-product {
            position: absolute;
            width: 100%;
            height: 100%;
            background: #ffffffc7;
            top: 0;
            left: 0;
            z-index: 1;
            display: grid;
            justify-content: center;
            align-items: center;
            h3 {
              background: #feebc8;
              padding: 10px 30px;
              color: #ed8936;
              border-radius: 1px;
              font-size: 1rem;
            }
          }

          .yext_offer_table {
            form {
              display: grid;
              grid-template-columns: auto 1fr;
              grid-gap: 10px;
              label {
                font-size: 1rem;
                color: #4f4f4f;
              }

              .prefix_input {
                position: relative;
                .prefix {
                  position: absolute;
                  top: 2px;
                  left: 3px;
                  background: white;
                  padding: 0 10px;
                  display: grid;
                  align-items: center;
                }
                input {
                  padding: 0 0 0 30px;
                  width: 90px;
                }
              }
            }
            [for='agency_profit'] {
              color: #27ae60;
            }
          }
          .footer-controls {
            display: grid;
            justify-content: right;
            grid-gap: 15px;
          }
        }
      }

      .reselling-btn {
        background: #27ae60;
        padding: 6px 10px;
        justify-self: right;
      }
      .reselling-toggle {
        display: grid;
        grid-template-columns: auto 1fr;
        justify-content: right;
        grid-gap: 10px;
        .tgl-light:checked + .tgl-btn:after {
          background: white;
        }
        .tgl-light:checked + .tgl-btn {
          background: #3182ce;
        }
      }
    } */
    
  }
}
</style>