<template>
  <div id="invite-trend" class="card avg-rating">
    <div class="card-header card-header--compact space-between">
      <h2>Invite Trends</h2>
      <div>
        <span v-if="filter === 'thisMonthWeekly'"> This Month</span>
        <span v-else-if="filter === 'lastSixMonthMonthly'"> Last 6 Month</span>
        <span v-else-if="filter === 'thisYearMonthly'"> This Year </span>
        <span v-else> {{ filter }}</span>
        <select class="selectpicker more-select" v-model="filter">
          <!-- <option>Today</option> -->
          <option value="thisMonthWeekly">This Month</option>
          <option value="lastSixMonthMonthly">Last 6 Months</option>
          <option value="thisYearMonthly">This Year</option>
        </select>
      </div>
    </div>
    <div class="card-body --max-height">
      <div class="chartContainer">
        <highcharts
          class="answered-missed-chart"
          :options="invitesTrendsChart"
        ></highcharts>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { ReviewRequestAggregateData } from '@/store/state_models'
import { Chart } from 'highcharts-vue'

declare var $: any

export default Vue.extend({
  components: {
    highcharts: Chart,
  },
  data() {
    return {
      filter: 'lastSixMonthMonthly',
      currentLocationId: '',
      invitesTrendsChart: {
        credits: {
          enabled: false,
        },
        chart: {
          type: 'column',
          height: 300,
        },
        legend: {
          enabled: false,
        },

        title: {
          text: '',
        },

        xAxis: {
          gridLineWidth: 0,
          categories: [],
          crosshair: true,
        },
        yAxis: {
          allowDecimals: false,
          min: 0,
          title: {
            text: '',
          },
        },
        tooltip: {
          headerFormat:
            '<span style="font-size:10px">{point.key}</span><table>',
          footerFormat: '</table>',
          shared: true,
          useHTML: true,
        },
        plotOptions: {
          column: {
            pointPadding: 0.2,
            borderWidth: 0,
          },
        },
        series: [
          {
            name: 'Review Requested',
            color:'#188BF6',
            data: [],
          },
        ],
      },
    }
  },
  computed: {
    currentReviewsAggregate() {
      let requestAggrigate = this.$store.getters[
        'reviewRequestAggregate/current'
      ](this.filter)
      let result = { label: [], data: [] }
      if (requestAggrigate && requestAggrigate.length > 0) {
        switch (this.filter) {
          case 'lastSixMonthMonthly':
          case 'thisYearMonthly':
            result = {
              label: requestAggrigate.map((x: any) =>
                x.label.format('MMM, YY')
              ),
              data: requestAggrigate.map((x: any) => x.aggregate.totalRequests),
            }
            break
          default:
            result = {
              label: requestAggrigate.map((x: any) =>
                x.label.format('DD-MM-YY')
              ),
              data: requestAggrigate.map((x: any) => x.aggregate.totalRequests),
            }
            break
        }
      }
      return result
    },
    previousReviewsAggregate(): ReviewRequestAggregateData {
      return this.$store.getters['reviewRequestAggregate/previous'](this.filter)
    },
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.filter = 'lastSixMonthMonthly'
    },
    filter: function () {
     if (this.filter === 'thisMonthWeekly') {
        this.$store.dispatch(
          'reviewRequestAggregate/fetchThisMonthWeekly',
          this.currentLocationId
        )
      } else if (this.filter === 'lastSixMonthMonthly') {
        this.$store.dispatch(
          'reviewRequestAggregate/lastSixMonthMonthly',
          this.currentLocationId
        )
      } else if (this.filter === 'thisYearMonthly') {
        this.$store.dispatch(
          'reviewRequestAggregate/thisYearMonthly',
          this.currentLocationId
        )
      }
    },
    currentReviewsAggregate: function () {
      this.invitesTrendsChart.xAxis.categories =
        this.currentReviewsAggregate.label
      this.invitesTrendsChart.series[0].data = this.currentReviewsAggregate.data
    },
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id;
    if(this.currentReviewsAggregate){
      this.invitesTrendsChart.xAxis.categories = this.currentReviewsAggregate.label
      this.invitesTrendsChart.series[0].data = this.currentReviewsAggregate.data
    }

  },
  methods: {
    getClass(star: number) {
      if (star === 1) {
        return 'icon-star-filled'
      } else if (star === 0.5) {
        return 'icon icon-star-half'
      } else {
        return 'icon icon-star'
      }
    },
    getBarWidth(star: string): string {
      const total = lodash.sumBy(
        lodash.values(this.currentReviewsAggregate.breakdown)
      )
      const baseline = total ? total : 1
      const aggregate: any = this.currentReviewsAggregate.breakdown
      return (aggregate[star] / baseline) * 90 + '%'
    },
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
})
</script>
<style lang="scss" scoped>
#invite-trend {
  .space-between {
    display: flex;
    justify-content: space-between;
  }

  .card-header--compact {
    padding: 12px 50px 10px 30px;
  }

  .card-header {
    span {
      color: #afb8bc;
    }
    .more-select {
      top: 53%;
      transform: translateY(-53%);
    }
  }

  .card-body {
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    max-height: 300px;
    .chartContainer {
      width: 35vw;
    }
  }
}
</style>
