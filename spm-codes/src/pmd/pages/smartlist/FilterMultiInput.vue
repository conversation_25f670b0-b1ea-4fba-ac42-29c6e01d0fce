<template>
<div>
  <div class="form-row">
      <div class="form-group col-4">
        <UITextInput
          v-model="firstValue"
        />
              <!--:placeholder="option.filterName"-->
      </div>
      <div class="form-group col-8">
        <select v-model="secondValue" class="form-control selectpicker">
          <option v-for="s in option.secondValueOptions" :value="s">{{s}}</option>
        </select>
      </div>
  </div>
    <div class="d-block"><span v-if="!firstValue" class="--red">* Enter a value</span></div>
    <div><span v-if="!secondValue" class="--red">* Select an option</span></div>
    <div class="my-2" v-if="example" ><label style="font-size:1.1em !important;">{{example}}</label></div>
</div>
</template>

<script lang="ts">

import Vue from 'vue';
import {FilterOption} from './vm/option_vm'

declare var $: any
export default Vue.extend({
  name: 'FilterMultiInput',
  props : {
    bus : Vue,
    option: FilterOption
  },
  data () {
    return {
      firstValue: this.option.firstValue,
      secondValue: this.option.secondValue,
      waitTime: 300,
      debouncer:undefined as NodeJS.Timer | undefined,
      example: this.option.example || ''
    };
  },
  watch : {
    firstValue(n,o){
      this.option.firstValue = n;
      this.sync();
    },
    secondValue(n,o) {
      this.option.secondValue = n;
      this.sync();
    }
  },
  methods: {
    sync(){
      if(this.debouncer) clearTimeout(this.debouncer);
      this.debouncer = setTimeout(() => this.publish(),this.waitTime);
    },
    publish(){
     this.$emit('inputChange');
    },
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }
  }
})
</script>
