import { currency } from '@/util/currency_helper'

export default {
  data() {
    return {
      subscriptionData: {
        planDetails: {
          title: '',
          saasProduct: {
            title: '',
            complementaryCredits: {
              amount: 0,
              type: 'monthly',
            },
          },
          subscription: {
            current_period_start: null,
          },
          amount: '',
          billingInterval: '',
          symbol: '',
        },
        loading: false,
        error: null,
      },
    }
  },
  computed: {
    companyComputed() {
      return this.$store.state.company.company
    },
  },
  created() {
    // this.fetchSubscriptionDetails(this.saasPlanId || '', this.saasSettings.stripe_plan_details?.price_id || '')
    this.fetchStripeSubscriptionDetails()
  },
  methods: {
    async fetchStripeSubscriptionDetails() {
      try {
        this.subscriptionData.loading = true

        const { data: subscriptions } = await this.saasService.get(
          `/saas-config/${this.locationId}/saas-stripe-subscription-details?stripeAccountId=${this.companyComputed.stripe_connect_id}`
        )

        // console.log('subscription plans available --> ', subscriptions)

        if (!subscriptions || !subscriptions.length) {
          // No subscriptions
          return
        }

        const saasPlans = subscriptions.filter(s => s.saasProduct)

        if (!saasPlans || !saasPlans.length) {
          // No SaaS plans
          return
        }

        const saasPlan = saasPlans[0];

        const { title, saasProduct, subscription, subscriptionItem } = saasPlan

        const { amount, currency: planCurrency, interval } = subscriptionItem.plan

        this.subscriptionData.planDetails = {
          title,
          saasProduct,
          subscription,
          amount: `${amount / 100}`,
          symbol: currency[planCurrency.toUpperCase()]?.symbol || '$',
          billingInterval: interval,
        }
      } catch (error) {
        console.error('Error while fetching subscription details --> ', error)
        this.subscriptionData.error =
          error && error.msg ? error.msg : 'Error while fetching data'
      } finally {
        this.subscriptionData.loading = false
      }
    },
    // async fetchSubscriptionDetails(saasPlanId: string, stripePriceId: string) {
    //   try {
    //     if (!this.saasPlanId) {
    //       console.log('location not under any saas plan')
    //       return
    //     }

    //     this.subscriptionData.loading = true
    //     const { data: saasPlan } = await this.saasService.get(
    //       `/saas-config/${this.locationId}/saas-plan-details/${saasPlanId}?stripePriceId=${stripePriceId}`
    //     )

    //     console.log('saas plan details --> ', saasPlan)

    //     const { title, stripePrice } = saasPlan

    //     const { amount, symbol, billingInterval } = stripePrice

    //     this.subscriptionData.planDetails = {
    //       title,
    //       amount: `${amount / 100}`,
    //       symbol,
    //       billingInterval,
    //     }
    //   } catch (error) {
    //     console.error('Error while fetching subscription details --> ', error)
    //     this.subscriptionData.error =
    //       error && error.msg ? error.msg : 'Error while fetching data'
    //   } finally {
    //     this.subscriptionData.loading = false
    //   }
    // },
  },
}
