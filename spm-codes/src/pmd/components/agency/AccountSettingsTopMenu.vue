<template>
	<div class="hl_settings--header">
		<div class="container-fluid">
			<h2>Settings</h2>
			<ul class="hl_settings--nav">
				<router-link :to="{name: 'agency_profile_settings'}" tag="li" active-class="active" exact>
					<a href="javascript:void(0);">User Settings</a>
				</router-link>
				<router-link :to="{name: 'agency_company_settings'}" tag="li" active-class="active" exact>
					<a href="javascript:void(0);">Agency Settings</a>
				</router-link>
				<router-link :to="{name: 'account_team_management'}" tag="li" active-class="active" exact>
					<a href="javascript:void(0);">Agency Team Management</a>
				</router-link>
				<router-link :to="{name: 'account_billing'}" tag="li" active-class="active">
					<a href="javascript:void(0);">Agency Billing</a>
				</router-link>
				<router-link
					:to="{name: 'snapshot'}"
					tag="li"
					:class="{'active': subIsActive('/snapshot/')}"
					exact
				>
					<a href="javascript:void(0);">Account Snapshot</a>
				</router-link>
				<!-- <router-link :to="{name: 'account_integrations'}" tag="li" active-class="active" exact>
					<a href="javascript:void(0);">Integrations</a>
				</router-link>-->
				<router-link :to="{name: 'twilio_settings'}" tag="li" active-class="active" exact v-if="!company.inPureIsvMode">
					<a href="javascript:void(0);">Twilio</a>
				</router-link>
				<!-- <router-link :to="{name: 'sendgrid_settings'}" tag="li" active-class="active" exact>
					<a href="javascript:void(0);">SendGrid</a>
				</router-link>-->
				<router-link :to="{name: 'mailgun_settings'}" tag="li" active-class="active" exact>
					<a href="javascript:void(0);">MailGun</a>
				</router-link>
				<router-link :to="{name: 'smtp_service_settings'}" tag="li" active-class="active" exact>
					<a href="javascript:void(0);">SMTP Service</a>
				</router-link>
        <router-link :to="{name: 'stripe_settings'}" tag="li" active-class="active" exact>
					<a href="javascript:void(0);">Payments</a>
				</router-link>
				<router-link :to="{name: 'affiliate_settings'}" tag="li" active-class="active" exact>
					<a href="javascript:void(0);">Affiliate Settings</a>
				</router-link>
				<router-link :to="{name: 'custom_menu_link_settings'}" tag="li" active-class="active" exact>
					<a href="javascript:void(0);">Custom Menu Link</a>
				</router-link>
				<!-- <router-link :to="{name: 'whitelabel_settings'}" tag="li" active-class="active" exact>
					<a href="javascript:void(0);">White Label Settings</a>
				</router-link>-->

        <router-link v-if="isAgencyAdmin" :to="{name: 'api_key'}" tag="li" active-class="active" exact>
					<a href="javascript:void(0);">API Keys</a>
				</router-link>
				<router-link :to="{name: 'compliance'}" tag="li" active-class="active" exact>
					<a href="javascript:void(0);">Compliance</a>
				</router-link>
				<router-link v-if="!hideLaunchpadSettings" :to="{name: 'launchpad_settings'}" tag="li" active-class="active" exact>
					<a href="javascript:void(0);">Launchpad Settings</a>
				</router-link>
			</ul>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
import config from '@/config';
import { mapState } from 'vuex'
import { Company, User } from '@/models'
import moment from 'moment-timezone'
import { UserState, CompanyState } from '@/store/state_models'

export default Vue.extend({
	data() {
		return {
			currentLocationId: '',
		};
	},
	watch: {
		'$route.params.location_id': function (id) {
			this.currentLocationId = id;
		},
	},
	computed: {
		isProduction(): boolean {
			return config.mode === 'production'
		},
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
		...mapState('company', {
			company: (s: CompanyState) => {
				return s.company ? new Company(s.company) : undefined
			},
		}),
		hideLaunchpadSettings(): boolean {
			if (moment('2021-07-21').isBefore(this.company?.dateAdded.format('YYYY-MM-DD'))) {
				return true
			}
			return false
		},
    isAgencyAdmin(): boolean {
      return this.user && this.user.type === User.TYPE_AGENCY && this.user.role === User.ROLE_ADMIN
    },
	},
	created() {
		this.currentLocationId = this.$router.currentRoute.params.location_id;
	},
	methods: {
		subIsActive(input: string): boolean {
			const paths: string[] = Array.isArray(input) ? input : [input];

			return paths.some(path => {
				return this.$route.path.indexOf(path) !== -1;
			});
		}
	}
});
</script>
