<template>
  <div>
    <section class="hl_wrapper">
      <section class="hl_wrapper--inner website-template-categories">
        <div class="container-fluid">
          <div class="hl_controls">
            <div class="hl_controls--left flex">
              <h2>
                <a
                  @click="$router.push({ name: inWebsitesTemplateCategoryPage ? 'websites_funnels' : 'all_funnels' })"
                  href="javascript:void(0);"
                  class="back"
                >
                  <i class="icon icon-arrow-left-2"></i>
                </a>
                Choose a relevant category
              </h2>
            </div>
          </div>
          <div v-if="showWebsitesVideo" class="row hl_help-article">
            <div class="col-sm-12 col-md-12 col-lg-12 intro-video">
              <iframe
                width="560"
                height="315"
                src="https://www.youtube.com/embed/w45BlAqIwkc"
                frameborder="0"
                allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen
              ></iframe>
            </div>
          </div>
          <div class="website-template-category-list row">
            <router-link
              :to="{
                name: inWebsitesTemplateCategoryPage ? 'website_templates_category_detail' : 'funnel_templates_category_detail',
                params: { id: category.id }
              }"
              tag="div"
              class="category-container col-xs-12 col-sm-12 col-md-4 col-lg-3"
              v-for="category in websiteTemplateCategories"
              :key="category.id"
            >
              <website-template-category-card :category="category" />
            </router-link>
          </div>
        </div>
      </section>
    </section>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import WebsiteTemplateCategoryCard from '@/pmd/components/funnels/WebsiteTemplateCategoryCard.vue'
import { WebsiteTemplateCategory, User } from '../../../models'
import { mapState } from 'vuex'
import { UserState } from '../../../store/state_models'
import { WebsiteTemplateCategoryType } from '../../../models/website_template_category'

export default Vue.extend({
  components: {
    WebsiteTemplateCategoryCard
  },
  data() {
    return {
      websiteTemplateCategories: [] as WebsiteTemplateCategory[]
    }
  },
  mounted() {
    this.fetchTemplateCategories()
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    inWebsitesTemplateCategoryPage(): boolean {
      return this.templateCategoryType === WebsiteTemplateCategoryType.Websites
    },
    templateCategoryType(): string {
      const routePath = this.$route.path
      const split = routePath.split('/')
      const lastRoute = split[split.length - 1]
      switch (lastRoute) {
        case 'funnel-templates': {
          return WebsiteTemplateCategoryType.Funnels
        }
        case 'website-templates': {
          return WebsiteTemplateCategoryType.Websites
        }
        default: {
          return WebsiteTemplateCategoryType.Websites
        }
      }
    },
    showWebsitesVideo(): boolean {
      return this.user.type == 'agency' && this.inWebsitesTemplateCategoryPage
    }
  },
  methods: {
    async fetchTemplateCategories() {
      const allTemplateCategories = await WebsiteTemplateCategory.getAllCategories()
      if (allTemplateCategories) {
        this.websiteTemplateCategories = allTemplateCategories
          .filter((c: WebsiteTemplateCategory) => {
            const categoryType = c.type || WebsiteTemplateCategoryType.Websites
            return categoryType === this.templateCategoryType
          })
          .sort((catA, catB) => catA.name.localeCompare(catB.name))
      }
    }
  }
})
</script>
<style scoped>
.intro-video {
  text-align: center;
  padding-bottom: 12px;
}
</style>
