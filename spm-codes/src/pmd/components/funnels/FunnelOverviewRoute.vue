<template>
  <section>
    <ul class="nav nav-tabs hl_funnels--nav" role="tablist">
      <template v-for="route in routeData">
        <li class="nav-item">
          <router-link
            :to="route"
            class="nav-link"
            active-class="active"
            id="steps-link"
            data-toggle="tab"
            href="#steps-tab"
            role="tab"
            aria-controls="steps-tab"
            aria-selected="true"
          >{{route.text}}</router-link>
        </li>
      </template>
    </ul>
  </section>
</template>
<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    routeData: {
      type: Array,
      default: []
    }
  }
})
</script>
