<template>
  <div class="req-card__no-user__wrap --left">
    <h6>
      <!-- To convert this account to SaaS, you need to add a location email first -->
      Account's Email address is missing, please add details first.
    </h6>
    <br />
    <div class="row">
      <div class="form-group col-md-12">
        <label>Email address</label>
        <input
          type="text"
          class="form-control msgsndr3"
          placeholder="Email address"
          v-model="email"
          v-validate="'required|email'"
          name="msgsndr31"
          data-lpignore="true"
          autocomplete="msgsndr31"
          data-vv-as="Email"
          @keyup="setEdited"
        />
        <span v-show="errors.has('msgsndr31')" class="error">{{
          errors.first('msgsndr31')
        }}</span>
      </div>
      <div class="form-group col-md-12">
        <label> Phone number </label>
        <PhoneNumber
          class="form-control msgsndr1"
          placeholder="Phone number"
          v-model="phone"
          v-validate="'phone'"
          name="msgsndr11"
          autocomplete="msgsndr11"
          data-vv-as="phone"
          :currentLocationId="location.id"
          @keyup="setEdited"
        />
        <span v-show="errors.has('msgsndr11')" class="error">{{
          errors.first('msgsndr11')
        }}</span>
      </div>
    </div>
    <div style="align-self: flex-end; margin-right: 16px;">
      <button
        v-if="!loading"
        :disabled="!edited"
        type="button"
        class="btn btn-blue"
        @click.prevent="save"
      >
        Save
      </button>
      <div v-show="loading">
        <moon-loader :loading="loading" color="#188bf6" size="30px" />
      </div>
    </div>
    <!-- <div class="btn create-user-btn" @click="$emit('createUser')">
      Create New User
    </div> -->
  </div>
</template>

<script>
import PhoneNumber from '@/pmd/components/util/PhoneNumber.vue'
import { Location } from '@/models'

export default {
  props: ['location'],
  data() {
    return {
      email: '',
      phone: '',
      loading: false,
      edited: false,
    }
  },
  components: {
    PhoneNumber,
  },
  created() {
    if(this.location) {
      this.email = (this.location.prospectInfo && this.location.prospectInfo.email) || '';
      this.phone = this.location.phone || '';
    }
  },
  methods: {
    setEdited() {
      this.edited = true
    },
    async save() {
      if (this.loading) return

      await this.$validator.validateAll()
      if (this.errors.any()) {
        return Promise.resolve(true)
      }
      this.loading = true
      if (this.location) {
        // this.location.prospectInfo.email = this.email
        // this.location.phone = this.phone
        // await this.location.save()
        try {
          await Location.collectionRef().doc(this.location.id).update({
            'prospect_info.email': this.email,
            'phone': this.phone,
          })
          this.loading = false
          this.edited = false
          this.$emit('saved')
        } catch (err) {
          this.loading = false
          console.error(err)
        }
      }
    },
  },
}
</script>

<style lang="scss">
.req-card__no-user__wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 400px;
  &.--left {
    width: 440px;
    margin: auto;
    // align-items: flex-start;
    // justify-content: flex-start;
    // h6, .row {
    //   width: 100%;
    // }

  }
}
.create-user-btn {
  background-color: #178af5 !important;
  width: 300px;
}
</style>
