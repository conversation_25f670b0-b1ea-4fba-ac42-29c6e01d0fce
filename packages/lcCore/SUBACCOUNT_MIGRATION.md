# SubAccount Model Migration Guide

## Overview

This document provides a comprehensive guide for migrating from SPM Location patterns to lcCore SubAccount patterns. The SubAccount model is a complete reimplementation of the SPM Location model using modern Redux Toolkit + RTK Query architecture.

## Why SubAccount Instead of Location?

The model was renamed from "Location" to "SubAccount" to better align with GHL (GoHighLevel) terminology and provide clearer context in the dashboard environment. This naming convention better reflects the business relationship and hierarchy.

## Architecture Comparison

### SPM Location (Vuex Pattern)
```javascript
// SPM Location Store Structure
{
  state: {
    locations: [],
    activeLocations: [],
    notificationLocations: [],
    currentLocation: null
  },
  mutations: {
    add, clearAll, addActiveLocation, 
    setNotificationLocation, setCurrentLocation
  },
  actions: {
    resetCurrentLocation, sync operations
  }
}
```

### lcCore SubAccount (RTK Query + Redux Toolkit)
```typescript
// lcCore SubAccount Store Structure
{
  state: {
    subAccounts: ISubAccount[],
    activeSubAccounts: ISubAccount[],
    notificationSubAccounts: ISubAccount[],
    currentSubAccount: ISubAccount | null,
    // Enhanced with additional state
    isLoading, isUpdating, isCreating, isDeleting,
    searchQuery, statusFilter, selectedSubAccountIds,
    subscriptions, sorting
  },
  reducers: {
    // All SPM mutations + enhanced functionality
    setSubAccounts, addOrUpdateSubAccount, removeSubAccount,
    setCurrentSubAccount, setActiveSubAccounts,
    // Plus modern state management
    setSearchQuery, setStatusFilter, setSorting
  },
  thunks: {
    // Business logic operations
    getAllSubAccounts, createSubAccount, updateSubAccount,
    deleteSubAccount, searchSubAccounts
  }
}
```

## Migration Mapping

### 1. Types and Interfaces

#### SPM Location Model Properties
```javascript
// SPM Location (JavaScript)
{
  id, name, nameLowerCase, address, city, state, 
  timezone, phone, email, website, status, companyId,
  settings: { email, sms, review, social, widget, ... },
  productStatus: { reviews, listings, conversations, social },
  // ... other properties
}
```

#### lcCore SubAccount Types
```typescript
// lcCore SubAccount (TypeScript)
interface ISubAccount extends IEntity {
  // All SPM properties + enhanced typing
  id: string;
  name: string;
  nameLowerCase: string;
  address?: string;
  city?: string;
  state?: string;
  timezone: string;
  phone?: string;
  email?: string;
  website?: string;
  status: SubAccountStatus;
  companyId: string;
  settings: ISubAccountSettings;
  productStatus: IProductStatus;
  // Enhanced with additional properties
  deleted: boolean;
  apiKey?: string;
  // ... comprehensive type safety
}
```

### 2. Store Operations

#### SPM Location Store Usage
```javascript
// SPM Vuex patterns
this.$store.commit('location/add', location);
this.$store.commit('location/setCurrentLocation', location);
this.$store.dispatch('location/resetCurrentLocation');

// Getters
this.$store.getters['location/currentLocation'];
this.$store.getters['location/locations'];
```

#### lcCore SubAccount Store Usage
```typescript
// lcCore Redux Toolkit patterns
import { useDispatch, useSelector } from 'react-redux';
import { 
  setCurrentSubAccount, 
  selectCurrentSubAccount,
  selectSubAccounts,
  createSubAccount 
} from '@gd/core';

// In components
const dispatch = useDispatch();
const currentSubAccount = useSelector(selectCurrentSubAccount);
const subAccounts = useSelector(selectSubAccounts);

// Actions
dispatch(setCurrentSubAccount(subAccount));
dispatch(createSubAccount(subAccountData));
```

### 3. API Operations

#### SPM Location API Patterns
```javascript
// SPM manual API calls
const location = new Location(data);
await location.save();
await location.delete();

// Static methods
const locations = await Location.getByCompanyId(companyId);
const location = await Location.getById(id);
```

#### lcCore SubAccount RTK Query
```typescript
// lcCore RTK Query hooks
import { 
  useGetSubAccountsQuery,
  useCreateSubAccountMutation,
  useUpdateSubAccountMutation,
  useDeleteSubAccountMutation 
} from '@gd/core';

// In components
const { data: subAccounts, isLoading } = useGetSubAccountsQuery({ companyId });
const [createSubAccount] = useCreateSubAccountMutation();
const [updateSubAccount] = useUpdateSubAccountMutation();
const [deleteSubAccount] = useDeleteSubAccountMutation();

// Usage
await createSubAccount(subAccountData);
await updateSubAccount({ id, ...updateData });
```

### 4. Computed Properties and Utilities

#### SPM Location Computed Properties
```javascript
// SPM Location getters (in model)
get formatedName() {
  return `${this.name} -- ${this.address}, ${this.city}, ${this.state}`;
}

get isActive() {
  return this.status === 'account' && !this.deleted;
}
```

#### lcCore SubAccount Utilities
```typescript
// lcCore utility classes
import { SubAccountComputedProperties } from '@gd/core';

// Usage
const formattedName = SubAccountComputedProperties.getFormattedName(subAccount);
const isActive = SubAccountComputedProperties.isActive(subAccount);
const fullAddress = SubAccountComputedProperties.getFullAddress(subAccount);

// With selectors (memoized)
const subAccountWithComputed = useSelector(selectCurrentSubAccountWithComputed);
// Returns subAccount with all computed properties attached
```

### 5. Validation

#### SPM Location Validation
```javascript
// SPM manual validation
if (!location.name) {
  throw new Error('Name is required');
}
if (location.email && !isValidEmail(location.email)) {
  throw new Error('Invalid email');
}
```

#### lcCore SubAccount Validation
```typescript
// lcCore validation utilities
import { SubAccountValidationUtils } from '@gd/core';

const validation = SubAccountValidationUtils.validateCreateSubAccount(data);
if (!validation.isValid) {
  console.error('Validation errors:', validation.errors);
}

// Automatic validation in thunks
dispatch(createSubAccount(data)); // Validates automatically
```

## Usage Examples

### 1. Creating a SubAccount
```typescript
import { useCreateSubAccountMutation } from '@gd/core';

const [createSubAccount, { isLoading }] = useCreateSubAccountMutation();

const handleCreate = async (data: ICreateSubAccountPayload) => {
  try {
    const newSubAccount = await createSubAccount(data).unwrap();
    console.log('Created:', newSubAccount);
  } catch (error) {
    console.error('Failed to create:', error);
  }
};
```

### 2. Listing SubAccounts with Search
```typescript
import { 
  useGetSubAccountsQuery,
  useSelector,
  selectFilteredSubAccounts,
  setSearchQuery 
} from '@gd/core';

const SubAccountList = ({ companyId }: { companyId: string }) => {
  const dispatch = useDispatch();
  const { data, isLoading } = useGetSubAccountsQuery({ companyId });
  const filteredSubAccounts = useSelector(selectFilteredSubAccounts);
  
  const handleSearch = (query: string) => {
    dispatch(setSearchQuery(query));
  };
  
  return (
    <div>
      <input onChange={(e) => handleSearch(e.target.value)} />
      {filteredSubAccounts.map(subAccount => (
        <div key={subAccount.id}>{subAccount.name}</div>
      ))}
    </div>
  );
};
```

### 3. SubAccount Management with State
```typescript
import { 
  useSelector,
  useDispatch,
  selectCurrentSubAccount,
  selectSubAccountOperationInProgress,
  setCurrentSubAccount,
  updateSubAccount 
} from '@gd/core';

const SubAccountManager = () => {
  const dispatch = useDispatch();
  const currentSubAccount = useSelector(selectCurrentSubAccount);
  const isOperationInProgress = useSelector(selectSubAccountOperationInProgress);
  
  const handleSelect = (subAccount: ISubAccount) => {
    dispatch(setCurrentSubAccount(subAccount));
  };
  
  const handleUpdate = async (updateData: IUpdateSubAccountPayload) => {
    if (currentSubAccount) {
      await dispatch(updateSubAccount({ 
        subAccountId: currentSubAccount.id, 
        ...updateData 
      }));
    }
  };
  
  return (
    <div>
      {currentSubAccount && (
        <div>
          <h2>{currentSubAccount.name}</h2>
          <p>Status: {currentSubAccount.status}</p>
          {isOperationInProgress && <div>Loading...</div>}
        </div>
      )}
    </div>
  );
};
```

## Key Benefits of Migration

1. **Type Safety**: Complete TypeScript coverage with compile-time error checking
2. **Performance**: Memoized selectors and optimized re-renders
3. **Developer Experience**: Auto-completion, IntelliSense, and better debugging
4. **Modern Patterns**: RTK Query for caching, background updates, and optimistic updates
5. **Maintainability**: Clear separation of concerns and standardized patterns
6. **Testing**: Better testability with pure functions and predictable state updates

## Migration Checklist

- [ ] Replace SPM Location imports with lcCore SubAccount imports
- [ ] Update Vuex store.commit calls to Redux dispatch calls
- [ ] Replace manual API calls with RTK Query hooks
- [ ] Update computed property access to use utility classes or selectors
- [ ] Replace manual validation with validation utilities
- [ ] Update component state management to use Redux selectors
- [ ] Test all CRUD operations with new API
- [ ] Verify real-time updates and optimistic updates work correctly

## Support

For questions or issues during migration, refer to:
- lcCore package documentation
- TypeScript interfaces in `packages/lcCore/src/types/subAccount.ts`
- Example usage in selectors and thunks
- RTK Query documentation for advanced patterns
