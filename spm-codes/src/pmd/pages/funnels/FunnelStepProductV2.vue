<template>
  <div class="bg-white rounded-sm">
    <div class="products-container py-4 my-4 px-3 mx-0 max-w-2xl ">
      <div class="pb-2">
        <span
          class="text-curious-blue-500 hover:text-curious-blue-400 cursor-pointer"
          @click="onClickCancel"
        >
          <i class="fas fa-arrow-left"></i> Back to products
        </span>
      </div>
      <div>
        <h3 class="text-lg leading-6 font-medium text-gray-900">Products</h3>
        <p class="mt-1 text-sm text-gray-500">
          This information will be displayed publicly so be careful what you
          share.
        </p>
      </div>
      <div class="space-y-4">
        <UITextInputGroup
          v-model="productName"
          label="Product name"
          name="productName"
          v-validate="'required'"
          :error="errors.has('productName')"
          :errorMsg="errors.first('productName')"
        />
        <UITextInputGroup
          v-model="priceDisaplayOverride"
          name="displayOverride"
          label="Price display override"
          description="(Optional) Customize the display of the price on the order form."
        />
        <UIMultiSelect
          label="Product"
          v-validate="'required'"
          v-model="selectedProduct"
          :options="products"
          :multiple="false"
          :allowEmpty="false"
          deselectLabel="Can't remove this value"
          :closeOnSelect="true"
          placeholder="Search for products"
          name="product"
          :error="errors.has('product')"
          :errorMsg="errors.first('product')"
        />
        <div class="mt-2 text-xs text-gray-500">
          Create a new products by clicking
          <router-link :to="{ name: 'product-list' }"> Here. </router-link>
        </div>
        <div v-if="selectedProduct">
          <UIMultiSelect
            label="Price"
            v-validate="'required'"
            v-model="selectedPrice"
            :options="prices"
            :multiple="false"
            :allowEmpty="false"
            deselectLabel="Can't remove this value"
            :closeOnSelect="true"
            placeholder="Search for price"
            name="price"
            :error="errors.has('price')"
            :errorMsg="errors.first('price')"
          />
        </div>
        <div
          class="mt-2 text-xs text-gray-500"
          v-if="selectedProduct && selectedProduct.value"
        >
          Create a new price for
          <span class="font-semibold">{{ selectedProduct.label }}</span> by
          clicking
          <router-link
            :to="{
              name: 'product-details',
              params: { product_id: selectedProduct.value },
            }"
          >
            Here.
          </router-link>
        </div>

        <div
          @click="enableAdditionalOptions = !enableAdditionalOptions"
          class="text-curious-blue-500"
        >
          <span class="cursor-pointer">
            Additional Options
            <i class="fas fa-chevron-down" v-show="enableAdditionalOptions"></i>
            <i
              class="fas fa-chevron-right"
              v-show="!enableAdditionalOptions"
            ></i>
          </span>
        </div>
        <div v-if="enableAdditionalOptions" class="space-y-2">
          <div>
            <UICheckbox v-model="bumpProduct" />
            <UITextLabel>Bump product</UITextLabel>
          </div>
          <p class="mt-2 text-xs text-gray-500">
            Should this product be the bump on the order page if present?
          </p>
          <UITextInputGroup
            v-if="enableAuthorizAmount"
            class="max-w-xs mt-2"
            v-model="authorizeAmount"
            label="Authorization price"
            name="amount"
            placeholder="amount"
            v-validate="'decimal:3'"
            :error="errors.has('authorizationAmount')"
            :errorMsg="errors.first('authorizationAmount')"
          />
        </div>

        <div class="space-x-2 d-flex">
          <UIButton use="outline" @click="onClickCancel">Cancel</UIButton>
          <UIButton
            use="primary"
            @click="
              funnelProduct ? updateFunnelProducts() : saveFunnelProducts()
            "
            :loading="saving"
            >Save</UIButton
          >
        </div>
        <div v-if="errorMessage">
          <p class="mt-2 text-xs text-red-500">
            {{errorMessage}}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { FunnelsServices, ProductServices } from '@/services'
import {
  defineComponent,
  ref,
  onMounted,
  computed,
  watch,
} from '@vue/composition-api'

export default defineComponent({
  inject: ['$validator'],
  setup(props, ctx) {
    const route = ctx.root.$route
    const router = ctx.root.$router
    const productName = ref('')
    const priceDisaplayOverride = ref('')
    const products = ref([])
    const selectedProduct = ref('')
    const selectedPrice = ref('')
    const authorizeAmount = ref('')

    const productsRawData = ref()
    const enableAdditionalOptions = ref(false)

    const bumpProduct = ref(false)
    const saving = ref(false)
    const errorMessage = ref('')
    onMounted(() => {
      loadProducts()
    })

    const funnelProduct = computed(() => route.params.funnel_product_id)

    const prices = computed(() => {
      if (!selectedProduct && productsRawData.value.length) return []
      const product = productsRawData.value.find(
        ({ _id }: any) => _id === selectedProduct.value?.value
      )
      if (!product) return []
      return product.prices.map((price: any) => {
        return {
          label: price.name,
          value: price._id,
        }
      })
    })

    const enableAuthorizAmount = computed(() => {
      const product = productsRawData.value.find(
        (product: any) => product._id === selectedProduct.value?.value
      )
      if (product) {
        const price = product.prices.find(
          (price: any) => price._id === selectedPrice.value.value
        )
        if (!price) return false
        return price.type === 'recurring'
      }
      return false
    })

    watch(selectedPrice, (val: any) => {
      if (val?.value) {
        const price = prices.value.find(({ _id }: any) => _id === val.value)

        if (price && !productName.value) productName.value = val.label
        if (price && !priceDisaplayOverride.value) productName.value = val.label
      }
    })

    async function loadProducts() {
      const { data } = await ProductServices.listProducts({
        locationId: route.params.location_id,
        limit: 1000,
      })
      productsRawData.value = data.products
      products.value = productsRawData.value.map((product: any) => {
        return {
          label: product.name,
          value: product._id,
        }
      })

      if (route.params.funnel_product_id) {
        loadFunnelProduct()
      }
    }

    async function saveFunnelProducts() {
      const isValidated = await ctx.parent.$validator.validateAll()
      if (isValidated) {
        try {
          errorMessage.value = ''
          saving.value = true
          await FunnelsServices.createFunnelProducts({
            locationId: route.params.location_id,
            funnel: route.params.funnel_id,
            step: route.params.step_id,
            name: productName.value,
            displayText: priceDisaplayOverride.value,
            product: selectedProduct.value.value,
            price: selectedPrice.value.value,
            bumpProduct: bumpProduct.value,
            authorizeAmount: parseFloat(authorizeAmount.value) || 0,
          })
          saving.value = false
          router.push({
            name: 'funnel_step_products',
            params: { step_id: route.params.step_id },
          })
        } catch (error) {
          saving.value = false
          console.error(error)
          errorMessage.value = 'Sorry, something went wrong with this request. Please try again'

        }
      }
    }

    async function updateFunnelProducts() {
      const isValidated = await ctx.parent.$validator.validateAll()
      if (isValidated) {
        try {
          saving.value = true
          errorMessage.value = ''
          await FunnelsServices.updateFunnelProducts(
            route.params.funnel_product_id,
            {
              name: productName.value,
              displayText: priceDisaplayOverride.value,
              product: selectedProduct.value.value,
              price: selectedPrice.value.value,
              bumpProduct: bumpProduct.value,
               authorizeAmount: parseFloat(authorizeAmount.value) || 0,
            }
          )
          saving.value = false
          router.push({
            name: 'funnel_step_products',
            params: { step_id: route.params.step_id },
          })
        } catch (error) {
          saving.value = false
          console.error(error)
          errorMessage.value = 'Sorry, something went wrong with this request. Please try again'
        }
      }
    }

    async function loadFunnelProduct() {
      try {
        const { data } = await FunnelsServices.findByFunnelProductId(
          route.params.funnel_product_id
        )
        bumpProduct.value = data.bumpProduct
        productName.value = data.name
        priceDisaplayOverride.value = data.displayText
        authorizeAmount.value = data.authorizeAmount
        selectedProduct.value = products.value.find(
          (p: any) => p.value === data.product
        )
        selectedPrice.value = prices.value.find(
          (price: any) => price.value === data.price
        )
        // enable additional options when bump option is selected
        if(bumpProduct.value) enableAdditionalOptions.value = true
      } catch (error) {
        console.log(error)
      }
    }

    function onClickCancel() {
      router.go(-1)
    }

    return {
      productName,
      priceDisaplayOverride,
      prices,
      products,
      selectedProduct,
      selectedPrice,
      enableAdditionalOptions,
      saveFunnelProducts,
      bumpProduct,
      saving,
      updateFunnelProducts,
      funnelProduct,
      onClickCancel,
      authorizeAmount,
      enableAuthorizAmount,
      errorMessage
    }
  },
})
</script>

<style scoped></style>
