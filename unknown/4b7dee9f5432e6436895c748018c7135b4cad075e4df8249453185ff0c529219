<template>
  <div
    class="tab-pane fade show active"
    id="tab2"
    role="tabpanel"
    aria-labelledby="tab2-tab"
    v-if="filterCalendars.length > 0"
  >
    <div v-if="initializing" class="text-center p-4">
      Loading . . .
    </div>
    <div v-else>
      <div class="form-group">
        <label>Calendar</label>
        <select
          class="selectpicker"
          v-model="calendarId"
          name="calendar_service"
          @change="resetTeamMemberAssignmentType"
        >
          <option value="undefined">Select calendar</option>
          <option
            v-for="calendar in rightFilterCalendars"
            :key="calendar.id"
            :value="calendar.id"
            >{{ calendar.name }}</option
          >
        </select>
      </div>
      <div class="row" v-if="currentCalendar && currentCalendar.providerId">
        <div class="col-md-4">
          <input
            type="radio"
            name="round_robin"
            :value="userAssignmentType.RoundRobin"
            id="round_robin"
            v-model="userAssignmentThrough"
            @change="resetTeamMemberSelection"
          />
          <label for="round_robin" class="pl-2">
            Round Robin
          </label>
        </div>
        <div class="col-md-8">
          <input
            type="radio"
            name="specific_member"
            :value="userAssignmentType.PickUserManually"
            id="specific_member"
            v-model="userAssignmentThrough"
            @change="resetTeamMemberSelection"
          />
          <label for="specific_member" class="pl-2">
            Pick Team Member
          </label>
          <div
            v-show="
              userAssignmentThrough === userAssignmentType.PickUserManually
            "
          >
            <select
              class="selectpicker"
              v-model="serviceMemberId"
              name="service_team_members"
            >
              <option :value="undefined" disabled>Select Team Member</option>
              <option
                v-for="teamMember in serviceTeamMembers"
                :key="teamMember.user_id"
                :value="teamMember.user_id"
                >{{ teamMember.name }}</option
              >
            </select>
          </div>
          <span
            v-show="
              userAssignmentThrough === userAssignmentType.PickUserManually &&
                !serviceMemberId &&
                showErrorMessage
            "
            class="text-danger"
            >Team Member is required</span
          >
        </div>
      </div>
      <appointment-slot
        v-if="calendarId && timezone"
        :calendarId="calendarId"
        :assignedUserId="serviceMemberId"
        :timezone="timezone"
        :slotPicked.sync="slotPicked"
        :eventSlot="eventSlot"
        :fetchSlot="fetchSlotFlag"
      />
      <div class="row mt-3">
        <div class="col-6">
          <div class="form-group">
            <UITextLabel>Meeting Location 
                 <span
                    style="margin-left: 5px;"
                    class="input-group-addon"
                    v-b-tooltip.hover
                    title="Consider leaving it blank, should you choose to use the pre-configured Meeting Location"
                  >
                    <i class="fas fa-question-circle"></i>
                  </span>   

            </UITextLabel>
            <UITextInputGroup
              type="text"
              class="msgsndr4"
              placeholder="Meeting Location"
              v-model="address"
              name="msgsndr4"
              data-lpignore="true"
              autocomplete="msgsndr4"
            />
          </div>
        </div>

        <div class="col-6">
          <div class="form-group">
            <UITextLabel>Appointment Title
                <span
                    style="margin-left: 5px;"
                    class="input-group-addon"
                    v-b-tooltip.hover
                    title="Consider leaving it blank, should you choose to use the pre-configured Appointment Title"
                  >
                    <i class="fas fa-question-circle"></i>
                  </span> 

            </UITextLabel>
            <UITextInputGroup
              type="text"
              class="msgsndr9"
              placeholder="Appointment Title"
              v-model="appointmentTitle"
              name="msgsndr9"
              data-lpignore="true"
              autocomplete="msgsndr9"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="tab-pane fade show active"
    id="tab2"
    role="tabpanel"
    aria-labelledby="tab2-tab"
    v-else
    style="text-align: center;"
  >
    <div v-if="!meta.contactId">
      Add/Select a contact to schedule appointments
    </div>
    <div v-else>Add a calendar to start booking appointments.</div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import moment from 'moment-timezone'
import {
  Opportunity,
  AppointmentRequest,
  User,
  UserCalendar,
  Contact,
  Team,
  Calendar,
  Location,
  CalendarEvent,
  UserAssignmentType,
  EventStatus
} from '@/models'
import Datepicker from 'vuejs-datepicker'
import {
  AppoinmentEventStatus,
  AppointmentSource,
  AppointmentSourceChannel
} from '../../../models/calendar_event'
import AppointmentSlot from './../AppointmentSlot.vue'
import { UserState } from '../../store/state_models'
import { mapState } from 'vuex'

declare var $: any

export default Vue.extend({
  props: ['meta', 'bus', 'opportunity'],
  components: { Datepicker, AppointmentSlot },
  data() {
    return {
      calendarEvent: undefined as CalendarEvent | undefined,
      timezone: '',
      initStartTime: null,
      appointmentDate: undefined as Date | undefined,
      calendarId: undefined,
      serviceTeamMembers: {},
      serviceMemberId: undefined,
      showErrorMessage: false,
      userAssignmentType: {
        RoundRobin: UserAssignmentType.RoundRobin,
        PickUserManually: UserAssignmentType.PickUserManually
      },
      userAssignmentThrough: UserAssignmentType.RoundRobin,
      userwiseMasterCalendarIds: {},
      availableSlots: [] as moment.Moment[],
      events: [] as CalendarEvent[],
      slotPicked: undefined as '' | moment.Moment | undefined,
      eventSlot: '' as '' | moment.Moment,
      initializing: true,
      address: '',
      appointmentTitle: '',
      location: undefined as undefined | Location,
      authUser: '',
      fetchSlotFlag: undefined,
      contact: {} as Contact
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    locationTeamMembers() {
      return this.$store.state.users.users.map(u => new User(u))
    },
    vuexCalendarProviders() {
      return this.$store.getters['teams/calendarProviders']
    },
    calendarProviders() {
      return this.vuexCalendarProviders.map(s => new Team(lodash.cloneDeep(s)))
    },
    providerIdwiseProviderName() {
      const _providerIdwiseProviderName = {}
      this.calendarProviders.forEach(p => {
        _providerIdwiseProviderName[p.id] = p.calendarProviderName
      })
      return _providerIdwiseProviderName
    },
    vuexCalendars() {
      return this.$store.state.calendars.calendars
    },
    calendars() {
      const _calendars = lodash.orderBy(
        this.vuexCalendars.map(s => new Calendar(lodash.cloneDeep(s))),
        ['providerId'],
        ['desc']
      )
      _calendars.forEach(calendar => {
        calendar.name = this.providerIdwiseProviderName[calendar.providerId]
          ? `${calendar.name} (${
              this.providerIdwiseProviderName[calendar.providerId]
            })`
          : calendar.name
      })
      return _calendars.filter(x => x.isActive)
    },
    filterCalendars() {
      return this.calendars.filter(
        x =>
          this.user &&
          (!this.user.permissions.assigned_data_only ||
            this.user.role === 'admin' ||
            (this.user.permissions.assigned_data_only === true &&
              (this.user.userCalendar[this.meta.currentLocationId] === x.id ||
                (x.teamMembers &&
                  x.teamMembers.find(
                    x =>  x.user_id === this.user.id
                  )))))
      )
    },
    currentCalendar(): Calendar {
      return this.filterCalendars.find(x => x.id === this.calendarId) || {}
    },
    isV2Calendar() {
      return (
        !this.calendarEvent ||
        (this.calendarEvent && !this.calendarEvent.assignedUserId)
      )
    },
    isNewEvent() {
      return (
        !this.calendarEvent ||
        (this.calendarEvent && !this.calendarEvent.createdBy)
      )
    },
    filterCalendars_V2() {
      return this.filterCalendars.filter(x => !x.providerId)
    },
    filterCalendars_V3() {
      return this.filterCalendars.filter(x => !!x.providerId)
    },
    rightFilterCalendars() {
      return this.isNewEvent
        ? this.filterCalendars
        : this.isV2Calendar
        ? this.filterCalendars_V2
        : this.filterCalendars_V3
    }
  },
  async created() {
    await Promise.all([
      this.$store.dispatch('teams/syncAll', this.$route.params.location_id)
    ])
    this.errors.clear()
    // this.authUser = await this.$store.dispatch('auth/get')
    const data: () => object = <() => object>this.$options.data
    if (data) Object.assign(this.$data, data.apply(this))
    await this.setTimezone()
    if (this.meta.contactId) {
      //this.fetchData()
    }
  },
  watch: {
    '$route.params.location_id': async function(id) {
      await Promise.all([
        this.$store.dispatch('teams/syncAll', id)
      ])
    },
    async meta(meta) {
      if (!this.meta.currentLocationId) return
      this.errors.clear()
      const data: () => object = <() => object>this.$options.data
      if (data) Object.assign(this.$data, data.apply(this))
      await this.setTimezone()
      await this.fetchData()
    },
       slotPicked(newVal , oldVal) {
      if(oldVal instanceof moment) {
      this.address = "";
      }
    },
    appointmentDate() {
      if (!this.initializing) {
        this.slotPicked = ''
      }
    },
    calendarId(calendarId: string) {
      if (calendarId && calendarId !== 'undefined') {
        this.serviceTeamMembers = this.filterCalendars
          .find(x => x.id === calendarId)
          .teamMembers.filter(x => x.selected)
          .filter(x => this.locationTeamMembers.find(y => y.id === x.user_id))
          .map(x => ({
            user_id: x.user_id,
            name: this.locationTeamMembers.find(y => y.id === x.user_id).name
          }))
      }

      if (!this.initializing) {
        this.slotPicked = ''
        this.fetchSlotFlag = true
      }
    },
    serviceMemberId() {
      if (!this.initializing) {
        this.slotPicked = ''
        this.fetchSlotFlag = true
      }
    }
  },
  methods: {
    resetTeamMemberAssignmentType() {
      this.userAssignmentThrough = UserAssignmentType.RoundRobin
      this.resetTeamMemberSelection()
    },
    resetTeamMemberSelection() {
      this.serviceMemberId = undefined
      this.address = "";
    },
    async submit(contactId?: string) {
      this.showErrorMessage = false
      let _errorMessage = ''
      if (
        this.userAssignmentThrough ===
          this.userAssignmentType.PickUserManually &&
        !this.serviceMemberId
      ) {
        this.showErrorMessage = true
        _errorMessage = 'Team Member is required'
      }

      if (!this.calendarId || this.calendarId === 'undefined') {
        this.bus.$emit('complete')
        return
      }

      if (!this.slotPicked || (this.slotPicked && !this.slotPicked.isValid())) {
        _errorMessage = 'Appointment slot is not yet picked'
      }

      if (_errorMessage) {
        this.bus.$emit('cancel_saving', {
          tab: 'AppointmentComponent',
          msg: _errorMessage
        })
        return false
      }
      if (this.slotPicked) {
        const _calendar: Calendar = this.filterCalendars.find(
          x => x.id === this.calendarId
        )
        let _isNewAppointment: Boolean = false
        if (
          !this.calendarEvent ||
          !this.calendarEvent.startTime ||
          !this.calendarEvent.startTime.isSame(this.slotPicked) ||
          !this.calendarEvent.address ||
          this.calendarEvent.address !== this.address
        ) {
          if (!this.calendarEvent) {
            this.calendarEvent = new CalendarEvent()
            _isNewAppointment = true
            this.calendarEvent.locationId = this.meta.currentLocationId
            this.calendarEvent.contactId = this.meta.contactId
            if (!this.calendarEvent.contactId && contactId) {
              this.calendarEvent.contactId = contactId
            }
          }
          this.contact = await Contact.getById(this.calendarEvent.contactId)
          this.calendarEvent.startTime = this.slotPicked
          this.calendarEvent.endTime = this.calendarEvent.startTime
            .clone()
            .add(_calendar.slotDuration, 'm')

          /**
           * The below IIFE is just to bind related items in one place.
           * From wherever the event is created, the set of five props are mandatory.
           * 1). locationId
           * 2). calendarProviderId
           * 3). calendarId
           * 4). userId
           * 5). userCalendarId
           */
          await (async () => {
            this.calendarEvent.locationId = this.meta.currentLocationId

            this.calendarEvent.calendarId = this.calendarId
            if (_calendar.providerId) {
              this.calendarEvent.calendarProviderId = _calendar.providerId
              let _userId = this.calendarEvent.assignedUserId
              if (
                this.userAssignmentThrough ===
                this.userAssignmentType.PickUserManually
              ) {
                _userId = this.serviceMemberId
              } else {
                const rr_user_id = await this.calendarEvent.getRoundRobinUserId(
                  this.calendarId,
                  this.slotPicked,
                  this.timezone
                )
                if (rr_user_id) {
                  _userId = rr_user_id
                }
              }
              if(_userId) {
              this.calendarEvent.assignedUserId = _userId
              this.calendarEvent.userCalendarId = this.userwiseMasterCalendarIds[
                _userId
              ]

              const calendarProvider: Team = this.calendarProviders.find(
                x => x.id === _calendar.providerId
              )

              if (
                calendarProvider &&
                calendarProvider.shouldAssignContactToTeamMember &&
                this.contact &&
                (!this.contact.assignedTo ||
                  !calendarProvider.shouldSkipAssigningContactForExisting)
              ) {
                this.contact.assignedTo = _userId
                await this.contact.save()
              }

              }
            }
          })()

          this.calendarEvent.userAssignmentType = this.userAssignmentThrough
          this.calendarEvent.status = EventStatus.BOOKED
          this.calendarEvent.appoinmentStatus =
            AppoinmentEventStatus.STATUS_CONFIRMED

          if (!this.calendarEvent.userId && this.authUser) {
            this.calendarEvent.userId = this.authUser.userId
          }
          if (this.appointmentTitle) {
            this.calendarEvent.title = this.appointmentTitle
          } else {
            this.calendarEvent.title = await this.calendarEvent.getEventTitle(
              this.contact,
              this.location,
              _calendar
            )
          }

          this.calendarEvent.address = this.address



      // This works even for v3 as well
      if (!this.calendarEvent.address) {
      this.calendarEvent.address = await this.calendarEvent.getEventAddress(
        this.contact,
        this.location,
        _calendar
        // this.user - Get user from vuex
      )
      }

          this.calendarEvent.setCreatedByAndOrLastUpdatedBy(
            _isNewAppointment,
            this.user.id,
            AppointmentSource.OPPORTUNITIES_PAGE,
            AppointmentSourceChannel.WEB_APP
          )
          await this.calendarEvent.save()
        }
      }
      this.bus.$emit('complete', undefined, this.contact.assignedTo)
    },
    async setTimezone() {
      this.location = new Location(
        await this.$store.dispatch(
          'locations/getById',
          this.meta.currentLocationId
        )
      )
      this.timezone = await this.location.getTimeZone() // Get location timezone || agency timezone
      if(!this.timezone) {
        this.$uxMessage('warning', 'Please set timezone for your location to get proper free slots.')
        this.timezone = 'UTC'
      }
    },
    async fetchData() {
      this.initializing = true

      if (!this.user.id || !this.meta.currentLocationId) {
        return
      }

      if (this.meta.contactId && this.calendars) {
        const calendarEvent = await CalendarEvent.fetchFutureEventForContact(
          this.meta.currentLocationId,
          this.meta.contactId,
          moment()
        )
        // if (calendarEvent) {
        //   this.initializing = false
        //   return
        // }
      }

      if (!this.userwiseMasterCalendarIds[this.user.id]) {
        this.userwiseMasterCalendarIds = await UserCalendar.getUserwiseMasterCalendarIdsByLocation(
          this.user.id,
          this.meta.currentLocationId
        )
      }

      if (!this.meta.contactId) {
        this.appointmentDate = new Date()
        this.initializing = false
        this.fetchSlotFlag = true
        this.calendarId = 'undefined'
        return
      }
      this.fetchSlotFlag = false
      this.calendarEvent = await CalendarEvent.fetchFutureEventForContact(
        this.meta.currentLocationId,
        this.meta.contactId,
        moment()
      )
      if (this.calendarEvent) {
        this.userAssignmentThrough = this.calendarEvent.userAssignmentType
        if (
          this.userAssignmentThrough ===
          this.userAssignmentType.PickUserManually
        ) {
          this.serviceMemberId = this.calendarEvent.assignedUserId
        }
      }
      if (this.calendarEvent && this.calendarEvent.startTime) {
        const date = this.calendarEvent.startTime.tz(this.timezone)
        this.initStartTime = this.calendarEvent.startTime.clone().tz(this.timezone)
        this.appointmentDate = new Date(date.year(), date.month(), date.date())
      } else {
        this.appointmentDate = new Date()
      }
      if (this.calendarEvent && this.calendarEvent.address) {
        this.address = this.calendarEvent.address
      } else {
        // this.address = location.fullAddressLine;
      }

      if (this.calendarEvent && this.calendarEvent.title) {
        this.appointmentTitle = this.calendarEvent.title
      }

      this.calendarId =
        this.calendarEvent && this.calendarEvent.calendarId
          ? this.calendarEvent.calendarId
          : this.calendars.length > 0
          ? ''
          : ''
      this.slotPicked =
        this.calendarEvent && this.calendarEvent.startTime
          ? this.calendarEvent.startTime
              .tz(this.timezone)
              .format('YYYY-MM-DDTHH:mm:ss')
          : ''
      this.eventSlot =
        this.calendarEvent && this.calendarEvent.startTime
          ? this.calendarEvent.startTime
              .tz(this.timezone)
              .format('YYYY-MM-DDTHH:mm:ss')
          : ''
      this.initializing = false
    }
  },
  async mounted() {
    this.bus.$on('save_appointment', this.submit)
    if (!this.authUser) {
      this.authUser = await this.$store.dispatch('auth/get')
    }
    await this.fetchData()
  },
  beforeDestroy() {
    this.bus.$off('save_appointment')
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  }
})
</script>
