<template>
  <div class="card subscription-plan">
    <div class="shimmer__wrap" v-if="subscriptionData.loading">
      <upgrade-modal-shimmer />
    </div>
    <div class="plan-details" v-else-if="subscriptionData.planDetails && subscriptionData.planDetails.amount">
      <div class="title">{{ subscriptionData.planDetails.title }} {{subscriptionData.planDetails.title.toLowerCase().includes('plan')? '': 'Plan'}}</div>
      <div class="price">
        {{ subscriptionData.planDetails.symbol }}{{ subscriptionData.planDetails.amount }}/{{ subscriptionData.planDetails.billingInterval }}
      </div>
    </div>
    <div class="plan-details" v-else>
      <div class="title">No SaaS Plan Enabled</div>
      <div class="price btn" @click="$emit('attachPlan')">
        Add a Subscription
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import UpgradeModalShimmer from '@/pmd/components/common/shimmers/UpgradeModalShimmer.vue'

export default Vue.extend({
  props: {
    saasSettings: {
      type: Object,
      default: null,
    },
    locationId: {
      type: String,
    },
    subscriptionData: {
      type: Object,
    },
  },
  components: {
    UpgradeModalShimmer,
  },
})
</script>
<style scoped lang="scss">
/* .subscription-plan .card-body {
  padding-top: 16px;
  padding-bottom: 8px;
} */

.shimmer__wrap {
  display: flex;
  justify-content: center;
  align-items: center;

  padding: 16px;
}
.plan-details {
  /* display: flex; */
  padding: 24px 0;
  /* justify-content: center;
  align-items: center; */
  background: linear-gradient(90deg, #3182ce 0%, #887bdc 100%);
  border-radius: 4px;
  /* margin-bottom: 24px; */
}

.plan-details .title {
  font-weight: bold;
  font-size: 18px;
  line-height: 21px;
  text-align: center;
  color: #ffffff;
  margin-bottom: 16px;
  text-transform: capitalize;
}

.plan-details .price {
  background: rgba(236, 244, 255, 0.85);
  box-sizing: border-box;
  border-radius: 70px;
  padding: 4px 8px;
  font-size: 14px;
  line-height: 16px;
  text-align: center;
  color: #577fd4;
  margin: auto;
  width: fit-content;
  display: block;
  &.btn{
    padding: 8px 16px;
  }
}

.plan-details .price span {
  /* font-weight: bold; */
}
</style>
