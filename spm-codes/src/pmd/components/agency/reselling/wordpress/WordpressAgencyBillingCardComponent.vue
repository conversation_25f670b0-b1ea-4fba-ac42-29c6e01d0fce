<template>
  <div class="hl_billing--account">
    <div class="card --addons">
      <div class="card-body">
        <h3 class="agency-plan__heading">Wordpress</h3>
        <p>
          ${{ company.wordpressReseller.subscriber_count * 30
          }}<span>/month</span>
        </p>
        <span v-if="company.wordpressReseller.subscriber_count > 1"
          >{{ company.wordpressReseller.subscriber_count }} x $30
        </span>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: {
    company: {
      type: Object,
    },
  },
})
</script>
<style lang="scss" scoped>
span {
  font-size: 1rem;
  font-weight: 400;
  margin-top: 10px;
  display: inline-block;
}
</style>
