<template>
  <div>
    <section class="hl_wrapper">
      <section class="hl_wrapper--inner website-template">
        <div class="container-fluid">
          <div class="hl_controls">
            <div class="hl_controls--left">
              <p class="template-back">
                <a
                  @click="$router.push({ name: inWebsitesTemplateCategoryPage ? 'website_templates_category_detail' : 'funnel_templates_category_detail', params: { id: categoryId  } })"
                  href="javascript:void(0);"
                  class="back"
                >
                  <i class="icon icon-arrow-left-2"></i>
                </a>
                Back to Templates
              </p>
            </div>
            <!-- <div class="hl_controls--right">
              <button :disabled="disableTemplateLoad" :class="['btn', 'btn-success', loadingTemplate ? 'loading' : '']" @click="useTemplate()">
                <span v-if="loadingTemplate">
                  <i :style="{marginRight: '10px'}" class="fas fa-spinner"></i>&nbsp;Loading Template
                </span>
                <span v-else>
                  <i class="icon icon-ok"></i>&nbsp;Get this template
                </span>
              </button>
            </div>-->
          </div>
          <moon-loader
            v-if="fetchingTemplate"
            :loading="fetchingTemplate"
            color="#188bf6"
            size="30px"
          />
          <div class="content-container">
            <div class="card template-card" v-if="!fetchingTemplate && !errorInFetchingTemplate">
              <div class="card-body">
                <div class="content name">
                  <h2>{{ websiteTemplate.name }}</h2>
                </div>
                <div class="content image" v-if="websiteTemplate.image">
                  <img :src="websiteTemplate.image" alt />
                </div>
                <hr />
                <div class="content live-preview" v-if="websiteTemplate.livePreview">
                  <a
                    class="btn btn-link"
                    target="_blank"
                    :href="websiteTemplate.livePreview"
                  >Click here for a live preview</a>
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="get-template">
              <div class="option option-md disclaimer">
                <input
                  type="checkbox"
                  v-model="disclaimerChecked"
                  id="template-disclaimer"
                />
                <label for="template-disclaimer">
                  {{ disclaimerText }}
                </label>
              </div>
              <button :disabled="disableTemplateLoad" :class="['btn', 'btn-success', loadingTemplate ? 'loading' : '']" @click="useTemplate()">
                <span v-if="loadingTemplate">
                  <i :style="{marginRight: '10px'}" class="fas fa-spinner"></i>&nbsp;Loading Template
                </span>
                <span v-else>
                  <i class="icon icon-ok"></i>&nbsp;Get this template
                </span>
              </button>
          </div>-->
          <div class="content-container">
            <div class="card template-card" v-if="!fetchingTemplate && !errorInFetchingTemplate">
              <div class="card-body">
                <div class="content">
                  <p class="promote-services">
                    <i class="icon icon-star-filled"></i>
                    <span>{{ promotionMessage }}</span>
                  </p>
                  <div class="option option-md">
                    <input type="checkbox" v-model="disclaimerChecked" id="template-disclaimer" />
                    <label for="template-disclaimer">{{ disclaimerText }}</label>
                  </div>
                  <button
                    :disabled="disableTemplateLoad"
                    :class="['btn', 'btn-block', 'btn-lg', 'btn-success', loadingTemplate ? 'loading' : '']"
                    @click="useTemplate()"
                  >
                    <span v-if="loadingTemplate">
                      <i :style="{marginRight: '10px'}" class="fas fa-spinner"></i>&nbsp;Loading Template
                    </span>
                    <span v-else>
                      <i class="icon icon-ok"></i>&nbsp;Get this template
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div
            v-if="errorInFetchingTemplate"
            @click="fetchTemplate(templateId)"
          >Error in fetching template! Click here to try again.</div>
        </div>
      </section>
    </section>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { WebsiteTemplate } from '../../../models'
import { FunnelType } from '../../../models/funnel'
import { WebsiteTemplateCategoryType } from '../../../models/website_template_category'

export default Vue.extend({
  data() {
    return {
      websiteTemplate: {} as WebsiteTemplate,
      fetchingTemplate: false,
      loadingTemplate: false,
      errorInFetchingTemplate: false,
      disclaimerChecked: false,
      disclaimerText:
        'I agree, this is a free template. If you need a custom design, check out our marketplace!'
    }
  },
  computed: {
    categoryId() {
      return this.$route.params.id
    },
    templateId() {
      return this.$route.params.template_id
    },
    disableTemplateLoad(): boolean {
      return (
        this.loadingTemplate ||
        !this.websiteTemplate.id ||
        !this.disclaimerChecked
      )
    },
    currentLocationId() {
      return this.$route.params.location_id
    },
    inWebsitesTemplateCategoryPage(): boolean {
      return this.templateCategoryType === WebsiteTemplateCategoryType.Websites
    },
    templateCategoryType(): string {
      const routePath = this.$route.path
      if (routePath.indexOf('funnel-templates') > -1) {
        return WebsiteTemplateCategoryType.Funnels
      }

      if (routePath.indexOf('website-templates') > -1) {
        return WebsiteTemplateCategoryType.Websites
      }

      return WebsiteTemplateCategoryType.Websites
    },
    promotionMessage(): string {
      return this.inWebsitesTemplateCategoryPage
        ? 'If you need any help with websites, check out the "Website Build" service in the marketplace.'
        : 'If you need any help with funnels, check out the "Funnel Build" service in the marketplace.'
    }
  },
  mounted() {
    this.fetchTemplate(this.templateId)
  },
  methods: {
    async fetchTemplate(templateId: string) {
      try {
        this.fetchingTemplate = true
        this.errorInFetchingTemplate = false
        const template = await WebsiteTemplate.getById(templateId)
        if (template) {
          this.websiteTemplate = template
        }
      } catch (error) {
        console.error('Error in fetching template --> ', error)
        this.errorInFetchingTemplate = true
      } finally {
        this.fetchingTemplate = false
      }
    },
    async useTemplate() {
      try {
        this.loadingTemplate = true
        await this.$http.post(
          `/website-template/${this.websiteTemplate.id}/load/${this.currentLocationId}`
        )
        const templateType = this.websiteTemplate.templateType

        switch (templateType) {
          case FunnelType.Funnel: {
            this.$router.push({ name: 'all_funnels' })
            break
          }
          case FunnelType.Website: {
            this.$router.push({ name: 'websites_funnels' })
            break
          }
          default: {
            this.$router.push({ name: 'websites_funnels' })
          }
        }
      } catch (error) {
        console.error('Error while loading template --> ', error)
        alert('Error while loading template!')
      } finally {
        this.loadingTemplate = false
      }
    }
  }
})
</script>
<style scoped>
.promote-services i {
  padding: 0px 3px;
}

.promote-services span {
  margin-left: 10px;
}

.content .option.option-md {
  margin-bottom: 10px;
}

hr {
  border-top: 2px solid #ededee;
}

.template-back {
  font-size: 1rem;
}

.back {
  margin-right: 5px;
}

.website-template .content-container {
  display: flex;
  justify-content: center;
}

/* .website-template .content-container .template-card {
  max-width: calc(100% - 250px);
} */

.website-template .image img {
  max-width: 100%;
  min-height: 300px;
}

.website-template button i {
  display: inline-block;
}

.website-template button.loading i {
  animation: keep-rotating 1s infinite linear;
}

.website-template .content {
  margin: 10px 0;
}

.website-template .content:first-child {
  margin-top: 0;
}

.website-template .content:last-child {
  margin-bottom: 0;
}

.website-template .live-preview {
  text-align: center;
}

.website-template .live-preview a {
  font-size: 24px;
}

.website-template .live-preview a:hover {
  text-decoration: underline;
}

.website-template .get-template {
  position: fixed;
  right: 50px;
}

.website-template .disclaimer label {
  font-size: 14px;
  line-height: 14px;
}

@keyframes keep-rotating {
  0% {
    transform: rotate(0);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
