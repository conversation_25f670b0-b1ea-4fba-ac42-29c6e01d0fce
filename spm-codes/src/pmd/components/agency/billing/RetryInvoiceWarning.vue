<template>
  <HLWarning
    :showAlert="showAlert && !isDismissed"
    title="You have one or more failed payments. Please check your billing
          history."
    description="Please take a moment to retry your failed invoices."
    @closeWarning="closeAlert"
  >
    <template v-slot:action>
      <div class="alert-actions">
        <button
          type="button"
          class="btn btn-success upgrade-btn"
          @click="handleResolve()"
        >
          Resolve Now
        </button>
        <button type="button" class="btn btn-primary" @click="closeAlert">
          Dismiss
        </button>
      </div>
      <div class="info-message">
        Only Agency Admins can see this warning. For more info
        <contact-support />
      </div>
    </template>
  </HLWarning>
</template>

<script>
import { Company, User } from '@/models'
import ContactSupport from '@/pmd/components/common/ContactSupport.vue'
import HLWarning from '@/pmd/components/common/HLWarning.vue'
import { trackGaEvent } from '@/util/helper'

export default {
  data() {
    return {
      showAlert: false,
    }
  },
  components: {
    ContactSupport,
    HLWarning,
  },
  watch: {
    company: function(newCompany, oldCompany) {
      if (newCompany !== oldCompany && !this.showAlert) this.fetchData()
    },
  },
  mounted() {
    this.fetchData()
  },
  computed: {
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    company() {
      return this.$store.state.company.company
    },
    isDismissed() {
      if (
        this.company &&
        this.company.upgrade_alert_dismissed &&
        this.$route.path
      ) {
        if (
          (this.company.upgrade_alert_dismissed.seconds + 3600) * 1000 >
          +new Date()
        ) {
          return true
        }
      }
      return false
    },
  },
  methods: {
    fetchData() {
      setTimeout(async () => {
        if (
          this.company &&
          this.company.status &&
          this.company.status.includes('failed-payment') &&
          this.$route &&
          this.$route.name &&
          !this.$route.name.includes('inactive') &&
          this.user &&
          this.user.type === 'agency' &&
          this.user.role === 'admin'
        ) {
          // this.showAlert = this.company.upgrade_alert_dismissed ? (this.company.upgrade_alert_dismissed.seconds + 3600) * 1000 < +new Date() : true;
          this.showAlert = true
          trackGaEvent(
            'BillingRetryInvoice',
            this.company.id,
            'In-app warning: Shown',
            1
          )
        }
      }, 5000)
    },
    async closeAlert() {
      // this.showAlert = false;
      let d = new Date()
      await Company.collectionRef()
        .doc(this.company.id)
        .update({
          upgrade_alert_dismissed: d,
        })
      this.showAlert = false
      trackGaEvent(
        'BillingRetryInvoice',
        this.company.id,
        'In-app warning: Dismissed',
        1
      )
    },
    handleResolve() {
      this.showAlert = false
      this.$router.push({ name: 'account_billing' })
      trackGaEvent(
        'BillingRetryInvoice',
        this.company.id,
        'In-app warning: Resolving',
        1
      )
    },
  },
}
</script>
<style scoped>
.alert-actions {
  margin: 20px 0px;
}

.upgrade-btn {
  margin-right: 20px;
}
.info-message {
  border-left: 4px solid #198bf5;
  padding: 8px;
  background-color: #eaf3fe;
}
</style>
