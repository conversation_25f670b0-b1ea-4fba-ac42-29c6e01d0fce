<template>
	<Filters
		:conditions="conditions"
		:filterMaster="filterMaster"
		v-on:update:conditions="$emit('update:conditions', $event)"
	/>
</template>

<script lang="ts">
import Vue from 'vue'
import { Condition } from '@/models/trigger';

const Filters = () => import( './Filters.vue');

export default Vue.extend({
	props: ['conditions'],
	components: { Filters },
	data() {
		return {
			filterMaster: [
        {
          id: 'before-days-in-number',
					placeHolder: 'Days in number',
					title: 'Before no. of days',
					value: 'task.dueDate',
          valueType: 'number',
          operator: 'time-diff-now-lte',
					type: 'input',
        },
        {
          id: 'after-days-in-number',
					placeHolder: 'Days in number',
					title: 'After no. of days',
					value: 'task.dueDate',
          valueType: 'number',
          operator: 'time-diff-now-gte',
					type: 'input',
				},
      ]
		}
  },
  watch: {
    conditions: {
      handler: function(conditions) {
        this.conditionsChanged(conditions);
      },
      deep: true
    }
  },
  methods: {
    conditionsChanged: function (condition) {
        if (
          lodash.find(condition, { id: 'before-days-in-number' })
        ) {
          if (lodash.find(this.filterMaster, { id: 'after-days-in-number' })) {
            let existingIndex = lodash.findIndex(this.filterMaster, { id: 'after-days-in-number' });
            if(existingIndex !== -1) {
              this.filterMaster.splice(existingIndex, 1);
            }
          }
        } else {
          let existingIndex = lodash.findIndex(this.filterMaster, { id: 'after-days-in-number' });
          if(existingIndex === -1) {
            this.filterMaster.push({
              id: 'after-days-in-number',
              placeHolder: 'Days in number',
              title: 'After no. of days',
              value: 'task.dueDate',
              valueType: 'number',
              operator: 'time-diff-now-gte',
              type: 'input',
            });
          }
        }

        if (
          lodash.find(condition, { id: 'after-days-in-number' })
        ) {
          if (lodash.find(this.filterMaster, { id: 'before-days-in-number' })) {
            let existingIndex = lodash.findIndex(this.filterMaster, { id: 'before-days-in-number' });
            if(existingIndex !== -1) {
              this.filterMaster.splice(existingIndex, 1);
            }
          }
        } else {
          let existingIndex = lodash.findIndex(this.filterMaster, { id: 'before-days-in-number' });
          if(existingIndex === -1) {
            this.filterMaster.push({
              id: 'before-days-in-number',
              placeHolder: 'Days in number',
              title: 'Before no. of days',
              value: 'task.dueDate',
              valueType: 'number',
              operator: 'time-diff-now-lte',
              type: 'input',
            });
          }
        }
    }
  },
	async created() {
		const currentLocationId = this.$router.currentRoute.params.location_id;
    this.conditionsChanged(this.conditions);
	}
})
</script>

