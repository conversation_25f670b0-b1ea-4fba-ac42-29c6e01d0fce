<template>
  <section class="hl_wrapper--inner hl_billing" id="billing">
    <div class="container-fluid">

      <div class="hl_billing--wrap">
        <div class="billing-header">
          <h2>Billing</h2>
          <!-- <button type="button" class="btn btn-blue" @click="handleUpgradeModal" v-if="company.status !== 'active_overused'">
            Upgrade Account </button> -->
        </div>
        <div class="row">
			    <div class="col-lg-6">
              <agency-active-plan @upgrade="showUpgradeModal = true"/>
              <WordpressAgencyBillingCardComponent v-if="company && company.wordpressReseller.subscriber_count && company.wordpressReseller.subscriber_count > 0" />
              <YextAgencyBillingCardComponent v-if="company && company.yextReseller.subscriber_count && company.yextReseller.subscriber_count > 0"
                :company="company"
                />
			        <AgencyAddonHIPAA v-if="company && company.hipaaCompliance" />
			        <AgencyAddonJumpstart v-if="company && company.jumpstartSupported && company.jumpstartSupported.seconds * 1000 >= +new Date()"
                :expiry="company.jumpstartSupported.seconds" />
			        <AgencyAddonPrioritySupport v-if="company && company.premiumUpgraded"
                :company="company"
              />
              <!-- <highlevel-credits v-if="company.elizaEnabled" /> -->
              <!-- <eliza-credits v-if="company.elizaEnabled" /> -->
              <AgencyTOS v-if="company && company.termsOfService" />
              <agency-services />
              <Agency-credits
                v-if="company.inIsvMode && creditsData.complimentaryCredits > 0"
                :loading="creditsData.loading"
                :complimentaryCredits="creditsData.complimentaryCredits"
                :walletCreatedAt="creditsData.createdAt"
                />
              <agency-wallet
                v-if="company.inIsvMode"
                :current-balance="creditsData.currentBalance"
                :auto-recharge-amount="creditsData.autoRechargeAmount"
                :recharge-threshold-balance="creditsData.rechargeThresholdBalance"
                :notification="walletNotification"
                :loading="creditsData.loading"
                :currency-symbol="currencySymbol"
                @updateRechargeAmount="updateAutoRechargeAmount"
                @updateThreshold="updateAutoRechargeThreshold"
                @addBalance="initAddBalance"
                @refresh="fetchWalletData"
              />

            </div>
            <div class="col-lg-6">
              <billing-method v-if="company && company.stripeId"/>
              <AgencyBillingAddress />
              <billing-history />
            </div>
        </div>
      </div>
    </div>
    <upgrade-modalv2
      :company="company"
      v-if="showUpgradeModal"
      :show="showUpgradeModal"
      source="billing-page"
      @close="showUpgradeModal = false"
    />
    <add-balance
      :show-modal="creditsData.addBalance.showModal"
      :stripe-customer-id="company.stripeId"
      :current-balance="creditsData.currentBalance"
      :currency-symbol="currencySymbol"
      :active-card="{cardNumber: company.cardLast4}"
      :company-id="company.id"
      @success="fetchWalletData"
      @ok="creditsData.addBalance.showModal = false"
      @cancel="creditsData.addBalance.showModal = false"
    />
  </section>
  <!-- END of .hl_billing -->
</template>

<script lang='ts'>
import Vue from 'vue';
import AgencyActivePlan from "./AgencyActivePlan.vue";
import AgencyServices from "./AgencyServices.vue";
import BillingMethod from "./BillingMethod.vue";
import BillingHistory from "./BillingHistory.vue";
import AgencyAddonHIPAA from "./AgencyAddonHIPAA.vue";
import AgencyAddonJumpstart from "./AgencyAddonJumpstart.vue";
import AgencyAddonPrioritySupport from "./AgencyAddonPrioritySupport.vue";
import AgencyTOS from "./AgencyTOS.vue";
import AgencyBillingAddress from "./AgencyBillingAddress.vue";
import YextAgencyBillingCardComponent from "@/pmd/components/agency/reselling/YextAgencyBillingCardComponent.vue";
import WordpressAgencyBillingCardComponent from "@/pmd/components/agency/reselling/wordpress/WordpressAgencyBillingCardComponent.vue";
// import UpgradeModal from "@/pmd/components/agency/billing/UpgradeModal.vue";

import UpgradeModalv2 from "@/pmd/components/agency/billing/UpgradeModalv2.vue";
import {trackGaEvent} from '@/util/helper';
import ElizaCredits from './ElizaCredits.vue';
import HighlevelCredits from './HighlevelCredits.vue';
import AgencyWallet from "@/pmd/components/agency/billing/AgencyWallet.vue";
import AgencyCredits from "@/pmd/components/agency/billing/AgencyCredits.vue";
import AgencyWallletMixin from "@/pmd/components/agency/billing/AgencyWallletMixin.js";
import AddBalance from '@/pmd/components/saas/location_billing/AddBalance.vue'

import {Company} from '@/models';
import {mapState} from 'vuex';
import {CompanyState} from '@/store/state_models';

export default Vue.extend({
  mixins: [AgencyWallletMixin],
  data() {
    return {
      showUpgradeModal: false
    };
  },
  components: {
    AgencyActivePlan,
    AgencyServices,
    BillingMethod,
    BillingHistory,
    AgencyAddonHIPAA,
    AgencyAddonJumpstart,
    AgencyAddonPrioritySupport,
    UpgradeModalv2,
    ElizaCredits,
    HighlevelCredits,
    AgencyTOS,
    AgencyBillingAddress,
    YextAgencyBillingCardComponent,
    WordpressAgencyBillingCardComponent,
    AgencyWallet,
    AgencyCredits,
    AddBalance,
  },
  computed: {
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined;
      },
    }),
  },
  methods: {
    handleUpgradeModal() {
      trackGaEvent('BillingUpgradeInitiate', this.company.id, 'Billing Page Button', 1);
      this.showUpgradeModal = true;
    },
    initAddBalance() {
      // if(!this.company.cardLast4) {
      //   // TODO: ADD GA Event here.
      //   alert('You have no payment method added ! Please add your payment method first.');
      // } else {
        this.creditsData.addBalance.showModal = true
      // }
    }
  }
});
</script>
<style>
.hl_billing--wrap {
    height: calc(100vh - 120px);
}
.hl_billing--wrap h2{
  margin-bottom: 0px;
}

.billing-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
}
.hl_billing--account .card{
  background-image: linear-gradient(to right, #009688, #0c2d3e) !important;
}
.hl_billing--account .card.--addons{
  background-image: linear-gradient(to right, #46acfe, #0c2d3e) !important;
}


.agency-plan__heading{
  font-size: 20px !important;
  font-weight: 500;
}
.agency-plan__description{
  text-transform: uppercase;
  font-size: 15px !important;
  opacity: 0.8;
  margin-top: 12px;
}
</style>
