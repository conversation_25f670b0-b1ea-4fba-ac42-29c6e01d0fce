<template>
  <b-modal ref="modal-sm-d" size="sm" @hide="close" hide-footer>
    <template v-slot:modal-title>
      <h5 class="modal-title">
        {{ modelContent.title }}
      </h5>
    </template>
    <template>
      <div class="modal-body">
        <div class="modal-body--inner" v-html="modelContent.bodyHtml"></div>
      </div>
      <div class="modal-footer">
        <div class="modal-footer--inner">
          <div class="modal-footer--inner nav">
            <template v-if="modelContent.type === 'info'">
              <button type="button" class="btn btn-primary" @click="close">
                Close
              </button>
            </template>
<!-- 
            <div style="display: inline-block; position: relative">
              <button
                type="button"
                class="btn btn-blue"
                @click.prevent="operate"
              >
                Action button
              </button>
              <div
                style="
                  position: absolute;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  top: 50%;
                "
              ></div>
            </div> -->
          </div>
        </div>
      </div>
    </template>
  </b-modal>
</template>

<script lang="ts">
// @ts-nocheck
import Vue from 'vue'
import { UxMessage } from '@/util/ux_message'
const MoonLoader = () => import('@/pmd/components/MoonLoader.vue')

export default Vue.extend({
  name: 'ReputationGenericModal',
  props: {
    modelContent: {
      type: Object,
    },
  },
  components: {
    MoonLoader,
  },
  inject: ['uxmessage'],
  data() {
    return {}
  },
  created() {},
  async mounted() {
    await this.$nextTick()
    this.showPopup()
  },
  methods: {
    async operate() {},
    showError(err: string, stack?: string) {
      this.uxmessage(UxMessage.errorType(err, stack))
    },
    close(processed: boolean = false) {
      this.$emit('closed', processed)
      this.hidePopup()
    },
    showPopup() {
      this.$refs['modal-sm-d'].show()
    },
    hidePopup() {
      this.$refs['modal-sm-d'].hide()
    },
  },
  computed: {},
  watch: {},
})
</script>
<style>
</style>
