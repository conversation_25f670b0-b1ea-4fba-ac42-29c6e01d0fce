import Vue from 'vue'
import { RouteConfig, Route, RawLocation } from 'vue-router'

import keys from 'lodash/keys'
import first from 'lodash/first'
import isEmpty from 'lodash/isEmpty'

import store from '../store'
import { AuthUser, Location, User, CustomerType } from '../models'
import EmptyRouterParent from '@/pmd/pages/EmptyRouterParent.vue'
import LoginWithEmail from '@/pmd/pages/LoginWithEmail.vue'
import NotificationHelper from '@/util/notification_helper'
import OnboardingMicroApp from '@/pmd/pages/OnboardingMicroApp.vue'
import LaunchpadMicroApp from '@/pmd/pages/LaunchpadMicroApp.vue'
/*** Permissions ***/
import {
  routesRequiredPermissions,
  locationLevelRoutesPermissions,
  paymentBetaAccessRoutes
} from '@/routes/permissions'
import { Utils } from '@/util/utils'
import routeMapping from './pmd_v2_mapping'
// ====================================================== //
/*** Agency Level Components: ***/
const Account = () =>
  import(/* webpackChunkName: "accounts" */ '@/pmd/pages/agency/Account.vue')

const SaasDashboard = () =>
  import(
    /* webpackChunkName: "saas-dashboard" */ '@/pmd/pages/agency/SaasDashboard.vue'
  ).then(m => m.default)

const AgencyDashboard = () =>
  import(
    /* webpackChunkName: "agency-dashboard" */ '@/pmd/pages/agency/AgencyDashboard.vue'
  ).then(m => m.default)
const SaasEducation = () =>
  import(
    /* webpackChunkName: "saas-education" */ '@/pmd/pages/agency/SaasEducation.vue'
  ).then(m => m.default)
const SaasFasttrack = () =>
  import(
    /* webpackChunkName: "saas-fasttrack" */ '@/pmd/pages/agency/SaasFasttrack.vue'
  ).then(m => m.default)
const ListAccounts = () =>
  import(
    /* webpackChunkName: "accounts" */ '@/pmd/pages/agency/ListAccounts.vue'
  ).then(m => m.default)
const AccountDetail = () =>
  import(
    /* webpackChunkName: "account-details" */ '@/pmd/pages/agency/AccountDetail.vue'
  )
const AddAccount = () =>
  import(
    /* webpackChunkName: "add-location" */ '@/pmd/pages/agency/AddAccount.vue'
  )
const AddAccountMap = () =>
  import(
    /* webpackChunkName: "map-search" */ '@/pmd/pages/agency/AddAccountMap.vue'
  )
const AddAccountTemplate = () =>
  import(
    /* webpackChunkName: "snapshots" */ '@/pmd/pages/agency/AddAccountTemplate.vue'
  )

const AgencyReselling= () =>
  import(
    /* webpackChunkName: "agency-reselling" */ '@/pmd/pages/agency/AgencyReselling.vue'
  )
const AccountsDashboard = () =>
  // This seems depreciated... //
  import(
    /* webpackChunkName: "account-dashboard" */ '@/pmd/pages/agency/AccountsDashboard.vue'
  ).then(m => m.default)
const AgencyIdeas = () =>
  import(/* webpackChunkName: "idea" */ '@/pmd/pages/agency/Ideas.vue').then(
    m => m.default
  )
const HelpDocuments = () =>
  import(/* webpackChunkName: "help" */ '@/pmd/pages/agency/Help.vue').then(
    m => m.default
  )
const AccountResources = () =>
  import(
    /* webpackChunkName: "recources" */ '@/pmd/pages/agency/AccountSalesResources.vue'
  ).then(m => m.default)
const CustomMenuLinkPage = () =>
  import(
    /* webpackChunkName: "custom-menu-link" */ '@/pmd/pages/agency/CustomMenuLinkPage.vue'
  )
const AgencyLaunchpad = () =>
  import(
    /* webpackChunkName: "launchpad" */ '@/pmd/pages/agency/AgencyLaunchpad.vue'
  )

const EmailBuilderMicroApp = () =>
  import('@/pmd/pages/emailBuilder/EmailBuilderMicroApp.vue')

// ========================================================== //
/*** Agency Level Settings Components: ***/
const AccountSettings = () =>
  import(
    /* webpackChunkName: "profile-settings" */ '@/pmd/pages/agency/AccountSettings.vue'
  )
const ProfileSettings = () =>
  import(
    /* webpackChunkName: "profile-settings" */ '@/pmd/pages/settings/ProfileSettingsPage.vue'
  )
const ProfileSettingsV3 = () =>
  import(
    /* webpackChunkName: "profile-settings" */ '@/pmd/pages/settings/ProfileSettingsV3Page.vue'
  )
const AccountCompanySettings = () =>
  import(
    /* webpackChunkName: "agency-settings" */ '@/pmd/pages/agency/AccountCompanySettings.vue'
  ).then(m => m.default)
const AccountTeamManagement = () =>
  import(
    /* webpackChunkName: "team-management" */ '@/pmd/pages/agency/AccountTeamManagement.vue'
  ).then(m => m.default)
const AccountTeamsManagement = () =>
  import(
    /* webpackChunkName: "team-management" */ '@/pmd/pages/agency/AccountTeamsManagement.vue'
  ).then(m => m.default)
const AccountBilling = () =>
  import(
    /* webpackChunkName: "agency-billing" */ '@/pmd/pages/agency/billing/Billing.vue'
  ).then(m => m.default)

const AgencyWalletTransactions = () =>
  import(
    /* webpackChunkName: "agency-wallet-transactions" */ '@/pmd/components/agency/billing/AgencyWalletTransactions.vue'
  ).then(m => m.default)

const AccountBillingParent = () =>
  import(
    /* webpackChunkName: "agency-billing-parent" */ '@/pmd/pages/agency/billing/AccountBillingParent.vue'
  ).then(m => m.default)
const InactiveAgency = () =>
  import(
    /* webpackChunkName: "agency-billing-inactive" */ '@/pmd/pages/agency/billing/InactiveAgency.vue'
  ).then(m => m.default)
const InactiveLocation = () =>
  import(
    /* webpackChunkName: "agency-billing-inactive-location" */ '@/pmd/pages/agency/billing/InactiveLocation.vue'
  ).then(m => m.default)

// const AccountBilling = () =>
//   import('@/pmd/pages/agency/AccountBilling.vue').then(m => m.default) // Depreciated !!
const AccountTwilioSettings = () =>
  import(
    /* webpackChunkName: "twilio-settings" */ '@/pmd/pages/agency/AgencyTwilioSettings.vue'
  ).then(m => m.default)
const AccountSendGridSettings = () =>
  import(
    /* webpackChunkName: "sendgrid-settings" */ '@/pmd/pages/agency/AgencySendGridSettings.vue'
  ).then(m => m.default)
const AccountAffiliateSettings = () =>
  import(
    /* webpackChunkName: "affiliate-settings" */ '@/pmd/pages/agency/AgencyAffiliateSettings.vue'
  ).then(m => m.default)
const AgencyStripeSettings = () =>
  import(
    /* webpackChunkName: "affiliate-settings" */ '@/pmd/pages/agency/AgencyStripeSettings.vue'
  ).then(m => m.default)
const AccountWhiteLabelSettings = () =>
  import(
    /* webpackChunkName: "whitelabel-settings" */ '@/pmd/pages/agency/AgencyWhiteLabelSettings.vue'
  ).then(m => m.default)
const AccountMailGunSettings = () =>
  import(
    /* webpackChunkName: "mailgun-settings" */ '@/pmd/pages/agency/AgencyMailGunSettings.vue'
  ).then(m => m.default)
const AccountCustomFieldsSettings = () =>
  import(
    /* webpackChunkName: "custom-fields-settings" */ '@/pmd/pages/agency/AgencyCustomFieldsSettings.vue'
  ).then(m => m.default)
const CloneAccountList = () =>
  import(
    /* webpackChunkName: "snapshot-list" */ '@/pmd/pages/agency/CloneAccountList.vue'
  ).then(m => m.default)
const ImportAccountTemplate = () =>
  import(
    /* webpackChunkName: "import-acc-template" */ '@/pmd/pages/agency/ImportAccountTemplate.vue'
  ).then(m => m.default)
const CustomMenuLinkSettings = () =>
  import(
    /* webpackChunkName: "import-acc-template" */ '@/pmd/pages/agency/AgencyCustomMenuLinkSettings.vue'
  ).then(m => m.default)

const AgencyAPIKeySettings = () =>
  import(
    /* webpackChunkName: "import-acc-template" */ '@/pmd/pages/agency/AgencyAPIKeySettings.vue'
  ).then(m => m.default)

const AgencyLaunchpadSettings = () =>
  import(
    /* webpackChunkName: "import-acc-template" */ '@/pmd/pages/agency/AgencyLaunchpadSettings.vue'
  ).then(m => m.default)

// ====================================================== //
/*** Report Adwords Components: ***/
const ReportingAdwordsIndex = () =>
  import(
    /* webpackChunkName: "adwords-reporting" */ '../pmd/pages/reporting/adwords/ReportingAdwordsIndex.vue'
  ).then(m => m.default)
const AdwordsCampaigns = () =>
  import(
    /* webpackChunkName: "adwords-reporting" */ '../pmd/pages/reporting/adwords/AdwordsCampaigns.vue'
  ).then(m => m.default)
const AdwordsAdgroups = () =>
  import(
    /* webpackChunkName: "adwords-reporting" */ '../pmd/pages/reporting/adwords/AdwordsAdgroups.vue'
  ).then(m => m.default)
const AdwordsAds = () =>
  import(
    /* webpackChunkName: "adwords-reporting" */ '../pmd/pages/reporting/adwords/AdwordsAds.vue'
  ).then(m => m.default)
const AdwordsKeywords = () =>
  import(
    /* webpackChunkName: "adwords-reporting" */ '../pmd/pages/reporting/adwords/AdwordsKeywords.vue'
  ).then(m => m.default)
const AdwordsConversions = () =>
  import(
    /* webpackChunkName: "adwords-reporting" */ '../pmd/pages/reporting/adwords/AdwordsConversions.vue'
  ).then(m => m.default)

// ========================================================== //
/*** Location Level Components: ***/
// const SideBar = () => import("@/pmd/components/locations/LocationSideBar.vue").then(m => m.default)
const LocationParent = () =>
  import(/* webpackChunkName: "location" */ '@/pmd/pages/LocationParent.vue')
const EmptyLocationParent = () =>
  import(
    /* webpackChunkName: "location-empty" */ '@/pmd/pages/EmptyLocationParent.vue'
  )

const DashboardPage = () =>
  import(
    /* webpackChunkName: "dashboard" */ '../pmd/pages/DashboardPage.vue'
  ).then(m => m.default)
const AppointmentsPage = () =>
  import(
    /* webpackChunkName: "appointments" */ '../pmd/pages/AppointmentsPage.vue'
  ).then(m => m.default)
const AppointmentsReportPage = () =>
  import(
    /* webpackChunkName: "appointments-reports" */ '@/pmd/pages/AppointmentsReportPage.vue'
  ).then(m => m.default)
const NotificationsPage = () =>
  import(
    /* webpackChunkName: "notifications" */ '../pmd/pages/NotificationsPage.vue'
  ).then(m => m.default)
const NotificationsList = () =>
  import(
    /* webpackChunkName: "notification-list" */ '../pmd/pages/NotificationsList.vue'
  ).then(m => m.default)

const RemindersPage = () =>
  import(
    /* webpackChunkName: "reminders" */ '../pmd/pages/RemindersPage.vue'
  ).then(m => m.default)

const ForgotPassword = () =>
  import(
    /* webpackChunkName: "forgot-password" */ '@/pmd/pages/ForgotPassword.vue'
  ).then(m => m.default)
const SignUp = () =>
  import(/* webpackChunkName: "signup" */ '@/pmd/pages/SignUp.vue').then(
    m => m.default
  )
// const CalendarPage = () => import("@/pmd/pages/CalendarPage.vue").then(m => m.default)
const ConversationsPage = () =>
  import(
    /* webpackChunkName: "conversations" */ '@/pmd/pages/ConversationsPage.vue'
  ).then(m => m.default)
// const ProfilePage = () => import("@/pmd/pages/ProfilePage.vue").then(m => m.default)
const AnalysisPage = () =>
  import(
    /* webpackChunkName: "analysis-p" */ '@/pmd/pages/PageMoved.vue'
  ).then(m => m.default)
const ReviewsAllPage = () =>
  import(
    /* webpackChunkName: "reviews-p" */ '@/pmd/pages/reputation/tabs/ReviewsAllPage.vue'
  ).then(m => m.default)
const ReputationLandingPage = () =>
  import(
    /* webpackChunkName: "reputation-landing-page" */ '@/pmd/pages/reputation/ReputationLandingPage.vue'
  ).then(m => m.default)

const ReputationSetingsPage = () =>
  import(
    /* webpackChunkName: "reputation-settings-page" */ '@/pmd/pages/reputation/tabs/ReputationSettingsPage.vue'
  ).then(m => m.default)

const ReputationOverviewPage = () =>
  import(
    /* webpackChunkName: "reputation-settings-page" */ '@/pmd/pages/reputation/tabs/ReputationOverviewPage.vue'
  ).then(m => m.default)

const ReputationOnlineListing = () =>
  import(
    /* webpackChunkName: "reputation-online-listing-page" */ '@/pmd/pages/reputation/tabs/OnlineListingPage.vue'
  ).then(m => m.default)

const ReviewRequestsAllPage = () =>
  import(
    /* webpackChunkName: "review-requests" */ '@/pmd/pages/reputation/tabs/ReviewRequestsAllPage.vue'
  ).then(m => m.default)
const NumberPoolDashboard = () =>
  import(
    /* webpackChunkName: "numberpool-dashboard" */ '@/pmd/pages/NumberPoolDashboard.vue'
  ).then(m => m.default)
const AgentReporting = () =>
  import(
    /* webpackChunkName: "agent-reporting" */ '@/pmd/pages/AgentReporting.vue'
  ).then(m => m.default)
const ScanReport = () =>
  import(
    /* webpackChunkName: "scan-report" */ '@/pmd/pages/ScanReport.vue'
  ).then(m => m.default)
// const AccountSearch = () => import("@/pmd/pages/agency/AccountSearch.vue").then(m => m.default)
const CustomersPage = () =>
  import(
    /* webpackChunkName: "customers-p" */ '@/pmd/pages/CustomersPage.vue'
  ).then(m => m.default)
const ManualActionList = () =>
  import(
    /* webpackChunkName: "manual-actions" */ '@/pmd/pages/ManualActionList.vue'
  ).then(m => m.default)
const SmartList = () =>
  import(
    /* webpackChunkName: "smartlist" */ '@/pmd/pages/smartlist/SmartList.vue'
  ).then(m => m.default)
const LeadsPage = () =>
  import(/* webpackChunkName: "leads" */ '@/pmd/pages/LeadsPage.vue').then(
    m => m.default
  )
// const CustomerDetail = () => // Seems depreciated
//   import( /* webpackChunkName: "customer-details-old" */ '@/pmd/pages/customers/CustomerDetail.vue')
const CustomerDetailNew = () =>
  import(
    /* webpackChunkName: "customer-details" */ '@/pmd/pages/customers/ContactDetailNew.vue'
  )
const OpportunitiesPage = () =>
  import(
    /* webpackChunkName: "opportunities" */ '@/pmd/pages/OpportunitiesPage.vue'
  )
const CustomerImportsPage = () =>
  import(
    /* webpackChunkName: "customer-imports" */ '@/pmd/pages/customers/ContactBulkRequests.vue'
  )
const BulkRequest = () =>
  import('@/pmd/pages/customers/CustomerImportsPage.vue')

const CustomerRestore = () =>
  import('@/pmd/pages/customers/CustomerRestore.vue')

const BulkActionsPage = () =>
  import(
    /* webpackChunkName: "customer-imports" */ '@/pmd/pages/smartlist/BulkActionsList.vue'
  )
  const BulkActionsView = () =>
  import(
    /* webpackChunkName: "customer-imports" */ '@/pmd/pages/smartlist/BulkActionsView.vue'
  )
// ========================================================== //
/*** Location Level Settings Components: ***/
const SettingsPage = () =>
  import(
    /* webpackChunkName: "location-settings" */ '@/pmd/pages/SettingsPage.vue'
  )
const CustomValues = () =>
  import(
    /* webpackChunkName: "location-custom-values" */ '@/pmd/pages/settings/CustomValues.vue'
  ).then(m => m.default)
const CompanySettingsPage = () =>
  import(
    /* webpackChunkName: "location-company-settings" */ '@/pmd/pages/settings/CompanySettingsPage.vue'
  ).then(m => m.default)
const LocationBillingPage = () =>
  import(
    /* webpackChunkName: "location-company-settings" */ '@/pmd/pages/settings/LocationBillingPage.vue'
  ).then(m => m.default)
const LocationBillingSettings = () =>
  import('@/pmd/pages/settings/LocationBillingSettings.vue').then(
    m => m.default
  )
const LocationWalletTransactions = () =>
  import('@/pmd/pages/settings/LocationWalletTransactions.vue').then(
    m => m.default
  )
const IntegrationSettingsPage = () =>
  import(
    /* webpackChunkName: "location-integration-settings" */ '@/pmd/pages/settings/IntegrationSettingsPage.vue'
  )
const SocialPostingSettings = () =>
  import(
    /* webpackChunkName: "location-social-settings" */ '@/pmd/pages/settings/SocialPostingSettings.vue'
  ).then(m => m.default)
const ReviewPopupSettings = () =>
  import(
    /* webpackChunkName: "location-review-settings-m" */ '@/pmd/pages/settings/ReviewPopupSettings.vue'
  ).then(m => m.default)
const PipelineSettings = () =>
  import(
    /* webpackChunkName: "location-pipeline-settings" */ '@/pmd/pages/settings/PipelineSettings.vue'
  ).then(m => m.default)
const PaymentSettings = () =>
  import(
    /* webpackChunkName: "location-payment-settings" */ '@/pmd/pages/settings/PaymentSettings.vue'
  ).then(m => m.default)
const AppointmentWidgetSettings = () =>
  import(
    /* webpackChunkName: "location-appointment-settings" */ '@/pmd/pages/settings/AppointmentWidgetSettings.vue'
  )
const ChatWidgetSettings = () =>
  import(
    /* webpackChunkName: "location-chat-settings" */ '@/pmd/pages/settings/ChatWidgetSettings.vue'
  ).then(m => m.default)
const NoShowSMSSettings = () =>
  import(
    /* webpackChunkName: "location-nosms-settings" */ '@/pmd/pages/settings/NoShowSMSSettings.vue'
  ).then(m => m.default)
const BirthdaySMSSettings = () =>
  import(
    /* webpackChunkName: "location-birthday-sms-settings" */ '@/pmd/pages/settings/BirthdaySMSSettings.vue'
  ).then(m => m.default)
const AllPhoneNumbersSettings = () =>
  import(
    /* webpackChunkName: "location-phone-number-settings" */ '@/pmd/pages/settings/AllPhoneNumbersSettings.vue'
  ).then(m => m.default)
// const BulkEmailSettings = () =>
//   import('@/pmd/pages/settings/BulkEmailSettings.vue').then(m => m.default)
const LocationMailGunSettings = () =>
  import(
    /* webpackChunkName: "location-mailgun-settings" */ '@/pmd/pages/settings/LocationMailGunSettings.vue'
  )
const CalendarSettings = () =>
  import(
    /* webpackChunkName: "location-calendar-settings" */ '@/pmd/pages/settings/CalendarSettings.vue'
  ).then(m => m.default)
const TemplateSettings = () =>
  import(
    /* webpackChunkName: "location-template-settings" */ '@/pmd/pages/settings/TemplateMessageSettings.vue'
  )

const SocialMediaDashboard = () =>
  import(
    /* webpackChunkName: "social-media-dashboard" */ '@/pmd/pages/social_media/SocialMediaDashboard.vue'
  ).then(m => m.default)
const LoginWithToken = () =>
  import(
    /* webpackChunkName: "login-token" */ '@/pmd/pages/LoginWithToken.vue'
  ).then(m => m.default)
const LoginWithShopify = () =>
  import(
    /* webpackChunkName: "login-shopify" */ '@/pmd/pages/LoginWithShopify.vue'
  ).then(m => m.default)
const ShopifyLocationSelect = () =>
  import(
    /* webpackChunkName: "login-token" */ '@/pmd/pages/ShopifyLocationSelect.vue'
  ).then(m => m.default)
const CampaignList = () =>
  import(
    /* webpackChunkName: "m-campaign-list" */ '@/pmd/pages/marketing/CampaignList.vue'
  ).then(m => m.default)
const CampaignNew = () =>
  import(
    /* webpackChunkName: "m-campaign-new" */ '@/pmd/pages/marketing/CampaignNew.vue'
  ).then(m => m.default)
const CampaignDetail = () =>
  import(
    /* webpackChunkName: "m-campaign-detail" */ '@/pmd/pages/marketing/CampaignDetail.vue'
  ).then(m => m.default)
const CampaignActivity = () =>
  import(
    /* webpackChunkName: "m-campaign-activity" */ '@/pmd/pages/marketing/CampaignActivity.vue'
  ).then(m => m.default)
const CampaignAccountsList = () =>
  import(
    /* webpackChunkName: "m-campaign-acc-list" */ '@/pmd/pages/marketing/CampaignAccountsList.vue'
  ).then(m => m.default)
const BulkCampaignsPage = () =>
  import(
    /* webpackChunkName: "m-bulk-campaigns" */ '@/pmd/pages/marketing/BulkCampaignsPage.vue'
  ).then(m => m.default)
const FormBuilderPage = () =>
  import(
    /* webpackChunkName: "m-form-builder" */ '@/pmd/pages/marketing/FormBuilderPage.vue'
  ).then(m => m.default)
const EmailBuilderPage = () =>
  import(
    /* webpackChunkName: "m-email-builder" */ '@/pmd/pages/emailBuilder/EmailBuilderListPage.vue'
  )
const Emails = () => import('@/pmd/pages/emailBuilder/Emails.vue')

const TextBuilder = () =>
  import(
    /* webpackChunkName: "m-text-builder" */ '@/pmd/pages/marketing/TextTemplates.vue'
  ).then(m => m.default)
const BuilderCreateForm = () =>
  import(
    /* webpackChunkName: "m-builder-create-from" */ '@/pmd/pages/marketing/BuilderCreateForm.vue'
  ).then(m => m.default)

const BuilderCreateFormOld = () =>
  import(
    /* webpackChunkName: "m-builder-create-from-old" */ '@/pmd/pages/marketing/BuilderCreateFormOld.vue'
  ).then(m => m.default)

const EmbedBuilder = () =>
  import(
    /* webpackChunkName: "m-email-builder" */ '@/pmd/pages/marketing/EmbedBuilder.vue'
  ).then(m => m.default)
const FacebookAdFieldsMappingSettings = () =>
  import(
    /* webpackChunkName: "form-builder-mapping-settings" */ '@/pmd/pages/settings/FacebookAdFieldsMappingSettings.vue'
  )

const DomainSettings = () =>
  import(
    /* webpackChunkName: "domain-settings" */ '@/pmd/pages/settings/DomainSettings.vue'
  ).then(m => m.default)

const RedirectSettings = () =>
  import(
    '@/pmd/pages/settings/RedirectSettings.vue'
  ).then(m => m.default)

const TagsSettings = () =>
  import(
    /* webpackChunkName: "tags-settings" */ '@/pmd/pages/settings/TagsSettings.vue'
  ).then(m => m.default)

const SMTPServiceSettings = () =>
  import(
    /* webpackChunkName: "smpt-settings" */ '@/pmd/pages/settings/SMTPServiceSettings.vue'
  ).then(m => m.default)

const AttributionSettings = () =>
  import('@/pmd/pages/settings/AttributionSettings.vue').then(m => m.default)

const EmbedSurvey = () =>
  import(
    /* webpackChunkName: "m-embed-survey" */ '@/pmd/pages/marketing/EmbedSurvey.vue'
  ).then(m => m.default)

const SurveyList = () =>
  import(
    /* webpackChunkName: "m-survey-list" */ '@/pmd/pages/marketing/SurveyList.vue'
  ).then(m => m.default)
const BuilderCreateSurvey = () =>
  import(
    /* webpackChunkName: "m-builder-create-survey" */ '@/pmd/pages/marketing/BuilderCreateSurvey.vue'
  ).then(m => m.default)

const JotboxForm = () =>
  import('@/pmd/pages/marketing/JotboxForm.vue').then(m => m.default)
const JotboxSurvey = () =>
  import('@/pmd/pages/marketing/JotboxSurvey.vue').then(m => m.default)

const FunnelsPage = () =>
  import(
    /* webpackChunkName: "funnels" */ '@/pmd/pages/funnels/FunnelsPage.vue'
  )

const WordpressDashboard = () =>
  import(
    /* webpackChunkName: "wordpress-dashboard" */ '@/pmd/pages/WordpressDashboard.vue'
  )
const FunnelsList = () =>
  import(
    /* webpackChunkName: "funnels-list" */ '@/pmd/pages/funnels/FunnelsList.vue'
  )

// import FunnelDetailPage from '@/pmd/pages/funnels/FunnelDetailPage.vue'
const FunnelDetailPage = () =>
  import(
    /* webpackChunkName: "funnel-details" */ '@/pmd/pages/funnels/FunnelDetailPage.vue'
  )
const FunnelSteps = () =>
  import(
    /* webpackChunkName: "funnel-steps" */ '@/pmd/pages/funnels/FunnelSteps.vue'
  )
const FunnelStats = () =>
  import(
    /* webpackChunkName: "funnel-stats" */ '@/pmd/pages/funnels/FunnelStats.vue'
  )
const FunnelSales = () =>
  import(
    /* webpackChunkName: "funnel-sales" */ '@/pmd/pages/funnels/FunnelSales.vue'
  )
const FunnelSettings = () =>
  import(
    /* webpackChunkName: "funnel-settings" */ '@/pmd/pages/funnels/FunnelSettings.vue'
  )

const WebsiteProducts = () =>
  import(
    /* webpackChunkName: "website-products" */ '@/pmd/components/funnels/WebsiteProducts.vue'
  )

const FunnelStepOverview = () =>
  import(
    /* webpackChunkName: "funnel-step-overview" */ '@/pmd/pages/funnels/FunnelStepOverview.vue'
  )
const FunnelStepAutomation = () =>
  import(
    /* webpackChunkName: "funnel-step-automation" */ '@/pmd/pages/funnels/FunnelStepAutomation.vue'
  )
const FunnelStepPublish = () =>
  import(
    /* webpackChunkName: "funnel-step-publish" */ '@/pmd/pages/funnels/FunnelStepPublish.vue'
  )
const FunnelStepProduct = () =>
  import(
    /* webpackChunkName: "funnel-step-product" */ '@/pmd/pages/funnels/FunnelStepProduct.vue'
  )

const FunnelStepProductV2 = () =>
  import(
    /* webpackChunkName: "funnel-step-product" */ '@/pmd/pages/funnels/FunnelStepProductV2.vue'
  )

  const FunnelProductsList = () =>
  import('@/pmd/pages/funnels/FunnelProductsList.vue')

const AcceptShareFunnel = () =>
  import(
    /* webpackChunkName: "funnel-accept-share" */ '@/pmd/pages/funnels/AcceptShareFunnel.vue'
  )

const AcceptShareMembershipProduct = () =>
  import(
    /* webpackChunkName: "membership-accept-share" */ '@/pmd/pages/membership/AcceptShareMembershipProduct.vue'
  )

const TriggersPage = () =>
  import(
    /* webpackChunkName: "triggers" */ '@/pmd/pages/triggers/TriggersPage.vue'
  )
const TriggerDetailPage = () =>
  import(
    /* webpackChunkName: "trigger-detail" */ '@/pmd/pages/triggers/TriggerDetailPage.vue'
  )
const LinksPage = () =>
  import(
    /* webpackChunkName: "m-links" */ '@/pmd/pages/marketing/LinksPage.vue'
  )

const TermsPage = () =>
  import(/* webpackChunkName: "terms" */ '@/pmd/pages/TermsPage.vue')
const FacebookPageSelect = () =>
  import(
    /* webpackChunkName: "fb-select" */ '@/pmd/pages/FacebookPageSelect.vue'
  )
const GoogleLocationSelect = () =>
  import(
    /* webpackChunkName: "google-select" */ '@/pmd/pages/GoogleLocationSelect.vue'
  )
const DrChronoOfficeSelect = () =>
  import(
    /* webpackChunkName: "dr-chrono-office" */ '@/pmd/components/settings/DrChronoOfficeSelect.vue'
  )

// const getPage = (pageName:string) => import(/* webpackChunkName: "p-[request]" */ `@/pmd/pages/${pageName}`);
// const getAgencyPage = (pageName:string) => import(/* webpackChunkName: "agency-[request]" */ `@/pmd/pages/agency/${pageName}`);

// import TeamSettingsPage from '@/pmd/pages/settings/TeamSettingsPage.vue';
const CalendarV2Page = () =>
  import(/* webpackChunkName: "calendar-v2" */ '@/pmd/pages/CalendarV2Page.vue')
const MarketplaceFrame = () =>
  import(
    /* webpackChunkName: "calendar-v2" */ '@/pmd/pages/agency/MarketplaceFrame.vue'
  )
const Services = () =>
  import(
    /* webpackChunkName: "marketplace" */ '@/pmd/pages/agency/services/Services.vue'
  )
const ServicesList = () =>
  import(
    /* webpackChunkName: "marketplace" */ '@/pmd/pages/agency/services/ServicesList.vue'
  ).then(m => m.default)
const ServiceDetail = () =>
  import(
    /* webpackChunkName: "marketplace" */ '@/pmd/pages/agency/services/ServiceDetail.vue'
  ).then(m => m.default)

// ========================================================== //
/*** Website-specific Components: ***/
const WebsiteTemplateCategory = () =>
  import(
    /* webpackChunkName: "website-template-category" */ '@/pmd/pages/agency/WebsiteTemplateCategory.vue'
  )
const WebsiteTemplateCategoryList = () =>
  import(
    /* webpackChunkName: "website-template-cat-list" */ '@/pmd/pages/agency/WebsiteTemplateCategoryList.vue'
  )
const WebsiteTemplateCategoryDetail = () =>
  import(
    /* webpackChunkName: "website-template-cat-details" */ '@/pmd/pages/agency/WebsiteTemplateCategoryDetail.vue'
  )
const WebsiteTemplateDetail = () =>
  import(
    /* webpackChunkName: "website-template-details" */ '@/pmd/pages/agency/WebsiteTemplateDetail.vue'
  )

const ChooseWebsiteTemplateCategory = () =>
  import(
    /* webpackChunkName: "website-template-category-select" */ '@/pmd/pages/funnels/ChooseWebsiteTemplateCategory.vue'
  )
const ChooseWebsiteTemplateCategoryDetail = () =>
  import(
    /* webpackChunkName: "website-template-cat-details-select" */ '@/pmd/pages/funnels/ChooseWebsiteTemplateCategoryDetail.vue'
  )
const UserWebsiteTemplateDetail = () =>
  import(
    /* webpackChunkName: "user-website-template-details" */ '@/pmd/pages/funnels/UserWebsiteTemplateDetail.vue'
  )
const oldEmailBuilder = () =>
  import('@/pmd/pages/emailBuilder/BuilderDashboard.vue')

const workflowBuilder = () =>
  import('@/pmd/pages/workflow/WorkflowMicroApp.vue')

const membershipBuilder = () =>
  import('@/pmd/pages/membership/MembershipMicroApp.vue')

// ========================================================== //
/*** Affiliates [Partner's Program] Components: ***/
const Affiliates = () =>
  import(
    /* webpackChunkName: "partners" */ '@/pmd/pages/affiliates/Affiliates.vue'
  )
const AffiliateDetail = () =>
  import(
    /* webpackChunkName: "partners" */ '@/pmd/pages/affiliates/AffiliateDetail.vue'
  )
const AffiliateList = () =>
  import(
    /* webpackChunkName: "partners" */ '@/pmd/pages/affiliates/AffiliateList.vue'
  )
const AddAffiliate = () =>
  import(
    /* webpackChunkName: "partners-edit" */ '@/pmd/pages/affiliates/AddAffiliate.vue'
  )
const UpdateAffiliate = () =>
  import(
    /* webpackChunkName: "partners-edit" */ '@/pmd/pages/affiliates/UpdateAffiliate.vue'
  )
const AffiliateSignup = () =>
  import(
    /* webpackChunkName: "partners-signup" */ '@/pmd/pages/affiliates/AffiliateSignup.vue'
  )
const AffiliatePortal = () =>
  import(
    /* webpackChunkName: "Affiliate-Dashboard" */ '@/pmd/pages/affiliates/SideBarAffiliatePortal/AffiliatePortal.vue'
  )
const AffiliateDashboard = () =>
  import(
    /* webpackChunkName: "Affiliate-Dashboard" */ '@/pmd/pages/affiliates/SideBarAffiliatePortal/AffiliateDashboard.vue'
  )
const AffiliateAboutPage = () =>
  import(
    /* webpackChunkName: "Affiliate-Dashboard" */ '@/pmd/pages/affiliates/SideBarAffiliatePortal/AffiliateAboutPage.vue'
  )
const TipaltiPaymentDetails = () =>
  import(
    /* webpackChunkName: "Affiliate-Dashboard" */ '@/pmd/pages/affiliates/SideBarAffiliatePortal/TipaltiPayment/TipaltiPaymentDetails.vue'
  )
const TipaltiPaymentHistory = () =>
  import(
    /* webpackChunkName: "Affiliate-Dashboard" */ '@/pmd/pages/affiliates/SideBarAffiliatePortal/TipaltiPayment/TipaltiPaymentHistory.vue'
  )
const TipaltiPaymentInvoice = () =>
  import(
    /* webpackChunkName: "Affiliate-Dashboard" */ '@/pmd/pages/affiliates/SideBarAffiliatePortal/TipaltiPayment/TipaltiPaymentInvoice.vue'
  )

// ========================================================== //
/*** FB Reporting Components: ***/
const ReportingFacebookIndex = () =>
  import(
    /* webpackChunkName: "fb-reporting" */ '@/pmd/pages/reporting/facebook/ReportingFacebookIndex.vue'
  ).then(m => m.default)
const ReportingFacebookCampaigns = () =>
  import(
    /* webpackChunkName: "fb-reporting" */ '@/pmd/pages/reporting/facebook/ReportingFacebookCampaigns.vue'
  ).then(m => m.default)
const ReportingFacebookAdsets = () =>
  import(
    /* webpackChunkName: "fb-reporting" */ '@/pmd/pages/reporting/facebook/ReportingFacebookAdsets.vue'
  ).then(m => m.default)
const ReportingFacebookAds = () =>
  import(
    /* webpackChunkName: "fb-reporting" */ '@/pmd/pages/reporting/facebook/ReportingFacebookAds.vue'
  ).then(m => m.default)

const ReportingFacebookDemographics = () =>
  import(
    /* webpackChunkName: "fb-reporting" */ '@/pmd/pages/reporting/facebook/ReportingFacebookDemographics.vue'
  ).then(m => m.default)
// const FacebookKeywords = () =>
//   import('@/pmd/pages/reporting/facebook/ReportingFacebookKeywords.vue').then(m => m.default)
// const FacebookConversions = () =>
//   import('@/pmd/pages/reporting/facebook/ReportingFacebookConversions.vue').then(m => m.default)

const ReportingAttribution = () =>
  import(
    /* webpackChunkName: "fb-reporting" */ '@/pmd/pages/reporting/attribution/ReportingAttribution.vue'
  ).then(m => m.default)
const ElizaFAQSetting = () =>
  import(
    /* webpackChunkName: "bot-faq" */ '@/pmd/pages/agency/ElizaFAQSetting.vue'
  ).then(m => m.default)
const TextWidgetSettingsPage = () =>
  import(
    /* webpackChunkName: "textWidgetSetting" */ '@/pmd/pages/settings/textWidgetSetting.vue'
  )
const CompliancePage = () =>
  import(
    /* webpackChunkName: "compliancePage" */ '@/pmd/pages/settings/CompliancePage.vue'
  )
const GdprCompliance = () =>
  import(
    /* webpackChunkName: "GDPRCompliance" */ '@/pmd/pages/agency/GdprCompliance.vue'
  )
const PaymentsPage = () =>
  import(
    /* webpackChunkName: "Payments" */ '@/pmd/pages/payments/PaymentsPage.vue'
  )

const Products = () =>
  import(
    /* webpackChunkName: "Products" */ '@/pmd/pages/payments/Products.vue'
)

const ProductsPage = () =>
  import(
    /* webpackChunkName: "PaymentsPage" */ '@/pmd/pages/payments/ProductsPage.vue'
  )
const CreateProduct = () =>
  import(
    /* webpackChunkName: "CreateProduct" */ '@/pmd/pages/payments/CreateProduct.vue'
  )

const ImportStripeProduct = () => import(/* webpackChunkName: "ImportStripeProduct" */ '@/pmd/pages/payments/ImportStripeProduct.vue')

  const ProductDetails = () =>
  import(
    /* webpackChunkName: "ProductDetails" */ '@/pmd/pages/payments/ProductDetails.vue'
  )


  const Transactions = () =>
  import(
    /* webpackChunkName: "Transactions" */ '@/pmd/pages/payments/Transactions.vue'
  )

  const PaymentIntegration = () =>
  import('@/pmd/pages/payments/PaymentIntegration.vue')

  const PaymentIntegrationCallback = () =>
  import('@/pmd/pages/payments/PaymentIntegrationCallback.vue')

  const LogViewer = () =>
    import('@/pmd/pages/payments/LogViewer.vue')

  const LogsPage = () =>
  import('@/pmd/pages/payments/LogsPage.vue')

  // media files
  import MediaFilesPage  from '@/pmd/pages/medias/MediaFilesApp.vue'
  import MediaSettings  from '@/pmd/pages/medias/MediaSettings.vue'


// ========================================================== //
/*** Location Level Launchpad ***/
const LaunchpadMobileAppLink = () =>
  import(
    /* webpackChunkName: "LaunchpadMobileAppLink" */ '@/pmd/components/launchpad/LaunchpadMobileAppLink.vue'
  )
/*** Location Level User Update ***/
const LocationUserUpdateMicroApp = () =>
  import(
    /* webpackChunkName: "LocationUserUpdate" */ '@/pmd/pages/LocationUserUpdateMicroApp.vue'
  )


// access firebase perf service
const perfInstance = new Vue()

const _requireAccessLevelAndLocation = (
  to: Route,
  from: Route,
  next: (to?: RawLocation) => void
) => {
  store
    .dispatch('auth/get')
    .then(async (auth: AuthUser) => {
      const user = new User(await store.dispatch('user/get'))
      if (user.type === User.TYPE_ACCOUNT && user.role === User.ROLE_USER) {
        const locationId = first(keys(user.locations)) || ''
        return next({
          name: 'dashboard',
          replace: true,
          params: { location_id: locationId },
        })
      } else {
        next()
      }
    })
    .catch(err => {
      next({
        name: 'login',
        query: { url: encodeURIComponent(to.fullPath) },
        replace: true,
      })
    })
}

let firstLoad = true

const requireAuthAndLocation = (
  to: Route,
  from: Route,
  next: (to?: RawLocation) => void
) => {
  store
    .dispatch('auth/get')
    .then(async (auth: AuthUser) => {
      const company = await store.dispatch('company/get')
      const user = new User(await store.dispatch('user/get'))
      const getSidebarVersion = store.getters['sidebarv2/getVersion']
      if (getSidebarVersion == 'v2' && to.name && routeMapping[to.name]) {
        return next({
          name: routeMapping[to.name],
          params: to.params,
          query: to.query,
          replace: true,
        })
      }
      const permissions = user.permissions

      await inactiveAuth(to, from, next)

      const requiredPermission = routesRequiredPermissions[to.name]
      if (permissions[requiredPermission] === false) {
        if(from.path) {
          return next({
            path: from.path,
            replace: true,
          })
        } else {
          return next({
            path: '/',
            replace: true,
          })
        }
      }

      if (
        company &&
        company.onboardingInfo.pending === true &&
        company.status !== 'active_test-agency' &&
        user &&
        user.type === 'agency'
      ) {
        return next({
          name: 'onboarding_micro_app',
          replace: true,
        })
      }
      if (!to.params.location_id) {
        if (
          company.customerType === CustomerType.AGENCY &&
          user.type === User.TYPE_AGENCY
        ) {
          // if (user.role === User.ROLE_ADMIN) {
          if (
            company.saasSettings &&
            company.saasSettings.agency_dashboard_visible_to === 'individual'
              ? user.saasSettings && user.saasSettings.agency_dashboard_visible
              : user.role === User.ROLE_ADMIN
          ) {
            return next({ name: 'agency_dashboard', replace: true })
          } else {
            return next({ name: 'accounts_list', replace: true })
          }
        } else if (!isEmpty(user.locations)) {
          // This is an location level user who only has access to certain locations.
          const locationId = first(keys(user.locations)) || ''
          const locationObj = await store.dispatch(
            'locations/getById',
            locationId
          )
          if (
            Utils.launchpadDefault(
              locationObj,
              company.hideLaunchpad,
              user.loginCount
            )
          ) {
            return next({
              name: 'location_launchpad',
              replace: true,
              params: { location_id: locationId },
            })
          } else {
            return next({
              name: 'dashboard',
              replace: true,
              params: { location_id: locationId },
            })
          }
        } else {
          // Direct accounts will come into this case.
          const locations = await Location.fetchAllLocations()
          if (locations.length > 0) {
            const locationObj = await store.dispatch(
              'locations/getById',
              locations[0].id
            )
            if (Utils.launchpadDefault(
                locationObj,
                company.hideLaunchpad,
                user.loginCount
            )) {
              return next({
                name: 'location_launchpad',
                replace: true,
                params: { location_id: locations[0].id },
              })
            } else {
              return next({
                name: 'dashboard',
                replace: true,
                params: { location_id: locations[0].id },
              })
            }
          }
        }
        return
      } else {
        if (user.type === User.TYPE_ACCOUNT) {
          const availableLocations = keys(user.locations) || []
          if (availableLocations.indexOf(to.params.location_id) === -1) {
            return next({
              path: '/',
              replace: true,
            })
          }
        }
        const location = await store.dispatch(
          'locations/getById',
          to.params.location_id
        )
        if (firstLoad) {
          firstLoad = false
          // const assignedTo = user.isAssignedTo ? user.id : ''
          // console.log(`Assigned to user => ${assignedTo}`)
          await store.dispatch(
            'auth/refreshFirebaseToken',
            { locationId: to.params.location_id || null, refresh: true },
            { root: true }
          )
          await Promise.all([
            store.dispatch('locations/resetCurrentLocation', {
              locationId: to.params.location_id,
            }),
            store.dispatch('users/syncAll', to.params.location_id),
            // store.dispatch('pipelines/syncAll', to.params.location_id),
            // store.dispatch('campaigns/syncAll', to.params.location_id),
            // store.dispatch('workflows/syncAll', to.params.location_id),
            // store.dispatch('contacts/syncAll', to.params.location_id),
            // store.dispatch('oauth2/syncAll', to.params.location_id),
            // store.dispatch('contacts/syncAll', to.params.location_id),
            store.dispatch('calendars/syncAll', to.params.location_id),
            store.dispatch('userCalendars/syncAll', to.params.location_id),
            store.dispatch('linkedCalendars/syncAll', {
              userId: user.id,
              locationId: to.params.location_id,
            }),
            // store.dispatch('conversation/syncAll', {
            //   locationId: to.params.location_id,
            //   assignedTo,
            // }),
            // store.dispatch('teams/syncAll', to.params.location_id),
            store.dispatch('smtpServices/syncAll', to.params.location_id),
            store.dispatch('mailgunServices/syncAll', to.params.location_id),
            store.dispatch('stripeConnect/syncAll', to.params.location_id),
            store.dispatch('membership/syncAll', { locationId: to.params.location_id }),
          ])
          store.dispatch('filters/syncAll', to.params.location_id)

          const sideBarRoute = ['contact_detail']
          if (sideBarRoute.includes(to.name)) {
            store.commit('setSideBarState', true)
          }
        }
        const locationLevelPermissionKey = locationLevelRoutesPermissions[
          to.name
        ]
          ? locationLevelRoutesPermissions[to.name]
          : requiredPermission

        let locationHasPermission = true

        if (location.permissions) {
          if (Array.isArray(locationLevelPermissionKey)) {
            const keyPermissions = locationLevelPermissionKey.filter(
              key => location.permissions[key] === true
            )
            // location has permission for any of the key in the list for route
            locationHasPermission = keyPermissions.length > 0
          } else {
            locationHasPermission =
              location.permissions[locationLevelPermissionKey] !== false
          }
        }

        // const hasUserPermission = user.locationPermissions[location.id]
        //   ? user.locationPermissions[location.id][requiredPermission] !== false
        //   : true

        if (
          !location ||
          location.company_id !== company.id ||
          location.deleted ||
          !locationHasPermission
          // hasUserPermission === false
        ) {
          if (
            location.permissions &&
            !location.permissions[locationLevelRoutesPermissions.dashboard]
          ) {
            if (to.name === 'dashboard') {
              return next({
                name: 'conversations',
                params: to.params,
                replace: true,
              })
            } else {
              return next({
                path: from.path,
                replace: true,
              })
            }
          } else {
            // return next({
            //   path: '/',
            //   replace: true,
            // })
            return next({
              name: 'dashboard',
              replace: true,
              params: { location_id: to.params.location_id },
            })
          }
        } else if (
          user.isPasswordPending &&
          user.role == User.ROLE_USER &&
          to.name !== 'location_user_update'
        ) {
          return next({
            name: 'location_user_update',
            replace: true,
            params: { location_id: to.params.location_id },
          })
        }
      }

      next()
    })
    .catch(err => {
      next({
        name: 'login',
        query: { url: encodeURIComponent(to.fullPath) },
        replace: true,
      })
    })
}

const forceLockedTimestamp = 1607990400 // 12/15/2020 @ 12:00am (UTC)

const inactiveAuth = async (
  to: Route,
  from: Route,
  next: (to?: RawLocation) => void
) => {
  const company = await store.dispatch('company/get')
  const user = new User(await store.dispatch('user/get'))
  const lockoutSkippedTimestamp = localStorage.getItem('lockoutSkipped')

  const lockoutSkipped = lockoutSkippedTimestamp
    ? parseInt(lockoutSkippedTimestamp) + 24 * 60 * 60 * 1000 > +new Date()
    : false

  // 1. check if location-level user
  if (user.type === User.TYPE_ACCOUNT) {
    if (
      company.status &&
      company.status.includes('inactive') &&
      +new Date() > forceLockedTimestamp * 1000
    ) {
      if (to.name === 'inactive_location') {
        // to prevent infinite loop
        next()
      } else {
        next({ name: 'inactive_location', replace: true })
      }
    } else {
      // continue
      return
    }
  } else if (!lockoutSkipped) {
    // 2. Agency Level user
    // 2.1 Lock-out with `active_failed-payment` case
    if (
      company.status &&
      company.status === 'active_failed-payment' &&
      company._data &&
      company._data.date_inactivation
    ) {
      // 2.1.1 Date of inactivation must be within next 7 days.
      const msLeftForInactivation =
        company._data.date_inactivation.seconds * 1000 - +new Date()
      if (
        msLeftForInactivation > 0 &&
        msLeftForInactivation < 7 * 24 * 60 * 60 * 1000
      ) {
        // 7 days
        if (to.name === 'inactive' && to.query.type === 'failed-payment') {
          // to prevent infinite loop
          next()
        } else {
          next({
            name: 'inactive',
            replace: true,
            query: { type: 'failed-payment' },
          })
        }
      }
    }
    // 2.2 Lock-out with all other `inactive` cases
    if (company.status && company.status.includes('inactive')) {
      if (to.name === 'inactive') {
        // to prevent infinite loop
        next()
      } else {
        next({ name: 'inactive', replace: true })
      }
    } else {
      // continue
      return
    }
  }
  return
}

const requireAuth = (
  to: Route,
  from: Route,
  next: (to?: RawLocation) => void
) => {
  store
    .dispatch('auth/get')
    .then(async (auth: AuthUser) => {
      const user = new User(await store.dispatch('user/get'))

      await inactiveAuth(to, from, next)

      const company = await store.dispatch('company/get')
      if (
        company &&
        company.onboardingInfo.pending === true &&
        company.status !== 'active_test-agency' &&
        user &&
        user.type === 'agency'
      ) {
        return next({
          name: 'onboarding_micro_app',
          replace: true,
        })
      }
      if (company && company.onboardingInfo.location === true) {
        if (
          company.onboardingInfo.customerCount !==
          'I don’t have any customers yet'
        ) {
          return next({
            name: 'launchpad',
            replace: true,
          })
        } else {
          return next({
            name: 'onboarding_micro_app_location',
            replace: true,
          })
        }
      }
      if (
        company &&
        company.onboardingInfo.conversationDemo === true &&
        company.onboardingInfo.customerCount == 'I don’t have any customers yet'
      ) {
        return next({
          name: 'onboarding_micro_app_conversation',
          replace: true,
        })
      }

      if (
        user.type !== User.TYPE_AGENCY &&
        !(to.name === 'inactive_location' || to.name === 'inactive' || to.name === 'shopify_dashboard')
      ) {
        next({ name: 'dashboard', replace: true })
      } else {
        // for all agency users.
        if (to.name === 'agency_dashboard') {
          if (
            company.saasSettings &&
            company.saasSettings.agency_dashboard_visible_to === 'individual'
              ? user.saasSettings && user.saasSettings.agency_dashboard_visible
              : user.role === User.ROLE_ADMIN
          ) {
            next()
          } else {
            next({ name: 'accounts_list', replace: true })
          }
        } else {
          next()
        }
      }
    })
    .catch(err => {
      next({
        name: 'login',
        query: { url: encodeURIComponent(to.fullPath) },
        replace: true,
      })
    })
}

const requireShareAcceptAccess = (
  to: Route,
  from: Route,
  next: (to?: RawLocation) => void
) => {
  store
    .dispatch('auth/get')
    .then(async (auth: AuthUser) => {
      const user = new User(await store.dispatch('user/get'))

      const userHasAccess =
        user.type === User.TYPE_AGENCY || user.role === User.ROLE_ADMIN

      if (!userHasAccess) {
        next({ name: 'dashboard', replace: true })
      } else {
        next()
      }
    })
    .catch(err => {
      next({
        name: 'login',
        query: { url: encodeURIComponent(to.fullPath) },
        replace: true,
      })
    })
}

const requireAuthAgencyAdmin = (to: Route, from: Route, next) => {
  store
    .dispatch('auth/get')
    .then(async (auth: AuthUser) => {
      const user = new User(await store.dispatch('user/get'))
      if (user.role === 'admin') {
        await requireAuth(to,from,next)
      } else {
        next({ path: '/', replace: true })
      }
    })
    .catch(err => {
      next({ path: '/', replace: true })
    })
}

const tipaltiBetaAccess = (to: Route, from: Route, next) => {
  store
    .dispatch('auth/get')
    .then(async (auth: AuthUser) => {
      const user = new User(await store.dispatch('user/get'))
      const company = await store.dispatch('company/get')
      if (company && company.allowBetaAccess.tipalti && user && user.role === 'admin') {
         await requireAuth(to, from, next)
      }else{
        next({ path: '/', replace: true })
      }
    })
    .catch(err => {
      next({ path: '/', replace: true })
    })
}


export const pmd: RouteConfig[] = [
  {
    path: '/download/mobile_app',
    name: 'mobile_app_links',
    component: LaunchpadMobileAppLink,
  },
  {
    path: '/onboarding',
    name: 'onboarding_micro_app',
    component: OnboardingMicroApp,
  },
  {
    path: '/onboarding/address',
    name: 'onboarding_micro_app',
    component: OnboardingMicroApp,
  },
  {
    path: '/onboarding/tools',
    name: 'onboarding_micro_app',
    component: OnboardingMicroApp,
  },
  {
    path: '/onboarding/password',
    name: 'onboarding_micro_app',
    component: OnboardingMicroApp,
  },
  {
    path: '/forgot_password',
    name: 'forgot_password',
    component: ForgotPassword,
  },
  {
    path: '/onboarding/snapshot',
    name: 'onboarding_micro_app_snapshot',
    component: OnboardingMicroApp,
  },
  {
    path: '/onboarding/search',
    name: 'onboarding_micro_app_location',
    component: OnboardingMicroApp,
  },
  {
    path: '/onboarding/account',
    name: 'onboarding_micro_app_location',
    component: OnboardingMicroApp,
  },
  {
    path: '/onboarding/conversations',
    name: 'onboarding_micro_app_conversation',
    component: OnboardingMicroApp,
  },
  {
    path: '/terms',
    name: 'terms',
    component: TermsPage,
  },
  {
    path: '/gdpr',
    name: 'gdpr_compliance',
    component: GdprCompliance,
    beforeEnter: requireAuth,
  },
  {
    path: '/agency_launchpad',
    name: 'launchpad',
    component: AgencyLaunchpad,
  },
  {
    path: '',
    name: 'login',
    component: LoginWithEmail,
    beforeEnter: (to, from, next) => {
      if (to.params.logout === 'true') return next()
      store
        .dispatch('auth/get')
        .then((auth: AuthUser) => {
          next({ name: 'dashboard', replace: true })
        })
        .catch(err => {
          next()
        })
    },
  },
  {
    path: '/login/token',
    name: 'login_page',
    component: LoginWithToken,
  },
  {
    path: '/login/shopify',
    name: 'shopify_login',
    component: LoginWithShopify,
    beforeEnter: (to, from, next) => {
      if (to.params.logout === 'true') return next()
      store
        .dispatch('auth/get')
        .then((auth: AuthUser) => {
          next({
            name: 'shopify_dashboard',
            replace: true,
            query: to.query,
          })
        })
        .catch(err => {
          next()
        })
    },
  },
  {
    path: '/shopify/dashboard',
    name: 'shopify_dashboard',
    component: ShopifyLocationSelect,
    beforeEnter: requireAuth,
  },
  {
    path: '/new-agency-97',
    name: 'signup',
    component: SignUp,
    beforeEnter: (to, from, next) => {
      store
        .dispatch('auth/get')
        .then((auth: AuthUser) => {
          next({ name: 'dashboard', replace: true })
        })
        .catch(err => {
          next()
        })
    },
  },
  {
    path: '/funnels/share/:share_id',
    name: 'share_funnel',
    component: AcceptShareFunnel,
    beforeEnter: requireShareAcceptAccess,
  },
  {
    path: '/membership/products/share/:share_id',
    name: 'share_membership_product',
    component: AcceptShareMembershipProduct,
    beforeEnter: requireShareAcceptAccess,
  },
  {
    path: '/location/:location_id/workflow/:workflow_id*',
    component: workflowBuilder,
    name: 'editWorkflow',
    beforeEnter: requireAuthAndLocation,
  },
  {
    path: '/location/:location_id',
    component: LocationParent,
    beforeEnter: requireAuthAndLocation,
    children: [
      {
        path: 'user-update',
        name: 'location_user_update',
        component: LocationUserUpdateMicroApp,
      },
      {
        path: '',
        name: 'dashboard_default',
        component: DashboardPage,
      },
      {
        path: 'reporting/adwords',
        name: 'reporting_adwords',
        component: ReportingAdwordsIndex,
        children: [
          {
            path: 'campaigns',
            name: 'reporting_adwords_campaigns',
            component: AdwordsCampaigns,
          },
          {
            path: 'adgroups',
            name: 'reporting_adwords_adgroups',
            component: AdwordsAdgroups,
          },
          {
            path: 'ads',
            name: 'reporting_adwords_ads',
            component: AdwordsAds,
          },
          {
            path: 'keywords',
            name: 'reporting_adwords_keywords',
            component: AdwordsKeywords,
          },
          {
            path: 'conversions/:report_type',
            name: 'reporting_adwords_conversions',
            component: AdwordsConversions,
          },
        ],
      },
      {
        path: 'reporting/facebook',
        name: 'reporting_facebook',
        component: ReportingFacebookIndex,
        children: [
          {
            path: 'campaigns',
            name: 'reporting_facebook_campaigns',
            component: ReportingFacebookCampaigns,
          },
          {
            path: 'adsets',
            name: 'reporting_facebook_adsets',
            component: ReportingFacebookAdsets,
          },
          {
            path: 'ads',
            name: 'reporting_facebook_ads',
            component: ReportingFacebookAds,
          },
          {
            path: 'demographics',
            name: 'reporting_facebook_demographics',
            component: ReportingFacebookDemographics,
          },
          // {
          //   path: 'conversions/:report_type',
          //   name: 'reporting_adwords_conversions',
          //   component: AdwordsConversions,
          // }
        ],
      },
      {
        path: 'reporting/attribution',
        name: 'reporting_attribution',
        component: ReportingAttribution,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'reporting/appointments-reports',
        name: 'appointments-reports',
        component: AppointmentsReportPage,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'call_stats',
        name: 'call_stats',
        component: NumberPoolDashboard,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'agent_reporting',
        name: 'agent_reporting',
        component: AgentReporting,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'dashboard',
        name: 'dashboard',
        component: DashboardPage,
      },
      {
        path: 'launchpad',
        name: 'location_launchpad',
        component: LaunchpadMicroApp,
      },
      {
        path: 'scheduling/appointments',
        name: 'appointments',
        component: AppointmentsPage,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'scheduling/appointments/reports',
        redirect: {
          name: 'appointments-reports',
        },
      },
      // {
      // 	path: 'scheduling/calendar',
      // 	name: 'calendar',
      // 	component: CalendarPage,
      // },
      {
        path: 'scheduling/calendar',
        name: 'calendar-v2',
        component: CalendarV2Page,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'conversations/:conversation_id?',
        name: 'conversations',
        component: ConversationsPage,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'reminders',
        name: 'reminders',
        component: RemindersPage,
      },
      /*       {
        path: 'reputation/reviews/:review_id?',
        name: 'reviews',
        component: ReviewsPage,
        beforeEnter: requireAuthAndLocation,
      },  */

      /*   {
        path: 'reputation/review_requests',
        name: 'reviews_requests',
        redirect: 'reputation/reviews_requests'
      }, */
      {
        path: 'opportunities',
        name: 'opportunities',
        component: OpportunitiesPage,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'chat_widget_settings',
        name: 'chat_widget_settings',
        component: TextWidgetSettingsPage,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'analysis',
        name: 'analysis',
        component: AnalysisPage,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'customers',
        name: 'customers_contact',
        component: EmptyRouterParent,
        children: [
          {
            path: 'bulk',
            component: CustomerImportsPage,
            children: [
              {
                path: 'imports',
                name: 'bulk_imports_log',
                component: BulkRequest,
                beforeEnter: requireAuthAndLocation,
              },
              {
                path: 'actions',
                name: 'bulk_actions_log',
                component: BulkActionsView,
                beforeEnter: requireAuthAndLocation,
              },
            ],
          },
          {
            path: 'bulk',
            name: 'customer_bulk_actions',
            component: CustomerImportsPage,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'bulkactions',
            name: 'bulk_actions',
            component: BulkActionsView,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'contacts',
            name: 'contacts',
            component: CustomersPage,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'manual_actions',
            name: 'manual_actions',
            component: ManualActionList,
            beforeEnter: requireAuthAndLocation,
          },
          // {
          //   path: 'smart_lists',
          //   name: 'smart_lists',
          //   component: SmartList,
          //   beforeEnter: requireAuthAndLocation
          // },
          {
            path: 'smart_list/contact_requests_old',
            name: 'contact_requests_old',
            component: BulkRequest,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'smart_list/bulk_actions_legacy',
            name: 'smart_list_legacy',
            component: BulkActionsPage,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'smart_list/:smart_list',
            name: 'smart_list',
            component: SmartList,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'import_filter/:import_filter',
            name: 'import_filter',
            component: SmartList,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'business_name/:business_name',
            name: 'business_name',
            component: SmartList,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'leads',
            name: 'leads',
            component: LeadsPage,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'detail/:contact_id',
            name: 'contact_detail',
            component: CustomerDetailNew,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'restore',
            name: 'contact_restore',
            component: CustomerRestore,
            beforeEnter: requireAuthAndLocation,
          },
        ],
      },
      {
        path: 'social_media',
        name: 'social_media',
        component: SocialMediaDashboard,
      },
      {
        path: 'membership',
        component: membershipBuilder,
        beforeEnter: requireAuthAndLocation,
        children: [
          {
            path: 'dashboard',
            name: 'membership',
            component: membershipBuilder,
          },
          {
            path: 'product_blueprints',
            name: 'membership-product-blueprints',
            component: membershipBuilder,
          },
          {
            path: 'products',
            name: 'membership-products',
            component: membershipBuilder,
            children: [
              {
                path: 'library-order',
                name: 'membership-library-order',
                component: membershipBuilder,
              },
              {
                path: '*',
                component: membershipBuilder,
              },
            ],
          },
          {
            path: 'offers',
            name: 'membership-offers',
            component: membershipBuilder,
            children: [
              {
                path: '*',
                component: membershipBuilder,
              },
            ],
          },
          {
            path: 'analytics',
            name: 'membership-analytics',
            component: membershipBuilder,
            children: [
              {
                path: 'product-progress',
                name: 'product-progress',
                component: membershipBuilder,
                children: [
                  {
                    path: '*',
                    component: membershipBuilder,
                  },
                ],
              },
            ],
          },
          {
            path: 'settings',
            name: 'membership-settings',
            component: membershipBuilder,
            children: [
              {
                path: 'site-details',
                name: 'membership-site-details',
                component: membershipBuilder,
              },
              {
                path: 'custom-domain',
                name: 'membership-custom-domain',
                component: membershipBuilder,
              },
              {
                path: 'email-settings',
                name: 'membership-email-settings',
                component: membershipBuilder,
              },
            ],
          },
          {
            path: 'categories',
            name: 'membership-categories',
            component: membershipBuilder,
            children: [
              {
                path: '*',
                component: membershipBuilder,
              },
            ],
          },
          {
            path: 'posts',
            name: 'membership-posts',
            component: membershipBuilder,
            children: [
              {
                path: '*',
                component: membershipBuilder,
              },
            ],
          },
          {
            path: 'themes',
            name: 'membership-themes',
            component: membershipBuilder,
            children: [
              {
                path: '*',
                component: membershipBuilder,
              },
            ],
          },
        ],
      },
      {
        path: 'wordpress',
        name: 'wordpress_dashboard',
        component: WordpressDashboard,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'funnels-websites',
        name: '',
        component: FunnelsPage,
        beforeEnter: requireAuthAndLocation,
        children: [
          {
            path: 'funnels',
            name: 'all_funnels',
            component: FunnelsList,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'websites',
            name: 'websites_funnels',
            component: FunnelsList,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'favorites',
            name: 'fav_funnels',
            component: FunnelsList,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'recent',
            name: 'recent_funnels',
            component: FunnelsList,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'archived',
            name: 'archived_funnels',
            component: FunnelsList,
            beforeEnter: requireAuthAndLocation,
          },
        ],
      },
      {
        path: 'funnels-websites/funnels/:funnel_id',
        name: 'funnels_detail',
        component: FunnelDetailPage,
        beforeEnter: requireAuthAndLocation,
        children: [
          {
            path: 'steps/:step_id?',
            name: 'funnel_steps',
            component: FunnelSteps,
            beforeEnter: requireAuthAndLocation,
            children: [
              {
                path: 'overview',
                name: 'funnel_step_overview',
                component: FunnelStepOverview,
              },
              {
                path: 'automation',
                name: 'funnel_step_automation',
                component: FunnelStepAutomation,
              },
              {
                path: 'publish',
                name: 'funnel_step_publish',
                component: FunnelStepPublish,
              },
              {
                path: 'product',
                name: 'funnel_step_product',
                component: FunnelStepProduct,
              },
              {
                path: 'products',
                name: 'funnel_step_products',
                component: FunnelProductsList,
              },
              {
                path: 'products/create',
                name: 'funnel_step_product_v2',
                component: FunnelStepProductV2,
              },
              {
                path: 'products/:funnel_product_id',
                name: 'funnel_step_product_v2_edit',
                component: FunnelStepProductV2,
              },
            ],
          },
          {
            path: 'stats',
            name: 'funnel_stats',
            component: FunnelStats,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'sales',
            name: 'funnel_sales',
            component: FunnelSales,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'settings',
            name: 'funnel_settings',
            component: FunnelSettings,
            beforeEnter: requireAuthAndLocation,
          },
        ],
      },
      {
        path: 'funnels-websites/websites/:funnel_id',
        name: 'websites_detail',
        component: FunnelDetailPage,
        beforeEnter: requireAuthAndLocation,
        children: [
          {
            path: 'pages/:step_id?',
            name: 'website_pages',
            component: FunnelSteps,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'stats',
            name: 'website_stats',
            component: FunnelStats,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'sales',
            name: 'website_sales',
            component: FunnelSales,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'settings',
            name: 'website_settings',
            component: FunnelSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'products/:step_id',
            name: 'website_products',
            component: WebsiteProducts,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'website-products/:step_id',
            name: 'website_step_products',
            component: FunnelProductsList,
          },
          {
            path: 'products/create',
            name: 'website_step_product_v2',
            component: FunnelStepProductV2,
          },
          {
            path: 'products/:funnel_product_id',
            name: 'website_step_product_v2_edit',
            component: FunnelStepProductV2,
          },
        ],
      },
      {
        path: 'funnel-templates',
        name: 'funnel_templates_category',
        component: ChooseWebsiteTemplateCategory,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'website-templates',
        name: 'website_templates_category',
        component: ChooseWebsiteTemplateCategory,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'funnel-templates/:id',
        name: 'funnel_templates_category_detail',
        component: ChooseWebsiteTemplateCategoryDetail,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'website-templates/:id',
        name: 'website_templates_category_detail',
        component: ChooseWebsiteTemplateCategoryDetail,
        beforeEnter: requireAuthAndLocation,
        // children: [
        //   {
        //     path: 'templates/:template_id',
        //     name: 'website_templates_detail',
        //     component: UserWebsiteTemplateDetail
        //   }
        // ]
      },
      {
        path: 'funnel-templates/:id/templates/:template_id',
        name: 'funnel_templates_detail',
        component: UserWebsiteTemplateDetail,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'website-templates/:id/templates/:template_id',
        name: 'website_templates_detail',
        component: UserWebsiteTemplateDetail,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'triggers',
        name: 'triggers',
        component: TriggersPage,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'reputation',
        component: ReputationLandingPage,
        beforeEnter: requireAuthAndLocation,
        children: [
          {
            path: '',
            name: 'reputation',
            redirect: 'overview',
          },
          {
            path: 'overview',
            name: 'reputation_overview',
            beforeEnter: requireAuthAndLocation,
            component: ReputationOverviewPage,
          },

          {
            path: 'requests',
            name: 'reputation_review_requests',
            beforeEnter: requireAuthAndLocation,
            component: ReviewRequestsAllPage,
          },
          ,
          {
            path: 'reviews/:review_id?',
            name: 'reputation_reviews',
            beforeEnter: requireAuthAndLocation,
            component: ReviewsAllPage,
          },
          {
            path: 'settings',
            name: 'reputation_settings',
            beforeEnter: requireAuthAndLocation,
            component: ReputationSetingsPage,
          },
        ],
      },
      {
        path: 'listing',
        name: 'reputation_listing',
        beforeEnter: requireAuthAndLocation,
        component: ReputationOnlineListing,
      },
      {
        path: 'new-workflow',
        component: workflowBuilder,
        name: 'newWorkflow',
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'workflows',
        name: 'workflows',
        component: workflowBuilder,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'triggers/:trigger_id',
        name: 'triggers_detail',
        component: TriggerDetailPage,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'marketing',
        component: EmptyRouterParent,
        children: [
          {
            path: 'acquisition',
            name: 'customer_acquisition',
            component: CampaignList,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'campaign/:campaign_id',
            name: 'campaign_edit',
            component: CampaignNew,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'campaign/:campaign_id/detail',
            name: 'campaign_detail',
            component: CampaignDetail,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'campaign/:campaign_id/list',
            name: 'campaign_accounts_list',
            component: CampaignAccountsList,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'activity',
            name: 'campaign_activity',
            component: CampaignActivity,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'bulk',
            name: 'bulk_requests',
            component: BulkCampaignsPage,
            beforeEnter: requireAuthAndLocation,
          },

          // {
          // 	path: 'workflows',
          // 	name: 'workflows',
          // 	component: WorkflowList,
          // },
          // {
          // 	path: 'workflow/:workflow_id?',
          // 	name: 'workflow_edit',
          // 	component: WorkflowNew,
          // },
          {
            path: 'links',
            name: 'links',
            component: LinksPage,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'form-builder',
            name: 'form_builder',
            redirect: to => {
              return { name: 'form_builder_main' }
            },
            component: FormBuilderPage,
            beforeEnter: requireAuthAndLocation,
            children: [
              {
                path: 'main',
                name: 'form_builder_main',
                component: FormBuilderPage,
                beforeEnter: requireAuthAndLocation,
              },
              {
                path: 'analyze',
                name: 'form_builder_analyze',
                component: FormBuilderPage,
                beforeEnter: requireAuthAndLocation,
              },
              {
                path: 'submissions',
                name: 'form_builder_submissions',
                component: FormBuilderPage,
                beforeEnter: requireAuthAndLocation,
              },
            ],
          },
          // old builder
          {
            path: 'email-builder',
            name: 'email_builder_list',
            component: EmailBuilderPage,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'email-builder/:builder_id',
            name: 'email_builder_dashboard',
            component: oldEmailBuilder,
            beforeEnter: requireAuthAndLocation,

            // beforeEnter: requireAuth
          },
          {
            path: 'emails/:path',
            name: 'emails_home',
            component: Emails,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'emails/detail/:path',
            name: 'emails_home',
            component: Emails,
            beforeEnter: requireAuthAndLocation,
          },

          {
            path: 'form/:builder_id',
            name: 'form_builder_manage',
            component: BuilderCreateForm,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'formold/:builder_id',
            name: 'form_builder_manage_old',
            component: BuilderCreateFormOld,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'form/:builder_id?/integrate',
            name: 'form_builder_integrate',
            component: EmbedBuilder,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'survey-builder',
            redirect: to => {
              return { name: 'survey_builder_main' }
            },
            name: 'survey_builder',
            component: SurveyList,
            beforeEnter: requireAuthAndLocation,
            children: [
              {
                path: 'main',
                name: 'survey_builder_main',
                component: SurveyList,
                beforeEnter: requireAuthAndLocation,
              },
              {
                path: 'analyze',
                name: 'survey_builder_analyze',
                component: SurveyList,
                beforeEnter: requireAuthAndLocation,
              },
              {
                path: 'submissions',
                name: 'survey_builder_submissions',
                component: SurveyList,
                beforeEnter: requireAuthAndLocation,
              },
            ],
          },
          {
            path: 'survey/:survey_id',
            name: 'survey_manage',
            component: BuilderCreateSurvey,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'survey/:survey_id?/integrate',
            name: 'survey_integrate',
            component: EmbedSurvey,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'text-templates',
            name: 'text_templates',
            component: TextBuilder,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'jotform/:builder_id',
            name: 'jotbox_form',
            component: JotboxForm,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'jotformsurvey/:survey_id',
            name: 'jotbox_survey',
            component: JotboxSurvey,
            beforeEnter: requireAuthAndLocation,
          },
        ],
      },
      {
        path: 'settings',
        component: SettingsPage,
        beforeEnter: requireAuthAndLocation,
        children: [
          {
            path: '',
            name: 'settings',
            component: ProfileSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'profile',
            name: 'profile',
            component: ProfileSettingsV3,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'company',
            name: 'company_settings',
            component: CompanySettingsPage,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'billing',
            name: 'company_billing-v2',
            beforeEnter: requireAuthAndLocation,
            component: LocationBillingPage,
            children: [
              {
                path: '',
                name: 'location_billing',
                component: LocationBillingSettings,
              },
              {
                path: 'transactions',
                name: 'location_wallet_transactions',
                component: LocationWalletTransactions,
              },
            ],
          },
          {
            path: 'email',
            name: 'email_settings',
            redirect: 'reputation/settings',
          },
          {
            path: 'sms',
            name: 'sms_settings',
            redirect: 'reputation/settings',
          },
          {
            path: 'noshow_sms',
            name: 'no_show_sms_settings',
            component: NoShowSMSSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'birthday_sms',
            name: 'birthday_sms_settings',
            component: BirthdaySMSSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'integrations',
            name: 'integrations_settings',
            component: IntegrationSettingsPage,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'social_posting',
            name: 'social_posting',
            component: SocialPostingSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'review_widgets',
            name: 'review_widgets',
            redirect: 'reputation/settings',
          },
          {
            path: 'review_popup',
            name: 'review_popup',
            component: ReviewPopupSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'pipeline',
            name: 'pipeline_settings',
            component: PipelineSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'payment',
            name: 'payment_settings',
            component: PaymentSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'chat_widget',
            name: 'chat_widget',
            component: ChatWidgetSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'appointment_widget',
            name: 'appointment_widget',
            component: AppointmentWidgetSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'phone_number',
            name: 'phone_number',
            component: AllPhoneNumbersSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'templates',
            name: 'template_settings',
            component: TemplateSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'team',
            name: 'location_team_management',
            component: AccountTeamManagement,
            beforeEnter: _requireAccessLevelAndLocation,
          },
          {
            path: 'teams',
            name: 'location_teams_management',
            component: AccountTeamsManagement,
            beforeEnter: _requireAccessLevelAndLocation,
          },
          {
            path: 'custom_fields',
            name: 'custom_fields_settings',
            component: AccountCustomFieldsSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'custom_values',
            name: 'custom_values_settings',
            component: CustomValues,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'calendar_settings',
            name: 'calendar_settings',
            component: CalendarSettings,
            beforeEnter: requireAuthAndLocation,
          },
          // {
          //   path: 'bulk_email_settings',
          //   name: 'bulk_email_settings',
          //   component: BulkEmailSettings,
          //   beforeEnter: requireAuthAndLocation
          // },
          {
            path: 'loc_mail_gun_settings',
            name: 'loc_mail_gun_settings',
            component: LocationMailGunSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'facebook',
            name: 'facebook_settings',
            component: FacebookAdFieldsMappingSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'domain',
            name: 'domain_settings',
            component: DomainSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'redirect',
            name: 'redirect_settings',
            component: RedirectSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'tags',
            name: 'tags_settings',
            component: TagsSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'smtp_service',
            name: 'smtp_service',
            component: SMTPServiceSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'eliza_faq',
            name: 'eliza_faq',
            component: ElizaFAQSetting,
            beforeEnter: async (to, from, next) => {
              const location = await Location.getById(to.params.location_id)
              store
                .dispatch('auth/get')
                .then((auth: AuthUser) => {
                  if (location && !location.botService)
                    next({
                      name: 'dashboard',
                      replace: true,
                      params: { location_id: to.params.location_id },
                    })
                  next()
                })
                .catch(err => {
                  next()
                })
            },
          },
          {
            path: 'payments/products',
            component: Products,
            beforeEnter: requireAuthAndLocation,
            children: [
              {
                path: 'create',
                name: 'product-create',
                component: CreateProduct,
                beforeEnter: requireAuthAndLocation,
              },
              {
                path: 'stripe/import',
                name: 'import-stripe-product',
                component: ImportStripeProduct,
                beforeEnter: requireAuthAndLocation,
              },
              {
                path: ':product_id',
                name: 'product-details',
                component: ProductDetails,
                beforeEnter: requireAuthAndLocation,
              },
              {
                path: ':product_id/edit',
                name: 'product-edit',
                component: CreateProduct,
                beforeEnter: requireAuthAndLocation,
              },
            ],
          },
          {
            path: 'payments',
            component: PaymentsPage,
            beforeEnter: requireAuthAndLocation,
            children: [
              {
                path: '/',
                name: 'product-list',
                component: ProductsPage,
                beforeEnter: requireAuthAndLocation,
              },
              {
                path: 'transactions',
                name: 'transactions',
                component: Transactions,
                beforeEnter: requireAuthAndLocation,
              },
              {
                path: 'integrations',
                name: 'payment-integrations',
                component: PaymentIntegration,
                beforeEnter: requireAuthAndLocation,
              },
              {
                path: 'integrations/:integration/callback',
                name: 'payment-integrations-callback',
                component: PaymentIntegrationCallback,
                beforeEnter: requireAuthAndLocation,
              },
            ],
          },
          {
            path: 'media-settings',
            name: 'media-settings',
            component: MediaSettings,
            beforeEnter: requireAuthAndLocation,
          },

          /*{
            path: 'attribution',
            name: 'attribution',
            component: AttributionSettings,
            beforeEnter: requireAuthAndLocation
          }*/

          {
            path: 'reputation',
            name: 'settings_reputation',
            component: ReputationLandingPage,
            beforeEnter: requireAuthAndLocation,
            children: [
              {
                path: 'settings',
                name: 'settings_reputation_settings',
                component: ReputationSetingsPage,
              },
            ],
          },
        ],
      },
      {
        path: 'logs',
        name: 'log-viewer',
        component: LogsPage,
        beforeEnter: requireAuthAndLocation,
        props: route => {
          status: route.query.status
        },
      },
      {
        path: 'logs/:request_id',
        name: 'log-viewer-detail',
        component: LogViewer,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'notifications/:notification_id?',
        name: 'notifications',
        component: NotificationsPage,
      },
      {
        path: 'notificationslist',
        name: 'notificationslist',
        component: NotificationsList,
      },
      {
        path: 'custom-menu-link/:id',
        name: 'location_custom_menu_link',
        component: CustomMenuLinkPage,
      },
    ],
  },
  {
    path: '/location/:location_id',
    component: EmptyLocationParent,
    // beforeEnter: requireAuthAndLocation,
    children: [
      {
        path: 'facebook_page_select',
        name: 'facebook_page_select',
        component: FacebookPageSelect,
      },
      {
        path: 'google_location_select',
        name: 'google_location_select',
        component: GoogleLocationSelect,
      },
      {
        path: 'dr_chrono_office_select',
        name: 'dr_chrono_office_select',
        component: DrChronoOfficeSelect,
      },
    ],
  },
  {
    path: '/scan_report/:scan_report_id',
    name: 'scan report',
    component: ScanReport,
    beforeEnter: requireAuth,
  },
  {
    path: '/saas_dashboard',
    name: 'saas_dashboard',
    component: SaasDashboard,
    beforeEnter: requireAuth,
  },
  {
    path: '/agency_dashboard',
    name: 'agency_dashboard',
    component: AgencyDashboard,
    beforeEnter: requireAuth,
  },
  {
    path: '/saas_education',
    name: 'saas_education',
    component: SaasEducation,
    beforeEnter: requireAuth,
  },
  {
    path: '/saas_fasttrack',
    name: 'saas_fasttrack',
    component: SaasFasttrack,
    beforeEnter: requireAuth,
  },
  {
    path: '/accounts',
    component: Account,
    name: 'accounts',
    beforeEnter: requireAuth,
    children: [
      {
        path: 'add/:id?',
        name: 'account_add',
        component: AddAccount,
        beforeEnter: requireAuth,
      },
      {
        path: 'search',
        name: 'account_add_map',
        component: AddAccountMap,
        beforeEnter: requireAuth,
      },
      {
        path: '',
        name: 'accounts_list',
        component: ListAccounts,
        beforeEnter: requireAuth,
      },
      {
        path: 'detail/:account_id',
        name: 'account_detail',
        component: AccountDetail,
        beforeEnter: requireAuth,
      },
    ],
  },
  {
    path: '/snapshots',
    name: 'account_add_template',
    component: AddAccountTemplate,
    beforeEnter: requireAuth,
  },
  {
    path: '/website-template-category',
    name: 'website_template_category',
    component: WebsiteTemplateCategory,
    beforeEnter: requireAuth,
    children: [
      {
        path: ':type',
        name: 'website_template_category_list',
        component: WebsiteTemplateCategoryList,
        beforeEnter: requireAuth,
      },
      {
        path: ':type/:id',
        name: 'website_template_category_detail',
        component: WebsiteTemplateCategoryDetail,
        beforeEnter: requireAuth,
      },
      {
        path: ':type/:id/template/:template_id',
        name: 'website_template_detail',
        component: WebsiteTemplateDetail,
        beforeEnter: requireAuth,
      },
    ],
  },
  {
    path: '/reselling',
    name: 'reselling',
    component: AgencyReselling,
    beforeEnter: requireAuth,
  },
  {
    path: '/marketplace',
    component: MarketplaceFrame,
    name: 'marketplace-frame',
    beforeEnter: requireAuth,
  },
  {
    path: '/affiliate_portal',
    component: AffiliatePortal,
    name: 'affiliate-portal',
    beforeEnter: requireAuthAgencyAdmin,
    children: [
      {
        path: '',
        name: 'affiliate-dashboard-frame',
        component: AffiliateDashboard,
      },
      {
        path: 'about',
        name: 'affiliate-about-frame',
        component: AffiliateAboutPage,
      },
      {
        path: 'tipalti/payment-details',
        name: 'affiliate-tipalti-payment-details-frame',
        beforeEnter: tipaltiBetaAccess,
        component: TipaltiPaymentDetails,
      },
      {
        path: 'tipalti/payment-history',
        name: 'affiliate-tipalti-payment-history-frame',
        beforeEnter: tipaltiBetaAccess,
        component: TipaltiPaymentHistory,
      },
      {
        path: 'tipalti/invoice-history',
        name: 'affiliate-tipalti-invoice-history-frame',
        beforeEnter: tipaltiBetaAccess,
        component: TipaltiPaymentInvoice,
      },
    ],
  },
  // {
  //   path: '/marketplace-v1',
  //   component: Services,
  //   beforeEnter: requireAuth,
  //   children: [
  //     {
  //       path: '',
  //       name: 'agency_services',
  //       component: ServicesList,
  //       beforeEnter: requireAuth,
  //     },
  //     {
  //       path: 'detail/:service_id',
  //       name: 'service_detail',
  //       component: ServiceDetail,
  //       beforeEnter: requireAuth,
  //     },
  //   ],
  // },
  {
    path: '/partners',
    name: 'partners',
    component: Affiliates,
    beforeEnter: requireAuth,
    children: [
      {
        path: '',
        name: 'affiliates',
        component: AffiliateSignup,
        beforeEnter: requireAuth,
      },
      {
        path: 'list',
        name: 'affiliates_list',
        component: AffiliateList,
        beforeEnter: requireAuth,
      },
      {
        path: 'add',
        name: 'add_affiliate',
        component: AddAffiliate,
        beforeEnter: (to, from, next) => {
          // @ts-ignore
          if (
            ['YuTUZlUtrwBtvmgByZDW', '5DP4iH6HLkQsiKESj6rh'].indexOf(
              store.state.user.user.company_id
            ) !== -1
          ) {
            next()
          } else {
            next({ name: 'account_list' })
          }
        },
      },
      {
        path: 'update/:id',
        name: 'update_affiliate',
        component: UpdateAffiliate,
        beforeEnter: (to, from, next) => {
          if (
            ['YuTUZlUtrwBtvmgByZDW', '5DP4iH6HLkQsiKESj6rh'].indexOf(
              store.state.user.user.company_id
            ) !== -1
          ) {
            next()
          } else {
            next({ name: 'account_list' })
          }
        },
      },
      {
        path: 'detail/:affiliate_id',
        name: 'affiliate_detail',
        component: AffiliateDetail,
        beforeEnter: requireAuth,
      },
    ],
  },
  {
    path: '/university',
    name: 'university',
    component: {},
    meta: {
      RedirectExternalUrl:
        'https://university.gohighlevel.com?loginCode=UP$D6e',
    },
  },
  {
    // This seems depreciated
    path: '/dashboard',
    name: 'agency_dashboard',
    component: AccountsDashboard,
    beforeEnter: requireAuth,
  },
  {
    path: '/location/:location_id/emails/create/:template_id/:path',
    name: 'email_edit',
    component: EmailBuilderMicroApp,
    beforeEnter: requireAuthAndLocation,
  },
  {
    path: '/location/:location_id/medias',
    name: 'medias',
    component: MediaFilesPage,
  },
  {
    path: '/resources',
    name: 'agency_resources',
    component: AccountResources,
    beforeEnter: requireAuth,
  },
  {
    path: '/ideas',
    name: 'agency_ideas',
    component: AgencyIdeas,
    beforeEnter: requireAuth,
  },
  {
    path: '/help',
    name: 'help',
    component: HelpDocuments,
    beforeEnter: requireAuth,
  },
  {
    path: '/custom-menu-link/:id',
    name: 'custom_menu_link',
    component: CustomMenuLinkPage,
  },

  {
    path: '/inactive',
    name: 'inactive',
    component: InactiveAgency,
    beforeEnter: requireAuth,
  },
  {
    path: '/locked',
    name: 'inactive_location',
    component: InactiveLocation,
    beforeEnter: requireAuth,
  },
  {
    path: '/settings',
    name: 'agency_settings',
    component: AccountSettings,
    beforeEnter: requireAuth,
    children: [
      {
        path: 'profile',
        name: 'agency_profile_settings-v2',
        component: ProfileSettings,
      },
      {
        path: '',
        name: 'agency_profile_settings',
        component: ProfileSettings,
      },
      {
        path: 'company',
        name: 'agency_company_settings',
        component: AccountCompanySettings,
      },
      {
        path: 'team',
        name: 'account_team_management',
        component: AccountTeamManagement,
      },
      {
        path: 'billing',
        component: AccountBillingParent,
        children: [
          {
            path: '',
            name: 'account_billing',
            component: AccountBilling,
          },
          {
            path: 'transactions',
            name: 'account_wallet_transactions',
            component: AgencyWalletTransactions,
          },
        ],
      },
      {
        path: 'snapshot',
        name: 'snapshot_settings',
        component: EmptyRouterParent,
        children: [
          {
            path: 'import/:share_id',
            name: 'import_account_template',
            component: ImportAccountTemplate,
          },
          {
            path: 'list/:type?',
            name: 'snapshot',
            component: CloneAccountList,
          },
        ],
      },
      // {
      //     path: 'integrations',
      //     name: 'account_integrations',
      //     component: IntegrationSettingsPage,
      // },
      {
        path: 'twilio',
        name: 'twilio_settings',
        component: AccountTwilioSettings,
      },
      {
        path: 'sendgrid',
        name: 'sendgrid_settings',
        component: AccountSendGridSettings,
      },
      {
        path: 'mailgun',
        name: 'mailgun_settings',
        component: AccountMailGunSettings,
      },
      {
        path: 'agency_smtp_service',
        name: 'smtp_service_settings',
        component: SMTPServiceSettings,
      },
      {
        path: 'affiliate',
        name: 'affiliate_settings',
        component: AccountAffiliateSettings,
      },
      {
        path: 'custom_menu_link',
        name: 'custom_menu_link_settings',
        component: CustomMenuLinkSettings,
      },
      {
        path: 'stripe',
        name: 'stripe_settings',
        component: AgencyStripeSettings,
      },
      {
        path: 'whitelabel',
        name: 'whitelabel_settings',
        component: AccountWhiteLabelSettings,
      },
      {
        path: 'api_key',
        name: 'api_key',
        component: AgencyAPIKeySettings,
      },
      {
        path: 'compliance',
        name: 'compliance',
        component: CompliancePage,
      },
      {
        path: 'launchpad',
        name: 'launchpad_settings',
        component: AgencyLaunchpadSettings,
      },
    ],
  },
  // {
  //   path: '*',
  //   redirect: '/'
  // }
]
