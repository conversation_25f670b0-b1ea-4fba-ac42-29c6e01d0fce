<template>
  <!-- Edit Event Modal -->
  <div
    class="modal fade hl_edit-event-modal"
    id="edit-event-modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog --mid" role="document">
      <div class="modal-content">
        <button
          type="button"
          class="close"
          data-dismiss="modal"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
        <div class="modal-body">
          <div class="modal-body-inner">
            <div class="hl_edit-event-modal-wrap">
              <div class="hl_edit-event-modal-main">
                <div class="card">
                  <div class="card-header --no-right-padding">
                    <h2>Add Task</h2>
                  </div>
                  <div class="card-body hl_campaign-configuration">
                    <div class="form-group">
                      <label>Title*</label>
                      <div style="width: inherit;">
                        <UITextInputGroup
                          type="text"
                          placeholder="Task title"
                          v-model="name"
                          v-validate="'required|handlebars'"
                          name="taskTitle"
                          ref="title"
                          data-vv-validate-on="input"
                          data-vv-as="title"
                          @keyup="edited = true"
                          :error="errors.has('taskTitle')"
                          :errorMsg="errors.first('taskTitle')"
                        />
                      </div>
                    </div>
                    <div class="form-group">
                        <label>Description*</label>
                        <div style="width: inherit;">
                          <UITextAreaGroup
                            rows="5"
                            placeholder="Task description"
                            v-model="taskBody"
                            v-validate="'required|handlebars'"
                            data-vv-validate-on="input"
                            data-vv-as="description"
                            name="taskBody"
                            @keyup="edited = true"
                            :error="errors.has('taskBody')"
                            :errorMsg="errors.first('taskBody')"
                          ></UITextAreaGroup>
                        </div>
                    </div>
                    <!-- <div class="form-group">
                        <label>Assign to</label>
                        <select
                          class="selectpicker"
                          title="Select"
                          v-model="assignTo"
                          name="assignTo"
                          @change="edited=true"
                        >
                          <option value>Not assigned</option>
                          <option v-for="user in users" :value="user.id">{{user.fullName}}</option>
                        </select>
                        <span v-show="errors.has('assignTo')" class="error">Task needs to be assigned to a user</span>
                      </div> -->
                    <div class="form-group">
                      <label>Due in*</label>
                      <div class="form-input-dropdown dropdown">
                        <div data-toggle="dropdown">
                          <i class="icon icon-arrow-down-1"></i>
                          <input
                            type="text"
                            class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800 msgsndr15"
                            placeholder="Due in"
                            v-model="dueDate"
                            v-validate="'required|regex:^[0-9][a-zA-Z0-9 ]*$'"
                            name="msgsndr15"
                            data-lpignore="true"
                            autocomplete="msgsndr15"
                            data-vv-validate-on="change|input|blur"
                            ref="dueDate"
                          />
                          <span v-show="errors.has('msgsndr15')" class="error"
                            >A Valid Due Date is required</span
                          >
                        </div>
                        <div class="dropdown-menu">
                          <a
                            class="dropdown-item trigger-type"
                            href="javascript:void(0);"
                            @click.prevent="dueDate = '1 day'"
                          >
                            <p>1 day</p>
                          </a>
                          <a
                            class="dropdown-item trigger-type"
                            href="javascript:void(0);"
                            @click.prevent="dueDate = '2 days'"
                          >
                            <p>2 days</p>
                          </a>
                          <a
                            class="dropdown-item trigger-type"
                            href="javascript:void(0);"
                            @click.prevent="dueDate = '3 days'"
                          >
                            <p>3 days</p>
                          </a>
                          <a
                            class="dropdown-item trigger-type"
                            href="javascript:void(0);"
                            @click.prevent="dueDate = '4 days'"
                          >
                            <p>4 days</p>
                          </a>
                          <a
                            class="dropdown-item trigger-type"
                            href="javascript:void(0);"
                            @click.prevent="dueDate = '5 days'"
                          >
                            <p>5 days</p>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="card-footer">
                    <!-- <div class="modal-footer-left">
                      <div style="display: inline-block;position: relative;">
                        <button
                          :class="{invisible: sending}"
                          type="button"
                          class="btn btn-primary"
                          @click.prevent="sendTest"
                        >Send Test</button>
                        <div style="position: absolute;top: 21%;left: 33%;" v-show="sending">
                          <moon-loader :loading="sending" color="#188bf6" size="30px"/>
                        </div>
                      </div>
                    </div> -->
                    <div class="modal-footer-right">
                      <div style="display: inline-block;position: relative;">
                        <div
                          style="display: inline-block;position: relative;margin-left: 10px;"
                        >
                          <UIButton
                            type="button"
                            @click.prevent="save"
                            :loading="loading"
                          >
                            Save
                          </UIButton>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Task, User, getCountryDateFormat } from '@/models'
import Datepicker from 'vuejs-datepicker'
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'
import moment from 'moment-timezone'

declare var $: any
let unsubscribeUsers: () => void
export default Vue.extend({
  props: ['enableButtons', 'values', 'templates'],
  components: {
    Datepicker
  },
  data() {
    return {
      loading: false,
      edited: false,
      dueDate: '',
      currentTask: undefined as Task | undefined,
      taskBody: '',
      assignTo: '',
      name: '',
      currentLocationId: '',
      currentAccountId: '',
      // users: [] as User[],
      contactId: undefined as undefined | string,
      getCountryDateFormat: getCountryDateFormat
    }
  },
  watch: {
    async values(values: { [key: string]: any }) {
      const data: () => object = <() => object>this.$options.data
      if (data) Object.assign(this.$data, data.apply(this))
      if (values.visible) {
        $(this.$refs.modal).modal('show')
        this.$validator.reset();
      }
      else {
        this.errors.clear()
        $(this.$refs.modal).modal('hide')
      }
      this.template = values.template
      this.currentLocationId = values.currentLocationId
      this.campaignId = values.campaignId

      if (values.template) {
        if (this.template.attributes.taskBody)
          this.taskBody = this.template.attributes.taskBody
        if (this.template.attributes.name)
          this.title = this.template.attributes.name
        if (this.template.attributes.assignTo)
          this.assignTo = this.template.attributes.assignTo
        if (this.template.attributes.dueDate)
          this.dueDate =
            this.template.attributes.dueDate +
            (Number(this.template.attributes.dueDate) > 1 ? ' days' : ' day')
        console.log(
          this.template.attributes,
          this.template.attributes.assignedTo
        )
        this.name = this.template.name
        if (values.template.window) {
          this.templateWindow = lodash.clone(values.template.window)
        }
      }

      if (this.$router.currentRoute.params.account_id)
        this.currentAccountId = this.$router.currentRoute.params.account_id
      if (this.$router.currentRoute.params.location_id)
        this.currentLocationId = this.$router.currentRoute.params.location_id
      if (this.$router.currentRoute.params.contact_id)
        this.contactId = this.$router.currentRoute.params.contact_id
      if (this.$route.query.task_id) {
        this.setCurrentTask(<string>this.$router.currentRoute.query.task_id)
      }
      // if (this.currentLocationId) {
      //   User.getFilteredSortedUsersByLocation(this.currentLocationId).then(
      //     users => {
      //       this.users = users
      //     }
      //   )
      // }

      // if (this.currentAccountId) {
      //   unsubscribeUsers = (await User.fetchAllAgencyUsers()).onSnapshot(
      //     snapshot => {
      //       this.users = snapshot.docs.map(d => new User(d))
      //     }
      //   )
      // }
    },
    '$route.params.account_id': function(id) {
      this.currentAccountId = id
    },
    '$route.params.location_id': function(id) {
      this.currentLocationId = id
    },
    '$route.params.contact_id': function(id) {
      this.contactId = id
    },
    '$route.query.task_id': async function(id) {
      if (id) {
        this.reset()
        this.setCurrentTask(id)
      }
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    })
  },
  beforeDestroy() {
    if (unsubscribeUsers) unsubscribeUsers()
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  methods: {
    async save() {
      await this.$validator.validateAll();
      if (this.errors.any()) {
        return false
      }
      this.template.attributes.taskBody = this.taskBody
      this.template.attributes.assignTo = this.assignTo
      this.template.attributes.dueDate = this.dueDate.split(' ')[0]
      Vue.set(this.template, 'window', this.templateWindow)
      this.template.name = this.name
      this.$emit('save')
      this.$emit('hidden')
      this.edited = false
      this.$validator.reset();
    }
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hidden')
    })

    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  }
})
</script>

<style scoped>
.spinner {
  margin: 10px 0px;
}
i.icon.icon-arrow-down-1 {
  position: absolute;
  top: 20px;
  right: 20px;
}
</style>
