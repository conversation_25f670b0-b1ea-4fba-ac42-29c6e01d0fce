<template>
  <div id="yext_shop_dialog">
    <div class="yext_shop_dialog_body">
      <template v-if="dialogType === 'non_us_location'">
        <h2>Oops!</h2>
        <p>
          Yext is only available in US for now. We are working to support this
          in other countries.
        </p>
      </template>
      <template v-if="dialogType === 'no_stripe_or_agency_disabled'">
        <h2>Oops!</h2>
        <p v-if="contactEmail">
          Please contact
          <a :href="`mailto:${contactEmail}`">{{ contactEmail }}</a> to get Yext
        </p>
        <p v-else>
          Please contact <b>{{ contactName }}</b> to get Yext
        </p>
      </template>
    </div>
  </div>
</template>
<script>
import Vue from 'vue'

export default Vue.extend({
  props: {
    dialogType: {
      type: String,
    },
    contactEmail: {
      type: String,
    },
    contactName: {
      type: String,
    },
  },
  methods: {},
  computed: {},
})
</script>

<style lang="scss" scoped>
#yext_shop_dialog {
  z-index: 3;
  position: absolute;
  /* background: antiquewhite; */
  padding: 20px;
  width: calc(100% - 40px);
  padding-top: 150px;
  display: flex;
  justify-content: center;
  .yext_shop_dialog_body {
    background: white;
    padding: 15px 20px;
    min-height: 140px;
    border: 1px solid #d5d5d5;
    border-radius: 2px;
    box-shadow: 0 0 4px 3px #0000000f;
    position: fixed;
    h2 {
      font-weight: 500;
      color: #1f1e1eab;
      margin-bottom: 10px;
    }
  }
}
</style>