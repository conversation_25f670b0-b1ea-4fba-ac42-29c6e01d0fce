<template>
  <div
    class="modal fade smsStatsDetailModal2"
    style="overflow: auto"
    id="create-number-step8-modal"
    tabindex="-1"
    role="dialog"
    data-keyboard="false"
    data-backdrop="static"
    ref="smsStatsDetailModal2">
    <div class="modal-dialog modal-custom-width" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div>
            <h2 class="modal-title">
              {{ modalTitle }}
            </h2>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
              @click.prevent="closeModal"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
            <NewSMSStats
              :modalTitle="modalTitle"
              :option="option"
              :campaign_id="campaign_id"
              :campaign_step_id="campaign_step_id"
              :selectedDate="selectedDate"
              :haveSMTP="bulk_req.eventStatsV2.haveSMTP"
              :bulk_req_id="bulk_req_id">
            </NewSMSStats>
        </div>
        <div class="modal-footer" v-if="bulk_req">
          <div class="my-3">
            <SMSStatsPanel
              v2="true"
              :eventStats="bulk_req.eventStatsV2.providerStats"
              :bulkReqId="bulk_req.id">
            </SMSStatsPanel>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import NewSMSStats from './NewSMSStats.vue'
import SMSStatsPanel from './SMSStatsPanel.vue'

export default Vue.extend({
  props: {
    modalTitle: String,
    option: String,
    campaign_id: String,
    campaign_step_id: String,
    selectedDate: Object,
    bulk_req_id: String,
    bulk_req: Object,
  },
  components: {
    NewSMSStats,
    SMSStatsPanel,
  },
  data() {
    return {
      isLoading: false,
      title: '',
    }
  },
  computed: {},
  methods: {
    closeModal() {
      this.$emit('closeSMSStatsDetailModal')
    },
  },
  mounted() {
    const _self = this
    $(this.$refs.smsStatsDetailModal2).on('hidden.bs.modal', function () {
      _self.$emit('closeSMSStatsDetailModal')
    })
    $(this.$refs.smsStatsDetailModal2).modal('show')
  },
  beforeDestroy() {
    $(this.$refs.smsStatsDetailModal2).modal('hide')
    $(this.$refs.smsStatsDetailModal2).off('hidden.bs.modal')
  },
})
</script>
<style lang="scss">
.smsStatsDetailModal2 .modal-custom-width {
  max-width: 1200px;
  width: 72%;
}

#bootstrap-tab-custom-id__BV_tab_controls_ {
  border-bottom: none;
}

.smsStatsDetailModal2 .modal-dialog {
  margin: 1rem auto;
}

.disabled-tab {
  cursor: regular;
}
.stick-to-bottom {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 50px;
}
</style>
