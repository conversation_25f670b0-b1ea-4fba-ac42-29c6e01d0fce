<template>
  <section class="hl_wrapper">
    <section
      class="hl_marketing--inner hl_wrapper--inner hl_marketing"
      id="marketing"
    >
      <div class="hl_marketing--header">
        <div class="container-fluid">
          <div class="hl_controls">
            <div class="hl_controls--left flex">
              <h2>Customer Acquisition</h2>
            </div>
            <div class="hl_controls--right">
              <UIButton
                type="button"
                use="primary"
                style="margin-right: 20px;"
                @click="createFolder"
              >
                <i class="icon icon-plus mr-2"></i> New Folder
              </UIButton>

              <UIButton use="primary" @click="createCampaign">
                <i class="icon icon-plus mr-2"></i> Create Campaign
              </UIButton>
              <!--  -->
            </div>
          </div>

          <ul class="hl_marketing--nav">
            <li class="active">
              <a href="javascript:void(0);">My Campaigns</a>
            </li>
            <!-- <li>
                            <a href="#">Recommended Campaigns</a>
						</li>-->
          </ul>
        </div>
      </div>

      <div class="container-fluid">
        <div class="hl_marketing--heading">
          <div class="hl_marketing--heading-text">
            <h3>Get More Customers</h3>
            <p>
              Customer acquisition campaigns help you to produce more "hot"
              sales leads who are ready to engage.
            </p>
          </div>
        </div>

        <div class="hl_customer-acquisition">
          <!-- <div class="card-group --wide-gutter">
                        <div class="card">
                            <div class="card-header">
                                <h2>Total Accounts on Campaigns</h2>
                            </div>
                            <div class="card-body">
                                <h3><i class="icon icon-inbox"></i> 62</h3>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header">
                                <h2>Average Open Rate</h2>
                            </div>
                            <div class="card-body">
                                <h3><i class="icon icon-mail"></i> 43.8%</h3>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header">
                                <h2>Average Click Through Rate</h2>
                            </div>
                            <div class="card-body">
                                <h3><i class="icon icon-link"></i> 32.2%</h3>
                            </div>
                        </div>
					</div>-->
          <div class="row">
            <!-- <div class="col-lg-3">
                            <div class="hl_customer-acquisition--filter">

                                <div class="card">
                                    <div class="card">
                                        <div class="card-header">
                                            <h3>Filter</h3>
                                            <ul>
                                                <li>
                                                    <a href="#">Select All</a>
                                                </li>
                                                <li>
                                                    <a href="#">Clear All</a>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-group">
                                                <label>Campaign Name</label>
                                                <input type="text" class="form-control">
                                            </div>
                                            <div class="form-group">
                                                <label>Market</label>
                                                <select class="selectpicker">
                                                    <option>All Market</option>
                                                    <option>Option 2</option>
                                                    <option>Option 3</option>
                                                    <option>Option 4</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label>Status</label>
                                                <div class="status-item">
                                                    <p>Draft</p>
                                                    <div class="option">
                                                        <input type="checkbox" id="status-draft">
                                                        <label for="status-draft"></label>
                                                    </div>
                                                </div>
                                                <div class="status-item">
                                                    <p>Published</p>
                                                    <div class="option">
                                                        <input type="checkbox" id="status-published">
                                                        <label for="status-published"></label>
                                                    </div>
                                                </div>
                                                <div class="status-item">
                                                    <p>Archived</p>
                                                    <div class="option">
                                                        <input type="checkbox" id="status-archived">
                                                        <label for="status-archived"></label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label>State</label>
                                                <div class="state-item">
                                                    <p>Active</p>
                                                    <div class="option">
                                                        <input type="checkbox" id="state-active">
                                                        <label for="state-active"></label>
                                                    </div>
                                                </div>
                                                <div class="state-item">
                                                    <p>Idle</p>
                                                    <div class="option">
                                                        <input type="checkbox" id="state-idle">
                                                        <label for="state-idle"></label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
						</div>-->
            <div class="col-lg-12">
              <div class="card hl_customer-acquisition--table">
                <div class="card-body --no-padding">
                  <div class="table-wrap">
                    <div class="w-100">
                      <div
                        class="w-100 d-flex py-2"
                        style="border-bottom: 1px solid rgba(0,0,0,0.1);"
                      >
                        <div
                          class="w-25"
                          style="padding-left: 40px; min-width: 25%;"
                        >
                          <span>Name</span>
                        </div>
                        <div class="w-75 d-flex justify-content-around">
                          <!-- <tr> -->
                          <span class="text-center" style="min-width: 34px;"
                            >Total</span
                          >
                          <span class="text-center" style="min-width: 40.5px;"
                            >Active</span
                          >
                          <span class="text-center" style="min-width: 70px;"
                            >Completed</span
                          >
                          <span class="text-center" style="min-width: 48px;"
                            >Replied</span
                          >
                          <span class="text-center" style="min-width: 50px;"
                            >Reply %</span
                          >
                          <span class="text-center" style="min-width: 75px;"
                            >Status</span
                          >
                          <span class="text-center" style="min-width: 162px;"
                            >Campaign ID</span
                          >
                        </div>
                        <div class="w-25" style="min-width: 25%;">
                          <span></span>
                        </div>
                      </div>
                      <CampaignListItemFolder
                        v-for="(folder, index) in filteredFolders"
                        :key="index"
                        :folder="folder"
                        @renameFolder="renameFolder"
                        :campaigns="
                          campaigns.filter(c => c.folder_id == folder.id)
                        "
                        :folders="filteredFolders"
                        :storeFolders="
                          FoldersList.map(i => i).find(id => id === folder.id)
                        "
                      >
                      </CampaignListItemFolder>

                      <CampaignListItem
                        v-for="campaign in campaigns.filter(
                          c =>
                            !c.folder_id ||
                            !filteredFolders.find(f => c.folder_id === f.id)
                        )"
                        :key="campaign.id"
                        :campaign="campaign"
                        :folders="filteredFolders"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <DetailPageModal
        :values="modal"
        @hidden="hideModal"
        v-if="modal.visible"
      ></DetailPageModal>
    </section>
    <!-- END of .hl_marketing -->
  </section>
  <!-- END of .hl_wrapper -->
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState, mapActions, mapGetters } from 'vuex'
import { Campaign, User } from '../../../models/'
import { UserState } from '../../../store/state_models'
import { Folder } from '../../../models'
import { EventBus } from '@/models/event-bus'
import firebase from 'firebase/app'
import { getCampaigns } from '../../../util/opportunity_user'

const TopBar = () => import('../../components/TopBar.vue').then(m => m.default)
const SideBar = () =>
  import('../../components/SideBar.vue').then(m => m.default)
const CampaignListItem = () =>
  import('../../components/marketing/CampaignListItem.vue').then(m => m.default)
const CampaignListItemFolder = () =>
  import('../../components/marketing/CampaignListItemFolder.vue')
const DetailPageModal = () =>
  import('../../components/triggers/DetailPageModal.vue').then(m => m.default)

declare var $: any
let triggersListener: () => void
let foldersListener: () => void

export default Vue.extend({
  components: {
    TopBar,
    SideBar,
    CampaignListItem,
    CampaignListItemFolder,
    DetailPageModal
  },
  data() {
    return {
      folders: [] as Folder[],
      currentLocationId: '',
      folderId: null,
      draggedCampaign: null,
      modal: {
        visible: false,
        name: '',
        action: '',
        title: '',
        type: '',
        folders: [] as Folder[],
        folder: {} as Folder
      }
    }
  },
  watch: {
    '$route.params.location_id': async function(id) {
      this.currentLocationId = id
      if (id) {
        this.folders = []
        this.fetchFoldersData()
      }
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchFoldersData()
  },
  computed: {
    ...mapGetters('campaign_folder', ['FoldersList']),
    allowEdit(): boolean {
      return this.user && this.user.permissions.campaigns_read_only !== true
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    campaigns() {
      if (!this.$store.state.campaigns.campaigns) return []
      return this.$store.state.campaigns.campaigns
    },
    filteredFolders(): Folder[] {
      let folders = this.folders
      let filtered = lodash.filter(folders, { type: 'campaigns' }) // Filter first, better performance
      let result = lodash.sortBy(filtered, 'name')
      return result
    }
  },
  methods: {
    createCampaign() {
      this.modal = {
        visible: true,
        title: 'Create Campaign',
        type: 'campaigns',
        action: 'new_trigger_campaign',
        folders: this.filteredFolders
      }
    },
    createFolder() {
      this.modal = {
        visible: true,
        action: 'new_folder',
        title: 'Create New Folder',
        type: 'campaigns'
      }
    },
    async renameFolder(name: string, folder: Folder) {
      this.modal = {
        visible: true,
        action: 'rename_folder',
        title: 'Rename Folder',
        name: name,
        folder: folder
      }
    },
    hideModal() {
      this.modal.visible = false
      // this.detailPageModal.visible = false
      document.querySelectorAll('.modal-backdrop').forEach(a => a.remove())
    },
    async fetchFoldersData() {
      await this.$store.dispatch('campaigns/syncAll', this.$route.params.location_id)
      if (foldersListener) {
        foldersListener()
      }

      foldersListener = Folder.getByLocationIdRealtime(
        this.currentLocationId
      ).onSnapshot(snapshot => {
        this.folders = snapshot.docs.map(d => new Folder(d))
      })
    }
  },
  beforeDestroy() {
    if (triggersListener) {
      triggersListener()
    }

    if (foldersListener) {
      foldersListener()
    }
  }
})
</script>
