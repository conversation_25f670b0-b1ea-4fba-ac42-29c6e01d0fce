<template>
  <div
    class="tab-pane fade show active"
    id="publishing-tab"
    role="tabpanel"
    aria-labelledby="publishing-tab"
  >
    <div class="hl_funnels--publishing">
      <div class="details">
        <div class="form-group">
          <UITextInputGroup v-model="stepName" label="Step Name" type="text" />
        </div>
        <div class="form-group">
          <UITextInputGroup v-model="stepUrl" label="Step Path" type="text" />
        </div>
      </div>
      <div class="control">
        <UIButton
          :disabled="updateButtonDisabled"
          @click="updateFunnelStep()"
          class="w-full justify-center"
        >{{ updatingStep ? 'Updating..' : 'Update Step' }}</UIButton>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { checkIfUserIsSettingFunnelPathAsRoot } from '../../../util/helper'
export default Vue.extend({
  props: {
    funnelStep: {
      type: Object
    },
    stepId: {
      type: String
    }
  },
  computed: {
    updateButtonDisabled: function(): Boolean {
      return this.updatingStep || !this.stepName || !this.stepUrl
    }
  },
  data() {
    return {
      stepName: this.funnelStep.name,
      stepUrl: this.funnelStep.url,
      updatingStep: false
    }
  },
  methods: {
    updateFunnelStep() {
      const updatedData = {
        name: this.stepName.trim(),
        url: this.stepUrl.trim(),
        stepId: this.stepId
      }

      if (checkIfUserIsSettingFunnelPathAsRoot(updatedData.url)) {
        return
      }

      this.updatingStep = true
      this.$emit('updateFunnelStep', updatedData)
    }
  },
  watch: {
    funnelStep: function(newValue, oldValue) {
      this.updatingStep = false
      this.stepUrl = newValue.url
      this.stepName = newValue.name
    }
  }
})
</script>

<style scoped>
.hl_funnels--publishing .form-group,
.hl_funnels--publishing .control {
  max-width: 500px;
  margin: 20px auto;
}
.hl_funnels--publishing .control .btn {
  height: 50px;
}
</style>>
