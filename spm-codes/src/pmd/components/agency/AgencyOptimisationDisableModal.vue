<template>
  <modal v-if="show" @close="$emit('close')" maxWidth="700" :showCloseIcon="true">
    <div>
      <div class="hl-oe__modal-header">Optimize Experience</div>
      <div class="hl-oe__modal-body">
        <div class="hl-oe__item-wrap">
          <i class="fas fa-exclamation-circle --warning-icon"></i>
          <div class="hl-oe__item-info">
            <div class="hl-oe__item-title">Disabling {{content.title}} is not recommended</div>
            <div class="hl-oe__item-description">
              {{content.description}}
            </div>
            <br/>
            <div class="hl-oe__item-wrap">
              <button class="btn btn-success --back-btn" @click="$emit('close')">Go Back</button>
              <div class="hl-oe__confirm-wrap">
                <button class="btn --confirm-btn" :class="{'--active': confirmed}" @click="confirmed = true">I still wish to disable {{content.title}}</button>
                <br /><br />
                <div class="hl-oe__final-confirm" :class="{ '--visible': confirmed }">
                  <div class="form-group flex">
                    <UICheckbox
                      id="hl-oe-confirm-disable"
                      v-model="checked"
                    />
                    <label for="hl-oe-confirm-disable">
                      I understand this may increase my churn
                    </label>
                  </div>
                  <button class="btn --disable-btn" :disabled="!checked" @click="$emit('disable')">Disable {{content.title}}</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </modal>
</template>

<script>
import Modal from '@/pmd/components/common/Modal.vue'

export default {
  props: ['show', 'type'],
  components: {
    Modal,
  },
  data() {
    return {
      confirmed: false,
      checked: false,
    }
  },
  computed: {
    content(){
      let result = {
        title: '',
        description: '',
      }
      switch(this.type){
        case 'nps':
          result.title = 'NPS Surveys'
          result.description = 'These surveys give us critical feedback and allow us to better analyze behaviors which result in lower churn. Having these surveys on will help us make the platform better for your clients which will enable you to retain your clients for longer. '
          break
        case 'guides':
          result.title = 'Product Guides'
          result.description = 'Product Guides help us onboard new users in the platform on your behalf. It improves the adoption & engagement of various products and helps reduce the churn for you. Keeping this option on will help you keep your clients for longer.'
          break
        case 'reports':
          result.title = 'Daily Updates'
          result.description = 'Daily updates motivate your clients to respond to leads quickly, maintain a health response rate and reduce avg. response times. This results in better lead conversions and reduces cost of acquisition. '
          break
      }
      return result
    },
  },
}
</script>

<style lang="scss">
.hl-oe__modal-header {
  padding: 12px 32px;
  border-bottom: 1px solid #efefef;
  font-size: 20px;
  font-weight: 500;
}
.hl-oe__modal-body{
  padding: 20px 32px;
  .--warning-icon{
    font-size: 40px;
    margin-right: 16px;
    color: #fc6e25;
  }
  button.--back-btn{
    margin-right: 24px;
  }
  button.--confirm-btn{
    border: 1px solid #8f8e8e;
    background-color: #ffffff;
    color: #8f8e8e;
    opacity: 0.5;
    &:hover, &.--active{
      opacity: 1;
    }
  }
  button.--disable-btn{
    border: 1px solid #fc6e25;
    color: #fc6e25;
    background-color: #ffffff;
  }
  .hl-oe__final-confirm {
    visibility: hidden;
    &.--visible {
      visibility: initial;
    }
  }
}
</style>
