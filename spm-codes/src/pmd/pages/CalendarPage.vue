<template>
    <section class="hl_wrapper">

        <section class="hl_wrapper--inner hl_calendar" id="calendar">
            <div class="container-fluid">

                <div class="hl_controls">
                    <div class="hl_controls--left">
                        <h2 class="heading4">Calendar</h2>
                    </div>
                    <div class="hl_controls--right">
                        <!-- <a href="#" class="btn btn-primary" data-toggle="modal" data-target="#book-appointment">Toggle Modal</a> -->
                    </div>
                </div>

                <div class="card" v-if="calendars.length > 0">
                    <div class="card-header --non-flex">
                        <div class="card-header-nav">
                            <div class="card-header-nav-left">
                                <!-- <a href="#" class="prev">
                                    <i class="icon icon-arrow-left-1"></i>
                                </a>
                                <h4>April 2018</h4>
                                <a href="#" class="next">
                                    <i class="icon icon-arrow-right-1"></i>
                                </a> -->
                            </div>
                            <div class="card-header-nav-right">
                                <a href="javascript:void(0);" class="prev" @click.prevent="previous">
                                    <i class="icon icon-arrow-left-1"></i>
                                </a>
                                <h5>{{startDay.format(getCountryDateFormat(false))}} - {{endDay.clone().subtract(1, 'day').format(getCountryDateFormat(false))}}</h5>
                                <a href="javascript:void(0);" class="next" @click.prevent="next">
                                    <i class="icon icon-arrow-right-1"></i>
                                </a>
                            </div>
                        </div>
                        <div class="card-header-tab">
                            <ul>
                                <li v-for="(calendar, index) in calendars" :calendar="calendar" :key="calendar.id" @click="selectedPosition = index" :class="{active: index === selectedPosition}">
                                    <a href="javascript:void(0);">{{calendar.name}}</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="card-body">
                        <CalendarBody :calendar="calendars[selectedPosition]" :startDay="startDay" :endDay="endDay" @editAppointment="editAppointment" />
                    </div>
                </div>
                <div v-else style="text-align: center;margin-top: 100px;">
                    You don't have any calendars yet -- <router-link :to="{name: 'calendar_settings', params: {location_id: currentLocationId}}" tag="a" exact>Click here to add one.</router-link>
                </div>
                <EditAppointmentModal @hidden="modalData={}" :values="modalData" />
            </div>
        </section>
    </section>
</template>

<script lang="ts">
import Vue from 'vue';
import moment from 'moment-timezone';
import { Calendar, Location, getCountryDateFormat } from '../../models';

const EditAppointmentModal = () => import( '../components/EditAppointmentModal.vue');
const CalendarBody = () => import( '../components/CalendarBody.vue');

export default Vue.extend({
    components: { CalendarBody, EditAppointmentModal },
    created() {
        this.currentLocationId = this.$router.currentRoute.params.location_id;
        this.fetchCalendars();
    },
    watch: {
        '$route.params.location_id': function (id) {
            this.currentLocationId = id;
            this.fetchCalendars();
        },
    },
    computed: {
        startDay() {
            let now = this.timezone ? moment.tz(this.timezone) : moment();
            now = now.isoWeekday(1).startOf('day');
            return this.offset !== 0 ? now.add(this.offset, 'days') : now;
        },
        endDay() {
            let now = this.timezone ? moment.tz(this.timezone) : moment();
            now = now.isoWeekday(1).startOf('day').add(7, 'days');
            return this.offset !== 0 ? now.add(this.offset, 'days') : now;
        }
    },
    data() {
        return {
            currentLocationId: '',
            modalData: {},
            timezone: '',
            selectedPosition: 0,
            offset: 0,
            calendars: [] as Calendar[],
        };
    },
    methods: {
        async fetchCalendars() {
            const location = new Location(await this.$store.dispatch('locations/getById', this.currentLocationId));
            this.timezone = await location.getTimeZone();
            this.calendars = await Calendar.getAllByLocation(this.currentLocationId);
        },
        editAppointment(appointmentId: string) {
            this.modalData = {
                visible: true,
                appointmentId: appointmentId,
            };
        },
        async previous() {
            this.offset -= 7;
        },
        async next() {
            this.offset += 7;
        },
    },
});
</script>
