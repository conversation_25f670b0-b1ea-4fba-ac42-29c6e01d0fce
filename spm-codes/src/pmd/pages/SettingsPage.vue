<template>
  <section class="hl_wrapper">
    <section
      class="hl_wrapper--inner hl_settings hl_settings--profile"
      id="settings"
    >
      <div class="hl_settings--header">
        <div class="container-fluid">
          <h2>Settings</h2>
          <ul class="hl_settings--nav">
            <router-link
              v-if="
                (company && company.customerType !== 'agency') ||
                (company &&
                  company.customerType === 'agency' &&
                  user &&
                  user.type === 'account' &&
                  isFromThisLocation)
              "
              v-show="!isCalendarV3On"
              :to="{
                name: 'settings',
                params: { location_id: currentLocationId },
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Profile</a>
            </router-link>
            <router-link
              v-if="
                isCalendarV3On &&
                ((company &&
                  company.customerType === 'agency' &&
                  isFromThisLocation) ||
                  (user && user.type === 'account'))
              "
              :to="{
                name: 'profile',
                params: { location_id: currentLocationId },
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Profile</a>
            </router-link>
            <router-link
              v-show="user && user.permissions.settings_enabled !== false"
              :to="{
                path: '/location/' + currentLocationId + '/settings/company',
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Company</a>
            </router-link>
            <router-link
              v-if="(isAdmin || isAgencyUser) && showCompanyBillingRoute"
              v-show="user && user.permissions.settings_enabled !== false"
              :to="{ name: 'location_billing' }"
              tag="li"
              active-class="active"
            >
              <a href="javascript:void(0);">Company Billing</a>
            </router-link>
            <router-link
              v-show="user && user.permissions.settings_enabled !== false"
              v-if="company && company.customerType !== 'agency'"
              :to="{
                name: 'billing',
                params: { location_id: currentLocationId },
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Billing</a>
            </router-link>
            <router-link
              v-show="user && user.permissions.settings_enabled !== false"
              v-if="isAdmin"
              :to="{ name: 'location_team_management' }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Team Management</a>
            </router-link>

            <router-link
              v-show="user && user.permissions.settings_enabled !== false"
              v-if="isAdmin && isCalendarV3On && hasLocationPermission('teams')"
              :to="{ name: 'location_teams_management' }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Teams</a>
            </router-link>

            <router-link
              v-show="
                user &&
                user.permissions.settings_enabled !== false &&
                user.permissions.reviews_enabled !== false
              "
              :to="{
                name: 'settings_reputation_settings',
                params: { location_id: currentLocationId },
              }"
              id="nav-reputation-settings"
              tag="li"
              active-class="active"
            >
              <a>
                <span>Reputation</span>
              </a>
            </router-link>

            <router-link
              v-show="user && user.permissions.settings_enabled !== false"
              v-if="drchronoOAuth.length"
              :to="{
                name: 'no_show_sms_settings',
                params: { location_id: currentLocationId },
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">No Show SMS</a>
            </router-link>

            <!-- <router-link v-if="drchronoConnection" :to="{name: 'birthday_sms_settings', params: {location_id: currentLocationId}}" tag="li" active-class="active" exact>
                        <a href="javascript:void(0);">Birthday SMS</a>
            </router-link>-->
            <!-- <router-link v-if="drchronoConnection" :to="{name: 'recall_settings', params: {location_id: currentLocationId}}" tag="li" active-class="active" exact>
                        <a href="javascript:void(0);">Recall Reminders</a>
            </router-link>-->

            <!-- <router-link :to="{name: 'social_posting',params: {location_id: currentLocationId}}" tag="li" active-class="active" exact>
                        <a href="javascript:void(0);">Social Posting</a>
            </router-link>-->
            <router-link
              v-show="
                user &&
                user.permissions.settings_enabled !== false &&
                hasLocationPermission('pipelines')
              "
              :to="{
                name: 'pipeline_settings',
                params: { location_id: currentLocationId },
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Pipelines</a>
            </router-link>
            <!-- <router-link
              v-show="user && user.permissions.settings_enabled !== false"
              :to="{
                name: 'payment_settings',
                params: { location_id: currentLocationId },
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Payments</a>
            </router-link> -->
            <!-- <router-link :to="{name: 'review_popup',params: {location_id: currentLocationId}}" tag="li" active-class="active" exact>
                            <a href="javascript:void(0);">Review Popup</a>
            </router-link>-->
            <!-- <router-link
							:to="{name: 'chat_widget',params: {location_id: currentLocationId}}"
							tag="li"
							active-class="active"
							exact
						>
							<a href="javascript:void(0);">Chat Widget</a>
            </router-link>-->
            <router-link
              v-show="
                (user && user.permissions.settings_enabled) ||
                user.permissions.appointments_enabled
              "
              :to="{
                name: 'integrations_settings',
                params: { location_id: currentLocationId },
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Integrations</a>
            </router-link>
            <router-link
              v-show="user && user.permissions.settings_enabled !== false"
              :to="{
                name: 'phone_number',
                params: { location_id: currentLocationId },
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Phone Numbers</a>
            </router-link>
            <!-- <router-link
              v-show="user && user.permissions.settings_enabled !== false"
							v-if="mailgunAccount"
							:to="{name: 'loc_mail_gun_settings', params: {location_id: currentLocationId}}"
							tag="li"
							active-class="active"
							exact>
							<a href="javascript:void(0);">Bulk Email (MailGun)</a>
				    </router-link> -->
            <router-link
              v-show="
                user &&
                user.permissions.settings_enabled !== false &&
                user.permissions.appointments_enabled !== false &&
                hasLocationPermission('calendars')
              "
              :to="{
                name: 'calendar_settings',
                params: { location_id: currentLocationId },
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Calendars</a>
            </router-link>
            <router-link
              v-show="
                user &&
                user.permissions.settings_enabled !== false &&
                hasLocationPermission('templates')
              "
              :to="{
                name: 'template_settings',
                params: { location_id: currentLocationId },
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Templates</a>
            </router-link>
            <router-link
              v-show="
                user &&
                user.permissions.settings_enabled !== false &&
                hasLocationPermission('custom_fields')
              "
              :to="{
                name: 'custom_fields_settings',
                params: { location_id: currentLocationId },
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Custom Fields</a>
            </router-link>
            <router-link
              v-show="user && user.permissions.settings_enabled !== false"
              :to="{
                name: 'facebook_settings',
                params: { location_id: currentLocationId },
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Facebook Form Fields Mapping</a>
            </router-link>
            <router-link
              v-show="
                user &&
                user.permissions.settings_enabled !== false &&
                hasLocationPermission('custom_values')
              "
              :to="{
                name: 'custom_values_settings',
                params: { location_id: currentLocationId },
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Custom Values</a>
            </router-link>
            <router-link
              v-show="
                user &&
                user.permissions.settings_enabled !== false &&
                hasLocationPermission('domains')
              "
              :to="{
                name: 'domain_settings',
                params: { location_id: currentLocationId },
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Domains</a>
            </router-link>
            <router-link
              v-show="
                user &&
                user.permissions.tags_enabled !== false &&
                hasLocationPermission('tags')
              "
              :to="{
                name: 'tags_settings',
                params: { location_id: currentLocationId },
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Tags</a>
            </router-link>
            <router-link
              v-show="user && user.permissions.settings_enabled !== false"
              :to="{
                name: 'smtp_service',
                params: { location_id: currentLocationId },
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">SMTP and MailGun Service</a>
            </router-link>

            <router-link
              :to="{ name: 'eliza_faq' }"
              tag="li"
              active-class="active"
              v-if="isBotSEviceEnabled && user && user.permissions.bot_service !== false"
              exact
            >
              <a href="javascript:void(0);">Eliza Service</a>
            </router-link>
            <router-link
              v-show="user && user.permissions.settings_enabled !== false"
              :to="{
                name: 'product-list',
              }"
              tag="li"
              active-class="active"
            >
              <a href="javascript:void(0);">
                Payments
              </a>
				    </router-link>
            <router-link
              v-show="user && user.permissions.settings_enabled !== false && isEnabled"
              :to="{
                name: 'redirect_settings',
                params: { location_id: currentLocationId },
              }"
              tag="li"
              active-class="active"
            >
					    <a href="javascript:void(0);">
                URL Redirects
                <UIBadge use="green">
                  New
                </UIBadge>
              </a>
            </router-link>

            <router-link
              v-show="user && user.permissions.settings_enabled !== false"
              :to="{
                name: 'media-settings',
              }"
              tag="li"
            >
              <div class=" cursor-pointer inline-flex items-center px-2 py-1 border border-transparent text-sm font-medium rounded shadow-sm text-white bg-curious-blue-500 hover:bg-curious-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-curious-blue-500">
                Media
              </div>
            </router-link>
            <!--<router-link
              v-show="(user && user.permissions.attributions_reporting_enabled) || (user.role === 'admin' && user.type === 'agency' && user.permissions.attributions_reporting_enabled !== false)"
              :to="{name: 'attribution', params: {location_id: currentLocationId}}"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Attribution</a>
            </router-link>-->
          </ul>
        </div>
      </div>
      <router-view />
    </section>
    <!-- END of .settings-profile -->
  </section>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import { UserState, CompanyState } from '../../store/state_models'
import { User, Company, OAuth2, MailGunAccount } from '@/models'
import { Location } from '@/models'
import firebase from 'firebase/app'

export default Vue.extend({
  data() {
    return {
      currentLocationId: '',
      location: null as Location | null,
      // isCalendarV3On: false,
      mailgunAccount: undefined as MailGunAccount | undefined,
      isBotSEviceEnabled: false,
      showCompanyBillingRoute: false,
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.fetchData()
    },
    async currentLocationId() {
      if (this.currentLocationId) {
        this.location = new Location(
          await this.$store.dispatch(
            'locations/getById',
            this.currentLocationId
          )
        )
        this.location = this.location
          ? this.location
          : await Location.getById(this.currentLocationId)
        this.isBotSEviceEnabled = this.location.botService
        if (
          this.location.settings.saas_settings &&
          this.location.settings.saas_settings.saas_mode &&
          this.location.settings.saas_settings.saas_mode != 'not_activated'
        ) {
          this.showCompanyBillingRoute = true
        } else if (
          this.location.settings.saas_settings &&
          this.location.settings.saas_settings.stripe_customer_id
        ) {
          this.showCompanyBillingRoute = true
        }
        // this.isCalendarV3On = location.isCalendarV3On
        this.paymentsBetaAccess = this.location.hasPaymentsBetaAccess
      }
    },
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.mailgunAccount = await MailGunAccount.getMailGunAccount({
        locationId: this.currentLocationId,
        fallback: false,
      })
      await this.$store.dispatch(
        'oauth2/syncAll',
        this.$route.params.location_id
      )
    },
    hasLocationPermission(tab: string) {
      if (!this.location || !this.location.permissions) return true
      const { permissions } = this.location

      switch (tab) {
        case 'teams': {
          return permissions.appointments_enabled
        }
        case 'calendars': {
          return permissions.appointments_enabled
        }
        case 'domains': {
          return permissions.funnels_enabled || permissions.websites_enabled
        }
        case 'templates': {
          return permissions.sms_email_templates_enabled
        }
        case 'pipelines': {
          return permissions.opportunities_enabled
        }
        case 'appointment_widget': {
          return permissions.appointments_enabled
        }
        case 'tags': {
          return permissions.contacts_enabled
        }
        case 'custom_fields': {
          return permissions.contacts_enabled || permissions.marketing_enabled
        }
        case 'custom_values': {
          return permissions.contacts_enabled || permissions.marketing_enabled
        }
        default: {
          console.log('got unexpected tab --> ', tab)
          return true
        }
      }
    },
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
    isAgencyUser(): boolean {
      return this.user && this.user.type === 'agency'
    },
    isAdmin(): boolean {
      return this.user && this.user.role === 'admin'
    },
    drchronoOAuth() {
      return this.$store.getters['oauth2/getByType'](OAuth2.TYPE_DRCHRONO)
    },
    isCalendarV3On() {
      return this.company && this.company.isCalendarV3On
    },
    isFromThisLocation() {
      return this.user.locations[this.currentLocationId] !== undefined
    },
    isEnabled(){
        return this.$store.getters['LevelUpDayFlag/isfeatursActive']
    }
  },
})
</script>
