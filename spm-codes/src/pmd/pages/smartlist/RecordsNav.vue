<template>
    <div class="d-inline-block" v-if="navSpecs">
      <span class="mx-2 pointer" v-if="previousVisible"  @click.stop.prevent="previousContact()"><i class="fa fa-caret-left fa-lg	 --blue"></i></span>
      <div class="d-inline-block" v-if="displayIndex && total">
        <span class="text-muted mx-1">{{displayIndex}} of</span>
        <a @click.stop.prevent="loadList()"
           v-b-tooltip.hover
           title="Click to reload list"
         href="javascript: void(0)" class="mx-1 --blue">{{total}}</a>
        <span class="mx-1">selected</span></div>
      <span class="mx-2 pointer" v-if="nextVisible" @click.stop.prevent="nextContact()"><i class="fa fa-caret-right fa-lg	 --blue"></i></span>
    </div>
</template>

<script lang="ts">

import Vue from 'vue';
import {SmartList, User} from '@/models'
import {ContactsServer} from './server/contacts_server.ts'
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'

export default Vue.extend({
  name: 'RecordsNav',
  props : ['route'],
  //inject: ['uxmessage'],
  data () {
    return {
      current:null,
      total: null,
      navSpecs: null,
      previousVisible: false,
      nextVisible: false,
      // total: Number(this.totalPages),
      // specs,
      // waitTime:1000,
      // debouncer:undefined as NodeJS.Timer | undefined,
    };
  },
  watch : {
    '$route.params.smart_list_specs': function(n) {
      this.reset();
    },
    '$route.params.contact_id': function(id) {
      this.reset();
    },
    route(n){
      //alert('route watch');
      this.clear();
      this.init();
    }
  },
  computed: {
     displayIndex() {
        if (!this.navSpecs) return 0;
        if (this.navSpecs.fewSelected === true) return this.current + 1;

        return (this.current + ((this.navSpecs.page-1) * this.navSpecs.pageLimit)) + 1;
     },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
  },
  methods: {
    async nextContact(){
      if(!this.navSpecs) return;
      if (this.navSpecs.fewSelected === true || this.current < (this.navSpecs.selectedContactIds.length -1)) {
        this.goTo( this.current + 1);
      } else if (this.navSpecs.page < this.navSpecs.totalPages){
          await this.updateNavSpecs(this.navSpecs.page + 1);
          this.goTo(0);
      }
    },
    async previousContact(){
      if(!this.navSpecs) return;
      if (this.navSpecs.fewSelected === true || this.current > 0 ) {
        this.goTo( this.current - 1);
      } else if (this.navSpecs.page > 1){
          await this.updateNavSpecs(this.navSpecs.page - 1);
          this.goTo(this.navSpecs.selectedContactIds.length -1);
      }
    },
    reset(){
      this.clear();
      this.init();
    },
    async updateNavSpecs(pageNum: number){
      try
      {
        let paginations = this.navSpecs.paginations;
        let searchAfter : any;
        if (!paginations) paginations = [];

        if (paginations.length >= pageNum) searchAfter = paginations[pageNum - 1];

        const query: IContactsQuery = {
          location_id: this.route.params.location_id,
          user_id: this.user ? this.user.id : null,
          assigned_to: this.user && this.user.isAssignedTo ? this.user.id : null,
          page: pageNum,
          filters: this.navSpecs.currentFilters || [],
          page_limit: this.navSpecs.pageLimit || 20,
          search_after: searchAfter,
          sort: this.navSpecs.sortDefinitions  || [],
          just_ids : true
        };

        const server = new ContactsServer(this.navSpecs.searchApi);
        const rs = await server.fetch(query);
        this.navSpecs.totalPages = rs.totalPages;
        this.navSpecs.totalContacts = rs.allRecordsCount;
        //alert(rs.page);
        this.navSpecs.page = rs.page;
        this.navSpecs.pageLimit = rs.pageLimit;
        this.navSpecs.selectedContactIds = rs.pageItems.map((a) => a.id);
        if (paginations.length < pageNum) paginations.push(rs.searchAfter);
        this.navSpecs.paginations = paginations;
        this.route.params.smart_list_specs = this.navSpecs;

      } catch(error){
          alert(error.message);
      }
    },
    init() {
      if (!this.route) return;

      this.navSpecs = this.route.params.smart_list_specs;

      if (!this.navSpecs || !this.navSpecs.selectedContactIds) return;

      this.current = this.navSpecs.currentIndex;

      if(this.navSpecs.fewSelected !== true) this.total = this.navSpecs.totalContacts
      else this.total = this.navSpecs.selectedContactIds ? this.navSpecs.selectedContactIds.length : null;

      this.previousVisible = this.displayIndex > 1;
      this.nextVisible =  this.displayIndex < this.total;
    },
    clear() {
        this.navSpecs = null;
        this.current = null;
        this.total = null;
    },
    goTo(gotoIndex: number){
      if (gotoIndex === null || gotoIndex < 0) return;
      if (!this.navSpecs || !this.navSpecs.selectedContactIds) return;

      const gotoId = this.navSpecs.selectedContactIds[gotoIndex];
      this.navSpecs.currentIndex = gotoIndex;

      this.route.params.contact_id = gotoId;
      // this.route.fullPath = '';
      // this.route.path = '';
      this.route.params.smart_list_specs = this.navSpecs;

      this.$router.push(this.route)
      //this.$router.push({ name: 'contact_detail', params: { contact_id: goToId, smart_list_specs: specs} })
    },
    getNavBackRoute(){
      if (!this.navSpecs || !this.navSpecs.originRoute) return null;
      if (!this.navSpecs.originRoute.params) return null;
      this.navSpecs.originRoute.params.previous_state = this.navSpecs;
      return this.navSpecs.originRoute;
    },
    loadList(){
      this.$router.push(this.getNavBackRoute());
    }
  },
  mounted(){
    this.reset();
  },
})

</script>
