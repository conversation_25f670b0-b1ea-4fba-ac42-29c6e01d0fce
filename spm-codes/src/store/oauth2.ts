import { Mo<PERSON><PERSON>, ActionContext } from 'vuex'
import { OAuth2 } from '../models'
import { RootState, Oauth2State } from '../store/state_models'

let oauth2Listner

export const oauth2: Module<Oauth2State, RootState> = {
  namespaced: true,
  state: {
    oauth2: [],
    locationId: undefined,
    isReady: false
  },
  getters: {
    getById: (state: Oauth2State) => {
      return (id: string) => state.oauth2.find(x => x.id === id)
    },
    getByType: (state: Oauth2State) => {
      return (type: string) => state.oauth2.filter(x => x.type === type)
    },
    updateContact: (state: Oauth2State) => {
      return state.oauth2.findIndex(oauth => oauth.type === OAuth2.TYPE_CLIO) > -1
    }
  },
  mutations: {
    addUpdate(state: Oauth2State, payload: any) {
      state.oauth2 = payload.oauth2
      if (payload.locationId) {
        state.locationId = payload.locationId
      }
    },
    clearAll: (state: Oauth2State) => {
      if (oauth2Listner) {
        oauth2Listner()
      }

      state.oauth2 = []
      state.locationId = undefined
      state.isReady = false
    }
  },
  actions: {
    syncAll(
      context: ActionContext<Oauth2State, RootState>,
      locationId: string
    ) {

      if (!locationId) {
        context.commit('clearAll')
        return
      }

      if (context.state.locationId === locationId) {
        return context.state.oauth2;
      }

      context.commit('clearAll')
      
      return new Promise((resolve, reject) => {
        if (oauth2Listner) {
          oauth2Listner()
        }

        oauth2Listner = OAuth2.collectionRef()
          .where('locations.' + locationId, '==', true)
          .onSnapshot(snapshot => {
            context.commit('addUpdate', {
              locationId: locationId,
              oauth2: snapshot.docs.map(d => {
                return {
                  id: d.id,
                  ...d.data()
                }
              })
            })

            resolve(context.state.oauth2)
          }, err => resolve())
      })
    },
    logout: {
      root: true,
      handler: async (context: ActionContext<Oauth2State, RootState>) => {
        context.commit('clearAll')
      }
    },
    clearAll: {
      handler: async (context: ActionContext<Oauth2State, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
