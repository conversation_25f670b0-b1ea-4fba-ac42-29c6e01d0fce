<template>
  <div class="dropdown" :class="{'form-group': form}" ref="tags">
    <UITextInputGroup
      :label="form ? 'Tags' :''"
      type="text"
      :class="getClass"
      :placeholder="placeholder"
      v-model="tagSearch"
      @enter="addNewTag(tagSearch)"
      v-if="!disabled && !fetching"
      class="tag-input"
    />
    <div class="d-inline-block align-middle" v-else>
      <moon-loader :loading="fetching" color="#188bf6" size="15px" />
    </div>
    <div class="tag-group mt-2" v-if="form && value.length > 0">
      <div class="tag" v-for="(tag,index) in value" :key="index">
        {{tag}}
        <a @click="removeTag(tag)" v-if="!disabled">
          <i class="icon icon-close"></i>
        </a>
      </div>
    </div>
    <div class="dropdown-menu show tag-options" v-show="tagSearch">
      <span v-for="(existingTag, index) in filteredTags" :key="index+existingTag">
        <a @click="pickTag(existingTag)" class="dropdown-item">
          <span>{{ existingTag }}</span>
        </a>
      </span>
      <span v-if="!exactMatch" class="add-new">
        <a @click="addNewTag(tagSearch)" class="dropdown-item">
          <i class="icon icon-plus"></i>
          {{ tagSearch }}
        </a>
      </span>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Tag, User } from '@/models'
import { UserState } from '@/store/state_models'
import { mapState } from 'vuex'
import clone from 'lodash/clone'
import difference from 'lodash/difference'
import filter from 'lodash/filter'

export default Vue.extend({
  props: {
    value: Array,
    form: {
      default: true
    },
    inputClass: String,
    disabled: {
      default: false
    },
    placeholder:  {
      default: 'Add Tags'
    },
  },
  methods: {
    onClick(e: any) {
      let el: any = this.$refs.tags
      let target = e.target
      if (el && el !== target && !el.contains(target)) {
        this.tagSearch = ''
      }
    },
    removeTag(tag: string) {
      const tags = clone(this.value)
      tags.splice(tags.indexOf(tag), 1)
      this.$emit('input', tags)
      this.$emit('change')
    },
    async fetchData() {
      this.existingTags = (await Tag.getByLocationId(
        this.currentLocationId
      )).map(tag => tag.name)
      this.fetching = false
    },
    pickTag(tag: string) {
      const lowerCaseTag = tag.toLowerCase()
      const tags = clone(this.value)
      tags.push(lowerCaseTag)
      this.tagSearch = ''
      this.$emit('input', tags)
      this.$emit('change')
    },
    async addNewTag(tag: string) {
      if (this.exactMatch) return
      const lowerCaseTags = tag
        .split(',')
        .map((t: string) => t.trim().toLowerCase())

      for (const lowerCaseTag of lowerCaseTags) {
        if (!this.existingTags.includes(lowerCaseTag)) {
          await Tag.createTag(this.currentLocationId, lowerCaseTag)
          this.existingTags.push(lowerCaseTag)
        }
      }

      const tags = clone(this.value)
      tags.push.apply(tags, lowerCaseTags)
      this.tagSearch = ''
      this.$emit('input', tags)
      this.$emit('change')
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    getClass() {
      const css = {}
      //if (this.form) css['form-control'] = this.form
      if (this.inputClass) css[this.inputClass] = true
      return css
    },
    filteredTags(): string[] {
      let filtered = <string[]>difference(this.existingTags, this.value)
      filtered = filter(
        filtered,
        tag => tag.indexOf(this.tagSearch.trim().toLowerCase()) !== -1
      )
      return filtered
    },
    exactMatch(): boolean {
      return (
        this.existingTags.indexOf(this.tagSearch.trim().toLowerCase()) !== -1
      )
    }
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.currentLocationId = id
      this.fetchData()
    }
  },
  data() {
    return {
      currentLocationId: '',
      existingTags: [] as string[],
      tagSearch: '',
      fetching: true
    }
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchData()
  },
  mounted() {
    document.addEventListener('click', this.onClick)
  },
  beforeDestroy() {
    document.removeEventListener('click', this.onClick)
  }
})
</script>
