<template>
  <div>
    <section class="hl_wrapper">
      <section class="hl_wrapper--inner marketplace pb-0" id="marketplace">
        <div class="container-fluid">
          <h3>Add Partner Form</h3>
          <div class="row">
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label for="name">Name</label>
                <input
                  v-model="name"
                  type="text"
                  class="form-control form-light"
                  placeholder="Name"
                />
              </div>
            </div>
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label for="name">Email</label>
                <input
                  v-model="email"
                  type="text"
                  class="form-control form-light"
                  placeholder="Email"
                />
              </div>
            </div>
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label for="name">City</label>
                <input
                  v-model="city"
                  type="text"
                  class="form-control form-light"
                  placeholder="City"
                />
              </div>
            </div>
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label for="name">State</label>
                <input
                  v-model="state"
                  type="text"
                  class="form-control form-light"
                  placeholder="State"
                />
              </div>
            </div>
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label for="name">Country</label>
                <input
                  v-model="country"
                  type="text"
                  class="form-control form-light"
                  placeholder="Country"
                />
              </div>
            </div>
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label for="name">Languages</label>
                <input
                  v-model="languages"
                  type="text"
                  class="form-control form-light"
                  placeholder="English, German, Hindi"
                />
              </div>
            </div>
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label for="name">Experience</label>
                <input
                  v-model="experience"
                  type="number"
                  class="form-control form-light"
                  placeholder="year of experience"
                />
              </div>
            </div>
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label for="name">Location Id</label>
                <input
                  v-model="location_id"
                  type="text"
                  class="form-control form-light"
                  placeholder="location id"
                />
              </div>
            </div>
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label for="name">Website</label>
                <input
                  v-model="website"
                  type="text"
                  class="form-control form-light"
                  placeholder="link to website"
                />
              </div>
            </div>
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label for="name">Facebook Url</label>
                <input
                  v-model="facebook"
                  type="text"
                  class="form-control form-light"
                  placeholder="Facebook account"
                />
              </div>
            </div>
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label for="name">LinkedIn</label>
                <input
                  v-model="linkedin"
                  type="text"
                  class="form-control form-light"
                  placeholder="LinkedIn account"
                />
              </div>
            </div>
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label for="name">Instagram</label>
                <input
                  v-model="instagram"
                  type="text"
                  class="form-control form-light"
                  placeholder="Instagram account"
                />
              </div>
            </div>
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label for="name">Youtube Url</label>
                <input
                  v-model="youtube"
                  type="text"
                  class="form-control form-light"
                  placeholder="Youtube account"
                />
              </div>
            </div>
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label for="name">Twitter Url</label>
                <input
                  v-model="twitter"
                  type="text"
                  class="form-control form-light"
                  placeholder="Twitter account"
                />
              </div>
            </div>
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label for="name">Company</label>
                <input
                  v-model="company"
                  type="text"
                  class="form-control form-light"
                  placeholder="Company"
                />
              </div>
            </div>
          </div>
          <div class="row hl_marketplace--search">
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label>Industry Served</label>
                <vSelect
                  :options="industry_served_options"
                  placeholder="Industry Served"
                  v-model="industries"
                  :clearable="true"
                  :multiple="true"
                ></vSelect>
              </div>
            </div>
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label>Service provided</label>
                <vSelect
                  :options="service_provided_options"
                  placeholder="Services"
                  v-model="services"
                  :clearable="true"
                  :multiple="true"
                ></vSelect>
              </div>
            </div>
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label>Partner Status</label>
                <vSelect
                  :options="partner_staus_options"
                  placeholder="Partner Status"
                  v-model="rank"
                  :clearable="true"
                ></vSelect>
              </div>
            </div>
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label>Badges</label>
                <vSelect
                  :options="badges_options"
                  placeholder="badges"
                  v-model="badges"
                  :clearable="true"
                  :multiple="true"
                ></vSelect>
              </div>
            </div>
          </div>
          <div class="row mb-0">
            <div class="col-md-6 col-lg-3">
              <div class="form-group">
                <label for="name">Profile Photo</label>
                <input
                  v-model="profile_photo"
                  type="text"
                  class="form-control form-light"
                  placeholder="profile photo url"
                />
              </div>
            </div>
            <div class="col-md-6 col-lg-2">
              <div class="marketplace_card--avatar">
                <div class="img-wrap">
                  <img
                    :src="profile_photo"
                    alt="photo preview"
                  />
                </div>
              </div>
            </div>
            <div class="col-md-6 col-lg-7">
              <div class="form-group">
                <label for="name">Description</label>
                <textarea
                  v-model="description"
                  class="form-control form-light"
                  placeholder="A brief bio"
                />
              </div>
            </div>
          </div>
          <div class="mb-5">
                  <button
                type="button"
                class="btn btn-blue"
                @click.prevent="addPartner"
              >
                Add Partner
              </button>
          </div>
          <h3>Search Partner</h3>
          <div class="hl_marketplace--search">
            <div class="row">
              <div class="col-md-6 col-lg-4">
                <div class="form-group">
                  <label>Industry Served</label>
                  <vSelect
                    :options="industry_served_options"
                    placeholder="Industry Served"
                    v-model="industry_served"
                    :clearable="true"
                    :multiple="true"
                  ></vSelect>
                </div>
              </div>
              <div class="col-md-6 col-lg-4">
                <div class="form-group">
                  <label>Service provided</label>
                  <vSelect
                    :options="service_provided_options"
                    placeholder="Partner Status"
                    v-model="service_provided"
                    :clearable="true"
                    :multiple="true"
                  ></vSelect>
                </div>
              </div>
              <!-- <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label>Partner Status</label>
                  <vSelect
                    :options="partner_staus_options"
                    placeholder="Partner Status"
                    v-model="partner_status"
                    :clearable="true"
                  ></vSelect>
                </div>
              </div> -->
              <div class="col-md-6 col-lg-4">
                <div class="form-group">
                  <label>Search Partner</label>
                  <div class="search-form">
                    <i class="icon icon-loupe"></i>
                    <input
                      style="height: 49px"
                      v-model="partner_name"
                      type="text"
                      class="form-control form-light"
                      placeholder="Partner Name"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="!loading">
            <ul class="hl_marketplace--results mb-0">
              <li>
                <Affiliate
                  v-for="affiliate in $store.state.affiliate.affiliateList"
                  :key="affiliate._id"
                  :data="affiliate._source"
                  :id="affiliate._id"
                  :development="true"
                />
              </li>
            </ul>
          </div>
          <div v-else class="my-5">
            <MoonLoader size="30px" radius="50%" />
          </div>
        </div>
      </section>
    </section>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import Affiliate from '../../components/affiliates/Affiliate'
import vSelect from 'vue-select'
import axios from 'axios'
import defaults from '@/config'
import MoonLoader from '../../components/MoonLoader'

export default Vue.extend({
  components: {
    Affiliate,
    vSelect,
    MoonLoader
  },
  data() {
    return {
      industry_served: this.$store.state.affiliate.industry_served,
      industry_served_options: [
        'Advertising / Marketing',
        'Attorney / Legal',
        'Automotive Sales / Repair',
        'Chiro',
        'Computer / Software',
        'Coaching',
        'Construction / Contractors',
        'Consulting',
        'Dental',
        'Design / Architecture / Engineering',
        'Diet / Health / Nutrition',
        'Financial Services',
        'Government Agency',
        'Gym / Crossfit / Personal Training / Martial Arts',
        'Hair / Beauty / Nails',
        'Health / Nutrition',
        'Hospitality',
        'Insurance / Brokage',
        'Jewellery',
        'Lawn Care / Landscaping',
        'Manufacturing',
        'Medical / Health Services',
        'Medspa',
        'Non Profit',
        'Pest / Rodent Control',
        'Plumbing / HVAC',
        'Property Management',
        'Real Estate / Developer',
        'Restaurant / Bar',
        'Retail',
        'Solar / Green Tech',
        'Salon / Beauty',
        'Telecommunications',
        'Transportation',
        'Wholesale Distribution'
      ],
      service_provided: this.$store.state.affiliate.service_provided,
      service_provided_options: [
        'Conversion Rate Optimization',
        'Copy Writing',
        'Facebook & Instagram Ads',
        'Funnel Builds',
        'Google Ads',
        'Graphic Design',
        'HighLevel Builds',
        'Search Engine Optimization',
        'Social Media Management',
        'Video Content',
        'Web Development',
        'YouTube Ads',
        'Other'
      ],
      partner_status: this.$store.state.affiliate.partner_status,
      partner_staus_options: ['Certified Partner', 'Partner'],
      partner_name: this.$store.state.affiliate.partner_name,
      baseURL: defaults.baseUrl,
      waitTime: 700,
      debouncer: undefined as NodeJS.Timer | undefined,
      loading: false,
      badges_options: ['Premier', 'Top', 'Affiliate'],
      badges: [],
      profile_photo: "",
      name: '',
      email: '',
      description: '',
      facebook: '',
      youtube: '',
      twitter: '',
      linkedin: '',
      instagram: '',
      website: '',
      languages: '',
      services: [],
      industries: [],
      rank: '',
      city: '',
      state: '',
      country: '',
      company: '',
      location_id: '',
      experience: ''
    }
  },
  watch: {
    industry_served(newValue) {
      this.$store.commit('affiliate/updateIndustryServed', newValue)
      this.search()
    },
    service_provided(newValue) {
      this.$store.commit('affiliate/updateServiceProvided', newValue)
      this.search()
    },
    partner_status(newValue) {
      this.$store.commit('affiliate/updatePartnerStatus', newValue)
      this.search()
    },
    partner_name(newValue) {
      this.$store.commit('affiliate/updatePartnerName', newValue)
      let _self = this
      if (this.debouncer) clearTimeout(this.debouncer)
      this.debouncer = setTimeout(() => this.search(), this.waitTime)
    }
  },
  mounted() {
    if (this.$store.state.affiliate.affiliateList.length === 0) {
      this.search()
    }
  },
  methods: {
    addPartner() {
      axios
        .post(`${this.baseURL}/partners/update`, {
          name: this.name,
          email: this.email,
          description: this.description,
          profile_photo: this.profile_photo,
          social_media: [
            {
              facebook: this.facebook,
              twitter: this.twitter,
              youtube: this.youtube,
              linkedin: this.linkedin,
              instagram: this.instagram,
              website: this.website,
            }
          ],
          languages: [this.languages],
          services: this.services,
          industries: this.industries,
          rank: this.rank,
          city: this.city,
          state: this.state,
          country: this.country,
          company: this.company,
          locationId: this.location_id,
          experience: this.experience,
          badges: this.badges
        })
        .then(res => {
          console.log(res.data)
          window.location.reload()
        })
        .catch(err => {
          console.log('error adding partner : ', err)
        })
    },
    update() {
      axios.post(`${this.baseURL}/partners/update`, {
        id: 'EPU5rGepYvUMfg79ebLn',
        name: 'Varun Vairavan',
        dateAdded: 1582009681593,
        services: ['Video Content', 'Web Development', 'YouTube Ads'],
        industries: [
          'Advertising / Marketing',
          'Attorney',
          'Automotive Sales / Repair'
        ],
        rank: ['Certified Partner'],
        profile_photo:
          'https://ca.slack-edge.com/TBL752DM0-UBL0MHZPW-289e5e3c88c5-512'
      })
    },
    async search() {
      this.loading = true
      const result = await axios.post(`${this.baseURL}/partners/search`, {
        filters: [
          { name: 'name', value: this.partner_name },
          { name: 'services', value: this.service_provided },
          { name: 'industries', value: this.industry_served },
          { name: 'rank', value: this.partner_status }
        ]
      })
      this.loading = false
      this.$store.commit('affiliate/updateAffiliateList', result.data.hits.hits)
      console.log('value : ', result)
    },
    delete() {}
  }
})
</script>

<style>
.hl_marketplace--search .v-select {
  background: white !important;
}
.hl_marketplace--search .v-select .vs__actions {
  cursor: pointer;
  padding-right: 8px;
}
.hl_marketplace--search .v-select .vs__selected {
  white-space: nowrap;
  height: 2rem;
  align-items: center;
}
.hl_marketplace--search .v-select .vs__selected-options {
  width: 90%;
  overflow: auto;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}
.hl_marketplace--search .v-select .vs__selected-options::-webkit-scrollbar {
  display: none;
}
.hl_marketplace--search .v-select .vs__search::placeholder {
  color: #2a3135;
  opacity: 0.5;
}
.hl_marketplace--search .v-select .vs__dropdown-menu {
  border: none;
  width: auto;
  min-width: 100%;
}
.hl_marketplace--search .v-select .vs__dropdown-menu .vs__dropdown-option {
  padding: 8px;
}
.hl_marketplace--search .dropdown {
  display: flex !important;
}
.hl_marketplace--search div .bootstrap-select #select-dropdown .dropdown-menu {
  width: inherit !important;
}
.hl_marketplace--results {
  list-style: none;
  padding: 0;
  margin: 0;
  margin-bottom: 40px;
}
.hl_marketplace--results li > a {
  display: block;
  color: #607179;
  border-radius: 4px;
}
.hl_marketplace--results li > a:hover .card {
  -webkit-box-shadow: 15px 26px 40px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 15px 26px 40px 0 rgba(0, 0, 0, 0.05);
}
</style>
