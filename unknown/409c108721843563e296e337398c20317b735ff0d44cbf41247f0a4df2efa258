<template>
  <div id="review-sentiment" class="card">
    <div class="card-header card-header--compact space-between">
      <h2>Sentiment</h2>
      <div>
        <span>{{ filter }}</span>
        <select class="selectpicker more-select" v-model="filter">
          <option>This Week</option>
          <option>Last Week</option>
          <option>This Month</option>
          <option>Last 6 Months</option>
          <option>This Year</option>
        </select>
      </div>
    </div>
    <div class="card-body">
      <div class="sentiment_review --positive">
        <div class="sentiment_review-inner">
          <p>Positive</p>
          <div class="sentiment_review-graph" id="sentiment_positive-review">
            <span
              class="percentage"
              :style="{ height: 100 - getPositivePercent + '%' }"
            ></span>
          </div>
          <h3>{{ this.currentReviewsAggregate.positiveReviews }}</h3>
          <h4>
            <i
              v-if="getPositivePercent < 0"
              class="icon icon-arrow-down-2 --red"
            ></i>
            <i v-else class="icon icon-arrow-up-2"></i>

            {{ getPositivePercent }}%
          </h4>
        </div>
      </div>

      <div class="sentiment_review --negative">
        <div class="sentiment_review-inner">
          <p>Negative</p>
          <div class="sentiment_review-graph" id="sentiment_negative-review">
            <span
              class="percentage"
              :style="{ height: 100 - getNegativePercent + '%' }"
            ></span>
          </div>
          <h3>{{ this.currentReviewsAggregate.negativeReviews }}</h3>
          <h4>
            <i
              v-if="getNegativePercent < 0"
              class="icon icon-arrow-down-2 --green"
            ></i>
            <i v-else class="icon icon-arrow-up-2"></i>
            {{ getNegativePercent }}%
          </h4>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { ReviewAggregateData } from '@/store/state_models'

declare var $: any

export default Vue.extend({
  data() {
    return {
      filter: 'Last 6 Months',
      currentLocationId: '',
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.filter = 'Last 6 Months'
    },
    filter: function () {
      if (this.filter === 'This Week') {
        this.$store.dispatch(
          'reviewAggregate/fetchThisWeek',
          this.currentLocationId
        )
      } else if (this.filter === 'Last Week') {
        this.$store.dispatch(
          'reviewAggregate/fetchLastWeek',
          this.currentLocationId
        )
      } else if (this.filter === 'This Month') {
        this.$store.dispatch(
          'reviewAggregate/fetchThisMonth',
          this.currentLocationId
        )
      } else if (this.filter === 'Last 6 Months') {
        this.$store.dispatch(
          'reviewAggregate/fetchLast6Months',
          this.currentLocationId
        )
      } else if (this.filter === 'This Year') {
        this.$store.dispatch(
          'reviewAggregate/fetchThisYear',
          this.currentLocationId
        )
      }
    },
  },
  computed: {
    currentReviewsAggregate(): ReviewAggregateData {
      return this.$store.getters['reviewAggregate/current'](this.filter)
    },
    getPositivePercent(): string {
      const total =
        this.currentReviewsAggregate.positiveReviews +
        this.currentReviewsAggregate.negativeReviews
      const baseline = total ? total : 1
      return (
        (this.currentReviewsAggregate.positiveReviews / baseline) *
        100
      ).toFixed(0)
    },
    getNegativePercent(): string {
      const total =
        this.currentReviewsAggregate.positiveReviews +
        this.currentReviewsAggregate.negativeReviews
      const baseline = total ? total : 1
      return (
        (this.currentReviewsAggregate.negativeReviews / baseline) *
        100
      ).toFixed(0)
    },
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
})
</script>

<style lang="scss" scoped>
#review-sentiment {
  .space-between {
    display: flex;
    justify-content: space-between;
  }

  .card-header--compact {
    padding: 12px 50px 10px 30px;
  }

  .card-header {
    span {
      color: #afb8bc;
    }
    .more-select {
      top: 53%;
      transform: translateY(-53%);
    }
  }

  .card-body {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.hl_dashboard--sentiment .card-body {
  padding-top: 40px;
  padding-bottom: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}
.hl_dashboard--sentiment hr {
  max-width: 100px;
  margin-top: 30px;
  margin-bottom: 30px;
}
.sentiment_review {
  text-align: center;

  &:first-child {
    margin-right: 50px;
  }
}

.sentiment_review-inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  flex-direction: column;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: 200px;
  margin-left: auto;
  margin-right: auto;
  p {
    color: #afb8bc;
    margin-bottom: 10px;
    font-size: 0.8rem;
  }
}
.sentiment_review h3 {
  font-size: 2rem;
  font-weight: 500;
}
.sentiment_review h4 {
  font-size: 1rem;
  font-weight: 500;
}
.sentiment_review-graph {
  width: 50px;
  height: 50px;
  position: relative;
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 10px;
}
.sentiment_review-graph .percentage {
  display: block;
  width: 50px;
  background: rgba(255, 255, 255, 0.5);
  position: absolute;
  margin: auto;
  top: 0;
  left: 0;
  right: 0;
}
.sentiment_review.--positive h4 {
  color: #37ca37;
}
.sentiment_review.--positive .sentiment_review-graph {
  background: url('/pmd/img/icon-positive.png') no-repeat center;
  background-size: 50px;
}
.sentiment_review.--negative h4 {
  color: #e93d3d;
}
.sentiment_review.--negative .sentiment_review-graph {
  background: url('/pmd/img/icon-negative.png') no-repeat center;
  background-size: 50px;
}

.sentiment_review-inner h3,
.sentiment_review-inner h4 {
  width: 60px;
}
</style>




