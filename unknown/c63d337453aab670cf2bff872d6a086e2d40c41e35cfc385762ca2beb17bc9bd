<template>
  <div
    class="modal fade hl_add-opportunities--modal"
    id="add-opportunities-modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title" v-if="editMode">
              <i class="icon icon-edit"></i>
              Edit opportunity -- {{ windowTitle }}
            </h2>
            <h2 class="modal-title" v-else>
              <i class="icon icon-plus"></i>
              Add opportunity -- {{ windowTitle }}
            </h2>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body" v-if="mounted">
          <div class="modal-body--inner">
            <ul
              class="nav nav-tabs"
              id="myTab"
              role="tablist"
              v-if="mode !== 'only-create'"
            >
              <li
                class="nav-item"
                @click.prevent="
                  currentDetailsTabComponent = 'OpportunityComponent'
                "
              >
                <a
                  :class="{
                    active:
                      currentDetailsTabComponent === 'OpportunityComponent'
                  }"
                  class="nav-link"
                  id="tab1-tab"
                  data-toggle="tab"
                  href="#tab1"
                  role="tab"
                  aria-controls="tab1"
                  aria-selected="true"
                  >Opportunity</a
                >
              </li>
              <li
                class="nav-item"
                @click.prevent="
                  currentDetailsTabComponent = 'AppointmentComponent'
                "
              >
                <a
                  :class="{
                    active:
                      currentDetailsTabComponent === 'AppointmentComponent'
                  }"
                  class="nav-link"
                  id="tab2-tab"
                  data-toggle="tab"
                  href="#tab2"
                  role="tab"
                  aria-controls="tab2"
                  aria-selected="false"
                  >Add/Edit Appointment</a
                >
              </li>
              <li
                class="nav-item"
                @click.prevent="currentDetailsTabComponent = 'TaskComponent'"
              >
                <a
                  :class="{
                    active: currentDetailsTabComponent === 'TaskComponent'
                  }"
                  class="nav-link"
                  id="tab3-tab"
                  data-toggle="tab"
                  href="#tab3"
                  role="tab"
                  aria-controls="tab3"
                  aria-selected="false"
                  >Tasks</a
                >
              </li>
              <li
                class="nav-item"
                @click.prevent="currentDetailsTabComponent = 'NoteComponent'"
                v-if="meta && meta.contactId && meta.contactId != ''"
              >
                <a
                  :class="{
                    active: currentDetailsTabComponent === 'NoteComponent'
                  }"
                  class="nav-link"
                  id="tab4-tab"
                  data-toggle="tab"
                  href="#tab4"
                  role="tab"
                  aria-controls="tab4"
                  aria-selected="false"
                  >Notes</a
                >
              </li>
            </ul>
            <div class="tab-content" id="myTabContent">
              <OpportunityComponent
                v-show="currentDetailsTabComponent === 'OpportunityComponent'"
                :bus="bus"
                :users="users"
                :opportunity="opportunity"
                :mode="mode"
              />
              <AppointmentComponent
                v-show="currentDetailsTabComponent === 'AppointmentComponent'"
                :bus="bus"
                :opportunity="opportunity"
                :meta="meta"
              />
              <TaskComponent
                v-show="currentDetailsTabComponent === 'TaskComponent'"
                :bus="bus"
                :users="users"
                :opportunity="opportunity"
                :meta="meta"
              />

              <div
                class="row"
                v-show="currentDetailsTabComponent === 'NoteComponent'"
              >
                <div class="col-xl-12">
                  <NotesListComponent
                    v-if="opportunity"
                    :passedInContactId="meta.contactId"
                    @editNote="editNote"
                    :heading="false"
                    :mode="'modal'"
                  />
                  <NoteComponent
                    v-if="opportunity"
                    :noteIdPassIn="editNoteId"
                    :passedInContactId="meta.contactId"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div
            v-if="errorMessage"
            class="error --red"
            style="text-align: center;margin-bottom: 10px;"
          >
            {{ errorMessage }}
          </div>
          <div class="modal-footer--inner nav between">
            <div>
              <UIButton
                :class="{ invisible: saving }"
                type="button"
                use="danger"
                data-dismiss="modal"
                style="color:#e93d3d;"
                @click.prevent="deleteOpportunity"
              >
                Delete
              </UIButton>
            </div>
            <div>
              <UIButton
                style="margin-right: 8px;"
                :class="{ invisible: saving }"
                type="button"
                use="outline"
                data-dismiss="modal"
              >
                Cancel
              </UIButton>
              <div style="display: inline-block;position: relative;">
                <UIButton
                  type="button"
                  use="primary"
                  :loading="saving"
                  @click.prevent="save"
                >
                  <span v-if="editMode">Update</span>
                  <span v-else>Add</span>
                </UIButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Pipeline, Stage, Status, Contact, User } from '@/models'
import Opportunity from '@/models/api/opportunity'
const OpportunityComponent = () => import('./OpportunityComponent.vue')
const AppointmentComponent = () => import('./AppointmentComponent.vue')
const TaskComponent = () => import('./TaskComponent.vue')
const NoteComponent = () => import('@/pmd/components/agency/NoteComponent.vue')
const NotesListComponent = () =>
  import('@/pmd/components/agency/NotesListComponent.vue')
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'

declare var $: any
const bus = new Vue()
export default Vue.extend({
  props: ['values', 'users', 'mode', '_contact'],
  components: {
    AppointmentComponent,
    TaskComponent,
    OpportunityComponent,
    NotesListComponent,
    NoteComponent
  },
  data() {
    return {
      saving: false,
      editMode: false,
      currentDetailsTabComponent: 'OpportunityComponent',
      opportunity: undefined as Opportunity | undefined,
      pipeline: {} as Pipeline,
      bus,
      saveCount: 0,
      mounted: false,
      errorMessage: '',
      error: false,
      meta: {
        contactId: '',
        currentLocationId: ''
      },
      editNoteId: '',
      windowTitle: ''
    }
  },
  methods: {
    async deleteOpportunity() {
      if (!this.opportunity?.id) return
      const opportunity = this.opportunity
      if (confirm('Are you sure you want to delete this opportunity?')) {
        await opportunity.delete()
        this.$emit('hidden', opportunity)
      }
    },
    async save() {
      this.saveCount = 2
      this.errorMessage = ''
      this.error = false
      this.saving = true
      this.bus.$emit('save_opportunity')
    },
    saveCompleteHandler(error?: { tab: string; msg?: string }) {
      if (this.error) {
        return false
      }
      if (error) {
        this.saving = false
        this.currentDetailsTabComponent = error.tab
        if (error.msg) this.errorMessage = error.msg
        this.error = true
        return false
      }
      return true
    },
    refresh(extras: { contactId: string; currentLocationId: string }) {
      this.meta = extras
    },
    editNote(noteId: string) {
      this.editNoteId = noteId
    }
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
    $(this.$refs.modal).modal('hide')
    this.bus.$off('update_contact')
    this.bus.$off('save_opportunity_complete')
    this.bus.$off('complete')
  },
  watch: {
    async values(values: { [key: string]: any }) {
      const data: () => object = <() => object>this.$options.data
      const $selectpicker = $(this.$el).find('.selectpicker')
      data.mounted = true

      if (data) Object.assign(this.$data, data.apply(this))
      this.mounted = true
      if (values.visible) {
        $(this.$refs.modal).modal('show')
      } else {
        $(this.$refs.modal).modal('hide')
        $('#tab1-tab').tab('show') // Hot-fix
      }

      this.errors.clear()
      if (!values.visible) return
      this.pipeline = values.pipeline
      const meta = {
        contactId: '',
        currentLocationId: values.currentLocationId
      }
      if (values.opportunity) {
        this.editMode = true
        this.opportunity = await Opportunity.getById(values.opportunity.id)
        meta.contactId = this.opportunity?.contactId
      } else {
        const opportunity = new Opportunity({})
        if (this.onlyOwnData) opportunity.assignedTo = this.user.id
        opportunity.locationId = values.currentLocationId
        if (this.pipeline) {
          const stages = lodash.sortBy(this.pipeline.stages, ['position'])
          opportunity.pipelineId = this.pipeline.id
          if (stages.length > 0) {
            opportunity.pipelineStageId = stages[0].id
          }
        }
        opportunity.status = Status.Open
        if (values.contact) {
          opportunity.contactId = values.contact.id
          opportunity.name = values.contact.fullName
        } else {
          opportunity.contactId = ''
        }
        this.opportunity = opportunity
      }
      if (values.tab) {
        this.currentDetailsTabComponent = values.tab
      }
      this.meta = meta

      if (this.opportunity && this.opportunity.name) {
        this.windowTitle = this.opportunity.name
      } else if (this.meta && this.meta.contactId) {
        if (this._contact) {
          this.windowTitle = this._contact.name
        } else if (this.opportunity && this.opportunity.contact && this.opportunity.contact.name) {
          this.windowTitle = this.opportunity.contact.name
        } else if (this.opportunity && this.opportunity.contact && this.opportunity.contact.email) {
          this.windowTitle = this.opportunity.contact.email
        }
      }
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    onlyOwnData() {
      return (
        this.user &&
        this.user.role === 'user' &&
        this.user.permissions.assigned_data_only === true
      )
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
  mounted() {
    const _self = this

    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hidden')
      $('#tab1-tab').tab('show') // Hot-fix
    })

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
      const $selectpicker = $(this.$el).find('.selectpicker')

      this.$nextTick(() => {
        $selectpicker.selectpicker()
      })
    }

    this.bus.$on('update_contact', this.refresh)

    this.bus.$on(
      'save_opportunity_complete',
      (payload?: {
        contactId?: string
        error?: { tab: string; msg?: string }
      }) => {
        if (payload.contactId || _self.saveCompleteHandler(payload.error)) {
          this.bus.$emit('save_appointment', payload.contactId)
          this.bus.$emit('save_task', payload.contactId)
        }
      }
    )

    this.bus.$on(
      'complete',
      async (
        error?: { tab: string; msg?: string },
        assignedToUserId?: string
      ) => {
        if (assignedToUserId) {
          this.opportunity.assignedTo = assignedToUserId
          await this.opportunity.save()
        }
        // console.log("Got complete")
        if (_self.saveCompleteHandler(error)) {
          this.saveCount--
          if (this.saveCount === 0) {
            this.opportunity.indexVersion = this.opportunity.indexVersion
              ? this.opportunity.indexVersion + 1
              : 1
            //const oldStatus = await this.opportunity.getOldValue('status')
            //const oldPipelineId = await this.opportunity.getOldValue('pipelineStageId') || '';
            await this.opportunity.save()
            this.$store.dispatch(
              'opportunities/updateOppsInState',
              this.opportunity
            )
            //await Opportunity.updateIndex(this.opportunity, oldStatus)
            // await Opportunity.updateStats({opportunity: {...this.opportunity.data, id: this.opportunity.id}, pipeline_stage_id_old: oldPipelineId, event_type: 'edit-modal', user: this.user });
            this.saving = false
            this.$emit('hidden', this.opportunity)
          }
        }
      }
    )

    this.bus.$on(
      'cancel_saving',
      async (error?: { tab: string; msg?: string }) => {
        this.saving = false
        _self.saveCompleteHandler(error)
      }
    )
  }
})
</script>
