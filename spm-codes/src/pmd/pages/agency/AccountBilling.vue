<template>
	<div>
		<div class="container-fluid">
			<div class="hl_controls">
				<div class="hl_controls--left">
					<h1>Payment method</h1>
				</div>
			</div>
			<div class="card">
				<div class="card-header --center" v-if="company">
					<h2 v-if="company.cardBrand">
						<MasterCardIcon />
						{{company.cardBrand}} •••• {{company.cardLast4}}
					</h2>
					<h2 v-if="!company.cardBrand">
						<MasterCardIcon /> Add New Card
					</h2>
				</div>
				<div class="card-body" style="max-width:100%">
					<form action="/charge" method="post" id="payment-form">
						<div class="row"></div>
						<div class="col-sm-12">
							<div class="form-group">
								<label>Credit card number</label>
								<div class="form-control" id="card-element">
									<!-- A Stripe Element will be inserted here. -->
								</div>
								<div id="card-errors" class="help is-danger" role="alert"></div>
							</div>
						</div>
						<!-- <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label>Credit card number</label>
                                    <input type="type" class="form-control" placeholder="Credit card number" v-model="cardNumber" id="cardNumber">
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="row">
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label>Exp. Month</label>

                                            <select class="selectpicker" ref="card_month_picker" v-model="cardMonth" id="cardMonth">
                                                <option value="">Month</option>
                                                <option v-for="index in 12" :key="index" :value="index">{{getMonth(index)}}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <label>Exp. Year</label>
                                            <select class="selectpicker" ref="card_year_picker" v-model="cardYear">
                                                <option value="">Year</option>
                                                <option v-for="index in 8" :key="index" :value="getYear(index)">{{getYear(index)}}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <label>CCV</label>
                                            <input type="type" class="form-control" placeholder="CCV" v-model="cardCvc">
                                        </div>
                                    </div>
                                </div>
                            </div>
						</div>-->
						<div class="hl_agency-billing-btns text-right" v-if="!loading">
							<button type="button" class="btn btn-md btn-light3" @click.stop="cancel">Cancel</button>
							<button type="button" class="btn btn-md btn-success" @click.stop="tokenize">Save</button>
						</div>
						<div v-if="loading" class="spinner">
							<moon-loader :loading="loading" color="#37ca37" size="30px"/>
						</div>
					</form>
				</div>
			</div>
			<br>
			<div class="hl_controls" v-if="invoices.length > 0">
				<div class="hl_controls--left">
					<h1>Billing history</h1>
				</div>
			</div>
			<div class="card hl_agency-billing--table" v-if="invoices.length > 0">
				<div class="card-body --no-padding" style="max-width:100%">
					<div class="table-wrap">
						<table class="table">
							<tbody>
								<tr
									v-for="invoice in invoices"
									v-if="invoice.status !== 'voided' && invoice.status !== 'void'"
								>
									<td>{{invoice.number}}</td>
									<td>Invoice for {{getInvoiceDate(invoice.date)}}</td>
									<td v-if="company">{{company.cardBrand}} •••• {{company.cardLast4}}</td>
									<td>${{invoice.total/100 | formatNumber}}</td>
									<td>
										<a :href="invoice.invoice_pdf">
											<i class="icon-eye"></i>
										</a>
										<!-- <select class="selectpicker more-select">
                                            <option>Option 1</option>
                                            <option>Option 2</option>
                                            <option>Option 3</option>
										</select>-->
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- END of .hl_agency-dashboard -->
</template>



<script lang='ts'>
import Vue from 'vue';
import { mapState } from 'vuex';
import { CompanyState } from '@/store/state_models';
import { Company, Location, User, Notification, NotificationType, AuthUser, CustomerType } from '@/models';
import config from "../../../config";
import * as moment from 'moment-timezone';
import MasterCardIcon from '@/assets/pmd/img/icon-mastercard.svg'

declare var Stripe: any;

export default Vue.extend({
	data() {
		return {
			stripe: undefined as any,
			loading: false,
			invoices: [] as { [key: string]: any }[],
			card: undefined as any,
			authUser: AuthUser
		}
	},
	components: {
		MasterCardIcon,
	},
	computed: {
		...mapState('company', {
			company: (s: CompanyState) => {
				return s.company ? new Company(s.company) : undefined;
			},
		}),
	},
	async created() {
		var _self = this;

		let recaptchaScript = document.createElement('script')
		recaptchaScript.onload = () => {
			_self.stripe = Stripe(config.stripeKey);
			// Create an instance of Elements.
			var elements = _self.stripe.elements();

			// Custom styling can be passed to options when creating an Element.
			// (Note that this demo uses a wider set of styles than the guide below.)
			var style = {
				base: {
					color: '#2a3135',
					lineHeight: '21px',
					fontFamily: 'Roboto,system,-apple-system,BlinkMacSystemFont,".SFNSDisplay-Regular","Helvetica Neue",Helvetica,Arial,sans-serif',
					fontSmoothing: 'antialiased',
					fontSize: '14px',
					'::placeholder': {
						color: '#aab7c4'
					}
				},
				invalid: {
					color: '#fa755a',
					iconColor: '#fa755a'
				}
			};

			// Create an instance of the card Element.
			this.card = elements.create('card', { style: style });

			// Add an instance of the card Element into the `card-element` <div>.
			this.card.mount('#card-element');

			this.card.addEventListener('change', function (event: any) {
				var displayError = document.getElementById('card-errors') || document.createElement('div');
				if (event.error) {
					displayError.textContent = event.error.message;
				} else {
					displayError.textContent = '';
				}
			});
		}

		recaptchaScript.setAttribute('src', 'https://js.stripe.com/v3/')
		document.head.appendChild(recaptchaScript)
		try {
			this.authUser = await this.$store.dispatch('auth/get');
			let resp = await this.$http.get("/api/stripe/invoices");
			this.invoices = resp.data;
		} catch (e) { }
	},
	methods: {
		cancel() {
			this.card.clear();
		},
		getInvoiceDate(date: number) {
			return moment.unix(date).format("MMMM, YYYY")
		},
		async tokenize() {
			this.loading = true;

			const { token, error } = await this.stripe.createToken(this.card);
			if (error) {
				console.log(error);
				this.loading = false;
				return;
			}

			let params = {
				"card_token": token
			}
			try {
			let resp = await this.$http.post('/api/stripe/save', params);
			this.card.clear();
			this.loading = false;
			setTimeout(() => { alert("Successfully added credit card."); }, 500);
			} catch (ex) {
				alert("Error: " + ex.msg + ".")
			}
		}
	}
});
</script>

<style scoped>
.spinner {
	margin: 10px 0px;
	width: 85px;
	float: right;
}
</style>
