import Vue from 'vue'
import { Modu<PERSON>, ActionContext } from 'vuex'
import { RootState, MailGunServiceState } from './state_models'
import { MailGunAccount } from '../models';

let mailgunServiceListener;
let companyMailGunServiceListener;
export const mailgunServices: Module<MailGunServiceState, RootState> = {
  namespaced: true,
  state: { mailgunLocationWithErrors: undefined, locationId: undefined, mailgunCompanyWithErrors: undefined, companyId: undefined },
  getters: {
    locationMailGunErrorMsg: (state: MailGunServiceState, getters, rootState, rootGetters) => {
      if (state.mailgunLocationWithErrors) {
        const location = rootGetters['locations/getById'](state.locationId)
        return `There is a problem with the MailGun integration of ${location.name}.`;
      } else {
        return null;
      }
    },
    companyMailGunErrorMsg: (state: MailGunServiceState, getters) => {
      if (state.mailgunCompanyWithErrors) {
        return 'There is a problem with the MailGun integration of this agency.';
      } else {
        return null;
      }
    }
  },
  mutations: {
    setLocationError: (
      state: MailGunServiceState,
      payload: { mailgunAccountErrors: string, locationId: string }
    ) => {
      state.mailgunLocationWithErrors = payload.mailgunAccountErrors
      if (payload.locationId) {
        state.locationId = payload.locationId
      }
    },
    setCompanyServices: (
      state: MailGunServiceState,
      payload: { mailgunAccountErrors: string; companyId: string }
    ) => {
      state.mailgunCompanyWithErrors = payload.mailgunAccountErrors
      if (payload.companyId) {
        state.companyId = payload.companyId
      }
    },
    clearLocation(state: MailGunServiceState) {
      if (mailgunServiceListener) {
        mailgunServiceListener();
      }

      state.mailgunLocationWithErrors = undefined
      state.locationId = undefined
    },
    clearCompany(state: MailGunServiceState) {
      if (companyMailGunServiceListener) {
        companyMailGunServiceListener();
      }

      state.mailgunCompanyWithErrors = undefined
      state.companyId = undefined
    }
  },
  actions: {
    async syncAll(
      context: ActionContext<MailGunServiceState, RootState>,
      locationId: string
    ) {
      if (!locationId) {
        context.commit('clearLocation')
        return
      }


      if (context.state.locationId === locationId) {
        return context.state.mailgunLocationWithErrors
      }

      return new Promise((resolve, reject) => {

        if (mailgunServiceListener) {
          mailgunServiceListener();
        }

        mailgunServiceListener = MailGunAccount.getByLocationIdRealtime(locationId)
          .onSnapshot(snapshot => {
            if (snapshot.docs && snapshot.docs[0]) {
              const mailgunAccount = new MailGunAccount(snapshot.docs[0])
              context.commit('setLocationError', {
                locationId: locationId,
                mailgunAccountErrors: mailgunAccount.mailgunError || undefined
              })
            }

            resolve(context.state.mailgunLocationWithErrors)
          }, err => resolve())
      })
    },
    async companySyncAll(
      context: ActionContext<MailGunServiceState, RootState>,
      companyId: string
    ) {
      if (!companyId) {
        context.commit('clearCompany')
        return
      }


      if (context.state.companyId === companyId) {
        return context.state.mailgunCompanyWithErrors
      }

      return new Promise((resolve, reject) => {

        if (companyMailGunServiceListener) {
          companyMailGunServiceListener();
        }

        companyMailGunServiceListener = MailGunAccount.getByCompanyIdRealtime(companyId)
          .onSnapshot(snapshot => {
            if (snapshot.docs && snapshot.docs[0]) {
              const mailgunAccount = new MailGunAccount(snapshot.docs[0])
              context.commit('setCompanyServices', {
                companyId: companyId,
                mailgunAccountErrors: mailgunAccount.mailgunError || undefined
              })
            }

            resolve(context.state.mailgunCompanyWithErrors)
          }, err => resolve())
      })
    },
    logout: {
      root: true,
      handler: (context: ActionContext<MailGunServiceState, RootState>) => {
        context.commit('clearLocation')
        context.commit('clearCompany')
      }
    },
    clearAll: {
      handler: (context: ActionContext<MailGunServiceState, RootState>) => {
        context.commit('clearLocation')
        context.commit('clearCompany')
      }
    }
  }
}
