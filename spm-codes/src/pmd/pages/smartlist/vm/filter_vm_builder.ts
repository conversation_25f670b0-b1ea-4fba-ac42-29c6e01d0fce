import { <PERSON>ilter, Filter } from './filter_vm'
import { FilterOption, IListFetch } from './option_vm'
import { IKeyVal } from './interfaces'
import {
  <PERSON><PERSON>atter,
  A<PERSON>yF<PERSON>atter,
  De<PERSON><PERSON><PERSON><PERSON>atter,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er,
  DateRangeFormatter,
} from './option_formatter'
import {
  Tag,
  Campaign,
  User,
  Opportunity,
  Pipeline,
  CustomField,
} from '@/models'
import store from '@/store'
import moment from 'moment-timezone'
import { Utils } from '../../../../util/utils'
import {
  sortBy as l_SortBy,
  startCase as lodashStartCase,
  lowerCase as lodashLowerCase,
} from 'lodash'
import { MessageType } from '../../../../models/message'
import axios from 'axios'
interface IBuilderStrategy {
  create(name: string, id?: string, extras?: { [key: string]: any }): IFilter
}

interface IDict {
  [key: string]: IBuilderStrategy
}

interface IFilterBuilder {
  filterName: string
  builder: IBuilderStrategy
  customFieldId?: string
  customFieldType?: string
}

interface IOptionBuilder {
  label: string
  displayLabel?: string
  selected?: boolean
  hasInput?: boolean
  listFetch?: IListFetch
  listFetch2?: IListFetch
  isAutoComplete?: boolean
  limitItems?: number
  example?: string
  nextFilters: IFilter[]
  firstValueMandatory: boolean
  secondValueMandatory: boolean
  isDateRange: boolean
}

// tslint:disable-next-line: max-classes-per-file
class RegularBuilder<T> implements IBuilderStrategy {
  private options: IOptionBuilder[]
  private formatter: IFormatter

  constructor(formatter: IFormatter, options: IOptionBuilder[]) {
    if (!options || !options.length || options.length <= 0)
      throw new Error('Apply options to build a filter')
    if (!formatter) throw Error('Apply formatter to build filter')
    this.options = options
    this.formatter = formatter
  }

  public create(
    name: string,
    id: string = null,
    extras: { [key: string]: any } = null
  ): IFilter {
    const filter = Filter.newInstance(name, id, extras)
    filter.extras = extras
    filter.options = []
    if (this.options) {
      let anyselected = false
      this.options.forEach((x: IOptionBuilder) => {
        const c = new FilterOption<T>(
          filter,
          x.label || '!--err',
          this.formatter,
          x.displayLabel
        )
        c.secondValueMandatory = x.secondValueMandatory || false
        c.firstValueMandatory =
          x.hasInput === true || x.firstValueMandatory || false // Setting firstValueMandatory is very important
        c.selected = x.selected === true
        c.nextFilters = x.nextFilters
        c.listFetch = x.listFetch
        c.listFetch2 = x.listFetch2
        c.limitItems = x.limitItems
        c.isAutoComplete = x.isAutoComplete
        c.example = x.example
        if (c.selected) anyselected = true
        filter.options.push(c)
      })
      if (!anyselected && filter.options.length > 0)
        filter.options[0].selected = true
    }
    filter.updateTitle()
    return filter
  }
}

// tslint:disable-next-line: max-classes-per-file
class DateRangeBuilder implements IBuilderStrategy {
  private showIsEmpty: boolean = false
  private showIsNotEmpty: boolean = false

  constructor(showIsEmpty: boolean = false, showIsNotEmpty: boolean = false) {
    this.showIsEmpty = showIsEmpty
    this.showIsNotEmpty = showIsNotEmpty
  }

  public create(
    name: string,
    id: string = null,
    extras: { [key: string]: any } = null
  ): IFilter {
    const df = new DefaultFormatter()
    const filter = Filter.newInstance(name, id, extras)
    filter.options = []

    if (this.showIsEmpty)
      filter.options.push(new FilterOption<boolean>(filter, 'is empty', df))
    if (this.showIsNotEmpty)
      filter.options.push(new FilterOption<boolean>(filter, 'is not empty', df))

    const timeZone = store.getters[`locations/getCurrentLocationTimeZone`]
    let timeZoneShortForm = moment.tz(timeZone).zoneAbbr() // .format("Z z") .format("z")
    const mt = new FilterOption<number>(filter, 'more than', df)
    mt.example = `*per ${timeZoneShortForm} time zone`
    mt.firstValueMandatory = true
    mt.secondValueMandatory = true
    mt.showSecondValueOptions = false
    mt.secondValue = mt.defaultSecondVal = 'days ago'
    mt.firstValue = mt.defaultFirstVal = 1
    mt.secondValueOptions = [
      'minutes ago',
      'hours ago',
      mt.secondValue,
      'months ago',
      'years ago',
    ]
    filter.options.push(mt)

    const lt = new FilterOption<number>(filter, 'less than', df)
    lt.example = `*per ${timeZoneShortForm} time zone`
    lt.firstValueMandatory = true
    lt.secondValueMandatory = true
    lt.showSecondValueOptions = true
    lt.selected = false
    lt.firstValue = lt.defaultFirstVal = 1
    lt.secondValue = lt.defaultSecondVal = 'days ago'
    lt.secondValueOptions = [
      'minutes ago',
      'hours ago',
      lt.secondValue,
      'months ago',
      'years ago',
    ]
    filter.options.push(lt)
    filter.updateTitle()

    const range = new FilterOption<string>(
      filter,
      'range',
      new DateRangeFormatter()
    )
    range.example = `*per ${timeZoneShortForm} time zone`
    range.selected = true
    range.isDateRange = true
    const currMoment = moment().tz(timeZone)
    range.firstValue = `${currMoment
      .startOf('day')
      .format('YYYY-MM-DD hh:mm a')}`
    range.secondValue = `${currMoment
      .endOf('day')
      .format('YYYY-MM-DD hh:mm a')}`
    // console.log(`SO ${range.firstValue}`)
    // console.log(`EO ${range.secondValue}`)
    filter.options.push(range)
    filter.updateTitle()

    filter.onQueryTransformation = (payload: any): any => {
      if (!payload) return payload
      if (!payload.extras) payload.extras = {}
      payload.extras.timeZone = timeZone
      return payload
    }
    // console.log(`DateRange query filter using timezone ${locTZ}`)
    // filter.options.forEach(a=> {
    //   const tzFilterBuilder = FilterBuilder.justIsBuilder<string>();
    //   const tzFilter = tzFilterBuilder.create('time_zone');
    //   tzFilter.cannotPersist = true;
    //   tzFilter.hidden = true;
    //   tzFilter.selectedOption.firstValue = locTZ;
    //   a.nextFilters = [tzFilter];
    // });

    return filter
  }
}

class AgeQueryBuilder implements IBuilderStrategy {
  public create(
    name: string,
    id: string = null,
    extras: { [key: string]: any } = null
  ): IFilter {
    const df = new DefaultFormatter()
    const filter = Filter.newInstance(name, id, extras)
    filter.options = []

    const mt = new FilterOption<number>(filter, 'more than', df)
    mt.firstValueMandatory = true
    mt.secondValueMandatory = true
    mt.showSecondValueOptions = true
    mt.secondValue = mt.defaultSecondVal = 'years'
    mt.firstValue = mt.defaultFirstVal = 1
    mt.secondValueOptions = [mt.secondValue]
    filter.options.push(mt)

    const lt = new FilterOption<number>(filter, 'less than', df)
    lt.firstValueMandatory = true
    lt.secondValueMandatory = true
    lt.showSecondValueOptions = true
    lt.selected = true
    lt.firstValue = lt.defaultFirstVal = 1
    lt.secondValue = lt.defaultSecondVal = 'years'
    lt.secondValueOptions = [lt.secondValue]
    filter.options.push(lt)
    filter.updateTitle()
    return filter
  }
}

class BirthDateQueryBuilder implements IBuilderStrategy {
  public create(
    name: string,
    id: string = null,
    extras: { [key: string]: any } = null
  ): IFilter {
    const df = new DefaultFormatter()
    const filter = Filter.newInstance(name, id, extras)
    filter.options = []

    filter.options.push(new FilterOption<boolean>(filter, 'is empty', df))
    const notEmpty = new FilterOption<boolean>(filter, 'is not empty', df)
    notEmpty.selected = true
    filter.options.push(notEmpty)
    filter.options.push(new FilterOption<boolean>(filter, 'today', df))
    filter.options.push(new FilterOption<boolean>(filter, 'this week', df))
    filter.options.push(new FilterOption<boolean>(filter, 'this month', df))

    const mt = new FilterOption<number>(filter, 'in month', df)
    mt.firstValueMandatory = true
    mt.secondValueMandatory = false
    mt.defaultFirstVal = 1
    mt.showSecondValueOptions = false
    filter.options.push(mt)

    const lt = new FilterOption<number>(filter, 'on date ', df)
    lt.firstValueMandatory = true
    lt.secondValueMandatory = true
    lt.showSecondValueOptions = true
    lt.selected = true
    lt.secondValueOptions = moment.months()
    lt.secondValue = lt.secondValueOptions[0]
    lt.firstValue = 1
    filter.options.push(lt)

    filter.updateTitle()

    const timeZone = store.getters[`locations/getCurrentLocationTimeZone`]
    filter.onQueryTransformation = (payload: any): any => {
      if (!payload) return payload
      if (!payload.extras) payload.extras = {}
      payload.extras.timeZone = timeZone
      return payload
    }

    return filter
  }
}

class AppointmentBuilder implements IBuilderStrategy {
  private showIsEmpty: boolean = false
  private showIsNotEmpty: boolean = false

  constructor(showIsEmpty: boolean = false, showIsNotEmpty: boolean = false) {
    this.showIsEmpty = showIsEmpty
    this.showIsNotEmpty = showIsNotEmpty
  }

  public create(
    name: string,
    id: string = null,
    extras: { [key: string]: any } = null
  ): IFilter {
    const df = new DefaultFormatter()
    const filter = Filter.newInstance(name, id, extras)
    filter.options = []

    if (this.showIsEmpty)
      filter.options.push(new FilterOption<boolean>(filter, 'is empty', df))
    if (this.showIsNotEmpty)
      filter.options.push(new FilterOption<boolean>(filter, 'is not empty', df))

    const timeZone = store.getters[`locations/getCurrentLocationTimeZone`]
    const timeZoneShortForm = moment.tz(timeZone).format('z')

    const todayFilter = new FilterOption<boolean>(filter, 'today', df)
    todayFilter.example = `*per ${timeZoneShortForm} time zone`
    filter.options.push(todayFilter)

    const mt = new FilterOption<number>(filter, 'more than', df)
    mt.example = `*per ${timeZoneShortForm} time zone`
    mt.firstValueMandatory = true
    mt.secondValueMandatory = true
    mt.showSecondValueOptions = true
    mt.secondValue = mt.defaultSecondVal = 'days ago'
    mt.firstValue = mt.defaultFirstVal = 1
    mt.secondValueOptions = [
      'minutes ago',
      'hours ago',
      mt.secondValue,
      'months ago',
      'years ago',
    ]
    filter.options.push(mt)

    const lt = new FilterOption<number>(filter, 'after', df)
    lt.example = `*per ${timeZoneShortForm} time zone`
    lt.firstValueMandatory = true
    lt.secondValueMandatory = true
    lt.showSecondValueOptions = true
    lt.selected = false
    lt.firstValue = lt.defaultFirstVal = 1
    lt.secondValue = lt.defaultSecondVal = 'days'
    lt.secondValueOptions = [
      'minutes after',
      'hours after',
      lt.secondValue,
      'months after',
      'years after',
    ]
    filter.options.push(lt)

    const withinNxt = new FilterOption<number>(filter, 'within next', df)
    withinNxt.example = `*per ${timeZoneShortForm} time zone`
    withinNxt.firstValueMandatory = true
    withinNxt.secondValueMandatory = true
    withinNxt.showSecondValueOptions = true
    withinNxt.selected = true
    withinNxt.firstValue = lt.defaultFirstVal = 1
    withinNxt.secondValue = lt.defaultSecondVal = 'days'
    withinNxt.secondValueOptions = [
      'minutes',
      'hours',
      lt.secondValue,
      'months',
      'years',
    ]
    filter.options.push(withinNxt)

    filter.updateTitle()
    filter.onQueryTransformation = (payload: any): any => {
      if (!payload) return payload
      if (!payload.extras) payload.extras = {}
      payload.extras.timeZone = timeZone
      return payload
    }
    return filter
  }
}

// tslint:disable-next-line: max-classes-per-file
// tslint:disable-next-line: max-classes-per-file
export class FilterBuilder {
  public static readonly GENERIC_FILTER = 'generic'
  public static readonly SINGLECONTACT_FILTER = 'single_contact'
  public static readonly ES_SEARCH_ENDPOINT = `/search/smartlist`
  public static readonly LastActivityTypeFilterName = 'Last Activity Type'
  public static readonly BusinessFilterName = 'Business Name'
  // public static readonly IsBusinessFilterName = 'Is Company';
  // public static readonly IsNotBusinessFilterName = 'Is Not Company';

  public static async resetForUser(user: User | null, locationId?: string) {
    FilterBuilder.types = [...FilterBuilder.nonCSTypes]
    FilterBuilder.keys = null

    if (user && !user.isAssignedTo) {
      const idx = FilterBuilder.types.findIndex(
        a => a.filterName === 'Assigned'
      )
      if (idx === -1)
        FilterBuilder.types.push({
          builder: FilterBuilder.getAssignedBuilder(),
          filterName: 'Assigned',
        })
    } else {
      const idx = FilterBuilder.types.findIndex(
        a => a.filterName === 'Assigned'
      )
      if (idx !== -1) FilterBuilder.types.splice(idx, 1)
    }

    if (!locationId) return

    // const snapshot = await CustomField.getByLocationId(locationId).get()
    // const availableFields = snapshot.docs.map(d => new CustomField(d))
    const availableFields = await (store.getters['locationCustomFields/getLocationId'])(locationId);
    availableFields.forEach(field => {
      if (['TEXTBOX_LIST'].includes(field.dataType)) {
        if (!Array.isArray(field.picklistOptions)) return
        field.picklistOptions.forEach(x => {
          FilterBuilder.types.push({
            filterName: `${Utils.cropDisplay(field.name, 10)} -${x.label}`,
            customFieldId: `${x.id}`,
            customFieldType: 'TEXT',
            builder: FilterBuilder.getCustomFieldBuilder<string>(),
          })
        })
        return
      }
      var item = {
        filterName: `${field.name}`,
        customFieldId: `${field.id}`,
        customFieldType: field.dataType,
        builder: null,
      }

      if (['TEXT', 'LARGE_TEXT', 'PHONE'].includes(field.dataType)) {
        item.builder = FilterBuilder.getCustomFieldBuilder<string>()
      } else if (
        ['NUMERICAL', 'MONETARY', 'MONETORY'].includes(field.dataType)
      ) {
        item.builder = FilterBuilder.getNumericBuilder<string>()
      } else if (['DATE'].includes(field.dataType)) {
        item.builder = new DateRangeBuilder(false, true)
      } else if (['MULTIPLE_OPTIONS', 'CHECKBOX'].includes(field.dataType)) {
        if (field.picklistOptions)
          item.builder = FilterBuilder.getArrayBuilder(
            field.picklistOptions.map(a => {
              return { key: a, value: a } as IKeyVal
            }),
            null
          )
      } else if (
        ['RADIO', 'CHECKBOX', 'SINGLE_OPTIONS'].includes(field.dataType)
      ) {
        if (field.picklistOptions) {
          item.builder = FilterBuilder.getArrayBuilder(
            field.picklistOptions.map(a => {
              return { key: a, value: a } as IKeyVal
            }),
            1
          )
        } else if (field.picklistOptionsImage) {
          item.builder = FilterBuilder.getArrayBuilder(
            field.picklistOptionsImage.map(a => {
              return { key: a.label, value: a.label } as IKeyVal
            }),
            1
          )
        }
      }
      FilterBuilder.types.push(item)
    })
  }

  public static async loadForLocation(locationId?: string) {
    if (!FilterBuilder.types || !FilterBuilder.types.length) {
      await FilterBuilder.resetForUser(null,locationId);
    }
  }

  public static getNames(search: string): string[] {
    if (search) search = search.trim().toLowerCase()
    if (!FilterBuilder.keys || FilterBuilder.keys.length <= 0) {
      FilterBuilder.keys = FilterBuilder.types.map(a => a.filterName).sort()
    }
    if (!search || search === '') return [...FilterBuilder.keys]
    return FilterBuilder.keys.filter(a => a.toLowerCase().includes(search))
  }

  public static getFilterByName(filterName: string) {
    return FilterBuilder.getFilter(filterName)
  }

  public static getFilter(
    filterName: string,
    id: string = null,
    extras?: { [key: string]: any }
  ) {
    //  if (filterName === FilterBuilder.IsBusinessFilterName) {
    //    return FilterBuilder.getYesBuilder().create(FilterBuilder.IsBusinessFilterName);
    //  }
    //  if (filterName === FilterBuilder.IsNotBusinessFilterName) {
    //   return FilterBuilder.getNoBuilder().create(FilterBuilder.IsNotBusinessFilterName);
    // }
    if (!FilterBuilder.types || !FilterBuilder.types.length) {
      throw new Error ('Populate Filters List') // call loadForLocation or resetForUser
    }
    const x = FilterBuilder.types.find(
      a =>
        (extras &&
          extras.customFieldId &&
          a.customFieldId &&
          extras.customFieldId === a.customFieldId) ||
        a.filterName === filterName
    )
    if (x && x.builder) {
      return !x.customFieldId
        ? x.builder.create(x.filterName, id, extras)
        : x.builder.create(x.filterName, id, {
            customFieldId: x.customFieldId,
            customFieldType: x.customFieldType,
            ...extras,
          })
    } else return undefined
  }

  public static getGenericFilter(countryCode: string, firstValue?: any) {
    const x = FilterBuilder.justIsBuilder()
    const filter = x.create(FilterBuilder.GENERIC_FILTER)
    filter.cannotPersist = true
    filter.hidden = true
    if (filter.selectedOption) {
      filter.selectedOption.firstValue = firstValue
      filter.selectedOption.secondValue = countryCode
    }
    return filter
  }

  public static getSingleContactFilter(contactId: string) {
    const x = FilterBuilder.justIsBuilder()
    const filter = x.create(FilterBuilder.SINGLECONTACT_FILTER)
    if (filter.selectedOption) {
      filter.selectedOption.firstValue = contactId
      console.log(filter.selectedOption.firstValue)
    }
    return filter
  }

  private static keys: string[]

  private static types: IFilterBuilder[] = []

  private static nonCSTypes: IFilterBuilder[] = [
    { filterName: 'Tag', builder: FilterBuilder.getTagBuilder() },
    {
      filterName: 'Wildcard Name',
      builder: FilterBuilder.justIsBuilder(
        'Example, builder :  Type *linc* or ab*linc* to search for contact with name Abraham Lincoln'
      ),
    },
    {
      filterName: 'First Name',
      builder: FilterBuilder.getRegularBuilder<string>(),
    },
    {
      filterName: 'Last Name',
      builder: FilterBuilder.getRegularBuilder<string>(),
    },
    {
      filterName: 'Business Name',
      builder: FilterBuilder.getRegularBuilder<string>(),
    },
    {
      filterName: 'Email',
      builder: FilterBuilder.getRegularBuilder<string>(
        'Input examples, builder :  <EMAIL>, @mycompany.com, *mycompany.com, some*company*.'
      ),
    },
    {
      filterName: 'Phone',
      builder: FilterBuilder.getRegularBuilder<number>(
        'To search contacts with international number, prefix your search with +zzz (where zzz means a country dial code) .'
      ),
    },
    {
      filterName: 'Street Name',
      builder: FilterBuilder.justContainsBuilder(
        'Please enter just a street name without suffix or prefix. Example, builder :  If you want to search for St. John Road just enter john to get proper results'
      ),
    },
    { filterName: 'City', builder: FilterBuilder.getRegularBuilder() },
    { filterName: 'State', builder: FilterBuilder.getRegularBuilder() },
    {
      filterName: 'Postal Zip Code',
      builder: FilterBuilder.getRegularBuilder(null, false),
    },
    { filterName: 'Address', builder: FilterBuilder.getEmptyNonEmptyBuilder() },
    { filterName: 'DND', builder: FilterBuilder.getYesNoBuilder() },
    {
      filterName: 'Source',
      builder: FilterBuilder.getRegularBuilder<string>(),
    },
    //  { filterName: 'Origin' , builder :  FilterBuilder.getRegularBuilder<string>()},
    { filterName: 'Campaign', builder: FilterBuilder.getAllCampainBuilder() },
    { filterName: 'Pipeline', builder: FilterBuilder.pipeLineBuilder() },
    {
      filterName: 'Pipeline Stage',
      builder: FilterBuilder.pipelineStageBuilder2(),
    },
    {
      filterName: 'Pipeline Status',
      builder: FilterBuilder.pipelineStatusBuilder(),
    },
    { filterName: 'Created', builder: new DateRangeBuilder() },
    { filterName: 'Updated', builder: new DateRangeBuilder() },
    {
      filterName: 'Last Activity',
      builder: new DateRangeBuilder(true, true),
    },
    { filterName: 'Age', builder: new AgeQueryBuilder() },
    { filterName: 'Birth Date', builder: new BirthDateQueryBuilder() },
    // 'Inactive', builder :  new DateRangeBuilder(true, true)},
    { filterName: 'Import', builder: FilterBuilder.getRecentImport() },
    {
      filterName: 'Last Appointment',
      builder: new DateRangeBuilder(true, true),
    },
    { filterName: 'Type', builder: FilterBuilder.getTypeBuilder() },
    { filterName: 'Offer', builder: FilterBuilder.getOfferBuilder() },
    { filterName: 'Product', builder: FilterBuilder.getProductBuilder() },
    {
      filterName: 'Workflow (Active)',
      builder: FilterBuilder.getWorkflowListBuilder(),
    },
    {
      filterName: 'Workflow (Finished)',
      builder: FilterBuilder.getWorkflowListBuilder(),
    },
    {
      filterName: FilterBuilder.LastActivityTypeFilterName,
      builder: FilterBuilder.getLastActvityTypeBuilder(),
    },
    {
      filterName: 'Attribution (Occurence)',
      builder: FilterBuilder.attributionOccurenceBuilder()
    },
    {
      filterName: 'Attribution Campaign',
      builder: FilterBuilder.justContainsBuilder()
    },
    {
      filterName: 'Attribution Content',
      builder: FilterBuilder.justContainsBuilder()
    },
    {
      filterName: 'Attribution Keyword',
      builder: FilterBuilder.justContainsBuilder()
    },
    {
      filterName: 'Attribution Match Type',
      builder: FilterBuilder.justContainsBuilder()
    },
    {
      filterName: 'Attribution Medium',
      builder: FilterBuilder.justContainsBuilder()
    },
    {
      filterName: 'Attribution Session Source',
      builder: FilterBuilder.justContainsBuilder()
    },
    {
      filterName: 'Attribution Source',
      builder: FilterBuilder.justContainsBuilder()
    },
    {
      filterName: 'Attribution Term',
      builder: FilterBuilder.justContainsBuilder()
    },
    {
      filterName: 'Attribution Ad Group Id',
      builder: FilterBuilder.justIsBuilder()
    },
    {
      filterName: 'Attribution Ad Id',
      builder: FilterBuilder.justIsBuilder()
    },
    {
      filterName: 'Attribution Campaign Id',
      builder: FilterBuilder.justIsBuilder()
    },
    {
      filterName: 'Attribution FB ClickId',
      builder: FilterBuilder.justIsBuilder()
    },
    {
      filterName: 'Attribution Google ClickId',
      builder: FilterBuilder.justIsBuilder()
    }
  ]

  private static getTagBuilder() {
    const fetcher = (params: { [key: string]: any }) => {
      return new Promise<IKeyVal[]>(async (rs, rj) => {
        try {
          if (!params || !params.locationId)
            throw new Error('Send location id to fetch list')
          const result = await Tag.getByLocationIdFirestore(params.locationId)
          result.forEach(async a => {
            if (!a.name && typeof a.name !== 'string') return
            const preTrimLen = a.name.length
            const trimLen = a.name.trim().length
            if (preTrimLen !== trimLen) {
              /* auto fix tags to reduce support calls */
              try {
                await axios.post(
                  '/tag/edit/' +
                    params.locationId +
                    '?old_tag=' +
                    encodeURIComponent(a.name) +
                    '&new_tag=' +
                    encodeURIComponent(a.name.trim())
                )
                a.name = a.name.trim()
                await a.save()
              } catch (err) {
                console.log(err)
              }
              // alert(preTrimLen + ' ' + trimLen)
              // console.log(preTrimLen + ' ' + trimLen)
            }
          })
          const kvs = result.map(tag => ({
            key: tag.id || Utils.safeLC(tag.name).trim() || '',
            value: Utils.safeLC(tag.name).trim(),
          }))
          // console.log('Tag builder', JSON.stringify(kvs))
          rs(kvs)
        } catch (err) {
          rj(err)
        }
      })
    }

    return new RegularBuilder<IKeyVal[]>(new ArrayFormatter(), [
      { label: 'is not empty', selected: false, hasInput: false },
      { label: 'is empty', selected: false, hasInput: false },
      { label: 'is', selected: true, hasInput: true, listFetch: fetcher },
      { displayLabel: 'any of', label: 'either of', selected: false, hasInput: true, listFetch: fetcher },
      { label: 'is not', selected: false, hasInput: true, listFetch: fetcher },
    ])
  }

  private static getOfferBuilder() {
    const fetcher = (params: { [key: string]: any }) => {
      return new Promise<IKeyVal[]>(async (rs, rj) => {
        try {
          if (params && params.locationId)
            await store.dispatch('membership/syncAll', {
              locationId: params.locationId,
              forceRefresh: true,
            }),
              rs(await store.state.membership.offers)
        } catch (err) {
          rj(err)
        }
      })
    }

    return new RegularBuilder<IKeyVal[]>(new ArrayFormatter(), [
      { label: 'is empty', selected: false, hasInput: false },
      { label: 'is not empty', selected: true, hasInput: false },
      { label: 'is', selected: true, hasInput: true, listFetch: fetcher },
      { label: 'is not', selected: false, hasInput: true, listFetch: fetcher },
    ])
  }

  private static getWorkflowListBuilder() {
    const fetcher = (params: { [key: string]: any }) => {
      return new Promise<IKeyVal[]>(async (rs, rj) => {
        try {
          if (params && params.locationId) {
            await store.dispatch('workflows/syncAll', params.locationId)
          }
          const items = store && store.state && store.state.workflows && store.state.workflows.workflows ?
                        store.state.workflows.workflows.map(a=> ({key: a.id, value: a.name})) : [];
          rs(items)
        } catch (err) {
          rj(err)
        }
      })
    }

    return new RegularBuilder<IKeyVal[]>(new ArrayFormatter(), [
      // { label: 'is empty', selected: false, hasInput: false },
      // { label: 'is not empty', selected: true, hasInput: false },
      { label: 'is', selected: true, hasInput: true, listFetch: fetcher },
      { label: 'is not', selected: false, hasInput: true, listFetch: fetcher },
    ])
  }

  private static getProductBuilder() {
    const fetcher = (params: { [key: string]: any }) => {
      return new Promise<IKeyVal[]>(async (rs, rj) => {
        try {
          if (params && params.locationId)
            await store.dispatch('membership/syncAll', {
              locationId: params.locationId,
              forceRefresh: true,
            }),
              rs(await store.state.membership.products)
        } catch (err) {
          rj(err)
        }
      })
    }

    return new RegularBuilder<IKeyVal[]>(new ArrayFormatter(), [
      { label: 'is empty', selected: false, hasInput: false },
      { label: 'is not empty', selected: true, hasInput: false },
      { label: 'is', selected: true, hasInput: true, listFetch: fetcher },
      { label: 'is not', selected: false, hasInput: true, listFetch: fetcher },
    ])
  }

  private static getTypeBuilder() {
    const fetcher = (params: { [key: string]: any }) => {
      return new Promise<IKeyVal[]>(async (rs, rj) => {
        rs([
          { key: 'customer', value: 'Customer' },
          { key: 'lead', value: 'Lead' },
        ])
      })
    }
    return new RegularBuilder<IKeyVal[]>(new ArrayFormatter(), [
      {
        label: 'is',
        selected: true,
        hasInput: true,
        listFetch: fetcher,
        isAutoComplete: false,
        limitItems: 1,
      },
    ])
  }

  private static getLastActvityTypeBuilder() {
    const fetcher = (params: { [key: string]: any }) => {
      return new Promise<IKeyVal[]>(async (rs, rj) => {
        const items = []
        for (let enumMember in MessageType) {
          const num = Number(enumMember)
          if (isNaN(num)) {
            let typeName = `${enumMember}`
            typeName = typeName.replace('TYPE', '').replace(/_/g, ' ').trim()
            typeName = lodashStartCase(lodashLowerCase(typeName))
            items.push({ key: MessageType[enumMember], value: typeName })
          }
        }
        rs(items)
      })
    }
    return new RegularBuilder<IKeyVal[]>(new ArrayFormatter(), [
      {
        label: 'is',
        selected: true,
        hasInput: true,
        listFetch: fetcher,
        isAutoComplete: false,
        limitItems: 1,
      },
      {
        label: 'is not',
        selected: false,
        hasInput: true,
        listFetch: fetcher,
        isAutoComplete: false,
        limitItems: 1,
      },
    ])
  }

  private static getArrayBuilder(list: IKeyVal[], limitItems?: number) {
    const fetcher = (params: { [key: string]: any }) => {
      return new Promise<IKeyVal[]>(async (rs, rj) => {
        rs(list)
      })
    }
    return new RegularBuilder<IKeyVal[]>(new ArrayFormatter(), [
      {
        label: 'is',
        selected: true,
        hasInput: true,
        listFetch: fetcher,
        isAutoComplete: false,
        limitItems: limitItems,
      },
    ])
  }

  private static getEmptyNonEmptyBuilder() {
    return new RegularBuilder<string[]>(new DefaultFormatter(), [
      { label: 'is empty', selected: false, hasInput: false },
      { label: 'is not empty', selected: true, hasInput: false },
    ])
  }

  private static getRecentImport() {
    return new RegularBuilder<string[]>(new DefaultFormatter(), [
      { label: 'batch id', selected: true, hasInput: true },
    ])
  }

  private static getYesNoBuilder() {
    return new RegularBuilder<string[]>(new DefaultFormatter(), [
      { label: 'yes', selected: false, hasInput: false },
      { label: 'no', selected: true, hasInput: false },
    ])
  }

  private static getYesBuilder() {
    return new RegularBuilder<string[]>(new DefaultFormatter(), [
      { label: 'yes', selected: true, hasInput: false },
    ])
  }

  private static getNoBuilder() {
    return new RegularBuilder<string[]>(new DefaultFormatter(), [
      { label: 'no', selected: true, hasInput: false },
    ])
  }
  private static campaignIdFetcher() {
    return (params: { [key: string]: any }) => {
      return new Promise<IKeyVal[]>(async (rs, rj) => {
        try {
          if (!params || !params.locationId)
            throw new Error('Send location id to fetch list')
          const result = await Campaign.getByLocationId(params.locationId)
          //console.log(result);
          const kvs = result.map(item => ({
            key: item.id,
            value: item.name.toLowerCase(),
          }))
          rs(kvs)
        } catch (err) {
          rj(err)
        }
      })
    }
  }

  private static getAllCampainBuilder() {
    const fetcher = FilterBuilder.campaignIdFetcher()
    return new RegularBuilder<IKeyVal[]>(new ArrayFormatter(), [
      { label: 'has active campaign', selected: false, hasInput: false },
      { label: 'has paused campaign', selected: false, hasInput: false },
      {
        label: 'in active campaign',
        selected: false,
        hasInput: true,
        listFetch: fetcher,
        isAutoComplete: true,
      },
      {
        label: 'in finished campaign',
        selected: true,
        hasInput: true,
        listFetch: fetcher,
        isAutoComplete: true,
      },
      {
        label: 'in paused campaign',
        selected: false,
        hasInput: true,
        listFetch: fetcher,
        isAutoComplete: true,
      },
      {
        label: 'in cancelled campaign',
        selected: false,
        hasInput: true,
        listFetch: fetcher,
        isAutoComplete: true,
      },
    ])
  }

  private static getRegularBuilder<T>(
    example: string = null,
    showIsNot: boolean = true
  ) {
    let items = [
      { label: 'is empty', selected: false, hasInput: false },
      { label: 'is not empty', selected: false, hasInput: false },
      { label: 'is', selected: true, hasInput: true, example },
    ]

    if (showIsNot === true)
      items.push({ label: 'is not', selected: false, hasInput: true, example })
    return new RegularBuilder<T>(new DefaultFormatter(), items)
  }

  private static getCustomFieldBuilder<T>(example: string = null) {
    let items = [
      { label: 'is not empty', selected: false, hasInput: false },
      { label: 'is', selected: true, hasInput: true, example },
    ]
    return new RegularBuilder<T>(new DefaultFormatter(), items)
  }

  private static getNumericBuilder<T>(
    example: string = null,
    showIsNot: boolean = true
  ) {
    let items = [
      { label: 'is not empty', selected: false, hasInput: false },
      { label: 'is', selected: true, hasInput: true, example },
      { label: 'greater than', selected: false, hasInput: true, example },
      { label: 'lesser than', selected: false, hasInput: true, example },
    ]
    return new RegularBuilder<T>(new DefaultFormatter(), items)
  }

  private static justContainsBuilder<T>(example: string = null) {
    return new RegularBuilder<T>(new DefaultFormatter(), [
      { label: 'contains', selected: true, hasInput: true, example },
    ])
  }

  public static justIsBuilder<T>(example: string = null) {
    return new RegularBuilder<T>(new DefaultFormatter(), [
      { label: 'is', selected: true, hasInput: true, example },
    ])
  }

  private static attributionOccurenceBuilder() {
    return new RegularBuilder<String>(new DefaultFormatter(), [
      { label: 'first attribution', selected: true, hasInput: false },
      { label: 'last attribution', selected: false, hasInput: false },
    ])
  }

  private static attributionBuilder() {
    return new RegularBuilder<String>(new DefaultFormatter(), [
      { label: 'contains', selected: true, hasInput: true },
    ])
  }

  private static getAssignedBuilder() {
    const fetcher = (params: { [key: string]: any }) => {
      return new Promise<IKeyVal[]>(async (rs, rj) => {
        try {
          if (!params || !params.locationId)
            throw new Error('Send location id to fetch list')
          const result = (await User.getByLocation(params.locationId)) || []
          let items = result.filter(a => !a.deleted)
          items = l_SortBy(items, 'fullName')
          const kvs = items.map(item => ({
            key: item.id,
            value: item.fullName,
          }))
          console.log(kvs)
          rs(kvs)
        } catch (err) {
          rj(err)
        }
      })
    }
    return new RegularBuilder<IKeyVal[]>(new ArrayFormatter(), [
      { label: 'none', selected: false, hasInput: false },
      {
        label: 'to',
        selected: true,
        hasInput: true,
        listFetch: fetcher,
        limitItems: 1,
        isAutoComplete: false,
      },
      {
        label: 'not to',
        selected: false,
        hasInput: true,
        listFetch: fetcher,
        limitItems: 1,
        isAutoComplete: false,
      },
    ])
  }

  private static pipeLineBuilder(hasStageBuilder: boolean = false) {
    const items = []
    if (hasStageBuilder !== true)
      items.push({ label: 'is empty', selected: false, hasInput: false })
    if (hasStageBuilder !== true)
      items.push({ label: 'is not empty', selected: false, hasInput: false })
    items.push({
      label: 'is',
      selected: true,
      hasInput: true,
      listFetch: FilterBuilder.pipeLineListFetcher(),
      limitItems: 1,
      isAutoComplete: false,
      nextFilters: hasStageBuilder
        ? [FilterBuilder.pipelineStageBuilder().create('Stage')]
        : null,
    })

    return new RegularBuilder<IKeyVal[]>(new ArrayFormatter(), items)
  }

  private static pipeLineListFetcher() {
    return (params: { [key: string]: any }) => {
      return new Promise<IKeyVal[]>(async (rs, rj) => {
        try {
          const result = await store.state.pipelines.pipelines
          const items = result.filter(a => !a.deleted)
          const kvs = items.map(item => ({ key: item.id, value: item.name }))
          console.log(kvs)
          rs(kvs)
        } catch (err) {
          rj(err)
        }
      })
    }
  }

  private static pipeLineStageListFetcher() {
    return (params: { [key: string]: any }) => {
      return new Promise<IKeyVal[]>(async (rs, rj) => {
        try {
          if (
            !params ||
            !params.filterBy ||
            params.filterBy.length <= 0 ||
            !params.filterBy[0].key
          ) {
            rs([])
          }
          const pipelines = await store.state.pipelines.pipelines.filter(
            a => a.id === params.filterBy[0].key
          )
          if (
            !pipelines ||
            !pipelines ||
            pipelines.length <= 0 ||
            !pipelines[0].stages ||
            pipelines[0].stages.length <= 0
          ) {
            rs([])
          }
          rs(
            pipelines[0].stages.map(item => ({
              key: item.id,
              value: item.name,
            }))
          )
        } catch (err) {
          rj(err)
        }
      })
    }
  }

  private static pipelineStageBuilder() {
    return new RegularBuilder<IKeyVal[]>(new ArrayFormatter(), [
      {
        label: 'is',
        selected: true,
        hasInput: true,
        listFetch: FilterBuilder.pipeLineStageListFetcher(),
        limitItems: 1,
        isAutoComplete: false,
      },
      //{label: 'is not', selected: true, hasInput: true, listFetch: fetcher, limitItems: 1, isAutoComplete: false, example: 'Stage selection is optional'},
    ])
  }

  private static pipelineStageBuilder2() {
    return new RegularBuilder<IKeyVal[]>(
      new KeyValFormatter('Pipeline', 'Stage'),
      [
        { label: 'is empty', selected: false, hasInput: false },
        //{label: 'is not empty', selected: false, hasInput: false},
        {
          label: 'is',
          selected: true,
          hasInput: true,
          listFetch: FilterBuilder.pipeLineListFetcher(),
          listFetch2: FilterBuilder.pipeLineStageListFetcher(),
          limitItems: 1,
          isAutoComplete: false,
          firstValueMandatory: true,
          secondValueMandatory: true,
        },
        //{label: 'is not', selected: true, hasInput: true, listFetch: fetcher, limitItems: 1, isAutoComplete: false, example: 'Stage selection is optional'},
      ]
    )
  }

  private static pipelineStatusBuilder() {
    const fetcher = (params: { [key: string]: any }) => {
      return new Promise<IKeyVal[]>(async (rs, rj) => {
        try {
          rs(Opportunity.getStatusKeyVals())
        } catch (err) {
          rj(err)
        }
      })
    }
    return new RegularBuilder<IKeyVal[]>(new ArrayFormatter(), [
      // {label: 'is empty', selected: false, hasInput: false},
      // {label: 'is not empty', selected: false, hasInput: false},
      {
        label: 'is',
        selected: true,
        hasInput: true,
        listFetch: fetcher,
        limitItems: 1,
        isAutoComplete: false,
      },
    ])
  }
}
