<template>

    <div class="ps-bubble">

        <div class="bubble-body mx-auto no-text-select stream-type" v-if="type=='stream'">
            <div class="bubble-icon">
                <img src="http://localhost:5000/google/getMapsSquare?location=san%20jose,ca">
            </div>
            <div class="bubble-content">
                <div class="bubble-title text-truncate">
                    <span class="text-truncate ">Mike <span class="stream-location">(London, UK)</span></span>
                </div>
                <div class="bubble-description">
                    <span class="">signed up for ProveSource</span>
                    <!-- ngIf: isProductPurchaseNotification() -->
                </div>
                <div class="bubble-time">
                    <div class="">40 minutes ago

                        <div class="ps-link">
                            <span class="bubble-bolt ">⚡ by </span>
                            <a href="" class="" style="color: rgb(118, 39, 243);">ProveSource</a>
                        </div>
                    </div>
                </div>
                <span id="ps-bubble-close" class="">
                    <span class="close-before"></span>
                    <span class="close-after"></span>
                </span>
            </div>
        </div>

        <div class="bubble-body mx-auto no-text-select live-type" v-if="type=='visitors'">
            <div class="bubble-live-pulse">
                <svg class="ps-pulse-dot" expanded="true">
                    <circle cx="50%" cy="50%" r="7px"></circle>
                    <circle class="pulse" cx="50%" cy="50%" r="10px"></circle>
                </svg>
            </div>
            <div class="bubble-content">
                <div class="bubble-title text-truncate">
                    <span class="">50</span>
                </div>
                <div class="bubble-description">
                    <span class="">music lovers online</span>
                </div>
                <div class="bubble-time">
                    <div>

                        <div class="ps-link">
                            <span class="bubble-bolt ">⚡ by </span>
                            <a href="" class="" style="color: rgb(118, 39, 243);">ProveSource</a>
                        </div>
                    </div>
                </div>
                <span id="ps-bubble-close" class="">
                    <span class="close-before"></span>
                    <span class="close-after"></span>
                </span>
            </div>
        </div>

        <div class="bubble-body mx-auto no-text-select" v-if="type=='page_visits'">
            <div class="bubble-icon">
                <img src="https://media.giphy.com/media/8rEVkruqCkNSR01K1U/giphy.gif">
            </div>
            <div class="bubble-content">
                <div class="bubble-title text-truncate">
                    <span class="">50 foodies</span>
                </div>
                <div class="bubble-description">
                    <span class="">seem to be hungry, too.</span>
                </div>
                <div class="bubble-time">
                    <div class="">in the past 21 minutes

                        <div class="ps-link">
                            <span class="bubble-bolt ">⚡ by </span>
                            <a href="" class="" style="color: rgb(118, 39, 243);">ProveSource</a>
                        </div>
                    </div>
                </div>
                <span id="ps-bubble-close" class="">
                    <span class="close-before"></span>
                    <span class="close-after"></span>
                </span>
            </div>
        </div>

        <div class="bubble-body mx-auto no-text-select" v-if="type=='conversions'">
            <div class="bubble-icon">
                <img src="https://static.wixstatic.com/media/8bf005_41aa51f9c6334de4af54e7c52efd150b~mv2.gif">
            </div>
            <div class="bubble-content">
                <div class="bubble-title text-truncate">
                    <span class="">50 visitors</span>
                </div>
                <div class="bubble-description">
                    <span class="">recently bought this</span>
                </div>
                <div class="bubble-time">
                    <div class="">in the past 16 minutes

                        <div class="ps-link">
                            <span class="bubble-bolt ">⚡ by </span>
                            <a href="" class="" style="color: rgb(118, 39, 243);">ProveSource</a>
                        </div>
                    </div>
                </div>
                <span id="ps-bubble-close" class="">
                    <span class="close-before"></span>
                    <span class="close-after"></span>
                </span>
            </div>
        </div>

        <div class="bubble-body combo-type mx-auto no-text-select" v-if="type=='combo'">
            <div class="bubble-icon">
                <div class="combo-number ">
                    <div class="num-value scale ">50</div>
                    <div class="refer text-truncate ">visitors</div>
                </div>
            </div>
            <div class="bubble-content">
                <div class="bubble-description">
                    <span class="">signed up for ProveSource in the last 24 hours</span>

                </div>
                <div class="bubble-time">
                    <div>

                        <div class="ps-link">
                            <span class="bubble-bolt ">verified by </span>
                            <a href="" class="" style="color: rgb(118, 39, 243);">ProveSource</a>
                        </div>
                    </div>
                </div>
                <span id="ps-bubble-close" class="">
                    <span class="close-before"></span>
                    <span class="close-after"></span>
                </span>
            </div>
        </div>

        <div class="bubble-body mx-auto no-text-select stream-type" v-if="type=='review'">
            <div class="bubble-icon"><img src="http://localhost:5000/img/google.svg"></div>
            <div class="bubble-content">
                <div class="bubble-title text-truncate">
                    <ul class="gfs-popup-052018__stars js-widget-stars" style="padding-top:1px !important">
                        <li class="material-icons md-24"></li>
                        <li class="material-icons md-24"></li>
                        <li class="material-icons md-24"></li>
                        <li class="material-icons md-24"></li>
                        <li class="material-icons md-24"></li>

                    </ul>
                </div>
                <div class="bubble-description"><span><b>5 star rating</b> by Varun Vairavan</span> </div>
                <div class="bubble-time">
                    <div><span>Recently</span>
                        <div class="pfs-link">
                            <span class="bubble-bolt">
                                <i>
                                    <svg width="7" height="13" viewBox="0 0 7 13" xmlns="http://www.w3.org/2000/svg">
                                        <g fill="none" fill-rule="evenodd">
                                            <path d="M4.127.496C4.51-.12 5.37.356 5.16 1.07L3.89 5.14H6.22c.483 0 .757.616.464 1.044l-4.338 6.34c-.407.595-1.244.082-1.01-.618L2.72 7.656H.778c-.47 0-.748-.59-.48-1.02L4.13.495z" fill="#F6A623"></path>
                                            <path fill="#FEF79E" d="M4.606.867L.778 7.007h2.807l-1.7 5.126 4.337-6.34H3.16"></path>
                                        </g>
                                    </svg>
                                </i>
                                by</span>
                            <a href="https://proofmsg.com?utm_source=http://localhost:8080&amp;utm_medium=notification&amp;utm_campaign=powered-by-proofmsg&amp;utm_content=undefined" target="_blank" onclick="event.stopPropagation()"> ProofMessge</a>
                        </div>
                    </div>
                </div>
                <span id="ps-bubble-close" class="no-visible"><span class="close-before"></span><span class="close-after"></span></span>
            </div>
        </div>

    </div>
</template>

<script lang='ts'>
import Vue from "vue";
import { NotificationTemplate } from "@/models";

declare var $: any;

export default Vue.extend({
    props: {
        notification: {}
    },
    data() {
        return {

        };
    },
    computed: {
        type(): string {
            return this.notification.type;
        },
    },
    methods: {

    }
})
</script>

<style>
</style>
