<template>
	<!-- Edit Event Modal -->
	<div
		class="modal fade hl_edit-event-modal"
		id="edit-event-modal"
		tabindex="-1"
		role="dialog"
		ref="modal"
	>
		<div class="modal-dialog --mid" role="document">
			<div class="modal-content">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
				<div class="modal-body">
					<div class="modal-body-inner">
						<div class="hl_edit-event-modal-wrap">
							<div class="hl_edit-event-modal-main">
								<div class="card">
									<div class="card-header --no-right-padding">
										<h2>Edit Wait</h2>
									</div>
									<div class="card-body hl_campaign-configuration edit-wait">
										<div class="scheduling">
											<div class="form-group">
												<label>Wait</label>
												<div class="form-group-controls edit-when">
													<UITextInputGroup
														type="text"
														v-model.number="delayValue"
														style="width: 100px;height: 40px;margin: 0px 5px;display: inline-block;"
													/>
													<select class="selectpicker" v-model="delayType" name="delayType" data-size="5">
														<option value="minutes">{{singular('minutes')}}</option>
														<option value="hours">{{singular('hours')}}</option>
														<option value="days">{{singular('days')}}</option>
													</select>
												</div>
											</div>
											<div class="form-group">
												<label>Which days can we resume</label>
												<div class="form-group-controls">
													<div class="week-select">
														<div class="week-select-day">
															<input
																type="checkbox"
																id="mon-wait-event"
																v-model="days"
																value="1"
																@change="dayError=false"
															>
															<label for="mon-wait-event">Mon</label>
														</div>
														<div class="week-select-day">
															<input
																type="checkbox"
																id="tue-wait-event"
																v-model="days"
																value="2"
																@change="dayError=false"
															>
															<label for="tue-wait-event">Tue</label>
														</div>
														<div class="week-select-day">
															<input
																type="checkbox"
																id="wed-wait-event"
																v-model="days"
																value="3"
																@change="dayError=false"
															>
															<label for="wed-wait-event">Wed</label>
														</div>
														<div class="week-select-day">
															<input
																type="checkbox"
																id="thu-wait-event"
																v-model="days"
																value="4"
																@change="dayError=false"
															>
															<label for="thu-wait-event">Thu</label>
														</div>
														<div class="week-select-day">
															<input
																type="checkbox"
																id="fri-wait-event"
																v-model="days"
																value="5"
																@change="dayError=false"
															>
															<label for="fri-wait-event">Fri</label>
														</div>
														<div class="week-select-day">
															<input
																type="checkbox"
																id="sat-wait-event"
																v-model="days"
																value="6"
																@change="dayError=false"
															>
															<label for="sat-wait-event">Sat</label>
														</div>
														<div class="week-select-day">
															<input
																type="checkbox"
																id="sun-wait-event"
																v-model="days"
																value="0"
																@change="dayError=false"
															>
															<label for="sun-wait-event">Sun</label>
														</div>
													</div>
												</div>
												<div v-show="dayError" class="--red">You need to pick atleast 1 day</div>
											</div>
											<div class="form-group">
												<label>What time can we resume</label>
												<div class="form-group-controls wait-start-time">
													<select class="selectpicker" v-model="startHour" name="startHour" data-size="5">
														<option v-for="hour in hours" :key="'hour-'+hour" :value="pad(hour,2)">{{pad(hour,2)}}</option>
													</select>
													<select class="selectpicker ml-1" v-model="startMinute" name="startMinute" data-size="5">
														<option
															v-for="minute in minutes"
															:key="'minute-'+minute"
															:value="pad(minute,2)"
														>{{pad(minute,2)}}</option>
													</select>
													<select class="selectpicker ampm" v-model="amORpm" name="startAMPM" data-size="5">
														<option value="AM">AM</option>
														<option value="PM">PM</option>
													</select>
												</div>
											</div>
										</div>
									</div>
									<div class="card-footer">
										<div class="modal-footer-left"></div>
										<div class="modal-footer-right">
											<div style="display: inline-block;position: relative;">
												<UIButton
													type="button"
													@click.prevent="save"
													:loading="saving"
												>Save</UIButton>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
import * as moment from 'moment-timezone';
import { CampaignTemplate, VoicemailTemplate, ActionCondition, Window } from '@/models';

declare var $: any;

type EditCallEventModalValues = {
	visible: boolean;
	template: CampaignTemplate<VoicemailTemplate>;
	currentLocationId: string;
	campaignId: string;
}
export default Vue.extend({
	props: {		values: {
			type: Object as () => EditCallEventModalValues
		}	},
	data() {
		return {
			when: 'after' as any,
			delayValue: 0,
			delayType: 'minutes' as any,
			amORpm: 'AM',
			startHour: '09',
			startMinute: '00',
			hours: Array(12).fill(0).map((item: number, index: number) => index + 1),
			minutes: Array(61).fill(0).map((item: number, index: number) => index),
			template: {} as CampaignTemplate<VoicemailTemplate>,
			currentLocationId: "",
			campaignId: "",
			saving: false,
			days: [1, 2, 3, 4, 5] as any[],
			dayError: false,
		}
	},
	methods: {
		pad(n: any, width: number, z?: string) {
			z = z || '0';
			n = n + '';
			return n.length >= width ? n : new Array(width - n.length + 1).join(z) + n;
		},
		singular(type: string) {
			return this.delayValue === 1 ? type.substring(0, type.length - 1) : type;
		},
		async save() {
			this.dayError = false;
			if (this.days.length === 0) {
				this.dayError = true;
				return false;
			}
			this.saving = true;
			this.template.start_after = {
				when: this.when,
				value: this.delayValue,
				type: this.delayType
			};
			this.template.window = {
				condition: 'wait',
				days: lodash.sortBy(this.days.map(day => typeof day === 'string' ? parseInt(day) : day)),
				start: moment(`${this.startHour}:${this.startMinute} ${this.amORpm}`, ["h:mm A"]).format('HH:mm')
			};
			this.$emit('save');
			this.$emit('hidden');
		}
	},
	computed: {
	},
	beforeDestroy() {
		$(this.$refs.modal).off('hidden.bs.modal');
	},
	watch: {
		values(values: EditCallEventModalValues) {
			const data: (() => object) = <(() => object)>this.$options.data;
			if (data) Object.assign(this.$data, data.apply(this));
			if (values.visible) $(this.$refs.modal).modal('show');
			else $(this.$refs.modal).modal('hide');
			this.template = values.template;
			this.currentLocationId = values.currentLocationId;
			this.campaignId = values.campaignId;
			if (values.template) {
				const template = values.template;
				if (!lodash.isEmpty(template.start_after)) {
					this.delayValue = template.start_after.value;
					this.delayType = template.start_after.type;
				}
				if (!lodash.isEmpty(template.window)) {
					this.days = template.window.days;
					const startTime = moment(template.window.start, ["HH:mm"]);
					this.startHour = startTime.format('hh');
					this.startMinute = startTime.format('mm');
					this.amORpm = startTime.format('A');
				}
			}
		}
	},
	updated() {
		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker('refresh');
		}

		if (this.values && this.values.visible) {
			$(this.$refs.modal).modal('show');
		}
	},
	mounted() {
		const _self = this;
		$(this.$refs.modal).on('hidden.bs.modal', function () {
			_self.$emit('hidden');
		});

		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker();
		}

		if (this.values && this.values.visible) {
			$(this.$refs.modal).modal('show');
		}
	},
})
</script>

