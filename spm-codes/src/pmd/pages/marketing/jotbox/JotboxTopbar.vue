<template>
  <div class="top-header px-3 py-2">
    <div class="row m-0">
      <div class="submission col px-0">
        <span class="grey-bg mr-2 px-2" @click="$emit('nextContact')">
          <i
            class="fa fa-chevron-down"
            aria-hidden="true"
          ></i>
        </span>
        <span class="grey-bg mr-2 px-2" @click="$emit('previousContact')">
          <i
            class="fa fa-chevron-up"
            aria-hidden="true"
          ></i>
        </span>

      <label class="pl-2 pt-3 font-weight-bold m-0"> {{totalRecods !==0 ? contactNumber+1 : 0}} of {{totalRecods}} Submissions</label>
      </div>
      <!-- <v-select
        class="custom-element"
        v-model="selected"
        :options="formbuilder"
        @change="$emit('getFormDetails', selected)"
        ></v-select> -->
      <!-- <div class="right-btn">
        <button class="btn btn-primary hidden-print mr-2">
          <i class="fa fa-print" aria-hidden="true"></i> Print
        </button>
        <button class="btn btn-primary hidden-print mr-2">
          <i class="fa fa-download" aria-hidden="true"></i> Download
        </button>
         <div class="dropdown">
          <button class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
            <i class="fa fa-ellipsis-v" aria-hidden="true"></i> more
          </button>
          <div class="dropdown-menu">
            <a class="dropdown-item" href="#">
              Link 1
            </a>
            <a class="dropdown-item" href="#">
              Link 2
            </a>
            <a class="dropdown-item" href="#">
              Link 3
            </a>
          </div>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
export default Vue.extend({
  props:['contactNumber', 'totalRecods']
})
</script>

<style>
</style>
