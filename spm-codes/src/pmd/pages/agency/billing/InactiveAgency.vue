<template>
  <div class="lockout-wrap">
    <div class="inactive__page-header">
      <div>
        <img
          v-if="company && company.logoURL"
          :src="company.logoURL"
          height="44"
        />
        <svg
          v-else-if="company"
          xmlns="http://www.w3.org/2000/svg"
          width="28"
          height="26"
        >
          <image
            width="28"
            height="26"
            xlink:href="data:img/png;base64,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"
          />
        </svg>
      </div>
      <div class="hl_header--dropdown dropdown --no-caret" v-if="user">
        <a
          href="javascript:void(0);"
          class="hl_header--avatar dropdown-toggle"
          role="button"
          data-toggle="dropdown"
          aria-haspopup="true"
          aria-expanded="false"
          v-b-tooltip.hover
          :title="user && user.name"
        >
          <div class="avatar">
            <div v-if="user && user.profilePhoto" class="avatar_img">
              <img :src="user.profilePhoto" />
            </div>
            <div
              v-else-if="user && user.initials"
              class="avatar_img"
              v-bind:style="{ backgroundColor: user.profileColor }"
            >
              {{ user.initials }}
            </div>
            <div v-else class="avatar_img --gray">?</div>
          </div>
        </a>
        <div class="dropdown-menu dropdown-menu-right">
          <a
            class="dropdown-item"
            href="javascript:void(0);"
            @click.stop="signout"
            >Signout</a
          >
        </div>
      </div>
    </div>
    <div class="lockout__info" v-if="!updateCard">
      <div class="lockout__title">
        You have been
        <span class="lockout__title--warning">
          <span v-if="$route.query.type === 'failed-payment'">temporarily</span>
          locked out</span
        >
      </div>
      <ol class="lockout__steps" v-if="$route.query.type === 'failed-payment'">
        <li>Only Agency admins / users can see this message</li>
        <li>
          Your clients (location admins / users) can still use the platform till
          <b v-if="company && company._data && company._data.date_inactivation">
            {{
              company._data.date_inactivation.seconds > forceLockedTimestamp
                ? inactivationDate(company._data.date_inactivation.seconds)
                : inactivationDate(forceLockedTimestamp)
            }}
          </b>
        </li>
        <li>
          All your data is safe and all triggers / campaigns. etc will still
          work
        </li>
      </ol>
      <ol class="lockout__steps" v-else>
        <!-- <li>Only Agency admins / users can see this message</li> -->
        <li>
          Your clients (location admins / users) are no longer able to use the
          platform
        </li>
        <li>
          All your data is safe and all triggers / campaigns. etc will resume
          working after payment
        </li>
      </ol>
      <div class="lockout__retry-section">
        <div
          class="lockout__title"
          v-show="
            pendingInvoices > 0 ||
            (company.status.includes('failed-payment') && pendingInvoices !== 0)
          "
        >
          To continue using the platform please retry pending invoices
        </div>
        <div
          class=""
          v-if="reducedPendingInvoices === 0 && pendingInvoices > 0"
          style="margin-bottom: 30px"
        >
          You have no more pending invoices.
        </div>
        <billing-history
          v-show="reducedPendingInvoices !== 0"
          :showHeading="false"
          @fetchedList="setInvoicesList"
          @paid="invoicePaid"
          source="inactive-screen"
        />
      </div>
      <div
        class="lockout__reactivate-btn"
        v-show="reducedPendingInvoices === 0"
      >
        <div
          class="btn btn-primary"
          @click="reactivate"
          v-if="company.status.includes('inactive')"
        >
          Reactivate Account
        </div>
        <div class="btn btn-success" @click="goToDashboard" v-else>
          Go To Dashboard
        </div>
      </div>
      <!-- <div class="lockout__change-card">
        <div class="lockout__title" @click="updateCard = !updateCard">
          If you need to change payment method
        </div>
        <div class="btn btn-primary lockout__change-card-btn" @click="updateCard = true">Add a new card</div>
      </div> -->
      <div class="lockout__mistake">
        <div>
          If you need to change payment method
          <span class="link-text" @click="updateCard = !updateCard">
            click here
          </span>
        </div>
        <div class="border-bottom--dotted">
          To modify your current subscription
          <a
            target="_blank"
            :href="`https://www.gohighlevel.com/modify-subscription?user_email=${user.email}&company_email=${company.email}&company_id=${company.id}`"
            title="Subscription modification process might take upto 10 working days."
            class="link-text"
          >
            click here
          </a>
        </div>
        <div
          v-if="
            +new Date() > forceLockedTimestamp * 1000 ||
            company.status === 'active'
          "
        >
          If you think this is a mistake <contact-support />
        </div>
        <div v-else>
          To avoid your account sunpension, please <contact-support /> by
          {{ inactivationDate(forceLockedTimestamp) }}
          <div class="skip-lockout-btn link-text" @click="skipForToday">
            Skip for today <i class="fas fa-angle-double-right"></i>
          </div>
        </div>
      </div>
    </div>
    <div class="lockout__add-card" v-if="updateCard">
      <div class="lockout__back-btn link-text" @click="updateCard = false">
        <i class="fas fa-angle-left"></i> Back
      </div>
      <div class="lockout__title" style="text-align: left">Add New Card</div>
      <billing-method
        :showHeading="false"
        @cardChangedTimeout="updateCard = false"
      />
    </div>
    <upgrade-modalv2
      :company="company"
      v-if="showUpgradeModal"
      :show="showUpgradeModal"
      type="reactivate"
      source="billing-page"
      @close="showUpgradeModal = false"
      @success="onUpgradation"
    />
  </div>
</template>

<script>
import BillingHistory from './BillingHistory.vue'
import BillingMethod from './BillingMethod.vue'
import ContactSupport from '@/pmd/components/common/ContactSupport.vue'
import * as moment from 'moment-timezone'
import UpgradeModalv2 from '@/pmd/components/agency/billing/UpgradeModalv2.vue'

import { Company, User } from '@/models'
import { mapState } from 'vuex'
// import {CompanyState} from '@/store/state_models';
import { trackGaEvent } from '@/util/helper'

export default {
  components: {
    BillingHistory,
    BillingMethod,
    ContactSupport,
    UpgradeModalv2,
  },
  data() {
    return {
      updateCard: false,
      showUpgradeModal: false,
      pendingInvoices: -1,
      reducedPendingInvoices: -1,
      forceLockedTimestamp: 1607990400, // 12/15/2020 @ 12:00am (UTC)
    }
  },
  computed: {
    ...mapState('company', {
      company: s => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
  },
  mounted() {
    trackGaEvent(
      'Inactive Agency',
      this.company ? this.company.id : '',
      `Page Loaded | ${this.company ? this.company.status : ''}`,
      1
    )
  },
  methods: {
    inactivationDate(timestampSeconds) {
      return moment(timestampSeconds * 1000).format('MMM DD, YYYY')
    },
    setInvoicesList(invoices) {
      // console.log(invoices);
      let pendingInvoicesCount = 0
      invoices &&
        invoices.length &&
        invoices.forEach(invoice => {
          if (!invoice.paid) {
            pendingInvoicesCount++
          }
        })
      this.pendingInvoices = pendingInvoicesCount
      this.reducedPendingInvoices = pendingInvoicesCount
    },
    invoicePaid(invoiceId, invoiceDetail, invoiceValue) {
      trackGaEvent(
        'Inactive Agency',
        this.company ? this.company.id : '',
        `Invoice Paid ${invoiceDetail}`,
        invoiceValue
      )
      this.reducedPendingInvoices--
      setTimeout(() => {
        if (
          this.reducedPendingInvoices === 0 &&
          this.company.status === 'active'
        ) {
          this.$router.push({
            name: 'account_billing',
            replace: true,
          })
        }
      }, 5000)
    },
    goToDashboard() {
      trackGaEvent(
        'Inactive Agency',
        this.company ? this.company.id : '',
        `Go To Dashboard`,
        1
      )
      this.$router.push({
        name: 'dashboard',
        replace: true,
      })
    },
    async signout() {
      trackGaEvent(
        'Inactive Agency',
        this.company ? this.company.id : '',
        `Signout`,
        1
      )
      try {
        await this.$store.dispatch('logout')
        await this.$auth.signOut()
        const response = await this.$http.post('/signout')
      } catch (err) {
        //
      }
      this.$router.push({ name: 'login' })
    },
    reactivate() {
      trackGaEvent(
        'Inactive Agency',
        this.company ? this.company.id : '',
        `Reactivating`,
        1
      )
      this.showUpgradeModal = true
    },
    skipForToday() {
      trackGaEvent(
        'Inactive Agency',
        this.company ? this.company.id : '',
        `SKIP for today`,
        1
      )
      localStorage.setItem('lockoutSkipped', +new Date())
      this.$router.push({
        name: 'dashboard',
        replace: true,
      })
    },
    onUpgradation(newPlan) {
      trackGaEvent(
        'Inactive Agency',
        this.company ? this.company.id : '',
        `Upgraded to ${newPlan.name || ''}`,
        1
      )
      // redirect to the billing page, after 5 seconds.
      setTimeout(() => {
        this.$router.push({
          name: 'account_billing',
          replace: true,
        })
      }, 5000)
    },
  },
}
</script>

<style>
.inactive__page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  padding: 20px;
}
.lockout__info,
.lockout__add-card {
  max-width: 660px;
  margin: 100px auto;
  box-shadow: 0px 0px 12px 4px rgba(0, 0, 0, 0.15);
  padding: 30px;
  border-radius: 8px;
}
.lockout-wrap {
  margin-top: -82px;
  padding: 80px 30px;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.8);
  width: 100%;
  height: 100%;
  min-height: 100vh;
}
.lockout__title {
  font-size: 20px;
  font-weight: 600;
  color: #373737;
  margin-bottom: 20px;
}
.lockout__title--warning {
  color: #e93d3d;
}
.lockout__steps {
  text-align: left;
}
.lockout__retry-section {
  margin-top: 80px;
}
.lockout__mistake {
  margin-top: 50px;
}
.lockout__change-card {
  margin-top: 80px;
}
.lockout__change-card-btn {
}
.lockout__reactivate-btn {
}
.lockout__back-btn {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;
}
.lockout__back-btn i {
  margin-right: 4px;
}
.reactivation-row {
}
.skip-lockout-btn {
  margin-top: 60px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.skip-lockout-btn i {
  margin-left: 4px;
}

.link-text {
  color: #178af6;
  font-weight: 600;
  cursor: pointer;
}
.border-bottom--dotted {
  border-bottom: 1px dotted #ccc;
  width: 75%;
  margin: auto;
  margin-bottom: 12px;
  padding-bottom: 12px;
}
</style>
