<template>
    <tr :class="highlight ? 'highlight': ''">
        <td>
            <Avatar v-if="user" :contact='user' :size="'sm'"  v-b-tooltip.hover
             :title="getToolTip(request)"/>
        </td>
        <td>
            <span v-if="request.status=='pending'" class="table_status --sent">Pending</span>
            <span v-if="request.status=='success'" class="table_status --opened">Success</span>
            <span v-if="request.status=='revert'" class="table_status --clicked">Revert</span>
        </td>
        <td>{{ request.dateAdded.fromNow()}}</td>
        <td>{{ request.type.toUpperCase() }}</td>
        <td>{{ request.created }}</td>
        <td>{{ request.updated }}</td>
        <td>{{ request.skipped }}
            <span
                v-if="request.skipped > 0 && request.type === 'bulk_validation'"
                style="margin-left: 5px; color: #aaa"
                class="input-group-addon"
                v-b-tooltip.hover
                title="Contacts without an e-mail address"
            >
                <i class="fas fa-question-circle"></i>
            </span>
        </td>
        <td>{{ request.failed }}</td>
        <td>{{ request.deleted }}</td>
        <td class="actions">
            <!-- <div v-if="request.status === 'success'"> -->
                <i class="fa fa-undo --red" v-if="!processing && request.type === 'bulk_import'" @click.prevent="undo"></i>
                <moon-loader :loading="processing" color="#e93d3d" size="15px" />
            <!-- </div> -->
        </td>
    </tr>
</template>

<script lang="ts">
import Vue from 'vue';
import { ImportRequest, User } from '../../../models';
import Avatar from '../Avatar.vue';

export default Vue.extend({
    props: { 
        request: ImportRequest,
        highlight: Boolean
    },
    components: { Avatar },
    data() {
        return {
            user: undefined as User | undefined,
            processing: false
        }
    },
    async created() {
        if (this.request.userId) {
          this.user = await this.$store.dispatch('agencyUsers/syncGet', this.request.userId)
        }
    },
    methods: {
        async undo() {
            let msg = '';
            if(this.request.created > 0 ) {
                msg += 'This will delete ' + this.request.created + ' contacts. ';
            }
            msg+= 'Are you sure you want to undo import?';
            if(confirm(msg)) {
                this.processing = true;
                await this.request.undoImport(this.request.locationId);
                this.processing = false;
            }
        },
        getToolTip(request: any){
          return request ? `Id: ${request.id}  Date:${request.dateAdded}` : 'no request id';
        }
    }
});
</script>

<style scoped>
.actions {
    text-align: center;
}
.actions svg {
    cursor: pointer;
}
.highlight {
    background-color: #dde7ec;
}
</style>

