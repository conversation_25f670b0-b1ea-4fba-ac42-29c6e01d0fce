<template>
  <div class="form-group">
    <label>{{ automationString }}</label>
    <vSelect
      :options="availableCampaigns"
      label="name"
      v-model="selectedCampaign"
      v-validate="'required'"
      :clearable="false"
      name="campaign"
    >
    </vSelect>
    <span v-show="errors.has('campaign')" class="error"
      >{{ errors.first('campaign') }}
    </span>
    <!-- <span v-show="errors.has('campaign')" class="error">Campaign is required.</span> -->
  </div>
</template>

<script lang="ts">
import * as lodash from 'lodash'
import Vue from 'vue'
import * as moment from 'moment-timezone'
import vSelect from 'vue-select'
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'
import {
  Campaign,
  BulkRequest,
  Location,
  Contact,
  User,
  getCountryInfo,
  Workflow,
} from '@/models'
import { IBulkCampaignSpecs } from './server/bulkreq_interfaces'
import { campaigns } from '../../../../../spm-appengine/src/handlers/zapier_functions'

declare var $: any

export default Vue.extend({
  name: 'CampaignSelection',
  inject: ['parentValidator'],
  props: {
    action: Object as () => IBulkCampaignSpecs,
    locationId: String,
  },
  components: {
    vSelect,
  },
  data() {
    return {
      selectedCampaign: null as { [key: string]: any } | null,
      availableCampaigns: [] as { [key: string]: any }[],
      activeCampaigns: [] as { [key: string]: any }[],
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        console.log(s.user)
        return s.user ? new User(s.user) : undefined
      },
    }),
    automationString() {
      return this.user.automationString(false) || 'Campaign / Workflow'
    }
  },
  methods: {
    async fetchData(): Promise<void> {
      let temp: any[] = []
      if (this.user?.automationPermission.campaigns) {
        await this.$store.dispatch('campaigns/syncAll', this.locationId)
        let campaigns = this.$store.state.campaigns.campaigns
        if (campaigns && campaigns.length > 0) {
          campaigns = lodash.orderBy(
            campaigns.filter((w: any) => w.status === 'published'),
            [item => item.name.toLowerCase()]
          )
          campaigns = campaigns.map((w: any) => ({
            name: `Campaign - ${w.name}`,
            id: w.id,
            type: 'campaign',
          }))
          temp = [...campaigns]
        }
      }

      if (this.user?.automationPermission.workflows) {
        await this.$store.dispatch('workflows/clearAll') // Clear workflows to get updated
        await this.$store.dispatch('workflows/syncAll', this.locationId) // Fetch workflows to get updated

        let workflows = this.$store.state.workflows.workflows
        if (workflows && workflows.length > 0) {
          const activeWorkflows = lodash.orderBy(
            workflows.filter((w: any) => w.status === 'published'),
            [item => item.name.toLowerCase()]
          )
          temp = [
            ...temp,
            ...activeWorkflows.map((w: any) => ({
              name: `Workflow - ${w.name}`,
              id: w.id,
              type: 'workflow',
            })),
          ]
        }
      }

      this.availableCampaigns = temp
    },
    async addToCampaign() {
      throw new Error('addToCampaign not implemented')
    },
  },
  watch: {
    '$route.params.location_id': async function (id) {
      await this.fetchData()
    },
    selectedCampaign(selectedCampaign: { [key: string]: any } | null) {
      if (this.action && selectedCampaign) {
        if (selectedCampaign.type === 'workflow') {
          this.action.opType = 'bulk-workflow'
          this.action.workflow_id = selectedCampaign.id
          this.action.description = selectedCampaign.name
        } else {
          this.action.opType = 'bulk-campaign'
          this.action.campaign_id = selectedCampaign.id
          this.action.description = selectedCampaign.name
        }
      } else if (this.action) {
        this.action.campaign_id = ''
        this.action.workflow_id = ''
        this.action.description = ''
      }
    },
  },
  created() {
    this.$validator = this.parentValidator
  },
  async mounted() {
    await this.fetchData()
    // if (this.action && this.action.campaign_id) {
    //   this.selectedCampaign = this.activeCampaigns.find(
    //     a => a.id === this.action.campaign_id && a.type === 'campaign'
    //   )
    // }
    // if (this.action && this.action.workflow_id) {
    //   this.selectedCampaign = this.activeCampaigns.find(
    //     a => a.id === this.action.workflow_id && a.type === 'workflow'
    //   )
    // }
  },
})
</script>
