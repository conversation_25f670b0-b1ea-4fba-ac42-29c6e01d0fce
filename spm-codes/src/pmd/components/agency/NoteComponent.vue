UITextAre
<template>
  <div class="tab-content">
    <div
      class="tab-pane fade show active"
      id="note"
      role="tabpanel"
      aria-labelledby="note-tab"
    >
      <div class="form-group">
        <!-- <label>Note</label> -->
        <UITextAreaGroup
          type="text"
          rows="4"
          :placeholder="placeholderValue"
          name="note"
          :value="note"
          v-validate="'required'"
          @input="val => (note = val)"
          :class="{ 'is-danger': errors.has('note') }"
          :error="errors.has('note')"
          :errorMsg="'Note cannot be empty'"
          @keyup="edited = true"
        />
      </div>
      <div class="form-footer save space-x-2" v-if="enableButtons || edited">
        <UIButton use="outline" @click.prevent="cancel"> Cancel </UIButton>

        <UIButton :loading="loading" @click.prevent="save">
          <span v-if="!loading">Save</span>
        </UIButton>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { User } from '@/models'
import Note from '@/models/api/Note'
import LocationNote from '@/models/api/LocationNote'
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'
import moment from 'moment'
import lodash from 'lodash'
import { UxMessage } from '@/util/ux_message'

interface IData {
  loading: boolean
  edited: boolean
  currentNote?: Note | LocationNote
  note: string
  currentLocationId: string
  currentAccountId: string
  contactId: string
  placeholderValue: string
}

export default Vue.extend({
  props: ['noteIdPassIn', 'passedInContactId', 'enableButtons'],
  inject: ['uxmessage'],
  data(): IData {
    return {
      loading: false,
      edited: false,
      note: '',
      currentLocationId: '',
      currentAccountId: '',
      contactId: '',
      placeholderValue: 'Enter note',
    }
  },
  watch: {
    '$route.params.account_id': function (id) {
      this.currentLocationId = id
    },
    '$route.query.note_id': function (id) {
      if (id) {
        this.setCurrentNote(id)
      }
    },
    '$route.params.contact_id': function (id) {
      this.contactId = id
    },
    noteIdPassIn: function (id) {
      if (id) {
        this.setCurrentNote(id)
      }
    },
    passedInContactId: function (id) {
      this.contactId = id
    },
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
  },
  created() {
    this.currentAccountId = this.$router.currentRoute.params.account_id
    this.currentLocationId = this.$router.currentRoute.params.location_id
    if (this.$router.currentRoute.params.contact_id) {
      this.contactId = this.$router.currentRoute.params.contact_id
    } else if (this.passedInContactId) {
      this.contactId = this.passedInContactId
    }
    if (this.$router.currentRoute.query.note_id) {
      this.setCurrentNote(this.$router.currentRoute.query.note_id)
    }
  },
  methods: {
    async setCurrentNote(id: string) {
      try {
        let locationId = this.currentAccountId||this.currentLocationId
        if(this.contactId){
          this.currentNote = await Note.read(this.contactId, id)
        }else{
          this.currentNote = await LocationNote.read(locationId, id)
        }
        this.note = this.currentNote.body
        this.edited = true
      }catch (error) {
        this.uxmessage(UxMessage.errorType(error), true)
      }
      this.$refs.note.focus()
    },
    reset() {
      this.errors.clear()
      this.loading = false
      this.edited = false
      this.note = ''
    },
    cancel() {
      this.$emit('cancelButton')
      this.reset()
      const query = Object.assign({}, this.$route.query)
      delete query['note_id']
      if (!lodash.isEqual(query, this.$route.query)) {
        //is_equal avoids navigation error printed in console
        this.$router.replace({ query: query, params: this.$route.params }) // make sure to send params to retain record navigation
      }
    },
    async save() {
      try {
        if (this.loading) return

        await this.$validator.validateAll()
        if (this.errors.any()) {
          return Promise.resolve(true)
        }

        this.loading = true
        if (!this.currentNote) {
          if(this.currentAccountId){
            this.currentNote = new LocationNote({
              userId: this.user.id,
              accountId: this.currentAccountId,
              body: this.note,
            })
          }else{
            this.currentNote = new Note({
              userId: this.user.id,
              contactId: this.contactId,
              body: this.note,
            })
          }
        } else {
          this.currentNote.body = this.note
          this.currentNote.dateUpdated = new Date()
        }

        await this.currentNote.save()

        this.currentNote = undefined

        this.$validator.reset()
        this.note = ''
        this.placeholderValue = 'Enter Note'
        this.loading = false
        this.cancel()
        this.$bus.$emit('refresh-notes')
      } catch (error) {
        const message = error?.response?.data?.message
          ? error.response.data.message.join(', ')
          : error.message
        this.uxmessage(UxMessage.errorType(message), true)
        this.loading = false
        this.currentNote = undefined
      }
    },
  },
})
</script>

<style scoped>
.spinner {
  margin: 10px 0px;
}
</style>
