/* Customize website's scrollbar like Mac OS
Not supports in Firefox and IE */
.light-scrollbar {
  /* total width */
  &::-webkit-scrollbar {
    background-color: transparent;
    width: 12px; /* 3px + 6px + 3px*/
  }
  /* background of the scrollbar except button or resizer */
  &::-webkit-scrollbar-track {
    background-color: transparent;
    box-shadow: inset 0 0 14px 14px transparent;
    border: solid 3px transparent;
  }
  &::-webkit-scrollbar-track:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  /* scrollbar itself */
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0);
    border-radius: 16px;
    box-shadow: inset 0 0 14px 14px rgba(0, 0, 0, 0);
    border: solid 3px transparent;
  }
  &:hover::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 14px 14px rgba(0, 0, 0, 0.15);
  }
  &::-webkit-scrollbar-thumb:hover {
    box-shadow: inset 0 0 14px 14px rgba(0, 0, 0, 0.2);
  }
  /* set button(top and bottom of the scrollbar) */
  &::-webkit-scrollbar-button {
    display: none;
  }
}
.light-scrollbar--none {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  &::-webkit-scrollbar {
    display: none;
  }
}
