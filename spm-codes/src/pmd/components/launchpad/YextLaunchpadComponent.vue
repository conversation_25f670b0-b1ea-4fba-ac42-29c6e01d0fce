<template>
  <div
    id="chat-connect"
    class="launchpad-item-sub-container bg-white my-1 launchpad-bottom-border"
    v-if="showYextOption"
  >
    <div class="items-tiles mx-3 my-2">
      <img src="/pmd/img/icon-yext-logo.svg" class="integration-icon mx-4" />
      <div class="item-info">
        <div class="font-normal text-sm text-gray-500">
          Yext enables you to list your business on 150+ portals and websites to
          boost online presence and SEO instantly.
          <div>
            <b v-if="yextId"> Congratulations! You have Yext Listing </b>
          </div>
        </div>
      </div>
      <div class="flex flex-col justify-center item-end content-end w-40">
        <div
          class="
            flex flex-row
            justify-end
            item-end
            content-end
            w-full
            pr-4
            pl-4
          "
        >
          <button
            id="launchpad-yext"
            class="green-btn green-filled-button green-btn"
            @click="gotoYextPitch"
          >
            <span v-if="!yextId">Connect</span>
            <i v-else class="fa fa-check"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: {
    location: {
      type: Object,
    },
    company: {
      type: Object,
    },
    user: {
      type: Object,
    },
  },
  computed: {
    showYextOption() {
      try {
        if (
          this.user.permissions.online_listings_enabled !== false &&
          this.company &&
          this.location &&
          this.company.country == 'US' &&
          this.location.country == 'US' &&
          this.company.stripeConnectId &&
          this.location.yextReseller &&
          this.location.yextReseller.location_enabled
        ) {
          return true
        }
      } catch (err) {
        console.error(err)
      }
      return false
    },

    yextId() {
      if (this.location) {
        return this.location.yextId || this.location.yextReseller.location_id
      }
    },
  },
  methods: {
    gotoYextPitch() {
      this.$router.push({ name: 'reputation_listing' })
    },
  },
})
</script>
