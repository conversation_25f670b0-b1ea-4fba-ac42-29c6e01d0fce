<template>
	<section class="hl_wrapper workflow_list">
		<section class="hl_marketing--inner hl_wrapper--inner hl_marketing" id="marketing">
			<div class="hl_marketing--header">
				<div class="container-fluid">
					<div class="hl_marketing--header-inner">
						<h2>Workflows</h2>
						<a
							v-if="allowEdit"
							href="workflow-new"
							class="btn btn-success"
							@click.prevent="createNew">Create Workflow</a>
					</div>
				</div>
			</div>

			<div class="container-fluid">
				<div class="hl_customer-acquisition">
					<!-- Update this with default styles as per opportunities page -->
					<div class="hl_controls">
						<div class="hl_controls--right">
							<select
								class="selectpicker filter-status"
								title="Status"
								data-width="fit"
								v-model="filters.status"
								name="filterStatus"
							>
								<option value>All</option>
								<option value="published">Published</option>
								<option value="draft">Draft</option>
							</select>

							<div class="search-form">
								<i class="icon icon-loupe"></i>
								<input type="text" class="form-control form-light" data-lpignore="true" v-model="filters.text" placeholder="Search Workflow">
							</div>
						</div>
					</div>

					<moon-loader class="mt-5" size="40px" v-if="loading" />

					<div v-else-if="workflows.length !== 0" class="row">
						<div class="col-lg-12">
							<div class="card hl_customer-acquisition--table">
								<div class="card-body --no-padding">
									<div class="table-wrap">
										<table class="table table-sort">
											<thead>
												<tr>
													<th>Name</th>
													<th>Status</th>
													<th>Workflow ID</th>
													<th>
														<!--Actions-->
													</th>
												</tr>
											</thead>
											<tbody v-if="filteredWorkflows">
												<WorkflowListItem
													v-for="workflow in filteredWorkflows"
													:workflow="workflow"
													:key="workflow.id"
												/>
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div v-else class="mt-3 d-flex flex-column align-items-center">
						<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="f20e0c25-d928-42cc-98d1-13cc230663ea" data-name="Layer 1" width="100" height="150" viewBox="0 0 820.16 780.81" src="https://42f2671d685f51e10fc6-b9fcecea3e50b3b59bdc28dead054ebc.ssl.cf5.rackcdn.com/illustrations/no_data_qbuo.svg" class="waiting__empty__img"><defs><linearGradient id="07332201-7176-49c2-9908-6dc4a39c4716" x1="539.63" y1="734.6" x2="539.63" y2="151.19" gradientTransform="translate(-3.62 1.57)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="gray" stop-opacity="0.25"></stop><stop offset="0.54" stop-color="gray" stop-opacity="0.12"></stop><stop offset="1" stop-color="gray" stop-opacity="0.1"></stop></linearGradient><linearGradient id="0ee1ab3f-7ba2-4205-9d4a-9606ad702253" x1="540.17" y1="180.2" x2="540.17" y2="130.75" gradientTransform="translate(-63.92 7.85)" xlink:href="#07332201-7176-49c2-9908-6dc4a39c4716"></linearGradient><linearGradient id="abca9755-bed1-4a97-b027-7f02ee3ffa09" x1="540.17" y1="140.86" x2="540.17" y2="82.43" gradientTransform="translate(-84.51 124.6) rotate(-12.11)" xlink:href="#07332201-7176-49c2-9908-6dc4a39c4716"></linearGradient><linearGradient id="2632d424-e666-4ee4-9508-a494957e14ab" x1="476.4" y1="710.53" x2="476.4" y2="127.12" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#07332201-7176-49c2-9908-6dc4a39c4716"></linearGradient><linearGradient id="97571ef7-1c83-4e06-b701-c2e47e77dca3" x1="476.94" y1="156.13" x2="476.94" y2="106.68" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#07332201-7176-49c2-9908-6dc4a39c4716"></linearGradient><linearGradient id="7d32e13e-a0c7-49c4-af0e-066a2f8cb76e" x1="666.86" y1="176.39" x2="666.86" y2="117.95" gradientTransform="matrix(1, 0, 0, 1, 0, 0)" xlink:href="#07332201-7176-49c2-9908-6dc4a39c4716"></linearGradient></defs><title>no data</title><rect x="317.5" y="142.55" width="437.02" height="603.82" transform="translate(-271.22 62.72) rotate(-12.11)" fill="#e0e0e0"></rect><g opacity="0.5"><rect x="324.89" y="152.76" width="422.25" height="583.41" transform="translate(-271.22 62.72) rotate(-12.11)" fill="url(#07332201-7176-49c2-9908-6dc4a39c4716)"></rect></g><rect x="329.81" y="157.1" width="411.5" height="570.52" transform="translate(-270.79 62.58) rotate(-12.11)" fill="#fafafa"></rect><rect x="374.18" y="138.6" width="204.14" height="49.45" transform="translate(-213.58 43.93) rotate(-12.11)" fill="url(#0ee1ab3f-7ba2-4205-9d4a-9606ad702253)"></rect><path d="M460.93,91.9c-15.41,3.31-25.16,18.78-21.77,34.55s18.62,25.89,34,22.58,25.16-18.78,21.77-34.55S476.34,88.59,460.93,91.9ZM470.6,137A16.86,16.86,0,1,1,483.16,117,16.66,16.66,0,0,1,470.6,137Z" transform="translate(-189.92 -59.59)" fill="url(#abca9755-bed1-4a97-b027-7f02ee3ffa09)"></path><rect x="375.66" y="136.55" width="199.84" height="47.27" transform="translate(-212.94 43.72) rotate(-12.11)" fill="#37ca37"></rect><path d="M460.93,91.9a27.93,27.93,0,1,0,33.17,21.45A27.93,27.93,0,0,0,460.93,91.9ZM470.17,135a16.12,16.12,0,1,1,12.38-19.14A16.12,16.12,0,0,1,470.17,135Z" transform="translate(-189.92 -59.59)" fill="#37ca37"></path><rect x="257.89" y="116.91" width="437.02" height="603.82" fill="#e0e0e0"></rect><g opacity="0.5"><rect x="265.28" y="127.12" width="422.25" height="583.41" fill="url(#2632d424-e666-4ee4-9508-a494957e14ab)"></rect></g><rect x="270.65" y="131.42" width="411.5" height="570.52" fill="#fff"></rect><rect x="374.87" y="106.68" width="204.14" height="49.45" fill="url(#97571ef7-1c83-4e06-b701-c2e47e77dca3)"></rect><path d="M666.86,118c-15.76,0-28.54,13.08-28.54,29.22s12.78,29.22,28.54,29.22,28.54-13.08,28.54-29.22S682.62,118,666.86,118Zm0,46.08a16.86,16.86,0,1,1,16.46-16.86A16.66,16.66,0,0,1,666.86,164Z" transform="translate(-189.92 -59.59)" fill="url(#7d32e13e-a0c7-49c4-af0e-066a2f8cb76e)"></path><rect x="377.02" y="104.56" width="199.84" height="47.27" fill="#37ca37"></rect><path d="M666.86,118a27.93,27.93,0,1,0,27.93,27.93A27.93,27.93,0,0,0,666.86,118Zm0,44.05A16.12,16.12,0,1,1,683,145.89,16.12,16.12,0,0,1,666.86,162Z" transform="translate(-189.92 -59.59)" fill="#37ca37"></path><g opacity="0.5"><rect x="15.27" y="737.05" width="3.76" height="21.33" fill="#47e6b1"></rect><rect x="205.19" y="796.65" width="3.76" height="21.33" transform="translate(824.47 540.65) rotate(90)" fill="#47e6b1"></rect></g><g opacity="0.5"><rect x="451.49" width="3.76" height="21.33" fill="#47e6b1"></rect><rect x="641.4" y="59.59" width="3.76" height="21.33" transform="translate(523.63 -632.62) rotate(90)" fill="#47e6b1"></rect></g><path d="M961,832.15a4.61,4.61,0,0,1-2.57-5.57,2.22,2.22,0,0,0,.1-.51h0a2.31,2.31,0,0,0-4.15-1.53h0a2.22,2.22,0,0,0-.26.45,4.61,4.61,0,0,1-5.57,2.57,2.22,2.22,0,0,0-.51-.1h0a2.31,2.31,0,0,0-1.53,4.15h0a2.22,2.22,0,0,0,.45.26,4.61,4.61,0,0,1,2.57,5.57,2.22,2.22,0,0,0-.1.51h0a2.31,2.31,0,0,0,4.15,1.53h0a2.22,2.22,0,0,0,.26-.45,4.61,4.61,0,0,1,5.57-2.57,2.22,2.22,0,0,0,.51.1h0a2.31,2.31,0,0,0,1.53-4.15h0A2.22,2.22,0,0,0,961,832.15Z" transform="translate(-189.92 -59.59)" fill="#4d8af0" opacity="0.5"></path><path d="M326.59,627.09a4.61,4.61,0,0,1-2.57-5.57,2.22,2.22,0,0,0,.1-.51h0a2.31,2.31,0,0,0-4.15-1.53h0a2.22,2.22,0,0,0-.26.45,4.61,4.61,0,0,1-5.57,2.57,2.22,2.22,0,0,0-.51-.1h0a2.31,2.31,0,0,0-1.53,4.15h0a2.22,2.22,0,0,0,.45.26,4.61,4.61,0,0,1,2.57,5.57,2.22,2.22,0,0,0-.1.51h0a2.31,2.31,0,0,0,4.15,1.53h0a2.22,2.22,0,0,0,.26-.45A4.61,4.61,0,0,1,325,631.4a2.22,2.22,0,0,0,.51.1h0a2.31,2.31,0,0,0,1.53-4.15h0A2.22,2.22,0,0,0,326.59,627.09Z" transform="translate(-189.92 -59.59)" fill="#fdd835" opacity="0.5"></path><path d="M855,127.77a4.61,4.61,0,0,1-2.57-5.57,2.22,2.22,0,0,0,.1-.51h0a2.31,2.31,0,0,0-4.15-1.53h0a2.22,2.22,0,0,0-.26.45,4.61,4.61,0,0,1-5.57,2.57,2.22,2.22,0,0,0-.51-.1h0a2.31,2.31,0,0,0-1.53,4.15h0a2.22,2.22,0,0,0,.45.26,4.61,4.61,0,0,1,2.57,5.57,2.22,2.22,0,0,0-.1.51h0a2.31,2.31,0,0,0,4.15,1.53h0a2.22,2.22,0,0,0,.26-.45,4.61,4.61,0,0,1,5.57-2.57,2.22,2.22,0,0,0,.51.1h0a2.31,2.31,0,0,0,1.53-4.15h0A2.22,2.22,0,0,0,855,127.77Z" transform="translate(-189.92 -59.59)" fill="#fdd835" opacity="0.5"></path><circle cx="812.64" cy="314.47" r="7.53" fill="#f55f44" opacity="0.5"></circle><circle cx="230.73" cy="746.65" r="7.53" fill="#f55f44" opacity="0.5"></circle><circle cx="735.31" cy="477.23" r="7.53" fill="#f55f44" opacity="0.5"></circle><circle cx="87.14" cy="96.35" r="7.53" fill="#4d8af0" opacity="0.5"></circle><circle cx="7.53" cy="301.76" r="7.53" fill="#47e6b1" opacity="0.5"></circle></svg>
						<p>No workflows found...</p>
						<a href="workflow-new" @click.prevent="createNew">Create new</a>
					</div>
				</div>
			</div>
		</section>
		<!-- END of .hl_marketing -->
	</section>
	<!-- END of .hl_wrapper -->
</template>

<script lang="ts">
import Vue from 'vue';
import { mapState } from 'vuex';
import { UserState } from '../../../store/state_models';
import { Workflow, User } from '../../../models/';
import initSelectPicker from '../../components-initer/selectpicker';

import TopBar from '../../components/TopBar.vue';
import SideBar from '../../components/SideBar.vue';
const WorkflowListItem = () => import( '../../components/workflow/WorkflowListItem.vue');

let unsubscribeCampaigns: () => void;

export default Vue.extend({
	components: {
		TopBar,
		SideBar,
		WorkflowListItem
	},
	data() {
		return {
			loading: true,
			currentLocationId: '',
			workflows: [] as Workflow[],
			filters: {
				text: '',
				status: null
			}
		};
	},
	watch: {
		'$route.params.location_id': function (id) {
			this.currentLocationId = id;
			this.fetchData();
		},
	},
	created() {
		this.currentLocationId = this.$router.currentRoute.params.location_id;
		this.fetchData();
	},
	mounted() {
		initSelectPicker(this.$el);
	},
	updated() {
		initSelectPicker(this.$el);
	},
	methods: {
		fetchData() {
			if (unsubscribeCampaigns) unsubscribeCampaigns();
			unsubscribeCampaigns = Workflow.getQueryWithLocationId(this.currentLocationId).onSnapshot(async snapshot => {
				this.workflows = snapshot.docs.map(d => new Workflow(d));
				this.loading = false;
			});
		},
		async createNew() {
			let workflow = new Workflow();
      workflow.locationId = this.currentLocationId;
      workflow.name = "Workflow " + (this.workflows.length + 1);
      workflow.workflowData = {
        templates: []
      };
      await workflow.save();

			this.$router.push({
				name: 'workflow_edit',
				params: {
					location_id: this.currentLocationId,
					workflow_id: workflow.id
				}
			});
		}
	},
	computed: {
		filteredWorkflows(): Workflow[] {
			return !this.filters.text && !this.filters.status
							? this.workflows
							: this.workflows.filter(
									x => (!this.filters.text || x.name.toLowerCase().includes(this.filters.text.toLowerCase()))
										&& (!this.filters.status || x.status === this.filters.status)
								);
		},
		allowEdit(): boolean {
			return this.user && this.user.permissions.campaigns_read_only !== true;
		},
		...mapState('user', {
			user: (s: UserState) => {
				return s.user ? new User(s.user) : undefined;
			},
		})
	},
	beforeDestroy() {
		if (unsubscribeCampaigns) unsubscribeCampaigns();
	}
});
</script>

<style>
.workflow_list .hl_customer-acquisition .hl_controls {
	justify-content: flex-end;
}
</style>
