import { <PERSON><PERSON><PERSON>, ActionContext } from 'vuex'
import { RootState, DefaultEmailService } from './state_models'
import { SMTPService } from '../models';

export const defaultEmailService: Module<DefaultEmailService, RootState> = {
  namespaced: true,
  state: { defaultEmailService: {} },
  mutations: {
    set: (
      state: DefaultEmailService,
      payload: any
    ) => {
      state.defaultEmailService = payload
    },
    clearAll(state: DefaultEmailService) {
      state.defaultEmailService = {}
    }
  },
  actions: {
    checkFromVisibility: (context: ActionContext<DefaultEmailService, RootState>, serviceId: string): Promise<Boolean> => {
      return new Promise(async (resolve, reject) => {
        let service;
        if (context.state.defaultEmailService && context.state.defaultEmailService.id === serviceId) {
          service = context.state.defaultEmailService;
        } else {
          let obj = await SMTPService.getById(serviceId);
          service = { ...obj.data, id: obj.id };
        }
        if (service) {
          context.commit('set', service)
          if ((['gmail', 'yahoo mail', 'outlook'].indexOf(service.provider_type) !== -1 || (service.email && service.email.includes('@hotmail.com')))) return resolve(true)
        }
        return resolve(false)
      })
    },
    set: (context: ActionContext<DefaultEmailService, RootState>, payload) => {
      let service = { ...payload.data, id: payload.id }
      context.commit('set', service)
    },
    logout: {
      root: true,
      handler: (context: ActionContext<DefaultEmailService, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
