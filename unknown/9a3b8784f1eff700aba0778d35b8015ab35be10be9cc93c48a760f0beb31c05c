<template>
  <div>
    <div class="form-group" v-if="localConditions.length>0">
      <label>
        Define filters
        <span
            style="margin-left: 5px"
            class="input-group-addon"
            v-b-tooltip.hover
            title="Optins are not triggered for One step order forms"
            >
            <i class="fas fa-question-circle"></i>
        </span>
      </label>
      <div
        class="form-input-group space-x-1 filters"
        v-for="(condition,index) in localConditions"
        style="margin-bottom: 5px;"
        :key="index"
      >
        <CustomDropDownElement
          side="left"
          :condition="condition"
          :options="availableFilters"
          placeholder="Select filter"
          :filterMaster="localFilters"
          @update="saveResponse(condition, 'field', $event)"
          group="true"
        />
        <CustomDropDownElement
          v-if="condition.field && getFilter(condition).operatorType==='custom'"
          side="center"
          :condition="condition"
          :options="getFilter(condition).operatorOptions"
          placeholder="Select condition"
          :filterMaster="getFilter(condition).operatorOptions"
          @update="saveResponse(condition, 'operator', $event)"
        />
        <CustomDropDownElement
          v-if="condition.field && getFilter(condition).type==='select' && (getFilter(condition).operatorType!=='custom' || (getFilter(condition).operatorType === 'custom' && condition.operator !== '=='))"
          side="right"
          :condition="condition"
          :options="getFilter(condition).options"
          :placeholder="getFilter(condition).placeHolder"
          :filterMaster="getFilter(condition).options"
          :allowMultiple="getFilter(condition).allowMultiple"
          @update="saveResponse(condition, getFilter(condition).allowMultiple ? 'multiple_select' : 'value', $event)"
        />
        <input
          class="form-control"
          v-if="condition.field && getFilter(condition).type==='input' && (getFilter(condition).operatorType!=='custom' || (getFilter(condition).operatorType === 'custom' && condition.operator !== '=='))"
          :type="getValueType(condition)"
          :value="condition.value"
          @input="saveTypeValue(condition, $event)"
          :placeholder="getFilter(condition).placeHolder"
          autocomplete="msgsndr_trigger_filter"
          name="msgsndr_trigger_filter"
          data-lpignore="true"
        />
        <div style="margin-left: 10px;flex: unset !important;" class="pointer">
          <i class="icon icon-trash --light" @click.prevent="removeFilter(condition)"></i>
        </div>
      </div>
    </div>
    <div class="form-group" v-if="availableFilters.length > 0">
      <a href="javascript:void(0);" @click.prevent="addFilter">Add filter</a>
    </div>
    <div class="form-input-group filters option" style="margin-bottom: 5px;" v-if="showMatchYear">
      <input
        type="checkbox"
        :checked="matchYear"
        id="matchYear"
        @click="$emit('update:matchYear', !matchYear)"
      />
      <label for="matchYear">Match on the year along with the day and month</label>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Campaign, CustomField, FieldType } from '@/models'
import { Condition } from '@/models/trigger'
const CustomDropDownElement = () => import('../CustomDropDownElement.vue')
import { clone, find, filter, groupBy, findIndex, orderBy } from 'lodash'
export default Vue.extend({
  props: ['conditions', 'filterMaster', 'matchYear', 'showMatchYear'],
  components: { CustomDropDownElement },
  data() {
    return {
      currentLocationId: '',
      localConditions: [] as Condition[],
      localFilters: [] as { [key: string]: any }[],
      customFields: [] as CustomField[],
      filter: ''
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.localConditions = clone(this.conditions)
    await CustomField.getByLocationId(this.currentLocationId)
      .get()
      .then((querySnapshot: any) => {
        let fields = querySnapshot.docs.map(
          (d: any) => new CustomField(d)
        )
        try {
          this.customFields = orderBy(fields, [field => field?.name?.toLowerCase()])
        } catch (err) {
          this.customFields = fields
        }
      })
    this.conditionsChanged()
  },
  computed: {
    availableFilters(): { [key: string]: any }[] {
      const availableFilter = filter(this.localFilters, filter => {
        if (filter.id) return !find(this.localConditions, { id: filter.id })
        return !find(this.localConditions, { field: filter.value })
      })
      if (availableFilter.length !== 0) {
        let { true: customFilter = [], undefined: filter = [] } = groupBy(
          availableFilter,
          'customField'
        )
        const groupOption = [
          { title: 'Standard filters', options: filter },
          { title: 'Custom field filters', options: customFilter }
        ]
        return groupOption
      }
      return []
    }
  },
  watch: {
    conditions: {
      handler: function(conditions) {
        this.localConditions = clone(this.conditions)
        this.conditionsChanged()
      },
      deep: true
    },
    filterMaster: {
      handler: function(filterMaster) {
        this.localFilters = clone(this.filterMaster)
        this.addCustomFilters()
        this.conditionsChanged()
      },
      immediate: true,
      deep: true
    },
    customFields(value) {
      this.addCustomFilters()
    }
  },
  methods: {
    addCustomFilters() {
      if (this.customFields.length) {
        this.customFields.forEach((v: CustomField) => {
          if (
            [FieldType.DATE, FieldType.PHONE, FieldType.TEXTBOX_LIST].indexOf(
              FieldType[v.dataType]
            ) !== -1
          )
            return
          let data = {
            id: v.id,
            placeHolder: v.name,
            title: v.name,
            value: 'contact.' + v.id,
            type: 'select',
            operatorType: '',
            operatorOptions: [] as { [key: string]: any }[],
            valueType: 'text',
            options: [] as { [key: string]: any }[],
            allowMultiple: false,
            customField: true
          }
          switch (FieldType[v.dataType]) {
            case FieldType.TEXT:
            case FieldType.LARGE_TEXT:
              data.operatorType = 'custom'
              data.type = ''
              data.operatorOptions = [
                { title: 'Contains phrase', value: 'contains' },
                { title: 'Exact match phrase', value: 'exact' },
                { title: 'Matches Intent', value: 'matches_intent' },
                { title: 'Is not empty', value: 'has_value' }
              ]
              break
            case FieldType.NUMERICAL:
            case FieldType.MONETORY:
              data.operatorType = 'custom'
              data.type = 'input'
              data.valueType = 'number'
              data.operatorOptions = [
                { title: '==', value: '=' },
                { title: '!=', value: '!=' },
                { title: '>', value: '>' },
                { title: '<', value: '<' },
                { title: '>=', value: '>=' },
                { title: '<=', value: '<=' }
              ]
              break
            case FieldType.MULTIPLE_OPTIONS:
            case FieldType.CHECKBOX:
              data.allowMultiple = true
            case FieldType.SINGLE_OPTIONS:
            case FieldType.RADIO:
              if (v.picklistOptions) {
                let options: { [key: string]: any }[] = []
                v.picklistOptions.forEach(plOption =>
                  options.push({
                    title: plOption,
                    value:
                      FieldType[v.dataType] === FieldType.SINGLE_OPTIONS
                        ? plOption.toLowerCase()
                        : plOption
                  })
                )
                data.options = options
              } else if (
                FieldType[v.dataType] === FieldType.RADIO &&
                v.picklistOptionsImage &&
                !v.picklistOptions
              ) {
                let options: { [key: string]: any }[] = []
                v.picklistOptionsImage.forEach(plOption =>
                  options.push({
                    title: plOption.label,
                    value: plOption.label
                  })
                )
                data.options = options
              }
              break
          }
          if (findIndex(this.localFilters, { id: v.id }) !== -1) {
            this.localFilters[findIndex(this.localFilters, { id: v.id })] = data
          } else {
            this.localFilters.push(data)
          }
        })
      }
    },
    conditionsChanged() {
      this.localConditions.forEach(condition => {
        let filterIndex = findIndex(this.localFilters, {
          id: condition.id
        })
        if (
          this.getFilter(condition).operatorType === 'custom' &&
          condition.operator
        ) {
          if (condition.operator === 'matches_intent') {
            this.localFilters[filterIndex].type = 'select'
            this.localFilters[filterIndex].options = [
              { title: 'Positive/Yes', value: 'schedule-yes' },
              { title: 'Negative/No', value: 'schedule-no' }
            ]
          } else if (['contains', 'exact'].indexOf(condition.operator) !== -1) {
            this.localFilters[filterIndex].type = 'input'
          }
        }
      })
    },
    publishUpdate() {
      this.localConditions.map(condition => {
        const filter = this.getFilter(condition)
        if (filter) condition.operator = condition.operator || '=='
      })
      this.$emit('update:conditions', this.localConditions)
    },
    getValueType(condition: Condition) {
      const filter = this.getFilter(condition)
      return filter ? filter.valueType : 'text'
    },
    getFilter(condition: Condition): { [key: string]: any } {
      if (condition.id)
        return find(this.localFilters, { id: condition.id }) || {}
      return find(this.localFilters, { value: condition.field }) || {}
    },
    addFilter() {
      this.localConditions.push({ field: '', operator: '==', value: '' })
      this.publishUpdate()
    },
    removeFilter(condition: Condition) {
      if (this.localConditions.indexOf(condition) !== -1) {
        this.localConditions.splice(this.localConditions.indexOf(condition), 1)
        this.publishUpdate()
      }
    },
    saveResponse(
      condition: Condition,
      type: string,
      picked: { [key: string]: any }
    ) {
      if (type === 'field') {
        if (picked.id) Vue.set(condition, 'id', picked.id)
        else Vue.delete(condition, 'id')
        Vue.set(condition, 'field', picked.value)
        Vue.set(condition, 'title', picked.title)
        Vue.set(
          condition,
          'operator',
          this.getFilter(condition).operator
            ? this.getFilter(condition).operator
            : ''
        )
        Vue.set(condition, 'value', '')
      } else if (type === 'operator') {
        Vue.set(condition, 'operator', picked.value)
      } else if (type === 'multiple_select') {
        let options = this.getFilter(condition).options
        let newValues: string[] = []
        if (Array.isArray(condition.value)) {
          options.map(data => {
            if (
              condition.value.includes(data.value) &&
              !newValues.includes(data.value)
            ) {
              newValues.push(data.value)
            }
          })
        }
        if (newValues.indexOf(picked.value) !== -1)
          newValues.splice(newValues.indexOf(picked.value), 1)
        else newValues.push(picked.value)
        Vue.set(condition, 'value', newValues)
      } else {
        Vue.set(condition, 'value', picked.value)
      }
      this.publishUpdate()
    },
    saveTypeValue(condition: Condition, event: any) {
      const val = event.target.value
      const valueType = this.getValueType(condition)
      if (valueType === 'number') Vue.set(condition, 'value', Number(val))
      else Vue.set(condition, 'value', val)
      this.publishUpdate()
    }
  }
})
</script>
