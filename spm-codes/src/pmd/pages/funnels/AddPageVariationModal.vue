<template>
  <div
    class="modal fade hl_funnel-add-new-variation--modal"
    id="add-new-variation"
  	tabindex="-1"
		role="dialog"
		aria-hidden="true"
		ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Create New Variation</h2>
            <button @click="hideModal()" type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner" style="text-align: center;">
            <div v-if="duplicatingPage">
              <div class="row">
                <div class="col-sm-2"></div>
                <div class="col-sm-8">
                  Duplicating Page in Progress...
                </div>
                <div class="col-sm-2"></div>
              </div>

            </div>
            <div v-if="noOptionChosen">
              <div class="row">
                <div class="col-sm"></div>
                <div class="col-sm">
                  <button @click="duplicatePage()" class="btn btn-primary">Create Duplicate from Control Page</button>
                </div>
                <div class="col-sm"></div>
              </div>
              <div class="row">
                <div class="col-sm"></div>
                <div class="col-sm">
                  <button @click="addBlankPage()" class="btn btn-success">Create Blank Page</button>
                </div>
                <div class="col-sm"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    pageName: {
      type: String,
      required: true
    }
  },
  mounted() {
    const _self = this;
		$(this.$refs.modal).on('hidden.bs.modal', function () {
			_self.$emit('hide');
		});
  },
  beforeDestroy() {
		$(this.$refs.modal).off('hidden.bs.modal');
  },
  computed: {
    noOptionChosen: function(): boolean {
      return !(this.duplicatingPage || this.showTemplates)
    }
  },
  data() {
    return {
      showTemplates: false,
      duplicatingPage: false
    }
  },
  methods: {
    hideModal() {
      this.$emit('hide');
    },
    duplicatePage() {
      this.duplicatingPage = true;
      this.$emit('duplicatePage');
    },
    addBlankPage() {
      //this.showTemplates = true;
      this.$emit('createBlankPage');
    }
  },
  watch: {
    showModal(newValue, oldValue) {
      if (newValue && !oldValue) {
        // show the modal
        this.showTemplates = false;
        this.duplicatingPage = false;
        $(this.$refs.modal).modal('show');
      } else if(!newValue && oldValue) {
        // hide the modal
        $(this.$refs.modal).modal('hide');
      }
    }
  }
})
</script>
