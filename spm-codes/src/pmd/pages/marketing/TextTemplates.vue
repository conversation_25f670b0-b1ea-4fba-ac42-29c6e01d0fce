<template>
  <section class="hl_wrapper d-flex">
    <section class="hl_wrapper--inner form-builder-list" :class="{'hl_template--container': getSideBarVersion == 'v2'}" id="form-builder">
      	<div class="container-fluid">
          <TemplateSettings />
      	</div>
    </section>
  </section>
</template>

<script lang="ts">
import Vue from 'vue'
import { User } from '@/models'
import { UserState } from '@/store/state_models'
const TemplateSettings = () =>
  import('@/pmd/pages/settings/TemplateMessageSettings.vue').then(
    m => m.default
  )
export default Vue.extend({
  components: {
    TemplateSettings
  },
  computed: {
    user() {
      const user: UserState = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  }
})
</script>
