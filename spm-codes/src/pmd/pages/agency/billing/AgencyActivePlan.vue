<template>
  <div>
    <div>
      <div
        class="billing-alert warning"
        v-if="company.status === 'active_test-agency'"
      >
        This is a test account. If you think this is a mistake
        <contact-support />
      </div>
      <div
        class="billing-alert danger"
        v-else-if="
          company.status === 'active_overused' &&
          locationCount > allowedLocations
        "
      >
        Your agency is over limit. You have {{ locationCount }} active locations
        while your plan only allows for
        {{ allowedLocations === 2 ? '1' : allowedLocations }} location. Please
        take a moment to upgrade your account.
        <span v-if="************* >= +new Date()" style="font-weight: 600">
          You will be auto-upgraded on September 15<sup>th</sup> 2020.</span
        >
        <br /><br />
        <button
          type="button"
          class="btn btn-success"
          @click="handlePlanUpgrade()"
        >
          Upgrade Now
        </button>
      </div>
      <div
        class="billing-alert danger"
        v-else-if="company.status === 'active_failed-payment'"
      >
        Your last payment method has failed. System will retry the payment. If
        it doesn't work, please <contact-support /> to avoid account suspension
        <span v-if="company.date_inactivation"
          >by {{ inactivationDate(company.date_inactivation.seconds) }}
        </span>
      </div>
      <div
        class="billing-alert danger"
        v-else-if="
          company.status && company.status.split('_')[0] === 'inactive'
        "
      >
        <!-- Your Agency account is inactive !! Please <contact-support /> to avoid account suspension. -->
        Your account has a billing issue. Please
        <contact-support /> immediately. Your Service will continue to run as
        Normal
      </div>
    </div>

    <div class="shimmer-card" v-if="loadingActivePlan">
      <upgrade-modal-shimmer style="max-width: 100%" />
    </div>

    <div class="hl_billing--account" v-else-if="planDetails.amount">
      <div class="card" style="margin-bottom: 0px">
        <div class="card-body">
          <h3>{{ planDetails.planName }}</h3>
          <p>
            ${{ planDetails.amount }}<span>/{{ planDetails.duration }}</span>
          </p>
        </div>
        <div
          class="hl-billing__upgrade-btn btn"
          @click="handlePlanUpgrade()"
          v-if="
            !(
              company.status === 'active_overused' &&
              locationCount > allowedLocations
            )
          "
        >
          UPGRADE
        </div>
      </div>
      <div class="card-extend" v-if="planDetails.annualUpgradable">
        <p>
          Get 2 months free, if you upgrade to annual subscription.
          <button
            class="upgrade-btn btn btn-primary"
            @click="showAnnualUpgradeModal"
          >
            Claim 2 months free
          </button>
        </p>
      </div>
      <annual-upgrade-modal
        :activePlan="activePlan"
        :planDetails="planDetails"
        :values="annualUpgradeModal"
        :company="company"
        @hidden="annualUpgradeModal = { visible: false }"
        @success="onUpgradation"
      />
    </div>
    <upgrade-modalv2
      :company="company"
      v-if="showUpgradeModal"
      :type="upgradeType === 'annual' ? 'annual-upgrade' : ''"
      :show="showUpgradeModal"
      source="billing-page"
      @close="showUpgradeModal = false"
      @success="onUpgradation"
    />
    <div>
      <div
        class="billing-alert warning"
        v-if="
          activePlan && activePlan.status === 'trialing' && planDetails.amount
        "
      >
        <b
          >Your trial will expire in
          {{ trialExpiredRemainingDays(activePlan.trial_end) }} days.</b
        >
        After {{ trialExpiredDate(activePlan.trial_end) }} system will
        automatically charge your account. To modify or cancel please
        <a
          target="_blank"
          :href="`https://www.gohighlevel.com/modify-subscription?user_email=${user.email}&company_email=${company.email}&company_id=${company.id}`"
          title="Trial Subscription modification process might take upto 3 working days."
        >
          <b>click here</b></a
        >
        <!-- To modify please <contact-support title="Trial-account modification process might take upto 3 working days."/> -->
      </div>
      <div
        class="billing-alert success"
        v-else-if="
          activePlan && activePlan.status === 'active' && activePlan.current_end
        "
      >
        Your next invoice is scheduled on:<b>
          {{ trialExpiredDate(activePlan.current_end) }}
        </b>
        <span v-if="company && user && user.role === 'admin'">
          <br />
          To modify or cancel your current subscription
          <a
            target="_blank"
            :href="`https://www.gohighlevel.com/modify-subscription?user_email=${user.email}&company_email=${company.email}&company_id=${company.id}`"
            title="Subscription modification process might take upto 10 working days."
          >
            <b>click here</b></a
          >
        </span>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { Company, User } from '@/models'
import { trackGaEvent } from '@/util/helper'
import AnnualUpgradeModal from './AnnualUpgradeModal.vue'
import ContactSupport from '@/pmd/components/common/ContactSupport.vue'
import * as moment from 'moment-timezone'
import UpgradeModalShimmer from '@/pmd/components/common/shimmers/UpgradeModalShimmer.vue'
import UpgradeModalv2 from '@/pmd/components/agency/billing/UpgradeModalv2.vue'

import { CompanyState, UserState, CalendarState } from '@/store/state_models'

export default Vue.extend({
  components: {
    AnnualUpgradeModal,
    ContactSupport,
    UpgradeModalShimmer,
    UpgradeModalv2,
  },
  data() {
    return {
      annualUpgradeModal: { visible: false },
      upgradeType: 'normal', // 'normal' || 'annual'
      showUpgradeModal: false,
      company: Company,
      activePlan: {} as { [key: string]: any },
      locationCount: '',
      allowedLocations: 2,
      loadingActivePlan: false,
    }
  },
  created() {
    // this.fetchCompanyDetails();
    this.fetchActivePlan()
  },
  methods: {
    showAnnualUpgradeModal() {
      trackGaEvent('Billing', this.company.id, 'Annual upgrade initiated')
      // this.annualUpgradeModal = {visible: true};
      this.upgradeType = 'annual'
      this.showUpgradeModal = true
    },
    trialExpiredRemainingDays(timestamp) {
      let today = moment()
      let expiry = moment.unix(timestamp)
      return expiry.diff(today, 'days')
    },
    trialExpiredDate(timestamp) {
      return moment.unix(timestamp).format('MMM DD, YYYY')
    },
    inactivationDate(timestampSeconds) {
      return moment(timestampSeconds * 1000).format('MMM DD, YYYY')
    },
    async fetchCompanyDetails() {
      let company = await this.$store.dispatch('company/get')
      this.company = company.data
    },
    async fetchActivePlan() {
      this.loadingActivePlan = true
      await this.fetchCompanyDetails()
      try {
        let activePlan = await this.$http.get(
          `/api/stripe/agency_plan?company_id=${this.company.id}`
        )
        if (activePlan.data && activePlan.data.plan) {
          this.activePlan = activePlan.data.plan
        }
        await this.fetchLocationsCount()
      } catch (e) {
        console.error(e)
      } finally {
        this.loadingActivePlan = false
      }
    },
    onUpgradation(plan: any) {
      this.activePlan = plan
      this.fetchCompanyDetails()
      // this.fetchActivePlan(); // critical, this will hide modal as well.
    },
    async fetchLocationsCount() {
      // await this.fetchCompanyDetails();
      let result = await this.$http.post('/stripe/sync_locations', {
        company_id: this.company.id,
      })
      this.locationCount = result.data.totalActiveLocations
      this.allowedLocations = result.data.allowedLocations
    },
    handlePlanUpgrade() {
      trackGaEvent(
        'LevelUp Upgrade',
        this.company.id,
        'Billing Page Initiate',
        1
      )
      // this.$emit('upgrade');
      this.upgradeType = 'normal'
      this.showUpgradeModal = true
    },
  },
  computed: {
    user() {
      const user: UserState = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    planDetails() {
      let planItems = this.activePlan.name
        ? this.activePlan.name.split('_')
        : []
      let duration = planItems.length ? planItems[1] : ''
      let amount = planItems.length ? planItems[2] : ''
      if (planItems[0] === 'direct') {
        // legacy code only. can be removed after 15-september-2020
        duration = planItems[2]
        amount = planItems[3]
      }
      let annualUpgradable = false
      if (duration === 'monthly') {
        duration = 'month'
        annualUpgradable = true
      }
      if (this.activePlan.status === 'trialing') {
        annualUpgradable = false
      }
      let planName = ''
      switch (amount) {
        case '97':
          planName = 'Agency Starter Account'
          // annualUpgradable = true;
          break
        case '297':
          planName = 'Agency Unlimited Account'
          // annualUpgradable = true;
          break
        case '497':
          planName = 'Agency Pro Account'
          // annualUpgradable = true;
          break
        case '597':
          planName = 'Premium Unlimited Account'
          annualUpgradable = false
          break
        case '237':
          planName = 'LVRG Account'
          break
        default:
          annualUpgradable = false
          break
      }
      if (duration === 'annual') {
        amount += '0'
        annualUpgradable = false
        duration = 'year'
      }
      return { planName, amount, duration, annualUpgradable }
    },
  },
})
</script>

<style scoped>
.hl_billing--account {
  background-color: #ffffff;
  border-radius: 5px;
  margin-bottom: 30px;
  /* box-shadow: 0 0px 20px 0 rgba(0, 0, 0, 0.1), 0 0px 20px 0 rgba(0, 0, 0, 0.1); */
}
.annual-upgrade .seperator {
  width: 100%;
  height: 1px;
  background-color: #ffffff;
  margin: 40px 0px 20px;
  border-radius: 50%;
}
.annual-upgrade p {
  font-size: 14px;
}
.annual-upgrade p .link {
  cursor: pointer;
  font-weight: 600;
}
.card-extend {
  padding: 10px 20px;
  background-color: #ffffff;
  border-radius: 5px;
  text-align: center;
  border-bottom: 2px solid #35ca38;
}
.card-extend .link {
  cursor: pointer;
  font-weight: 900;
}
.card-extend p {
  line-height: 40px;
}
.upgrade-btn {
  margin: 15px;
}
.shimmer-card {
  padding: 20px 20px 0px;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
}
/* .hl_billing--account .card{
    background-image: linear-gradient(to right, #e8c411, #b66a00);
} */
.hl_billing--account .card .card-body {
  padding: 70px 30px !important;
}
.hl_billing--account .card h3 {
  font-size: 1.2rem;
  margin-bottom: 20px;
}

.billing-alert {
  background-color: #ffffff;
  border-left: 4px solid #ffbc00;
  padding: 20px;
  margin-bottom: 30px;
  line-height: 20px;
}
.billing-alert.warning {
  border-left: 4px solid #ffbc00;
}
.billing-alert.danger {
  border-left: 4px solid #e93d3d;
}
.billing-alert.success {
  border-left: 4px solid #35ca38;
}
.hl_billing--account .card .card-body {
  position: relative;
}
.hl-billing__upgrade-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: #35ca38;
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 4px;
  z-index: 2;
  transition: all 0.3s ease-in-out;
}
.hl-billing__upgrade-btn:hover {
  background-color: #24a55a;
}
/* .billing-alert__item{
  border-bottom: 1px solid #eeeeee;
  padding: 12px 0px;
}
.billing-alert__item:nth-last-child{
  border-bottom: none;
} */
</style>
