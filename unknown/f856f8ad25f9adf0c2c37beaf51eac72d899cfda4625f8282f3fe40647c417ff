<template>
    <div class="form-group">
        <div class="form-input-dropdown dropdown">
            <div data-toggle="dropdown">
                <i class="icon icon-arrow-down-1"></i>
                <input type="text" class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800" data-lpignore="true" placeholder="Select campaign" :value="campaignName">
            </div>
            <div class="dropdown-menu">
                <a class="dropdown-item trigger-type" href="javascript:void(0);" v-for="campaign in campaigns" :key="campaign.id" @click.prevent="setValue(campaign.id)">
                    <p>{{campaign.name}}</p>
                </a>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import Vue from 'vue';
import { Campaign } from '@/models';

export default Vue.extend({
    props: ['action'],
    data() {
        return {
            currentLocationId: '',
            campaigns: [] as Campaign[],
        }
    },
    computed:{
        campaignName(): string {
            const campaign = lodash.find(this.campaigns, {id: this.action.campaign_id});
            if(campaign) return campaign.name;
            
            return '';
    }
    },
    async created() {
        this.currentLocationId = this.$router.currentRoute.params.location_id;
        this.campaigns = await Campaign.getWithLocationId(this.currentLocationId);
    },
    methods: {
        setValue(value: string) {
            Vue.set(this.action, 'campaign_id', value);
            this.$emit('update:action', this.action);
        }
    },
})
</script>
