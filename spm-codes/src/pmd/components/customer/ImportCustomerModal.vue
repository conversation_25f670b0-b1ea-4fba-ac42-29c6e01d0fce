<template>
  <div
    class="modal fade hl_import-customers"
    id="import-modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header --no-border">
          <div class="modal-header--inner">
            <h5 class="modal-title">
              <i class="icon icon-plus"></i>
              Import {{type | toTitleCase}}
            </h5>
            <button type="button" class="close" data-dismiss="modal" v-if="!processing">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="tab-content" id="hl_import-customers--steps">
          <div
            v-show="currentStep === 1"
            class="fade show"
            id="step1"
            role="tabpanel"
            aria-labelledby="step1-tab"
          >
            <div class="modal-body">
              <div class="modal-body--inner">
                <ol class="hl_import-customers--steps-heading">
                  <li class="active">1</li>
                  <li>2</li>
                  <li>3</li>
                  <li>4</li>
                  <li>5</li>
                </ol>
                <div class="hl_import-customers--heading">
                  <h3>
                    <strong>Step 1:</strong> Upload Document
                  </h3>
                  <p>
                    File must be CSV
                    <!--, XLS, XLSX or ODS-->
                  </p>
                </div>
                <div class="hl_import-customers--steps1">
                  <vue2Dropzone
                    @vdropzone-file-added="onFileChange"
                    :options="dropzoneOptions"
                    :includeStyling="false"
                    id="dropZone"
                    ref="dropZone"
                    :useCustomSlot="true"
                  >
                    <div class="upload-spreadsheet">
                      <i class="icon icon-document-plus"></i>
                      <h4>Upload contacts</h4>
                      <p>
                        File must be CSV
                        <!--, XLS, XLSX or ODS-->
                      </p>
                      <div class="upload-file">
                        <label id="import-file" class="btn btn-success">Select File</label>
                      </div>
                      <span v-show="fileError" class="--red">{{ fileError }}</span>
                      <div id="preview_container" style="display:none;"></div>
                    </div>
                  </vue2Dropzone>
                </div>
              </div>
            </div>
          </div>
          <div
            v-show="currentStep === 2"
            class="fade show"
            id="step2"
            role="tabpanel"
            aria-labelledby="step2-tab"
          >
            <div class="modal-body">
              <div class="modal-body--inner">
                <ol class="hl_import-customers--steps-heading">
                  <li>1</li>
                  <li class="active">2</li>
                  <li>3</li>
                  <li>4</li>
                  <li>5</li>
                </ol>
                <div class="hl_import-customers--heading">
                  <h3>
                    <strong>Step 2:</strong> Map Fields
                  </h3>
                  <p>Map the fields in your spreedsheet to {{companyName}}'s Field</p>
                </div>
                <div class="hl_import-customers--steps2">
                  <div class="hl_imported-file">
                    <h3>Found {{ records.length }} Contacts in:</h3>
                    <div class="the-uploaded-file">
                      <div class="the-file">
                        <i class="icon icon-ok"></i>
                        <span v-if="selectedFile">{{ selectedFile.name }}</span>
                      </div>
                      <div class="upload-diffrent-file">
                        <input
                          type="file"
                          id="upload-different-file"
                          @change="onFileChange"
                          accept=".csv"
                          ref="upload2"
                        />
                        <label for="upload-different-file">Upload a different file</label>
                        <span v-show="fileError" class="--red">{{ fileError }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="hl_imported-file-map">
                    <h3>Map your fields to {{companyName}}'s Field</h3>
                    <div class="card">
                      <div class="table-wrap">
                        <table class="table">
                          <thead>
                            <tr>
                              <th>Document Field</th>
                              <th>{{companyName}} Field</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr v-for="header in headers">
                              <td style="max-width:150px">{{ header }}</td>
                              <td>
                                <select
                                  class="selectpicker selectContactFields"
                                  title="Select Field"
                                  v-model="mappings[header]"
                                  data-size="5"
                                >
                                  <option value>Select Field</option>
                                  <option value="name">Full Name</option>
                                  <option value="firstName">First Name</option>
                                  <option value="lastName">Last Name</option>
                                  <option value="address1">Address</option>
                                  <option value="city">City</option>
                                  <option value="state">State</option>
                                  <option value="postalCode">Postal Code</option>
                                  <option value="country">Country</option>
                                  <option value="email">Email</option>
                                  <option value="phone">Phone</option>
                                  <option value="dnd">DND</option>
                                  <option value="birthDay">Birthday</option>
                                  <option value="tags">Tags</option>
                                  <option value="company_name">Company Name</option>
                                  <option value="website">Website</option>
                                  <option value="type">Type</option>
                                  <option value="notes">Notes</option>
                                  <option value="source">Source</option>
                                  <option value="timezone">Timezone</option>
                                  <option value="assignedTo">Assigned</option>
                                  <option value="contactId">Contact ID</option>
                                  <option
                                    style=" text-transform: capitalize;"
                                    v-for="field in customFields"
                                    :value="field.id"
                                  >{{field.name}} (custom field)</option>
                                </select>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                  <div v-show="mappingError" class="error-block --red">{{ mappingError }}</div>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <div class="modal-footer--inner nav">
                <div class="right-holder">
                  <button type="button" class="btn btn-primary" data-dismiss="modal">Cancel</button>
                  <div class="nav">
                    <a
                      class="btn btn-blue"
                      data-toggle="tab"
                      href="#step3"
                      role="tab"
                      aria-controls="step3"
                      @click.prevent="finishedMapping"
                    >Continue</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            v-show="currentStep === 3"
            class="fade show"
            id="step3"
            role="tabpanel"
            aria-labelledby="step3-tab"
          >
            <div class="modal-body">
              <div class="modal-body--inner">
                <ol class="hl_import-customers--steps-heading">
                  <li>1</li>
                  <li>2</li>
                  <li class="active">3</li>
                  <li>4</li>
                  <li>5</li>
                </ol>
                <div class="hl_import-customers--heading">
                  <h3>
                    <strong>Step 3:</strong> Confirm Mappings
                  </h3>
                  <p>Confirm you've correctly mapped your fields.</p>
                </div>
                <div class="hl_import-customers--steps3">
                  <div class="field-mapping-preview">
                    <h4>Field Mapping Preview</h4>
                    <div class="field-mapping-preview--inner">
                      <ul>
                        <li v-for="(key, value) in mappings">
                          <strong>{{ friendlyName[key] }}</strong>
                          <span>{{ testData(key, value) }}</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <div class="refresh-sample">
                    <a @click.prevent="nextElement">Another sample</a>
                  </div>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <div class="modal-footer--inner nav justify-content-between">
                <button type="button" class="btn btn-link2" @click.prevent="currentStep = 2">Back</button>
                <div class="right-holder">
                  <button type="button" class="btn btn-primary" data-dismiss="modal">Cancel</button>
                  <div class="nav">
                    <a
                      class="btn btn-blue"
                      data-toggle="tab"
                      href="#step3"
                      role="tab"
                      aria-controls="step3"
                      @click.prevent="currentStep = 4"
                    >Continue</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            v-show="currentStep === 4"
            class="fade show"
            id="step4"
            role="tabpanel"
            aria-labelledby="step4-tab"
          >
            <div class="modal-body">
              <div class="modal-body--inner">
                <ol class="hl_import-customers--steps-heading">
                  <li>1</li>
                  <li>2</li>
                  <li>3</li>
                  <li class="active">4</li>
                  <li>5</li>
                </ol>
                <div class="hl_import-customers--heading">
                  <h3>
                    <strong>Step 4:</strong> Select Duplicate Strategy
                  </h3>
                  <p>Choose how you'd like to handle any duplicates.</p>
                </div>
                <div class="hl_import-customers--steps4">
                  <div class="existing-contacts">
                    <h4>If an existing contact is in the spreadsheet:</h4>
                    <div class="radio">
                      <input
                        type="radio"
                        name="existing-contacts"
                        id="existing-contacts1"
                        v-model="conflictResolution"
                        value="updateEmpty"
                      />
                      <label
                        for="existing-contacts1"
                      >Update them with the data in the document, but only for fields that are empty (recommended)</label>
                    </div>
                    <div class="radio">
                      <input
                        type="radio"
                        name="existing-contacts"
                        id="existing-contacts2"
                        v-model="conflictResolution"
                        value="updateAll"
                      />
                      <label
                        for="existing-contacts2"
                      >Update them with the data in the spreadsheet, including fields that already have a value</label>
                    </div>
                    <div class="radio">
                      <input
                        type="radio"
                        name="existing-contacts"
                        id="existing-contacts3"
                        v-model="conflictResolution"
                        value="skip"
                      />
                      <label for="existing-contacts3">Don't modify them</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <div class="modal-footer--inner nav justify-content-between">
                <button type="button" class="btn btn-link2" @click.prevent="currentStep = 3">Back</button>
                <div class="right-holder">
                  <button type="button" class="btn btn-primary" data-dismiss="modal">Cancel</button>
                  <div class="nav">
                    <a
                      class="btn btn-blue"
                      data-toggle="tab"
                      href="#step3"
                      role="tab"
                      aria-controls="step3"
                      @click.prevent="currentStep = 5"
                    >Continue</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            v-show="currentStep === 5"
            class="fade show"
            id="step5"
            role="tabpanel"
            aria-labelledby="step5-tab"
          >
            <div class="modal-body">
              <div class="modal-body--inner">
                <ol class="hl_import-customers--steps-heading" v-if="!processing">
                  <li>1</li>
                  <li>2</li>
                  <li>3</li>
                  <li>4</li>
                  <li class="active">5</li>
                </ol>
                <div class="hl_import-customers--heading" v-if="!processing">
                  <!-- <h3><strong>Step 5:</strong> Add Customers to List</h3>
                  <p>Enter the list(s) to import your customers to:</p>-->
                  <h3>
                    <strong>Step 5:</strong> Start import
                  </h3>
                </div>
                <div class="hl_import-customers--steps5">
                  <div class="add-new-contacts">
                    <div v-if="!processing">
                      <h4>Add Tags</h4>
                      <TagComponent v-model="tags" />
                      <div v-if="mailGunValidationEnabled" style="display: flex; justify-content: space-between;">
                        <h4>Email validation
                          <span
                              style="margin-left: 5px; color: #aaa"
                              class="input-group-addon"
                              v-b-tooltip.hover
                              title="Fires a MailGun validation for all contacts with an e-mail address. May include extra charges from MailGun."
                          >
                              <i class="fas fa-question-circle"></i>
                          </span>
                        </h4>
                        <div class="toggle">
                          <input
                            type="checkbox"
                            class="tgl tgl-light"
                            id="email_validation_enabled"
                            v-model="emailValidation"
                          />
                          <label class="tgl-btn" for="email_validation_enabled"></label>
                        </div>
                      </div>
                    </div>
                    <div class="uploading" v-else>
                      <div class="progress">
                        <div
                          class="progress-bar progress-bar-striped bg-success progress-bar-animated"
                          :style="percent"
                        ></div>
                      </div>
                      <p v-if="uploading">Uploading...</p>
                      <p v-else>{{totalUploaded}}/{{records.length}}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <div class="modal-footer--inner nav justify-content-between">
                <button
                  type="button"
                  class="btn btn-link2"
                  @click.prevent="currentStep = 4"
                  :class="{invisible: processing}"
                >Back</button>
                <div class="right-holder">
                  <div style="display: inline-block;position: relative;">
                    <button
                      :class="{invisible: processing}"
                      type="button"
                      class="btn btn-primary"
                      @click.prevent="save"
                    >Done</button>
                    <div style="position: absolute;top: 21%;left: 33%;">
                      <moon-loader :loading="processing" color="#188bf6" size="30px" />
                    </div>
                  </div>
                  <!-- <button type="button" class="btn btn-blue">Import New Customers</button> -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import * as csv from 'csvtojson'
import { v4 as uuid } from 'uuid';
import libphonenumber from 'google-libphonenumber'
import {
  Contact,
  ImportRequest,
  ImportType,
  ImportStatus,
  CustomField,
  Company,
  CustomDateField,
  Note,
  User,
  Location
} from '@/models'
import { mapState } from 'vuex'
import { CompanyState, UserState } from '../../../store/state_models'
import vue2Dropzone from 'vue2-dropzone'
import { Utils } from '@/util/utils'
import firebase from 'firebase/app';
import { MailGunAccount } from '../../../models';
const TagComponent = () => import('./TagComponent.vue')
var phoneUtil = libphonenumber.PhoneNumberUtil.getInstance()
var PNF = libphonenumber.PhoneNumberFormat
declare var $: any

let unsubscribeImportRequest: () => void;

export default Vue.extend({
  props: ['visible', 'currentLocationId', 'type'],
  components: { vue2Dropzone, TagComponent },
  data() {
    return {
      dropzoneOptions: {
        url: '/file/post',
        clickable: '#import-file',
        previewsContainer: '#preview_container',
        addRemoveLinks: false,
        createImageThumbnails: false
      },
      currentStep: 1,
      processing: false,
      uploading: false,
      uploadingPercent: 0,
      selectedFile: undefined as File | undefined,
      fileError: '',
      mappingError: '',
      headers: [] as string[],
      records: [] as any[],
      mappings: {} as { [key: string]: any },
      testDataIndex: 0,
      friendlyName: {
        firstName: 'First name',
        lastName: 'Last name',
        name: 'Full name',
        address1: 'Address',
        city: 'City',
        state: 'State',
        postalCode: 'Zip code',
        country: 'Country',
        email: 'Email',
        phone: 'Phone',
        birthDay: 'Birthday',
        tags: 'Tags',
        company_name: 'Company Name',
        website: 'Website',
        type: 'Type',
        notes: 'Notes',
        dnd: 'DND',
        source: 'Source',
        timezone: 'Timezone',
        assignedTo: 'Assigned',
        contactId: 'Contact ID',
      },
      conflictResolution: 'updateEmpty',
      totalUploaded: 0,
      tags: [] as string[],
      customFields: [] as CustomField[],
      emailValidation: false,
      mailGunValidationEnabled: false,
      location: undefined as Location | undefined,
    }
  },
  async created() {
    this.location = new Location(await this.$store.dispatch('locations/getById', this.currentLocationId));
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      }
    }),
    highlevelProperties(): string[] {
      return lodash.values(this.mappings)
    },
    percent(): { [key: string]: any } {
      return { width: (this.uploadingPercent || (this.totalUploaded * 100 / this.records.length)) + '%' }
    },
    companyName(): string {
      return this.company.name ? this.company.name : 'Internal'
    }
  },
  methods: {
    testData(key: string, value: string): string {
      if (this.records.length === 0) return ''

      let data = this.records[this.testDataIndex][value]

      if (key == 'phone' && data) {
        data = data.split(/[;,|/\\]/)[0]
        try {
            let raw = phoneUtil.parse(data, this.location.country || 'US')
            if (!phoneUtil.isValidNumber(raw)) data = ''
            else data = phoneUtil.format(raw, PNF.E164);
          } catch (error) {
             data = ''
          }
      }

      if (key == 'email' && data) {
        data = data.split(/[;,|/\\]/)[0]
        const emailRegex = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        if (!emailRegex.test(String(data).toLowerCase())) data = ''
      }

      return data
    },
    randomIntFromInterval(min: number, max: number) {
      return Math.floor(Math.random() * (max - min + 1) + min)
    },
    nextElement() {
      if (this.records.length > 20) {
        this.testDataIndex = this.randomIntFromInterval(0, this.records.length)
      } else {
        if (
          this.records.length > 0 &&
          this.testDataIndex < this.records.length - 1
        ) {
          this.testDataIndex++
        } else {
          this.testDataIndex = 0
        }
      }
    },
    finishedMapping() {
      this.mappingError = ''
      const mappings = lodash.pickBy(this.mappings, lodash.identity)
      const nameAvailable = lodash.some(lodash.values(mappings), value => {
        return ['firstName', 'lastName', 'name', 'company_name', 'phone', 'email'].indexOf(value) !== -1
      })
      if (!nameAvailable) {
        this.mappingError = 'Atleast one name field (first, last, full or company name) or phone or email should be mapped.'
        return
      }

      this.currentStep = 3
    },
    async save() {
      this.processing = true
      try {
        const authUser = await this.$store.dispatch('auth/get')

        const request = new ImportRequest()
        request.userId = authUser.userId
        request.locationId = this.currentLocationId
        request.status = ImportStatus.UPLOADING
        request.type = ImportType.BULK_IMPORT
        request.filePath = `location/${this.currentLocationId}/contact-import/${uuid()}`;

        this.uploading = true;

        // compress records and mappings
        const keys = Object.keys(this.mappings);
        const indexes = keys.reduce((acc, cur, i) => { acc[cur] = i; return acc; }, {});
        const mappings = keys.reduce((acc, cur, i) => { acc[this.mappings[cur]] = indexes[cur]; return acc; }, {});
        const records = this.records.map(record => {
          const row = [];
          Object.keys(record).forEach(key => {
            row[indexes[key]] = record[key];
          });
          return row;
        });

        // store data in firebase storage
        const data = { records, mappings };
        const blob = new Blob([JSON.stringify(data)], { type : 'application/json' });
        const uploadPath = firebase.storage().ref(request.filePath);
        const uploadTask = uploadPath.put(blob, {
          contentType: 'application/json',
          customMetadata: { importRequestId: request.id }
        });
        // show uploading percentage
        await (new Promise((resolve, reject) => {
          uploadTask.on('state_changed', (snapshot) => {
            this.uploadingPercent = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          }, reject, resolve)
        }));

        await request.save();

        if (unsubscribeImportRequest) unsubscribeImportRequest();
        unsubscribeImportRequest = await ImportRequest.getByIdRealTime(request.id).onSnapshot(snapshot => {
          const data = snapshot.data();
          this.uploading = data.status === ImportStatus.UPLOADING;
          this.uploadingPercent = data.status === ImportStatus.PENDING ? 0 : this.uploadingPercent;
          this.totalUploaded = (data.created || 0) + (data.updated || 0) + (data.skipped || 0);
          if (data.status === ImportStatus.SUCCESS) {
            if (unsubscribeImportRequest) unsubscribeImportRequest();
            this.processing = false
            this.$emit('on_import_request', snapshot.id)
            this.$emit('hidden', true)
          }
        });

        await Contact.bulkImport({
          tags: this.tags,
          conflictResolution: this.conflictResolution,
          locationId: this.currentLocationId,
          importRequestId: request.id,
          emailValidation: this.emailValidation
        });
      } catch (e) {
        console.error(e);
      }
    },
    uniq(a: string[]) {
      var seen: { [key: string]: any } = {}
      return a.filter(function(item) {
        return seen.hasOwnProperty(item) ? false : (seen[item] = true)
      })
    },
    async onFileChange(e: any) {

      let file
      if (e instanceof Event) {
        const element = <HTMLInputElement>e.target
        if (!element.files) return
        file = element.files[0]
      } else {
        file = e
      }
      const dropZone: any = this.$refs.dropZone
      dropZone.removeAllFiles()
      this.fileError = ''
      this.mappings = {}
      this.mappingError = ''
      // if (file.type !== 'text/csv') { this.fileError = "Select a CSV file, we do not support other formats at this time."; return; }
      this.selectedFile = file

      await this.fetchCustomFields();

      var reader = new FileReader()
      reader.addEventListener('load', async (event: any) => {
        const content = event.target.result
        this.records = await csv({
          trim: true,
          ignoreEmpty: true,
		  flatKeys: true
        })
          .on('error', err => {
            console.log(err)
          })
          .on('header', (headers: string[]) => {
            const filteredHeaders = headers.filter(header => header)
            const mappings = {} as { [key: string]: any }
            filteredHeaders.forEach(header => {
              switch (header.toLowerCase()) {
                case 'contact id':
                  mappings[header] = 'contactId';
                  break;
                case 'first name':
                case 'first_name':
                  mappings[header] = 'firstName'
                  break
                case 'last name':
                case 'last_name':
                  mappings[header] = 'lastName'
                  break
                case 'name':
                case 'full name':
                case 'full_name':
                  mappings[header] = 'name'
                  break
                case 'address':
                  mappings[header] = 'address1'
                  break
                case 'city':
                  mappings[header] = 'city'
                  break
                case 'state':
                  mappings[header] = 'state'
                  break
                case 'zip code':
                case 'zip_code':
                case 'zip':
                case 'postal code':
                case 'postal_code':
                  mappings[header] = 'postalCode'
                  break
                case 'country':
                  mappings[header] = 'country'
                  break
                case 'email':
                  mappings[header] = 'email'
                  break
                case 'phone':
                case 'mobile_phone':
                case 'mobile phone':
                case 'phone_number':
                case 'phone number':
                case 'cell phone':
                case 'cell_phone':
                  mappings[header] = 'phone'
                  break
                case 'birthday':
                case 'birth day':
                case 'birth_day':
                case 'date of birth':
                case 'dateofbirth':
                case 'date_of_birth':
                case 'dob':
                  mappings[header] = 'birthDay'
                  break
                case 'tag':
                case 'tags':
                  mappings[header] = 'tags'
                  break
                case 'company':
                case 'company name':
                case 'company_name':
                case 'business':
                case 'business name':
                case 'business_name':
                  mappings[header] = 'company_name'
                  break
                case 'website':
                case 'website_name':
                case 'website name':
                  mappings[header] = 'website'
                  break
                case 'type':
                  mappings[header] = 'type'
                  break
                case 'note':
                case 'notes':
                  mappings[header] = 'notes'
                  break
               case 'dnd':
                  mappings[header] = 'dnd'
                  break
                case 'source':
                  mappings[header] = 'source'
                  break
                case 'timezone':
                  mappings[header] = 'timezone'
                  break
                case 'assigned':
                case 'assigned to':
                case 'assigned_to':
                case 'assignedto':
                  mappings[header] = 'assignedTo'
                  break
              }
            })
            if (this.customFields){
              this.customFields.forEach(c => {
                const idx = filteredHeaders.findIndex(a=> a && c && Utils.safeLC(c.name) === Utils.safeLC(a));
                if (idx != -1) mappings[filteredHeaders[idx]] = c.id;
              })
            }
            this.mappings = mappings
            this.headers = filteredHeaders
            this.refreshSelectPicker()
          })
          .fromString(content)
      })
      reader.readAsText(file)

      if (this.currentStep === 1) this.currentStep = 2
    },
    showHideModal() {
      if (this.visible) {
        var _self = this
        $(this.$refs.modal)
          .modal('show')
          .on('hidden.bs.modal', function() {
            _self.$emit('hidden')
          })
      } else {
        $(this.$refs.modal)
          .modal('hide')
          .off('hidden.bs.modal')
      }
    },
    refreshSelectPicker() {
      const $selectpicker = $(this.$el).find('.selectpicker')
      if ($selectpicker) {
        $selectpicker.selectpicker('refresh')
      }
    },
    async fetchCustomFields() {
      this.customFields = (await CustomField.getByLocationId(
        this.currentLocationId
      ).get()).docs.map(d => new CustomField(d))
    }
  },
  watch: {
    visible(visible: boolean) {
      this.showHideModal()
      if (!visible) {
        const upload2 = <HTMLInputElement>this.$refs.upload2
        upload2.value = ''
        const data: () => object = <(() => object)>this.$options.data
        if (data) Object.assign(this.$data, data.apply(this))
      }
    },
    customFields(value) {
      if (value.length > 0) {
        value.forEach(v => {
          Vue.set(this.friendlyName, v.id, v.name)
        })
      }
    }
  },
  updated() {
    this.refreshSelectPicker()
  },
  async mounted() {
    this.showHideModal()
    const mailGunAccount = await MailGunAccount.getByLocationId(this.$route.params.location_id)
    this.mailGunValidationEnabled = Boolean(mailGunAccount && mailGunAccount.validateEmails)
  },
  beforeDestroy() {
    if (unsubscribeImportRequest) unsubscribeImportRequest();
  }
})
</script>

<style scoped>
.right-holder {
  display: inherit;
}
.right-holder > :not(:first-child) {
  margin-left: 0.25rem;
}
.right-holder > :not(:last-child) {
  margin-right: 0.25rem;
}
.upload-diffrent-file label {
  cursor: pointer;
}
.upload-diffrent-file span {
  display: block;
}
.hl_import-customers--steps2 .error-block {
  text-align: center;
}
.refresh-sample {
  text-align: right;
  cursor: pointer;
  color: #188bf6;
  text-decoration: underline;
}
.uploading p {
  text-align: center;
}
</style>

<style lang="css">
.selectContactFields > .dropdown-menu .inner .dropdown-menu li a{
  white-space: normal !important;
}
</style>
