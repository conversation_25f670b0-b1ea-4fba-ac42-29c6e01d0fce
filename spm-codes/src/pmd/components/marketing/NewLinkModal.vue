 <template>
  <div class="modal fade hl_sms-template--modal" tabindex="-1" role="dialog" ref="modal">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Add Trigger Link</h2>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div class="form-group">
              <UITextInputGroup
                class="msgsndr2"
                type="text"
                v-model="linkName"
                name="msgsndr2"
                data-lpignore="true"
                label="Name"
                autocomplete="msgsndr2"
                data-vv-as="link name"
                v-validate="'required'"
                :error="errors.has('msgsndr2')"
                :errorMsg="errors.first('msgsndr2')"
              />
            </div>
            <div class="form-group">
              <UITextInputGroup
                type="text"
                name="redirectURL"
                label="Link URL"
                data-vv-as="redirect URL"
                v-model="redirectURL"
                v-validate="'required'"
                :error="errors.has('redirectURL')"
                :errorMsg="errors.first('redirectURL')"
              />
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner nav">
            <UIButton type="button" use="outline" data-dismiss="modal">Cancel</UIButton>
            <div style="display: inline-block;position: relative;">
              <UIButton
                type="button"
                @click.prevent="save"
                :loading="saving"
                use="primary"
              >Save</UIButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Link } from '@/models'

declare var $: any

export default Vue.extend({
  props: ['values'],
  data() {
    return {
      linkName: '',
      redirectURL: '',
      link: undefined as Link | undefined,
      saving: false
    }
  },
  methods: {
    async save() {
      if (!this.link) return
      await this.$validator.validateAll()
      if (this.errors.any()) {
        return
      }
      this.saving = true
      this.link.name = this.linkName
      this.link.redirectTo = this.redirectURL
      await this.link.save()
      this.$emit('updated');
      this.saving = false
      $(this.$refs.modal).modal('hide')
    }
  },
  watch: {
    async values() {
      if (this.values.visible) {
        $(this.$refs.modal).modal('show')
        if (!this.values.selectedLinkId) {
          this.link = new Link()
          this.link.locationId = this.values.currentLocationId
          this.errors.clear()
        } else {
          this.link = await Link.getById(this.values.selectedLinkId)
          this.linkName = this.link.name
          this.redirectURL = this.link.redirectTo
        }
      } else {
        const data: (() => object) = <(() => object)>this.$options.data;
			  if (data) Object.assign(this.$data, data.apply(this));
        this.errors.clear()
      }
    }
  },
  mounted() {
    $(this.$refs.modal).on('hidden.bs.modal', () => {
      this.$emit('hidden')
    })

    if (this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  }
})
</script>


