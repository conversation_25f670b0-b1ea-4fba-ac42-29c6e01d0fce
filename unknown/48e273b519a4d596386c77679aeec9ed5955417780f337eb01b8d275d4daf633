<template>
  <div
    id="review-sms-communication"
    class="hl_settings--body hl_settings--customize-communication"
  >
    <div class="container">
      <div class="row">
        <div class="card">
          <div class="card-header --no-right-padding card-header--withbutton">
  <!--           <div class="card-control-right">
              <div class="toggle">
                <input
                  type="checkbox"
                  class="tgl tgl-light"
                  id="tgl2"
                  v-model="smsSettings.enabled"
                />
                <label class="tgl-btn" for="tgl2"></label>
              </div>
            </div> -->
            <h3>Automatic SMS review</h3>
          </div>
          <div class="card-body">
            <div class="form-group">
              <p>
                Automatic “Review Request” sms are sent directly to all your
                customers. Below you can customize when they are sent out.
              </p>
            </div>
            <div class="form-group">
              <label>When to send email after check-in?</label>
              <div class="box-selection row">
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      name="when-send-sms"
                      value="0"
                      id="sms_check_in_0"
                      v-model="smsSettings.after_check_in_hours"
                    />
                    <label for="sms_check_in_0">Immediately</label>
                  </div>
                </div>
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      name="when-send-sms"
                      value="1"
                      id="sms_check_in_1"
                      v-model="smsSettings.after_check_in_hours"
                    />
                    <label for="sms_check_in_1">1 Hour</label>
                  </div>
                </div>
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      value="2"
                      name="when-send-sms"
                      id="sms_check_in_2"
                      v-model="smsSettings.after_check_in_hours"
                    />
                    <label for="sms_check_in_2">2 Hours</label>
                  </div>
                </div>
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      value="4"
                      name="when-send-sms"
                      id="sms_check_in_4"
                      v-model="smsSettings.after_check_in_hours"
                    />
                    <label for="sms_check_in_4">4 Hours</label>
                  </div>
                </div>
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      value="24"
                      name="when-send-sms"
                      id="sms_check_in_24"
                      v-model="smsSettings.after_check_in_hours"
                    />
                    <label for="sms_check_in_24">1 Day</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label>Repeat this every</label>
              <div class="box-selection row">
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      name="repeat-send-sms"
                      id="sms_repeat_none"
                      value=""
                      v-model="smsSettings.repeat_every_days"
                    />
                    <label for="sms_repeat_none">Don't Repeat</label>
                  </div>
                </div>
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      name="repeat-send-sms"
                      id="sms_repeat_30"
                      value="30"
                      v-model="smsSettings.repeat_every_days"
                    />
                    <label for="sms_repeat_30">Month</label>
                  </div>
                </div>
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      name="repeat-send-sms"
                      id="sms_repeat_180"
                      value="180"
                      v-model="smsSettings.repeat_every_days"
                    />
                    <label for="sms_repeat_180">6 Month</label>
                  </div>
                </div>
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      name="repeat-send-sms"
                      id="sms_repeat_365"
                      value="365"
                      v-model="smsSettings.repeat_every_days"
                    />
                    <label for="sms_repeat_365">Year</label>
                  </div>
                </div>
              </div>
            </div>
      

            <div class="save-button-holder">
              <button
                type="button"
                class="btn btn-success"
                @click.stop="validateBeforeSubmit"
              >
                <i class="icon" :class="saving ? 'icon-clock' : 'icon-ok'"></i> {{saveButtonText}} 
              </button>
            </div>
            <!-- <div class="option">
                                <input type="checkbox" id="send-weekly-sms" checked>
                                <label for="send-weekly-sms">Send me a weekly report of all review requests?</label>
							</div>-->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang='ts'>
import Vue from 'vue'
import { Location } from '@/models'
import libphonenumber from 'google-libphonenumber'

var phoneUtil = libphonenumber.PhoneNumberUtil.getInstance()
var PNF = libphonenumber.PhoneNumberFormat
declare var $: any

export default Vue.extend({
  data() {
    return {
      smsSettings: {} as { [key: string]: any },
      currentLocationId: '',
      location: undefined as Location | undefined,
      saving : false
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.fetchData()
    },
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.location = new Location(
        await this.$store.dispatch('locations/getById', this.currentLocationId)
      )
      this.smsSettings = this.location.settings.sms;
    },
    async validateBeforeSubmit() {
      if (!this.location) return
      this.saving = true;
      await this.location.save();
      this.saving = false;
    },
  },
  computed:{
      saveButtonText(): string {
      return this.saving ? 'Saving' : 'Save'
    },
  }
})
</script>
<style lang="scss" scoped>
#review-sms-communication {
  .container {
    left: 15px;
    right: 15px;
    max-width: unset;
  }
  .card {
    width: 100%;
    .card-header--withbutton {
      display: grid;
      grid-template-columns: auto 1fr;
      grid-gap: 1rem;
    }
    .card-body {
      margin: unset;
      max-width: unset;
      .save-button-holder {
        padding: 25px 0 0 0;
        button {
          width: 200px;
        }
      }
    }
  }
}
</style>

