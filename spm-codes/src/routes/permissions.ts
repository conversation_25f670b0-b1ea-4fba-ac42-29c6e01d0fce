const routesRequiredPermissions = {
    template_settings: 'settings_enabled',
    company_settings: 'settings_enabled',
    location_team_management: 'settings_enabled',
    location_teams_management: 'settings_enabled',
    email_settings: 'settings_enabled',
    chat_widget_settings: 'settings_enabled',
    sms_settings: 'settings_enabled',
    customize_communications: 'settings_enabled',
    review_widgets: 'settings_enabled',
    appointment_widget: 'settings_enabled',
    pipeline_settings: 'settings_enabled',
    payment_settings: 'settings_enabled',
    phone_number: 'settings_enabled',
    custom_fields_settings: 'settings_enabled',
    facebook_settings: 'settings_enabled',
    custom_values_settings: 'settings_enabled',
    conversations: 'conversations_enabled',
    triggers: 'triggers_enabled',
    triggers_detail: 'triggers_enabled',
    customer_acquisition: 'campaigns_enabled',
    campaign_edit: 'campaigns_enabled',
    campaign_detail: 'campaigns_enabled',
    campaign_accounts_list: 'campaigns_enabled',
    campaign_activity: 'campaigns_enabled',
    workflows: 'workflows_enabled',
    newWorkflow: 'workflows_enabled',
    editWorkflow: 'workflows_enabled',
    bulk_requests: 'bulk_requests_enabled',
    email_builder_dashboard: 'marketing_enabled',
    email_builder_list: 'marketing_enabled',
    text_templates: 'marketing_enabled',
    form_builder_manage: 'marketing_enabled',
    form_builder_integrate: 'marketing_enabled',
    jotbox_form: 'marketing_enabled',
    jotbox_survey: 'marketing_enabled',
    appointments: 'appointments_enabled',
    'calendar-v2': 'appointments_enabled',
    reviews: 'reviews_enabled',
    reviews_requests: 'reviews_enabled',
    customers: 'contacts_enabled',
    leads: 'contacts_enabled',
    contact_detail: 'contacts_enabled',
    contacts: 'contacts_enabled',
    manual_actions: 'contacts_enabled',
    smart_list: 'contacts_enabled',
    customer_imports: 'contacts_enabled',
    analysis: 'online_listings_enabled',
    phone_calls: 'phone_call_enabled',
    opportunities: 'opportunities_enabled',
    pipeline: 'opportunities_enabled',
    funnel_main: 'funnels_enabled',
    all_funnels: 'funnels_enabled',
    fav_funnels: 'funnels_enabled',
    recent_funnels: 'funnels_enabled',
    archived_funnels: 'funnels_enabled',
    funnels_detail: 'funnels_enabled',
    funnel_steps: 'funnels_enabled',
    funnel_step_overview: 'funnels_enabled',
    funnel_step_automation: 'funnels_enabled',
    funnel_step_publish: 'funnels_enabled',
    funnel_step_product: 'funnels_enabled',
    funnel_stats: 'funnels_enabled',
    funnel_sales: 'funnels_enabled',
    funnel_settings: 'funnels_enabled',
    funnel_templates_category: 'funnels_enabled',
    websites_funnels: 'websites_enabled',
    websites_detail: 'websites_enabled',
    website_pages: 'websites_enabled',
    website_stats: 'websites_enabled',
    website_settings: 'websites_enabled',
    website_templates_category: 'websites_enabled',
    website_templates_category_detail: 'websites_enabled',
    website_templates_detail: 'websites_enabled',
    tag: 'tags_enabled',
    smtp_service: 'settings_enabled',
    membership: 'membership_enabled',
    'form-builder-v2': 'marketing_enabled',
    'form-builder-main-v2': 'marketing_enabled',
    'form-builder-analyze-v2': 'marketing_enabled',
    'form-builder-submissions-v2': 'marketing_enabled',
    'form-builder-manage': 'marketing_enabled',
    'jotbox_form-v2': 'marketing_enabled',
    'survey-builder-v2': 'marketing_enabled',
    'survey-builder-main-v2': 'marketing_enabled',
    'survey-builder-analyze-v2': 'marketing_enabled',
    'survey-builder-submissions-v2': 'marketing_enabled',
    'chat-widget-settings-v2': 'settings_enabled',
    'calendar-settings-v2': 'settings_enabled',
    'smart-list-settings-v2': 'settings_enabled',
    'pipeline-settings-v2': 'settings_enabled',
    'pipeline-opportunities-v2': 'opportunities_enabled',
    'company-settings-v2': 'settings_enabled',
    'location_billing-v2': 'settings_enabled',
    'team-v2': 'settings_enabled',
    'teams-v2': 'settings_enabled',
    'staff': 'settings_enabled',
    'eliza-faq-v2': 'settings_enabled',
    'settings-reputation-v2': 'settings_enabled',
    'phone-number-v2': 'settings_enabled',
    'custom-fields-v2': 'settings_enabled',
    'custom-fields-settings-v2': 'settings_enabled',
    'tags-settings-v2': 'tags_enabled',
    'custom-values-settings-v2': 'settings_enabled',
    'domain-settings-v2': 'settings_enabled',
    'smtp-service-v2': 'settings_enabled',
    'settings-v2': 'settings_enabled',
    'product-list-v2': 'settings_enabled',
    'transactions-v2': 'settings_enabled',
    'payment-integrations-v2': 'settings_enabled',
    'payments-v2': 'settings_enabled',
    'triggers-links-link-v2': 'trigger_links_enabled',
    'triggers-links-analyze-v2': 'trigger_links_enabled',
    'triggers_detail-v2': 'triggers_enabled',
    'workflows-v2': 'workflows_enabled',
    'newWorkflows-v2': 'workflows_enabled',
    'campaign_edit-v2': 'campaigns_enabled',
    'campaigns-v2': 'campaigns_enabled',
    'opportunities-list-v2': 'opportunities_enabled',
    'opportunities-v2': 'opportunities_enabled',
    'conversations-id-v2': 'conversations_enabled',
    'manual-actions-v2': 'contacts_enabled',
    'conversations-v2': 'conversations_enabled',
    'smart-list-v2': 'contacts_enabled',
    'contact_detail-v2': 'contacts_enabled',
    'bulk-imports-log-v2': 'contacts_enabled',
    'bulk-actions-log-v2': 'contacts_enabled',
    'contact_restore-v2': 'contacts_enabled',
    'contacts-contacts-v2': 'contacts_enabled',
    'leads-v2': 'contacts_enabled',
    'contacts-v2': 'contacts_enabled',
    'calendars-v2': 'appointments_enabled',
    'calendars-page-v2': 'appointments_enabled',
    'appointments-v2': 'appointments_enabled',
    'call-stats-v2': 'phone_call_enabled',
    'funnels-v2': 'funnels_enabled',
    'funnels-page-v2': 'funnels_enabled',
    'fav_funnels-v2': 'funnels_enabled',
    'recent_funnels-v2': 'funnels_enabled',
    'archived_funnels-v2' : 'funnels_enabled',
    'funnels-detail-v2': 'funnels_enabled',
    'funnel-steps-v2': 'funnels_enabled',
    'funnel-step-overview-v2': 'funnels_enabled',
    'funnel-step-automation-v2': 'funnels_enabled',
    'funnel-step-publish-v2': 'funnels_enabled',
    'funnel-step-product-v2': 'funnels_enabled',
    'funnel-step-products-v2': 'funnels_enabled',
    'funnel-step-product-v2-create': 'funnels_enabled',
    'funnel-step-product-v2-edit': 'funnels_enabled',
    'funnel-stats-v2': 'funnels_enabled',
    'funnel-sales-v2': 'funnels_enabled',
    'funnel-settings-v2': 'funnels_enabled',
    'funnel-templates-category': 'funnels_enabled',
    'funnel-templates-category-detail': 'funnels_enabled',
    'funnel-templates-detail': 'funnels_enabled',
    'websites-funnels-v2': 'websites_enabled',
    'websites-detail-v2': 'websites_enabled',
    'website-pages-v2': 'websites_enabled',
    'website-stats-v2': 'websites_enabled',
    'website-sales-v2': 'websites_enabled',
    'website-settings-v2': 'websites_enabled',
    'website-products-v2': 'websites_enabled',
    'website-step-products-v2': 'websites_enabled',
    'website-templates-category': 'websites_enabled',
    'website-templates-category-detail': 'websites_enabled',
    'website-templates-detail': 'websites_enabled',
    'emails_home-v2': 'marketing_enabled',
    'emails_home-detail-v2': 'marketing_enabled',
    'marketing-v2': 'marketing_enabled',
    'templates-v2': 'marketing_enabled',
    'conversations-templates-v2': 'marketing_enabled',
    'email_builder_dashboard-v2': 'marketing_enabled',
    'email_builder_list-v2': 'marketing_enabled',
  }
  
  const locationLevelRoutesPermissions = {
    dashboard: 'dashboard_stats_enabled',
    'dashboard-v2': 'dashboard_stats_enabled',
    email_builder_dashboard: 'html_builder_enabled',
    email_builder_list: 'html_builder_enabled',
    text_templates: 'sms_email_templates_enabled',
    'templates-v2': 'sms_email_templates_enabled',
    'conversations-templates-v2': 'sms_email_templates_enabled',
    form_builder: 'forms_enabled',
    form_builder_manage: 'forms_enabled',
    form_builder_integrate: 'forms_enabled',
    template_settings: 'sms_email_templates_enabled',
    appointment_widget: 'appointments_enabled',
    location_team_management: 'appointments_enabled',
    location_teams_management: 'appointments_enabled',
    calendar_settings: 'appointments_enabled',
    'calendars-v2': 'appointments_enabled',
    'calendars-page-v2': 'appointments_enabled',
    'appointments-v2': 'appointments_enabled',
    domain_settings: ['funnels_enabled', 'websites_enabled'], // show domains for anyone of the permissions,
    'funnels-page-v2': 'funnels_enabled',
    'websites-funnels-v2': 'websites_enabled',
    emails_home: 'email_builder_enabled',
    list_survey: 'surveys_enabled',
    survey_manage: 'surveys_enabled',
    survey_integrate: 'surveys_enabled',
    reporting_adwords: 'adwords_reporting_enabled',
    reporting_facebook: 'facebook_ads_reporting_enabled',
    reporting_attribution: 'attributions_reporting_enabled',
    'appointments-reports': 'appointments_enabled',
    'appointments-reports-v2': 'appointments_enabled',
    call_stats: 'phone_call_enabled',
    agent_reporting: 'agent_reporting_enabled',
    gmb: 'gmb_messaging_enabled',
    'emails_home-v2': 'email_builder_enabled',
    'emails_home-detail-v2': 'email_builder_enabled',
    'marketing-v2': 'email_builder_enabled',
    'reporting-adwords-campaigns-v2': 'adwords_reporting_enabled',
    'reporting-adwords-adgroups-v2': 'adwords_reporting_enabled',
    'reporting-adwords-ads-v2': 'adwords_reporting_enabled',
    'reporting-adwords-keywords-v2': 'adwords_reporting_enabled',
    'reporting-adwords-conversions-v2': 'adwords_reporting_enabled',
    'reporting-google-v2': 'adwords_reporting_enabled',
    'reporting-facebook-campaigns-v2': 'facebook_ads_reporting_enabled',
    'reporting-facebook-adsets-v2': 'facebook_ads_reporting_enabled',
    'reporting-facebook-ads-v2': 'facebook_ads_reporting_enabled',
    'reporting-facebook-demographics-v2': 'facebook_ads_reporting_enabled',
    'reporting-facebook-v2': 'facebook_ads_reporting_enabled',
    'reporting-attribution-v2': 'attributions_reporting_enabled',
    'call-stats-v2': 'phone_call_enabled',
    'agent-reporting-v2': 'adwords_reporting_enabled',
    'email_builder_dashboard-v2': 'html_builder_enabled',
    'email_builder_list-v2': 'html_builder_enabled',
}

const paymentBetaAccessRoutes = [
  'product-list',
  'transactions'
]

export { routesRequiredPermissions, locationLevelRoutesPermissions, paymentBetaAccessRoutes }