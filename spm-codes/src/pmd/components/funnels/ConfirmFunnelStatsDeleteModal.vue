<template>
  <div
    class="modal fade"
    ref="modal"
    id="funnel-stats-reset-modal"
    tabindex="-1"
    role="dialog"
    aria-labelledby="client-checkin--modalLabel"
    aria-hidden="true"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h5 class="modal-title" id="funnel-stats-reset-modalLabel">
              <i class="fas fa-exclamation-triangle"></i>&nbsp;Reset Stats?
            </h5>
            <button type="button" class="close" aria-label="Close" @click="closeModal">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <span>Warning! This can't be undone.</span>
          <p style="color: rgba(0, 0, 0, 0.54); font-size: 13px;">
            <span style="color:#ff5250">Note:</span> Recently generated stats (Around 30 min) won’t get deleted.
          </p>
          <div class="modal-body--inner">
            <div class="modal-buttons modal-footer--inner">
              <button
                type="button"
                class="btn btn-red-lt"
                @click="deleteFunnel"
              >{{loader?'Deleting..': 'Yes, Delete' }}</button>
              <button type="button" class="btn btn-primary" @click="closeModal">Cancel, Keep</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    loader: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    showModal(status: Boolean) {
      var _self = this
      if (status) {
        $(this.$refs.modal)
          .modal('show')
          .on('hidden.bs.modal', function() {
            _self.$emit('hidden', true)
          })
      } else {
        $(this.$refs.modal).modal('hide')
      }
    }
  },
  methods: {
    deleteFunnel() {
      this.$emit('delete', true)
    },
    closeModal() {
      this.$emit('showDeleteModal', false)
      $(this.$refs.modal).modal('hide')
    }
  }
})
</script>
<style scoped>
#funnel-stats-reset-modalLabel {
  color: #ff5250;
}
.modal .modal-dialog {
  max-width: 500px;
}
</style>
