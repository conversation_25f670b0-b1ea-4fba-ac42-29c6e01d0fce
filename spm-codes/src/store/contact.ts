import Vue from 'vue'
import { <PERSON><PERSON><PERSON>, ActionContext } from 'vuex'
import { RootState, ContactsState } from '../store/state_models'

export const contactsPage: Module<ContactsState, RootState> = {
  namespaced: true,
  state: { contacts: {} },
  getters: {
    getByLocationId: (
      state: ContactsState,
      getters: any,
      rootState: RootState,
      rootGetters: any
    ) => {
      return (locationId: string) => state.contacts[locationId]
    }
  },
  mutations: {
    addUpdate: (state: ContactsState, payload: { [key: string]: any }) => {
      Vue.set(state.contacts, payload.locationId, payload)
    },
    clearAll: (state: ContactsState) => {
      Vue.set(state, 'contacts', {})
    }
  },
  actions: {
    logout: {
      root: true,
      handler: async (context: ActionContext<ContactsState, RootState>) => {
        context.commit('clearAll')
      }
    },
    clearAll: {
      handler: async (context: ActionContext<ContactsState, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
