<template>
  <modal
    v-if="modalShow"
    @close="closeModal"
    :maxWidth="modalWidth"
    :showCloseIcon="true"
    style="font-family: 'Helvetica'"
    :noBackdropClose="true"
  >
    <div v-if="modalType == 'send-instructions'" class="chat-instractions">
      <form
        data-vv-scope="form-widgetcode"
        name="form-widgetcode"
        style="width: 100%"
      >
        <h3>Send Instructions</h3>
        <p>
          Who should we send instructions to? We'll email them your webchat code
          and instructions.
        </p>
        <div class="chat-send-instruction-container">
          <label>
            <div class="label-container">
              <span>Web Admin Email</span>
            </div>
            <input
              placeholder="<EMAIL>"
              type="email"
              v-model="email"
              v-validate="{
                required: true,
                email: true,
              }"
              data-lpignore="true"
              autocomplete="email"
              data-vv-as="Email"
              name="email"
              @focus="requestSend = false"
            />
            <span v-show="errors.has('form-widgetcode.email')" class="error">{{
              errors.first('form-widgetcode.email')
            }}</span>
          </label>
          <p class="include-heading">Included Items</p>
          <p class="include-items">
            <svg
              viewBox="0 0 24 24"
              stroke="none"
              width="20px"
              height="20px"
              focusable="false"
              color="#e66a5c"
              style="
                stroke: currentcolor;
                fill: currentcolor;
                stroke-width: 1.5;
              "
            >
              <path
                d="M7.625 16.374l8.75-8.75m-7.929 4.394A3.73 3.73 0 005.598 13.1l-1.875 1.875a3.75 3.75 0 005.304 5.303h0l1.875-1.875a3.73 3.73 0 001.083-2.847m3.569-3.576a3.73 3.73 0 002.848-1.083l1.875-1.875a3.75 3.75 0 00-5.304-5.304l-1.875 1.875a3.731 3.731 0 00-1.079 2.848"
                fill="none"
                fill-rule="evenodd"
                stroke-linecap="round"
                stroke-linejoin="round"
              ></path>
            </svg>
            <span class="include-items-text"
              ><strong>Link</strong> to webchat Install Guide</span
            >
          </p>
          <p class="include-items">
            <svg
              viewBox="0 0 24 24"
              stroke="none"
              width="20px"
              height="20px"
              focusable="false"
              color="#4C76E0"
              style="
                stroke: currentcolor;
                fill: currentcolor;
                stroke-width: 1.5;
              "
            >
              <g
                fill-rule="evenodd"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <path
                  d="M20.75 20.128c0 .69-.56 1.25-1.25 1.25h-15c-.69 0-1.25-.56-1.25-1.25V3.878c0-.69.56-1.25 1.25-1.25h12.538c.326 0 .64.127.873.355l2.462 2.402c.241.235.377.558.377.895v13.848h0z"
                  fill="none"
                ></path>
                <path
                  d="M10.75 8.877h-.625a1.174 1.174 0 00-1.25 1.079v1.039c-.01.378-.223.72-.557.897L7 12.65l1.318.758c.334.176.546.519.557.896v1.04c.05.641.608 1.122 1.25 1.078h.625m2.5-7.545h.625c.642-.044 1.2.437 1.25 1.079v1.039c.01.378.223.72.557.897L17 12.65l-1.318.758a1.047 1.047 0 00-.557.896v1.04a1.173 1.173 0 01-1.25 1.078h-.625"
                  fill="none"
                ></path>
              </g>
            </svg>
            <span class="include-items-text"
              >Your unique <strong>Widget Code/ API key</strong></span
            >
          </p>
        </div>
      </form>
    </div>
    <ChatWidgetStep
      v-else-if="modalType == 'steps'"
      :cms="cms"
      :instruction="cmsInstruction"
      :currentStep="currentStep"
      :screenshot="websiteScreenshot"
      :chatIntegrated="chatIntegrated"
      :widgetSettings="widgetSettings"
    />
    <SelectCmsModal v-else :cms="cms" />
    <div class="chat-btn-grp">
      <div class="instruction-btn" v-if="requestSend">
        <div class="card-verify--success">
          <i class="far fa-check-circle"></i> Your request has been sent
          successfully
        </div>
        <button class="btn btn-primary" @click="closeModal" :disabled="sending">
          Close
        </button>
      </div>
      <div class="instruction-btn" v-else-if="modalType == 'send-instructions'">
        <button class="btn btn-primary" @click="closeModal" :disabled="sending">
          Cancel
        </button>
        <button
          id="launchpad-chatwidget-sendemail"
          class="btn btn-primary active next-btn"
          @click="sendEmail"
          :disabled="!email || errors.has('form-widgetcode.email')"
        >
          <moon-loader
            v-if="sending"
            :loading="sending"
            color="#37ca37"
            size="20px"
          />
          <span v-else>Send</span>
        </button>
      </div>

      <div class="instruction-btn" v-else-if="modalType == 'steps'">
        <div
          class="instruction-email"
          v-if="currentStep < cmsInstruction.steps.length - 1"
        >
          <div @click="sendInstructionsMail">Email Instructions</div>
        </div>
        <button
          v-if="currentStep == 0"
          class="btn btn-primary"
          @click="changeCms"
        >
          Select different CMS
        </button>
        <button
          v-if="currentStep != 0 && !chatIntegrated && !chatWidgetTestDone"
          class="btn btn-primary"
          @click="prevStep"
          :disabled="sending"
        >
          Back
        </button>
        <button
          v-if="currentStep < cmsInstruction.steps.length - 1"
          class="btn btn-primary active next-btn"
          @click="nextStep"
        >
          Next
        </button>
        <button
          v-if="
            currentStep >= cmsInstruction.steps.length - 1 &&
            !chatIntegrated &&
            !chatWidgetTestDone
          "
          class="btn btn-primary active next-btn"
          @click="testWidgetIntegration"
        >
          <moon-loader
            v-if="sending"
            :loading="sending"
            color="#37ca37"
            size="20px"
          />
          <span v-else>Test Widget</span>
        </button>
        <button
          v-if="
            currentStep >= cmsInstruction.steps.length - 1 &&
            !chatIntegrated &&
            chatWidgetTestDone
          "
          class="btn btn-primary"
          @click="prevStep"
        >
          Back
        </button>
        <button
          v-if="
            currentStep >= cmsInstruction.steps.length - 1 &&
            !chatIntegrated &&
            chatWidgetTestDone
          "
          class="btn btn-primary active next-btn"
          @click="sendInstructionsMail"
        >
          Send Instructions
        </button>
      </div>

      <div class="instruction-btn" v-if="modalType == 'cms-select'">
        <button class="btn btn-primary" @click="closeModal">Cancel</button>
        <button
          class="btn btn-primary active next-btn"
          @click="startInstalling"
        >
          Continue
        </button>
      </div>
    </div>
  </modal>
</template>
<script lang="ts">
import Vue from 'vue'
import Modal from '@/pmd/components/common/Modal.vue'
import { Location } from '@/models'
import SelectCmsModal from './SelectCmsModal.vue'
import ChatWidgetStep from './ChatWidgetStep.vue'
import { TextWidgetSettings } from '@/models/location'

import axios from 'axios'
import config from '@/config'
import { trackGaEvent } from '@/util/helper'

const DEPLOYMENT_BASE_URL = 'https://widgets.leadconnectorhq.com/' // for staging 'https://storage.googleapis.com/staging-leadconnector/'
const DEFAULT_AVATAR_IMAGE = `${DEPLOYMENT_BASE_URL}chat-widget/assets/defaultAvatar.png`
const TEXT_WIDGET_DEPLOYMENT = `${DEPLOYMENT_BASE_URL}loader.js`
const TEXT_WIDGET_IE_DEPLOYMENT = `${DEPLOYMENT_BASE_URL}chat-widget/loader.js`
const DEFAULT_LEGAL_MESSAGE =
  'By submitting you agree to receive SMS or e-mails for the provided channel. Rates may be applied.'
const DEFAULT_REVISIT_MSG = 'Welcome back {{name}}, How can we help you today?'
const DEFAULT_HEADING = 'Have a question?'
const DEFAULT_SUB_HEADING =
  'Enter your question below and a representative will get right back to you.'
const DEFAULT_SUCCESS_MESSAGE =
  'One of our representatives will contact you shortly.'
const DEFAULT_THANKS_MESSAGE = 'Thank You!'
const DEFAULT_PROMPT_MESSAGE = 'Hi there, have a question? Text us here.'
const NAME_VARIABLE = `{{name}}`

const INITIAL_DATA = {
  useEmailField: false,
  autoCountryCode: true,
  showPrompt: true,
  enableReturnUserMessage: true,
  enableMobileNumberValidation: true,
  headerText: 'Have a question?',
  subHeaderText:
    'Enter your question below and a representative will get right back to you.',
  legalMsg:
    'By submitting you agree to receive SMS or e-mails for the provided channel. Rates may be applied.',
  promptMsg: 'Hi there, have a question? Text us here.',
  successMsg: 'One of our representatives will contact you shortly.',
  returnUserMessage: `Welcome back ${NAME_VARIABLE}, How can we help you today?`,
  thankMsg: 'Thank You!',
  customerContact: '',
  countryCode: '',
  avatarImageUrl: DEFAULT_AVATAR_IMAGE,
}

export default Vue.extend({
  props: [
    'modalShow',
    'cms',
    'websiteScreenshot',
    'modalType',
    'cmsInstruction',
  ],
  components: {
    Modal,
    SelectCmsModal,
    ChatWidgetStep,
  },
  computed: {
    modalWidth() {
      return this.modalType === 'cms-select' ? 510 : 800
    },
  },
  data() {
    return {
      loaderScriptURL: TEXT_WIDGET_DEPLOYMENT,
      IELoaderScriptURL: TEXT_WIDGET_IE_DEPLOYMENT,
      widgetPrimaryColor: '#188bf6',
      widgetSettings: {} as TextWidgetSettings,
      currentLocationId: '',
      location: {} as Location,
      newCms: this.cms,
      currentStep: 0,
      showCopiedCodeToolTip: false,
      sending: false,
      email: '',
      chatIntegrated: undefined,
      currentStepInstruction: undefined,
      requestSend: false,
      chatWidgetTestDone: false,
      name_variable: NAME_VARIABLE,
      ...INITIAL_DATA,
    }
  },
  async mounted() {
    await this.fetchData()
    if (this.cms) {
      this.newCms = this.cms
    }
    this.$root.$on('set-cms', (data: any) => {
      this.newCms = data
      this.chatIntegrated = undefined
      this.currentStep = 0
      this.chatWidgetTestDone = false
    })
  },
  watch: {
    cms: async function () {
      this.newCms = this.cms
      this.currentStep = 0
      this.chatIntegrated = undefined
      this.chatWidgetTestDone = false
    },
    '$route.params.location_id': async function (id) {
      this.currentLocationId = id

      await this.fetchData()
      Object.assign(this.$data, INITIAL_DATA)
    },
  },
  methods: {
    changeModalView(type: string) {
      this.$root.$emit('modal-type', type)
    },
    async fetchData() {
      this.currentLocationId = this.$router.currentRoute.params.location_id
      this.location = new Location(
        await this.$store.dispatch(
          'locations/getCurrentLocation',
          this.currentLocationId
        )
      )
      this.widgetSettings = this.location.settings.textwidget
      this.headerText = this.widgetSettings.heading || this.headerText
      this.useEmailField =
        typeof this.widgetSettings.useEmailField === 'boolean'
          ? this.widgetSettings.useEmailField
          : this.useEmailField
      this.autoCountryCode =
        typeof this.widgetSettings.autoCountryCode === 'boolean'
          ? this.widgetSettings.autoCountryCode
          : this.autoCountryCode
      this.showPrompt =
        typeof this.widgetSettings.showPrompt === 'boolean'
          ? this.widgetSettings.showPrompt
          : this.showPrompt
      this.enableReturnUserMessage =
        typeof this.widgetSettings.enableRevisitMessage === 'boolean'
          ? this.widgetSettings.enableRevisitMessage
          : this.enableReturnUserMessage
      this.subHeaderText = this.widgetSettings.subHeading || this.subHeaderText
      this.legalMsg = this.widgetSettings.legalMsg || this.legalMsg
      this.promptMsg = this.widgetSettings.promptMsg || this.promptMsg
      this.successMsg = this.widgetSettings.successMsg || this.successMsg
      this.returnUserMessage =
        this.widgetSettings.revisitPromptMsg || this.returnUserMessage
      this.thankMsg = this.widgetSettings.thankYouMsg || this.thankMsg
      this.customerContact =
        this.widgetSettings.supportContact || this.customerContact
      this.countryCode = this.widgetSettings.countryCode || this.countryCode
      this.avatarImageUrl =
        this.widgetSettings.promptAvatar || this.avatarImageUrl
      this.widgetPrimaryColor =
        this.widgetSettings.widgetPrimaryColor || this.widgetPrimaryColor

      if (!Object.keys(this.widgetSettings).length) {
        console.log('Assigning Chat Object')
        Object.assign(this.widgetSettings, {
          heading: this.headerText,
          useEmailField: this.useEmailField,
          autoCountryCode: this.autoCountryCode,
          showPrompt: this.showPrompt,
          enableRevisitMessage: this.enableReturnUserMessage,
          subHeading: this.subHeaderText,
          legalMsg: this.legalMsg,
          promptMsg: this.promptMsg,
          successMsg: this.successMsg,
          revisitPromptMsg: this.returnUserMessage,
          thankYouMsg: this.thankMsg,
          supportContact: this.customerContact,
          countryCode: this.countryCode,
          promptAvatar: this.avatarImageUrl,
          widgetPrimaryColor: this.widgetPrimaryColor,
        })
      }
    },
    closeModal() {
      this.chatIntegrated = undefined
      this.chatWidgetTestDone = false
      this.requestSend = false
      this.currentStep = 0
      if (this.cms) {
        this.currentStepInstruction = this.cmsInstruction.steps[
          this.currentStep
        ]
      }
      this.$root.$emit('close-modal')
    },
    changeCms() {
      this.changeModalView('cms-select')
    },
    startInstalling() {
      if (this.newCms) {
        this.currentStep = 0
        this.currentStepInstruction = this.cmsInstruction.steps[
          this.currentStep
        ]
        this.changeModalView('steps')
      }
    },
    nextStep() {
      this.currentStep += 1
      this.currentStepInstruction = this.cmsInstruction.steps[this.currentStep]
    },
    prevStep() {
      this.currentStep -= 1
      this.currentStepInstruction = this.cmsInstruction.steps[this.currentStep]
      this.chatIntegrated = undefined
      this.chatWidgetTestDone = false
    },
    async sendInstructionsMail() {
      this.chatIntegrated = undefined
      this.changeModalView('send-instructions')
    },
    getScriptCode() {
      const elementName = 'chat-widget'
      const script = 'script'
      const attrPrefix = ''
      const revisitMessage = this.widgetSettings.revisitPromptMsg
      const importScriptCode = `<${script} src="${this.loaderScriptURL}"
          data-resources-url="${this.IELoaderScriptURL}" ></${script}>`

      let scriptCode = `    <${elementName}
            data-chat-widget
            ${attrPrefix}style="--chat-widget-primary-color: ${
        this.widgetPrimaryColor
      }; --chat-widget-active-color:${
        this.widgetPrimaryColor
      } ;--chat-widget-bubble-color: ${this.widgetPrimaryColor}"
            ${attrPrefix}location-id="${this.currentLocationId}"            
            ${attrPrefix}heading="${this.widgetSettings.heading}"
            ${attrPrefix}sub-heading="${this.widgetSettings.subHeading}"
            ${attrPrefix}prompt-msg="${this.widgetSettings.promptMsg}"
            ${attrPrefix}legal-msg="${this.widgetSettings.legalMsg}"
            ${attrPrefix}show-prompt="${this.widgetSettings.showPrompt}"
            ${attrPrefix}use-email-field="${this.widgetSettings.useEmailField}"
            ${attrPrefix}enable-revisit-message="${
        this.widgetSettings.enableRevisitMessage
      }"
            ${attrPrefix}revisit-prompt-msg="${revisitMessage}"
            ${attrPrefix}support-contact="${this.widgetSettings.supportContact}"
            ${attrPrefix}success-msg="${this.widgetSettings.successMsg}"
            ${attrPrefix}thank-you-msg="${this.widgetSettings.thankYouMsg}"
            ${attrPrefix}prompt-avatar="${this.widgetSettings.promptAvatar}"
            ${attrPrefix}auto-country-code="${
        this.widgetSettings.autoCountryCode
      }"
            ${attrPrefix}country-code="${
        this.widgetSettings.countryCode ? this.widgetSettings.countryCode : 'US'
      }"
      ></${elementName}>
       ${importScriptCode}`

      // for staging server-u-r-l="https://highlevel-staging.uc.r.appspot.com/"

      let startIndex = scriptCode.indexOf('data-chat-widget')
      let endIndex = scriptCode.indexOf('\n', startIndex)
      scriptCode = scriptCode.replace(
        scriptCode.substring(startIndex, endIndex + 13),
        ''
      )
      if (
        DEFAULT_PROMPT_MESSAGE === this.widgetSettings.promptMsg ||
        !this.widgetSettings.showPrompt
      ) {
        let startIndex = scriptCode.indexOf(`${attrPrefix}prompt-msg="`)
        let endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }
      if (DEFAULT_HEADING === this.widgetSettings.heading) {
        let startIndex = scriptCode.indexOf(`${attrPrefix}heading="`)
        let endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }

      if (DEFAULT_SUB_HEADING === this.widgetSettings.subHeading) {
        let startIndex = scriptCode.indexOf(`${attrPrefix}sub-heading="`)
        let endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }
      if (
        this.widgetSettings.enableRevisitMessage ||
        !this.widgetSettings.showPrompt
      ) {
        let startIndex = scriptCode.indexOf(
          `${attrPrefix}enable-revisit-message="`
        )
        let endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
        if (
          DEFAULT_REVISIT_MSG === this.widgetSettings.revisitPromptMsg ||
          !this.widgetSettings.showPrompt
        ) {
          startIndex = scriptCode.indexOf(`${attrPrefix}revisit-prompt-msg="`)
          endIndex = scriptCode.indexOf('\n', startIndex)
          scriptCode = scriptCode.replace(
            scriptCode.substring(startIndex, endIndex + 13),
            ''
          )
        }
      } else {
        const startIndex = scriptCode.indexOf(
          `${attrPrefix}revisit-prompt-msg="`
        )
        const endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }

      if (this.widgetSettings.legalMsg === DEFAULT_LEGAL_MESSAGE) {
        const startIndex = scriptCode.indexOf(`${attrPrefix}legal-msg="`)
        const endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }

      if (this.widgetSettings.showPrompt) {
        const startIndex = scriptCode.indexOf(`${attrPrefix}show-prompt="`)
        const endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }
      if (!this.widgetSettings.useEmailField) {
        const startIndex = scriptCode.indexOf(`${attrPrefix}use-email-field="`)
        const endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }
      if (!this.widgetSettings.supportContact) {
        const startIndex = scriptCode.indexOf(`${attrPrefix}support-contact="`)
        const endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }
      if (
        !this.widgetSettings.successMsg ||
        DEFAULT_SUCCESS_MESSAGE === this.widgetSettings.successMsg
      ) {
        const startIndex = scriptCode.indexOf(`${attrPrefix}success-msg="`)
        const endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }

      if (
        !this.widgetSettings.thankYouMsg ||
        DEFAULT_THANKS_MESSAGE === this.widgetSettings.thankYouMsg
      ) {
        const startIndex = scriptCode.indexOf(`${attrPrefix}thank-you-msg="`)
        const endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }
      if (
        !this.widgetSettings.promptAvatar ||
        !this.widgetSettings.showPrompt
      ) {
        const startIndex = scriptCode.indexOf(`${attrPrefix}prompt-avatar="`)
        const endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
      }
      if (this.widgetSettings.autoCountryCode) {
        let startIndex = scriptCode.indexOf(`${attrPrefix}auto-country-code="`)
        let endIndex = scriptCode.indexOf('\n', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 13),
          ''
        )
        startIndex = scriptCode.indexOf(`${attrPrefix}country-code="`)
        endIndex = scriptCode.indexOf('"', startIndex)
        scriptCode = scriptCode.replace(
          scriptCode.substring(startIndex, endIndex + 4),
          ''
        )
      }
      return scriptCode
    },
    async sendEmail() {
      let result = await this.$validator.validateAll('form-widgetcode.email')
      console.log(result)
      if (!result) {
        return false
      }
      this.sending = true
      try {
        const script = this.getScriptCode()
        await this.$http.post('/chat/send_instructions', {
          email: this.email,
          script,
          location_id: this.location.id,
          cms: this.newCms,
        })
        this.requestSend = true
        this.email = ''
        trackGaEvent(
          'Launchpad',
          'Sent WebChat Widget Instructions',
          `WebChat widget instructions mail has been sent for location: ${this.currentLocationId}`
        )
      } catch (err) {}
      this.sending = false
    },
    async testWidgetIntegration() {
      this.sending = true
      try {
        const response = await axios.get(
          `${config.cloudFunctionsUrl}/isChatWidgetIntegrated?website=${this.location.website}`
        )
        if (response.status == 200) {
          if (response.data.is_chat_integrated) {
            this.chatIntegrated = true
            await this.location.ref.update({
              is_chat_widget_integrated: true,
            })
            this.location = new Location(
              await this.$store.dispatch(
                'locations/getCurrentLocation',
                this.currentLocationId
              )
            )
            setTimeout(() => {
              this.closeModal()
            }, 8000)
            this.$root.$emit('chat-integrated')
            trackGaEvent(
              'Launchpad',
              'WebChat Widget Integrated',
              `WebChat Widget has been integrated for location: ${this.currentLocationId}`
            )
          } else {
            this.chatIntegrated = false
          }
        }
      } catch (error) {
        this.chatIntegrated = false
        console.log(error)
      }
      this.chatWidgetTestDone = true
      this.sending = false
    },
  },
})
</script>
<style scoped>
.chat-instractions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: start;
  padding: 20px 30px 0px 30px;
  font-family: 'Helvetica';
}
.chat-instractions .chat-step-count {
  margin-top: 20px;
  color: rgb(99, 114, 125);
  font-size: 12px;
}
.chat-btn-center {
  border-top: 1px solid rgb(228, 233, 240);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 20px;
}
.chat-btn-grp {
  border-top: 1px solid rgb(228, 233, 240);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  padding: 10px;
  position: relative;
}
.chat-btn-grp .instruction-btn {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: end;
  align-items: end;
  align-items: end;
}
.chat-btn-grp .instruction-btn .next-btn {
  margin-left: 20px;
  margin-left: 20px;
  margin-left: 20px;
}
.chat-modal-containter {
  max-width: 600px;
  max-height: 500px;
  overflow: hidden;
}
.chat-send-instruction-container {
  background: rgb(244, 246, 249);
  border-radius: 12px;
  padding: 40px;
  width: 97%;
}
.chat-send-instruction-container label {
  width: 100%;
  position: relative;
}
.chat-send-instruction-container .label-container {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  min-height: 20px;
  line-height: 1;
  position: relative;
}
.chat-send-instruction-container .label-container span {
  color: rgb(99, 114, 125);
  font-weight: 500;
  font-size: 14px;
  margin-right: 4px;
}
.chat-send-instruction-container input {
  background: rgb(255, 255, 255);
  color: rgb(45, 47, 49);
  padding: 0px 16px;
  font-family: inherit;
  border-radius: 4px;
  box-sizing: border-box;
  width: 100%;
  border: 1px solid rgb(173, 182, 190);
  font-size: 16px;
  height: 40px;
}
.chat-send-instruction-container .include-heading {
  border-bottom: 1px solid rgb(173, 182, 190);
  font-size: 14px;
  padding: 24px 0px 12px;
}
.chat-send-instruction-container .include-items {
  align-items: center;
  display: flex;
  font-size: 12px;
  margin: 12px 0px;
}
.chat-send-instruction-container .include-items-text {
  margin-left: 4px;
}
.instruction-email {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: start;
  margin: auto 150px auto 0px;
  position: absolute;
  left: 20px;
  top: 19px;
}
.instruction-email div {
  background-color: transparent;
  border: none;
  color: rgb(99, 114, 125);
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  padding: 0px;
  text-decoration: none;
  vertical-align: baseline;
}
.card-verify--success {
  color: #11b735;
  margin-right: 27px;
  margin-top: 7px;
}
</style>
