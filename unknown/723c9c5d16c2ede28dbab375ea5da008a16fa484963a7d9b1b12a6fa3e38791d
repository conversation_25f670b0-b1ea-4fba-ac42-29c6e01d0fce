<template>
  <div>
    <div class="form-group">
      <div class="form-input-dropdown dropdown">
        <div data-toggle="dropdown">
          <i class="icon icon-arrow-down-1"></i>
          <input
            type="text"
            class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
            placeholder="Select pipeline"
            :value="pipelineName"
          />
        </div>
        <div class="dropdown-menu">
          <a
            class="dropdown-item trigger-type"
            href="javascript:void(0);"
            v-for="pipeline in pipelines"
            :key="pipeline.id"
            @click.prevent="setValue('pipeline_id', pipeline.id)"
          >
            <p>{{pipeline.name}}</p>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Pipeline  } from '@/models'

export default Vue.extend({
  props: ['action'],
  data() {
    return {
      pipelines: [] as Pipeline[]
    }
  },
  computed: {
    pipelineName(): string {
      const pipeline = lodash.find(this.pipelines, {
        id: this.action.pipeline_id
      })
      if (pipeline) return pipeline.name
      return ''
    }
  },
  async created() {
    this.pipelines = await Pipeline.getByLocationId(this.$router.currentRoute.params.location_id)
  },
  methods: {
    setValue(field: string, value: string) {
      Vue.set(this.action, field, value)
      this.$emit('update:action', this.action)
    }
  }
})
</script>