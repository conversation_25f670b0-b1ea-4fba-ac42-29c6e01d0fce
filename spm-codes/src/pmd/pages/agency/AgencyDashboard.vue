<template>
  <div>
    <SideBarV2 v-if="getSideBarVersion === 'v2'"/>
    <SideBar v-else />
    <TopBar />

    <section class="hl_wrapper">
      <section class="hl_wrapper--inner agency-dash">
        <div class="container-fluid">
          <div class="hl_controls" style="height: 50px">
            <div class="hl_controls--left">
              <h1>
                Agency Dashboard
                <div
                  class="processing-stats"
                  v-if="
                    company.stripeConnectId &&
                    company.stripeConnectProcessed !== true
                  "
                >
                  <div class="processing-stats__indicator"></div>
                  Processing
                </div>
                <div
                  class="processing-stats"
                  style="cursor: pointer"
                  v-else-if="company.stripeConnectId"
                  @click="initData"
                >
                  <i class="fas fa-sync" style="margin-right: 4px"></i>
                  Refresh
                </div>
              </h1>
            </div>
            <!-- <div class="hl_controls--right">
                    <vue-ctk-date-time-picker
                      v-model="dateFilter"
                      :right="true"
                      :range="true"
                      color="#188bf6"
                      :only-date="true"
                      enable-button-validate
                      :noClearButton="true"
                      formatted="ddd, MMM Do"
                      name="start_time"
                      id="random3423324444"
                    />
						      </div> -->
          </div>
          <div class="row">
            <div class="col-md-3 col-sm-6">
              <div class="card --short">
                <div class="card-body">
                  <div class="card-title">
                    <div class="card-title__icon-wrap">
                      <DollarIcon
                        class="card-title__icon"
                      />
                    </div>
                    Revenue Last Month
                  </div>
                  <div class="card-value --green">
                    {{ currentCurrency.symbol }}{{ revenueLastMonth
                    }}<span class="subscript">k</span>
                  </div>
                </div>
                <!-- <div class="card-bottom">
                          <a href="https://dashboard.stripe.com" target="_blank" class="--link">Stripe Dashboard &nbsp;&nbsp; <i class="fas fa-chevron-right"></i></a>
                        </div> -->
                <div class="card-body__overlap" v-if="!company.stripeConnectId">
                  <div
                    class="stripe-connect__setup-btn btn"
                    @click="gotoStripeSettings()"
                  >
                    Connect Stripe
                    <i class="fas fa-exclamation-circle"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 col-sm-6">
              <div
                class="card --short"
                :class="{ '--yellow': company.stripeConnectId }"
              >
                <div class="card-body">
                  <div class="card-title">
                    <div class="card-title__icon-wrap">
                      <MrrIcon
                        class="card-title__icon"
                      />
                    </div>
                    Monthly Recurring Revenue
                  </div>
                  <div
                    class="card-value"
                    :class="{ '--yellow': company.stripeConnectId }"
                  >
                    {{ currentCurrency.symbol }}{{ mrr
                    }}<span class="subscript"
                      >k &nbsp; &nbsp;
                      <!-- <i class="fas fa-arrow-down"></i> -->
                    </span>
                  </div>
                </div>
                <!-- <div class="card-bottom">
                          <span class="--warning" title="Your recurring revenue is less compared to overall revenue">You may have cash flow risk&nbsp;&nbsp; <i class="fas fa-exclamation-triangle"></i></span>
                        </div> -->
                <div class="card-body__overlap" v-if="!company.stripeConnectId">
                  <div
                    class="stripe-connect__setup-btn btn"
                    @click="gotoStripeSettings()"
                  >
                    Connect Stripe
                    <i class="fas fa-exclamation-circle"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 col-sm-6">
              <div class="card --short">
                <div class="card-body">
                  <div class="card-title">
                    <div class="card-title__icon-wrap">
                      <CustomerIcon
                        class="card-title__icon"
                      />
                    </div>
                    Customers in Stripe
                  </div>
                  <div class="card-value --purple" v-if="!connecting">
                    {{ stripeCustomersCount }}<span class="subscript"></span>
                  </div>
                  <moon-loader
                    v-else
                    color="#7F9CF5"
                    size="30px"
                    style="height: 60px; padding: 15px"
                  />
                </div>
                <!-- <div class="card-bottom">&nbsp;
                        </div> -->
                <div class="card-body__overlap" v-if="!company.stripeConnectId">
                  <div
                    class="stripe-connect__setup-btn btn"
                    @click="gotoStripeSettings()"
                  >
                    Connect Stripe
                    <i class="fas fa-exclamation-circle"></i>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-3 col-sm-6">
              <div class="card --short">
                <div class="card-body">
                  <div class="card-title">
                    <div class="card-title__icon-wrap">
                      <NewCustomerIcon
                        class="card-title__icon"
                      />
                    </div>
                    New Customers this Month
                  </div>
                  <div class="card-value --purple" v-if="!connecting">
                    {{ recentStripeCustomersCount
                    }}<span class="subscript"></span>
                  </div>
                  <moon-loader
                    v-else
                    color="#7F9CF5"
                    size="30px"
                    style="height: 60px; padding: 15px"
                  />
                </div>
                <!-- <div class="card-bottom">&nbsp;
                        </div> -->
                <div class="card-body__overlap" v-if="!company.stripeConnectId">
                  <div
                    class="stripe-connect__setup-btn btn"
                    @click="gotoStripeSettings()"
                  >
                    Connect Stripe
                    <i class="fas fa-exclamation-circle"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="card">
                <div class="card-body">
                  <div class="card-title">Revenue Distribution</div>
                  <div class="card-graph">
                    <highcharts
                      style="height: 250px"
                      :options="callPieChart"
                    ></highcharts>
                  </div>
                  <div class="mrr-distribution--bottom">
                    <a
                      href="https://help.gohighlevel.com/support/solutions/articles/48001171909-best-practices-for-using-stripe-with-your-agency-dashboard"
                      target="_blank"
                    >
                      Why are my stats inaccurate?
                      <i class="fas fa-info-circle"></i>
                    </a>
                  </div>
                </div>
                <div class="card-body__overlap" v-if="!company.stripeConnectId">
                  <i class="fas fa-exclamation-circle --big-icon"></i>
                  <div
                    class="stripe-connect__setup-btn btn"
                    @click="gotoStripeSettings()"
                  >
                    Connect Stripe
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card">
                <div class="card-body">
                  <div class="card-title">Growth in Last 6 Months</div>
                  <div class="card-graph">
                    <highcharts
                      style="height: 260px"
                      :options="callBarChart"
                    ></highcharts>
                  </div>
                </div>
                <div class="card-body__overlap" v-if="!company.stripeConnectId">
                  <i class="fas fa-exclamation-circle --big-icon"></i>
                  <div
                    class="stripe-connect__setup-btn btn"
                    @click="gotoStripeSettings()"
                  >
                    Connect Stripe
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="saas-banner">
            <div class="saas-banner__intro">
              <SaasIcon
                class="saas-banner__icon"
              />
              <div>
                <div>
                  <!-- We are launching SaaS Mode that enables you to resell SaaS -->
                  <!-- We have launched SaaS Mode v1 (Twilio rebilling) on the pro plan. -->
                  SaaS Mode is Live on the pro plan
                  <span class="saas-banner__learn-link" @click="gotoSaasEducation()" v-if="!agencyInSaasPlan"> Learn More </span>
                </div>
                <div
                  class="saas-banner__learn-btn btn"
                  @click="gotoSaasEducation()"
                  v-if="agencyInSaasPlan"
                >
                  Learn More &nbsp; <i class="fas fa-chevron-right"></i>
                </div>
                <div
                  class="saas-banner__learn-btn btn"
                  @click="initUpgradation()"
                  v-if="!agencyInSaasPlan"
                >
                  Upgrade Now &nbsp; <i class="fas fa-chevron-right"></i>
                </div>
                <!-- <div class="saas-banner__learn-btn" @click="saasComingSoon()">
                  Coming Soon
                </div> -->
              </div>
            </div>
            <div
              class="sass-banner__updates"
              @click="toggleKeepMePosted(!updates)"
            >
              Keep me posted with Developments &nbsp; &nbsp;
              <div class="toggle" @click.stop style="margin-left: 6px">
                <UIToggle
                  id="dashboard_connect_updates"
                  :disabled="user.postedForSaas"
                  :value="updates || user.postedForSaas"
                />
                <label class="tgl-btn" for="dashboard_connect_updates"></label>
              </div>
            </div>
          </div>
        </div>
      </section>
      <!-- END of .hl_agency-dashboard -->
    </section>
    <upgrade-modalv2
      :company="company"
      v-if="showUpgradeModal"
      type=""
      :show="showUpgradeModal"
      source="billing-page"
      @close="showUpgradeModal = false"
      @success="onUpgradation"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import TopBar from '../../components/TopBar.vue'
import SideBar from '../../components/agency/SideBar.vue'
import SideBarV2 from '../../components/sidebar/SideBar.vue'
import moment from 'moment-timezone'
import { Chart } from 'highcharts-vue'
import { trackGaEvent } from '@/util/helper'
import { currency } from '@/util/currency_helper'

import { mapState } from 'vuex'
import { CompanyState, UserState } from '@/store/state_models'
import { Company, User } from '@/models'
import UpgradeModalv2 from '@/pmd/components/agency/billing/UpgradeModalv2.vue'
import config from '@/config'
import DollarIcon from '@/assets/pmd/img/saas/icon_dollar.svg'
import MrrIcon from '@/assets/pmd/img/saas/icon_mrr.svg'
import CustomerIcon from '@/assets/pmd/img/saas/icon_customers.svg'
import NewCustomerIcon from '@/assets/pmd/img/saas/icon_new_customers.svg'
import SaasIcon from '@/assets/pmd/img/saas-icon.svg'

export default Vue.extend({
  components: {
    TopBar,
    SideBar,
    highcharts: Chart,
    UpgradeModalv2,
    DollarIcon,
    MrrIcon,
    CustomerIcon,
    NewCustomerIcon,
    SaasIcon,
    SideBarV2,
  },
  data() {
    return {
      dateFilter: {
        start: moment().subtract(1, 'months').toDate(),
        end: moment().toDate(),
      },
      showUpgradeModal: false,
      connecting: false,
      stripeCustomersCount: 0,
      recentStripeCustomersCount: 0,
      mrr: 0,
      revenueLastMonth: 0,
      updates: false,
      liveMode: config.mode === 'production',
      seriesMonth: [],
      seriesMRR: [],
      seriesNewCustomers: [],
      seriesOldCustomers: [],
      seriesMrrDistribution: [],
      get callPieChart() {
        return {
          chart: {
            plotBackgroundColor: null,
            plotBorderWidth: 0,
            plotShadow: false,
            style: {
              fontFamily: 'Roboto',
            },
          },
          title: {
            text: '',
            align: 'center',
            verticalAlign: 'middle',
            y: 40,
          },
          tooltip: {
            valueSuffix: ' USD',
            // pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
          },
          plotOptions: {
            pie: {
              dataLabels: {
                enabled: false, // true: https://api.highcharts.com/highcharts/plotOptions.pie.dataLabels.connectorShape
                // enabled: true,
                // distance: 0,
                // style: {
                //   "backgroundColor": '#ffffff',
                //   "border": "1px solid #cccccc"
                // }
              },
            },
            series: {
              showInLegend: true,
            },
          },
          colors: ['#4FD1C5', '#81E6D9', '#667EEA', '#F6E05E', '#f5ad55'],
          series: [
            {
              type: 'pie',
              name: 'MRR contribution',
              innerSize: '60%',
              data: this.seriesMrrDistribution,
              // data: [{
              //   name: 'Product 1',
              //   y: 28,
              // },{
              //   name: 'Product 2',
              //   y: 41,
              // },{
              //   name: 'Product 3',
              //   y: 9,
              // },{
              //   name: 'Product 4',
              //   y: 22,
              // },
              // ]
            },
          ],
          legend: {
            enabled: true,
            layout: 'vertical',
            align: 'right',
            verticalAlign: 'middle',
            itemMarginTop: 10,
            itemMarginBottom: 10,
            itemCheckboxStyle: {
              marginRight: '20px',
            },
            labelFormat: '{name} <b>({percentage:.1f}%)</b>',
          },
          credits: {
            enabled: false,
          },
        }
      },
      get callBarChart() {
        return {
          chart: {
            type: 'column',
            plotBackgroundColor: null,
            plotBorderWidth: 0,
            plotShadow: false,
            style: {
              fontFamily: 'Roboto',
            },
          },
          xAxis: {
            // categories: ['June', 'July', 'August', 'September', 'October', 'November']
            categories: this.seriesMonth,
          },
          yAxis: [
            {
              min: 0,
              title: {
                text: '',
              },
            },
            {
              // Secondary yAxis
              title: {
                text: 'MRR (USD)',
              },
              opposite: true,
            },
          ],
          title: {
            text: '',
            align: 'center',
            verticalAlign: 'middle',
            y: 40,
          },
          // tooltip: {
          //   pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
          // },
          plotOptions: {
            column: {
              stacking: 'normal',
              dataLabels: {
                enabled: false,
              },
            },
          },
          colors: ['#4FD1C5', '#F6E05E', '#f5ad55'],
          series: [
            {
              name: 'New customers',
              type: 'column',
              // data: [30, 50, 30, 20, 40, 35]
              data: this.seriesNewCustomers,
            },
            {
              name: 'Old customers',
              type: 'column',
              // data: [150, 180, 200, 250, 270, 300]
              data: this.seriesOldCustomers,
            },
            {
              name: 'Monthly Recurring Revenue',
              type: 'line',
              yAxis: 1,
              // data: [800, 500, 2000, 500, 2500, 2300],
              data: this.seriesMRR,
              tooltip: {
                valueSuffix: ' USD',
              },
            },
          ],
          legend: {
            enabled: true,
            layout: 'horizontal',
            align: 'center',
            verticalAlign: 'bottom',
            itemCheckboxStyle: {
              marginRight: '20px',
            },
          },
          credits: {
            enabled: false,
          },
        }
      },
    }
  },
  computed: {
    user() {
      const user: UserState = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    ...mapState('company', {
      company: s => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
    currentCurrency() {
      return (
        currency[
          this.company?.data?.stripe_connect_default_currency?.toUpperCase()
        ] || currency['USD']
      )
    },
    agencyInSaasPlan(): boolean {
      return (
        this.$store.getters['company/inSaasPlan']
      )
    },
    getSideBarVersion(): string {
        return this.$store.getters['sidebarv2/getVersion'] 
    },
  },
  mounted() {
    if (this.company && this.company.stripeConnectId) {
      this.fetchCustomers()
      this.fetchMrr()
      this.fetchMrrDistribution()
      this.fetchRevenue()
    } else {
      this.revenueLastMonth = 17.4
      this.mrr = 7.4
      this.stripeCustomersCount = 57
      this.recentStripeCustomersCount = 4

      this.seriesMrrDistribution = [
        {
          name: 'Product 1',
          y: 28,
        },
        {
          name: 'Product 2',
          y: 41,
        },
        {
          name: 'Product 3',
          y: 9,
        },
        {
          name: 'Product 4',
          y: 22,
        },
      ]
      this.seriesOldCustomers = [70, 100, 150, 220, 310, 360]
      this.seriesNewCustomers = [30, 50, 70, 90, 50, 90]
      this.seriesMRR = [800, 500, 1200, 500, 2500, 2300]
      let monthsName = []
      for (let i = 5; i >= -1; i--) {
        monthsName.push(moment().subtract(i, 'months').format('MMMM'))
      }
      this.seriesMonth = monthsName
      // this.seriesMonth = ['August', 'September', 'October', 'November', 'December', 'January']
    }
  },
  methods: {
    async initData() {
      try {
        this.company.stripeConnectProcessed = false
        const { data } = await this.saasService.post(`/connect/init_data`, {
          company_id: this.company?.id,
          account_id: this.company?.stripeConnectId,
        })
        // console.log(data)
        this.fetchCustomers()
        this.fetchMrr()
        this.fetchMrrDistribution()
        this.fetchRevenue()
      } catch (err) {
        //
      }
    },
    async fetchMrr() {
      // this.connecting = true;
      try {
        const { data: mrr } = await this.saasService.get(
          `/connect/monthly_mrr?account_id=${this.company.stripeConnectId}&livemode=${this.liveMode}`
        )
        // console.log(mrr);
        this.mrr = Math.round(mrr[mrr.length - 1].value / 10) / 100
        this.seriesMRR = []
        for (let i = 1; i < mrr.length; i++) {
          this.seriesMRR.push(mrr[i].value)
        }
      } catch (err) {
        //
      }
      // this.connecting = false
    },
    async fetchMrrDistribution() {
      const { data: mrrDistribution } = await this.saasService.get(
        `/connect/mrr_distribution?account_id=${this.company.stripeConnectId}&livemode=${this.liveMode}`
      )
      // console.log(mrrDistribution);
      let othersValue = 0
      this.seriesMrrDistribution = []
      for (let i = 0; i < mrrDistribution.length; i++) {
        if (i >= 4) {
          othersValue += mrrDistribution[i].normalized_amount
        } else {
          this.seriesMrrDistribution.push({
            name: mrrDistribution[i].product_name,
            y: mrrDistribution[i].normalized_amount / 100,
          })
        }
      }
      if (othersValue) {
        this.seriesMrrDistribution.push({
          name: 'Other Products',
          y: othersValue / 100,
        })
      }
    },
    async fetchRevenue() {
      const { data: revenue } = await this.saasService.get(
        `/connect/monthly_revenue?account_id=${this.company.stripeConnectId}&livemode=${this.liveMode}`
      )
      // console.log(revenue);
      this.revenueLastMonth =
        Math.round(revenue[revenue.length - 2].value / 10) / 100
      // this.revenueLastMonth = Math.round(revenue[revenue.length - 2].value) / 1000
    },
    async fetchCustomers() {
      this.connecting = true
      try {
        const { data: monthlyCustomers } = await this.saasService.get(
          `/connect/monthly_customers?account_id=${this.company.stripeConnectId}&livemode=${this.liveMode}`
        )
        // console.log(monthlyCustomers);

        this.stripeCustomersCount =
          monthlyCustomers[monthlyCustomers.length - 1].total_customers
        this.recentStripeCustomersCount =
          monthlyCustomers[monthlyCustomers.length - 1].new_customers

        this.seriesMonth = []
        this.seriesNewCustomers = []
        this.seriesOldCustomers = []
        for (let i = 1; i < monthlyCustomers.length; i++) {
          this.seriesMonth.push(monthlyCustomers[i].month)
          this.seriesNewCustomers.push(monthlyCustomers[i].new_customers)
          this.seriesOldCustomers.push(monthlyCustomers[i].old_customers)
        }
      } catch (err) {
        //
      }
      this.connecting = false
    },
    toggleKeepMePosted(newValue: boolean) {
      if (newValue) {
        trackGaEvent(
          'SaaS Mode',
          this.company.id,
          'Keep Me Updated (Source: Agency Dashboard)',
          1
        )
        this.updates = newValue
        this.user?.ref.update({
          posted_for_saas: true,
        })
      }
    },
    gotoSaasEducation() {
      trackGaEvent(
        'SaaS Mode',
        this.company.id,
        'Clicked on SaaS Education from Agency Dashboard',
        1
      )
      // this.$router.push({ name: 'saas_education' })
      window.open('https://help.gohighlevel.com/support/solutions/articles/48001184920-saas-mode-full-setup-guide-faq', '_blank')
    },
    saasComingSoon() {
      trackGaEvent(
        'SaaS Mode',
        this.company.id,
        'Clicked on SaaS Coming Soon from Agency Dashboard',
        1
      )
    },
    gotoStripeSettings() {
      trackGaEvent(
        'StripeConnect',
        this.company.id,
        'Clicked on Stripe Connect from Agency Dashboard',
        1
      )
      this.$router.push({ name: 'stripe_settings' })
    },
    initUpgradation(){
      this.showUpgradeModal = true;
      trackGaEvent('AgencyDashboard',this.company.id, 'Init Upgradation from Agency Dashboard',1)
    },
    onUpgradation(){
      trackGaEvent('AgencyDashboard',this.company.id, 'Completed Upgradation from Agency Dashboard',1)
    }
  },
})
</script>
<style scoped lang="scss">
@keyframes blink {
  from,
  to {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}
.agency-dash {
  .processing-stats {
    display: inline-flex;
    margin-left: 8px;
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 30px;
    color: #a0aec0;
    border: 1px solid #a0aec0;
    font-weight: 500;

    align-items: center;

    opacity: 0.8;
    &:hover {
      opacity: 1;
    }
    .processing-stats__indicator {
      margin-right: 4px;
      height: 6px;
      min-width: 6px;
      border-radius: 50%;
      background-color: #68d391;
      animation: 1s blink ease infinite;
    }
  }
  .card {
    &.--short {
      height: 154px;
      .card-value {
        margin-left: 36px;
      }
    }
    &.--yellow {
      background-color: #fffaf0;
    }
  }
  .card-title {
    font-weight: 500;
    font-size: 16px;
    line-height: 18px;
    color: #4a5568;
    height: 24px;

    display: flex;
    align-items: center;
    .card-title__icon-wrap {
      border-radius: 50%;
      height: 24px !important;
      min-width: 24px !important;

      background-color: #a0aec0;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
    }
  }
  .card-value {
    // padding-left: 132px;
    font-size: 44px;
    line-height: 56px;
    color: #68d391;
    white-space: nowrap;
    &.--green {
      color: #68d391;
    }
    &.--yellow {
      color: #f6ad55;
    }
    &.--purple {
      color: #7f9cf5;
    }
    .subscript {
      font-size: 24px;
    }
  }
  .card-bottom {
    display: flex;
    justify-content: flex-end;
    padding: 8px 16px;
    line-height: 16px;
    min-height: 32px;
    margin-top: -20px;

    background-color: #ffffff;
    .--link {
      color: #63b3ed;
      cursor: pointer;
    }
    .--warning {
      color: #ec8936;
    }
  }
  .mrr-distribution--bottom {
    position: absolute;
    bottom: 8px;
    right: 32px;
    color: #a0aec0;
    cursor: pointer;
    a {
      color: #a0aec0;
    }
  }
  .card-body__overlap {
    position: absolute;
    width: 100%;
    height: calc(100% - 44px);
    bottom: 0px;

    background-color: rgba(255, 255, 255, 0.85);

    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    .--big-icon {
      color: #fbd38d;
      font-size: 64px;
      margin-bottom: 104px;
    }
  }
  .stripe-connect__setup-btn {
    background: #feebc8;
    border-radius: 3px;

    font-weight: 500;
    font-size: 12px;
    line-height: 14px;
    text-align: center;

    margin-bottom: 14px;
    color: #f6ad55;
    padding: 6px 8px !important;
    i {
      margin-left: 6px;
    }
    &:hover {
      background: #f6ad55;
      color: #ffffff;
    }
  }
  .card-graph {
    height: 220px;
    width: 100%;
    margin-bottom: 20px;
  }
  .connect-stripe {
    font-weight: 500;
    font-size: 12px;
    line-height: 14px;
    text-align: center;
    color: #ed8936;
    padding: 6px 12px;
    background-color: #feebc8;
    border-radius: 3px;
    margin: auto;
    width: fit-content;
    cursor: pointer;
  }
  .saas-banner {
    width: 100%;
    min-height: 160px;
    background: linear-gradient(90deg, #3182ce 0%, #887bdc 100%);
    border-radius: 3px;

    padding: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #ffffff;
    .saas-banner__intro {
      padding-right: 84px;
      border-right: 1px solid #f7fafc;

      display: flex;
      align-self: center;
    }
    .saas-banner__icon {
      margin-right: 34px;
    }
    .saas-banner__learn-btn {
      background-color: #ffffff;
      backdrop-filter: blur(21px);
      border-radius: 68px;
      line-height: 16px;
      padding: 6px 12px;
      color: #577fd4;
      width: fit-content;
      margin-top: 4px;
      // cursor: pointer;
    }
    .saas-banner__learn-link {
      font-weight: 600;
      cursor: pointer;

    }
    .sass-banner__updates {
      margin-left: 32px;
      background: rgba(255, 255, 255, 0.86);
      backdrop-filter: blur(21px);
      border-radius: 3px;

      padding: 6px 12px;
      color: #718096;
      cursor: pointer;
      user-select: none;

      display: flex;
      align-items: center;
    }
  }
}
</style>
