<template>
    <div class="ps-content col-xs-9">
        <!--WIZARD STEPS-->
        <!-- ngSwitchWhen: type -->
        <!-- ngSwitchWhen: track -->
        <!-- ngSwitchWhen: display -->
        <!-- ngSwitchWhen: message -->
        <!-- ngIf: shouldShowStep('message') -->
        <div class="step-message">
            <div class="message-step">
                <div autocomplete="false" class="ng-pristine ng-invalid ng-invalid-required ng-valid-url">
                    <div class="notification-live-preview animated bounceInUp">
                        <div class="pb-2 text-success"><i class="fa fa-circle animated fadeIn infinite mr-1"></i> Live Preview</div>
                        <PsBubble :notification="notification" />
                        <!-- <div class="ps-bubble">
                            <div class="bubble-body mx-auto no-text-select ng-scope" ng-class="{'rtl-bubble' : notification.settings.rtl}">
                                <div class="bubble-icon">
                                    <img data-ng-src="https://cdn.provesrc.com/icon.gif" src="https://cdn.provesrc.com/icon.gif">
                                </div>
                                <div class="bubble-content">
                                    <div class="bubble-title text-truncate">
                                        <span ng-style="notification.settings.theme.title" class="ng-binding" style="color: rgb(120, 37, 243); background-color: rgb(242, 233, 255);">50 visitors</span>
                                    </div>
                                    <div class="bubble-description">
                                        <span class="ng-binding">signed up for ProveSource</span>
                                    </div>
                                    <div class="bubble-time">
                                        <div class="ng-binding">in the past 8 minutes
                                            
                                            <div class="ps-link ng-scope" ng-if="!$parent.settings.whitelabel || ($parent.settings.whitelabel &amp;&amp; $parent.settings.branding.active)">
                                                <span class="bubble-bolt ng-binding">⚡ by</span>
                                                <a href="" ng-style="{'color' : $parent.$parent.settings.whitelabel ? $parent.$parent.settings.branding.color || '#7627f3' : '#7627f3'}" class="ng-binding" style="color: rgb(118, 39, 243);">ProveSource</a>
                                            </div>
                                        </div>
                                    </div>
                                    <span id="ps-bubble-close" ng-show="notification.settings.allowClose" class="ng-hide">
                                        <span class="close-before"></span>
                                        <span class="close-after"></span>
                                    </span>
                                </div>
                            </div>
                        </div> -->
                    </div>
                    <div class="step-content" v-if="notification.type!='review'">
                        <div class="step-panel">
                            <div class="title">Message Settings</div>
                            <!-- <div class="section row">
                                <div class="col-sm-6 sub-title">Notification Language</div>
                                <div class="col-sm-6 text-right">
                                    <select class="form-control float-right ng-pristine ng-untouched ng-valid ng-not-empty" style="width: 140px;">
                                        <option label="Bengali" value="string:bn">Bengali</option>
                                        <option label="Bulgarian" value="string:bg">Bulgarian</option>
                                        <option label="Croatian" value="string:hr">Croatian</option>
                                        <option label="Czech" value="string:cs">Czech</option>
                                        <option label="Danish" value="string:da">Danish</option>
                                        <option label="Dutch" value="string:nl">Dutch</option>
                                        <option label="English" value="string:en" selected="selected">English</option>
                                        <option label="French" value="string:fr">French</option>
                                        <option label="German" value="string:de">German</option>
                                        <option label="Greek" value="string:el">Greek</option>
                                        <option label="Hebrew" value="string:he">Hebrew</option>
                                        <option label="Hindi" value="string:hi">Hindi</option>
                                        <option label="Hungarian" value="string:hu">Hungarian</option>
                                        <option label="Indonesian" value="string:id">Indonesian</option>
                                        <option label="Italian" value="string:it">Italian</option>
                                        <option label="Japanese" value="string:ja">Japanese</option>
                                        <option label="Norwegian" value="string:nb">Norwegian</option>
                                        <option label="Portuguese" value="string:pt">Portuguese</option>
                                        <option label="Polish" value="string:pl">Polish</option>
                                        <option label="Romanian" value="string:ro">Romanian</option>
                                        <option label="Russian" value="string:ru">Russian</option>
                                        <option label="Slovak" value="string:sk">Slovak</option>
                                        <option label="Slovenian" value="string:sl">Slovenian</option>
                                        <option label="Swedish" value="string:sv">Swedish</option>
                                        <option label="Spanish" value="string:es">Spanish</option>
                                        <option label="Turkish" value="string:tr">Turkish</option>
                                        <option label="Vietnamese" value="string:vi">Vietnamese</option>
                                    </select>
                                </div>
                            </div>
                            <div class="section row">
                                <div class="col-sm-6 sub-title">Right-To-Left Support</div>
                                <div class="col-sm-6 text-right">
                                    <div class="float-right">
                                        <ps-toggle ng-model="notification.settings.rtl" class="ng-pristine ng-untouched ng-valid ng-isolate-scope ng-empty">
                                            <span ng-if="!hideTitle" class="toggle-state no-text-select ng-binding ng-scope" ng-class="{'mr-3':leftTitle, 'on' : !leftTitle &amp;&amp; model === true}">OFF</span>
                                            <label class="switch float-right no-text-select"><input type="checkbox" ng-model="model" data-ng-change="setState()" ng-checked="model" class="ng-pristine ng-untouched ng-valid ng-empty"><span class="slider round"></span></label>
                                        </ps-toggle>
                                    </div>
                                </div>
                            </div> -->
                            <div class="section">

                                <div class="ps-url-input" v-if="notification.type=='live'| notification.type=='page_visits'| notification.type=='conversions'| notification.type=='combo'">
                                    <div class="ps-input-frame">
                                        <form name="msgsndr13" data-lpignore="true" autocomplete="msgsndr13" class="ng-pristine  -required -pattern msgsndr13">
                                            <label class="input-title" for="refer">Refer to visitors as</label>
                                            <input show-focus="shouldFocus" type="text" name="fieldName" id="refer" autofocus="" placeholder="visitors" required="required">
                                        </form>
                                    </div>
                                </div>

                                <div class="ps-url-input" v-if="notification.type=='stream' | notification.type=='page_visits'| notification.type=='conversions'">
                                    <div class="ps-input-frame">
                                        <form name="msgsndr13" data-lpignore="true" autocomplete="msgsndr13" class="-required -pattern msgsndr13">
                                            <label class="input-title" for="message_text">Message text</label>
                                            <input show-focus="shouldFocus" type="text" name="message_text" id="message_text" autofocus="" placeholder="visitors" required="required">
                                        </form>
                                    </div>
                                </div>

                                <div class="ps-url-input" v-if="notification.type=='stream'| notification.type=='page_visits'| notification.type=='conversions'">
                                    <div class="ps-input-frame">
                                        <form name="msgsndr13" data-lpignore="true" autocomplete="msgsndr13" class="-required -pattern msgsndr13">
                                            <label class="input-title" for="image">Image</label>
                                            <input show-focus="shouldFocus" type="text" name="image" id="image" autofocus="" placeholder="visitors" required="required">
                                        </form>
                                    </div>
                                </div>

                                <div class="ps-tip" v-if="notification.type=='stream'| notification.type=='page_visits'| notification.type=='conversions'">
                                    <div class="tip blue mt-2 mb-2">
                                        <i class="fa fa-lg fa-info-circle mr-2"></i>
                                        Visit <a href="https://icons8.com/" target="_blank" class="ng-scope">Icons8.com</a> for nice looking icons (right click an icon -&gt; <strong class="ng-scope">Copy Image Address</strong>)
                                    </div>
                                </div>

                            </div>

                            <!-- ngIf: notification.type == 'stream' -->
                        </div>
                    </div>

                    <div class="step-content" v-if="notification.type=='review'">
                        <div class="step-panel">
                            <div class="title">Message Settings</div>

                            <div class="section">
                                <div class="row">
                                    <div class="col-sm-12">
                                        <p class="description mb-0 mt-3 text-center">
                                            <i class="far fa-4x fa-smile"></i>
                                            <br>
                                            Wow! you don't need to configure anything here, you can move on.
                                            <div class="ps-tip">
                                                <div class="tip green mt-2 mb-5" style="margin-top: 10px;text-align:center;">
                                                    <i class="fa fa-info-circle"></i>
                                                    We will automatically track your website visitors in real time.
                                                </div>
                                            </div>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="step-nav-buttons-container">
                        <div class="button-container no-text-select">
                            <button class="wizard-btn next" :disabled="nextDisabled">Next<i class="fa fa-arrow-right ml-2"></i></button>
                            <button class="wizard-btn back ng-scope" v-on:click="$emit('go-back', 3)">Back
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div><!-- end ngIf: shouldShowStep('message') -->
        <!-- end ngSwitchWhen: -->
        <!-- ngSwitchWhen: customize -->
        <!-- ngSwitchWhen: launch -->
    </div>
</template>

<script lang='ts'>
import Vue from "vue";

import {
    NotificationTemplate
} from "@/models";

declare var $: any;

import PsBubble from "./psbubble.vue";

export default Vue.extend({
    components: {
        PsBubble
    },
    props: {
        notificationId: String
    },
    data() {
        return {
            notification: {} as NotificationTemplate
        };
    },
    computed: {
        nextDisabled(): boolean {
            return true;
        },
    },
    async created() {
        this.fetchData();
    },
    watch: {
        'notificationId': function (id) {
            this.fetchData();
        },
    },
    methods: {
        async fetchData() {
            this.notification = await NotificationTemplate.getById(this.notificationId);
        },
        saveAndNext() {
            this.$emit('save-next', 5);
        },
    }
})
</script>
