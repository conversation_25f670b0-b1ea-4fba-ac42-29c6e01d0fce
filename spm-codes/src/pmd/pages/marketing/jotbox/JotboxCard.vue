<template>
  <div class="row m-0">
    <div class="user-info-area col-md-12 mx-auto" v-if="!loading && filterData.length">
      <div class="user-detail-area">
        <div class="user-info-header user-info-submission-header pb-2">
          <div class="media border-0 d-flex align-items-center">
            <div class="media-body d-flex align-items-center">
              <a
                @click.stop.prevent="goToContact()"
                class="avatar_img"
                :style="{backgroundColor: '#38a0db'}"
              >{{ initials }}</a>
              <h4 class="m-0 ml-3 align-middle" @click.stop.prevent="goToContact()" style="cursor: pointer;">{{ title ? title : 'Submission'}}</h4>
            </div>
            <div class="date-stra">
              <p class="submission-date">{{submitDate}}</p>
            </div>
          </div>
        </div>
        <div class="bg-white user-info-body shadow-sm rounded my-2">
          <table class="table user-info-table table-hl" v-if="!loading">
            <tbody>
              <tr v-for="data in filterData" :key="data.name">
                <template v-if="data.value && typeof data.value == 'object'">
                  <td>{{ data.value.tag }}</td>
                  <td><JotboxValueHolder :value = "data.value.value"/></td>
                </template>
                <template v-else>
                  <td width="20%">{{ standardFields.hasOwnProperty(data.name) ? standardFields[data.name] : data.name }}</td>
                  <td ><JotboxValueHolder :value = "data.value"/></td>
                </template>
              </tr>
              <tr v-if="Object.keys(selectedValues).length === 0 && !loading"></tr>
            </tbody>
          </table>
        </div>
      </div>

      <JotBoxPageDetail
        :loading="loading"
        :util="util" />
    </div>
    <div v-else class="col-md-12" :class="{ 'bg-white p-4' : ! loading }">
      <div v-if="loading" ref="disp-tab">
        <div class="user-detail-area">
          <div class="user-info-header pb-2" v-if="!isContactPage">
            <div class="media border-0 d-flex align-items-center loading">
              <div class="media-body d-flex align-items-center">
                <div
                  class="avatar_img"
                  :style="{background: 'linear-gradient(to right, #eee 20%, #ddd 50%, #eee 80%)'}"
                ></div>
                <div class="p-1 td-loading-short m-0 ml-3 align-middle">
                  <span></span>
                </div>
              </div>
              <div class="date-stra">
                <div class="submission-date p-1 td-loading-short">
                  <span></span>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-white user-info-body shadow-sm rounded my-2 loading">
            <table class="table user-info-table">
              <tbody>
                <tr v-for="index2 in 6" :key="index2">
                  <td class="p-3 td-loading-short">
                    <span></span>
                  </td>
                  <td class="p-3 td-loading-short">
                    <span></span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <JotBoxPageDetail
          :loading="loading"
          :util="util" />
      </div>
      <p v-else style="fontWeight: 600">No records found</p>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { JotBoxUtils } from './../utils/JotBoxUtils'
import { User } from '@/models'
const JotBoxPageDetail = () => import('@/pmd/pages/marketing/jotbox/JotBoxPageDetail.vue').then(m => m.default)
const JotboxValueHolder = () => import('./JotboxValueHolder').then(m => m.default)

export default Vue.extend({
  props: [
    'contactNumber',
    'selectedValues',
    'loading',
    'title',
    'submitDate',
    'contactId',
    'isContactPage'
  ],
  data() {
    return {
      util: {}
    }
  },
  components: {
    JotboxValueHolder,
    JotBoxPageDetail
  },
  async mounted() {
    this.util = new JotBoxUtils()
    this.util.selectedValues = this.selectedValues
  },
  watch: {
    selectedValues(val) {
      this.util.selectedValues = val
    }
  },
  computed: {
    filterData() {
      return this.util.filterData()
    },

    eventData() {
      return this.util.eventData()
    },

    standardFields() {
      return this.util.standardFields
    },

    eventsFields() {
      return this.util.eventsFields
    },

    initials() {
      return this.util.initials(this.title)
    },

    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },

    getSideBarVersion() {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  created() {
    this.util = new JotBoxUtils()
  },
  methods: {
    goToContact() {
      this.$router.push({
        name: this.getSideBarVersion == 'v2' ? 'contact_detail-v2': 'contact_detail',
        params: { contact_id: this.contactId }
      })
    }
  }
})
</script>
<style scoped lang="scss">
#form-builder {
  .user-detail-area {
    .media-body {
      padding: 0;
    }
    .avatar_img {
      min-width: 30px;
      width: 30px;
      height: 30px;
      line-height: 30px;
      color: #fff;
    }
    h6.text-muted {
      margin-bottom: 0;
    }
  }
  .user-info-table {
    margin: 5px 0;
    overflow-wrap: break-word;
    word-wrap: break-word;
    hyphens: auto;

    &.table-hl {
      tbody {
        tr {
          td:first-child {
            font-weight: 500;
          }
        }
      }
    }
  }
}

td.detail {
  word-break: break-all;
}

.loading {
  td {
    vertical-align: middle;
    &.td-loading-short {
      width: 50%;
      height: 45px;
      float: left;
      span {
        width: 270px;
        height: 25px;
        display: block;
        background: linear-gradient(to right, #eee 20%, #ddd 50%, #eee 80%);
        background-size: 700px 100px;
        animation-name: moving-gradient;
        animation-duration: 1.5s;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
      }
    }
    &.td-loading-short-page-detail {
      width: 65%;
      &:first-child {
        width: 35%;
      }
    }
  }
  div {
    height: 40px;
    vertical-align: middle;
    padding: 8px;
    span {
      display: block;
    }
    &.td-loading-short {
      max-width: 150px;
    }
    &.td-loading-short {
      float: left;
      span {
        width: 70px;
        height: 25px;
        background: linear-gradient(to right, #eee 20%, #ddd 50%, #eee 80%);
        background-size: 700px 100px;
        animation-name: moving-gradient;
        animation-duration: 1.5s;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
        animation-fill-mode: forwards;
      }
    }
  }
  &:first-child {
    div {
      height: 25px;
      &.td-loading-short {
        max-width: 270px;
        span {
          width: 150px;
          height: 15px;
        }
      }
    }
  }
}
</style>
