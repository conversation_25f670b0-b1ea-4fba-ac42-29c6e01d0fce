import Vue from 'vue'
import { Module, ActionContext } from 'vuex'
import { RootState, CalendarState } from './state_models'
import Calendar from '../models/calendar'
import { sortBy } from 'lodash';

let calendarListner;
export const calendars: Module<CalendarState, RootState> = {
  namespaced: true,
  state: { calendars: [], locationId: undefined },
  getters: {
    getById: (state: CalendarState) => {
      return (id: string) => state.calendars.find(x => x.id === id)
    },
    calendarsWithErrors(state: CalendarState) {
      return state.calendars.filter(c => c.linked_calendars && c.linked_calendars.google && c.linked_calendars.google.error)
    },
    errorMsg: (state: CalendarState, getters) => {
      if (getters.calendarsWithErrors.length === 0) return null;

      let msg = 'There is a problem with the integration on these calendars "'
      getters.calendarsWithErrors.forEach(cal => {
        msg += cal.name + ', '
      })
      msg = lodash.trimEnd(msg, ', ')
      msg += '"'
      return msg
    }
  },
  mutations: {
    set: (
      state: CalendarState,
      payload: { calendars: any[]; locationId: string }
    ) => {
      state.calendars = payload.calendars
      if (payload.locationId) {
        state.locationId = payload.locationId
      }
    },
    clearAll(state: CalendarState) {
      if (calendarListner) {
        calendarListner();
      }

      state.calendars = []
      state.locationId = undefined
    }
  },
  actions: {
    async syncAll(
      context: ActionContext<CalendarState, RootState>,
      locationId: string
    ) {
      if (!locationId) {
        context.commit('clearAll')
        return
      }


      if (context.state.locationId === locationId) {
        return context.state.calendars
      }

      return new Promise((resolve, reject) => {

        if (calendarListner) {
          calendarListner();
        }

        calendarListner = Calendar.fetchAllByLocation(locationId)
          .onSnapshot(snapshot => {
            const calendars = snapshot.docs
            // .filter(cal => !cal.data().provider_id)
            .map(d => {
              return {
                id: d.id,
                ...d.data()
              }
            })
            const sortedCalendars = sortBy(calendars, (c) => c.name ? c.name.toUpperCase() : '')
            context.commit('set', {
              locationId: locationId,
              calendars: sortedCalendars
            })

            resolve(context.state.calendars)
          }, err => resolve())
      })
    },
    logout: {
      root: true,
      handler: (context: ActionContext<CalendarState, RootState>) => {
        context.commit('clearAll')
      }
    },
    clearAll: {
      handler: (context: ActionContext<CalendarState, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
