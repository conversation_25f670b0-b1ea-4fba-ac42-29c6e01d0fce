<template>
    <div class="ps-content col-xs-9">

        <div v-if="notification.type=='combo'" class="step-content pb-0 ng-scope">
            <div class="step-panel mb-0">
                <div class="title">Combo Type</div>
                <div class="section">
                    <div class="row">
                        <div class="col-sm-6">
                            <p class="description mb-0 mt-2">What would you like to track?</p>
                        </div>
                        <div class="col-sm-6">
                            <div class="item float-right text-capitalize" ng-class="{'greyed-out' : isEditOrDuplicate}">
                                <div class="ps-optionsbox">
                                    <div class="btn-group" role="group">
                                        <button type="button" :class="{'btn btn-radio-option text-capitalize ': true, 'option-selected':notification.comboType == 'visits'}" @click="setComboType('visits')">

                                            <i v-if="notification.comboType == 'visits'" class="fa fa-check" ></i> Visits
                                        </button>
                                        <button type="button" :class="{'btn btn-radio-option text-capitalize ': true, 'option-selected':notification.comboType == 'conversions'}" @click="setComboType('conversions')">
                                            <i v-if="notification.comboType == 'conversions'" class="fa fa-check" ></i> Conversions
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="step-content pb-0" v-if="notification.type=='stream' || notification.type=='conversions' || (notification.type=='combo' && notification.comboType == 'conversions')">
            <div class=" step-panel">
                <div class="title">How to Track</div>
                <div class="section">
                    <div class="row">
                        <div class="col-sm-12">
                            <p class="description mb-0 mt-1">How should we track your conversions?</p>
                        </div>
                        <div class="col-sm-3 no-text-select webhook-platform " title="Form Submissions">
                            <div :class="{'platform-container no-text-select': true, 'selected-platform':notification.subtype == 'form_submission'}" @click="selectType('form_submission')">
                                <img class="platform-icon" src="../../../assets/pmd/img/autoTrack.png">
                                <div class="platform-title ">Form Submissions</div>
                            </div>
                        </div>
                        <div class="col-sm-3 no-text-select webhook-platform">
                            <div :class="{'platform-container no-text-select': true, 'selected-platform':notification.subtype == 'webhook'}" @click="selectType('webhook')">
                                <img class="platform-icon" src="../../../assets/pmd/img/webhook.png">
                                <div class="platform-title ">Webhook Request</div>
                            </div>
                        </div>
                        <div v-if="notification.type=='stream'" class="col-sm-3 no-text-select webhook-platform">
                            <div :class="{'platform-container no-text-select': true, 'selected-platform':notification.subtype == 'woocommerce'}" @click="selectType('woocommerce')">
                                <img class="platform-icon" src="../../../assets/pmd/img/woocommerce.png">
                                <div class="platform-title ">Product Purchase</div>
                            </div>
                        </div>

                        <div v-if="notification.type=='stream'" class="col-sm-3 no-text-select webhook-platform">
                            <div :class="{'platform-container no-text-select': true, 'selected-platform':notification.subtype == 'magento'}" @click="selectType('magento')">
                                <img class="platform-icon p-1" src="../../../assets/pmd/img/magento.png">
                                <div class="platform-title ">Product Purchase</div>
                            </div>
                        </div>
                        <div v-if="notification.type=='stream'" class="col-sm-3 no-text-select webhook-platform">
                            <div :class="{'platform-container no-text-select': true, 'selected-platform':notification.subtype == 'wordpress'}" @click="selectType('wordpress')">
                                <img class="platform-icon p-1" src="../../../assets/pmd/img/wordpress.png">
                                <div class="platform-title ">User Signup</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="step-content pt-0" v-if="notification.type=='stream' | notification.type=='conversions' | (notification.type=='combo' && notification.comboType == 'conversions')">
            <div class="step-panel">
                <div class="title" v-if="showCapture">Where to Capture</div>
                <div class="section" v-if="showCapture">
                    <div class="row">
                        <div class="col-sm-6">
                            <p class="description mb-0 mt-2">Select URL match type
                                <div class="mr-1 help-center-trigger">
                                    <i class="fa fa-question"></i>
                                </div>
                            </p>
                        </div>
                        <div class="col-sm-6">
                            <div class="item float-right text-capitalize">
                                <div class="ps-optionsbox">
                                    <div class="btn-group" role="group">
                                        <button type="button" :class="{'btn btn-radio-option text-capitalize ': true, 'option-selected':notification.matchType == 'simple'}" @click="setMatchType('simple')">
                                           <i class="fa fa-check" v-if="notification.matchType == 'simple'"></i> simple
                                        </button>
                                        <button type="button" :class="{'btn btn-radio-option text-capitalize ': true, 'option-selected':notification.matchType == 'contains'}" @click="setMatchType('contains')">
                                            <i class="fa fa-check" v-if="notification.matchType == 'contains'"></i> contains
                                        </button>
                                        <button type="button" :class="{'btn btn-radio-option text-capitalize ': true, 'option-selected':notification.matchType == 'regex'}" @click="setMatchType('regex')">
                                            <i class="fa fa-check" v-if="notification.matchType == 'regex'"></i> regex
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="section" v-if="showCapture">
                    <p class="description" v-if="showCapture && notification.matchType == 'simple'">
                        <span>Where are you capturing conversions?<br>Type one or more page URLs or copy them directly from your web browser.<br><strong>Each page
                                URL must have an email form field</strong> and ProveSource installed.
                        </span>
                    </p>

                    <div class="ps-tip" v-if="showCapture && notification.matchType == 'simple'">
                        <div class="tip yellow mb-4">
                            <i class="fa fa-info-circle"></i> We treat http://, https://, www and non-www as separate versions of the URL and page.
                        </div>
                    </div>

                    <div class="ps-input-add" v-if="showCapture && notification.matchType == 'simple'">
                        <div class="ps-input-frame">
                            <form name="msgsndr12" data-lpignore="true" autocomplete="msgsndr12" :class="{'ng-pristine  -pattern -minlength -required msgsndr12':true, 'ng-invalid ng-invalid-pattern':errors.has('watchURL')}" @submit="addURL($event);">
                                <input type="text" data-lpignore="true" :class="{'form-control border-green -pattern -minlength -required':true, 'ng-invalid ng-invalid-pattern':errors.has('watchURL')}" name="watchURL" ref="matchURL" placeholder="Page URL" v-validate="{ required: true, regex: /(^https:\/\/|^http:\/\/)(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,256}\b([-a-zA-Z0-9@:%_\+.~#?&amp;\/\/=]*)/ }" v-model="newURL">
                                <button class="btn" type="submit" @click="addURL($event);">
                                    <i class="fa fa-plus"></i>
                                </button>
                                <div v-show="errors.has('watchURL')" class="text-danger pt-1 ">You must add at least one URL. URL is invalid. Make sure to add http:// or https://</div>
                            </form>
                        </div>
                    </div>
                    <div v-if="showCapture && notification.matchType == 'simple'">
                        <span v-for="(url, index) in notificationURLs" :key="url" class="urls-item text-truncate ng-scope">
                            <span class="remove-button" @click="removeURL(index, url)">×</span>
                            <a class="text text-truncate ng-binding" :href="url" target="_blank">{{url}}</a>
                        </span>
                    </div>

                    <!-- Contains & RegEx Section -->
                    <p class="description" v-if="showCapture && (notification.matchType == 'contains' || notification.matchType == 'regex')">
                        <span>Track all page URLs that contain the following expression.
                            <br><strong>Each page URL must have an email form field</strong> and ProveSource installed.
                        </span>
                    </p>
                    <div class="ps-input-add" v-if="showCapture && (notification.matchType == 'contains' || notification.matchType == 'regex')">
                        <div class="ps-input-frame">
                            <form name="msgsndr12" data-lpignore="true" data-ng-submit="addToList(model)" autocomplete="msgsndr12" class="ng-pristine ng-valid ng-valid-pattern ng-valid-minlength ng-valid-required msgsndr12">
                                <input type="text" data-lpignore="true" class="form-control border-green ng-pristine ng-valid ng-empty ng-valid-pattern ng-valid-minlength ng-valid-required ng-touched" name="fieldName" id="add-url" placeholder="Expression" ng-required="isRequired" ng-minlength="1" data-ng-model="model" validate-email="">

                                <!-- <div class="text-danger pt-1 ng-hide" ng-show="addItemForm.$error.required &amp;&amp; !isRegularList">This field is required</div> -->
                                <div v-show="errors.has('fieldName')" class="text-danger pt-1 ">{{ errors.first('fieldName') }}</div>
                            </form>
                        </div>
                    </div>

                </div>
                <div class="section" v-if="showWebhook">
                    <div editing="isEditNotification" show-section="false">

                        <div class="ps-webhook-container " ng-switch-default="">
                            <div class="section" data-ng-init="generateWebhook()">
                                <div class="ps-loader">

                                </div>

                                <div class="">
                                    <!-- <div class="text-success font-weight-bold animated fadeIn ps-msg success">
                                                <i class="fa fa-check-cirle"></i> Your webhook is ready.
                                            </div>
                                            <div class="ps-tip blue">
                                                <div class="tip blue mb-3" ng-style="{'margin-top': ntop ? '-10px' : '0px'}" style="margin-top: -10px;">
                                                    <i class="fa fa-info-circle"></i> Working with Zapier? <a data- class="">Here is a quick guide.</a>
                                                </div>
                                            </div> -->

                                    <p class="mt-2">Call the following endpoint to let us know about an event or conversion that happened on your side
                                        and that is related to this notification, we'll do the rest.</p>
                                </div>

                                <div class="ps-url-input">
                                    <div class="ps-input-frame">
                                        <form name="msgsndr13" data-lpignore="true" autocomplete="msgsndr13" class="ng-pristine  -required -pattern msgsndr13">
                                            <label class="input-title " for="webhook-url">Your Webhook URL</label>
                                            <input show-focus="shouldFocus" type="text" name="fieldName" id="webhook-url" autofocus="" placeholder="" required="required" readonly="readOnly" :value="webhookURL">
                                        </form>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="step-content" v-if="notification.type=='page_visits' | (notification.type=='combo' && notification.comboType == 'visits')">
            <div class=" step-panel">
                <div class="title">Track Visits</div>

                <div class="section">
                    <div class="row">
                        <div class="col-sm-6">
                            <p class="description mb-0 mt-2">Select URL match type
                                <div class="mr-1 help-center-trigger">
                                    <i class="fa fa-question"></i>
                                </div>
                            </p>
                        </div>
                        <div class="col-sm-6">
                            <div class="item float-right text-capitalize">
                                <div class="ps-optionsbox">
                                    <div class="btn-group" role="group">
                                        <button type="button" :class="{'btn btn-radio-option text-capitalize ': true, 'option-selected':notification.matchType == 'simple'}" @click="setMatchType('simple')">
                                            <i class="fa fa-check" v-if="notification.matchType == 'simple'"></i> simple
                                        </button>
                                        <button type="button" :class="{'btn btn-radio-option text-capitalize ': true, 'option-selected':notification.matchType == 'contains'}" @click="setMatchType('contains')">
                                            <i class="fa fa-check" v-if="notification.matchType == 'contains'"></i> contains
                                        </button>
                                        <button type="button" :class="{'btn btn-radio-option text-capitalize ': true, 'option-selected':notification.matchType == 'regex'}" @click="setMatchType('regex')">
                                            <i class="fa fa-check" v-if="notification.matchType == 'regex'"></i> regex
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="section">
                    <p class="description">
                        <span ng-switch-when="simple" class="ng-scope">Which pages would you like us to track?<br>Add one or more page URLs or copy them directly from your web browser.</span><!-- end ngSwitchWhen: -->
                    </p>

                    <div class="ps-tip" v-if="showCapture && notification.matchType == 'simple'">
                        <div class="tip yellow mb-4">
                            <i class="fa fa-info-circle"></i> We treat http://, https://, www and non-www as separate versions of the URL and page.
                        </div>
                    </div>
                    <div class="ps-input-add" v-if="showCapture && notification.matchType == 'simple'">
                        <div class="ps-input-frame">
                            <form name="msgsndr12" data-lpignore="true" autocomplete="msgsndr12" :class="{'ng-pristine  -pattern -minlength -required msgsndr12':true, 'ng-invalid ng-invalid-pattern':errors.has('watchURL')}" @submit="addURL($event);">
                                <input type="text" data-lpignore="true" :class="{'form-control border-green -pattern -minlength -required':true, 'ng-invalid ng-invalid-pattern':errors.has('watchURL')}" name="watchURL" ref="matchURL" placeholder="Page URL" v-validate="{ required: true, regex: /(^https:\/\/|^http:\/\/)(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,256}\b([-a-zA-Z0-9@:%_\+.~#?&amp;\/\/=]*)/ }" v-model="newURL">
                                <button class="btn" type="submit" @click="addURL($event);">
                                    <i class="fa fa-plus"></i>
                                </button>
                                <div v-show="errors.has('watchURL')" class="text-danger pt-1 ">You must add at least one URL. URL is invalid. Make sure to add http:// or https://</div>
                            </form>
                        </div>
                    </div>
                    <div v-if="showCapture && notification.matchType == 'simple'">
                        <span v-for="(url, index) in notificationURLs" :key="url" class="urls-item text-truncate ng-scope">
                            <span class="remove-button" @click="removeURL(index, url)">×</span>
                            <a class="text text-truncate ng-binding" :href="url" target="_blank">{{url}}</a>
                        </span>
                    </div>

                    <!-- Contains & RegEx Section -->
                    <p class="description" v-if="showCapture && (notification.matchType == 'contains' || notification.matchType == 'regex')">
                        <span>Track all page URLs that contain the following expression.
                            <br><strong>Each page URL must have an email form field</strong> and ProveSource installed.
                        </span>
                    </p>
                    <div class="ps-input-add" v-if="showCapture && (notification.matchType == 'contains' || notification.matchType == 'regex')">
                        <div class="ps-input-frame">
                            <form name="msgsndr12" data-lpignore="true" data-ng-submit="addToList(model)" autocomplete="msgsndr12" class="ng-pristine ng-valid ng-valid-pattern ng-valid-minlength ng-valid-required msgsndr12">
                                <input type="text" data-lpignore="true" class="form-control border-green ng-pristine ng-valid ng-empty ng-valid-pattern ng-valid-minlength ng-valid-required ng-touched" name="fieldName" id="add-url" placeholder="Expression" ng-required="isRequired" ng-minlength="1" data-ng-model="model" validate-email="">

                                <!-- <div class="text-danger pt-1 ng-hide" ng-show="addItemForm.$error.required &amp;&amp; !isRegularList">This field is required</div> -->
                                <div v-show="errors.has('fieldName')" class="text-danger pt-1 ">{{ errors.first('fieldName') }}</div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="step-content" v-if="notification.type=='visitors'">
            <div class="step-panel">
                <div class="title">Track Visitors</div>

                <div class="section">
                    <div class="row">
                        <div class="col-sm-12">
                            <p class="description mb-0 mt-3 text-center">
                                <i class="far fa-4x fa-smile"></i>
                                <br>
                                Wow! you don't need to configure anything here, you can move on.
                                <div class="ps-tip">
                                    <div class="tip green mt-2 mb-5" style="margin-top: 10px;text-align:center;">
                                        <i class="fa fa-info-circle"></i>
                                        We will automatically track your website visitors in real time.
                                    </div>
                                </div>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="step-content" v-if="notification.type=='review'">
            <div class="step-panel">
                <div class="title">Track Visitors</div>

                <div class="section">
                    <div class="row">
                        <div class="col-sm-12">
                            <p class="description mb-0 mt-1">Make sure to connect the accounts you want to pull reviews from</p>
                        </div>
                        <div class="col-sm-6 col-md-6">
                            <div class="card-integration">
                                <div class="card-integration--inner">
                                    <MedallionGoogleIcon style="height:100px" alt="Avatar Name" />
                                    <p v-if="!googleConnection">Connect your location's Google Account</p>
                                    <p v-if="googleConnection">Google Account</p>
                                    <button v-if="googleConnection" type="button" class="btn btn-green-lt btn-block" @click.stop="disconectGoogle">Connected!</button>
                                    <button v-if="!googleConnection" type="button" class="btn btn-primary btn-block" @click.stop="connectGoogle">Connect</button>

                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 col-md-6">
                            <div class="card-integration">
                                <div class="card-integration--inner">
                                    <MedallionFacebookIcon style="height:100px" alt="Avatar Name" />
                                    <p v-if="!facebookConnection">Connect your location's Facebook Account</p>
                                    <p v-if="facebookConnection">Facebook Account</p>
                                    <button v-if="!facebookConnection" type="button" class="btn btn-primary btn-block" @click.stop="connectFacebook">Connect</button>
                                    <button v-if="facebookConnection" type="button" class="btn btn-green-lt btn-block" @click.stop="disconnectFacbook">Connected!</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="step-nav-buttons-container">
            <div class="button-container no-text-select">
                <button class="wizard-btn next" :disabled="nextDisabled" @click="saveAndNext();">
                    Next
                    <i class="fa fa-arrow-right"></i>
                </button>
                <button class="wizard-btn back" v-on:click="$emit('go-back', 1)">Back</button>
            </div>
        </div>

    </div>
</template>

<script lang='ts'>
import Vue from "vue";

let cancelGoogleSubscription: () => void;
let cancelFacebookSubscription: () => void;
let openedWindow: Window | null;

import  firebase from 'firebase/app';
import {
    NotificationTemplate,
    OAuth2,
    User,
    Location
} from "@/models";



import { UserState } from '@/store/state_models';

import { mapState } from 'vuex';

import defaults from '@/config';
import MedallionGoogleIcon from '/public/pmd/img/logo_medallions/medallion-google.svg';
import MedallionFacebookIcon from '/public/pmd/img/logo_medallions/medallion-facebook.svg'

declare var $: any;

export default Vue.extend({
    props: {
        notificationId: String,
    },
    components: {
        MedallionGoogleIcon,
        MedallionFacebookIcon,
    },
    data() {
        return {
            notification: {} as NotificationTemplate,
            showCapture: false,
            showWebhook: false,
            webhookURL: "https://api.gohighlevel.com/webhook/78e65aaskdjasldjlu23y9u293",
            newURL: '' as string,
            notificationURLs: [] as string[],
            googleConnection: undefined as OAuth2 | undefined,
            facebookConnection: undefined as OAuth2 | undefined,
            location: {} as Location,
        };
    },
    async created() {
        this.fetchData();
    },
    computed: {
        matchURLs(): string[] {
            return this.notification.matchURLs;
        },
        ...mapState('user', {
            user: (s: UserState) => {
                return s.user ? new User(s.user) : undefined;
            },
        }),
        nextDisabled(): boolean {
            return true;
        },
    },
    watch: {
        'notificationId': function (id) {
            this.fetchData();
        },
    },
    methods: {
        async fetchData() {
            this.notification = await NotificationTemplate.getById(this.notificationId);
            if (this.notification.subtype == "webhook") {
                this.showWebhook = true;
                this.showCapture = false;
            } else if (this.notification.subtype == "form_submission" || this.notification.type == "page_visits" || this.notification.type == "conversions" || this.notification.type == "combo") {
                this.showCapture = true;
            }

            if (cancelGoogleSubscription) {
                cancelGoogleSubscription();
            }

            cancelGoogleSubscription = OAuth2.getByLocationIdAndType(this.notification.locationId, OAuth2.TYPE_GOOGLE).onSnapshot((snapshot:firebase.firestore.QuerySnapshot) => {
                this.googleConnection = snapshot.docs.map((d:firebase.firestore.QueryDocumentSnapshot) => new OAuth2(d))[0];
            });

            if (cancelFacebookSubscription) {
                cancelFacebookSubscription();
            }
            cancelFacebookSubscription = OAuth2.getByLocationIdAndType(this.notification.locationId, OAuth2.TYPE_FACEBOOK).onSnapshot((snapshot:firebase.firestore.QuerySnapshot) => {
                this.facebookConnection = snapshot.docs.map((d:firebase.firestore.QueryDocumentSnapshot) => new OAuth2(d))[0];
            });

            this.location = new Location(await this.$store.dispatch('locations/getById', this.notification.locationId));
        },
        selectType(type: string) {
            this.notification.subtype = type
            if (type == "form_submission") {
                this.showCapture = true;
                this.showWebhook = false;
            } else if (type == "webhook") {
                this.showCapture = false;
                this.showWebhook = true;
            } else {
                this.showCapture = false;
                this.showWebhook = false;
            }
            this.notification.save();
        },
        setMatchType(type: string) {
            this.notification.matchType = type;
        },
        setComboType(type: string) {
            this.notification.comboType = type;
            if (type == "conversions" && this.notification.subtype == "") {
                this.notification.subtype = "form_submission";
            }
        },
        async addURL(event) {
            event.preventDefault();
            console.log("Add URL:")

            let valid = await this.$validator.validateAll();
            console.log("Valid:", valid)

            if (valid && this.notification.matchURLs.indexOf(this.newURL) == -1) {
                this.$refs.matchURL.blur();
                this.notificationURLs.push(this.newURL);
                this.newURL = '';

            }
        },
        removeURL(index: number, url: string) {
            this.notificationURLs.splice(index, 1);
        },
        saveAndNext() {
            this.notification.matchURLs = this.notificationURLs;
            this.$emit('save-next', 3);
        },
        connectGoogle() {
            openedWindow = window.open(
                defaults.baseUrl +
                "/api/gmail/start_oauth?location_id=" +
                this.notification.locationId + "&user_id=" + this.user.id,
                "MyWindow",
                "toolbar=no, menubar=no,scrollbars=no,resizable=no,location=no,directories=no,status=no"
            );
        },
        connectFacebook() {
            openedWindow = window.open(
                defaults.baseUrl +
                "/api/facebook/start_oauth?location_id=" +
                this.notification.locationId,
                "MyWindow",
                "toolbar=no, menubar=no,scrollbars=no,resizable=no,location=no,directories=no,status=no"
            );
        },
        async disconectGoogle() {
            var _self = this;
            if (confirm("Are you sure you want to delete your Google connection?")) {
                if (_self.googleConnection) {
                    _self.googleConnection.removeLocation(_self.notification.locationId);
                    console.log(
                        "googleConnection locations:",
                        Object.keys(_self.googleConnection.locations)
                    );
                    await _self.googleConnection.save();
                    console.log("Google connection saved");
                }
            }
        },
        async disconnectFacbook() {
            if (
                confirm("Are you sure you want to delete your Facebook connection?")
            ) {
                try {
                    if (this.facebookConnection) {
                        this.facebookConnection.removeLocation(this.notification.locationId);
                    }

					await this.$http.delete(`/facebook/${this.location.id}`)
				} catch (err) {
					console.error(err)
				}
            }
        },
    },
    beforeDestroy(): void {
        if (cancelGoogleSubscription) {
            cancelGoogleSubscription();
        }
        if (cancelFacebookSubscription) {
            cancelFacebookSubscription();
        }
    }
})
</script>
