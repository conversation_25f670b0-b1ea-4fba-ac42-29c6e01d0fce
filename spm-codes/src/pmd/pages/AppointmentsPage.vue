<template>
  <div>
    <section class="hl_wrapper">
      <section class="hl_wrapper--inner customers" id="calendarEvents">
        <div class="container-fluid">
          <div class="hl_controls">
            <div class="hl_controls--left">
              <h2>Appointments</h2>
            </div>
            <div class="hl_controls--right">
              <div class="d-flex align-items-center">
                <button
                  v-if="hasAnyUploadError"
                  type="button"
                  class="mr-3 btn btn-primary"
                  @click.stop="uploadAppoitments"
                  id="pg-appt__btn--retry-upload"
                >
                  <i class="fa fa-refresh"></i>
                  Retry upload
                </button>

                <!-- <select
                  class="selectpicker mr-2"
                  title="Calendar"
                  data-width="fit"
                  name="filterCalendar"
                  v-model="filterCalendarId"
                  @change="applyChangeFilter"
                >
                  <option value="all">All Calendars</option>
                  <option
                    v-for="calendar in filterdCalendars"
                    :key="calendar.id"
                    :value="calendar.id"
                    v-text="calendar.name"
                  />
                </select>-->

                <b-dropdown
                  :text="selectedItemText"
                  variant="primary"
                  size="sm"
                  class="mr-2"
                  id="pg-appt__drpdwn--menu-apt"
                >
                  <b-dropdown-header id="dropdown-header-label"
                    >All</b-dropdown-header
                  >
                  <b-dropdown-item-button
                    @click="allAppointments('All Appointments')"
                    >All Appointments</b-dropdown-item-button
                  >

                  <template v-if="hasAnyCalendarProviders">
                    <b-dropdown-header id="dropdown-header-label"
                      >Primary</b-dropdown-header
                    >
                    <b-dropdown-item-button
                      @click="filterMyCalendarAppointments('My Calendar')"
                      >My Calendar</b-dropdown-item-button
                    >
                    <!-- <b-dropdown-divider></b-dropdown-divider> -->

                    <b-dropdown-header
                      v-if="filteredUsers.length > 0"
                      id="dropdown-header-label"
                      >Users</b-dropdown-header
                    >
                    <b-dropdown-item
                      v-for="user in filteredUsers"
                      :key="user.id"
                      @click="filterUserAppointments(user.id, user.name)"
                      >{{ user.name }}</b-dropdown-item
                    >
                    <!-- <b-dropdown-divider></b-dropdown-divider> -->

                    <b-dropdown-header
                      v-if="filteredCalendarProviders.length > 0"
                      id="dropdown-header-label"
                      >Teams</b-dropdown-header
                    >
                    <b-dropdown-item
                      v-for="calendarProvider in filteredCalendarProviders"
                      :key="calendarProvider.id"
                      @click="
                        filterCalendarProviderAppointments(
                          calendarProvider.id,
                          calendarProvider.calendarProviderName
                        )
                      "
                      >{{
                        calendarProvider.calendarProviderName
                      }}</b-dropdown-item
                    >
                  </template>

                  <!-- <b-dropdown-divider></b-dropdown-divider> -->

                  <b-dropdown-header
                    v-if="filterCalendars.length > 0"
                    id="dropdown-header-label"
                    >Calendars</b-dropdown-header
                  >
                  <b-dropdown-item
                    v-for="calendar in filterCalendars"
                    :key="calendar.id"
                    @click="
                      filterCalendarAppointments(calendar.id, calendar.name)
                    "
                    >{{ calendar.name }}</b-dropdown-item
                  >

                  <!-- <b-dropdown-item
                v-for="calendar in filterdCalendars"
                :key="calendar.id"
                @click="calendarId = calendar.id"
                v-text="calendar.name"
                  />-->
                </b-dropdown>

                <select
                  class="selectpicker"
                  title="Status"
                  data-width="fit"
                  name="filterStatus"
                  v-model="filterStatus"
                  id="pg-appt__sel--filter-status"
                >
                  <option value="all">All</option>
                  <option value="confirmed">Confirmed</option>
                  <option value="new">New (Action Required)</option>
                  <option value="cancelled">Cancelled</option>
                  <option value="showed">Showed</option>
                  <option value="noshow">No Show</option>
                  <option value="invalid">Invalid</option>
                </select>

                <select
                  class="selectpicker ml-2"
                  title="Order By"
                  data-width="fit"
                  name="appoinmentOrderBy"
                  v-model="orderBy"
                  id="pg-appt__sel--order-by"
                >
                  <option value="added_desc">Date Added (DESC)</option>
                  <option value="start_date_desc">Start Date (DESC)</option>
                  <option value="start_date_asc_today">
                    Start Date Ascending From Today
                  </option>
                </select>

                <UIButton
                  type="button"
                  class="ml-3"
                  use="primary"
                  @click.stop="addAppointment"
                  id="pg-appt__btn--appt-add"
                >
                  <i class="icon icon-plus mr-2"></i>
                  Schedule appointment
                </UIButton>
              </div>
              <!-- <h6>Sun, Apr 1st, 2018 - Mon, Apr 31st, 2018
                                <i class="icon-calendar text-secondary ml-5 d-none d-lg-inline-block"></i>
              </h6>-->
            </div>
          </div>

          <div class="py-1 px-2 my-2 info-blue --blue">
            <div class="my-1">
              <span class="input-group-addon mx-1">
                <i class="fa fa-exclamation-triangle"></i>
              </span>
              <span class="mx-1 my-2 text-muted"
                >We have moved appointment report under Reporting tab.
                <router-link
                  id="pg-appt__link--appt-reports"
                  :to="{
                    name: 'appointments-reports',
                    params: { location_id: currentLocationId },
                  }"
                  ><u>Click Here</u></router-link
                >
                to navigate.</span
              >
            </div>
          </div>

          <div class="card hl_appointments--table" :class="{'hl_appointments--table-margin': getSideBarVersion == 'v2'}">
            <div class="card-body --no-padding">
              <div class="table-wrap">
                <table class="table">
                  <thead>
                    <tr>
                      <th>Actions</th>
                      <th>Name</th>
                      <th>Requested time</th>
                      <th>Date Added</th>
                      <!-- <th>Type</th>
                      <th>Method</th>-->
                      <th>Calendar</th>
                      <th>Appointment Owner</th>
                      <th>Notes</th>
                    </tr>
                  </thead>

                  <tbody
                    v-infinite-scroll="loadMore"
                    infinite-scroll-disabled="busyOrDone"
                    infinite-scroll-distance="threshold"
                  >
                    <AppointmentRequestCard
                      v-for="calenderEvent in filterdcalendarEvents"
                      v-bind:calenderEvent="calenderEvent"
                      :timezone="timezone"
                      v-bind:key="calenderEvent.id"
                      @editAppointment="
                        modalData = { visible: true, eventId: $event }
                      "
                      :ref="calenderEvent.id"
                      :calendarName="getCalendarName(calenderEvent.calendarId)"
                      :assignedUserName="
                        getUserName(calenderEvent.assignedUserId)
                      "
                      :isAdmin="isAdmin"
                    />
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div
            v-if="calendars.length === 0"
            style="text-align: center; margin-top: 100px"
          >
            You don't have any calendars yet --
            <router-link
              :to="{
                name: 'calendar_settings',
                params: { location_id: currentLocationId },
              }"
              tag="a"
              exact
              >Click here to add one.</router-link
            >
          </div>
        </div>
      </section>
    </section>
    <EditAppointmentModal
      @hidden="doneEditing"
      :values="modalData"
      :isFromAppointmentsPage="true"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Location, Team, Calendar, CalendarEvent, User } from '../../models'
import { UserState } from '../../store/state_models'
import { mapState } from 'vuex'
import firebase from 'firebase/app'
import * as moment from 'moment-timezone'
const AppointmentRequestCard = () =>
  import('../components/AppointmentRequestCard.vue')
const EditAppointmentModal = () =>
  import('../components/EditAppointmentModal.vue')
const store = require('store')
import { sortBy } from 'lodash'

let cancelCalendarEventSubscription: () => void
// let cancelUsersSubscription: () => void

export default Vue.extend({
  name: 'LocationAppointmentPage',
  components: {
    AppointmentRequestCard,
    EditAppointmentModal,
  },
  data() {
    return {
      currentLocationId: '',
      currentLocation: {} as Location,
      selectedItemText: 'All Appointments' as string,
      filterParams: {} as {
        assigned_user_id?: string
        calendar_provider_id?: string
        calendar_id?: string
      },
      // users: [] as User[],
      calendarEvents: [] as CalendarEvent[],
      modalData: {},
      busyOrDone: false,
      limit: 25,
      threshold: 10 * 71,
      timezone: '',
      navigate: true,
      firstSnapshot: true,
      orderBy: '',
      filterStatus: 'all',
      filterCalendarId: 'all',
      seqCalling: 0,
    }
  },
  async created() {
    await this.refreshLocationData()
  },
  beforeDestroy() {
    if (cancelCalendarEventSubscription) cancelCalendarEventSubscription()
    // if (cancelUsersSubscription) cancelUsersSubscription()
  },
  computed: {
    isAdmin() {
      return this.user.role === 'admin'
    },
    filteredUsers() {
      return this.isAdmin ? this.locationTeamMembers : []
    },
    vuexCalendarProvider() {
      return this.$store.getters['teams/calendarProviders']
    },
    calendarProviders() {
      return this.vuexCalendarProvider.map(p => new Team(lodash.cloneDeep(p)))
    },
    hasAnyCalendarProviders() {
      return this.calendarProviders.length > 0
    },
    filteredCalendarProviders() {
      return this.isAdmin
        ? this.calendarProviders
        : this.calendarProviders.filter(x => x.userIds.includes(this.user.id))
    },
    providerIdwiseProviderName() {
      const _providerIdwiseProviderName = {}
      this.calendarProviders.forEach(p => {
        _providerIdwiseProviderName[p.id] = p.calendarProviderName
      })
      return _providerIdwiseProviderName
    },
    vuexCalendars() {
      return this.$store.state.calendars.calendars
    },
    calendars() {
      const _calendars = this.vuexCalendars.map(
        s => new Calendar(lodash.cloneDeep(s))
      )

      const v2Calendars = _calendars.filter(key => !key.providerId)
      const v3Calendars = _calendars.filter(key => key.providerId)
      v3Calendars.forEach(cal => {
        cal.name = `${cal.name} (${
          this.providerIdwiseProviderName[cal.providerId]
        })`
      })

      const sortedv3Calendars = sortBy(v3Calendars, cal =>
        this.providerIdwiseProviderName[cal.providerId]
          ? this.providerIdwiseProviderName[cal.providerId].toUpperCase()
          : ''
      )

      return [...v2Calendars, ...sortedv3Calendars].filter(x => x.isActive)
    },
    filterCalendars() {
      if (this.user) {
        if (this.isAdmin || !this.user.permissions.assigned_data_only)
          return this.calendars
        if (this.user.permissions.assigned_data_only)
          return this.calendars.filter(
            cal =>
              this.user.userCalendar[this.currentLocationId] === cal.id ||
              (cal.teamMembers &&
                cal.teamMembers.find(m => m.user_id === this.user.id))
          )
      }
      return []
    },
    filterCalendarIds() {
      return this.filterCalendars.map(x => x.id)
    },
    hasAnyUploadError(): boolean {
      return this.calendarEvents.findIndex(x => x.uploadError) !== -1
    },
    filterdcalendarEvents(): CalendarEvent[] {
      // return this.calendarEvents.filter(x => !this.user || !this.user.calendar || this.user.calendar === x.calendarId)
      const isFilterParamsEmpty = lodash.isEmpty(this.filterParams)
      return this.calendarEvents.filter(
        x =>
          !x.isRecurring &&
          (this.filterStatus === 'all' ||
            x.appoinmentStatus === this.filterStatus) &&
          (isFilterParamsEmpty ||
            (this.filterParams.assigned_user_id &&
              x.assignedUserId === this.filterParams.assigned_user_id) ||
            (this.filterParams.calendar_provider_id &&
              x.calendarProviderId ===
                this.filterParams.calendar_provider_id) ||
            (this.filterParams.calendar_id &&
              x.calendarId === this.filterParams.calendar_id)) &&
          (!this.user.permissions.assigned_data_only ||
            this.user.role === 'admin' ||
            x.createdByUserId === this.user.id ||
            (this.user.permissions.assigned_data_only === true &&
              (this.user.id === x.assignedUserId ||
                this.user.userCalendar[this.currentLocationId] ===
                  x.calendarId)))
      )
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    locationTeamMembers() {
      return this.$store.state.users.users.map(u => new User(u))
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  methods: {
    async refreshLocationData() {
      this.currentLocationId = this.$router.currentRoute.params.location_id
      const location = new Location(
        await this.$store.dispatch('locations/getById', this.currentLocationId)
      )
      this.currentLocation = location
      this.timezone = await location.getTimeZone()

      if (!this.timezone)
        this.$uxMessage(
          'warning',
          'Please set timezone for your location in Company Settings to view your appointments.'
        )

      await Promise.all([
        this.$store.dispatch('teams/syncAll', this.currentLocationId)
      ])
    },
    getCalendarName(calendarId: string) {
      const calendar = this.calendars.find(x => x.id === calendarId)
      return calendar && calendar.name
    },
    getUserName(userId: string) {
      const _user = this.locationTeamMembers.find(x => x.id === userId)
      return _user && _user.name
    },
    // async fetchLocationUsers() {
    //   if (this.currentLocationId) {
    //     cancelUsersSubscription = (
    //       await User.fetchAllLocationUsers(this.currentLocationId)
    //     ).onSnapshot(snapshot => {
    //       this.users = snapshot.docs.map(d => new User(d))
    //     })
    //   }
    // },
    allAppointments(selectedItemText: string) {
      this.selectedItemText = selectedItemText
      this.filterParams = {}
      this.applyChangeFilter()
    },
    filterMyCalendarAppointments(selectedItemText: string) {
      this.filterUserAppointments(this.user.id, selectedItemText)
    },
    filterUserAppointments(assigned_user_id: string, selectedItemText: string) {
      this.selectedItemText = selectedItemText
      this.filterParams = { assigned_user_id }
      this.applyChangeFilter()
    },
    filterCalendarProviderAppointments(
      calendar_provider_id: string,
      selectedItemText: string
    ) {
      this.selectedItemText = selectedItemText
      this.filterParams = { calendar_provider_id }
      this.applyChangeFilter()
    },
    filterCalendarAppointments(calendar_id: string, selectedItemText: string) {
      this.selectedItemText = selectedItemText
      this.filterParams = { calendar_id }
      this.applyChangeFilter()
    },
    async fetchData() {
      if (cancelCalendarEventSubscription) cancelCalendarEventSubscription()
      this.firstSnapshot = true
      cancelCalendarEventSubscription = CalendarEvent.fetchAllBookedcalendarEvents(
        this.currentLocationId
      )
        .limit(5)
        .onSnapshot((snapshot: firebase.firestore.QuerySnapshot) => {
          if (!this.firstSnapshot) {
            for (let i = snapshot.docChanges().length - 1; i >= 0; i--) {
              const docChange = snapshot.docChanges()[i]
              const newEvent = new CalendarEvent(docChange.doc)
              let oldEventIndex = this.calendarEvents.findIndex(
                x => x.id === newEvent.id
              )
              if (oldEventIndex > -1 && docChange.type === 'removed') {
                // this.calendarEvents.splice(oldEventIndex, 1)
              } else if (
                (docChange.type === 'added' || docChange.type === 'modified') &&
                newEvent
              ) {
                if (oldEventIndex > -1) {
                  this.calendarEvents.splice(oldEventIndex, 1)
                }

                //If appointment is deleted, do not add it back to the list
                if (newEvent.deleted) continue
                const sortedIndex = this.sortedIndex(newEvent)
                if (
                  sortedIndex >= 0 &&
                  sortedIndex < this.calendarEvents.length
                ) {
                  this.calendarEvents.splice(sortedIndex, 0, newEvent)
                }
              }
            }
          }
          this.firstSnapshot = false
        })
    },
    sortedIndex(element: CalendarEvent): number {
      return lodash.sortedIndexBy(
        this.calendarEvents,
        element,
        (value: CalendarEvent) => {
          if (this.orderBy === 'start_date_asc_today') {
            return value.startTime.valueOf()
          } else if (this.orderBy === 'start_date_desc') {
            return -value.startTime.valueOf()
          } else if (this.orderBy === 'added_desc') {
            return -value.dateAdded.valueOf()
          }
        }
      )
    },
    uploadAppoitments() {
      const calendarEvents = this.calendarEvents.filter(x => x.uploadError)

      calendarEvents.map(e => e.upload())
    },
    doneEditing(response: boolean) {
      this.modalData = {}
    },
    addAppointment() {
      this.modalData = { visible: true }
    },
    applyChangeFilter() {
      // When switching the calendar ID we need it attempt a loadMore because there might not be enough to allow a scroll
      if (
        !lodash.isEmpty(this.filterParams) &&
        this.filterdcalendarEvents.length < 10
      ) {
        this.busyOrDone = false
        this.loadMore()
      }
    },
    async loadMore() {
      this.seqCalling++
      const seqCalling = this.seqCalling
      if (!this.orderBy) {
        const orderBy = store.get(
          `appointment-orderby-${this.currentLocationId}`
        )
        if (orderBy) {
          this.orderBy = orderBy
        } else {
          this.orderBy = 'added_desc'
        }
      }

      new Promise<CalendarEvent[]>(async (resolve, reject) => {
        this.busyOrDone = true
        let query: firebase.firestore.Query
        if (this.orderBy == 'added_desc') {
          query = await CalendarEvent.fetchAllBookedcalendarEventsByDateAddedDesc(
            this.currentLocationId
          )
        } else if (this.orderBy == 'added_asc') {
          query = await CalendarEvent.fetchAllBookedcalendarEventsByDateAddedAsc(
            this.currentLocationId
          )
        } else if (this.orderBy == 'date_updated') {
          query = await CalendarEvent.fetchAllBookedcalendarEvents(
            this.currentLocationId
          )
        } else if (this.orderBy == 'start_date_asc_today') {
          if (!this.timezone) await this.refreshLocationData() //getLocationData Calling this function to get Location Timezone

          query = await CalendarEvent.fetchAllBookedcalendarEventsByDateAsc(
            this.currentLocationId
          ).where(
            'start_time',
            '>=',
            //moment()
            //  .utc()
            // .startOf('day')
            // .toDate()
            moment().tz(this.timezone).startOf('day').toDate()
          )
        } else if (this.orderBy == 'start_date_asc') {
          query = await CalendarEvent.fetchAllBookedcalendarEventsByDateAsc(
            this.currentLocationId
          )
        } else {
          query = await CalendarEvent.fetchAllBookedcalendarEventsByDateDesc(
            this.currentLocationId
          )
        }
        if (this.calendarEvents.length > 0) {
          const last = this.calendarEvents[this.calendarEvents.length - 1]
          query = query.startAfter(last.snapshot)
        }
        const snapshot = await query.limit(this.limit).get()
        resolve(snapshot.docs.map(d => new CalendarEvent(d)))
      })
        .then((calendarEvents: CalendarEvent[]) => {
          if (this.seqCalling !== seqCalling) {
            return
          }
          this.calendarEvents.push.apply(this.calendarEvents, calendarEvents)
          if (calendarEvents.length === this.limit) this.busyOrDone = false
        })
        .catch(err => {})
        .finally(() => {
          if (this.firstSnapshot) {
            this.fetchData()
          }
        })
    },
    navigateToAppointment() {
      if (this.navigate && this.$router.currentRoute.params.appointment_id) {
        const element = this.$refs[
          this.$router.currentRoute.params.appointment_id
        ]
        if (element) {
          element[0].$el.scrollIntoView()
          this.navigate = false
        }
      }
    },
  },
  watch: {
    '$route.params.location_id': async function(id) {
      this.currentLocationId = id
      this.busyOrDone = false
      this.calendarEvents = []
      this.firstSnapshot = true

      this.loadMore()

      await Promise.all([
        this.$store.dispatch('teams/syncAll', id)
      ])

    },
    '$route.params.appointment_id': function (id) {
      this.navigate = true
      this.navigateToAppointment()
    },
    orderBy(val, oldVal) {
      if (!oldVal) return

      this.busyOrDone = false
      this.calendarEvents = []
      this.firstSnapshot = true
      this.loadMore()
      store.set(`appointment-orderby-${this.currentLocationId}`, this.orderBy)
    },
    async currentLocationId() {
      await this.refreshLocationData()
    },
  },
  async mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  updated() {
    this.navigateToAppointment()

    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
})
</script>
<style lang="scss">
.previous-version-page-flag-wrapper {
  border: 1px solid #ebebeb;
  box-sizing: border-box;
  border-radius: 3px;
}
.dropdown-header {
  margin-top: 14px;
  padding-left: 14px;
  font-size: 0.8rem;
  font-weight: bold;
  line-height: 16px;
  text-transform: uppercase;
  color: #92a0b0;
}
#calenderEvents {
  .hl_datepicker {
    padding: 0 20px;
    .field {
      .field-input {
        height: auto !important;
        padding: 0 !important;
        color: #188bf6 !important;
        text-align: center;
        border: none !important;
        background: transparent !important;
      }
    }
  }
}
.hl_appointments--table-margin {
  margin-bottom: 100px;
}
</style>
