<!-- prettier-ignore -->
<template>
  <div class="more-info more-detail-animation d-flex w-100 justify-content-end">
   <template v-if="exportStats">
      <div class="more-info-data text-center space-apart">
        <span class="--light h5 mx-2">{{exportStats.label}}</span>
         <span class="--green h5 my-0 py-0">
           {{exportStats.quantity | formatNumberNoDecimal}}
         </span>
      </div>
    </template>
   <template v-else-if="mergedStats">
      <div class="more-info-data text-center space-apart">
        <span class="--light h5 mx-2">{{mergedStats.label}}</span>
         <span class="--green h5 my-0 py-0">
           {{mergedStats.quantity | formatNumberNoDecimal}}
         </span>
      </div>
    </template>
    <template v-else-if="eventStats">
      <div class="more-info-data text-center space-apart">
        <!--stats-clickable"  @click="openBulkWorkerStats('total')"-->
         <div><span><wbr></span></div>
         <div class="--purple h5 my-0 py-0">
           {{eventStats.total | formatNumberNoDecimal}}
          </div>
        <div class="--dark">Total</div>
      </div>
      <!-- <div class="more-info-data text-center space-apart stats-clickable" @click="openSmsStatsDetailModal('delivered')">
        <div>
          <span class="--green h5 stats-clickable">{{getPercentage(eventStats.delivered, eventStats.count) + '%'}}</span>
        </div>
        <div class="--purple">{{eventStats.delivered | formatNumberNoDecimal}}</div>
        <div class="--dark" >Delivered </div>
      </div> -->
      <!-- <div class="more-info-data text-center space-apart stats-clickable" @click="openSmsStatsDetailModal('clicked')">
        <div><span class="--yellow stats-clickable h5" >{{getPercentage(eventStats.clicked, eventStats.count) + '%'}}</span></div>
        <div class="--purple">{{eventStats.clicked | formatNumberNoDecimal}}</div>
        <div class="--dark" >Clicked</div>
      </div> -->
      <!-- <div class="more-info-data text-center space-apart stats-clickable" @click="openSmsStatsDetailModal('failed')">
        <div class="--red">
          <span class="h5">{{getPercentage(eventStats.failed, eventStats.count) + '%'}}</span>
        </div>
        <div class="--purple">{{eventStats.failed | formatNumberNoDecimal}}</div>
        <div class="--dark" >Failed</div>
      </div> -->
      <div class="more-info-data text-center space-apart" v-if="opType !== 'bulk-import'" :class="{'stats-clickable': eventStats && !eventStats.noDrillDown}"  @click="showStats('success')">
        <div>
          <span class="--green h5" >{{getPercentage(eventStats.success, eventStats.total) + '%'}}</span>
        </div>
        <div class="--purple">{{eventStats.success | formatNumberNoDecimal}}</div>
        <div class="--dark" >Successful</div>
      </div>
      <div class="more-info-data text-center space-apart" v-if="opType === 'bulk-import'" :class="{'stats-clickable': eventStats && !eventStats.noDrillDown}"  @click="showStats('created')">
        <div>
          <span class="--green h5" >{{getPercentage(eventStats.created, eventStats.total) + '%'}}</span>
        </div>
        <div class="--purple">{{eventStats.created | formatNumberNoDecimal}}</div>
        <div class="--dark" >Created</div>
      </div>
      <div class="more-info-data text-center space-apart" v-if="opType === 'bulk-import'" :class="{'stats-clickable': eventStats && !eventStats.noDrillDown}"  @click="showStats('updated')">
        <div>
          <span class="--green h5" >{{getPercentage(eventStats.updated, eventStats.total) + '%'}}</span>
        </div>
        <div class="--purple">{{eventStats.updated | formatNumberNoDecimal}}</div>
        <div class="--dark" >Updated</div>
      </div>
      <div class="more-info-data text-center space-apart" :class="{'stats-clickable': eventStats && !eventStats.noDrillDown}"  @click="openBulkWorkerStats('error')">
        <div class="--red">
          <span class="h5">{{getPercentage(eventStats.error, eventStats.total) + '%'}}</span>
        </div>
        <div class="--purple">{{eventStats.error | formatNumberNoDecimal}}</div>
        <div class="--dark" >Error</div>
      </div>

    </template>

    <BulkActionItemsReport v-if="bulkReq && showBulkWorkerStats"
                             v-on:closed="closeListReport"
                            :showError="this.filter === 'error'"
                            :showSuccess="this.filter === 'success'"
                            :description="bulkReq.opSpecs && bulkReq.opSpecs.description || ''"
                            :bulkReqId="bulkReq.id"/>
    <NewEmailStatsModal
          v-if="showEmailStatsDetailModal"
          option='total'
          modalTitle="Email Stats"
          :bulk_req='bulkReq'
          :bulk_req_id='bulkReqId || bulkReq.id'/>
    <NewSMSStatsModal
          v-if="showSmsStatsDetailModal"
          option='total'
          modalTitle="SMS Stats"
          :bulk_req='bulkReq'
          :bulk_req_id='bulkReqId || bulkReq.id'/>
    <!-- <SmsStatsDetailModal
            v-if="showSmsStatsDetailModal"
            :option="filter"
            @closeSmsStatsDetailModal="showSmsStatsDetailModal = !showSmsStatsDetailModal"
            modalTitle="SMS Stats"
            :bulk_req_id="bulkReqId || (bulkReq && bulkReq.id)"/> -->
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import SmsStatsDetailModal from '../../components/SmsStatsDetailModal.vue'
import BulkActionItemsReport from './BulkActionItemsReport.vue'
import NewEmailStatsModal from './NewEmailStatsModal.vue'
import NewSMSStatsModal from './NewSMSStatsModal.vue'

export default Vue.extend({
  name: 'BulkActionStatsPanel',
  props: ['bulkReqId', 'bulkReq'],
  data() {
    return {
      showBulkWorkerStats: false,
      showSmsStatsDetailModal: false,
      showEmailStatsDetailModal: false,
      filter: 'total',
      eventStats: this.bulkReq.eventStatsV2 || null,
      exportStats: null as { label: string; quantity: number } | null,
      mergedStats: null as { label: string; quantity: number } | null,
      opType: null as string | null,
    }
  },
  components: {
    BulkActionItemsReport,
    NewEmailStatsModal,
    NewSMSStatsModal,
  },
  watch: {},
  methods: {
    async openSmsStatsDetailModal(filter: string) {
      this.filter = filter
      this.showSmsStatsDetailModal = true
    },
    openBulkWorkerStats(filter: string) {
      if(!this.eventStats) return;
      if (this.eventStats.noDrillDown) return;
      this.filter = filter
      this.showBulkWorkerStats = true
    },
    showStats(filter: string) {
      this.filter = filter
      if (!this.bulkReq) return;
      if(!this.eventStats) return;
      if (this.eventStats.noDrillDown) return;
      const opType = (
        (this.bulkReq && this.bulkReq.operationType) ||
        ''
      ).toLowerCase()
      if (opType === 'bulk-email') {
        this.showEmailStatsDetailModal = true
      } else if (opType === 'bulk-sms') {
        this.showSmsStatsDetailModal = true
      } else this.showBulkWorkerStats = true
    },
    getPercentage(value: number, total: number) {
      // console.log(value ? ((value / total) * 100).toFixed(2):0)
      return value ? ((value / total) * 100).toFixed(2) : 0
    },
    async closeListReport() {
      this.showBulkWorkerStats = false
      this.showEmailStatsDetailModal = false
      this.showSmsStatsDetailModal = false
      this.filter = ''
    },
  },
  mounted() {
    if (!this.bulkReq) return
    this.opType = this.bulkReq?.operationType?.toLowerCase()
    // if (opType === 'bulk-export') {
    //   this.exportStats = {
    //     label: 'Total Exported',
    //     quantity: this.bulkReq?.opSpecs?.total,
    //   }
    // } else if (opType === 'bulk-merge-contact') {
    //   this.mergedStats = {
    //     label: 'Total Merged',
    //     quantity: this.bulkReq?.opSpecs?.contactIds?.length,
    //   }
    // }
  },
  // async created() {},
  // async mounted() {}
  // async beforeDestroy() {},
})
</script>

<style scoped>
.space-apart {
  margin-right: 45px;
  margin-left: 45px;
}
.fa-phone {
  color: #188bf6;
  margin-right: 10px;
  font-size: 20px;
}
.email-stats__single {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 1rem;
  color: #2a3135;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}

.more-detail-animation {
  max-height: 87px;
  overflow: hidden;
  transition: max-height 0.5s ease-out;
}

.stats-clickable {
  cursor: pointer;
}
</style>
