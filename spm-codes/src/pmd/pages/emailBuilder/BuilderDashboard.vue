<template>
  <div class="hl_wrapper" :class="{'hl_html_builder': getSideBarVersion == 'v2'}">
    <GridLoader v-show="loader" color="#37ca37" size="15px" class="email-builder--loader" style="margin-top:100px;"/>
    <!-- <moon-loader v-show="loader" color="#37ca37" size="50px" class="email-builder--loader"/> -->
    <div id="gjs" v-show="!loader"></div>
    <div
      class="modal fade"
      ref="modal"
      id="client-checkin--modal"
      tabindex="-1"
      role="dialog"
      aria-labelledby="client-checkin--modalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <div class="modal-header--inner">
              <h5 class="modal-title" id="client-checkin--modalLabel">
                <i class="far fa-envelope --green fa-lg"></i>&nbsp;Save Template
              </h5>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
          </div>
          <div class="modal-body">
            <div class="modal-body--inner">
              <div class="form-group">
                <UITextInputGroup label="Template Name:" type="text" data-lpignore="true" v-model="emailBuilder.name" />
              </div>
              <div class="form-group">
                <label>Media Query Support( only if you are using manually)</label>
                <UICheckbox
                  id="account-buffer-tgl"
                  v-model="isMediaQueryPresent"
                  @change="isMediaQueryPresent !=isMediaQueryPresent"
                />
                <label class="tgl-btn" for="account-buffer-tgl"></label>
              </div>

              <div class="modal-buttons d-flex align-items-center justify-content-between">
                <UIButton
                  type="button"
                  @click="saveBuilderTemplate"
                  :disabled="saving"
                  :loading="saving"
                >{{saving ? 'Saving..': 'Save' }}</UIButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script  lang="ts">
import Vue from 'vue'
// @ts-ignore
import grapesjs from 'grapesjs'
import 'grapesjs-preset-newsletter'
import 'grapesjs-plugin-ckeditor'
// @ts-ignore
import GrapeJsCustomCode from 'grapesjs-custom-code'
// import ckeditor from 'ckeditor'

import '../../../../node_modules/grapesjs/dist/css/grapes.min.css'
import '../../../../node_modules/grapesjs-preset-newsletter/dist/grapesjs-preset-newsletter.css'

import firebase from 'firebase/app'
import { getTagOptions } from '@/util/merge_tags'
import { MediaFiles, EmailBuilder, User } from '@/models'
import { UserState } from '@/store/state_models'
import { v4 as uuid } from 'uuid'
import { getDefaultTemplate } from '@/util/email_template'
import {
  uploadPathEmailBuilder,
  uploadJsonToStorage
} from '@/util/email_builder_helper'

let customFieldValues = [] as { [key: string]: any }
import GridLoader from 'vue-spinner/src/GridLoader.vue'

export default Vue.extend({
  name: 'email-builder-dashboard',
  components: {
    GridLoader
  },
  data() {
    return {
      editor: null,
      LandingPage: {
        html: null,
        css: null,
        components: null,
        style: null
      },
      templateId: 1,
      currentLocationId: '',
      builderId: '',
      mediaFiles: undefined as MediaFiles[] | undefined,
      emailBuilder: {} as EmailBuilder,
      firstInitalization: true,
      isMediaQueryPresent: false,
      saving: false,
      loader: false
    }
  },
  computed: {
    user() {
      const user: UserState = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.builderId = this.$router.currentRoute.params.builder_id
    customFieldValues = await getTagOptions()
    // this.$nextTick(function () {
    // 	this.initalize()
    // })
  },
  mounted() {
    this.initalize()
  },
  watch: {
    '$route.params.location_id': {
      handler: function(newLocationId, oldLocationId) {
        this.currentLocationId = newLocationId
        this.$router.push({
          name: 'email_builder_list',
          params: {
            location_id: newLocationId
          }
        })
      }
    },
    '$route.params.builder_id': {
      handler: function(id) {
        this.builderId = id
        this.emailBuilder = {} as EmailBuilder
        if (this.builderId) this.fetchBuilderTemplate()
      }
    }
  },
  methods: {
    async initalize() {
      var self = this
      this.editor = grapesjs.init({
        container: '#gjs',
        autorender: 0,
        // height: '100%',
        noticeOnUnload: false,
        components: this.LandingPage.components || this.LandingPage.html,
        style: this.LandingPage.style || this.LandingPage.css,
        plugins: [
          'gjs-preset-newsletter',
          'gjs-plugin-ckeditor',
          GrapeJsCustomCode
        ],
        pluginsOpts: {
          'gjs-preset-newsletter': {
            modalLabelImport: 'Paste all your code here below and click import',
            modalLabelExport: 'Copy the code and use it wherever you want',
            codeViewerTheme: 'material',
            //defaultTemplate: templateImport,
            importPlaceholder:
              '<table class="table"><tr><td class="cell">Hello world!</td></tr></table>',
            cellStyle: {
              'font-size': '12px',
              'font-weight': 300,
              'vertical-align': 'top',
              margin: 0,
              padding: 0,
              'overflow-wrap': 'break-word',
              'word-wrap': 'break-word',
              hyphens: 'auto'
            },
            tableStyle: {
              height: '150px',
              margin: '0 auto 10px auto',
              padding: '5px 5px 5px 5px',
              width: '100%',
              'overflow-wrap': 'break-word',
              'word-wrap': 'break-word',
              hyphens: 'auto'
            }
          },
          'gjs-plugin-ckeditor': {
            position: 'center',
            options: {
              language: 'en',
              skin: 'moono-dark',
              enterMode: CKEDITOR.ENTER_BR,
              toolbar: [
                {
                  name: 'document',
                  groups: ['mode', 'document', 'doctools'],
                  items: [
                    'Source',
                    '-',
                    'Save',
                    'NewPage',
                    'Preview',
                    'Print',
                    '-',
                    'Templates'
                  ]
                },
                {
                  name: 'clipboard',
                  groups: ['clipboard', 'undo'],
                  items: [
                    'Cut',
                    'Copy',
                    'Paste',
                    'PasteText',
                    'PasteFromWord',
                    '-',
                    'Undo',
                    'Redo'
                  ]
                },
                {
                  name: 'editing',
                  groups: ['find', 'selection', 'spellchecker'],
                  items: ['Find', 'Replace', '-', 'SelectAll', '-', 'Scayt']
                },
                {
                  name: 'forms',
                  items: [
                    'Form',
                    'Checkbox',
                    'Radio',
                    'TextField',
                    'Textarea',
                    'Select',
                    'Button',
                    'ImageButton',
                    'HiddenField'
                  ]
                },
                '/',
                {
                  name: 'basicstyles',
                  groups: ['basicstyles', 'cleanup'],
                  items: [
                    'Bold',
                    'Italic',
                    'Underline',
                    'Strike',
                    'Subscript',
                    'Superscript',
                    '-',
                    'RemoveFormat'
                  ]
                },
                {
                  name: 'paragraph',
                  groups: ['list', 'indent', 'blocks', 'align', 'bidi'],
                  items: [
                    'NumberedList',
                    'BulletedList',
                    '-',
                    'Outdent',
                    'Indent',
                    '-',
                    'Blockquote',
                    'CreateDiv',
                    '-',
                    'JustifyLeft',
                    'JustifyCenter',
                    'JustifyRight',
                    'JustifyBlock',
                    '-',
                    'BidiLtr',
                    'BidiRtl',
                    'Language'
                  ]
                },
                { name: 'links', items: ['Link', 'Unlink', 'Anchor'] },
                {
                  name: 'insert',
                  items: [
                    'Table',
                    'HorizontalRule',
                    'Smiley',
                    'SpecialChar',
                    'PageBreak',
                    'Iframe'
                  ]
                },
                '/',
                {
                  name: 'styles',
                  items: ['Styles', 'Format', 'Font', 'FontSize']
                },
                { name: 'colors', items: ['TextColor', 'BGColor'] },
                { name: 'tools', items: ['Maximize', 'ShowBlocks'] },
                { name: 'others', items: ['-'] },
                { name: 'my-combo', items: ['my-combo'] }
              ],
              on: {
                pluginsLoaded: function() {
                  const editor: any = this
                  const config = editor.config
                  editor.ui.addRichCombo('my-combo', {
                    label: 'Custom Values',
                    title: 'Custom Values',
                    toolbar: 'basicstyles,0',
                    panel: {
                      css: [CKEDITOR.skin.getPath('editor')].concat(
                        config.contentsCss
                      ),
                      multiSelect: false,
                      attributes: {
                        'aria-label': 'Custom Values'
                      }
                    },
                    init: function() {
                      // this.startGroup("My Dropdown 1");
                      // this.add("foo", "test!");
                      // this.add("bar", "test1!");

                      for (var i = 0; i < customFieldValues.length; i++) {
                        this.startGroup(customFieldValues[i].text)
                        for (
                          let j = 0;
                          j < customFieldValues[i].menu.length;
                          j++
                        ) {
                          let data = customFieldValues[i].menu[j]
                          this.add(data.value, data.text)
                        }
                      }
                    },
                    onClick: function(value: any) {
                      editor.focus()
                      editor.fire('saveSnapshot')
                      editor.insertHtml(value)
                      editor.fire('saveSnapshot')
                    }
                  })
                }
              }
            }
          },
          GrapeJsCustomCode: {
            blockLabel: 'Custom Code',
            blockCustomCode: {},
            propsCustomCode: {},
            placeholderContent: '<span>Insert here your custom code</span>',
            toolbarBtnCustomCode: {},
            placeholderScript: `<div style="pointer-events: none; padding: 10px;">
                <svg viewBox="0 0 24 24" style="height: 30px; vertical-align: middle;">
                  <path d="M13 14h-2v-4h2m0 8h-2v-2h2M1 21h22L12 2 1 21z"></path>
                  </svg>
                Custom code with <i>&lt;script&gt;</i> can't be rendered on the canvas
              </div>`,
            modalTitle: 'Insert your code',
            codeViewOptions: {},
            buttonLabel: 'Save',
            commandCustomCode: {}
          }
        },
        blockManager: {},
        storageManager: {
          id: 'gjs-',
          type: 'local',
          autosave: false,
          autoload: false,
          // contentTypeJson: true,
          contentTypeHTML: true
        },
        assetManager: {
          disableUpload: false,
          uploadFile: this.uploadAsset
        }
      })
      // this.editor.on('storage:load', function(e) {
      // console.log('Loaded ', e);
      // })
      // this.editor.on('storage:store', function(e) {
      // console.log('Stored ', e);
      // })
      // var blockManager = this.editor.BlockManager;
      // 'my-first-block' is the ID of the block
      // blockManager.add('divider-line', {
      // 	label: 'Simple block',
      // 	content: '<div class="my-block">This is a simple block</div>',
      // // });
      // const block = blockManager.getAll();
      // const test = blockManager.get('divider')
      // console.log(JSON.stringify(test));

      this.editor.BlockManager.get('divider').set({
        content: `<table style="width: 100%; margin-top: 10px; margin-bottom: 10px;">
        <tr>
        <td class="divider"></td>
        </tr>
        </table>
        <style>
        .divider {background-color: rgba(0, 0, 0, 0.0);}
        </style>`
      })
      this.editor.BlockManager.get('image').set({
        content: {
          type: 'image',
          style: {
            color: 'black',
            'max-width': '100%',
            'max-height': '100%'
          },
          activeOnRender: 1
        }
      })

      this.editor.BlockManager.get('button').set({
        content: `<a class="button" style="
                font-family: Roboto, system, -apple-system, BlinkMacSystemFont, '.SFNSDisplay-Regular', 'Helvetica Neue', Helvetica, Arial, sans-serif;
                background-color: #37ca37;
                font-size: 0.875rem;
                font-weight: 500;
                color: #fff;
                padding: 11px 20px;
                -webkit-transition: all 0.2s ease-in-out 0s;
                transition: all 0.2s ease-in-out 0s;
                min-width: 85px;
                border-radius: 0.3125rem;
                border: none;
                  ">Button</a>`
      })
      // cutsom blocl
      var mdlClass = 'gjs-mdl-dialog-sm'
      var pnm = this.editor.Panels
      var cmdm = this.editor.Commands
      var md = this.editor.Modal

      pnm.addButton('options', [
        {
          id: 'undo',
          className: 'fa fa-undo',
          attributes: { title: 'Undo' },
          command: function() {
            self.undo()
          }
        },
        {
          id: 'redo',
          className: 'fa fa-repeat',
          attributes: { title: 'Redo' },
          command: function() {
            self.redo()
          }
        },
        {
          id: 'clear-all',
          className: 'fa fa-trash',
          attributes: { title: 'Clear canvas' },
          command: {
            run: function(editor: any, sender: any) {
              sender && sender.set('active', false)
              if (confirm('Are you sure to clean the canvas?')) {
                editor.DomComponents.clear()
                // TODO
                // setTimeout(function () {

                // 	localStorage.clear()
                // }, 0)
              }
            }
          }
        },
        {
          id: 'send-test',
          className: 'far fa-save',
          // command: 'send-test',
          attributes: {
            title: 'Save',
            'data-tooltip-pos': 'bottom'
          },
          command: {
            run: function() {
              self.saveEdits()
            }
          }
        }
      ])

      // Beautify tooltips
      var titles = document.querySelectorAll('*[title]')
      for (var i = 0; i < titles.length; i++) {
        var el = titles[i]
        var title = el.getAttribute('title')
        title = title ? title.trim() : ''
        if (!title) break
        el.setAttribute('data-tooltip', title)
        el.setAttribute('title', '')
      }
      // cutsom blocl

      this.editor.on('storage:load', (e: any) => {
        this.editor.render()
      })
      this.editor.on('change', this.change)
      this.editor.render()
      this.fetchBuilderTemplate()
      this.loadImages()
    },

    undo() {
      this.editor.runCommand('core:undo')
    },
    redo() {
      this.editor.runCommand('core:redo')
    },
    async fetchBuilderTemplate() {
      this.loader = true
      this.emailBuilder = await EmailBuilder.getById(this.builderId)

      let emailData: { [key: string]: any }

      if (this.emailBuilder.downloadUrl) {
        // @ts-ignore
        const { data } = await this.$http.get(
          `/email_builder/get_builder_template`,
          {
            params: {
              location_id: this.currentLocationId,
              template_id: this.builderId
            }
          }
        )

        emailData = data
      } else {
        emailData = this.emailBuilder.emailBuilderData
      }

      if (emailData && emailData.components && emailData.styles) {
        this.editor.setComponents(JSON.parse(emailData.components))
        this.editor.setStyle(JSON.parse(emailData.styles))
        if (emailData.html && emailData.css) {
          this.isMediaQueryPresent = true
        }
      } else if (emailData.html) {
        this.editor.setComponents(emailData.html)
        this.editor.setStyle(emailData.css)
        this.isMediaQueryPresent = true
      } else {
        this.editor.setComponents(emailData)
      }
      this.loader = false
    },
    async saveBuilderTemplate() {
      try {
        this.saving = true
        const { components, styles } = this.editor.store()

        const payloadToUpload = {
          components,
          styles,
          ...(this.isMediaQueryPresent && { html: this.editor.getHtml() }),
          ...(this.isMediaQueryPresent && { css: this.editor.getCss() }),
          ...(!this.isMediaQueryPresent && {
            html_string: this.editor.runCommand('gjs-get-inlined-html')
          })
        }

        const uploadPath = uploadPathEmailBuilder(
          this.currentLocationId,
          this.emailBuilder.id
        )

        const { storageUrl, downloadUrl } = await uploadJsonToStorage(
          uploadPath,
          payloadToUpload
        )

        this.emailBuilder.storageUrl = storageUrl
        this.emailBuilder.downloadUrl = downloadUrl
        this.emailBuilder.save()
        this.saving = false
      } catch (error) {
        alert('Oops, something went wrong while saving Email Template.')
        // eslint-disable-next-line
        console.error(error)
      }
      // @ts-ignore
      $(this.$refs.modal).modal('hide')
    },
    change() {
      this.$emit('change', this.editor.getHtml())
    },
    saveEdits() {
      $(this.$refs.modal)
        // @ts-ignore
        .modal('show')
        .on('hidden.bs.modal', () => {
          this.$emit('hidden', true)
        })
      this.editor.store()
    },
    async uploadAsset(e: any) {
      const files = e.dataTransfer ? e.dataTransfer.files : e.target.files
      const basePath = `location/${this.currentLocationId}/images/${uuid()}`
      let url = await this.uploadImageToStore(basePath, files[0])
      const assets = {
        name: files[0].name,
        src: url
      }
      if (!this.currentLocationId) return
      await MediaFiles.add({
        name: files[0].name,
        type: files[0].type,
        locationId: this.currentLocationId,
        url: url,
        readCount: 0
      })
      // let id = ''; //Picked media file id
      // MediaFiles.collectionRef().doc(id).update({read_count: firebase.firestore.FieldValue.increment(1)})

      this.editor.AssetManager.add(assets)
      this.editor.AssetManager.render()
    },
    async uploadImageToStore(path: string, file: File) {
      const metadata = {
        contentType: file.type,
        contentDisposition: `inline; filename="${file.name}"`,
        name: file.name
      }
      var uploadPath = firebase.storage().ref(path)
      const snapshot = await uploadPath.put(file, metadata)
      return await snapshot.ref.getDownloadURL()
    },
    async loadImages() {
      this.mediaFiles = await MediaFiles.getAllByLocation(
        this.currentLocationId
      )
      if (!this.mediaFiles) return
      const assets = this.mediaFiles.map(el => {
        return {
          name: el.data.name,
          src: el.data.url
        }
      })
      this.editor.AssetManager.add(assets)
      this.editor.AssetManager.render()
    }
  }
})
</script>

<style scope>
.gjs-one-bg {
  background-color: #363d49;
}
.gjs-pn-panel#gjs-pn-views-container,
.gjs-pn-panel.gjs-pn-views-container {
  height: calc(100% - 50px);
}
.gjs-pn-panel#gjs-pn-views-container,
.gjs-pn-panel.gjs-pn-views-container {
  height: calc(100% - 150px);
}
.gjs-frame {
  padding-bottom: 50px;
}
.email-builder--loader {
  position: absolute;
  left: 50%;
  top: 50%;
}
.gjs-editor-cont .fa,
.gjs-editor-cont .fas {
  font-family: 'FontAwesome' !important;
}
</style>

