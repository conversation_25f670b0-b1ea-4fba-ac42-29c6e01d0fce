<template>
  <b-modal ref="modal-sm-d" size="sm"  @hide="close" hide-footer>
      <template v-slot:modal-title>
        <moon-loader-title :title="'Bulk Action'" />
        <!-- <h5 class="modal-title"></h5> -->
      </template>
      <template>
      <div class="modal-body" style="overflow:auto" v-if="request">
          <div class="mb-2" >
            <div class="text-capitalize">
                <strong>Type:</strong>
                  <span class="text-capitalize"
                        v-b-tooltip.hover
                        :title="`Request Id ${request.id}`">
                        {{ request.operationType ? request.operationType.replace('-', ' ') : '' }}
                  </span>
            </div>
          </div>
          <!-- <div>
            <i class="fas fa-clipboard --dark copier zoomable pointer my-1" @click.prevent.stop="clipboardCopy(request.id)"></i>
          </div> -->
          <div class="mt-3, mb-2">
            <span class="text-capitalize">
                <strong>Current Status:</strong> {{ request.currentStatus }}
            </span>
          </div>
          <div class="mt-3, mb-2">
            <span class="text-capitalize">
                <strong>Description:</strong> {{ request.operationDescription }}
            </span>
          </div>
          <div class="mt-3, mb-2">
            <span class="text-capitalize" v-if="request.processingStartedOn" :set="parts = getDateParts(request.processingStartedOn, locationTimeZone)">
                <strong>Scheduled Start:</strong>
                <span class="date-display text-nowrap mx-1">{{parts[0]}}</span>
                <span class="date-display --blue text-nowrap mx-1">{{parts[1]}} <small>({{parts[2]}})</small></span>
              </span>
          </div>
          <div class="mt-3, mb-2" v-if="request.selectedRecordsCount">
            <span class="text-capitalize">
                <strong>Selected Records Count:</strong> {{request.selectedRecordsCount}}
            </span>
          </div>
          <div class="mt-3, mb-1" v-if="request.sprintSchedule">
              <AllAtScheduler :schedule="schedule" :modes="modes" v-if="schedule && (schedule.dripMode ==='processAllAtSchedule')"></AllAtScheduler>
              <BulkScheduler :schedule="schedule" :modes="modes" v-if="schedule && (schedule.dripMode === 'drip')"></BulkScheduler>
          </div>
      </div>
      </template>
  </b-modal>
</template>

<script lang="ts">

import Vue from 'vue'
import { BulkRequestV2 } from './server/bulk_req_v2'
import { BulkSchedule } from './server/bulk_schedule'
import  BulkScheduler  from './BulkScheduler.vue';
import  AllAtScheduler from './AllAtScheduler.vue'

export default Vue.extend({
  name:"BulkActionDescription",
  props: {
    request: BulkRequestV2,
  },
  async mounted(){
    this.showPopup();
  },
  provide () {
    return { parentValidator: this.$validator }
  },
  components:{
    BulkScheduler,
    AllAtScheduler
  },
  data() {
    return {
      schedule: new BulkSchedule(this.request.sprintSchedule),
      modes: [
        { value: 'none', text: 'Immediately' },
        { value: 'processAllAtSchedule', text:'All On Given Date' },
        { value: 'drip', text: "Drip" }
      ]
    }
  },
  methods: {
    close() {
      this.hidePopup();
      this.$emit('closed')
    },
    async showPopup(){
      if (this.request && this.$refs['modal-sm-d']) this.$refs['modal-sm-d'].show();
    },
    hidePopup(){
      if(this.$refs['modal-sm-d']) this.$refs['modal-sm-d'].hide();
    }
  },
  computed: {
    locationTimeZone(){
      console.log(`Location Time Zone -- ${this.$store.getters[`locations/getCurrentLocationTimeZone`]}`)
      return this.$store.getters[`locations/getCurrentLocationTimeZone`];
    },
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.close();
    },
    'request': function(item){
      if (item)  this.showPopup();
    }
  },
})
</script>
<style scoped>

.hl_rules--wrap{
  margin-top: 5px;
}
.form-input-dropdown{
    position: absolute;
    will-change: transform;
    top: 0px;
    left: 0px;
    transform: translate3d(0px, 51px, 0px);
    min-width: 0;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
}
td.td-borderless{
  border-top: none !important;
  padding: 0px !important;
  padding-bottom: 6px !important;

}
</style>
