<template>
  <div class="container-fluid">
    <div class="form-builder-integrate-wrapper">
      <div class="row m-0">
        <JotboxSidebar
          :contactNumber="contactNumber"
          :formData="formData"
          :formbuilder="formSurvey"
          :formId="surveyId"
          :loading="loading"
          :totalRows="totalRows"
          :page="page"
          :limit="limit"
          :locationId="locationId"
          :deletedItem="deletedItem"
          type="Survey"
          v-on:showDetails="showDetails"
          v-on:deleteHistory="deleteHistory"
          v-on:downloadData="downloadData"
          ref="jotboxSidebarRef"
        />

        <div class="col-md-9 pl-3">
          <div class="rounded">
            <!--<JotboxTopbar
              :contactNumber="contactNumber"
              :totalRecods="formData.length"
              v-on:previousContact ="previousContact"
              v-on:nextContact ="nextContact"
            />-->
            <JotboxCard
              :contactNumber="contactNumber"
              :title="title"
              :contactId="contactId"
              :submitDate="submitDate"
              :selectedValues="selectedValues"
              :loading="loading"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.has-search .form-control {
  padding-left: 2.375rem;
  background: #fff;
  height: 37px;
  border: 0 solid #ccc !important;
}
.has-search .form-control-feedback {
  position: absolute;
  z-index: 2;
  display: block;
  width: 15px;
  height: 15px;
  line-height: 2.375rem;
  text-align: center;
  pointer-events: none;
  color: #aaa;
  top: 9px;
  left: 22px;
}
.form-history label.custom-control-label {
  text-indent: -999px;
  height: 27px;
}
.form-history label.custom-control-label::before {
  background-color: #fff;
  width: 25px;
  height: 25px;
  border: 1px solid #e5e5e5;
  left: 0;
  top: 5px;
}
.form-history label.custom-control-label::after {
  top: 0.65rem;
  left: 5px;
}
button.btn.dropdown-toggle.custom-dropDown {
  padding: 22px 0;
  min-width: auto;
  background: 0 0;
  color: #000;
}
.text-grey {
  color: #b3b5ce;
  font-size: 15px;
  padding: 0 10px;
}
.media-body {
  -ms-flex: 1;
  flex: 1;
}
.media {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: start;
  align-items: flex-start;
}
span.grey-bg {
  background: #f6f5f8;
  margin-top: 10px !important;
  float: left;
}
.right-btn button {
  background: #ececf1 !important;
  color: #333 !important;
}
.right-btn .dropdown-menu {
  min-width: auto;
}
.light-bg {
  background: rgba(24, 139, 246, 0.1);
}
body .user-info-table td {
  border-top: 0 !important;
  border-bottom: 1px solid #f2f7fa;
  line-height: 26px !important;
  padding-left: 0 !important;
}
.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}
.submission-date {
  font-size: 13px;
  line-height: 10px;
}
button.btn.dropdown-toggle.custom-dropDown:after {
  top: 44%;
}
.primary-bg {
  background: #f2f7fa;
}
.align-items-center .media-body {
  padding: 0 10px;
}
.height-100-vh {
  min-height: 100vh;
}
.top-header .dropdown.show {
  display: inline-block;
}
.form-builder-integrate-wrapper {
  background: transparent;
}
.form-builder-integrate-wrapper .select-control-wrap {
  background: #f2f7fa;
  width: 100%;
  padding: 0 8px;
}
.form-builder-integrate-wrapper .select-control-wrap select {
  background: #fff;
  width: 100%;
}
.user-list-detauil label.custom-control-label::before {
  background-color: #fff;
  width: 17px;
  height: 17px;
  border: 1px solid #e5e5e5;
  left: 0;
  top: 9px;
  border-radius: 3px;
}
.user-list-detauil label.custom-control-label::after {
  top: 0.55rem;
  left: 1px;
}
</style>

<script lang="ts">
import Vue from 'vue'
import axios from 'axios'
import config from '@/config'
import {
  Location,
  Contact,
  CustomField,
  ICustomField,
  FieldType,
  Item,
  Formsurvey
} from '@/models'
import { parse } from 'path'
import moment from 'moment'
import JotboxSidebar from './jotbox/JotboxSidebar.vue'
import JotboxTopbar from './jotbox/JotboxTopbar.vue'
import JotboxCard from './jotbox/JotboxCard.vue'
import * as json2csv from 'json2csv'
import download from 'downloadjs'
import { JotBoxUtils } from './utils/JotBoxUtils'

const Json2csvParser = json2csv.Parser

const standradField = [
  'full_name',
  'first_name',
  'last_name',
  'phone',
  'address',
  'city',
  'postal_code',
  'organization',
  'state',
  'email',
  'website',
  'source',
  'date_of_birth'
]
const deletedField = ['contact_id', 'formId', 'location_id', 'disqualified', 'funneEventData']
export default Vue.extend({
  components: {
    JotboxSidebar,
    JotboxTopbar,
    JotboxCard
  },
  props: {
    formSurvey: {
      type: Array,
      required: true
    },
    date: {
      type: Object,
      required: true
    },
    selectedDate: {
      type: String
    }
  },
  data() {
    return {
      baseUrl: config.baseUrl,
      contactId: '',
      locationId: '',
      surveyId: '',
      formData: [],
      formDetails: {} as any,
      contactNumber: 0,
      selectedValues: {} as any,
      loading: true,
      title: '',
      submitDate: '',
      customFields: [] as CustomField[],
      start: 0,
      limit: 10,
      keyword: '',
      totalRows: 0,
      page: 0,
      deletedItem: 0,
      callingSequence: 0
    }
  },
  watch: {
    $route(to, from) {
      if (to.params.location_id != this.locationId) {
        this.$router.push({ query: { id: 'all' } })
      }

      this.loading = true
      this.formData = []
      if (to.query.page) {
        this.page = to.query.page
        this.start = (to.query.page - 1) * to.query.limit
      } else {
        this.start = 0
        this.page = 0
      }
      this.surveyId = to.query.id ? to.query.id : 'all'
      this.keyword = to.query.q
      if (to.query.limit) {
        this.limit = to.query.limit
      } else {
        this.limit = 10
      }
      this.selectedValues = {}
      if (! this.locationId || this.locationId != to.params.location_id) {
        this.getCustomFields()
      } else {
        this.fetchData()
      }
      this.locationId = to.params.location_id
    },
    '$route.params.location_id': function(id) {
      this.getCustomFields()
    },
    selectedValues: function(val) {
      this.title = Object.keys(val).length > 0 ? val.full_name || val.title : ''
      this.submitDate = Object.keys(val).length > 0 ? val.dateAdded : ''
    },
    selectedDate() {
      this.loading = true
      this.selectedValues = {}
      this.fetchData()
    }
  },

  methods: {
    async getCustomFields() {
      await CustomField.getByLocationId(this.locationId).onSnapshot(
        snapshot => {
          this.customFields = snapshot.docs.map(d => new CustomField(d));

          this.fetchData();
        }
      )
    },
    async fetchData() {
      this.callingSequence++;
      const callingSequence = this.callingSequence;
      axios
        .get(`${config.attributionUrl}/survey/submission/${this.locationId}`, {
          params: {
            startAt: this.date.start_date,
            endAt: this.date.end_date,
            surveyId: this.surveyId != 'all' ? this.surveyId : null,
            keyword: this.keyword,
            start: this.start,
            limit: this.limit
          }
        })
        .then(({ data }) => {
          if (callingSequence != this.callingSequence) {
            return
          }
          this.totalRows = data.count
          this.formData = data.records
          if (this.formData.length > 0) {
            this.showDetails(this.formData[0], 0)
          }

          this.loading = false
        })
        .catch(error => {
          this.loading = false
        })
    },

    // Show details in jotform card
    showDetails(item: any, itemIndex: any) {
      this.formDetails = {}
      this.selectedValues = {}
      this.getSubmittedDetails(item)
      setTimeout(() => {
        this.$refs.jotboxSidebarRef.highlightItems(item.id)
      }, 100)
      this.contactNumber = itemIndex
    },
    previousContact() {
      if (this.contactNumber > 0) {
        this.showDetails(
          this.formData[this.contactNumber - 1],
          this.contactNumber
        )
        this.contactNumber--
      }
    },
    nextContact() {
      if (
        this.formData.length > 0 &&
        this.contactNumber < this.formData.length - 1
      ) {
        this.contactNumber++
        this.showDetails(this.formData[this.contactNumber], this.contactNumber)
      }
    },
    getSubmittedDetails(item: any) {
      const dateAdded = item.createdAt
      const selectedData = Object.assign({}, item.others)
      for (var key in selectedData) {
        if (!(standradField.includes(key) || deletedField.includes(key))) {
          this.customFields.forEach(item => {
            if (item.id === key) {
              selectedData[item.id] = {
                tag: item.data.field_name,
                value: this.parseValue(
                  selectedData[key]
                )
              }
            }
          })
        } else if (deletedField.includes(key)) {
          delete selectedData[key]
        }
      }
      selectedData.dateAdded = moment(dateAdded).format('MMM Do YYYY, h:mm a')
      if (!selectedData.full_name) {
        selectedData.title = item.name
      }
      this.contactId = item.contactId
      this.selectedValues = selectedData

      return selectedData
    },
    // Delete History single or bulk
    deleteHistory(deletedItems: []) {
      if (deletedItems.length) {
        this.$uxMessage('confirmation', 'Are you sure you want to delete this survey submission?', async (response: string) => {
          if (response === 'ok') {
            this.loading = true
            axios
              .delete(`${config.attributionUrl}/survey/submission/${this.locationId}`, {
                data: {
                  ids: deletedItems
                }
              }).then(res => {
                this.selectedValues = {}
                this.deletedItem++;
                this.fetchData()
              }).catch(error => {
                this.loading = false
              })
          }
        })
      }
    },

    async fetchAllData() {
      return new Promise(async (resolve, reject) => {
        let hasMorePage = true
        let results = [];
        let start = 0;
        let page = 1
        let limit = 100

        while (hasMorePage) {
          if (page > 0) {
            start = (page - 1) * limit
          }
          const { data } = await axios
            .get(`${config.attributionUrl}/survey/submission/${this.locationId}`, {
              params: {
                startAt: this.date.start_date,
                endAt: this.date.end_date,
                surveyId: this.surveyId != 'all' ? this.surveyId : null,
                keyword: this.keyword,
                start: start,
                limit: limit
              }
            })

          data.records.forEach(record => {
            results.push(record)
          })
          page++

          if (results.length >= data.count) {
            hasMorePage = false
          }
        }

        resolve(results)
      })
    },

    parseCSVValue(fieldValue: any) : any {
      if (! fieldValue) {
        return '';
      }
      if (typeof fieldValue === 'object' && !Array.isArray(fieldValue) && fieldValue.hasOwnProperty('meta')) {

        if(fieldValue.meta.isSignature) {
          return 1;
        }

        return fieldValue.url;
      }else if(fieldValue && typeof fieldValue === 'object' && !Array.isArray(fieldValue) && this.isFileType(fieldValue)) {
        let str = '' , cnt = 0;
        Object.keys(fieldValue).forEach((key , ind) => {
          if(fieldValue[key].url) {
              str += fieldValue[key].url + (cnt === (Object.keys(fieldValue).length - 2) ? '' : ', ');
              cnt++;
          }
        })
        return str;
      }

      return fieldValue;
    },

    dowloadFile(csv: string) {
      let filename = `Survey Submission.csv`
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
      if (navigator.msSaveBlob) { // IE 10+
        navigator.msSaveBlob(blob, filename);
      } else {
        const link = document.createElement('a');
        if (link.download !== undefined) {
          // Browsers that support HTML5 download attribute
          const url = URL.createObjectURL(blob);
          link.setAttribute('href', url);
          link.setAttribute('download', filename);
          link.style.visibility = 'hidden';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      }
    },

    // Download submitted data
    async downloadData(checkedItem: any) {
      this.loading = true
      let fields: any = {};
      this.formSurvey.forEach((survey: any) => {
        if (survey._data && survey._data.form_data && survey._data.form_data.slides) {
          survey._data.form_data.slides.forEach((slide: any) => {
            slide.slideData.forEach((field: any) => {
              if (field.tag && ! fields.hasOwnProperty(field.tag)) {
                fields[field.tag] = field.label || field.name || field.placeholder || field.tag;
              }
            })
          });
        }
      })
      console.log('fields', fields)

      let items: any = [];

      let formData = []

      if (checkedItem.length > 0) {
        formData = this.formData.filter(item => {
          return checkedItem.includes(item.id)
        })
      } else {
        formData = await this.fetchAllData()
      }

      if (! formData || formData.length < 1) {
        this.loading = false
        return
      }

      const util = new JotBoxUtils();
      formData.forEach(item => {
        let records: any = {};
        let data = this.getSubmittedDetails(item);
        util.selectedValues = data;
        this.loading = true

        Object.keys(fields).forEach(key => {
          if (data.hasOwnProperty(key)) {
            records[fields[key]] = this.parseCSVValue((data[key] && typeof data[key] == 'object') ? data[key].value : data[key])
          } else {
            if (! records.hasOwnProperty(fields[key])) {
              records[fields[key]] = '';
            }
          }
        })
        records['Submission Date'] = data.dateAdded
        const pageDetails = util.pageDetails();
        pageDetails.forEach((data: {name: String; value: String}) => {
          records[data.name] = data.value || '';
        });
        items.push(records)
      })

      let filteredColumns = [];
      let columns = Object.keys(items[0]);
      columns.forEach((column) => {
        let flag = false;
        items.forEach((item: any) => {
          if(item[column]) {
            flag = true;
          }
        })
        if(flag) {
          filteredColumns.push(column)
        }
      })
      let filteredItems = []
      items.forEach((item) => {
        filteredItems.push(lodash.clone( lodash.pickBy(item , (value, key) => filteredColumns.includes(key))))
      })

      const json2csvParser = new Json2csvParser(fields);
      const csv = json2csvParser.parse(filteredItems);

      this.dowloadFile(csv)

      setTimeout(() => {
        this.loading = false
      }, 500)
    },

    getSelectedData(checkedItem: any) {
      let data = ''
      const el = this
      if (checkedItem.length > 0) {
        checkedItem.forEach((val: any) => {
          this.formData.forEach(function(item: any, index: any) {
            if (val === item.id) {
              const dateAdded = item.data.date_added.seconds
              const selectedData = Object.assign({}, item.data.form_data)
              for (var key in selectedData) {
                if (!standradField.includes(key)) {
                  el.customFields.forEach(fieldItem => {
                    if (fieldItem.id === key) {
                      data += `<p> <b>${
                        fieldItem.data.field_name
                      }</b> : ${el.parseValue(selectedData[key])}</p>`
                    }
                  })
                } else if (
                  key === 'contact_id' ||
                  key === 'form_id' ||
                  key === 'location_id'
                ) {
                  delete selectedData[key]
                } else {
                  data += `<p> <b>${key}</b> : ${selectedData[key]}</p>`
                }
              }
              data += `<p> <b>dateAdded</b> : ${moment
                .utc(dateAdded * 1000)
                .format('MMM Do YYYY')}</p>`
              let dashLine = ''
              for (let i = 0; i < 100; i++) {
                dashLine += '-'
              }
              data += `<p>${dashLine}</p>`
            }
          })
        })
      } else {
        for (var key in this.selectedValues) {
          data += `<p> <b>${key}</b> : ${this.selectedValues[key]}</p>`
        }
      }
      return data
    },
    isFileType(value) {
      let flag = false;
      Object.keys(value).forEach((id) =>{

            if(value[id].hasOwnProperty('meta')) {
              flag = true;
          }
      })
      return flag;
    },
    parseValue(value: any) {
      let parseVal = ''

      if (!value) {
        return parseVal;
      }

      if (typeof value === 'object' && !Array.isArray(value)) {

        if(value.hasOwnProperty('meta')) {//Old File type or Signature
          if(value.meta.isSignature) {
            return value;
          }
          let obj = {}
          obj["id"] = value;
          obj.fileType = true;
          return obj;
        }else if(this.isFileType(value)) {
          value.fileType = true;
          return value;
        }

        for (const key in value) {
          if (value[key] !== '') {
            parseVal += value[key] + ' '
          }
        }
        return parseVal
      } else if (Array.isArray(value)) {
        value.forEach(item => {
          if (typeof item === 'object') {
            parseVal += this.parseValue(item)
          } else {
            parseVal += item + ' '
          }
        })
        return parseVal
      } else {
        return value
      }
    }
  },

  created() {
    this.locationId = this.$router.currentRoute.params.location_id
    this.surveyId = 'all'
    this.locationId = this.$router.currentRoute.params.location_id
    let query = this.$router.currentRoute.query

    if (query.id) {
      this.surveyId = query.id
    }

    if (query.limit) {
      this.limit = query.limit
    }

    if (query.page) {
      this.page = query.page
      this.start = (query.page - 1) * query.limit
    }

    if (query.q) {
      this.keyword = query.q
    }

    this.getCustomFields()
  }
})
</script>
