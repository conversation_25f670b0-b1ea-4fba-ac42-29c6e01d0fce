<template>
  <section class="hl_wrapper">
    <section class="hl_wrapper--inner funnels" id="funnels">
      <div class="container-fluid">
        <div class="hl_controls">
          <div class="hl_controls--left flex">
            <h2>{{ pageTitle }}s</h2>
          </div>
          <div class="hl_controls--right">
            <UIButton use="primary" @click="showCreateNewFunnel()">
              <i class="icon icon-plus mr-2"></i>&nbsp;New {{ pageTitle }}
            </UIButton>
          </div>
        </div>
        <div class="search-form" :style="{marginBottom: '20px'}">
          <UITextInputGroup icon="icon-loupe" type="text" data-lpignore="true" class="form-light" v-on:input="searchFunnels" v-model="searchText" :placeholder="`Search ${ pageTitle }s`"/>
        </div>
        <route-links :routeData="routeLinks" />
        <!-- <ul class="nav nav-tabs" role="tablist">
          <li class="nav-item">
            <router-link
              :to="{name: 'all_funnels'}"
              id="all_funnels"
              tag="a"
              class="nav-link"
              active-class="active"
              exact
              data-toggle="tab" role="tab" aria-controls="all-funnels-tab" aria-selected="true"
            >
              Funnels
            </router-link>
          </li>
          <li class="nav-item">
            <router-link
              :to="{name: 'websites_funnels'}"
              id="websites"
              tag="a"
              class="nav-link"
              active-class="active"
              exact
              data-toggle="tab" role="tab" aria-controls="all-funnels-tab" aria-selected="true"
            >
              Websites
            </router-link>
          </li>
        </ul> -->
        <router-view @cloneFunnel="cloneFunnel" :funnels="inWebsitesPage ? websites : funnels" :websites="websites" :showing-websites="inWebsitesPage" :fetching-data="fetchingData" :currentLocationId="currentLocationId" />
      </div>
    </section>
    <CreateNewFunnelModal
      :show-modal="createNewFunnelShown"
      :create-website="inWebsitesPage"
      @hide="hideCreateFunnelModal"
      @newFunnelDetails="createNewFunnel"
    />
    <CloneFunnelToLocation
      :show-modal="cloneFunnelModalShown"
      :funnel-id="funnelToClone.id"
      :funnel-name="funnelToClone.name"
      :entity-name="inWebsitesPage ? 'Website' : 'Funnel'"
      @hide="hideCloneFunnelModal"
      @cloneSuccessful="cloneSuccessful"
    />
  </section>
</template>
<script lang="ts">
import CreateNewFunnelModal from './CreateNewFunnelModal.vue'
import CloneFunnelToLocation from '../../components/funnels/CloneFunnelToLocation.vue'
import RouteLinks from '@/pmd/components/funnels/FunnelOverviewRoute.vue'
import Funnel, { FunnelType } from '../../../models/funnel'
import Location from '../../../models/location'
import { FunnelLookupType } from '../../../models/funnel_lookup'
import { getFunnelUrl, getValidFunnelLookupUrl, createNewFunnelLookup } from '../../../util/helper';
import { EditorActiveViewer } from '@/models'
import { defineComponent, ref, computed } from '@vue/composition-api'
import { useStore } from '@/store'

let funnelListListener: () => void
let unsubscribeEditorActiveViewer: () => void

export default defineComponent({
  components: {
    CreateNewFunnelModal,
    CloneFunnelToLocation,
    RouteLinks
  },
  setup() {
    const store = useStore()
    const currentLocationId = ref('');

    const allowNewPaymentsAccess = computed((): boolean => {
      return store.getters['company/isAbove97Plan']
    });

    return {
      currentLocationId,
      allowNewPaymentsAccess
    }
  },
  async mounted() {
    await this.fetchFunnels()
    this.listenForEditorActiveViwerChanges()
  },
  data() {
    return {
      allLocations: [],
      createNewFunnelShown: false,
      fetchingData: false,
      funnels: [] as Funnel[],
      websites: [] as Funnel[],
      allFunnels: [] as Funnel[],
      allWebsites: [] as Funnel[],
      searchText: '',
      cloneFunnelModalShown: false,
      funnelToClone: {} as Funnel,
      loading: {
        locations: false
      }
    }
  },
  computed: {
    routeLinks(): object[] {
      const funnelsRoute = [
        { name: 'all_funnels', params: { location_id: this.currentLocationId }, text: 'Funnels' }
      ]

      const websitesRoute = [
        { name: 'websites_funnels', params: { location_id: this.currentLocationId }, text: 'Websites' }
      ]

      if (this.inFunnelsPage) {
        return funnelsRoute
      } else if (this.inWebsitesPage) {
        return websitesRoute
      } else {
        return funnelsRoute
      }
    },
    inFunnelsPage(): boolean {
    const funnelRouteName = ['all_funnels', 'funnels-page-v2']

      if (funnelRouteName.includes(this.$route.name as string)) {
        return true
      }

      return false
    },
    inWebsitesPage(): boolean {
      const websiteRouteName = ['websites_funnels','websites-funnels-v2']

      if (websiteRouteName.includes(this.$route.name as string)) {
        return true
      }

      return false
    },
    pageTitle(): string {
      if (this.inFunnelsPage) {
        return 'Funnel'
      } else if (this.inWebsitesPage) {
        return 'Website'
      }

      return 'Funnel'
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
  },
  beforeDestroy() {
    if (funnelListListener) {
      funnelListListener()
    }
    if (unsubscribeEditorActiveViewer) unsubscribeEditorActiveViewer()

  },
  methods: {
    listenForEditorActiveViwerChanges(){
        if (unsubscribeEditorActiveViewer) unsubscribeEditorActiveViewer()
        const query = EditorActiveViewer.queryByLocation(this.$route.params.location_id)
        unsubscribeEditorActiveViewer = query.onSnapshot(snapshot => {
          if(snapshot && snapshot.docs.length > 0) {
            const items = []
            for (const funnel of snapshot.docs) {
              items.push(funnel.data())
            }
            this.$store.dispatch('funnel/SET_ACTIVE_EDITOR_VIEWER',items)
          }
        })

    },
    async fetchFunnels() {
      try {
        this.fetchingData = true

        if (funnelListListener) {
          funnelListListener()
        }

        funnelListListener = Funnel.getAllByLocationRealtime(this.currentLocationId)
        .onSnapshot((snapshot) => {
            const listOfAllFunnels = snapshot.docs.map(f => new Funnel(f))
            const { funnels, websites } = listOfAllFunnels.reduce((agg, funnel: Funnel) => {
              switch (funnel.type) {
                case FunnelType.Website: {
                  agg.websites.push(funnel)
                  break
                }
                case FunnelType.Funnel: {
                  agg.funnels.push(funnel)
                  break
                }
                default: {
                  agg.funnels.push(funnel)
                }
              }

              return agg
            }, {
              funnels: [] as Funnel[],
              websites: [] as Funnel[]
            })


            this.allFunnels = funnels
            this.allWebsites = websites
            this.funnels = Array.from(this.allFunnels)
            this.websites = Array.from(this.allWebsites)
            this.fetchingData = false
        })
      } catch (error) {
        console.error('error while fetching funnels --> ', error)
      }
    },
    cloneFunnel(funnelId: string) {
      const list = this.inWebsitesPage ? this.websites : this.funnels
      const index = list.findIndex((f: Funnel) => f.id === funnelId)

      if (index === -1) return

      this.funnelToClone = list[index]

      this.showCloneFunnelModal()
    },
    showCloneFunnelModal() {
      this.cloneFunnelModalShown = true
    },
    hideCloneFunnelModal() {
      this.cloneFunnelModalShown = false
    },
    cloneSuccessful(cloneData: any) {
      const { locationIds } = cloneData

      if (locationIds.length === 1) {
        const locationWhereFunnelCloned = locationIds[0]

        if (locationWhereFunnelCloned !== this.currentLocationId) {
          this.$router.push({
            name: this.$route.name,
            params: { location_id: locationWhereFunnelCloned }
          })
        }
      }

      this.hideCloneFunnelModal()
    },
    showCreateNewFunnel() {
      this.createNewFunnelShown = true
    },
    hideCreateFunnelModal() {
      console.log('got hide event ')
      this.createNewFunnelShown = false
    },
    // createFunnelSubmitted(funnelDetails: { name: string, markAsWebsite: boolean }) {
    //   console.log('got funnel details --> ', funnelDetails)
    //   this.createNewFunnel(funnelDetails);
    // },
    async createNewFunnel({ name }: { name: string }) {
      const newFunnel = new Funnel();
      newFunnel.name = name;
      newFunnel.orderFormVersion = 2
      newFunnel.type = this.inWebsitesPage ? FunnelType.Website : FunnelType.Funnel;
      newFunnel.locationId = this.currentLocationId;
      const funnelUrl = getFunnelUrl(name)
      // const uniqueFunnelUrl = await getUniqueFunnelUrl('', funnelUrl)
      newFunnel.url = funnelUrl
      // set domain id of funnel as empty
      newFunnel.domainId = ''

      await newFunnel.save();
      // TAG: LOOKUPS
      // await this.addNewFunnelLookup(newFunnel);

      // this.funnels.unshift(newFunnel);
      this.$router.push({ name: this.inWebsitesPage ? 'websites_detail' : 'funnels_detail', params: { funnel_id: newFunnel.id } })
      this.hideCreateFunnelModal();
    },
    async addNewFunnelLookup(funnel: Funnel) {
      const newLookupPayload = {
        type: FunnelLookupType.Funnel,
        typeId: funnel.id,
        path: getValidFunnelLookupUrl(funnel.url),
        funnelId: funnel.id,
        locationId: funnel.locationId
      }

      await createNewFunnelLookup(newLookupPayload)
    },
    async searchFunnels() {
      const cleanedSearchText = this.searchText.trim().toLowerCase()

      this.fetchingData = true

      let filteredList = []

      let baseSearchEntity = [] as Funnel[]

      if (this.inFunnelsPage) {
        baseSearchEntity = this.allFunnels
      } else if (this.inWebsitesPage) {
        baseSearchEntity = this.allWebsites
      }

      if (cleanedSearchText) {
        filteredList = baseSearchEntity.filter(f => f.name.toLowerCase().indexOf(cleanedSearchText) > -1)
      } else {
        filteredList = Array.from(baseSearchEntity)
      }

      if (this.inFunnelsPage) {
        this.funnels = filteredList
      } else if (this.inWebsitesPage) {
        this.websites = filteredList
      }

      this.fetchingData = false
    },
    resetData() {
      this.allFunnels = []
      this.funnels = []
      this.websites = []
    }
  },
  watch: {
    "$route.params.location_id": function(id) {
      this.resetData()
      this.currentLocationId = id
      this.fetchFunnels()
    }
  }
})
</script>
<style  scoped>
.hl_wrapper--inner.funnels .container-fluid {
  max-width: 1200px !important;
}
</style>
