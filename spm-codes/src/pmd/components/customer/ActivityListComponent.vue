<template>
  <div>
    <div class="activity-tab-details" :class="{'attribution-source-both' : contact && contact.attributionSource && contact.lastAttributionSource}">
      <div class="card" v-if="activities && activities.length > 0">
        <div class="card-body">
          <div class="hl_tasks">
            <ActivityListCard
              v-for="activity in activities"
              :activity="activity"
              :key="activity.id"
              :timezone="timezone"
              :locationId="locationId"
              @showDetail="showDetail"
              @showPageVisit="showPageVisit"
            ></ActivityListCard>

            <div v-if="loadingMore">
              <moon-loader color="#188bf6" size="20px" />
            </div>
          </div>
          <div class="load-more" v-if="hasMore">
            <a @click="loadMore">Load more activities</a>
          </div>
        </div>
      </div>
      <template v-else>
        <div class="item-center">
          <moon-loader :loading="loading" color="#188bf6" size="20px" />
          <template v-if="!loading">
            <div class="text-center">
              <i
                class="fa fa-chart-line"
                aria-hidden="true"
                style="font-size: 20px; color: #bdbdbd"
              ></i>
            </div>
            <p>
              {{ message === undefined ? defaultMessage : message }}
              <br />
            </p>
          </template>
        </div>
      </template>
    </div>
    <div class="attribution-source" v-if="contact && (contact.attributionSource || contact.lastAttributionSource)">
      <div class="card">
        <div class="card-body">
          <template v-if="contact.attributionSource">
            <h6>First Attribution</h6>
            <p><strong>Session Source</strong>: {{contact.attributionSource.sessionSource }}
              <i
                class="fas fa-question-circle"
                id="first-attribution-tooltip"
              ></i>
            </p>

            <b-tooltip target="first-attribution-tooltip" placement="bottom">
              <strong>Session Source</strong>: {{contact.attributionSource.sessionSource }} <br />

              <template v-if="contact.attributionSource.campaign">
                <strong>Campaign:</strong> {{ contact.attributionSource.campaign }} <br />
              </template>

              <template v-if="contact.attributionSource.utmSource">
                <strong>UTM Source:</strong> {{ contact.attributionSource.utmSource }} <br />
              </template>

              <template v-if="contact.attributionSource.utmMedium">
                <strong>UTM Medium:</strong> {{ contact.attributionSource.utmMedium }} <br />
              </template>

              <template v-if="contact.attributionSource.utmContent">
                <strong>UTM Content:</strong> {{ contact.attributionSource.utmContent }} <br />
              </template>

              <template v-if="contact.attributionSource.referrer">
                <strong>Referrer:</strong> {{ contact.attributionSource.referrer }} <br />
              </template>

              <template v-if="contact.attributionSource.campaignId">
                <strong>Campaign Id:</strong> {{ contact.attributionSource.campaignId }} <br />
              </template>

              <template v-if="contact.attributionSource.fbclid">
                <strong>FB ClickId:</strong> {{ contact.attributionSource.fbclid }} <br />
              </template>

              <template v-if="contact.attributionSource.gclid">
                <strong>Google ClickId:</strong> {{ contact.attributionSource.gclid }} <br />
              </template>
            </b-tooltip>
            <hr class="my-2" />
          </template>

          <template v-if="contact.lastAttributionSource">
            <h6>Latest Attribution</h6>
            <p><strong>Session Source</strong>: {{contact.lastAttributionSource.sessionSource }}
              <i
                class="fas fa-question-circle"
                id="last-attribution-tooltip"
              ></i>
            </p>

            <b-tooltip target="last-attribution-tooltip" placement="bottom">
              <strong>Session Source</strong>: {{contact.lastAttributionSource.sessionSource }} <br />
              <template v-if="contact.lastAttributionSource.campaign">
                <strong>Campaign:</strong> {{ contact.lastAttributionSource.campaign }} <br />
              </template>

              <template v-if="contact.lastAttributionSource.utmSource">
                <strong>UTM Source:</strong> {{ contact.lastAttributionSource.utmSource }} <br />
              </template>
              <template v-if="contact.lastAttributionSource.utmMedium">
                <strong>UTM Medium:</strong> {{ contact.lastAttributionSource.utmMedium }} <br />
              </template>

              <template v-if="contact.lastAttributionSource.utmContent">
                <strong>UTM Content:</strong> {{ contact.lastAttributionSource.utmContent }} <br />
              </template>

              <template v-if="contact.lastAttributionSource.referrer">
                <strong>Referrer:</strong> {{ contact.lastAttributionSource.referrer }} <br />
              </template>

              <template v-if="contact.lastAttributionSource.campaignId">
                <strong>Campaign Id:</strong> {{ contact.lastAttributionSource.campaignId }} <br />
              </template>

              <template v-if="contact.lastAttributionSource.fbclid">
                <strong>FB ClickId:</strong> {{ contact.lastAttributionSource.fbclid }} <br />
              </template>

              <template v-if="contact.lastAttributionSource.gclid">
                <strong>Google ClickId:</strong> {{ contact.lastAttributionSource.gclid }} <br />
              </template>
            </b-tooltip>
          </template>

        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Formbuilder, Formsurvey, Location, SessionType } from '@/models/index'
import axios from 'axios'
import config from '@/config'

const ActivityListCard = () =>
  import('@/pmd/components/customer/ActivityListCard.vue')

let locationForms: Formbuilder[] = [];
let locationSurveys: Formsurvey[] = [];

export default Vue.extend({
  props: ['heading', 'action', 'message', 'contact'],
  data() {
    return {
      locationId: '',
      contactId: '',
      defaultMessage: 'No Activity for this contact',
      location: undefined as Location | undefined,
      timezone: 'UTC',
      loading: true as boolean,
      loadingMore: false as boolean,
      forms: [] as Formbuilder[],
      surveys: [] as Formsurvey[],
      page: 0,
      records: [],
      hasMore: false,
      callingSequence: 0,

      submissionDetailsModal: { visible: false } as { [key: string]: any },
      triggerLinks: []
    }
  },
  components: { ActivityListCard },
  watch: {
    '$route.params.location_id': function (id) {
      this.locationId = id
      this.calendarEvent = [];
      locationSurveys = [];
      locationForms = [];
      this.fetchData()
    },
    '$route.params.contact_id': function (id) {
      this.contactId = id
      this.calendarEvent = []
      this.fetchData()
    },
  },
  async created() {
    this.locationId = this.$router.currentRoute.params.location_id
    this.contactId = this.$router.currentRoute.params.contact_id
    await this.loadTriggerLinks()
    await this.fetchData()
  },
  computed: {
    calendars() {
      return this.$store.state.calendars.calendars
    },
    activities() {
      return this.records.map(activity => {
        return {
          id: activity.id,
          type: activity.type,
          createdAt: activity.createdAt,
          pageUrl: activity.pageUrl,
          title: this.activityTitle(activity),
          oriPageTitle: activity.pageTitle || '',
          pageTitle: this.pageTitle(activity),
          marketingId: activity.marketingId,
          isBookingCustomForm: activity.isBookingCustomForm,
          source: activity.sessionSource,
          campaign: activity.campaign,
          utm_medium: activity.utmMedium,
          utm_content: activity.utmContent,
          referrer: activity.referrer,
          url_params: activity.urlParams,
          adSource: activity.adSource,
        }
      })
    },
  },
  methods: {
    activityTitle(activity) {
      if ([SessionType.SURVEY, SessionType.FORM].indexOf(activity.type) != -1) {
        return (
          (this.$options.filters
            ? this.$options.filters.capitalize(activity.type)
            : activity.type) + ' Submitted'
        )
      }

      if ([SessionType.TRIGGER_LINK].indexOf(activity.type) != -1) {
        return 'Trigger Link Visited'
      }

      if ([SessionType.PAGE_VISIT].indexOf(activity.type) != -1) {
        return 'Page Visited'
      }

      if (
        [SessionType.APPOINTMENT, SessionType.APPOINTMENT_V3].indexOf(
          activity.type
        ) != -1
      ) {
        return 'Appointment Booked'
      }

      if ([SessionType.CONTACT_CREATED].indexOf(activity.type) != -1) {
        return 'Contact Created'
      }

      if ([SessionType.CALL].indexOf(activity.type) != -1) {
        return 'Call'
      }

      if ([SessionType.TWO_STEP_PAY].indexOf(activity.type) != -1) {
        return 'Two Step Purchased'
      }

      if ([SessionType.ONE_STEP_CONTACT].indexOf(activity.type) != -1) {
        return 'One Step Form Submitted'
      }

      if ([SessionType.ONE_STEP_PAY].indexOf(activity.type) != -1) {
        return 'Purchase Processing Step'
      }

      if ([SessionType.TWO_STEP_CONTACT].indexOf(activity.type) != -1) {
        return 'Two Step Form Submitted'
      }

      if ([SessionType.FB_LEAD_FORM].indexOf(activity.type) != -1) {
        return 'Facebook Lead Form'
      }

      if ([SessionType.CHAT_WIDGET_FORM].indexOf(activity.type) != -1) {
        return 'Chat Widget'
      }

      return 'Visit'
    },
    pageTitle(activity) {
      if (
        activity &&
        activity.parentName == 'Chat Widget Form' &&
        activity.type == SessionType.CONTACT_CREATED
      ) {
        return 'Chat Widget'
      }

      if (
        [
          SessionType.CONTACT_CREATED,
          SessionType.CALL,
          SessionType.APPOINTMENT_V3,
        ].indexOf(activity.type) != -1
      ) {
        return activity.parentName
      }

      if ([SessionType.CHAT_WIDGET_FORM].indexOf(activity.type) != -1) {
        return 'Chat Widget Form'
      }

      if (
        [SessionType.SURVEY].indexOf(activity.pageVisitType) != -1 ||
        [SessionType.SURVEY].indexOf(activity.type) != -1
      ) {
        let survey = this.surveys.find(survey => survey.id == activity.parentId)
        if (survey) {
          return survey.name
        }
      }

      if (
        [SessionType.FORM].indexOf(activity.pageVisitType) != -1 ||
        [SessionType.FORM].indexOf(activity.type) != -1
      ) {
        let form = this.forms.find(form => form.id == activity.parentId)
        if (form) {
          return form.name
        }
      }

      if (
        [SessionType.TRIGGER_LINK].indexOf(activity.pageVisitType) != -1 ||
        [SessionType.TRIGGER_LINK].indexOf(activity.type) != -1
      ) {
        const triggerLink = this.triggerLinks.find(link => link.value == `{{trigger_link.${activity.parentId}}}`)
        if (triggerLink) {
          return triggerLink.text;
        }
      }

      if (
        this.calendars &&
        this.calendars.length &&
        (['calendar'].indexOf(activity.pageVisitType) != -1 ||
          [SessionType.APPOINTMENT].indexOf(activity.type) != -1)
      ) {
        let calender = this.calendars.find(x => x.id === activity.parentId)
        if (calender) {
          return calender.name
        }
      }

      if (activity.pageUrl) {
        let url = new URL(activity.pageUrl)
        if ([SessionType.TRIGGER_LINK].indexOf(activity.type) != -1) {
          return url.pathname.substr(0, 24)
        }

        if (!url.pathname || url.pathname == '/') {
          return url.hostname
        }

        return url.pathname
      }
    },
    async fetchData() {
      if (this.locationId && this.contactId) {
        this.records = []
        this.fetchFormData()
      }
      this.location = new Location(
        await this.$store.dispatch('locations/getById', this.locationId)
      )
      this.timezone = await this.location.getTimeZone()
    },
    fetchFormData() {
      if (locationForms && locationForms.length > 0) {
        this.forms = locationForms;
        this.fetchSurveyData()
        return;
      }

      Formbuilder.getQueryWithLocationId(this.locationId)
        .get()
        .then(async snapshot => {
          this.forms = snapshot.docs.map(d => new Formbuilder(d))
          locationForms = this.forms;
          this.fetchSurveyData()
        })
    },
    fetchSurveyData() {
      if (locationSurveys && locationSurveys.length > 0) {
        this.surveys = locationSurveys;
        this.fetchRecord()
        return;
      }

      Formsurvey.getQueryWithLocationId(this.locationId)
        .get()
        .then(async snapshot => {
          this.surveys = snapshot.docs.map(d => new Formsurvey(d))
          locationSurveys = this.surveys;
          this.fetchRecord()
        })
    },
    fetchRecord() {
      this.loading = true
      this.callingSequence++
      const callingSequence = this.callingSequence
      axios
        .get(
          `${config.attributionUrl}/activity/${this.locationId}/contact/${this.contactId}`,
          {
            params: {
              page: this.page,
            },
          }
        )
        .then(({ data }) => {
          if (callingSequence != this.callingSequence) {
            return
          }
          data.records.forEach(activity => {
            if (activity.contactCreatedAt) {
              this.records.push({
                id: activity.id + 'contact_created',
                type: 'contact-created',
                createdAt: activity.createdAt,
                pageUrl: activity.pageUrl,
                parentName: activity.parentName,
              })
            }

            if (activity.parentName == 'Facebook Lead Form') {
              activity.type = SessionType.FB_LEAD_FORM
            }

            if (activity.parentName == 'Chat Widget Form') {
              activity.type = SessionType.CHAT_WIDGET_FORM
            }

            this.records.push(activity)
          })

          if (data.records.length == data.limit) {
            this.hasMore = true
          } else {
            this.hasMore = false
          }
          this.loadingMore = false
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    async loadTriggerLinks() {
      if (!this.triggerLinks || this.triggerLinks.length < 1) {
        let obj = this.$store.getters['conversation/getTriggerLinksMenuItem']
        if (!obj || !obj.text) {
          obj = await this.$store.dispatch('conversation/fetchTriggerLinks', {locationId: this.$route.params.location_id})
        }
        delete obj.location_id
        this.triggerLinks.push.apply(this.triggerLinks, obj.menu)
      }
    },
    loadMore() {
      this.hasMore = false
      this.loadingMore = true
      this.page++
      this.fetchRecord()
    },

    showDetail(event) {
      this.$emit('showDetailModal', event)
    },
    showPageVisit(event) {
      this.$emit('pageDetailModal', event)
    },
  },
})
</script>

<style lang="scss">
.activity-tab-details {
  height: calc(100vh - 235px);
  &.attribution-source-both {
    height: calc(100vh - 320px);
  }
  overflow-x: auto;
  padding-bottom: 10px;
  .card {
    .card-body {
      .hl_tasks {
        position: relative;

        &::before {
          content: '';
          left: 0.93em;
          position: absolute;
          height: calc(100% - 5px);
          width: 0;
          top: 5px;
          border-left: 1px dashed #999;
        }

        .hl_tasks--item.hl_activity--item:last-child {
          position: relative;
          &::after {
            content: '';
            position: absolute;
            height: calc(100% - 32px);
            bottom: 0;
            width: 28px;
            background: #fff;
            left: 0;
          }
        }

        &--item-header {
          .task-actions {
            margin-left: auto;
          }
        }
      }
    }
  }
  .load-more {
    text-align: center;
    color: #188bf6;
  }
}
.attribution-source {
  height: 270px;
  box-shadow: 0 1rem 3rem rgba(0,0,0,.175)!important;
  border-top: 1px solid #e4e7ea
}
</style>
