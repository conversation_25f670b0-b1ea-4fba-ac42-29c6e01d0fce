<template>
  <div class="hl_saas--services">
    <div class="card" style="height: calc(100% - 20px)">
      <div class="card-header">
        <div class="title">
          <h3>Email Resell Settings</h3>
          <p>
            This allows you to rebill Email costs to your clients and include a
            markup.
            <a
              href="https://help.gohighlevel.com/support/solutions/articles/48001188579-email-re-billing"
              target="_blank"
              style="font-weight: 500"
            >
              See how <i class="fas fa-external-link-alt"></i
            ></a>
          </p>
        </div>
        <div class="toggle" v-if="canUpdate && !updating">
          <span>Enable</span>
          <UIToggle
            id="mailgun_rebilling"
            v-model="rebillingEnabled"
          />
          <label class="tgl-btn" for="mailgun_rebilling"></label>
        </div>
        <div v-else-if="updating">
          <span> Updating... </span>
        </div>
      </div>
      <div class="card-body" :class="[!rebillingEnabled ? 'disabled' : '']">
        <div v-if="!rebillingEnabled || !canUpdate" class="mask"></div>
        <span class="description"
          >These are margins that are billed to your clients over the Email cost
          borne by you
        </span>
        <div class="profit-level">
          <h6 class="label">Rebill Amount</h6>
          <div class="slider">
            <input
              v-if="rebillingEnabled"
              v-model="markup"
              @change="rebillingDataUpdated"
              type="range"
              id="markup"
              name="markup"
              :min="billRange.min"
              :max="billRange.max"
              :step="billRange.step"
              @focus="reset"
            />
            <input
              v-else
              disabled
              :value="0"
              min="0"
              max="100"
              step="5"
              type="range"
              name="markup"
            />
            <span
              class="value"
              :style="{ left: `calc(${markup * 10}% - 30px)` }"
              >{{ markup }}x</span
            >
            <span>1x</span>
            <span>10x</span>
          </div>
        </div>
        <div class="profit-boxes">
          <div class="row">
            <div class="label">
              <i
                class="info-icon fas fa-info-circle"
                v-b-tooltip.hover
                title=""
                style="opacity: 0.6"
              ></i>
              Provider
            </div>
            <div
              class="data primary"
              v-if="provider"
              style="font-weight: bold; color: #188bf6"
            >
              <span>{{ provider.email }} ({{ provider.providerType }})</span>
            </div>
            <div v-else>No provider found</div>
          </div>
          <div class="row" style="position: relative">
            <div class="label">Your email cost</div>
            <div class="data" style="position: relative">
              <span>$</span>
              <input
                type="number"
                class="email-price-input"
                step="0.0001"
                @change="emailPriceUpdated"
                @input="emailPriceUpdated"
                @focus="reset"
                v-model="basePrice"
                :class="!isValidPrice ? 'error' : ''"
              />
              <span>/ email
                <a href="https://help.gohighlevel.com/support/solutions/articles/48001188579-email-re-billing" target="_blank" style="color: #a7a7a7; margin-left: 4px;">
                  <i class="fas fa-question-circle" v-b-tooltip.hover title="How do i calculate my email cost? Click on the icon to know more"></i>
                </a>
              </span>
              <div class="tooltip__invalid-value" v-if="!isValidPrice">
                <span class="value">
                  Minimum must be between 0.0001 to 0.005
                </span>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="label">Charged to customer</div>
            <div class="data">
              <span>$</span>
              <p>{{ emailCharge }}</p>
              <span>/ email</span>
            </div>
          </div>
          <div class="row">
            <div class="label">$10 will give you about</div>
            <div class="data">
              <p>{{ countEstimateFromDollarAmount(emailCharge) }}</p>
              <span>emails</span>
            </div>
          </div>
          <div class="row profit">
            <div class="label">Profit</div>
            <div class="data">
              <span>$</span>
              <p>{{ emailProfit }}</p>
              <span>/ email</span>
            </div>
          </div>
        </div>
        <div class="save-changes">
          <div class="warning">
            <i>
              Modifying these settings will change the cost incurred by your
              clients going forward.
              <br />
              Please do not modify these if you aren't sure of its effects.
              <br />
              These settings will apply to your new clients going forward
            </i>
          </div>
          <i class="card-verify--error" v-if="error">{{ error }}</i>
          <i class="reminder" v-if="unsavedChanges">Unsaved Changes</i>
          <UIButton
            v-if="!autoSave"
            @click="updateRebillingSettings"
            :disabled="updating"
            :class="unsavedChanges ? 'unsaved' : ''"
          >
            {{ updating ? 'Saving..' : 'Save' }}
          </UIButton>
        </div>
        <div class="tc">
           <sup>*</sup>Email validations are currently not rebilled.
        </div>
      </div>
      <!-- <div class="card-header">
        <div class="title">
          <h3>Provider</h3>
          <span v-for="(service, index) in smtpServices" :key="index">
            {{ service.email }} ({{ service.providerType }})
          </span>
        </div>
      </div> -->
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { debounce, DebouncedFunc } from 'lodash'
import { MailGunAccount, SMTPService } from '@/models'
import firebase from 'firebase'

interface IService {
  providerName: string
  providerType: string
  email: string
  id: string
}

export default Vue.extend({
  props: {
    settings: Object,
    canUpdate: {
      type: Boolean,
      default: false,
    },
    updating: {
      type: Boolean,
      default: false,
    },
    error: String,
    autoSave: {
      type: Boolean,
      default: false,
    },
    locationId: String,
    // company: Object,
    location: Object,
  },
  created() {
    this.emailPriceUpdated = debounce(() => {
      if (this.emailPrice && this.isValidPrice) {
        this.rebillingDataUpdated()
      }
    }, 1000)
  },
  mounted() {
    this.fetchData()
    if (this.settings && this.settings.markup) {
      this.markup = this.computeMarkupAmountFromPercent(this.settings.markup)
    }
    if (this.settings.price) {
      this.basePrice = this.settings.price
    }
  },

  computed: {
    company() {
      return this.$store.state.company.company
    },
    defaultService: {
      get: function () {
        if (!this.locationId && this.company)
          return this.company.defaultEmailService
        else if (this.location) return this.location.defaultEmailService
        return ''
      },
    },
    markupPercent(): number {
      const markupAmount = parseFloat(this.markup)
      if (markupAmount === 1) {
        return 5
      }

      // if markup is set to 2.5 ie. 2.5x
      // then markup % would be 150
      return markupAmount * 100 - 100
    },
    isValidPrice(): boolean {
      if (this.emailPrice < 0.0001 || this.emailPrice > 0.005) return false
      return true
    },
    emailPrice(): number {
      const temp = parseFloat(this.basePrice)
      return isNaN(temp) ? 0 : temp
    },
    emailProfit(): string {
      return this.computeMarkupProfit(this.emailPrice)
    },
    emailCharge(): string {
      const charge = this.readableFloat(parseFloat(this.emailProfit) + this.emailPrice)
      this.$emit('updatePerEmailCost', charge)
      return charge
    },
    vuexLocationSMTPServices() {
      return this.$store.state.smtpServices.locationSMTPServices
    },
    vuexCompanySMTPServices() {
      return this.$store.state.smtpServices.companySMTPServices
    },
    rebillingEnabled: {
      get(): boolean {
        return this.settings.enabled
      },
      set(value: boolean) {
        const updatedSettings = {
          enabled: value,
          markup: this.markupPercent,
          price: this.emailPrice,
          companyId: this.company.id,
        }
        this.emitUpdatedSettings(updatedSettings)
      },
    },
  },
  data() {
    return {
      markup: '1',
      billRange: {
        min: 1,
        max: 10,
        step: 0.1,
      },
      unsavedChanges: false,
      basePrice: '0.0001',
      DOLLAR_AMOUNT: 10,
      emailPriceUpdated: null as null | DebouncedFunc<() => void>,
      smtpServices: [] as IService[],
      mailGunAccount: undefined as MailGunAccount | undefined,
      currentLocationId: '',
      provider: {
        providerName: '',
        providerType: '',
        email: 'No Provider',
        id: 'No Provider',
      },
    }
  },
  methods: {
    rebillingDataUpdated() {
      this.unsavedChanges = true
      if (this.autoSave) {
        this.updateRebillingSettings()
      }
    },
    reset() {
      this.$emit('reset')
    },

    countEstimateFromDollarAmount(charge: number): number {
      if (charge <= 0) return 0

      const count = Math.round(this.DOLLAR_AMOUNT / charge)
      return Math.round(count / 5) * 5
    },
    async companyProvider(): Promise<IService> {
      this.mailGunAccount = await MailGunAccount.getByCompanyId(this.company.id)
      if (this.mailGunAccount) {
        this.smtpServices.push({
          providerName: 'mailgun',
          providerType: 'mailgun',
          email: this.mailGunAccount.domain,
          id: 'mailgun',
        })
      }

      // find other smtp services from vuex for company
      if (this.vuexCompanySMTPServices)
        this.smtpServices.push(
          ...this.vuexCompanySMTPServices.map(d => {
            let serv = new SMTPService(d)
            return {
              providerName: serv.providerName,
              providerType: serv.providerType,
              email: serv.email,
              id: serv.id,
            }
          })
        )

      return this.smtpServices.find(
        serv => this.company.default_email_service == serv.id
      ) as IService
    },
    computeMarkupAmountFromPercent(markupPercent: number): string {
      return `${(markupPercent * 100) / 10000 + 1}`
    },
    computeMarkupProfit(charge: number): string {
      const profit = parseFloat(
        `${((this.markupPercent * 100) / 10000) * charge}`
      )
      return this.readableFloat(profit)
    },
    async fetchData() {
      this.smtpServices = []
      // Check condition for agency's saas-config page
      if (!this.locationId) {
        this.provider = (await this.companyProvider()) as IService
      } else {
        // searching mailgun for location
        this.mailGunAccount = await MailGunAccount.getByLocationId(
          this.locationId
        )

        // if found store in smtpServices
        if (this.mailGunAccount) {
          this.smtpServices.push({
            providerName: 'mailgun',
            providerType: 'mailgun',
            email: this.mailGunAccount.domain,
            id: 'mailgun',
          })
        }

        // find other smtp services from vuex for perticuar location
        if (this.vuexLocationSMTPServices)
          // if found -> push it into smtpServices arrray
          this.smtpServices.push(
            ...this.vuexLocationSMTPServices.map(d => {
              let serv = new SMTPService(d)
              return {
                providerName: serv.providerName,
                providerType: serv.providerType,
                email: serv.email,
                id: serv.id,
              }
            })
          )

        /*if smtpServices is still empty it means
        location do not have services setup
         in this case we will show agency's provider info */
        if (this.smtpServices.length == 0) {
          this.provider = (await this.companyProvider()) as IService
        } else {
          let defaultProvider = this.smtpServices.find(
            serv => this.location.defaultEmailService == serv.id
          )
          this.provider = defaultProvider as IService

          // set default agency's smtp if they do not have ay selected provider
          this.provider = defaultProvider
            ? defaultProvider
            : ((await this.companyProvider()) as IService)
        }
      }
    },
    readableFloat(amount: number): string {
      return parseFloat(amount.toFixed(6)).toString()
    },
    updateRebillingSettings() {
      if (!this.unsavedChanges || !this.rebillingEnabled) return

      const updatedSettings = {
        enabled: this.rebillingEnabled,
        markup: this.markupPercent,
        price: this.emailPrice,
        companyId: this.company.id,
      }

      this.emitUpdatedSettings(updatedSettings)
    },
    emitUpdatedSettings(updatedSettings: any) {
      this.$emit('update', updatedSettings)
      this.unsavedChanges = false
    },
  },
  watch: {
    settings: function (newValue) {
      if (newValue.markup) {
        this.markup = this.computeMarkupAmountFromPercent(newValue.markup)
        this.basePrice = `${newValue.price}`
      }
    },
    '$route.params.location_id': async function (id) {
      this.locationId = id
    },
    async vuexLocationSMTPServices() {
      await this.fetchData()
    },
    async vuexCompanySMTPServices() {
      await this.fetchData()
    },
  },
})
</script>

<style lang="scss">
@import './dashboard/lightscroll.scss';
</style>

<style scoped>
.card-header {
  display: flex;
  flex-wrap: nowrap;
  justify-items: space-between;
  padding: 25px 30px;
}

.card-header .title {
  max-width: 70%;
}

.card-header h3 {
  margin-bottom: 12px;
}

.card-header p {
  line-height: 16px;
  font-size: 12px;
}

.card-header .toggle {
  display: flex;
  align-items: center;
}

.card-header .toggle span {
  margin-right: 12px;
}

.card-body > span.description {
  color: lightslategray;
  font-size: 14px;
}

.card-body {
  position: relative;
  z-index: 1;
}

/* .card-body.disabled * {
  color: #4A5568 !important;
} */

.card-body .mask {
  position: absolute;
  width: 100%;
  height: 100%;
  background: lightgray;
  z-index: 2;
  top: 0;
  left: 0;
  cursor: not-allowed;
  opacity: 0.5;
}

.profit-level {
  display: flex;
  margin: 30px 0 20px 0;
  justify-content: space-between;
}

.profit-level h6.label {
  margin: 0;
  /* margin-top: 5px; */
  font-weight: bold;
}

.profit-level > span {
  width: 100px;
}

.profit-level .slider {
  width: calc(100% - 100px);
  position: relative;
}

.profit-level .slider input {
  width: 100%;
}

.profit-level .slider span:last-child {
  float: right;
}

.profit-level .slider .value {
  text-align: center;
  position: absolute;
  left: 0;
  top: -25px;
  background: #2d3748;
  border-radius: 2px;
  color: white;
  padding: 3px;
  border-radius: 2px;
  font-size: 12px;
  line-height: 16px;
  width: 40px;
  transition: opacity 0.25s ease-in;
  opacity: 0;
}

#markup:focus + span.value {
  opacity: 1;
}
.tooltip__invalid-value {
  position: absolute;
  left: 40px;
  top: -30px;
  height: 20px;
  z-index: 5;
}
.profit-level .slider .value:before {
  content: '';
  width: 0px;
  height: 0px;
  position: absolute;
  border-left: 5px solid #2d3748;
  border-right: 5px solid transparent;
  border-top: 5px solid #2d3748;
  border-bottom: 5px solid transparent;
  left: calc(50% - 5px);
  top: 14px;
  transform: rotate(225deg);
}

.profit-boxes .row {
  padding: 6px 0;
  align-items: center;
}

.profit-boxes .row .label {
  width: 40%;
  text-align: right;
  margin-right: 30px;
}

.profit-boxes .row .data {
  display: inline-flex;
}

.profit-boxes .row span:first-child {
  margin-right: 3px;
}

.profit-boxes .row span:last-child {
  margin-left: 3px;
}

.profit-boxes .row.profit {
  font-weight: bold;
  color: #38b2ac;
  padding-bottom: 0;
}

.profit-boxes .row .data input {
  border: none;
  border-bottom: 1px solid;
  padding: 0;
}

.profit-boxes .row .data input:focus {
  outline: none;
}

.save-changes {
  text-align: right;
}

.save-changes .warning {
  color: #fd9725;
  margin: 12px 0;
  font-size: 10px;
}

.save-changes .reminder {
  font-size: 16px;
}

.save-changes button {
  margin-left: 12px;
  background: #e2e8f0;
  color: #4a5568;
  padding: 8px 12px;
  border-radius: 3px;
}

.save-changes button.unsaved {
  background: #178af6;
  color: white;
}

.tc {
  font-style: italic;
  font-size: 11px;
  margin-top: 12px;
}

.email-price-input {
  width: 64px;
  background: rgba(255, 255, 255, 0.85) !important;
  border: 1px solid #d1d5db !important;
  -webkit-box-sizing: border-box !important;
  box-sizing: border-box;
  border-radius: 3px;
  outline: none;
  text-align: center;
  margin: 0px 8px;
}

#markup {
  -webkit-appearance: none;
}

#markup:focus {
  outline: none;
}

#markup::-moz-range-thumb {
  border: 6px solid #38b2ac;
  height: 10px;
  width: 20px;
  border-radius: 25px;
  background: #b2f5ea;
}

input[type='range']#markup::-webkit-slider-thumb {
  -webkit-appearance: none !important;
  border: 6px solid #38b2ac;
  height: 19px;
  width: 26px;
  border-radius: 25px;
  background: #b2f5ea;
  margin-top: -8px;
}

#markup::-webkit-slider-runnable-track {
  height: 6px;
  cursor: pointer;
  background: #38b2ac;
  border-radius: 17px;
  border: 0.2px solid #38b2ac;
}

#markup::-moz-range-track {
  height: 6px;
  cursor: pointer;
  border-radius: 17px;
}

input[type='range']#markup::-moz-range-progress {
  background: #38b2ac;
  height: 6px;
  border-radius: 17px;
}
input[type='range']#markup::-moz-range-track {
  background: #e5e7e6;
}
.card-verify--error {
  text-align: center;
  color: #e93d3d;
}

.error {
  color: rgb(255, 34, 34) !important;
  border: 1px solid rgb(255, 34, 34) !important;
}
</style>

<style lang="scss">
@import './dashboard/slider.scss';
span.value {
  position: absolute;
  text-align: center;
  background: #2d3748;
  border-radius: 5px;
  color: white;
  padding: 4px 6px;
  border-radius: 2px;
  font-size: 12px;
  line-height: 16px;
  white-space: nowrap;
  transition: opacity 0.25s ease-in;
  transform: translateX(-50%);
  opacity: 0.8;
  &:before {
    content: '';
    width: 0px;
    height: 0px;
    position: absolute;
    border-left: 5px solid #2d3748;
    border-right: 5px solid transparent;
    border-top: 5px solid #2d3748;
    border-bottom: 5px solid transparent;
    left: calc(50% - 5px);
    top: 18px;
    transform: rotate(225deg);
  }
}
</style>
