<template>
  <div class="hl_activity-history--item d-flex flex-column pl-0">
    <div class="d-flex">
      <i class="position-static icon fas mr-3" :class="`${icon} ${color}`"> </i>
      <div class="hl_activity-history--item-header">
        <h4>{{ heading }}</h4>
        <p v-if="user">
          By:
          <strong>{{ user.fullName }}</strong>
        </p>
        <p>{{ date }}</p>
      </div>
      <i
        class="fa fa-info-circle ml-2"
        aria-hidden="true"
        data-toggle="collapse"
        :data-target="`#collapse-${audit.id}`"
        aria-expanded="false"
        :aria-controls="`collapse-${audit.id}`"
      ></i>
    </div>
    <div class="collapse" :id="`collapse-${audit.id}`" data-parent="#auditLogs">
      <div class="card card-body p-0">
        <span class="my-1 line-bottom">
          <strong>Made by: </strong>
          <a
            v-if="madeBy.link"
            :id="`madeBy-${audit.id}`"
            :href="madeBy.link"
            target="_blank"
          >
            {{ madeBy.text }}
          </a>
          <span v-else-if="madeBy.text">{{ madeBy.text }}</span>
        </span>
        <div v-if="audit.type === 'UPDATED'" class="d-flex flex-column">
          <span class="my-1 line-bottom"><strong>Changes:</strong></span>
          <table>
            <thead>
              <tr>
                <th>Field</th>
                <th>Before</th>
                <th>After</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="change in changes" :key="change.key">
                <td>{{ change.field }}</td>
                <td>{{ change.before }}</td>
                <td>{{ change.after }}</td>
              </tr>
              <tr v-for="change in changesCustomFields" :key="change.key">
                <td>{{ change.field }}</td>
                <td>{{ change.before }}</td>
                <td>{{ change.after }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <hr />
    </div>
  </div>
</template>

<script lang="ts">
import { User } from '@/models'
import moment from 'moment'
import Vue from 'vue'
import * as lodash from 'lodash'

enum types {
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
  DELETED = 'DELETED',
  RESTORED = 'RESTORED',
}

const excludedChanges = [
  'date_updated',
  'last_session_activity_at',
  'birth_month',
  'birth_day',
  'custom_fields',
]

const AppSources = ['WEB_USER', 'MOBILE_USER']

const InternalApiSources = [
  'TRIGGER',
  'WORKFLOW',
  'CAMPAIGN',
  'FORM',
  'SURVEY',
  'CALENDAR',
  'PUBLIC_API',
  'BULK_REQUEST',
]

export default Vue.extend({
  props: ['audit'],
  computed: {
    icon(): string {
      switch (this.audit.type) {
        case types.CREATED:
          return 'fa-plus'
        case types.UPDATED:
          return 'fa-edit'
        case types.DELETED:
          return 'fa-trash-alt'
        case types.RESTORED:
          return 'fa-trash-restore'
        default:
          return 'fa-times'
      }
    },
    color(): string {
      switch (this.audit.type) {
        case types.CREATED:
          return '--green'
        case types.UPDATED:
          return '--orange'
        case types.DELETED:
          return '--red'
        case types.RESTORED:
          return '--teal'
        default:
          return '--green'
      }
    },
    heading(): string {
      switch (this.audit.type) {
        case types.CREATED:
          return 'Contact created'
        case types.UPDATED:
          return 'Contact updated'
        case types.DELETED:
          return 'Contact deleted'
        case types.RESTORED:
          return 'Contact restored'
        default:
          return 'Unknow'
      }
    },
    date(): string {
      return moment(this.audit.createdAt).format('MM/DD/YYYY h:mm A')
    },
    changes(): { [key: string]: any }[] {
      const keys = lodash
        .uniq([
          ...Object.keys(this.audit.before),
          ...Object.keys(this.audit.after),
        ])
        .filter((key: string) => !excludedChanges.includes(key))
      const map = keys.map((key: string) => {
        let before = this.audit.before[key]
        let after = this.audit.after[key]

        if (key === 'date_of_birth') {
          if (before) before = moment.utc(before).format('MM/DD/YYYY')
          if (after) after = moment.utc(after).format('MM/DD/YYYY')
        }

        if (key === 'tags') {
          if (before) before = before.join(', ')
          if (after) after = after.join(', ')
        }

        return {
          key,
          field: lodash.startCase(key.toLowerCase()),
          before,
          after,
        }
      })
      return map
    },
    changesCustomFields(): { [key: string]: any }[] {
      let arrayBefore = this.audit.before?.custom_fields ?? []
      let arrayAfter = this.audit.after?.custom_fields ?? []

      const ids = lodash.uniq([
        ...arrayBefore.map((e: { [key: string]: any }) => {
          return e.id
        }),
        ...arrayAfter.map((e: { [key: string]: any }) => {
          return e.id
        }),
      ])

      const map = ids
        .map((id: string) => {
          let before = lodash.find(arrayBefore, { id: id })
          let after = lodash.find(arrayAfter, { id: id })

          return {
            id,
            field: before?.field_name || after?.field_name,
            before: before?.field_value,
            after: after?.field_value,
          }
        })
        .filter(obj => {
          return obj.after !== obj.before
        })

      return map
    },
  },
  data() {
    return {
      user: undefined as User | undefined,
      showInfo: false,
      madeBy: {
        link: '',
        text: '',
      },
    }
  },
  methods: {
    openInfoModal() {
      this.showInfo = true
    },
    closeInfoModal() {
      this.showInfo = false
    },
    createLink() {
      let routeData
      switch (this.audit.source) {
        case 'BULK_REQUEST':
          routeData = this.$router.resolve({
            name: 'bulk_imports_log',
            query: { id: this.audit.sourceId },
          })
          break
      }

      this.madeBy.link = routeData?.href ? routeData.href : ''
    },
  },
  created() {
    try {
      if (AppSources.includes(this.audit.source)) {
        const source = this.audit.source.split('_')[0]
        User.getById(this.audit.sourceId).then(user => {
          this.madeBy.text = `${user.name} - ${user.email} (${source})`
        })
      } else if (InternalApiSources.includes(this.audit.source)) {
        const source = lodash.startCase(this.audit.source.toLowerCase())
        this.madeBy.text = `${source}`
        this.createLink()
      }
    } catch (error) {}
  },
})
</script>

<style scoped>
.fa-info-circle:hover {
  color: #424446 !important;
}
</style>
