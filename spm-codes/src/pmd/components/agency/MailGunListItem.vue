<template>
	<tr>
		<td>{{location.name}}</td>
		<td>
			<input
				type="text"
				class="form-control"
				placeholder="API Key"
				v-model="apiKey"
				v-validate="'required'"
				name="apiKey"
				@change="edited=true"
>
			<span v-show="errors.has('apiKey')" class="--red">API Key Required</span>
		</td>
		<td>
			<div style="display: inline-block;position: relative;" :class="{invisible: !edited}">
				<button
					:class="{invisible: processing}"
					type="button"
					class="btn btn-success"
					@click.prevent="saveAccount"
				>Save</button>
				<div
					style="position: absolute;left: 50%;transform: translate(-50%, -50%);top: 50%;"
					v-show="processing"
				>
					<moon-loader :loading="processing" color="#37ca37" size="30px"/>
				</div>
			</div>
		</td>
	</tr>
</template>

<script lang="ts">
import Vue from 'vue'
import { MailGunAccount } from '@/models';

export default Vue.extend({
	props: ['location'],
	data() {
		return {
			mailGunAccount: {} as MailGunAccount,
			processing: false,
			apiKey: '',
			edited: false,
		}
	},
	async created() {
		this.mailGunAccount = await MailGunAccount.getByLocationId(this.location.id);
		if (!this.mailGunAccount) {
			this.mailGunAccount = new MailGunAccount();
			this.mailGunAccount.locationId = this.location.id;
		} else {
			this.apiKey = this.mailGunAccount.apiKey
		}
	},
	methods: {
		async saveAccount() {
			const result = await this.$validator.validateAll();
			if (!result) {
				return false;
			}
			this.processing = true;

			this.mailGunAccount.apiKey = this.apiKey && this.apiKey.trim()
			this.mailGunAccount.mailgunError = undefined
			await this.mailGunAccount.save();

			try {
				let response = await this.$http.get('/mailgun/set_reply_webhook?location_id=' + this.location.id);
			} catch (err) {
				console.error("Error creating webhook:", err)
			}

			this.edited = false;
			this.processing = false;
		}
	}
})
</script>

