<template>
  <div class="col-md-6 col-lg-4 mb-3">
    <div class="card">
      <div class="card-header">
        <div class="d-flex">
          <h2 class="mr-0">Time Spent</h2>
          <div class="ml-2 mt-1">
            <span
              style="margin-top: -7px;"
              class="input-group-addon"
              v-b-tooltip.hover
              title="trigger links (embedded in message) clicked as % of messages sent"
            >
              <i class="fas fa-question-circle"></i>
            </span>
          </div>
        </div>
      </div>
      <div class="card-body">
        <table class="table">
          <tbody>
            <tr>
              <td>Total time logged</td>
              <td>10H 45M</td>
              <td>
                <span>
                  <i class="fas fa-arrow-down text-danger"/> 5%
                </span>
              </td>
            </tr>
            <tr>
              <td>Avg. Time/Day</td>
              <td>6H 23M</td>
              <td>
                <span>
                  <i class="fas fa-arrow-up text-success"/> 7%
                </span>
              </td>
            </tr>
            <tr>
              <td>Call</td>
              <td>25%</td>
              <td>
                <span>
                  <i class="fas fa-arrow-up text-success"/> 17%
                </span>
              </td>
            </tr>
            <tr>
              <td>Email</td>
              <td>50%</td>
              <td>
                <span>
                  <i class="fas fa-arrow-down text-danger"/> 5%
                </span>
              </td>
            </tr>
            <tr>
              <td>Text</td>
              <td>25%</td>
              <td>
                <span>
                  <i class="fas fa-arrow-up text-success"/> 17%
                </span>
              </td>
            </tr>
            <tr>
              <td>Misc</td>
              <td>11%</td>
              <td>
                <span>
                  <i class="fas fa-arrow-up text-success"/> 17%
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({})
</script>

<style scoped>

</style>