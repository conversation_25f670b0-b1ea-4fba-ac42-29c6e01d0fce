<template>
  <div class="power-dialer-main">
    <div class="power-dialer-header">
      <div class="call-section-box">
        <div class="call-section">
          <div>
            <p>Call Ended</p>
            <span>00:{{timerFullTime}}</span>
          </div>
          <div class="name-box">
            <span class="name" v-if="!isUserNotAssigned">
              <i class="icon icon-share-2" @click="loadDetailPage"></i>
            </span>
            <div>
              {{callContactName}}
            </div>
          </div>
          <div class="number">
              <PhoneNumber
                type="display"
                placeholder="Phone Number1"
                v-model="numberToDial"
                :currentLocationId="currentLocationId"
              />
            </div>
        </div>
      </div>
    </div>
    <div class="power-dialer-body">
      <div class="call-end-info" v-if="manualActionsData.length > 0">
          <span style="margin-right: 10px;">
            <i>
            <svg width="15" height="18" viewBox="0 0 15 18" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path opacity="0.27" d="M1.62896 1.49661C1.84872 1.36534 2.06486 1.21857 2.29615 1.10908C3.15032 0.704361 4.16528 0.977128 4.7221 1.75422C4.91863 2.02869 5.01962 2.33892 5.10215 2.66702C5.21039 3.09797 5.52948 4.3484 5.56737 4.52071C5.75386 5.37647 5.39325 6.29438 4.61996 6.75597C4.46362 6.84945 4.30383 6.93492 4.13398 6.9637C3.94453 6.99588 2.98591 7.44589 3.30419 8.62975C3.51917 9.15026 3.76645 9.66276 4.047 10.165C4.31552 10.6467 4.60793 11.1053 4.92127 11.5413L4.94351 11.5638C5.77643 12.4412 6.63291 11.8079 6.75481 11.6544C6.86321 11.5168 7.01543 11.4165 7.17144 11.3234C7.94505 10.8618 8.89461 10.998 9.51782 11.5932C9.64384 11.7133 10.5316 12.624 10.8389 12.9361C11.0726 13.1738 11.282 13.4192 11.4136 13.7326C11.7869 14.6194 11.507 15.6645 10.7415 16.2269C10.5341 16.3791 10.3031 16.499 10.0832 16.6302C9.0208 17.2645 7.75379 17.2011 6.77804 16.5786C4.78734 15.383 3.06384 13.6506 1.83027 11.4429C0.572323 9.19061 -0.0164539 6.72822 0.00034952 4.30175C0.00825699 3.17561 0.697031 2.0527 1.62896 1.49661ZM13.9821 6.94122C14.544 6.94122 15 7.41218 15 7.99313C15 8.57408 14.544 9.04537 13.9821 9.04537C13.42 9.04537 12.9642 8.57391 12.9642 7.99313C12.9642 7.41235 13.42 6.94122 13.9821 6.94122ZM10.8225 6.94122C11.3845 6.94122 11.8406 7.41218 11.8406 7.99313C11.8406 8.57408 11.3845 9.04537 10.8225 9.04537C10.2605 9.04537 9.80479 8.57391 9.80479 7.99313C9.80479 7.41235 10.2605 6.94122 10.8225 6.94122ZM7.66302 6.94122C8.22494 6.94122 8.68078 7.41218 8.68078 7.99313C8.68078 8.57408 8.22494 9.04537 7.66302 9.04537C7.10093 9.04537 6.64526 8.57391 6.64526 7.99313C6.64526 7.41235 7.10093 6.94122 7.66302 6.94122Z" fill="black"/>
          </svg>
            </i>
          </span>
          <p>Next Call in<strong> {{timeLeft}}(s)</strong></p>
        </div>
    </div>
    <div class="power-dialer-footer">
      <div class="call-end-btn" v-if="manualActionsData.length > 0">
          <button
            type="button"
            role="button"
            class="snooze"
            v-on:click="setManualScreen"
          >
          <i>
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M14.4531 14.6314L13.3925 15.692C13.2031 15.8804 12.6484 15.9996 12.6318 15.9996C9.27431 16.0279 6.04186 14.7096 3.66633 12.3336C1.28398 9.95188 -0.0358831 6.70684 0.000742399 3.33975C0.000742399 3.33975 0.122338 2.79873 0.311309 2.61124L1.37189 1.54974C1.76105 1.16106 2.50618 0.985285 3.02816 1.15912L3.25179 1.23431C3.77376 1.40815 4.31918 1.98429 4.46371 2.51455L4.99791 4.47447C5.14294 5.0057 4.94809 5.76156 4.55943 6.1502L3.85042 6.85918C4.54574 9.43627 6.56677 11.4567 9.1425 12.153L9.85151 11.444C10.2402 11.0553 10.998 10.861 11.5293 11.0055L13.4893 11.5407C14.0186 11.6842 14.5948 12.2291 14.7696 12.7516L14.8438 12.9752C15.0176 13.4977 14.8418 14.2428 14.4531 14.6314ZM16 0H14V6.9998H16V0ZM11.9999 0H9.99992V6.9998H11.9999V0Z" fill="#EB5757"/>
          </svg>

          </i>
            SNOOZE
          </button>
          <button
            type="button"
            role="button"
            class="callnow"
            @click="callNext"
          >
            CALL NOW
          </button>
        </div>
        <div v-if="callTabs[callTabs.currentTab].activeTab !== 'manual' || !manualActionsData.length">
          <button
            type="button"
            role="button"
            class="done-btn"
            @click="$emit('leaveCallEnded')"
          >
            DONE
          </button>
        </div>
    </div>
  </div>

</template>

<script lang="ts">
import Vue from 'vue'
import PhoneNumber from '@/pmd/components/util/PhoneNumber.vue';
import {
  ManualQueue,
  MessageType,
  Contact,
  User
} from '../../../models';
let cancelManualqueueSubscription: () => void
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'

export default Vue.extend({
  props: ['phoneNumbers', 'currentLocationId', 'numberToDial', 'manualActionsData', 'callTabs'],
  components: { PhoneNumber },
  data() {
    return {
      manualQueue: [] as ManualQueue[],
      manualQueueItem: undefined as ManualQueue | undefined,
      timerId: undefined as NodeJS.Timeout | undefined,
      timeLeft: 15
    }
  },
  watch: {
    timeLeft(){
      if (this.timeLeft === 0) {
        if (this.timerId) clearInterval(this.timerId);
        this.callNext();
      }
    },
    manualActionsData() {
      if(this.manualActionsData.length > 0){
        this.timerId = setInterval(()=>{
            if (this.timeLeft !== 0)
              this.timeLeft--;
        },1000);
      }
    }
  },
  computed: {
    dialOutNumber: {
      // getter
      get: function(): string | undefined {
        return this.$store.state.numbers.dialOutNumber
      },
      //setter
      set: function(newValue: string | undefined) {
        if (newValue) {
          this.$store.commit('numbers/setDialOutNumber', newValue)
        }
      }
    },
    timerMinutes(): number {
      return Math.floor(this.callTabs.lastCallDetails.time / 60)
    },
    timerSeconds(): number {
      return this.callTabs.lastCallDetails.time - this.timerMinutes * 60
    },
    timerFullTime(): string {
      return (
        this.timerMinutes.toLocaleString(undefined, {
          minimumIntegerDigits: 2
        }) +
        ':' +
        this.timerSeconds.toLocaleString(undefined, { minimumIntegerDigits: 2 })
      )
    },
    callContact() {
      return this.callTabs.lastCallDetails.callContact;
    },
    callContactName(): string {
      if(this.isUserNotAssigned) return 'unknown';
      return this.callContact && this.callContact.fullName ? this.callContact.fullName : 'unknown'
    },
    isUserNotAssigned() {
      if (!this.callContact) return true;
      return this.user && this.user.permissions.assigned_data_only && this.callContact.assignedTo !== this.user.id;
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  methods:{
    setManualScreen(){
      if (this.timerId) clearInterval(this.timerId);
      this.$emit('leaveCallEnded')
    },
    async callNext() {
       if(this.manualActionsData.length){
        this.manualQueueItem = this.manualActionsData[0];
        const contact = await Contact.getById(this.manualQueueItem.contactId);
        this.manualQueueItem.ref.update({
          'in_progress': true
        });
        this.$root.$emit('makeCall', {
          phone_number: contact.phone,
          manualCallId: this.manualQueueItem.id,
          contact,
          manualCallCampaignId: this.manualQueueItem.campaignId,
          showModal: true
        });
        this.$emit('leaveCallEnded');
       }
    },
    loadDetailPage(id: string) {
      if (!this.callTabs.lastCallDetails.callContact) return
      this.$router.push({
        name: this.getSideBarVersion == 'v2' ? 'contact_detail-v2': 'contact_detail',
        params: { contact_id: this.callTabs.lastCallDetails.callContact.id }
      })
    },
  }
})
</script>
<style lang="scss" scoped>
.call-section-box {
	.call-section {
		border: 1px solid rgba(196, 196, 196, 0.55);
		box-sizing: border-box;
		border-radius: 5px;
		height: 175px;
		width: 268px;
		text-align: center;
		padding: 20px;
		p {
			font-style: normal;
			font-weight: normal;
			font-size: 23px;
			line-height: 27px;
			letter-spacing: 0.06em;
			color: #000000;
			margin-bottom: 10px;
		}
		span {
			font-style: normal;
			font-weight: normal;
			font-size: 25px;
			line-height: 29px;
			color: #555555;
			opacity: 0.35;
		}
		.name-box {
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 14px;
			line-height: 16px;
			color: #555555;
			border-top: 1px solid #d9d9d9;
      padding-top: 5px;
      margin-top: 10px;
			span {
				font-size: 13px;
				color: #2284FF;
				opacity: 1;
				margin-right: 8px;
				height: 25px;
				width: 25px;
				background: #F6F6F6;
				border-radius: 50%;
				display: flex;
				justify-content: center;
				align-items: center;
				padding-left: 4px;
				margin-left: -20px;
        cursor: pointer;
			}
		}
		.number {
			margin-top: 0;
			span {
				font-weight: normal;
				font-size: 14px;
				line-height: 16px;
				text-align: center;
				color: #555555;
				opacity: 1;
			}
		}
	}
}

.call-end-btn {
	display: flex;
	justify-content: space-between;
	button {
		width: 129px;
		height: 37px;
		&.snooze {
			background: #FFFFFF;
			border: 1px solid rgba(235, 87, 87, 0.35);
			box-sizing: border-box;
			border-radius: 34px;
			font-weight: 500;
			font-size: 12px;
			letter-spacing: 0.1em;
			text-transform: uppercase;
			color: #EB5757;
			padding: 7px;
			i {
				margin-right: 5px;
			}
		}
		&.callnow {
			background: rgba(39, 174, 96, 0.07);
			border: 1px solid #27AE60;
			box-sizing: border-box;
			border-radius: 42px;
			font-size: 12px;
			line-height: 14px;
			text-align: center;
			letter-spacing: 0.1em;
			text-transform: uppercase;
			color: #27AE60;
			padding: 12px;
		}
	}
}
.call-end-info {
	display: flex;
	height: 100%;
	justify-content: center;
	align-items: center;
}
button.done-btn {
    background: rgba(191, 191, 191, 0.17);
    border-radius: 42px;
    width: 268px;
    height: 37px;
    box-shadow: none;
    border: none;
    font-weight: 500;
    font-size: 12px;
    line-height: 14px;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: #717171;

}

</style>

