<template>
  <tbody class="table-with-collapse">
  <tr
    :class="{ active: showingPages, 'table-collapse-toggle': isFunnelAndHasPages }"
    @click="toggleShowingPages()"
  >
    <td>
      <i v-if="isFunnelAndHasPages || isWebsiteAndHasProducts" class="icon-arrow-right-1"></i>
      <i class="icon icon-mail"></i>
      {{ funnelStepStat.funnel_step_name }}
    </td>
    <td>{{ funnelStepStat.page_views_all || '-' }}</td>
    <td>{{ funnelStepStat.page_views_unique || '-' }}</td>
    <td>{{ funnelStepStat.optins_all || '-' }}</td>
    <td>{{ funnelStepStat.optins_rate ? `${funnelStepStat.optins_rate}%` : '-' }}</td>
    <td>{{ funnelStepStat.sale_count || '-' }}</td>
    <td>{{ funnelStepStat.sale_rate ? `${funnelStepStat.sale_rate}%` : '-' }}</td>
    <td>{{ funnelStepStat.sale_value ? `$${funnelStepStat.sale_value}` : '-' }}</td>
    <td>{{ funnelStepStat.earnings_per_page_view_all ? `$${funnelStepStat.earnings_per_page_view_all}` : '-' }}</td>
    <td>{{ funnelStepStat.earnings_per_page_view_unique ? `$${funnelStepStat.earnings_per_page_view_unique}` : '-' }}</td>
  </tr>
  <template v-if="showingPages && !isWebsite" v-for="page in funnelStepStat.page_stats">
    <FunnelPageStats
      :funnel-page-stats="page"
    />
  </template>
  </tbody>
</template>
<script lang="ts">import Vue from 'vue'
import axios from 'axios'
import FunnelPageStats from './FunnelPageStats.vue'
export default Vue.extend({
  components: { FunnelPageStats },
  props: {
    funnelStep: {
      type: Object
    },
    funnelStepStat: {
      type: Object
    },
    isWebsite: {
      type: Boolean
    }
  },
  data() {
    const isFunnelAndHasPages = !this.isWebsite
      && this.funnelStepStat.page_stats
      && this.funnelStepStat.page_stats.length
    const isWebsiteAndHasProducts = this.isWebsite
      && this.funnelStepStat.products
      && this.funnelStepStat.products.length
    return {
      showingPages: false,
      isFunnelAndHasPages,
      isWebsiteAndHasProducts
    }
  },
  methods: {
    toggleShowingPages: function() {
      this.showingPages = !this.showingPages
    }
  }
})
</script>
