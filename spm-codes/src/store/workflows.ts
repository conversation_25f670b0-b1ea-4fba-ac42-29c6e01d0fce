import { Module, ActionContext } from 'vuex'
import { RootState, WorkflowState } from './state_models'
import { Campaign, Workflow } from '../models'

export const workflows: Module<WorkflowState, RootState> = {
  namespaced: true,
  state: { workflows: [], locationId: undefined },
  getters: {
    getById: (state: WorkflowState) => {
      return (id: string) => state.workflows.find(x => x.id === id)
    },
    getAll: (state: WorkflowState) => {
      return state.workflows
    },
  },
  mutations: {
    addUpdate: (state: WorkflowState, payload: { workflows: any, locationId: string }) => {
      state.workflows = payload.workflows
      if (payload.locationId) {
        state.locationId = payload.locationId
      }
    },
    clearAll: (state: WorkflowState) => {
      state.workflows = []
      state.locationId = undefined
    }
  },
  actions: {
    async syncAll(
      context: ActionContext<WorkflowState, RootState>,
      locationId: string
    ) {
      if(!locationId) {
        context.commit('clearAll')
        return
      }


      if (context.state.locationId === locationId) {
        return context.state.workflows;
      }

      context.commit('clearAll')
      
      return new Promise(async (resolve, reject) => {
        try {
          const workflows = await Workflow.getWithLocationId(locationId)

          context.commit('addUpdate', {
            locationId,
            workflows
          })

          resolve(context.state.workflows.workflows)
        } catch (err) {
          resolve([])
          // reject(err)
        }
      })
    },
    logout: {
      root: true,
      handler: async (context: ActionContext<WorkflowState, RootState>) => {
        context.commit('clearAll')
      }
    },
    clearAll: {
      handler: async (context: ActionContext<WorkflowState, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
