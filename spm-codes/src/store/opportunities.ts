import { Module, ActionContext } from 'vuex'
import Vue from 'vue'
import { Opportunity, Contact } from '../models'
import { RootState, OpportunityState } from './state_models'
import axios from 'axios'
import hasValidOpportunityFilter from '../util/opportunity_filter'
import { trackPendoEvent } from '../util/helper'

let opportunityListner
const limit = 15; // Number of records to be fetch from opp. API

function removeAggregates(state: OpportunityState, opportunity: any) {
  opportunity = Object.assign({}, opportunity)
  let oppStageData = state.opportunitiesStageData[opportunity.pipeline_stage_id]
  if (!oppStageData) {
    oppStageData = {}
    oppStageData.totalCount = 0
    oppStageData.revenues = 0
  } else {
    oppStageData = Object.assign({}, oppStageData)
  }
  if (oppStageData) {
    if (oppStageData.totalCount > 0) {
      oppStageData.totalCount--;
      oppStageData.revenues -= (parseFloat(opportunity.monetary_value) || 0);
    }
    Vue.set(state.opportunitiesStageData, opportunity.pipeline_stage_id, oppStageData)
  }
}

function updateAggregates(state: OpportunityState, opportunity: any) {
  let oppStageData = state.opportunitiesStageData[opportunity.pipeline_stage_id]
  if (!oppStageData) {
    oppStageData = {}
    oppStageData.totalCount = 0
    oppStageData.revenues = 0
  } else {
    oppStageData = Object.assign({}, oppStageData)
  }
  if (oppStageData) {
    oppStageData.totalCount++;
    oppStageData.revenues += (parseFloat(opportunity.monetary_value) || 0);
    Vue.set(state.opportunitiesStageData, opportunity.pipeline_stage_id, oppStageData)
  }
}

function getOpportunityContactESData(contact: Contact) {
  if (!contact) return {}

  return {
    contact_name: contact.displayName,
    email: contact.email,
    full_name: contact.fullName,
    tags: contact.tags,
    company_name: contact.companyName,
    phone:contact.phone
  }
}

export const opportunities: Module<OpportunityState, RootState> = {
  namespaced: true,
  state: {
    opportunities: [],
    locationId: undefined,
    filters: {},
    opportunitiesStageData: {},
    loadingAggregates: false,
    isReady: false
  },
  getters: {
    getById: (state: OpportunityState) => {
      return (pipelineStageId: string) =>
        state.opportunities.find(x => x.pipeline_stage_id === pipelineStageId)
    }
  },
  mutations: {
    splice: (state: OpportunityState, index: number) => {
      const opportunity = state.opportunities[index]
      state.opportunities.splice(index, 1)
      removeAggregates(state, opportunity)
    },
    spliceAndAdd: (state: OpportunityState, payload: any) => {
      const opportunity = Object.freeze(payload.opportunity)
      state.opportunities.splice(payload.index, 0, opportunity)
      updateAggregates(state, opportunity)
    },
    removeChild: (state: OpportunityState, id: { [key: string]: any }) => {
      const opportunityIndex = state.opportunities.findIndex(x => x.id === id)
      if (opportunityIndex > -1) {
        //
        // state.opportunities.splice(opportunityIndex, 1)
      }
    },
    addUpdateChild: (
      state: OpportunityState,
      payload: { [key: string]: any }
    ) => {
      // .filter(x => x.pipeline_stage_id === payload.pipeline_stage_id)
      const opportunityIndex = state.opportunities.findIndex(
        x => x.id === payload.opportunity.id
      )

      if (opportunityIndex > -1) {
        const oldOpportunity = state.opportunities[opportunityIndex]

        if (lodash.isEqual(oldOpportunity, payload.opportunity)) {

          return
        }
        if (payload.nextOpportunityId !== undefined) {
          let nextOpportunityIndex = -1
          state.opportunities.splice(opportunityIndex, 1)
          removeAggregates(state, oldOpportunity)

          if (payload.nextOpportunityId) {
            nextOpportunityIndex = state.opportunities.findIndex(
              x => x.id === payload.nextOpportunityId
            )
          }


          if (nextOpportunityIndex > -1) {
            state.opportunities.splice(
              nextOpportunityIndex,
              0,
              Object.freeze(payload.opportunity)
            )
            // if (oldOpportunity.pipeline_stage_id !== payload.pipeline_stage_id) {
            //   removeAggregates(state, payload.opportunity)
            // }
            updateAggregates(state, payload.opportunity)
          } else {
            state.opportunities.push(Object.freeze(payload.opportunity))
            updateAggregates(state, payload.opportunity)
          }
        } else {
          removeAggregates(state, oldOpportunity)
          Vue.set(state.opportunities, opportunityIndex, Object.freeze(payload.opportunity))
          updateAggregates(state, payload.opportunity)
        }
      }
    },
    loadMore: (state: OpportunityState, opportunities: any[]) => {
      state.opportunities = state.opportunities.concat(
        opportunities.filter(
          x => state.opportunities.findIndex(y => y.id === x.id) === -1
        )
      )
    },
    addUpdate: (state: OpportunityState, payload: { [key: string]: any }) => {
      state.opportunities = payload.opportunities || {}
      state.filters = payload.filters
      state.opportunitiesStageData = payload.opportunitiesStageData
      if (payload.locationId) {
        state.locationId = payload.locationId
      }
    },
    clearAll: (state: OpportunityState) => {
      if (opportunityListner) {
        opportunityListner()
      }

      state.opportunitiesStageData = {}
      state.opportunities = []
      state.locationId = undefined
      state.isReady = false
    },
    changeIsReady(state: OpportunityState) {
      state.isReady = true
    },
    updateLoadedForStage(
      state: OpportunityState,
      payload: { stageId: string; isLoaded?: boolean }
    ) {
      if (state.opportunitiesStageData[payload.stageId]) {
        let stageData = Object.assign({}, state.opportunitiesStageData[payload.stageId])
        stageData.isLoaded = payload.isLoaded || false
        Vue.set(state.opportunitiesStageData, payload.stageId, stageData)
      }
    },
    updateLoadingForStage(
      state: OpportunityState,
      payload: { stageId: string; isLoading?: boolean }
    ) {
      if (state.opportunitiesStageData[payload.stageId]) {
        let stageData = Object.assign({}, state.opportunitiesStageData[payload.stageId])
        stageData.isLoading = payload.isLoading || false
        Vue.set(state.opportunitiesStageData, payload.stageId, stageData)
      }
    },
    updateAggregatesDataForStage(state: OpportunityState, payload: any) {
      state.opportunitiesStageData[payload.stageId] = payload.opportunitiesStageData
    },
    updateAggregatesData(state: OpportunityState, payload: any) {
      state.opportunitiesStageData = payload
      state.loadingAggregates = false
    },
    isloadingAggregates(state: OpportunityState, loadingAggregates: boolean) {
      state.loadingAggregates = loadingAggregates
    }
  },
  actions: {
    async syncAll(
      context: ActionContext<OpportunityState, RootState>,
      locationId: string
    ) {
      const filters = lodash.cloneDeep(context.rootState.filters.filters['opportunities'])
      if (context.state.locationId === locationId && lodash.isEqual(lodash.cloneDeep(filters), lodash.cloneDeep(context.state.filters))) {
        return
      }

      context.commit('clearAll')
      let query = undefined;
      if (filters.text) {
        if(filters.text.trim().startsWith('+')) query = filters.text.replace('+', '%2B');
        else query = filters.text

      }
      const filtersEndpoint = Object.assign(
        {
          location_id: locationId,
          q: query, // since this is going in query string with URL more special chars may be required to be encoded https://www.w3schools.com/tags/ref_urlencode.ASP
          assigned_to: filters.user,
          pipeline_id: filters.pipelineId,
          campaignId: filters.campaignId,
          date: filters.date,
          endDate: filters.endDate,
          order: filters.orderBy || 'date_desc',
          limit: limit
          // country: this.location && this.location.country
        },
        filters.status !== 'all'
          ? {
              status: filters.status
            }
          : {}
      )

      if (!filters.pipelineId) {
        return
      }

      // if (pipelineStageId) {
      //   filters.pipeline_stage_id = pipelineStageId
      //   if (startAfter) {
      //     filters.startAfter = startAfter
      //   }
      // }

      const url = lodash.pickBy(
        filtersEndpoint,
        v => v !== null && v !== undefined && v !== ''
      )

      const response = await axios.get(
        '/search/opportunity/v2?' +
          Object.keys(url)
            .map(key => key + '=' + url[key])
            .join('&')
      )
      const opportunities = [] as any
      const opportunitiesStageData = {}
      response.data.aggregations.pipelines.buckets.forEach(pipeline => {
        opportunitiesStageData[pipeline.key] = {
          id: pipeline.key,
          totalCount: pipeline.doc_count,
          revenues:
            pipeline.revenues && pipeline.revenues.value
              ? pipeline.revenues.value
              : 0
        }

        pipeline.pipelines.hits.hits.forEach(elasticOpportunity => {
          opportunities.push({
            id: elasticOpportunity._id,
            ...elasticOpportunity._source
          })
        })
      })

      context.commit('addUpdate', {
        opportunities,
        opportunitiesStageData,
        filters: Object.assign({ currentLocationId: locationId }, filters),
        locationId
      })

      context.commit(
        'contacts/addToWatchList',
        opportunities.map(x => x.contact_id),
        { root: true }
      )

      context.dispatch('addRealtimeListner', {
        locationId,
        filters: Object.assign({ currentLocationId: locationId }, filters)
      })
    },
    addRealtimeListner(
      context: ActionContext<OpportunityState, RootState>,
      payload: any
    ) {
      if (opportunityListner) {
        opportunityListner()
      }

      const filters = payload.filters
      filters.locationId = payload.locationId

      opportunityListner = Opportunity.getByPipelineRealtime(
        payload.locationId,
        filters.pipelineId
      )
        .limit(5)
        .onSnapshot(
          {
            includeMetadataChanges: true
          },
          async querySnapshot => {
            if (!context.state.isReady) {
              context.commit('changeIsReady')
            } else {
              if (!querySnapshot.metadata.hasPendingWrites) {
                for (let docChange of querySnapshot.docChanges()) {



                  if (
                    docChange.type === 'modified' ||
                    docChange.type === 'added'
                  ) {
                    let opportunity = new Opportunity(docChange.doc)

                    if (opportunity.lastActionDate) {
                      context.dispatch('updateOppsInState', opportunity)
                    }
                  }
                }
              }
            }
          }
        )
    },
    async updateOppsInState(context: ActionContext<OpportunityState, RootState>, opportunity: Opportunity) {
      // If the opportunity is deleted and it is present in our list, remove it.
      if (opportunity.deleted) {
        const index = context.state.opportunities.findIndex(
          x => x.id === opportunity.id
        )
        if (index !== -1) {
          context.commit('splice', index)
        }
        return
      }

      // Add the contact data in the opp. as it will require
      let contact;
      if (opportunity.contactId) {
        contact = await context.dispatch('contacts/syncGet', opportunity.contactId, { root: true })
        trackPendoEvent('perf_contact_get', {
          source: 'opp_vuex',
          location_id: opportunity.locationId
        })
      }

      const filters = context.state.filters

      // Check if the newly change opportunity has valid filter criteria
      let hasValidSearch = await hasValidOpportunityFilter(
        opportunity,
        contact,
        filters
      )


      const newIndex = context.state.opportunities.findIndex(
        x => x.id === opportunity.id
      )

      // If the opportunity is there is the list, update/remove it
      if (newIndex !== -1) {
        if (!hasValidSearch) {
          // If it is already in the list but if it not passing the filter criteria remove from the list
          // For example when you will change the status of any opportunity and filter has some other status
          context.commit('splice', newIndex)
          context.dispatch('updateAggregates')
        } else {
          // Update the opportunity
          context.commit('addUpdateChild', {
            opportunity: {
              id: opportunity.id,
              ...opportunity.data,
              ...getOpportunityContactESData(contact)
            }
          })
          context.dispatch('updateAggregates')
        }
      } else if(hasValidSearch) {
        // If the opportunity is added and it is not in the list add it at appropiate place
        if (filters.orderBy === 'added_asc') {
          // If the data if fully loaded, not need to see the index we can add this at last
          // Our fully loaded flag will not work in some extreme case like when you will add so many opp.
          if (
            false
            // && // Temporary
            // this.baseOpportunitiesData[
            //   opportunity.pipelineStageId
            // ] &&
            // this.baseOpportunitiesData[
            //   opportunity.pipelineStageId
            // ].isLoaded
          ) {
            context.state.opportunities.push(opportunity)
          } else {
            const addIndex = context.state.opportunities.findIndex(
              x =>
                x.dateAdded && x.dateAdded.isAfter(opportunity.dateAdded) && // Check this
                x.pipelineStageId == opportunity.pipelineStageId
            )
            const lastIndex = context.state.opportunities.findIndex(
              x =>
                x.pipelineStageId == opportunity.pipelineStageId
            )

            // If we found we can add opp. to be added at any index other than last we will add it
            if (addIndex > -1 && addIndex !== lastIndex) {
              context.commit('spliceAndAdd', {
                index: addIndex,
                opportunity: {
                  id: opportunity.id,
                  ...opportunity.data,
                  ...getOpportunityContactESData(contact)
                }
              })
              context.dispatch('updateAggregates')
            }
          }
        } else {
          // Just add this at first place :)
          context.commit('spliceAndAdd', {
            index: 0,
            opportunity: {
              id: opportunity.id,
              ...opportunity.data,
              ...getOpportunityContactESData(contact)
            }
          })
          context.dispatch('updateAggregates')
          // context.state.opportunities.unshift({
          //   id: opportunity.id,
          //   ...opportunity.data
          // });
        }
      }
    },
    async updateStatus(context, payload: any) {
      // Add the contact data in the opp. as it will require
      let contact;
      if (payload.opportunity.contactId) {
        if (!payload.dontFetchContact) {
          contact = await context.dispatch('contacts/syncGet', payload.opportunity.contactId, { root: true })
          trackPendoEvent('perf_contact_get', {
            source: 'opp_vuex',
            location_id: payload.opportunity.locationId
          })
        }
      }

      const filters = context.state.filters

      // Check if the newly change opportunity has valid filter criteria
      let hasValidSearch = await hasValidOpportunityFilter(
        payload.opportunity,
        contact,
        filters
      )


      //
      const newIndex = context.state.opportunities.findIndex(
        x => x.id === payload.opportunity.id
      )

      // If the opportunity is there is the list, update/remove it
      if (newIndex !== -1) {
        if (!hasValidSearch) {
          // If it is already in the list but if it not passing the filter criteria remove from the list
          // For example when you will change the status of any opportunity and filter has some other status
          context.commit('splice', newIndex)
          // context.dispatch('updateAggregates')
        } else {
          // Update the opportunity
          context.commit('addUpdateChild', {
            opportunity: {
              id: payload.opportunity.id,
              ...payload.opportunity.data,
              ...getOpportunityContactESData(contact)
            },
            nextOpportunityId: payload.nextOpportunityId
          })
          // context.dispatch('updateAggregates')
        }
      }
    },
    updateAggregates(context: ActionContext<OpportunityState, RootState>) {
      return
      if (context.state.loadingAggregates) {
        return
      }

      context.commit('isloadingAggregates', true)

      const filters = context.rootState.filters.filters['opportunities'] || {}

      const filtersEndpoint = Object.assign(
        {
          location_id: filters.locationId,
          q: filters.text,
          assigned_to: filters.user,
          pipeline_id: filters.pipelineId,
          campaignId: filters.campaignId,
          date: filters.date,
          endDate: filters.endDate,
          order: filters.orderBy || 'date_desc'
          // country: this.location && this.location.country
        },
        filters.status !== 'all'
          ? {
              status: filters.status
            }
          : {}
      )

      const url = lodash.pickBy(
        filtersEndpoint,
        v => v !== null && v !== undefined && v !== ''
      )

      const opportunitiesStageData = {}

      axios
        .get('/aggregate/opportunity?' +
            Object.keys(url)
              .map(key => key + '=' + url[key])
              .join('&')
        )
        .then(response => {
          response.data.aggregations.pipelines.buckets.forEach(bucket => {
            const statusBucket = bucket.status.buckets[0]
            opportunitiesStageData[bucket.key] = {
              id: bucket.key,
              totalCount: statusBucket ? statusBucket.doc_count : 0,
              revenues: statusBucket ? statusBucket.revenues.value : 0,
              isLoaded: false,
              isLoading: false
            }
          })

          context.commit('updateAggregatesData', opportunitiesStageData)
        }).catch(err => {
          console.error(err)
          context.commit('isloadingAggregates', false)
        })
    },
    loadMoreData(
      context: ActionContext<OpportunityState, RootState>,
      payload: { pipelineStageId: string; startAfter: string }
    ) {
      if (
        !context.state.opportunitiesStageData[payload.pipelineStageId] ||
        context.state.opportunitiesStageData[payload.pipelineStageId].isLoading
      ) {
        return
      }

      context.commit('updateLoadingForStage', {
        stageId: payload.pipelineStageId,
        isLoading: true
      })

      const filters = Object.assign({}, context.state.filters)

      if (!filters.pipelineId) {
        return
      }

      const filtersEndpoint = Object.assign(
        {
          location_id: context.state.locationId,
          q: filters.text,
          assigned_to: filters.user,
          pipeline_id: filters.pipelineId,
          pipeline_stage_id: payload.pipelineStageId,
          campaignId: filters.campaignId,
          date: filters.date,
          endDate: filters.endDate,
          order: filters.orderBy || 'date_desc',
          limit: limit,
          startAfter: payload.startAfter
          // country: this.location && this.location.country
        },
        filters.status !== 'all'
          ? {
              status: filters.status
            }
          : {}
      )

      const url = lodash.pickBy(
        filtersEndpoint,
        v => v !== null && v !== undefined && v !== ''
      )

      axios
        .get('search/opportunity?' +
            Object.keys(url)
              .map(key => key + '=' + url[key])
              .join('&')
        )
        .then(response => {
          const opportunities = response.data.hits.hits.map(
            elasticOpportunity => {
              return {
                id: elasticOpportunity._id,
                ...elasticOpportunity._source
              }
            }
          )

          if (opportunities.length > 0) {
            context.commit('loadMore', opportunities)
            context.commit(
              'contacts/addToWatchList',
              opportunities.map(x => x.contact_id),
              { root: true }
            )
          }

          if (opportunities.length < limit) {
            context.commit('updateLoadedForStage', {
              stageId: payload.pipelineStageId,
              isLoaded: true
            })
          }

          context.commit('updateLoadingForStage', {
            stageId: payload.pipelineStageId,
            isLoading: false
          })
        })
    },
    logout: {
      root: true,
      handler: async (context: ActionContext<OpportunityState, RootState>) => {
        context.commit('clearAll')
      }
    },
    clearAll: {
      handler: async (context: ActionContext<OpportunityState, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
