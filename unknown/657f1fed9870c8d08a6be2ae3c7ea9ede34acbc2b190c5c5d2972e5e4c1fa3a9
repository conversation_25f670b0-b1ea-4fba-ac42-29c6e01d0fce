<template>
  <div class="tab-content">
    <div
      class="tab-pane fade show active"
      id="email"
      role="tabpanel"
      aria-labelledby="email-tab"
    >
      <div class="form-group">
        <UITextInputGroup
          type="text"
          placeholder="Task title"
          v-model="title"
          v-validate="'required'"
          label="Title*"
          name="taskTitle"
          ref="title"
          @input="setValue('title', title)"
          :error="errors.has('taskTitle')"
          :errorMsg="'A title is required'"
        />
      </div>
      <div class="form-group">
        <UITextAreaGroup
          rows="5"
          label="Description"
          placeholder="Task description"
          v-model="taskBody"
          name="taskBody"
          @input="setValue('body', taskBody)"
          :error="errors.has('taskBody')"
          :errorMsg="'Task description cannot be empty'"
        ></UITextAreaGroup>
      </div>
      <div class="form-group">
        <UITextLabel>Assign to</UITextLabel>
        <select
          class="selectpicker"
          title="Select"
          v-model="assignTo"
          name="assignTo"
          @change="
            setValue('assignedTo', assignTo == 'currentUser' ? '' : assignTo)
          "
        >
          <option value="currentUser">Contact's Assigned User</option>
          <option v-for="user in users" :value="user.id">{{
            user.fullName
          }}</option>
        </select>
        <span v-show="errors.has('assignTo')" class="error"
          >Task needs to be assigned to a user</span
        >
      </div>
      <div class="form-group">
        <label>Due in*</label>
        <div class="form-input-dropdown dropdown">
          <div data-toggle="dropdown">
            <i class="icon icon-arrow-down-1"></i>
            <input
              type="text"
              class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
              placeholder="Due in"
              v-model="dueDate"
              v-validate="'required|regex:^[0-9][a-zA-Z0-9 ]*$'"
              name="dueDate"
              ref="dueDate"
              data-vv-validate-on="change|custom"
            />
            <span v-show="errors.has('dueDate')" class="error"
              >A Valid Due Date is required</span
            >
          </div>
          <div class="dropdown-menu">
            <a
              class="dropdown-item trigger-type"
              href="javascript:void(0);"
              @click.prevent="dueDate = '1 day'"
            >
              <p>1 day</p>
            </a>
            <a
              class="dropdown-item trigger-type"
              href="javascript:void(0);"
              @click.prevent="dueDate = '2 days'"
            >
              <p>2 days</p>
            </a>
            <a
              class="dropdown-item trigger-type"
              href="javascript:void(0);"
              @click.prevent="dueDate = '3 days'"
            >
              <p>3 days</p>
            </a>
            <a
              class="dropdown-item trigger-type"
              href="javascript:void(0);"
              @click.prevent="dueDate = '4 days'"
            >
              <p>4 days</p>
            </a>
            <a
              class="dropdown-item trigger-type"
              href="javascript:void(0);"
              @click.prevent="dueDate = '5 days'"
            >
              <p>5 days</p>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import firebase from 'firebase/app'
import { Task, User, getCountryDateFormat } from '@/models'
import Datepicker from 'vuejs-datepicker'
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'
import moment from 'moment-timezone'

declare var $: any
let unsubscribeUsers: () => void
export default Vue.extend({
  props: ['action', 'triggerType'],
  components: {
    Datepicker
  },
  data() {
    return {
      currentTask: undefined as Task | undefined,
      taskBody: '',
      dueDate: '',
      assignTo: '',
      currentLocationId: '',
      currentAccountId: '',
      title: '',
      users: [] as User[],
      contactId: undefined as undefined | string,
      getCountryDateFormat: getCountryDateFormat
    }
  },
  watch: {
    '$route.params.account_id': function(id) {
      this.currentAccountId = id
    },
    '$route.params.location_id': function(id) {
      this.currentLocationId = id
    },
    '$route.params.contact_id': function(id) {
      this.contactId = id
    },
    '$route.query.task_id': async function(id) {
      if (id) {
        this.reset()
        this.setCurrentTask(id)
      }
    },
    dueDate: function(value) {
      this.$validator.validate('dueDate')
      this.setValue('dueDate', value.split(' ')[0])
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    })
  },
  async created() {
    if (this.$router.currentRoute.params.account_id)
      this.currentAccountId = this.$router.currentRoute.params.account_id
    if (this.$router.currentRoute.params.location_id)
      this.currentLocationId = this.$router.currentRoute.params.location_id
    if (this.$router.currentRoute.params.contact_id)
      this.contactId = this.$router.currentRoute.params.contact_id
    if (this.$route.query.task_id) {
      this.setCurrentTask(<string>this.$router.currentRoute.query.task_id)
    }
    if (this.currentLocationId) {
      this.users = this.$store.state.users.users.map(
        user => new User(Object.assign({}, user))
      )
    }

    if (this.currentAccountId) {
      unsubscribeUsers = (await User.fetchAllAgencyUsers()).onSnapshot(
        snapshot => {
          this.users = snapshot.docs.map(d => new User(d))
        }
      )
    }

    if (this.action.body) this.taskBody = this.action.body
    if (this.action.title) this.title = this.action.title
    if (this.action.dueDate)
      this.dueDate =
        this.action.dueDate +
        (Number(this.action.dueDate) > 1 ? ' days' : ' day')
    if (this.action.assignedTo == '') {
      this.assignTo = 'currentUser'
    } else {
      this.assignTo = this.action.assignedTo
    }
  },
  beforeDestroy() {
    if (unsubscribeUsers) unsubscribeUsers()
  },
  methods: {
    setValue(field: string, value: string) {
      Vue.set(this.action, field, value)
      this.$emit('update:action', this.action)
    }
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  }
})
</script>

<style scoped>
.spinner {
  margin: 10px 0px;
}
i.icon.icon-arrow-down-1 {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 0.75rem;
}
</style>
