import { requests } from '.'
import config from '../config'

interface ListFunnelProducts {
  locationId: string
  funnel: string
  step: string
}
interface CreateFunnelProduct {
  locationId: string
  funnel: string
  step: string
  name: string
  product: string
  price: string
  bumpProduct: boolean
  displayText: string
  authorizeAmount: number
}

interface UpdateFunnelProduct {
  name: string
  product: string
  price: string
  bumpProduct: boolean
  displayText: string
  authorizeAmount: number
}

interface GetTransactions {
  locationId: string
  offset: number
  limit:number
}

interface StopSplitTestsAndResetParams {
  locationId: string
  funnelId: string
  userId: string
}

const FunnelsServices = {
  listFunnelProducts: (params: ListFunnelProducts) =>
    requests(config.funnelsApiURL).get('/funnels/order-form/products', {
      params,
    }),
  listFunnelProductsByStep: (funnelId:string) =>
    requests(config.funnelsApiURL).get(`/funnels/order-form/funnels/${funnelId}/products`),
  createFunnelProducts: (params: CreateFunnelProduct) =>
    requests(config.funnelsApiURL).post('/funnels/order-form/products', params),
  updateFunnelProducts: (id: string, params: UpdateFunnelProduct) =>
    requests(config.funnelsApiURL).put(
      `/funnels/order-form/products/${id}`,
      params
    ),
  deleteFunnelProduct: id =>
    requests(config.funnelsApiURL).delete(`/funnels/order-form/products/${id}`),
  findByFunnelProductId: id =>
    requests(config.funnelsApiURL).get(`/funnels/order-form/products/${id}`),
  getTransactions: (params:GetTransactions) =>
    requests(config.funnelsApiURL).get(`/funnels/transaction/list`, { params }),
  stopAllSplitTestsAndReset: (params:StopSplitTestsAndResetParams) =>
    requests(config.funnelsApiURL).post(`/funnels/funnel/stop-all-split-tests-and-reset`, params),
}

export default FunnelsServices
