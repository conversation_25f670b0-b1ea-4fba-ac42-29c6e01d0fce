<template>
  <div>
    <div v-show="showConnectDialog" class="col-sm-12">
      <div class="card-integration">
        <div class="card-integration--inner">
          <MedallionFacebookIcon
            style="height:100px"
            alt="Avatar Name"
          />
          <p>Connect your location's Facebook Account</p>
          <button
            type="button"
            class="btn btn-primary btn-block"
            @click.stop="connectFacebook"
          >
            Connect
          </button>
        </div>
      </div>
    </div>
    <div class="form-group" v-show="facebookConnection">
      <div class="form-input-dropdown dropdown">
        <div data-toggle="dropdown">
          <i class="icon icon-arrow-down-1"></i>
          <input
            type="text"
            class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800 disabled:opacity-50"
            placeholder="Select Ad Account"
            :value="accountName"
          />
        </div>
        <div class="dropdown-menu">
          <a
            class="dropdown-item trigger-type"
            href="javascript:void(0);"
            v-for="account in adAccounts"
            :key="account.id"
            @click.prevent="setValue('facebook_account_id', account.id)"
          >
            <p>{{ account.name }}</p>
          </a>
        </div>
      </div>
    </div>
    <div
      class="form-group"
      v-show="facebookConnection && adAccounts.length > 0"
    >
      <div class="form-input-dropdown dropdown">
        <div data-toggle="dropdown">
          <i class="icon icon-arrow-down-1"></i>
          <input
            type="text"
            class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800 disabled:opacity-50"
            placeholder="Select custom audience"
            :value="audienceName"
          />
        </div>
        <div class="dropdown-menu">
          <a
            class="dropdown-item trigger-type"
            href="javascript:void(0);"
            v-for="audience in customAudiences"
            :key="audience.id"
            @click.prevent="
              setValue('facebook_custom_audience_id', audience.id)
            "
          >
            <p>{{ audience.name }}</p>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { OAuth2 } from '@/models'
import firebase from 'firebase/app'
import config from '../../../config'
import MedallionFacebookIcon from '/public/pmd/img/logo_medallions/medallion-facebook.svg'

let cancelFacebookSubscription: () => void
let openedWindow: Window | null

export default Vue.extend({
  props: ['action'],
  components: {
    MedallionFacebookIcon
  },
  data() {
    return {
      currentLocationId: '',
      facebookConnection: undefined as OAuth2 | undefined,
      adAccounts: [],
      customAudiences: [],
      showConnectDialog: false
    }
  },
  computed: {
    accountName(): string {
      const account = lodash.find(this.adAccounts, {
        id: this.action.facebook_account_id
      })
      if (account) return account.name
      return ''
    },
    audienceName(): string {
      const account = lodash.find(this.customAudiences, {
        id: this.action.facebook_custom_audience_id
      })
      if (account) return account.name
      return ''
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    if (cancelFacebookSubscription) {
      cancelFacebookSubscription()
    }
    cancelFacebookSubscription = await OAuth2.getByLocationIdAndType(
      this.currentLocationId,
      OAuth2.TYPE_FACEBOOK
    ).onSnapshot((snapshot: firebase.firestore.QuerySnapshot) => {
      this.facebookConnection = snapshot.docs.map(
        (d: firebase.firestore.QueryDocumentSnapshot) => new OAuth2(d)
      )[0]
      if (!this.facebookConnection) {
        this.showConnectDialog = true
      } else {
        this.getAdAccounts()
        if (this.action.facebook_account_id) {
          this.getCustomAudiences()
        }
      }
    })
  },
  methods: {
    setValue(field: string, value: string) {
      Vue.set(this.action, field, value)
      this.$emit('update:action', this.action)
      if (field == 'facebook_account_id') {
        this.getCustomAudiences()
      }
    },
    connectFacebook() {
      openedWindow = window.open(
        config.baseUrl +
          '/api/facebook/start_oauth?location_id=' +
          this.currentLocationId,
        'MyWindow',
        'toolbar=no, menubar=no,scrollbars=no,resizable=no,location=no,directories=no,status=no'
      )
    },
    async getAdAccounts() {
      let _self = this
      let params = {}

      let response = await this.$http.get(
        '/facebook/get_ad_accounts/' + this.currentLocationId,
        {
          params: params
        }
      )
      console.log('Got response:', response)
      this.adAccounts = []

      if (response.data && response.data.length > 0) {
        response.data.forEach(function(account: any) {
          _self.adAccounts.push(account)
        })
      }
    },
    async getCustomAudiences() {
      let _self = this
      let params = {
        account_id: this.action.facebook_account_id
      }

      let response = await this.$http.get(
        '/facebook/get_custom_audiences/' + this.currentLocationId,
        {
          params: params
        }
      )
      console.log('Got response:', response)
      this.customAudiences = []

      if (response.data && response.data.length > 0) {
        response.data.forEach(function(audience: any) {
          _self.customAudiences.push(audience)
        })
      }
    }
  },
  beforeDestroy() {
    if (cancelFacebookSubscription) {
      cancelFacebookSubscription()
    }
  }
})
</script>
