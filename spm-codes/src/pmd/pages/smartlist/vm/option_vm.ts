
import {IFilter, ISerializationSpecs} from './filter_vm';
import {IFormatter} from './option_formatter';
import {IKeyVal} from './interfaces';
import { Utils } from '@/util/utils';

export type IListFetch = (params: {[key:string]: any}) => Promise<IKeyVal[]>;

export interface IFilterOption {
  filter: IFilter;
  filterName: string;
  condition: string;
  selected: boolean;
  firstValue?: any;
  secondValue?: any;
  nextFilters: IFilter[];
  secondValueOptions?: string [];
  firstValueMandatory: boolean;
  secondValueMandatory: boolean;
  isDateRange:boolean;
  showSecondValueOptions: boolean;
  Formatter: IFormatter;
  hasChanges: boolean;
  listFetch?: IListFetch;
  listFetch2?: IListFetch;
  limitItems?: number;
  isAutoComplete?: boolean;
  defaultFirstVal?: any;
  defaultSecondVal?: any;
  example?: string;

  serialize(options?: ISerializationSpecs): any;
  copy(parent: IFilter): IFilterOption;
  captureState(): void;
  hasListFetch(): boolean;
}

// tslint:disable-next-line: max-classes-per-file
export class FilterOption<T> implements IFilterOption {

 public filter: IFilter;
 public condition: string;
 public get conditionUX() {
   return this.displayOverride || this.option
 }
 public listFetch?: IListFetch;
 public listFetch2?: IListFetch;
 public limitItems?: number;
 public isAutoComplete?: boolean;

 public selected: boolean = false;
 // public  get selected(): boolean {
 //   return this._selected;
 // }
 // public set selected(val:boolean): boolean {
 //    this._selected = val;
 //    this.filter.hasChanged = true;
 // }

 public firstValue?: T;
 public secondValue?: any;
 public nextFilters: IFilter[] | null;
 public defaultFirstVal?: any;
 public defaultSecondVal?: any;
 public example?: string;

 public secondValueOptions?: string [];
 public Formatter: IFormatter;
 public firstValueMandatory: boolean = false ;
 public secondValueMandatory: boolean = false ;
 public showSecondValueOptions: boolean = false;
 public isDateRange:boolean = false;

 public get hasChanges(): boolean {
   if (!this._originalState) return true;
   if (this._originalState.selected !== this.selected) return true;
   if (this.firstValue instanceof Array) {
    const compareArrays = this.isDifferentArray (this.firstValue, this._originalState.firstValue);
    if (compareArrays === true) return true;
   } else if (this._originalState.firstValue !== this.firstValue) {
     return true;
   }

   if (this.secondValue instanceof Array) {
     const compareArrays = this.isDifferentArray (this.secondValue, this._originalState.secondValue);
     if (compareArrays === true) return true;
   } else if (this._originalState.secondValue !== this.secondValue) return true;

   if (this.nextFilters && this.nextFilters.length){
     const items = this.nextFilters.filter(a=> a.hasChanges === true);
     if (items && items.length > 0) return true;
   }

   return false;
 }

 public get filterName() {
   return this.filter ? this.filter.filterName || '' : '';
 }

 private _originalState: FilterOption<T>;

 constructor(filter: IFilter, protected option: string, formatter: IFormatter, protected displayOverride?: string) {
   if (!filter) throw new Error('Filter is required to initialize filter otpion');
   if (!option || option.trim() === '') throw new Error('Option name required to initialize filter otpion');
   if (!formatter) throw new Error('Formatter required');
   this.filter = filter;
   this.condition = Utils.serverReady(option);
   this.Formatter = formatter;
 }

 public hasListFetch(): boolean {
    if (this.listFetch) return true;
    return false;
 }

 public serialize(options?: ISerializationSpecs): any {
    const jObj = {
     filterName: this.filter.filterName,
     filterName_lc: Utils.serverReady(this.filterName),
     condition: this.condition, // this is already serverReady
   };
   // console.log(this.firstValue);
   // console.log(this.firstValueMandatory);
    if (this.isDateRange) {
      jObj.firstValue = this.firstValue?.trim();
      jObj.secondValue = this.secondValue?.trim();
      console.log(`In Serialize  ${jObj}`);
    } else {
      if (this.firstValueMandatory && this.firstValue) {
        if (this.firstValue instanceof Array)  jObj.firstValue = [...this.firstValue];
        else if (typeof this.firstValue === 'string') jObj.firstValue = this.firstValue.trim();
        else jObj.firstValue = this.firstValue;
      }
      if (this.secondValueMandatory || this.secondValue) {
        if (this.secondValue instanceof Array)  jObj.secondValue = [...this.secondValue];
        else if (typeof this.secondValue === 'string') jObj.secondValue = this.secondValue.trim();
        else jObj.secondValue = this.secondValue;
      }
    }
    if (this.nextFilters) {
      const isForPersistence= options && options.isForPersistence === true;
      let subFilters = isForPersistence
                      ? this.nextFilters.filter(a=> a.cannotPersist !== true)
                      : [...this.nextFilters];
      jObj.nextFilters = subFilters.map(a=> a.serialize(options));
    }

    return jObj;
 }

 public copy(parent: IFilter): IFilterOption {
   if (!parent) return;
   const f = new FilterOption<T>(parent, this.condition, this.Formatter);
   if (this.firstValue && this.firstValue instanceof Array)  {
     f.firstValue = [...this.firstValue];
   }  else if (this.firstValue) {
     f.firstValue = this.firstValue;
   }
   if (this.secondValue && this.secondValue instanceof Array) {
      f.secondValue = [...this.secondValue];
   } else if (this.secondValue) {
      f.secondValue = this.secondValue;
   }
   if (this.nextFilters && this.nextFilters) f.nextFilters = this.nextFilters.map(a=> a.copy());

   if (this.secondValueOptions) f.secondValueOptions = [...this.secondValueOptions];
   f.selected = this.selected;
   f.firstValueMandatory = this.firstValueMandatory;
   f.secondValueMandatory = this.secondValueMandatory;
   f.showSecondValueOptions = this.showSecondValueOptions;
  //  f.limitItems = this.limitItems;
  //  f.isAutoComplete = this.isAutoComplete;
  //  f.defaultFirstVal = this.defaultFirstVal;
  //  f.defaultSecondVal = this.defaultSecondVal
  //  f.example = this.example;
   return f;
 }

 // tslint:disable-next-line: align
 public isDifferentArray(arr1: IKeyVal[], arr2: IKeyVal[]): boolean {
   if (!arr1) arr1 = [];
   if (!arr2) arr2 = [];
   if (arr1.length !== arr2.length) return true;
   const keys = arr2.map((a) => a.key);
   for (let i = 0; i < keys.length; i++) {
     if (!keys.includes(arr1[i].key)) {
       //console.log('not found ' + arr1[i]);
       return true;
     }
   }
   return false;
 }

 public captureState() {
   this._originalState = this.copy(this.filter);
   if (this.nextFilters  && this.nextFilters) {
     this.nextFilters.forEach(a=> a.captureState());
   }
  }
}
