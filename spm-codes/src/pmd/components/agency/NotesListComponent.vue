<template>
  <div :style="[fullheight ? { height: '100%' } : {}]" style="overflow-y: auto">
    <div class="card" v-if="notes.length > 0">
      <div v-show="heading" class="card-header">
        <h2>Notes</h2>
      </div>
      <div class="card-body">
        <div class="hl_tasks">
          <NotesListCard
            v-for="note in notes"
            :note="note"
            :key="note.id"
            :mode="mode"
            @editNote="editNote"
            @NoteDeleted="deleteNote(note.id)"
          />
        </div>
      </div>
    </div>
    <template v-else-if="placeholder">
      <div class="item-center">
        <moon-loader :loading="loading" color="#188bf6" size="20px" />
        <template v-if="!loading">
          <div class="text-center">
            <i
              class="far fa-clipboard"
              style="font-size: 20px; color: #bdbdbd"
            ></i>
          </div>
          <p>
            No notes for this contact
            <br />
          </p>
          <div class="text-center">
            Click
            <a
              @click="createNew"
              style="font-weight: bold; font-size: 15px; color: #188bf6"
              >here</a
            >
            to create one
          </div>
        </template>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import Note from '@/models/api/Note'
import LocationNote from '@/models/api/LocationNote'
import NotesListCard from './NotesListCard.vue'
import { UxMessage } from '@/util/ux_message'
let unsubscribeNotes: () => void

export default Vue.extend({
  props: [
    'passedInContactId',
    'mode',
    'heading',
    'action',
    'placeholder',
    'fullheight',
  ],
  inject: ['uxmessage'],
  data() {
    return {
      notes: [] as Note[],
      accountId: '',
      contactId: '',
      initialLoad: true,
      loading: true as boolean,
      currentLocationId: '',
    }
  },
  components: { NotesListCard },
  watch: {
    '$route.params.account_id': function (id) {
      this.accountId = id
      this.notes = []
      this.fetchData()
    },
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.notes = []
      this.fetchData()
    },
    '$route.params.contact_id': function (id) {
      this.contactId = id
      this.notes = []
      this.fetchData()
    },
    passedInContactId: function (id) {
      this.contactId = id
      this.fetchData()
    },
  },
  async created() {
    this.accountId = this.$router.currentRoute.params.account_id
    this.currentLocationId = this.$router.currentRoute.params.location_id
    if (this.$router.currentRoute.params.contact_id) {
      this.contactId = this.$router.currentRoute.params.contact_id
    } else if (this.passedInContactId) {
      this.contactId = this.passedInContactId
    }
    this.fetchData()

    this.$bus.$on('refresh-notes', this.fetchData)
  },
  methods: {
    async fetchData() {
      if (this.contactId) {
        try {
          this.notes = await Note.list(this.contactId)
        } catch (error) {
          this.uxmessage(UxMessage.errorType(error), true)
        }
        this.loading = false
      }else if (this.accountId){
          try {
          this.notes = await LocationNote.list(this.accountId)
        } catch (error) {
          this.uxmessage(UxMessage.errorType(error), true)
        }
        this.loading = false
      }
    },
    editNote(noteId: string) {
      this.$emit('editNote', noteId)
    },
    createNew() {
      this.$emit('createNew', true)
    },
    deleteNote(id){
      const i = this.notes.findIndex(t => t.id === id)
      this.notes.splice(i,1)
    }
  },
  beforeDestroy() {
    if (unsubscribeNotes) unsubscribeNotes()
    this.$bus.$off('refresh-notes', this.fetchData)
  },
})
</script>
