<template>
  <div>
    <section class="hl_wrapper">
      <section class="hl_wrapper--inner customers" id="customers">
        <div class="container-fluid mb-5">
          <div class="hl_controls">
            <div class="hl_controls--left">
              <h3>
                Manual Actions
                <!-- <span v-if="total">{{ total }} total</span> -->
              </h3>
            </div>
            <div class="hl_controls--right" id="campaign-picker">
              <select
                class="selectpicker"
                title="Select Campaign / Workflow"
                data-width="fit"
                @change="campaignSelect()"
                name="campaignSelect"
                v-model="filters.campaign"
              >
                <option value>All</option>
                <option
                  v-for="campaign in campaigns"
                  :key="campaign.id"
                  :value="campaign.id"
                >
                  {{ campaign.name }}
                </option>
              </select>

              <select
                class="selectpicker"
                v-if="!user.permissions.assigned_data_only"
                title="Select Assignee"
                data-width="fit"
                name="existingUsers"
                v-model="filters.assignee"
              >
                <option value>All</option>
                <option
                  v-for="user in existingUsers"
                  :key="user.id"
                  :value="user.id"
                >
                  {{ user.name }}
                </option>
              </select>

              <button
                :disabled="this.manualQueue.length === 0"
                class="btn btn-success"
                type="button"
                data-toggle="dropdown"
                aria-haspopup="true"
                aria-expanded="false"
                @click.prevent="startCalling"
              >
                Let's start
              </button>
            </div>
          </div>
          <nav aria-label="Page navigation example">
            <ul class="pagination hl_controls justify-content-end">
              <li
                class="page-item mr-0"
                :class="{ disabled: !previousAvailable || fetching }"
              >
                <a
                  class="page-link"
                  href="javascript:void(0);"
                  tabindex="-1"
                  @click.prevent="load('previous')"
                  >Previous</a
                >
              </li>
              <li
                class="page-item"
                :class="{ disabled: !nextAvailable || fetching }"
              >
                <a
                  class="page-link"
                  href="javascript:void(0);"
                  @click.prevent="load('next')"
                  >Next</a
                >
              </li>
            </ul>
          </nav>
          <div class="card hl_customers--table">
            <div class="card-body --no-padding">
              <div v-if="manualQueue.length > 0" class="table-wrap">
                <table class="table table-sort">
                  <thead>
                    <tr>
                      <th data-sort="string">Contacts</th>
                      <th data-sort="string">Campaign / Workflow</th>
                      <th data-sort="string">Date Added</th>
                      <th data-sort="string">Assigned To</th>
                      <th data-sort="string">Type</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="item in manualQueue" v-bind:key="item.id">
                      <td
                        class="pointer"
                        @click.prevent="loadDetailPage(item.contactId)"
                      >
                        <ContactAvatar
                          :contactId="item.contactId"
                          :include_name="true"
                        />
                      </td>
                      <td class="pointer">
                        <div v-if="item.campaignId">
                          {{ getCampaignName(item.campaignId) }}
                        </div>
                      </td>
                      <td>
                        <div v-if="item.dateAdded">
                          {{
                            item.dateAdded
                              .tz(timezone)
                              .format(getCountryDateFormat('extended-normal'))
                          }}
                        </div>
                      </td>
                      <td>
                        <UserAvatar
                          v-if="item.userId"
                          :userId="item.userId"
                          :showName="true"
                          :size="'sm'"
                        />
                      </td>
                      <td>{{ getMessageType(item) }}</td>
                      <td>
                        <div class="icon-with-loader">
                          <i
                            class="fa fa-trash pointer hint-icon center-inside"
                            @click.prevent="deleteManualAction(item)"
                          />
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div v-else-if="isLoaded" class="text-center py-5">
                <i class="far fa-check-circle text-success fa-3x"></i>
                <h2>Good Work!</h2>
                <p>You have no pending tasks</p>
              </div>
            </div>
          </div>
          <nav aria-label="Page navigation example">
            <ul class="pagination justify-content-end" :class="{'sidebar-v2-padding': getSideBarVersion == 'v2' }">
              <li
                class="page-item"
                :class="{ disabled: deleteAllDisabled }"
                v-if="manualQueue.length > 1"
              >
                <a class="page-link" @click.prevent="deleteAllManualActions">
                  <i class="fa fa-trash" />
                  Delete all
                </a>
              </li>
              <li
                class="page-item"
                :class="{ disabled: !previousAvailable || fetching }"
              >
                <a
                  class="page-link"
                  href="javascript:void(0);"
                  tabindex="-1"
                  @click.prevent="load('previous')"
                  >Previous</a
                >
              </li>
              <li
                class="page-item"
                :class="{ disabled: !nextAvailable || fetching }"
              >
                <a
                  class="page-link"
                  href="javascript:void(0);"
                  @click.prevent="load('next')"
                  >Next</a
                >
              </li>
            </ul>
          </nav>
        </div>
      </section>
      <!-- END of .customers -->
    </section>
    <!-- END of .hl_wrapper -->
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import {
  User,
  ManualQueue,
  Location,
  MessageType,
  Campaign,
  getCountryDateFormat,
} from '../../models'
import { UserState } from '../../store/state_models'
import { mapState } from 'vuex'
import firebase from 'firebase/app'
import lodash, { orderBy } from 'lodash'
import config from '@/config'

const ContactAvatar = () => import('../components/ContactAvatar.vue')
// const PhoneNumber = () => import('../components/util/PhoneNumber.vue')
const UserAvatar = () => import('../components/UserAvatar.vue')
// const MoonLoader = () => import('../components/MoonLoader.vue')

declare var $: any

let realtimeUnsubscribe: () => void
export default Vue.extend({
  props: ['type'],
  components: {
    ContactAvatar,
    // MoonLoader,
    // PhoneNumber,
    UserAvatar,
  },
  data() {
    return {
      manualQueue: [] as ManualQueue[],
      location: undefined as Location | undefined,
      timezone: undefined as string | undefined,
      fetching: false,
      filters: {
        locationId: '',
        limit: 25,
        assignee: '',
        campaign: '',
      },
      isLoaded: false,
      previousAvailable: true,
      nextAvailable: true,
      existingUsers: [] as User[],
      campaigns: [] as Campaign[],
      getCountryDateFormat: getCountryDateFormat,
      deleteAllDisabled: false,
    }
  },
  async created() {
    this.fetchData()
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
    $('#campaign-picker a.dropdown-item').each(function () {
      if ($(this).text().length >= 30) {
        $(this).attr('title', $(this).text())
        $(this).tooltip()
      }
    })
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        console.log(s.user)
        return s.user ? new User(s.user) : undefined
      },
    }),
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  watch: {
    '$route.params.location_id': async function () {
      this.fetchData()
    },
    filters: {
      handler() {
        console.info('Filters changed')
        this.manualQueue = []
        this.load()
      },
      deep: true,
    },
  },
  beforeDestroy() {
    if (realtimeUnsubscribe) realtimeUnsubscribe()
  },
  methods: {
    async fetchData() {
      const locationId = this.$router.currentRoute.params.location_id
      await Promise.all([
        this.$store.dispatch('campaigns/syncAll', locationId),
        this.$store.dispatch('workflows/syncAll', locationId)
      ])
      this.location = new Location(
        await this.$store.dispatch('locations/getById', locationId)
      )
      await this.setTimezone()
      this.existingUsers = this.$store.state.users.users.map(
        user => new User(Object.assign({}, user))
      )

      let allCampaigns = this.$store.state.campaigns.campaigns
      this.campaigns = []
      for (let i = 0; i < allCampaigns.length; i++) {
        if (
          allCampaigns[i].campaign_data &&
          allCampaigns[i].campaign_data.templates
        ) {
          let templates = allCampaigns[i].campaign_data.templates
          let isManual = false
          for (let j = 0; j < templates.length; j++) {
            if (
              templates[j].type === 'manual-sms' ||
              templates[j].type === 'manual-call'
            ) {
              isManual = true
            }
          }
          if (isManual) {
            this.campaigns.push(allCampaigns[i])
          }
        }
      }
      this.filters.locationId = locationId

      // We will show all the list of workflows where in case of campaign we were only showing which has manual-call and manual-sms as step
      if (!this.$store.state.workflows.workflows) await this.$store.dispatch('workflows/syncAll')
      let activeWorkflows = this.$store.state.workflows.workflows
        .filter(workflow => workflow.status === 'published')
        .map(workflow => {
          return {
            name: `Workflow - ${workflow.name}`,
            id: `workflow_${workflow.id}`,
          }
        })
      try {
        activeWorkflows = orderBy(activeWorkflows, workflow => workflow?.name?.toLowerCase())
      } catch (err) { }
      this.campaigns.push.apply(this.campaigns, activeWorkflows)
    },
    async setTimezone() {
      this.timezone = await this.location.getTimeZone()
      if (!this.timezone) {
        this.$uxMessage(
          'error',
          'To use Manual Actions, please set a Timezone in Company Settings.'
        )
      }
    },
    campaignSelect() {
      $('#campaign-picker a.dropdown-item').tooltip('dispose')
    },
    getCampaignName(campaignId) {
      if (lodash.findIndex(this.campaigns, { id: campaignId }) !== -1) {
        return lodash.find(this.campaigns, { id: campaignId }).name
      }
      return ''
    },
    loadDetailPage(id: string) {
      this.$router.push({ name: this.getSideBarVersion == 'v2' ? 'contact_detail-v2': 'contact_detail', params: { contact_id: id } })
    },
    startCalling() {
      if (!this.manualQueue.length) return

      const topItem: ManualQueue = this.manualQueue[0]
      this.$router.push({
        name: this.getSideBarVersion == 'v2' ? 'contact_detail-v2': 'contact_detail',
        params: { contact_id: topItem.contactId },
        query: {
          manualId: topItem.id,
          assignee: this.filters.assignee,
          campaignId: this.filters.campaign,
        },
      })
    },
    getMessageType(item: ManualQueue): string {
      return item.messageType == MessageType.TYPE_CAMPAIGN_MANUAL_CALL
        ? 'Call'
        : 'SMS'
    },
    async deleteManualAction(item: ManualQueue) {
      if (confirm('Are you sure you want to delete this action?')) {
        await item.ref.update({
          deleted: true,
          date_updated: firebase.firestore.FieldValue.serverTimestamp(),
          deleted_by: this.user ? this.user.id : 'User',
        })
        if (item.campaignStatusId && item.campaignStatusId.includes('workflow_')) {
          const workflowStatusId = item.campaignStatusId.replace('workflow_', '')
          const workflowId = item.campaignId.replace('workflow_', '')
          await this.$http.post(`${config.workflowServiceURL}/${item.locationId}/${workflowId}/status/${workflowStatusId}/manual-action-deleted`, {
            user_id: this.user ? this.user.id : ''
          })
        } else {
          await this.$http.post(
            `/campaign/${item.campaignId}/status/${item.campaignStatusId}/next`,
            {}
          )
        }
      }
    },
    async deleteAllManualActions() {
      if (
        confirm(
          'Are you sure you want to delete all of the manual actions in this page?'
        )
      ) {
        this.deleteAllDisabled = true
        for (let item of this.manualQueue) {
          await item.ref.update({
            deleted: true,
            date_updated: firebase.firestore.FieldValue.serverTimestamp(),
            deleted_by: this.user ? this.user.id : 'User',
          })
          if (item.campaignStatusId && item.campaignStatusId.includes('workflow_')) {
            const workflowStatusId = item.campaignStatusId.replace('workflow_', '')
            const workflowId = item.campaignId.replace('workflow_', '')
            await this.$http.post(`${config.workflowServiceURL}/${item.locationId}/${workflowId}/status/${workflowStatusId}/manual-action-deleted`, {
              user_id: this.user ? this.user.id : ''
            })
          } else {
            await this.$http.post(
              `/campaign/${item.campaignId}/status/${item.campaignStatusId}/next`,
              {}
            )
          }
        }
        this.deleteAllDisabled = false
      }
    },
    async load(fetchType?: 'previous' | 'next') {
      const { filters } = this
      filters.assignee = this.user.permissions.assigned_data_only
        ? this.user.id
        : filters.assignee

      let query: firebase.firestore.Query = await ManualQueue.getByUserIdAndLocationIdRealtime(
        filters
      )

      if (this.manualQueue && this.manualQueue.length) {
        if (fetchType == 'next') {
          query = query
            .orderBy('date_added', 'asc')
            .startAfter(this.manualQueue[this.manualQueue.length - 1].snapshot)
        } else if (fetchType == 'previous') {
          query = query
            .orderBy('date_added', 'desc')
            .startAfter(this.manualQueue[0].snapshot)
        }
      } else {
        query = query.orderBy('date_added', 'asc')
      }

      if (realtimeUnsubscribe) realtimeUnsubscribe()
      this.isLoaded = false
      this.previousAvailable = false
      this.nextAvailable = false

      realtimeUnsubscribe = query
        .limit(this.filters.limit + 1)
        .onSnapshot(snapshot => {
          if (!this.isLoaded) {
            let docs = snapshot.docs
            if (fetchType === 'previous') {
              if (docs.length < filters.limit) {
                docs = []
              }
              docs = docs.reverse()

              this.nextAvailable = true
              this.previousAvailable = docs.length === this.filters.limit + 1
              if (docs.length === this.filters.limit + 1) {
                docs.splice(0, 1)
              }
            } else if (fetchType === 'next') {
              this.previousAvailable = true
              this.nextAvailable = docs.length === this.filters.limit + 1
              if (docs.length === this.filters.limit + 1) {
                docs.splice(docs.length - 1, 1)
              }
            } else {
              this.nextAvailable = docs.length === this.filters.limit + 1
              if (docs.length === this.filters.limit + 1) {
                docs.splice(docs.length - 1, 1)
              }
            }
            this.manualQueue = docs.map(queue => new ManualQueue(queue))
            this.$nextTick(() => {
              this.isLoaded = true
            })
          } else {
            for (let i = snapshot.docChanges().length - 1; i >= 0; i--) {
              const docChange = snapshot.docChanges()[i]
              if (docChange.type === 'removed') {
                const index = this.manualQueue.findIndex(
                  x => x.id === docChange.doc.id
                )
                if (index > -1) {
                  this.manualQueue.splice(index, 1)
                }
              } else {
                const newQueue = new ManualQueue(docChange.doc)
                const existingIndex = this.findExistingIndex(newQueue)
                if (existingIndex !== -1) {
                  // Updates existing in array
                  this.manualQueue.splice(existingIndex, 1, newQueue)
                } else {
                  // Inserts new into array
                  const sortedIndex = this.sortedIndex(newQueue)

                  if (
                    (!this.previousAvailable && sortedIndex === 0) ||
                    (this.manualQueue.length < this.filters.limit &&
                      sortedIndex === this.manualQueue.length) ||
                    (sortedIndex > 0 && sortedIndex < this.manualQueue.length)
                  ) {
                    this.manualQueue.splice(sortedIndex, 0, newQueue)
                  }
                }
              }
            }
          }
        })
    },
    sortedIndex(element: ManualQueue): number {
      return lodash.sortedIndexBy(
        this.manualQueue,
        element,
        (value: ManualQueue) => {
          return value.dateAdded.valueOf()
        }
      )
    },
    findExistingIndex(element: ManualQueue): number {
      return lodash.findIndex(this.manualQueue, (value: ManualQueue) => {
        return value.id === element.id
      })
    },
  },
})
</script>
<style>
#campaign-picker .bootstrap-select .dropdown-menu {
  max-width: 125% !important;
}
#campaign-picker .dropdown-item span.text {
  width: 230px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.sidebar-v2-padding {
  padding-bottom: 30px;
}
</style>
