<template>
  <div
    id="yext_account_creation"
    v-if="
      location &&
      location.yextReseller &&
      location.yextReseller.payment_status === 'COMPLETE'
    "
  >
    <div class="card">
      <div class="card-header hl_compact_header">Please fill the form</div>
      <div class="card-body">
        <div>
          <div class="yext_reg_form">
            <template v-if="loading">
              <div class="loader-backdrop">
                <moon-loader :loading="loading" color="#188bf6" size="30px" />
              </div>
            </template>

            <form action="">
              <div class="form-group">
                <UITextInputGroup
                  label="Company Name (Will be shown on all the listings)"
                  type="text"
                  v-model="formData.locationName"
                  name="company_name"
                />
                <!--   <label>Company Name (Will be shown on all the listings)</label>
                <input
                  type="text"
                  name="company_name"
                  id="company_name"
                  class="form-control"
                  v-model="formData.locationName"
                /> -->
                <template v-if="formErrors('locationName')">
                  <label class="field-error"
                    >{{ formErrors('locationName') }}
                  </label>
                </template>
              </div>
              <div class="form-group">
                <UITextInputGroup
                  label="Phone"
                  type="text"
                  v-model="formData.phone"
                  name="phone_no"
                  id="phone_no"
                />
                <!--  <label>Phone</label>
                <input
                  type="text"
                  name="phone_no"
                  id="phone_no"
                  class="form-control"
                  v-model="formData.phone"
                /> -->
                <template v-if="formErrors('phone')">
                  <label class="field-error">{{ formErrors('phone') }} </label>
                </template>
              </div>
              <div class="form-group">
                <UITextInputGroup
                  label="Email"
                  type="text"
                  id="email"
                  v-model="formData.email"
                  name="email"
                />
                <!--  <label>Email</label>

                <input
                  type="text"
                  name="email"
                  id="email"
                  class="form-control"
                  v-model="formData.email"
                /> -->
                <template v-if="formErrors('email')">
                  <label class="field-error">{{ formErrors('email') }} </label>
                </template>
              </div>

              <div class="form-group">
                <UITextInputGroup
                  :label="`Featured Message
                  ${
                    formData.featuredMessage
                      ? formData.featuredMessage.length + '/ 50'
                      : ''
                  }
                  `"
                  type="text"
                  placeholder="Call today!"
                  name="featured_message"
                  id="featured_message"
                  v-model="formData.featuredMessage"
                />

                <!--   <label
                  >Featured Message
                  {{
                    formData.featuredMessage
                      ? formData.featuredMessage.length + '/ 50'
                      : ''
                  }}</label
                >
                <input
                  type="text"
                  placeholder="Call today!"
                  name="featured_message"
                  id="featured_message"
                  class="form-control"
                  v-model="formData.featuredMessage"
                /> -->
                <template v-if="formErrors('featuredMessage')">
                  <label class="field-error"
                    >{{ formErrors('featuredMessage') }}
                  </label>
                </template>
              </div>

              <div class="form-group category-select-container">
                <label
                  class="
                    hl-text-input-label
                    block
                    text-sm
                    font-medium
                    text-gray-700
                    mb-1
                  "
                  >Business Category</label
                >
                <multiselect
                  v-model="formData.selectedCategories"
                  label="Yext Categories"
                  track-by="id"
                  placeholder="Type to search"
                  open-direction="bottom"
                  class="hl-yext-category-search"
                  :preserveSearch="true"
                  :options="filteredCategories"
                  :searchable="true"
                  :loading="catLoader"
                  :limit="3"
                  :max-height="600"
                  :show-no-results="false"
                  :hide-selected="true"
                  @search-change="filterCategories"
                  :internal-search="false"
                  :multiple="true"
                  :options-limit="300"
                >
                  <template slot="tag" slot-scope="{ option, remove }"
                    ><span class="custom__tag"
                      ><span>{{ option.name }}</span
                      ><span class="custom__remove" @click="remove(option)"
                        >❌</span
                      ></span
                    ></template
                  >
                  <template slot="option" slot-scope="props" class="flex">
                    <div class="option__desc">
                      <div class="option__header">
                        {{ props.option.fullName }}
                      </div>
                      <span class="option__title">{{ props.option.name }}</span>
                    </div>
                  </template>
                </multiselect>
                <template v-if="formErrors('selectedCategories')">
                  <label class="field-error"
                    >{{ formErrors('selectedCategories') }}
                  </label>
                </template>
              </div>

              <div class="form-group">
                <UITextInputGroup
                  label="Address (complete)"
                  type="text"
                  name="address_complete"
                  id="address_complete"
                  v-model="formData.addressComplete"
                />
                <label style="margin: 0px"
                  >Please use a physical address and don't use a PO Box.
                </label>
                <template v-if="formErrors('addressComplete')">
                  <label class="field-error"
                    >{{ formErrors('addressComplete') }}
                  </label>
                </template>
              </div>
              <div class="form-group">
                <UITextInputGroup
                  label="City"
                  type="text"
                  name="address_city"
                  id="address_city"
                  v-model="formData.city"
                />
                <!--  <label>City</label>
                <input
                  type="text"
                  name="address_city"
                  id="address_city"
                  class="form-control"
                  v-model="formData.city"
                />
 -->
                <template v-if="formErrors('city')">
                  <label class="field-error">{{ formErrors('city') }} </label>
                </template>
              </div>
              <div class="form-group">
                <label>State</label>
                <!--     <input
                  type="text"
                  name="address_state"
                  id="address_state"
                  class="form-control"
                  v-model="formData.state"
                /> -->

                <div class="dropdown">
                  <button
                    class="btn btn-ghl_lite dropdown-toggle"
                    type="button"
                    id="unitTypeDropDown"
                    data-toggle="dropdown"
                    aria-haspopup="true"
                    aria-expanded="false"
                  >
                    {{ formData.state.name }} ({{ formData.state.code }})
                  </button>
                  <div class="dropdown-menu">
                    <template v-for="eachState of worldMap['US']">
                      <span
                        class="dropdown-item"
                        v-bind:key="eachState.code"
                        @click="formData.state = eachState"
                      >
                        {{ eachState.name }} ({{ eachState.code }})
                      </span>
                    </template>
                  </div>
                </div>
              </div>
              <div class="form-group">
                <UITextInputGroup
                  label="Zip"
                  type="text"
                  name="address_zip"
                  id="address_zip"
                  v-model="formData.postalCode"
                />

                <!--  <label>Zip</label>
                <input
                  type="text"
                  name="address_zip"
                  id="address_zip"
                  class="form-control"
                  v-model="formData.postalCode"
                /> -->
                <template v-if="formErrors('postalCode')">
                  <label class="field-error"
                    >{{ formErrors('postalCode') }}
                  </label>
                </template>
              </div>
              <div class="form-group">
                <label
                  class="
                    hl-text-input-label
                    block
                    text-sm
                    font-medium
                    text-gray-700
                    mb-1
                  "
                  >Country Code</label
                >
                <input
                  disabled
                  type="text"
                  name="address_country_code"
                  id="address_country_code"
                  class="form-control"
                  v-model="formData.countryCode"
                />
              </div>
            </form>
          </div>
          <button
            class="btn btn-success hl_calltoaction-btn"
            @click="createListing"
            id="yext_create_listing"
          >
            Create Yext Location
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { TwilioAccount } from '@/models'
import Vue from 'vue'
import Multiselect from 'vue-multiselect'

export default Vue.extend({
  components: {
    Multiselect,
  },
  props: {
    location: {
      type: Object,
    },
  },
  data() {
    return {
      currentLocationId: ``,
      loading: false,
      catLoader: false,
      allCategories: [],
      filteredCategories: [],
      formData: {
        locationName: '',
        addressComplete: '',
        city: '',
        state: {
          name: '',
          code: '',
        },
        postalCode: '',
        countryCode: '',
        phone: '',
        email: '',
        featuredMessage: `Call us today to learn more!`,
        twilioAccount: {} as TwilioAccount,
        selectedCategories: [],
      },
    }
  },
  async mounted() {
    this.init()
  },
  methods: {
    init: async function () {
      this.formData.locationName = this.location.name
      this.formData.addressComplete = this.location.address
      this.formData.city = this.location.city
      this.formData.state = this.getStateByCodeOrGiveDefault(
        'US',
        this.location.state
      )
      this.formData.postalCode = this.location.postalCode
      this.formData.countryCode = 'US' //this.location.country
      this.formData.email =
        this.location.email || this.location.prospectInfo.email
      try {
        this.twilioAccount = await TwilioAccount.getByLocation(this.location.id)
        if (this.twilioAccount && this.twilioAccount.defaultOutboundNumber) {
          this.formData.phone = this.twilioAccount.defaultOutboundNumber
        } else {
          this.formData.phone = this.location.phone
        }
      } catch {
        this.formData.phone = this.location.phone
      }
      this.updateCategories()
    },
    createListing: async function () {
      try {
        if (this.inputValidation()) {
          this.loading = true
          await this.$http.post(`/yext/create_listing`, {
            location_id: this.location.id,
            location_name: this.formData.locationName,
            address: this.formData.addressComplete,
            city: this.formData.city,
            state: this.formData.state.code,
            zip: this.formData.postalCode,
            country_code: this.formData.countryCode,
            phone: this.formData.phone,
            email: this.formData.email,
            featured_message: this.formData.featuredMessage || 'Call today!',
            entity_type: `location`,
            category_ids: this.formData.selectedCategories.map(
              eachCat => eachCat.id
            ),
          })
          setTimeout(_ => {
            this.loading = false
            this.$emit('accountCreated')
            // this timeout has been added after  observing, changing location data on app engine, it takes some time to sync in
          }, 3000)
        }
      } catch (err) {
        this.loading = false
        console.error(err.response.data)
        this.$uxMessage(
          'error',
          err.response.data.message[0].code +
            ':' +
            err.response.data.message[0].message
        )
      }
    },

    inputValidation(): boolean {
      let errors = []
      let fields = [
        'locationName',
        'phone',
        'email',
        'featuredMessage',
        'selectedCategories',
        'addressComplete',
        'city',
        'postalCode',
      ]
      for (let eachField of fields) {
        let error = this.formErrors(eachField)
        if (error) {
          errors.push(error)
        }
      }
      if (errors.length > 0) {
        errors = errors.map(eachError => {
          return `<li>${eachError}</li>`
        })
        this.$uxMessage('error', `<ol>${errors.join('<br>')}</ol>`, null, {
          isMessageInRawHTML: true,
        })
        return false
      } else {
        return true
      }
    },
    formErrors(field: string) {
      switch (field) {
        case 'locationName':
          {
            let curLocationName = this.formData.locationName
            if (!curLocationName) {
              return `Location name required`
            } else if (curLocationName.trim().length < 3) {
              return `Invalid Location Name`
            }
          }
          break
        case 'phone':
          {
            let phoneNo = this.formData.phone
            if (!phoneNo) {
              return `Phone number required`
            } else if (phoneNo.trim().length < 8) {
              return `Invalid Phone Number`
            }
          }
          break
        case 'email':
          {
            let email = this.formData.email
            if (!email) {
              return `Email address required`
            } else if (!email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
              return `Invalid Email`
            }
          }
          break
        case 'featuredMessage':
          {
            let featuredMessage = this.formData.featuredMessage
            if (!featuredMessage) {
              return `Featured message required`
            } else if (featuredMessage.length > 50) {
              return `Feature Message Must be less than 50 character `
            }
          }
          break
        case 'addressComplete':
          {
            let address = this.formData.addressComplete
            if (!address) {
              return `Address required`
            } else if (address.length < 3) {
              return `Invalid Address`
            }
          }
          break

        case 'city':
          {
            let city = this.formData.city
            if (!city) {
              return `City required`
            } else if (city.length < 2) {
              return `Invalid City`
            }
          }
          break
        case 'postalCode':
          {
            let postalCode = this.formData.postalCode
            if (!postalCode) {
              return `Zip required`
            } else if (!postalCode.match(/(^\d{5}$)|(^\d{5}-\d{4}$)/)) {
              return `Invalid Zip`
            }
          }
          break
        case 'selectedCategories':
          {
            let selectedCategories = this.formData.selectedCategories
            if (!selectedCategories || selectedCategories.length == 0) {
              return `Please select atleast one category`
            } else if (selectedCategories.length > 3) {
              return `Max 3 categories are allowed`
            }
          }
          break
      }
    },
    getStateByCodeOrGiveDefault(country = 'US', code = 'AL'): any {
      let found = this.worldMap[country].find(eachState => {
        if (eachState.code === code) {
          return true
        } else false
      })
      if (found) {
        return found
      } else {
        return this.worldMap[country][0]
      }
    },
    async updateCategories() {
      this.catLoader = true
      let cats = await this.$http.get(`/yext/categories`)
      this.allCategories = cats.data
      this.filteredCategories = cats.data
      this.catLoader = false
    },
    filterCategories(query: string) {
      console.log(query)
      this.catLoader = true
      const temp = this.allCategories.filter(eachCat => {
        return (
          eachCat.name.toLowerCase().includes(query.toLowerCase()) ||
          eachCat.fullName.toLowerCase().includes(query.toLowerCase())
        )
      })
      this.catLoader = false
      this.filteredCategories = [...temp]
      console.log(this.filteredCategories)
    },
  },
  watch: {
    location: function (oldLoc, newLoc) {
      if (oldLoc.id && oldLoc.id != newLoc.id) {
        this.init()
      }
    },
  },
  computed: {
    worldMap() {
      let world = {
        US: [
          { name: 'Alabama', code: 'AL' },
          { name: 'Alaska', code: 'AK' },
          { name: 'Arizona', code: 'AZ' },
          { name: 'Arkansas', code: 'AR' },
          { name: 'California', code: 'CA' },
          { name: 'Colorado', code: 'CO' },
          { name: 'Connecticut', code: 'CT' },
          { name: 'Delaware', code: 'DE' },
          { name: 'Florida', code: 'FL' },
          { name: 'Georgia', code: 'GA' },
          { name: 'Hawaii', code: 'HI' },
          { name: 'Idaho', code: 'ID' },
          { name: 'Illinois', code: 'IL' },
          { name: 'Indiana', code: 'IN' },
          { name: 'Iowa', code: 'IA' },
          { name: 'Kansas', code: 'KS' },
          { name: 'Kentucky[D]', code: 'KY' },
          { name: 'Louisiana', code: 'LA' },
          { name: 'Maine', code: 'ME' },
          { name: 'Maryland', code: 'MD' },
          { name: 'Massachusetts[D]', code: 'MA' },
          { name: 'Michigan', code: 'MI' },
          { name: 'Minnesota', code: 'MN' },
          { name: 'Mississippi', code: 'MS' },
          { name: 'Missouri', code: 'MO' },
          { name: 'Montana', code: 'MT' },
          { name: 'Nebraska', code: 'NE' },
          { name: 'Nevada', code: 'NV' },
          { name: 'New Hampshire', code: 'NH' },
          { name: 'New Jersey', code: 'NJ' },
          { name: 'New Mexico', code: 'NM' },
          { name: 'New York', code: 'NY' },
          { name: 'North Carolina', code: 'NC' },
          { name: 'North Dakota', code: 'ND' },
          { name: 'Ohio', code: 'OH' },
          { name: 'Oklahoma', code: 'OK' },
          { name: 'Oregon', code: 'OR' },
          { name: 'Pennsylvania[D]', code: 'PA' },
          { name: 'Rhode Island', code: 'RI' },
          { name: 'South Carolina', code: 'SC' },
          { name: 'South Dakota', code: 'SD' },
          { name: 'Tennessee', code: 'TN' },
          { name: 'Texas', code: 'TX' },
          { name: 'Utah', code: 'UT' },
          { name: 'Vermont', code: 'VT' },
          { name: 'Virginia[D]', code: 'VA' },
          { name: 'Washington', code: 'WA' },
          { name: 'West Virginia', code: 'WV' },
          { name: 'Wisconsin', code: 'WI' },
          { name: 'Wyoming', code: 'WY' },
        ],
      }
      return world
    },
  },
})
</script>
<style lang="scss" scoped>
#yext_account_creation {
  padding-top: 50px;
  display: grid;
  justify-content: center;
  align-items: center;
  .card {
    border: 1px solid #e1e1e1;
    box-shadow: 0 2px 4px 0px #b1b1b13b;
    width: 650px;
    min-height: 375px;
    .hl_compact_header {
      font-size: 1.2rem;
      font-weight: 500;
    }
    .card-body {
      position: relative;
      .yext_reg_form {
        .field-error {
          color: #e93d3d;
        }
        .dropdown {
          .btn-ghl_lite {
            width: 200px;
            color: #188bf6 !important;
            background-color: rgba(24, 139, 246, 0.2) !important;
            border-color: rgba(24, 139, 246, 0.2) !important;
            width: 200px;
            text-align: unset;
          }
          .dropdown-menu {
            max-height: 400px;
            overflow: auto;
          }
        }
        .loader-backdrop {
          background: #ffffff91;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          display: grid;
          justify-content: center;
          align-items: center;
          z-index: 1;
        }
      }

      .category-select-container {
        .hl-yext-category-search::v-deep .multiselect__option--highlight {
          background: #f2f7fa;
          color: #607179;
          outline: none;
        }
        .hl-yext-category-search::v-deep
          .multiselect__option--highlight::after {
          display: none;
        }

        .option__header {
          padding: 2px 0;
          font-size: 0.8rem;
        }
        .option__title {
          font-weight: 500;
        }

        .custom__tag {
          background: aliceblue;
          padding: 3px 27px 3px 5px;
          border: 1px solid #efefef;
          border-radius: 3px;
          margin-right: 5px;
          margin-bottom: 6px;
          position: relative;
          display: inline-block;
          .custom__remove {
            cursor: pointer;
            font-weight: 600;
            display: inline-block;
            position: absolute;
            top: -2px;
            right: 2px;
            font-size: 0.8rem;
            /* font-size: 1rem; */
            color: #37a2f9;
          }
        }
      }
    }
  }
}
</style>
