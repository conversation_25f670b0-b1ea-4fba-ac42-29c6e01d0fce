<template>
  <div id="latest-reviews" class="card">
    <div class="card-header card-header--compact space-between">
      <h2>Latest Review</h2>
      <div>
         <router-link   :to="{ name: 'reputation_reviews' }">View All</router-link>
      </div>
    </div>
    <div class="card-body --max-height">
      <div class="review-row" v-for="review in reviews" v-bind:key="review.id">
        <component
          :is="get_component_name(review)"
          v-bind:review="review"
          :location="location"
          :ref="review.id"
          :compact="{ smallView: true, readOnly: true }"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import {
  Contact,
  Estimate,
  Review,
  Location,
  getCountryDateFormat,
  ReviewStatus,
} from '@/models'

const Avatar = () => import('../../Avatar.vue')
const PhoneNumber = () => import('../../util/PhoneNumber.vue')

const GoogleReviewCard = () =>
  import('../../../components/GoogleReviewCard.vue')
const FacebookReviewCard = () =>
  import('../../../components/FacebookReviewCard.vue')

export default Vue.extend({
  components: {
    Avatar,
    PhoneNumber,
    GoogleReviewCard,
    FacebookReviewCard,
  },
  data() {
    return {
      currentLocationId: '',
      reviews: [] as Review[],
      location: {} as Location,
    }
  },
  computed: {},
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.fetchData()
    },
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchData()
  },
  methods: {
    get_component_name(review: Review) {
      if (review.source == Review.SOURCE_GOOGLE) {
        return 'GoogleReviewCard'
      } else if (review.source == Review.SOURCE_FACEBOOK) {
        return 'FacebookReviewCard'
      }
    },
    fetchData: async function () {
      this.location = new Location(
        await this.$store.dispatch('locations/getById', this.currentLocationId)
      )
      this.load()
    },

    async load() {
      let query = Review.collectionRef()
        .where('deleted', '==', false)
        .where('location_id', '==', this.currentLocationId)
      query = query.orderBy('date_added', 'desc')
      const snapshot = await query.limit(4).get()
      let docs = snapshot.docs
      this.reviews = docs.map(d => new Review(d))
      // this.reviews = this.reviews.filter(x => x.source === 71) // to see ony facebook
    },
  },

  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
})
</script>
<style lang="scss" scoped>
#latest-reviews {
  width: 100%;
  .space-between {
    display: flex;
    justify-content: space-between;
  }

  .card-header--compact {
    padding: 12px 50px 10px 30px;
  }

  .card-header {
    span {
      color: #afb8bc;
    }
  }

  .card-body {
    padding: 15px;
    overflow: auto;

    .review-row {
      border-bottom: 1px solid #dcdcdc;
      margin-bottom: 5px;
    }
  }
}
</style>
