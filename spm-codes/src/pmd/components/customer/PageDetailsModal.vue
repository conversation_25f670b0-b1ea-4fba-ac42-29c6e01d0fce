<template>
  <div
    class="modal fade hl_sms-template--modal"
    id="page-detail-modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog modal-xl modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Page Detail</h2>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <JotBoxPageDetail
              :loading="false"
              :isActivityPage="true"
              :util="util"
            />
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner nav">
           <UIButton use="outline" data-dismiss="modal">
              Close
            </UIButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { JotBoxUtils } from '@/pmd/pages/marketing/utils/JotBoxUtils'
const JotBoxPageDetail = () =>
  import('@/pmd/pages/marketing/jotbox/JotBoxPageDetail.vue').then(
    m => m.default
  )
declare var $: any

export default Vue.extend({
  props: ['values'],
  components: {
    JotBoxPageDetail,
  },
  data() {
    return {
      details: [
        'pageUrl',
        'oriPageTitle',
        'source',
        'campaign',
        'utm_medium',
        'utm_content',
        'referrer',
        'url_params',
      ],
      event: {},
      util: undefined,
    }
  },
  watch: {
    values(values: { [key: string]: any }) {
      if (values.visible) $(this.$refs.modal).modal('show')
      else $(this.$refs.modal).modal('hide')
      if (values.visible) {
        this.event = values.event
      }
    },
    event() {
      let results: any = {}
      Object.keys(this.event).forEach(key => {
        if (this.details.includes(key) && this.event[key]) {
          if (key == 'pageUrl') {
            results['url'] = this.event[key]
          } else if (key == 'oriPageTitle') {
            results['page_title'] = this.event[key]
          } else {
            results[key] = this.event[key]
          }
        }
      })

      this.util.selectedValue = {
        eventData: results,
      }
    },
  },
  updated() {
    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
  mounted() {
    this.util = new JotBoxUtils()
    $(this.$refs.modal).on('hidden.bs.modal', () => {
      this.$emit('hidden')
    })

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
  created() {
    this.util = new JotBoxUtils()
  },
})
</script>

<style lang="scss" scoped>
#page-detail-modal {
  .modal-header {
    .modal-header--inner {
      margin-left: 0;
      margin-right: 0;
    }
  }
  .modal-body {
    padding: 0;

    .modal-body--inner {
      max-width: 100%;
    }
  }
}
</style>
