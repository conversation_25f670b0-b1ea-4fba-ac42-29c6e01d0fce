<template>
  <div id="membership">
    <MembershipBuilder
      v-if="location"
      :key="`${$route.params.location_id}`"
      :location="location"
      :user="user"
    />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import MembershipBuilder from './MembershipBuilder.vue'

export default Vue.extend({
  components: { MembershipBuilder },
  computed: {
    locationId() {
      return this.$route.params.locationId
    },
    location() {
      const locations = this.$store.state.locations.locations
      return locations.find(
        (x: { [key: string]: any }) => x.id === this.$route.params.location_id
      )
    },
    user() {
      return this.$store.state.auth.user
    },
  },
})
</script>
