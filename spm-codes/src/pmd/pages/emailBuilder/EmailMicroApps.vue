<template>
  <div
    id="emailHome"
    ref="emailBuilder"
    style="height: calc(100vh - 95px); width: 100%; border: none"
  ></div>
</template>

<script lang="ts">
import Vue from 'vue'
import Postmate from 'postmate'
import config from '@/config'
import firebase from 'firebase/app'
import { Location } from '@/models'
export default Vue.extend({
  data() {
    return {
      loading: true,
      handshake: {},
    }
  },
  mounted() {
    this.loadMicroApp()
  },
  methods: {
    async getToken() {
      return await firebase.auth().currentUser.getIdToken()
    },
    async loadMicroApp() {
      const routeName = this.$route.name
      console.log(routeName)
      if (!routeName) return
      const { id: userId, email, first_name, last_name } = this.$store.state.user.user
      const { locations } = this.$store.state.locations
      const locationData = locations.find(
        (x: Location) => x.id === this.$route.params.location_id
      )
      if (process.env.NODE_ENV === 'development') Postmate.debug = true
      this.handshake = new Postmate({
        container: this.$refs.emailBuilder as HTMLElement,
      url: `${config.emailHomeAppUrl}${this.$route.path.replace('/v2/location/','/location/')}`,
        name: 'emails-home',
        model: {
          userId,
          email,
          apiKey: locationData.api_key,
          tokenId: await this.getToken(),
          companyId: locationData.company_id,
          timeZone: locationData.timezone,
          userName: `${first_name} ${last_name}`,
        },
      })
      //@ts-expect-error postmate handler
      this.handshake.then((child: any) => {
        child.on('spm-ts', (data: any) => {
          if (data.newTab) {
            let routeUrl = this.$router.resolve(data.router)
            window.open(routeUrl.href, '_blank')

            console.log(routeUrl.href)
          } else {
            this.$router.push(data.router)
          }
        })
        child.on('refresh-token', async () => {
          child.call('updated-token', await this.getToken())
        })
      })
    },
    destroy() {
      //@ts-expect-error postmate handler
      this.handshake.then((child: any) => {
        child.destroy()
      })
    },
  },
  beforeDestroy() {
    this.destroy()
  },
})
</script>

<style>
#emailHome iframe {
  width: 100%;
  height: 100%;
  border: none;
}
</style>
