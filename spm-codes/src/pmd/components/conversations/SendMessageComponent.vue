<template>
  <div class="message-input-wrap overflowdisabled">
    <!-- overflowdisabled is very important. See case 988-->
    <div class="nav-item float-right" v-if="allowMessaging">
      <a
        @click.stop.prevent="toggleView()"
        :class="{ 'disable-button': isOnboarding }"
        v-if="collapseContent === true"
      >
        <i class="fa fa-expand font17" aria-hidden="true"></i>
      </a>
      <a
        @click.stop.prevent="toggleView()"
        :class="{ 'disable-button': isOnboarding }"
        v-if="!collapseContent"
      >
        <i class="fa fa-compress font17" aria-hidden="true"></i>
      </a>
    </div>

    <ul
      class="nav nav-tabs --dont-stack"
      style="border: none !important"
      role="tablist"
    >
      <li class="nav-item" v-if="sms">
        <a
          class="nav-link open-sms-tab"
          :class="{
            active: tab === 'sms',
            font17: collapseContent,
            'disable-button': isOnboarding,
          }"
          id="sms-tab"
          href="javascript:void(0);"
          @click.prevent="show('sms')"
          >{{ collapseContent ? 'Send ' : '' }}SMS</a
        >
      </li>
      <li class="nav-item" v-if="email && !isOnboarding">
        <a
          class="nav-link open-email-tab"
          :class="{ active: tab === 'email', font17: collapseContent }"
          id="email-tab"
          href="javascript:void(0);"
          @click.prevent="show('email')"
          >{{ collapseContent ? 'Send ' : '' }}Email</a
        >
      </li>
      <li class="nav-item" v-if="facebook">
        <a
          class="nav-link open-facebook-tab"
          :class="{ active: tab === 'messenger', font17: collapseContent }"
          id="facebook-tab"
          href="javascript:void(0);"
          @click.prevent="show('messenger')"
          >{{ collapseContent ? 'Send ' : '' }}FB Message</a
        >
      </li>
      <li class="nav-item" v-if="gmb">
        <a
          class="nav-link open-gmb-tab"
          :class="{active: tab==='gmb', font17:collapseContent }"
          id="gmb-tab"
          href="javascript:void(0);"
          @click.prevent="show('gmb')"
        >{{ collapseContent ? 'Send ': ''}}GMB Message</a>
      </li>
      <li class="nav-item" v-if="review">
        <a
          class="nav-link open-review-tab"
          :class="{active: tab==='review', font17:collapseContent }"
          id="review-tab"
          href="javascript:void(0);"
          @click.prevent="show('review')"
        >{{ collapseContent ? 'Send ': ''}}Review Response</a>
      </li>
      <li class="nav-item" v-if="instagram">
        <a
          class="nav-link open-instagram-tab"
          :class="{ active: tab === 'instagram', font17: collapseContent }"
          id="instagram-tab"
          href="javascript:void(0);"
          @click.prevent="show('instagram')"
          >{{ collapseContent ? 'Send ' : '' }}Instagram DM</a
        >
      </li>
    </ul>
    <div class="tab-content" v-if="(!collapseContent && allowMessaging)">
      <SendSMSComponent
        ref="sendSmsComponent"
        v-show="tab === 'sms'"
        :conversation="conversation"
        :contact="contact"
        :manualSMS="manualSMS"
        @messageSent="messageSent"
      />
      <SendEmailComponent
        v-show="tab === 'email'"
        :conversation="conversation"
        :contact="contact"
        @editorLoaded="sendContainerHeight"
        @messageSent="messageSent"
        :replyEmailMessage.sync="replyEmailMessageLocal"
      />
      <SendFacebookMessageComponent
        v-show="tab === 'messenger'"
        :conversation="conversation"
        :contact="contact"
        @messageSent="messageSent"
      />
      <SendGMBMessage
        v-show="tab==='gmb'"
        :conversation="conversation"
        :contact="contact"
        @messageSent="messageSent"
      />
      <SendReviewReplyComponent
        v-show="tab==='review'"
        :conversation="conversation"
        @messageSent="messageSent"
      />
      <SendInstagramDMComponent
        v-show="tab === 'instagram'"
        :conversation="conversation"
        :contact="contact"
        @messageSent="messageSent"
      />
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import firebase from 'firebase/app'
const SendSMSComponent = () => import('./SendSMSComponent.vue')
const SendEmailComponent = () => import('./SendEmailComponent.vue')
const SendFacebookMessageComponent = () =>
  import('./SendFacebookMessageComponent.vue')
const SendGMBMessage = () => import('./SendGMBMessage.vue')
const SendReviewReplyComponent = () => import('./SendReviewReplyComponent.vue')
const SendInstagramDMComponent = () => import('./SendInstagramDMComponent.vue')
import { EmailMessage, MessageType, GMBIntegration } from '@/models'
import { EventBus } from '@/models/event-bus'

let cancelGMBIntegrationSubscription:() => void

export default Vue.extend({
  props: ['conversation', 'contact', 'manualSMS'],
  components: {
    SendSMSComponent,
    SendEmailComponent,
    SendFacebookMessageComponent,
    SendReviewReplyComponent,
    SendGMBMessage,
    SendInstagramDMComponent
  },
  mounted() {
    const _self = this
    EventBus.$on('reply_to_email', (message: EmailMessage) => {
      // On inital load -> This component will be mounted when the Reply button is clicked. SendEmail wont.
      // So that we cover the case where Reply is clicked on inital load, we'll use a prop instead of event-bus on SendEmail
      // But, this component will still require the EventBus because it's not parent/child of the reply button component
      if (_self.contact) {
        _self.tab = 'email'
        _self.collapseContent = false
        _self.replyEmailMessageLocal = message
      }
    })
    EventBus.$on('reply_to_review', () => {
      if (_self.review) {
        _self.tab = 'review'
        _self.collapseContent = false
      }
    })
    if (this.$store.state.conversation.onboarding) {
      this.$emit('hideScrollDownShortcut')
      this.collapseContent = false
      this.setActiveTab()
    }
    // this.$root.$on('set_manual_sms', function(body: any) {
    //   if (_self.contact) {
    //     _self.tab = 'sms';
    //     _self.collapseContent = false;
    //     _self.manualSMS = body;
    //   }
    // })
  },
  computed: {
    allowMessaging() {
      return this.sms || this.email || this.facebook || this.gmb || this.review || this.instagram
    },
    email(): boolean {
      return this.contact && this.contact.email ? true : false
    },
    sms(): boolean {
      return this.contact && this.contact.phone ? true : false
    },
    facebook(): boolean {
      return this.contact && this.contact.facebookId ? true : false
    },
    instagram(): boolean {
      return this.location && this.location.instagram_page_id && this.contact && this.contact.instagramId ? true : false
    },
    review(): boolean {
      return this.conversation && this.location && this.location.gmb && this.conversation.gmbPageId && this.conversation.gmbPageId === this.location.gmb.id ? true : false
    },
    isOnboarding(): boolean {
      if (this.$store.state.conversation.onboarding) {
        return true
      }
      return false
    },
    disableSendSms(): boolean {
      if (
        this.$store.state.conversation.onboarding &&
        this.$store.state.conversation.popupStep <= 2
      ) {
        return true
      }
      return false
    },
    gmb(): boolean {
      return this.gmbIntegration && this.gmbIntegration.messagingStatus === 'Success' && this.contact && this.contact.googleData && this.contact.googleData.id ? true : false
    },
    location() {
      return this.$store.getters['locations/getById'](this.$route.params.location_id);
    },
  },
  watch: {
    manualSMS() {
      this.settingsForManualSMS()
    },
    contact() {
      if (this.collapseContent) {
        this.tab = ''
        return
      }
      if (this.tab) {
        if (!this.email && this.tab === 'email') this.tab = ''
        if (!this.sms && this.tab === 'sms') this.tab = ''
        if (!this.facebook && this.tab === 'messenger') this.tab = ''
        if (!this.review && this.tab === 'review') this.tab = ''
        if (!this.gmb && this.tab === 'gmb') this.tab = ''
        if (!this.instagram && this.tab === 'instagram') this.tab = ''
      }
      if (!this.tab && this.conversation && this.conversation.lastMessageType) {
        switch (this.conversation.lastMessageType) {
          case MessageType.TYPE_EMAIL:
          case MessageType.TYPE_CAMPAIGN_EMAIL:
            if (this.email) this.tab = 'email'
            break
          case MessageType.TYPE_SMS:
          case MessageType.TYPE_SMS_REVIEW_REQUEST:
          case MessageType.TYPE_SMS_NO_SHOW_REQUEST:
          case MessageType.TYPE_CAMPAIGN_SMS:
          case MessageType.TYPE_CAMPAIGN_MANUAL_SMS:
            if (this.sms) this.tab = 'sms'
            break
          case MessageType.TYPE_FACEBOOK:
          case MessageType.TYPE_CAMPAIGN_FACEBOOK:
            if (this.facebook) this.tab = 'messenger'
            break
          case MessageType.TYPE_REVIEW:
            if (this.review) this.tab = 'review'
            break
          case MessageType.TYPE_GMB:
            if (this.gmb) this.tab = 'gmb'
            break
          case MessageType.TYPE_INSTAGRAM:
            if (this.instagram) this.tab = 'instagram'
            break
        }
      }
      if (!this.tab && this.sms) this.tab = 'sms'
      if (!this.tab && this.email) this.tab = 'email'
      if (!this.tab && this.facebook) this.tab = 'messenger'
      if (!this.tab && this.review) this.tab = 'review'
      if (!this.tab && this.gmb) this.tab = 'gmb'
      if (!this.tab && this.instagram) this.tab = 'instagram'
    },
    tab() {
      this.lastActive = this.tab
      this.sendContainerHeight()
    },
  },
  methods: {
    messageSent() {
      this.sendContainerHeight()
      EventBus.$emit('scrollToBottom')
    },
    settingsForManualSMS() {
      if (this.manualSMS && this.manualSMS.body && this.contact) {
        this.tab = 'sms'
        this.collapseContent = false
      }
    },
    show(view: string) {
      this.collapseContent = undefined
      this.tab = view
    },
    toggleView() {
      this.$emit('hideScrollDownShortcut')
      this.collapseContent = !this.collapseContent
      this.setActiveTab()
    },
    setActiveTab() {
      if (this.collapseContent === true) this.tab = ''
      else
        this.tab = this.lastActive
          ? this.lastActive
          : this.sms
          ? 'sms'
          : this.email
          ? 'email'
          : this.facebook
          ? 'messenger'
          : this.gmb
          ? 'gmb'
          : this.review
          ? 'review'
          : this.instagram
          ? 'instagram'
          : ''
    },
    sendContainerHeight() {
      setTimeout(() => {
        this.$nextTick(() => {
          this.$emit('update:activeTabHeight', this.$el.clientHeight)
        })
      }, 100)
    },
  },
  data() {
    return {
      tab: '',
      collapseContent: true,
      lastActive: '',
      replyEmailMessageLocal: undefined as EmailMessage,
      gmbIntegration: undefined as GMBIntegration
      // manualSMS: undefined
    }
  },
  created() {
    if (window.screen.height > 1020) this.collapseContent = undefined
    this.sendContainerHeight()
    this.settingsForManualSMS()
    if (cancelGMBIntegrationSubscription) {
      this.gmbIntegration = undefined
      cancelGMBIntegrationSubscription()
    }

    cancelGMBIntegrationSubscription = GMBIntegration.getByLocationIdRealtime(
      this.location.id
    ).onSnapshot(async (snapshot: firebase.firestore.QuerySnapshot) => {
      this.gmbIntegration = snapshot.docs.map(
        (d: firebase.firestore.QueryDocumentSnapshot) => new GMBIntegration(d)
      )[0]
    })
    EventBus.$on('pick_tab', (name: string) => {
      this.tab = name
    })
  },
  beforeDestroy() {
    // console.log('destroy message acivity')
    EventBus.$off('pick_tab')
    EventBus.$off('reply_to_email') // Removes all instances of this event listener
    EventBus.$off('reply_to_review')
    //this.$root.$off('set_manual_sms');
    if (this.collapseContent) {
      this.tab = ''
    }
    if (cancelGMBIntegrationSubscription) {
      cancelGMBIntegrationSubscription()
    }
  },
})
</script>

<style scope>
.fa-expand:hover {
  font-size: 24px;
}
.fa-compress:hover {
  font-size: 24px;
}
.font17 {
  font-size: 16px;
}
.disable-button {
  pointer-events: none;
  color: #178af6;
  opacity: 0.35;
}
</style>
