<template>
  <div id="latest-review-requests" class="card">
    <div class="card-header card-header--compact space-between">
      <h2>Latest Review Requests</h2>
      <div>
        <router-link   :to="{ name: 'reputation_review_requests' }">View All</router-link>
      </div>
    </div>
    <div class="card-body --max-height">
      <table class="table table-sort">
        <thead>
          <tr>
            <th>Invite <PERSON>t to</th>
            <th>Email/Phone</th>

            <th>Date Sent</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="request in requests" :key="request.id">
            <td>
              <Avatar
                v-if="contacts[request.contactId]"
                :contact="contacts[request.contactId]"
                :include_name="true"
              />
            </td>

            <td v-if="request.method == 'email'">
              {{ (contacts[request.contactId] && contacts[request.contactId].email) || request.meta.email }}
            </td>
            <td v-else-if="request.method == 'sms'">
              <PhoneNumber
                type="display"
                :value="(contacts[request.contactId] && contacts[request.contactId].phone) ? contacts[request.contactId].phone : request.meta.phone"
                :currentLocationId="request.locationId"
              />
            </td>
            <td>{{ request.dateAdded.calendar() }}</td>
          </tr>
          <!--    <ReviewRequestCard
                    v-for="request in requests"
                    :key="request.id"
                    :request="request"
                  /> -->
        </tbody>
      </table>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
const ReviewRequestCard = () =>
  import('../../../components/ReviewRequestCard.vue').then(m => m.default)

import { ReviewRequest, Contact } from '../../../../models'
const Avatar = () => import('../../Avatar.vue')
const PhoneNumber = () => import('../../util/PhoneNumber.vue')

export default Vue.extend({
  components: {
    Avatar,
    PhoneNumber,
  },
  data() {
    return {
      currentLocationId: '',
      requests: [] as ReviewRequest[],
      contacts : [] as Contact[]
    }
  },
  computed: {
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.filter = 'lastSixMonthMonthly';
      this.updateRequests()
    },
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id;
    this.updateRequests();
  },
  methods: {
    updateRequests(){
      ReviewRequest.fetchAllRequests(this.currentLocationId, 6).onSnapshot(
        async snapshot => {
          const allReqests:any = snapshot.docs.map(d => new ReviewRequest(d))
        for (let i = 0; i < allReqests.length ; i++) {
          if(this.contacts[ allReqests[i].contactId]){
            continue;
          }
          let contact: Contact | any = await this.$store.dispatch(
            'contacts/syncGet',
           allReqests[i].contactId
          )
          if (!contact) {
            contact = { fullName: 'Deleted' }
          }
          this.contacts[allReqests[i].contactId] = contact
        }
        // cooking the contacts before creating request, for reactive purpose. dont change order 
        this.requests = allReqests
      }
    )
    }
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
})
</script>
<style lang="scss" scoped>
#latest-review-requests {
  width: 100%;
  .space-between {
    display: flex;
    justify-content: space-between;
  }

  .card-header--compact {
    padding: 12px 50px 10px 30px;
  }

  .card-header {
    span {
      color: #afb8bc;
    }
  }

  .card-body {
    padding: 15px;
    overflow: auto;

  }
}
</style>
