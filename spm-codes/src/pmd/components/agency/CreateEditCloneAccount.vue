 <template>
	<div class="modal fade hl_sms-template--modal" tabindex="-1" role="dialog" ref="modal">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<div class="modal-header--inner">
						<h2 class="modal-title">Account Snapshot</h2>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</div>
				<div class="modal-body">
					<div class="modal-body--inner">
						<div class="form-group">
							<UITextInputGroup
								type="text"
								label="Snapshot name"
								placeholder="Snapshot name"
								v-model="name"
								v-validate="'required'"
								name="snapshotname"
								:error="errors.has('snapshotname')"
								:errorMsg="'You need to give the snapshot a name.'"
							/>
						</div>
						<div class="form-group" v-if="!editMode">
							<UITextLabel>Account</UITextLabel>
							<vSelect
								:options="locations"
								label="name"
								v-model="locationPicked"
								:clearable="false"
								placeholder="Select account"
								v-validate="'required'"
								name="location"
								data-vv-as="Location"
								:loading="loading.locations"
							>
								<template #spinner="{ loading }">
									<div v-if="loading" style="border-left-color: rgba(88,151,251,0.71)" class="vs__spinner"></div>
								</template>
							</vSelect>
							<span v-show="errors.has('location')" class="--red">You need to pick an account.</span>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<div class="modal-footer--inner nav">
						<UIButton type="button" use="outline" data-dismiss="modal">Cancel</UIButton>
						<div style="display: inline-block;position: relative;">
							<UIButton
								type="button"
								@click.prevent="() => editMode ? updateSnapshot() : createSnapshot()"
                				:disabled="submitDisabled"
								use="primary"
								:loading="processing"
							>Save</UIButton>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from "vue";
import * as lodash from "lodash";
import vSelect from "vue-select";
import { Location, CloneAccount } from "@/models";

declare var $: any;

export default Vue.extend({
	props: ['values'],
	components: {
		vSelect
	},
	data() {
		return {
			locationPicked: undefined as { [key: string]: any } | undefined,
			name: "",
			snapshot: undefined as CloneAccount | undefined,
			processing: false,
			editMode: false,
			locations: [],
			loading: {
				locations: false
			}
		};
	},
	computed: {
		submitDisabled: function(): boolean {
			return !this.name
		}
	},
	methods: {
    async updateSnapshot() {
      const { snapshotId } = this.values;
      if (snapshotId) {
        this.processing = true;
        await this.$http.post(`/snapshot/${snapshotId}/update`, { name: this.name });
        this.$emit('hidden');
      }
    },
		async createSnapshot() {
			const result = await this.$validator.validateAll();
			if (!result) {
				return false;
			}
      this.processing = true;
      const response = await this.$http.post(`/snapshot`, {
        name: this.name,
        location_id: this.locationPicked.id
      })
			this.$emit("hidden");
		}
	},
	beforeDestroy() {
		$(this.$refs.modal).off("hidden.bs.modal");
	},
	watch: {
		async values(values: { [key: string]: any }) {
			const data: (() => object) = <(() => object)>this.$options.data;
			if (data) Object.assign(this.$data, data.apply(this));
			if (values.visible) $(this.$refs.modal).modal('show');
			else $(this.$refs.modal).modal('hide');

			if(values.visible) {
				this.loading.locations = true
				this.locations = await this.$store.dispatch('locations/getAll')
				this.loading.locations = false
			}

			if (values.snapshotId) {
				this.editMode = true;
				this.snapshot = await CloneAccount.getById(values.snapshotId);
				this.name = this.snapshot.name;
			}
		}
	},
	updated() {
		if (this.values && this.values.visible) {
			$(this.$refs.modal).modal('show');
		}
	},
	mounted() {
		const _self = this;
		$(this.$refs.modal).on("hidden.bs.modal", function () {
			_self.$emit("hidden");
		});

		if (this.values && this.values.visible) {
			$(this.$refs.modal).modal('show');
		}
	}
});
</script>
<style>
.dropdown-toggle::after {
	content: none;
}
</style>
