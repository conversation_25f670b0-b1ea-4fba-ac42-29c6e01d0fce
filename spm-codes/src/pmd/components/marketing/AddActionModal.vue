<template>
  <!-- Add Event Modal -->
  <div
    class="modal fade hl_add-event-modal"
    id="add-event-modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog --small" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h5 class="modal-title">
              <i class="icon icon-plus"></i> Add New Event
            </h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body-inner">
            <div class="row">
              <div class="col-sm-6">
                <a @click.prevent="selected('sms')">
                  <i class="icon icon-smartphone"></i>
                  <h4>SMS</h4>
                </a>
              </div>
              <div class="col-sm-6">
                <a @click.prevent="selected('messenger')">
                  <i class="fab fa-facebook-messenger"></i>
                  <h4>Messenger</h4>
                </a>
              </div>
              <div class="col-sm-6">
                <a @click.prevent="selected('email')">
                  <i class="icon icon-mail"></i>
                  <h4>Email</h4>
                </a>
              </div>
              <div class="col-sm-6">
                <a @click.prevent="selected('call')">
                  <i class="fas fa-phone"></i>
                  <h4>Call</h4>
                </a>
              </div>
              <div class="col-sm-6">
                <a @click.prevent="selected('voicemail')">
                  <i class="icon icon-mic"></i>
                  <h4>Voicemail</h4>
                </a>
              </div>
              <div class="col-sm-6">
                <a @click.prevent="selected('wait')">
                  <i class="icon icon-clock"></i>
                  <h4>Wait</h4>
                </a>
              </div>
              <div class="col-sm-6">
                <a @click.prevent="selected('manual-sms')">
                  <i class="icon icon-smartphone"></i>
                  <h4>Manual SMS</h4>
                </a>
              </div>
              <div class="col-sm-6">
                <a @click.prevent="selected('manual-call')">
                  <i class="fas fa-phone"></i>
                  <h4>Manual Call</h4>
                </a>
              </div>
              <div class="col-sm-6">
                <a @click.prevent="selected('webhook')">
                  <i class="fas fa-satellite-dish"></i>
                  <h4>Webhook</h4>
                </a>
              </div>
              <div class="col-sm-6">
                <a @click.prevent="selected('task-notification')">
                  <i class="fas fa-tasks"></i>
                  <h4>Add Task</h4>
                </a>
              </div>
              <div class="col-sm-6">
                <a @click.prevent="selected('gmb')">
                  <i class="icon icon-google"></i>
                  <h4>GMB Message</h4>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
declare var $: any

export default Vue.extend({
  props: ['values'],
  async created() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hidden')
    })
  },
  methods: {
    selected(type: string) {
      this.$emit('selected', type)
      this.$emit('hidden')
    }
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  watch: {
    values(values: { [key: string]: any }) {
      if (values.visible) $(this.$refs.modal).modal('show')
      else $(this.$refs.modal).modal('hide')
    }
  },
  updated() {
    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
  mounted() {
    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  }
})
</script>

