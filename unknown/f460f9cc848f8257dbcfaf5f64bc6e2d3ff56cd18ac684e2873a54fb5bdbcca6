<template>
	<Filters
		:conditions="conditions"
		:filterMaster="filterMaster"
		v-on:update:conditions="$emit('update:conditions', $event)"
	/>
</template>

<script lang="ts">
import Vue from 'vue'
import { Campaign, Formbuilder, FacebookAdFieldsMap } from '@/models';
import { Condition } from '@/models/trigger';

const Filters = () => import('./Filters.vue');

export default Vue.extend({
	props: ['conditions'],
	components: { Filters },
	data() {
		return {
			filterMaster: [
				{
					placeHolder: 'Select form',
					title: 'In Form',
					value: 'facebook.formId',
					valueType: 'text',
					type: 'select',
					options: [] as { [key: string]: any }[]
				},
			],
		}
	},
	async created() {
		console.log(this.conditions);
		const currentLocationId = this.$router.currentRoute.params.location_id;
		const filter = lodash.find(this.filterMaster, { value: 'facebook.formId' });
		if (filter) {
			const forms = await this.$http.get("/facebook/get_leadgen_forms/" + currentLocationId);
			if (forms.data) {
				let mappedForms = await FacebookAdFieldsMap.getByLocationAndPage(currentLocationId, forms.data[0].page_id);
				filter.options = forms.data && forms.data.filter((form: { [key: string]: any }) => {
					return lodash.some(mappedForms, { formId: form.id });
					//mappedForms.forEach((f) => {return lodash.find(forms, {formId: f.formId})});
				}).map((form: { [key: string]: any }) => {
					return {
						title: form.name,
						value: form.id
					}
				});
			}
		}
	}
})
</script>

