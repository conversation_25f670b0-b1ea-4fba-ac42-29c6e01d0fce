<template>
  <div
    class="modal fade hl_sms-template--modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content" style="padding-top: 30px; padding-bottom: 30px;" v-if="firstLoading">
         <moon-loader
            :loading="true"
            color="#37ca37"
            size="30px"
          />
      </div>
      <div class="modal-content" v-else>
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">
              {{
                editMode
                  ? `Edit a custom menu link`
                  : `Create a custom menu link`
              }}
            </h2>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div class="form-group">
              <UITextLabel>Link icon</UITextLabel>
              <icon-picker
                ref="icon-picker"
                :data.sync="icon"
                validation="linkicon"
                error-message="The link requires an icon."
              />
            </div>

            <div class="form-group">
              <UITextInputGroup
                type="text"
                label="Link title"
                placeholder="Link name"
                v-model="title"
                maxlength="40"
                v-validate="'required'"
                name="linktitle"
                :error="errors.has('linktitle')"
                :errorMsg="'The link requires a title.'"
              />
            </div>

            <div class="form-group">
              <UITextLabel
                >URL
                <i
                  class="fa fa-info-circle"
                  v-b-tooltip.hover
                  :title="
                    `You may use Location values and Custom Values in here. Example: http://url.com/{{location.id}}?value={{custom_values.my_value}} (These will only work on the Location side bar, when inside an account)`
                  "
                ></i
              ></UITextLabel>
              <UITextInputGroup
                type="text"
                placeholder="Link URL"
                v-model="url"
                v-validate="'required|validUrl'"
                name="linkurl"
                :error="errors.has('linkurl')"
                :errorMsg="errors.first('linkurl') === 'Invalid url.' ? 'Invalid link URL.' : 'The link requires an URL.'"
              />
            </div>

                <div class="form-group">
                  <label>Show link on Agency sidebar</label>
                  <UIToggle
                    id="company-menu"
                    v-model="show_on_company"
                  />
                </div>
                <div class="row">
                  <div class="col-sm-6">
                <div class="form-group">
                  <label>Show link on Account sidebar</label>
                  <UIToggle
                    id="location-menu"
                    v-model="show_on_location"
                  />
                </div>
                  </div>
                  <div class="col-sm-6">
                    <div class="form-group" v-if="show_on_location">
                      <label>Show to all accounts</label>
                      <UIToggle
                        id="show-to-all-locations"
                        v-model="show_to_all_locations"
                      />
                    </div>
                  </div>
                </div>

                <div v-if="show_on_location && !show_to_all_locations" class="form-group"> 
                  <vSelect
                    multiple
                    :options="locations"
                    label="name"
                    v-model="locationsToShow"
                    :clearable="false"
                    name="location"
                    v-validate="'required'"
                    data-vv-as="Location"
                    :loading="loading.locations"
                  >
                    <template #spinner="{ loading }">
                      <div v-if="loading" style="border-left-color: rgba(88,151,251,0.71)" class="vs__spinner"></div>
                    </template>
                  </vSelect>
                  <span v-show="errors.has('location') && locationsToShow.length === 0" class="--red"
                    >At least one location should be selected.</span
                  >
                </div>

            <div class="form-group">
              <label>When clicked:</label>
              <div class="radio">
                <input
                  type="radio"
                  v-model="open_mode"
                  value="iframe"
                  id="iframe"
                />
                <label for="iframe">
                  Open in iFrame inside the platform
                  <i
                    class="fa fa-info-circle"
                    v-b-tooltip.hover
                    :title="
                      `For security reasons, some sites may not allow to be used in a iframe. You can open them in a new tab instead.`
                    "
                  ></i>
                </label>
              </div>
              <div class="radio">
                <input
                  type="radio"
                  v-model="open_mode"
                  value="new_tab"
                  id="new_tab"
                />
                <label for="new_tab">Open in a New Tab</label>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <div class="modal-footer--inner nav">
            <UIButton use="outline" data-dismiss="modal">
              Cancel
            </UIButton>
            <div style="display: inline-block;position: relative;">
              <UIButton
                @click.prevent="
                  () =>
                    editMode
                      ? editCustomSidebarLink()
                      : createCustomSidebarLink()
                "
                :disabled="submitDisabled"
                :loading="processing"
              >
                Save
              </UIButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { v4 as uuid } from 'uuid'
import IconPicker from '@/pmd/components/common/IconPicker.vue'
import vSelect from 'vue-select'
import { Company, Location } from '@/models'

declare var $: any

export default Vue.extend({
  components: { IconPicker, vSelect },
  props: ['values'],
  data() {
    return {
      company: {} as Company,
      title: '' as string,
      url: '' as string,
      show_on_company: true as boolean,
      show_on_location: true as boolean,
      show_to_all_locations: true as boolean,
      open_mode: 'iframe' as string,
      locations: [] as Location[],
      locationsToShow: [] as Location[],
      icon: {
        value: {
          name: '',
          unicode: '',
          fontFamily: ''
        }
      } as object,
      processing: false as boolean,
      firstLoading: false as boolean,
      search: '' as string,
      loading: {
        locations: false
      }
    }
  },
  computed: {
    submitDisabled: function(): boolean {
      return !this.url
    },
    editMode(): boolean {
      return Boolean(this.values.linkId)
    }
  },
  methods: {
    reset() {
      this.$validator.reset()
      this.title = ''
      this.url = ''
      this.show_on_company = true
      this.show_on_location = true
      this.open_mode = 'iframe'
      this.icon = {
        value: {
          name: '',
          unicode: '',
          fontFamily: ''
        }
      }
      this.processing = false
    },
    async createCustomSidebarLink() {
      const result = await this.$validator.validateAll()
      const resultFromIconPicker = await this.$refs[
        'icon-picker'
      ].$validator.validateAll()
      if (!result || !resultFromIconPicker) return false
      try {
        this.processing = true
        const customMenuLink = {
          id: uuid(),
          icon: this.icon,
          title: this.title,
          url: this.url,
          show_on_company: this.show_on_company,
          show_on_location: this.show_on_location,
          open_mode: this.open_mode
        }

        if(this.show_on_location && !this.show_to_all_locations) {
          customMenuLink.locations = this.locationsToShow.map(location => location.id)
        }

        this.company.addCustomMenuLink(customMenuLink)
        await this.company.save()
        this.reset()
        this.$emit('hidden')
      } catch (error) {
        console.log(error)
      }
    },
    async editCustomSidebarLink() {
      const result = await this.$validator.validateAll()
      const { linkId } = this.values
      if (!result) return false
      try {
        this.processing = true
        const customMenuLink = {
          id: linkId,
          icon: this.icon,
          title: this.title,
          url: this.url,
          show_on_company: this.show_on_company,
          show_on_location: this.show_on_location,
          open_mode: this.open_mode
        }

        if(this.show_on_location && !this.show_to_all_locations) {
          customMenuLink.locations = this.locationsToShow.filter(location => location).map(location => location.id)
        }

        this.company.customMenuLinks = this.company.customMenuLinks.map(link =>
          link.id === linkId ? customMenuLink : link
        )
        await this.company.save()
        this.reset()
        this.$emit('hidden')
      } catch (error) {
        console.log(error)
      }
    }
  },
  beforeDestroy() {
    this.reset()
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  watch: {
    async values(values: { [key: string]: any }) {
      const data: () => object = <() => object>this.$options.data
      if (data) Object.assign(this.$data, data.apply(this))
      if (values.visible) $(this.$refs.modal).modal('show')
      else $(this.$refs.modal).modal('hide')

      if (values.visible) {
        try {
          this.firstLoading = true
          this.locationsToShow = []
          this.company = await Company.getById(this.values.companyId)

          this.loading.locations = true
          this.locations = await this.$store.dispatch('locations/getAll')
          this.loading.locations = false
          
        
          if (values.linkId) {
            const customMenuLink = this.company.customMenuLinks.find(
              link => link.id === values.linkId
            )
            if (customMenuLink) {
              this.icon = customMenuLink.icon
              this.title = customMenuLink.title
              this.url = customMenuLink.url
              this.show_on_company = customMenuLink.show_on_company
              this.show_on_location = customMenuLink.show_on_location
              this.open_mode = customMenuLink.open_mode
            }
  
            if(customMenuLink.locations && customMenuLink.locations.length) {
              this.show_to_all_locations = false

              customMenuLink.locations.forEach(locationId => {
                this.locationsToShow.push(this.locations.find(location => location.id === locationId))
              })
            } else {
              this.show_to_all_locations = true
            }
          } else {
            this.show_to_all_locations = true
          }
        } catch (error) {
          console.log(error)
        } finally {
          this.firstLoading = false
        }
      }
    }
  },
  updated() {
    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.reset()
      _self.$emit('hidden')
    })

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  }
})
</script>
<style scoped>
.sm-button::before {
  font-family: var(--ff);
  font-weight: 900;
  content: var(--fa);
  font-style: normal;
}

.radio {
  display: inline-block;
  margin-right: 10px;
}
</style>
