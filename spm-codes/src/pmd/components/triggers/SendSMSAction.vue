<template>
	<div>
		<div class="form-group">
			<label>To</label>
			<PhoneNumber
				v-model="to"
				v-validate="'required|handlebars'"
				name="toPhone"
				data-vv-validate-on="input"
				data-vv-name="To"
				@input="setValue('to', to.length ? to.split(',').map(e => e.trim()) : [])"
				:currentLocationId="currentLocationId"
			/>
		</div>
			<span v-show="errors.has('To')" class="--red">{{errors.first('To')}}</span>
		<div class="form-group">
			<label>Message</label>
			<div class="custom-dropdown-menu">
				<NestedMenu
				class="my-2"
				v-show="displayMenu"
				:items="menuItems"
				:title="'Merge fields'"
				v-on:MenuSelected="menuItemSelected"
				:fromAction="true"
			/>
			<editor
				:init="editorOptions"
				v-model="tinymcehtml"
        :id='getRandomId'
				v-validate="'handlebars'"
        name="messageEditor"
				data-vv-validate-on="input"
        ref='editor'
			></editor>
			</div>
			<span v-show="errors.has('messageEditor')" class="--red">{{errors.first('messageEditor')}}</span>
		</div>
		<div class="form-group form-btn-group">
			<div style="display: inline-block;position: relative;">
				<UIButton
					type="button"
					use="outline"
					@click.prevent="preview"
					:loading="sendingPreview"
				>Send Test SMS</UIButton>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
import Editor from '@tinymce/tinymce-vue';
import { mapState } from 'vuex';
import libphonenumber from 'google-libphonenumber';
import { UserState } from '../../../store/state_models';
import {
  User,
  CustomField
} from "@/models";
import NestedMenu from '@/pmd/components/NestedMenu.vue'
import { EventBus } from '@/models/event-bus'
import { FilterMergeTags } from '../../../util/filter_merge_tags'

const TagComponent = () => import('../customer/TagComponent.vue');
const PhoneNumber = () => import('../util/PhoneNumber.vue');

const phoneUtil = libphonenumber.PhoneNumberUtil.getInstance();

export default Vue.extend({
	props: ['action', 'editorOptions', 'triggerType', 'displayMenu'],
	components: { TagComponent, 'editor': Editor, PhoneNumber, NestedMenu },
	data() {
		return {
			tinymcehtml: '',
			body: '',
			to: '',
			sendingPreview: false,
      currentLocationId: '',
      menuItems: [] as any[],
      customFields: [] as { [key: string]: any }[]
		}
	},
	methods: {
	  setValue(field: string, value: string) {
      Vue.set(this.action, field, value)
      this.$emit('update:action', this.action)
	  },
    menuItemSelected(item) {
      console.log("From method" + item);
      EventBus.$emit('customFieldSelected', item);
		},
		async checkEditor() {
      await this.$validator.validate('toPhone')
    },
		async preview() {
			const result = await this.$validator.validateAll();
			if (!result) {
				return false;
			}

			this.sendingPreview = true;

			let data: { [key: string]: any } = {
				"location_id": this.currentLocationId,
				"phone": this.to,
				"message": this.body,
				"user_id": this.user.id,
				"type": "sms"

			}

			try {
				let response = await this.$http.post('/message/preview', data);
			} catch (err) {
				console.error(err);
			}

			this.sendingPreview = false;
    }
	},
	computed: {
		...mapState('user', {
			user: (s: UserState) => {
				return s.user ? new User(s.user) : undefined;
			},
    }),
    getRandomId() {
      return 'send_sms_' + Math.random().toString(36).substring(7);
    }
  },
	async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id;
    const fields = await CustomField.getByLocationIdAndType(this.currentLocationId, 'contact');
    this.customFields = fields.map(field => {
      return { text: field.name, value: `{{${field.fieldKey}}}` }
    });
    this.menuItems = await FilterMergeTags.syncList(this.currentLocationId, this.customFields, this.menuItems)
    this.menuItems = await FilterMergeTags.resyncList(this.menuItems, this.customFields, this.triggerType)
    if (this.action.body) this.tinymcehtml = this.body = this.action.body.replace(/\n/g, '<br/>');
		if (this.action.to) this.to = this.action.to.toString();
	},
	watch: {
		tinymcehtml(value: string) {
      var div = document.createElement("div");
      value = value.replace(new RegExp('<br/>', 'g'), '\n');
      value = value.replace(new RegExp('<br />', 'g'), '\n');
			div.innerHTML = value;
			this.body = div.textContent || div.innerText || "";
      this.body = this.body.replaceAll(String.fromCharCode(160), ' ')
			this.setValue('body', this.body);
    },
    async triggerType() {
      this.menuItems = await FilterMergeTags.resyncList(this.menuItems, this.customFields, this.triggerType);
    },
    action() {
      if (this.action.body && this.body !== this.action.body) {
        const editor = tinymce.get(this.$refs.editor.id);
        editor.setContent(this.action.body.replace(/\n/g, '<br/>'));
      }
		  if (this.action.to) this.to = this.action.to.toString();
    }
	}
})
</script>

