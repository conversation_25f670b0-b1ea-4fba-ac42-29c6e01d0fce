<template>
  <div
    class="modal fade hl_sms-template--modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Copy Campaign</h2>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div class="form-group">
              <UITextInputGroup
                type="text"
                placeholder="New Campaign Name"
                label="New Campaign Name"
                v-model="campaignName"
              />
            </div>
            <div class="form-group">
              <UITextLabel>Location</UITextLabel>
              <vSelect
                multiple
                :options="locations"
                label="name"
                v-model="copyToLocations"
                :clearable="false"
                v-validate="'required'"
                name="location"
                data-vv-as="Location"
                :loading="loading.locations"
              >
                <template #spinner="{ loading }">
                  <div v-if="loading" style="border-left-color: rgba(88,151,251,0.71)" class="vs__spinner"></div>
                </template>
              </vSelect>
              <span v-show="errors.has('location')" class="error"
                >Location is required.</span
              >
            </div>
            <!-- <ul class="list-group">
							<li v-for="location in locations" :key="location.id" class="list-group-item">
								<div class="option">
									<input
										type="checkbox"
										:id="'copy-' + location.id"
										v-model="copyToLocations"
										:value="location.id"
									>
									<label :for="'copy-' + location.id">
										<strong>{{location.name}}</strong>
									</label>
								</div>
							</li>
						</ul>-->
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner nav">
            <UIButton type="button" use="outline" data-dismiss="modal">
              Cancel
            </UIButton>
            <div
              style="
                display: inline-block;
                position: relative;
                margin-left: 10px;
              "
            >
              <UIButton
                type="button"
                @click="copyCampaign()"
                :loading="saving"
              >
                Copy
              </UIButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import * as lodash from 'lodash'
import vSelect from 'vue-select'

import { Campaign, Location, User, Template, EmailBuilder } from '@/models'
import { UserState } from '@/store/state_models'
import config from '@/config'

declare var $: any

export default Vue.extend({
  props: ['campaign', 'showModal', 'currentLocationId'],
  components: {
    vSelect,
  },
  data() {
    return {
      locations: [] as { [key: string]: any }[],
      campaignName: '',
      copyToLocations: [] as { [key: string]: any }[],
      saving: false,
      loading: {
        locations: false
      }
    }
  },
  methods: {
    async toggleVisibility() {
      if (this.showModal) {
        $(this.$refs.modal).modal('show')
      } else {
        $(this.$refs.modal).modal('hide')
      }
    },
    async fetchData() {
      if (this.campaign) {
        this.campaignName = this.campaign.name + ' Copy'
      } else {
        this.campaignName = ''
      }
      this.copyToLocations = []
      this.locations = []

      const agencyType = this.user?.type === User.TYPE_AGENCY
      const admin = this.user?.role === User.ROLE_ADMIN

      this.loading.locations = true
      const locations = await this.$store.dispatch('locations/getAll')
      this.loading.locations = false

      if (agencyType || (admin && locations.length > 1)) {
        locations.forEach((location: Location) => {
          this.locations.push(location)
        })
      } else {
        let location = new Location(
          await this.$store.dispatch(
            'locations/getById',
            this.currentLocationId
          )
        )
        this.copyToLocations.push({ ...location.data, id: location.id })
      }
    },
    async copyCampaign() {
      let currentTemplate: { [key: string]: any } | undefined
      let template: { [key: string]: any }
      const result = await this.$validator.validateAll()
      if (!result) {
        return false
      }
      if (this.copyToLocations && this.copyToLocations.length !== 0) {
        this.saving = true
        const locationIds = this.copyToLocations.map(loc => loc.id)
        await this.$store.dispatch(
          'auth/refreshFirebaseToken',
          { locationId: locationIds, refresh: false },
          { root: true }
        )
      }
      for (let location of this.copyToLocations) {
        const newCampaign = new Campaign()
        newCampaign.campaignData = lodash.cloneDeep(this.campaign.campaignData) // Copies the complete campaign data
        this.$delete(newCampaign.campaignData, 'users')
        this.$delete(newCampaign.campaignData, 'no_response') // Removes the Users and Next Campaign, so that it can be copied to a different location
        if (this.currentLocationId !== location.id) {
          await Promise.all(
            newCampaign.campaignData.templates.map(async (tmplt: any) => {
              if (tmplt.attributes && tmplt.attributes.template_id) {
                try {
                  currentTemplate = await Template.getByOriginId(
                    location.id,
                    tmplt.attributes.template_id
                  )
                } catch (e) {
                  console.error(e)
                }
                try {
                  if (!currentTemplate)
                    currentTemplate = await EmailBuilder.getByOriginId(
                      location.id,
                      tmplt.attributes.template_id
                    )
                } catch (e) {
                  console.error(e)
                }
                if (!currentTemplate) {
                  try {
                    template = await Template.getById(
                      tmplt.attributes.template_id
                    )
                  } catch (e) {
                    console.error(e)
                  }
                  try {
                    if (!template)
                      template = await EmailBuilder.getById(
                        tmplt.attributes.template_id
                      )
                  } catch (e) {
                    console.error(e)
                  }
                  if (template && template.type) {
                    const newTemplate = new Template()
                    newTemplate.template = lodash.cloneDeep(template.template)
                    newTemplate.type = template.type
                    newTemplate.name = template.name
                    newTemplate.locationId = location.id
                    newTemplate.originId = template.id
                    await newTemplate.save()
                    tmplt.attributes.template_id = newTemplate.id
                  } else if (
                    template?.emailBuilderData ||
                    template?.builderVersion
                  ) {
                    let newTemplateId
                    if (
                      !template.builderVersion ||
                      template.builderVersion === '1'
                    ) {
                      // handle old email builer
                      const newEmailBuilder = new EmailBuilder()
                      newEmailBuilder.emailBuilderData = lodash.cloneDeep(
                        template.template
                      )
                      newEmailBuilder.name = template.name
                      newEmailBuilder.locationId = location.id
                      newEmailBuilder.originId = template.id
                      newEmailBuilder.downloadUrl = template.downloadUrl
                      newEmailBuilder.storageUrl = template.storageUrl
                      await newEmailBuilder.save()
                      newTemplateId = newEmailBuilder.id
                    } else {
                      // handle new emal builder
                      const params = {
                        locationId: this.currentLocationId,
                        templateId: template.id,
                        destination: location.id,
                        updatedBy: this.user?.id,
                      }
                      const { data } = await this.$http.post(
                        config.emailBuilderServiceUrl +
                          '/v1/emails/clone-template',
                        params
                      )
                      newTemplateId = data._id
                    }
                    tmplt.attributes.template_id = newTemplateId
                  }
                  return tmplt
                }
              }
            })
          )
        }
        newCampaign.locationId = location.id
        newCampaign.name = this.campaignName
        newCampaign.stopOnReply = this.campaign.stopOnReply
        newCampaign.allowMultiple = this.campaign.allowMultiple
        await newCampaign.save()
      }
      this.saving = false
      this.$emit('hidden')
    },
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  computed: {
    user(): User | undefined {
      const user: UserState = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
  },
  watch: {
    async showModal(value) {
      if(value) {
        await this.fetchData()
      }
      this.toggleVisibility()
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
    this.toggleVisibility()
  },
  mounted() {
    $(this.$refs.modal).on('hidden.bs.modal', () => {
      this.$emit('hidden')
    })

    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }

    this.toggleVisibility()
  },
})
</script>
<style>
.dropdown-toggle::after {
  content: none;
}
</style>
