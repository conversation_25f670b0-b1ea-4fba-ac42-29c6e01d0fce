<template>
	<div class="hl_settings--body">
		<div class="container-fluid">
			<div class="row">
				<div class="hl_settings--main col-lg-12">
					<div class="card">
						<div class="card-header">
							<h3>MailGun Settings</h3>
						</div>
						<div class="card-body">
							<div class="personal-logo">
								<div class="col-sm-3">
									<img
										src="/pmd/img/logo_medallions/mailgun-medallion.png"
										style="height:100px"
										alt="Avatar Name">
								</div>
								<div class="col-sm-auto">
								  	<CompanyMailGunView v-on:edit="enterCompanyEdit" :bus="bus"
										:fetchingData="loading"
										v-model="companyId"
										:companyId="companyId"
										:companyMailgun="companyMailGunAccount"/>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="container-fluid" v-if="locationMailGunAccounts.length > 0">
			<div class="hl_settings--controls">
				<div class="hl_settings--controls-left">
					<h2>
						MailGun Settings for Locations
						<span>{{ locationMailGunAccounts.length }} locations</span>
					</h2>
				</div>
				<div class="hl_settings--controls-right"></div>
			</div>
			<div class="card hl_settings--team-management">
				<div class="card-body --no-padding" style="max-width:100%;">
					<div class="table-wrap">
						<table class="table">
							<thead>
								<tr>
									<th style="width: 20%" data-sort="string">Name</th>
									<th style="width: 25%" data-sort="string">API Key</th>
									<th style="width: 20%" data-sort="string">Domain</th>
									<th data-sort="string">Email Validation</th>
								</tr>
							</thead>
							<tbody>
								<LocationMailGunApiItem v-on:edit="enterEdit" :bus="bus"
                                        v-for="locationMailGunAccount in locationMailGunAccounts"
                                        :key="locationMailGunAccount.id" :locationMailGunAccount="locationMailGunAccount.mailgunAccount"
                                        :location="locationMailGunAccount.location"
										:locationMailgun="getLocationMailgunAccount"
										showKeyDomain="true"
										:locationId="editLocationId"/>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
		<b-modal ref="bv-modal"  hide-footer>
			<template v-slot:modal-title>
				<div class="flex justify-items-center items-center">
					<div class="d-inline-block" >
						<img src="/pmd/img/logo_medallions/mailgun-medallion.png" style="height: 30px;"/>
					</div>
					<div class="px-1 font-semibold text-xl">MailGun</div>
					<div class="d-inline-block align-middle">
						<moon-loader  :loading="loadingEditor"  color="#188bf6" size="15px" />
					</div>
				</div>
			</template>
			<LocationMailGun
        v-model="editLocationId"
        v-on:saved="exitEdit"
				@deleted="connectionDeleted"
				:locationMailgun="getLocationMailgunAccount"
				v-if="editLocationId"
				:locationId="editLocationId"
        showKeyDomain="true"
				v-on:refreshing="loadingEditor = true"
        v-on:refreshed="loadingEditor = false"/>
			<CompanyMailGunEdit
        v-model="editCompanyId"
        v-on:saved="exitEdit"
				@deleted="connectionDeleted"
				:companyMailgun="companyMailGunAccount"
				v-if="editCompanyId"
				:companyId="editCompanyId"
        showKeyDomain="true"
				v-on:refreshing="loadingEditor = true"
        v-on:refreshed="loadingEditor = false"/>

		</b-modal>
 	</div>
</template>

<script lang='ts'>
import Vue from 'vue';
import { Location, MailGunAccount, Company } from '@/models';
//const MailGunListItem = () => import( '@/pmd/components/agency/MailGunListItem.vue');
import CompanyMailGunView from '@/pmd/components/agency/CompanyMailGunView.vue';
import LocationMailGunApiItem from '@/pmd/components/agency/LocationMailGunApiItem.vue';
const LocationMailGun = () => import( /* webpackChunkName: "location-mailgun-edit" */ '@/pmd/pages/settings/LocationMailGunListItem.vue');
const CompanyMailGunEdit = () => import( /* webpackChunkName: "agency-mailgun-edit" */ '@/pmd/components/agency/CompanyMailGunEdit.vue');
let unsubscribeLocations: () => void;

export default Vue.extend({
	components: {
  //  MailGunListItem,
    LocationMailGunApiItem,
    LocationMailGun,
    CompanyMailGunView,
    CompanyMailGunEdit
	},
	data() {
		return {
			editLocationId : '',
			editCompanyId:'',
			companyId:'',
			company: {} as Company,
			locations: [] as Location[],
			companyMailGunAccount:  {},
			locationMailGunAccounts: [],
			processing: false as boolean,
			edited: false,
			apiKey: '',
			bus: new Vue(),
			loadingEditor:false,
			loading: false,
		}
  },
  computed: {
    getLocationMailgunAccount() {
      if (this.editLocationId && this.getLocationIndex() !== -1) {
        return this.locationMailGunAccounts[this.getLocationIndex()].mailgunAccount;
      }
      return {};
    }
  },
	async created() {
		var _self = this;
    const authUser = await this.$store.dispatch('auth/get');
    this.companyId = authUser.companyId;

    this.loading = true;
    this.$http.get(`/mailgun/accounts/${this.companyId}`).then(res => {
      if (res && res.status === 200) {
        this.companyMailGunAccount = res.data.companyMailgun;
        this.locationMailGunAccounts = lodash.orderBy(res.data.locationMailguns, function(e) { return e.location.name_lower_case });
        this.loading = false;
      }
    }).catch(err => { this.loading = false; })

		// this.mailgunAccount = await MailGunAccount.getByCompanyId(this.company.id);
		// if (!this.mailgunAccount) {
		// 	this.mailgunAccount = new MailGunAccount();
		// 	this.mailgunAccount.companyId = this.company.id;
		// } else {
		// 	this.apiKey = this.mailgunAccount.apiKey;
		// }
	},
	beforeDestroy() {
		if (unsubscribeLocations) unsubscribeLocations();
	},
	methods: {
    getLocationIndex() {
      return lodash.findIndex(this.locationMailGunAccounts, { 'location': { 'id': this.editLocationId }});
    },
    enterEdit(event){
      this.editLocationId = event.locationId;
      this.editCompanyId = undefined;
      this.$refs['bv-modal'].show();
    },
    enterCompanyEdit(event){
      this.editCompanyId = event.companyId;
      this.editLocationId = undefined;
      this.$refs['bv-modal'].show();
    },
    exitEdit(event) {
      if (event.companyId) this.companyMailGunAccount = event.mailgunAccount;
      else if (event.locationId && this.getLocationIndex() !== -1) {
        this.locationMailGunAccounts[this.getLocationIndex()].mailgunAccount = event.mailgunAccount;
      }
      this.bus.$emit('refresh', event);
      this.$refs['bv-modal'].hide();
      this.editLocationId = undefined;
      this.editCompanyId = undefined;
	},
	connectionDeleted(event) {
		if(event.companyId) {
			this.companyMailGunAccount = {}
		} else if(event.locationId) {
			const index = this.locationMailGunAccounts.findIndex(obj => obj.location.id === event.locationId)
			const location = this.locationMailGunAccounts[index].location
			this.$set(this.locationMailGunAccounts, index, { location, mailgunAccount: {}})
		}
		this.bus.$emit('refresh', event);
		this.$refs['bv-modal'].hide();
		this.editLocationId = undefined;
		this.editCompanyId = undefined;
	}
		// async saveAccount() {
		// 	const result = await this.$validator.validateAll();
		// 	if (!result) {
		// 		return false;
		// 	}
		// 	this.processing = true;

		// 	this.mailgunAccount.apiKey = this.apiKey;
		// 	await this.mailgunAccount.save();
		// 	try {
		// 		let response = await this.$http.get('/mailgun/set_reply_webhook?company_id=' + this.company.id);
		// 	} catch (err) {
		// 		console.error("Error creating webhook:", err)
		// 	}
		// 	this.processing = false;
		// 	this.edited = false;
		// }
	}
});
</script>
