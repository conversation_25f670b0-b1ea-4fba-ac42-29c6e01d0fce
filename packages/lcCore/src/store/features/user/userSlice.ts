/**
 * User Slice
 * 
 * Redux slice for user-specific state management beyond authentication
 * Based on SPM Vuex user store patterns and functionality
 */

import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import type { IUser } from '../../../types/user';

/**
 * User state interface
 * Based on SPM UserState interface
 */
export interface IUserState {
  /** Current user data (may be different from auth user for admin viewing other users) */
  currentUser: IUser | null;
  
  /** Flag for password updated during onboarding */
  passwordUpdatedInOnboarding: boolean;
  
  /** Flag for internal user (support team member) */
  internalUser: boolean;
  
  /** Loading states */
  isLoading: boolean;
  isUpdating: boolean;
  
  /** Error state */
  error: string | null;
  
  /** User preferences */
  preferences: {
    theme?: 'light' | 'dark' | 'auto';
    language?: string;
    timezone?: string;
    notifications?: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
  };
  
  /** Session information */
  session: {
    lastActivity?: string;
    sessionTimeout?: number;
    isIdle?: boolean;
  };
  
  /** Real-time subscription status */
  subscriptions: {
    userUpdates: boolean;
    permissionChanges: boolean;
  };
}

/**
 * Initial state
 */
const initialState: IUserState = {
  currentUser: null,
  passwordUpdatedInOnboarding: false,
  internalUser: false,
  isLoading: false,
  isUpdating: false,
  error: null,
  preferences: {
    theme: 'auto',
    notifications: {
      email: true,
      push: true,
      sms: false,
    },
  },
  session: {
    isIdle: false,
  },
  subscriptions: {
    userUpdates: false,
    permissionChanges: false,
  },
};

/**
 * User slice
 */
export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    /**
     * Set current user
     * Based on SPM user store 'set' mutation
     */
    setCurrentUser: (state: IUserState, action: PayloadAction<IUser | null>) => {
      state.currentUser = action.payload;
      state.error = null;
    },

    /**
     * Update password updated in onboarding flag
     * Based on SPM user store 'updatePasswordUpdatedInOnboarding' mutation
     */
    setPasswordUpdatedInOnboarding: (state: IUserState, action: PayloadAction<boolean>) => {
      state.passwordUpdatedInOnboarding = action.payload;
    },

    /**
     * Set internal user flag
     * Based on SPM user store 'setInternalUser' mutation
     */
    setInternalUser: (state: IUserState, action: PayloadAction<boolean>) => {
      state.internalUser = action.payload;
    },

    /**
     * Set loading state
     */
    setLoading: (state: IUserState, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
      if (action.payload) {
        state.error = null;
      }
    },

    /**
     * Set updating state
     */
    setUpdating: (state: IUserState, action: PayloadAction<boolean>) => {
      state.isUpdating = action.payload;
      if (action.payload) {
        state.error = null;
      }
    },

    /**
     * Set error state
     */
    setError: (state: IUserState, action: PayloadAction<string | null>) => {
      state.error = action.payload;
      state.isLoading = false;
      state.isUpdating = false;
    },

    /**
     * Update user preferences
     */
    updatePreferences: (state: IUserState, action: PayloadAction<Partial<IUserState['preferences']>>) => {
      state.preferences = {
        ...state.preferences,
        ...action.payload,
      };
    },

    /**
     * Update notification preferences
     */
    updateNotificationPreferences: (
      state: IUserState,
      action: PayloadAction<Partial<{ email: boolean; push: boolean; sms: boolean }>>
    ) => {
      if (state.preferences.notifications) {
        state.preferences.notifications = {
          ...state.preferences.notifications,
          ...action.payload,
        };
      }
    },

    /**
     * Update session information
     */
    updateSession: (state: IUserState, action: PayloadAction<Partial<IUserState['session']>>) => {
      state.session = {
        ...state.session,
        ...action.payload,
      };
    },

    /**
     * Set idle state
     */
    setIdle: (state: IUserState, action: PayloadAction<boolean>) => {
      state.session.isIdle = action.payload;
      if (action.payload) {
        state.session.lastActivity = new Date().toISOString();
      }
    },

    /**
     * Update last activity
     */
    updateLastActivity: (state: IUserState) => {
      state.session.lastActivity = new Date().toISOString();
      state.session.isIdle = false;
    },

    /**
     * Set subscription status
     */
    setSubscriptionStatus: (
      state: IUserState,
      action: PayloadAction<{ type: keyof IUserState['subscriptions']; active: boolean }>
    ) => {
      state.subscriptions[action.payload.type] = action.payload.active;
    },

    /**
     * Clear user state
     * Based on SPM user store 'deleteUserState' action
     */
    clearUserState: (state: IUserState) => {
      state.currentUser = null;
      state.passwordUpdatedInOnboarding = false;
      state.internalUser = false;
      state.isLoading = false;
      state.isUpdating = false;
      state.error = null;
      state.session = {
        isIdle: false,
      };
      state.subscriptions = {
        userUpdates: false,
        permissionChanges: false,
      };
      // Keep preferences as they might be device-specific
    },

    /**
     * Reset error state
     */
    clearError: (state: IUserState) => {
      state.error = null;
    },

    /**
     * Update current user data
     * For optimistic updates
     */
    updateCurrentUser: (state: IUserState, action: PayloadAction<Partial<IUser>>) => {
      if (state.currentUser) {
        state.currentUser = {
          ...state.currentUser,
          ...action.payload,
        };
      }
    },

    /**
     * Set user timezone
     */
    setUserTimezone: (state: IUserState, action: PayloadAction<string>) => {
      if (state.currentUser) {
        state.currentUser.timezone = action.payload;
      }
      state.preferences.timezone = action.payload;
    },

    /**
     * Add location to user
     */
    addUserLocation: (
      state: IUserState,
      action: PayloadAction<{ locationId: string; notificationType: string }>
    ) => {
      if (state.currentUser) {
        state.currentUser.locations = {
          ...state.currentUser.locations,
          [action.payload.locationId]: action.payload.notificationType,
        };
      }
    },

    /**
     * Remove location from user
     */
    removeUserLocation: (state: IUserState, action: PayloadAction<string>) => {
      if (state.currentUser && state.currentUser.locations) {
        const newLocations = { ...state.currentUser.locations };
        delete newLocations[action.payload];
        state.currentUser.locations = newLocations;
      }
    },

    /**
     * Update user permissions
     */
    updateUserPermissions: (
      state: IUserState,
      action: PayloadAction<Partial<IUser['permissions']>>
    ) => {
      if (state.currentUser) {
        state.currentUser.permissions = {
          ...state.currentUser.permissions,
          ...action.payload,
        };
      }
    },
  },
});

/**
 * Export actions
 */
export const {
  setCurrentUser,
  setPasswordUpdatedInOnboarding,
  setInternalUser,
  setLoading,
  setUpdating,
  setError,
  updatePreferences,
  updateNotificationPreferences,
  updateSession,
  setIdle,
  updateLastActivity,
  setSubscriptionStatus,
  clearUserState,
  clearError,
  updateCurrentUser,
  setUserTimezone,
  addUserLocation,
  removeUserLocation,
  updateUserPermissions,
} = userSlice.actions;

/**
 * Export reducer
 */
export default userSlice.reducer;

/**
 * Export types
 */
export type UserState = IUserState;
