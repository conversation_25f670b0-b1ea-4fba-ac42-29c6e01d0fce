<template>
  <div class="hl_billing--services">
    <div class="card">
      <div class="card-header">
        <h3 style="margin-bottom:0px"> Eliza Service </h3>
      </div>
      <div class="card-body">
        <div class="no-active-service">
          <div v-if="company.eliza_first_location_enabled">
            <p>Number of free Eliza leads for your subscription: <b>{{ monthlyFreeLeads }}</b></p>
            <p>Remaining Free Monthly Leads: <b>{{ remainingMonthlyFreeLeads }}</b></p>
            <p v-if="remainingMonthlyFreeLeads === 0" class="free-credits-finished">You don't have any more free leads and will be charged from available credits</p>
          </div>

          <div v-if="!company.eliza_first_location_enabled">
            <p>You haven't subscribed to Eliza Service. Please go to Location Settings and enable Eliza Service for a
              Location</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';
import axios from 'axios';
import config from '@/config';
import {Company, Location} from '@/models';

export default Vue.extend({
  data() {
    return {
      company: Company,
      location: Location,
      canMakeCallsToDialogflow: false,
      remainingMonthlyFreeLeads: null,
      monthlyFreeLeads: null,
      perLeadPrice: ''
    };
  },
  async created() {
    await this.fetchCompanyDetails();
    await this.fetchCredits();
  },
  methods: {
    async fetchCompanyDetails() {
      let company = await this.$store.dispatch('company/get');
      this.company = company.data;
    },
    async fetchCredits() {
      const {data} = await this.dialogflowEngagements.get(`/credits/${this.company.id}/remaining-credits`);
      this.canMakeCallsToDialogflow = data.canMakeCallsToDialogflow;
      this.remainingMonthlyFreeLeads = data.remainingMonthlyFreeLeads;
      this.monthlyFreeLeads = data.monthlyFreeLeads;
      this.perLeadPrice = data.perLeadPrice;
    }
  },
});
</script>

<style>
.hl_billing--services .service-card .table .label.--warning {
  background-color: rgba(255, 188, 0, 0.9);
  color: #ffffff;
  border-left: 2px solid #ff870d;
  margin-left: 10px;
}

.hl_billing--services .service-card .table .label.--basic {
  background-color: #ff870d
}

.hl_billing--services .service-card .table .label.--plus {
  background-color: #6e22ff
}

#eliza-credits-purchase-button {
  position: relative;
  top: 15px;
  left: 15px;
}

.free-credits-finished {
  color: #ff0000;
}

</style>
