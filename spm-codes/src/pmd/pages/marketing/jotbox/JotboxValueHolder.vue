<template>
  <div v-if="value && value.meta" class="">
    <div v-if="value.meta.isSignature" class="">
      <img
        v-if="value && isImage"
        :src="value.url"
        class="img-thumbnail w-50 d-block"
        :class="{ signatureImg: value.meta.isSignature }"
        alt="image"
        @click.prevent="preview($event.target.src)"
      />
      <p class="date-text" v-if="value.meta.isSignature">
        Time Signed: <b>{{ signedDate(value.meta.timestamp) }}</b>
      </p>
    </div>
  </div>
  <div v-else-if="value && value.fileType">
    <FileDownloadFieldV2 :forSubmission="true" :files="value" />
  </div>
  <div v-else>
    {{ value }}
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import firebase from 'firebase/app'
import * as moment from 'moment-timezone'
import FileDownloadFieldV2 from '../../../components/util/FileDownloadFieldV2.vue'

export default Vue.extend({
  props: ['value'],
  computed: {
    isImage() {
      return this.value.meta.mimetype.startsWith('image')
    }
  },
  methods: {
    preview(src: string) {
      this.$store.commit('imagePreview/show', src)
    },
    signedDate(timestamp) {
      return moment.unix(timestamp).format('MMMM Do YYYY, h:mm:ss a')
    },
    humanFileSize(size) {
      var i = size == 0 ? 0 : Math.floor(Math.log(size) / Math.log(1024))
      return (
        (size / Math.pow(1024, i)).toFixed(2) * 1 +
        ' ' +
        ['B', 'kB', 'MB', 'GB', 'TB'][i]
      )
    }
  },
  components: {
    FileDownloadFieldV2
  }
})
</script>

<style scoped>
img {
  cursor: pointer;
}

.date-text {
  font-size: 12px;
}

.thumbnail {
  display: block;
  width: 6rem;
}

.filename {
  min-width: 40%;
  max-width: 40%;
}

.bi-file-earmark-text {
  color: #969494;
}

.signatureImg {
  pointer-events: none;
}
</style>
