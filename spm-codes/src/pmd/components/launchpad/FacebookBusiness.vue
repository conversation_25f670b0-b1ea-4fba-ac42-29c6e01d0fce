<template>
  <div
    id="fb-connect"
    class="launchpad-item-sub-container launchpad-bottom-border bg-white my-1"
  >
    <div class="items-tiles mx-3 my-2">
         <img
        class="integration-icon mx-4"
         :class="{ hl_disable_action: isFbConnected }"
        src="https://static.msgsndr.com/img/icon-facebook.0fedcae1.svg"
      />
      <div class="item-info">
        <div
          class="font-normal text-sm text-gray-500"
          v-if="reconnect && !slideShow"
        >
          Your Facebook connection seems to have an issue, please reconnect to
          engage with prospects via Messenger in Conversations and sync Facebook
          leads with our CRM.
        </div>
        <div
          class="font-normal text-sm text-gray-500"
          v-else-if="isFbConnected"
        >
          Awesome! Leads that you generate from Facebook Form Ads can now
          populate into your CRM and you can now respond directly to Facebook
          Messenger requests on the conversations page.
        </div>
        <div class="font-normal text-sm text-gray-500" v-else-if="!fbAuth">
          Connect directly with prospects and customers via Messenger in
          Conversation and sync your Facebook leads with our CRM.
        </div>
        <div class="font-normal text-sm text-gray-500" v-else-if="fbAuth">
          Complete connecting Facebook Business Page.
        </div>
      </div>
      <div class="flex flex-col justify-center item-end content-end w-40">
        <div
          class="flex flex-row justify-end item-end content-end w-full pr-4 pl-4"
        >
          <button
            id="launchpad-fb-connect"
            class="green-btn"
            :class="{
              'green-filled-button green-btn': !isFbConnected && !slideShow,
              'btn btn-circle ligh-green-btn': slideShow,
              'btn btn-circle success-green-btn': isFbConnected,
            }"
            @click="perfromAction"
            :style="disableButton"
          >
            <span
              v-show="
                !slideShow &&
                (!isFbConnected ||
                  (facebookConnection && location.fbTokenExpired))
              "
              >{{ btnText }}</span
            >
            <i class="fa fa-chevron-down" v-show="slideShow"></i>
            <i v-show="isFbConnected" class="fa fa-check"></i>
          </button>
        </div>
      </div>
    </div>
    <transition name="slide">
      <div v-if="slideShow" class="gmb-mailer-grp mx-5 my-2">
        <div class="form-group gmb-mailer-from-group">
          <div class="form-group dropdown">
            <label class="gmb-input-label"
              >Which business do you want to link to this location?</label
            >
            <select
              class="gmb-input selectpicker googleBusinessAccount"
              v-model="selectedPage"
              name="selectedPage"
              data-size="5"
            >
              <option value>Select Page</option>
              <option
                v-for="page in facebookPages"
                v-bind:key="page.id"
                :value="page"
                :selected="page.id == selectedId"
              >
                {{ page.name }} ({{ page.id }})
              </option>
            </select>
            <span v-show="errors.has('selectedPage')" class="error">{{
              errors.first('selectedPage')
            }}</span>
          </div>
        </div>
        <div class="gmb-button-grp" @click.stop="">
          <button
            id="launchpad-fb-link"
            class="green-filled-button green-btn"
            @click="linkBusinessPage"
            :class="{ invisible: processing }"
          >
            Done
          </button>
          <moon-loader
            class="loader-postion"
            :loading="processing"
            color="#37ca37"
            size="30px"
          />
        </div>
      </div>
    </transition>
    <facebook-business-modal
      :userid="userid"
      :name="name"
      :modalShow="showModal"
    />
    <AlertTriangleIcon
      v-if="reconnect"
      width="30px"
      height="30px"
      style="position: absolute; right: -44px"
      src="https://static.msgsndr.com/img/alert-triangle.0e06f0da.svg"
    />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { Location, OAuth2 } from '@/models'
import config from '@/config'
import FacebookBusinessModal from './FacebookBusinessModal.vue'
import FacebookIcon from '@/assets/pmd/img/launchpad/icon-facebook.svg'
import AlertTriangleIcon from '@/assets/pmd/img/launchpad/alert-triangle.svg'
import { trackGaEvent, trackPendoEvent } from '@/util/helper'

declare var $: any
let openedWindow: Window | null

// Create IE + others compatible event handler
const eventMethod: string = window.addEventListener
  ? 'addEventListener'
  : 'attachEvent'
const win: any = window
const eventer = win[eventMethod]
const messageEvent = eventMethod == 'attachEvent' ? 'onmessage' : 'message'

// Listen to message from child window
eventer(
  messageEvent,
  function (e) {
    if (e.data && e.data.page === 'integrations_settings') {
      if (e.data.actionType === 'close' && openedWindow) {
        openedWindow.close()
      }
    }
  },
  false
)

export default Vue.extend({
  props: ['currentLocationId', 'userid', 'name'],
  components: {
    FacebookBusinessModal,
    FacebookIcon,
    AlertTriangleIcon,
  },
  data() {
    return {
      location: {} as Location,
      slideShow: false,
      fbAuth: false,
      showModal: false,
      facebookPages: [],
      selectedPage: undefined as { [key: string]: any } | undefined,
      processing: false,
      selectedId: '',
      connectLoader: false,
      disableHandler: {
        action: 'facebook',
        disable: false,
      },
      facebookConnection: undefined as OAuth2 | undefined,
    }
  },
  computed: {
    isFbConnected(): boolean | undefined {
      return this.location.facebookPageId ? true : false
    },
    btnText(): string {
      let btnTxt = 'Connect'
      if (this.reconnect) {
        btnTxt = 'Reconnect'
      }
      return this.connectLoader ? 'Connecting...' : btnTxt
    },
    reconnect(): boolean | undefined {
      return (
        (this.location.isFbDisconnected && !this.isFbConnected) ||
        (this.facebookConnection && this.location.fbTokenExpired)
      )
    },
    disableButton(): string {
      return this.slideShow ? 'pointer-events:none !important' : ''
    },
  },
  watch: {
    '$route.params.location_id': async function (id) {
      this.location = new Location(
        await this.$store.dispatch('locations/getCurrentLocation', id)
      )
      if (!this.isFbConnected) await this.fetchData()
      if (this.facebookPages.length > 0 && !this.location.facebookPageId) {
        this.fbAuth = true
      }
    },
  },
  async mounted() {
    this.location = new Location(
      await this.$store.dispatch(
        'locations/getCurrentLocation',
        this.currentLocationId
      )
    )
    this.$root.$on('fb-connect', () => {
      this.showModal = false
      this.connectFacebook()
    })
    this.$root.$on('close-modal', () => {
      this.showModal = false
    })
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
    if (!this.isFbConnected) await this.fetchData()
    if (this.facebookPages.length > 0 && !this.location.facebookPageId) {
      this.fbAuth = true
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
      if (
        this.company &&
        this.company.country == 'US' &&
        this.$refs.stateSelect
      ) {
        $(this.$refs.stateSelect).selectpicker('val', this.company.state)
      }
    }
  },
  methods: {
    connectFacebook() {
      trackGaEvent(
        'Launchpad',
        'Initiate Facebook connect',
        `Init Facebook connect for location: ${this.currentLocationId}`
      )
      this.disableHandler.disable = true
      this.$root.$emit(
        'disable-launchpad-action',
        JSON.stringify(this.disableHandler)
      )
      this.connectLoader = true
      openedWindow = window.open(
        `${config.baseUrl}/api/facebook/start_oauth?location_id=${this.currentLocationId}&user_id=${this.userid}&launchpad=true`,
        'MyWindow',
        'toolbar=no, menubar=no,scrollbars=no,resizable=no,location=no,directories=no,status=no'
      )
      const timer = setInterval(async () => {
        if (openedWindow.closed) {
          clearInterval(timer)
          try {
            await this.fetchData()
          } catch (err) {}
          this.connectLoader = false
          if (this.facebookPages.length > 1 && !this.isFbConnected) {
            this.slideShow = true
            this.fbAuth = true
          } else if (!this.facebookPages.length) {
            this.disableHandler.disable = false
            this.$root.$emit(
              'disable-launchpad-action',
              JSON.stringify(this.disableHandler)
            )
          }
        }
      }, 500)
    },
    async fetchData() {
      try {
        this.facebookConnection = await OAuth2.getByLocationIdAndTypeOnce(
          this.currentLocationId,
          OAuth2.TYPE_FACEBOOK
        )
        if (this.facebookConnection) {
          let response = await this.$http.get('/api/facebook/get_pages', {
            params: {
              access_token: this.facebookConnection.accessToken,
            },
          })

          this.facebookPages = response.data
          if (this.facebookPages.length == 1 && !this.location.facebookPageId) {
            this.selectedPage = this.facebookPages[0]
            this.selectedId = this.selectedPage.id
            this.disableHandler.disable = false
            this.$root.$emit(
              'disable-launchpad-action',
              JSON.stringify(this.disableHandler)
            )
            this.linkBusinessPage()
          }
        }
      } catch (err) {
        console.log(err)
      }
    },
    connectPage() {
      this.disableHandler.disable = true
      this.$root.$emit(
        'disable-launchpad-action',
        JSON.stringify(this.disableHandler)
      )
      this.slideShow = !this.slideShow
    },
    async linkBusinessPage() {
      if (!this.currentLocationId || !this.selectedPage) return

      try {
        if (this.processing) return
        this.processing = true
        await this.$http.post('/facebook/linkPage', {
          location_id: this.currentLocationId,
          page_id: this.selectedPage.id,
          page_token: this.selectedPage.access_token,
          page_name: this.selectedPage.name,
          page_link: this.selectedPage.link,
          instagram_page: this.selectedPage.instagram_business_account,
          launchpad: true,
          userId: this.userid,
        })
      } catch (err) {
        console.log(err)
      }
      await this.location.ref.update({
        is_fb_disconnected: false,
      })
      this.location = new Location(
        await this.$store.dispatch(
          'locations/getCurrentLocation',
          this.currentLocationId
        )
      )
      this.processing = false
      this.disableHandler.disable = false
      this.$root.$emit(
        'disable-launchpad-action',
        JSON.stringify(this.disableHandler)
      )
      this.slideShow = false
      trackGaEvent(
        'Launchpad',
        'Successfully Facebook connected',
        `Successfully Facebook connected for location: ${this.currentLocationId}`
      )
      this.$root.$emit('launchpad-status')
    },
    perfromAction() {
      if (this.isFbConnected) {
        return false
      } else if (this.fbAuth) {
        this.connectPage()
      } else if (!this.fbAuth) {
        this.showModal = !this.showModal
      }
    },
  },
})
</script>
