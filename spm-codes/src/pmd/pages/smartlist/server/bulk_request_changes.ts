import { User, Location } from '@/models'
import store from '@/store'
import e from 'express'
import { pointerMixin } from 'vue-multiselect'
import { BulkRequestV2 } from './bulk_req_v2'

export enum BulkRequestChangeType {
  ADDED = 'added',
  MODIFIED = 'modified',
  REMOVED = 'removed',
}

export class BulkRequestV2Subscriber {
  public constructor (
    public locationId: string,
    public userId: string,
    protected callback: () => {}
  ) {
  }

  private locUpdateUnsubscribe = []
  public async resumeListen(timeout: number = 2000 ) {
    try {
      await this.suspendListen()
      if (!this.callback) return
      setTimeout(() => {
        this.listen()
      }, timeout);
    } catch(err) {
      console.log(err)
    }
  }

  public async suspendListen() {
    try {
      for (let unsub of this.locUpdateUnsubscribe) {
        console.log('unbindLocation')
        await unsub()
      }
      this.locUpdateUnsubscribe = []
    } catch(err) {
      console.log(err)
    }
  }

  public terminate() {
    this.suspendListen()
    this.callback = null
  }
  private waitToCallBack: boolean
  private notifications = [];
  private async notify(){
    if (this.notifications.length) {
      this.notifications.pop()
      setTimeout(() => this.notify(), 2000);
    }
    if (!this.notifications.length || this.notifications.length % 20 === 0) {
      setTimeout(async () => {
        await this.processCallback();
        setTimeout(() => this.processCallback(), 8000);
      }, 2000);
    }
  }

  private async processCallback(){
    if (this.waitToCallBack) return
    this.waitToCallBack = true;
    await this.callback()
    this.waitToCallBack = false;
  }

  private async listen() {
    try {
    const currentUser = new User(await store.dispatch('user/get'))
    const currentLocation = await new Location(
      store.getters['locations/getCurrentLocation']
    )
    if (!currentUser || !currentLocation) return
    const onSnapshotHandler = querySnapshot => {
      // do not use async
      if (querySnapshot.metadata.hasPendingWrites) return
      //console.log('global')
      const items: BulkRequestV2[] = [];
      querySnapshot.docChanges().forEach(change => {
        if (change.type === 'added') return
        else items.push(new BulkRequestV2(change.doc))
      })
      const item = items.find(bulkRequestV2=> {
        if (!bulkRequestV2) return false
        if (this.userId && bulkRequestV2.byUserId !== this.userId) return false
        if (this.locationId && bulkRequestV2.locationId !== this.locationId) return false
        if (bulkRequestV2.operationType === 'bulk-tag-add'
            || bulkRequestV2.operationType === 'bulk-tag-remove'
            || bulkRequestV2.operationType === 'bulk-contact-delete'
            || bulkRequestV2.operationType === 'bulk-import') return true
        return false
      })
      if (item) {
        this.notifications.push(item)
        this.notify()
      }
    }

      this.locUpdateUnsubscribe.push(
        // We only have 2 indexes on this collection
        // location_id alt_ids date_added  desc / asc
        BulkRequestV2.collectionRef()
          .where('location_id', '==', currentLocation.id)
          .where('alt_ids', 'array-contains', 'filter_version_1')
          .orderBy('date_added', 'desc')
          .limit(10)
          .onSnapshot(onSnapshotHandler)
      )
    } catch(err) {
      console.log(err)
    }
  }
}
