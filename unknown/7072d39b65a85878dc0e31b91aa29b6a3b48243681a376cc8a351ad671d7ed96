<template>
  <div
    class="modal fade hl_sms-template--modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Copy Form</h2>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div class="form-group">
              <UITextInputGroup
                type="text"
                placeholder="New Form Name"
                label="New Form Name"
                v-model="formName"
              />
            </div>
            <div class="form-group">
              <UITextLabel>Location</UITextLabel>
              <vSelect
                multiple
                :options="locations"
                label="name"
                v-model="copyToLocations"
                :clearable="false"
                v-validate="'required'"
                name="location"
                data-vv-as="Location"
                :loading="loading.locations"
              >
                <template #spinner="{ loading }">
                  <div v-if="loading" style="border-left-color: rgba(88,151,251,0.71)" class="vs__spinner"></div>
                </template>
              </vSelect>
              <span v-show="errors.has('location')" class="error"
                >Location is required.</span
              >
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner nav">
            <UIButton type="button" use="outline" data-dismiss="modal">
              Cancel
            </UIButton>
            <div
              style="display: inline-block;position: relative;margin-left: 10px;"
            >
              <UIButton
                type="button"
                use="primary"
                :loading="saving"
                @click="copyForm()"
              >
                Copy
              </UIButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import * as lodash from 'lodash'
import vSelect from 'vue-select'

import {
  Formsurvey,
  BulkRequest,
  Location,
  User,
  Company,
  Formbuilder,
  CustomField
} from '@/models'
import { UserState, CompanyState } from '@/store/state_models'
import { CreateOpportunity } from '@/models/trigger'
import { MessageTemplate } from '@/models/campaign'

const MoonLoader = () => import('../MoonLoader.vue')

declare var $: any

export default Vue.extend({
  props: ['values'],
  components: {
    vSelect,
    MoonLoader
  },
  data() {
    return {
      locations: [] as { [key: string]: any }[],
      copyToLocations: [] as { [key: string]: any }[],
      formName: '',
      saving: false,
      loading: {
        locations: false
      }
    }
  },
  methods: {
    async toggleVisibility() {
      if (this.values.visible) {
        $(this.$refs.modal).modal('show')
      } else {
        $(this.$refs.modal).modal('hide')
      }
    },
    async fetchData() {
      if (this.values.form) {
        if (this.values.form.name)
          this.formName = this.values.form.name + ' copy'
        else if (this.values.count) this.formName = 'Form ' + this.values.count
      } else {
        this.formName = ''
      }
      this.copyToLocations = []
      this.locations = []

      const agencyType = this.user.type === User.TYPE_AGENCY
      const admin = this.user.role === User.ROLE_ADMIN

      this.loading.locations = true
      const locations = await this.$store.dispatch('locations/getAll')
      this.loading.locations = false

      if (agencyType || admin && locations.length > 1) {
		    locations.forEach(location => {
          this.locations.push(location);
				});
			} else {
				let location = new Location(await this.$store.dispatch('locations/getById', this.values.currentLocationId));
				this.copyToLocations.push({ ...location.data, id: location.id });
      }
    },
    camelize(str: string) {
      return str.replace(/\W+(.)/g, function(match, chr) {
        return chr.toUpperCase()
      })
    },
    async copyForm() {
      this.saving = true
      const result = await this.$validator.validateAll()
      if (!result) {
        return false
      }
      if (this.copyToLocations && this.copyToLocations.length !== 0) {
        const locationIds = this.copyToLocations.map(loc => loc.id);
        await this.$store.dispatch('auth/refreshFirebaseToken', { locationId: locationIds, refresh: false } , {root: true});
      }
      for (let location of this.copyToLocations) {
        const formbuilder = new Formbuilder()
        let customFields: CustomField[] = (
          await CustomField.getByLocationId(location.id).get()
        ).docs.map(d => new CustomField(d))
        let formData = lodash.cloneDeep(
          lodash.get(this.values.form, 'formData', {})
        )
        let formFields = lodash.get(formData, 'form.fields', [])
        const newFields = formFields.map(formField => {
          if (
            !lodash.has(formField, 'id') ||
            location.id === this.values.form.locationId
          )
            return formField
          let customField = lodash.find(customFields, {
            name: formField.fieldName,
            dataType: formField.dataType,
            deleted: false
          })
          if (!customField) {
            customField = new CustomField()
            customField.locationId = location.id
            if (formField.name) customField.name = formField.name
            if (formField.dataType) customField.dataType = formField.dataType
            if (formField.placeholder)
              customField.placeholder = formField.placeholder
            if (formField.fieldKey) customField.fieldKey = formField.fieldKey
            if (formField.picklistOptions)
              customField.picklistOptions = formField.picklistOptions
            if (formField.availableOn)
              customField.availableOn = formField.availableOn
            if (formField.allowCustomOption)
              customField.allowCustomOption = formField.allowCustomOption
            if (formField.model) customField.model = formField.model
            if (formField.picklistOptionsImage)
              customField.picklistOptionsImage = formField.picklistOptionsImage
            customField.position = 0
            customField.save()
          }
          var createDOM: any = {
            id: customField.id,
            name: customField.name,
            type: customField.dataType.toLowerCase(),
            required: false,
            label: formField.label,
            placeholder: customField.placeholder
              ? customField.placeholder
              : customField.name,
            tag: customField.id
          }

          //Adding more fields on the basis of field type -> Accept camelcase only
          if (customField.data) {
            for (var key in customField.data) {
              var camelkey = this.camelize(key.replace(/_/g, ' '))
              createDOM[camelkey] = customField.data[key]
            }
          }
          return createDOM
        })
        if (!lodash.isEmpty(newFields)) formData.form.fields = newFields
        formbuilder.formData = formData
        formbuilder.locationId = location.id
        formbuilder.name = this.formName
        await formbuilder.save()
      }
      this.$emit('updated')
      this.saving = false
      this.$emit('hidden')
    }
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      }
    })
  },
  watch: {
    async values() {
      this.toggleVisibility()
      if (this.values.visible) this.fetchData()
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
    this.toggleVisibility()
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hidden')
    })

    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }

    this.toggleVisibility()
  }
})
</script>
<style>
.dropdown-toggle::after {
  content: none;
}
</style>
