/**
 * SubAccount Thunks
 * 
 * Async thunks for complex SubAccount operations and business logic
 * Based on SPM Location store actions and lcCore User thunk patterns
 */

import { createAsyncThunk } from '@reduxjs/toolkit';
import type { RootState } from '../../index';
import type { 
  ISubAccount, 
  ICreateSubAccountPayload, 
  IUpdateSubAccountPayload,
  SubAccountStatus
} from '../../../types/subAccount';
import { subAccountSlice } from './subAccountSlice';
import { SubAccountValidationUtils, SubAccountBusinessUtils, EntityUtils } from '../../../utils';

// Extract action creators from the slice
const {
  setSubAccounts,
  addOrUpdateSubAccount,
  removeSubAccount,
  setCurrentSubAccount,
  setActiveSubAccounts,
  setLoading,
  setUpdating,
  setCreating,
  setDeleting,
  setError,
  updateCurrentSubAccount,
  clearSubAccountState,
} = subAccountSlice.actions;

/**
 * Get all sub-accounts for a company
 * Based on SPM location store 'sync' action patterns
 */
export const getAllSubAccounts = createAsyncThunk<
  ISubAccount[],
  string,
  { state: RootState }
>(
  'subAccount/getAllSubAccounts',
  async (companyId, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      
      // In a real implementation, this would call the API
      // For now, we'll simulate the API call
      const subAccounts: ISubAccount[] = [];
      
      dispatch(setSubAccounts(subAccounts));
      return subAccounts;
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to get sub-accounts';
      dispatch(setError(message));
      return rejectWithValue(message);
    } finally {
      dispatch(setLoading(false));
    }
  }
);

/**
 * Get active sub-accounts only
 * Based on SPM Location.fetchAllLocations patterns
 */
export const getActiveSubAccounts = createAsyncThunk<
  ISubAccount[],
  string,
  { state: RootState }
>(
  'subAccount/getActiveSubAccounts',
  async (companyId, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      
      // In a real implementation, this would call the API
      // For now, we'll simulate the API call
      const activeSubAccounts: ISubAccount[] = [];
      
      dispatch(setActiveSubAccounts(activeSubAccounts));
      return activeSubAccounts;
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to get active sub-accounts';
      dispatch(setError(message));
      return rejectWithValue(message);
    } finally {
      dispatch(setLoading(false));
    }
  }
);

/**
 * Create a new sub-account
 * Based on SPM Location model creation patterns
 */
export const createSubAccount = createAsyncThunk<
  ISubAccount,
  ICreateSubAccountPayload,
  { state: RootState }
>(
  'subAccount/createSubAccount',
  async (subAccountData, { getState, dispatch, rejectWithValue }) => {
    try {
      dispatch(setCreating(true));
      
      // Validate sub-account data
      const validation = SubAccountValidationUtils.validateCreateSubAccount(subAccountData);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      const state = getState();
      const currentUserId = state.auth.authData?.userId;
      
      // Prepare sub-account data for creation
      const preparedData = SubAccountBusinessUtils.prepareForCreation(subAccountData);
      const entityData = EntityUtils.prepareForCreate(preparedData, currentUserId);
      
      // In a real implementation, this would call the API
      // For now, we'll simulate the creation
      const newSubAccount: ISubAccount = {
        id: `sa_${Date.now()}`,
        ...entityData,
        nameLowerCase: subAccountData.name.toLowerCase(),
        apiKey: SubAccountBusinessUtils.generateApiKey(),
      } as ISubAccount;

      dispatch(addOrUpdateSubAccount(newSubAccount));
      return newSubAccount;
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to create sub-account';
      dispatch(setError(message));
      return rejectWithValue(message);
    } finally {
      dispatch(setCreating(false));
    }
  }
);

/**
 * Update sub-account data
 * Based on SPM Location.save method patterns
 */
export const updateSubAccount = createAsyncThunk<
  ISubAccount,
  { subAccountId: string } & IUpdateSubAccountPayload,
  { state: RootState }
>(
  'subAccount/updateSubAccount',
  async ({ subAccountId, ...updateData }, { getState, dispatch, rejectWithValue }) => {
    try {
      dispatch(setUpdating(true));
      
      // Validate update data
      const validation = SubAccountValidationUtils.validateUpdateSubAccount(updateData);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }

      const state = getState();
      const currentUserId = state.auth.authData?.userId;
      const currentSubAccount = state.subAccount.subAccounts.find(sa => sa.id === subAccountId);
      
      if (!currentSubAccount) {
        throw new Error('Sub-account not found');
      }
      
      // Prepare update data
      const preparedData = SubAccountBusinessUtils.prepareForUpdate(updateData);
      const entityData = EntityUtils.prepareForUpdate(preparedData, currentUserId);

      // Optimistic update
      dispatch(updateCurrentSubAccount(entityData as Partial<ISubAccount>));

      // In a real implementation, this would call the API
      // For now, we'll simulate the update
      const updatedSubAccount: ISubAccount = {
        ...currentSubAccount,
        ...entityData,
      } as ISubAccount;

      dispatch(addOrUpdateSubAccount(updatedSubAccount));
      return updatedSubAccount;
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to update sub-account';
      dispatch(setError(message));
      return rejectWithValue(message);
    } finally {
      dispatch(setUpdating(false));
    }
  }
);

/**
 * Delete sub-account (soft delete)
 * Based on SPM Location.delete method patterns
 */
export const deleteSubAccount = createAsyncThunk<
  void,
  string,
  { state: RootState }
>(
  'subAccount/deleteSubAccount',
  async (subAccountId, { getState, dispatch, rejectWithValue }) => {
    try {
      dispatch(setDeleting(true));
      
      const state = getState();
      const subAccount = state.subAccount.subAccounts.find(sa => sa.id === subAccountId);
      
      if (!subAccount) {
        throw new Error('Sub-account not found');
      }

      // Check if sub-account can be deleted
      const canDelete = SubAccountBusinessUtils.canDelete(subAccount);
      if (!canDelete.canDelete) {
        throw new Error(canDelete.reason || 'Cannot delete sub-account');
      }

      const currentUserId = state.auth.authData?.userId;
      
      // Prepare delete data (soft delete)
      const deleteData = EntityUtils.prepareForDelete(currentUserId);
      
      // In a real implementation, this would call the API
      // For now, we'll simulate the deletion
      const updatedSubAccount: ISubAccount = {
        ...subAccount,
        ...deleteData,
        deleted: true,
      };

      dispatch(addOrUpdateSubAccount(updatedSubAccount));
      
      // If deleting current sub-account, clear it
      if (state.subAccount.currentSubAccount?.id === subAccountId) {
        dispatch(setCurrentSubAccount(null));
      }
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to delete sub-account';
      dispatch(setError(message));
      return rejectWithValue(message);
    } finally {
      dispatch(setDeleting(false));
    }
  }
);

/**
 * Update sub-account status
 * Based on SPM Location status management patterns
 */
export const updateSubAccountStatus = createAsyncThunk<
  ISubAccount,
  { subAccountId: string; status: SubAccountStatus },
  { state: RootState }
>(
  'subAccount/updateSubAccountStatus',
  async ({ subAccountId, status }, { getState, dispatch, rejectWithValue }) => {
    try {
      dispatch(setUpdating(true));
      
      const state = getState();
      const currentSubAccount = state.subAccount.subAccounts.find(sa => sa.id === subAccountId);
      
      if (!currentSubAccount) {
        throw new Error('Sub-account not found');
      }

      const currentUserId = state.auth.authData?.userId;
      const updateData = EntityUtils.prepareForUpdate({ status }, currentUserId);

      // Optimistic update
      dispatch(updateCurrentSubAccount({ status }));
      
      // In a real implementation, this would call the API
      // For now, we'll simulate the update
      const updatedSubAccount: ISubAccount = {
        ...currentSubAccount,
        ...updateData,
        status,
      };

      dispatch(addOrUpdateSubAccount(updatedSubAccount));
      return updatedSubAccount;
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to update sub-account status';
      dispatch(setError(message));
      return rejectWithValue(message);
    } finally {
      dispatch(setUpdating(false));
    }
  }
);

/**
 * Set current sub-account with real-time updates
 * Based on SPM location store 'resetCurrentLocation' action
 */
export const setCurrentSubAccountWithRealtime = createAsyncThunk<
  void,
  { subAccountId: string; forceRefresh?: boolean },
  { state: RootState }
>(
  'subAccount/setCurrentSubAccountWithRealtime',
  async ({ subAccountId, forceRefresh = false }, { getState, dispatch }) => {
    try {
      const state = getState();
      let subAccount = state.subAccount.subAccounts.find(sa => sa.id === subAccountId);
      
      if (!subAccount || forceRefresh) {
        // In a real implementation, this would fetch from API
        // For now, we'll use the existing data
        if (!subAccount) {
          throw new Error('Sub-account not found');
        }
      }

      dispatch(setCurrentSubAccount(subAccount));
      
      // In a real implementation, this would set up real-time listeners
      // For now, we'll just log the action
      console.log(`Current sub-account set to ${subAccount.name} (${subAccount.id})`);
      
    } catch (error) {
      console.error('Failed to set current sub-account:', error);
      dispatch(setError(error instanceof Error ? error.message : 'Failed to set current sub-account'));
    }
  }
);

/**
 * Search sub-accounts by name
 * Based on SPM location store search patterns
 */
export const searchSubAccounts = createAsyncThunk<
  ISubAccount[],
  { companyId: string; query: string },
  { state: RootState }
>(
  'subAccount/searchSubAccounts',
  async ({ companyId, query }, { dispatch, rejectWithValue }) => {
    try {
      dispatch(setLoading(true));
      
      // In a real implementation, this would call the API
      // For now, we'll simulate the search
      const searchResults: ISubAccount[] = [];
      
      return searchResults;
      
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to search sub-accounts';
      dispatch(setError(message));
      return rejectWithValue(message);
    } finally {
      dispatch(setLoading(false));
    }
  }
);

/**
 * Sync sub-account data with real-time updates
 * Based on SPM location store real-time listener patterns
 */
export const syncSubAccountData = createAsyncThunk<
  void,
  ISubAccount,
  { state: RootState }
>(
  'subAccount/syncSubAccountData',
  async (subAccountData, { dispatch }) => {
    try {
      // Update sub-account in store
      dispatch(addOrUpdateSubAccount(subAccountData));
      
      // If this is the current sub-account, update it
      // This will be handled by the selector automatically
      
    } catch (error) {
      console.error('Failed to sync sub-account data:', error);
    }
  }
);

/**
 * Initialize sub-account session
 * Based on SPM location store initialization patterns
 */
export const initializeSubAccountSession = createAsyncThunk<
  void,
  string,
  { state: RootState }
>(
  'subAccount/initializeSubAccountSession',
  async (companyId, { dispatch }) => {
    try {
      // Get all sub-accounts for the company
      await dispatch(getAllSubAccounts(companyId));
      
      // Get active sub-accounts
      await dispatch(getActiveSubAccounts(companyId));
      
    } catch (error) {
      console.error('Failed to initialize sub-account session:', error);
    }
  }
);

/**
 * Clear sub-account session
 * Based on SPM location store cleanup patterns
 */
export const clearSubAccountSession = createAsyncThunk<
  void,
  void,
  { state: RootState }
>(
  'subAccount/clearSubAccountSession',
  async (_, { dispatch }) => {
    try {
      dispatch(clearSubAccountState());
    } catch (error) {
      console.error('Failed to clear sub-account session:', error);
    }
  }
);
