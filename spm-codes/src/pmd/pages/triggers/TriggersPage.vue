<template>
  <section class="hl_wrapper">
    <section class="hl_wrapper--inner triggers" id="triggers">
      <div class="container-fluid">
        <div class="hl_controls">
          <div class="hl_controls--left flex">
            <h3>Triggers</h3>
            <div class="triggers-results">
              <p>Showing</p>
              <select
                class="selectpicker"
                data-width="fit"
                v-model="type"
                @change="addToUrlQuery('type')"
              >
                <option value="all">All</option>
                <option value="draft">Draft</option>
                <option value="active">Active</option>
                <!-- <option>Paused</option> -->
              </select>
              <p>rules based on</p>
              <select
                class="selectpicker"
                data-width="fit"
                v-model="conditionFilter"
                @change="addToUrlQuery('conditionFilter')"
              >
                <option :value="null">Any Condition</option>
                <option
                  v-for="(condition, conditionIndex) in avaliableConditions"
                  :key="conditionIndex"
                  :value="condition.value"
                  >{{ condition.title }}</option
                >
                <!-- <option>Paused</option> -->
              </select>
              <p>that will fire</p>
              <select
                class="selectpicker"
                data-width="fit"
                v-model="actionFilter"
                @change="addToUrlQuery('actionFilter')"
              >
                <option :value="null">Any Action</option>
                <option
                  v-for="(action, actionIndex) in avaliableActions"
                  :key="actionIndex"
                  :value="action.value"
                  >{{ action.title }}</option
                >
                <!-- <option>Paused</option> -->
              </select>
              <!-- <select class="selectpicker" data-width="fit" v-model="sort">
                <option value="dateDesc">Most Recent</option>
                <option value="dateAsc">Oldest</option>
                <option>A-Z</option>
                <option>Z-A</option>
                <option data-divider="true"></option>
                <option>Customer Order</option>
              </select> -->
            </div>
          </div>
          <div class="hl_controls--right">
            <!-- <div class="search-form">
              <i class="icon icon-loupe"></i>
              <input type="text" class="form-control form-light" placeholder="Search Rules">
            </div>-->

            <UIButton type="button" use="primary" @click="createFolder">
              <i class="icon icon-plus mr-2"></i> New Folder
            </UIButton>

            <UIButton use="primary" @click="createTrigger">
              <i class="icon icon-plus mr-2"></i> Add Trigger
            </UIButton>
          </div>
        </div>
        <div class="hl_triggers--wrap">
          <TriggerFolder
            v-for="(folder, folderIndex) in filteredFolders"
            :key="folderIndex"
            :folder="folder"
            :folders="filteredFolders"
            :triggers="filteredTriggers.filter(x => x.folderId === folder.id)"
            :storeFolders="FoldersList.map(i => i).find(id => id === folder.id)"
            @rename_folder="renameFolder"
            @cloneTrigger="cloneTrigger"
            @runTrigger="runTrigger"
            :currentLocationId="currentLocationId"
          >
          </TriggerFolder>

          <!-- <div class="hl_triggers--control">
            <div class="dropdown">
              <button class="btn btn-light dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                Group: <strong>Everything</strong>
              </button>
              <div class="dropdown-menu">
                <div class="form-group">
                  <input type="text" class="form-control" placeholder="Search Groups">
                </div>
                <p><a href="#" class="active">Everything</a>
                </p>
                <p><a href="#">Test Group</a>
                </p>
                <p><a href="#" class="btn btn-light2">Manage Groups</a>
                </p>
              </div>
            </div>
          </div>-->

          <TriggerItem
            v-for="trigger in filteredTriggers.filter(x => !x.folderId)"
            :key="trigger.id"
            :trigger="trigger"
            :folders="filteredFolders"
            @cloneTrigger="cloneTrigger"
            @runTrigger="runTrigger"
          >
          </TriggerItem>
        </div>
      </div>
    </section>
    <!-- END of .customers -->
    <CopyTriggerModal
      :values="copyValues"
      @hidden="copyValues = { visible: false }"
    />
    <!-- <FormModal :values="formModal" :items="this.triggers" @hidden="hideModal" v-if="formModal.visible"/> -->
    <DetailPageModal
      :values="modal"
      @hidden="hideModal"
      v-if="modal.visible"
    ></DetailPageModal>
  </section>
  <!-- END of .hl_wrapper -->
</template>

<script lang="ts">
import Vue from 'vue'
import { Trigger, TriggerType, Location, User } from '../../../models'
import { Folder } from '../../../models'
import { mapGetters, mapState } from 'vuex'
import firebase from 'firebase/app'
import draggable from 'vuedraggable'
import { TriggerMaster, ActionMaster } from '@/util/trigger'
import { UserState } from '../../../store/state_models'
import { cloneDeep } from 'lodash'
const CopyTriggerModal = () =>
  import('../../components/triggers/CopyTriggerModal.vue')
const FormModal = () => import('../../components/FormModal.vue')
const TriggerFolder = () => import('../../components/TriggerFolder.vue')
const TriggerItem = () => import('../../components/triggers/TriggerItem.vue')
const DetailPageModal = () =>
  import('../../components/triggers/DetailPageModal.vue')

declare var $: any
let triggersListener: () => void
let foldersListener: () => void
export default Vue.extend({
  components: {
    CopyTriggerModal,
    FormModal,
    TriggerFolder,
    TriggerItem,
    DetailPageModal
  },
  data() {
    return {
      formModal: {
        visible: false
      },
      modal: {
        visible: false,
        name: '',
        action: '',
        title: '',
        type: '',
        folders: [] as Folder[],
        folder: {} as Folder
      },
      triggers: [] as Trigger[],
      folders: [] as Folder[],
      currentLocationId: '',
      type: 'all',
      sort: 'dateDesc',
      copyValues: {
        visible: false,
        trigger: undefined as Trigger | undefined,
        currentLocationId: ''
      },
      dateBasedTrigger: [
        'task_due_date_reminder',
        'custom_date_reminder',
        'birthday_reminder',
        'opportunity_decay'
      ],
      folderId: null,
      draggedTrigger: null,
      actionFilter: null,
      conditionFilter: null,
      botService: false
    }
  },
  async created() {
    this.saveRouteVariables()
    this.fetchTriggersData()
    this.fetchFoldersData()
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.currentLocationId = id
      if (id) {
        this.fetchTriggersData()
        this.fetchFoldersData()
      }
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    ...mapGetters('trigger_folder', ['FoldersList']),
    filteredTriggers: {
      get(): Trigger[] {
        let filtered = this.triggers

        filtered = lodash.filter(this.triggers, trigger => {
          if (this.type !== 'all') {
            if (trigger.active !== Boolean(this.type === 'active')) return false
          }
          if (this.actionFilter) {
            const actionFiltered = trigger.actions.find(
              action => action.type === this.actionFilter
            )
            if (!actionFiltered) return false
          }
          if (this.conditionFilter) {
            if (trigger.type !== this.conditionFilter) return false
          }
          return Boolean(!trigger.workflowId) // Any workflow triggers will be filtered out
        })

        filtered.sort(function(a, b) {
          var x = a.title
            ? a.title.toLowerCase()
            : a.type
            ? a.type.toLowerCase()
            : ''
          var y = b.title
            ? b.title.toLowerCase()
            : b.type
            ? b.type.toLowerCase()
            : ''
          // var x = a.position;
          // var y = b.position;
          return x < y ? -1 : x > y ? 1 : 0
        })
        return filtered
      }
    },
    filteredFolders(): Folder[] {
      let folders = this.folders
      let filtered = lodash.filter(folders, { type: 'triggers' }) // Filter first, better performance
      let result = lodash.sortBy(filtered, 'name')
      return result
    },
    getfolderValues() {
      let result = this.folders
      return result
        .map(folder => {
          return { name: folder.name, type: folder.type, id: folder.id }
        })
        .filter(folder => folder.type === 'triggers')
    },
    avaliableActions() {
      const actionMaster = cloneDeep(ActionMaster)
      const actionTypes = []
      for (let actions of Object.values(actionMaster)) {
        actions.forEach(action => {
          if (
            !this.botService ||
            (this.user && !this.user.permissions.bot_service)
          ) {
            action.options = action.options.filter(
              option => option.value !== 'add_to_bot_conversation'
            )
          }
          actionTypes.push.apply(actionTypes, action.options)
        })
      }
      return actionTypes
    },
    avaliableConditions() {
      const triggerMaster = cloneDeep(TriggerMaster)
      const conditionTypes = []
      for (let conditions of Object.values(triggerMaster)) {
        conditions.forEach(condition => {
          if (
            !this.botService ||
            (this.user && !this.user.permissions.bot_service)
          ) {
            if (condition.title === 'Eliza triggers') condition.options = []
          }
          conditionTypes.push.apply(conditionTypes, condition.options)
        })
      }
      return conditionTypes
    }
  },
  methods: {
    createTrigger() {
      this.modal = {
        visible: true,
        title: 'New Basic Rule',
        type: 'triggers',
        action: 'new_trigger_campaign',
        folders: this.filteredFolders
      }
    },
    createFolder() {
      this.modal = {
        visible: true,
        action: 'new_folder',
        title: 'Create New Folder',
        type: 'triggers'
      }
    },
    async renameFolder(name: any, folder: Folder) {
      this.modal = {
        visible: true,
        action: 'rename_folder',
        title: 'Rename Folder',
        name: name,
        folder: folder
      }
    },
    resetModal() {
      this.modal = {
        visible: false,
        action: '',
        title: '',
        type: '',
        folders: [] as Folder[]
      }
    },
    hideModal() {
      this.resetModal()
      document.querySelectorAll('.modal-backdrop').forEach(a => a.remove())
    },
    cloneTrigger(trigger?: Trigger) {
      this.copyValues = {
        currentLocationId: this.currentLocationId,
        trigger: trigger,
        visible: true
      }
    },

    getEndPoint(triggerType: string): string {
      if (triggerType === TriggerType.OPPORTUNITY_DECAY) {
        return '/trigger/processDecayOpportunities'
      } else if (triggerType === TriggerType.BIRTHDAY_REMINDER) {
        return '/trigger/processBirthdayReminder'
      } else if (triggerType === TriggerType.CUSTOM_DATE_REMINDER) {
        return '/trigger/processCustomDateReminder'
      } else if (triggerType === TriggerType.TASK_DUE_DATE_REMINDER) {
        return '/trigger/processTaskDueReminder'
      }
      return ''
    },
    async runTrigger(trigger: Trigger) {
      let response = await this.$http.post(this.getEndPoint(trigger.type), {
        trigger_id: trigger.id
      })
    },

    getStatus(trigger: Trigger) {
      return !trigger.active ? 'Draft' : 'Active'
    },
    formatType(type: string) {
      return type.split('_').join(' ')
    },
    async fetchTriggersData() {
      if (triggersListener) {
        triggersListener()
      }

      triggersListener = Trigger.getByLocationIdRealtime(
        this.currentLocationId
      ).onSnapshot(snapshot => {
        this.triggers = snapshot.docs.map(d => new Trigger(d))
      })
      const location = new Location(
        await this.$store.dispatch('locations/getById', this.currentLocationId)
      )
      this.botService = location.botService
    },
    fetchFoldersData() {
      if (foldersListener) {
        foldersListener()
      }

      foldersListener = Folder.getByLocationIdRealtime(
        this.currentLocationId
      ).onSnapshot(snapshot => {
        this.folders = snapshot.docs.map(d => new Folder(d))
      })
    },
    addToUrlQuery(filterName) {
      this.$router.push({
        query: Object.assign({}, this.$route.query, {
          [filterName]: this[filterName] || undefined
        })
      })
    },
    saveRouteVariables() {
      this.currentLocationId = this.$router.currentRoute.params.location_id
      this.actionFilter = this.$router.currentRoute.query.actionFilter || null
      this.conditionFilter =
        this.$router.currentRoute.query.conditionFilter || null
      this.type = this.$router.currentRoute.query.type || 'all'
    }
  },
  beforeDestroy() {
    if (triggersListener) {
      triggersListener()
    }
    if (foldersListener) {
      foldersListener()
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }
  }
})
</script>
