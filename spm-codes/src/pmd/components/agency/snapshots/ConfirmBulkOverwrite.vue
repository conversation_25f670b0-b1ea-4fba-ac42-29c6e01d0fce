<template>
  <div class="confirmation-input">
    <h5 class="title">Are you sure you want to overwrite selected data?</h5>
    <p>This action is irreversible, and may change configurations of campaigns, opportunities, funnels etc. Please confirm your action by typing following in the input.</p>
    <p class="confirmation-message" :class="{ matched: matched }">
      Confirm by typing
      <span>{{ confirmationMessage }}</span> below.
    </p>
    <input :class="{ matched: matched }" v-model="userInput" @input="checkForMatch" />
    <div class="controls">
      <button type="button" class="btn btn-primary" @click="$emit('cancel')">Cancel</button>
      <button
        type="button"
        :disabled="!matched"
        class="btn btn-success"
        @click="confirm"
      >Confirm Overwrite</button>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  data() {
    return {
      userInput: '',
      confirmationMessage: 'Confirm',
      matched: false
    }
  },
  methods: {
    checkForMatch() {
      this.matched =
        this.userInput.toLowerCase() === this.confirmationMessage.toLowerCase()
    },
    confirm() {
      if (this.matched) {
        this.$emit('confirmed')
      }
    }
  }
})
</script>
<style scoped>
.confirmation-input {
  position: absolute;
  background: white;
  z-index: 10;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 40px;
  text-align: center;
  border-radius: 6px;
}

.title {
  font-weight: bold;
  margin-bottom: 16px;
}

p {
  margin: 0;
}

p.confirmation-message {
  font-weight: bold;
  font-size: 18px;
  margin: 22px 0;
}

.confirmation-message span {
  color: red;
}

.confirmation-message.matched span {
  color: var(--success);
}

input {
  border: 2px solid lightgray;
  border-radius: 4px;
  width: 100%;
  padding: 5px 10px;
}

input.matched,
input.matched:focus {
  border-color: var(--success) !important;
}

.controls {
  position: absolute;
  bottom: 0;
  right: 0;
  margin-bottom: 40px;
  margin-right: 40px;
}

.controls button:last-child {
  margin-left: 20px;
}
</style>
