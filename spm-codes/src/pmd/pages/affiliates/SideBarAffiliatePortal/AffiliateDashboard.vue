<template>
  <div>
      <template>
        <!-- Show Loading -->
        <div  v-if="loading" class="loader-backdrop">
          <div>
            <moon-loader  color="#188bf6" size="30px" />
            <p>Please wait...</p>
          </div>
        </div>
        <!-- Show Error Msg -->
        <div  v-else-if="errorMsg" class="loader-backdrop">
          <div>
            <p class="errorMsg" >{{errorMsg}}</p>
          </div>
        </div>
        <!-- FirstPromoter Dashboard - iFrame -->
        <section v-else
          class="hl_agency hl_agency-sales-resources"
          style="background-color: white"
        >
          <iframe
            :src="affiliateDashboardURL"
            style="height: calc(100vh - 84px); width: 100%; border: none"
          ></iframe>
        </section>
      </template>
  </div>
</template>

<script>
import Vue from 'vue'
import TopBar from '../../../components/TopBar.vue'
import SideBar from '../../../components/agency/SideBar.vue'
import SideBarV2 from '../../../components/sidebar/SideBar.vue'
import { User } from '@/models'

export default {
  components: { TopBar, SideBar, SideBarV2 },
  data(){
    return {
      companyId: this.$store.state?.company?.company?.id,
      loading: false,
      errorMsg:"",
      auth_token: ""
    }
  },
  async created(){
     await this.getAuthToken()
  },
  computed:{
    affiliateDashboardURL(){
      let baseURL = `https://gohighlevel.firstpromoter.com/iframe?at=${this.auth_token}`
      return baseURL
    },
  },
  methods: {
     async getAuthToken(){
        try {
          this.loading = true
          const { data } = await this.$http.get(`/getfirstPromoterAuth/${this.companyId}`)
          this.auth_token = data.fp_auth_token
          this.loading = false
        } catch (error) {
          this.loading = false
          this.errorMsg = error.message
        }
     }
  }
}

</script>

<style scoped lang="scss" >

  .loader-backdrop {
    background: #ffffffc7;
    width: 100%;
    height: 90vh;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;

    p {
      margin-top: 10px;
      padding: 10px;
      color: #009000;
      font-weight: 500;
    }

    .errorMsg{
      color:red;
      font-size: 20px;
      font-weight: 500;
    }

 }
</style>
