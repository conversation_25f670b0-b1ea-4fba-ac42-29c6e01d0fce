<template>
  <div>
    <SideBarV2 v-if="isV2SideBar && agencyUser" />
    <SideBar v-else-if="agencyUser" />
    <TopBar v-if="agencyUser" />

    <section :class="agencyUser ? 'hl_wrapper' : ''">
      <section v-if="cloned">
        <div style="text-align: center" class="container-fluid">
          <h4>Product Cloned! Switch to an account where it was cloned and find it under Membership > Products tab.</h4>
        </div>
      </section>
      <section v-else class="hl_wrapper--inner">
        <div v-if="fetchingShareDetails" class="container-fluid">
          <moon-loader :loading="fetchingShareDetails" color="#188bf6" size="30px" />
        </div>
        <div
          v-else-if="!fetchingShareDetails && !errorInFetchingShareDetails"
          style="text-align: center"
          class="container-fluid"
        >
          <h4
            style="margin-bottom: 18px;"
          >Copy of {{ productDetails && productDetails.productName }}</h4>
          <div v-if="settingEnabled">
            <button
              @click="showCloneModal()"
              type="button"
              class="btn btn-primary btn-sm"
              style="margin-right: 24px;"
            >Add Product</button>
            <button @click="cancelShare()" type="button" class="btn btn-light btn-sm">Cancel</button>
          </div>
          <div v-else>
            <h6>Permissions not enabled!</h6>
            <button
              @click="cancelShare()"
              type="button"
              class="btn btn-primary btn-sm"
            >Go to Dashboard</button>
          </div>
        </div>
        <div v-if="errorInFetchingShareDetails" class="container-fluid">
          <h4>There seems to be an error while getting product details. Please check the url or try again!</h4>
        </div>
      </section>
    </section>
    <CloneProductToLocation
      :show-modal="cloneModalShown"
      :product-details="productDetails"
      :locations="locationsForClone"
      @hide="hideCloneModal"
      @cloneSuccessful="cloneSuccessful"
    />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import axios from 'axios'
import TopBar from '../../components/TopBar.vue'
import SideBar from '../../components/agency/SideBar.vue'
import SideBarV2 from '../../components/sidebar/SideBar.vue'
import store from '@/store'
import { User, Location } from '@/models'
import CloneProductToLocation from '../../components/memberships/CloneProductToLocation.vue'

export default Vue.extend({
  components: {
    TopBar,
    SideBar,
    CloneProductToLocation,
    SideBarV2
  },
  computed: {
    locationsForClone(): Array<object> {
      return this.allLocations.map((location: Location) => {
        const { name, id } = location
        return { label: name, id }
      })
    },
    isV2SideBar() {
      return this.$store.getters['sidebarv2/getVersion'] === 'v2'
    },
  },
  data() {
    return {
      allLocations: [],
      cloned: false,
      fetchingShareDetails: false,
      errorInFetchingShareDetails: false,
      agencyUser: false,
      settingEnabled: false,
      productDetails: null,
      cloneModalShown: false,
      loading: {
        locations: false
      }
    }
  },
  async created(){
    this.loading.locations = true
    this.allLocations = await this.$store.dispatch('locations/getAll')
    this.loading.locations = false
  },
  async mounted() {
    const shareId = this.$router.currentRoute.params.share_id

    if (!shareId) {
      console.log('invalid share id --> ', shareId)
      return
    }

    try {
      this.errorInFetchingShareDetails = false
      this.fetchingShareDetails = true
      const user = new User(await store.dispatch('user/get'))
      this.agencyUser = user.type === User.TYPE_AGENCY
      this.settingEnabled = !!user.permissions.membership_enabled

      const response = await axios.get(
        `/memberships/get-product-share-details/${shareId}`
      )
      const { data } = response
      this.productDetails = data
    } catch (error) {
      console.error('Error while getting share details --> ', error)
      this.errorInFetchingShareDetails = true
    } finally {
      this.fetchingShareDetails = false
    }
  },
  methods: {
    cancelShare() {
      this.$router.push({ name: 'dashboard', replace: true })
    },
    showCloneModal() {
      this.cloneModalShown = true
    },
    hideCloneModal() {
      this.cloneModalShown = false
    },
    cloneSuccessful(cloneData: any) {
      // const { locationIds } = cloneData

      // const clonedLocationId = locationIds[0]

      // this.redirectToListingPage(clonedLocationId)

      this.cloned = true
      this.hideCloneModal()
    },
    redirectToListingPage(locationId: string) {
      this.$router.push({
        name: 'membership',
        params: { location_id: locationId }
      })
    }
  }
})
</script>
<style scoped>
.container-fluid {
  text-align: center;
}
</style>
