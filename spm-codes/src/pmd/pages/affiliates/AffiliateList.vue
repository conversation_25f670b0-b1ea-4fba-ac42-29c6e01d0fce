<template>
  <div>
    <section class="hl_wrapper">
      <section class="hl_wrapper--inner marketplace pb-0" id="marketplace">
          <div class="padding-container">
            <div class="hl_marketplace--search">
              <div class="row">
                <div class="col-md-4 col-lg-4">
                  <div class="form-group">
                    <UITextLabel>Industry Served</UITextLabel>
                    <vSelect
                      class="mt-1"
                      :options="industry_served_options"
                      placeholder="Industry Served"
                      v-model="industry_served"
                      :clearable="true"
                      :multiple="true"
                    ></vSelect>
                  </div>
                </div>
                <div class="col-md-4 col-lg-4">
                  <div class="form-group">
                    <UITextLabel>Service provided</UITextLabel>
                    <vSelect
                      class="mt-1"
                      :options="service_provided_options"
                      placeholder="Service provided"
                      v-model="service_provided"
                      :clearable="true"
                      :multiple="true"
                    ></vSelect>
                  </div>
                </div>
                <!-- <div class="col-md-6 col-lg-3">
                  <div class="form-group">
                    <label>Partner Status</label>
                    <vSelect
                      :options="partner_staus_options"
                      placeholder="Partner Status"
                      v-model="partner_status"
                      :clearable="true"
                    ></vSelect>
                  </div>
                </div> -->
                <div class="col-md-4 col-lg-4">
                  <div class="form-group">
                    <UITextLabel>Search Partner</UITextLabel>
                    <UITextInputGroup
                      type="text"
                      icon="icon-loupe"
                      placeholder="Partner Name"
                      v-model="partner_name"
                    />
                    <!-- <div class="search-form">
                      <i class="icon icon-loupe"></i>
                      <input
                        style="height: 49px"
                        v-model="partner_name"
                        type="text"
                        class="form-control form-light"
                        placeholder=""
                      />
                    </div> -->
                  </div>
                </div>
              </div>
            </div>
            <div v-if="!loading">
            <ul class="hl_marketplace--results mb-0" v-if="randomList.length > 0">
              <li>
                <Affiliate v-for="affiliate in randomList" :key="affiliate._id" :data="affiliate._source" :id="affiliate._id"/>
                <!-- <Affiliate />
                <Affiliate />
                <Affiliate /> -->
              </li>
            </ul>
            <div class="no-results" v-else>
              Could not find a partner matching your filters. Click <a href="javascript:void(0);">here</a> to apply to become a partner.
            </div>
            </div>
            <div v-else class="my-5">
          <MoonLoader size="30px" radius="50%" />
        </div>
          </div>
      </section>
    </section>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import Affiliate from '../../components/affiliates/Affiliate'
import vSelect from 'vue-select'
import axios from 'axios'
import defaults from '@/config'
import MoonLoader from '../../components/MoonLoader'

export default Vue.extend({
  components: {
    Affiliate,
    vSelect,
    MoonLoader
  },
  data() {
    return {
      industry_served: this.$store.state.affiliate.industry_served,
      industry_served_options: [
        'Advertising / Marketing',
        'Attorney / Legal',
        'Automotive Sales / Repair',
        'Chiro',
        'Computer / Software',
        'Coaching',
        'Construction / Contractors',
        'Consulting',
        'Dental',
        'Design / Architecture / Engineering',
        'Diet / Health / Nutrition',
        'Financial Services',
        'Government Agency',
        'Gym / Crossfit / Personal Training / Martial Arts',
        'Hair / Beauty / Nails',
        'Health / Nutrition',
        'Hospitality',
        'Insurance / Brokage',
        'Jewellery',
        'Lawn Care / Landscaping',
        'Manufacturing',
        'Medical / Health Services',
        'Medspa',
        'Non Profit',
        'Pest / Rodent Control',
        'Plumbing / HVAC',
        'Property Management',
        'Real Estate / Developer',
        'Restaurant / Bar',
        'Retail',
        'Solar / Green Tech',
        'Salon / Beauty',
        'Telecommunications',
        'Transportation',
        'Wholesale Distribution'
      ],
      service_provided: this.$store.state.affiliate.service_provided,
      service_provided_options: [
        'Conversion Rate Optimization',
        'Copy Writing',
        'Facebook & Instagram Ads',
        'Funnel Builds',
        'Google Ads',
        'Graphic Design',
        'HighLevel Builds',
        'Search Engine Optimization',
        'Social Media Management',
        'Video Content',
        'Web Development',
        'YouTube Ads',
        'Other'
      ],
      partner_status: this.$store.state.affiliate.partner_status,
      partner_staus_options: ['Certified Partner', 'Partner'],
      partner_name: this.$store.state.affiliate.partner_name,
      baseURL: defaults.baseUrl,
      waitTime: 700,
      debouncer: undefined as NodeJS.Timer | undefined,
      loading: false
    }
  },
  computed: {
    randomList(): Array<{[key: string]: any}> {
      return this.$store.state.affiliate.affiliateList.filter((a: any) => true).sort(() => Math.random() - 0.5);
    }
  },
  watch: {
    industry_served(newValue) {
      this.$store.commit('affiliate/updateIndustryServed', newValue)
      this.search()
      //this.add()
    },
    service_provided(newValue) {
      this.$store.commit('affiliate/updateServiceProvided', newValue)
      this.search()
    },
    partner_status(newValue) {
      this.$store.commit('affiliate/updatePartnerStatus', newValue)
      this.search()
    },
    partner_name(newValue) {
      this.$store.commit('affiliate/updatePartnerName', newValue)
      let _self = this
      if (this.debouncer) clearTimeout(this.debouncer)
      this.debouncer = setTimeout(() => this.search(), this.waitTime)
    }
  },
  mounted() {
    if(this.$store.state.affiliate.affiliateList.length === 0){
      this.search()
    }
  },
  methods: {
    async search() {
      this.loading = true
      const result = await axios.post(`${this.baseURL}/partners/search`, {
        filters: [
          { name: 'name', value: this.partner_name },
          { name: 'services', value: this.service_provided },
          { name: 'industries', value: this.industry_served },
          { name: 'rank', value: this.partner_status }
        ]
      })
      this.loading = false
      this.$store.commit('affiliate/updateAffiliateList', result.data.hits.hits)
    }
  }
})
</script>

<style>
.no-results {
  text-align: center;
  margin-top: 50px;
}
.padding-container {
  max-width: 1200px !important;
  padding-left: 20px;
  padding-right: 20px;
  margin-right: auto;
  margin-left: auto;
}
.hl_marketplace--search .v-select {
  background: white !important;
}
.hl_marketplace--search .v-select .vs__actions {
  cursor: pointer;
  padding-right: 8px;
}
.hl_marketplace--search .v-select .vs__selected {
  white-space: nowrap;
  height: 2rem;
  align-items: center;
}
.hl_marketplace--search .v-select .vs__selected-options {
  width: 90%;
  overflow: auto;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}
.hl_marketplace--search .v-select .vs__selected-options::-webkit-scrollbar{
  display: none;
}
.hl_marketplace--search .v-select .vs__search::placeholder {
  color: #2a3135;
  opacity: 0.5;
}
.hl_marketplace--search .v-select .vs__dropdown-menu {
  border: none;
  width: auto;
  min-width: 100%;
}
.hl_marketplace--search .v-select .vs__dropdown-menu .vs__dropdown-option {
  padding: 8px;
}
.hl_marketplace--search .dropdown {
  display: flex !important;
}
.hl_marketplace--search div .bootstrap-select #select-dropdown .dropdown-menu {
  width: inherit !important;
}
.hl_marketplace--results {
  list-style: none;
  padding: 0;
  margin: 0;
  margin-bottom: 40px;
}
.hl_marketplace--results li > a {
  display: block;
  color: #607179;
  border-radius: 4px;
}
.hl_marketplace--results li > a:hover .card {
  -webkit-box-shadow: 15px 26px 40px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 15px 26px 40px 0 rgba(0, 0, 0, 0.05);
}
.marketplace_card--avatar .img-wrap > img[data-v-0a453ca8] {
    max-width: 130px;
    height: auto;
    max-height: 130px;
    width: auto;
    border: 1px solid #c6c3bd;
}
</style>
