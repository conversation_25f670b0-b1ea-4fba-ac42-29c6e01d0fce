<template>
	  <div class="menu-item">
      <div class="form-builder--item">
        <!--Adding description for each field-->
        <label v-if="customFields.description !== undefined" style="display:block;" v-html="customFields.description"></label>
        <div style="display:inline-flex;">
          <label v-if="formLabelVisible">{{customFields.label}} <span v-if="customFields.required">*</span></label>
          <div v-if="customFields.hidden">
            <small style="background: #e1e1e1;padding: 2px 5px;border-radius: 2px; margin-left: 5px;">Hidden</small>
          </div>
        </div>
				<component
          v-bind:is="getFieldInput(customFields.dataType)"
          :placeholder="customFields.placeholder"
          :picklistOptions="customFields.picklistOptions"
		      :picklistOptionsImage="customFields.picklistOptionsImage"
          :disabled="true"
          :field="customFields"
        ></component>
      </div>
      <a class="close-icon" @mousedown="removeField(customFields.id)" v-if="customFields.active">
        <i class="icon-close"></i>
      </a>
      <add-field-dropzone :index="index" v-if="showDropZones"></add-field-dropzone>
    </div>
</template>

<script lang="ts">
import Vue from "vue";

import { isoToState, countries } from "@/util/state_helper";

import {
	Location,
	Contact,
	CustomField,
	ICustomField,
	FieldType
} from "@/models";

import libphonenumber from "google-libphonenumber";
const TextField = () => import("../util/TextField.vue");
const LargeTextField = () => import("../util/LargeTextField.vue");
const NumericalField = () => import("../util/NumericalField.vue");
const FileUploadField = () => import("../util/FileUploadField.vue");
const SignatureField = () => import("../util/SignatureField.vue");
const PhoneField = () => import("../util/PhoneField.vue");
import CheckboxField from "../util/CheckboxField.vue";
import MonetaryField from "../util/MonetaryField.vue";
import MultipleOptionsField from "../util/MultipleOptionsField.vue";
import SingleOptionsField from "../util/SingleOptionsField.vue";
import RadioField from "../util/RadioField.vue";
import DateField from "../util/DateField.vue";
import TextboxListField from "../util/TextboxListField.vue";
const AddFieldDropzone = () => import("../../components/marketing/AddFieldDropzone.vue");

export default Vue.extend({
	props: {
    customFields: {} as any,
    index: {} as any,
    showDropZones: {} as any,
    formLabelVisible: {} as any
	},
	components: {
		TextField,
    LargeTextField,
    NumericalField,
    PhoneField,
    CheckboxField,
    MonetaryField,
    MultipleOptionsField,
    SingleOptionsField,
    RadioField,
    DateField,
    "add-field-dropzone": AddFieldDropzone,
    TextboxListField,
    FileUploadField,
    SignatureField
	},
	data() {
		return {
			contactCustomFieldsData: {}
		};
	},
	computed: {

	},
	watch: {

	},
	updated() {

	},
	mounted() {

	},
	methods: {
		getFieldInput(fieldType: string) {
      return FieldType[fieldType] ? FieldType[fieldType].replace(/ /g, '') + "Field" : "";
		},
		removeField(customFieldsId: string) {
			this.$bus.$emit("remove-custom-field", {
				id: customFieldsId
			});
		}
	}
});
</script>

<style lang="scss">
  .form-control {
    border-radius: 0.3125rem;
    padding: 15px 20px;
    font-size: 0.875rem;
    &:disabled, [readonly] {
      background-color: #e9ecef;
      opacity: 1;
    }
  }

  .vs__search, .vs__search:focus {
    padding: 12px 18px !important
  }

  .form-builder--item {
    .v-select {
      width: 100%;
      background: #f3f8fb;
      border-radius: 4px !important;
      height: auto;
      border: transparent;
      .vs__dropdown-toggle {
        height: 50px;
      }
    }
  }
  .add-custom-opt {
    padding: 3px;
    font-size: 14px;
  }
</style>
