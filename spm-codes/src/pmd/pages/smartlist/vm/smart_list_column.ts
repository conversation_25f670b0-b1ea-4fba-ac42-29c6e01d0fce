import { SmartListColumnsVM } from './smart_list_columns';
import { IColDefinition } from './interfaces';
import {cloneDeep as lodashCloneDeep} from 'lodash'
import { Utils } from '../../../../util/utils';

export class SmartListColumn {

  private item: IColDefinition;

  public get key(): string {
    return this.item.key;
  }

  public get name(): string {
    return this.item.value;
  }

  public get name_lc(): string {
    return Utils.safeLC(this.item.value).trim();
  }

  public get sortName(): string {
    return this.item.sortName ? this.item.sortName : this.item.key;
  }

  public get order(): number {
    return this.item.order;
  }

  public get checked(): boolean {
    return this.chk;
  }

  public set checked(val: boolean) {
    this.chk = val;
    if (this.debouncer) clearTimeout(this.debouncer);
    this.debouncer = setTimeout(() => {
      if (this.vm) this.vm.buildView();
    }, 200);
  }

  public get sortOrder(): number {
    return this._sortOrder;
  }

  public set sortOrder(val: number) {
    this._sortOrder = val;
  }

  public get isCustomField(): boolean {
    return this.item.isCustomField || false;
  }

  public get parentCustomFieldId(): string{
    return this.item.parentCustomFieldId || '';
  }

  public get isRequired(): boolean {
    return this.item.isRequired || false;
  }

  public get ascending(): boolean{
    return this.sortDirection === 'asc';
  }

  public get descending(): boolean{
    return this.sortDirection === 'desc';
  }

  public get sortDirection(): 'asc' | 'desc' | '' | null {
    return this._sortDirection || '';
  }

  public set sortDirection(val : 'asc' | 'desc' | '' | null) {
    this._sortDirection = val;
  }

  public toggleSortDirection(){
    if (this.sortDirection === 'desc') this.sortDirection = 'asc';
    else this.sortDirection = 'desc'
  }

  public get isSortable(): boolean{
    return this.item.isSortable === true;
  }

  public get exportName(): string{
    return this.item.exportName || this.name;
  }

  public exportFormatter: (item:string) => string ;

  private chk: boolean;
  private vm: SmartListColumnsVM;
  private debouncer: NodeJS.Timer;
  private _sortOrder: number;
  private _sortDirection: 'asc' | 'desc' | '' | null;

  constructor(vm: SmartListColumnsVM, item: IColDefinition, checked: boolean, ) {
    if (!item) throw new Error ('Cannot create a SmartListColumn instance without column definition')
    this.item = lodashCloneDeep(item);
    this.chk = checked;
    this.vm = vm;
    if (item.sortDirection) {
      this._sortDirection = item.sortDirection
    }
    //if (item.sortOrder) this._sortOrder = item.sortOrder;
    if (item && item.exportFormatter) this.exportFormatter = item.exportFormatter;
  }

  public enable(){
    if (this.chk !== true) this.chk = true;
  }

  public disable(){
    if(this.chk === true) this.chk = false;
  }

  public serialize() {
      var item =  lodashCloneDeep(this.item);
      if (this.sortDirection) item.sortDirection = this.sortDirection;
      else delete item.sortDirection;
      delete item.exportFormatter;
      delete item.sortName;
      delete item.isSortable;
      return item;
  }
}
