<template>
  <div class="container-fluid">
    <div class="hl_controls">
      <div class="hl_controls--left funnel-title">
        <h2>
          <a @click="goBack()" class="back">
            <i class="icon icon-arrow-left-2"></i>
          </a>
          Transactions
        </h2>
      </div>
      <div class="hl_controls--right">
        <p class="disclaimer --blue">
          <i class="fa fa-exclamation-triangle"></i>
          <i>&nbsp;Some records may take 24 hrs to update here.</i>
        </p>
      </div>
    </div>
    <div class="row">
      <HLTable
        class="transaction-table"
        :items="transactions"
        :fields="fields"
        :currentPage="currentPage"
        :isSelectableRow="false"
        :defaultPerPage="perPage"
        :rows="transactionsCount"
        :isBusy="fetching"
        :title="`DEBUG Wallet Transactions, Total: ${transactionsCount}`"
        :detailsPagination="true"
        :headerOptions="headerOptions"
        :isExternelDownload="true"
        :exportItem="exportItems"
        :fileName="fileName"
        :isTransactionTable="true"
        @pageChanged="pageChanged"
        @perPageChanged="perPageChanged"
        @exportAll="exportAllTransactions"
        @exportCompleted="exportCompleted"
      >
        <template v-slot:id="slotProps">
          <a
            class="transaction-id"
            @click="showTransactionDetails(slotProps.data.value, slotProps.data.item)"
          >
            {{ slotProps.data.value }}
          </a>
        </template>
        <template v-slot:amount="slotProps">
          <div
            class="amount"
            :class="slotProps.data.value < 0 ? 'debit' : 'credit'"
          >
            <b>
              {{ getReadableAmount(slotProps.data.value) }}
            </b>
          </div>
        </template>
        <template v-slot:type="slotProps">
          <span class="type">
            {{ slotProps.data.value }}
          </span>
        </template>
        <template v-slot:balance="slotProps">
          <div class="balance">
            <b>{{ currency }}{{ slotProps.data.value }}</b>
          </div>
        </template>
      </HLTable>
    </div>
    <wallet-transaction-modal
      :show="transactionDetails.showModal"
      :transaction-id="transactionDetails.transactionId"
      :loading="transactionDetails.loading"
      :message-details="transactionDetails.details"
      :transaction-data="transactionDetails.transaction"
      :currency="currency"
      :get-readable-amount="getReadableAmount"
      :location-name="transactionDetails.locationName"
      @close="transactionDetails.showModal = false"
    />
    <mail-transaction-modal
      :show="transactionModal.modalState"
      :loading="transactionDetails.loading"
      :email="transactionDetails.email"
      :accountId="company.id"
      view="company"
      endpoint="company-wallet"
      @close="transactionModal.modalState = false"
    />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import HLTable from '@/pmd/components/HLTable.vue'
import moment from 'moment'
import WalletTransactionModal from '@/pmd/components/saas/location_billing/WalletTransactionModal.vue'
import MailTransactionModal from '@/pmd/components/saas/location_billing/MailTransactionModal.vue'

export default Vue.extend({
  props: {
    currentLocationId: {
      type: String,
    },
    timezone: String,
  },
  components: {
    HLTable,
    WalletTransactionModal,
    MailTransactionModal
  },
  data() {
    return {
      transactions: [],
      transactionsCount: 0,
      fetching: false,
      currentPage: 1,
      perPage: 25,
      currency: '$',
      fields: [
        { key: 'id', checked: true },
        { key: 'locationName', label: 'Location Name', checked: true },
        { key: 'date', label: 'Billing Date', checked: true, type: 'browserTZ' },
        { key: 'messageDate', label: 'Activity Date', checked: true, type: 'browserTZ' },
        { key: 'description', label: 'Description', checked: true },
        { key: 'amount', label: 'Amount', checked: true },
        // { key: 'type', label: 'Type' },
        { key: 'balance', label: 'Balance', checked: true },
      ],
      headerOptions: {
        isExportOption: true,
      },
      exportItems: [],
      fileName: 'Wallet transactions',
      transactionDetails: {
        locationName: null,
        showModal: false,
        transactionId: '',
        loading: false,
        error: '',
        details: {},
        transaction: {},
      },
      transactionModal: {
        modalState: false,
        email: '',
      },
    }
  },
  mounted() {
    this.fetchData()
  },
  computed: {
    tz() {
      const tz =
        this.timezone || Intl.DateTimeFormat().resolvedOptions().timeZone
      return tz
    },
    company() {
      return this.$store.state.company.company
    },
  },
  methods: {
    async exportAllTransactions() {
      try {
        const {
          data: { transactions },
        } = await this.saasService.get(
          `/company-wallet/${this.company.id}/transactions?tz=${this.tz}`
        )

        this.fileName = `Wallet Transactions - All - ${moment().format(
          'MMM Do YYYY'
        )}`
        this.exportItems = transactions
      } catch (error) {
        console.error(error)
        alert('Something went wrong!')
        this.exportItems = []
      }
    },
    exportCompleted() {
      this.exportItems = []
    },
    goBack() {
      this.$router.push({ name: 'account_billing' })
    },
    getReadableAmount(transactionAmount: number) {
      const amount = Math.abs(transactionAmount)
      if (transactionAmount < 0) {
        return `-${this.currency}${parseFloat(amount.toFixed(6).toString())}`
      } else {
        return `+${this.currency}${amount.toFixed(2)}`
      }
    },
    async fetchData() {
      this.fetching = true
      try {
        const skip = (this.currentPage - 1) * this.perPage
        const limit = this.perPage

        const {
          data: { transactions, total },
        } = await this.saasService.get(
          `/company-wallet/${this.company.id}/transactions?timezone=${this.tz}&skip=${skip}&limit=${limit}`
        )

        this.transactions = transactions

        // this.transactions = transactions.map((t: any) => {
        //   const {
        //     _id: id,
        //     createdAt: date,
        //     description,
        //     transactionAmount,
        //     transactionType: type,
        //     walletBalance: balance,
        //   } = t

        //   return {
        //     id,
        //     date: moment(date).format('MMM Do YYYY, hh:mm:ss A'),
        //     description,
        //     amount:
        //       type === 'CREDIT' ? transactionAmount : 0 - transactionAmount,
        //     // type,
        //     balance: `${this.currency}${parseFloat(balance).toFixed(2)}`,
        //   }
        // })

        this.transactionsCount = total
      } catch (error) {
        console.error('Error while fetching transactions --> ', error)
      } finally {
        this.fetching = false
      }
    },
    pageChanged(page: number) {
      this.currentPage = page
      this.fetchData()
    },
    perPageChanged(perPage: number) {
      this.perPage = perPage
      this.currentPage = 1
      this.fetchData()
    },
    async showTransactionDetails(transactionId: string, item: any) {
      this.transactionDetails.error = ''
      this.transactionDetails.transactionId = transactionId
      this.transactionDetails.loading = true
      this.transactionDetails.details = {}
      this.transactionDetails.transaction = {}
      this.transactionDetails.showModal = true
      this.transactionDetails.locationName = item.locationName

      try {
        const {
          data: { details, transaction },
        } = await this.saasService.get(
          `/company-wallet/${this.company.id}/transaction/${transactionId}?timezone=${this.tz}`
        )
        this.transactionDetails.details = details
        this.transactionDetails.transaction = transaction
      } catch (error) {
        this.transactionDetails.error =
          error?.msg ||
          error?.message ||
          'Error while fetching transaction details'
      } finally {
        this.transactionDetails.loading = false
      }
    },
  },
})
</script>
<style scoped>
.row {
  background: white;
}

.row .disclaimer {
  padding: 4px 16px;
}

.transaction-table {
  width: 100%;
}

.transaction-table .transaction-id {
  color: var(--primary);
}

.transaction-table .amount {
  padding-left: 16px;
}

.transaction-table .amount.credit {
  color: green;
}

.transaction-table .amount.debit {
  color: red;
}

.transaction-table .balance {
  padding-left: 16px;
}
</style>
