<template>
  <!-- Edit Event Modal -->
  <div
    class="modal fade hl_edit-event-modal"
    id="edit-event-modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog --mid" role="document">
      <div class="modal-content">
        <button
          type="button"
          class="close"
          data-dismiss="modal"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
        <div class="modal-body">
          <div class="modal-body-inner">
            <div class="hl_edit-event-modal-wrap">
              <div class="hl_edit-event-modal-main">
                <div class="card">
                  <div class="card-header --no-right-padding">
                    <h2>
                      Compose {{ eventType }}
                      {{ isComposingTemplate && 'Text Template' }}
                    </h2>
                  </div>
                  <div class="card-body hl_campaign-configuration">
                    <div class="form-group" style="display: block">
                      <UITextInputGroup
                        type="text"
                        placeholder="Name"
                        v-model="name"
                        v-validate="'required'"
                        name="msgsndr2"
						            data-lpignore="true"
						            autocomplete="msgsndr2"
                        :error="errors.has('msgsndr2')"
                        :errorMsg="'Name is required.'"
                      />
                    </div>
                    <!-- <div class="form-group">
											<textarea
												class="form-control"
												rows="6"
												placeholder="Message"
												v-model="body"
												v-validate="'required'"
												name="body"
											></textarea>
											<span v-show="errors.has('body')" class="--red">Message body is required.</span>
                    </div>-->
                    <div class="form-group" v-if="eventType !== 'GMB Message'">
                      <vSelect
                        style="min-width: 240px"
                        :options="smsTemplates"
                        label="text"
                        v-model="selectedSMSTemplate"
                        name="template"
                        placeholder="Pick message template"
                        v-if="isComposingTemplate !== true"
                      >
                      </vSelect>
                    </div>
                    <div
                      class="form-group"
                      style="margin-top: 20px; flex-direction: column"
                      v-if="!selectedSMSTemplate"
                    >
                      <editor
                        :init="editorOptions"
                        id="editsmseditor"
                        ref="editsmseditor"
                        v-model="tinymcehtml"
                        v-validate="'handlebars'"
                        data-vv-validate-on="input"
                        name="editor"
                      ></editor>
                      <span v-show="errors.has('editor')" class="--red">{{
                        errors.first('editor')
                      }}</span>
                    </div>

                    <div class="form-group" v-if="attachments.length > 0">
                      <div>
                        <div
                          class="attachment"
                          v-for="(attachment, index) in attachments"
                          :key="index"
                          style="position: relative"
                        >
                          <i class="fa fa-paperclip"></i>
                          <span class="mx-2">{{ attachment.name }}</span>
                          <i
                            class="icon-close"
                            @click.prevent="deleteAttachment(attachment.name)"
                            v-if="!selectedSMSTemplate"
                          ></i>
                        </div>
                      </div>
                    </div>

                    <div class="form-group" v-if="urlAttachments.length > 0">
                      <div>
                        <div
                          class="attachment"
                          v-for="(url, index) in urlAttachments"
                          :key="index"
                          style="
                            position: relative;
                            display: flex;
                            align-items: center;
                          "
                        >
                          <i class="fa fa-link"></i>
                          <div
                            style="
                              max-width: 400px;
                              white-space: nowrap;
                              text-overflow: ellipsis;
                              overflow: hidden;
                            "
                          >
                            <span class="mx-2">{{ url }}</span>
                          </div>
                          <i
                            class="icon-close"
                            @click.prevent="deleteUrlAttachment(url)"
                            v-if="!selectedSMSTemplate"
                          ></i>
                        </div>
                      </div>
                    </div>

                    <WindowComponent
                      v-if="!isComposingTemplate"
                      v-model="templateWindow"
                      parent="sms"
                      style="margin-top: 30px"
                    />
                  </div>
                  <div class="card-footer">
                    <div class="modal-footer-left" v-if="eventType !== 'GMB Message'">
                      <div>
                        <input
                          type="file"
                          id="image_uploads"
                          name="image_uploads"
                          multiple
                          @change="updateAttachments"
                          @click="resetAttachments"
                          style="display: none"
                          ref="upload"
                        />
                        <a
                          @click.prevent="$refs.upload.click()"
                          href="javascript:void(0);"
                          v-if="!selectedSMSTemplate"
                        >
                          <i
                            style="margin-right: 4px"
                            class="fa fa-paperclip"
                          />Attach File
                        </a>
                        <div
                          v-if="!selectedSMSTemplate"
                          class="mt-3 flex"
                        >
                          <div style="display: flex; align-items: center">
                            <UITextInputGroup
                              type="text"
                              placeholder="Add file through URL"
                              v-model="urlAttachment"
                              ref="upload_URL"
                              v-validate.continues="
                                'notEmpty|validUrl|handlebars'
                              "
                              name="msgsndr8"
                              autocomplete="msgsndr8"
                              data-vv-scope="url_attachment"
                              :error="errors.has('url_attachment.msgsndr8')"
                              :errorMsg="errors.first('url_attachment.msgsndr8')"
                            />
                          </div>
                          <i
                              class="icon icon-plus add-urlattachment-icon mt-2"
                              @click="addUrlAttachment"
                            ></i>
                        </div>
                      </div>
                    </div>
                    <div class="modal-footer-right">
                      <div style="display: inline-block; position: relative">
                        <UIButton
                          use="primary"
                          @click.prevent="save"
                          :loading="saving"
                        >
                          Save
                        </UIButton>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <div class="hl_edit-event-modal-preview">
                  <div class="hl_edit-event-modal-preview-inner">
                    <div class="hl_sms-preview">
                      <div class="hl_sms-preview-header">
                        <i class="icon icon-menu"></i>
                      </div>
                      <div class="hl_sms-preview-body" style="overflow-y: auto">
                        <div
                          class="message-wrap"
                          v-if="selectedSMSTemplate || body || attachments"
                        >
                          <Avatar :contact="user" :size="'sm'" />
                          <div
                            class="message-bubble attachment"
                            style="background-color: #188bf6"
                            v-for="(
                              attachment, attachmentIndex
                            ) in allAttachments"
                            :key="attachmentIndex"
                          >
                            <img
                              v-if="attachment.type === 'url'"
                              v-bind:src="attachment.url"
                              @error="imageLoadError(attachment.url)"
                              alt="url-image"
                            />
                            <img
                              v-else
                              v-bind:src="attachment.url"
                              alt="file-image"
                            />
                          </div>
                          <div
                            v-if="selectedSMSTemplate || body"
                            class="message-bubble"
                            style="
                              background-color: #188bf6;
                              word-break: break-word;
                            "
                          >
                            <!-- <img v-bind:src="this.photoUrl" alt="image" v-if="this.photoUrl"> -->
                            <p>
                              {{
                                selectedSMSTemplate
                                  ? selectedSMSTemplate.value
                                  : body
                              }}
                            </p>
                          </div>
                        </div>
                      </div>
                      <div class="hl_sms-preview-footer">
                        <p>Enter message</p>
                        <button type="button" class="btn btn-green-lt">
                          Send
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  v-if="eventType !== 'Facebook Message' && eventType !== 'GMB Message'"
                  style="overflow: hidden; margin-top: 15px"
                  class="flex space-x-2 items-center justify-center"
                >
                  <div
                    class="flex"
                  >
                    <PhoneNumber
                      placeholder="Mobile Phone Number"
                      v-model="mobileNumber"
                      :currentLocationId="currentLocationId"
                    />
                  </div>
                  <div>
                    <UIButton
                      @click.prevent="sendTest"
                      :loading="sending"
                    >
                      Send test
                    </UIButton>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Utils } from '../../../util/utils'
import { CustomFields } from '../../../util/custom_fields'
import firebase from 'firebase/app'
import WindowComponent from './WindowComponent.vue'
import { mapState } from 'vuex'
import { UserState, FileAttachment } from '@/store/state_models'
import { User, Template, Link, CustomField, CustomValue } from '@/models'
import Avatar from '../Avatar.vue'
import ImageTools from '../../../util/image_tools'
import { v4 as uuid } from 'uuid'
import { getTagOptions } from '@/util/merge_tags'
import vSelect from 'vue-select'
import lodash from 'lodash'
import { defaultTinyMceOptions } from '../../../util/tiny_mce_defaults'

const Editor = () => import('@tinymce/tinymce-vue')
const PhoneNumber = () => import('../../components/util/PhoneNumber.vue')
const path = require('path')

declare var $: any
const imageTools = new ImageTools()
const triggerLinks: { [key: string]: any }[] = []
let customFieldValues = [] as { [key: string]: any }

export default Vue.extend({
  props: ['values', 'templates', 'isComposingTemplate'],
  components: {
    Avatar,
    editor: Editor,
    PhoneNumber,
    WindowComponent,
    vSelect,
  },
  data() {
    return {
      body: '',
      tinymcehtml: '',
      name: '',
      templateWindow: {} as any,
      template: {} as { [key: string]: any },
      existingFiles: [] as FileAttachment[],
      filesAdded: [] as FileAttachment[],
      currentLocationId: '',
      campaignId: '',
      Utils: Utils,
      saving: false,
      sending: false,
      statusbar: true,
      selectedSMSTemplate: undefined,
      eventType: 'SMS',
      mobileNumber: '',
      faultyURLs: [],
      editorOptions: {
        ...defaultTinyMceOptions,
        selector: 'p',
        format: 'text',
        height: 340,
        plugins: [
          'advlist autolink image lists charmap hr anchor pagebreak spellchecker',
          'searchreplace visualblocks visualchars code fullscreen insertdatetime media nonbreaking',
          'table contextmenu directionality emoticons template textcolor charactercount',
        ],
        toolbar: 'mybutton,templates,triggerlinks ',
        setup: function (editor: any) {
          $('.mce-tinymce').show('fast')
          $(editor.getContainer()).find('.mce-path').css('display', 'none')

          window.tinyMCE.PluginManager.add('charactercount', function (editor) {
            var self = this

            function update() {
              var isSMS = Boolean(
                window.vue && window.vue.eventType !== 'Facebook Message'
              )
              var smsCount = self.getSMSCount()
              var totalCost = 0
              if (
                (window.vue &&
                  window.vue.existingFiles &&
                  window.vue.existingFiles.length > 0) ||
                window.vue.filesAdded.length > 0
              ) {
                totalCost = 0.02
              } else {
                totalCost = smsCount.totalSms * 0.0075
              }

              var hideTwilioCost = window.vue && window.vue.hideTwilioCost

              editor.theme.panel
                .find('#charactercount')
                .text(
                  isSMS && !hideTwilioCost
                    ? [
                        'Characters: {0} (Num. Segments {1}) Approx. Cost ${2}',
                        self.getCount(),
                        smsCount.totalSms,
                        totalCost,
                      ]
                    : ['Characters: {0}', self.getCount()]
                )
            }

            editor.on('init', function () {
              var statusbar =
                editor.theme.panel && editor.theme.panel.find('#statusbar')[0]

              if (statusbar) {
                window.setTimeout(function () {
                  statusbar.insert(
                    {
                      type: 'label',
                      name: 'charactercount',
                      text: ['Characters: {0}', self.getCount()],
                      classes:
                        'charactercount mce-branding mce-widget mce-label mce-flow-layout-item mce-last',
                      disabled: editor.settings.readonly,
                    },
                    0
                  )

                  editor.on('setcontent beforeaddundo execcommand', update)

                  editor.on('keyup', function (e) {
                    update()
                  })
                }, 0)
              }
            })

            self.getSMSCount = function () {
              var sms = SMS()
              var count = {
                totalSms: 0,
              }
              // var decoded = decodeHtml(tx);
              // var decodedStripped = decoded.replace(/(<([^>]+)>)/ig, "").trim();

              var value = editor.getContent({ format: 'raw' })
              if (value) {
                var div = document.createElement('div')
                value = value.replace(new RegExp('<br/>', 'g'), '\n')
                value = value.replace(new RegExp('<br />', 'g'), '\n')

                div.innerHTML = value
                var text = div.textContent || div.innerText || ''
                text = text.replaceAll(String.fromCharCode(160), ' ')

                count = sms.count(text)
              }

              return count
            }

            self.getSegments = function () {
              var count = self.getCount()
              if (count > 0) {
                return Math.floor(count / 160)
              } else {
                return 0
              }
            }

            self.getCount = function () {
              var tx = editor.getContent({ format: 'raw' })
              var decoded = decodeHtml(tx)
              var decodedStripped = decoded.replace(/(<([^>]+)>)/gi, '').trim()
              var tc = decodedStripped.length
              return tc
            }

            function decodeHtml(html) {
              var txt = document.createElement('textarea')
              txt.innerHTML = html
              return txt.value
            }
          })

          editor.addButton('mybutton', {
            type: 'listbox',
            text: 'Custom Values',
            icon: false,
            onselect: function (e: any) {
              editor.insertContent(this.value())
              this.value(null)
            },
            values: customFieldValues,
            onPostRender: function () {
              // Select the second item by default
              this.value('<em>Some italic text!</em>')
            },
          })

          editor.addButton('triggerlinks', {
            type: 'listbox',
            text: 'Trigger Links',
            icon: false,
            onselect: function (e: any) {
              editor.insertContent(this.value())
              this.value(null)
            },
            values: triggerLinks,
            onPostRender: function () {
              // Select the second item by default
              this.value('<em>Some italic text!</em>')
            },
          })

          // editor.on('init', function (e) {
          // 	editor.execCommand("fontName", false, "Verdana");
          // 	editor.execCommand("fontSize", true, "11pt");
          // });

          // editor.addButton('templates', {
          // 	text: 'SMS Templates',
          // 	icon: false,
          // 	tooltip: 'Insert text template',
          // 	onclick: async function () {
          // 		editor.windowManager.open({
          // 			title: 'SMS Templates',
          // 			width: 400,
          // 			height: 100,
          // 			body: [{
          // 				type: 'listbox',
          // 				name: 'listboxName',
          // 				label: 'Select template',
          // 				'values': window.vue.getTemplates(),
          // 			}],

          // 			onsubmit: function (e) {
          // 				let template = lodash.find(window.vue.templates, { id: e.data.listboxName });
          // 				editor.insertContent(template.template.body);
          // 			}
          // 		});
          // 	}
          // });
        },
      },
      editor: {} as any,
      urlAttachment: null,
      urlAttachments: [],
    }
  },
  methods: {
    imageLoadError(url) {
      if (!this.faultyURLs.includes(url)) {
        this.faultyURLs.push(url)
      }
    },
    async addUrlAttachment() {
      const result = await this.$validator.validate('url_attachment.msgsndr8')
      if (!result) {
        return
      }

      this.urlAttachments.push(this.urlAttachment)
      this.urlAttachment = null
    },
    deleteUrlAttachment(url: string) {
      const faultyIndex = this.faultyURLs.indexOf(url)
      if (faultyIndex > -1) this.$delete(this.faultyURLs, faultyIndex)
      this.$delete(this.urlAttachments, this.urlAttachments.indexOf(url))
    },
    async setAsSelectedTemplate(templateid: any) {
      if (templateid) {
        this.selectedSMSTemplate = lodash.find(this.smsTemplates, {
          id: templateid,
        })
      }
    },
    deleteAttachment(name: string) {
      let attachment = lodash.find(this.filesAdded, { name: name })
      if (attachment) {
        this.filesAdded.splice(this.filesAdded.indexOf(attachment), 1)
      }
      attachment = lodash.find(this.existingFiles, { name: name })
      if (attachment) {
        this.existingFiles.splice(this.existingFiles.indexOf(attachment), 1)
      }
      window.tinyMCE.execCommand('mceRepaint')
    },
    resetAttachments() {
      this.$refs.upload.value = ''
    },
    async updateAttachments() {
      const element = <HTMLInputElement>this.$refs.upload
      if (!element.files) return
      for (var i = 0; i < element.files.length; i++) {
        const file = element.files[i]
        const selectedImage = this.filesAdded.filter(
          data => data.name === file.name
        )
        if (selectedImage.length > 0) continue
        let fileType = file.type

        if (fileType == 'text/directory') {
          fileType = 'text/x-vcard'
        }

        if (this.Utils.isSupportedMMSType(fileType) !== true) {
          alert(`Unsupported SMS Content Type ${fileType}`)
          return
        }

        const response = <File | Blob>(
          await imageTools.resize(file, { height: 1000, width: 1000 })
        )
        let url = URL.createObjectURL(response)
        this.filesAdded.push({
          name: file.name,
          type: fileType,
          url: url,
          data: response,
        })
      }
      window.tinyMCE.execCommand('mceRepaint')
    },
    async save() {
      await this.$validator.validateAll()
      if (this.errors.any()) {
        return false
      }
      if (
        this.faultyURLs.length > 0 &&
        !confirm(
          `${this.faultyURLs.length} of the provided URL attachments ${
            this.faultyURLs.length > 1 ? 'are' : 'is'
          } not an image. This may cause errors within Twilio. Do you wish to save anyways?`
        )
      )
        return false

      this.saving = true
      if (this.selectedSMSTemplate) {
        this.template.attributes.template_id = this.selectedSMSTemplate.id
        delete this.template.attributes.body
        if (this.template.attributes.attachments) {
          delete this.template.attributes.attachments
          // don't delete files already uploaded on
          // firebase just in case a campaign already ran
          // and used it.
        }
      } else {
        const urls = this.existingFiles.map(file => file.url)
        let basePath: String
        if (this.campaignId) {
          basePath =
            'location/' +
            this.currentLocationId +
            '/campaign/' +
            this.campaignId +
            '/'
        } else if (this.template.id) {
          basePath =
            'location/' +
            this.currentLocationId +
            '/templates/' +
            this.template.id +
            '/'
        }
        if (this.filesAdded.length > 0) {
          const newURLs: string[] = await Promise.all(
            this.filesAdded.map(async attachment => {
              let imagePath = basePath + uuid() + path.extname(attachment.name)
              var uploadPath = firebase.storage().ref(imagePath)
              const snapshot = await uploadPath.put(attachment.data, {
                contentType: attachment.type,
                contentDisposition: `inline; filename="${attachment.name}"`,
                customMetadata: { name: attachment.name },
              })
              return await snapshot.ref.getDownloadURL()
            })
          )
          urls.push.apply(
            urls,
            newURLs.filter(url => url)
          )
        }
        delete this.template.attributes.template_id
        this.template.attributes.body = this.body
        this.template.attributes.attachments = urls
      }

      Vue.set(this.template, 'window', this.templateWindow)
      this.template.name = this.name
      this.template.urlAttachments = this.urlAttachments
      this.$emit('save')
      this.$emit('hidden')
    },
    async sendTest() {
      if (!this.mobileNumber) {
        this.$uxMessage(
          'warning',
          'Please provide a phone number to send the test to.'
        )
        return
      }
      this.sending = true
      const data: { [key: string]: any } = {
        location_id: this.currentLocationId,
        phone: this.mobileNumber,
        message: this.body,
        user_id: this.user ? this.user.id : null,
        type: 'sms',
      }

      if (this.selectedSMSTemplate) {
        // add body message when a sms template is selected
        data.message = this.selectedSMSTemplate.value
      }

      if (this.attachments.length > 0) {
        const newURLs: string[] = await Promise.all(
          this.attachments.map(async attachment => {
            if (attachment.data) {
              let imagePath =
                'location/' + this.currentLocationId + '/preview/' + uuid()
              var uploadPath = firebase.storage().ref(imagePath)

              const snapshot = await uploadPath.put(attachment.data, {
                contentType: attachment.type,
                contentDisposition: `inline; filename="${attachment.name}"`,
                customMetadata: { name: attachment.name },
              })
              return await snapshot.ref.getDownloadURL()
            } else if (attachment.url) {
              return attachment.url
            }
          })
        )
        data['attachments'] = newURLs
      }
      if (this.urlAttachments.length > 0) {
        data['url_attachments'] = this.urlAttachments
      }

      try {
        let response = await this.$http.post('/message/preview', data)
      } catch (err) {
        if (err.response && err.response.status === 400) {
          alert(err.response.data.message)
        }
        console.error(err)
      }
      this.sending = false
    },
    getTemplates() {
      let values = []

      if (this.templates && this.templates.length > 0) {
        for (let template of this.templates) {
          if (template.type === Template.TYPE_SMS) {
            values.push({ text: template.name, value: template.id })
          }
        }
      }
      return values
    },
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    attachments(): FileAttachment[] {
      if (this.selectedSMSTemplate) {
        return this.selectedSMSTemplate.attachments || []
      }
      return [...this.filesAdded, ...this.existingFiles]
    },
    allAttachments() {
      const attachmentURLs = []
      if (this.attachments.length > 0)
        attachmentURLs.push(
          ...this.attachments.filter(attachment =>
            Utils.isImage(attachment.type)
          )
        )
      if (this.urlAttachments.length > 0)
        attachmentURLs.push(
          ...this.urlAttachments.map(url => {
            return { url, type: 'url' }
          })
        )
      return attachmentURLs
    },
    smsTemplates(): [] {
      let temp = []
      this.templates.forEach(template => {
        if (template.type === Template.TYPE_SMS) {
          temp.push({
            text: template.name,
            value: template.template.body,
            id: template.id,
            type: 'tinymce',
            attachments: undefined,
            attachurls: template.template.attachments, //these are the urls stored in templates collection
            urlAttachments: template.urlAttachments,
          })
        }
      })
      return temp
    },
    hideTwilioCost(): boolean {
      const rebillingEnabled = this.$store.getters['locations/twilioRebillingEnabledForCurrentLocation']
      return rebillingEnabled;
    }
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  watch: {
    selectedSMSTemplate: function (newVal, oldVal) {
      if (this.existingFiles) this.existingFiles.length = 0
      if (this.filesAdded) this.filesAdded.length = 0
      this.tinymcehtml = ''
      if (newVal) {
        if (newVal && newVal.attachments) {
          newVal.attachments.forEach(x => {
            this.existingFiles.push(x)
          })
        } else if (newVal && !newVal.attachments && newVal.attachurls) {
          newVal.attachments = []
          let itr = newVal.attachurls.map(url => ({
            url: url,
            template: newVal,
          }))
          itr.forEach(async item => {
            const metadata = await firebase
              .storage()
              .refFromURL(item.url)
              .getMetadata()
            let obj = {
              type: metadata.contentType,
              name: metadata.customMetadata.name,
              url: item.url,
            }
            if (!item.template.attachments) item.template.attachments = []
            item.template.attachments.push(obj)
            if (
              this.selectedSMSTemplate &&
              this.selectedSMSTemplate.id === item.template.id
            )
              this.existingFiles.push(obj)
          })
        }
        this.urlAttachments = newVal.urlAttachments || []
      }
    },
    async values(values: { [key: string]: any }) {
      const data: () => object = <() => object>this.$options.data
      if (data) Object.assign(this.$data, data.apply(this))
      if (values.visible) $(this.$refs.modal).modal('show')
      else {
        this.errors.clear()
        $(this.$refs.modal).modal('hide')
      }
      this.template = values.template
      this.currentLocationId = values.currentLocationId
      this.campaignId = values.campaignId
      this.eventType = values.eventType
      if (values.currentLocationId) {
        Link.fetchAllByLocation(values.currentLocationId)
          .get()
          .then(snapshot => {
            const links = snapshot.docs
              .map((d: any) => new Link(d))
              .map(l => {
                return {
                  text: l.name,
                  value: `{{trigger_link.${l.id}}}`,
                }
              })
            triggerLinks.length = 0
            triggerLinks.push.apply(triggerLinks, links)
          })
        customFieldValues.length = 0
        customFieldValues.push.apply(customFieldValues, await getTagOptions())
        CustomField.getByLocationIdAndType(
          this.currentLocationId,
          'contact'
        ).then(fields => {
          const availableFields = fields.map(field => {
            return { text: field.name, value: `{{${field.fieldKey}}}` }
          })
          if (availableFields.length > 0) {
            const contactMergeTags = lodash.find(customFieldValues, {
              text: 'Contact',
            })
            contactMergeTags.menu.push({
              text: 'Custom Fields',
              menu: lodash.orderBy(
                availableFields,
                [value => value.text.toLowerCase()],
                ['asc']
              ),
            })
          }
        })
        CustomValue.getByLocationId(this.currentLocationId).then(values => {
          const availableValues = values.map(value => {
            return {
              text: value.name,
              value: `{{custom_values.${value.fieldKey}}}`,
            }
          })

          if (availableValues.length > 0) {
            customFieldValues.push({
              text: 'Custom Values',
              menu: lodash.orderBy(
                availableValues,
                [value => value.text.toLowerCase()],
                ['asc']
              ),
            })
          }
        })
      }

      if (values.template) {
        this.name = this.template.name
        if (
          this.template.urlAttachments &&
          this.template.urlAttachments.length > 0
        )
          this.urlAttachments = this.template.urlAttachments
        if (this.template && this.template.attributes.attachments) {
          lodash.each(this.template.attributes.attachments, async url => {
            const metadata = await firebase
              .storage()
              .refFromURL(url)
              .getMetadata()
            this.existingFiles.push({
              type: metadata.contentType,
              name: metadata.customMetadata.name,
              url,
            })
            window.tinyMCE.execCommand('mceRepaint')
          })
        }

        if (values.template.window) {
          this.templateWindow = lodash.clone(values.template.window)
        }

        if (
          values.template.attributes &&
          values.template.attributes.template_id &&
          this.smsTemplates
        ) {
          this.setAsSelectedTemplate(values.template.attributes.template_id)
        } else if (
          values.template.attributes &&
          values.template.attributes.body
        ) {
          this.body = this.template.attributes.body || ''
          let bodyText = this.template.attributes.body || ''
          this.tinymcehtml = bodyText.replace(/\n/g, '<br/>')
        }

        //tinymce.EditorManager.execCommand('mceRemoveControl', true, this.$refs.editsmseditor.editor);
        //tinymce.EditorManager.execCommand('mceAddControl', true, this.$refs.editsmseditor.editor);
      }
    },
    tinymcehtml(value: string) {
      var div = document.createElement('div')
      value = value.replace(new RegExp('<br/>', 'g'), '\n')
      value = value.replace(new RegExp('<br />', 'g'), '\n')

      div.innerHTML = value
      var text = div.textContent || div.innerText || ''
      text = text.replaceAll(String.fromCharCode(160), ' ') // replace &nbsp - this is ucs-2 char, and increases segment number

      this.body = text
    },
  },
  async created() {
    window.vue = this

    // "SMS count" JavaScript library v1.0.0
    // (c) Lizurchik Alexey - https://github.com/likerRr/smscountjs
    // License: MIT (http://www.opensource.org/licenses/mit-license.php)
    ;(function (window) {
      /**
       * (0, eval)('this')
       * @see http://stackoverflow.com/questions/14119988/return-this-0-evalthis/14120023#14120023
       */
      window = this || (0, eval)('this')
      /**
       * Object to work with sms count and characters left to next sms
       * @constructor
       * @param {object} [options]
       * @param {number} [options._7bit]
       * @param {number} [options._8bit]
       * @param {number} [options._16bit]
       * @returns {SMS|window.SMS}
       */
      window.SMS = function (options) {
        /**
         * Create instance anyway
         */
        if (this instanceof SMS === false) {
          return new SMS(options)
        }

        var self = this,
          totalSms = 0,
          charsLeft = 0,
          parts = [],
          // Default 7-bit GSM char set
          // http://en.wikipedia.org/wiki/GSM_03.38#GSM_7_bit_default_alphabet_and_extension_table_of_3GPP_TS_23.038_.2F_GSM_03.38
          _7bit = /^[@£\$¥èéùìòÇ\nØø\rÅåΔ_ΦΓΛΩΠΨΣΘΞÆæßÉ !\"#¤%&'\(\)\*\+,-\.\/0123456789:;<=>\?¡ABCDEFGHIJKLMNOPQRSTUVWXYZÄÖÑÜ§¿abcdefghijklmnopqrstuvwxyzäöñüà\f\^\{\}\\\[~\]|€]*$/g,
          // 7-bit mask + 8-bit mask
          _8bit = /^[ÀÂâçÈÊêËëÎîÏïÔôŒœÙÛû«»₣„“”°@£\$¥èéùìòÇ\nØø\rÅåΔ_ΦΓΛΩΠΨΣΘΞÆæßÉ !\"#¤%&'\(\)\*\+,-\.\/0123456789:;<=>\?¡ABCDEFGHIJKLMNOPQRSTUVWXYZÄÖÑÜ§¿abcdefghijklmnopqrstuvwxyzäöñüà\f\^\{\}\\\[~\]|€]*$/g,
          limit

        var sliceStr = function (str, limit) {
          var end = str.slice(limit),
            begin = str.slice(0, limit)

          if (begin.length > 0) {
            parts.push(str.slice(0, limit))
          }
          if (end.length > 0) {
            sliceStr(end, limit)
          }
        }

        /**
         * Limits to _7bit, _8bit & _16bit chars count
         * @type {{_7bit: number, _8bit: number, _16bit: number}}
         */
        self.options = {
          // latin as usual
          _7bit: 160,
          // french, german (specific symbols, not in utf-8)
          _8bit: 140,
          // cyrillic and other
          _16bit: 67,
        }

        limit = self.options._7bit

        // extend options if passed
        if (options !== 'undefined' && typeof options == 'object') {
          for (var p in options) {
            if (options.hasOwnProperty(p)) {
              self.options[p] = options[p]
            }
          }
        }

        /**
         * Calculate sms count, characters left to next sms and call callback
         * function with 4 params: totalSms, charsLeft, parts, limit
         * @param text - source string
         * @param [cb] - callback(totalSms, charsLeft, parts, limit)
         * @return {SMS|window.SMS|{totalSms: number, charsLeft: number, parts: Array, limit: number}}
         */
        self.count = function (text, cb) {
          var is7bit = _7bit.test(text),
            is8bit = _8bit.test(text),
            textLen = text.length

          limit = is7bit
            ? self.options['_7bit']
            : is8bit
            ? self.options['_8bit']
            : self.options['_16bit']
          parts = []

          totalSms = Math.ceil(textLen / limit)
          charsLeft = limit - (textLen % limit)
          sliceStr(text, limit)

          if (typeof cb == 'function') {
            cb(totalSms, charsLeft, parts, limit)
          } else if (cb === undefined) {
            return {
              totalSms: totalSms,
              charsLeft: charsLeft,
              parts: parts,
              limit: limit,
            }
          }

          return this
        }

        /**
         * Returns characters left to next sms
         * @returns {number}
         */
        self.charsLeft = function () {
          return charsLeft
        }

        /**
         * Returns total sms count
         * @returns {number}
         */
        self.total = function () {
          return totalSms
        }

        /**
         * Returns current limit according to charset
         * @returns {number}
         */
        self.limit = function () {
          return limit
        }

        /**
         * Returns message parts, separated by `limit` count
         * @returns {Array}
         */
        self.parts = function () {
          return parts
        }

        return self
      }

      /**
       * Calculate and pass sms count, characters left to next sms, source in callback. If no options passed,
       * callback will be called as first parameter
       * @param {object} [options]
       * @param {number} options._16bit
       * @param {number} options._7bit
       * @param [cb] - will call after counting with 4 params: totalSms, charsLeft, parts, limit
       * @returns {String}
       */
      String.prototype.smsCount = function (options, cb) {
        var sms,
          str = this.concat()

        if (arguments.length == 0) {
          return this.concat()
        }

        if (arguments.length === 1) {
          sms = new SMS()
          sms.count(str, options)
        } else {
          sms = new SMS(options)
          sms.count(str, cb)
        }

        return this.concat()
      }
    })(window)
    /**
     * Pass window as parameter is a hack for allowing autocomplete
     */
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function () {
      _self.$emit('hidden')
    })

    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
})
</script>
<style>
/* Optional: Adjust the positioning of the character count text. */
.mce-charactercount {
  margin: 2px 0 2px 2px !important;
  padding: 8px !important;
  font-size: 11px !important;
}

/* Optional: Remove the html path code from the status bar. */
.mce-path {
  display: none !important;
}

.add-urlattachment-icon {
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 36px;
  height: 30px;
  border-radius: 50%;
  background-color: #37ca37;
  color: #fff;
  margin-left: 8px;
  position: relative;
  z-index: 2;
  margin-right: 40px;
}

.red-text {
  color: red;
}
</style>
