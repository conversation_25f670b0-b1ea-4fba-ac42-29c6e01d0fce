<template>
    <div class="ps-content col-xs-9">
        <div>
            <div autocomplete="false">
                <div class="step-content type-step no-text-select">
                    <div class="step-panel" v-on:click="$emit('choose-type', 'stream')">
                        <div class="title">Stream</div>
                        <div class="section">
                            <div>What's better than putting a face on that social proof? Show your recent customers and their actions.</div>
                            <div class="notification-preview-container">
                                <PsBubble :notification="{type:'stream'}" />
                            </div>
                        </div>
                    </div>
                    <div class="step-panel" v-on:click="$emit('choose-type', 'visitors')">
                        <div class="title">Live Visitors<div class="badge badge-success mt-1 float-right">Beta</div>
                        </div>
                        <div class="section">
                            <div>Show how many visitors are currently viewing your entire site in real-time.</div>
                            <div class="notification-preview-container notification-live-type">
                                <PsBubble :notification="{type:'visitors'}" />
                            </div>
                        </div>
                    </div>
                    <div class="step-panel" v-on:click="$emit('choose-type', 'page_visits')">
                        <div class="title">Page Visits</div>
                        <div class="section">
                            <div>Show your visitors they aren't the only ones at the party by highlighting recent page visits.</div>
                            <div class="notification-preview-container">
                                <PsBubble :notification="{type:'page_visits'}" />
                            </div>
                        </div>
                    </div>
                    <div class="step-panel" v-on:click="$emit('choose-type', 'conversions')">
                        <div class="title">Conversions </div>
                        <div class="section">
                            <div>Create social validation by displaying how many customers did a specific action.</div>
                            <div class="notification-preview-container">
                                <PsBubble :notification="{type:'conversions'}" />
                            </div>
                        </div>
                    </div>
                    <div class="step-panel" v-on:click="$emit('choose-type', 'combo')">
                        <div class="title">Combo</div>
                        <div class="section">
                            <div>Show the total number of people who did a specific action over 24 hours, 7 or 30 days.</div>
                            <div class="notification-preview-container">
                                <PsBubble :notification="{type:'combo'}" />
                            </div>
                        </div>
                    </div>
                    <div class="step-panel" v-on:click="$emit('choose-type', 'review')">
                        <div class="title">Live Reviews</div>
                        <div class="section">
                            <div>Show your visitors real time 4+ star reviews from the last 6 months.</div>
                            <div class="notification-preview-container">
                                <PsBubble :notification="{type:'review'}" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script lang='ts'>
import Vue from "vue";

import { NotificationTemplate } from "@/models";

import PsBubble from "./psbubble.vue";


declare var $: any;

export default Vue.extend({
    components: {
        PsBubble
    },
    props: {
        notificationId: String
    },
    data() {
        return {
            notification: {} as NotificationTemplate
        };
    },
    async created() {
        this.fetchData();
    },
    watch: {
        'notificationId': function (id) {
            this.fetchData();
        },
    },
    methods: {
        async fetchData() {
            this.notification = await NotificationTemplate.getById(this.notificationId);
        },
    }
})
</script>
