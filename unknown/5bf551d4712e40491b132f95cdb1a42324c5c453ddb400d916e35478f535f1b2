<template>
	<!-- Edit Event Modal -->
	<div
		class="modal fade hl_edit-event-modal"
		id="edit-event-modal"
		tabindex="-1"
		role="dialog"
		ref="modal"
	>
		<div class="modal-dialog --mid" role="document">
			<div class="modal-content">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
				<div class="modal-body">
					<div class="modal-body-inner">
						<div class="hl_edit-event-modal-wrap">
							<div class="hl_edit-event-modal-main">
								<div class="card">
									<div class="card-header --no-right-padding">
										<h2>Edit Call</h2>
									</div>
									<div class="card-body hl_campaign-configuration">
										<div class="form-group" >
											<label for="whisperMessage">Event name</label>
											<div class="form-group"  style="display:block;">
												<UITextInputGroup
													type="text"
													class="msgsndr2"
													placeholder="Name"
													v-model="name"
													v-validate="'required'"
													name="msgsndr2"
													data-lpignore="true"
													autocomplete="msgsndr2"
													:error="errors.has('msgsndr2')"
													:errorMsg="'Name is required.'"
												/>
											</div>
										</div>
										<div class="form-group">
											<label for="whisperMessage">Call whisper</label>
											<div style="position:relative; width: 100%;">
												<NestedMenu
												class="my-2 merge-fields-call-modal call-whisper-custom-value"
												v-show="menuItems && menuItems.length > 0"
												:items="menuItems"
												:title="'Merge fields'"
												v-on:MenuSelected="menuItemSelected"
												/>
												<UITextAreaGroup
												placeholder="Whisper message"
												v-model="whisperMessage"
												name="whisperMessage"
												type="text"
												/>
											</div>
											<span
												style="margin-left: 5px;"
												class="input-group-addon"
												v-b-tooltip.hover
												title="This is the message you want the person who picks up the phone to hear, you can use custom variables to customize this."
											>
												<i class="fas fa-question-circle"></i>
											</span>
										</div>
										<div class="form-group">
											<label for="timeout">Call timeout (s)</label>
											<UITextInputGroup
												placeholder="Timeout (seconds)"
												v-model.number="timeout"
												name="timeout"
												type="number"
												id="timeout"
											/>
										</div>
										<div class="form-group">
											<label>Disable voicemail detect</label>
											<div>
												<UIToggle
												id="voicemail"
												v-model="disableDetectVoicemail"
												/>
												<label class="tgl-btn" for="voicemail"></label>
											</div>
											<span
												style="margin-left: 5px;"
												class="input-group-addon"
												v-b-tooltip.hover
												title="Call will patch instantly, we will not listen to see if the voicemail picked up on the leads side. This means the camapign will be marked as replied even if voicemail picked up."
											>
												<i class="fas fa-question-circle"></i>
											</span>
										</div>
										<WindowComponent v-model="templateWindow" parent="call" />
									</div>
									<div class="card-footer">
										<div class="modal-footer-left"></div>
										<div class="modal-footer-right">
											<div style="display: inline-block;position: relative;">
												<UIButton
													@click.prevent="save"
													:loading="saving"
												>Save</UIButton>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
import { CampaignTemplate, CallTemplate, ActionCondition, Window } from '@/models';
import { CustomFields } from '../../../util/custom_fields'
import NestedMenu from '@/pmd/components/NestedMenu.vue'
const WindowComponent = () => import('./WindowComponent.vue');

declare var $: any;
const menuItems = [] as any[]
type EditCallEventModalValues = {
	visible: boolean;
	template: CampaignTemplate<CallTemplate>;
	currentLocationId: string;
	campaignId: string;
}
export default Vue.extend({
	props: {		values: {
			type: Object as () => EditCallEventModalValues
		}	},
	components: {
    WindowComponent,
    NestedMenu
	},
	data() {
		return {
			templateWindow: {} as any,
			timeout: 0 as number | undefined,
			template: {} as CampaignTemplate<CallTemplate>,
			currentLocationId: "",
			campaignId: "",
			saving: false,
			whisperMessage: "" as string | undefined,
			disableDetectVoicemail: false,
			menuItems,
			name: ''
		}
	},
	methods: {
    async loadCustomFieldItems() {
      if(!this.currentLocationId) return;
      try {
        menuItems.push.apply(
          menuItems,
          await CustomFields.getList(this.currentLocationId, true)
        )
      } catch (err) {
        console.log(err)
      }
    },
    menuItemSelected(selectedMergeTag: string) {
      if (selectedMergeTag) {
        this.whisperMessage = this.whisperMessage ? `${this.whisperMessage.trim()} ${selectedMergeTag}` : selectedMergeTag
      }
    },
		async save() {
			console.log("Saving...");
			this.saving = true;
			const result = await this.$validator.validateAll()
			if (!result) {
				this.saving = false;
				return false
			}

			if (this.timeout) this.template.attributes.timeout = this.timeout; else if (this.template.attributes.timeout) delete this.template.attributes.timeout;
			this.template.attributes.disable_detect_voicemail = this.disableDetectVoicemail;
			if (this.whisperMessage) this.template.attributes.whisper_message = this.whisperMessage; else if (this.template.attributes.whisper_message) delete this.template.attributes.whisper_message;
			Vue.set(this.template, 'window', this.templateWindow);
			this.template.name = this.name
			this.$emit('save');
			this.$emit('hidden');
		}
	},
	beforeDestroy() {
		$(this.$refs.modal).off('hidden.bs.modal');
	},
	watch: {
		values(values: EditCallEventModalValues) {
			const data: (() => object) = <(() => object)>this.$options.data;
			if (data) Object.assign(this.$data, data.apply(this));
			if (values.visible) $(this.$refs.modal).modal('show');
			else $(this.$refs.modal).modal('hide');
			this.template = values.template;
			this.currentLocationId = values.currentLocationId;
      this.campaignId = values.campaignId;
      this.loadCustomFieldItems();
			if (values.template) {
				const template = values.template;
				this.timeout = this.template.attributes.timeout;
        		this.name = this.template.name
				if (template.window) {
					this.templateWindow = lodash.clone(template.window);
				}
        this.whisperMessage = this.template.attributes.whisper_message;
        this.disableDetectVoicemail = this.template.attributes.disable_detect_voicemail || false;
			}
		}
	},
	updated() {
		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker('refresh');
		}

		if (this.values && this.values.visible) {
			$(this.$refs.modal).modal('show');
		}
	},
	mounted() {
		const _self = this;
		$(this.$refs.modal).on('hidden.bs.modal', function () {
			_self.$emit('hidden');
		});

		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker();
		}

		if (this.values && this.values.visible) {
			$(this.$refs.modal).modal('show');
		}
	},
})
</script>

