<template>
  <!-- Edit Event Modal -->
  <div
    class="modal fade hl_edit-event-modal"
    id="edit-event-modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog --mid" role="document">
      <div class="modal-content">
        <button
          type="button"
          class="close"
          data-dismiss="modal"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
        <div class="modal-body">
          <div class="modal-body-inner">
            <div class="hl_edit-event-modal-wrap">
              <div class="hl_edit-event-modal-main">
                <div class="card">
                  <div class="card-header --no-right-padding">
                    <h2>Edit Voicemail</h2>
                  </div>
                  <div
                    class="card-body hl_campaign-configuration edit-voicemail"
                  >
                    <div class="form-group">
                      <label for="timeout">Voicemail file</label>
                      <div>
                        <vue2Dropzone
                          v-if="!audioFile"
                          @vdropzone-file-added="onFileChange"
                          :options="dropzoneOptions1"
                          :includeStyling="false"
                          id="dropZone1"
                          ref="dropZone1"
                          :useCustomSlot="true"
                        >
                          <div class="audio-upload" id="import-file">
                            <div class="message">
                              Drop file here or click to upload
                            </div>
                            <div
                              id="preview_container1"
                              style="display:none;"
                            ></div>
                          </div>
                          <div v-show="fileError" class="--red">
                            {{ fileError }}
                          </div>
                        </vue2Dropzone>
                        <vue2Dropzone
                          v-else
                          @vdropzone-file-added="onFileChange"
                          :options="dropzoneOptions2"
                          :includeStyling="false"
                          id="dropZone2"
                          ref="dropZone2"
                          :useCustomSlot="true"
                        >
                          <audio-player
                            :file="audioFile"
                            @remove="audioFile = ''"
                            :allowDelete="true"
                          ></audio-player>
                          <div
                            id="preview_container2"
                            style="display:none;"
                          ></div>
                          <div v-show="fileError" class="--red">
                            {{ fileError }}
                          </div>
                        </vue2Dropzone>
                      </div>
                    </div>
                    <WindowComponent
                      v-model="templateWindow"
                      parent="voicemail"
                    />
                  </div>
                  <div class="card-footer">
                    <div class="modal-footer-left">
                      <div
                        class="voicemail-drop_warning-message hl_help-article"
                      >
                        <span
                          >Warning: Voicemail drops are a best attempt system
                          and work roughly 70% of the time.</span
                        >
                        <br />
                        <span class="voicemail-drop_warning-message-faq"
                          >If you have additional questions please see the
                          Voicemail Drop FAQ in the help section.</span
                        >
                      </div>
                    </div>
                    <div class="modal-footer-right">
                      <div style="display: inline-block;position: relative;">
                        <button
                          :class="{ invisible: saving }"
                          type="button"
                          class="btn btn-primary"
                          @click.prevent="save"
                        >
                          Save
                        </button>
                        <div
                          style="position: absolute;top: 21%;left: 33%;"
                          v-show="saving"
                        >
                          <moon-loader
                            :loading="saving"
                            color="#188bf6"
                            size="30px"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import firebase from 'firebase/app'
import Vue from 'vue'

import {
  CampaignTemplate,
  VoicemailTemplate,
  ActionCondition,
  Window
} from '@/models'
import { v4 as uuid } from 'uuid'
import vue2Dropzone from 'vue2-dropzone'

const AudioPlayer = () => import('../AudioPlayer.vue')
const WindowComponent = () => import('./WindowComponent.vue')
declare var $: any

type EditCallEventModalValues = {
  visible: boolean
  template: CampaignTemplate<VoicemailTemplate>
  currentLocationId: string
  campaignId: string
}
export default Vue.extend({
  props: {
    values: {
      type: Object as () => EditCallEventModalValues
    }
  },
  components: {
    AudioPlayer,
    vue2Dropzone,
    WindowComponent
  },
  data() {
    return {
      existingFileURL: undefined as string | undefined,
      fileAddedURL: undefined as string | undefined,
      fileAdded: undefined as File | undefined,
      template: {} as CampaignTemplate<VoicemailTemplate>,
      templateWindow: {} as any,
      currentLocationId: '',
      campaignId: '',
      saving: false,
      dropzoneOptions1: {
        url: '/file/post',
        clickable: '#import-file',
        previewsContainer: '#preview_container1',
        addRemoveLinks: false,
        createImageThumbnails: false
      },
      dropzoneOptions2: {
        url: '/file/post',
        previewsContainer: '#preview_container2',
        addRemoveLinks: false,
        createImageThumbnails: false
      },
      fileError: '',
      supportedTypes: [
        'audio/mp3',
        'audio/mpeg',
        'audio/wav',
        'audio/wave',
        'audio/x-wav',
        'audio/aiff',
        'audio/x-aifc',
        'audio/x-aiff',
        'audio/x-gsm',
        'audio/gsm',
        'audio/ulaw'
      ]
    }
  },
  methods: {
    onFileChange(file: File) {
      if (this.$refs.dropZone1) this.$refs.dropZone1.removeAllFiles()
      if (this.$refs.dropZone2) this.$refs.dropZone2.removeAllFiles()
      this.fileError = ''
      if (this.supportedTypes.indexOf(file.type) === -1) {
        this.fileError =
          'Select a mp3 or wav file, we do not support other formats.'
        return
      }
      this.fileAdded = file
      this.fileAddedURL = URL.createObjectURL(file)
      this.existingFileURL = undefined
    },
    async save() {
      this.fileError = ''
      this.saving = true
      if (this.template.attributes.file_url && !this.existingFileURL) {
        delete this.template.attributes.file_url
      }
      if (this.fileAdded) {
        let audioPath =
          'location/' +
          this.currentLocationId +
          '/campaign/' +
          this.campaignId +
          '/' +
          uuid()
        const snapshot = await firebase
          .storage()
          .ref(audioPath)
          .put(this.fileAdded, {
            contentType: this.fileAdded.type,
            contentDisposition: `inline; filename="${this.fileAdded.name}"`,
            customMetadata: { name: this.fileAdded.name }
          })
        this.template.attributes.file_url = await snapshot.ref.getDownloadURL()
      }
      Vue.set(this.template, 'window', this.templateWindow)
      this.$emit('save')
      this.$emit('hidden')
    }
  },
  computed: {
    audioFile: {
      get: function(): string | undefined {
        return this.fileAddedURL || this.existingFileURL
      },
      set: function(value: string) {
        this.fileAdded = undefined
        this.fileAddedURL = undefined
        this.existingFileURL = undefined
      }
    }
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  watch: {
    values(values: EditCallEventModalValues) {
      const data: () => object = <() => object>this.$options.data
      if (data) Object.assign(this.$data, data.apply(this))
      if (values.visible) $(this.$refs.modal).modal('show')
      else $(this.$refs.modal).modal('hide')
      this.template = values.template
      this.currentLocationId = values.currentLocationId
      this.campaignId = values.campaignId
      if (values.template) {
        const template = values.template
        this.existingFileURL = this.template.attributes.file_url
        if (template.window) {
          this.templateWindow = lodash.clone(template.window)
        }
      }
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hidden')
    })

    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  }
})
</script>
