<template>
  <div
    class="tab-pane fade show active"
    id="stats-tab"
    role="tabpanel"
    aria-labelledby="stats-tab"
  >
    <div class="hl_controls">
      <div class="hl_controls--left">
        <!-- <h2>Stats</h2> -->
      </div>
      <div class="hl_controls--right">
        <div class="control">
          <vue-ctk-date-time-picker
            v-model="statsFilterDate"
            :range="true"
            color="#188bf6"
            :only-date="true"
            enable-button-validate
            :noClearButton="true"
            :formatted="getCountryDateFormat(false)"
            @validate="fetchFunnelStats"
            id="funnelStatsDatePicker"
          />
        </div>
        <div class="control">
          <b-button
            variant="light"
            @click="showDeleteModal = true"
            class="reset-btn"
          >
            <span>Reset</span>
            <i class="fas fa-sync-alt"></i>
          </b-button>
        </div>
      </div>
    </div>

    <div class="hl_stats--filter" id="hl_stats--filter" style="display: none">
      <div class="row">
        <div class="col-12 col-md">
          <div class="form-group">
            <label>UTM Content</label>
            <input
              type="text"
              class="form-control form-light form-comtrol-md"
              placeholder="UTM Content"
            />
          </div>
        </div>
        <div class="col-12 col-md">
          <div class="form-group">
            <label>UTM Medium</label>
            <input
              type="text"
              class="form-control form-light form-comtrol-md"
              placeholder="UTM Medium"
            />
          </div>
        </div>
        <div class="col-12 col-md">
          <div class="form-group">
            <label>UTM Name</label>
            <input
              type="text"
              class="form-control form-light form-comtrol-md"
              placeholder="UTM Name"
            />
          </div>
        </div>
        <div class="col-12 col-md">
          <div class="form-group">
            <label>UTM Source</label>
            <input
              type="text"
              class="form-control form-light form-comtrol-md"
              placeholder="UTM Source"
            />
          </div>
        </div>
        <div class="col-12 col-md">
          <div class="form-group">
            <label>UTM Term</label>
            <input
              type="text"
              class="form-control form-light form-comtrol-md"
              placeholder="UTM Term"
            />
          </div>
        </div>
      </div>
      <div class="row">
        <div class="col-12 col-md-3">
          <div class="form-group">
            <label>Affiliate ID</label>
            <input
              type="text"
              class="form-control form-light form-comtrol-md"
              placeholder="Affiliate ID"
            />
          </div>
        </div>
        <div class="col-12 col-md-3">
          <div class="form-group">
            <label>Affiliate Sub</label>
            <input
              type="text"
              class="form-control form-light form-comtrol-md"
              placeholder="Affiliate Sub"
            />
          </div>
        </div>
        <div class="col-12 col-md-3">
          <div class="form-group">
            <label>Affiliate Sub 2</label>
            <input
              type="text"
              class="form-control form-light form-comtrol-md"
              placeholder="Affiliate Sub 2"
            />
          </div>
        </div>
        <div class="col-12 col-md-3">
          <div class="form-group">
            <label>Affiliate Sub 3</label>
            <input
              type="text"
              class="form-control form-light form-comtrol-md"
              placeholder="Affiliate Sub 3"
            />
          </div>
        </div>
        <div class="col-12 col-md-3">
          <div class="form-group">
            <label>Start</label>
            <input
              type="date"
              class="form-control form-light form-comtrol-md"
              placeholder="Start"
            />
          </div>
        </div>
        <div class="col-12 col-md-3">
          <div class="form-group">
            <label>End</label>
            <input
              type="date"
              class="form-control form-light form-comtrol-md"
              placeholder="End"
            />
          </div>
        </div>
        <div class="col-12 col-md-3">
          <div class="form-group">
            <label>Device Category</label>
            <select class="selectpicker --white --small">
              <option>All</option>
              <option>Desktop</option>
              <option>Mobile</option>
            </select>
          </div>
        </div>
        <div class="col-12 col-md-3">
          <div class="form-group">
            <label>Costs</label>
            <input
              type="number"
              class="form-control form-light form-comtrol-md"
              placeholder="End"
            />
          </div>
        </div>
      </div>
      <div class="filter-buttons">
        <button type="button" class="btn btn-success">
          <i class="icon icon-ok"></i> Apply Filter
        </button>
        <button type="button" class="btn btn-light">Clear</button>
      </div>
    </div>

    <div
      class="hl_funnels--stats"
      v-if="!fetchingFunnelStats && !errorInFetchingStats"
    >
      <div class="card-group stats--infos --wide-gutter" v-if="false">
        <div class="card">
          <div class="card-header">
            <h2>Earning Per Click</h2>
          </div>
          <div class="card-body">
            <h3>$26.00</h3>
          </div>
        </div>
        <div class="card">
          <div class="card-header">
            <h2>Gross</h2>
          </div>
          <div class="card-body">
            <h3>$2,596.00</h3>
          </div>
        </div>
        <div class="card">
          <div class="card-header">
            <h2>Average Card Value</h2>
          </div>
          <div class="card-body">
            <h3>$62.00</h3>
          </div>
        </div>
      </div>
      <div class="card" v-if="stepStats.length">
        <div class="card-body --no-padding">
          <div class="table-wrap">
            <table class="table stats--table">
              <!-- <thead v-if="isWebsite">
                <tr>
                  <th>
                  </th>
                  <th colspan="2" class="table-blue">Page Views</th>
                  <th colspan="2" class="table-blue2">Opt-Ins</th>
                </tr>
                <tr>
                  <th>
                  </th>
                  <th class="table-blue">All</th>
                  <th class="table-blue">Uniques</th>
                  <th class="table-blue2">All</th>
                  <th class="table-blue2">Rate</th>
                </tr>
              </thead> -->
              <thead>
                <tr>
                  <th>
                    <!-- Leave Blank -->
                  </th>
                  <th colspan="2" class="table-blue">Page Views</th>
                  <th colspan="2" class="table-blue2">Opt-Ins</th>
                  <th colspan="3" class="table-green">Sales</th>
                  <th colspan="2" class="table-teal">Earnings / Pageview</th>
                </tr>
                <tr>
                  <th>
                    <!-- Leave Blank -->
                  </th>
                  <th class="table-blue">All</th>
                  <th class="table-blue">Uniques</th>
                  <th class="table-blue2">All</th>
                  <th class="table-blue2">Rate</th>
                  <th class="table-green">Count</th>
                  <th class="table-green">Rate</th>
                  <th class="table-green">Value</th>
                  <th class="table-teal">All</th>
                  <th class="table-teal">Uniques</th>
                </tr>
              </thead>
              <funnel-step-stats
                v-for="stat in stepStats"
                :key="stat.id"
                :funnelStepStat="stat"
                :is-website="isWebsite"
              />
            </table>
          </div>
        </div>
      </div>
      <div v-else>
        Looks like we don't have any stats yet! Try adjusting the filters or
        Please check back later.
      </div>
    </div>
    <div v-if="fetchingFunnelStats">
      <div style="padding: 20px">
        <moon-loader
          :loading="fetchingFunnelStats"
          color="#188bf6"
          size="30px"
        />
      </div>
    </div>
    <div v-if="errorInFetchingStats" @click="fetchFunnelStats">
      Aw, Snap! Looks like we have an error. Click again to retry!
    </div>
    <ConfirmDelete
      :showModal="showDeleteModal"
      @showDeleteModal="val => (showDeleteModal = val)"
      @hidden="showDeleteModal = false"
      @delete="resetFunnelEvents"
      :loader="resetLoader"
    />
  </div>
</template>
<script lang="ts">
import axios from 'axios'
import moment from 'moment-timezone'
import FunnelStepStats from '../../components/funnels/FunnelStepStats.vue'
import ConfirmDelete from '../../components/funnels/ConfirmFunnelStatsDeleteModal.vue'
import { FunnelPage, getCountryDateFormat } from '../../../models'
import { FunnelType } from '../../../models/funnel'
import { defineComponent } from '@vue/composition-api'
import { FunnelsServices } from '@/services'
import groupBy from 'lodash/groupBy'
export default defineComponent({
  components: {
    FunnelStepStats,
    ConfirmDelete,
  },
  props: {
    funnel: {
      type: Object,
    },
    funnelDomain: {
      type: String,
    },
  },
  async mounted() {
    await this.loadFunnelProudct()
    await this.fetchFunnelStats()
  },
  data() {
    const endDate = moment().toDate()
    const startDate = moment().subtract(1, 'months').toDate()

    const statsFilterDate = {
      start: startDate,
      end: endDate,
    }

    const funnelSteps = {};
    this.funnel.steps.forEach(step => {
      if(!funnelSteps[step.id]){
        funnelSteps[step.id] = step
      }
    })

    return {
      stepStats: [] as Array<object>,
      fetchingFunnelStats: false,
      errorInFetchingStats: false,
      statsFilterDate,
      getCountryDateFormat,
      showDeleteModal: false,
      resetLoader: false,
      funnelSteps,
      isWebsite: this.funnel.type === FunnelType.Website,
      stepProducts:{}
    }
  },
  methods: {
    async loadFunnelProudct(){
      try {
        const {data} = await FunnelsServices.listFunnelProductsByStep(this.funnel.id)
        this.stepProducts = groupBy(data.products,'step')
      } catch (error) {
        console.error(error);
      }
    },
    async resetFunnelEvents() {
      try {
        this.resetLoader = true
        let response = await this.$http.get(
          `/analytics/funnel/reset/${this.funnel.id}`
        )
        this.resetLoader = false
        this.showDeleteModal = false
        const stats = await this.getFunnelStats()
        const pages = await FunnelPage.getAllByFunnelId(this.funnel.id, this.funnel.locationId);
        this.populateStats(stats, pages)
      } catch (error) {
        this.resetLoader = false
        this.showDeleteModal = false
        console.error(error)
      }
    },
    populateStats(stats: Array<object>, pages: Array<object>) {
      this.stepStats = this.funnel.steps.reduce(
        (statsAgg: Array<object>, step: any) => {

          const stat: any = stats.find((s: any) => s.funnel_step_id === step.id)
          const { products: productsInStep } = step

          let populatedStat = {
            funnel_step_name: step.name
          }

          if (stat) {
            stat.page_stats = stat.page_stats
              .map(pageStat => {
                const page = pages.find(x => x._id === pageStat.page_id)
                const page_index = step.pages.indexOf(pageStat.page_id)
                if(pageStat.products){
                  pageStat.products = pageStat.products.map((productStat: any) => {
                    let productName = '(Unnamed Product)'
                    let isDeleted = false;
                    let productInPage
                    if(this.funnel.orderFormVersion === 2){
                      productInPage = this.stepProducts[step.id]

                      /*
                        If step has multiple products then
                        this.stepProducts[step.id] would be an array of all the products
                        Filter the product from the array
                      */
                      if (Array.isArray(productInPage)){
                        productInPage = productInPage.find(
                          (product: any) => product._id === productStat.id
                        )
                      }
                    }else {
                      productInPage = productsInStep.find(
                        (product: any) => product.id === productStat.id
                      )
                    }
                    if (productInPage) {
                      const { productName:oldProductName, name:newProductName, deleted } = productInPage
                      productName = oldProductName || newProductName
                      isDeleted = deleted;
                    }
                    return { name: productName, is_deleted: isDeleted, ...productStat }
                  })
                } else {
                  pageStat.products = []
                }

                return {
                  name: page.name,
                  page_index, // To sort pages
                  is_control: page_index === 0, // 0 => Control, 1 => Variation
                  is_deleted: page.deleted,
                  updated_ms: page.dateUpdated.valueOf(),
                  products: this.isWebsite ? pageStat.products : null,
                  ...pageStat
                }
              });

            stat.page_stats = [
              ...(stat.page_stats
                .filter(x => !x.is_deleted)
                .sort((first, second) => { return first.page_index - second.page_index})
              ),
              ...(stat.page_stats
                .filter(x => x.is_deleted)
                .sort((first, second) => { return second.updated_ms - first.updated_ms})
              )
            ]

            populatedStat = { ...stat, ...populatedStat }
          }

          const newStatsAgg = Array.from(statsAgg)
          newStatsAgg.push(populatedStat)

          return newStatsAgg
        },
        []
      )
    },
    async fetchFunnelStats() {
      this.errorInFetchingStats = false
      this.fetchingFunnelStats = true
      try {
        const stats = await this.getFunnelStats()
        const pages = await FunnelPage.getAllByFunnelId(this.funnel.id, this.funnel.locationId)
        this.populateStats(stats, pages)
      } catch (error) {
        this.errorInFetchingStats = true
        console.error('error while getting stats --> ', error)
      }
      this.fetchingFunnelStats = false
    },
    getFunnelStats(): Promise<object[]> {
      return new Promise((resolve, reject) => {
        const funnelId = this.funnel.id
        const fromDate = moment(
          this.statsFilterDate.start,
          'YYYY-MM-DD hh:mm a'
        )
          .toDate()
          .toISOString()

        let toDate: string
        if (!this.statsFilterDate.end) {
          toDate = moment(this.statsFilterDate.start, 'YYYY-MM-DD hh:mm a')
            .endOf('day')
            .toDate()
            .toISOString()
        } else {
          toDate = moment(this.statsFilterDate.end, 'YYYY-MM-DD hh:mm a')
            .toDate()
            .toISOString()
        }

        const API_ENDPOINT = `/analytics/funnel/aggregate_events?funnel_id=${funnelId}&from_date=${fromDate}&to_date=${toDate}`
        axios
          .get(API_ENDPOINT)
          .then(resp => {
            console.log('got response for stats --> ', resp.data)
            resolve(resp.data)
          })
          .catch(err => {
            console.error('error while getting stats --> ', err)
            reject(err)
          })
      })
    },
  },
})
</script>
<style>
.funnel-stats-date-picker {
  display: inline-block;
}
.funnel-stats-date-control {
  background-color: inherit;
  border: none;
  color: inherit;
  font-size: inherit;
  outline: none;
}
.date-control:focus {
  outline: none;
}
#funnelStatsDatePicker {
  color: #188bf6 !important;
}
#funnelStatsDatePicker input {
  width: 225px !important;
}
#funnelStatsDatePicker .datepicker {
  left: -120%;
}

#funnelStatsDatePicker-wrapper {
  color: #188bf6 !important;
}
#funnelStatsDatePicker-wrapper .datepicker {
  left: -120%;
}

#funnelStatsDatePicker-input {
  width: 225px !important;
}
.reset-btn {
  color: #188bf6;
  font-weight: normal;
}
.reset-btn span,
.reset-btn i {
  color: #188bf6;
}
.reset-btn span {
  margin-right: 5px;
  font-weight: normal;
}

/*.hl_funnels--stats .stats--table tbody tr.funnel-page-stat td:first-child {*/
/*  margin-left: 60px;*/
/*}*/
</style>
