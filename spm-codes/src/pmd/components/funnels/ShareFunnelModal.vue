<template>
  <div class="modal fade hl_add_domain--modal" tabindex="-1" role="dialog" ref="shareFunnelModal">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Share {{ entityName }}</h2>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div v-if="fetchingLink">
              <moon-loader :loading="fetchingLink" color="#188bf6" size="30px" />
            </div>
            <div v-else class="form-group">
              <UITextInputGroup name="shareLink" :label="`Share this link to give a cloned copy of your ${ entityName.toLowerCase() }`" type="text" :value="shareLink" readonly />
            </div>
          </div>
        </div>
        <!-- <div class="modal-footer">
          <div class="modal-footer--inner">
						<button type="button" class="btn btn-light2" data-dismiss="modal">Cancel</button>
            <button type="button" @click="copyFunnelLink" class="btn btn-success">
              Copy
            </button>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import axios from 'axios'
import config from '@/config'
import { copyTextToClipboard } from '../../../util/helper'
import { Company } from '../../../models'

export default Vue.extend({
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    funnelId: {
      type: String,
      required: true
    },
    entityName: {
      type: String,
      default: 'Funnel'
    }
  },
  data() {
    return {
      fetchingLink: false,
      shareLink: ''
    }
  },
  methods: {
    // copyFunnelLink() {
    //   copyTextToClipboard(this.shareLink)
    //   // $(this.$refs.shareFunnelModal).modal('hide')
    //   // $(this.$refs.shareFunnelModal).off('hidden.bs.modal')
    // },
    async fetchNewShareLink() {
      try {
        this.fetchingLink = true
        const response = await axios.get(`/funnelbuilder/get-share-url/${this.funnelId}`)
        console.log('response for share link --> ', response)
        const { data: { shareId } } = response

        const companyId = this.$store.state.auth.user.companyId
        const company = await Company.getById(companyId)
        switch (config.mode) {
          case 'production': {
            this.shareLink = `https://affiliates.gohighlevel.com/?fp_ref=${company.referralId}&funnel_share=${shareId}`
            break
          }
          case 'dev': {
            this.shareLink = `http://localhost:8080/funnels/share/${shareId}`
            break
          }
          case 'staging': {
            this.shareLink = `https://affiliate-dot-highlevel-staging.appspot.com/?fp_ref=${company.referralId}&funnel_share=${shareId}`
            break
          }
          default: {
            this.shareLink = `https://${window.location.hostname}/funnels/share/${shareId}`
          }
        }
      } catch (error) {
        console.error('error while getting share link --> ', error)
      } finally {
        this.fetchingLink = false
      }
    }
  },
  mounted() {
    const _self = this
    $(this.$refs.shareFunnelModal).on('hidden.bs.modal', function() {
      _self.$emit('hide')
    })
  },
  beforeDestroy() {
    $(this.$refs.shareFunnelModal).off('hidden.bs.modal')
  },
  watch: {
    showModal(newValue, oldValue) {
      if (newValue !== oldValue) {
        $(this.$refs.shareFunnelModal).modal('show')
        this.fetchNewShareLink()
      }
    }
  }
})
</script>
