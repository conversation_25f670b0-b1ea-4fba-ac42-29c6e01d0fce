<template>
  <div>
    <section class="hl_wrapper">
      <section class="hl_wrapper--inner customers" id="calenderEvents">
        <div class="container-fluid">
          <div class="hl_controls">
            <div class="hl_controls--left">
              <h2>Appointments Report</h2>
            </div>
            <div class="hl_controls--right">
              <button
                type="button"
                v-if="isFilterApplied"
                @click.prevent="clearFilter(true)"
                class="mt-2 mr-0 btn btn-link"
              >
                Clear Filters
              </button>

              <b-dropdown
                id="report-dropdown-filters"
                right
                text="Filters"
                :variant="isFilterApplied ? 'primary' : 'light3'"
                class="m-2"
                ref="filterDropdown"
              >
                <b-dropdown-form class="p-2">
                  <UITextLabel>Calendar</UITextLabel>
                  <UISelect
                    v-if="isAccessAllCalendar"
                    :options="twFilterCalnedars"
                    :value="tempfilterCalendarId"
                    name="calendar"
                    :class="{
                      input: true,
                      'mb-2': true
                    }"
                    @change="val => this.tempfilterCalendarId = val"
                  />



                  <UITextLabel>Filter by</UITextLabel>
                  <UISelect
                    v-if="isAccessAllCalendar"
                    :options="[
                      {
                        label: 'Appointment created on',
                        value: 'Appointment created on'
                      },
                      {
                        label: 'Appointment booked for',
                        value: 'Appointment booked for'
                      }
                    ]"
                    :value="tempFilterOn"
                    name="filterBy"
                    :class="{
                      input: true,
                      'mb-2': true
                    }"
                    @change="val => this.tempFilterOn = val"
                  />

                  <div class="d-flex justify-content-between">
                    <div class="mr-2">
                      <UITextLabel>From</UITextLabel>

                      <datepicker
                        v-model="startDate"
                        input-class="block w-full mt-1 text-gray-800 border-gray-300 rounded-md shadow-sm date-picker focus:ring-curious-blue-500 focus:border-curious-blue-500 sm:text-sm disabled:opacity-50"
                        class="mt-2"
                        format="d-MMM-yyyy"
                        :monday-first="true"
                        placeholder="Select date"
                        maximum-view="day"
                        name="day"
                        @input="startDateUpdate"
                      />
                    </div>
                    <div class="ml-2 to-date-picker">
                      <UITextLabel>To</UITextLabel>
                      <datepicker
                        v-model="endDate"
                        class="mt-2"
                        input-class="block w-full mt-1 text-gray-800 border-gray-300 rounded-md shadow-sm date-picker focus:ring-curious-blue-500 focus:border-curious-blue-500 sm:text-sm disabled:opacity-50"
                        :disabled-dates="disabledDates"
                        format="d-MMM-yyyy"
                        :monday-first="true"
                        placeholder="Select date"
                        maximum-view="day"
                        name="day"
                      />
                    </div>
                  </div>

                  <div class="pt-4">
                    <hr class="my-3"/>
                    <div class="d-flex justify-content-between">
                      <UIButton
                        type="button"
                        use="outline"
                        @click.prevent="clearFilter(true)"
                      >
                        Clear
                      </UIButton>
                      <UIButton
                        type="button"
                        :disabled="!isFilterChange"
                        @click.prevent="applyFilter"
                      >
                        Apply
                      </UIButton>
                    </div>
                  </div>
                </b-dropdown-form>
              </b-dropdown>
            </div>
          </div>

          <Report
            :timezone="timezone"
            :locationId="currentLocationId"
            :start_at="appointmentDate.start_date"
            :end_at="appointmentDate.end_date"
            :calendarId="filterCalendarId"
            :dateField="dateField"
            :isFilterChange="filterChangeSeq"
            :version="2"
          />
          <div
            v-if="calendars.length == 0"
            style="text-align: center; margin-top: 100px"
          >
            You don't have any calendars yet --
            <router-link
              :to="{
                name: 'calendar_settings',
                params: { location_id: currentLocationId },
              }"
              tag="a"
              exact
              >Click here to add one.</router-link
            >
          </div>
        </div>
      </section>
    </section>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Location, Calendar, User } from '../../models'
import { UserState } from '../../store/state_models'
import { mapState } from 'vuex'
import * as moment from 'moment-timezone'
const Report = () => import('../components/reporting/appointment/Report.vue')
import Datepicker from 'vuejs-datepicker'
import vSelect from 'vue-select'
import { calendars } from '../../store/calendars'

export default Vue.extend({
  name: 'LocationAppointmentReportPage',
  components: {
    Report,
    Datepicker,
    vSelect,
  },
  data() {
    return {
      appointmentDate: {
        start: moment().subtract(1, 'month').set({"hour": 0, "minute": 59}).format('YYYY-MM-DD h:m a'),
        end: moment().set({"hour": 23, "minute": 59}).format('YYYY-MM-DD h:m a'),
        start_date: moment
          .utc()
          .subtract(1, 'month')
          .add(moment().utcOffset(), 'minutes')
          .toDate()
          .getTime(),
        end_date: moment
          .utc()
          .set({
            hour: 23,
            minute: 59,
          })
          .add(moment().utcOffset(), 'minutes')
          .toDate()
          .getTime(),
      },
      startDate: moment
        .utc()
        .subtract(1, 'month')
        .add(moment().utcOffset(), 'minutes')
        .toDate(),
      endDate: moment
        .utc()
        .set({
          hour: 23,
          minute: 59,
        })
        .add(moment().utcOffset(), 'minutes')
        .toDate(),
      disabledDates: {
        to: new Date(
          moment()
            .subtract(1, 'month')
            .add(1, 'days')
            .format('MMM DD, YYYY HH:MM')
        ),
      },
      currentLocationId: '',
      currentLocation: {} as Location,
      timezone: 'UTC',
      tempfilterCalendarId: 'all',
      filterCalendarId: 'all',
      tempFilterOn: 'Appointment created on',
      filterOn: 'Appointment created on',
      isDateAppointmentBooked: false,
      isFilterApplied: false,
      filterChangeSeq: 1,
    }
  },
  async created() {
    await this.refreshLocationData()
    this.filterFromUrl();
  },
  computed: {
    calendars(): undefined | Calendar[] {
      return this.$store.state.calendars.calendars.filter(x=> x.is_active)
    },
    filterdCalendars(): Calendar[] {
      let calendars = this.calendars.filter(
        x =>
          this.user &&
          (!this.user.permissions.assigned_data_only ||
            this.user.role === 'admin' ||
            (this.user.permissions.assigned_data_only === true &&
              this.user.userCalendar[this.currentLocationId] === x.id))
      )
      if (!calendars || !calendars.length) {
        calendars = []
      }

      calendars.unshift({
        id: 'all',
        name: 'All Calendars',
      })

      return calendars
    },
    twFilterCalnedars() {
      return this.filterdCalendars.map((calendar: any) => {
        return {
          label: calendar.name,
          value: calendar.id,
          ...calendar
        }
      })
    },
    dateField() {
      return this.filterOn == 'Appointment created on' ? 'createdAt' : 'startAt'
    },
    isFilterChange() {
      return (
        this.filterCalendarId != this.tempfilterCalendarId ||
        this.filterOn != this.tempFilterOn ||
        !moment(this.appointmentDate.start_date)
          .startOf('day')
          .isSame(moment(this.startDate).startOf('day')) ||
        !moment(this.appointmentDate.end_date)
          .startOf('day')
          .isSame(moment(this.endDate).startOf('day'))
      )
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    isAccessAllCalendar() {
      return (
        this.user &&
        (!this.user.permissions.assigned_data_only ||
          this.user.role === 'admin')
      )
    },
  },
  beforeMount(){
    this.filterFromUrl();
  },
  methods: {
    filterFromUrl(){
      if (this.$route.query.filterCalendarId && this.$route.query.filterOn && this.$route.query.startDate && this.$route.query.endDate) {

        this.filterCalendarId = this.$route.query.filterCalendarId??'all';
        this.tempfilterCalendarId = this.$route.query.filterCalendarId??'all';

        this.filterOn = this.$route.query.filterOn??'Appointment created on';
        this.tempFilterOn = this.$route.query.filterOn;

        this.$set(this.appointmentDate, 'start_date', parseInt(this.$route.query.startDate))
        this.startDate = moment(parseInt(this.$route.query.startDate)).toDate();
        this.$set(this.appointmentDate,'end_date',parseInt(this.$route.query.endDate));
        this.endDate = moment(parseInt(this.$route.query.endDate)).subtract(1, 'day').toDate();
        this.isFilterApplied = true
        this.filterChangeSeq++
      }
    },
    async refreshLocationData() {
      this.currentLocationId = this.$router.currentRoute.params.location_id
      const location = new Location(
        await this.$store.dispatch('locations/getById', this.currentLocationId)
      )
      this.currentLocation = location
      this.timezone = await location.getTimeZone()
    },
    startDateUpdate() {
      const date = moment(this.startDate)
      this.disabledDates.to = new Date(
        date.add(1, 'days').format('MMM DD, YYYY HH:MM')
      )

      if (moment(this.endDate).valueOf() < date.valueOf()) {
        this.endDate = date.toDate()
      }
    },

    applyFilter() {
      this.$refs.filterDropdown.hide()
      this.isFilterApplied = true
      this.filterCalendarId = this.tempfilterCalendarId
      this.filterOn = this.tempFilterOn

      const startAt = moment
        .utc(this.startDate, [
          'YYYY-MM-DD h:m a'
        ])
        .set({"hour": 0, "minute": 0})
        .add(moment().utcOffset(), 'minutes');
      this.$set(this.appointmentDate, 'start_date', startAt.toDate().getTime())

      const endTime = moment
        .utc(this.endDate, [
          'YYYY-MM-DD h:m a'
        ])
        .set({"hour": 23, "minute": 59})
        .add(moment().utcOffset(), 'minutes')
      this.$set(
        this.appointmentDate,
        'end_date',
        endTime.toDate().getTime()
      );

      this.$router.push({ query: {
          ...this.$route.query,
          filterCalendarId: this.filterCalendarId,
          filterOn: this.filterOn,
          startDate: startAt.toDate().getTime(),
          endDate: endTime.toDate().getTime()
        }
      })

      this.filterChangeSeq++
    },
   async clearFilter(resetUrl: boolean = false) {
      this.tempfilterCalendarId = 'all'
      this.filterCalendarId = 'all'
      this.filterOn = 'Appointment created on'
      this.tempFilterOn = 'Appointment created on'

      const startAt =  moment.utc().subtract(1, 'month').add(moment().utcOffset(), 'minutes').toDate()
      const endAt = moment.utc().set({hour: 23, minute: 59}).add(moment().utcOffset(), 'minutes').toDate()
      this.startDate = startAt
      this.endDate = endAt

      this.startDateUpdate()

      this.$set(this.appointmentDate, 'start_date', startAt.getTime())
      this.$set(this.appointmentDate, 'end_date', endAt.getTime())

      this.isFilterApplied = false
      this.filterChangeSeq = 1
      if(resetUrl && Object.keys( this.$route.query ).length > 0){
        this.$refs.filterDropdown.hide()
        this.$router.push({ query: { } })
      }
    },
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
    },
    async currentLocationId() {
      this.clearFilter()
      await this.refreshLocationData()
    },
  },
})
</script>
<style lang="scss">
.previous-version-page-flag-wrapper {
  border: 1px solid #ebebeb;
  box-sizing: border-box;
  border-radius: 3px;
}
.btn-success--schedule-appointment {
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    // pointer-events: none;
  }
}
#calenderEvents {
  .hl_datepicker {
    padding: 0 20px;
    .field {
      .field-input {
        height: auto !important;
        padding: 0 !important;
        color: #188bf6 !important;
        text-align: center;
        border: none !important;
        background: transparent !important;
      }
    }
  }
}

#report-dropdown-filters {
  .dropdown-menu {
    min-width: 360px;
  }
  .b-dropdown-form {
    .v-select,
    .vs__actions {
      height: 40px;
    }
    .vs__selected {
      font-size: 15px;
      height: 40px;
      margin: 0 2px 0;
    }
  }

  .dropdown-toggle {
    &::after {
      content: '\e97e';
    }
  }

  .to-date-picker .vdp-datepicker__calendar {
    left: -125px;
  }
}
</style>
