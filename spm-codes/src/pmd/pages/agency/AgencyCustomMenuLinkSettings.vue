<template>
  <div class="container-fluid">
    <div class="container-fluid">
      <div class="hl_marketing--header-inner">
        <h2>Custom Menu Links Settings</h2>
        <UIButton
          use="primary"
          type="button"
          @click.prevent="showCreateCustomMenuLinkModal()"
        >
          <i class="icon icon-plus mr-2" style="color:white;margin-right: 10px;"></i>
          Create New
        </UIButton>
      </div>
    </div>

    <div class="card">
      <div class="--no-padding">
        <div class="table-wrap">
          <table class="table">
            <thead>
              <tr>
                <th>Icon</th>
                <th>Title</th>
                <th>URL</th>
                <th>Open mode</th>
                <th>Show on</th>
                <th>Accounts to show</th>
                <th style="width: 1px;"></th>
                <th style="width: 1px;"></th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(link, index) in company.customMenuLinks" :key="index">
                <td>
                  <i
                    v-if="link.icon.value"
                    class="sm-button"
                    :style="
                      `--fa:'\\${link.icon.value.unicode}';--ff:'${link.icon.value.fontFamily}'`
                    "
                  />
                </td>
                <td>{{ link.title }}</td>
                <td>{{ link.url }}</td>
                <td>{{ link.open_mode | openMode }}</td>
                <td>{{ showOnString(link) }}</td>
                <td>{{ accountsToShowString(link) }}</td>
                <td>
                  <i
                    class="icon icon-pencil pointer --light"
                    v-b-tooltip.hover
                    title="Edit custom menu link"
                    @click.prevent="showCreateCustomMenuLinkModal(link.id)"
                  ></i>
                </td>
                <td>
                  <i
                    v-b-tooltip.hover
                    title="Delete custom menu link"
                    class="icon icon-trash pointer --light"
                    @click.prevent="deleteCustomMenuLink(link.id)"
                  ></i>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <create-edit-custom-menu-link
      :values="createCustomMenuLinkModal"
      @hidden="createCustomMenuLinkModal = { visible: false, linkId: '' }"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { AuthUser, Company } from '@/models'
import CreateEditCustomMenuLink from '@/pmd/components/agency/CreateEditCustomMenuLink.vue'
let unsubscribeCustomMenuLinks: () => void

export default Vue.extend({
  components: {
    CreateEditCustomMenuLink
  },
  data: () => ({
    authUser: {} as AuthUser,
    createCustomMenuLinkModal: { visible: false } as { [key: string]: any },
    company: {} as Company
  }),
  methods: {
    showOnString(link) {
      return [link.show_on_company ? 'Agency' : null, link.show_on_location ? 'Account' : null].filter(obj => obj).join(', ')
    },
    accountsToShowString(link) {
      if(!link.show_on_location) return '-'
      return link.locations ? link.locations.length : 'All'
    },
    async fetchData() {
      if (unsubscribeCustomMenuLinks) unsubscribeCustomMenuLinks()

      this.authUser = await this.$store.dispatch('auth/get')

      unsubscribeCustomMenuLinks = Company.getStreamById(
        this.authUser.companyId
      ).onSnapshot(snapshot => {
        this.company = new Company(snapshot)
      })
    },
    showCreateCustomMenuLinkModal(id: string) {
      this.createCustomMenuLinkModal = {
        visible: true,
        companyId: this.authUser.companyId,
        linkId: id
      }
    },
    async deleteCustomMenuLink(id: string) {
      let message = 'This will delete the custom link. Do you wish to continue?'

      this.$uxMessage('confirmation', message, async response => {
        if (response === 'ok') {
          this.company.customMenuLinks = this.company.customMenuLinks.filter(
            link => link.id !== id
          )
          await this.company.save()
        }
      })
    }
  },
  async created() {
    this.fetchData()
  },
  beforeDestroy(): void {
    if (unsubscribeCustomMenuLinks) unsubscribeCustomMenuLinks()
  },
  filters: {
    openMode(mode) {
      switch (mode) {
        case 'iframe':
          return 'iframe'
        case 'new_tab':
          return 'New tab'
        default:
          return ''
      }
    }
  }
})
</script>
<style scoped>
.sm-button::before {
  font-family: var(--ff);
  font-weight: 900;
  /* content: attr(data-icon); */
  content: var(--fa);
  font-style: normal;
}
</style>
