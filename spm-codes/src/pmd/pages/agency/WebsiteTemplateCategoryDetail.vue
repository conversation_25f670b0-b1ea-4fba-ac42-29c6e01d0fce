<template>
  <div>
    <SideBarV2 v-if="getSideBarVersion === 'v2'"/>
    <SideBar v-else />
    <TopBar />
    <section class="hl_wrapper">
      <section
        class="hl_wrapper--inner website-templates-category-detail"
        id="websiteTemplatesCategoryDetail"
      >
        <div class="container-fluid">
          <div class="hl_controls">
            <div v-if="fetchingCategory || errorInFetchingCategory">
              <moon-loader :loading="fetchingCategory" color="#188bf6" size="15px" />
              <span
                v-if="errorInFetchingCategory"
                @click="fetchTemplateCategory()"
              >Error while fetching category. Click here to retry!</span>
            </div>
            <div v-else class="hl_controls--left">
              <h2>
                <a @click="$router.go(-1)" href="javascript:void(0);" class="back">
                  <i class="icon icon-arrow-left-2"></i>
                </a>
                {{ websiteTemplateCategory.name }}
              </h2>
            </div>
            <div class="hl_controls--right">
              <UIButton @click="createNewTemplate()">
                <i class="icon icon-plus mr-2"></i>&nbsp;Create New Template
              </UIButton>
            </div>
          </div>
          <div class="card" v-if="!fetchingCategory && !errorInFetchingCategory">
            <div class="card-header">
              <h5>Edit Details for {{ websiteTemplateCategory.name }}</h5>
            </div>
            <div class="card-body">
              <div class="form-group">
                <UITextInputGroup
                  label="Category Name"
                  type="text"
                  placeholder="Name for Template Category"
                  required
                  v-model="websiteTemplateCategory.name"
                />
              </div>
              <div class="form-group">
                <UITextAreaGroup
                  label="Category Description"
                  type="text"
                  placeholder="Description for Template Category"
                  v-model="websiteTemplateCategory.description"
                ></UITextAreaGroup>
              </div>
              <div class="form-group">
                <UITextLabel for="description">Image</UITextLabel>
                <input
                  class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800 disabled:opacity-50"
                  type="url"
                  placeholder="Image URL for Template Category"
                  v-model="websiteTemplateCategory.image"
                />
              </div>
              <div class="form-group">
                <UITextLabel for="description">Video</UITextLabel>
                <input
                  class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800 disabled:opacity-50"
                  type="url"
                  placeholder="Video URL for Template Category"
                  v-model="websiteTemplateCategory.video"
                />
              </div>
              <div class="form-group template-category-options">
                <UIButton
                  :disabled="updatingCategory"
                  @click="updateTemplateCategory()"
                >
                  <span>{{ updatingCategory ? 'Updating..' : 'Update Template Category' }}</span>
                </UIButton>
                <UIButton
                  :disabled="deletingCategory"
                  use="danger"
                  @click="deleteTemplateCategory()"
                >
                  <span>{{ deletingCategory ? 'Deleting..' : 'Delete Template Category' }}</span>
                </UIButton>
              </div>
            </div>
          </div>
          <div v-if="fetchingTemplates || errorInFetchingTemplates">
            <moon-loader :loading="fetchingTemplates" color="#188bf6" size="30px" />
            <span
              v-if="errorInFetchingTemplates"
              @click="fetchWebsiteTemplateForCategory(templateCategoryId)"
            >Error while fetching templates. Click here to retry!</span>
          </div>
          <div v-else>
            <div
              v-if="websiteTemplates.length === 0"
            >No Templates found! Click on Create New Template button to add one.</div>
            <h5 v-else>Templates for category</h5>
            <div class="row">
              <div
                class="col-sm-6 col-md-4 col-xl-3"
                v-for="template in websiteTemplates"
                :key="template.id"
              >
                <router-link
                  :to="{name: 'website_template_detail', params: { id: templateCategoryId, template_id: template.id }}"
                  tag="div"
                >
                  <website-template-card :template="template" />
                </router-link>
              </div>
            </div>
          </div>
        </div>
      </section>
      <CreateEditWebsiteTemplate
        :show-modal="createEditTemplateModal.visible"
        :template-category-id="templateCategoryId"
        :template-category-title="templateCategoryTitle"
        @newTemplateId="gotNewTemplateId"
        @hide="hideCreateEditWebsiteTemplateModal"
      />
    </section>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import TopBar from '../../components/TopBar.vue'
import SideBar from '../../components/agency/SideBar.vue'
import { WebsiteTemplateCategory, WebsiteTemplate, User } from '../../../models'
import CreateEditWebsiteTemplate from '../../components/agency/CreateEditWebsiteTemplate.vue'
import WebsiteTemplateCard from '@/pmd/components/funnels/WebsiteTemplateCard.vue'
import { WebsiteTemplateCategoryType } from '../../../models/website_template_category'
import SideBarV2 from '../../components/sidebar/SideBar.vue'

export default Vue.extend({
  components: {
    TopBar,
    SideBar,
    CreateEditWebsiteTemplate,
    WebsiteTemplateCard,
    SideBarV2
  },
  data() {
    return {
      websiteTemplateCategory: {} as WebsiteTemplateCategory,
      createEditTemplateModal: {
        visible: false
      },
      websiteTemplates: [] as WebsiteTemplate[],
      fetchingCategory: false,
      errorInFetchingCategory: false,
      fetchingTemplates: false,
      errorInFetchingTemplates: false,
      updatingCategory: false,
      deletingCategory: false
    }
  },
  computed: {
    templateCategoryId() {
      return this.$route.params.id
    },
    templateType(): string {
      return this.$route.params.type
    },
    templateCategoryTitle(): string {
      switch (this.templateType) {
        case WebsiteTemplateCategoryType.Websites: {
          return 'Website'
        }
        case WebsiteTemplateCategoryType.Funnels: {
          return 'Funnel'
        }
        default: {
          return 'Website'
        }
      }
    },
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  mounted() {
    this.fetchTemplateCategory(this.templateCategoryId)
    this.fetchWebsiteTemplateForCategory(this.templateCategoryId)
  },
  methods: {
    async fetchTemplateCategory(categoryId: string) {
      try {
        this.fetchingCategory = true
        this.errorInFetchingCategory = false

        this.websiteTemplateCategory = await WebsiteTemplateCategory.getById(
          categoryId
        )
      } catch (error) {
        console.error('error in fetching category --> ', error)
        this.errorInFetchingCategory = true
      } finally {
        this.fetchingCategory = false
      }
    },
    async fetchWebsiteTemplateForCategory(categoryId: string) {
      try {
        this.fetchingTemplates = true
        this.errorInFetchingTemplates = false

        const availableTemplates = await WebsiteTemplate.getByCategoryId(
          categoryId
        )
        if (availableTemplates) {
          this.websiteTemplates = availableTemplates.sort(
            (tempA, tempB) =>
              new Date(tempB.dateAdded.format()).getTime() -
              new Date(tempA.dateAdded.format()).getTime()
          )
        }
      } catch (error) {
        console.error(
          'Error while fetching website templates for category --> ',
          error
        )
        this.errorInFetchingTemplates = true
      } finally {
        this.fetchingTemplates = false
      }
    },
    async gotNewTemplateId(templateId: string) {
      try {
        const template = await WebsiteTemplate.getById(templateId)
        if (template) {
          this.websiteTemplates = [...this.websiteTemplates, template]
        } else {
          console.error('Template not found for id --> ', templateId)
        }
      } catch (error) {
        console.error('Error while fetching template --> ', templateId)
      }
    },
    createNewTemplate() {
      console.log('create new template')
      this.createEditTemplateModal = {
        visible: true
      }
    },
    hideCreateEditWebsiteTemplateModal() {
      this.createEditTemplateModal = {
        visible: false
      }
    },
    async updateTemplateCategory() {
      try {
        this.updatingCategory = true
        await this.websiteTemplateCategory.save()
      } catch (error) {
        console.error('Error while updating category --> ', error)
        alert('Error while updating category')
      } finally {
        this.updatingCategory = false
      }
    },
    async deleteTemplateCategory() {
      try {
        if (
          confirm(
            'Are you really sure you want to delete the template category? This will delete all the templates inside this category.'
          )
        ) {
          this.deletingCategory = true
          await this.$http.delete(
            `/website-template-category/${this.templateCategoryId}`
          )
          this.$router.push({ name: 'website_template_category_list' })
        }
      } catch (error) {
        console.error('error while deleting category --> ', error)
        alert('Something went wrong while deleting category!')
      } finally {
        this.deletingCategory = false
      }
    }
  }
})
</script>
<style scoped>
.template-category-options {
  display: flex;
  justify-content: space-between;
  margin-top: 2.5rem;
}
</style>
