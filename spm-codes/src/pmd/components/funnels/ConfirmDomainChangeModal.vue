<template>
  <div
    id="change-confirm-modal"
    ref="modal"
    aria-hidden="true"
    aria-labelledby="client-checkin--modalLabel"
    class="modal fade"
    role="dialog"
    tabindex="-1"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 id="change-confirm-modal-label" class="modal-title">
            Changing Domain?
          </h5>
          <button aria-label="Close" class="close" type="button" @click="closeModal">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div v-if="onStepOne">
                <p>Changing the domain will affect the following in your funnel:</p>
                <ul class="list-style">
                    <li class="list-item">Stop and delete any variation step in split testing</li>
                    <li>Delete Stats associated with split testing</li>
                </ul>
                <p class="font-weight-bold mt-2">Are you sure you want to continue?</p>
            </div>
            <form v-if="onStepTwo">
              <div class="form-group">
                <UITextInputGroup
                  id="change-confirm"
                  v-model="changeConfirmation"
                  :class="changeConfirmation.toLowerCase() === 'change' ? 'is-valid' : 'is-invalid'"
                  label="Confirm deletion by typing 'CHANGE' below:"
                  type="text"
                />
                <div
                  v-if="changeConfirmation && changeConfirmation.toLowerCase() !== 'change'"
                  id="validation-feedback"
                  class="invalid-feedback d-block h1">
                  Enter <strong>CHANGE</strong> to confirm
                </div>
              </div>
            </form>
          </div>
          <div v-if="onStepOne" class="modal-footer--inner mt-4">
            <UIButton use="outline" type="button" @click="closeModal">
              Cancel
            </UIButton>
            <UIButton
              use="primary"
              type="button"
              @click="switchToStepTwo"
            >
              Continue
            </UIButton>
          </div>
          <div v-if="onStepTwo" class="modal-footer--inner mt-4">
            <UIButton use="outline" type="button" @click="closeModal">
              Cancel
            </UIButton>
            <UIButton
              :disabled="changeDisabled"
              use="primary"
              type="button"
              @click="changeConfirmed"
            >Yes, Change
            </UIButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: {
    showModal: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      changeConfirmation: '',
      changeDisabled: true,
      onStepOne: true,
      onStepTwo: false
    }
  },
  watch: {
    showModal(status: Boolean) {
      this.changeConfirmation = ''
      var _self = this
      if (status) {
        $(this.$refs.modal)
          .modal('show')
          .on('hidden.bs.modal', function() {
            _self.$emit('hidden', true)
          })
      } else {
        $(this.$refs.modal).modal('hide')
        this.reset()
      }
    },
    changeConfirmation(value: string) {
      this.changeDisabled = !(value && value === 'CHANGE')
    }
  },
  methods: {
    reset(){
      this.onStepOne = true
      this.onStepTwo = false
      this.changeConfirmation = ''
    },
    changeConfirmed() {
      this.$emit('change', true)
      this.reset()
      this.closeModal()
    },
    closeModal() {
      this.reset()
      this.$emit('showDomainChangeModal', false)      
    },
    switchToStepTwo() {
      this.onStepOne = false
      this.onStepTwo = true
    }
  }
})
</script>
<style scoped>
.modal .modal-dialog {
  max-width: 500px;
}

.list-style{
  list-style: disc;
  padding: 0.5em 2em
}
</style>
