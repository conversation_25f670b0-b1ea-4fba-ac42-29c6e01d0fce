<template>
  <div class="row hl_funnels-steps-pages">
    <div class="col-lg-4">
      <div class="steps-item control-block" v-if="controlPageExists">
        <div class="steps-item--header">
          <i class="fas fa-flag"></i> Control
        </div>
        <div class="steps-item--image">
          <img
            v-if="controlPage.snapshotPreview"
            @load="screenshotLoaded = true"
            :src="controlPage.snapshotPreview"
          />
          <img
            v-else
            src="https://via.placeholder.com/400x350?text=Edit Your Funnel"
          />
          <img
            v-if="controlPage.snapshotPreview && !screenshotLoaded"
            width="50"
            src="../../../assets/pmd/img/loading-spinner.gif"
            alt="Loading"
          />
          <div v-if="funnelStep.split" class="declare-winner --control">
            <i class="icon-trophy"></i>
            <h5>Declare this as Winner</h5>
            <button id="pg-funnel-pages__btn-declare-winner-control" class="btn btn-sm continue" @click="declareWinner(controlPage)">
              Continue with this version
              <i class="fa fa-arrow-right"></i>
            </button>
          </div>
        </div>
        <div class="steps-item--controls">
          <div class="steps-item--controls-left">
            <a :href="controlPageBuilderUrl" class="btn btn-sm btn-yellow-lt" target='_blank'>
              <i class="icon icon-edit"></i> Edit Page
            </a>
          </div>
          <div class="steps-item--controls-right">
            <a
              :href="controlPagePreviewUrl"
              target="_blank"
              class="btn btn-light2 btn-sm"
              data-tooltip="tooltip"
              data-placement="top"
              title="Preview Page"
            >
              <i class="icon icon-share-2"></i>
              <span class="sr-only">URL</span>
            </a>
            <button
              @click="editPage(controlPage)"
              type="button"
              class="btn btn-light2 btn-sm"
              data-tooltip="tooltip"
              data-placement="top"
              title="Edit Page"
            >
              <i class="icon icon-settings-1"></i>
              <span class="sr-only">Settings</span>
            </button>
          </div>
        </div>
        <div class="flex justify-end mt-1">
           <div class="active-users-group">
            <OnlineUserAvatarGroup :users="controlPageViewers" v-if="isLevelUpDay" />
            </div>
        </div>
      </div>
    </div>
    <div v-if="is497Plan" class="col-lg-4 middle-block">
      <div class="steps-split-test" v-if="variationPageExists">
        <div class="steps-split-test-range">
          <input
            type="range"
            class="slider"
            min="0"
            max="100"
            v-model="computedControlPageTraffic"
          />
          <div class="range-values">
            <span>
              <strong>{{ trafficForControlPage }}%</strong> of traffic
            </span>
            <span>
              <strong>{{ trafficForVariationPage }}%</strong> of traffic
            </span>
          </div>
        </div>
        <div
          v-if="!confirmingNewSplit && funnelStep.split"
          class="split-stats-container"
        >
          <div class="split-stats-header">
            <i class="icon-split-test-2"></i>
            <h5>
              Split test is running
            </h5>
          </div>
          <div v-if="loadingStats && !statsAvailable" class="loading-stats">
            <moon-loader color="#188bf6" size="30px"/>
            <p>Loading split test stats</p>
          </div>
          <div v-else-if="!loadingStats && statsAvailable" class="split-stats-content">
            <table class="split-stats-table">
              <tbody>
                <tr>
                  <td>CONTROL</td><td></td><td>VARIATION</td>
                </tr>
                <tr>
                  <td>{{stats[controlPage.id].page_views_unique || '-'}}</td>
                  <td>PAGE VIEWS</td>
                  <td>{{stats[variationPage.id].page_views_unique|| '-'}}</td>
                </tr>
                <tr class="table-spacer"><td></td></tr>
                <tr>
                  <td>{{stats[controlPage.id].optins_rate ? stats[controlPage.id].optins_rate + '%' : '-'}}</td>
                  <td>OPT-IN RATE</td>
                  <td>{{stats[variationPage.id].optins_rate ? stats[variationPage.id].optins_rate + '%' : '-'}}</td>
                </tr>
                <tr class="table-spacer"><td></td></tr>
                <tr>
                  <td>{{stats[controlPage.id].sale_rate ? stats[controlPage.id].sale_rate + '%' : '-'}}</td>
                  <td>SALES RATE</td>
                  <td>{{stats[variationPage.id].sale_rate ? stats[variationPage.id].sale_rate + '%' : '-'}}</td>
                </tr>
                <tr class="table-spacer"><td></td></tr>
                <tr>
                  <td>{{stats[controlPage.id].earnings_per_page_view_unique ? '$'+stats[controlPage.id].earnings_per_page_view_unique : '-'}}</td>
                  <td>EARNINGS / PGV</td>
                  <td>{{stats[variationPage.id].earnings_per_page_view_unique ? '$'+stats[variationPage.id].earnings_per_page_view_unique : '-'}}</td>
                </tr>
              </tbody>
            </table>
          </div>
          <div v-else class="no-stats">
            <p>No stats available yet</p>
          </div>
        </div>
        <div v-else class="steps-split-test-card">
          <div v-if="!confirmingNewSplit">
            <h4>Almost there!</h4>

            <p v-if="updatingSplit">
              You've changed your split test, which will invalidate any previous results.
              Please confirm that you'd like to apply these changes and start the test over
            </p>
            <p v-else>
              Your split test isn't running just yet. Once you're happy with the
              changes to the variation click the button below and we'll start
              sending traffic to both.
            </p>
            <button id="pg-funnel-pages__btn-init-split-test" type="button" class="btn blue start-split notransition" @click="confirmStartSplit">
              Start Split Test
            </button>
          </div>
          <div v-else>
              <div class="connected-lookups" v-if='additionalRoutes.length'>
                <p>This step is configured as a home page for following domains, do you want to route all the incoming traffic through this split test?</p>
                <table>
                  <tr v-for="route of additionalRoutes" :key=route.id>
                    <td>
                      <a :href=route.fullPath target='_blank'><span>{{ route.fullPath }}</span></a>
                    </td>
                  </tr>
                </table>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" id="pg-funnel-pages__split-traffic" :value=true v-model=routeAllTraffic>
                  <label class="form-check-label" for="pg-funnel-pages__split-traffic">Yes</label>
                </div>
                <div class="form-check form-check-inline">
                  <input class="form-check-input" type="radio" id="pg-funnel-pages__no-split-traffic" :value=false v-model=routeAllTraffic>
                  <label class="form-check-label" for="pg-funnel-pages__no-split-traffic">No, don't include above domains in split test</label>
                </div>
              </div>
            <h6 class="result-warning" v-if=updatingSplit>
              Changing the configuration will invalidate previous results.
              Please confirm that you would like to apply these changes and start the test over.
            </h6>
            <p v-else>
              Please confirm that you would like to apply these changes and start the split test.
            </p>
            <button id="pg-funnel-pages__btn-start-split-test" type="button" class="btn blue notransition" @click="startSplit">
              Apply
            </button>
            <button id="pg-funnel-pages__btn-cancel-split-test" type="button" class="btn orange notransition" @click="cancelSplit">
              Cancel
            </button>
          </div>
        </div>
        <div
          v-if="!confirmingNewSplit && funnelStep.split && !loadingStats"
          class="split-routing-info"
        >
          <h5>Routing traffic from following path(s)</h5>
          <ul>
            <li>
              <a :href="'https://'+funnelDomain+funnelStep.url" target='_blank'>{{funnelDomain}}{{funnelStep.url}}</a>
            </li>
            <li v-if=routeAllTraffic v-for='route of additionalRoutes'>
              <a :href="'https://'+route.fullPath" target='_blank'>{{route.fullPath}}</a>
            </li>
          </ul>
        </div>
      </div>
      <div v-else class="create-variation" >
        <div class="row">
          <div class="col-4"></div>
          <div class="col-4"><i class="icon-split-test"></i></div>
          <div class="col-4"></div>
        </div>
        <h4>Start Split Test</h4>
        <p>Optimize your lead and sales generation with split tests.</p>
       </div>
    </div>
    <div v-if="is497Plan" class="col-lg-4">
      <div class="steps-item variation-block" >
        <div :class="['steps-item--header', !variationPageExists ? 'header-disabled' : '']">
          <i class="far fa-flag"></i> Variation
        </div>
        <div class="steps-item--image" v-if="variationPageExists">
          <img
            v-if="variationPage.snapshotPreview"
            :src="variationPage.snapshotPreview"
          />
          <img
            v-else
            src="https://via.placeholder.com/400x350?text=Edit Your Funnel"
          />
          <div v-if="funnelStep.split" class="declare-winner --variation">
            <i class="icon-trophy"></i>
            <h5>Declare this as Winner</h5>
            <button id="pg-funnel-pages__btn-declare-winner-variation" class="btn btn-sm continue" @click="declareWinner(variationPage)">
              Continue with this version
              <i class="fa fa-arrow-right"></i>
            </button>
          </div>
        </div>
        <div v-else class="steps-item--novariation">
          <div v-if="creatingVariation" class="add-variation--wrapper">
            <moon-loader color="#188bf6" size="30px"/>
            <p>Creating variation page...</p>
          </div>
          <div v-if="!creatingVariation" class="add-variation--wrapper">
            <div
              :class="['add-variation-options', addPageVariationModalShown ? 'show': 'hide']"
            >
              <i class="fa fa-times add-variation-modal-close" @click="addPageVariationModalShown = false"></i>
              <div>
              <button
                id="pg-funnel-pages__btn-create-variation-clone"
                type="button"
                class="add-variation--clone"
                @click="cloneControlPage()"
              >
                Create a Duplicate Variation
              </button>
              </div>
              <div>
                <button
                  id="pg-funnel-pages__btn-create-variation-blank"
                  type="button"
                  class="add-variation--create"
                  @click="createBlankPage()"
                >
                  Create from Blank
                </button>
              </div>
            </div>
          </div>
          <div v-if="!creatingVariation" class="add-variation--wrapper">
            <button
              id="pg-funnel-pages__btn-create-variation"
              @click="addPageVariation()"
              type="button"
              :class="['add-variation-button', addPageVariationModalShown ? 'hide' : 'show']">
              <i class="icon icon-plus"></i>
              Create Variation
            </button>
          </div>

        </div>
        <div class="steps-item--controls" v-if="variationPageExists">
          <div class="steps-item--controls-left">
            <a
              :href="variationPageBuilderUrl"
              class="btn btn-sm btn-yellow-lt"
              target='_blank'
            >
              <i class="icon icon-edit"></i> Edit Page
            </a>
          </div>
          <div class="steps-item--controls-right">
            <a
              :href="variationPagePreviewUrl"
              target="_blank"
              class="btn btn-light2 btn-sm"
              data-tooltip="tooltip"
              data-placement="top"
              title="Preview Page"
            >
              <i class="icon icon-share-2"></i>
              <span class="sr-only">URL</span>
            </a>
            <button
              @click="deletePageConfirmation(variationPage)"
              type="button"
              class="btn btn-light2 btn-sm"
              data-tooltip="tooltip"
              data-placement="top"
              title="Delete Page"
            >
              <i class="fa fa-trash-alt"></i>
              <span class="sr-only">Delete Page</span>
            </button>
            <button
              @click="editPage(variationPage)"
              type="button"
              class="btn btn-light2 btn-sm"
              data-tooltip="tooltip"
              data-placement="top"
              title="Edit Page"
            >
              <i class="icon icon-settings-1"></i>
              <span class="sr-only">Settings</span>
            </button>
          </div>
        </div>
         <div class="flex justify-end mt-1">
           <div class="active-users-group">
            <OnlineUserAvatarGroup :users="variationPageViewers" v-if="isLevelUpDay" />
            </div>
        </div>
      </div>
    </div>
    <EditFunnelPageModal
      :show-modal="pageSettingsModalShown"
      @hide="hidePageSettingsModal"
      :page-to-edit="funnelPageToEdit"
      :errorInEditingPage="errorInEditingPage"
      @updatedPage="updateFunnelPage"
    />
    <ConfirmDeleteModal
      :showModal="showDeleteModal"
      @showDeleteModal="val => (showDeleteModal = val)"
      @hidden="showDeleteModal = false"
      @delete="deleteEditingFunnelPage"
    />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import firebase from 'firebase/app'
import moment from 'moment';
import isEmpty from 'lodash/isEmpty'
import {
  FunnelPage,
  FunnelSection,
  FunnelLookup
} from '../../../models'
import EditFunnelPageModal from './EditFunnelPageModal.vue'
import ConfirmDeleteModal from '../../components/funnels/ConfirmDeleteModal'
import OnlineUserAvatarGroup from '../../components/funnels/OnlineUserAvatarGroup.vue'
import {
  generateFunnelPageUrl,
  getUniqueFunnelUrl,
  cloneControlPage,
  cloneFunnelSection,
  getValidFunnelLookupUrl,
  createVariationPage, getNewLookupObject, getValidUrl
} from '../../../util/helper'
import config from '@/config'
import {
  updateCachingIndex,
  cacheUpdateEvents
} from '../../../util/caching.helper'
import { FunnelLookupType } from '../../../models/funnel_lookup'
import axios from 'axios'
import moment = require('moment')
import lodash from 'lodash'
import { ActiveUser } from '@/models/editor_active_viewer';

export default Vue.extend({
  components: {
    EditFunnelPageModal,
    ConfirmDeleteModal,
    OnlineUserAvatarGroup
  },
  props: {
    funnel:{
      type: Object
    },
    funnelDomain: {
      type: String
    },
    funnelPages: {
      type: Array as () => FunnelPage[],
      required: true
    },
    funnelStep: {
      type: Object,
      required: true
    },
    stepId: {
      type: String,
      required: true
    },
    is497Plan: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      trafficForControlPage: this.funnelStep.split
        ? this.funnelStep.control_traffic
        : 100,
      pageSettingsModalShown: false,
      funnelPageToEdit: {} as FunnelPage,
      addPageVariationModalShown: false,
      currentLocationId: '',
      screenshotLoaded: false,
      errorInEditingPage: false,
      showDeleteModal: false,
      confirmingNewSplit: false,
      confirmingAdditionalRoutes: false,
      updatingSplit: false,
      stats: {},
      creatingVariation: false,
      loadingStats: true,
      additionalRoutes: [],
      routeAllTraffic: this.funnelStep.split ? false : true
    }
  },
  async mounted() {
    this.currentLocationId = this.$route.params.location_id
    this.routeAllTraffic = this.funnelStep.additional_routes ? this.funnelStep.additional_routes.length > 0 : false;
    try {
      await this.getAdditionalRoutes()
      if (this.funnelStep.split)
        await this.getStepStats();
    } catch (err) {
      console.error(err)
      this.loadingStats = false
    }
  },
  computed: {
    isLevelUpDay() {
			return this.$store.getters['LevelUpDayFlag/isfeatursActive']
		},
     loggedInUser():any{
      return this.$store.state.user.user
    },
    controlPageViewers(){
      const page = this.funnelPages[0]
      const activeUsers = this.$store.state.funnel.editorActiveViewers.find((viewer:any) => viewer.alt_id.includes(`page_${page.id}`) > 0 )


      return activeUsers ? this.filterUsers(activeUsers.active_users) : []
    },
    variationPageViewers(){
      const page = this.funnelPages[1]
      if(!page) return []
      const activeUsers = this.$store.state.funnel.editorActiveViewers.find((viewer:any) => viewer.alt_id.includes(`page_${page.id}`) > 0 )
       return activeUsers ? this.filterUsers(activeUsers.active_users) : []
    },
    controlPage: function(): FunnelPage {
      return this.funnelPages[0]
    },
    variationPage: function(): FunnelPage {
      return this.funnelPages[1]
    },
    controlPageExists: function(): Boolean {
      if (this.controlPage && !isEmpty(this.controlPage)) {
        return true
      }

      return false
    },
    variationPageExists: function(): Boolean {
      if (this.variationPage && !isEmpty(this.variationPage)) {
        return true
      }

      return false
    },
    controlPageBuilderUrl: function(): string {
      if (this.controlPageExists) {
        return this.getBuilderUrl(this.controlPage)
      } else {
        return ''
      }
    },
    controlPagePreviewUrl: function(): string {
      if (this.controlPageExists) {
        return this.getPreviewUrl(this.controlPage)
      } else {
        return ''
      }
    },
    variationPageBuilderUrl: function(): string {
      if (this.variationPageExists) {
        return this.getBuilderUrl(this.variationPage)
      } else {
        return ''
      }
    },
    variationPagePreviewUrl: function(): string {
      if (this.variationPageExists) {
        return this.getPreviewUrl(this.variationPage)
      } else {
        return ''
      }
    },
    trafficForVariationPage: function(): Number {
      return 100 - this.trafficForControlPage
    },
    computedControlPageTraffic: {
      get() {
        return this.trafficForControlPage;
      },
      set(value){
        this.trafficForControlPage = parseInt(value, 10)
        this.confirmingNewSplit = true;
        this.confirmingAdditionalRoutes = this.additionalRoutes.length > 0;
        this.updatingSplit = this.funnelStep.split ? true : false;
      }
    },
    controlPagePreview: function(): String {
      return (
        this.controlPage.pagePreview || 'https://via.placeholder.com/400x350'
      )
    },
    splitTestRunning: function(): Boolean {
      return this.funnelStep.split ? true : false;
    },
    statsAvailable: function(): Boolean {
      return this.stats[this.controlPage.id]
        || this.stats[this.variationPage.id]
        ? true
        : false;
    }
  },
  methods: {
    filterUsers(activeUsers:ActiveUser[]){
        return activeUsers.filter((user:ActiveUser) => {
        return user.id !== this.loggedInUser.id && !moment(user.date_update.toMillis()).isBefore(
       moment().subtract(15, 'minutes'))
      } )
    },
    getStepStats(){

      return new Promise((resolve, reject) => {
        const funnelId = this.funnel.id;
        const stepId = this.funnelStep.id;
        const fromDate = moment(
          new Date(this.funnelStep.split_started_at.toMillis()),
          'YYYY-MM-DD hh:mm a'
        )
          .toDate()
          .toISOString()
        const toDate = moment(new Date(), 'YYYY-MM-DD hh:mm a')
          .toDate()
          .toISOString()
        const controlUrl = this.controlPage.url;
        const variationUrl = this.variationPage.url;
        const API_ENDPOINT = `/analytics/funnel/aggregate_events?funnel_id=${funnelId}&step_id=${stepId}&from_date=${fromDate}&to_date=${toDate}&page_urls[0]=${controlUrl}&page_urls[1]=${variationUrl}`
        axios
          .get(API_ENDPOINT)
          .then(resp => {
            this.populateStats(resp.data);
            resolve();
          })
          .catch(err => {
            console.error('error while getting stats --> ', err)
            reject(err);
          })
      });
    },
    populateStats(data) {
      this.loadingStats = false
      const stepStats = data.find(x => x.funnel_step_id === this.funnelStep.id) || {};
      const { page_stats: pageStats } = stepStats, stats = {};
      stats[this.controlPage.id] = pageStats.find(x => x.page_id === this.controlPage.id) || {};
      stats[this.variationPage.id] = pageStats.find(x => x.page_id === this.variationPage.id) || {};
      this.stats = stats;
    },
    async getFunnelPageUrl(
      name: string,
      url: string,
      type: string
    ): Promise<string> {
      const funnelPageUrl = generateFunnelPageUrl(name, url, type)
      const uniqueFunnelPageUrl = await getUniqueFunnelUrl(
        this.funnelDomain,
        funnelPageUrl
      )
      return uniqueFunnelPageUrl
    },
    screenshotUrl(funnelPage: FunnelPage) {
      const pageUrl = funnelPage.url
      const domain = this.funnelDomain
      const screenShotEndPoint =
        'https://us-central1-highlevel-staging.cloudfunctions.net/webshotFunction'
      return `${screenShotEndPoint}?url=https://${domain}${pageUrl}`
    },
    editPage(funnelPage: FunnelPage) {
      this.funnelPageToEdit = funnelPage
      this.pageSettingsModalShown = true
    },
    deletePageConfirmation(funnelPage: FunnelPage) {
      this.funnelPageToEdit = funnelPage
      this.showDeleteModal = true
    },
    hidePageSettingsModal() {
      this.pageSettingsModalShown = false
    },
    async updateFunnelPage(updates: { name: string; url: string }) {
      try {
        this.errorInEditingPage = false
        const { name, url } = updates
        this.funnelPageToEdit.name = name

        if (this.funnelPageToEdit.url !== url && this.funnelDomain) {
          this.funnelPageToEdit.url = await this.getFunnelPageUrl(
            name,
            url,
            this.funnelPageToEdit.templateType
          )
        } else if (this.funnelPageToEdit.url !== url && !this.funnelDomain) {
          this.funnelPageToEdit.url = url
        }

        if (this.funnelDomain) {
          await this.updateFunnelPageLookup(this.funnelPageToEdit)
        }
        await this.funnelPageToEdit.save()

        const domains = [this.funnelDomain]
        if(this.splitTestRunning){
          domains.push(`split.testing.${this.funnelDomain}`)
        }
        await updateCachingIndex({
          domains,
          index_type: cacheUpdateEvents.PAGE_PATH_UPDATE,
          event_origin: this.currentLocationId
        })
        this.hidePageSettingsModal()
      } catch (error) {
        console.error('error while updating page url --> ', error)

        this.errorInEditingPage = true
      }
    },
    async deleteEditingFunnelPage() {
      this.showDeleteModal = false

      this.funnelPageToEdit.deleted = true
      await this.funnelPageToEdit.save()

      try {
        const sections = await FunnelSection.getByPageId(
          this.funnelPageToEdit.id,
          this.currentLocationId
        )
        const deletingSections = sections
          ? sections.map((section: FunnelSection) => {
            section.deleted = true
            return section.save()
          })
          : []

        const deletingSectionsFromStorage = []

        if (this.funnelPageToEdit.sectionUrl) {
          const deletingSections = firebase
            .storage()
            .ref(this.funnelPageToEdit.sectionUrl)
            .delete()
          deletingSectionsFromStorage.push(deletingSections)
        }

        await Promise.all(deletingSections.concat(deletingSectionsFromStorage))

      } catch (err) {
        console.error(err)
      }

      await this.deleteFunnelPageLookup(this.funnelPageToEdit)
      this.hidePageSettingsModal()

      if(this.splitTestRunning){
        this.funnelStep.split = false;
        this.funnelStep.control_traffic = 100;
        await this.funnel.updateStep(this.funnelStep);
      }
      setTimeout(() => {
        this.$emit('removePageFromStep', {
          pageId: this.funnelPageToEdit.id,
          stepId: this.stepId
        })
      }, 50)
    },
    getBuilderUrl(page: FunnelPage): string {
      const pageId = page.id
      let url
      if (config.mode === 'dev') {
        url = 'http://localhost:3333'
      } else {
        url = `https://${window.location.hostname}`
      }
      return `${url}/v2/builder/${pageId}`
    },
    getPreviewUrl(page: FunnelPage): string {
      // const pageId = page.id
      let host
      if (config.mode === 'dev') {
        host = 'http://localhost:3344'
      } else {
        host = `https://${window.location.hostname}`
      }

      if (!this.funnelDomain) {
        return `${host}/v2/preview/${page.id}`
      }

      // const pageUrl = encodeURIComponent(page.url)
      const url = `https://${this.funnelDomain}${page.url}`
      return url
      // return `${host}/v2/preview?domain=${this.funnelDomain}&page_url=${pageUrl}`
    },
    hideAddPageVariationModal() {
      this.addPageVariationModalShown = false
    },
    addPageVariation() {
      if(!(this.funnelDomain && this.funnelDomain.length)){
        alert('Please attach a domain from Funnel > Settings to start a Split Test');
        return false;
      }
      this.addPageVariationModalShown = !this.addPageVariationModalShown
    },
    async createBlankPage() {
      this.creatingVariation = true;
      const variationPage = await createVariationPage(this.controlPage, this.funnelStep, this.funnelDomain);
      this.hideAddPageVariationModal();
      setTimeout(()=> {
        this.$emit('addVariationPageToStep', {
          page: variationPage,
          stepId: this.stepId
        })
      }, 50);
    },
    async cloneControlPage() {
      this.creatingVariation = true;
      const variationPage = await cloneControlPage(this.controlPage, this.funnelStep, this.funnelDomain)
      this.hideAddPageVariationModal()
      setTimeout(() => {
        this.$emit('addVariationPageToStep', {
          page: variationPage,
          stepId: this.stepId
        })
      }, 50)
    },
    async updateFunnelPageLookup(funnelPage: FunnelPage) {
      try {
        const lookup = await FunnelLookup.getByTypeId(
          funnelPage.id,
          this.currentLocationId
        )
        lookup.path = getValidFunnelLookupUrl(funnelPage.url)
        await lookup.save()
      } catch (err) {
        console.error(err)
      }
    },
    async deleteFunnelPageLookup(funnelPage: FunnelPage) {
      try {
        const lookup = await FunnelLookup.getByTypeId(
          funnelPage.id,
          this.currentLocationId
        )
        await lookup.delete()
      } catch (error) {
        console.error('Error while deleting lookup for funnel page --> ', error)
      }
    },
    async confirmStartSplit(){
      if(!(this.funnelDomain && this.funnelDomain.length)){
        alert('Please attach a domain from Funnel > Settings to start a Split Test');
        return false;
      }
      this.confirmingNewSplit = true;
      this.confirmingAdditionalRoutes = this.additionalRoutes.length > 0;
      if(!this.funnelStep.split){
        this.trafficForControlPage = 50;
      }
    },
    cancelSplit(){
      this.confirmingNewSplit = false;
      if(!this.funnelStep.split){
        this.trafficForControlPage = 100;
      } else {
        this.trafficForControlPage = this.funnelStep.control_traffic
      }
    },
    async getAdditionalRoutes() {
      try {
        let lookups = await FunnelLookup.getAllByTypeId(this.stepId, this.funnel.locationId)

        this.additionalRoutes = lookups.map(lookup => {
          if (!(lookup.domain === this.funnelDomain && lookup.path === this.funnelStep.url))
            return {
              id: lookup.id,
              domain: lookup.domain,
              path: lookup.path,
              fullPath: lookup.domain + lookup.path
            }
        }).filter(lookup => lookup != undefined)
      } catch (err) {
        console.error(err)
        this.additionalRoutes = []
      }
    },
    async startSplit() {
      try {
        if(!confirm('Are you sure to apply the changes?')) return false;
        this.confirmingNewSplit = false;
        this.confirmingAdditionalRoutes = false;
        this.funnelStep.split = true;
        this.funnelStep.control_traffic = this.computedControlPageTraffic;
        this.funnelStep.split_started_at = firebase.firestore.Timestamp.now();
        this.funnelStep.split_ended_at = null;
        this.funnelStep.route_all_requests = this.additionalRoutes.length ? this.routeAllTraffic : true;
        this.funnelStep.additional_routes = this.routeAllTraffic
          ? this.additionalRoutes.map(({ id: lookup_id, path, domain }) => {
            return {
              lookup_id, path, domain
            }
          })
          : [];

        this.funnel.updateStep(this.funnelStep);
        await this.funnel.save()
        this.confirmingNewSplit = false;
        this.updatingSplit = false;
        const additionalDomains =
          this.routeAllTraffic
            ? this.additionalRoutes.reduce((routes, obj) => {
              return [
                obj.domain,
                `split.testing.${obj.domain}`,
                ...routes,
              ]
            }, []) : [];
        await updateCachingIndex({
          domains: [
            this.funnelDomain,
            `split.testing.${this.funnelDomain}`,
            ...additionalDomains
          ],
          index_type: cacheUpdateEvents.PAGE_PATH_UPDATE,
          event_origin: this.currentLocationId
        })
      } catch (error) {
        console.error(error)
      }
    },
    async deletePage(page: FunnelPage){
      page.deleted = true;
      await page.save();
      await this.deleteFunnelPageLookup(page);
      setTimeout(() => {
        this.$emit('removePageFromStep', {
          pageId: page.id,
          stepId: this.stepId
        })
      }, 50)
    },
    async declareWinner(winnerPage: FunnelPage) {
      try {
        if(!confirm('This will promote this page as control and delete the remaining page')){
          return false;
        }

        let pageIdToArchive
        if(winnerPage.id === this.controlPage.id) {
          // control page declared winner
          await this.deletePage(this.variationPage);
          pageIdToArchive = this.variationPage.id;
        } else {
          // variation page declared winner
          await this.deletePage(this.controlPage);
          pageIdToArchive = this.controlPage.id;
        }

        const additionalDomains =
          this.routeAllTraffic
            ? this.additionalRoutes.reduce((routes, obj) => {
              return [
                obj.domain,
                `split.testing.${obj.domain}`,
                ...routes,
              ]
            }, []) : []

        this.funnelStep.pages.splice(this.funnelStep.pages.indexOf(pageIdToArchive), 1);
        this.funnelStep.split = false;
        this.funnelStep.control_traffic = 100;
        this.funnelStep.additional_routes = [];
        this.funnelStep.route_all_requests = false;
        this.funnelStep.split_ended_at = firebase.firestore.Timestamp.now();
        await this.funnel.updateStep(this.funnelStep);
        await this.funnel.save();

        await updateCachingIndex({
          domains: [
            this.funnelDomain,
            `split.testing.${this.funnelDomain}`,
            ...additionalDomains
          ],
          index_type: cacheUpdateEvents.PAGE_PATH_UPDATE,
          event_origin: this.currentLocationId
        })
      } catch (error) {
        console.error('Error: ', error)
      }
    }
  }
})
</script>
<style scoped>
.steps-item {
  display: flex;
  flex-direction: column;
}
.steps-item--header {
  width: fit-content;
  border-radius: 3px 3px 0px 0px;
  color: white;
  font-style: normal;
  font-weight: 500;
  font-size: 15px;
  line-height: 15px;
  padding: 5px;
}
.steps-item--header i {
  margin-right: 5px;
}
.steps-item--image {
  line-height: 200px;
  text-align: center;
  min-height: 260px;
  position: relative;
  margin: 0px !important;
  border-radius: 0px 4px 4px 4px;
}
.control-block > .steps-item--image {
  border: 2px solid #188bf6;
  border-radius: 0px 4px 4px 4px;
}
.control-block > .steps-item--header {
  background: #188bf6;
}
.variation-block .steps-item--image {
  border: 2px solid #27ae60;
  border-radius: 0px 4px 4px 4px;
}
.variation-block .steps-item--header {
  background: #27ae60;
}
.header-disabled {
  background: #E5E7EB !important;
}
.steps-item--image img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: 100%;
}
.steps-item--controls {
  margin-top: 5px;
}
.steps-split-test-range {
  background: #188bf61a;
}
.declare-winner .--variation button.continue{
  color: #27ae60 !important;
}
.slider {
  margin: auto;
  -webkit-appearance: none;
  position: relative;
  overflow: hidden;
  height: 20px;
  width: 90%;
  cursor: pointer;
  border-radius: 0;
  background: none;
}
.slider::-webkit-slider-runnable-track {
  background: #188bf6;
  height: 4px;
  border-radius: 2px;
}
.slider::-moz-range-track {
  background: #188bf6;
  height: 4px;
  border-radius: 2px;
}
.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 26px;
  height: 20px;
  background: #E4F2FF;
  border: 7px solid #188bf6;
  border-radius: 25px;
  display: block;
  margin-top: -8px;
}
.range-values {
  width: 90%;
  margin: auto;
}
.no-stats {
  text-align: center;
  line-height: 20px;
  padding: 20px;
}
button.continue i {
  margin-left: 5px;
}
.icon-trophy {
  width: 45px;
  height: 45px;
  background-size: 45px 45px;
}
.info-message {
  background: #F3F4F6;
  border-radius: 5px;
  color: #6B7280;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  line-height: 14px;
  text-align: center;
  overflow: hidden;
}
.info-message p {
  padding: 5px 10px;
  display: inline-block;
  width: 100%;
}
.info-message p a {
  color: #188bf6;
}
.show {
  display: block;
}
.hide {
  display: none;
}
.notransition {
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  transition: none !important;
}
.connected-lookups {
  padding: 10px;
  text-align: left;
}
.connected-lookups table {
  width: 100%;
  margin-bottom: 10px;
}
.connected-lookups table tr td {
  text-align: center;
}
.connected-lookups input {
  color: #188bf6;
}
h6.result-warning {
  font-size: 14px;
  padding: 0px 10px 0px 10px;
  text-align: left;
}
</style>
