<template>
  <div class="hl_funnels--card-main">
    <div class="hl_funnels--card-main-header">
      <h3>{{ stepDetails.name }}</h3>
      <ul class="nav nav-tabs justify-content-end" role="tablist">
        <li class="nav-item">
          <router-link
            :to="getRoute('funnel_step_overview', { step_id: stepId, funnel_id: funnelId, location_id: locationId })"
            id="overview-link"
            tag="a"
            class="nav-link"
            active-class="active"
            data-toggle="tab" role="tab" aria-controls="overview-tab" aria-selected="true"
          >
            Overview
          </router-link>
        </li>
        <!-- <li class="nav-item">
          <router-link
            :to="{name: 'funnel_step_automation', params: { step_id: stepId, funnel_id: funnelId, location_id: locationId }}"
            id="automation-link"
            tag="a"
            class="nav-link"
            active-class="active"
            data-toggle="tab" role="tab" aria-controls="automation-tab" aria-selected="false"
          >
            Automation
          </router-link>
        </li> -->
        <li class="nav-item">
          <router-link
            :to="getRoute(orderFormVersion === 1 ? 'funnel_step_product' : 'funnel_step_products', { step_id: stepId, funnel_id: funnelId, location_id: locationId })"
            id="automation-link"
            tag="a"
            class="nav-link"
            active-class="active"
            data-toggle="tab" role="tab" aria-controls="automation-tab" aria-selected="false"
          >
            Products
          </router-link>
        </li>
        <li class="nav-item">
          <router-link
            :to="getRoute('funnel_step_publish', { step_id: stepId, funnel_id: funnelId, location_id: locationId })"
            id="publishing-link"
            tag="a"
            class="nav-link"
            active-class="active"
            data-toggle="tab" role="tab" aria-controls="publishing-tab" aria-selected="false"
          >
            Publishing
          </router-link>
        </li>
      </ul>
    </div>
    <div class="tab-content">
      <router-view
        :funnel="funnel"
        :funnel-domain="funnelDomain"
        :funnel-step="stepDetails"
        :step-id="stepId"
        :funnel-id="funnelId"
        :location-id="locationId"
        :cloning-funnel-step="cloningFunnelStep"
        @deleteFunnelStep="deleteFunnelStep"
        @updateFunnelStep="updateFunnelStep"
        @removePageFromStep="removeFunnelPageFromStep"
        @addFunnelPageToStep="addFunnelPageToStep"
        @cloneFunnelStep="cloneFunnelStep"
      />
    </div>
    <br>
  </div>
</template>
<script lang="ts">
import { defineComponent } from '@vue/composition-api'

import { SALES_FUNNEL_PAGE  } from '../../../util/funnel_consts'
import { User } from '@/models'

export default defineComponent({
  components: { },
  props: {
    funnel: {
      type: Object
    },
    funnelDomain: {
      type: String
    },
    stepDetails: {
      type: Object
    },
    deleteFunnelStep: {
      type: Function
    },
    updateFunnelStep: {
      type: Function
    },
    removeFunnelPageFromStep: {
      type: Function
    },
    addFunnelPageToStep: {
      type: Function
    },
    cloneFunnelStep: {
      type: Function
    },
    cloningFunnelStep: {
      type: Boolean,
      default: false
    },
    orderFormVersion:{
        type:Number,
        default: 1
    }
  },
  data() {
    return {
      SALES_FUNNEL_PAGE:SALES_FUNNEL_PAGE,
      routeMapping: {
        funnel_step_overview: 'funnel-step-overview-v2',
        funnel_step_product: 'funnel-step-product-v2',
        funnel_step_publish: 'funnel-step-publish-v2',
        funnel_step_products: 'funnel-step-products-v2',

      } as { [key: string]: string}
    }
  },
  computed: {
    stepId(): String {
      return this.$route.params.step_id
    },
    funnelId(): String {
      return this.$router.currentRoute.params.funnel_id
    },
    locationId(): String {
      return this.$router.currentRoute.params.location_id
    },
    user(): User | undefined {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  methods: {
    getRoute(routeName: string, params: any) {
      if (this.getSideBarVersion == 'v2') {
        return {
          name: this.routeMapping[routeName],
          params
        }
      } else {
        return {
          name: routeName,
          params
        }
      }
    }
  }
})
</script>
<style>
h3.archived-header {
  font-size: 20px;
}
</style>
