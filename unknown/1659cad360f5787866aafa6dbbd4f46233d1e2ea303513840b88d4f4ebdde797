<template>
  <div class="card">
    <div class="card-header">
      <h3>Payment Methods</h3>
    </div>
    <div v-if="loading" class="card-body payment-container">
      <div class="payment-methods">
        <shimmer class="shimmer" />
      </div>
      <div class="payment-options">
        <div class="options">
          <shimmer class="shimmer" />
          <shimmer class="shimmer" />
        </div>
      </div>
    </div>
    <div v-else-if="activeCard" class="card-body payment-container">
      <div class="payment-methods">
        <div class="credit-card">
          <div class="type">
            <VisaIcon
              v-if="activeCard.cardBrand === 'visa'"
              alt="VISA"
            />
            <MastercardIcon
              v-else-if="activeCard.cardBrand === 'mastercard'"
              alt="MasterCard"
            />
            <AmexIcon
              v-else-if="activeCard.cardBrand === 'amex'"
              alt="AMEX"
            />
            <DinersClubIcon
              v-else-if="activeCard.cardBrand === 'diners_club'"
              alt="Diners Club"
            />
          </div>
          <div class="number field">
            <div class="label">Card Number</div>
            <div class="input">
              ****&nbsp;&nbsp;****&nbsp;&nbsp;****&nbsp;&nbsp;{{
                activeCard.cardNumber
              }}
            </div>
          </div>
          <div class="details">
            <div class="name field">
              <div class="label">Name</div>
              <div class="input">{{ activeCard.nameOnCard }}</div>
            </div>
            <div class="cvv field">
              <div class="label">CVV</div>
              <div class="input">***</div>
            </div>
            <div class="expiry field">
              <div class="label">Expiry Date</div>
              <div class="input">
                {{ expiryMonth }}&nbsp;/&nbsp;{{ expiryYear }}
              </div>
            </div>
          </div>
          <div class="default">
            <div class="title">
              {{
                activeCardIsPrimary ? 'Primary Card' : 'Make this card primary'
              }}
            </div>
            <div class="toggle" v-if="!activeCardIsPrimary">
              <UIToggle
                id="primary_card"
                v-model="cardSetToPrimary"
              />
              <label class="tgl-btn" for="primary_card"></label>
            </div>
          </div>
        </div>
        <div class="switcher" v-if="noOfCards > 1">
          <div class="button" @click="$emit('setActiveCard', -1)">
            <i class="fa fa-angle-left" aria-hidden="true"></i>
          </div>
          <div class="position">
            <span>{{ currentCardIndex + 1 }}</span
            >/{{ noOfCards }}
          </div>
          <div class="button" @click="$emit('setActiveCard', 1)">
            <i class="fa fa-angle-right" aria-hidden="true"></i>
          </div>
        </div>
      </div>
      <div class="payment-options">
        <div class="options">
          <button @click="$emit('addPaymentMethod')" class="btn add-card">
            <i class="fa fa-plus" aria-hidden="true"></i>Add Card
          </button>
          <button
            v-if="noOfCards > 1"
            @click="removePaymentMethod"
            class="btn remove-card"
          >
            Remove Current Card
          </button>
        </div>
        <!-- <div class="notification success">
          <span>Added!</span>
        </div> -->
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import Shimmer from '../../common/shimmers/Shimmer.vue'
import VisaIcon from '@/assets/pmd/img/saas/visa.svg'
import MastercardIcon from '@/assets/pmd/img/saas/mastercard.svg'
import AmexIcon from '@/assets/pmd/img/saas/amex.svg'
import DinersClubIcon from '@/assets/pmd/img/saas/diners.svg'

export default Vue.extend({
  components: {
    Shimmer,
    VisaIcon,
    MastercardIcon,
    AmexIcon,
    DinersClubIcon,
  },
  props: {
    noOfCards: {
      type: Number,
    },
    currentCardIndex: {
      type: Number,
      default: 0,
    },
    activeCard: {
      type: Object,
    },
    activeCardIsPrimary: {
      type: Boolean,
      default: false,
    },
    makeActiveCardPrimary: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    expiryMonth(): string {
      const expMonth = this.activeCard.expiryMonth
      return expMonth < 10 ? `0${expMonth}` : `${expMonth}`
    },
    expiryYear(): string {
      return `${this.activeCard.expiryYear}`.slice(-2)
    },
    cardSetToPrimary: {
      get(): boolean {
        return this.makeActiveCardPrimary
      },
      set(value: boolean) {
        if (value) {
          this.$emit('makePrimaryCard', this.activeCard._id)
        }
      },
    },
  },
  methods: {
    // changePrimaryPaymentMethod() {
    //   this.$emit('makePrimaryCard', this.activeCard._id)
    // },
    removePaymentMethod() {
      if (this.activeCard) {
        this.$emit('removePaymentMethod', this.activeCard._id)
      }
    },
  },
})
</script>
<style scoped>
@media only screen and (min-width: 1500px) {
  .payment-container {
    flex-direction: row !important;
  }

  .payment-methods {
    margin-right: 12px;
    margin-bottom: 0px !important;
  }

  .payment-options {
    margin-left: 12px;
  }
}

.payment-methods .shimmer {
  width: 365px;
  height: 222px;
}

.payment-options .shimmer {
  width: 100%;
  height: 32px;
}

.payment-container {
  display: flex;
  flex-direction: column;
}

.payment-methods {
  margin-bottom: 12px;
}

.credit-card {
  background: #667eea;
  box-shadow: 0px 1px 7px rgba(0, 0, 0, 0.11);
  width: 365px;
  height: 222px;
  color: white;
  padding: 16px 24px;
  margin: auto;
}

.credit-card .type {
  display: flex;
  justify-content: end;
}

.credit-card .type img {
  height: 17px;
}

.credit-card .field .label {
  font-size: 12px;
  line-height: 14px;
  margin-bottom: 3px;
}

.credit-card .field .input {
  background: rgba(255, 255, 255, 0.11);
  border-radius: 3px;
  padding: 8px;
  letter-spacing: 2px;
  line-height: 14px;
}

.credit-card .field.number {
  margin-bottom: 16px;
}

.credit-card .field.number .input {
  font-size: 18px;
  word-spacing: 20px;
  letter-spacing: 4px;
  padding: 12px 16px;
  line-height: 21px;
  white-space: nowrap;
}

.credit-card .details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.credit-card .details .name {
  flex: 1;
  max-width: 55%;
}

.credit-card .details .name .input {
  padding: 8px 16px;
  font-weight: bold;
  text-transform: uppercase;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.credit-card .details .cvv {
  margin: 0 12px;
}

.credit-card .details .expiry .input {
  font-weight: bold;
}

.credit-card .default {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 16px;
}

.credit-card .default .title {
  margin-right: 10px;
  font-size: 13px;
}

.credit-card .default .tgl-light:checked + .tgl-btn {
  background: #4c51bf;
}

.credit-card .default .tgl-light:checked + .tgl-btn:after {
  background: white;
}

.payment-methods .switcher {
  display: flex;
  margin-top: 8px;
  align-items: center;
  justify-content: center;
}

.payment-methods .switcher .button {
  background: #ebf8ff;
  border-radius: 10px;
  width: 20px;
  height: 20px;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin: 0 12px;
  color: #3182ce;
  font-weight: bold;
  user-select: none;
}

.payment-methods .switcher .position {
  font-size: 12px;
  color: gray;
}

.payment-methods .switcher .position span {
  color: #3182ce;
}

.payment-options {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
}

.payment-options .btn {
  border-radius: 3px;
  font-size: 13px;
  line-height: 15px;
  width: 100%;
}

.payment-options .add-card {
  background: #63b3ed;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payment-options .add-card i {
  margin-right: 6px;
}

.payment-options .remove-card {
  color: #fc8181;
  border: 1px solid #fc8181;
  background: white;
}

.payment-options .notification {
  align-self: flex-end;
  font-style: italic;
}

.payment-options .notification.success {
  color: #68d391;
}

.payment-options .notification.failed {
  color: #fc8181;
}
</style>
