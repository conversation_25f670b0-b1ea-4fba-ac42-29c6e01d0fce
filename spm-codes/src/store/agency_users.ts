import { <PERSON><PERSON><PERSON>, ActionContext } from 'vuex'
import Vue from 'vue'
import { User } from '../models'
import { RootState, AgencyUsersState } from './state_models'

let agencyUserListner
export const agencyUsers: Module<AgencyUsersState, RootState> = {
  namespaced: true,
  state: {
    users: {},
    locationId: undefined
  },
  getters: {
    getById: (state: AgencyUsersState, getters, RootState) => {
      return (id: string) => state.users[id]
    }
  },
  mutations: {
    clearAll: (state: AgencyUsersState) => {
      // Need to decide
      if (agencyUserListner) {
        agencyUserListner()
      }

      state.users = {}
      state.locationId = undefined
    },
    addUpdate(state: AgencyUsersState, user: any) {
      Vue.set(state.users, user.id, user)
    }
  },
  actions: {
    async syncGet(context: ActionContext<AgencyUsersState, RootState>, id: string) {
      if (context.state.users[id]) {

        return new User(context.state.users[id])
      } else if(context.rootState.users.users[id]) {
        return new User(context.rootState.users.users[id])
      }

      const user = await User.getById(id)
      if (user) {
        context.commit('addUpdate', {
          id: user.id,
          ...user.data
        })
        return user
      }
    },
    logout: {
      root: true,
      handler: async (context: ActionContext<AgencyUsersState, RootState>) => {
        context.commit('clearAll')
      }
    },
    clearAll: {
      handler: async (context: ActionContext<AgencyUsersState, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
