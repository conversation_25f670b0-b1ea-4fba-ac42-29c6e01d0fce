<template>
  <div class="hl_tasks--note">
    <div class="hl_products--item-body">
      <div class="hl_products--item-text">
        <p v-html="body" style="white-space: pre-wrap"></p>
      </div>
    </div>
    <div class="hl_tasks--item-footer">
      <p>
        Date:
        <strong>{{ dateAdded }}</strong>
      </p>
      <p v-if="user">
        By:
        <strong>{{ user.fullName }}</strong>
      </p>
      <div class="note-actions">
        <i
          class="icon icon-edit --blue"
          style="margin-right: 10px"
          @click.prevent="editNote"
        ></i>
        <i class="icon icon-trash --gray" @click.prevent="removeNote"></i>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { User, AuthUser, getCountryDateFormat } from '@/models/'
import Note from '@/models/api/Note'
import LocationNote from '@/models/api/LocationNote'
import moment from 'moment-timezone'
import { UxMessage } from '@/util/ux_message'

export default Vue.extend({
  props: {
    note: Note | LocationNote,
    mode: String,
  },
  inject: ['uxmessage'],
  data() {
    return {
      user: undefined as User | undefined,
      authUser: {} as AuthUser,
      getCountryDateFormat: getCountryDateFormat,
      plainText: '',
      body: '' as any,
    }
  },
  watch:{
    note(){
      this.plainText = this.note.body.replace(/\n/g, '<br/>')
      this.body = this.addUrl(this.plainText)
    }
  },
  async created() {
    this.authUser = await this.$store.dispatch('auth/get')
    this.plainText = this.note.body.replace(/\n/g, '<br/>')
    this.body = this.addUrl(this.plainText)
    // console.log(this.body)
    if (this.note.userId) {
      this.user = await this.$store.dispatch(
        'agencyUsers/syncGet',
        this.note.userId
      )
    }
  },
  methods: {
    async removeNote() {
      if (confirm('Are you sure you want to delete this note?')) {
        if(this.note.contactId){
          try {
            let resonse = await this.note.delete()
            if(resonse.succeded){
              this.$emit('NoteDeleted')
            }
          } catch (error) {
            const message = error?.response?.data?.message
              ? error.response.data.message.join(', ')
              : error.message
            this.uxmessage(UxMessage.errorType(message), true)
          }
        }else{
          try {
            let resonse = await this.note.delete()
            if(resonse.succeded){
              this.$emit('NoteDeleted')
            }
          } catch (error) {
            const message = error?.response?.data?.message
              ? error.response.data.message.join(', ')
              : error.message
            this.uxmessage(UxMessage.errorType(message), true)
          }
        }
      }
    },
    addUrl(plainText: string) {
      var urlRegex = /[-a-zA-Z0-9@:%_\+.~#?&//=!]{2,256}\.[a-z]{2,4}\b(\/[-a-zA-Z0-9@:%_\+.~#?&//=!]*)?/gi
      // var urlRegex = /(?:(?:https?|ftp|file):\/\/|www\.|ftp\.)(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[-A-Z0-9+&@#\/%=~_|$?!:,.])*(?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|[A-Z0-9+&@#\/%=~_|$])/igm;
      return plainText.replace(urlRegex, function (url, b, c) {
        var url2 = c == 'www.' ? 'http://' + url : url
        if (url2 === url && !url.includes('http')) url2 = 'http://' + url
        return '<a href="' + url2 + '" target="_blank">' + url + '</a>'
      })
    },
    editNote() {
      if (this.mode && this.mode == 'modal') {
        this.$emit('editNote', this.note.id)
      } else {
        const params = { ...this.$route.params } // make sure to send params to retain record navigation
        if (!this.note.contactId) {
          this.$router.push({
            name: 'account_detail',
            params: { account_id: this.note.accountId, ...params },
            query: Object.assign({}, this.$route.query, {
              note_id: this.note.id,
            }),
          })
        } else {
          this.$router.push({
            name: 'contact_detail',
            params: { contact_id: this.note.contactId, ...params },
            query: Object.assign({}, this.$route.query, {
              note_id: this.note.id,
            }),
          })
        }
      }
    },
  },
  computed: {
    dateAdded() {
      return moment(this.note.dateAdded).format(
        getCountryDateFormat('month date year, time')
      )
    }
  },
})
</script>

<style scoped>
.icon-trash {
  cursor: pointer;
}

.note-actions {
  margin-left: auto;
}
.hl_tasks--note {
  position: relative;
  font-size: 0.75rem;
  margin-bottom: 25px;
}
p {
  white-space: unset !important;
}
</style>
