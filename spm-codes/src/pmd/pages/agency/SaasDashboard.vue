<template>
  <div>
    <SideBarV2 v-if="getSideBarVersion === 'v2'"/>
    <SideBar v-else />
    <TopBar />
    <section class="hl_wrapper">
      <section
        class="hl_agency hl_agency-sales-resources"
      >
        <div class="saas-dash__top-bar">
          <div class="saas-dash__top-bar__page-name">SaaS Dashboard</div>
          <div class="saas-dash__top-bar__page-menu">
            <!-- <li v-b-tooltip.hover title="Coming Soon">Overview</li> -->
            <!-- <li v-b-tooltip.hover title="Coming Soon">Branding</li> -->
            <li class="--active">Plans &amp; Pricing</li>
            <!-- <li v-b-tooltip.hover title="Coming Soon">Advanced Settings</li> -->
            <!-- <li v-b-tooltip.hover title="Coming Soon">Support</li> -->
          </div>
        </div>
        <div class="saas-dash__page-content">
          <plan-builder />
          <!-- <br/>
          <div class="row">
            <default-twilio-rebilling class="col-lg-6" type="twilio"/>
            <default-twilio-rebilling class="col-lg-6"/>
          </div> -->
        </div>
      </section>
    </section>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import TopBar from '../../components/TopBar.vue'
import SideBar from '../../components/agency/SideBar.vue'
import SideBarV2 from '../../components/sidebar/SideBar.vue'
import { User } from '@/models'

import PlanBuilder from "@/pmd/components/saas/agency/dashboard/PlanBuilder.vue";
// import DefaultTwilioRebilling from "@/pmd/components/saas/agency/dashboard/DefaultTwilioRebilling.vue";
import { trackGaEvent } from "@/util/helper";

export default Vue.extend({
  components: { TopBar, SideBar, PlanBuilder, SideBarV2 },
  created(){
    if(!this.agencyInSaasPlan) {
      trackGaEvent('SaasPlanBuilder', this.company.id, `Unable to access SaaS Dashboard. Route guard preevented.`, 1);
      this.$router.replace('/');
    }
  },
  computed:{
    company() {
      return this.$store.state.company.company
    },
    agencyInSaasPlan(): boolean {
      return this.$store.getters['company/inSaasPlan']
    },
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  }
})
</script>

<style lang="scss">
.saas-dash__top-bar{
  height: 54px;
  width: 100%;
  background-color: #ffffff;
  box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.11);
  padding-top: 8px;

  display: flex;
  align-items: center;

  .saas-dash__top-bar__page-name{
    font-weight: 500;
    font-size: 19px;
    line-height: 22px;
    color: #2D3748;
    padding: 16px 20px;
    cursor: pointer;

    border-right: 1px solid #E2E8F0;
  }
  .saas-dash__top-bar__page-menu {
    padding: 0px 20px;
    display: flex;
    align-items: center;
    li {
      padding: 6px 12px;
      font-size: 14px;
      line-height: 16px;
      letter-spacing: 0.3px;
      color: #718096;
      cursor: pointer;
      list-style: none;
      &.--active{
        background: #EBF8FF;
        border-radius: 1px;
        color: #3182CE;
      }
    }
  }
}
.saas-dash__page-content{
  padding: 16px;
}
</style>
