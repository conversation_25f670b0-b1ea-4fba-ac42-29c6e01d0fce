import { RootState, AuthState } from './state_models'
import { AuthUser } from '../models'
import { Module, ActionContext } from 'vuex'
import Vue from 'vue'
import localStorage from 'store'
import axios from 'axios'
import firebase from 'firebase/app'
import 'firebase/auth'
import moment from 'moment'
import restAgentV1 from '../restAgent'

export const auth: Module<AuthState, RootState> = {
  namespaced: true,
  state: {
    user: undefined,
    locationId: undefined as undefined | string
  },
  mutations: {
    setLocationId: (state: AuthState, locationId: string) => {
      state.locationId = locationId
    },
    set: (state: AuthState, payload: AuthUser) => {
      state.user = payload
    },
    delete: (state: AuthState) => {
      state.user = undefined
    }
  },
  actions: {
    refreshFirebaseToken: async (
      context: ActionContext<AuthState, RootState>,
      params: { [key: string]: any }
    ) => {
      //
      if (
        !params.locationId ||
        (context.state.locationId &&
          params.locationId === context.state.locationId && !params.refresh)
      ) {
        return
      }
      context.commit('setLocationId', params.locationId)
      const {
        activeLocations,
        isChanged
      } = await context.dispatch(
        'locations/addToActiveLocation',
        params.locationId && typeof params.locationId === 'string'
          ? [params.locationId]
          : params.locationId,
        { root: true }
      )
      if (!params.refresh && !isChanged) {
        return
      }
      try {
        const response = await axios.get(
          `/signin/refresh?version=2&location_id=${activeLocations}`
        )
        if (response && response.status === 200) {
          await firebase.auth().signInWithCustomToken(response.data.token)
          localStorage.set('refreshedToken', response.data.token)
          restAgentV1.setApiKey(response.data.token)
        }
      } catch (e) {}
    },
    get: (context: ActionContext<AuthState, RootState>) => {
      return new Promise((resolve, reject) => {
        if (context.state.user) {
          return resolve(context.state.user)
        }
        const b64String = localStorage.get('a')
        if (!b64String) return reject('The user is not logged in')

        const user = JSON.parse(atob(b64String))
        context.commit('set', user)
        return resolve(user)
      })
    },
    set: (context: ActionContext<AuthState, RootState>, payload: AuthUser) => {
      context.commit('set', payload)
      .set('a', btoa(JSON.stringify(payload)))
      localStorage.set('loginDate', moment().toDate())
      const hostName = window.location.hostname.split('.')
      const hostLength = hostName.length
      let domainName = 'localhost'
      if (hostName[0] !== 'localhost') {
        domainName = `.${hostName[hostLength - 2]}.${hostName[hostLength - 1]}`
      }

      document.cookie = `a=${btoa(
        JSON.stringify(payload)
      )};domain=${domainName};path=/;max-Age=********;`
    },
    logout: {
      root: true,
      handler: async (context: ActionContext<AuthState, RootState>) => {
        localStorage.remove('app_referer')
        context.commit('delete')
        localStorage.remove('a')
        localStorage.remove('loginDate')
        if ( window.pendo) {
          window.pendo.clearSession();
        }
        document.cookie =
          'a=; Max-Age=-********;domain=.gohighlevel.com;path=/;'

        if (window.google && google.accounts) {
          google.accounts.id?.disableAutoSelect();
        }
      }
    }
  }
}
