import BaseService from './BaseService'
import servicesConfig from '../config/services'

class LocationService extends BaseService {
  constructor(baseUrl: string) {
    super(baseUrl)
  }

  ids = ids => this.requests.post(`/ids`, { ids })
  getTags = (locationId: string) => this.requests.get(`/${locationId}/tags`)
  search = (companyId, deleted, status?, skip?, limit?, query?) =>
    this.requests.get(
      `/search?deleted=${deleted}&companyId=${companyId}${
        status ? `&status=${status}` : ''
      }${skip ? `&skip=${skip}` : ''}${limit ? `&limit=${limit}` : ''}${
        query ? `&query=${query}` : ''
      }`
    )
  searchQuery = (companyId, deleted, query) =>
    this.requests.get(
      `/search?deleted=${deleted}&companyId=${companyId}&query=${query}&limit=50`
    ) 
  CustomValues = {
    create: (locationId: string, data: { [key: string]: any }) =>
      this.requests.post(`/${locationId}/customValues/`, data),
    read: (locationId: string, customValueId: string) =>
      this.requests.get(`/${locationId}/customValues/${customValueId}`),
    update: (
      locationId: string,
      customValueId: string,
      data: { [key: string]: any }
    ) =>
      this.requests.put(`/${locationId}/customValues/${customValueId}`, data),
    delete: (locationId: string, customValueId: string) =>
      this.requests.delete(`/${locationId}/customValues/${customValueId}`),
    list: (locationId: string) =>
      this.requests.get(`/${locationId}/customValues/`),
  }

  CustomFields = {
    create: (locationId: string, data: { [key: string]: any }) =>
      this.requests.post(`/${locationId}/customFields/`, data),
    read: (locationId: string, customFieldId: string) =>
      this.requests.get(`/${locationId}/customFields/${customFieldId}`),
    update: (
      locationId: string,
      customFieldId: string,
      data: { [key: string]: any }
    ) =>
      this.requests.put(`/${locationId}/customFields/${customFieldId}`, data),
    delete: (locationId: string, customFieldId: string) =>
      this.requests.delete(`/${locationId}/customFields/${customFieldId}`),
    list: (locationId: string) =>
      this.requests.get(`/${locationId}/customFields/`),
  }
  Tasks = {
    list: (queryData:{ [key: string]: any }) =>
      this.requests.get(`/${queryData.locationId}/tasks?isLocation=${queryData.isLocation}${ queryData.userId ? `&userId=${queryData.userId}` : ''}${ queryData.contactId ? `&contactId=${queryData.contactId}` : ''}`),
    read: (locationId: string,taskId:string) =>
      this.requests.get(`/${locationId}/tasks/${taskId}`),
    delete: (locationId: string,taskId:string) =>
      this.requests.delete(`/${locationId}/tasks/${taskId}`),
    accountTaskUpdate:(locationId: string,taskId:string,queryData:{ [key: string]: any }) =>
      this.requests.put(`/${locationId}/tasks/${taskId}`,queryData),
    accountTaskCreate:(locationId: string,queryData:{ [key: string]: any }) =>
      this.requests.post(`/${locationId}/tasks`,queryData),
  }
  Notes = {
    list: (locationId: string) =>
      this.requests.get(`/${locationId}/notes`),
    read: (locationId: string,noteId:string) =>
      this.requests.get(`/${locationId}/notes/${noteId}`),
      create:(locationId: string,queryData:{ [key: string]: any }) =>
      this.requests.post(`/${locationId}/notes`,queryData),
    update:(locationId: string,noteId:string,queryData:{ [key: string]: any }) =>
      this.requests.put(`/${locationId}/notes/${noteId}`,queryData),
    delete: (locationId: string,noteId:string) =>
      this.requests.delete(`/${locationId}/notes/${noteId}`),
  }
}

const instance = new LocationService(servicesConfig.Locations)

export default instance
