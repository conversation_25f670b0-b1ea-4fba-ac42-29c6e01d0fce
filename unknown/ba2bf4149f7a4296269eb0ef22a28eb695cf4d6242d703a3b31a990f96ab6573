<template>
  <div id="review-sms" class="card">
    <div class="card-header --no-right-padding card-header--withbutton">
      <div class="image-toggle__btn">
        <div class="toggle">
          <UIToggle
            id="tgl"
            v-model="smsSettings.enabled"
            @change="toggleSwitched"
          />
          <label class="tgl-btn" for="tgl" id="toggle_sms_settings"></label>
        </div>
      </div>
      <h3 @click="fetchData()">Customize Review SMS </h3>
    </div>
    <div class="card-body">
      <div class="two-part">
        <div>
          <div class="image-config">
            <div>
              <label class="secondary-header"> Request with Image</label>
              <div class="upload-box">
                <vue2Dropzone
                  v-show="!photoUrl"
                  class="picture drag_drop"
                  @vdropzone-file-added="vfileAdded"
                  :options="dropzoneOptions"
                  :include-styling="false"
                  id="customer_photo_dropzone"
                  ref="file_drop_zone"
                ></vue2Dropzone>
                <img v-if="this.photoUrl" alt="Logo" :src="this.photoUrl" />
                <UIButton
                  type="button"
                  id="smsChangePhoto"
                  use="secondary"
                  class="justify-center"
                >
                  <span v-if="this.photoUrl">Change</span>
                  <span v-else>Add image</span>
                </UIButton>
              </div>
            </div>
            <div class="image-toggle__btn">
              <p>Image</p>
              <div class="toggle">
                <UIToggle
                  id="imgenebled"
                  v-model="smsSettings.image_enabled"
                />
                <label
                  class="tgl-btn"
                  for="imgenebled"
                  id="sms_image_enabled"
                ></label>
              </div>
            </div>
          </div>
          <div class="sms-text-box">
            <div>
              <label class="secondary-header"> SMS sent to User</label>
            </div>

            <UITextAreaGroup
              rows="7"
              label="Message"
              placeholder="Message"
              v-model="smsSettings.message"
            ></UITextAreaGroup>
          </div>
          <div
            class="communication"
            v-if="review_request_behaviour === 'custom_schedule'"
          >
            <form>
              <div class="form-group">
                <label>When to send SMS after check-in?</label>
                <div class="dropdowns">
                  <div class="dropdown">
                    <button
                      class="btn btn-ghl_lite dropdown-toggle"
                      type="button"
                      id="reviw_time"
                      data-toggle="dropdown"
                      aria-haspopup="true"
                      aria-expanded="false"
                    >
                      <template
                        v-if="
                          local_after_check_in_time.unit === 'hours' &&
                          local_after_check_in_time.value == 0 &&
                          local_after_check_in_time.type != 'custom'
                        "
                      >
                        Immediately
                      </template>
                      <template
                        v-else-if="
                          local_after_check_in_time.unit === 'hours' &&
                          local_after_check_in_time.value == 1 &&
                          local_after_check_in_time.type != 'custom'
                        "
                      >
                        1 Hour
                      </template>
                      <template
                        v-else-if="
                          local_after_check_in_time.unit === 'hours' &&
                          local_after_check_in_time.value == 2 &&
                          local_after_check_in_time.type != 'custom'
                        "
                      >
                        2 Hours
                      </template>
                      <template
                        v-else-if="
                          local_after_check_in_time.unit === 'hours' &&
                          local_after_check_in_time.value == 4 &&
                          local_after_check_in_time.type != 'custom'
                        "
                      >
                        4 Hours
                      </template>
                      <template v-else> Custom </template>
                    </button>
                    <div class="dropdown-menu" aria-labelledby="reviw_time">
                      <span
                        class="dropdown-item"
                        @click="afterChckinTimeChanged('hours', 0)"
                      >
                        Immediately</span
                      >
                      <span
                        class="dropdown-item"
                        @click="afterChckinTimeChanged('hours', 1)"
                      >
                        1 Hour</span
                      >
                      <span
                        class="dropdown-item"
                        @click="afterChckinTimeChanged('hours', 2)"
                      >
                        2 Hours</span
                      >
                      <span
                        class="dropdown-item"
                        @click="afterChckinTimeChanged('hours', 4)"
                      >
                        4 Hours</span
                      >
                      <span
                        class="dropdown-item"
                        @click="afterChckinTimeChanged('hours', null, 'custom')"
                      >
                        Custom</span
                      >
                      <!--   <a class="dropdown-item" href="#!">1 Hour</a>
                      <a class="dropdown-item" href="#!">2 Hours</a>
                      <a class="dropdown-item" href="#!">4 Hours</a>
                      <a class="dropdown-item" href="#!">Custom</a> -->
                    </div>
                  </div>

                  <template v-if="local_after_check_in_time.type === 'custom'">
                    <div class="customCheckinTime">
                      <input
                        type="number"
                        name="when-send-sms"
                        id="sms_check_in_custom"
                        min="0"
                        v-model="local_after_check_in_time.value"
                        class="form-control"
                      />
                      <div class="dropdown">
                        <button
                          class="btn btn-ghl_lite dropdown-toggle"
                          type="button"
                          id="unitTypeDropDown"
                          data-toggle="dropdown"
                          aria-haspopup="true"
                          aria-expanded="false"
                        >
                          <template
                            v-if="local_after_check_in_time.unit == 'hours'"
                          >
                            Hour(s)
                          </template>
                          <template
                            v-if="local_after_check_in_time.unit == 'days'"
                          >
                            Day(s)
                          </template>
                        </button>
                        <div
                          class="dropdown-menu"
                          aria-labelledby="unitTypeDropDown"
                        >
                          <span
                            class="dropdown-item"
                            @click="
                              afterChckinTimeChanged('days', null, 'custom')
                            "
                          >
                            Day(s)</span
                          >
                          <span
                            class="dropdown-item"
                            @click="
                              afterChckinTimeChanged('hours', null, 'custom')
                            "
                          >
                            Hour(s)</span
                          >
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </form>

            <form>
              <div class="form-group">
                <label>Until clicked, repeat this every</label>
                <div class="dropdowns">
                  <div class="dropdown">
                    <button
                      class="btn btn-ghl_lite dropdown-toggle"
                      type="button"
                      id="reviw_repeat_time"
                      data-toggle="dropdown"
                      aria-haspopup="true"
                      aria-expanded="false"
                    >
                      <template
                        v-if="
                          local_repeat_review_request_in.value == -1 &&
                          local_repeat_review_request_in.type != 'custom'
                        "
                      >
                        Don't Repeat
                      </template>
                      <template
                        v-else-if="
                          local_repeat_review_request_in.unit === 'days' &&
                          local_repeat_review_request_in.value == 3 &&
                          local_repeat_review_request_in.type != 'custom'
                        "
                      >
                        3 Days
                      </template>
                      <template
                        v-else-if="
                          local_repeat_review_request_in.unit === 'days' &&
                          local_repeat_review_request_in.value == 7 &&
                          local_repeat_review_request_in.type != 'custom'
                        "
                      >
                        1 Week
                      </template>
                      <template
                        v-else-if="
                          local_repeat_review_request_in.unit === 'days' &&
                          local_repeat_review_request_in.value == 15 &&
                          local_repeat_review_request_in.type != 'custom'
                        "
                      >
                        15 Days
                      </template>
                      <template
                        v-else-if="
                          local_repeat_review_request_in.unit === 'days' &&
                          local_repeat_review_request_in.value == 30 &&
                          local_repeat_review_request_in.type != 'custom'
                        "
                      >
                        1 Month
                      </template>
                      <template v-else> Custom </template>
                    </button>
                    <div
                      class="dropdown-menu"
                      aria-labelledby="reviw_repeat_time"
                    >
                      <span
                        class="dropdown-item"
                        @click="repeateReviewRequestTimeChanged('hours', -1)"
                      >
                        Don't Repeat</span
                      >
                      <span
                        class="dropdown-item"
                        @click="repeateReviewRequestTimeChanged('days', 3)"
                      >
                        3 Days</span
                      >
                      <span
                        class="dropdown-item"
                        @click="repeateReviewRequestTimeChanged('days', 7)"
                      >
                        1 Week</span
                      >
                      <span
                        class="dropdown-item"
                        @click="repeateReviewRequestTimeChanged('days', 15)"
                      >
                        15 Days</span
                      >
                      <span
                        class="dropdown-item"
                        @click="repeateReviewRequestTimeChanged('days', 30)"
                      >
                        1 Month</span
                      >
                      <span
                        class="dropdown-item"
                        @click="
                          repeateReviewRequestTimeChanged('days', 1, 'custom')
                        "
                      >
                        Custom</span
                      >
                      <!--   <a class="dropdown-item" href="#!">1 Hour</a>
                      <a class="dropdown-item" href="#!">2 Hours</a>
                      <a class="dropdown-item" href="#!">4 Hours</a>
                      <a class="dropdown-item" href="#!">Custom</a> -->
                    </div>
                  </div>

                  <template
                    v-if="local_repeat_review_request_in.type === 'custom'"
                  >
                    <div class="customCheckinTime">
                      <input
                        type="number"
                        name="when-repeat-sms"
                        id="sms_check_in_repeat_custom"
                        min="0"
                        v-model="local_repeat_review_request_in.value"
                        class="form-control"
                      />
                      <div class="dropdown">
                        <button
                          class="btn btn-ghl_lite dropdown-toggle"
                          type="button"
                          id="unitTypeDropDown"
                          data-toggle="dropdown"
                          aria-haspopup="true"
                          aria-expanded="false"
                        >
                          <template
                            v-if="
                              local_repeat_review_request_in.unit == 'hours'
                            "
                          >
                            Hour(s)
                          </template>
                          <template
                            v-if="local_repeat_review_request_in.unit == 'days'"
                          >
                            Day(s)
                          </template>
                        </button>
                        <div
                          class="dropdown-menu"
                          aria-labelledby="sms_check_in_repeat_custom"
                        >
                          <span
                            class="dropdown-item"
                            @click="
                              repeateReviewRequestTimeChanged(
                                'days',
                                null,
                                'custom'
                              )
                            "
                          >
                            Day(s)</span
                          >
                          <span
                            class="dropdown-item"
                            @click="
                              repeateReviewRequestTimeChanged(
                                'hours',
                                null,
                                'custom'
                              )
                            "
                          >
                            Hour(s)</span
                          >
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </form>
            <form>
              <div class="row">
                <div class="col-sm-6">
                  <div
                    class="form-group"
                    v-if="
                      local_repeat_review_request_in.value &&
                      local_repeat_review_request_in.value != '-1'
                    "
                  >
                    <label>Maximum retries</label>
                    <div>
                      <input
                        type="number"
                        name="max-repeat-sms"
                        id="sms_check_in_repeat_count"
                        min="1"
                        v-model.number="smsSettings.repeat_count"
                        class="form-control"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="save-button-holder">
            <UIButton
              type="button"
              use="primary"
              @click.stop="validateBeforeSubmit"
              :disabled="saving"
              class="justify-center"
              id="sms_settings_save"
            >
              <i class="icon mr-2" :class="saving ? 'icon-clock' : 'icon-ok'"></i>
              {{ saveButtonText }}
            </UIButton>
          </div>
        </div>
        <div>
          <EmulatorComponent :content="emulatorContent" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// @ts-nocheck
import Vue from 'vue'
import EmulatorComponent from '../EmulatorComponent.vue'

import vue2Dropzone from 'vue2-dropzone'
import 'vue2-dropzone/dist/vue2Dropzone.min.css'
import { Location } from '@/models'
import { v4 as uuid } from 'uuid'
import firebase from 'firebase/app'

export default Vue.extend({
  props:{
    location:Object as Location,
    review_request_behaviour:String
  },
  data() {
    return {
      local_after_check_in_time: {
        unit: 'hours',
        value: 0,
      },
      local_repeat_review_request_in: {
        unit: 'hours',
        value: -1,
      },
      smsSettings: {
        message: '',
        after_check_in_hours: '0',
        repeat_count: 5,
        image_enabled: false,
      } as { [key: string]: any },
      dropzoneOptions: {
        url: '/file/post',
        maxFilesize: 2.5,
        clickable: '#smsChangePhoto',
        resizeWidth: 1024,
        resizeHeight: 1024,
      },
      photoUrl: null as String | undefined,
      currentLocationId: '',
      saving: false as Boolean,
    }
  },
  components: {
    vue2Dropzone,
    EmulatorComponent,
  },
  computed: {
    emulatorContent: function () {
      let message = this.smsSettings.message || ''
      message = message.replace(
        '{{review_link}}',
        `<a href="${
          this.smsSettings.review_link || '#'
        }" target="_blank">https://bit.ly/gty52</a>`
      )
      return {
        type: 'sms',
        emulators: [{ name: 'mobile', default: true }],
        imageEnabled: this.smsSettings.image_enabled,
        body: message,
        photoUrl: this.smsSettings.image_enabled ? this.photoUrl || null : null,
      }
    },

    placeIdErrorDialogData: function () {
      return this.placeIdError
    },

    saveButtonText(): string {
      return this.saving ? 'Saving' : 'Save'
    },
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.fetchData(id)
    },
  },

  methods: {
   async toggleSwitched(){
      await this.location.ref.update({
        'settings.sms.enabled':this.smsSettings.enabled,
        date_updated: firebase.firestore.FieldValue.serverTimestamp(),
      })
    },
    afterChckinTimeChanged(unit, time, type = 'generic') {
      this.local_after_check_in_time = {
        ...this.local_after_check_in_time,
        type,
        unit,
        ...(time != null ? { value: parseInt(time) } : {}),
      }
    },
    repeateReviewRequestTimeChanged(unit, time, type = 'generic') {
      this.local_repeat_review_request_in = {
        ...this.local_repeat_review_request_in,
        type,
        unit,
        ...(time != null ? { value: parseInt(time) } : {}),
      }
    },
    async fetchData() {
      
      this.photoUrl = this.location.settings.sms.image
      this.smsSettings = { ...this.smsSettings, ...this.location.settings.sms }

      if (this.smsSettings.after_check_in_hours == undefined) {
        this.smsSettings.after_check_in_hours = 0
      }

      if (
        this.smsSettings.after_check_in_hours == 0 ||
        this.smsSettings.after_check_in_hours == 1 ||
        this.smsSettings.after_check_in_hours == 2 ||
        this.smsSettings.after_check_in_hours == 4
      ) {
        this.local_after_check_in_time = {
          type: 'generic',
          unit: 'hours',
          value: this.smsSettings.after_check_in_hours || 0,
        }
      } else {
        let unit =
          this.smsSettings.after_check_in_hours % 24 == 0 ? 'days' : 'hours'
        let value = this.smsSettings.after_check_in_hours || 0
        if (unit === 'days') {
          value = value / 24
        }
        this.local_after_check_in_time = {
          type: 'custom',
          unit,
          value,
        }
      }

      if (this.smsSettings.repeat_review_request_in == undefined) {
        this.smsSettings.repeat_review_request_in = -1
      }
      if (
        this.smsSettings.repeat_review_request_in == -1 ||
        this.smsSettings.repeat_review_request_in == 3 * 24 ||
        this.smsSettings.repeat_review_request_in == 7 * 24 ||
        this.smsSettings.repeat_review_request_in == 15 * 24 ||
        this.smsSettings.repeat_review_request_in == 30 * 24
      ) {
        let hoursInDay =
          this.smsSettings.repeat_review_request_in != -1
            ? this.smsSettings.repeat_review_request_in / 24
            : this.smsSettings.repeat_review_request_in
        this.local_repeat_review_request_in = {
          type: 'generic',
          unit: 'days',
          value: hoursInDay || -1,
        }
      } else {
        let unit =
          this.smsSettings.repeat_review_request_in % 24 == 0 ? 'days' : 'hours'
        let value = this.smsSettings.repeat_review_request_in || 0
        if (unit === 'days') {
          value = value / 24
        }
        this.local_repeat_review_request_in = {
          type: 'custom',
          unit,
          value,
        }
      }
    },
    async vfileAdded(file: any) {
      const dropZone: any = this.$refs.file_drop_zone
      dropZone.removeAllFiles()

      this.selectedFile = file
      this.photoUrl = URL.createObjectURL(file)
      this.smsSettings.image_enabled = true
    },
    async validateBeforeSubmit() {
      if (!this.location) return
      this.saving = true
      if (this.selectedFile) {
        let imagePath =
          'locationFiles/' + this.currentLocationId + '/sms/review/' + uuid()
        var uploadPath = firebase.storage().ref(imagePath)

        const snapshot = await uploadPath.put(this.selectedFile, {
          contentDisposition: `inline; filename="${this.selectedFile.name}"`,
        })
        this.photoUrl = await snapshot.ref.getDownloadURL()
        this.smsSettings.image = this.photoUrl
      }

      if (this.review_request_behaviour === 'immediate') {
        //changed in between
        this.smsSettings.after_check_in_hours = 0
        this.smsSettings.repeat_review_request_in = -1
        this.local_after_check_in_time = {
          unit: 'hours',
          value: 0,
          type: 'generic',
        }
        this.local_repeat_review_request_in = {
          unit: 'hours',
          value: -1,
          type: 'generic',
        }
      }

      if(this.smsSettings.repeat_count < 1){
        this.smsSettings.repeat_count = 1;
      }
      this.smsSettings.after_check_in_hours = parseInt(
        this.local_after_check_in_time.value
      )

      if (this.smsSettings.after_check_in_hours < 0) {
        this.smsSettings.after_check_in_hours = 0
        this.local_after_check_in_time = {
          unit: 'hours',
          value: 0,
          type: 'generic',
        }
      }

      if (this.local_after_check_in_time.type === 'custom') {
        if (this.local_after_check_in_time.unit === 'days') {
          this.smsSettings.after_check_in_hours =
            this.smsSettings.after_check_in_hours * 24
        }
      }

      this.smsSettings.repeat_review_request_in = parseInt(
        this.local_repeat_review_request_in.value
      )
      if (this.smsSettings.repeat_review_request_in <= 0) {
        this.smsSettings.repeat_review_request_in = -1
        this.local_repeat_review_request_in = {
          unit: 'hours',
          value: -1,
          type: 'generic',
        }
      }
      if (
        this.local_repeat_review_request_in.unit === 'days' &&
        this.smsSettings.repeat_review_request_in > 0
      ) {
        this.smsSettings.repeat_review_request_in =
          this.smsSettings.repeat_review_request_in * 24
      }

      this.location.settings.sms = this.smsSettings
      if (!this.location.settings.review) {
        this.location.settings.review = {}
      }
      this.location.settings.review.review_request_behaviour =
        this.review_request_behaviour
      await this.location.ref.update({
        'settings.sms':this.smsSettings,
        'settings.review': this.location.settings.review,
        date_updated: firebase.firestore.FieldValue.serverTimestamp()
      })
      this.selectedFile = undefined
      this.saving = false
    },
  },

  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchData()
  },
})
</script>
<style lang="scss" scoped>
#review-sms {
  .card-body {
    max-width: unset;

    .communication {
      padding: 20px 0 0 0;

      .dropdowns {
        display: grid;
        grid-template-columns: auto 1fr;

        .btn-ghl_lite {
          color: #188bf6 !important;
          background-color: rgba(24, 139, 246, 0.2) !important;
          border-color: rgba(24, 139, 246, 0.2) !important;
        }
      }

      .customCheckinTime {
        display: inline-grid;
        grid-template-columns: auto auto 1fr;
        padding: 0 10px;
        grid-gap: 10px;
        /* height: 52px; */
        /* overflow: hidden; */

        input[type='number'] {
          padding: 10px;
          max-width: 80px;
        }
      }
    }
  }

  .card-header--withbutton {
    display: grid;
    grid-template-columns: auto 1fr;
    grid-gap: 1rem;
  }
  .secondary-header {
    font-size: 0.88rem;
  }

  .save-button-holder {
    padding: 25px 0 0 0;
    button {
      width: 200px;
    }
  }
  .two-part {
    display: grid;
    width: 100%;
    grid-template-columns: 1fr 1fr;
    grid-column-gap: 10px;

    & > div:first-child {
      border-right: 1px solid #eaeaea;
      padding-right: 20px;
    }

    .image-config {
      display: grid;
      grid-template-columns: 1fr auto;

      .upload-box {
        min-height: 220px;
        background: #e6f2f8;
        display: grid;
        grid-template-rows: 1fr auto;
        max-width: 220px;
        padding: 3px;

        img {
          width: 100%;
          background: #fff;
        }

        button {
          margin-top: 6px;
        }
      }

      .image-toggle__btn {
        display: grid;
        grid-template-columns: auto auto;
        justify-content: right;
        grid-column-gap: 10px;
        .toggle {
          padding-top: 10px;
          padding-top: 3px;
        }
      }
    }

    .sms-text-box {
      margin-top: 2rem;
    }
  }

  input{
    background: #ecf3f8;
  }
}
</style>
