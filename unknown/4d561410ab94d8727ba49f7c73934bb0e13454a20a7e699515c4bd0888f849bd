<template>
  <Filters
    :conditions="conditions"
    :filterMaster="filterMaster"
    v-on:update:conditions="$emit('update:conditions', $event)"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import { Pipeline, Stage, Tag } from '@/models'
import { Condition } from '@/models/trigger'
const Filters = () => import('./Filters.vue')

export default Vue.extend({
  props: ['conditions'],
  components: { Filters },
  data() {
    return {
      filterMaster: [
        {
          placeHolder: 'Select pipeline',
          title: 'In pipeline',
          value: 'opportunity.pipelineId',
          valueType: 'text',
          type: 'select',
          options: [] as { [key: string]: any }[]
        },
        {
          id: 'has-tag',
          placeHolder: 'Has Tag',
          title: 'Has Tag',
          value: 'contact.tags',
          valueType: 'text',
          operator: 'index-of-true',
          type: 'select',
          options: [] as { [key: string]: any }[]
        }
      ],
      pipelines: [] as Pipeline[]
    }
  },
  watch: {
    '$route.params.location_id': async function(id) {
      await this.$store.dispatch('pipelines/syncAll', id)
    },
    conditions: {
      handler: function(conditions) {
        this.conditionsChanged(conditions)
      },
      deep: true
    }
  },
  methods: {
    conditionsChanged: function(condition) {
      const pipelineIdOption = lodash.find(condition, {
        field: 'opportunity.pipelineId'
      })
      if (
        pipelineIdOption &&
        pipelineIdOption.value &&
        !lodash.some(this.filterMaster, {
          value: 'opportunity.pipelineStageId'
        })
      ) {
        var pipeline_stage = {
          placeHolder: 'Select stage',
          title: 'Pipeline stage',
          value: 'opportunity.pipelineStageId',
          type: 'select',
          options: []
        }
        var pipeline = lodash.find(this.pipelines, {
          id: pipelineIdOption.value
        })
        pipeline_stage.options = pipeline.stages.map(stage => {
          return {
            title: stage.name,
            value: stage.id
          }
        })

        this.filterMaster.push(pipeline_stage)
        console.log(
          'pipeline object :' + JSON.stringify(pipelineIdOption) + ' modified'
        )
      } else if (
        (!pipelineIdOption || !pipelineIdOption.value) &&
        lodash.some(this.filterMaster, {
          value: 'opportunity.pipelineStageId'
        })
      ) {
        let refreshedCondition = lodash.cloneDeep(this.conditions)
        if (
          lodash.findIndex(refreshedCondition, {
            field: 'opportunity.pipelineStageId'
          }) !== -1
        ) {
          refreshedCondition.splice(
            lodash.findIndex(refreshedCondition, {
              field: 'opportunity.pipelineStageId'
            }),
            1
          )
          this.$emit('update:conditions', refreshedCondition)
        }
        this.filterMaster.splice(
          lodash.findIndex(this.filterMaster, {
            value: 'opportunity.pipelineStageId'
          }),
          1
        )
      }
    }
  },
  async created() {
    const currentLocationId = this.$router.currentRoute.params.location_id
    await this.$store.dispatch('pipelines/syncAll', this.$route.params.location_id)
    const pipelineIdOption = lodash.find(this.filterMaster, {
      value: 'opportunity.pipelineId'
    })
    if (pipelineIdOption) {
      this.pipelines = this.$store.state.pipelines.pipelines
      pipelineIdOption.options = this.pipelines.map(pipeline => {
        return {
          title: pipeline.name,
          value: pipeline.id
        }
      })
    }
    const hasTagIdOption = lodash.find(this.filterMaster, {
      id: 'has-tag'
    })
    let tagOptions = (await Tag.getByLocationId(currentLocationId)).map(tag => {
      return {
        title: tag.name,
        value: tag.name
      }
    })
    hasTagIdOption.options = tagOptions

    this.conditionsChanged(this.conditions)
  }
})
</script>
