<template>
  <div class="summary-card__wrap">
    <div class="upper-block separator">
      <div class="month-info">
        <span class="main-head">{{summary.title}}</span>
        <span class="sub-head">({{summary.month}})</span>
        <span class="pricing">${{totalCharges | formatDecimalValue}}</span>
      </div>
    </div>
    <!-- Bottom block -->
    <div class="bottom-block">
      <div class="info-row">
        <div class="info">Calls ({{summary.callAmount.count}})</div>
        <div class="price">${{summary.callAmount.total}}</div>
      </div>
      <!-- row2 -->
      <div class="info-row">
        <div class="info">SMS ({{summary.smsAmount.count}})</div>
        <div class="price">${{summary.smsAmount.total}}</div>
      </div>
      <!-- expandable row -->
      <div class="other-charges-row" @click="$emit('toggleOtherCharges')">
        <div class="heading">Other Charges
          <i class="fas fa-chevron-down" v-if="!showOtherCharges"></i>
          <i class="fas fa-chevron-up" v-else></i>
        </div>
        <div class="price">${{otherCharges | formatDecimalValue}}</div>
      </div>
      <!-- expand items -->
      <div v-if="showOtherCharges" class="other-items">
        <div class="info-row" v-for="item of summary.otherCharges" :key="item._id">
          <div class="info-other">{{item.title}}</div>
          <div class="price">${{item.total}}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatDecimalValue } from '../../../../util/helper'
export default {
  props: {
    summary: Object,
    showOtherCharges: {
      type: Boolean,
      default: false
    }
  },
  filters: {
    formatDecimalValue
  },
  computed: {
    totalCharges() {
      return this.otherCharges + this.summary?.callAmount.total + this.summary.smsAmount.total;
    },
    otherCharges() {
      let otherSum = 0;
      this.summary?.otherCharges.forEach(element => {
        otherSum += element.total;
      });
      return otherSum;
    }
  },
}
</script>


<style scoped lang='scss'>
.summary-card__wrap{
  width: 243px;

  padding: 28px 6px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: #FFFFFF;
  border: 1px solid #E2E8F0;
  box-sizing: border-box;
  border-radius: 3px;
}
.upper-block{
  .month-info{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .main-head{
      font-size: 14px;
      font-weight: 500;
      color: #4F4F4F;
      margin-bottom: 8px;
    }
    .sub-head{
      font-size: 12px;
      font-weight: 400;
      color: #4F4F4F;
      margin-bottom: 12px;
    }
    .pricing{
      color: #37ca37;
      font-weight: 500;
      font-size: 30px;
      margin-bottom: 16px;
    }
  }
}

.separator{
  width: 218px;
  border-bottom: 1px solid #E2E8F0;
}

.bottom-block{
  width: 100%;
  padding: 24px 24px 0px 24px;
  // margin-top: 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  // align-items: center;
}
.info-row{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  font-size: 12px;
  color: #4F4F4F;
  margin-bottom: 8px;

  .info{
    width: 149px;
    font-weight: 400;
  }
  .info-other{
    width: 95px;
    font-weight: 400;

  }

  .price{
    font-weight: 700;
  }
}
.other-charges-row{
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin: 4px 0px;

  .heading{
    width: 149px;
    font-size: 12px;

    font-weight: 400;
    color: #178Af6;
    span{
      color: #4F4F4F;
    }
  }
  .price{
    font-weight: 700;
    color: #4F4F4F;
  }
}
.other-items{
  margin-top: 12px;
  margin-left: 16px;
  // display: flex;
  // flex-direction: column;
  // align-items:center;
}
</style>
