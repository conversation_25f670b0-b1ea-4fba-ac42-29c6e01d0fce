<template>
  <section class="hl_wrapper">
        <section class="hl_wrapper--inner" id="launchpad">
          <div
            id="launchpadHome"
            ref="launchpadHome"
            class="container-fluid"
            v-if="!showChatWidgetSetup"
          >
            <div class="launchpad-center-containter">
              <div class="launchpad-title text-center">
                <img
                  class="mb-2"
                  v-if="hideLaunchpadDate"
                  src="@/assets/pmd/img/launchpad/launchpad-completed-icon.svg"
                />
                <p v-if="hideLaunchpadDate" class="launchpad-completed">
                  Congratulations!
                </p>
                <p v-else>Let's get you on the path to success</p>
                <div v-if="hideLaunchpadDate" class="launchpad-subtitle mt-2">
                  You have completed all the tasks and Launchpad will hide on
                  {{ hideLaunchpadDate }}
                </div>
              </div>
              <!-- Middle Section -->
              <div class="launchpad-item-container">
                <!-- Lead Connector -->
                <LeadConnector
                  v-if="!isWhitLabelAppEnabled"
                  :activePlan="company.stripeActivePlan"
                  :currentLocationId="currentLocationId"
                  :userid="user.id"
                  :name="user.name"
                  :mobileLinkError="showMobileAppError"
                  :shortenUrl="shortenUrlMobileApp"
                  :mobileAppChecking="mobileAppChecking"
                  :isLeadConnectorConnected="isLeadConnectorConnected"
                  :class="{
                    hl_disable_action:
                      disableAction && active != 'lead-connector',
                  }"
                />

                <!-- Google my business -->
                <GoogleMyBusiness
                  :currentLocationId="currentLocationId"
                  :userid="user.id"
                  :class="{
                    hl_disable_action: disableAction && active != 'google',
                  }"
                />

                <!-- Facebook -->
                <FacebookBusiness
                  :currentLocationId="currentLocationId"
                  :userid="user.id"
                  :name="user.name"
                  :class="{
                    hl_disable_action: disableAction && active != 'facebook',
                  }"
                />

                <!-- Book An Appointment -->
                <!-- <BookAppointment
                  :currentLocationId="currentLocationId"
                  :userid="user.id"
                  :name="user.name"
                  :class="{
                    hl_disable_action:
                      disableAction && active != 'book-appointment',
                  }"
                /> -->

                <!-- Chat widget -->
                <ChatWidget
                  :currentLocationId="currentLocationId"
                  :userid="user.id"
                  :name="user.name"
                  :website="website"
                  :isChatWidgetConnected="isChatWidgetConnected"
                  :chatWidgetChecking="chatWidgetChecking"
                  :reconnect="reconnect"
                  :class="{
                    hl_disable_action: disableAction && active != 'chat-widget',
                  }"
                />

                <!-- Stripe Connector -->
                <StripeConnect
                  :currentLocationId="currentLocationId"
                  :userid="user.id"
                  :class="{
                    hl_disable_action: disableAction && active != 'stripe',
                  }"
                />

                <YextLaunchpadComponent 
                :location="location"
                :company="company"
                :user="user"
                />
                
                <WordpressLaunchpadComponent 
                :location="location"
                :company="company"
                :user="user"
                />
                <!-- Add New Users -->
                <AddUsers
                  v-if="isAdmin"
                  :currentLocationId="currentLocationId"
                  :userid="user.id"
                />
              </div>
            </div>
          </div>
          <chat-widget-setup
            v-else
            :cms="cmsName"
            :websiteScreenshot="websiteScreenshot"
          />
        </section>
  </section>
</template>

<script lang="ts">
import Vue from 'vue'
import GoogleMyBusiness from '@/pmd/components/launchpad/GoogleMyBusiness.vue'
import FacebookBusiness from '@/pmd/components/launchpad/FacebookBusiness.vue'
import ChatWidget from '@/pmd/components/launchpad/ChatWidget.vue'
import ChatWidgetSetup from '@/pmd/components/launchpad/ChatWidgetSetup.vue'
import LeadConnector from '@/pmd/components/launchpad/LeadConnector.vue'
import BookAppointment from '@/pmd/components/launchpad/BookAppointment.vue'
import StripeConnect from '@/pmd/components/launchpad/StripeConnect.vue'
import AddUsers from '@/pmd/components/launchpad/AddUsers.vue'
import YextLaunchpadComponent from '@/pmd/components/launchpad/YextLaunchpadComponent.vue'
import WordpressLaunchpadComponent from '@/pmd/components/launchpad/WordpressLaunchpadComponent.vue'

import { mapState } from 'vuex'
import { Location, User, OAuth2, UserState, Company } from '@/models'
import defaults from '@/config'
import { trackGaEvent } from '@/util/helper'
import StepCompletedIcon from '@/assets/pmd/img/step-completed.svg'
import moment from 'moment-timezone'
import axios from 'axios'

export default Vue.extend({
  name: 'LocationLaunchpad',
  components: {
    GoogleMyBusiness,
    FacebookBusiness,
    ChatWidget,
    ChatWidgetSetup,
    LeadConnector,
    StepCompletedIcon,
    StripeConnect,
    AddUsers,
    YextLaunchpadComponent,
    WordpressLaunchpadComponent
  },
  data() {
    return {
      location: {} as Location,
      onboardingData: {},
      baseUrl: defaults.baseUrl,
      newOnboardingFrontend: defaults.newOnboardingFrontend,
      firstName: '',
      childLoaded: false,
      authKey: '',
      apiKey: '',
      modalName: '',
      googleConnections: [] as OAuth2[],
      googleLocations: [],
      currentLocationId: '',
      analyticsAccounts: [],
      adwordsCustomerIds: [],
      adwordsMCCIds: [],
      selectedMCCId: '',
      analyticsViews: [],
      isUpdating: false,
      isFirstFetch: true,
      isComponentReq: true,
      facebookPages: [],
      cmsName: '',
      cmsSelection: false,
      gmbPageName: '',
      appLoader: false,
      websiteScreenshot: undefined,
      sendInstruction: false,
      chatWidgetSetup: 0,
      analyticsAcc: '',
      googleBusinessAccount: '',
      sending: false,
      slideShow: false,
      disableAction: false,
      active: 'google',
      showChatWidgetSetup: false,
      showMobileAppError: false,
      shortenUrlMobileApp: '',
      isChatWidgetConnected: false,
      reconnect: false,
      website: '',
      chatWidgetChecking: false,
      mobileAppChecking: false,
      isLeadConnectorConnected: false,
      hideLaunchpadDate: undefined,
      wlIds: [],
    }
  },
  watch: {
    '$route.params.location_id': async function (id) {
      this.currentLocationId = id
      await this.fetchData()
    },
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    if (this.currentLocationId) {
      await this.fetchData()
    }
    const wlAppAgencies = this.remoteConfig.getString('wl_app_agencies')
    if (wlAppAgencies) {
      this.wlIds = wlAppAgencies.split(',')
    }
  },
  async mounted() {
    if (this.user) {
      //this.loadMicroApp()
      this.childLoaded = true
    }
    this.$root.$on('close-modal', () => {
      if (this.modalName == 'chat-widget') this.closeChatModal()
      this.modalName = ''
    })
    /* Facebook Event listener */
    this.$root.$on('show-select-cms', () => {
      this.cmsSelection = true
    })
    this.$root.$on('cms-change', (data: string) => {
      this.cmsSelection = false
      this.cmsName = data
    })
    this.$root.$on('send-instruction', () => {
      this.cmsSelection = false
      this.sendInstruction = true
    })
    this.$root.$on('show-loader', () => {
      this.showHideLoader()
    })
    this.$root.$on('hide-loader', () => {
      this.showHideLoader()
    })
    this.$root.$on('disable-launchpad-action', (data: string) => {
      const actionData = JSON.parse(data)
      this.disableAction = actionData.disable
      this.active = actionData.action
    })
    this.$root.$on('get-screenshot', () => {
      this.getScreenshot()
    })
    this.$root.$on('detected-cms', cms => {
      this.cmsName = cms
      this.showChatWidgetSetup = true
    })
    this.$root.$on('hide-widget-setup', () => {
      this.showChatWidgetSetup = false
      this.websiteScreenshot = undefined
      this.isLaunchpadCompleted(true)
    })
    this.$root.$on('chat-integrated', () => {
      this.showChatWidgetSetup = false
      this.isLaunchpadCompleted(true)
    })
    trackGaEvent(
      'Launchpad',
      'Landed on launchpad',
      `User landed on launchpad for location: ${this.currentLocationId}`
    )
    if (this.$route.query.mobile_app_error) {
      await this.getMobileAppShortenedUrl()
      this.showMobileAppError = true
      this.$router.replace({ query: undefined })
    }
    this.$root.$on('hide-mobile-error', () => {
      this.showMobileAppError = false
    })
    this.$root.$on('launchpad-status', async () => {
      this.location = new Location(
        await this.$store.dispatch(
          'locations/getCurrentLocation',
          this.currentLocationId
        )
      )
      this.isLaunchpadCompleted(false)
    })
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
    adminOrOwnerOfGoogleIntegration() {
      // If the field doesn't exists any user should be able to edit the pages.
      if (!this.googleConnection || !this.googleConnection.ownerId) return true
      if (this.user.isAdmin) return true
      return this.googleConnection.ownerId === this.user?.id
    },
    googleConnection() {
      return this.googleConnections.find(
        x => x.id === this.location.primaryGoogleConnectionId
      )
    },
    isSupport() {
      return Boolean(this.$route.query.support === 'true')
    },
    hasSettingsPermission() {
      // In general admin level users do have this permission despite the role type
      return this.user.permissions.settings_enabled
    },
    hasAppointmentsPermission() {
      return this.user.permissions.appointments_enabled
    },
    filteredGoogleConnections() {
      return this.user.role === 'admin' || this.hasSettingsPermission
        ? this.googleConnections
        : this.googleConnections.filter(x => x.users && this.user.id in x.users)
    },
    isGMBConnected(): boolean | undefined {
      return this.location.gmb?.name ? true : false
    },
    isFbConnected(): boolean | undefined {
      return this.location.facebookPageId || this.location.fbTokenExpired
        ? true
        : false
    },
    isStripeConnected() {
      return this.$store.state.stripeConnect.accountId
    },
    isWhitLabelAppEnabled() {
      return this.wlIds.includes(this.company.id) && !this.company?.whitelabelAppLinks?.android
    },
    isAdmin() {
      return this.user.role == User.ROLE_ADMIN
    },
  },
  methods: {
    async fetchData() {
      let _self = this

      if (this.currentLocationId) {
        this.location = new Location(
          await this.$store.dispatch(
            'locations/getCurrentLocation',
            this.currentLocationId
          )
        )
      }
      this.isLaunchpadCompleted(true)
    },
    showHideLoader() {
      const element = document.getElementById('app')
      const htmlTag = document.getElementsByTagName('html')[0]
      if (!this.appLoader && element && htmlTag ) {
        element.className = 'loading'
        htmlTag.className = 'overflow-hidden'
      } else if (element && htmlTag) {
        element.className = ''
        htmlTag.className = ''
      }
      this.appLoader = !this.appLoader
    },
    async getScreenshot() {
      try {
        this.location = new Location(
          await this.$store.dispatch(
            'locations/getCurrentLocation',
            this.currentLocationId
          )
        )
        const response = await axios.get(
          `${defaults.cloudFunctionsUrl}/websitescreenshot?website=${this.location.website}`
        )
        this.websiteScreenshot = response.data
      } catch (err) {
        console.log(err)
      }
    },
    closeChatModal() {
      this.sendInstruction = false
      this.cmsSelection = false
    },
    async getMobileAppShortenedUrl() {
      try {
        const domain = window.location.origin
        const appLink = `${domain}/login/token?token=${
          this.user.token
        }&url=${encodeURIComponent(
          `/location/${this.currentLocationId}/mobile_app_links`
        )}`
        const response = await this.$http.post('url/shorten', {
          originalUrl: appLink,
        })
        if (response.status == 200) {
          this.shortenUrlMobileApp = response.data.shortUrl
        }
      } catch (error) {
        console.log(error)
      }
    },
    async isLaunchpadCompleted(api: boolean) {
      try {
        let launchpadCompleted = true
        let updateRequire = true
        let locationUpdate = {}
        let launchpad = {}
        if (api) {
          const response = await Promise.all([
            this.isChatWidgetIntegrated(),
            this.isMobileAppInstalled(),
          ])
          locationUpdate = {
            ...response[0],
            ...response[1],
            is_stripe_connected: this.isStripeConnected ? true : false,
          }

          if (this.location.launchpad) {
            updateRequire = false
            launchpad = this.location.launchpad
            for (const prop in locationUpdate) {
              if (
                (this.location.launchpad[prop] &&
                  this.location.launchpad[prop] != locationUpdate[prop]) ||
                !this.location.launchpad[prop]
              ) {
                updateRequire = true
                launchpad[prop] = locationUpdate[prop]
              }
            }
          }

          if (updateRequire) {
            await this.location.ref.update({
              launchpad: launchpad,
            })
            this.location = new Location(
              await this.$store.dispatch(
                'locations/getCurrentLocation',
                this.currentLocationId
              )
            )
          }
        }

        if (
          !this.isGMBConnected ||
          !this.isFbConnected ||
          !this.isChatWidgetConnected ||
          (!this.isLeadConnectorConnected && !this.isWhitLabelAppEnabled) ||
          !this.isStripeConnected
        ) {
          launchpadCompleted = false
          trackGaEvent(
            'Launchpad',
            'Launchpad not completed',
            `All integrations not yet completed for location: ${this.currentLocationId}`
          )
        }
        if (this.user.type === 'account') {
          let launchpadCompletedDate = this.user?.launchpadCompletedDate
          let updateUser = false
          if (!launchpadCompleted && launchpadCompletedDate) {
            launchpadCompletedDate = undefined
            updateUser = true
          } else if (launchpadCompleted && !launchpadCompletedDate) {
            launchpadCompletedDate = moment().add(1, 'days')
            updateUser = true
          }

          if (updateUser) {
            const user = await User.getById(this.user.id)
            user.launchpadCompletedDate = launchpadCompletedDate
            await user.save()
            if (launchpadCompletedDate) {
              trackGaEvent(
                'Launchpad',
                'Launchpad completed',
                `All integrations has benn completed for location: ${this.currentLocationId}`
              )
            }
          }

          if (this.user?.launchpadCompletedDate) {
            this.hideLaunchpadDate = this.user.launchpadCompletedDate.format(
              'ddd, Do MMM YYYY'
            )
          }
        }
      } catch (err) {
        console.log(err)
      }
    },
    async isChatWidgetIntegrated() {
      this.reconnect = this.location.launchpad?.is_chat_widget_disconnected
        ? true
        : false
      this.isChatWidgetConnected = this.location.launchpad
        ?.is_chat_widget_integrated
        ? true
        : false
      this.website = this.location.launchpad?.website
        ? this.location.launchpad?.website
        : ''
      if (this.location.website && this.location.website != this.website) {
        this.website = this.location.website
      }
      try {
        if (this.website) {
          this.chatWidgetChecking = true
          const response = await axios.get(
            `${defaults.cloudFunctionsUrl}/isChatWidgetIntegrated?website=${this.website}`
          )
          if (response.status == 200) {
            this.isChatWidgetConnected = response.data.is_chat_integrated
          } else {
            this.isChatWidgetConnected = false
          }
          this.chatWidgetChecking = false
        }
      } catch (err) {
        this.chatWidgetChecking = false
        this.isChatWidgetConnected = false
      }
      try {
        if (
          !this.isChatWidgetConnected &&
          (this.location.launchpad?.is_chat_widget_integrated ||
            this.location.launchpad?.is_chat_widget_disconnected)
        ) {
          this.reconnect = true
        } else {
          this.reconnect = false
        }
        if (this.isChatWidgetConnected) {
          trackGaEvent(
            'Launchpad',
            'WebChat Widget Integrated',
            `WebChat Widget has been integrated for location: ${this.currentLocationId}`
          )
        }
        return {
          is_chat_widget_disconnected: this.reconnect,
          is_chat_widget_integrated: this.isChatWidgetConnected,
          website: this.website,
        }
      } catch (err) {
        console.log(err)
      }
    },
    async isMobileAppInstalled() {
      this.mobileAppChecking = true
      try {
        if (!this.user.launchpad?.is_mobile_app_installed) {
          const response = await this.$http.post('/mobile/is_app_installed', {
            location_id: this.currentLocationId,
          })
          if (response.status == 200) {
            this.isLeadConnectorConnected =
              response.data.is_mobile_app_installed
          }
        } else if (this.location.launchpad?.is_mobile_app_installed) {
          this.isLeadConnectorConnected = this.location.launchpad?.is_mobile_app_installed
        }
        if (this.isLeadConnectorConnected) {
          trackGaEvent(
            'Launchpad',
            'Mobile App Installed',
            `Mobile App has been installed for location: ${this.currentLocationId}`
          )
        }
      } catch (err) {
        console.log(err)
      }
      this.mobileAppChecking = false
      return {
        is_mobile_app_installed: this.isLeadConnectorConnected,
      }
    },
  },
  beforeDestroy() {
    window.removeEventListener('popstate', () => console.log('remove popstate'))
  },
})
</script>

<style>
.launchpad-title {
  color: rgba(31, 41, 55, 1);
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-size: 25px;
  line-height: 29px;
  margin-bottom: 25px;
}
.launchpad-subtitle {
  font-family: Roboto;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 16px;
  color: #718096;
}
.launchpad-center-containter {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.launchpad-item-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 650px;
  background: #ffffff;
  border: 1px solid #cbd5e0;
  box-sizing: border-box;
  border-radius: 5px;
}
.launchpad-item-sub-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: start;
  background: #fff;
  width: 100%;
  min-height: 90px;
  position: relative;
}
.items-tiles {
  display: flex;
  flex-direction: row !important;
  justify-content: center;
  align-items: center;
}
.integration-icon {
  width: 3.5rem;
  height: 3.5rem;
}
.connect-icon {
  width: 1.5rem;
  height: 1.5rem;
}
.item-info {
  display: flex;
  flex-direction: row !important;
  justify-content: start;
  align-items: center;
  width: 450px;
}
.gmb-button-grp {
  margin-top: 20px;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  width: 560px;
}
.gmb-button-grp .yes-button {
  margin-left: 50px;
}
.gmb-mailer-grp {
  margin-top: 20px;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  width: 100%;
  justify-content: center;
  align-items: flex-start;
}
.gmb-mailer-from-group {
  text-align: initial;
  color: #1a202c;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 400px;
}
#gmb-connect .dropdown-toggle,
#fb-connect .dropdown-toggle {
  background: #ffffff;
  border: 1px solid #cbd5e0;
  box-sizing: border-box;
  border-radius: 3px;
}
.gmb-input-label {
  font-family: Roboto;
  font-style: normal;
  font-weight: bold;
  font-size: 14px;
  line-height: 16px;
  text-align: start;
}
.gmb-form-error {
  border: 1px solid red;
}
.gmb-from-error-message {
  color: red;
  text-align: start;
  font-size: 12px;
}
.loader-postion {
  position: absolute;
}
.slide-enter-active {
  transition: all 1s cubic-bezier(0.17, 0.67, 0.83, 0.67);
}

.slide-leave-active {
  transition: all 0.5s ease-out;
}

.slide-enter-to,
.slide-leave {
  max-height: 300px;
  overflow: hidden;
}

.slide-enter,
.slide-leave-to {
  max-height: 0px;
  overflow: hidden;
}

.green-filled-button {
  margin-left: 0.75rem;
  display: inline-flex;
  justify-content: center;
  padding: 0.65rem 1.5rem;
  border-color: transparent;
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  font-size: 0.75rem;
  line-height: 1.25rem;
  font-weight: 700;
  border-radius: 3px;
  cursor: pointer;
  white-space: nowrap;
  position: relative;
}
.green-filled-button:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.green-btn {
  color: #fff;
  background-color: #38a169;
}
.green-btn:hover {
  background-color: #38a169;
}
.ligh-green-btn {
  background: rgba(215, 246, 230, 1) !important;
  color: rgba(56, 161, 105, 1) !important;
}
.success-green-btn {
  color: #fff !important;
  background: rgba(56, 161, 105, 1) !important;
  pointer-events: none !important;
}
.hl_disable_action {
  opacity: 0.5;
  pointer-events: none;
}
@media (min-width: 768px) {
  body {
    padding-top: 0px !important;
  }
}
.launchpad-iframe {
  border: none;
  width: 100%;
  height: calc(100vh - 110px);
  overflow: auto;
}
#app + .app-loader {
  display: none;
}
.launchpad-bottom-border {
  border-bottom: 1px solid #cbd5e0;
}
.launchpad-completed {
  color: #059669;
}
</style>
