<template>
  <div class="tab-pane fade show active" id="sms" role="tabpanel" aria-labelledby="sms-tab">
    <div
      class="message-box"
      @drop="handleDrag"
      @dragover="handleDragover"
      @dragleave="handleDragleave"
      :class="{focused:isFocus, dragoverd: isDrogover}"
    >
      <textarea
        v-model="msg"
        class="form-control"
        rows="4"
        placeholder="Type a message"
        @keydown.enter.exact.prevent
        @keyup.enter.exact="send"
        @keydown.enter.shift.exact="msg+='\n'"
      ></textarea>

      <div
        v-if="allAttachments.length > 0"
        style="position: absolute; right: 0px; bottom: 66px;display: inline-flex;"
      >
        <div
          style="position: relative;margin-right: 10px;"
          v-for="attachment in allAttachments"
          :key="attachment"
        >
          <img :src="attachment" style="max-height:60px;max-width:60px;border-radius: 4px;" />
          <a
            @click="removeAttachment(attachment)"
            style="position: absolute;right: -11px;top: -13px;padding: 4px;color: #e93d3d !important;"
            class="pointer"
          >
            <i class="fas fa-minus-circle"></i>
          </a>
        </div>
      </div>
      <div class="message-action-top">
        <div class="file-open">
          <input
            type="file"
            id="file-open-gmb"
            ref="fileupload"
            @change="onFileChange"
            multiple
            accept="image/*"
          />
          <label for="file-open-gmb">
            <i class="icon icon-file"></i>
          </label>
        </div>
      </div>
      <div v-if="errorMsg" class="--red" style="text-align: center;">{{errorMsg}}</div>
      <div class="message-action-bottom">
        <p>
          <a @click.prevent="requestPayment" class="use-template py-0 px-2" >
            <i class="icon icon-request" v-b-tooltip.hover title="Request Payment"></i>
          </a>
        </p>
        <div style="display: inline-block;position: relative;">
          <UIButton
            @click.prevent="send"
            type="submit"
            :loading="sending"
            :disabled="sending"
          >
            <SendWhiteIcon class="mr-2"/>  
            <span>Send</span>
          </UIButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import ImageTools from '../../../util/image_tools'
import { v4 as uuid } from 'uuid'
import { MessageTemplate } from '@/models/campaign'
import {
  Message,
  Conversation,
  Contact,
  MessageType,
  MessageContentType,
  Template,
  User,
  SMSTemplate,
} from '../../../models'
import { mapState } from 'vuex'
import { UserState, FileAttachment, CompanyState } from '@/store/state_models'
import * as lodash from 'lodash'
import firebase from 'firebase/app'
import { EventBus } from '@/models/event-bus'
const path = require('path')
import { trackGaEvent } from "@/util/helper";
import SendWhiteIcon from '/public/pmd/img/icon-send-white.svg'

const imageTools = new ImageTools()

export default Vue.extend({
  props: ['conversation', 'contact'],
  components: {
    SendWhiteIcon
  },
  watch: {
    conversation: function (newConv, oldConv) {
      if (newConv && newConv.id && (!oldConv || oldConv.id !== newConv.id)) {
        const data: () => object = <() => object>this.$options.data
        if (data) Object.assign(this.$data, data.apply(this))
      }
    },
    msg(val: string, before: string) {
      if (this.firstLetter && val && val.length - before.length === 1) {
        this.firstLetter = false
        this.msg = val.charAt(0).toUpperCase() + val.slice(1)
      }
    },
  },
  data() {
    return {
      errorMsg: '',
      attachments: [] as string[],
      filesAdded: [] as FileAttachment[],
      msg: '',
      firstLetter: true,
      isFocus: false,
      isDrogover: false,
      sending: false,
    }
  },
  computed: {
    allAttachments(): string[] {
      if (this.filesAdded.length === 0) return this.attachments
      return this.filesAdded.map(f => f.url).concat(this.attachments)
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
  },
  methods: {
    requestPayment() {
      trackGaEvent('Text to Pay', 'Request button clicked (GMB)', this.$route.params.location_id, 1);
      EventBus.$emit('request_payment', 'gmb')
    },
    invoiceCreated(invoice_url: string) {
      this.msg = invoice_url + '\n' + this.msg
    },
    send: async function () {
      this.errorMsg = ''
      if (this.sending) return
      if (!this.conversation) return
      if (!this.contact) return

      if (!this.contact.googleData.id) {
        this.errorMsg = 'The contact does not have a gmb Id.'
        return
      }

      if (
        this.msg.trim().length == 0 &&
        lodash.isEmpty(this.attachments) &&
        lodash.isEmpty(this.filesAdded)
      )
        return
      this.sending = true
      const urls = [] as string[]
      if (this.filesAdded.length > 0) {
        if (this.filesAdded.length > 0) {
          const basePath =
            'location/' +
            this.conversation.locationId +
            '/conversation/' +
            this.conversation.id +
            '/'
          const newURLs: string[] = await Promise.all(
            this.filesAdded.map(async attachment => {
              let imagePath = basePath + uuid() + path.extname(attachment.name)
              var uploadPath = firebase.storage().ref(imagePath)
              const snapshot = await uploadPath.put(attachment.data, {
                contentType: attachment.type,
                contentDisposition: `inline; filename="${attachment.name}"`,
                customMetadata: { name: attachment.name },
              })
              return await snapshot.ref.getDownloadURL()
            })
          )
          urls.push.apply(
            urls,
            newURLs.filter(url => url)
          )
        }
      }
      urls.push.apply(urls, this.attachments)
      // const realConversation = await Conversation.getById(this.conversation.id)
      try {
        let messageToSend = this.msg
        const data = {
          locationId: this.contact.locationId,
          contactId: this.contact.id,
          type: MessageType.TYPE_GMB,
          message: messageToSend,
          assignedTo: this.contact.assignedTo,
          //templateId: templateId,
          attachments: urls,
        }
        let response = await this.$http.post('/message/sendgmbmessage', data)
        this.$emit('messageSent')
        console.log(urls)
        this.msg = ''
        this.firstLetter = true
        this.attachments = []
        this.filesAdded = []
      } catch (err) {
        this.$uxMessage(
          'error',
          `An error has occured. Please try again or check the Google My Business integration. Error message: ${
            err.response && err.response.data.msg
              ? err.response.data.msg
              : err.message && err.message.error
              ? err.message.error.message
              : err.message
          }`
        )
        console.error(err)
      } finally {
        this.sending = false
      }
    },
    handleDrag(e: any) {
      var fileList = e.dataTransfer.files
      if (fileList && fileList.length > 0) {
        this.vfileAdded(fileList[0])
      }
      this.isDrogover = false
      e.preventDefault()
    },
    handleDragover(e: any) {
      this.isDrogover = true
      e.preventDefault()
    },
    handleDragleave(e: any) {
      this.isDrogover = false
      e.preventDefault()
    },
    async onFileChange(e: any) {
      const element = <HTMLInputElement>e.target
      if (!element.files) return
      for (let i = 0; i < element.files.length; i++) {
        this.vfileAdded(element.files[i])
      }
      element.files = null
    },
    async vfileAdded(file: File) {
      if (!this.conversation) return
      const response = <File | Blob>(
        await imageTools.resize(file, { height: 1000, width: 1000 })
      )
      this.filesAdded.push({
        name: file.name,
        type: file.type,
        url: URL.createObjectURL(response),
        data: response,
      })
      this.$refs.fileupload.value = ''
    },
    removeAttachment(attachment: string) {
      if (this.attachments.indexOf(attachment) !== -1)
        this.attachments.splice(this.attachments.indexOf(attachment), 1)
      let exists = lodash.find(this.filesAdded, { url: attachment })
      if (exists) {
        this.filesAdded.splice(this.filesAdded.indexOf(exists), 1)
      }
    },
  },
  mounted() {
    EventBus.$on('gmb_invoice_created', this.invoiceCreated)
  },
  beforeDestroy() {
    EventBus.$off('sms_invoice_created')
  },
})
</script>

<style scoped>
textarea.form-control {
  padding-right: 35px;
}
.hl_conversations--message-body .message-box .message-action-top,
.dropdown.my-2,
.file-open,
a.emoji-open {
  display: block;
}
.message-action-top {
  top: 5px;
  right: 20px !important;
}
.message-action-top > div,
.message-action-top > a {
  margin: 3px 0px 0px 0px !important;
}
.message-action-top > a {
  margin-left: -2px !important;
}
</style>
