<template>
  <div>
    <slot name="default"></slot>
  </div>
</template>

<script>
export default {
  props: ['uuid', 'priorityWaitTimer', 'disable'],
  watch: {
    uuid: function(newState, prevState) {
      //if location gets changed and there is any previous request pending then cancel it
      if (newState !== prevState && this.waitTimer !== -1) {
        clearTimeout(this.waitTimer)
        this.waitTimer = -1
      }
    },
    disable: function(newState, prevState) {
      for (
        let childrenIndex = 0;
        childrenIndex < this.$children.length;
        childrenIndex++
      ) {
        const child = this.$children[childrenIndex]
        const compRef = child.$children[0]
        if (!compRef) continue
        if (!prevState && newState && compRef.visibilityApproved) {
          compRef.visibilityApproved = false
          compRef.lastVisibilityApproved = true
          break
        }
        if (prevState && !newState && compRef.lastVisibilityApproved) {
          compRef.visibilityApproved = true
          compRef.lastVisibilityApproved = false
          break
        }
      }
    },
  },
  mounted() {
    this.alertDone = {}
    this.priorityOrder = []
    this.compToShowRef = null
    this.leastPriorityOrder = this.$children.length - 1
    this.waitTimer = -1

    this.$children.forEach(col => {
      if (col && col.$children && this.$children.length > 0) {
        const compRef = col.$children[0]
        if (compRef) {
          this.priorityOrder.push(compRef._uid)

          compRef.showMe = this.showMe
          if (compRef.showAlert && !this.alertDone[this.uuid]) {
            compRef.visibilityApproved = true
            this.alertDone[this.uuid] = true
          }
        }
      }
    })
  },
  methods: {
    showPriorityAlert() {
      if (!this.alertDone[this.uuid]) {
        this.compToShowRef.visibilityApproved = true
        this.alertDone[this.uuid] = true
      }
    },
    //TODO hkurra, we can also received the showMe(false) from higher pririty child so that we can show the
    //low prioritychild immediately without waiting for higher pririty child
    showMe(componentRef) {
      if (componentRef) {
        if (!this.alertDone[this.uuid]) {
          const priroity = this.priorityOrder.indexOf(componentRef._uid)
          if (priroity === 0 && !this.alertDone[this.uuid]) {
            componentRef.visibilityApproved = true
            this.alertDone[this.uuid] = true
          }
          //wait for 5000 ms for higher priority items
          else if (priroity <= this.leastPriorityOrder) {
            this.leastPriorityOrder = priroity
            this.compToShowRef = componentRef
            if (this.waitTimer !== -1) {
              clearTimeout(this.waitTimer)
              this.waitTimer = -1
            }
            //it might be visible last time
            componentRef.visibilityApproved = false
            const priorityTimer = this.priorityWaitTimer || 5000
            this.waitTimer = setTimeout(this.showPriorityAlert, priorityTimer)
          }
        }
      }
    },
  },
}
</script>
