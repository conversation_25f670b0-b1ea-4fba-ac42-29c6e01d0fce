<template>
  <div class="col-md-6 col-lg-4 mb-3">
    <div class="card">
      <div class="card-header">
        <div class="d-flex">
          <h2 class="mr-0">Appointments</h2>
          <div class="ml-2 mt-1">
            <span
              style="margin-top: -7px;"
              class="input-group-addon"
              v-b-tooltip.hover
              title="trigger links (embedded in message) clicked as % of messages sent"
            >
              <i class="fas fa-question-circle"></i>
            </span>
          </div>
        </div>
      </div>
      <div class="card-body">
        <table class="table">
          <tbody>
            <tr>
              <td>Requested</td>
              <td>45</td>
              <td>
                <span>
                  <i class="fas fa-arrow-down text-danger"/> 5%
                </span>
              </td>
            </tr>
            <tr>
              <td>Booked</td>
              <td>34</td>
              <td>
                <span>
                  <i class="fas fa-arrow-up text-success"/> 7%
                </span>
              </td>
            </tr>
            <tr>
              <td>Show</td>
              <td>56</td>
              <td>
                <span>
                  <i class="fas fa-arrow-up text-success"/> 17%
                </span>
              </td>
            </tr>
            <tr>
              <td>No Show</td>
              <td>26</td>
              <td>
                <span>
                  <i class="fas fa-arrow-down text-danger"/> 5%
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({})
</script>

<style scoped>

</style>
