<template>
  <div class="hl_settings--body" :class="{ 'hl_v2_stettings--body': isV2SideBarEnabled}">
    <div class="container-fluid">
      <div class="hl_controls">
        <div class="hl_controls--left">
          <h2>
            Team
            <!-- <span>{{ filteredUsers.length }} {{ pluralizeEmployee }}</span> -->
          </h2>
        </div>
        <div class="hl_controls--right">
          <div v-if="!currentLocationId">
            <select
              class="selectpicker type-picker"
              title="Type"
              data-width="fit"
              name="userTypes"
              @change="filterUsers"
              v-model="filters.type"
            >
              <option value>All</option>
              <option
                v-for="(type, typeIndex) in userTypes"
                :value="type.toLowerCase()"
                :key="typeIndex"
              >
                {{ type }}
              </option>
            </select>
          </div>
          <div>
            <select
              class="selectpicker role-picker"
              title="Role"
              data-width="fit"
              name="userRoles"
              @change="filterUsers"
              v-model="filters.role"
            >
              <option value>All</option>
              <option
                v-for="(role, roleIndex) in userRoles"
                :value="role.toLowerCase()"
                :key="roleIndex"
              >
                {{ role }}
              </option>
            </select>
          </div>
          <div id="account-page-location-filter" v-if="!currentLocationId">
            <vSelect
              :options="filteredLocations"
              label="name"
              placeholder="Search Accounts"
              v-model="filters.location"
              @search="updateSearchText"
              @input="filterUsers"
          ></vSelect>
          </div>
          <div class="search-form">
            <UITextInputGroup
              type="text"
              icon="icon-loupe"
              class="form-light"
              :placeholder="'Search by name, email or phone'"
              v-model="filters.search"
              @input="filterUsers"
            />
            <div style="position: absolute; top: 33%; right: 10px">
              <moon-loader
                v-if="searchLoading"
                :loading="searchLoading"
                color="#1ca7ff"
                size="15px"
              />
            </div>
          </div>
          <UIButton
            use="primary"
            @click.prevent="
              modalValues = {
                visible: true,
                companyId: company.id,
                isEditing: false,
              }
            "
          >
            <i class="icon icon-plus mr-2"></i> Add Employee
          </UIButton>
        </div>
      </div>
      <PageListNavigation
        :skip="skip"
        :limit="limit"
        :length="users.length"
        @previousPage="previousPage"
        @nextPage="nextPage"
      />
      <div class="card hl_settings--team-management">
        <div class="card-body --no-padding" style="max-width: 100%">
          <div class="table-wrap">
            <table class="table table-sort">
              <thead>
                <tr>
                  <th data-sort="string">Name</th>
                  <th data-sort="string">Email</th>
                  <th data-sort="string">Phone</th>
                  <th data-sort="string" v-if="!currentLocationId">
                    Locations
                  </th>
                  <th></th>
                </tr>
              </thead>
              <ListLoading
                v-if="loadingUsers"
                :sampleRow="['short', 'short', 'short', 'short', 'long']"
              />
              <tbody v-else>
                <tr v-if="users.length === 0">
                  <td>
                    <Avatar
                      :contact="{
                        fullName: 'There are no users that match these filters',
                      }"
                      :include_name="true"
                    />
                  </td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                </tr>
                <tr
                  v-for="user in users"
                  v-bind:index="user.id"
                  :key="user.id"
                >
                  <td>
                    <Avatar v-if="user" :contact="user" :include_name="true" />
                  </td>
                  <td>{{ user.email }}</td>
                  <td>
                    <PhoneNumber
                      type="display"
                      v-model="user.phone"
                      :currentLocationId="currentLocationId"
                    />
                  </td>
                  <td v-if="!currentLocationId">
                    {{ getUserLocationsString(user) }}
                    <span class="--blue">{{
                      getUserLocationsCount(user)
                    }}</span>
                  </td>
                  <td>
                    <div
                      class="btn-group"
                      :class="{ invisible: deleteLoading[user.id] }"
                    >
                      <span
                        tag="button"
                        :class="
                          !canSeeDeletesDropDown(user) && 'btn-border-right'
                        "
                        class="btn btn-primary btn-keep-height"
                        @click.prevent="editUser(user)"
                        >Edit</span
                      >
                      <button
                        v-if="canSeeDeletesDropDown(user)"
                        type="button"
                        class="btn btn-primary dropdown-toggle dropdown-toggle-split btn-keep-height"
                        data-toggle="dropdown"
                        aria-haspopup="true"
                        aria-expanded="true"
                      ></button>
                      <div class="dropdown-menu dropdown-menu-right">
                        <a
                          v-if="currentLocationId"
                          class="dropdown-item"
                          href="javascript:void(0);"
                          @click.prevent="removeFromLocation(user)"
                          >Remove from Location</a
                        >
                        <a
                          v-if="logInUser.id !== user.id"
                          class="dropdown-item"
                          href="javascript:void(0);"
                          @click.prevent="deleteUser(user)"
                          >Delete</a
                        >
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <PageListNavigation
        :skip="skip"
        :limit="limit"
        :length="users.length"
        @previousPage="previousPage"
        @nextPage="nextPage"
      />
    </div>
    <EditTeamMemberModal
      :values="modalValues"
      @created="filterUsers"
      @hidden="modalValues = { visible: false }"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import libphonenumber from 'google-libphonenumber'
import { Company, Location, User } from '@/models'
import { UserState } from '@/store/state_models'
import { mapState } from 'vuex'
import Avatar from '@/pmd/components/Avatar.vue'
import vSelect from 'vue-select'
const EditTeamMemberModal = () =>
  import(
    /* webpackChunkName: "team-edit" */ '../../components/EditTeamMemberModal.vue'
  )
import PhoneNumber from '@/pmd/components/util/PhoneNumber.vue'
import ListLoading from '../../components/util/ListLoading.vue'
import PageListNavigation from '../../components/util/PageListNavigationMongo.vue'
import UserService from '../../../services/UsersService'
import LocationsService from '../../../services/LocationsService'
var phoneUtil = libphonenumber.PhoneNumberUtil.getInstance()
var PNF = libphonenumber.PhoneNumberFormat
declare var $: any
let unsubscribeLocations: () => void
let cancelUsersSubscription: () => void
export default Vue.extend({
  components: {
    Avatar,
    EditTeamMemberModal,
    PhoneNumber,
    ListLoading,
    PageListNavigation,
    vSelect,
  },
  data() {
    return {
      company: undefined as Company | undefined,
      currentLocationId: '' as string,
      users: [] as User[],
      locations: [] as Location[],
      modalValues: {
        visible: false,
        user: undefined as User | undefined,
      },
      deleteLoading: {},
      loadingUsers: true,
      searchLoading: false,
      userTypes: ['Account', 'Agency'],
      userRoles: ['User', 'Admin'],
      filters: {
        search: null,
        role: null,
        type: null,
        location: null,
      },
      filteredUsers: [] as User[],
      currentPageNumber: 0,
      maxAmountOfPages: 1,
      searchDebouncer: undefined,
      fetchCounter: 0,
      isLoading: false,
      limit: 20,
      skip: 0,
      selectedLocations: [],
      userLocations: [],
      userLocationIds: []
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.fetchData()
    },
  },
  computed: {
    pluralizeEmployee(): string {
      return this.filteredUsers.length === 1 ? 'Employee' : 'Employees'
    },
    ...mapState('user', {
      logInUser: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    filteredLocations(): Location[] {
      if (
        this.locations.length > 0 &&
        this.selectedLocations?.length > 0
      ) {
        return lodash.filter(this.locations, location => {
          return !this.selectedLocations.find(selectedLocation => location.id === selectedLocation.id)
        })
      }
      return this.locations
    },
    isV2SideBarEnabled() {
      return this.getSideBarVersion == 'v2'
    },
    getSideBarVersion(): string {
        return this.$store.getters['sidebarv2/getVersion'] 
    },
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    const authUser = await this.$store.dispatch('auth/get')
    this.company = await Company.getById(authUser.companyId)
    this.fetchData()
  },
  beforeDestroy() {
    if (unsubscribeLocations) unsubscribeLocations()
    if (cancelUsersSubscription) cancelUsersSubscription()
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }
    if (this.$route.query.search) {
      // When the user is specified in the query, have it filtered already. Implemented because of Internal Source of manual user inputs.
      this.filters.search = this.$route.query.search
    }
  },
  updated() {
    const _self = this
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  methods: {
    async editUser(user: User) {
      let newUser = await User.getById(user.id);
      this.modalValues = {
        visible: true,
        user: newUser,
        companyId: this.company.id,
        isEditing: true,
      };
    },
    profileColor(user): string {
      let str = [user.title, user.firstName, user.lastName].filter(d => d).join(' ')
      if (user.phone) str += user.phone
      if (user.email) str += user.email
      let hash = 0
      if (str.length == 0) return '#afb8bc'
      for (let i = 0; i < str.length; i++) {
        hash = str.charCodeAt(i) + ((hash << 5) - hash)
        hash = hash & hash // Convert to 32bit integer
      }
      // var colourIndex = Math.abs(hash) % 10;
      // return User.colours[colourIndex];
      const shortened = Math.abs(hash) % 360
      return 'hsl(' + shortened + ',35%, 60%)'
    },
    initials(user) {
      let initials = ''
      if (user.firstName) initials += user.firstName.substring(0, 1).toUpperCase()
      if (user.lastName) initials += user.lastName.substring(0, 1).toUpperCase();

      return initials
    },
    async loadMore() {
      this.fetchCounter++;
      const counter = this.fetchCounter
      this.loadingUsers = true
      this.users = [];
      return UserService.search(
        this.company.id,
        this.skip,
        this.limit,
        this.filters.search || undefined,
        this.filters.role || undefined,
        this.filters.type || undefined,
        this.currentLocationId || this.filters.location?.id || undefined
      ).then(async (response) => {
        if (counter !== this.fetchCounter) return
        let locationIds = [];
        this.users = response.users.map(user => {
          if (user.locations && Object.keys(user.locations).length) {
            Object.keys(user.locations).forEach(locationId => {
              if (! this.userLocationIds.includes(locationId)) {
                locationIds.push(locationId)
              }
            })
          }

          return  {
            ...user,
            fullName: [user.title, user.firstName, user.lastName].filter(d => d).join(' '),
            initials: this.initials(user),
            profileColor: this.profileColor(user)
          }
        });
        if (!this.currentLocationId && locationIds.length) {
           await LocationsService.ids(locationIds).then(response => {
            this.userLocations = this.userLocations.concat(response.locations.map(location => {
              this.userLocationIds.push(location._id)
              return { ...location, id: location._id }
            }))
          })
        }
        this.filteredUsers = this.users
        this.skip += this.limit
        this.loadingUsers = false
      })
    },
    async fetchData() {
      this.loadingUsers = true
      if (cancelUsersSubscription) cancelUsersSubscription()
      if (unsubscribeLocations) unsubscribeLocations()
      this.skip = 0;
      await this.loadMore()

      // if (!this.currentLocationId) {
      //   unsubscribeLocations = (
      //     await Location.fetchAllLocationsRealtimeFirestore()
      //   ).onSnapshot(snapshot => {
      //     this.locations = snapshot.docs.map(d => new Location(d))
      //   })
      // }

      if (!this.currentLocationId) {
        this.locations = await this.$store.getters['locations/searchByName']('')
      }
    },
    runSearch: async function(name){
      let locations = await this.$store.getters['locations/searchByName'](name)
      this.locations = locations
    },
    updateSearchText: function (name) {
      if (this.searchDebouncer) clearTimeout(this.searchDebouncer)
      this.searchDebouncer = setTimeout(() => {
          this.runSearch(name)
      }, 300)
    },
    filterUsers() {
      this.users = [];
      this.skip = 0;
      this.loadMore()
    },
    getUserLocationsString(user: User) {
      if (!lodash.isEmpty(user.locations) && !lodash.isEmpty(this.locations)) {
        const ids = lodash.keys(this.locations)
        for (let i = 0; i < ids.length; i++) {
          let location = <Location>(
            lodash.find(this.userLocations, { id: Object.keys(user.locations)[i] })
          )
          if (location) return location.name
        }
      }
      return ''
    },
    getUserLocationsCount(user: User): string {
      if (lodash.isEmpty(user.locations)) return ''
      const count = Object.keys(user.locations).length
      if (count > 1) return '+' + (count - 1)
      else return ''
    },
    async deleteUser(user: User) {
      let msg = `${
        user && user.fullName ? user.fullName : 'This user'
      } will be unassigned from Calendars, Campaigns, Conversations, Contacts, Smart Lists, Opportunities, Triggers and Twilio Numbers in ALL Locations that they currently belong to. Are you sure you want to delete ${
        user && user.fullName ? user.fullName : 'this user'
      }?`
      this.$uxMessage('consent', msg, async res => {
        if (res === 'ok') {
          try {
            this.$set(this.deleteLoading, user.id, true)
            const response = await this.$http.get(
              `/user/delete/${user.email}?contact_id=${user.id}`
            )
            if (response.status === 200) {
              this.deleteLoading[user.id] = false
              this.$uxMessage(
                'info',
                `${
                  user && user.fullName ? user.fullName : 'This user'
                } is being deleted. Changes can take a few minutes to take effect.`
              )

              setTimeout(async() => {
                await this.filterUsers()
              }, 1000)
            }
          } catch (err) {
            console.error(err.response && err.response.data)
            this.deleteLoading[user.id] = false
          }
        }
      })
    },
    async removeFromLocation(mongoUser: User) {
      let user = await User.getById(mongoUser.id)
      let msg = `${
        user && user.name ? user.name : 'This user'
      } will be unassigned from Calendars, Campaigns, Conversations, Contacts, Smart Lists, Opportunities, Triggers and Twilio Numbers. Are you sure you want to remove ${
        user && user.name ? user.name : 'this user'
      } from the current Location?`
      this.$uxMessage(
        'consent',
        msg,
        async res => {
          if (res === 'ok') {
            this.$set(this.deleteLoading, user.id, true)
            try {
              user.removeAllSmartListsForLocation(this.currentLocationId)
              const response = await this.$http.get(
                `/user/remove-from-location/${user.id}?location_id=${this.currentLocationId}`
              )
              if (response.status === 200) {
                this.deleteLoading[user.id] = false
                this.$uxMessage(
                  'info',
                  `${
                    user && user.name ? user.name : 'This user'
                  } is being removed from the location. Changes can take a few minutes to take effect.`
                )

                setTimeout(async() => {
                  await this.filterUsers()
                }, 1000)
              }
            } catch (err) {
              this.$uxMessage(
                'error',
                'An error has ocurred. Please try again.'
              )
              console.error(err.response && err.response.data)
              this.deleteLoading[user.id] = false
            }
          }
        },
        { consentString: 'Remove' }
      )
    },
    canSeeDeletesDropDown(user: User): boolean {
      return Boolean(
        this.logInUser &&
          this.logInUser.role == 'admin' &&
          ((this.logInUser.type == 'agency' && this.currentLocationId) ||
            this.logInUser.id !== user.id)
      )
    },
    previousPage() {
      if (this.loadingUsers) {
        return
      }
      this.skip = this.skip - (this.limit * 2);
      this.loadMore()
    },
    nextPage() {
      if (this.loadingUsers) {
        return
      }
      this.loadMore()
    }
  },
})
</script>

<style>
#account-page-location-filter .vs--open .vs__selected {
  margin-top: 7px;
  display: block;
}
</style>

<style scoped>
.btn-border-right {
  border-top-right-radius: 0.3125rem !important;
  border-bottom-right-radius: 0.3125rem !important;
}

.btn-keep-height {
  height: 43px;
}

.btn-group {
  white-space: nowrap;
}

.v-select {
  min-width: 230px;
  height: auto;

}
.v-select .vs__dropdown-toggle {
  height: 50px;
}
</style>
