<template>
  <div>
    <div class="form-group">
      <UITextInputGroup
        type="text"
        v-model="customerId"
        name="customerId"
        placeholder="Stripe Customer ID"
        @input="setValue('stripe_customer_id', customerId)"
        :error="errors.has('customerId')"
        :errorMsg="'Customer Id is required.'"
      />
    </div>
    <div class="form-group">
      <UITextInputGroup
        type="text"
        v-model="description"
        name="description"
        placeholder="Description"
        @input="setValue('description', description)"
        :error="errors.has('description')"
        :errorMsg="'Description is required.'"
      />
    </div>
    <div class="form-group">
      <div class="form-input-dropdown dropdown">
        <div data-toggle="dropdown">
          <i class="icon icon-arrow-down-1"></i>
          <input
            type="text"
            class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
            placeholder="Enter Amount"
            v-model="formattedAmount"
            name="amount"
          >
        </div>
        <div class="dropdown-menu">
          <a
            class="dropdown-item trigger-type"
            href="javascript:void(0);"
            v-for="customField in customFieldsList"
            :key="customField.id"
            @click.prevent="formattedAmount = customField.fieldKey"
          >
            <p>
              {{ customField.fieldKey }}
            </p>
          </a>
        </div>
      </div>
    </div>
    <div class="form-group">
      <div class="form-input-dropdown dropdown">
        <div data-toggle="dropdown">
          <i class="icon icon-arrow-down-1"></i>
          <input type="text" class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800" data-lpignore="true" placeholder="Select Currency" :value="currencyPicked">
        </div>
        <div class="dropdown-menu">
          <a class="dropdown-item trigger-type" href="javascript:void(0);" v-for="option in currencyOptions" :key="option" @click.prevent="setValue('currency', option)">
              <p>{{option}}</p>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import { currency } from '../../../util/currency_helper';
import vSelect from 'vue-select';
import { Location, CustomField, CustomValue } from '../../../models';

declare var $: any
export default Vue.extend({
  props: ['action', 'triggerType'],
  components: { vSelect },
  data() {
    return {
      customerId: '',
      description: '',
      amount: 0,
      currentLocationId: '',
      customFieldsList: [] as any[]
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
			this.currentLocationId = id;
		}
  },
  computed: {
    currencyOptions() {
      let currencyOptions = []
      for (const key of Object.keys(currency)) {
        currencyOptions.push(currency[key].code)
      }
      return currencyOptions
    },
    formattedAmount: {
      get() {
        if (lodash.find(this.customFieldsList, { fieldKey: this.amount })) {
          return this.amount;
        }
        return !isNaN(this.amount) ? this.amount : '';
      },
      set(value) {
        this.amount = value;
        if (lodash.find(this.customFieldsList, { fieldKey: value })) {
          this.setValue('amount', this.amount);
        } else if (/^\d*\.?\d{0,2}$/.test(value)) {
          this.setValue('amount', this.amount);
        } else {
          this.amount = this.action.amount;
        }
      }
    },
    currencyPicked() {
      return this.action.currency ? this.action.currency : 'USD';
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id;

    const customFields = await CustomField.getByLocationIdAndType(this.currentLocationId, 'contact');
    this.customFieldsList = customFields.filter(c => {
      if (c.dataType === 'NUMERICAL') {
        c.fieldKey = '{{' + c.fieldKey + '}}';
        return c;
      }
    });

    const customValues = await CustomValue.getByLocationId(this.currentLocationId);
    this.customFieldsList.push(...customValues.filter(c => {
      if (!isNaN(c.value)) {
        c.fieldKey = '{{custom_values.' + c.fieldKey + '}}';
        return c;
      };
    }));

    if (this.action.stripe_customer_id) this.customerId = this.action.stripe_customer_id;
    if (this.action.description) this.description = this.action.description;
    if (this.action.amount) this.amount = this.action.amount;
    else this.setValue('currency', 'USD');
  },
  methods: {
    setValue(field: string, value: string) {
      Vue.set(this.action, field, value)
      this.$emit('update:action', this.action)
    }
  }
})
</script>
