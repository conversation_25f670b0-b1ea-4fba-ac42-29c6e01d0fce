<template>
  <div class="hl_saas--services">
    <div class="card">
      <div class="card-header">
        <h3>SaaS Mode</h3>
        <span v-if="updating">Updating...</span>
        <div v-else-if="saasModeInActive" class="toggle">
          <span>Enable</span>
          <UIToggle
            id="manage_saas_mode"
            :disabled="updating"
            :value="toggleState"
            @change="toggleSaasMode"
          />
          <label class="tgl-btn" for="manage_saas_mode"></label>
        </div>
      </div>
      <div class="card-body">
        <div v-if="saasModeEnabled">
          <p>Saas Mode is Active!</p>
        </div>
        <div v-else-if="saasModePending">
          <p>Seems like no payment methods are added! Please add a payment method to activate the saas mode</p>
        </div>
        <div v-else>
          <p>Activate Saas Mode to take your agency to next level!</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
   currentSaasMode: {
     type: String
   },
   updating: {
     type: Boolean,
     default: false
   }
  },
  computed: {
    saasModeEnabled(): boolean {
      return this.currentSaasMode === 'activated';
    },
    saasModePending(): boolean {
      return this.currentSaasMode === 'setup_pending';
    },
    saasModeInActive(): boolean {
      return this.currentSaasMode === 'not_activated';
    }
  },
  data() {
    return {
      toggleState: false
    }
  },
  methods: {
    toggleSaasMode() {
      this.$emit('input', !this.toggleState);
    }
  },
  watch: {
    'updating': function(newValue, oldValue) {
      if (!newValue && oldValue) {
        this.toggleState = this.saasModeEnabled
      }
    }
  }
})
</script>
<style scoped>
.card-header {
  display: flex;
  justify-items: space-between;
  padding: 25px 30px;
}

.card-header .toggle {
  display: flex;
  align-items: center;
}

.card-header .toggle span {
  margin-right: 12px;
}
</style>
