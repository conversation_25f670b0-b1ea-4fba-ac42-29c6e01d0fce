import { <PERSON><PERSON><PERSON>, ActionContext } from 'vuex'
import { RootState, PipelineState } from '../store/state_models'
import { Pipeline } from '../models'

let pipelineListner

export const pipelines: Module<PipelineState, RootState> = {
  namespaced: true,
  state: { pipelines: [], locationId: undefined },
  getters: {
    getById: (state: PipelineState) => {
      return (id: string) => state.pipelines.find(x => x.id === id)
    }
  },
  mutations: {
    addUpdate: (state: PipelineState, payload: { pipelines: any[], locationId: string } ) => {
      state.pipelines = payload.pipelines
      if (payload.locationId) {
        state.locationId = payload.locationId
      }
    },
    clearAll: (state: PipelineState) => {
      state.pipelines = []
      state.locationId = undefined
      if (pipelineListner) {
        pipelineListner()
      }
    }
  },
  actions: {
    async syncAll(
      context: ActionContext<PipelineState, RootState>,
      locationId: string
    ) {

      if (!locationId) {
        context.commit('clearAll')
        return
      }

      if (context.state.locationId === locationId) {
        return context.state.pipelines;
      }

      context.commit('clearAll')
      return new Promise((resolve, reject) => {
        
        if (pipelineListner) {
          pipelineListner()
        }

        pipelineListner = Pipeline.getByLocationIdQuery(locationId).onSnapshot(
          snapshot => {
            context.commit('addUpdate', {
              locationId: locationId,
              pipelines: snapshot.docs.map(d => {
                return {
                  id: d.id,
                  ...d.data()
                }
              })
            })

            resolve(context.state.pipelines.pipelines)
          }, err => resolve()
        )
      })
    },
    logout: {
      root: true,
      handler: async (context: ActionContext<PipelineState, RootState>) => {
        context.commit('clearAll')
      }
    },
    clearAll: {
      handler: async (context: ActionContext<PipelineState, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
