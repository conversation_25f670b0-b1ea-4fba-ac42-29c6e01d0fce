import axios from 'axios'

export default class BaseService {
  private baseUrl: string

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl
  }

  private responseBody = res => res.data
  private getHeader = () => {
    return {
      Channel: 'APP',
      Source: 'WEB_USER',
      Version: '2021-04-15',
    }
  }

  protected requests = {
    get: (url: string) =>
      axios({
        url: this.baseUrl + url,
        method: 'GET',
        headers: this.getHeader(),
      }).then(this.responseBody),
    post: (url: string, body: { [key: string]: any }) =>
      axios({
        url: this.baseUrl + url,
        method: 'POST',
        headers: this.getHeader(),
        data: body,
      }).then(this.responseBody),
    put: (url: string, body: { [key: string]: any }) =>
      axios({
        url: this.baseUrl + url,
        method: 'PUT',
        headers: this.getHeader(),
        data: body,
      }).then(this.responseBody),
    delete: (url: string) =>
      axios({
        url: this.baseUrl + url,
        method: 'DELETE',
        headers: this.getHeader(),
      }).then(this.responseBody),
  }
}
