<template>
	<div
		class="modal fade hl_services-add-location-modal"
		id="services-add-location-modal"
		tabindex="-1"
		role="dialog"
	>
		<div class="modal-dialog --small" role="document">
      <div class="modal-content" v-if="showPaymentMethod" >
        <billing-method :show-heading="true" :show-cancel="true" :show-sca-warning="true" save-button-text="Update" @cardChangedTimeout="hidePaymentMethod" @cancel="hidePaymentMethod"/>
      </div>
      <div class="modal-content" v-show="!showPaymentMethod">
				<div class="modal-header">
					<div class="modal-header--inner">
						<h5 class="modal-title">Add to a location</h5>
						<button
							type="button"
							class="close"
							data-dismiss="modal"
							aria-label="Close"
						>
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</div>

				<div class="modal-body" v-if="selectedPlan">
					<div class="modal-body--inner">
						<div class="plan">
							<i class="plan-icon --blue"
								><img :src="service.icon_url" alt="icon"
							/></i>
							<div class="text">
								<h4>{{ service.name }}</h4>
								<p>{{ selectedPlan.name }} plan</p>
							</div>
						</div>
						<div class="form-group">
							<label>Location</label>
							<select
								class="selectpicker"
								v-model="selectedLocation"
								@change="onChangeLocation"
							>
								<option
									v-for="location in locations"
									:key="location.id"
									:value="location.id"
									>{{ location.name }} <span v-if="location.address"
										>({{ location.address
												.split(',')
												.splice(0, 2)
												.join(',') }})</span
									></option
								>
							</select>
						</div>
						<div class="row" v-if="status !== 'paid' && !isAlreadyActive">
							<div class="form-group col-sm-6">
								<label>Name* &nbsp;<span class="hl_tooltip" v-b-tooltip.hover title="Name of the person to contact">i</span></label>
								<input
									type="text"
									class="form-control"
									placeholder="Full Name"
									v-model="contactName"
									name="userName"
									v-validate="'required'"
								/>
								<span v-show="errors.has('userName')" class="error">Name is required</span>
							</div>
							<div class="form-group col-sm-6" >
								<label>Best number to call*</label>
								<PhoneNumber
									placeholder="Phone Number"
									v-model="contactNumber"
									name="msgsndr1"
									autocomplete="msgsndr1"
									v-validate="'required|phone'"
								/>
								<span v-show="errors.has('msgsndr1')" class="error">Phone Number is required</span>
							</div>
							<div class="form-group col-sm-12" >
								<label>Contact email*</label>
								<input
									type="email"
									class="form-control"
									placeholder="Email Address"
									v-model="contactEmail"
									name="userEmail"
									v-validate="'required|email'"
								/>
								<span v-show="errors.has('userEmail')" class="error">Email ID is required</span>
							</div>
						</div>

						<ul class="prices" v-if="status !== 'paid' && !isAlreadyActive">
							<li>
								<div class="price-info">
									<!-- <i class="icon --green"><img src="./img/icon-dollar.svg" alt="icon"></i> -->
									<div class="text">
										<h5>Setup fee</h5>
										<p>One time payment right now</p>
									</div>
								</div>
								<p class="the-price">
									${{ selectedPlan.wholesale_setup_price }}
								</p>
							</li>
							<li>
								<div class="price-info">
									<!-- <i class="icon --yellow"><img src="./img/icon-refresh.svg" alt="icon"></i> -->
									<div class="text">
										<h5>Recurring monthly payment</h5>
										<!-- <p>Starting {{ trialEndDate() }}</p> -->
									</div>
								</div>
								<p class="the-price">
									${{
										selectedPlan.wholesale_recur_price
									}}/month
								</p>
							</li>
						</ul>
						<div
							class="option"
							v-if="status !== 'paid' && !isAlreadyActive"
						>
							<input
								type="checkbox"
								id="agree"
								v-model="isAgree"
							/>
							<label for="agree" style="line-height:20px">
								I agree to pay the setup fee immediately using the card on file.
								Once services are fully online, I will start my monthly subscription.
							</label>
						</div>
						<div class="success-msg" v-else-if="status === 'paid' || isLoading" style="line-height:20px">
							We have received your request. <br/>Our Whitelabel Services team will contact you ASAP with the next steps!
						</div>
						<div class="success-msg" v-else>
							The service is currently {{activePlan.status}} for this location !!
						</div>
						<div class="warning-bar" v-if="company && !company.stripeId">
							Your Payment configurations are missing !!
							<br /><NAME_EMAIL>
						</div>
					</div>
				</div>
				<div class="modal-footer">
          <div class="modal-footer--inner nav" style="display: block; padding: 12px;" v-if="scaPending">
            <div>
              Please <span class="purchase-summary__link" @click="showPaymentMethod = true">authenticate</span> your card details again
              <span class="hl_tooltip pointer" v-b-tooltip.hover title="We have moved to a more secure billing system. For added security you need to authenticate your card again." ><i class="fa fa-info" style="font-size: 10px"></i></span>
            </div>
            <div class="purchase-summary__pay-btn btn btn-blue" @click="showPaymentMethod = true">
              <moon-loader v-if="isLoading" color="#ffffff" size="20px"/>
              <span v-else>Update Payment Method</span>
            </div>
          </div>
					<div
						class="modal-footer--inner"
						v-else-if="status !== 'paid' && !isAlreadyActive"
					>
						<button
							type="button"
							class="btn btn-light"
							data-dismiss="modal"
							v-if="!isLoading"
						>
							Cancel
						</button>
						<button
							type="button"
							class="btn btn-blue"
							@click="buyService"
							:disabled="!isAgree || isLoading"
						>
							<span v-if="!isLoading"
								>Pay now - ${{
									selectedPlan.wholesale_setup_price
								}}</span
							>
							<moon-loader color="#ffffff" size="20px" v-else />
						</button>
					</div>
					<div class="modal-footer--inner" v-else>
						<button
							type="button"
							class="btn btn-light"
							data-dismiss="modal"
						>
							Close
						</button>
						<button
							type="button"
							class="btn btn-blue"
							:title="`Request to ${upgradeAction[1]} this service`"
							@click="upgradeRequest"
							v-if="status !== 'paid'"
							:disabled="isRequesting"
						>
							<span v-if="!isRequesting">{{upgradeAction[0]}}</span>
							<moon-loader color="#ffffff" size="20px" v-else />
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
import moment from "moment-timezone";
import { Company } from '@/models';
const PhoneNumber = () => import('../../../components/util/PhoneNumber.vue');
import BillingMethod from "@/pmd/pages/agency/billing/BillingMethod.vue";

declare var $: any

export default Vue.extend({
	props: ["selectedPlan", "service", "company"],
	components: { PhoneNumber, BillingMethod },
	data() {
		return {
			searchText: '',
			selectedLocation: '',
			contactName: '',
			contactEmail: '',
			contactNumber: '',
			isAgree: false,
			isLoading: false,
			isRequesting: false,
			status: 'unpaid', // 'unpaid' || 'paid' || 'failed',
			// company: Company,
      activePlan: {},
      scaPending: false,
      showPaymentMethod: false
		}
	},
	mounted() {
		setTimeout(() => {
			this.initModal();
		}, 3000);
		this.fetchCompanyDetails();
		this.$parent.$on('openModal', this.resetModal);
	},
	methods: {
		trialEndDate() {
			if (this.selectedPlan.trial_days === 0)
				return 'today';
			return moment().add(this.selectedPlan.trial_days + 1, 'days').format('MMM D, YYYY') //.toLocaleString();
    },
    async verifySCA(){
      this.isLoading = true;
      try{
        let verificationData = await this.$http.post('/stripe/verify_sca',{
          company_id: this.company.id
        });
        if(!verificationData.data.default_payment_method){
          this.scaPending = true;
        }

      } catch (err) {
        //
        console.error(err);
      } finally{
        this.isLoading = false;
      }
    },
    hidePaymentMethod(){
      this.scaPending = false;
      this.verifySCA();
      this.showPaymentMethod = false;
    },
		async buyService() {
			const result = await this.$validator.validateAll();
			if (!result || !this.isAgree) {
				return false;
      		}
			this.isLoading = true;
			const authUser = await this.$store.dispatch("auth/get");
			const company = await this.$store.dispatch('company/get');
			const customerId = company.data.stripe_id;
			const locationIndex = this.locations.findIndex(x => x.id === this.selectedLocation);
			const locationName = this.locations[locationIndex].name || '';
			// TODO: check for valid companyId and setupId
			try {
				const response = await this.$http.post(`/service/${this.service.id}/load/${this.selectedLocation}`, {
					planLevel: this.selectedPlan.plan_level,
					companyId: company.id,
					customerId,
					locationName,
					// serviceName: this.service.name,
					userId: authUser.userId,
					userName: this.contactName,
					userMail: this.contactEmail,
					userNumber: this.contactNumber,
					medium: this.company.card_brand + '_' + this.company.card_last_4,
				})
				this.status = 'paid';
				this.isLoading = false;
			} catch (err) {
				this.status = 'failed'
				this.isLoading = false;
				console.error(err);
			}
		},
		async upgradeRequest(){
			try{
				this.isRequesting = true;
				const response = await this.$http.post(`/service/upgrade_request/${this.activePlan.sevice_order_id}?action=${this.upgradeAction[2]}`);
				this.status = 'paid';
				this.isRequesting = false;
			} catch (err) {
				this.status = 'failed'
				this.isRequesting = false;
				console.error(err);
			}

		},
		initModal() {
			this.selectedLocation = this.locations[0] ? this.locations[0].id : '';
			this.status = 'unpaid';
		},
		onChangeLocation(e) {
			this.status = 'unpaid';
			this.isAgree = false;
		},
		async fetchCompanyDetails() {
			// let company = await this.$store.dispatch('company/get');
			// this.company = company.data;
			let user = await this.$store.dispatch('user/get');
			this.contactEmail = user.email;
			this.contactNumber = user.phone;
			this.contactName = `${user.first_name} ${user.last_name}` ;
		},
		resetModal() {
			this.isRequesting = false;
			this.isLoading = false;
      this.status = 'unpaid';
      this.verifySCA();
		}
	},
	computed: {
		locations(): Location[] {
			var locations = this.$store.getters['locations/searchByName'](this.searchText)
			return locations;
		},
		upgradeAction(): string[] {
			if(this.activePlan.status === 'inactive'){
				return ['Activate','activate','ACTIVATION'];
			}else if(this.activePlan.plan_level === this.selectedPlan.plan_level){
				return ['Change Plan', 'change current plan of','PLAN-CHANGE'];
			}else{
				return ['Upgrade','upgrade','UPGRADATION'];
			}
		},
		isAlreadyActive(): Boolean {
			const loc_index = this.locations.findIndex(x => x.id === this.selectedLocation);
			if (loc_index !== -1) {
				const locationServices = this.locations[loc_index].services;
				if (locationServices) {
					const enabledServices = Object.values(locationServices);
					const serviceIndex = enabledServices.findIndex(x => x.service_id === this.service.id )
					if (serviceIndex !== -1) { // Plan is already active for this location.
						// this.status = 'paid';
						this.activePlan = enabledServices[serviceIndex];
						return true;
					}
				}

			}
			// this.status = 'unpaid';
			return false;
		}
	},
	updated() {
		const $selectpicker = $(this.$el).find('.selectpicker')
		if ($selectpicker) {
			$selectpicker.selectpicker('refresh')
		}
	},
})
</script>
<style scoped>
/* .selectpicker li.active a.active{
    background: #188bf6;
    color: #fff;
} */

.btn-blue[disabled='disabled'] {
	cursor: not-allowed;
	pointer-events: none;
}
.success-msg {
	text-align: center;
}
.warning-bar{
    width: 100%;
    padding: 12px;
    border-left: 4px solid #ffbc00;
    background-color: #fafafa;
    font-size: 12px;
    line-height: 16px;
    margin: 10px 0px;
}
.purchase-summary__link{
  color: #2F80ED;
  font-weight: 500;
  cursor: pointer;
}
.purchase-summary__pay-btn{
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  font-weight: 500;
  padding: 12px;
  width: 100%;
  margin-top: 8px;
  margin-left: 0px !important;
}
</style>
