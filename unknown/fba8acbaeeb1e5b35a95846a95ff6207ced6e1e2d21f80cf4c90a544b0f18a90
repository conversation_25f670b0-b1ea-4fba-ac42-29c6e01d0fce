<template>
  <div class="relative flex items-start">
    <div class="flex items-center h-5">
      <input
        :id="id"
        aria-describedby="checkbox-description"
        type="checkbox"
        class="group focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded cursor-pointer"
        :disabled="disabled"
        :checked="value"
        @input="$emit('input', $event.target.checked)"
      />
    </div>
    <div class="ml-2 text-sm">
      <label
        :for="id"
        class="font-medium cursor-pointer"
        :class="disabled ? 'text-gray-300' : 'text-gray-700'"
        v-b-tooltip.hover
        :title="tooltip"
        >{{ label }}</label
      >
    </div>
  </div>
</template>
<script>
import { v4 as uuid } from 'uuid'
export default {
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    tooltip:{
        type:String,
        default:''
    }
  },
  data() {
    return {
      id: uuid()
    }
  },
}
</script>
