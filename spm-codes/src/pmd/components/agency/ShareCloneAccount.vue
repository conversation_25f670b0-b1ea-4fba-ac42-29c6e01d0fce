 <template>
  <div class="modal fade hl_sms-template--modal" tabindex="-1" role="dialog" ref="modal">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Share Snapshot</h2>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner" v-if="page==='initial'">
            <div class="form-group">
              <UITextLabel>Share Type</UITextLabel>
              <select class="selectpicker" title="Share Type" name="shareType" v-model="shareType">
                <option value="link">Get share link</option>
                <option value="email">Email share link</option>
                <option value="permanent_link">Get permanent link</option>
              </select>
            </div>
            <div class="form-group" v-if="shareType==='email'">
              <UITextLabel class="mr-3">
                Emails
                <small>(Enter one per line)</small>
              </UITextLabel>
              <UITextAreaGroup
                type="text"
                v-model="shareEmails"
                v-validate="'required'"
                name="emails"
                rows="5"
                :error="errors.has('emails')"
                :errorMsg="'Alteast 1 email required.'"
              ></UITextAreaGroup>
            </div>
          </div>
          <div class="modal-body--inner" v-if="page==='showLink'">
            <code style="word-break: break-word;background: #f2f7fa;" v-if="share">{{ shareLink }}</code>
          </div>
        </div>

        <div class="modal-footer">
          <div class="modal-footer--inner nav" v-if="page==='initial'">
            <UIButton type="button" use="outline" data-dismiss="modal">Cancel</UIButton>
            <div style="display: inline-block;position: relative;">
              <UIButton
                type="button"
                @click.prevent="positiveButtonClicked"
                :loading="processing"
              >{{positiveButton}}</UIButton>
            </div>
          </div>
          <div class="modal-footer--inner nav" v-if="page==='showLink'">
            <UIButton type="button" use="outline" data-dismiss="modal">Close</UIButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import * as lodash from 'lodash'
import vSelect from 'vue-select'
import { mapState } from 'vuex'
import { LocationState } from '@/store/state_models'
const MoonLoader = () => import('@/pmd/components/MoonLoader.vue')
import { ShareAccount, CloneAccount, Company } from '@/models'
import config from '../../../config'

declare var $: any

export default Vue.extend({
  props: ['values'],
  components: {
    MoonLoader,
    vSelect
  },
  data() {
    return {
      snapshot: undefined as CloneAccount | undefined,
      share: undefined as ShareAccount | undefined,
      company: undefined as Company | undefined,
      processing: false,
      shareType: 'link',
      shareEmails: '',
      page: 'initial',
      userId: ''
    }
  },
  methods: {
    async positiveButtonClicked() {
      const result = await this.$validator.validateAll()
      if (!result) {
        return false
      }

      this.processing = true
      if (this.shareType === 'email') {
        const emails = this.shareEmails.split(/[\s,;\t\n]+/).map(v => v.trim())
        const response = await this.$http.post('/share/email', {
          emails,
          user_id: this.userId,
          id: this.snapshot.id
        })
        this.$emit('hidden')
      } else if (
        this.shareType === 'link' ||
        this.shareType === 'permanent_link'
      ) {
        const response = await this.$http.post('/share/link', {
          share_type: this.shareType,
          snapshot_id: this.snapshot.id
        })
        this.share = new ShareAccount(response.data)
        this.processing = false
        this.page = 'showLink'
      }
    }
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  computed: {
    positiveButton(): string | undefined {
      switch (this.shareType) {
        case 'link':
        case 'permanent_link':
          return 'Get link'
        case 'email':
          return 'Send email'
      }
    },
    shareLink(): string {
      let link = ''
      const referralId = this.company ? this.company.referralId : ''
      const shareId = this.share ? this.share.id : ''

      switch (config.mode) {
        case 'production': {
          link = `https://affiliates.gohighlevel.com/?fp_ref=${referralId}&share=${shareId}`
          break
        }
        case 'dev': {
          link = `http://localhost:8080/settings/snapshot/import/${shareId}`
          break
        }
        case 'staging': {
          link = `https://affiliate-dot-highlevel-staging.appspot.com/?fp_ref=${referralId}&share=${shareId}`
          break
        }
        default: {
          link = `https://${window.location.hostname}/settings/snapshot/import/${shareId}`
        }
      }

      return link
    }
  },
  watch: {
    async values(values: { [key: string]: any }) {
      const data: () => object = <() => object>this.$options.data
      if (data) Object.assign(this.$data, data.apply(this))
      if (values.visible) $(this.$refs.modal).modal('show')
      else $(this.$refs.modal).modal('hide')

      if (values.snapshotId) {
        this.snapshot = await CloneAccount.getById(values.snapshotId)
        this.company = await Company.getById(this.snapshot.companyId)
        this.userId = values.userId
      }
    }
  },
  updated() {
    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }

    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hidden')
    })

    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  }
})
</script>
<style>
.dropdown-toggle::after {
  content: none;
}
</style>
