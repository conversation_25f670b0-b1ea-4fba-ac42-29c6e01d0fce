<template>
  <tr :class="warning ? 'table_warning' : ''">
    <td>
      <div class="avatar --sm">
        <div class="avatar_img --shadow">
          <img v-if="imgUrl" v-bind:src="imgUrl" :alt="publisherTitle" />
        </div>
        <h4>{{ publisherTitle || listingUrlDomain }}</h4>
      </div>
    </td>
    <td>
      <span>{{ name }}</span>
    </td>
    <td>{{ address }}</td>
    <td>
      <span>
        <PhoneNumber
          type="display"
          v-model="phone"
          :currentLocationId="location.id"
        />
      </span>
    </td>
    <td>
      <div class="rating --sm">
        <span class="sr-only">{{ stars }} Stars</span>
        <i v-for="stars in wholeStars" class="icon icon-star-filled"></i>
        <i v-for="stars in halfStars" class="icon icon-star-half"></i>
        <i v-for="stars in emptyStars" class="icon icon-star"></i>
      </div>
    </td>
    <td>{{ reviews }}</td>
    <td>
      <div v-if="yextReport" :class="{ 'yext-status': true }">
        <span :class="{ [`yext-status-type-${yextReport.status}`]: true }">
          {{
            yextReport.status &&
            yextReport.status.replace(/_/g, ' ').toLowerCase()
          }}

          <template
            v-if="
              $router.currentRoute.query &&
              $router.currentRoute.query.debug &&
              yextReport.statusDetails &&
              yextReport.statusDetails.length > 0
            "
          >
            <ol>
              <template v-for="eachStatus of yextReport.statusDetails">
                <li v-bind="eachStatus.message">
                  [actionable : {{ eachStatus.actionable }}]
                  {{ eachStatus.message }}
                </li>
              </template>
            </ol>
          </template>
        </span>
        <span class="link-container">
          <a v-if="listingUrl" v-bind:href="listingUrl" target="_blank">
            <i class="icon icon-share-2"></i>
          </a>
        </span>
      </div>
    </td>

    <!-- <td>
            <button type="button" class="btn btn-blue">Fix</button>
            <select class="selectpicker more-select">
                <option>Option 1</option>
                <option>Option 2</option>
                <option>Option 3</option>
            </select>
		</td>-->
  </tr>
</template>

<script lang='ts'>
import Vue from 'vue'
import ListingHelper from './../../../util/listing_helper'
import PhoneNumber from './../util/PhoneNumber.vue'

export default Vue.extend({
  props: ['report', 'yextReport', 'location', 'yextLocation'],
  components: { PhoneNumber },
  data() {
    return {
      currentLocationId: '',
    }
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
    },
  },
  computed: {
    name(): string | undefined {
      if (this.yextLocation) {
        return this.yextLocation.name
      }
    },
    address(): string | undefined {
      if (this.yextLocation) {
        let address = this.yextLocation.address
        return `${address.line1},${address.postalCode} `
      }
    },
    phone(): string | undefined {
      if (this.yextLocation) {
        return this.yextLocation.mainPhone
      } 
    },
    stars(): string | undefined {
      if (this.report) {
        return this.report.stars
      } else if (this.yextReport) {
        return 0
      }
    },
    reviews(): string | undefined {
      if (this.report) {
        return this.report.reviews
      } else if (this.yextReport) {
        return 0
      }
    },
    listingUrl(): string | undefined {
      if (this.report) {
        return this.report.listingUrl
      } else if (this.yextReport) {
        return this.yextReport.listingUrl
      }
    },
    listingUrlDomain(): string | undefined {
      let url = null
      if (this.report) {
        url = this.report.listingUrl
      } else if (this.yextReport) {
        url = this.yextReport.listingUrl
      }
      let domain = url ? new URL(url).hostname : ''
      return domain
    },
    warning(): boolean | undefined {
      if (this.report) {
        return (
          this.report &&
          (!this.report.listingFound ||
            !this.report.matchAddress ||
            !this.report.matchName ||
            !this.report.matchPhone)
        )
      } else if (this.yextReport) {
        return false
      }
    },
    imgUrl(): string | undefined {
      if (this.report && this.report.brandId) {
        const listing = ListingHelper.getNetworkInfo(this.report.brandId)
        if (listing) return listing.imgPath
      } else if (this.yextReport) {
        const listing = ListingHelper.getNetworkInfo(
          this.yextReport.publisherId
        )
        if (listing) return listing.imgPath
      }
    },
    publisherTitle(): string | undefined {
      if (this.report && this.report.brandId) {
        const listing = ListingHelper.getNetworkInfo(this.report.brandId)
        if (listing) return listing.name
      } else if (this.yextReport) {
        const listing = ListingHelper.getNetworkInfo(
          this.yextReport.publisherId
        )
        if (listing) return listing.name
      }
      console.log(
        this.report && this.report.brandId,
        this.yextReport && this.yextReport.publisherId
      )
    },
    wholeStars(): number {
      if (
        this.report &&
        this.report.averageReviewScore &&
        this.report.averageReviewScore > 0
      ) {
        return Math.floor(this.report.averageReviewScore)
      } else {
        return 0
      }
    },
    halfStars(): number {
      if (
        this.report &&
        this.report.averageReviewScore &&
        this.report.averageReviewScore > 0
      ) {
        return Math.ceil(this.report.averageReviewScore - this.wholeStars)
      } else {
        return 0
      }
    },
    emptyStars(): number {
      if (
        this.report &&
        this.report.averageReviewScore &&
        this.report.averageReviewScore > 0
      ) {
        return 5 - this.wholeStars - this.halfStars
      } else {
        return 5
      }
    },
  },
})
</script>
<style lang="scss" scoped>
.yext-status {
  text-transform: capitalize;
  color: #1789cc;
  font-size: 1rem;
  display: inline-block;
  padding-right: 10px;
  display: grid;
  grid-template-columns: auto 1fr;
  align-items: center;
  grid-gap: 10px;

  .link-container {
    width: 10px;
  }
  & .yext-status-type-WAITING_ON_YEXT,
  & .yext-status-type-WAITING_ON_PUBLISHER,
  & .yext-status-type-WAITING_ON_CUSTOMER {
    color: #f1326b;
  }

  .yext-status-type-UNAVAILABLE {
    color: #fcc031;
  }
  ol,
  li {
    margin: unset;
    list-style: auto;
  }
  ol {
    margin-top: 20px;
    min-width: 350px;
  }
  li {
    margin: 10px 10px;
  }
}
</style>
