import { <PERSON><PERSON><PERSON>, ActionContext } from 'vuex'
import { RootState, StripeConnectState } from './state_models'
import { LocationStripeConnect } from '../models'

export const stripeConnect: Module<StripeConnectState, RootState> = {
  namespaced: true,
  state: { locationId: undefined, accountId: undefined },
  mutations: {
    setAccount: (
      state: StripeConnectState,
      payload: { accountId: string; locationId: string }
    ) => {
      state.accountId = payload.accountId
      if (payload.locationId) {
        state.locationId = payload.locationId
      }
    },
    clearLocation(state: StripeConnectState) {
      state.accountId = undefined
      state.locationId = undefined
    }
  },
  actions: {
    async syncAll(context: ActionContext<StripeConnectState, RootState>, locationId: string) {
      if (!locationId) {
        context.commit('clearLocation')
        return
      }

      const accountId = await LocationStripeConnect.forLocation(locationId);
      context.commit('setAccount', { accountId, locationId });
    },
    logout: {
      root: true,
      handler: async (context: ActionContext<StripeConnectState, RootState>) => {
        context.commit('clearAll')
      }
    }
  }
}
