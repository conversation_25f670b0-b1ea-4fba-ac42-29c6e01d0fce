/**
 * User Utilities
 * 
 * Utility functions for user operations, computed properties, and permission checks
 * Based on SPM User model methods and getters
 */

import type { 
  IUser, 
  IUserPermissions, 
  IUserPermissionChecks, 
  IUserComputedProperties,
  UserRole 
} from '../types/user';
import { EntityUtils } from './modelUtils';

/**
 * User computed properties utilities
 * Based on SPM User model computed getters
 */
export class UserComputedUtils {
  /**
   * Get user's display name (firstName + lastName)
   * Based on SPM User.name getter
   */
  static getName(user: Pick<IUser, 'firstName' | 'lastName'>): string {
    return [user.firstName, user.lastName].filter(val => val).join(' ');
  }

  /**
   * Get user's full name (title + firstName + lastName)
   * Based on SPM User.fullName getter
   */
  static getFullName(user: Pick<IUser, 'title' | 'firstName' | 'lastName'>): string {
    return [user.title, user.firstName, user.lastName].filter(d => d).join(' ');
  }

  /**
   * Get user's display name with email fallback
   */
  static getDisplayName(user: Pick<IUser, 'firstName' | 'lastName' | 'email'>): string {
    const name = this.getName(user);
    return name || user.email || 'Unknown User';
  }

  /**
   * Generate profile color for user
   * Based on SPM User.profileColor getter
   */
  static getProfileColor(user: Pick<IUser, 'firstName' | 'lastName' | 'phone' | 'email'>): string {
    const fullName = this.getFullName(user);
    return EntityUtils.generateProfileColor({
      name: fullName,
      email: user.email,
      phone: user.phone
    });
  }

  /**
   * Get all computed properties for a user
   */
  static getComputedProperties(user: IUser): IUserComputedProperties {
    return {
      name: this.getName(user),
      fullName: this.getFullName(user),
      profileColor: this.getProfileColor(user),
      displayName: this.getDisplayName(user)
    };
  }
}

/**
 * User permission utilities
 * Based on SPM User model permission checking methods
 */
export class UserPermissionUtils {
  /**
   * Get default permissions for a new user
   * Based on SPM User.permissions getter default values
   */
  static getDefaultPermissions(): IUserPermissions {
    return {
      campaigns_enabled: true,
      campaigns_read_only: false,
      workflows_enabled: true,
      workflows_read_only: false,
      contacts_enabled: true,
      triggers_enabled: true,
      opportunities_enabled: true,
      settings_enabled: true,
      tags_enabled: true,
      lead_value_enabled: true,
      dashboard_stats_enabled: true,
      bulk_requests_enabled: true,
      appointments_enabled: true,
      reviews_enabled: true,
      online_listings_enabled: true,
      phone_call_enabled: true,
      conversations_enabled: true,
      marketing_enabled: true,
      bot_service: false,
      websites_enabled: true,
      membership_enabled: true,
      funnels_enabled: true,
      assigned_data_only: false
    };
  }

  /**
   * Check if user is admin
   * Based on SPM User.isAdmin getter
   */
  static isAdmin(user: Pick<IUser, 'role'>): boolean {
    return user.role === 'admin';
  }

  /**
   * Check if user can access all data (not restricted to assigned data only)
   * Based on SPM User.canAccessAll getter
   */
  static canAccessAll(user: Pick<IUser, 'permissions'>): boolean {
    return user.permissions?.assigned_data_only !== true;
  }

  /**
   * Check if user is restricted to assigned data only
   * Based on SPM User.isAssignedTo getter
   */
  static isAssignedTo(user: Pick<IUser, 'role' | 'permissions'>): boolean {
    return !this.isAdmin(user) && !this.canAccessAll(user);
  }

  /**
   * Check if user can view opportunities
   * Based on SPM User.canViewOpportunities getter
   */
  static canViewOpportunities(user: Pick<IUser, 'role' | 'permissions'>): boolean {
    return this.isAdmin(user) || (user.permissions?.opportunities_enabled === true);
  }

  /**
   * Check if user can create review requests
   * Based on SPM User.canCreateReviewRequest getter
   */
  static canCreateReviewRequest(user: Pick<IUser, 'role' | 'permissions'>): boolean {
    return this.isAdmin(user) || (user.permissions?.reviews_enabled === true);
  }

  /**
   * Check if user can access campaigns
   */
  static canAccessCampaigns(user: Pick<IUser, 'role' | 'permissions'>): boolean {
    return this.isAdmin(user) || (user.permissions?.campaigns_enabled === true);
  }

  /**
   * Check if user can access workflows
   */
  static canAccessWorkflows(user: Pick<IUser, 'role' | 'permissions'>): boolean {
    return this.isAdmin(user) || (user.permissions?.workflows_enabled === true);
  }

  /**
   * Check if user can access contacts
   */
  static canAccessContacts(user: Pick<IUser, 'role' | 'permissions'>): boolean {
    return this.isAdmin(user) || (user.permissions?.contacts_enabled === true);
  }

  /**
   * Check if user can access settings
   */
  static canAccessSettings(user: Pick<IUser, 'role' | 'permissions'>): boolean {
    return this.isAdmin(user) || (user.permissions?.settings_enabled === true);
  }

  /**
   * Get all permission checks for a user
   */
  static getPermissionChecks(user: IUser): IUserPermissionChecks {
    return {
      isAdmin: this.isAdmin(user),
      canAccessAll: this.canAccessAll(user),
      isAssignedTo: this.isAssignedTo(user),
      canViewOpportunities: this.canViewOpportunities(user),
      canCreateReviewRequest: this.canCreateReviewRequest(user),
      canAccessCampaigns: this.canAccessCampaigns(user),
      canAccessWorkflows: this.canAccessWorkflows(user),
      canAccessContacts: this.canAccessContacts(user),
      canAccessSettings: this.canAccessSettings(user)
    };
  }

  /**
   * Merge permissions with defaults
   * Ensures all permission properties are present
   */
  static mergeWithDefaults(permissions: Partial<IUserPermissions> = {}): IUserPermissions {
    return {
      ...this.getDefaultPermissions(),
      ...permissions
    };
  }

  /**
   * Check if user has specific permission
   */
  static hasPermission(
    user: Pick<IUser, 'role' | 'permissions'>, 
    permission: keyof IUserPermissions
  ): boolean {
    // Admins have all permissions
    if (this.isAdmin(user)) {
      return true;
    }
    
    // Check specific permission
    return user.permissions?.[permission] === true;
  }

  /**
   * Check if user has any of the specified permissions
   */
  static hasAnyPermission(
    user: Pick<IUser, 'role' | 'permissions'>, 
    permissions: (keyof IUserPermissions)[]
  ): boolean {
    return permissions.some(permission => this.hasPermission(user, permission));
  }

  /**
   * Check if user has all of the specified permissions
   */
  static hasAllPermissions(
    user: Pick<IUser, 'role' | 'permissions'>, 
    permissions: (keyof IUserPermissions)[]
  ): boolean {
    return permissions.every(permission => this.hasPermission(user, permission));
  }
}

/**
 * User location utilities
 * Based on SPM User model location management methods
 */
export class UserLocationUtils {
  /**
   * Check if user has access to a specific location
   */
  static hasLocationAccess(user: Pick<IUser, 'locations'>, locationId: string): boolean {
    return locationId in (user.locations || {});
  }

  /**
   * Get user's notification type for a location
   */
  static getLocationNotificationType(
    user: Pick<IUser, 'locations'>, 
    locationId: string
  ): string | undefined {
    return user.locations?.[locationId];
  }

  /**
   * Get all location IDs user has access to
   */
  static getAccessibleLocationIds(user: Pick<IUser, 'locations'>): string[] {
    return Object.keys(user.locations || {});
  }

  /**
   * Add location access for user
   */
  static addLocationAccess(
    locations: IUser['locations'] = {}, 
    locationId: string, 
    notificationType = ''
  ): IUser['locations'] {
    return {
      ...locations,
      [locationId]: notificationType
    };
  }

  /**
   * Remove location access for user
   */
  static removeLocationAccess(
    locations: IUser['locations'] = {}, 
    locationId: string
  ): IUser['locations'] {
    const newLocations = { ...locations };
    delete newLocations[locationId];
    return newLocations;
  }
}

/**
 * User validation utilities
 */
export class UserValidationUtils {
  /**
   * Validate user creation data
   */
  static validateCreateUser(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!data.firstName?.trim()) {
      errors.push('First name is required');
    }
    
    if (!data.lastName?.trim()) {
      errors.push('Last name is required');
    }
    
    if (!data.email?.trim()) {
      errors.push('Email is required');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push('Invalid email format');
    }
    
    if (!data.companyId?.trim()) {
      errors.push('Company ID is required');
    }
    
    if (!data.role || !['admin', 'user'].includes(data.role)) {
      errors.push('Valid role is required (admin or user)');
    }
    
    if (!data.type || !['agency', 'account'].includes(data.type)) {
      errors.push('Valid type is required (agency or account)');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate user update data
   */
  static validateUpdateUser(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (data.firstName !== undefined && !data.firstName?.trim()) {
      errors.push('First name cannot be empty');
    }
    
    if (data.lastName !== undefined && !data.lastName?.trim()) {
      errors.push('Last name cannot be empty');
    }
    
    if (data.email !== undefined) {
      if (!data.email?.trim()) {
        errors.push('Email cannot be empty');
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
        errors.push('Invalid email format');
      }
    }
    
    if (data.role !== undefined && !['admin', 'user'].includes(data.role)) {
      errors.push('Valid role is required (admin or user)');
    }
    
    if (data.type !== undefined && !['agency', 'account'].includes(data.type)) {
      errors.push('Valid type is required (agency or account)');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
