<template>
  <div class="hl_settings--body">
    <div class="container-fluid">
      <div class="hl_controls">
        <div class="hl_controls--left">
          <h2>Eliza Service</h2>
        </div>
      </div>
      <div class="card">
        <div class="hl_controls" style="margin: 10px 35px">
          <div class="hl_controls--left">
            <b style="font-size: 17px">Eliza Bypass Tag (Optional)</b>
          </div>
          <div
            class="hl_controls--center"
            style="display: flex; flex-direction: row"
          >
            <div v-if="tags.length">
              <select
                class="selectpicker tag-picker --blue"
                title="Select Eliza Bypass Tag"
                data-width="fit"
                name="existingTags"
                data-size="10"
                v-model="location.ignoreElizaTag"
                @change="saveLocation"
              >
                <option value>Select Eliza Bypass Tag</option>
                <option
                  v-for="tag in tags.filter(
                    tag => tag.name != location.sendElizaTag
                  )"
                  :key="tag.id"
                  :value="tag.name"
                >
                  {{ tag.name }}
                </option>
              </select>
            </div>

            <!-- <div class="dropdown">
            <button
              class="btn btn-blue dropdown-toggle"
              type="button"
              style="margin-right: 10px"
              data-toggle="dropdown"
              aria-haspopup="true"
              aria-expanded="false"
            >{{ defaultElizaName }}</button>
            <div class="dropdown-menu">
              <a
                class="dropdown-item"
                v-for="tab in tabs"
                :key="tab.id"
                @click="saveDefaultEliza(tab)"
              >{{ tab.logicalName }}</a>
            </div>
          </div>-->
            <!-- <button
            type="button"
            :class="{ disabledBtn: !isBotService }"
            class="btn btn-blue mr-2"
            @click.prevent="showaddToBotModal"
            style="height: 43px; width: 130px; padding: 0"
            v-if="showAddEliza"
          >
            <i class="icon icon-plus"></i> Add Eliza
          </button> -->
            <div style="margin: 10px 85px 0px 40px">
              <div style="border-left: 2px solid gray; padding-left: 10px">
                <p>
                  When this tag is present on the contact, then those messages
                  will NOT get delivered to Eliza
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="hl_controls" style="margin: 10px 35px">
          <div class="hl_controls--left">
            <b style="font-size: 17px">Send to Eliza Tag (Optional) </b>
          </div>
          <div
            class="hl_controls--center"
            style="display: flex; flex-direction: row"
          >
            <div v-if="tags.length">
              <select
                class="selectpicker tag-picker --blue"
                title="Select Send to Eliza Tag"
                data-width="fit"
                name="existingTags"
                data-size="10"
                v-model="location.sendElizaTag"
                @change="saveSendElizaTag"
              >
                <option value>Select Send to Eliza Tag</option>
                <option
                  v-for="tag in tags.filter(
                    tag => tag.name != location.ignoreElizaTag
                  )"
                  :key="tag.id"
                  :value="tag.name"
                >
                  {{ tag.name }}
                </option>
              </select>
            </div>

            <!-- <div class="dropdown">
            <button
              class="btn btn-blue dropdown-toggle"
              type="button"
              style="margin-right: 10px"
              data-toggle="dropdown"
              aria-haspopup="true"
              aria-expanded="false"
            >{{ defaultElizaName }}</button>
            <div class="dropdown-menu">
              <a
                class="dropdown-item"
                v-for="tab in tabs"
                :key="tab.id"
                @click="saveDefaultEliza(tab)"
              >{{ tab.logicalName }}</a>
            </div>
          </div>-->
            <!-- <button
            type="button"
            :class="{ disabledBtn: !isBotService }"
            class="btn btn-blue mr-2"
            @click.prevent="showaddToBotModal"
            style="height: 43px; width: 130px; padding: 0"
            v-if="showAddEliza"
          >
            <i class="icon icon-plus"></i> Add Eliza
          </button> -->
            <div style="margin: 0px 35px">
              <div style="border-left: 2px solid gray; padding-left: 10px">
                <p>
                  By default all the conversations will be delivered to Eliza.
                  If this tag is configured, <br />then the new conversations
                  will be sent to Eliza, only when this tag is present on a
                  contact.<br />
                  If both Eliza bypass tag and Send to Eliza tag are configured,
                  then only Send to Eliza will be honored
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <br />
      <div
        v-if="location && !isBotService"
        id="info"
        class="form-group w-100 text-center"
      >
        Your user permissions is not enabled for this page.
        <br />Please talk to your administrator
      </div>
      <div class="card">
        <div class="hl_controls--left w-100">
          <div
            class="d-flex justify-content-between header-underline align-items-end"
          >
            <ul class="nav nav-tabs header-ul">
              <li
                class="nav-item"
                :class="{ active: selectedTab === logicalEliza.id }"
                v-for="logicalEliza in tabs"
                v-bind:key="logicalEliza.id"
              >
                <a
                  class="nav-link pb-3"
                  @click.prevent="selectedTab = logicalEliza.id"
                >
                  <span>{{ logicalEliza.logicalName }}</span>
                </a>
              </li>
            </ul>
            <div class="float-right pb-2 d-inline-block">
              <div class="d-flex justify-content-end align-items-end">
                <div style="position: relative">
                  <UIButton
                    :class="{ disabledBtn: !isBotService }"
                    type="button"
                    class="mr-2"
                    @click="saveBot"
                    :loading="saving"
                  >
                    <i class="icon icon-ok mr-2"></i> Save
                  </UIButton>
                </div>
                <UIButton
                  type="button"
                  :class="{ disabledBtn: !isBotService }"
                  @click.prevent="addnewFAQ"
                >
                  <i class="icon icon-plus mr-2"></i> Add FAQ
                </UIButton>
              </div>
            </div>
          </div>
        </div>
        <div v-if="selectedTab">
          <div class="card hl_settings--team-management">
            <div class="card-body --no-padding" style="max-width: 100%">
              <div class="table-wrap">
                <table class="table table-sort">
                  <thead>
                    <tr>
                      <th data-sort="string" style="width: 50%">Questions</th>
                      <th data-sort="string" style="width: 50%">Answers</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="(faq, index) in faqData"
                      v-bind:index="index"
                      :key="index"
                    >
                      <td>
                        <div v-if="faq.default">{{ faq.question }}</div>
                        <div v-else>
                          <UITextInputGroup
                            type="text"
                            v-model="faq.question"
                            name="msgsndr2"
                            data-lpignore="true"
                          />
                        </div>
                      </td>
                      <td>
                        <div
                          class="d-flex justify-content-center align-items-center"
                        >
                          <UITextAreaGroup
                            v-model="faq.answer"
                            name="msgsndr2"
                            class="w-full"
                            :class="{ disabledBtn: !isBotService }"
                            :readonly="!isBotService"
                          ></UITextAreaGroup>
                          <i
                            style="cursor: pointer; padding: 10px"
                            @click="addAnswerModal(index, faq)"
                            :class="{ disabledBtn: !isBotService }"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="30.023"
                              height="23.81"
                              viewBox="0 0 30.023 23.81"
                            >
                              <g
                                id="Group_8"
                                data-name="Group 8"
                                transform="translate(-1769.376 -443.995)"
                              >
                                <g
                                  id="Icon_feather-mail"
                                  data-name="Icon feather-mail"
                                  transform="translate(1779 448)"
                                >
                                  <path
                                    id="Path_1"
                                    data-name="Path 1"
                                    d="M4.6,6h12.8a1.605,1.605,0,0,1,1.6,1.6v9.6a1.605,1.605,0,0,1-1.6,1.6H4.6A1.605,1.605,0,0,1,3,17.2V7.6A1.605,1.605,0,0,1,4.6,6Z"
                                    fill="none"
                                    stroke="#00d47b"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                  />
                                  <path
                                    id="Path_2"
                                    data-name="Path 2"
                                    d="M19.006,9l-8,5.6L3,9"
                                    transform="translate(0 -1.399)"
                                    fill="none"
                                    stroke="#00d47b"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                  />
                                </g>
                                <path
                                  id="Path_6"
                                  data-name="Path 6"
                                  d="M3771.973,2068.706v-5.387h-16.4v13.656h8.24"
                                  transform="translate(-1985.194 -1618.324)"
                                  fill="none"
                                  stroke="#00d47b"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="2"
                                />
                                <line
                                  id="Line_4"
                                  data-name="Line 4"
                                  x2="5"
                                  transform="translate(1778.5 449.5)"
                                  fill="none"
                                  stroke="#00d47b"
                                  stroke-linecap="round"
                                  stroke-width="2"
                                />
                                <line
                                  id="Line_5"
                                  data-name="Line 5"
                                  transform="translate(1774.5 449.5)"
                                  fill="none"
                                  stroke="#00d47b"
                                  stroke-linecap="round"
                                  stroke-width="2"
                                />
                              </g>
                            </svg>
                          </i>
                          <i
                            :class="{ disabledBtn: !isBotService }"
                            style="cursor: pointer; padding: 10px"
                            @click="deleteFAQ(index)"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="25.795"
                              height="24.639"
                              viewBox="0 0 25.795 24.639"
                            >
                              <g
                                id="Group_7"
                                data-name="Group 7"
                                transform="translate(-1816.376 -443.995)"
                              >
                                <path
                                  id="Path_9"
                                  data-name="Path 9"
                                  d="M3771.973,2068.706v-5.387h-16.4v13.656h8.24"
                                  transform="translate(-1938.194 -1618.324)"
                                  fill="none"
                                  stroke="#e93d3d"
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="2"
                                />
                                <line
                                  id="Line_8"
                                  data-name="Line 8"
                                  x2="5"
                                  transform="translate(1825.5 449.5)"
                                  fill="none"
                                  stroke="#e93d3d"
                                  stroke-linecap="round"
                                  stroke-width="2"
                                />
                                <line
                                  id="Line_9"
                                  data-name="Line 9"
                                  transform="translate(1821.5 449.5)"
                                  fill="none"
                                  stroke="#e93d3d"
                                  stroke-linecap="round"
                                  stroke-width="2"
                                />
                                <g
                                  id="Group_4"
                                  data-name="Group 4"
                                  transform="translate(1825 451)"
                                >
                                  <path
                                    id="Path_10"
                                    data-name="Path 10"
                                    d="M3,6H16.171"
                                    transform="translate(0 -1.073)"
                                    fill="none"
                                    stroke="#e93d3d"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                  />
                                  <path
                                    id="Path_11"
                                    data-name="Path 11"
                                    d="M15.244,4.927V15.171a1.463,1.463,0,0,1-1.463,1.463H6.463A1.463,1.463,0,0,1,5,15.171V4.927m2.2,0V3.463A1.463,1.463,0,0,1,8.659,2h2.927a1.463,1.463,0,0,1,1.463,1.463V4.927"
                                    transform="translate(-0.537)"
                                    fill="none"
                                    stroke="#e93d3d"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                  />
                                  <line
                                    id="Line_10"
                                    data-name="Line 10"
                                    y2="4"
                                    transform="translate(8 9)"
                                    fill="none"
                                    stroke="#e93d3d"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                  />
                                  <line
                                    id="Line_11"
                                    data-name="Line 11"
                                    y2="4"
                                    transform="translate(11 9)"
                                    fill="none"
                                    stroke="#e93d3d"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                  />
                                </g>
                              </g>
                            </svg>
                          </i>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <AddFAQSMSModal
      :values="editSMSEvent"
      @save="saveAnswer"
      @hidden="editSMSEvent = { visible: false }"
    />

    <AddElizaModal
      :values="addElizaModal"
      @hidden="addElizaModal = { visible: false }"
      @saved="selectedTab = $event"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { orderBy, find, isEmpty } from 'lodash'
import { mapState } from 'vuex'
import { LogicalEliza, Location, User, Tag } from '@/models'
import { UserState } from '../../../store/state_models'
const AddElizaModal = () =>
  import('@/pmd/components/customer/AddElizaModal.vue')
const AddFAQSMSModal = () =>
  import('@/pmd/components/workflow/modals/AddFAQSMSModal.vue')

let logicalElizaSubscription: () => void
import firebase from 'firebase/app'
declare var $: any

export default Vue.extend({
  components: { AddElizaModal, AddFAQSMSModal },
  data() {
    return {
      currentLocationId: '',
      faqData: [] as { [key: string]: any }[],
      elizaData: undefined as LogicalEliza | undefined,
      saving: false,
      addElizaModal: { visible: false } as { [key: string]: any },
      tabs: undefined as LogicalEliza | undefined,
      selectedTab: '',
      showAddEliza: false,
      location: undefined as Location | undefined,
      editSMSEvent: {
        visible: false,
        currentLocationId: '',
        index: -1,
        faq: {},
      },
      defaultElizaName: 'Select Default Eliza',
      tags: [] as Tag[],
    }
  },
  watch: {
    selectedTab: function () {
      this.fetchFAQs()
    },
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.fetchData()
    },
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.location = await Location.getById(this.currentLocationId)

    if (this.location) this.showAddEliza = this.location.showAddEliza
    this.fetchData()
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    isBotService() {
      if (
        this.location &&
        this.location.botService &&
        this.user &&
        this.user.permissions.bot_service
      ) {
        return true
      } else {
        return false
      }
    },
  },
  methods: {
    async fetchData() {
      this.tags = await Tag.getByLocationIdFirestore(this.currentLocationId)
      logicalElizaSubscription = (
        await LogicalEliza.getByLocationId(this.currentLocationId)
      ).onSnapshot(snapshot => {
        this.tabs = orderBy(
          snapshot.docs.map(d => new LogicalEliza(d)),
          'dateAdded'
        )
        if (this.tabs && this.tabs.length && !this.selectedTab)
          this.selectedTab = this.tabs[0].id
        if (this.location && this.location.defaultLogicalEliza) {
          const defaultLogicalEliza = find(this.tabs, {
            id: this.location.defaultLogicalEliza,
          })
          if (defaultLogicalEliza)
            this.defaultElizaName = defaultLogicalEliza.logicalName
        }
      })
    },
    async fetchFAQs() {
      if (this.selectedTab) {
        this.elizaData = find(this.tabs, { id: this.selectedTab })
        if (isEmpty(this.elizaData))
          this.elizaData = await LogicalEliza.getById(this.selectedTab)
        if (this.elizaData) this.faqData = this.elizaData.faq
      }
    },
    async saveBot() {
      if (this.selectedTab) {
        this.saving = true
        if (this.elizaData) {
          this.faqData = this.faqData.filter(faq => faq.question)
          this.elizaData.faq = this.faqData
          await this.elizaData.ref.update({
            faq: this.faqData,
            date_updated: firebase.firestore.FieldValue.serverTimestamp(),
          })
        }
        this.saving = false
      }
    },
    addnewFAQ() {
      this.faqData.unshift({ question: '', answer: '' })
    },
    async deleteFAQ(index: number) {
      this.$uxMessage(
        'confirmation',
        `Are you sure you want to delete these FAQ?`,
        async res => {
          if (res === 'ok') {
            this.faqData.splice(index, 1)
            await this.elizaData.ref.update({
              faq: this.faqData,
              date_updated: firebase.firestore.FieldValue.serverTimestamp(),
            })
          }
        }
      )
    },
    addAnswerModal(index: number, faq: any) {
      this.editSMSEvent = {
        visible: true,
        currentLocationId: this.currentLocationId,
        index,
        faq,
      }
    },
    async saveAnswer(body: string) {
      this.faqData[this.editSMSEvent.index].answer = body
      await this.elizaData.ref.update({
        faq: this.faqData,
        date_updated: firebase.firestore.FieldValue.serverTimestamp(),
      })
    },
    showaddToBotModal() {
      this.addElizaModal = {
        visible: true,
      }
    },
    async saveDefaultEliza(logicalEliza: LogicalEliza) {
      this.location.defaultLogicalEliza = logicalEliza.id
      this.defaultElizaName = logicalEliza.logicalName
      await this.location.ref.update({
        default_logical_eliza: logicalEliza.id,
        date_updated: firebase.firestore.FieldValue.serverTimestamp(),
      })
    },
    async saveLocation() {
      await this.location.ref.update({
        ignore_eliza_tag: this.location.ignoreElizaTag,
        date_updated: firebase.firestore.FieldValue.serverTimestamp(),
      })
    },
    async saveSendElizaTag() {
      await this.location.ref.update({
        send_eliza_tag: this.location.sendElizaTag,
        date_updated: firebase.firestore.FieldValue.serverTimestamp(),
      })
    },
  },
  beforeDestroy() {
    if (logicalElizaSubscription) logicalElizaSubscription()
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
})
</script>

<style scoped>
.header-underline {
  display: inline-block;
  border-bottom: 1px solid #d5dde1;
  list-style: none;
  width: 100% !important;
  margin-bottom: 20px;
  padding: 15px 15px 0px;
}
ul.header-ul li.active a {
  color: #2a3135;
  border-color: #188bf6;
}
.disabledBtn {
  opacity: 0.5;
  pointer-events: none;
}
.annual-offer {
  margin-left: 5px;
}
</style>
