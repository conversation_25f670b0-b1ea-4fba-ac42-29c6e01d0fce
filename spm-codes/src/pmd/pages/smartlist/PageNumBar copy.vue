<template>
  <div class="row" style="background:#E8F3FE;height:40px">
    <!-- <div class="col-12" v-show="disabled" >
          <div class="workinprogress" style="height:25px !important;"></div>
    </div>-->
    <div class="col-12" v-show="disabled">
      <div style="height:25px !important;"></div>
      <!-- class="workinprogress"  -->
    </div>
    <div
      class="col-12"
      v-show="!disabled"
      style="position:static !important; overflow:hidden !important"
    >
      <div
        class="float-right"
        style="position:static !important; overflow:hidden !important; display:inline-block "
      >
        <div class="barsection mx-4">
          <span v-if="total > 0">{{selected}} of {{total}} Pages</span>
          <span v-else>No Pages</span>
          <!-- <template  v-if="total> 1 && allowCustomInput">
              <input type="number" v-model="inputNum" placeholder="go to page" class="numInput mx-2">
          </template>-->
        </div>
        <div class="pagination">
          <template v-if="total > 1 &&  !allowCustomInput">
            <div class="d-inline-flex">
              <a
                href="javascript:void(0);"
                v-if="selected > 1"
                @click.stop.prevent="first"
              >Go To First</a>
              <a
                href="javascript:void(0)"
                v-if="selected > 1"
                @click.stop.prevent="jumpTo(selected - 1)"
              >❮ &nbsp;</a>
              <template v-for="(n,i) in 5 " v-if="(selected + i) <= (total)">
                <a
                  href="javascript:void(0);"
                  class="active"
                  v-if="selected === (selected + i)"
                  @click.stop.prevent="jumpTo(selected + i)"
                >{{selected + i}}</a>
                <a
                  href="javascript:void(0);"
                  v-else
                  @click.stop.prevent="jumpTo(selected + i)"
                >{{selected + i}}</a>
              </template>
              <a
                href="javascript:void(0)"
                v-if="total > (selected + 5)"
                @click.stop.prevent="jumpTo(selected + 1)"
              >&nbsp; ❯</a>
              <a
                href="javascript:void(0);"
                v-if="total > (selected + 4)"
                @click.stop.prevent="last"
              >Go To Last</a>
              <input
                type="number"
                v-model="inputNum"
                placeholder="go to page"
                class="numInput mx-2"
              />
              <!-- <a href="javascript:void(0);"  style="min-width:50px;"
                      v-if="total > 5"
                      @click.stop.prevent="customInput">Go To Page
              </a>-->
            </div>
          </template>
        </div>
        <!-- <a href="javascript:void(0);" @click.stop.prevent="jumpTo(selected || 1)">
                <i class="fa fa-undo mx-2 --blue"></i>
        </a>-->

        <div class="barsection" v-if="total > 0">
          <div class="dropdown mt-0 mb-0 mx-3" style="position:static !important;">
            <a
              id="dropdownMenuButton"
              @click.prevent="pageSizeToggler.toggle()"
              data-toggle="dropdown"
              aria-haspopup="true"
              aria-expanded="false"
              class="align-middle"
            >
              Page Size: {{pageSize}}
              <i class="fas fa-caret-down --light mx-2"></i>
            </a>
            <div
              aria-labelledby="dropdownMenuButton"
              :id="pageSizeToggler.id"
              style="max-height: 200px; overflow: auto;"
              class="dropdown-menu"
            >
              <div class="dropdown-item" @click.prevent="setPageSize(20)">
                <span class="text align-right">20</span>
              </div>
              <div class="dropdown-item" @click.prevent="setPageSize(50)">
                <span class="text align-right">50</span>
              </div>
              <div class="dropdown-item" @click.prevent="setPageSize(100)">
                <span class="text align-right">100</span>
              </div>

              <!-- class="dropdown-item my-1" v-for="(s,index) in smartLists"
                      v-if="index > 4"
              "-->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { UxMessage } from '@/util/ux_message'
import { SafeDropDownToggle } from './safe_drop_down_toggle'

export default Vue.extend({
  name: 'PageNumBar',
  props: ['totalPages', 'currentPage', 'disabled', 'pageSize'],
  inject: ['uxmessage'],
  data() {
    return {
      // selected: Number(this.currentPage),
      // total: Number(this.totalPages),
      allowCustomInput: false,
      inputNum: undefined,
      waitTime: 1000,
      debouncer: undefined as NodeJS.Timer | undefined,
      pageSizeToggler: new SafeDropDownToggle(this, 'pageSizeToggler')
    }
  },
  watch: {
    inputNum(n, o) {
      if (!n) return
      if (this.debouncer) clearTimeout(this.debouncer)
      if (n <= 0 || n > this.total) {
        this.uxmessage(UxMessage.errorType('Select a valid page number'))
        return
      }
      this.debouncer = setTimeout(() => this.jumpTo(n), this.waitTime)
    }
  },
  computed: {
    selected() {
      return Number(this.currentPage)
    },
    total() {
      return Number(this.totalPages)
    }
  },
  methods: {
    customInput() {
      this.allowCustomInput = !this.allowCustomInput
    },
    first() {
      this.jumpTo(1)
    },
    last() {
      this.jumpTo(this.total)
    },
    jumpTo(num: number) {
      this.inputNum = undefined
      this.$emit('goToPage', num)
      //this.allowCustomInput = false;
    },
    setPageSize(num: number) {
      this.$emit('pageSizeChange', num)
    }
  }
})
</script>

<style  scoped>
.light-font {
  color: #607179 !important;
}
.buttonWidth {
  max-width: 40px !important;
  min-width: 40px !important;
}
.btn-link:focus,
.btn-link:hover {
  color: #23527c !important;
  text-decoration: underline !important;
  background-color: transparent !important;
}
.numInput {
  max-width: 90px !important;
  max-height: 20px !important;
  min-height: 20px !important;
  font-size: small !important;
  -moz-appearance: textfield;
  padding-bottom: 2px;
}

.numInput::-webkit-inner-spin-button,
.numInput::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}

.numInput::-webkit-inner-spin-button,
.numInput::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}

.barsection {
  display:inline-block;
  height: 100%;
  padding-top: 8px;
}

.pagination {
  display:inline-block;
  word-spacing: -1;
  height: 100%;
}

.pagination a {
  color: #607179;
  float: left;
  text-decoration: none;
  padding: 8px 10px;
  padding-top:6px;
}

.pagination input {
  margin-top: 8px;
}

.pagination a.active {
  background-color: #188bf6; /*#4CAF50;*/
  color: white;
  cursor: unset !important;
}

.pagination a:hover:not(.active) {
  background-color: #ddd;
}
</style>
