/* eslint-disable prettier/prettier */ /* eslint-disable prettier/prettier */
<template>
  <aside class="hl_form-builder--sidebar">
    <ul class="nav nav-tabs top-nav-wrapper-customfield" role="tablist">
      <li class="nav-item">
        <a
          class="nav-link active cmp-form-builder-sidebar__tab--fields"
          id="fields-tab"
          data-toggle="tab"
          href="#fields"
          role="tab"
          aria-controls="fields"
          aria-selected="true"
          >Fields</a
        >
      </li>
      <li class="nav-item">
        <a
          class="nav-link cmp-form-builder-sidebar__tab--styles"
          id="styles-tab"
          data-toggle="tab"
          href="#styles"
          role="tab"
          aria-controls="styles"
          aria-selected="false"
          >Styles</a
        >
      </li>
      <li class="nav-item">
        <a
          class="nav-link cmp-form-builder-sidebar__tab--options"
          id="options-tab"
          data-toggle="tab"
          href="#options"
          role="tab"
          aria-controls="options"
          aria-selected="false"
          >Options</a
        >
      </li>
    </ul>

    <div class="tab-content">
      <div
        class="tab-pane active"
        id="fields"
        role="tabpanel"
        aria-labelledby="fields-tab"
      >
        <div class="dragdrop-wrap">
          <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item">
              <a
                class="nav-link active cmp-form-builder-sidebar__tab--standard"
                id="standard-tab"
                data-toggle="tab"
                href="#standard"
                role="tab"
                aria-controls="standard"
                aria-selected="false"
                >Standard</a
              >
            </li>
            <li class="nav-item">
              <a
                class="nav-link cmp-form-builder-sidebar__tab--custom-fields"
                id="myfields-tab"
                data-toggle="tab"
                href="#custom-fields"
                role="tab"
                aria-controls="custom-fields"
                aria-selected="true"
                >Custom Fields</a
              >
            </li>
          </ul>
          <div class="tab-content" id="myTabContent">
            <div
              class="tab-pane fade show active margin-top-25"
              id="standard"
              role="tabpanel"
              aria-labelledby="standard-tab"
            >
              <ul class="dragArea dragdrop-items">
                <li
                  v-for="(item, index) in sepearatedStandardFields.inputFields"
                  :key="index"
                  class="menu-item"
                >
                  <Drag
                    class="drag"
                    :transfer-data="{ item }"
                    @dragstart="$emit('startDraggingItem')"
                    @dragend="$emit('dragEndItem')"
                  >
                    <span>{{ item.label }}</span>
                    <i class="icon icon-resize-plus-2"></i>
                  </Drag>
                </li>
              </ul>

              <hr class="border" />

              <ul class="dragArea dragdrop-items">
                <li
                  v-for="(item, index) in sepearatedStandardFields.elements"
                  :key="index"
                  class="menu-item"
                >
                  <Drag
                    class="drag"
                    :transfer-data="{ item }"
                    @dragstart="$emit('startDraggingItem')"
                    @dragend="$emit('dragEndItem')"
                  >
                    <span>{{ item.label }}</span>
                    <i class="icon icon-resize-plus-2"></i>
                  </Drag>
                </li>
              </ul>
            </div>
            <div
              class="tab-pane fade margin-top-25"
              id="custom-fields"
              role="tabpanel"
              aria-labelledby="custom-fields-tab"
            >
              <div class="glh-search">
                <input
                  type="text"
                  class="form-control cmp-form-builder-sidebar__txt--search-customfield"
                  placeholder="Search by name or field type"
                  @input="searchCustomFields"
                />
                <i class="fa fa-search"></i>
              </div>
              <ul class="dragArea dragdrop-items">
                <li
                  v-for="(item, index) in filteredCustomFieldSorted"
                  :key="index"
                  class="menu-item"
                >
                  <Drag
                    class="drag"
                    :transfer-data="{ item }"
                    @dragstart="$emit('startDraggingItem')"
                    @dragend="$emit('dragEndItem')"
                  >
                    <span>{{ item.name }}</span>
                    <i class="icon icon-resize-plus-2"></i>
                  </Drag>
                </li>
              </ul>
              <div class="custom-field-wrap-btn">
                <button
                  type="button"
                  class="btn btn-warning btn-block"
                  @click="$emit('showAddCustomFieldModal')"
                >
                  Add Custom Field
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div
        class="tab-pane"
        id="styles"
        role="tabpanel"
        aria-labelledby="styles-tab"
      >
        <FormbuilderSidebarStyle
          :styleFormStyle="formStyle"
          :optFormAction="formAction"
        />
      </div>

      <div
        class="tab-pane"
        id="options"
        role="tabpanel"
        aria-labelledby="options-tab"
      >
        <FormbuilderAdvanceOptions
          :optFormAction="formAction"
          :formStyle="formStyle"
        />
      </div>
    </div>

    <!--Integrate Button-->
    <div class="custom-field py-1" v-if="formAction.source != 'survey'">
      <button
        type="button"
        class="btn btn-success btn-block cmp-form-builder-sidebar__btn--integrate"
        @click="$emit('showIntegratePage')"
      >
        Integrate Form
      </button>

      <button
        type="button"
        class="btn btn-primary btn-block cmp-form-builder-sidebar__btn--save"
        @click="$emit('saveForm')"
        :disabled="disableFormSaveButton"
      >
        <span v-if="disableFormSaveButton"> Form Saved </span>

        <span v-else> Save Form </span>
      </button>
    </div>
    <div class="custom-field" v-else="formAction.source == 'survey'">
      <UIButton
        type="button"
        use="primary"
        class="justify-center w-full cmp-form-builder-sidebar__btn--integrate-survey"
        @click="$emit('showIntegratePageSurvey')"
      >
        Integrate Survey
      </UIButton>
      <UIButton
        class="justify-center w-full cmp-form-builder-sidebar__btn--save-survey mt-2 bg-curious-blue-500 text-white hover:text-curious-blue-500"
        use="secondary"
        type="button"
        @click="$emit('saveSurvey')"
        :disabled="disableSurveySaveButton"
      >
        <span v-if="disableSurveySaveButton"> Survey Saved </span>

        <span v-else> Save Survey </span>
      </UIButton>
    </div>
    <!--End Integrate Button-->

    <!--Overlay Settings-->
    <formFieldsOverlaySettings
      v-if="fieldSettings.enable"
      :fieldInfo="fieldSettings.fieldInfo"
      :formButtonStyle="buttonStyle"
      :source="formAction.source"
      :logicSkipStore="logicSkipStore"
      :currentSlide="currentSlide"
    />

    <formFieldsOverlaySettings
      v-else-if="isSurvey && slideSettings.enable"
      :slideInfo="slideSettings.slideInfo"
      :slidePosition="slideSettings.slidePosition"
      :isSlidePanel="true"
      :allSlides="allSlides"
    />

    <!--Overlay settings end-->
  </aside>
</template>

<script lang="ts">
import Vue from 'vue'
import { Drag, Drop } from 'vue-drag-drop'
const formFieldsOverlaySettings = () =>
  import('../../components/marketing/formFieldsOverlaySettings.vue')
const FormbuilderSidebarStyle = () =>
  import('../../components/marketing/FormbuilderSidebarStyle.vue')
const FormbuilderAdvanceOptions = () =>
  import('../../components/marketing/FormbuilderAdvanceOptions.vue')

export default Vue.extend({
  props: {
    itemDragable: {} as any,
    customFields: {} as any,
    fieldSettings: {} as any,
    slideSettings: {} as any,
    buttonStyle: {} as any,
    formStyle: {} as any,
    formAction: {} as any,
    logicSkipStore: {} as any,
    allSlides: {} as any,
    currentSlide: Number,
    isSurvey: Boolean,
  },
  components: {
    Drag,
    formFieldsOverlaySettings,
    FormbuilderSidebarStyle,
    FormbuilderAdvanceOptions,
  },
  data() {
    return {
      filteredCustomField: [] as any,
      disableFormSaveButton: true,
      disableSurveySaveButton: true,
    }
  },
  computed: {
    filteredCustomFieldSorted() {
      let sorted = this.filteredCustomField.sort((a, b) => {
        return a.data.date_added.seconds - b.data.date_added.seconds
      })

      return sorted
    },
    sepearatedStandardFields() {
      let inputFields = this.itemDragable.filter(({ type }) => {
        return !['html', 'img', 'captcha', 'submit', 'h1'].includes(type)
      })

      let elements = this.itemDragable.filter(({ type }) => {
        return ['html', 'img', 'captcha', 'submit', 'h1'].includes(type)
      })

      return { inputFields, elements }
    },
  },
  watch: {
    customFields(val: { [key: string]: any }) {
      this.filteredCustomField = val
    },
  },
  updated() {},
  mounted() {},
  created() {
    this.$bus.$on('form-has-unsaved-data', doesFormHaveUnsavedData => {
      this.disableFormSaveButton = !doesFormHaveUnsavedData
    })

    this.$bus.$on('survey-has-unsaved-data', doesSurveyHaveUnsavedData => {
      this.disableSurveySaveButton = !doesSurveyHaveUnsavedData
    })
  },
  methods: {
    searchCustomFields({ target }) {
      const inputValue = target.value.toLowerCase()
      this.filteredCustomField = this.customFields.filter(function (
        items: any
      ) {
        if (
          items.dataType.toLowerCase().indexOf(inputValue) > -1 ||
          items.name.toLowerCase().indexOf(inputValue) > -1
        )
          return items
      })
      if (target.value.trim('').length <= 0) {
        this.filteredCustomField = this.customFields
      }
    },
  },
})
</script>
<style>
.glh-search {
  position: relative;
}
.glh-search i {
  position: absolute;
  right: 15px;
  top: 18px;
}
</style>
>
