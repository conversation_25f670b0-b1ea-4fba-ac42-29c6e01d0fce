<template>
  <Filters
    :conditions="conditions"
    :filterMaster="filterMaster"
    v-on:update:conditions="$emit('update:conditions', $event)"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import { User } from '@/models';
import { Condition } from '@/models/trigger';
const Filters = () => import( './Filters.vue');
export default Vue.extend({
  props: ['conditions'],
  components: { Filters },
  data() {
    return {
      filterMaster: [
        {
          placeHolder: 'Select Error Status',
          title: 'Error is',
          value: 'contact.phoneInfo',
          valueType: 'text',
          type: 'select',
          operator: 'contains-any',
          options: [
            { title: 'SMS Incapable', value: 'sms_incapable' },
            { title: 'Not Valid', value: 'not_valid' }
          ],
          allowMultiple: true
        }
      ]
    }
  }
})
</script>