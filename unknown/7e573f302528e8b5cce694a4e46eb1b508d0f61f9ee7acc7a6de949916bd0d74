<template>
  <Filters
    :conditions="conditions"
    :filterMaster="filterMaster"
    v-on:update:conditions="$emit('update:conditions', $event)"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import { Campaign, TwilioAccount, NumberPool } from '@/models'
import { Condition } from '@/models/trigger'
const Filters = () => import('./Filters.vue')
export default Vue.extend({
  props: ['conditions'],
  components: { Filters },
  data() {
    return {
      filterMaster: [
        {
          placeHolder: 'Select Call Status',
          title: 'Call Status',
          value: 'call_status',
          valueType: 'text',
          type: 'select',
          operator: 'contains-any',
          options: [
            { title: 'canceled', value: 'canceled' },
            { title: 'busy', value: 'busy' },
            { title: 'voicemail', value: 'voicemail' },
            { title: 'no-answer', value: 'no-answer' },
            { title: 'completed', value: 'completed' }
          ],
          allowMultiple: true
        },
        {
          placeHolder: 'Select campaign',
          title: 'In campaign',
          value: 'campaign.id',
          valueType: 'text',
          type: 'select',
          options: [] as { [key: string]: any }[]
        },
        {
          placeHolder: 'Select direction',
          title: 'Call Direction',
          value: 'message.direction',
          valueType: 'text',
          type: 'select',
          options: [
            { title: 'Incoming', value: 'inbound' },
            { title: 'Outgoing', value: 'outbound' }
          ],
        }
      ],
      currentLocationId: ''
    }
  },
  watch: {
    conditions: {
      handler: async function(conditions) {
        await this.conditionsChanged(conditions)
      },
      deep: true
    },
    phoneNumbers: function() {
      this.phoneNumbersUpdated()
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id;
    const campaignIdOption = lodash.find(this.filterMaster, { value: 'campaign.id' });
    if (campaignIdOption) {
      const campaigns = await Campaign.getByLocationId(this.currentLocationId);
      campaignIdOption.options = campaigns.map(campaign => {
        return {
          title: campaign.name,
          value: campaign.id
        }
      })
    }
    await this.conditionsChanged(this.conditions);
  },
  computed: {
    phoneNumbers() {
      return this.$store.state.numbers.numbers;
    }
  },
  methods: {
    phoneNumbersUpdated() {
      const twilioPhoneOptions = lodash.find(this.filterMaster, { value: 'inbound_number' });
      if (twilioPhoneOptions) {
        twilioPhoneOptions.options = this.phoneNumbers.map(phoneNumber => {
          return {
            title: phoneNumber.friendly_name,
            value: phoneNumber.phone_number
          }
        });
      }
    },
    async conditionsChanged(condition: any) {
      const twilioPhoneOptions = lodash.find(this.filterMaster, { value: 'inbound_number' });
      const numberPoolOptions = lodash.find(this.filterMaster, { value: 'number_pool' });
      const direction = lodash.find(condition, {
        field: 'message.direction'
      })
      if (
        direction &&
        direction.value &&
        direction.value === 'inbound'
      ) {
        if (!twilioPhoneOptions) {
          var inboundNumber = {
            placeHolder: 'Select phone',
            title: 'In Phone Number',
            operator: 'contains-any',
            value: 'inbound_number',
            valueType: 'text',
            type: 'select',
            allowMultiple: true,
            options: [] as { [key: string]: any }[]
          }
          inboundNumber.options = this.phoneNumbers && this.phoneNumbers.map(phoneNumber => {
            return {
              title: phoneNumber.friendly_name,
              value: phoneNumber.phone_number
            }
          })
          this.filterMaster.push(inboundNumber);
        }
        if (!numberPoolOptions) {
          var numberPool =
          {
            placeHolder: 'Select number pool',
            title: 'In Number Pool',
            value: 'number_pool',
            valueType: 'text',
            type: 'select',
            options: [] as { [key: string]: any }[]
          }
          const pools = await NumberPool.getByLocationId(this.currentLocationId);
          numberPool.options = pools.map(pool => {
            return {
              title: pool.name,
              value: pool.id
            }
          });
          this.filterMaster.push(numberPool);
        }
      } else {
        let refreshedConditions = lodash.cloneDeep(this.conditions);
        if (twilioPhoneOptions) {
          if (lodash.findIndex(refreshedConditions, { field: 'inbound_number' }) !== -1) {
            refreshedConditions.splice(lodash.findIndex(refreshedConditions, { field: 'inbound_number' }), 1);
            this.$emit('update:conditions', refreshedConditions)
          }
          this.filterMaster.splice(lodash.indexOf(twilioPhoneOptions, 1));
        }
        if (numberPoolOptions) {
          if (lodash.findIndex(refreshedConditions, { field: 'number_pool' }) !== -1) {
            refreshedConditions.splice(lodash.findIndex(refreshedConditions, { field: 'number_pool' }), 1);
            this.$emit('update:conditions', refreshedConditions)
          }
          this.filterMaster.splice(lodash.indexOf(numberPoolOptions, 1));
        }
      }
    }
  }
})
</script>
