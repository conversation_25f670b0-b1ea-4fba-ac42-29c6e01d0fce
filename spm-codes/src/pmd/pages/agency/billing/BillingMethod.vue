<template>
    <div class="hl_billing--method">
        <div class="card">
            <div class="card-header" v-if="showHeading !== false">
            <h3>Payment Method</h3>
            </div>
            <div class="card-body">
              <div class="transaction-status__warning" v-if="showScaWarning">
                We have moved to a more secure billing system.
                <br/>
                For added security you need to authenticate your card details again.
              </div>
                <div class="billing-card">
                    <img class="billing-card-img" src="../../../../assets/pmd/img/billing/billing-card-empty.png" />
                    <img class="billing-card-type" src="../../../../assets/pmd/img/billing/hl_ae.png" v-if="company.card_brand === 'American Express'"/>
                    <img class="billing-card-type" src="../../../../assets/pmd/img/billing/hl_visa.png"  v-else-if="company.card_brand === 'Visa'"/>
                    <img class="billing-card-type" src="../../../../assets/pmd/img/billing/hl_mastercard.png" v-else/>
                    <div class="card-last-digits">{{company.card_last_4}}</div>
                </div>
                <div class="new-card-label" v-if="showHeading !== false">{{`Add new card`}}</div>
                <div class="form-control" id="card-element">
                    <!-- A Stripe Element will be inserted here. -->
                </div>
                <div id="card-errors" class="help is-danger" role="alert"></div>
            </div>
            <div class="card-footer">
                <div>
                  <div class="card-verify--error" v-if="verificatonError">{{verificatonError}}</div>
                  <div class="card-verify--success" v-if="cardAdded"> <i class="far fa-check-circle"></i> Your payment method has been updated</div>
                </div>
                <div>
                  <UIButton type="button" class="mr-2" use="outline" v-if="!loading && showCancel" @click.stop="cancel">Cancel</UIButton>
                  <UIButton type="button" :loading="loading" @click.stop="tokenize">{{saveButtonText || 'Save'}}</UIButton>
                </div>
            </div>
        </div>
        </div>
</template>
<script lang='ts'>
import { Company } from '@/models';

import config from "../../../../config";

declare var Stripe: any;
import Vue from 'vue';

export default Vue.extend({
    props: ['showHeading','showCancel', 'showScaWarning', 'saveButtonText'],
    data() {
		return {
			company: Company,
			stripe: undefined as any,
			loading: false,
      card: undefined as any,
      verificatonError: '',
      cardAdded: false,
		}
	},
    created() {
        this.fetchCompanyDetails();
    },
    mounted(){
        this.initStripeBar();
    },
    methods:{
        async fetchCompanyDetails(){
          let company = await this.$store.dispatch('company/get');
          this.company = company.data;
        },
        async initStripeBar(){
            var _self = this;

            let recaptchaScript = document.createElement('script')
            recaptchaScript.onload = () => {
                _self.stripe = Stripe(config.stripeKey);
                // Create an instance of Elements.
                var elements = _self.stripe.elements();

                // Custom styling can be passed to options when creating an Element.
                // (Note that this demo uses a wider set of styles than the guide below.)
                var style = {
                    base: {
                        color: '#2a3135',
                        lineHeight: '21px',
                        fontFamily: 'Roboto,system,-apple-system,BlinkMacSystemFont,".SFNSDisplay-Regular","Helvetica Neue",Helvetica,Arial,sans-serif',
                        fontSmoothing: 'antialiased',
                        fontSize: '14px',
                        '::placeholder': {
                            color: '#aab7c4'
                        }
                    },
                    invalid: {
                        color: '#fa755a',
                        iconColor: '#fa755a'
                    }
                };

                // Create an instance of the card Element.
                this.card = elements.create('card', { style: style });

                // Add an instance of the card Element into the `card-element` <div>.
                this.card.mount('#card-element');

                this.card.addEventListener('change', function (event: any) {
                    var displayError = document.getElementById('card-errors') || document.createElement('div');
                    if (event.error) {
                        displayError.textContent = event.error.message;
                    } else {
                        displayError.textContent = '';
                    }
                });
            }

            recaptchaScript.setAttribute('src', 'https://js.stripe.com/v3/')
            document.head.appendChild(recaptchaScript)
        },

    cancel() {
      this.card.clear();
      this.$emit('cancel');
		},
		async tokenize() {
      this.loading = true;
      this.verificatonError = '';
      this.cardAdded = false;
      const _self = this;

      try{
        const intent = await this.$http.post('/api/stripe/setup_intent',{});
        await this.stripe
          .confirmCardSetup(intent.data.client_secret, {
              payment_method: {
                  card: this.card,
                  // billing_details: {
                  //     name: this.fullName
                  // }
              }
          })
          .then( (result: any) => {
              if (result.error) {
                  // _self.paymentError = result.error.message
                  this.verificatonError = result.error.message
                  this.loading = false
              } else {
                  // Send the token to your server.
                  let paymentMethodId = result.setupIntent.payment_method
                  this.updateCard(paymentMethodId);
              }
          })
        } catch (err) {
          this.loading = false;
          //
        }
      },
      async updateCard(paymentMethodId:string) {
        try {
          let resp = await this.$http.post('/api/stripe/add_card', {
          payment_method_id: paymentMethodId,
          stripe_id: this.company.stripe_id,
          company_id: this.company.id
        })
          this.card.clear()
          this.loading = false
          this.cardAdded = true;
          this.$emit('cardChanged');
          setTimeout(() => {
            this.fetchCompanyDetails()
          }, 500)
          setTimeout(() => {
            this.fetchCompanyDetails()
            this.$emit('cardChangedTimeout');
          }, 2000)
        } catch (err) {
          this.verificatonError = err?.response?.data?.msg || 'Adding new payment method failed';
          this.loading = false
        }
        this.card.clear();
      }
  }
})
</script>
<style scoped>
.billing-card{
    position: relative;
    width: 300px;
    margin: 0px auto 20px;
    user-select: none;
}
.billing-card-img{
    max-width: 300px;
}
.billing-card-type{
    position: absolute;
    bottom: 10px;
    right: 0;
    width: 110px;
    margin: 0px !important;
}
.card-last-digits{
    position: absolute;
    bottom: 14px;
    left: 96px;
    font-size: 24px;
    color: #ffffff;
}
.new-card-label{
    margin-top: 40px;

}
.card-verify--error{
  text-align: center;
  color: #e93d3d;
}
.card-verify--success{
  text-align: center;
  color: #35ca38;
}
.hl_billing--method .card .card-footer{
  justify-content: space-between;
  align-items: center;
}
.transaction-status__warning{
  /* margin: 0px 16px 16px; */
  margin-bottom: 32px;
  border-left: 4px solid #188bf6;
  padding: 12px;
  background-color: #e9f5fb;
  line-height: 18px;
  font-size: 14px;
  text-align: left;
}
</style>
