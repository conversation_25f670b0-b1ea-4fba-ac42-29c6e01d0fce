/**
 * User Types and Interfaces
 * 
 * Complete type definitions for User entities based on SPM User model
 * Includes all properties, permissions, and relationships from the original model
 */

import type { IEntity } from './base';

/**
 * User roles based on SPM User model constants
 */
export type UserRole = 'admin' | 'user';

/**
 * User types based on SPM User model constants
 */
export type UserType = 'agency' | 'account';

/**
 * User source enumeration
 * Based on SPM UserSource enum
 */
export enum UserSource {
  AGENCY_TEAM = 'agency_team',
  LOCATION_TEAM = 'location_team',
  LAUNCH_PAD = 'launchPad',
  THIRD_PARTY = 'third_party',
  MOBILE_APP = 'mobile_app'
}

/**
 * User source channel enumeration
 * Based on SPM UserSourceChannel enum
 */
export enum UserSourceChannel {
  WEB_APP = 'web_app',
  PUBLIC_API = 'public_api'
}

/**
 * Comprehensive permissions interface
 * Based on SPM Permissions interface with all available permissions
 */
export interface IUserPermissions {
  // Core permissions
  campaigns_enabled?: boolean;
  campaigns_read_only?: boolean;
  contacts_enabled?: boolean;
  workflows_enabled?: boolean;
  workflows_read_only?: boolean;
  triggers_enabled?: boolean;
  funnels_enabled?: boolean;
  websites_enabled?: boolean;
  opportunities_enabled?: boolean;
  dashboard_stats_enabled?: boolean;
  bulk_requests_enabled?: boolean;
  appointments_enabled?: boolean;
  reviews_enabled?: boolean;
  online_listings_enabled?: boolean;
  phone_call_enabled?: boolean;
  conversations_enabled?: boolean;
  assigned_data_only?: boolean;
  
  // Reporting permissions
  adwords_reporting_enabled?: boolean;
  facebook_ads_reporting_enabled?: boolean;
  attributions_reporting_enabled?: boolean;
  agent_reporting_enabled?: boolean;
  
  // Additional permissions
  membership_enabled?: boolean;
  bot_service?: boolean;
  settings_enabled?: boolean;
  tags_enabled?: boolean;
  lead_value_enabled?: boolean;
  marketing_enabled?: boolean;
  
  // Extended permissions (can be added as needed)
  [key: string]: boolean | undefined;
}

/**
 * SaaS settings interface
 * Based on SPM ISaasSettings interface
 */
export interface ISaasSettings {
  [key: string]: any;
}

/**
 * Location-wise settings interfaces
 * Based on SPM User model location-wise properties
 */
export interface ILocationWiseSettings {
  [locationId: string]: string;
}

/**
 * Location notification settings
 * Based on SPM User model locations property
 */
export interface ILocationNotifications {
  [locationId: string]: string; // notification type
}

/**
 * Complete User interface
 * Based on SPM User model with all properties and methods
 */
export interface IUser extends IEntity {
  // Basic Information
  firstName: string;
  firstNameLowerCase: string;
  lastName: string;
  lastNameLowerCase: string;
  email: string;
  phone?: string;
  title?: string;
  
  // Authentication & Security
  isActive: boolean;
  role: UserRole;
  type: UserType;
  isPasswordPending?: boolean;
  lastPasswordChange?: string | Date;
  lastLoginTime?: string | Date;
  attempt?: number;
  
  // Company & Location Relationships
  companyId: string;
  locations: ILocationNotifications;
  
  // Permissions
  permissions: IUserPermissions;
  
  // Location-wise Settings
  locationWiseMeetingLocation?: ILocationWiseSettings;
  locationWiseZoomAdded?: ILocationWiseSettings;
  locationWiseTimezone?: ILocationWiseSettings;
  locationWiseOpenHours?: ILocationWiseSettings;
  
  // User Preferences
  isUserAvailabilityOn?: boolean;
  timezone?: string;
  profileImage?: string;
  
  // SaaS Settings
  saasSettings?: ISaasSettings;
  
  // Additional Properties
  dirty?: boolean;
  previousRole?: UserRole | null;
  
  // Computed Properties (will be handled by selectors/utilities)
  // name: string; // firstName + lastName
  // fullName: string; // title + firstName + lastName
  // profileColor: string; // generated color
  // isAdmin: boolean; // role === 'admin'
  // canAccessAll: boolean; // !permissions.assigned_data_only
  // isAssignedTo: boolean; // !isAdmin && !canAccessAll
}

/**
 * User creation payload
 * Required fields for creating a new user
 */
export interface ICreateUserPayload {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  title?: string;
  role: UserRole;
  type: UserType;
  companyId: string;
  locations?: ILocationNotifications;
  permissions?: Partial<IUserPermissions>;
  timezone?: string;
  isActive?: boolean;
}

/**
 * User update payload
 * Fields that can be updated
 */
export interface IUpdateUserPayload {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  title?: string;
  role?: UserRole;
  type?: UserType;
  locations?: ILocationNotifications;
  permissions?: Partial<IUserPermissions>;
  timezone?: string;
  isActive?: boolean;
  profileImage?: string;
  locationWiseMeetingLocation?: ILocationWiseSettings;
  locationWiseZoomAdded?: ILocationWiseSettings;
  locationWiseTimezone?: ILocationWiseSettings;
  locationWiseOpenHours?: ILocationWiseSettings;
  isUserAvailabilityOn?: boolean;
  saasSettings?: ISaasSettings;
}

/**
 * User query parameters
 * Based on SPM User model static query methods
 */
export interface IUserQueryParams {
  companyId?: string;
  locationId?: string;
  email?: string;
  phone?: string;
  role?: UserRole;
  type?: UserType;
  isActive?: boolean;
  deleted?: boolean;
  limit?: number;
  skip?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * User list response
 */
export interface IUserListResponse {
  users: IUser[];
  total: number;
  count: number;
  skip: number;
  limit: number;
  hasMore: boolean;
}

/**
 * User permission check utilities interface
 * Based on SPM User model permission checking methods
 */
export interface IUserPermissionChecks {
  isAdmin: boolean;
  canAccessAll: boolean;
  isAssignedTo: boolean;
  canViewOpportunities: boolean;
  canCreateReviewRequest: boolean;
  canAccessCampaigns: boolean;
  canAccessWorkflows: boolean;
  canAccessContacts: boolean;
  canAccessSettings: boolean;
}

/**
 * User computed properties interface
 * Based on SPM User model computed getters
 */
export interface IUserComputedProperties {
  name: string; // firstName + lastName
  fullName: string; // title + firstName + lastName
  profileColor: string; // generated HSL color
  displayName: string; // name or email fallback
}

/**
 * Private user data interface
 * Based on SPM PrivateUser class
 */
export interface IPrivateUserData {
  passwordHash?: string;
  isEmailVerified?: boolean;
  emailVerificationToken?: string;
  passwordResetToken?: string;
  smsOTP?: string;
}

/**
 * User with private data
 * For admin operations that need access to sensitive data
 */
export interface IUserWithPrivateData extends IUser {
  privateData?: IPrivateUserData;
}

/**
 * User session information
 * For tracking user sessions and activity
 */
export interface IUserSession {
  userId: string;
  sessionId: string;
  deviceId: string;
  deviceName?: string;
  deviceType?: string;
  ipAddress?: string;
  userAgent?: string;
  loginTime: string | Date;
  lastActivity: string | Date;
  isActive: boolean;
}

/**
 * User activity log entry
 * For tracking user actions and changes
 */
export interface IUserActivity {
  id: string;
  userId: string;
  action: string;
  entityType?: string;
  entityId?: string;
  details?: Record<string, any>;
  timestamp: string | Date;
  ipAddress?: string;
  userAgent?: string;
}
