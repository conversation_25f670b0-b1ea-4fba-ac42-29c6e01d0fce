<template>
  <div id="yext_promo_banner" v-if="!yextId && showYextBanner">
    <div>
      <i class="fas fa-bullhorn" style="margin-right: 8px"></i>
      Yext enables you to list your business on 150+ portals and websites to
      boost online presence and SEO instantly.
    </div>
    <div>
      <div class="hl-agency-banner__actions">
        <div
          class="btn hl-alert__action-btn"
          @click="gotoYextPitch()"
          id="yext_dashboard_banner"
        >
          Get Yext
        </div>
      </div>
      <div
        class="hl-alert__close-icon"
        @click="hideYextPitch()"
        id="yext_dashboard_banner_close"
      >
        <i class="fa fa-times"></i>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { User, Location, Company } from '@/models'
import { mapState } from 'vuex'
import { CompanyState } from '@/store/state_models'
export default {
  props: {},
  components: {},
  computed: {
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },

    showYextBanner() {
      if (
        this.user &&
        this.user.permissions.online_listings_enabled !== false &&
        this.company &&
        this.location &&
        this.company.country == 'US' &&
        this.location.country == 'US' &&
        this.company.stripeConnectId &&
        this.location.yextReseller.location_enabled
      ) {
        const found = !!this.hiddenUser.find(candidate => {
          return (
            candidate.email === this.user.email &&
            candidate.location_id === this.location.id
          )
        })
        return !found
      } else {
        return false
      }
    },

    yextId() {
      if (this.location) {
        return this.location.yextId || this.location.yextReseller.location_id
      }
    },
  },
  data() {
    return {
      showPromoteFreeUpgradeModal: false,
      showUpgradeBannerDefault: true,
      location: null,
      hiddenUser: [],
    }
  },
  async mounted() {
    this.location = new Location(
      await this.$store.getters['locations/getById'](
        this.$route.params.location_id
      )
    )

    let promoHiddenForUsers = window.localStorage.getItem('hl_hide_yext_banner')
    if (!promoHiddenForUsers) {
      promoHiddenForUsers = []
    } else {
      promoHiddenForUsers = JSON.parse(promoHiddenForUsers)
    }
    this.hiddenUser = promoHiddenForUsers
    /*  let dismissedDate = window.localStorage.getItem('free_upgrade_alert_dismissed');
    if (dismissedDate) {
      let currentTime = + new Date();
      if (currentTime - parseInt(dismissedDate) < 24 * 60 * 60 * 1000) {
        this.showUpgradeBannerDefault = false
      }
    } */
  },
  methods: {
    gotoYextPitch() {
      this.$router.push({ name: 'reputation_listing' })
    },
    hideYextPitch() {
      this.hiddenUser.push({
        email: this.user.email,
        location_id: this.location.id,
      })
      window.localStorage.setItem(
        'hl_hide_yext_banner',
        JSON.stringify(this.hiddenUser)
      )
    },
  },
}
</script>

<style lang="scss">
#yext_promo_banner {
  background-color: #e3f2fd;
  padding: 8px 8px 8px 16px;
  border-left: 3px solid #158bf5;
  color: #158bf5;
  border-radius: 8px;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #e0f2f1;
  border-left: 3px solid #009688;
  color: #009688;
  position: relative;

  .hl-alert__close-icon {
    color: #158bf5;
    cursor: pointer;
    font-weight: 500;
    opacity: 0.7;
    position: absolute;
    top: 0px;
    right: 4px;
    &:hover {
      opacity: 1;
    }
    &.--success {
      color: #009688;
    }
  }
  .hl-alert__action-btn {
    padding: 4px 12px;
    background-color: rgba(187, 222, 251, 0.5);
    color: #158bf5 !important;
    margin-left: 16px;
    cursor: pointer;
    background-color: #009688;
    color: #e0f2f1 !important;
    margin: 3px 14px;
    min-width: 120px;
  }
  .hl-agency-banner__actions {
    display: flex;
    align-items: center;
  }
}
</style>