<template>
  <div>
    <modal v-if="show" @close="$emit('close')" maxWidth="850" :showCloseIcon="step !== 'email' || checked">
      <div class="isv-switch-modal__header" v-if="step==='email'">
        Twilio Support Email
      </div>
      <div class="isv-switch-modal__header" v-else>
        LeadConnector Phone Service
      </div>
      <div v-if="loading" class="isv-switch-modal__body">
        <moon-loader :loading="loading" color="#37ca37" size="30px" />
      </div>
      <div class="isv-switch-modal__body" v-else-if="step==='confirmation'">
        <div style="margin: auto; width: 500px;">
          <span v-if="locationId">
          We will move your client {{locationName}} to a new service
          </span>
          <span v-else>
          This will switch your default telephone system from Twilio to LeadConnector phone service
          </span>
        </div>
        <div class="isv-switch-modal__logo-row">
          <img
            src="/pmd/img/logo_medallions/twilio-medallion.png"
            class="w-20 h-20"
            alt="twilio"
          >
          <img
            src="@/assets/pmd/img/isv-arrow-right.png"
            style="width: 140px"
            alt="Arrow"
          >
          <img
            src="@/assets/pmd/img/logo-leadconnector.png"
            class="w-20 h-20"
            alt="Lead Connector"
          >
        </div>
        <div class="isv-switch-modal__info-wrap" v-if="locationId">
          <i class="far fa-check-circle" />
          All phone numbers, call logs, SMS and MMS will be moved to new account
          <br/><br/>
          <i class="far fa-check-circle" />
          Call Recordings might get lost that they will still be archived in your Twilio account
        </div>
        <div class="isv-switch-modal__info-wrap" v-else>
          <i class="far fa-check-circle" />
          Your existing clients will be unaffected, you can move them to LeadConnector phone service later if you wish to.
          <br/><br/>
          <i class="far fa-check-circle" />
          Your new clients this point forward will use LeadConnector phone service
        </div>
        <div class="isv-switch-modal__footer-row">
          <UIButton
          type="button"
          use="outline"
          @click="$emit('close')"
        >
          Cancel
        </UIButton>
        <UIButton
          :loading="processing"
          type="button"
          :id="locationId ? 'init-migrate-location-isv' : 'init-agency-location-isv'"
          :use="failed ? 'danger' : 'primary'"
          @click="confirmIsvMigration()"
        >
          {{failed ? 'Failed' : 'Confirm'}}
        </UIButton>
        </div>
      </div>
      <div class="isv-switch-modal__body" v-else-if="step==='success'">
        <div class="isv-switch-modal__success-icon">
          <i class="fas fa-check-circle"></i>
        </div>
        <div class="isv-switch-modal__info-wrap text-center">
            <b>Success! </b>
            <br/><br/>
          LeadConnector Phone System is active on this account.
        </div>
        <div class="isv-switch-modal__footer-row">
          <div></div>
          <UIButton
          type="button"
          :id="locationId ? 'close-location-isv-migrate-modal' : 'close-location-agency-migrate-modal'"
          use="outline"
          @click="$emit('close')"
        >
          Close
        </UIButton>
        </div>
      </div>
      <div class="isv-switch-modal__body" v-else-if="step==='email'">
        <div style="font-size: 18px;">Please send this email:</div>
        <div class="isv-switch-modal__subject" style="width: 800px;">
          <div class="isv-switch-modal__subject-label">To:</div>
          <span id="isv-switch-modal__to-text">  <EMAIL> </span>
          <div class="isv-switch-modal__copy-btn" @click="copyText('isv-switch-modal__to-text')">
            <i class="far fa-copy"></i>
          </div>
        </div>
        <div class="isv-switch-modal__subject" style="width: 800px;">
          <div class="isv-switch-modal__subject-label">cc:</div>
          <span id="isv-switch-modal__cc-text"> <EMAIL> </span>
          <div class="isv-switch-modal__copy-btn" @click="copyText('isv-switch-modal__cc-text')">
            <i class="far fa-copy"></i>
          </div>
        </div>
        <div class="isv-switch-modal__subject --bold" style="width: 800px;">
          <div class="isv-switch-modal__subject-label">Subject:</div>
          <span id="isv-switch-modal__subject-text"> {{subjectText}} </span>
          <div class="isv-switch-modal__copy-btn" @click="copyText('isv-switch-modal__subject-text')">
            <i class="far fa-copy"></i>
          </div>
        </div>
        <div class="isv-switch-modal__info-wrap" style="width: 800px;">
          <div id="isv-switch-modal__body-text">
            Dear Twilio Support,
            <br /> <br />
            I would like to move the following phone numbers to a new sub account -
            <ul style="list-style: disc; margin-left: 16px">
              <li v-for="number in numbers" :key="number"><b>{{number}}</b></li>
            </ul>
            <br />
            Losing sub-account SID - <b>{{oldAccountSID}}</b> (my Twilio sub-account)<br/>
            Gaining sub-account SID - <b>{{newAccountSID}}</b> (my ISV's sub-account).<br/>
            Time-frame : ASAP <br/> <br />

            Please process this request at the earliest because it is business critical for me<br/><br />

            Thanks!
          </div>
            <div class="isv-switch-modal__copy-btn" @click="copyText('isv-switch-modal__body-text')">
              <i class="far fa-copy"></i>
            </div>
        </div>

        <div class="option">
            <input type="checkbox" id="emailed-to-twilio" v-model="checked" />
            <label for="emailed-to-twilio">
                I have sent this <NAME_EMAIL> from my registered Email ID.
            </label>
        </div>
        <br/> <br/>
        <div class="isv-switch-modal__footer-row">
          <div></div>
          <UIButton
          :disabled="!checked"
          type="button"
          use="outline"
          @click="$emit('close')"
        >
          Close
        </UIButton>
        </div>
      </div>
    </modal>
  </div>
</template>

<script>
import Modal from '@/pmd/components/common/Modal.vue'
import copyToClipboard from '@/util/copy-to-clipboard.ts'

export default {
  components: {
    Modal
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    locationName: {
      type: String,
      default: ''
    },
    locationId: {
      type: String,
      default: ''
    },
    migrationStatus: {
      type: String,
      default: ''
    },
    companyId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      step: 'confirmation', // 'confirmation' | 'success' | 'failed' | 'email'
      checked: false,
      loading: false,
      processing: false,
      oldAccountSID: '',
      newAccountSID: '',
      failed: false,
      numbers: [],
      // subject: 'Need to move phone numbers urgently - Business Critical'
    }
  },
  created() {
    if(this.migrationStatus === 'pending') {
      this.loading = true;
      this.confirmIsvMigration();
    }
  },
  computed: {
    subjectText() {
      return `Need to move phone numbers urgently - Business Critical | ${this.oldAccountSID}`
    }
  },
  methods: {
    async confirmIsvMigration() {
      this.processing = true;
      try{
        if(this.locationId) {
          const { data } = await this.$http.post(`/twilio/migrate_subaccount/${this.locationId}`)
          this.newAccountSID = data.newAccountSID;
          this.oldAccountSID = data.oldAccountSID;
          this.numbers = Object.keys(data.numbers);
          // console.log(this.numbers);
          this.loading = false;
          this.$emit('updated');
          this.step = this.numbers.length === 0 ? 'success' : 'email';
        } else {
          const { data } = await this.saasService.get(
            `/isv-mode/enable-isv-mode/${this.companyId}`
          )
          this.$emit('success');
          this.step = 'success';
        }
      } catch (err) {
        console.log(err);
        this.failed = true;
        setTimeout( ()=> {
          this.failed = false;
        }, 2000);
      }
      this.loading = false;
      this.processing = false;
    },
    copyText(elementId) {
      let bodyEl = document.getElementById(elementId);
      if(bodyEl) {
        copyToClipboard(bodyEl.innerText)
      }
    }
  }
}
</script>

<style lang="scss">
.isv-switch-modal__header{
  border-bottom: 1px solid #C4C4C4;
  padding: 20px 32px;
  font-size: 18px;
}
.isv-switch-modal__body{
  padding: 32px;
  max-height: calc(100vh - 100px);
  overflow-y: auto;
}
.isv-switch-modal__logo-row{
  width: 500px;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 32px;
}
.isv-switch-modal__info-wrap{
  width: 661px;
  background: #F3F8FB;
  border-radius: 6px;
  padding: 24px;
  margin: 20px auto 32px;
  position: relative;
  // i {
  //   color: #37CA37;
  //   margin-right: 12px;
  // }
}
.isv-switch-modal__subject {
  width: 100%;
  background: #F3F8FB;
  border-radius: 6px;
  padding: 24px 24px 16px;
  margin: 16px auto 0px;
  position: relative;
  &.--bold {
    font-weight: 700;

  }
  .isv-switch-modal__subject-label {
    font-size: 12px;
    font-weight: 300;
    user-select: none;
    margin-top: -12px;
    &.--inline {
      display: inline;
    }
  }
}
.isv-switch-modal__copy-btn{
  position: absolute;
  right: 20px;
  top: 20px;
  background-color: #ffffff;
  border: 1px solid #eaf0f4;
  border-radius: 4px;
  height: 30px;
  width: 30px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  i {
    color: #777777;
  }
  &:hover {
    background-color: #fafafa;
    box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.1);
  }
}
.isv-switch-modal__footer-row{
  display: flex;
  justify-content: space-between;
}
.isv-switch-modal__success-icon{
  font-size: 74px;
  margin: 50px;
  color: #37CA37;
  margin: auto;

  border-radius: 50%;
  text-align: center;
  height: 92px;
  width: 92px;

  border: 4px solid #DDF6DD;

  display: flex;
  align-items: center;
  justify-content: center;

}
</style>
