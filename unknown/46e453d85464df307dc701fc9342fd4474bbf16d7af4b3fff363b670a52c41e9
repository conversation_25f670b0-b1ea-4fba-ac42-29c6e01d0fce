<template>
  <div class="power-dialer-main">
    <div class="power-dialer-header">
      <SearchBox
        :searching="searching"
        :disableSearch="isContactSelected"
        @focus="searchContact"
        @search="searchContact"
      />
    </div>
    <div class="power-dialer-body mb-2" ref="contactList">
      <div class="custom-contact" v-if="!isContactSelected">
        <div class="label-header">
          <p>{{ this.label }}</p>
        </div>
        <div class="contact-list" v-if="!fetching">
          <div class="contact-type">
            <div class="contactList">
              <div
                class="detail-box"
                v-for="contact in contacts"
                :key="contact.id"
                @click="assignSelectedContact(contact)"
              >
                <span class="img-circle">
                  <Avatar :contact="contact" :include_name="false" />
                </span>
                <div class="user-info">
                  <p>{{ contact.fullName || 'unknown' }}</p>
                  <PhoneNumber
                    type="display"
                    v-model="contact.phone"
                    :currentLocationId="currentLocationId"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          style="position: absolute;left: 50%;transform: translate(-50%, -50%);top: 40%;"
          v-else
        >
          <moon-loader :loading="fetching" color="#37ca37" size="30px" />
        </div>
      </div>
      <div class="selectedList" v-else>
        <SelectedItem
          :currentLocationId="currentLocationId"
          :selectedItem="selectedContact"
          @removeAssignedItem="removeAssignedContact"
        />
      </div>
    </div>
    <div class="power-dialer-footer">
      <div v-if="showCallFrom">
        <CallFrom
          :currentLocationId="currentLocationId"
          :phoneNumbers="phoneNumbers"
          iconName="fas fa-phone"
          :isDisabled="!isContactSelected"
          @makeCall="makeCall"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import * as moment from 'moment-timezone'
import config from '../../../config'
import PhoneNumber from '@/pmd/components/util/PhoneNumber.vue'
import SearchBox from '@/pmd/components/power_dialer/SearchBox.vue'
import SelectedItem from '@/pmd/components/power_dialer/SelectedItem.vue'
import CallFrom from '@/pmd/components/power_dialer/CallFrom.vue'
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'
import firebase from 'firebase/app'

import { Contact, PhoneCall, User } from '../../../models'
const Avatar = () => import('../Avatar.vue')
export default Vue.extend({
  props: ['currentLocationId', 'phoneNumbers'],
  components: { PhoneNumber, Avatar, SearchBox, SelectedItem, CallFrom },
  data() {
    return {
      fetching: false,
      label: 'Recently Contacted',
      showCallFrom: true,
      selectedContact: undefined as Contact | undefined,
      contacts: [] as Contact[],
      total: 0,
      filters: {
        filters: [
          {
            filterName: 'Phone',
            filterName_lc: 'phone',
            selectedOption: {
              condition: 'is_not_empty',
              filterName: 'Phone',
              filterName_lc: 'phone'
            }
          }
        ],
        just_ids: 'false',
        location_id: this.currentLocationId,
        page: 1,
        page_limit: 10
      },
      searchDebouncer: undefined as NodeJS.Timer | undefined,
      searching: false,
      seqCalling: 0
    }
  },
  created() {
    if (this.user && this.user.permissions.assigned_data_only) {
      this.filters['assigned_to'] = this.user.id
      this.label = 'Listing all contacts'
      this.load()
    } else {
      this.getContacts()
    }
  },
  computed: {
    isContactSelected() {
      return lodash.isEmpty(this.selectedContact) ? false : true
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    })
  },
  watch: {
    currentLocationId() {
      this.contacts = []
      if (this.user && this.user.permissions.assigned_data_only) {
        this.filters['assigned_to'] = this.user.id
        this.label = 'Listing all contacts'
        this.load()
      } else {
        this.getContacts()
      }
    }
  },
  methods: {
    assignSelectedContact(contact: any): any {
      this.selectedContact = contact
      this.showCallFrom = true
    },
    removeAssignedContact() {
      this.selectedContact = {}
    },
    async getContacts() {
      this.fetching = true
      let dateRange = {
        start: new Date(moment().subtract(7, 'days')).toString(),
        end: new Date().toString()
      }
      let limit = 10
      let snapshot
      let lastObject: firebase.firestore.DocumentSnapshot | undefined

      do {
        let query = PhoneCall.fetchLatestCalls(
          this.currentLocationId,
          dateRange,
          (this.user && this.user.permissions.assigned_data_only) || ''
        )
        if (lastObject) {
          query = query.startAfter(lastObject)
        }
        snapshot = await query.limit(limit).get()
        if (snapshot.size > 0) {
          lastObject = snapshot.docs[snapshot.size - 1]
          await Promise.all(
            snapshot.docs.map(async snap => {
              const phoneCall = new PhoneCall(snap)
              let contact = await Contact.getById(phoneCall.contactId)
              if (
                contact &&
                !contact.deleted &&
                contact.phone &&
                !lodash.find(this.contacts, { id: contact.id }) &&
                this.contacts.length < 5
              ) {
                this.contacts.push(contact)
              }
            })
          )
        }
      } while (snapshot.size === limit && this.contacts.length < 5)
      this.fetching = false
    },
    load() {
      this.seqCalling++
      const seqCall = this.seqCalling
      new Promise<Contact[]>(async (resolve, reject) => {
        try {
          if (!this.contacts.length) this.fetching = true
          const response = await this.$http.post(
            `${config.baseUrl}/search/smartlist`,
            this.filters
          )
          if (response.status !== 200) return reject()
          if (seqCall !== this.seqCalling) return
          this.total = lodash.get(response, 'data.hits.total.value')
          let data: { [key: string]: any }[] = response.data.hits.hits
          return resolve(
            data.map(elasticContact => {
              const data = {
                id: elasticContact._id,
                ...elasticContact._source
              }
              return new Contact(data)
            })
          )
        } catch (err) {
          return reject(err)
        }
      })
        .then((newContacts: Contact[]) => {
          if (this.searching) {
            this.contacts = newContacts
          } else {
            this.contacts = this.contacts.concat(
              newContacts.filter(
                x => this.contacts.findIndex(y => y.id === x.id) === -1
              )
            )
          }
          this.searching = false
          this.fetching = false
        })
        .catch(err => {
          this.searching = false
          this.fetching = false
          console.log(err)
        })
    },
    scroll(e) {
      if (this.label === 'Recently Contacted') return
      let bottomOfWindow =
        100 >=
        e.target.scrollHeight - e.target.scrollTop - e.target.clientHeight

      if (bottomOfWindow && this.contacts.length !== this.total) {
        this.filters.page += 1
        this.load()
      }
    },
    searchContact(searchText?: string) {
      if (this.searchDebouncer) clearTimeout(this.searchDebouncer)
      let genericFilterIndex = lodash.findIndex(this.filters['filters'], {
        filterName_lc: 'generic'
      })
      if (genericFilterIndex !== -1)
        this.filters.filters.splice(genericFilterIndex, 1)
      if (searchText && searchText.trim().length > 0) {
        this.label = 'Showing Results'
        this.showCallFrom = false
        this.searching = true
        this.filters.page = 1
        this.filters['filters'].push({
          filterName: 'generic',
          filterName_lc: 'generic',
          selectedOption: {
            condition: 'is',
            filterName: 'generic',
            filterName_lc: 'generic',
            firstValue: searchText
          }
        })
      } else {
        this.label = 'Listing all contacts'
        this.showCallFrom = false
      }
      this.searchDebouncer = setTimeout(this.load, 300)
    },
    makeCall() {
      if (!this.selectedContact) return
      this.$root.$emit('makeCall', {
        phone_number: this.selectedContact.phone,
        contact: this.selectedContact,
        showModal: true
      })
    }
  },
  mounted() {
    if (this.$refs.contactList)
      this.$refs.contactList.addEventListener('scroll', this.scroll)
  },
  beforeDestroy() {
    if (this.$refs.contactList)
      this.$refs.contactList.removeEventListener('scroll', this.scroll)
  }
})
</script>
<style lang="scss" scoped></style>
