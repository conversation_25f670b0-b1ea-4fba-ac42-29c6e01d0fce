<template>
	<div class="hl_settings--body">
		<div class="container-fluid" v-if="!company.inIsvMode && !companyMigratedToIsv">
			<div class="row">
				<div class="hl_settings--main col-lg-12">
					<div class="card">
						<div class="card-header">
							<h3>Twilio Settings</h3>
						</div>
						<div class="low-twilio-credits" v-if="isAgencyEmpty || (company && company.twilioTrialMode)">
							<div class="low-credits-info">
								<h5>You are low on your free Twilio credits ! Only {{company.twilioFreeCredits > 0 ? company.twilioFreeCredits : 0}} free credits remaining !!
									<i class="fa fa-info-circle" style="font-size:16px; color:#607179;" v-b-tooltip.hover title="Twilio credits offered by HighLevel for free"></i>
								</h5>
								<p>You need to connect a Twilio account to manage calls &amp; texts, that are sent/recieved in the account.
									<span class="setup-link" @click="showGuide = !showGuide">{{showGuide?'Hide':'Show'}} Instructions</span>
								</p>
							</div>
							<agency-twilio-setup-guide v-show="showGuide"/>
						</div>
						<div class="row" style="margin: 64px auto;">

							<div class="personal-logo" :class="company.country && isCountrySupportIsv(company.country) && isvModeBetaAccess? 'col-md-6' : ' col-md-12'">
								<form>
									<div class="flex">
										<div class="px-2" >
											<img
												src="/pmd/img/logo_medallions/twilio-medallion.png"
                        class="w-20 h-20"
												alt="twilio"
											>
										</div>
										<div class="col-sm-9">
											<div class="row">
												<div class="col-sm-12">
													<div class="form-group">
														<UITextInputGroup
															type="text"
															placeholder="Account SID"
															label="Account SID"
															v-model="accountSID"
															v-validate="'required'"
															name="accountSID"
															@blur="verifyTwilio"
															@change="edited=true;isVerified=false"
															:error="errors.has('accountSID')"
															:errorMsg="'Account SID Required'"
														/>
													</div>
													<i class="fa fa-check verfied-icon" v-if="isVerified"></i>
												</div>
												<div class="col-sm-12" v-if="edited">
													<div class="form-group">
														<UITextInputGroup
															type="text"
															placeholder="Auth Token"
															label="Auth Token"
															v-model="accountToken"
															v-validate="'required'"
															name="token"
															@blur="verifyTwilio"
															@change="edited=true;isVerified=false"
															:error="errors.has('token')"
															:errorMsg="'Auth Token Required'"
														/>
													</div>
													<i class="fa fa-check verfied-icon"  v-if="isVerified"></i>
												</div>
												<div class="col-sm-12">
													<div class="form-group flex">
														<UICheckbox
															id="create-subaccounts-automatically"
															v-model="createSubAccountsAutomatically"
															@change="edited=true"
														/>
														<label for="create-subaccounts-automatically">
															Automatically create Sub-Accounts
															<span
																style="margin-left: 5px;"
																class="input-group-addon"
																v-b-tooltip.hover
																title="A new Twilio Sub-Account will be created automatically when a new Location is created. Twilio charges for every Sub-account creation."
															>
																<i class="fas fa-question-circle"></i>
															</span>
														</label>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div class="row">
										<div class="col-sm-9 offset-3">
											<div class="form-group" :class="{invisible: !edited}">
												<div style="display: inline-block;position: relative;">
													<p class="error" v-if="!isVerified">The provided credentials are invalid.</p>
													<UIButton
														:disabled="!isVerified"
														@click.prevent="saveTwilioAccount"
														:loading="processing"
														use="primary"
													>Save</UIButton>
												</div>
											</div>
										</div>
									</div>
								</form>
							</div>
              <CompanyTwilioIsvSwitcher v-if="company.country && isCountrySupportIsv(company.country) && isvModeBetaAccess" class="col-md-6" @optIsv="showCompanyTwilioIsvSwitchModal = true, companyTwilioIsvSwitchModalLocationName = ''"/>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="container-fluid">
			<div class="hl_settings--controls">
				<div class="hl_settings--controls-left">
					<h2>
						Twilio Settings for Locations
						<span>{{ locationTwilioAccounts.length }} locations</span>
					</h2>
				</div>
				<div class="hl_settings--controls-right">
          <button type="button" class="btn btn-warning" @click="toggleMoveNumbersModal">
						Move numbers
					</button>
        </div>
			</div>
			<div class="card hl_settings--team-management">
				<div class="card-body --no-padding" style="max-width:100%;">
					<div class="table-wrap">
						<table class="table">
							<thead>
								<tr>
									<th style="width: 20%" data-sort="string">Name</th>
									<th style="width: 30%" data-sort="string">Account SID</th>
									<!-- <th style="width: 30%" data-sort="string">Account Token</th> -->
									<th style="width: 10%" data-sort="string">
										Number Validation
										<span
											style="margin-left: 5px; color: #aaa"
											class="input-group-addon"
											v-b-tooltip.hover
											title="This will validate the phone number when first SMS / Call is made"
										>
											<i class="fas fa-question-circle"></i>
										</span>
									</th>
									<th style="width: 10%"></th>
                  <th v-if="companyMigratedToIsv || company.inIsvMode"></th>
								</tr>
							</thead>
							<tbody v-if="!loading && locationTwilioAccounts.length > 0">
								<TwilioListItem v-for="locationTwilioAccount in locationTwilioAccounts"
									:key="locationTwilioAccount.location.id"
                  :companyId="company.id"
									:location="locationTwilioAccount.location"
									:locationTwilioAccount="locationTwilioAccount.twilioAccount"
									:isAgencyEmpty="isAgencyEmpty"
                  :inIsvMode="companyMigratedToIsv || company.inIsvMode"
                  :isvBetaEnabled="isvModeBetaAccess"
									@edit="enterEdit(locationTwilioAccount)"
									@updated="editSuccess"
                  @optIsv="e => optLocationIsv(locationTwilioAccount.location, e)"
									/>
							</tbody>
						</table>
					</div>
          <div style="margin: 30px auto;" v-if="loading">
            <upgrade-modal-shimmer style="margin: auto;"/>
          </div>
				</div>
			</div>
			<div class="card">
				<MoveTwilioNumbers :master-sid="accountSID" :api-key="accountToken" :toggle.sync="moveNumbersModal" :twilioAccounts="{companyTwilioAccount, locationTwilioAccounts}" />
			</div>
		</div>
		<twilio-edit-modal
			:values="twilioEditModalValues"
			@hidden="twilioEditModalValues={visible: false}"
			@success="editSuccess"
		/>
    <company-twilio-isv-switch-modal
      v-if="showCompanyTwilioIsvSwitchModal"
      :show="showCompanyTwilioIsvSwitchModal"
      @updated="handleUpdate"
      @success="handleAgencyMigrationSuccess"
      @close="showCompanyTwilioIsvSwitchModal = false"
      :locationName="companyTwilioIsvSwitchModalLocationName"
      :locationId="companyTwilioIsvSwitchModalLocationId"
      :migrationStatus="companyTwilioIsvSwitchModalStatus"
      :companyId="company.id"
    />
	</div>
</template>

<script lang='ts'>
import Vue from 'vue';
import { Company, Location, TwilioAccount } from '@/models';
import TwilioListItem from '@/pmd/components/agency/TwilioListItem.vue';
import MoveTwilioNumbers from "../../components/agency/MoveTwilioNumbers.vue";
import TwilioEditModal from "@/pmd/components/agency/TwilioEditModal.vue";
import AgencyTwilioSetupGuide from './AgencyTwilioSetupGuide.vue';
import CompanyTwilioIsvSwitcher from "@/pmd/components/isv/agency/CompanyTwilioIsvSwitcher.vue";
import CompanyTwilioIsvSwitchModal from "@/pmd/components/isv/agency/CompanyTwilioIsvSwitchModal.vue";
import UpgradeModalShimmer from '@/pmd/components/common/shimmers/UpgradeModalShimmer.vue'

let unsubscribeLocations: () => void;

export default Vue.extend({
	components: {
		TwilioListItem,
		MoveTwilioNumbers,
		TwilioEditModal,
		AgencyTwilioSetupGuide,
    CompanyTwilioIsvSwitcher,
    CompanyTwilioIsvSwitchModal,
    UpgradeModalShimmer
	},
	data() {
		return {
			// editLocationId : '',
			// editCompanyId:'',
      loading: false,
			locations: [] as Location[],
			companyTwilioAccount: {
        account_sid: '',
        createSubAccountsAutomatically: false
      },
			locationTwilioAccounts: [],
			processing: false,
			edited: false,
			company: {} as Company,
			accountSID: '',
			accountToken: '',
			moveNumbersModal: false,
			isVerified: false,
			isAgencyEmpty: false,
			showGuide: false,
			twilioEditModalValues: {
				visible:false,
			},
			createSubAccountsAutomatically: true,
      showCompanyTwilioIsvSwitchModal: false,
      companyTwilioIsvSwitchModalLocationName: '',
      companyTwilioIsvSwitchModalLocationId: '',
      companyTwilioIsvSwitchModalStatus: '',
      companyMigratedToIsv: false,
		}
	},
	async created() {
		var _self = this;
		// company can directly be accessed from store:
		const authUser = await this.$store.dispatch('auth/get');
		this.company = await Company.getById(authUser.companyId);
    this.loading = true;
    this.fetchData();
	},
	beforeDestroy() {
		if (unsubscribeLocations) unsubscribeLocations();
	},
	methods: {
    async fetchData() {
      this.$http.get(`/twilio/accounts/${this.company.id}`).then(res => {
      if (res && res.status === 200) {
        this.companyTwilioAccount = res.data.companyTwilioAccount;

        if (this.companyTwilioAccount && this.companyTwilioAccount.account_sid) {
          this.accountSID = this.companyTwilioAccount.account_sid;
          // this.accountToken = this.companyTwilioAccount.token;
          this.createSubAccountsAutomatically = this.companyTwilioAccount.createSubAccountsAutomatically
          this.isAgencyEmpty = false;
          this.verifyTwilio();
        } else {
          this.isAgencyEmpty = true;
        }

        this.locationTwilioAccounts = lodash.orderBy(res.data.locationTwilioAccounts, function(e) { return e.location.name_lower_case });
        this.loading = false;
      }
      }).catch(err => {
        console.log(err)
        this.loading = false;
      });
    },
    handleAgencyMigrationSuccess() {
      // this.showCompanyTwilioIsvSwitchModal = false;
      this.companyMigratedToIsv = true;
    },
    optLocationIsv(location, e) {
      // console.log(location);
      this.showCompanyTwilioIsvSwitchModal = true;
      this.companyTwilioIsvSwitchModalLocationName = location.name;
      this.companyTwilioIsvSwitchModalLocationId = location.id;
      this.companyTwilioIsvSwitchModalStatus = e.migration_status;
    },
		async saveTwilioAccount() {
			const result = await this.$validator.validateAll();
			if (!result || !this.isVerified) {
				this.$uxMessage('warning', `The provided credentials are invalid. Please provide the Main Project's credentials.`)
				return false;
			}
			this.processing = true;
			try {
        const newSID  = this.accountSID && this.accountSID.trim().length ? this.accountSID.trim() : undefined
        const newToken = this.accountToken && this.accountToken.trim().length ? this.accountToken.trim() : undefined

				let body = {
					accountSID: newSID ,
					accountToken: newToken,
          createSubAccountsAutomatically: this.createSubAccountsAutomatically
        };
        this.$http.post('/twilio/edit?company_id=' + this.company.id, body).then(async res => {
          if (res.data === 'invalid') {
            alert("Invalid accountSID and access token");
          } else {
            this.isAgencyEmpty = false;
            this.company.twilioTrialMode = false;
            await this.company.save();
            this.$store.commit('agencyTwilio/toggleAlert', false);
          }
          this.edited = false;
          this.processing = false;
        });
      } catch (ex) {
          console.error("Couldn't create twilio app:", ex);
          this.edited = false;
          this.processing = false;
		}
	},
	async verifyTwilio() {
    let query = '';
		if (this.accountSID && this.accountToken) {
			const accountSID = this.accountSID && this.accountSID.trim();
      const accountToken = this.accountToken && this.accountToken.trim();

      query = `account_sid=${accountSID}&account_token=${accountToken}`
		} else {
      query =  `company_id=${this.company.id}`
    }

    try {
      const res = await this.$http.get(`/twilio/checkAccount?${query}`)
      if (res.data === 'invalid') this.$uxMessage('warning', `The provided credentials are invalid. Please provide the Main Project's credentials.`)
      this.isVerified = (res.data !== 'invalid');
    } catch (err) {
      if (err.response && err.response.status === 401) {
        this.$uxMessage('warning', `The provided credentials belong to a Sub-account within Twilio. Please provide the Main Project's credentials.`)
      }
      this.isVerified = false;
    }
	},
    toggleMoveNumbersModal() {
      	this.moveNumbersModal = !this.moveNumbersModal
	},
	getLocationIndex(locationId) {
    	return lodash.findIndex(this.locationMailGunAccounts, { 'location': { 'id': locationId }});
    },
    enterEdit(locationTwilioAccount: { [key: string]: any}){
      let response = true

      if (
        locationTwilioAccount.location &&
        locationTwilioAccount.location.twilio_rebilling_enabled
      ) {
        alert(
          'Twilio rebilling is enabled for this account, please turn off rebilling before proceeding'
        )
        return
        // const { saas_settings } = locationTwilioAccount.location.settings
        // const rebillingEnabled =
        //   saas_settings &&
        //   saas_settings.saas_mode === 'activated' &&
        //   saas_settings.twilio_rebilling &&
        //   saas_settings.twilio_rebilling.enabled
        // if (rebillingEnabled) {
        //   alert(
        //     'Twilio rebilling is enabled for this account, please turn off rebilling before proceeding'
        //   )
        //   return
        // }
      }
		if( locationTwilioAccount.twilioAccount && locationTwilioAccount.twilioAccount.account_sid){
			response = confirm("Are you sure you want to change the Twilio set up. This may break your existing functionality.");
		}
		if(response == true) {
			this.twilioEditModalValues = {
				visible:true,
				location: locationTwilioAccount.location,
				twilioAccount: locationTwilioAccount.twilioAccount
			};
		}
    },
    enterCompanyEdit(){
		this.twilioEditModalValues = {
			visible:true,
			twilioAccount: {
				account_sid: this.companyTwilioAccount.account_sid,
				token: this.companyTwilioAccount.token
			}
		};
	},
  handleUpdate() {
    this.fetchData();
  },
	editSuccess( e ) {
    //   if (event.companyId) this.companyMailGunAccount = event.mailgunAccount;
	//   else
		if (e.locationId && e.twilioAccount) {
			let locationIndex = this.locationTwilioAccounts.findIndex( account => account.location.id === e.locationId)
			if(locationIndex != -1) {
				// this.locationTwilioAccounts[locationIndex].twilioAccount = {...e.twilioAccount.data};
				this.locationTwilioAccounts = [
					...this.locationTwilioAccounts.slice(0,locationIndex),
					{
						location: this.locationTwilioAccounts[locationIndex].location,
						twilioAccount: e.twilioAccount.data,
					},
					...this.locationTwilioAccounts.slice(locationIndex +1),
				]
			}
		}
		this.twilioEditModalValues={visible: false};
    },
    isCountrySupportIsv(country: string): boolean {
      const listOfCountriesToEnableISV = [
        'US',
        'CA',
        'EE', // Estonia
        'GB', // UK
        'HU', // Hungary
        'KE', // Kenya
        'PH', // Phillippines
        'PL' // Poland
      ];
      return (listOfCountriesToEnableISV.indexOf(country) !== -1) ? true : false;
    },
  },
  computed: {
    isvModeBetaAccess(): boolean {
      if (!this.company) return false

      return this.company.allowBetaAccess && this.company.allowBetaAccess.isvMode === true
    }
  }
});
</script>
<style scoped>
.low-twilio-credits{
	/* text-align: center; */
    padding: 30px;
}
.low-credits-info{
	text-align: center;
	padding-bottom: 20px;
	border-bottom: 2px solid #f2f7fa;
}
.setup-link{
	color: #188bf6;
	cursor: pointer;
	font-weight: 700;
	min-width: 120px;
	display: inline-block;
}
.verfied-icon{
	position: absolute;
    right: -30px;
    top: 32px;
    font-size: 20px;
    color: #35ca38;
}
</style>
