<template>
  <b-modal ref="modal-xl-d" id="modal-bulkstatusreport-44" size="xl" @hide="close" hide-footer>
      <template v-slot:modal-title>
        <moon-loader-title :title="'Bulk Action Status'" :loading="loading"/>
        <!-- <h5 class="modal-title"></h5> -->
      </template>
      <template>
      <div class="modal-body" style="overflow:auto">
        <!-- <div class="mt-3, mb-1"><span class="mx-2">{{description || ''}}</span></div> -->
        <StatsDetailModalTable
            :timeZone="locationTimeZone"
            :title="title"
            :items="items"
            :fields="fields"
            :currentPage="currentPage"
            :itemPerPage="itemsPerPage"
            :rows="rows"
            :isLoading="loading"
            @pageChanged="pageChanged"
            @perPageChanged="perPageChanged"/>
      </div>
       <div class="modal-footer">
        <div class="modal-footer--inner">
          <div class="modal-footer--inner nav" v-if="!loading">
            <UIButton use="outline" @click.prevent.stop="close">
              Close
            </UIButton>
          </div>
        </div>
      </div>
      </template>
  </b-modal>
</template>

<script lang="ts">

import Vue from 'vue'
import { Contact } from '@/models'
import { ContactBulkAction } from './server/bulkreq_api'
import { UxMessage } from '@/util/ux_message'
import { BulkRequestV2 } from './server/bulk_req_v2'
const StatsDetailModalTable = () => import('../../components/StatsDetailModalTable.vue')

export default Vue.extend({
  name:"BulkActionItemsReport",
  props: {
    description: String,
    bulkReqId : String,
    showError: Boolean,
    showSuccess: Boolean,
  },
  components: {
    StatsDetailModalTable
  },
  inject: ['uxmessage'],
  data() {
    return {
      items: [] as ContactBulkAction[],
      contact: null as Contact | null,
      loading: false,
      page: null,
            title: 'Contact Bulk Actions',
      exportItems: [],
      fields: [
        { key: 'date',   label: 'Date', sortable: false, checked: true, type: 'tzdate'  },
        { key: 'name',   label: 'Name', sortable: false, checked: true, type: 'capitalize'},
        { key: 'email',  label: 'Email', sortable: false, checked: true },
        { key: 'phone',  label: 'Phone', sortable: false, checked: true },
        { key: 'status', label: 'Status', sortable: false, checked: true, type: 'capitalize' },
        { key: 'type',   label: 'Type', sortable: false, checked: true, type:  'bulk-capitalize' },
        { key: 'error',  label: 'Error', sortable: false, checked: true, type: 'capitalize' }
      ],
      currentPage: 0,
      itemsPerPage: 8,
      rows: 0,
      fileName: 'hello-world.txt'
    }
  },
  async mounted(){
    this.showPopup();
    await this.$nextTick();
    await this.fetch();
  },
  methods: {
    async fetch(page?:number) {
      try {
        this.items = [];
        if (!this.bulkReqId) return;
        this.loading = true;
        const result = await ContactBulkAction.getActionHistory(this.bulkReqId, {showSuccess: this.showSuccess === true ? true : null, showError: this.showError === true ? true : null, page, pageSize: this.itemsPerPage});
        this.items = result.items;
        this.rows = result.totalCount;
        this.currentPage = result.page;
        this.itemsPerPage = result.pageSize;
      } catch (e) {
        this.loading = false
        console.error(e);
        this.showErrorMessage(e && e.message || 'unknown error',e);
        this.$emit('onError',e);
      } finally {
        this.loading = false;
      }
    },
    showErrorMessage(err:string, stack?:string){
      this.uxmessage(UxMessage.errorType(err,stack))
    },
    async pageChanged(page: number) {
      if (this.currentPage === page) return;
      await this.fetch(page);
    },
    async perPageChanged(perPage: number) {
      // page size change
    },
    close() {
      this.hidePopup();
      this.$emit('closed')
    },
    showPopup(){
      if (this.$refs['modal-xl-d']) this.$refs['modal-xl-d'].show();
    },
    hidePopup(){
      this.$refs['modal-xl-d'].hide();
    }
  },
  computed: {
    locationTimeZone(){
      //console.log(`Location Time Zone -- ${this.$store.getters[`locations/getCurrentLocationTimeZone`]}`)
      return this.$store.getters[`locations/getCurrentLocationTimeZone`];
    },
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.close();
    },
    'contactId': function(id) {
      this.fetch();
    },
  },
})
</script>
<style scoped>

.hl_rules--wrap{
  margin-top: 5px;
}
.form-input-dropdown{
    position: absolute;
    will-change: transform;
    top: 0px;
    left: 0px;
    transform: translate3d(0px, 51px, 0px);
    min-width: 0;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
}
td.td-borderless{
  border-top: none !important;
  padding: 0px !important;
  padding-bottom: 6px !important;

}
</style>

<!--
            <table class="table table-sort" v-if="loading">
              <tbody>
                <tr v-for="index in 5" :key="index">
                  <td v-for="index2 in 6" :key="index2">
                    <span class="workinprogress"></span>
                  </td>
                </tr>
              </tbody>
            </table>
          <table class="table table-sort" v-if="items && !loading" >
              <thead>
                <th>Date</th>
                <th>Name</th>
                <th>Email</th>
                <th>Phone</th>
                <th>Status</th>
                <th>Operation</th>
              </thead>
              <template v-for="item in items" >
                <tr v-bind:key="item.id">
                  <td>
                    <div v-if="item.date" style="align-items: normal !important;"
                        :set="parts = getDateParts(item.date, locationTimeZone)">
                      <div class="date-display text-nowrap">{{parts[0]}}</div>
                      <div class="date-display --blue text-nowrap">{{parts[1]}} <small>({{parts[2]}})</small></div>
                    </div>
                  </td>
                  <td style="max-width:150px; word-break: break-word;">{{item.contact_name || item.name}}</td>
                  <td style="max-width:200px; word-break: break-word;">{{item.email}}</td>
                  <td style="max-width:200px; word-break: break-word;">{{item.phone}}</td>
                  <td style="max-width:50px; word-break: break-word;">{{item.status}}</td>
                  <td style="max-width:50px; word-break: break-word;">{{item.type}}</td>
                  <td><div style="max-width:250px">{{item.action}}</div></td>
                </tr>
                <tr v-if="item.error" v-bind:key="item.id">
                  <td colspan="20" class="td-borderless"><span class="mx-2 --gray"><i class="fa fa-minus-circle"></i></span>{{item.error}}</td>
                </tr>
              </template>
          </table>
-->
