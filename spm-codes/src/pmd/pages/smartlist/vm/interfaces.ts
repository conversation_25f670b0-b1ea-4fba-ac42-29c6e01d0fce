
export interface IKeyVal {
  key: string;
  value: string;
}

export interface IOrderedKeyVal extends IKeyVal {
  order: number;
}

export interface IColDefinition extends IOrderedKeyVal{
  parentCustomFieldId?:string;
  isCustomField?: boolean;
  isRequired?: boolean;
  isChecked?: boolean;
  isSortable?: boolean;
  sortDirection?: 'asc' | 'desc' | '' | null;
  sortName?:string;
  exportName?:string;
  exportFormatter?: (items: string) => string;
}

export interface ISortDefintion{
  fieldName: string;
  direction: 'asc' | 'desc' | null | '';
  isCustomField?: boolean;
  parentCustomFieldId?:string;
}


