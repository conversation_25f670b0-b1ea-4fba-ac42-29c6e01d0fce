<template>
  <div
    id="chat-connect"
    class="launchpad-item-sub-container launchpad-bottom-border bg-white my-1"
  >
    <div class="items-tiles mx-3 my-2">
       <img
        class="integration-icon mx-4"
        :class="{ hl_disable_action: isLeadConnectorConnected }"
        src="@/assets/pmd/img/launchpad/icon-android-ios.png"
      />
      <div class="item-info">
        <div
          class="font-normal text-sm text-gray-500"
          v-if="isLeadConnectorConnected"
        >
          Mobile app is now installed!
        </div>
        <div
          class="font-normal text-sm text-gray-500"
          v-else-if="!isLeadConnectorConnected"
        >
          Download our app and engage with your leads on the go!
        </div>
      </div>
      <div class="flex flex-col justify-center item-end content-end w-40">
        <div
          class="flex flex-row justify-end item-end content-end w-full pr-4 pl-4"
        >
          <button
            id="launchpad-lc-sendlink"
            v-if="!sending && !mobileAppChecking"
            class="green-btn"
            :class="{
              'green-filled-button green-btn connetGoogle': !isLeadConnectorConnected,
              'btn btn-circle success-green-btn': isLeadConnectorConnected,
            }"
            @click="showPhoneNumberModal = true"
          >
            <span v-show="!isLeadConnectorConnected">Send Link</span>
            <i v-show="isLeadConnectorConnected" class="fa fa-check"></i>
          </button>
          <moon-loader
            v-if="sending || mobileAppChecking"
            :loading="sending || mobileAppChecking"
            color="#37ca37"
            size="30px"
          />
        </div>
      </div>
    </div>
    <lead-connector-modal
      :email="user.email"
      :phone="user.phone"
      :shortenedUrl="shortenedUrl"
      :mobileError="mobileLinkError"
      :modalShow="showModal || mobileLinkError"
      :company="company"
    />
    <lead-connector-mobile-modal
      :phone="phone"
      :modalShow="showPhoneNumberModal"
      :loader="sending"
    />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { Location, User, Company } from '@/models'
import { trackGaEvent } from '@/util/helper'

import LeadConnectorModal from './LeadConnectorModal.vue'
import LeadConnectorIcon from '@/assets/pmd/img/launchpad/icon-lead-connector.svg'
import LeadConnectorMobileModal from './LeadConnectorMobileModal.vue'

export default Vue.extend({
  props: [
    'currentLocationId',
    'userid',
    'activePlan',
    'mobileLinkError',
    'shortenUrl',
    'isLeadConnectorConnected',
    'mobileAppChecking',
  ],
  components: {
    LeadConnectorModal,
    LeadConnectorIcon,
    LeadConnectorMobileModal,
  },
  data() {
    return {
      sending: false,
      shortenedUrl: '',
      showModal: false,
      location: {} as Location,
      user: {} as User,
      showPhoneNumberModal: false,
      phone: '',
      company: {} as Company,
    }
  },
  async mounted() {
    this.user = await User.getById(this.userid)
    this.company = await Company.getById(this.user.companyId)
    this.phone = this.user.phone
    this.location = new Location(
      await this.$store.dispatch(
        'locations/getCurrentLocation',
        this.currentLocationId
      )
    )
    this.$root.$on('close-modal', () => {
      this.showModal = false
      this.showPhoneNumberModal = false
      if (this.mobileLinkError) this.$root.$emit('hide-mobile-error')
    })
    this.$root.$on('send-link', (phoneNumber: string) => {
      this.savePhoneAndSendLink(phoneNumber)
    })
  },
  watch: {
    shortenUrl: function (url) {
      this.shortenedUrl = url
    },
  },
  methods: {
    async sendLink() {
      try {
        const body = {
          location_id: this.location.id,
          type: 'mobile_app_link',
          user_id: this.userid,
        }

        const response = await this.$http.post('/launchpad/reminder', body)
        if (response.status == 200) {
          const data = response.data
          this.shortenedUrl = data.sms?.shortened_url
          this.showModal = true
          trackGaEvent(
            'Launchpad',
            'Sent mobile app link',
            `Sent mobile app link for location: ${this.currentLocationId}`
          )
        }
      } catch (err) {
        console.log(err)
      }
      this.showPhoneNumberModal = false
    },
    async savePhoneAndSendLink(phoneNumber: string) {
      this.sending = true
      try {
        this.user.phone = phoneNumber
        await this.user.save()
        await this.sendLink()
        this.phone = this.user.phone
      } catch (err) {
        console.log(err)
      }
      this.sending = false
    },
  },
})
</script>
<style>
.mobile-container {
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: start;
  padding: 27px 30px;
  text-align: start;
  width: 100%;
}
.mobile-headsup-logo {
  width: 200px;
  height: 200px;
  margin-bottom: 10px;
  margin-top: 20px;
}
.mobile-sub-title {
  font-family: Roboto;
  font-style: normal;
  font-weight: 400;
  font-size: 18px;
  line-height: 157.19%;
  color: #111827;
}
button:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}
.mobile-form {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  align-items: center;
}
</style>
