<template>
    <div class="card">
        <div class="card-body pb-0">
            <HLTable
            :items="items"
            :fields="fields"
            :exportItem="exportItems"
            :currentPage="currentPage"
            :perPage="itemPerPage"
            :rows="rows"
            :isBusy="false"
            :defaultPerPage="itemPerPage"
            :headerOptions="headerOptions"
            :isExternelDownload="true"
            :isShowFilter="false"
            :isPerPageOption="true"
            :detailsPagination="true"
            :hasTitle="false"
            :title="title"
            titlePosition="center"
        />
        </div>
    </div>
</template>

<script lang="ts">
    import Vue from 'vue'
    const HLTable = () => import('./../HLTable')

    export default Vue.extend({
        components: {
            HLTable,
        },
        data() {
            return {
                items: [
                    {
                        serial_number: 1,
                        timestamp: '30 April 2011 11:53AM',
                        mode: 'call',
                        agent: '<PERSON>',
                        contact: '<PERSON>',
                        status: 'concluded'
                    },
                    {
                        serial_number: 2,
                        timestamp: '30 April 2011 11:53AM',
                        mode: 'call',
                        agent: '<PERSON>',
                        contact: '<PERSON>',
                        status: 'concluded'
                    },
                    {
                        serial_number: 3,
                        timestamp: '30 April 2011 11:53AM',
                        mode: 'call',
                        agent: '<PERSON>',
                        contact: 'Josh Mayer',
                        status: 'concluded'
                    }
                ],
                currentPage: 1,
                itemPerPage: 25,
                rows: 3,
                showGraph: false,
                headerOptions: {
                    alwaysShowHeader: false,
                    isExportOption: false
                },
                exportItems: [],
                /* emailReportingUrl: defaults.emailReportingUrl,
                baseUrl: defaults.baseUrl, */
                fields: [
                    /* { key: 'first_name', label: 'First Name', sortable: false, checked: true }, */
                    { key: 'serial_number', label: 'S.No', checked: true, sortable: false },
                    { key: 'timestamp', label: 'TimeStamp', sortable: false, checked: true },
                    { key: 'mode', label: 'Mode', sortable: false, checked: true },
                    { key: 'agent', label: 'Agent', sortable: false, checked: true },
                    { key: 'contact', label: 'Contact', sortable: false, checked: true },
                    { key: 'status', label: 'Status', sortable: false, checked: true }
                ],
                contactIds: [],
                contacts: {},
                title: 'SMS Stats Detail',
                showCount: true,
                chartData: [],
                chartInnerData: [],
                chartDataLoading: false
            }
        },
    })
</script>

<style scoped>

</style>