<template>
  <div id="yext_shop">
    <template v-if="location && company">
      <template v-if="location.country != 'US' || company.country != 'US'">
        <YextDialogComponent dialogType="non_us_location" />
      </template>

      <template
        v-else-if="
          !company.stripeConnectId || !location.yextReseller.location_enabled
        "
      >
        <YextDialogComponent
          :contactEmail="company.email"
          :contactName="company.name"
          dialogType="no_stripe_or_agency_disabled"
        />
      </template>
      <template v-else-if="shopState === `PITCH`">
        <YextPitchComponent
          @callToActionClicked="shopState = `YEXT_PAYMENT`"
          :location="location"
          :company="company"
        />
      </template>
      <template v-else-if="shopState === `YEXT_PAYMENT`">
        <YextPaymentComponent
          @paymentComplete="paymentComplete($event)"
          @cancel="shopState = `PITCH`"
          :location="location"
          :company="company"
          :user="user"
        />
      </template>
      <template v-else-if="shopState === `ACCOUNT_CREATION`">
        <YextAccountCreationComponent
          @accountCreated="accountCreated"
          :location="location"
        />
      </template>
      <template v-else-if="shopState === `PENDING_ON_YEXT`">
        <YextStatusComponent :location="location" />
      </template>
      <div
        id="backdrop"
        v-if="shopState != `ACCOUNT_CREATION` && shopState != `YEXT_PAYMENT`"
      >
        <YextPreSaleDemoComponent />
      </div>
    </template>
    <div v-else id="backdrop">
      <YextPreSaleDemoComponent />
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import { Company, Location, User } from '@/models'
import { CompanyState, UserState } from '@/store/state_models'

import YextPitchComponent from '@/pmd/components/online_listing/YextPitchComponent.vue'
import YextPreSaleDemoComponent from '@/pmd/components/online_listing/YextPreSaleDemoComponent.vue'
import YextAccountCreationComponent from '@/pmd/components/online_listing/YextAccountCreationComponent.vue'
import YextStatusComponent from '@/pmd/components/online_listing/YextStatusComponent.vue'
import YextPaymentComponent from '@/pmd/components/online_listing/YextPaymentComponent.vue'
import YextDialogComponent from '@/pmd/components/online_listing/YextDialogComponent.vue'
import moment from 'moment-timezone'
import firebase from 'firebase/app'

export default Vue.extend({
  components: {
    YextPreSaleDemoComponent,
    YextPitchComponent,
    YextAccountCreationComponent,
    YextStatusComponent,
    YextPaymentComponent,
    YextDialogComponent,
  },
  data: function () {
    return {
      currentLocationId: '',
      location: null,
      shopState: `PITCH`,
    }
  },
  async created() {
    this.initShop()
  },
  computed: {
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.initShop()
    },
  },
  methods: {
    async initShop() {
      this.shopState = `PITCH`
      this.currentLocationId = this.$router.currentRoute.params.location_id
      this.location = new Location(
        await this.$store.dispatch(
          'locations/getCurrentLocation',
          this.currentLocationId
        )
      )
      if (this.location && this.location.yextReseller.status && this.location.yextReseller.status != `UNSUBSCRIBED` ) {
        this.shopState = `PENDING_ON_YEXT`
      } else if (
        this.location &&
        this.location.yextReseller.payment_status === 'COMPLETE'
      ) {
        this.shopState = `ACCOUNT_CREATION`
      }

      if (
        this.location.yextReseller.status &&
        this.location.yextReseller.status != 'COMPLETE'
      ) {
        this.updateListingStatus()
      }
      try {
        console.log({
          'Agency has Connected Stripe': !!this.company.stripeConnectId,
          'Agency has enabled YEXT for this location':
            this.location.yextReseller.location_enabled,
          'Location is from US': this.location.country === 'US',
          'Company is from US': this.company.country === 'US',
        })
      } catch {}
    },
    async accountCreated() {
      this.shopState = `PENDING_ON_YEXT`
      this.location = new Location(
        await this.$store.dispatch(
          'locations/getCurrentLocation',
          this.currentLocationId
        )
      )
      this.updateListingStatus()
    },
    async updateListingStatus() {
      let response = await this.$http.post(`/yext/update_listing_status`, {
        location_id: this.currentLocationId,
      })
      await this.delay(3000) // need to remove this 
      this.location = new Location(
        await this.$store.dispatch(
          'locations/getCurrentLocation',
          this.currentLocationId
        )
      )
      this.$emit(`statusUpdate`);
    },
    async paymentComplete(subData) {
      this.shopState = `ACCOUNT_CREATION`
      if (!this.location.yextReseller.meta) {
        this.location.yextReseller.meta = {}
      }
      this.location.yextReseller.meta.user_id = this.user.id
      this.location.yextReseller.meta.purchase_date = moment().utc().format()
      this.location.yextReseller.meta.ip = subData.ip || ''
      if (subData.amount > -1) {
        this.location.yextReseller.meta.location_sell_amount = subData.amount
      }
      this.location.yextReseller.meta.sell_currency = subData.currency || ''
      this.location.yextReseller.meta.ip = subData.ip || ''
      this.location.yextReseller.payment_status = 'COMPLETE'
      this.location.yextReseller.subscription_id =
        subData.location_subscription_id
      this.location.yextReseller.price_id = subData.price_id
      await this.location.ref.update({
        'reseller.yext': this.location.yextReseller,
        date_updated: firebase.firestore.FieldValue.serverTimestamp(),
      })
    },
    async delay(time: number) {
      return new Promise(resolve => {
        setTimeout(resolve, time)
      })
    },
  },
})
</script>
<style lang="scss" scoped>
#yext_shop {
  z-index: 1;
  position: relative;
  padding: 15px;
  overflow: hidden;
  #backdrop {
    z-index: -1;
  }
  #backdrop::after {
    content: '';
    width: 100%;
    height: 100%;
    background: #ffffffab;
    z-index: 2;
    position: absolute;
    top: 0;
    left: 0;
  }
}
</style>