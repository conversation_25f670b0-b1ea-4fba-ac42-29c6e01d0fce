fetch("https://backend.leadconnectorhq.com/locations/ErcZjGpxMqLYjK1UwRwe/customFields/search?parentId=&skip=0&limit=10000&documentType=folder&model=all&query=&includeStandards=true", {
    "headers": {
      "accept": "application/json, text/plain, */*",
      "accept-language": "en-US,en;q=0.9",
      "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzb3VyY2UiOiJXRUJfVVNFUiIsImNoYW5uZWwiOiJBUFAiLCJzb3VyY2VJZCI6IlVQYkRSeUxWckpoVHhGQ0JScHZkIiwic291cmNlTmFtZSI6IkFsIEFtaW5Jc2xhbSIsImNvbXBhbnlJZCI6Im85YTlhekVGQjlSNHVPNUJ2Q0FGIiwibWV0YSI6eyJ1c2VyUm9sZSI6ImFkbWluIiwidXNlclR5cGUiOiJhZ2VuY3kifSwicHJpbWFyeVVzZXIiOnt9LCJpYXQiOjE3NDk2ODI0MzUsImV4cCI6MTc0OTY4NjAzNSwianRpIjoiNjgzOWY5N2RhYjE0Zjg5ODcyZTI5NWJkIn0.Uzbc5Z5r_z6DulTuXCBpvENwv-LS6F_NlHUpTHJazAw",
      "cache-control": "no-cache",
      "channel": "APP",
      "source": "WEB_USER",
      "token-id": "eyJhbGciOiJSUzI1NiIsImtpZCI6ImE0YTEwZGVjZTk4MzY2ZDZmNjNlMTY3Mjg2YWU5YjYxMWQyYmFhMjciLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LJ1OgKIXqbT20Kk6cPThB878JhE7OgDYW_xAiG46dgo9sVs0bAdWix6ir6WV1ValZXFNeXLYknxSTtxBx8N5petfdlMsayI2R_X-HZTVxhN_RVILyCYH5DdG8iaxAnrGJR8mI5osJqOtjWsal6JmrNmMQ3M9UzqXYY1k9-vLxMBHFC6R7eZY-NxejCq8ucMfp46v3ysZlN1LMpszSsGUautj2fIcyReZCFVF_qdK4NwBf4WxTqgHemrRC-ENOC3N0NUBOuMNV5-38fbrykbUTumY_hdmpC0vq-SiX70nIrIHBTqfczGwsoG-t2xwM6EtbCMUcrsM8OuNNePkMXbTXg"
    },
    "referrer": "https://app.gohighlevel.com/",
    "referrerPolicy": "strict-origin-when-cross-origin",
    "body": null,
    "method": "GET",
    "mode": "cors",
    "credentials": "include"
  });

// Response
//   {"customFieldFolders":[{"_id":"N073x9Gzce0t59WHgjq5","dateAdded":"2025-05-05T23:47:50.731Z","documentType":"folder","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","position":100,"standard":true,"standardFieldsFolder":true,"fieldsCount":7,"id":"N073x9Gzce0t59WHgjq5","name":"Contact"},{"_id":"SRVrqGapbV6McIR2yaIZ","dateAdded":"2025-05-05T23:47:51.003Z","documentType":"folder","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"opportunity","position":100,"standard":true,"standardFieldsFolder":true,"fieldsCount":8,"id":"SRVrqGapbV6McIR2yaIZ","name":"Opportunity Details"},{"_id":"qOPBX0q1XT5hbcCdJvrw","dateAdded":"2025-05-05T23:47:50.780Z","documentType":"folder","generalFieldsFolder":true,"locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","position":200,"standard":true,"fieldsCount":8,"id":"qOPBX0q1XT5hbcCdJvrw","name":"General Info"},{"_id":"bmnMvQWDh6zBUXjBteSY","customFieldsFolder":true,"dateAdded":"2025-05-05T23:47:50.824Z","documentType":"folder","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","position":300,"standard":true,"fieldsCount":0,"id":"bmnMvQWDh6zBUXjBteSY","name":"Additional Info"},{"_id":"NmhMwTgWhb2zJLgk2hSa","dateAdded":"2025-05-05T23:47:50.866Z","documentType":"folder","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"business","position":350,"standard":true,"fieldsCount":10,"id":"NmhMwTgWhb2zJLgk2hSa","name":"Company Info"}],"totalItems":5,"traceId":"ae2397d8-22ee-4532-a1a4-d65879a7a2cb"}


fetch("https://backend.leadconnectorhq.com/locations/ErcZjGpxMqLYjK1UwRwe/customFields/search?parentId=&skip=0&limit=10000&documentType=field&model=all&query=&includeStandards=true", {
    "headers": {
      "accept": "application/json, text/plain, */*",
      "accept-language": "en-US,en;q=0.9",
      "authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzb3VyY2UiOiJXRUJfVVNFUiIsImNoYW5uZWwiOiJBUFAiLCJzb3VyY2VJZCI6IlVQYkRSeUxWckpoVHhGQ0JScHZkIiwic291cmNlTmFtZSI6IkFsIEFtaW5Jc2xhbSIsImNvbXBhbnlJZCI6Im85YTlhekVGQjlSNHVPNUJ2Q0FGIiwibWV0YSI6eyJ1c2VyUm9sZSI6ImFkbWluIiwidXNlclR5cGUiOiJhZ2VuY3kifSwicHJpbWFyeVVzZXIiOnt9LCJpYXQiOjE3NDk2ODI0MzUsImV4cCI6MTc0OTY4NjAzNSwianRpIjoiNjgzOWY5N2RhYjE0Zjg5ODcyZTI5NWJkIn0.Uzbc5Z5r_z6DulTuXCBpvENwv-LS6F_NlHUpTHJazAw",
      "cache-control": "no-cache",
      "channel": "APP",
      "source": "WEB_USER",
      "token-id": "eyJhbGciOiJSUzI1NiIsImtpZCI6ImE0YTEwZGVjZTk4MzY2ZDZmNjNlMTY3Mjg2YWU5YjYxMWQyYmFhMjciLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LJ1OgKIXqbT20Kk6cPThB878JhE7OgDYW_xAiG46dgo9sVs0bAdWix6ir6WV1ValZXFNeXLYknxSTtxBx8N5petfdlMsayI2R_X-HZTVxhN_RVILyCYH5DdG8iaxAnrGJR8mI5osJqOtjWsal6JmrNmMQ3M9UzqXYY1k9-vLxMBHFC6R7eZY-NxejCq8ucMfp46v3ysZlN1LMpszSsGUautj2fIcyReZCFVF_qdK4NwBf4WxTqgHemrRC-ENOC3N0NUBOuMNV5-38fbrykbUTumY_hdmpC0vq-SiX70nIrIHBTqfczGwsoG-t2xwM6EtbCMUcrsM8OuNNePkMXbTXg"
    },
    "referrer": "https://app.gohighlevel.com/",
    "referrerPolicy": "strict-origin-when-cross-origin",
    "body": null,
    "method": "GET",
    "mode": "cors",
    "credentials": "include"
  });

  // Response
  // {"customFields":[{"_id":"l67drUnxR2WgulC7s0PQ","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:50.871Z","documentType":"field","fieldKey":"contact.first_name","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","parentId":"N073x9Gzce0t59WHgjq5","position":0,"standard":true,"fieldsCount":0,"id":"l67drUnxR2WgulC7s0PQ","name":"First Name"},{"_id":"DP7pp2Mm95veeLGI5RBf","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:50.871Z","documentType":"field","fieldKey":"contact.last_name","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","parentId":"N073x9Gzce0t59WHgjq5","position":50,"standard":true,"fieldsCount":0,"id":"DP7pp2Mm95veeLGI5RBf","name":"Last Name"},{"_id":"id6Sc44cN3hfsJAt7EeW","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:50.871Z","documentType":"field","fieldKey":"contact.email","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","parentId":"N073x9Gzce0t59WHgjq5","position":100,"standard":true,"fieldsCount":0,"id":"id6Sc44cN3hfsJAt7EeW","name":"Email"},{"_id":"i7rU9rN1ruRe2MPQ9iEt","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:50.871Z","documentType":"field","fieldKey":"contact.phone","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","parentId":"N073x9Gzce0t59WHgjq5","position":150,"standard":true,"fieldsCount":0,"id":"i7rU9rN1ruRe2MPQ9iEt","name":"Phone"},{"_id":"LR3aLgAXUkHX897uJOi8","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:50.871Z","documentType":"field","fieldKey":"contact.date_of_birth","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","parentId":"N073x9Gzce0t59WHgjq5","position":200,"standard":true,"fieldsCount":0,"id":"LR3aLgAXUkHX897uJOi8","name":"Date Of Birth"},{"_id":"a58kJtrb9ZvbKiKByUMN","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:50.871Z","documentType":"field","fieldKey":"contact.source","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","parentId":"N073x9Gzce0t59WHgjq5","position":250,"standard":true,"fieldsCount":0,"id":"a58kJtrb9ZvbKiKByUMN","name":"Contact Source"},{"_id":"56wQeb6wiWIlgQe5FXyJ","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:50.871Z","documentType":"field","fieldKey":"contact.type","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","parentId":"N073x9Gzce0t59WHgjq5","position":300,"standard":true,"fieldsCount":0,"id":"56wQeb6wiWIlgQe5FXyJ","name":"Contact Type","picklistOptions":[{"value":"lead","name":"Lead"},{"value":"customer","name":"Customer"}]},{"_id":"cKpyQXpF7YNKw2mw3AEQ","dataType":"TEXT","dateAdded":"2025-05-05T23:47:51.274Z","documentType":"field","fieldKey":"business.name","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"business","parentId":"NmhMwTgWhb2zJLgk2hSa","position":1,"standard":true,"fieldsCount":0,"id":"cKpyQXpF7YNKw2mw3AEQ","name":"Company Name"},{"_id":"K9vn1MzqV2A8F5dUa4CA","dataType":"PHONE","dateAdded":"2025-05-05T23:47:51.635Z","documentType":"field","fieldKey":"business.phone","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"business","parentId":"NmhMwTgWhb2zJLgk2hSa","position":2,"standard":true,"fieldsCount":0,"id":"K9vn1MzqV2A8F5dUa4CA","name":"Phone"},{"_id":"encdCGgFj58kBMM5LTLI","dataType":"EMAIL","dateAdded":"2025-05-05T23:47:51.987Z","documentType":"field","fieldKey":"business.email","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"business","parentId":"NmhMwTgWhb2zJLgk2hSa","position":3,"standard":true,"fieldsCount":0,"id":"encdCGgFj58kBMM5LTLI","name":"Email"},{"_id":"3jSg4CwIjbI2TPTU5pY6","dataType":"TEXT","dateAdded":"2025-05-05T23:47:52.342Z","documentType":"field","fieldKey":"business.website","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"business","parentId":"NmhMwTgWhb2zJLgk2hSa","position":4,"standard":true,"fieldsCount":0,"id":"3jSg4CwIjbI2TPTU5pY6","name":"Website"},{"_id":"rBHcePUIXKMPevxk1C2U","dataType":"TEXT","dateAdded":"2025-05-05T23:47:52.688Z","documentType":"field","fieldKey":"business.address","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"business","parentId":"NmhMwTgWhb2zJLgk2hSa","position":5,"standard":true,"fieldsCount":0,"id":"rBHcePUIXKMPevxk1C2U","name":"Address"},{"_id":"2nXZjeOCMsNvE8ABezlY","dataType":"TEXT","dateAdded":"2025-05-05T23:47:53.052Z","documentType":"field","fieldKey":"business.state","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"business","parentId":"NmhMwTgWhb2zJLgk2hSa","position":6,"standard":true,"fieldsCount":0,"id":"2nXZjeOCMsNvE8ABezlY","name":"State"},{"_id":"5JL0TPynnmuIEPYMTTIp","dataType":"TEXT","dateAdded":"2025-05-05T23:47:53.403Z","documentType":"field","fieldKey":"business.city","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"business","parentId":"NmhMwTgWhb2zJLgk2hSa","position":7,"standard":true,"fieldsCount":0,"id":"5JL0TPynnmuIEPYMTTIp","name":"City"},{"_id":"A6Ia5WhnVZgie7Is18Ne","dataType":"LARGE_TEXT","dateAdded":"2025-05-05T23:47:53.772Z","documentType":"field","fieldKey":"business.description","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"business","parentId":"NmhMwTgWhb2zJLgk2hSa","position":8,"standard":true,"fieldsCount":0,"id":"A6Ia5WhnVZgie7Is18Ne","name":"Description"},{"_id":"M2bhuNumkvdSE2F819d2","dataType":"TEXT","dateAdded":"2025-05-05T23:47:54.141Z","documentType":"field","fieldKey":"business.postalcode","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"business","parentId":"NmhMwTgWhb2zJLgk2hSa","position":9,"standard":true,"fieldsCount":0,"id":"M2bhuNumkvdSE2F819d2","name":"Postal Code"},{"_id":"YF0VOBIbP9XNJhNljFIx","dataType":"SINGLE_OPTIONS","dateAdded":"2025-05-05T23:47:54.486Z","documentType":"field","fieldKey":"business.country","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"business","options":[{"key":"af","label":"Afghanistan"},{"key":"ax","label":"Aland Islands"},{"key":"al","label":"Albania"},{"key":"dz","label":"Algeria"},{"key":"as","label":"American Samoa"},{"key":"ad","label":"AndorrA"},{"key":"ao","label":"Angola"},{"key":"ai","label":"Anguilla"},{"key":"aq","label":"Antarctica"},{"key":"ag","label":"Antigua and Barbuda"},{"key":"ar","label":"Argentina"},{"key":"am","label":"Armenia"},{"key":"aw","label":"Aruba"},{"key":"au","label":"Australia"},{"key":"at","label":"Austria"},{"key":"az","label":"Azerbaijan"},{"key":"bs","label":"Bahamas"},{"key":"bh","label":"Bahrain"},{"key":"bd","label":"Bangladesh"},{"key":"bb","label":"Barbados"},{"key":"by","label":"Belarus"},{"key":"be","label":"Belgium"},{"key":"bz","label":"Belize"},{"key":"bj","label":"Benin"},{"key":"bm","label":"Bermuda"},{"key":"bt","label":"Bhutan"},{"key":"bo","label":"Bolivia"},{"key":"ba","label":"Bosnia and Herzegovina"},{"key":"bw","label":"Botswana"},{"key":"bv","label":"Bouvet Island"},{"key":"br","label":"Brazil"},{"key":"io","label":"British Indian Ocean Territory"},{"key":"bn","label":"Brunei Darussalam"},{"key":"bg","label":"Bulgaria"},{"key":"bf","label":"Burkina Faso"},{"key":"bi","label":"Burundi"},{"key":"kh","label":"Cambodia"},{"key":"cm","label":"Cameroon"},{"key":"ca","label":"Canada"},{"key":"cv","label":"Cape Verde"},{"key":"ky","label":"Cayman Islands"},{"key":"cf","label":"Central African Republic"},{"key":"td","label":"Chad"},{"key":"cl","label":"Chile"},{"key":"cn","label":"China"},{"key":"cx","label":"Christmas Island"},{"key":"cc","label":"Cocos (Keeling) Islands"},{"key":"co","label":"Colombia"},{"key":"km","label":"Comoros"},{"key":"cg","label":"Congo"},{"key":"cd","label":"Congo, The Democratic Republic of the"},{"key":"ck","label":"Cook Islands"},{"key":"cr","label":"Costa Rica"},{"key":"ci","label":"Cote D\"Ivoire"},{"key":"hr","label":"Croatia"},{"key":"cu","label":"Cuba"},{"key":"cy","label":"Cyprus"},{"key":"cz","label":"Czech Republic"},{"key":"dk","label":"Denmark"},{"key":"dj","label":"Djibouti"},{"key":"dm","label":"Dominica"},{"key":"do","label":"Dominican Republic"},{"key":"ec","label":"Ecuador"},{"key":"eg","label":"Egypt"},{"key":"sv","label":"El Salvador"},{"key":"gq","label":"Equatorial Guinea"},{"key":"er","label":"Eritrea"},{"key":"ee","label":"Estonia"},{"key":"et","label":"Ethiopia"},{"key":"fk","label":"Falkland Islands (Malvinas)"},{"key":"fo","label":"Faroe Islands"},{"key":"fj","label":"Fiji"},{"key":"fi","label":"Finland"},{"key":"fr","label":"France"},{"key":"gf","label":"French Guiana"},{"key":"pf","label":"French Polynesia"},{"key":"tf","label":"French Southern Territories"},{"key":"ga","label":"Gabon"},{"key":"gm","label":"Gambia"},{"key":"ge","label":"Georgia"},{"key":"de","label":"Germany"},{"key":"gh","label":"Ghana"},{"key":"gi","label":"Gibraltar"},{"key":"gr","label":"Greece"},{"key":"gl","label":"Greenland"},{"key":"gd","label":"Grenada"},{"key":"gp","label":"Guadeloupe"},{"key":"gu","label":"Guam"},{"key":"gt","label":"Guatemala"},{"key":"gg","label":"Guernsey"},{"key":"gn","label":"Guinea"},{"key":"gw","label":"Guinea-Bissau"},{"key":"gy","label":"Guyana"},{"key":"ht","label":"Haiti"},{"key":"hm","label":"Heard Island and Mcdonald Islands"},{"key":"va","label":"Holy See (Vatican City State)"},{"key":"hn","label":"Honduras"},{"key":"hk","label":"Hong Kong"},{"key":"hu","label":"Hungary"},{"key":"is","label":"Iceland"},{"key":"in","label":"India"},{"key":"id","label":"Indonesia"},{"key":"ir","label":"Iran, Islamic Republic Of"},{"key":"iq","label":"Iraq"},{"key":"ie","label":"Ireland"},{"key":"im","label":"Isle of Man"},{"key":"il","label":"Israel"},{"key":"it","label":"Italy"},{"key":"jm","label":"Jamaica"},{"key":"jp","label":"Japan"},{"key":"je","label":"Jersey"},{"key":"jo","label":"Jordan"},{"key":"kz","label":"Kazakhstan"},{"key":"ke","label":"Kenya"},{"key":"ki","label":"Kiribati"},{"key":"kp","label":"Korea, Democratic People\"S Republic"},{"key":"kr","label":"Korea, Republic of"},{"key":"xk","label":"Kosovo"},{"key":"kw","label":"Kuwait"},{"key":"kg","label":"Kyrgyzstan"},{"key":"la","label":"Lao People's Democratic Republic"},{"key":"lv","label":"Latvia"},{"key":"lb","label":"Lebanon"},{"key":"ls","label":"Lesotho"},{"key":"lr","label":"Liberia"},{"key":"ly","label":"Libyan Arab Jamahiriya"},{"key":"li","label":"Liechtenstein"},{"key":"lt","label":"Lithuania"},{"key":"lu","label":"Luxembourg"},{"key":"mo","label":"Macao"},{"key":"mk","label":"Macedonia, The Former Yugoslav Republic of"},{"key":"mg","label":"Madagascar"},{"key":"mw","label":"Malawi"},{"key":"my","label":"Malaysia"},{"key":"mv","label":"Maldives"},{"key":"ml","label":"Mali"},{"key":"mt","label":"Malta"},{"key":"mh","label":"Marshall Islands"},{"key":"mq","label":"Martinique"},{"key":"mr","label":"Mauritania"},{"key":"mu","label":"Mauritius"},{"key":"yt","label":"Mayotte"},{"key":"mx","label":"Mexico"},{"key":"fm","label":"Micronesia, Federated States of"},{"key":"md","label":"Moldova, Republic of"},{"key":"mc","label":"Monaco"},{"key":"mn","label":"Mongolia"},{"key":"me","label":"Montenegro"},{"key":"ms","label":"Montserrat"},{"key":"ma","label":"Morocco"},{"key":"mz","label":"Mozambique"},{"key":"mm","label":"Myanmar"},{"key":"na","label":"Namibia"},{"key":"nr","label":"Nauru"},{"key":"np","label":"Nepal"},{"key":"nl","label":"Netherlands"},{"key":"an","label":"Netherlands Antilles"},{"key":"nc","label":"New Caledonia"},{"key":"nz","label":"New Zealand"},{"key":"ni","label":"Nicaragua"},{"key":"ne","label":"Niger"},{"key":"ng","label":"Nigeria"},{"key":"nu","label":"Niue"},{"key":"nf","label":"Norfolk Island"},{"key":"mp","label":"Northern Mariana Islands"},{"key":"no","label":"Norway"},{"key":"om","label":"Oman"},{"key":"pk","label":"Pakistan"},{"key":"pw","label":"Palau"},{"key":"ps","label":"Palestinian Territory, Occupied"},{"key":"pa","label":"Panama"},{"key":"pg","label":"Papua New Guinea"},{"key":"py","label":"Paraguay"},{"key":"pe","label":"Peru"},{"key":"ph","label":"Philippines"},{"key":"pn","label":"Pitcairn"},{"key":"pl","label":"Poland"},{"key":"pt","label":"Portugal"},{"key":"pr","label":"Puerto Rico"},{"key":"qa","label":"Qatar"},{"key":"re","label":"Reunion"},{"key":"ro","label":"Romania"},{"key":"ru","label":"Russian Federation"},{"key":"rw","label":"Rwanda"},{"key":"sh","label":"Saint Helena"},{"key":"kn","label":"Saint Kitts and Nevis"},{"key":"lc","label":"Saint Lucia"},{"key":"mf","label":"Saint Martin"},{"key":"pm","label":"Saint Pierre and Miquelon"},{"key":"vc","label":"Saint Vincent and the Grenadines"},{"key":"ws","label":"Samoa"},{"key":"sm","label":"San Marino"},{"key":"st","label":"Sao Tome and Principe"},{"key":"sa","label":"Saudi Arabia"},{"key":"sn","label":"Senegal"},{"key":"rs","label":"Serbia"},{"key":"sc","label":"Seychelles"},{"key":"sl","label":"Sierra Leone"},{"key":"sg","label":"Singapore"},{"key":"sx","label":"Sint Maarten"},{"key":"sk","label":"Slovakia"},{"key":"si","label":"Slovenia"},{"key":"sb","label":"Solomon Islands"},{"key":"so","label":"Somalia"},{"key":"za","label":"South Africa"},{"key":"gs","label":"South Georgia and the South Sandwich Islands"},{"key":"es","label":"Spain"},{"key":"lk","label":"Sri Lanka"},{"key":"sd","label":"Sudan"},{"key":"sr","label":"Suriname"},{"key":"sj","label":"Svalbard and Jan Mayen"},{"key":"sz","label":"Swaziland"},{"key":"se","label":"Sweden"},{"key":"ch","label":"Switzerland"},{"key":"sy","label":"Syrian Arab Republic"},{"key":"tw","label":"Taiwan"},{"key":"tj","label":"Tajikistan"},{"key":"tz","label":"Tanzania, United Republic of"},{"key":"th","label":"Thailand"},{"key":"tl","label":"Timor-Leste"},{"key":"tg","label":"Togo"},{"key":"tk","label":"Tokelau"},{"key":"to","label":"Tonga"},{"key":"tt","label":"Trinidad and Tobago"},{"key":"tn","label":"Tunisia"},{"key":"tr","label":"Turkey"},{"key":"tm","label":"Turkmenistan"},{"key":"tc","label":"Turks and Caicos Islands"},{"key":"tv","label":"Tuvalu"},{"key":"ug","label":"Uganda"},{"key":"gb","label":"UK"},{"key":"ua","label":"Ukraine"},{"key":"ae","label":"United Arab Emirates"},{"key":"us","label":"United States"},{"key":"um","label":"United States Minor Outlying Islands"},{"key":"uy","label":"Uruguay"},{"key":"uz","label":"Uzbekistan"},{"key":"vu","label":"Vanuatu"},{"key":"ve","label":"Venezuela"},{"key":"vn","label":"Viet Nam"},{"key":"vg","label":"Virgin Islands, British"},{"key":"vi","label":"Virgin Islands, U.S."},{"key":"wf","label":"Wallis and Futuna"},{"key":"eh","label":"Western Sahara"},{"key":"ye","label":"Yemen"},{"key":"zm","label":"Zambia"},{"key":"zw","label":"Zimbabwe"}],"parentId":"NmhMwTgWhb2zJLgk2hSa","position":10,"standard":true,"fieldsCount":0,"id":"YF0VOBIbP9XNJhNljFIx","name":"Country"},{"_id":"vEGJhOZuHIOFD36TyMCx","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:51.054Z","documentType":"field","fieldKey":"opportunity.name","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"opportunity","parentId":"SRVrqGapbV6McIR2yaIZ","position":0,"standard":true,"fieldsCount":0,"id":"vEGJhOZuHIOFD36TyMCx","name":"Opportunity Name"},{"_id":"CQdngIpETPJ2zrxkD2GK","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:51.054Z","documentType":"field","fieldKey":"opportunity.pipeline_id","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"opportunity","parentId":"SRVrqGapbV6McIR2yaIZ","position":50,"standard":true,"fieldsCount":0,"id":"CQdngIpETPJ2zrxkD2GK","name":"Pipeline"},{"_id":"43PHGgVr0sMB0MXHVyDf","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:51.054Z","documentType":"field","fieldKey":"opportunity.pipeline_stage_id","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"opportunity","parentId":"SRVrqGapbV6McIR2yaIZ","position":100,"standard":true,"fieldsCount":0,"id":"43PHGgVr0sMB0MXHVyDf","name":"Stage"},{"_id":"hKMYvYR3ZRb81VeOSS9k","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:51.054Z","documentType":"field","fieldKey":"opportunity.status","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"opportunity","parentId":"SRVrqGapbV6McIR2yaIZ","position":150,"standard":true,"fieldsCount":0,"id":"hKMYvYR3ZRb81VeOSS9k","name":"Status"},{"_id":"SUMuLG8SPk50QbJrQpRF","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:51.054Z","documentType":"field","fieldKey":"opportunity.monetary_value","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"opportunity","parentId":"SRVrqGapbV6McIR2yaIZ","position":200,"standard":true,"fieldsCount":0,"id":"SUMuLG8SPk50QbJrQpRF","name":"Lead Value"},{"_id":"1xdpv15SsyUqoRVsw4Vg","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:51.054Z","documentType":"field","fieldKey":"opportunity.assigned_to","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"opportunity","parentId":"SRVrqGapbV6McIR2yaIZ","position":250,"standard":true,"fieldsCount":0,"id":"1xdpv15SsyUqoRVsw4Vg","name":"Opportunity Owner"},{"_id":"oAjOcsoUJOZl0cwLqdmB","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:51.054Z","documentType":"field","fieldKey":"opportunity.source","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"opportunity","parentId":"SRVrqGapbV6McIR2yaIZ","position":300,"standard":true,"fieldsCount":0,"id":"oAjOcsoUJOZl0cwLqdmB","name":"Opportunity Source"},{"_id":"4aGpsW8B3cD3NYOGzLLf","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:51.054Z","documentType":"field","fieldKey":"opportunity.lost_reason","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"opportunity","parentId":"SRVrqGapbV6McIR2yaIZ","position":350,"standard":true,"fieldsCount":0,"id":"4aGpsW8B3cD3NYOGzLLf","name":"Lost Reason"},{"_id":"mMOfR5V42RwxyMlB82v3","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:50.937Z","documentType":"field","fieldKey":"contact.company_name","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","parentId":"qOPBX0q1XT5hbcCdJvrw","position":0,"standard":true,"fieldsCount":0,"id":"mMOfR5V42RwxyMlB82v3","name":"Business Name"},{"_id":"WQpx8cPJPg0t5zhuEI6V","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:50.937Z","documentType":"field","fieldKey":"contact.address1","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","parentId":"qOPBX0q1XT5hbcCdJvrw","position":50,"standard":true,"fieldsCount":0,"id":"WQpx8cPJPg0t5zhuEI6V","name":"Street Address"},{"_id":"klDeJ7I0QlTo6vCpV5zs","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:50.937Z","documentType":"field","fieldKey":"contact.city","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","parentId":"qOPBX0q1XT5hbcCdJvrw","position":100,"standard":true,"fieldsCount":0,"id":"klDeJ7I0QlTo6vCpV5zs","name":"City"},{"_id":"J81OcGg3PFPPqImSZtEg","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:50.937Z","documentType":"field","fieldKey":"contact.country","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","parentId":"qOPBX0q1XT5hbcCdJvrw","position":150,"standard":true,"fieldsCount":0,"id":"J81OcGg3PFPPqImSZtEg","name":"Country"},{"_id":"NCI0U3bsj9L08n2xHu3d","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:50.937Z","documentType":"field","fieldKey":"contact.state","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","parentId":"qOPBX0q1XT5hbcCdJvrw","position":200,"standard":true,"fieldsCount":0,"id":"NCI0U3bsj9L08n2xHu3d","name":"State"},{"_id":"RDtpGlomFIqquMcpuiYq","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:50.937Z","documentType":"field","fieldKey":"contact.postal_code","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","parentId":"qOPBX0q1XT5hbcCdJvrw","position":250,"standard":true,"fieldsCount":0,"id":"RDtpGlomFIqquMcpuiYq","name":"Postal Code"},{"_id":"rzIkcJxOvARjf07sRyw3","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:50.937Z","documentType":"field","fieldKey":"contact.website","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","parentId":"qOPBX0q1XT5hbcCdJvrw","position":300,"standard":true,"fieldsCount":0,"id":"rzIkcJxOvARjf07sRyw3","name":"Website"},{"_id":"PeTL9xRnVT83tVO27HDc","dataType":"STANDARD_FIELD","dateAdded":"2025-05-05T23:47:50.937Z","documentType":"field","fieldKey":"contact.timezone","locationId":"ErcZjGpxMqLYjK1UwRwe","model":"contact","parentId":"qOPBX0q1XT5hbcCdJvrw","position":350,"standard":true,"fieldsCount":0,"id":"PeTL9xRnVT83tVO27HDc","name":"Time Zone"}],"totalItems":33,"traceId":"cfafd97c-cfbb-4623-8daf-bceee1313516"}