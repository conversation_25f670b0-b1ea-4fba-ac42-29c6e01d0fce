/**
 * User Selectors
 * 
 * Memoized selectors for user data, computed properties, and permission checks
 * Based on SPM User model computed getters and permission methods
 */

import { createSelector } from '@reduxjs/toolkit';
import type { RootState } from '../../index';
import type { IUser, IUserPermissionChecks, IUserComputedProperties } from '../../../types/user';
import { UserComputedUtils, UserPermissionUtils, UserLocationUtils } from '../../../utils/userUtils';

/**
 * Base selectors for user state
 */
export const selectCurrentUser = (state: RootState): IUser | null => 
  state.auth.user ? state.auth.user as unknown as IUser : null;

export const selectIsAuthenticated = (state: RootState): boolean => 
  state.auth.isAuthenticated;

export const selectAuthData = (state: RootState) => 
  state.auth.authData;

/**
 * Computed properties selectors
 * Based on SPM User model computed getters
 */
export const selectUserComputedProperties = createSelector(
  [selectCurrentUser],
  (user): IUserComputedProperties | null => {
    if (!user) return null;
    return UserComputedUtils.getComputedProperties(user);
  }
);

export const selectUserDisplayName = createSelector(
  [selectCurrentUser],
  (user): string => {
    if (!user) return 'Unknown User';
    return UserComputedUtils.getDisplayName(user);
  }
);

export const selectUserFullName = createSelector(
  [selectCurrentUser],
  (user): string => {
    if (!user) return '';
    return UserComputedUtils.getFullName(user);
  }
);

export const selectUserProfileColor = createSelector(
  [selectCurrentUser],
  (user): string => {
    if (!user) return '#afb8bc';
    return UserComputedUtils.getProfileColor(user);
  }
);

/**
 * Permission selectors
 * Based on SPM User model permission checking methods
 */
export const selectUserPermissions = createSelector(
  [selectCurrentUser],
  (user) => user?.permissions || {}
);

export const selectUserPermissionChecks = createSelector(
  [selectCurrentUser],
  (user): IUserPermissionChecks | null => {
    if (!user) return null;
    return UserPermissionUtils.getPermissionChecks(user);
  }
);

export const selectIsAdmin = createSelector(
  [selectCurrentUser],
  (user): boolean => {
    if (!user) return false;
    return UserPermissionUtils.isAdmin(user);
  }
);

export const selectCanAccessAll = createSelector(
  [selectCurrentUser],
  (user): boolean => {
    if (!user) return false;
    return UserPermissionUtils.canAccessAll(user);
  }
);

export const selectIsAssignedTo = createSelector(
  [selectCurrentUser],
  (user): boolean => {
    if (!user) return false;
    return UserPermissionUtils.isAssignedTo(user);
  }
);

export const selectCanViewOpportunities = createSelector(
  [selectCurrentUser],
  (user): boolean => {
    if (!user) return false;
    return UserPermissionUtils.canViewOpportunities(user);
  }
);

export const selectCanCreateReviewRequest = createSelector(
  [selectCurrentUser],
  (user): boolean => {
    if (!user) return false;
    return UserPermissionUtils.canCreateReviewRequest(user);
  }
);

export const selectCanAccessCampaigns = createSelector(
  [selectCurrentUser],
  (user): boolean => {
    if (!user) return false;
    return UserPermissionUtils.canAccessCampaigns(user);
  }
);

export const selectCanAccessWorkflows = createSelector(
  [selectCurrentUser],
  (user): boolean => {
    if (!user) return false;
    return UserPermissionUtils.canAccessWorkflows(user);
  }
);

export const selectCanAccessContacts = createSelector(
  [selectCurrentUser],
  (user): boolean => {
    if (!user) return false;
    return UserPermissionUtils.canAccessContacts(user);
  }
);

export const selectCanAccessSettings = createSelector(
  [selectCurrentUser],
  (user): boolean => {
    if (!user) return false;
    return UserPermissionUtils.canAccessSettings(user);
  }
);

/**
 * Location-related selectors
 * Based on SPM User model location management
 */
export const selectUserLocations = createSelector(
  [selectCurrentUser],
  (user) => user?.locations || {}
);

export const selectAccessibleLocationIds = createSelector(
  [selectCurrentUser],
  (user): string[] => {
    if (!user) return [];
    return UserLocationUtils.getAccessibleLocationIds(user);
  }
);

/**
 * Factory function to create location-specific selectors
 */
export const createLocationSelector = (locationId: string) => {
  return createSelector(
    [selectCurrentUser],
    (user): boolean => {
      if (!user) return false;
      return UserLocationUtils.hasLocationAccess(user, locationId);
    }
  );
};

/**
 * Factory function to create permission selectors
 */
export const createPermissionSelector = (permission: keyof IUser['permissions']) => {
  return createSelector(
    [selectCurrentUser],
    (user): boolean => {
      if (!user) return false;
      return UserPermissionUtils.hasPermission(user, permission);
    }
  );
};

/**
 * User role and type selectors
 */
export const selectUserRole = createSelector(
  [selectCurrentUser],
  (user) => user?.role || null
);

export const selectUserType = createSelector(
  [selectCurrentUser],
  (user) => user?.type || null
);

export const selectUserCompanyId = createSelector(
  [selectCurrentUser],
  (user) => user?.companyId || null
);

/**
 * User status selectors
 */
export const selectIsUserActive = createSelector(
  [selectCurrentUser],
  (user): boolean => user?.isActive === true
);

export const selectIsUserDeleted = createSelector(
  [selectCurrentUser],
  (user): boolean => user?.deleted === true
);

export const selectIsPasswordPending = createSelector(
  [selectCurrentUser],
  (user): boolean => user?.isPasswordPending === true
);

/**
 * User profile selectors
 */
export const selectUserEmail = createSelector(
  [selectCurrentUser],
  (user) => user?.email || null
);

export const selectUserPhone = createSelector(
  [selectCurrentUser],
  (user) => user?.phone || null
);

export const selectUserTimezone = createSelector(
  [selectCurrentUser],
  (user) => user?.timezone || null
);

export const selectUserProfileImage = createSelector(
  [selectCurrentUser],
  (user) => user?.profileImage || null
);

/**
 * Combined user info selector
 * Provides all commonly needed user information in one selector
 */
export const selectUserInfo = createSelector(
  [
    selectCurrentUser,
    selectUserComputedProperties,
    selectUserPermissionChecks,
    selectAccessibleLocationIds
  ],
  (user, computedProperties, permissionChecks, locationIds) => ({
    user,
    computedProperties,
    permissionChecks,
    locationIds,
    isAuthenticated: !!user,
    isAdmin: permissionChecks?.isAdmin || false,
    canAccessAll: permissionChecks?.canAccessAll || false
  })
);

/**
 * User session selectors
 */
export const selectLastLoginTime = createSelector(
  [selectCurrentUser],
  (user) => user?.lastLoginTime || null
);

export const selectDateAdded = createSelector(
  [selectCurrentUser],
  (user) => user?.dateAdded || null
);

export const selectDateUpdated = createSelector(
  [selectCurrentUser],
  (user) => user?.dateUpdated || null
);

/**
 * Audit trail selectors
 */
export const selectCreatedBy = createSelector(
  [selectCurrentUser],
  (user) => user?.createdBy || null
);

export const selectLastUpdatedBy = createSelector(
  [selectCurrentUser],
  (user) => user?.lastUpdatedBy || null
);

export const selectDeletedBy = createSelector(
  [selectCurrentUser],
  (user) => user?.deletedBy || null
);
