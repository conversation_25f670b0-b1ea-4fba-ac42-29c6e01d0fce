<template>
  <div class="power-dialer-main">
    <div class="power-dialer-header">
      <SearchBox
        :searching="searching"
        @search="filterUsers"
        @updateLabel="updateLabel"
      />
    </div>
    <div class="power-dialer-body mb-2" ref="contactList">
      <div class="custom-contact" v-if="!isUserSelected">
        <div class="label-header">
          <p>{{this.label}}</p>
        </div>
        <div  class="contact-list">
          <div class="contact-type">
            <div class="contactList">
              <div class="detail-box" v-for="user in filteredUsers" :key="user.id" @click="assignSelectedUser(user)">
              <span class="img-circle">
                <Avatar :contact="user" :include_name="false" />
              </span>
                <div class="user-info">
                  <p>{{user.fullName || 'unknown'}}</p>
                  <PhoneNumber
                    type="display"
                    v-model="user.phone"
                    :currentLocationId="currentLocationId"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="selectedList" v-else>
        <SelectedItem
          :currentLocationId="currentLocationId"
          :selectedItem="selectedUser"
          @removeAssignedItem="removeAssignedUser"
        />
      </div>
    </div>
    <div class="power-dialer-footer">
      <div class="footer-buttons">
        <button
          type="button"
          role="button" class="back-btn"  @click.prevent="$emit('backButton')">
          <svg
            width="23"
            height="18"
            viewBox="0 0 23 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M16.381 5.73866H3.65728L7.1416 2.31756C7.44464 2.02007 7.44908 1.53322 7.15156 1.23019C6.85407 0.927194 6.36726 0.922751 6.06418 1.22023L1.2302 5.96652C1.08321 6.11086 1.00026 6.30817 1 6.5142C0.999744 6.72022 1.08215 6.9177 1.22879 7.06243L6.06278 11.8332C6.21263 11.9811 6.40776 12.0548 6.60285 12.0548C6.80132 12.0548 6.99974 11.9785 7.15019 11.8261C7.44848 11.5238 7.44528 11.037 7.14305 10.7386L3.63498 7.27644H16.381C18.7961 7.27644 20.761 9.24131 20.761 11.6565C20.761 14.0716 18.7961 16.0365 16.381 16.0365C15.9563 16.0365 15.6121 16.3808 15.6121 16.8054C15.6121 17.2301 15.9563 17.5743 16.381 17.5743C19.6441 17.5743 22.2988 14.9196 22.2988 11.6565C22.2988 8.39333 19.6441 5.73866 16.381 5.73866Z"
              fill="#575C5F"
              stroke="#575C5F"
              stroke-width="0.5"
            />
          </svg>
        </button>
        <button
          v-if="callTabs.transferType === 'warm'"
          type="button"
          role="button"
          class="callandhold"
          :disabled="!isUserSelected"
          :class="{disabledBtn: !isUserSelected}"
          @click="callTransfer">
            <i>
              <svg width="23" height="23" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3.28524 8.76221L1.64239 9.30983L0.54715 10.9527V13.1432L2.19001 16.4289L4.38048 19.167L8.21382 21.9051L11.4995 22.4527L13.69 21.3574V19.167L10.9519 17.5241L8.76144 19.167L7.6662 18.6193L4.9281 16.4289L3.83286 13.6908L5.47572 11.5003L3.28524 8.76221Z" fill="white"/>
                <path d="M8.76144 1.09547V2.73833V9.30976L9.30906 10.9526L11.4995 11.5002V14.2383L14.2376 11.5002H21.9043L22.4519 9.85738V1.09547L20.2614 0.547852H9.85668L8.76144 1.09547Z" fill="white"/>
                <path d="M21.0141 0H10.1414C9.0462 0 8.1555 0.891322 8.1555 1.9865V10.1395C8.1555 11.2347 9.0462 12.126 10.1414 12.126H10.8728V14.2172C10.8728 14.4707 11.0258 14.7002 11.2598 14.7968C11.3375 14.8294 11.4191 14.8445 11.5 14.8445C11.6631 14.8445 11.8237 14.7805 11.9435 14.6607L14.4782 12.126H21.0141C22.1093 12.126 23 11.2347 23 10.1395V1.9865C23 0.891322 22.1093 0 21.0141 0ZM21.7455 10.1395C21.7455 10.5434 21.4174 10.8715 21.0141 10.8715H14.2185C14.0517 10.8715 13.8923 10.9374 13.775 11.0553L12.1272 12.7031V11.4987C12.1272 11.1525 11.8462 10.8715 11.5 10.8715H10.1414C9.73806 10.8715 9.41 10.5434 9.41 10.1395V1.9865C9.41 1.58255 9.73806 1.2545 10.1414 1.2545H21.0141C21.4174 1.2545 21.7455 1.58255 21.7455 1.9865V10.1395ZM13.7945 18.268L13.2576 17.9726C12.8781 17.7631 12.498 17.553 12.1342 17.3384C12.0815 17.3058 12.0256 17.2663 11.9673 17.2249C11.7151 17.0468 11.3695 16.8028 10.9468 16.8028C10.3446 16.8028 9.89424 17.3591 9.41753 17.9481C9.27577 18.1231 9.05435 18.3972 8.91698 18.5208C8.82039 18.4763 8.68678 18.3941 8.60462 18.3439L8.45157 18.2517C6.80504 17.3391 5.66281 16.1975 4.74201 14.5397L4.65231 14.3904C4.60151 14.3082 4.51871 14.1721 4.47606 14.0862C4.599 13.9488 4.87436 13.7261 5.05062 13.5837C5.64023 13.1064 6.19723 12.656 6.19723 12.0539C6.19723 11.6305 5.95197 11.2842 5.77321 11.0314C5.73306 10.9737 5.69355 10.9185 5.66658 10.8753C5.4577 10.5215 5.25761 10.1589 5.05689 9.79639L4.73511 9.21305C4.51683 8.76394 4.28788 8.34932 3.9385 8.23579C3.75095 8.15425 3.58473 8.15425 3.48563 8.15425C3.05847 8.15425 2.29573 8.32361 1.89618 8.50425C1.21373 8.79718 0.827343 9.49343 0.568289 9.95947C0.186293 10.6651 0 11.3501 0 12.0539C0 12.9295 0.287908 13.6929 0.567034 14.4305L0.703774 14.7962C0.949029 15.4749 1.22941 16.0651 1.56122 16.6008C2.63194 18.3339 4.66611 20.3687 6.39858 21.4394C6.93425 21.77 7.54205 22.0585 8.20192 22.2956L8.56447 22.4311C9.304 22.7108 10.068 23 10.9455 23C11.6499 23 12.3349 22.8143 13.0462 22.4286C13.5066 22.1733 14.2034 21.7869 14.4914 21.117C14.677 20.7068 14.8458 19.9434 14.8458 19.515C14.8458 19.4153 14.8458 19.2478 14.7843 19.1148C14.6526 18.7153 14.2411 18.4863 13.7945 18.268ZM13.3441 20.6096C13.2205 20.8975 12.7501 21.1584 12.4434 21.3284C11.919 21.6125 11.4429 21.7449 10.9461 21.7449C10.2982 21.7449 9.67219 21.5078 9.00919 21.2575L8.62971 21.1151C8.04824 20.9069 7.51947 20.6566 7.05844 20.3712C5.49785 19.4065 3.59351 17.5022 2.6288 15.9403C2.34278 15.4793 2.09941 14.9649 1.88363 14.3672L1.74125 13.9864C1.4916 13.3247 1.25513 12.6999 1.25513 12.0532C1.25513 11.5565 1.38748 11.0816 1.66911 10.5623C1.84161 10.2505 2.10317 9.78008 2.40237 9.65149C2.62253 9.55176 3.11492 9.43635 3.42165 9.41314C3.49065 9.52165 3.57407 9.69415 3.60669 9.76126L3.9592 10.4036C4.16557 10.7762 4.3713 11.1487 4.59147 11.522C4.63663 11.596 4.69183 11.6744 4.74891 11.7553C4.80285 11.8312 4.88502 11.9479 4.92328 12.0143C4.80411 12.1699 4.47041 12.4396 4.26154 12.6084C3.68948 13.0706 3.19521 13.4702 3.19521 14.0222C3.19521 14.4129 3.40973 14.7636 3.5816 15.0452L3.64997 15.1581C4.68556 17.0236 5.97895 18.3163 7.82996 19.3425L7.94726 19.4134C8.2314 19.5878 8.58454 19.8048 8.97657 19.8048C9.52918 19.8048 9.92937 19.3105 10.3923 18.7378C10.5598 18.5308 10.8257 18.2022 10.9813 18.0799C11.0578 18.1194 11.1694 18.1984 11.2428 18.2498C11.325 18.3082 11.4047 18.364 11.4875 18.4142C11.8688 18.6387 12.259 18.8551 12.6504 19.0709L13.2431 19.3952C13.3096 19.4278 13.4821 19.5125 13.5894 19.5802C13.5718 19.8562 13.4514 20.3731 13.3441 20.6096Z" fill="white"/>
                <path d="M18.9037 5.62097C19.1489 5.86623 19.1489 6.26265 18.9037 6.5079L16.9818 8.4298C16.8594 8.55211 16.6989 8.61358 16.5383 8.61358C16.3777 8.61358 16.2171 8.55211 16.0948 8.4298C15.8496 8.18454 15.8496 7.78812 16.0948 7.54287L16.946 6.69169H12.8588C12.512 6.69169 12.2316 6.41068 12.2316 6.06444C12.2316 5.71819 12.512 5.43719 12.8588 5.43719H16.946L16.0948 4.58601C15.8496 4.34075 15.8496 3.94433 16.0948 3.69908C16.3401 3.45382 16.7365 3.45382 16.9818 3.69908L18.9037 5.62097Z" fill="#FF9416"/>
              </svg>
            </i>
            <p>CALL & HOLD</p>
        </button>
        <button
          v-if="callTabs.transferType === 'blind'"
          type="button"
          role="button"
          class="transferend"
          :disabled="!isUserSelected || callTabs.callTransferring"
          :class="{disabledBtn: !isUserSelected || callTabs.callTransferring}"
          @click="callTransfer"
        >
          <i>
            <svg width="25" height="19" viewBox="0 0 25 19" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M4.30699 6.56025C4.33072 6.46206 4.3696 6.29994 4.40189 6.20373C4.42561 6.20241 4.45527 6.20175 4.49019 6.20175C4.69909 6.20175 4.94885 6.22943 5.2019 6.25645C5.5024 6.28808 5.81345 6.32103 6.09813 6.32103C6.41708 6.32103 6.87706 6.28479 7.19008 5.97111C7.50441 5.65677 7.57954 5.21722 7.63489 4.89695C7.64741 4.82381 7.65928 4.75395 7.67114 4.70189C7.77921 4.28343 7.89981 3.86498 8.0204 3.44652L8.2148 2.77369C8.38614 2.27813 8.52387 1.80036 8.34858 1.45505C8.27082 1.25867 8.14891 1.1361 8.07378 1.06032C7.75615 0.742682 7.06355 0.301158 6.63257 0.139706C6.40258 0.0481062 6.1436 0.00131788 5.86353 0.00131788C5.41937 0.00131788 4.99893 0.11071 4.56532 0.2346C3.7574 0.474473 3.11027 0.845484 2.58703 1.36806C1.93661 2.01915 1.58471 2.79939 1.24335 3.55459L1.07333 3.92758C0.751084 4.61425 0.521096 5.26138 0.369529 5.90587C-0.122737 7.98828 -0.123396 11.0117 0.36887 13.0935C0.520438 13.7373 0.758333 14.4029 1.07267 15.0704L1.24071 15.4401C1.58207 16.1967 1.93529 16.9795 2.58703 17.6313C3.11027 18.1552 3.7574 18.5255 4.57191 18.768C4.91458 18.8656 5.38444 19 5.86485 19C6.14426 19 6.40192 18.9539 6.62005 18.8669C7.06289 18.7002 7.75549 18.2586 8.07312 17.941C8.14759 17.8672 8.27214 17.742 8.3242 17.5983C8.52321 17.2029 8.38812 16.7278 8.21876 16.2342L8.03556 15.6055C7.91035 15.1706 7.78448 14.7357 7.67443 14.308C7.65928 14.2447 7.64741 14.1736 7.63489 14.1004C7.57954 13.7808 7.50376 13.3432 7.19008 13.0289C6.87706 12.7152 6.41774 12.6796 6.09945 12.6796C5.8141 12.6796 5.50372 12.7126 5.20322 12.7449C4.95017 12.7712 4.70041 12.7996 4.49085 12.7996C4.45658 12.7996 4.42825 12.7989 4.40518 12.7976C4.36696 12.6921 4.32874 12.5327 4.30568 12.4351L4.26021 12.2526C3.71456 10.3514 3.71456 8.65452 4.26218 6.73883L4.30699 6.56025ZM2.98967 12.5986L3.02394 12.7383C3.105 13.079 3.20582 13.5027 3.49775 13.7946C3.78573 14.0826 4.20287 14.1156 4.49151 14.1156C4.75577 14.1156 5.05429 14.0839 5.34293 14.053C5.60455 14.0253 5.87078 13.9956 6.10011 13.9956C6.17721 13.9956 6.2319 13.9996 6.26947 14.0042C6.29649 14.0912 6.32087 14.2309 6.33668 14.3225C6.35448 14.4273 6.37293 14.5281 6.39665 14.6263C6.51198 15.0744 6.64114 15.5212 6.76964 15.9686L6.97261 16.6606C6.99831 16.7337 7.06289 16.9235 7.09189 17.054C6.87376 17.2451 6.39995 17.5403 6.14426 17.6372C5.87605 17.7426 5.30075 17.6043 4.94094 17.5015C4.3406 17.3229 3.88854 17.0685 3.5195 16.6988C3.03844 16.2177 2.74914 15.5765 2.44271 14.8978L2.2661 14.511C1.98933 13.9231 1.7824 13.3445 1.65126 12.791C1.20842 10.9149 1.20908 8.08515 1.65258 6.20834C1.78306 5.65282 1.9834 5.0907 2.26742 4.4864L2.44535 4.0976C2.75046 3.42082 3.0391 2.78094 3.5195 2.30053C3.88854 1.9315 4.33994 1.67647 4.93369 1.50052C5.20058 1.42408 5.56632 1.31996 5.86353 1.31996C5.97556 1.31996 6.07045 1.33445 6.15678 1.36938C6.39336 1.45834 6.84476 1.73776 7.09057 1.94863C7.06091 2.08043 6.99501 2.26956 6.96997 2.34403L6.75382 3.08275C6.62993 3.51308 6.50604 3.94405 6.3927 4.38492C6.37161 4.47388 6.35382 4.57273 6.33668 4.67553C6.32021 4.77109 6.29517 4.91738 6.27408 4.99514C6.23586 4.99976 6.17984 5.00371 6.09879 5.00371C5.86946 5.00371 5.60323 4.97406 5.34095 4.94638C5.05231 4.91607 4.75445 4.88443 4.49019 4.88443C4.20156 4.88443 3.78573 4.91738 3.49775 5.20536C3.2078 5.49532 3.10697 5.91575 3.02526 6.2525L2.99231 6.38693C2.37615 8.54117 2.37681 10.4621 2.98967 12.5986ZM23.7986 5.90521C23.647 5.26335 23.4091 4.59777 23.0941 3.9289L22.9268 3.56184C22.5847 2.804 22.2315 2.02046 21.5791 1.36806C21.0552 0.844825 20.4088 0.473814 19.5942 0.231965C19.2509 0.133775 18.7817 0 18.3013 0C18.0219 0 17.7642 0.0461291 17.5461 0.133116C17.1033 0.300499 16.4107 0.741363 16.0937 1.059C16.0252 1.12753 15.8973 1.25538 15.842 1.40233C15.6429 1.79838 15.7787 2.27417 15.9487 2.76842L16.1293 3.38919C16.2551 3.82675 16.381 4.26301 16.4917 4.69267C16.5069 4.75593 16.5187 4.8271 16.5319 4.90025C16.5866 5.21986 16.6624 5.65743 16.9761 5.97177C17.2898 6.28479 17.7484 6.32037 18.0667 6.32037C18.3521 6.32037 18.6618 6.28742 18.9623 6.25579C19.216 6.22877 19.4657 6.2011 19.6746 6.2011C19.7089 6.2011 19.7372 6.20175 19.7603 6.20307C19.7999 6.31049 19.8387 6.47458 19.8598 6.56552L19.9053 6.74806C20.4503 8.64858 20.4503 10.3455 19.9033 12.2605L19.8592 12.4417C19.8354 12.5386 19.7966 12.7001 19.7643 12.7949C19.7405 12.7963 19.7115 12.7976 19.676 12.7976C19.4671 12.7976 19.2173 12.7699 18.9643 12.7429C18.6638 12.7113 18.3527 12.6783 18.068 12.6783C17.7491 12.6783 17.2884 12.7146 16.9761 13.0276C16.6617 13.3419 16.5866 13.7801 16.5319 14.1004C16.5187 14.1736 16.5075 14.2441 16.495 14.2968C16.3869 14.7152 16.2657 15.1344 16.1458 15.5522L15.9513 16.225C15.7794 16.7212 15.6423 17.199 15.8182 17.5436C15.8953 17.7394 16.0159 17.8606 16.093 17.939C16.4107 18.2567 17.1033 18.6975 17.5342 18.8596C17.7649 18.9519 18.0232 18.998 18.3039 18.998C18.7481 18.998 19.1685 18.8886 19.6015 18.7647C20.4101 18.5249 21.0565 18.1539 21.5798 17.6306C22.2309 16.9789 22.5834 16.1967 22.9254 15.4408L23.0941 15.0711C23.4164 14.3851 23.647 13.7373 23.7979 13.0928C24.2889 11.0111 24.2895 7.98828 23.7986 5.90521ZM22.5142 12.7917C22.3844 13.3465 22.1834 13.9086 21.8994 14.5129L21.7228 14.8991C21.4164 15.5772 21.1277 16.2177 20.6467 16.6988C20.2776 17.0678 19.8262 17.3222 19.2325 17.4988C18.8634 17.6043 18.3224 17.7538 18.0107 17.6306C17.7735 17.541 17.3207 17.2609 17.0762 17.0507C17.1059 16.9189 17.1718 16.7298 17.1968 16.6553L17.4123 15.9166C17.5362 15.4863 17.6601 15.0553 17.7741 14.6151C17.7952 14.5255 17.813 14.4259 17.8308 14.3225C17.8466 14.2269 17.8716 14.082 17.8934 14.0042C17.931 13.9996 17.9876 13.9956 18.0687 13.9956C18.298 13.9956 18.5642 14.0253 18.8259 14.053C19.1145 14.0833 19.4124 14.1149 19.6766 14.1149C19.9653 14.1149 20.3811 14.0813 20.6684 13.7946C20.9577 13.5053 21.0592 13.0869 21.1402 12.7508L21.1752 12.6124C21.7913 10.4569 21.7907 8.5359 21.1778 6.39945L21.1435 6.2604C21.0618 5.91971 20.961 5.49663 20.6691 5.20404C20.3811 4.91606 19.9646 4.88312 19.6753 4.88312C19.411 4.88312 19.1125 4.91475 18.8239 4.94506C18.5623 4.97274 18.296 5.00239 18.0674 5.00239C17.9903 5.00239 17.9356 4.9991 17.898 4.99449C17.871 4.9075 17.8473 4.76779 17.8308 4.67619C17.813 4.57141 17.7945 4.47059 17.7708 4.3724C17.6548 3.92231 17.5257 3.47354 17.3965 3.0241L17.1955 2.33941C17.1698 2.26561 17.1046 2.07516 17.0756 1.94468C17.2937 1.75357 17.7675 1.45834 18.0232 1.36147C18.2901 1.25406 18.8661 1.39376 19.2265 1.49722C19.8262 1.67581 20.2783 1.93084 20.6473 2.29987C21.129 2.7816 21.419 3.42411 21.7254 4.10353L21.9007 4.48772C22.1788 5.07686 22.3857 5.65545 22.5162 6.20769C22.9584 8.08449 22.9584 10.9142 22.5142 12.7917ZM15.5467 9.75437C15.5204 9.81697 15.4789 9.86969 15.436 9.92043C15.4235 9.93493 15.4189 9.95338 15.405 9.96722L13.3859 11.9864C13.2574 12.1149 13.0887 12.1795 12.92 12.1795C12.7513 12.1795 12.5826 12.1149 12.4541 11.9864C12.1964 11.7287 12.1964 11.3122 12.4541 11.0546L13.3483 10.1603H9.22767C8.86391 10.1603 8.56868 9.86508 8.56868 9.50132C8.56868 9.13755 8.86391 8.84233 9.22767 8.84233H13.3483L12.4541 7.94808C12.1964 7.69041 12.1964 7.27393 12.4541 7.01627C12.7118 6.7586 13.1282 6.7586 13.3859 7.01627L15.405 9.03541C15.4189 9.04925 15.4235 9.0677 15.436 9.0822C15.4789 9.1336 15.5204 9.18566 15.5467 9.24827C15.6133 9.40972 15.6133 9.59292 15.5467 9.75437Z" fill="white"/>
              <path d="M3.37109 1.5L1.87109 3L0.871094 7L0.371094 11L1.37109 14.5L3.37109 17.5L5.87109 18.5L7.87109 17.5L6.37109 13.5H3.87109L2.87109 9L3.87109 5.5H6.87109L7.87109 2.5L7.37109 1L4.87109 0.5L3.37109 1.5Z" fill="white"/>
              <path d="M20.8711 1.5L22.3711 3L23.3711 7L23.8711 11L22.8711 14.5L20.8711 17.5L18.3711 18.5L16.3711 17.5L17.8711 13.5H20.3711L21.3711 9L20.3711 5.5H17.3711L16.3711 2.5L16.8711 1L19.3711 0.5L20.8711 1.5Z" fill="white"/>
            </svg>
          </i>
          <p>
            <span v-if="!callTabs.callTransferring">TRANSFER & END</span>
            <span v-else>TRANSFERRING...</span>
          </p>
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import * as moment from 'moment-timezone'
import config from '../../../config'

let cancelUsersSubscription: () => void;

import PhoneNumber from '@/pmd/components/util/PhoneNumber.vue'
import SearchBox from '@/pmd/components/power_dialer/SearchBox.vue'
import SelectedItem from '@/pmd/components/power_dialer/SelectedItem.vue'
import CallFrom from '@/pmd/components/power_dialer/CallFrom.vue'
import { User } from '../../../models';
const Avatar = () => import('../Avatar.vue')
export default Vue.extend({
  props: ['currentLocationId', 'phoneNumbers', 'callTabs'],
  components: { PhoneNumber, Avatar, SearchBox, SelectedItem, CallFrom },
  data() {
    return {
      label: 'Recently Contacted',
      selectedUser: undefined as User | undefined,
      users: [] as User[],
      filteredUsers: [] as User[],
      searching: false
    }
  },
  computed: {
    isUserSelected() {
      return lodash.isEmpty(this.selectedUser) ? false : true;
    }
  },
  created() {
    this.fetchData();
  },
  beforeDestroy() {
    if (cancelUsersSubscription) cancelUsersSubscription();
  },
  methods: {
    updateLabel(searchText: string){
      if(searchText.trim().length > 0)
        this.label = 'Showing Results';
      else if (searchText.trim().length === 0)
        this.label = 'Recently Contacted';
    },
    assignSelectedUser (contact: any): any {
      this.selectedUser = contact;
    },
    removeAssignedUser(){
      this.selectedUser = undefined
    },
    callTransfer() {
      if (!this.selectedUser) return;
      this.$root.$emit('makeSecondCall' , { phone_number: this.selectedUser.phone, contact: this.selectedUser });
    },
    async fetchData() {
      if (cancelUsersSubscription) cancelUsersSubscription();
      cancelUsersSubscription = (await User.fetchAllLocationUsers(this.currentLocationId)).onSnapshot(
        (snapshot) => {
          this.users = lodash.sortBy(lodash.filter(snapshot.docs.map((d) => new User(d)), (u) => { return u.phone && !u.deleted }), ['data.first_name_lower_case']);
          this.filterUsers();
        });
    },
    filterUsers(searchText?: string) {
      let filteredUsers = this.users;
      if (searchText) {
        if (!isNaN(searchText)) filteredUsers = filteredUsers.filter(user => user.phone.includes(searchText.trim()))
        else filteredUsers = filteredUsers.filter(user => user.name.toLowerCase().includes(searchText.toLowerCase().trim()))
      }
      this.filteredUsers = filteredUsers;
    }
  }
})
</script>
<style lang="scss" scoped>
</style>
