<template>
  <div
    v-clickOutside="
      () => {
        show = false
      }
    "
    :class="inline?'flex items-center align-middle':''"
  >
    <label id="listbox-label" class="block text-xs font-medium text-gray-700 mr-2 cursor-pointer" @click="toggle()">
      {{ label }}
    </label>
    <div class="mt-1 relative flex-grow">
      <button
        type="button"
        class="relative w-full bg-white border border-gray-300 rounded-md shadow-sm pl-3 pr-10 py-2 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm cursor-pointer"
        aria-haspopup="listbox"
        aria-expanded="true"
        aria-labelledby="listbox-label"
        @click="toggle()"
      >
        <span
          class="w-full inline-flex truncate cursor-pointer"
          v-if="selected"
        >
          <span class="truncate">
            {{ selected[item_text] || selected }}
          </span>
          <span v-if="secondary_text" class="ml-2 truncate text-gray-500">
            {{ selected[item_secondary_text] }}
          </span>
        </span>
        <span class="w-full inline-flex truncate cursor-pointer" v-else>
          <span class="truncate text-gray-300">
            {{ placeholder }}
          </span>
        </span>
        <span
          class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"
        >
          <!-- Heroicon name: solid/selector -->
          <svg
            class="h-5 w-5 text-gray-400"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
            aria-hidden="true"
          >
            <path
              fill-rule="evenodd"
              d="M10 3a1 1 0 01.707.293l3 3a1 1 0 01-1.414 1.414L10 5.414 7.707 7.707a1 1 0 01-1.414-1.414l3-3A1 1 0 0110 3zm-3.707 9.293a1 1 0 011.414 0L10 14.586l2.293-2.293a1 1 0 011.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z"
              clip-rule="evenodd"
            />
          </svg>
        </span>
      </button>

    <div v-show="show" class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
    <div
          class="group select-none relative p-1 shadow"
          id="listbox-option-0"
          role="option"
          v-if="searchable"
        >
          <div class="flex cursor-pointer">
           <input
        type="text"
        name="name"
        class="block text-xs w-full border-gray-300 rounded-md focus:ring-blue-600 focus:border-blue-600"
        placeholder="Search"
        ref="searchbox"
        autocomplete="off"
        autocorrect="off"
        autocapitalize="none"
        spellcheck="false"
        v-model="search"
      />
          </div>
        </div>
      <ul
        class="max-h-60 overflow-auto"
        tabindex="-1"
        role="listbox"
        aria-labelledby="listbox-label"
        aria-activedescendant="listbox-option-3"
      >
      <li
          class="group text-gray-900 hover:text-white hover:bg-blue-600 cursor-default select-none relative py-2 pl-3 pr-9 cursor-pointer"
          
          role="option"
          @click="select(null)"
          v-if="!search&&!hideSelect"
        >
          <div class="flex cursor-pointer">
            <span class="font-normal truncate">
              Select
            </span>
          </div>
          <span
            v-if="value === null || value === undefined || value === ''"
            class="text-blue-600 group-hover:text-white absolute inset-y-0 right-0 flex items-center pr-4"
          >
            <svg
              class="h-5 w-5"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd"
              />
            </svg>
          </span>
        </li>
      <template v-if="!divider">
        <li
          class="group text-gray-900 hover:text-white hover:bg-blue-600 cursor-default select-none relative py-2 pl-3 pr-9 cursor-pointer"
        
          role="option"
          :key="i"
          @click="select(item)"
          v-b-tooltip.hover
          :title="item[item_tooltip]"
          v-for="(item, i) in filtered"
          :ref="value === item[item_value] || value === item?'selected':''"
        >
          <div class="flex cursor-pointer">
            <span class="font-normal truncate">
              {{ item[item_text] }}
            </span>
            <span
              v-if="secondary_text"
              class="text-gray-500 group-hover:text-white ml-2 truncate"
            >
              {{ item[item_secondary_text] }}
            </span>
          </div>
          <span
            v-if="value === item[item_value] || value === item"
            
            class="text-blue-600 group-hover:text-white absolute inset-y-0 right-0 flex items-center pr-4"
          >
            <svg
              class="h-5 w-5"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd"
              />
            </svg>
          </span>
        </li>
      </template>
      <template v-else>
          <template v-for="(group,name) in filtered">
              <li
          class="group select-none relative py-2 pl-2 pr-9"
          id="listbox-option-0"
          role="option"
          :key="name"
        >
          <div class="flex cursor-pointer">
            <span
              class="text-gray-500 font-bold ml-2 truncate text-sm"
            >
              {{ name }}
            </span>
          </div>
        </li>
           <li
          class="group text-gray-900 hover:text-white hover:bg-blue-600 select-none relative py-2 pl-4 pr-9 cursor-pointer"
          id="listbox-option-0"
          role="option"
          :key="i"
          @click="select(item)"
          v-for="(item, i) in group"
        >
          <div class="flex cursor-pointer">
            <span class="font-normal truncate">
              {{ item[item_text] }}
            </span>
            <span
              v-if="secondary_text"
              class="text-gray-500 group-hover:text-white ml-2 truncate"
            >
              {{ item[item_secondary_text] }}
            </span>
          </div>
          <span
            v-if="value === item[item_value] || value === item"
            class="text-blue-600 group-hover:text-white absolute inset-y-0 right-0 flex items-center pr-4"
          >
            <svg
              class="h-5 w-5"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fill-rule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clip-rule="evenodd"
              />
            </svg>
          </span>
          
        </li>
          </template>
      </template>
      </ul>
    </div>
    </div>
  </div>
</template>
<script>
export default {
  directives: {
    clickOutside: {
      bind: function (el, binding, vnode) {
        el.clickOutsideEvent = function (event) {
          // here I check that click was outside the el and his children
          if (!(el == event.target || el.contains(event.target))) {
            // and if it did, call method provided in attribute value
            binding.value.bind(vnode.context)(event)
          }
        }
        document.body.addEventListener('click', el.clickOutsideEvent)
      },
      unbind: function (el) {
        document.body.removeEventListener('click', el.clickOutsideEvent)
      },
    },
  },
  props: {
    value: {},
    items: {
      type: Array|Object,
    },
    item_text: {
      type: String,
      default: 'item_text',
    },
    item_value: {
      type: String,
      default: 'item_value',
    },
    item_secondary_text: {
      type: String,
      default: 'item_secondary_text',
    },
    secondary_text: {
      type: Boolean,
      default: false,
    },
    item_tooltip:{
        type:String
    },
    label: {
      type: String,
      default: 'Select',
    },
    placeholder: {
      type: String,
      default: 'Select',
    },
    inline:{
        type:Boolean,
        default:false
    },
    divider:{
        type:Boolean,
        default:false
    },
    searchable:{
        type:Boolean,
        default:false
    },
    hideSelect:{
        type:Boolean,
        default:false
    }
  },
  data() {
    return {
      show: false,
      search:''
    }
  },
  computed: {
    selected() {
        if(Array.isArray(this.items))
      return (
        this.items.flat().find(
          i => i[this.item_value] === this.value || i === this.value
        ) || ''
      )
    const items = []
      Object.entries(this.items).forEach(([key, value]) => items.push(value))
      return (
        items.flat().find(
          i => i[this.item_value] === this.value || i === this.value
        ) || ''
      )
    },
    filtered(){
        if(Array.isArray(this.items))
            return this.items.filter(i=>i[this.item_text].toLowerCase().indexOf(this.search.toLowerCase())>-1||!this.search)
        else
        {
            const items = {}
            for(const key in this.items)
        {
            items[key] = this.items[key].filter(i=>i[this.item_text].toLowerCase().indexOf(this.search.toLowerCase())>-1||!this.search)
        }
        return items;
        }
    }
  },
  methods: {
    select(item) {
      if(item)
      this.$emit('input', item[this.item_value] || item)
      else
      this.$emit('input', undefined)
      this.show = false
    },
    toggle(){
        this.show = !this.show
        if(this.show)
        {
            this.$el.scrollIntoView()
            this.$nextTick().then(()=>{
                this.$refs?.searchbox?.focus()
            })
            
        }
    }
  },
}
</script>
