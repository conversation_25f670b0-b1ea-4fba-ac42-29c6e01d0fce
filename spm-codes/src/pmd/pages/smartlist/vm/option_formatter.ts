import {IFilterOption} from './option_vm';
import {IKeyVal} from './interfaces'
import { Utils } from '../../../../util/utils';
import * as moment from 'moment-timezone'

export interface IFormatter {
  format(option: IFilterOption): string;
  titleGenerator?: (option: IFilterOption) => string;
}

// tslint:disable-next-line: max-classes-per-file
export class DefaultFormatter implements IFormatter {
  public format(option: IFilterOption): string {
    if (option) {
      return `${option.conditionUX} ${option.firstValueMandatory  ? option.firstValue || '' : ''} ${option.secondValueMandatory ? option.secondValue || '' : ''}`;
    } else return '';
  }
}

export class KeyValFormatter implements IFormatter {

  public readonly firstTitleLabel: string;
  public readonly secondTitleLabel: string;

  public constructor(firstTitleLabel: string, secondTitleLabel?: string){
      this.firstTitleLabel = firstTitleLabel;
      this.secondTitleLabel = secondTitleLabel;
  }

  public format(option: IFilterOption): string {
    if (option) {
       return `${option.conditionUX} ${this.getValueTitle(option.firstValue)} ${this.getValueTitle(option.secondValue)}`;
    } else return '';
  }

   public titleGenerator = (option: IFilterOption) => {
      if (!option) return '';
      let title1 = option.firstValue ?  `${this.firstTitleLabel} ${this.getValueTitle(option.firstValue)}` : '';
      let title2 = option.secondValue ?  `${this.secondTitleLabel} ${this.getValueTitle(option.secondValue)}` : '';
      if (!Utils.isEmptyStr(title1) && !Utils.isEmptyStr(title2)) return title1 +  ', '  + title2;
      else if (!Utils.isEmptyStr(title1)) return title1
      else if (!Utils.isEmptyStr(title2)) return title2;
      else return `${option.filterName} ${option.conditionUX}` ;
   }

   private getValueTitle(val: any){
      if (!val) return ' ';
      else return `${val.value ? val.value: val}`;
   }

}


// tslint:disable-next-line: max-classes-per-file
export class ArrayFormatter implements IFormatter {
  public format(option: IFilterOption): string {
    if (option) {
      return `${option.conditionUX} ${this.joinArray(option)}`;
    } else return '';
  }

  private joinArray(option: IFilterOption) {
    if (!option || !option.firstValue) return '';
    const items = option.firstValue;

    if (!items.length || items.length <= 0) return '';
    if (items[0].value) {
      return items.map((a) => a.value).join(', ');
    } else return items.join(', ');
  }
}

export class DateRangeFormatter implements IFormatter {

  public format(option: IFilterOption): string {
    let startDate = '';
    let endDate = '';
    //expected format 'YYYY-MM-DD hh:mm a'
    if(option.firstValue) startDate = moment(option.firstValue,'YYYY-MM-DD hh:mm a').format('MMM D, YYYY'); // moment construction needs expected format for it to work in safari browser
    if(option.secondValue) endDate = moment(option.secondValue,'YYYY-MM-DD hh:mm a').format('MMM D, YYYY'); // moment construction needs expected format for it to work in safari browser
    if (option) {
      return `${startDate} - ${endDate}`;
    } else return '';
  }

}
