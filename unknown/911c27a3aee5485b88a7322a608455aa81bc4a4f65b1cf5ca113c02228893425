<template>
  <HLWarning
    :showAlert="
      $route.name !== 'location_billing' &&
        showAlert &&
        !isDismissed &&
        company &&
        computedLocation &&
        computedLocation.settings &&
        computedLocation.settings.saas_settings &&
        computedLocation.settings.saas_settings.saas_mode === 'setup_pending'
    "
    title="You need to enter your card information."
    description="Contact us for any clarifications."
    @closeWarning="closeAlert"
  >
    <template v-slot:action>
      <button
        type="button"
        class="btn btn-success upgrade-btn"
        @click="handleResolve()"
      >
        Add Now
      </button>
    </template>
  </HLWarning>
</template>

<script>
import { Location } from '@/models'
import HLWarning from '@/pmd/components/common/HLWarning.vue'

export default {
  components: {
    HLWarning,
  },
  data() {
    return {
      location: {},
      showAlert: false,
      currentLocationId: '',
    }
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.currentLocationId = id
      this.showAlert = false
      this.fetchData()
    },
  },

  mounted() {
    this.fetchData()
  },
  computed: {
    isDismissed() {
      if (
        this.location &&
        this.location.saas_setup_alert_dismissed &&
        this.$route.path
      ) {
        if (
          (this.location.saas_setup_alert_dismissed.seconds + 60 * 60 * 3) *
            1000 >
          +new Date()
        ) {
          return true
        }
      }
      return false
    },
    company() {
      return this.$store.state.company.company
    },
    computedLocation() {
      return this.$store.getters['locations/getById'](
        this.$route.params.location_id
      )
    },
  },

  methods: {
    async fetchData() {
      this.location = new Location(
        await this.$store.dispatch('locations/getById', this.currentLocationId)
      )
      setTimeout(async () => {
        if (
          this.location &&
          this.location.settings &&
          this.location.settings.saas_settings &&
          this.location.settings.saas_settings.saas_mode === 'setup_pending'
        ) {
          this.showAlert = true
        }
      }, 5000)
    },

    async closeAlert() {
      // this.showAlert = false;
      let d = new Date()
      await Location.collectionRef()
        .doc(this.currentLocationId)
        .update({
          saas_setup_alert_dismissed: d,
        })
      this.showAlert = false
    },
    handleResolve() {
      this.showAlert = false
      this.$router.push({ name: 'location_billing' })
    },
  },
}
</script>
