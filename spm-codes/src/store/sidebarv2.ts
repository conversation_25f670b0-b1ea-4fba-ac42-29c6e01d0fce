import { Modu<PERSON> } from 'vuex'
import { RootState, SideBarV2 } from './state_models'
import { User, Company } from '../models'
import moment from 'moment-timezone'

const newAgencyDate = '2021-11-12'

export const sidebarv2: Module<SideBarV2, RootState> = {
    namespaced: true,
    state: { 
        version: 'v1',
        notification: 'disable',
    },
    getters: {
        getNotification: (
            state: SideBarV2,
            getters: any,
            rootState: RootState,
            rootGetters: any
        ) => {
            const user: User | undefined = (rootState.user?.user) ? new User(rootState.user.user) : undefined
            const company: Company | undefined = (rootState.company?.company) ? new Company(rootState.company.company) : undefined
            const mandatoryTypes = ['mandatory_v1', 'mandatory_v2']
            let notification = false
            if (company) {
                if (company.sideBarNotificationType == 'optional') notification = true
                if (mandatoryTypes.includes(company.sideBarNotificationType) && notification) {
                    notification = false
                }
                
                if (user && user.role == User.ROLE_ADMIN && 
                    user.type == User.TYPE_AGENCY && 
                    !mandatoryTypes.includes(company.sideBarNotificationType)) {
                        notification = true
                }

                if (!company.sideBarNotificationType && company.dateAdded 
                    && moment(newAgencyDate).isBefore(company.dateAdded))
                    notification = false
            }

            return notification
        },
        getNotificationType: (
            state: SideBarV2,
            getters: any,
            rootState: RootState,
            rootGetters: any
        ) => {
            const company: Company | undefined = (rootState.company?.company) ? new Company(rootState.company.company) : undefined
            let notification
            if (company) {
                if (company.sideBarNotificationType)
                    notification = company.sideBarNotificationType

                if (!company.sideBarNotificationType && company.dateAdded 
                    && moment(newAgencyDate).isBefore(company.dateAdded))
                    notification = 'mandatory_v2'
            }
            
            if (notification) return notification
            
            return state.notification
        },
        getVersion: (
            state: SideBarV2,
            getters: any,
            rootState: RootState,
            rootGetters: any
        ) => {
            const user: User | undefined = (rootState.user?.user) ? new User(rootState.user.user) : undefined
            const company: Company | undefined = (rootState.company?.company) ? new Company(rootState.company.company) : undefined
            let version
            if (user) {
                if (user.sideBarVersion == 'v2') {
                    version = 'v2'
                } else if (!user.sideBarVersion && company && 
                    company.sideBarNotificationType == 'optional') {
                    version = 'v2'
                }
            }
            if (company) {
                if (company.sideBarNotificationType == 'mandatory_v2')
                    version = 'v2'
                else if (company.sideBarNotificationType == 'mandatory_v1')
                    version = 'v1'

                if (!company.sideBarNotificationType && company.dateAdded 
                    && moment(newAgencyDate).isBefore(company.dateAdded))
                    version = 'v2'
            }
            
            if (version) return version

            return state.version
        },
        isSidebarSettingsEnable: (
            state: SideBarV2,
            getters: any,
            rootState: RootState,
            rootGetters: any
        ) => {
            let enable = true
            const company: Company | undefined = (rootState.company?.company) ? 
            new Company(rootState.company.company) : undefined

            if (company.dateAdded && moment('2021-11-12T10:00:00.000+03:00').isBefore(company.dateAdded))
                enable = false

            return enable
        }
    },
}