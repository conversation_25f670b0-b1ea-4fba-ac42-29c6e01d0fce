<template>
  <section class="hl_wrapper">
    <section class="hl_wrapper--inner hl_marketing" id="trigger-links">
      <div class="hl_marketing--header border-bottom-0 mb-0">
        <div class="container-fluid">
          <div class="hl_controls mb-0">
            <div class="hl_controls--left flex">
              <h2 class="pb-0 capitalize">{{ getHeading }}</h2>
            </div>

            <div class="hl_controls--right">
              <UIButton type="button" use="primary" v-if="tabIndex === 0" @click.stop="addLink">Add Link</UIButton>
              <div
                v-if="tabIndex !== 0 && links.length > 1"
                id="hl_datepicker"
                class="hl_datepicker d-flex justify-content-center align-items-center"
              >
                <i class="icon icon-calendar"></i>
                <div class="d-flex" style="width: 230px;">
                  <vue-ctk-date-time-picker
                    v-model="date"
                    :only-date="true"
                    :range="true"
                    :right="true"
                    :noClearButton="true"
                    formatted="ddd, MMM Do"
                    @validate="updateFilterDate"
                  />
                </div>
              </div>
              <button
                v-if="tabIndex !== 0 && links.length > 1"
                type="button"
                class="btn btn-sm btn-primary p-8"
                data-tooltip="tooltip"
                data-placement="top"
                v-b-tooltip.hover
                title="Refresh Data"
                @click="refreshData"
                data-original-title="Refresh"
              >
                <i class="icon icon-reload-1"></i>
              </button>
            </div>
          </div>

          <div class="hl_marketing--heading-text mb-3">
            <p>Trigger links allow you to put links inside SMS messages and emails, which allow you to track specific customer actions and trigger events based on when the link is clicked.</p>
          </div>
        </div>
      </div>

      <b-tabs content-class="mt-4" v-model="tabIndex">
        <b-tab :title="getTitle(0)" active>
          <div class="container-fluid">
            <div class="hl_controls justify-content-end">
              <div class="hl_controls--right">
                <div class="search-form" v-if="links.length !== 0">
                  <UITextInputGroup
                    type="text"
                    class="form-light"
                    icon="icon-loupe"
                    v-model="filters.text"
                    placeholder="Search Trigger links"
                  />
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-lg-12">
                <moon-loader class="mt-5" color="#188bf6" size="20px" v-if="loading" />
                <div
                  v-else-if="links.length !== 0"
                  class="card hl_customer-acquisition--table"
                >
                  <div class="card-body --no-padding">
                    <div class="table-wrap">
                      <table class="table table-sort">
                        <thead>
                          <tr>
                            <th>Name</th>
                            <th>Link URL</th>
                            <!-- <th># of Clicks</th> -->
                            <th>Actions</th>
                          </tr>
                        </thead>
                        <tbody v-if="links">
                          <tr v-for="link in filteredLinks" :key="link.id">
                            <td>{{link.name}}</td>
                            <td class="pointer">
                              <a :href="link.redirectTo" target="_blank">{{link.redirectTo}}</a>
                            </td>

                            <!-- <td></td> -->
                            <td>
                              <i
                                class="icon icon-pencil pointer --light"
                                @click.stop="editLink(link)"
                                style="margin-left:15px;"
                              ></i>
                              <i
                                v-if="!isProcessingDelete(link)"
                                class="icon icon-trash pointer --light"
                                style="margin-left:15px;"
                                @click.prevent="deleteLink(link)"
                              ></i>
                              <moon-loader
                                v-if="isProcessingDelete(link)"
                                :loading="isProcessingDelete(link)"
                                color="#188bf6"
                                size="15px"
                                style="display: inline-block;margin-left: 15px;"
                              />
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
                <div v-else class="nothing-found">
                  <p>
                    You do not have any trigger link yet.
                    <a
                      @click.prevent="addLink"
                      href="javascript:void(0);"
                    >Click here</a> to create your first trigger link.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </b-tab>
        <b-tab :title="getTitle(1)">
          <div>
            <TriggerLinkAnalyze
              :location_id="currentLocationId"
              :links="links"
              @changeTab="changeTab"
              :date="date"
              :selectedDate="selectedDate"
              v-if="links.length > 0 && tabIndex == 1"
            />

            <div class="nothing-found" v-else>
              <p>
                You do not have any trigger links yet.
                <a
                  @click.prevent="addLink"
                  href="javascript:void(0);"
                >Click here</a> to create your first trigger link.
              </p>
            </div>
          </div>
        </b-tab>
      </b-tabs>
    </section>

    <NewLinkModal
      :values="modalValues"
      :currentLocationId="currentLocationId"
      @hidden="hideWindow"
      @updated="fetchData"
    />
  </section>
</template>

<script lang='ts'>
import Vue from 'vue'
import defaults from '../../../config'
import * as moment from 'moment'
import {
  Company,
  Location,
  User,
  Notification,
  NotificationType,
  TwilioAccount,
  Calendar,
  Link
} from '@/models'
import { UserState } from '@/store/state_models'
const Avatar = () => import('../../components/Avatar.vue').then(m => m.default)
const EditTeamMemberModal = () =>
  import('../../components/EditTeamMemberModal.vue').then(m => m.default)
const NewLinkModal = () =>
  import('../../components/marketing/NewLinkModal.vue').then(m => m.default)
const TriggerLinkAnalyze = () =>
  import('@/pmd/components/marketing/TriggerLinkAnalyze.vue').then(m => m.default)

declare var $: any

export default Vue.extend({
  components: { NewLinkModal, TriggerLinkAnalyze},
  data() {
    return {
      currentLocationId: '' as string,
      loading: true,
      links: [] as Link[],
      filters: {
        text: ''
      },
      tabIndex: 0,
      modalValues: {
        visible: false,
        selectedLinkId: undefined as undefined | string,
        currentLocationId: ''
      },
      processingDelete: {} as { [key: string]: boolean },
      date: {
        start: moment()
          .subtract(1, 'month')
          .toDate(),
        end: moment(),
        start_date: moment()
          .subtract(1, 'month')
          .format('YYYY-MM-DD'),
        end_date: moment().format('YYYY-MM-DD')
      },
      selectedDate: '',
      tabs: ['link', 'analyze'],
      tabsTitle: ['Link', 'Analyze'],
    }
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.currentLocationId = id
      this.fetchData()
    },
    '$route.name': function (name) {
      if (this.getSideBarVersion == 'v2') {
        let tabName = name.split("-")[2];
        if (tabName) this.tabIndex = this.tabs.indexOf(tabName);
      }
    },
    tabIndex: function(val) {
      console.log('Tab Index Watch', val)
    }
  },
  computed: {
    filteredLinks(): Link[] {
      return !this.filters.text
        ? this.links
        : this.links.filter(
            x =>
              !this.filters.text ||
              x.name.toLowerCase().includes(this.filters.text.toLowerCase())
          )
    },
    user() {
      const user: UserState = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    getHeading() {
      let heading = 'Trigger Links'
      if (this.getSideBarVersion == 'v2') {
        heading = this.tabsTitle[this.tabIndex]
      }
      return heading
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    await this.fetchData()
    if (this.getSideBarVersion == 'v2') {
      let tabName = this.$router.currentRoute.name.split("-")[2];
      const tabIndex = this.tabs.indexOf(tabName);
      this.tabIndex = tabIndex
    }
  },
  async mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  methods: {
    isProcessingDelete(link: Link) {
      return this.processingDelete[link.id]
    },
    getTitle(index: number) {
      return !this.user && this.getSideBarVersion != 'v2' ? this.tabsTitle[index] : ''
    },
    async fetchData() {
      await Link.fetchAllByLocation(
        this.currentLocationId
      ).get().then((snapshot) => {
        this.links = snapshot.docs.map((d: any) => new Link(d)).sort((a: Link,b: Link) => {
          var nameA = (a && a.name || '').toUpperCase(); // ignore upper and lowercase
          var nameB = (b && b.name || '').toUpperCase(); // ignore upper and lowercase
          if (nameA < nameB) {
            return -1;
          }
          if (nameA > nameB) {
            return 1;
          }

          // names must be equal
          return 0;
        })

        this.loading = false;
      })
    },
    editLink(link: Link) {
      this.modalValues = {
        visible: true,
        selectedLinkId: link.id,
        currentLocationId: this.currentLocationId
      }
    },
    addLink() {
      this.modalValues = {
        visible: true,
        selectedLinkId: undefined,
        currentLocationId: this.currentLocationId
      }
    },
    async deleteLink(link: Link) {
      if (confirm('Are you sure you want to delete this link?')) {
        Vue.set(this.processingDelete, link.id, true)
        link.deleted = true
        await link.save()
        await this.fetchData();
        await this.$http.delete(`${defaults.shortenUrlServiceUrl}url/origin/${link.id}`);
        Vue.set(this.processingDelete, link.id, false)
      }
    },
    hideWindow() {
      this.modalValues = {
        visible: false,
        selectedLinkId: undefined,
        currentLocationId: this.currentLocationId
      }
    },
    changeTab(tab) {
      console.log('Change Tab Index', tab)
      this.tabIndex = tab
    },
    updateFilterDate() {
      this.$set(
        this.date,
        'start_date',
        moment(this.date.start, 'YYYY-MM-DD').format('YYYY-MM-DD')
      )
      this.$set(
        this.date,
        'end_date',
        moment(this.date.end, 'YYYY-MM-DD').format('YYYY-MM-DD')
      )
      this.selectedDate =
        moment(this.date.start, 'YYYY-MM-DD').format('YYYY-MM-DD') +
        moment(this.date.end, 'YYYY-MM-DD').format('YYYY-MM-DD')
    },
    refreshData() {
      this.selectedDate = moment(this.date.start, 'YYYY-MM-DD').toDate() + moment().format()
    }
  }
})
</script>

<style lang="scss">
#trigger-links {
  .hl_datepicker {
    padding: 0 20px;
  }

  .date-time-picker {
    .field-input {
      background-color: white !important;
      height: 0 !important;
    }
  }
}

.hl_wrapper--inner .nav.nav-tabs {
  padding: 0px 20px;
}
.hl_wrapper--inner .nav.nav-tabs .nav-link.active {
  color: #188bf6;
  border-color: #188bf6;
}
.hl_wrapper--inner .nav.nav-tabs .nav-link {
  padding-bottom: 10px;
  border-bottom: 3px solid transparent;
  color: #607179;
  font-weight: 500;
  font-size: 14px;
}
*:focus {
  outline: none;
}
</style>
