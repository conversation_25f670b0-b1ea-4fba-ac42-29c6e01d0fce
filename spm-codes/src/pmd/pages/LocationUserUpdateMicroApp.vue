<template>
  <div id="locationUserUpdate" ref="locationUserUpdate"></div>
</template>

<script lang="ts">
import Vue from 'vue'
import Postmate from 'postmate'
import { trackGaPageView } from '@/util/helper'
import { mapState } from 'vuex'
import { Company, User, UserState, PrivateUser } from '@/models'
import defaults from '@/config'
import moment from 'moment'
import firebase from 'firebase/app'
import localStorage from 'store'
import { UserSource, UserSourceChannel } from '@/models/user'

export interface UserData {
  firstName: string
  lastName: string
  phone: string
  password: string
  countryCode: string
  logo: string
  agencyName: string
}

export default Vue.extend({
  data() {
    return {
      handshake: {} as any,
      userData: {} as UserData,
      baseUrl: defaults.baseUrl,
      newOnboardingFrontend: defaults.newOnboardingFrontend,
      firstName: '',
      childLoaded: false,
      authKey: '',
      importing: false,
      appLoader: false,
      locationId: '',
    }
  },
  mounted() {
    this.locationId = this.$route.params.location_id
    if (this.company && this.user) {
      this.loadMicroApp()
      this.childLoaded = true
    }
    window.addEventListener('popstate', () => {
      this.handshake.then(child => {
        child.call('routeChange', { path: this.$route.path, direction: 'back' })
      })
    })
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
    isV2SideBar() {
      return this.$store.getters['sidebarv2/getVersion'] === 'v2'
    },  
  },
  methods: {
    async loadMicroApp() {
      let logoUrl = ''
      let agencyName = 'HighLevel'

      if (this.company?.domain && this.company?.logoURL) {
        logoUrl = this.company.logoURL
        agencyName = this.company.name
      }

      if (this.user && !this.user.isPasswordPending) {
        await this.goToLaunchpad()
        return false
      }

      if (process.env.NODE_ENV === 'development') Postmate.debug = true

      this.handshake = new Postmate({
        container: this.$refs.locationUserUpdate as HTMLElement,
        url: `${this.newOnboardingFrontend}${this.$route.path}`,
        name: 'location-user-update',
        classListArray: ['location-user-iframe'],
        model: {
          userData: {
            firstName: this.user?.firstName,
            lastName: this.user?.lastName,
            phone: this.user?.phone,
            password: '',
            countryCode: this.company?.country,
            logo: logoUrl,
            agencyName,
          },
        },
      })

      this.handshake.then(child => {
        child.on('ga-event', (data: any) => {
          trackGaPageView(data)
        })
        child.on('user-data', data => {
          this.userData = JSON.parse(data)
          this.$nextTick(() => {
            this.updateData()
          })
        })
      })
    },
    async updateData() {
      try {
        if (!this.userData.password) throw new Error('Password Required')
        const user = await User.getById(this.user.id)

        this.$store.commit('user/updatePasswordUpdatedInOnboarding', true)
        const currentPrivateUser = new PrivateUser(user)
        currentPrivateUser.passwordHash = this.userData.password
        await currentPrivateUser.save()

        localStorage.set('loginDate', moment().toDate()) // Prevent user from being logged out from current session when changing password
        await user.ref.update({
          last_password_change: firebase.firestore.Timestamp.now(),
          attempt: 0,
          isPasswordPending: false,
          first_name: this.userData.firstName,
          last_name: this.userData.lastName,
          phone: this.userData.phone,
          last_updated_by: {
            source: UserSource.LaunchPad,
            channel: UserSourceChannel.WEB_APP,
            timestamp: firebase.firestore.Timestamp.now()
          }
        })
        await this.sendLink()
        this.goToLaunchpad()
      } catch (err) {
        this.goToLaunchpad()
        console.log(err)
      }
    },
    async goToLaunchpad() {
      this.$router.push({
        name: this.isV2SideBar ? 'location-launchpad-v2' : 'location_launchpad',
        replace: true
      })
    },
    async sendLink() {
      try {
        const body = {
          location_id: this.locationId,
          type: 'mobile_app_link',
          user_id: this.user?.id,
        }

        await this.$http.post('/launchpad/reminder', body)
      } catch (err) {
        console.log(err)
      }
    },
    destroy() {
      //@ts-ignore
      this.handshake.then((child: any) => {
        child.destroy()
      })
    },
  },
  beforeDestroy() {
    this.destroy()
    window.removeEventListener('popstate', () => console.log('remove popstate'))
  },
})
</script>

<style>
@media (min-width: 768px) {
  body {
    padding-top: 0px !important;
  }
}
.location-user-iframe {
  border: none;
  width: 100%;
  height: 100vh;
  overflow: auto;
}
#app + .app-loader {
  display: none;
}
#locationUserUpdate {
  width: 100%;
}
</style>
