<template>
  <section class="hl_wrapper d-flex">
    <section class="hl_wrapper--inner form-builder-list" id="form-builder">
      <div class="hl_marketing--header">
        <div class="container-fluid">
          <div class="hl_marketing--header-inner">
            <h2>Email Templates</h2>
            <a
              href="javascript:void(0);"
              class="btn btn-success"
              @click.prevent="createNew"
              >New Template</a
            >
          </div>
        </div>
      </div>

      <div class="container-fluid">
        <UIAlert class="mb-4">The old email-builder is going to be deprecated in next few weeks, all the previous templates created here will be migrated to new email builder and will marked as HTML Type Templates.</UIAlert>
        <div class="hl_controls justify-content-end">
          <div class="hl_controls--right">
            <div class="search-form" v-if="emailBuilder.length !== 0">
              <UITextInputGroup
                type="text"
                class="form-light"
                icon="icon-loupe"
                v-model="filters.text"
                placeholder="Search Templates"
              />
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-12">
            <moon-loader
              class="mt-5"
              color="#188bf6"
              size="20px"
              v-if="loading"
            />
            <div
              v-else-if="emailBuilder.length !== 0"
              class="card hl_customer-acquisition--table"
            >
              <div class="card-body --no-padding">
                <div class="table-wrap">
                  <table class="table table-sort">
                    <thead>
                      <tr>
                        <th>Name</th>
                        <th>
                          <!--Actions-->
                        </th>
                      </tr>
                    </thead>
                    <tbody v-if="filteredemailBuilder">
                      <EmailBuilderListItem
                        v-for="emailBuilder in filteredemailBuilder"
                        :emailBuilder="emailBuilder"
                        :key="emailBuilder.id"
                      ></EmailBuilderListItem>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <div v-else class="nothing-found">
              <p>
                You don't have any templates yet.
                <a @click.prevent="createNew" href="javascript:void(0);"
                  >Click here</a
                >
                to create your first template.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  </section>
</template>

<script lang="ts">
import Vue from 'vue'
let unsubscribeemailBuilderList: () => void
const EmailBuilderListItem = () =>
  import('../../components/marketing/EmailBuilderListItem.vue')
import { EmailBuilder } from '@/models'
import { getDefaultTemplate } from '@/util/email_template'
import { sortBy } from 'lodash'
export default Vue.extend({
  components: {
    EmailBuilderListItem,
  },
  data() {
    return {
      loading: true,
      currentLocationId: '',
      emailBuilder: [] as EmailBuilder[],
      filters: {
        text: '',
      },
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.fetchData()
    },
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchData()
  },
  methods: {
    fetchData() {
      if (unsubscribeemailBuilderList) unsubscribeemailBuilderList()
      unsubscribeemailBuilderList = EmailBuilder.getQueryWithLocationId(
        this.currentLocationId
      ).onSnapshot(async snapshot => {
        this.emailBuilder = sortBy(
          snapshot.docs
            .map(d => {
              const emailBuilder = new EmailBuilder(d)
              if (
                !emailBuilder.builderVersion ||
                emailBuilder.builderVersion === '1'
              ) {
                return emailBuilder
              }
              return null
            })
            .filter(x => x),
          [template => template.name]
        )
        this.loading = false
      })
    },
    async createNew() {
      let emailBuilder = new EmailBuilder()
      emailBuilder.locationId = this.currentLocationId
      emailBuilder.name = `Email Template ${this.emailBuilder.length + 1}`
      emailBuilder.emailBuilderData = getDefaultTemplate()
      await emailBuilder.save()
      this.$router.push({
        name: 'email_builder_dashboard',
        params: {
          location_id: this.currentLocationId,
          builder_id: emailBuilder.id,
        },
      })
    },
  },
  computed: {
    filteredemailBuilder(): EmailBuilder[] {
      return !this.filters.text
        ? this.emailBuilder
        : this.emailBuilder.filter(
            x =>
              !this.filters.text ||
              x.name.toLowerCase().includes(this.filters.text.toLowerCase())
          )
    },
  },
  beforeDestroy() {
    if (unsubscribeemailBuilderList) unsubscribeemailBuilderList()
  },
})
</script>
