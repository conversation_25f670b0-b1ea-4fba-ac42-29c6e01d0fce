<template>
  <modal
    v-if="modalShow"
    @close="closeModal"
    :maxWidth="500"
    :showCloseIcon="true"
    :noBackdropClose="true"
  >
    <form
      data-vv-scope="form-leadconnector"
      name="form-leadconnector"
    >
      <div class="mobile-container" style="padding-top: 40px">
        <div v-if="phone" class="mobile-sub-title mt-2 mb-5">
          Please verify this is your mobile number. If you want to receive the app
          download link to another number, please enter the new number and click
          continue.
        </div>
        <div v-else class="mobile-sub-title mt-2 mb-4">
          Please enter the mobile number to receive the download link.
        </div>
        <div class="mobile-form" @click.stop="">
          <div class="form-group" style="width: 300px">
            <label>Mobile Number</label>
            <PhoneNumber
              placeholder="Mobile Number"
              v-model="cellNumber"
              name="phone"
              autocomplete="phone"
              v-validate="{
                required: true,
                phone: true,
                twilioPhone: incomingPhoneNumbers.map(
                  phone => phone.phone_number
                ),
              }"
              data-vv-as="phone"
              data-vv-validate-on="input"
            />
          </div>
          <button
            id="launchpad-lc-mobile"
            class="btn btn-primary active"
            type="button"
            :disabled="!cellNumber || errors.has('form-leadconnector.phone')"
            @click.prevent="savePhoneAndSendLink()"
          >
            <span v-if="!loader">Continue</span>
            <moon-loader
              v-if="loader"
              :loading="loader"
              color="#37ca37"
              size="20px"
            />
          </button>
        </div>
        <span v-show="errors.has('form-leadconnector.phone')" class="error">{{
          errors.first('form-leadconnector.phone')
        }}</span>
      </div>
    </form>
  </modal>
</template>
<script lang="ts">
import Vue from 'vue'
import Modal from '@/pmd/components/common/Modal.vue'
import PhoneNumber from '../util/PhoneNumber.vue'

export default Vue.extend({
  props: ['phone', 'loader', 'modalShow'],
  components: {
    PhoneNumber,
    Modal,
  },
  data() {
    return {
      popupWidth: 500,
      cellNumber: this.phone,
      incomingPhoneNumbers: [] as Array<{ [key: string]: any }>,
    }
  },
  mounted() {
    this.cellNumber = this.phone
  },
  watch: {
    phone: function (phone) {
      this.cellNumber = phone
    },
  },
  methods: {
    closeModal() {
      this.$root.$emit('close-modal')
    },
    async savePhoneAndSendLink() {
      let result = await this.$validator.validateAll('form-subaccount')
      if (!result) {
        return false
      }
      this.$root.$emit('send-link', this.cellNumber)
    },
  },
})
</script>
<style>
.mobile-container .form-group {
  margin-bottom: 0px;
}
.mobile-container .form-group label {
  font-family: Roboto;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 16px;
  align-items: center;
  color: #374151;
}
.mobile-container .form-group input {
  background: #ffffff;
  border: 1px solid #c4c4c4;
  box-sizing: border-box;
  border-radius: 3px;
  padding: 10px 20px 12px 20px;
}
.mobile-container .mobile-form button {
  margin-top: 1.4rem;
  margin-left: 1.3rem;
}
</style>
