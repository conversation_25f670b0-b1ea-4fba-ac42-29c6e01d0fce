<template>
  <!--Modal-->
  <div
    class="modal fade field-description--modal"
    tabindex="-1"
    role="dialog"
    ref="descriptionModel"
    >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <!-- Header - START -->
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Field Description</h2>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <!-- Header - END -->

        <!-- Body - START -->
        <div class="modal-body pb-2">
          <div class="modal-body--inner">
            <div id="editorLoader"> Please wait, editor is loading</div>
            <editor
              
              :init="editorOptions"
              v-model="descriptionText"
             ></editor>
         </div>

        </div>
        <!-- Body - END -->

        <!-- Footer - START -->
        <div class="modal-footer">
          <div class="modal-footer--inner nav">
            <button
              type="button"
              class="btn btn-primary"
              data-dismiss="modal"
              @click="handleSave"
            >Save</button>
          </div>
        </div>
        <!-- Footer - END -->
      </div>
    </div>
  </div>
  <!--End Modal-->
</template>

<script lang="ts">
import Vue from "vue";
import Editor from '@tinymce/tinymce-vue'
import { defaultTinyMceOptions } from '../../../util/tiny_mce_defaults';
declare var $: any;

export default Vue.extend({
	props: {
		values: {} as any
  },
  components: {
    editor: Editor
	},
	data() {
		return {
      descriptionText: "",
      editorOptions: {
        ...defaultTinyMceOptions,
        height: 200,
        init_instance_callback: this.initEditor,
        plugins: [
          'advlist autolink link image lists charmap hr anchor pagebreak spellchecker',
          'searchreplace wordcount visualblocks visualchars code fullscreen insertdatetime media nonbreaking',
          'table contextmenu directionality emoticons template textcolor link'
        ],
        toolbar:
          'bold italic bullist numlist link image forecolor backcolor | styleselect',
        setup: function (editor: any) {
          editor.on('init', function (e: any) {
            document.getElementById('editorLoader').style = 'display:none'
          });
        }
      }
		};
	},
	computed: {},
  beforeDestroy() {
    $(this.$refs.descriptionModel).off('hidden.bs.modal');
 	},
	watch: {
		values(values: { [key: string]: any }) {
      if (values && values.visible) {
        this.descriptionText = (values.text) ? values.text : "";
        $(this.$refs.descriptionModel).modal("show");
      }
		}
	},
	updated() {},
	mounted() {
    $(this.$refs.descriptionModel).on('show.bs.modal', function () {
      $(document).on('focusin', function(e) {
        if ($(e.target).closest(".mce-window").length) {
          e.stopImmediatePropagation();
        }
      });
    });
	},
	methods: {
    handleSave(){
      this.$emit('save',{'type': 'text-description', 'value': this.descriptionText});
    }
	},
});
</script>
