<template>
  <div class="col-lg-4 col-md-6 mb-3">
    <div class="card">
      <div class="card-header">
        <div class="d-flex">
          <h2 class="mr-0">Efficiency</h2>
        </div>
      </div>
      <div v-if="!isLoading" class="card-body">
        <table class="table">
          <tbody>
            <tr>
              <td>Total Sale Value</td>
              <td>{{location.country | symbole}}{{(Math.ceil(data.total_sale_value * 100) / 100) || 0}}</td>
              <td>
                <span>
                  <i class="fas fa-arrow-up text-success"/> {{data.total_sale_value_percentage || 0}}%
                </span>
              </td>
            </tr>
            <tr>
              <td>Average Sales Duration</td>
              <td>{{get_formatted_duration_improved(data.average_sale_cycle * 86400)}}</td>
              <td>
                <span>
                  <i class="fas fa-arrow-up text-success"/> {{data.average_sale_cycle_percentage || 0}}%
                </span>
              </td>
            </tr>
            <tr>
              <td>Sales Velocity</td>
              <td>{{location.country | symbole}}{{(Math.ceil(data.monthly_sale_value * 100) / 100) || 0}}/M</td>
              <td>
                <span>
                  <i class="fas fa-arrow-up text-success"/> {{data.monthly_sale_value_percentage || 0}}%
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div v-else class="card-body">
        <MoonLoader size="30px" radius="50%" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import MoonLoader from '../MoonLoader.vue'
import { get_formatted_duration_improved } from '../../../util/get_formatted_duration'
import get_percent_change_html from '../../../util/get_percent_change_html'

export default Vue.extend({
  components: {
    MoonLoader,
  },
  props: {
    isLoading: {
      type: Boolean,
      default: false
    },
    data: {}
  },
  computed:{
    location(): { [key: string]: any } {
      return this.$store.getters['locations/getById'](this.$route.params.location_id);
    }
  },
  methods: {
    get_formatted_duration_improved: get_formatted_duration_improved,
    getPercentHTML: get_percent_change_html
  },
})
</script>

<style scoped>
table tbody tr td:nth-child(2) {
    white-space: nowrap;
}
</style>
