<template>
  <div class="">
    <upgrade-modal-shimmer v-if="connecting" />
    <div v-else>
      <div class="connected-acc__wrap">
        <div class="connected-acc__overview">
          <div class="connected-acc__overview-heading flex--space-between">
            <div style="display: flex; align-items: center">
              <i class="fas fa-store" style="margin-right: 4px"></i> Connected
              account
              <div class="connected-acc__overview-status --green">VERIFIED</div>
            </div>
            <div
              class="form-group mb-0"
              style="display: flex; align-items: center"
              v-if="user.isAdmin && locationIntegration"
            >
              <label class="mr-2 my-auto">Test</label>
              <UIToggle
                id="stripe-connect-mode"
                :value="location && location.stripe_connect_mode !== 'test'"
                @click="changeMode"
              />
              <label class="tgl-btn" for="stripe-connect-mode"></label>
              <label class="ml-2 my-auto">Live</label>
            </div>
            <div>{{ accDetails.id }}</div>
          </div>
          <div class="flex--space-between">
            <div
              class="connected-acc__overview-name"
              v-if="accDetails && accDetails.business_profile"
            >
              {{ accDetails.business_profile.name }}
            </div>
            <div
              class="connected-acc__overview-email"
              v-if="accDetails && accDetails.email"
            >
              {{ accDetails.email }}
            </div>
            <!-- <div class="connected-acc__more-info"></div> -->
          </div>
        </div>
        <div class="connected-acc__stats" v-if="!connecting">
          <!-- <div class="connected-acc__stat">
          <div class="connected-acc__stat-label">Payments</div>
          <div class="connected-acc__stat-value">Enabled</div>
        </div> -->
          <div class="connected-acc__stat">
            <div class="connected-acc__stat-label">Payouts</div>
            <div class="connected-acc__stat-value">
              <i
                class="fas fa-check-circle"
                style="color: #1ea672"
                v-if="accDetails.payouts_enabled"
              ></i>
              <i
                class="fas fa-exclamation-circle"
                style="color: #ffbc00"
                v-else
              ></i>
              {{ accDetails.payouts_enabled ? 'Enabled' : 'Not Enabled' }}
            </div>
          </div>
          <div
            class="connected-acc__stat"
            v-if="balance && balance.pending && balance.pending[0]"
          >
            <div class="connected-acc__stat-label">Total Balance</div>
            <div class="connected-acc__stat-value">
              {{ getCurrencySymbol(balance.pending[0].currency) }}
              {{ balance.pending[0].amount / 100 }}
              <span style="text-transform: uppercase">{{
                balance.pending[0].currency
              }}</span>
            </div>
          </div>
          <!-- <div class="connected-acc__stat">
          <div class="connected-acc__stat-label">Lifetime Volume</div>
          <div class="connected-acc__stat-value">---</div>
        </div> -->
        </div>
      </div>
      <!-- <div class="connect-identity__wrap">
      <div class="connect-identity__header flex--space-between">
        <div class="connect-identity__header-title"> Identity </div>
        <div class="connect-identity__header-edit-btn"> Edit </div>
      </div>
      <div class="connect-identity__body">
        <div class="connect-identity__column">
          <div class="connect-identity__row">
            <div class="connect-identity__row-label">Name</div>
            <div class="connect-identity__row-value">Pulkit</div>
          </div>
            <div class="connect-identity__row">
            <div class="connect-identity__row-label">Date of birth</div>
            <div class="connect-identity__row-value">04/10/1996</div>
          </div>
          <div class="connect-identity__row">
            <div class="connect-identity__row-label">Identity document</div>
            <div class="connect-identity__row-value">Provided</div>
          </div>
        </div>
        <div class="connect-identity__body-column">
          <div class="connect-identity__row">
            <div class="connect-identity__row-label">Email address</div>
            <div class="connect-identity__row-value">{{accDetails.email}}</div>
          </div>
          <div class="connect-identity__row">
            <div class="connect-identity__row-label">Phone Number</div>
            <div class="connect-identity__row-value">{{accDetails.support_phone}}</div>
          </div>
          <div class="connect-identity__row" v-if="accDetails.support_address">
            <div class="connect-identity__row-label">Address</div>
            <div class="connect-identity__row-value">
              {{accDetails.support_address.line1}}, {{accDetails.support_address.line2}}
              <br />
              {{accDetails.support_address.city}}, {{accDetails.support_address.state}} {{accDetails.support_address.postal_code}}
            </div>
          </div>
        </div>

      </div>
    </div> -->
      <div class="connect-actions__row" v-if="user && user.role === 'admin'">
        <div class="connect-action --disconnect-btn btn" v-if="disconnecting">
          <moon-loader :loading="disconnecting" color="#ffffff" size="20px" />
        </div>
        <button
          class="connect-action --disconnect-btn btn"
          @click="initDisconnect"
          v-else
          :disabled="haveSubscriptions"
          v-b-tooltip.hover
          :title="
            haveSubscriptions
              ? stripeLockedReason
              : ''
          "
        >
          <i class="fas fa-unlink"></i>
          Disconnect Stripe
        </button>

        <div v-if="haveSubscriptions">
          <i class="fas fa-exclamation-circle" style="color: #ffbc00"></i>
          {{stripeLockedReason}}
        </div>

        <!-- <div class="connect-action btn btn-blue" @click="setupAccount">
        Setup Stripe Account <i class="fas fa-angle-right"></i>
      </div> -->
      </div>
    </div>
    <AgencyStripeConnectDisconnectModal
      :show="showDeletePlansModal"
      @close="showDeletePlansModal = false"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex'
// import { CompanyState, UserState } from '@/store/state_models'
import { Company, User, Location } from '@/models'
import { currency } from '@/util/currency_helper'
import { trackGaEvent } from '@/util/helper'
import UpgradeModalShimmer from '@/pmd/components/common/shimmers/UpgradeModalShimmer.vue'
import AgencyStripeConnectDisconnectModal from "./AgencyStripeConnectDisconnectModal.vue";
import { updateCachingIndex, cacheUpdateEvents } from '../../../util/caching.helper'
import { PaymentServices } from '@/services'

export default {
  props: ['locationIntegration'],
  components: {
    UpgradeModalShimmer,
    AgencyStripeConnectDisconnectModal,
  },
  computed: {
    ...mapState('company', {
      company: s => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    haveSubscriptions(){
      return this.haveSaasLocations ||
        this.haveWpLocations ||
        this.haveYextLocations
    },
    stripeLockedReason(){
      let message = null; 
      if(this.haveSubscriptions){
        let subscriptions = [];
        if(this.haveSaasLocations){
          subscriptions.push(`SaaS`)
        }
        if(this.haveWpLocations){
          subscriptions.push(`Wordpress`)
        }
        if(this.haveYextLocations){
          subscriptions.push(`Yext`)
        }
        if(subscriptions.length > 1){
          let lastOne = subscriptions.pop();
          message = `Can't disconnect because  ${subscriptions.join(', ')  } and ${lastOne} clients are using this stripe account!`
        }else{
          message =`Can't disconnect because  ${subscriptions.join(', ')  } clients are using this stripe account!`
        }
      }
      return message
    },

    
    // async haveSaasLocations() {
    //   if (this.locationIntegration) return false;

    //   this.loading.locations = true
    //   var locations = await this.$store.dispatch('locations/getAll')
    //   this.loading.locations = false

    //   for(let location of locations){
    //     if(location.settings && location.settings.saas_settings && location.settings.saas_settings.saas_mode === 'activated' ){
    //       return true
    //     }
    //   }
    //   return false;
    // },
    location() {
      return this.$store.getters['locations/getById'](
        this.$route.params.location_id
      )
    },
  },
  mounted() {
    this.fetchAccountDetails()
    this.checkForStripeSubscriptions()
  },
  data() {
    return {
      connecting: false,
      disconnecting: false,
      accDetails: {},
      balance: null,
      showDeletePlansModal: false,
      loading: {
        locations: false,
      },
      haveSaasLocations: false,
      haveWpLocations: false,
      haveYextLocations: false
    }
  },
  watch: {
    '$route.params.location_id': async function (id) {
      await this.fetchAccountDetails()
    },
  },
  methods: {
    async updateModeInDB(mode) {
      const locationObj = new Location(this.location)
      await locationObj.ref.update({
        stripe_connect_mode: mode,
      })
      this.location.stripe_connect_mode = mode
      await this.fetchAccountDetails()
      await updateCachingIndex({
        index_type: cacheUpdateEvents.STRIPE_SETTINGS,
        event_origin: this.location?.id,
      })
    },
    async changeMode(event) {
      event.preventDefault()
      event.stopPropagation()
      let mode = 'live'
      if (!event.target.checked) mode = 'test'
      let message =
        'Switching the current stripe settings to test mode, Your live products/subscriptions in funnels/websites/memberships/text2pay might not work for customers.'
      if (mode === 'test') {
        this.$uxMessage('confirmation', message, async response => {
          if (response === 'ok') {
            this.updateModeInDB(mode)
          }
        })
      } else {
        this.updateModeInDB(mode)
      }
    },
    getCurrencySymbol(currencyCode) {
      return currency[currencyCode.toUpperCase()]?.symbol || ''
    },
    async fetchAccountDetails() {
      this.connecting = true
      try {
        trackGaEvent(
          'StripeConnect',
          this.locationIntegration ? this.location.id : this.company.id,
          'Retriving Account Details',
          1
        )

        if (this.locationIntegration) {
          const { data } = await PaymentServices.retrieveStripeAccount(
            { locationId: this.location.id, liveMode: this.location.stripe_connect_mode === 'live' }
          )
          this.accDetails = data.account
          this.balance = data.balance
        } else {
          const { data } = await this.saasService.get(
            `/connect/retrieve_account?account_id=${this.company.stripeConnectId}`
          )
          this.accDetails = data.account
          this.balance = data.balance
        }
        // console.log(data);
      } catch (err) {
        //
      }
      this.connecting = false
    },
    async checkForStripeSubscriptions() {
      if (this.locationIntegration) return false

      this.loading.locations = true
      var locations = await this.$store.dispatch('locations/getAll')
      this.loading.locations = false

      for (let location of locations) {
        if (
          location.settings &&
          location.settings.saas_settings &&
          location.settings.saas_settings.saas_mode === 'activated'
        ) {
          this.haveSaasLocations = true
        }
        if (
         !this.haveWpLocations &&
         location?.reseller?.wordpress?.payment_status === 'COMPLETE'
        ) {
          this.haveWpLocations = true 
        }
        
        if (
          !this.haveYextLocations &&
          location?.reseller?.yext?.payment_status === 'COMPLETE'
        ) {
          this.haveYextLocations = true
        }
        if(
          this.haveSaasLocations &&
          this.haveWpLocations &&
          this.haveYextLocations){
          return
        }
      }
    },
    async setupAccount() {
      try {
        const account_id = this.locationIntegration
          ? this.location.stripe_connect_id
          : this.company.stripeConnectId
        const { data } = await this.saasService.post(`/connect/setup_account`, {
          account_id,
        })
        // console.log(data);
        window.open(data.accountLink.url, '_blank')
      } catch (err) {
        //
      }
    },
    async disconnect() {
      this.disconnecting = true
      try {
        trackGaEvent('StripeConnect', this.company.id, 'Start Disconnect', 1)
        const account_id = this.locationIntegration
          ? this.location.stripe_connect_id
          : this.company.stripeConnectId
        const post_data = {
          account_id,
        }
        if (this.locationIntegration) {
          post_data['locationId'] = this.location.id;
          const { data } = await PaymentServices.disconnectStripe(
            this.location.id
          )
          await updateCachingIndex({
            index_type: cacheUpdateEvents.STRIPE_SETTINGS,
            event_origin: this.location.id,
          })
          this.$store.dispatch('stripeConnect/syncAll', this.location.id)
          const locationObj = new Location(this.location)
          await locationObj.ref.update({
            'launchpad.is_stripe_disconnected': true,
          })
        } else {
          post_data['company_id'] = this.company.id
          const { data } = await this.saasService.post(
            `/connect/disconnect_account`,
            post_data
          )
        }
        trackGaEvent(
          'StripeConnect',
          this.locationIntegration ? this.location.id : this.company.id,
          'Finish Disconnect',
          1
        )
        // console.log(data);
      } catch (err) {
        //
      }
      this.disconnecting = false
    },
    async initDisconnect() {
      if (this.locationIntegration) return this.disconnect()
      this.disconnecting = true

      try {
        const { data } = await this.saasService.get(
          `/subscription-plans/${this.company.id}/`
        )
        if (data.default !== true) {
          // confirm user to delete SaaS Plans.
          this.showDeletePlansModal = true
          this.disconnecting = false
        } else {
          this.disconnect()
        }
      } catch (err) {
        //
      }
    },
  },
}
</script>

<style lang="scss">
.flex--space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.connected-acc__wrap {
  border: 2px solid #edf2f7;
  border-radius: 4px;
}
.connected-acc__overview {
  padding: 24px 24px 16px;
}
.connected-acc__overview-heading {
  opacity: 0.7;
}
.connected-acc__overview-name {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 600;
}
.connected-acc__overview-email {
  // text-align: left;
  cursor: pointer;
  opacity: 0.8;
  &:hover {
    opacity: 1;
  }
}
.connected-acc__overview-status {
  margin-left: 8px;
  border: 1px solid #4fd1c5;
  color: #4fd1c5;
  background-color: #ffffff;

  font-size: 12px;
  line-height: 14px;
  font-weight: 500;
  border-radius: 30px;
  padding: 4px 8px;
}

.connected-acc__stats {
  background-color: #f7fafc;
  border-top: 1px solid #edf2f7;
  padding: 24px 0px 16px;

  display: flex;
  flex-wrap: wrap;
}
.connected-acc__stat {
  padding: 0px 24px;
  border-right: 1px solid #dbe4ed;
  &:last-child {
    border-right: none;
  }
}
.connected-acc__stat-label {
  font-weight: 600;
}

.connect-identity__wrap {
  border: 2px solid #edf2f7;
  border-radius: 4px;
  margin-top: 12px;
}
.connect-identity__header {
  padding: 12px 24px;
  font-size: 20px;
  font-weight: 600;
}
.connect-identity__header-title {
}
.connect-identity__header-edit-btn {
  padding: 8px;
  border: 1px solid #edf2f7;
  font-size: 16px;
  line-height: 16px;
  border-radius: 4px;
  cursor: pointer;
}
.connect-identity__body {
  padding: 24px;
  border-top: 1px solid #edf2f7;
  background-color: #f7fafc;
  display: flex;
}
.connect-identity__column {
  flex: 0 0 50%;
}
.connect-identity__row {
  display: flex;
}
.connect-identity__row-label {
  font-weight: 500;
  min-width: 150px;
}

.connect-actions__row {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  flex-direction: column;
  align-items: center;
  .connect-action {
    margin: 0px 12px;
    &.--disconnect-btn {
      background-color: #a0aec0;
      color: #ffffff;
      border-radius: 4px;
      // padding: 14px 28px;
      cursor: pointer;
      font-weight: 500;
      &:hover {
        background-color: #8f9aa7;
      }
      i {
        margin-right: 4px;
      }
    }
  }
}
</style>
