import { SmartList, Contact, User } from '@/models'
import { SmartListColumnsVM } from './smart_list_columns'
import { IFilter } from './filter_vm'
import { FilterBuilder } from './filter_vm_builder'
import {
  IContactsServer,
  IContactsQuery,
  IContactsResult,
  ContactsServer,
  DisplayContactFetcher,
} from '../server/contacts_server'
import { Utils } from '@/util/utils'
import store from '@/store'
import lodash from 'lodash'
import { IColDefinition, ISortDefintion } from './interfaces'
import { SmartListColumn } from './smart_list_column'
export class SmartListVM {
  public static readonly DEFAULT_LIST_NAME = 'All'
  public static readonly LIST_SETTINGS_VIEW_NAME = 'Settings'
  public static readonly ONLY_ONE_SORT = true

  public static async fromNew(
    server: IContactsServer,
    user: User,
    locationId: string
  ) {
    const item = new SmartListVM(
      server,
      SmartListVM.createNewModel(user ? user.id : '', locationId)
    )
    if (item) await item.reset()
    //item.cols = SmartListColumnsVM.getInstanceForAllQuery( item.locationId,item.smartList.columns)
    //if (item) setTimeout(()=> item.reset(),2000)// reset is very important
    return item
  }

  public static async fromExisting(
    server: IContactsServer,
    smartList: SmartList,
    initVM: boolean = false
  ): Promise<SmartListVM> {
    const item = new SmartListVM(server, smartList)
    if (item && initVM === true) await item.reset() // reset is very important
    return item
  }

  public pageNumber: number
  public totalContacts: number
  public pageLimit: number
  public inProgress = false
  public inSilentUpdate = false
  public fetcherror: string | null = null

  public cols: SmartListColumnsVM = undefined

  private _allPageSelected = false
  private _currentPageCheck = false
  public get isCurrentPageCheck(): boolean {
    return this._currentPageCheck
  }
  public get areAllPagesSelected(): boolean {
    return this._allPageSelected
  }
  // private _filterDefintions: any[]

  public selectedContactIds: string[] = []
  public smartList: SmartList
  public fetchJustIds = false
  private _pvtDispModels: IFilter[] = [] // don't use this._pvtDispModels = []
  public displayModels: ReadonlyArray<IFilter> = this._pvtDispModels // we want read only array because displaymodel is tightly coupled with actual smartlist model

  public server: IContactsServer
  protected items = [] as Contact[]
  protected readonly url: string = ''
  protected totalPages = 0
  private hasChanged = false
  private _user: User // this will be set in getServerQuery
  //private contactsAssignedTo: string;
  private timeInProgress: NodeJS.Timeout
  public paginations: any[] = []
  public sortDefinitions: ISortDefintion[] = []

  protected constructor(server: IContactsServer, smartList: SmartList) {
    if (!server) throw new Error('Provide fetch server')
    if (
      !smartList ||
      !smartList.filterSpecs ||
      !smartList.filterSpecs.filters
    ) {
      throw new Error(
        'Specify appropriate smart list specs and filters to fetch'
      )
    }
    this.server = server
    this.smartList = smartList
  }

  public get hasUnsavedFilters(): boolean {
    return this.hasChanged
  }
  public set hasUnsavedFilters(val: boolean) {
    if (!this.hasChanged && val === true) this.hasChanged = true
  }

  public get contacts(): Contact[] {
    return this.items
  }

  public set contacts(items: Contact[]) {
    this.items = items
  }

  public async selectedForDisplayOnly(
    quantity: number = 10
  ): Promise<Contact[]> {
    try  {
      if (!this.selectedContactIds || this.selectedContactIds.length <= 0)
        return []
      const selectedIds = [...this.selectedContactIds]
      const fetcher = new DisplayContactFetcher()
      const payload = {
        locationId: this.locationId,
        contactIds: selectedIds.length > quantity ? selectedIds.slice(0,quantity) : selectedIds
      }
      return await fetcher.fetch(payload)
    } catch(err) {
      console.log(err);
      return [];
    }
  }

  public get listName(): string {
    return this.smartList.listName || ''
  }

  public get listId(): string {
    return this.smartList.id
  }

  public get locationId(): string {
    return this.smartList.locationId || ''
  }

  public get userId(): User {
    return this.smartList.userId || ''
  }

  public get currentFilters(): any {
    return this.getSerializedFilters()
    // const sm = this.smartList;
    // if (this._filterDefintions){
    //   return [...this._filterDefintions];
    // } else return [];
  }

  public async reset(
    overrideFilters?: any[],
    selectedColumns?: IColDefinition[]
  ) {
    await this.buildColsView(selectedColumns)
    await this.buildFiltersView(overrideFilters)
  }

  public async buildFiltersView(overrideFilters?: any[]) {
    if (!overrideFilters) overrideFilters = []
    overrideFilters = lodash.sortBy(overrideFilters, 'extras.isLocked')
    const uiDisplay = []
    overrideFilters.forEach(x => {
      if (!x.filterName || !x.selectedOption) return
      if (x.isLocked === true) (x.extras || {}).isLocked = x.isLocked
      const filter = FilterBuilder.getFilter(x.filterName, x.id, x.extras)
      if (filter) {
        uiDisplay.push(filter)
        filter.selectedOption = x.selectedOption
        filter.captureState()
      }
    })
    this.rebuildDisplayModels(uiDisplay)
  }

  public async buildColsView(columns?: IColDefinition[]) {
    //console.log(columns);
    this.cols = await SmartListColumnsVM.getInstance(
      this.smartList.locationId,
      columns ? columns : this.smartList.columns
    )
    this.resetColumnSortDisplay(this.smartList.sortSpecs)
  }

  public async first() {
    this.pageNumber = 1
    this.paginations = []
    await this.run()
  }

  public async last() {
    this.pageNumber = this.totalPages
    if (this.pageNumber <= 0) this.pageNumber = 1
    await this.run()
  }

  public async next() {
    this.pageNumber++
    if (this.pageNumber > this.totalPages) this.pageNumber = this.totalPages
    await this.run()
  }

  public async previous() {
    this.pageNumber--
    if (this.pageNumber <= 0) this.pageNumber = 1
    await this.run()
  }

  public async jumpTo(num: number) {
    if (num === this.pageNumber + 1) this.next()
    else if (num === this.pageNumber - 1) this.previous()
    else {
      if (this.totalPages !== 0 && num > this.totalPages)
        this.pageNumber = this.totalPages
      if (num <= 0) this.pageNumber = 1
      else if (num > this.paginations.length)
        this.pageNumber = this.paginations.length
      else this.pageNumber = num
      await this.run()
    }
  }

  public async run(params?: { retainTillFetch: boolean, silentUpdate?: boolean}) {
    try {
      this.fetcherror = null
      if (this.inProgress) return
      if (this.inSilentUpdate) return

      if (!this._pvtDispModels)
        throw new Error('Smart List VM has no query models')

      const genericFilter =
        this._pvtDispModels.filter(
          a => a.filterName === FilterBuilder.GENERIC_FILTER
        )[0] || null

      const hasGenericFilter =
        genericFilter &&
        genericFilter.selectedOption &&
        !Utils.isEmptyStr(genericFilter.selectedOption.firstValue)

      if (
        this.listName !== SmartListVM.DEFAULT_LIST_NAME &&
        !hasGenericFilter &&
        this.displayModels.length <= 0
      ) {
        this.contacts = []
        this.totalPages = 0
        this.totalContacts = 0
        return
      }


      if (params && params.silentUpdate) this.inSilentUpdate = true
      else this.inProgress = true // this controls UI loading animation

      if (!this.pageLimit) this.pageLimit = 20
      if (!this.pageNumber) this.pageNumber = 1
      if (!params || !params.retainTillFetch) this.contacts = []

      if (this.paginations.length === 0) this.paginations.push(null) // only 1st time.

      const rs = await this.getserverContacts()
      this.totalPages = rs.totalPages
      this.totalContacts = rs.allRecordsCount
      this.items = rs.pageItems
      this.pageNumber = rs.page // same as current page
      this.pageLimit = rs.pageLimit

      if (rs.searchAfter) {
        if (this.paginations.length === this.pageNumber)
          this.paginations.push(rs.searchAfter)
        else if (this.paginations.length >= this.pageNumber)
          this.paginations[this.pageNumber] = rs.searchAfter
      }

      if (this._allPageSelected === true) this.selectCurrentPage()
      else this.resetCurrentPageSelected()
    } catch (err) {
      this.fetcherror = `${err?.serverMessage || err?.message || err?.msg || err?.Error}` || 'Server Error'
      this.contacts = []
      // this.totalPages = 0
      // this.totalContacts = 0
      console.error('Error fetching smart list.')
      console.error(JSON.stringify(err))
    } finally {
      this.inProgress = false
      this.inSilentUpdate = false
    }
  }

  public async getServerQuery(): Promise<IContactsQuery> {
    if (!this._user) this._user = new User(await store.dispatch('user/get'))

    let searchAfter = null

    if (
      this.pageNumber > 1 &&
      this.paginations &&
      this.paginations.length >= this.pageNumber
    ) {
      searchAfter = this.paginations[this.pageNumber - 1]
    }

    const query: IContactsQuery = {
      location_id: this.locationId,
      user_id: this._user ? this._user.id : null,
      assigned_to: this._user && this._user.isAssignedTo ? this._user.id : null,
      page: this.pageNumber || 1,
      filters: this.getSerializedFilters() || [],
      sort: this.sortDefinitions || [],
      page_limit: this.pageLimit || 20,
      search_after: searchAfter,
      just_ids: this.fetchJustIds,
    }
    return Promise.resolve(query)
  }

  public getGenericFilter() {
    if (this._pvtDispModels)
      return (
        this._pvtDispModels.filter(
          a => a.filterName === FilterBuilder.GENERIC_FILTER
        )[0] || null
      )
    return null
  }

  public async replaceContact(contact: Contact) {
    if (this.inProgress) return
    if (!contact || !contact.id) return
    const idx = this.items.findIndex(a => a.id === contact.id)
    if (idx !== -1) this.items[idx] = contact
  }

  public async syncSingleContact(
    contactId: string,
    contactServer: IContactsServer
  ): Promise<IContactsQuery> {
    if (this.inProgress) return
    if (!contactId || !contactServer) return
    if (this.items.findIndex(a => a.id === contactId) === -1) return // no contact in view dont do anything.
    if (!this._user) this._user = new User(await store.dispatch('user/get'))
    const filter = FilterBuilder.getSingleContactFilter(contactId)
    const specs = []
    specs.push(filter.serialize())

    const query: IContactsQuery = {
      location_id: this.locationId,
      user_id: this._user ? this._user.id : null,
      assigned_to: this._user && this._user.isAssignedTo ? this._user.id : null,
      filters: specs,
      page: this.pageNumber || 1,
      sort: null,
      page_limit: 1,
      search_after: undefined,
      just_ids: false,
    }
    const result = await contactServer.fetch(query)
    if (result && result.pageItems && result.pageItems.length > 0) {
      this.replaceContact(result.pageItems[0])
    }
  }

  public async getContactsForPage(num: number): Promise<IContactsResult> {
    try {
      if (this.totalPages !== 0 && num > this.totalPages)
        this.pageNumber = this.totalPages
      if (num <= 0) this.pageNumber = 1
      else this.pageNumber = num
      if (!this.pageLimit) this.pageLimit = 20
      return this.getserverContacts()
    } catch (err) {
      console.error(err)
      throw err
    }
  }

  public async getNextContacts(): Promise<IContactsResult> {
    try {
      if (!this.pageNumber) this.pageNumber = 0
      this.pageNumber += 1
      if (this.paginations.length === 0) this.paginations.push(null) // only 1st time.
      const result = await this.getserverContacts()
      if (result) {
        if (result.searchAfter) {
          if (this.paginations.length === this.pageNumber)
            this.paginations.push(result.searchAfter)
          else if (this.paginations.length >= this.pageNumber)
            this.paginations[this.pageNumber] = result.searchAfter
        }
      }
      return result
    } catch (err) {
      console.error(err)
      throw err
    }
  }

  public getTotalPageCount() {
    return this.totalPages
  }

  //   public getQueryFilters(): any[] {
  //  _filterDefintion   return this.s|| []
  //   }

  public resetCurrentPageSelected() {
    if (!this.contacts) {
      this._currentPageCheck = false
      return
    }
    let thisPageSelectedCount = 0
    const currentPageIds = this.contacts.map(a => a.id)
    currentPageIds.forEach(i => {
      if (this.selectedContactIds.includes(i)) thisPageSelectedCount += 1
    })
    this._currentPageCheck = thisPageSelectedCount === currentPageIds.length
    if (this._currentPageCheck === false) this._allPageSelected = false
  }

  public selectCurrentPage() {
    this._currentPageCheck = true
    if (!this.selectedContactIds) this.selectedContactIds = []
    if (!this.contacts) return
    const currentPageIds = this.contacts.map(a => a.id)
    currentPageIds.forEach(i => {
      if (!this.selectedContactIds.includes(i)) this.selectedContactIds.push(i)
    })
  }

  public unSelectCurrentPage() {
    this._currentPageCheck = false
    this._allPageSelected = false
    if (!this.selectedContactIds) return
    if (!this.contacts) return
    const currentPageIds = this.contacts.map(a => a.id)
    currentPageIds.forEach(id => {
      const idx = this.selectedContactIds.findIndex(a => a === id)
      if (idx !== -1) this.selectedContactIds.splice(idx, 1)
    })
  }

  public selectAll() {
    this.selectCurrentPage()
    this._allPageSelected = true
  }

  public unSelectAll() {
    this._currentPageCheck = false
    this._allPageSelected = false
    this.selectedContactIds = []
  }

  public async setExpanded(filter: IFilter) {
    if (!this._pvtDispModels || !filter) return
    if (filter.expandedUX !== true) return
    this._pvtDispModels.forEach(x => {
      if (x !== filter) {
        x.expandedUX = false
      }
    })
  }

  public async setRecentImport(importId: string) {
    const item = FilterBuilder.getFilterByName('Import')
    if (item) {
      item.selectedOption.firstValue = importId
      item.expandedUX = true
      item.updateTitle()
      this.setExpanded(item)
      this.hasChanged = true
      if (this.timeInProgress) clearTimeout(this.timeInProgress)
      this.timeInProgress = setTimeout(() => {
        this._pvtDispModels.push(item)
        this.first()
      }, 1000)
    }
  }

  public async setNewContactFilters(
    firstName: string,
    lastName: string,
    onComplete: (boolean) => boolean | void
  ) {
    this.clearFilters()
    const filters = []
    if (firstName) {
      const item = FilterBuilder.getFilterByName('First Name')
      const temp = item.selectedOption.serialize()
      temp.firstValue = firstName
      item.selectedOption = temp
      filters.push(item)
    }
    if (lastName) {
      const item = FilterBuilder.getFilterByName('Last Name')
      const temp = item.selectedOption.serialize()
      temp.firstValue = lastName
      item.selectedOption = temp
      filters.push(item)
    }
    filters.forEach(item => {
      item.updateTitle()
    })
    this.hasChanged = true
    if (this.timeInProgress) clearTimeout(this.timeInProgress)
    this.timeInProgress = setTimeout(() => {
      // settimeout is required otherwise it causes a Maximum call stack size exceeded error
      this.rebuildDisplayModels(filters)
      if (onComplete) onComplete(true)
    }, 0)
    // if (filters && filters.length > 0) {
    //   const item = filters[filters.length - 1];
    //   item.expandedUX = true;
    //   this.setExpanded(item);
    // }
  }

  public resetColumnSortDisplay(newSorts: ISortDefintion[]) {
    this.sortDefinitions = newSorts
    if (!this.cols || !this.sortDefinitions) return
    this.cols.setSortOrder(this.sortDefinitions.map(a => a.fieldName))
  }

  public async sort(toggleCol?: SmartListColumn) {
    if (toggleCol) {
      const sorts = [...this.sortDefinitions]
      const addName = toggleCol.isCustomField
        ? toggleCol.sortName
        : Utils.serverReady(toggleCol.sortName)
      const idx = sorts.findIndex(a => a.fieldName === addName)
      if (idx !== -1) {
        toggleCol.toggleSortDirection()
        sorts.splice(idx, 1)
      } else {
        toggleCol.sortDirection = 'desc'
      }
      if (SmartListVM.ONLY_ONE_SORT === true) {
        sorts.forEach(a => this.cols.removeSort(a.fieldName)) // can bind with UI click
        sorts.splice(0, sorts.length)
      }
      const sortDefinition = {
        fieldName: addName,
        direction: toggleCol.sortDirection || 'desc',
        isCustomField: toggleCol.isCustomField,
      }
      if (toggleCol.parentCustomFieldId)
        sortDefinition.parentCustomFieldId = toggleCol.parentCustomFieldId
      sorts.unshift(sortDefinition)
      this.resetColumnSortDisplay(sorts)
    }
    await this.first()
  }

  public async addFilterByName(
    selected: string,
    onComplete: (boolean) => void | boolean,
    params?: { value: string }
  ) {
    let shouldUpdateView = false
    try {
      const item = FilterBuilder.getFilterByName(selected)
      if (item) {
        item.expandedUX = true
        item.updateTitle()
        this.addDisplayModelItem(item)
        this.setExpanded(item)
        if (item && item.selectedOption && params && params.value)
          item.selectedOption.firstValue = params.value
        if (item && item.selectedOption && item.selectedOption.firstValue)
          shouldUpdateView = true
      }
    } catch (err) {
      console.error(err)
    } finally {
      if (onComplete) onComplete(shouldUpdateView)
    }
  }

  public filterUpdate(item: IFilter) {
    if (!item || !item.id) return
    const index = this._pvtDispModels.findIndex(a => a.id === item.id)
    if (index <= -1) return
    if (item) item.updateTitle()
  }

  public clearFilters() {
    this.removeFilters(this._pvtDispModels, null)
    this.selectedContactIds = []
    this.paginations = []
    this.pageNumber = 0
  }

  public removeFilters(items: IFilter[], onComplete: () => void) {
    if (!items || items.length <= 0) return
    const temporary = [...this._pvtDispModels]
    this.rebuildDisplayModels([]) // temporarily set display models to empty array and then reset changes using settimeout to avoid error: 'RangeError: Maximum call stack size exceeded"'
    items.forEach(item => {
      // if (this.smartList) this.smartList.removeFilter(item.id);
      const idx = temporary.findIndex(a => a.id === item.id)
      if (idx !== -1) {
        const removedFilter = temporary[idx]
        temporary.splice(idx, 1)
        if (removedFilter.cannotPersist !== true) this.hasChanged = true //set hasChanged only if the filter is not a temporary filter
      }
      // const idx2 = this._filterDefintions.findIndex((a) => a.id !== item.id);
      // if(idx2 !== -1) this._filterDefintions.splice(idx2,1);
    })
    if (this.timeInProgress) clearTimeout(this.timeInProgress)
    this.timeInProgress = setTimeout(() => {
      // timeout prevents UI Range Max error gives error: 'RangeError: Maximum call stack size exceeded"'
      try {
        this.rebuildDisplayModels(temporary)
      } catch (err) {
        console.error(err)
      } finally {
        if (onComplete) onComplete()
      }
    }, 0)
  }

  public async saveList() {
    if (!this.smartList) return
    try {
      this.inProgress = true
      this.smartList.columns = this.cols.getSelectedColDefinitions()
      this.smartList.sortSpecs = this.sortDefinitions
      if (!this.smartList.filterSpecs) this.smartList.filterSpecs = {}
      this.smartList.filterSpecs.filters = [...this.getSerializedFilters(true)]

      await this.smartList.save()
      this.hasChanged = false
      this._pvtDispModels.forEach(x => x.captureState())
    } catch (err) {
      console.error(err)
    } finally {
      this.inProgress = false
    }
  }

  private getSerializedFilters(isForPersistence: boolean = false) {
    if (!this._pvtDispModels || this._pvtDispModels.length <= 0) return []
    if (isForPersistence)
      return this._pvtDispModels
        .filter(a => a.cannotPersist !== true)
        .map(a => a.serialize({ isForPersistence: true }))
    else
      return this._pvtDispModels.map(a =>
        a.serialize({ isForPersistence: false })
      )
  }

  public async deleteList() {
    await this.smartList.delete()
  }

  public getListCopy(): SmartList {
    if (!this.smartList || !this._pvtDispModels) return
    const newList = this.smartList.copy()
    newList.displayOrder = 10000
    newList.columns = this.cols.getSelectedColDefinitions()
    newList.sortSpecs = [...this.sortDefinitions]
    this._pvtDispModels.forEach((a: IFilter) => {
      newList.filterSpecs.filters = this.getSerializedFilters(true) // smart_list doc can be directly saved by fetcher
    })
    return newList
  }

  public get hasChanges(): boolean {
    return this.hasFilterChanges || this.hasColumnChanges || this.hasSortChanges
  }

  public get hasFilterChanges(): boolean {
    if (this.listName === SmartListVM.DEFAULT_LIST_NAME) return false
    if (this.hasChanged === true) return true

    if (this._pvtDispModels) {
      const permanentFilters = this._pvtDispModels.filter(
        a => a.cannotPersist !== true
      )
      for (let i = 0; i < permanentFilters.length; i++) {
        if (this._pvtDispModels[i].hasChanges) return true
      }
    }
    return false
  }

  public get hasColumnChanges(): boolean {
    if (this.listName === SmartListVM.DEFAULT_LIST_NAME) return false

    if (
      this.smartList &&
      (!this.smartList.columns || this.smartList.columns.length <= 0)
    ) {
      this.smartList.columns = this.cols.getSelectedColDefinitions()
      this.smartList.save()
      return false
    }

    if (!this.cols) return false // somebody is checking changes for the cols has initialized.

    const newCols =
      this.cols && this.cols.selected ? this.cols.selected.map(a => a.key) : []
    const existingCols =
      this.smartList && this.smartList.columns
        ? this.smartList.columns.map(a => a.key)
        : []
    const difference = lodash.xor(
      lodash.sortBy(existingCols),
      lodash.sortBy(newCols)
    ) as string[]
    //return !lodash.isEqual(lodash.sortBy(existingCols), lodash.sortBy(newCols))
    if (difference && this.cols.allOptionKeys) {
      for (const d of difference)
        if (this.cols.allOptionKeys.includes(d)) return true
      // if different includes columns no longer available to locaiton then no need to flag it as change
      // (delete from custom fields or deprecated ones)
    }
    return false
  }

  public get hasSortChanges(): boolean {
    if (this.listName === SmartListVM.DEFAULT_LIST_NAME) return false

    if (this.smartList && !this.smartList.sortSpecs && this.sortDefintions) {
      this.smartList.sortSpecs = this.sortDefintions
      this.smartList.save()
      return false
    }

    if (!this.sortDefinitions) return false // somebody is checking changes for the cols has initialized.
    return !lodash.isEqual(this.sortDefinitions, this.smartList.sortSpecs) // don't sort parameters
  }

  private async getserverContacts(): Promise<IContactsResult> {
    try {
      return this.server.fetch(await this.getServerQuery())
    } catch (err) {
      console.error('Error fetching contacts')
      console.error(err)
    }
  }

  private rebuildDisplayModels(items: IFilter[]) {
    this.displayModels = this._pvtDispModels = items
  }

  public addDisplayModelItem(item: IFilter) {
    // do we want to check for duplicate id - may be in future ?
    if (!this._pvtDispModels) this.displayModels = this._pvtDispModels = []
    this._pvtDispModels.push(item)
    if (item.cannotPersist !== true) this.hasChanged = true //set hasChanged only if the filter is not a temporary filter
  }

  private hasFilter(filterName: string) {
    // do we want to check for duplicate id - may be in future ?
    if (!this._pvtDispModels) this.displayModels = this._pvtDispModels = []
    return (
      this._pvtDispModels.findIndex(a => a.filterName === filterName) !== -1
    )
  }

  public static createNewModel(userId: string, locationId: string) {
    const nw = new SmartList()
    nw.listName = SmartListVM.DEFAULT_LIST_NAME
    nw.locationId = locationId
    nw.userId = userId
    return nw
  }

  public removeContacts(contactsIds: string[]) {
    this.items = this.items.filter(item => !contactsIds.includes(item.id))
    this.totalContacts -= contactsIds.length
  }
}

// tslint:disable-next-line: max-classes-per-file
export class ReadOnlySmartListVM extends SmartListVM {
  protected constructor(server: IContactsServer, smartList: SmartList) {
    super(server, smartList)
  }

  public static async fromVM(smartListVM: SmartListVM) {
    const item = await SmartListVM.fromExisting(
      smartListVM.server,
      smartListVM.smartList.copy()
    )
    await item.reset(smartListVM.currentFilters)
    const genericFilter = smartListVM.getGenericFilter()
    if (
      genericFilter &&
      genericFilter.selectedOption &&
      !Utils.isEmptyStr(genericFilter.selectedOption.firstValue)
    ) {
      item.addDisplayModelItem(genericFilter)
    }
    return item
  }

  public static async forFilters(
    filters: IFilter[],
    locationId: string,
    assignedTo: string
  ) {
    const queryRunner = new ContactsServer(FilterBuilder.ES_SEARCH_ENDPOINT)
    const sm = SmartListVM.createNewModel(
      assignedTo ? assignedTo : '',
      locationId
    )
    const vm = new ReadOnlySmartListVM(queryRunner, sm)
    if (filters != null) filters.forEach(f => vm.addDisplayModelItem(f))
    return vm
  }

  public async saveList() {
    throw new Error('Cannot save a readonly VM. Use it only in operations.')
  }

  // public constructor(server: IContactsServer, smartList: SmartList) {
  //   super(server, smartList);
  // }

  // public async reset(overrideFilters?: [], selectedColumns?: IColDefinition[]) { // this overrides base method
  //   await this.buildColsView(selectedColumns);
  //   await this.buildFiltersView(overrideFilters);
  // }
}
