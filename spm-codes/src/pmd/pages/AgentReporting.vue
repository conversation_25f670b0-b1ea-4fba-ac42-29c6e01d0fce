<template>
  <section class="hl_wrapper">
    <section class="hl_wrapper--inner customers" id="customers">
      <div class="container-fluid agent_reporting_main">
        <div class="hl_controls mb-0">
          <div class="hl_controls--left">
            <h2>Agent Reporting</h2>
          </div>
          <div class="hl_controls--right">
            <select
              v-model="selectedUser"
              class="selectpicker --white hl_datepicker px-0"
              data-width="200px"
              @change="updateSelectUser"
              v-if="showDropDown"
              multiple
              data-actions-box="true"
            >
              <option v-for="user in this.$store.state.users.users" :key="user.id" :value="user.id">{{user.first_name}} {{user.last_name}}</option>
              <option v-if="this.$store.state.users.users.length === 0" key="no_users" value="no_users">No Users</option>
            </select>
            <div style="margin-right: 30px;" v-if="showDropDown">
              <button type="button" class="btn btn-blue" @click="fetchData">Fetch</button>
            </div>
            <div
              id="hl_datepicker"
              class="hl_datepicker d-flex justify-content-center align-items-center"
            >
              <i class="icon icon-calendar"></i>
              <div class="d-flex" style="width: 220px;">
                <vue-ctk-date-time-picker
                  v-model="agentReportingFilterDate"
                  :right="true"
                  :range="true"
                  color="#188bf6"
                  :only-date="true"
                  enable-button-validate
                  :noClearButton="true"
                  name="start_time"
                  format="YYYY-MM-DDTHH:mm:ss.sss"
                  formatted="ddd, MMM Do"
                  :maxDate="lastDate"
                  @validate="setSelectedDate"
                />
              </div>
              <i class="icon icon-arrow-down-1"></i>
            </div>
            <b-button v-b-toggle.collapse-2 variant="link">Compare
              <span
                style="margin-top: -7px;"
                class="input-group-addon"
                v-b-tooltip.hover
                title="We have a custom compare date by default e.g- if you have 30 days as curent date range then compare date range also 30 days. You can change it as per need."
              >
                <i class="fas fa-question-circle"></i>
              </span>
            </b-button>
            <span v-b-tooltip.hover @click="refresh" class="refresh-btn" title="Refresh Data.">
              <i class="fas fa-sync"></i>
            </span>
          </div>
        </div>
        <b-collapse id="collapse-2" class="mb-2">
          <div style="margin-right: 174px;" class="d-flex justify-content-end align-items-center">
            <div
              id="hl_datepicker"
              class="hl_datepicker d-flex align-items-center"
            >
              <i class="icon icon-calendar"></i>
              <div class="d-flex" style="width: 220px;">
                <vue-ctk-date-time-picker
                  v-model="agentReportingCompareFilterDate"
                  :right="true"
                  :range="true"
                  color="#188bf6"
                  :only-date="true"
                  enable-button-validate
                  :noClearButton="true"
                  name="start_time"
                  formatted="ddd, MMM Do"
                  :maxDate="compareMaxDate"
                   @validate="setCompareDate"
                />
              </div>
              <i class="icon icon-arrow-down-1"></i>
            </div>
          </div>
        </b-collapse>
        <div class="py-1 my-2 info-blue --blue">
            <div class="mx-3 my-1">
               <span class="input-group-addon mx-1">
                 <i class="fa fa-exclamation-triangle" ></i>
              </span>
              <span class="mx-1 my-2">Accurate stats are available from October 21, 2020 onwards.</span>
            </div>
        </div>
        <div class="agent_reporting">
          <div class="row">
            <div class="col-lg-8 order-sm-2 order-lg-1">
              <div class="row">
                <AgentReportingOpportunityStats :data="opportunityData" :isLoading="isLoadingOpportunityData"/>
                <AgentReportingConversionStats :data="conversionData" :isLoading="isLoadingConversionData" @pipeline-changed="changePipelineId"/>
              </div>
              <div class="row">
                <AgentReportingSmsStats :data="smsData" :isLoading="isLoadingSmsData"/>
                <AgentReportingEmailStats :data="emailData" :isLoading="isLoadingEmailData"/>
              </div>
            </div>
            <div class="col-lg-4 order-sm-1 order-lg-2 mb-3">
              <AgentReportingLeaderBoard :data="leaderboardData" :isLoading="isLoadingLeaderboardData" @parameter-changed="changeLeaderboardParameter"/>
            </div>
          </div>
          <div class="row">
            <div class="col">
              <AgentReportingCallStats :data="callData" :isLoading="isLoadingCallData"/>
            </div>
          </div>
          <div class="row">
            <AgentReportingEfficiencyStats :data="salesVelocity" :isLoading="isLoadingSalesVelocity"/>
            <!-- <AgentReportingChannelUsageStats/> -->
           <!--  <AgentReportingTimeSpentStats/>
            <AgentReportingReviewStats/>
            <AgentReportingAppointmentStats/> -->
          </div>
          <!-- <div class="row">
            <div class="col">
              <AgentReportingLogsTable/>
            </div>
          </div> -->
        </div>
      </div>
    </section>
  </section>
</template>

<script lang="ts">
import Vue from 'vue'
import moment from 'moment'
import axios from 'axios'
import defaults from '@/config'
import { mapState } from 'vuex'

import { User } from '../../models'
const AgentReportingOpportunityStats = () => import('./../components/agent_reporting/AgentReportingOpportunityStats')
const AgentReportingConversionStats = () => import('./../components/agent_reporting/AgentReportingConversionStats')
const AgentReportingChannelUsageStats = () => import('./../components/agent_reporting/AgentReportingChannelUsageStats')
const AgentReportingEfficiencyStats = () => import('./../components/agent_reporting/AgentReportingEfficiencyStats')
const AgentReportingLeaderBoard = () => import('./../components/agent_reporting/AgentReportingLeaderBoard')
const AgentReportingCallStats = () => import('./../components/agent_reporting/AgentReportingCallStats')
const AgentReportingSmsStats = () => import('./../components/agent_reporting/AgentReportingSmsStats')
const AgentReportingEmailStats = () => import('./../components/agent_reporting/AgentReportingEmailStats')
const AgentReportingTimeSpentStats = () => import('./../components/agent_reporting/AgentReportingTimeSpentStats')
const AgentReportingReviewStats = () => import('./../components/agent_reporting/AgentReportingReviewStats')
const AgentReportingAppointmentStats = () => import('./../components/agent_reporting/AgentReportingAppointmentStats')
const AgentReportingLogsTable = () => import('./../components/agent_reporting/AgentReportingLogsTable')

export default Vue.extend({
  components: {
    AgentReportingOpportunityStats,
    AgentReportingConversionStats,
    AgentReportingChannelUsageStats,
    AgentReportingEfficiencyStats,
    AgentReportingLeaderBoard,
    AgentReportingCallStats,
    AgentReportingSmsStats,
    AgentReportingEmailStats,
    AgentReportingTimeSpentStats,
    AgentReportingReviewStats,
    AgentReportingAppointmentStats,
    AgentReportingLogsTable
  },
  data() {
    return {
      selectedUser: [],
      location_id: this.$route.params.location_id,
      pipelineId: '',
      agentReportingFilterDate: {
        start: moment()
          .subtract(30, 'days')
          .startOf('day')
          .toDate(),
        end: moment()
          .endOf('day')
          .toDate()
      },
      lastDate: moment().toDate().toString(),
      agentReportingCompareFilterDate: {
        start: moment()
          .subtract(60, 'days')
          .startOf('day')
          .toDate(),
        end: moment()
           .subtract(31, 'days')
          .endOf('day')
          .toDate()
      },
      leaderboardParameter: '',
      callData: {},
      emailData: {},
      smsData: {},
      opportunityData: {},
      conversionData: {},
      leaderboardData: {},
      salesVelocity: {},
      isLoadingConversionData: false,
      isLoadingCallData: false,
      isLoadingEmailData: false,
      isLoadingSmsData: false,
      isLoadingOpportunityData: false,
      isLoadingLeaderboardData: false,
      isLoadingSalesVelocity: false,
      emailReportingUrl: defaults.emailReportingUrl,
    }
  },
   watch: {
    '$route.params.location_id': async function(id) {
      if(id !== this.location_id){
        this.location_id = id;
        this.initialSelectedUser()
        await this.fetchMarketingCampaigns()
        this.fetchData()
      }
    },
  },
  async mounted() {
    this.canAccessPage()
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
    this.initialSelectedUser()
    await this.fetchMarketingCampaigns()
    this.fetchData()
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  computed: {
    ...mapState('user', {
      showDropDown: (s: UserState) => {
        const user = s.user ? new User(s.user) : undefined
        return (user && !user.isAssignedTo)
      },
      user: (s: UserState) => {
				return s.user ? new User(s.user) : undefined;
			}
    }),
    compareMaxDate() {
      return moment(new Date(this.agentReportingFilterDate.start).toISOString()).subtract(1, 'days').endOf('day').toDate().toString()
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion']
		},
  },
  methods: {
    canAccessPage(){
      if(this.$store.state.company.company.stripe_active_plan.includes("497") && ((this.user && this.user.permissions.agent_reporting_enabled) || (this.user && this.user.role === 'admin' && this.user.type === 'agency' && this.user.permissions.agent_reporting_enabled !== false))){
        console.log('Agent Reporting')
      }else{
        if (this.getSideBarVersion = 'v2') {
           this.$router.push({ name: 'dashboard-v2' })
        } else {
          this.$router.push({ name: 'dashboard' })
        }
      }
    },
    refresh() {
      this.fetchData()
    },
    updateSelectUser() {
      // console.log(this.selectedUser)
      // this.fetchData(false)
    },
    initialSelectedUser() {
      const allUsers = this.$store.state.users.users;
      if(allUsers.length > 0 && allUsers.find(user => user.id === this.$store.state.user.user.id)){
        this.selectedUser = [this.$store.state.user.user.id]
      }else{
        this.selectedUser = [this.$store.state.users.users.length > 0 ? this.$store.state.users.users[0].id : 'no_users']
      }
    },
    setSelectedDate() {
      const timeDiff = moment(new Date(this.agentReportingFilterDate.end).toISOString()).diff(new Date(this.agentReportingFilterDate.start).toISOString());
      var pastISODate = moment(new Date(this.agentReportingFilterDate.start).toISOString())
          .subtract(timeDiff)
          .toISOString()
      this.agentReportingCompareFilterDate = {
        start : moment(pastISODate, 'YYYY-MM-DD').add(1, 'days')
          .startOf('day')
          .toDate(),
        end : moment(new Date(this.agentReportingFilterDate.start).toISOString()).subtract(1, 'days')
          .endOf('day')
          .toDate()
      };
      this.fetchData()
    },
    setCompareDate() {
      this.fetchData()
    },
    async fetchMarketingCampaigns() {
      await Promise.all([
        this.$store.dispatch('campaigns/syncAll', this.$route.params.location_id)
      ])

      let tempCampaigns = this.$store.state.campaigns.campaigns;
      this.marketingCampaigns = tempCampaigns.map((campaign) => campaign.id)
    },
    async fetchData(fetchLeaderBoard = true) {
      this.isLoadingCallData = true
      this.isLoadingEmailData = true
      this.isLoadingChannelData = true
      this.isLoadingOpportunityData = true
      this.isLoadingSalesVelocity = true
      this.isLoadingSmsData = true
      this.callData = {}
      this.emailData = {}
      this.smsData = {}
      this.opportunityData = {}
      this.conversionData = {}
      this.salesVelocity = {}

      const after_date = {
        start: new Date(this.agentReportingFilterDate.start).toISOString(),
        end: new Date(this.agentReportingFilterDate.end).toISOString()
      };
      const before_date = {
        start: new Date(this.agentReportingCompareFilterDate.start).toISOString(),
        end: new Date(this.agentReportingCompareFilterDate.end).toISOString()
      };

      const allUsersSelected = this.selectedUser.length === this.$store.state.users.users.length
      /** ==== Fetching call metrics ========**/
      axios
        .post(`${defaults.phoneCallReportingService}/getAgentCallMetrics`, {
          location_id: this.location_id,
          after_date,
          before_date,
          user_id: this.selectedUser
        })
        .then(response => {
          this.callData = response.data
          this.isLoadingCallData = false
        })
        .catch(error => {
          this.isLoadingCallData = false
          console.log('Error fetching stats calls data', error)
        })
      /** ==== Fetching email stats ========**/
      axios
        .post(`${this.emailReportingUrl}/agent_reporting/email_stats`, {
          location_id: this.location_id,
          after_date,
          before_date,
          user_id: this.selectedUser
        })
        .then(response => {
          this.emailData = response.data.response
          this.isLoadingEmailData = false
        })
        .catch(error => {
          this.isLoadingEmailData = false
          console.log('Error fetching stats calls data', error)
        })

      /** ======== Fetching sms stats ======== **/
      axios
        .post(`${this.emailReportingUrl}/agent_reporting/user_sms_stats`, {
          location_id: this.location_id,
          current_period: after_date,
          previous_period: before_date,
          user_id: this.selectedUser,
          manual_sms: true
        })
        .then(response => {
          this.smsData = response.data.result
          this.isLoadingSmsData = false
        })
        .catch(error => {
          this.isLoadingSmsData = false
          console.log('Error fetching stats calls data', error)
        })

      /** ======== Fetching opportunity stats ======== **/
      axios
        .post(`${defaults.opportunityReportingService}/opportunity_stats`, {
          location_id: this.location_id,
          after_date,
          before_date,
          user_id: allUsersSelected ? null : this.selectedUser,
        })
        .then(response => {
          this.opportunityData = response.data.result
          this.isLoadingOpportunityData = false
        })
        .catch(error => {
          this.isLoadingOpportunityData = false
          console.log('Error fetching stats opportunity data', error)
        })

      /** ======== Fetching sales_velocity stats ======== **/
      axios
          .post(`${defaults.opportunityReportingService}/sales_velocity`, {
            location_id: this.location_id,
            after_date,
            before_date,
            user_id: this.selectedUser
          })
          .then(response => {
            this.salesVelocity = response.data.result
            this.isLoadingSalesVelocity = false
          })
          .catch(error => {
            this.isLoadingSalesVelocity = false
            console.log('Error fetching stats calls data', error)
          })

      /** ======== Fetching conversion stats ======== **/
      this.fetchConversationData();

      /** ======== Fetching leader board ======== **/
      if(fetchLeaderBoard) this.fetchLeaderboardData()
    },
    async fetchConversationData(){
      this.isLoadingConversionData = true;
      const after_date = {
        start: new Date(this.agentReportingFilterDate.start).toISOString(),
        end: new Date(this.agentReportingFilterDate.end).toISOString()
      };
      const before_date = {
        start: new Date(this.agentReportingCompareFilterDate.start).toISOString(),
        end: new Date(this.agentReportingCompareFilterDate.end).toISOString()
      };
      if(this.pipelineId){
        axios
          .post(`${defaults.opportunityReportingService}/conversion_stats`, {
            location_id: this.location_id,
            pipeline_id: this.pipelineId,
            after_date,
            before_date,
            user_id: this.selectedUser
          })
          .then(response => {
            this.conversionData = response.data.result
            this.isLoadingConversionData = false
          })
          .catch(error => {
            this.isLoadingConversionData = false
            console.log('Error fetching stats calls data', error)
          })
      }else{
        this.isLoadingConversionData = false
      }
    },
    async fetchLeaderboardData(){
      this.isLoadingLeaderboardData = true
      this.leaderboardData = {}
      const after_date = {
        start: new Date(this.agentReportingFilterDate.start).toISOString(),
        end: new Date(this.agentReportingFilterDate.end).toISOString()
      };
      const before_date = {
        start: new Date(this.agentReportingCompareFilterDate.start).toISOString(),
        end: new Date(this.agentReportingCompareFilterDate.end).toISOString()
      };
      let user_ids = []
      for(let user of this.$store.state.users.users){
        user_ids.push(user.id)
      }
      if(this.leaderboardParameter === '') return;
      axios
        .post(`${defaults.opportunityReportingService}/leaderboard_stats`, {
          location_id: this.location_id,
          after_date,
          user_ids,
          parameter: this.leaderboardParameter
        })
        .then(response => {
          this.leaderboardData = response.data && response.data.result || []
          this.isLoadingLeaderboardData = false
        })
        .catch(error => {
          this.isLoadingLeaderboardData = false
          console.log('Error fetching leaderboard data', error)
        })
    },
    changePipelineId(newPipelineId: string){
      this.pipelineId = newPipelineId
      this.fetchConversationData()
    },
    changeLeaderboardParameter(parameter: string){
      this.leaderboardParameter = parameter
      this.fetchLeaderboardData()
    }
  }
})
</script>

<style>
.agent_reporting .card {
  height: 100%;
  font-size: 0.90rem;
  font-weight: 450;
}
.agent_reporting .card .card-header h2 {
  font-size: 1.5rem !important;
  font-weight: 500;
}
.agent_reporting_main .hl_datepicker {
  padding: 0px 10px;
}
.agent_reporting_main .dropdown .dropdown-menu {
  max-height: 245px;
  overflow: auto;
}
.agent_reporting .table tbody tr td {
  border-top: none;
  border-bottom: 1px #cbd3d7 dotted;
  text-align: right;
}
.agent_reporting .table tbody tr:last-child td {
  border-bottom: none !important;
}
.agent_reporting .table tbody tr td:first-child {
  text-align: left;
  padding-left: 0;
}
.agent_reporting .table tbody tr td:last-child {
  white-space: nowrap;
  padding-right: 0;
}
.agent_reporting .table thead tr:last-child th {
  border-bottom: none !important;
}
.agent_reporting .table thead tr th {
  text-align: right;
}
.agent_reporting .table thead tr th:first-child {
  text-align: left;
  padding-left: 0;
}
.agent_reporting .table thead tr th:last-child {
  padding-right: 0;
}
.agent_reporting .card {
  -webkit-box-shadow: 0px 1px 5px -2px rgba(64,64,64,1);
  -moz-box-shadow: 0px 1px 5px -2px rgba(64,64,64,1);
  box-shadow: 0px 1px 5px -2px rgba(64,64,64,1);
}
.agent_reporting .card .card-body {
  padding-bottom: 5px;
}
.agent_reporting .card .card-header {
  border: none;
  padding-bottom: 0;
}
.refresh-btn{
  width: 38px;
  height: 40px;
  background: white;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  cursor: pointer;
}
.agent_reporting_main .bootstrap-select > .btn.dropdown-toggle .filter-option{
  top: 0px
}
@media (max-width: 991px){
  .agent_reporting .table tbody tr:nth-child(odd) {
     background-color:transparent;
  }
}
@media (max-width: 991px){
  .agent_reporting .card .table tbody tr {
      border-top: none;
  }
}
</style>
