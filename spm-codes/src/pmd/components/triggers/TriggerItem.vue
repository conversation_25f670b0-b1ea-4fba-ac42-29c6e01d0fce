<template>
  <div class="hl_triggers--item w-100">
    <div class="triggers_name d-flex">
      <router-link
        :to="{ name: 'triggers_detail', params: { trigger_id: trigger.id } }"
        tag="a"
      >
        <h4 v-if="trigger.title">{{ trigger.title | capitalize }}</h4>
        <h4 v-else-if="trigger.type">
          {{ formatType(trigger.type) | capitalize }}
        </h4>
        <h4 v-else>Empty trigger</h4>
      </router-link>
    </div>
    <div class="triggers_actions">
      <div class="dropdown">
        <button
          class="btn btn-light4 dropdown-toggle"
          type="button"
          data-toggle="dropdown"
          aria-haspopup="true"
          aria-expanded="false"
          :disabled="trigger.workflowId"
        >
          {{ getStatus(trigger) }}
        </button>
        <div class="dropdown-menu dropdown-menu-right">
          <a
            class="dropdown-item"
            href="javascript:void(0);"
            @click.prevent="trigger.toggleActive"
            v-if="trigger.active"
            >Draft</a
          >
          <a
            class="dropdown-item"
            href="javascript:void(0);"
            @click.prevent="trigger.toggleActive"
            v-else-if="!trigger.loopIdentified"
            >Activate</a
          >
        </div>
      </div>
      <div class="btn-group">
        <button
          type="button"
          class="btn btn-primary"
          @click.prevent="detailPage(trigger)"
          :disabled="trigger.workflowId"
        >
          Edit
        </button>
        <button
          type="button"
          class="btn btn-primary dropdown-toggle dropdown-toggle-split"
          data-toggle="dropdown"
          aria-haspopup="true"
          aria-expanded="true"
          :disabled="trigger.workflowId"
        ></button>
        <div class="dropdown-menu dropdown-menu-right">
          <a
            class="dropdown-item"
            href="javascript:void(0);"
            @click.prevent="cloneTrigger(trigger)"
            >Duplicate</a
          >
          <a
            v-if="dateBasedTrigger.includes(trigger.type)"
            class="dropdown-item"
            href="javascript:void(0);"
            @click.prevent="runTrigger(trigger)"
            >Run Now</a
          >
          <a
            class="dropdown-item"
            href="javascript:void(0);"
            @click.prevent="deleteTrigger(trigger)"
            >Delete</a
          >

          <span
            class="dropdown-divider"
            v-if="availableFolders.length > 0 || folder"
          ></span>
          <!-- removeFromFolder -->
          <a
            class="dropdown-item"
            tabindex="-1"
            href="javascript:void(0);"
            v-if="folder"
            @click="removeFromFolder(trigger)"
            >Remove From folder</a
          >
          <span class="dropdown-submenu" v-if="availableFolders.length > 0">
            <a class="dropdown-item" tabindex="-1" href="javascript:void(0);"
              >Move to folder <i class="fas fa-angle-right hint-icon"></i
            ></a>
            <div class="dropdown-menu triggerItem">
              <span
                class="dropdown-item"
                style="cursor: pointer;"
                v-for="(folder, index) in this.availableFolders"
                :key="index"
                @click="addToFolder(folder.id, trigger)"
              >
                <a tabindex="-1" href="javascript:void(0);">{{
                  folder.name
                }}</a>
              </span>
            </div>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Trigger } from '../../../models'
import { EventBus } from '@/models/event-bus'
import firebase from 'firebase/app'

export default Vue.extend({
  props: ['trigger', 'folders', 'folder'],
  data() {
    return {
      dateBasedTrigger: [
        'task_due_date_reminder',
        'custom_date_reminder',
        'birthday_reminder',
        'opportunity_decay'
      ],
      copyValues: {
        visible: false,
        trigger: undefined as Trigger | undefined,
        currentLocationId: ''
      }
    }
  },
  computed: {
    availableFolders() {
      if (!this.folders) return []
      return this.folders.filter(
        (folder: any) => folder.id !== this.trigger.folderId
      )
    }
  },
  methods: {
    detailPage(trigger?: Trigger) {
      this.$router.push({
        name: 'triggers_detail',
        params: trigger ? { trigger_id: trigger.id } : {}
      })
    },
    async addToFolder(id: string, trigger: Trigger) {
      if (!trigger) return

      trigger.folderId = id
      await trigger.save()
    },
    async removeFromFolder(trigger: Trigger) {
      if (!trigger) return
      await trigger.ref.update({
        folder_id: firebase.firestore.FieldValue.delete()
      })
    },
    getStatus(trigger: Trigger) {
      return !trigger.active ? 'Draft' : 'Active'
    },
    async detailPage(trigger?: Trigger) {
      this.$router.push({
        name: 'triggers_detail',
        params: trigger ? { trigger_id: trigger.id } : {}
      })
    },
    async runTrigger(trigger: Trigger) {
      this.$emit('runTrigger', trigger)
    },
    cloneTrigger(trigger?: Trigger) {
      this.$emit('cloneTrigger', trigger)
    },
    async deleteTrigger(trigger: Trigger) {
      if (confirm('Are you sure you want to delete this trigger?')) {
        await trigger.delete()
      }
    },
    formatType(type: string) {
      return type.split('_').join(' ')
    }
  }
})
</script>

<style lang="css" scope>
.dropdown-submenu {
  position: relative;
}

.dropdown-submenu > .dropdown-menu {
  top: 0px;
  right: 2px;
  left: auto !important;
}

.dropdown-submenu:hover > .dropdown-menu {
  display: block;
}

.dropdown-item > a {
  color: #607179;
}

#dropdownMenu1::after {
  content: none;
}

.dropdown-divider {
  margin-bottom: 5px !important;
}

.triggers_name h4 {
  color: #188bf6;
}

.triggerItem {
  max-height: 300px;
  max-width: 250px;
  overflow-y: scroll;
  bottom: -40px !important;
  top: auto !important;
}
.triggerItem > span {
  white-space: normal;
}
</style>
