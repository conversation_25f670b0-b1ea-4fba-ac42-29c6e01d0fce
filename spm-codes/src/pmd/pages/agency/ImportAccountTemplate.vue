<template>
  <div>
    <div class="container-fluid">
      <div class="card">
        <div class="card-header --no-right-padding">
          <h3>Import Snapshot</h3>
        </div>
        <div class="card-body" v-if="share">
          <div style="text-align:center;" v-if="!alreadyUsed">
            <h6>Do you want to import this snapshot to this account?</h6>
            <button
              :disabled="importing"
              style="margin-top: 20px;"
              class="btn btn-lg btn-success import-btn"
              @click.prevent="importSnapshot"
            >
              <span>{{ importing ? 'Importing..' : 'Yes! Import now.' }}</span>
              <moon-loader v-if="importing" :loading="importing" color="#fff" size="24px" />
            </button>
          </div>
          <div class="card-body" v-else>
            <div style="text-align:center;">
              <h6>The snapshot link you are using has already been used.</h6>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import TopBar from '@/pmd/components/TopBar.vue'
import { ShareAccount, AuthUser } from '@/models/'

export default Vue.extend({
  components: { TopBar },
  data() {
    return {
      share: undefined as ShareAccount | undefined,
      alreadyUsed: false,
      authUser: {} as AuthUser,
      importing: false
    }
  },
  async created() {
    this.share = await ShareAccount.getById(
      this.$router.currentRoute.params.share_id
    )
    if (
      this.share &&
      this.share.type !== 'permanent_link' &&
      this.share.redeemedBy
    ) {
      this.alreadyUsed = true
    }
    this.authUser = await this.$store.dispatch('auth/get')
  },
  methods: {
    async importSnapshot() {
      if (this.importing) return

      try {
        const shareId = this.share ? this.share.id : null

        if (!shareId) {
          alert('Invalid import data!')
          return
        }

        this.importing = true
        const response = await this.$http.post('/share/redeem', {
          share_id: shareId
        })

        this.importing = false
        this.$router.push({ name: 'snapshot', params: { type: 'imported' } })
      } catch (error) {
        alert(
          'Something went wrong while importing snapshot, please try again later!'
        )
        this.importing = false
      }
    }
  }
})
</script>
<style scoped>
.import-btn {
  display: flex;
  align-items: center;
  margin: auto;
}

.import-btn > div {
  margin-left: 12px;
}
</style>
