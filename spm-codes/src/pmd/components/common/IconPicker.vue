<template>
  <div class="hl_style-control">
    <div class="relative">
      <div class="form-group">
        <input
          v-model="data.value.name"
          type="text"
          class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
          placeholder="Click to select icon"
          readonly
          v-validate="'required'"
          data-vv-validate-on="change"
          :name="validation"
          @click.self="isActive = !isActive"
        />
        <span v-show="errors.has('linkicon')" class="--red">{{
          errorMessage
        }}</span>
      </div>
      <div class="icon-h">
        <i
          v-if="data.value.name"
          class="fas fa-times"
          @click.self="clearIcon"
        />
        <i v-else class="fas fa-crosshairs" />
      </div>
      <div v-if="isActive" class="ul-container">
        <input
          v-model="textSearch"
          type="text"
          class="form-control"
          placeholder="Search icons.."
        />
        <hr />
        <div v-if="showContainer && iconList.length > 0" class="icon-container">
          <div class="row">
            <div
              class="column"
              v-for="(item, index) in iconList"
              :key="index"
            >
              <i
                :key="index"
                class="sm-button"
                :style="
                  `--fa:'\\${item.unicode}';--ff:'${
                    item.membership.free[0] === 'brands'
                      ? 'Font Awesome 5 Brands'
                      : 'Font Awesome 5 Free'
                  }'`
                "
                @click="onClickIcon(item)"
              />
            </div>
          </div>
        </div>
        <div v-else class="empty-content">
          <p>No item found</p>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
const axios = require('axios')

export default Vue.extend({
  props: ['data', 'validation', 'errorMessage'],
  data() {
    return {
      isActive: false,
      textSearch: '',
      iconList: [],
      showContainer: false
    }
  },
  computed: {
    // icons() {
    //   return icons
    // }
  },
  watch: {
    textSearch() {
      this.searchIcon()
      this.showContainer = false
    },
    isActive() {
      this.searchIcon()
    }
  },
  methods: {
    searchIcon() {
      const self = this
      const url =
        'https://m19dxw5x0q-dsn.algolia.net/1/indexes/fontawesome_com-5.8.1/query?x-algolia-agent=Algolia%20for%20JavaScript%20(3.33.0)%3B%20Browser&x-algolia-application-id=M19DXW5X0Q&x-algolia-api-key=********************************'
      const params = `query=${this.textSearch}&hitsPerPage=100&page=0&facets=%5B%22type%22%2C%22categories%22%2C%22styles%22%2C%22membership.free%22%2C%22membership.pro%22%2C%22changes%22%5D&facetFilters=%5B%5B%22type%3Aicon%22%5D%2C%5B%22membership.free%3Asolid%22%2C%22membership.free%3Aregular%22%2C%22membership.free%3Abrands%22%5D%5D`
      axios
        .post(url, { params })
        .then(function(response: any) {
          // eslint-disable-next-line
          // console.log(response.data.hits)
          self.iconList = response.data.hits
          self.showContainer = true
        })
        .catch((err: any) => err)
    },
    onClickIcon(item: any) {
      const payload = {
        name: item.name,
        unicode: item.unicode,
        fontFamily:
          item.membership.free[0] === 'brands'
            ? 'Font Awesome 5 Brands'
            : 'Font Awesome 5 Free',
        color: 'var(--black)'
      }
      this.$emit(
        'update:data',
        Object.assign({}, this.data, { value: payload })
      )
      this.isActive = false
    },
    clearIcon() {
      const payload = {
        name: '',
        unicode: '',
        fontFamily: '',
        color: 'var(--black)'
      }
      this.$emit('input', Object.assign({}, this.data, { value: payload }))
    }
  }
})
</script>

<style scoped>
.icon-h {
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
  color: #4a5568;
  right: 0;
  top: 0;
  bottom: 0;
  position: absolute;
  align-items: center;
  display: -webkit-flex;
  display: flex;
}
.relative {
  position: relative;
}
.ul-container {
  height: 200px;
  overflow-y: auto;
  width: 100%;
  margin-top: -20px;
  padding: 0;
  position: absolute;
  z-index: 1000;
  color: #485b6e;
  background: none repeat scroll 0% 0% #f0f2f5;
  box-shadow: 0px 1px 5px rgba(0, 0, 0, 0.3);
  border: 1px solid #b0baca;
  border-radius: 3px;
}
.ul-container {
  padding: 5px;
  background-color: #fff;
}
.ul-container i {
  font-size: 30px;
  width: 30px;
  height: 30px;
}
.ul-container .icon-preview {
  justify-content: center;
}
.ul-container .item {
  border-radius: 5px;
  margin: auto;
  padding: 2px;
  -webkit-box-shadow: 0px 0px 6px -2px rgba(0, 0, 0, 0.66);
  -moz-box-shadow: 0px 0px 6px -2px rgba(0, 0, 0, 0.66);
  box-shadow: 0px 0px 6px -2px rgba(0, 0, 0, 0.33);
  transition: 0.3ms;
}
.ul-container .item:hover {
  transform: translateY(-5px);
  box-shadow: 0px 0px 6px -2px rgba(0, 0, 0, 0.88);
}

.icon-container .row {
  margin: 0px;
  justify-content: center;
}

.icon-container .column {
  min-width: 40px;
  width: 40px;
  text-align: center;
}
.empty-content {
  text-align: center;
  margin-top: 20px;
}
.sm-button {
  cursor: pointer;
}
.sm-button::before {
  font-family: var(--ff);
  font-weight: 900;
  /* content: attr(data-icon); */
  content: var(--fa);
  font-style: normal;
}
</style>
