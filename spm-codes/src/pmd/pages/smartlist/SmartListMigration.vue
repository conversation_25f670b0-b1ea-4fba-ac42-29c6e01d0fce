<template>
    <div style="width:100%;height:80%;padding:5%;"  class="align-middle d-block" >
        <div v-if="!isProcessing && !isProcessed">
            <div class="my-4"><h3>Hello !! We have updates for this feature.</h3></div>
            <div class="my-4"> Please click this button to start using this feature.</div>
            <button class="btn btn-success"
                        @click.prevent="migrate">
                Click here to enable this feature
            </button>
        </div>
        <moon-loader-title  v-if="isProcessing" title="Please wait. This may take few minutes..." :loading="isProcessing"/>
        <div v-if="isProcessed">
            <h2 class="my-4" v-if="!errors || errors.length <= 0">Thank you. Update complete.</h2>
            <div class="my-4" v-else>
                <div class="mx-2">Total Records: {{totalContacts}}</div>
                <div class="mx-2">Successfully Migrated: {{totalSuccess}}</div>
                <pre class="my-4 mx-4" v-if="errors && errors.length > 0"><div v-for="e in errors">{{e}}</div></pre>
            </div>
            <button v-if="!isProcessing" class="btn btn-primary"
                                @click.prevent="exit">
                    Click me to continue...
            </button>
        </div>
    </div>
</template>

<script lang="ts">

import Vue from 'vue';
import {Location} from '@/models'
import axios from 'axios';

let unsubscribeMigration: () => void;

export default Vue.extend({
  name:'SmartListMigration',
  props: {
    locationId:String
  },
  data(){
    return {
        isProcessing:false as boolean,
        isProcessed: false as boolean,
        errors: [],
        totalContacts:0 as number,
        totalSuccess:0 as number,
    }
  },
  methods :{
    async migrate(){
        this.isProcessing = true;
        try {
            const response = await axios.get(`/updateLocationsContactsES/${this.locationId}/all`);
            if (unsubscribeMigration) unsubscribeMigration();
            console.log(response);
            if (response.status === 200 && response.data) {
                this.totalContacts = response.data.total_contacts;
                this.totalSuccess = response.data.total_es_updated;
                this.errors = response.data.errors;
            } else if (response.data) {
               this.errors.push(response.data)
            }
        }
        catch(err){
           this.errors.push(err);
        }
        finally{
            this.isProcessed = true;
            this.isProcessing = false;
        }
    },
    exit() {
       this.$emit('exit');
    },
  },
  async mounted(){
      if (unsubscribeMigration) unsubscribeMigration();
      if (!this.locationId) this.exit();
      this.isProcessing = true;
      const loc = new Location(await this.$store.dispatch(`locations/getById`, this.locationId))
      this.isProcessing = false;
      if (loc.esUpdateStatus === 'updated') this.exit();
      unsubscribeMigration =  Location.getByIdRealtime(this.locationId)
      .onSnapshot(snapshot => {
          try {
            const status = snapshot.data().es_update_status;
            if (status === 'updated'){
                if (unsubscribeMigration) unsubscribeMigration();
                this.isProcessed = true;
                this.isProcessing = false;
            } else if (status === 'updating'){
                this.isProcessed = false;
                this.isProcessing = true;
            }
          } catch(err){
            if (unsubscribeMigration) unsubscribeMigration();
          }
      });
   },
   beforeDestroy(){
    if (unsubscribeMigration) {
        unsubscribeMigration();
    }
   }

});

</script>
