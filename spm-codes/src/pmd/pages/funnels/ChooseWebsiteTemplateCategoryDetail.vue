<template>
  <div>
    <section class="hl_wrapper">
      <section class="hl_wrapper--inner website-template-category-detail">
        <div class="container-fluid">
          <div class="hl_controls">
            <div class="hl_controls--left flex">
              <p>
                <a
                  @click="$router.push({ name: inWebsitesTemplateCategoryPage ? 'website_templates_category' : 'funnel_templates_category' })"
                  href="javascript:void(0);"
                  class="back"
                >
                  <i class="icon icon-arrow-left-2"></i>&nbsp;&nbsp;
                </a>
                Back to Categories
              </p>
            </div>
          </div>
          <div class="card website-template-category-details">
            <div class="card-body">
              <div class="name-image">
                <div
                  class="image"
                  :style="{backgroundImage: `url(${websiteTemplateCategory.image})`}"
                ></div>
                <!-- <img class="image" :src="websiteTemplateCategory.image" alt="Website Template Category Image"> -->
                <h4 class="name">{{ websiteTemplateCategory.name }}</h4>
              </div>
              <div class="row">
                <div class="col-sm-12 col-md-6 col-lg-6 video" v-if="websiteTemplateCategory.video">
                  <iframe
                    frameborder="0"
                    allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
                    allowfullscreen
                    :src="videoUrl"
                    v-if="websiteTemplateCategory.video"
                  ></iframe>
                </div>
                <div class="col-sm-12 col-md-6 col-lg-6">
                  <h6>Description</h6>
                  <p>{{ websiteTemplateCategory.description }}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="website-templates-list row">
            <div class="col-sm-12 col-lg-12">
              <h3 class="title">Check out all the templates</h3>
            </div>
            <router-link
              :to="{name: inWebsitesTemplateCategoryPage ? 'website_templates_detail' : 'funnel_templates_detail', params: { id: websiteTemplateCategory.id, template_id: template.id }}"
              tag="div"
              class="website-template-container col-xs-12 col-sm-12 col-md-6 col-lg-3"
              v-for="template in websiteTemplates"
              :key="template.id"
            >
              <website-template-card :template="template" />
            </router-link>
            <div
              v-if="!fetchingTemplates && !errorInFetchingTemplates &&  websiteTemplates.length === 0"
            >Sorry, there are no templates available for this category.</div>
          </div>
        </div>
      </section>
    </section>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { WebsiteTemplateCategory, WebsiteTemplate } from '../../../models'
import WebsiteTemplateCard from '@/pmd/components/funnels/WebsiteTemplateCard.vue'
import { getVideoId, getVimeoId } from '@/util/video_utils'
import { WebsiteTemplateCategoryType } from '../../../models/website_template_category'

export default Vue.extend({
  components: {
    WebsiteTemplateCard
  },
  data() {
    return {
      websiteTemplateCategory: {} as WebsiteTemplateCategory,
      websiteTemplates: [] as WebsiteTemplate[],
      fetchingCategory: false,
      errorInFetchingCategory: false,
      fetchingTemplates: false,
      errorInFetchingTemplates: false,
      getVideoId
    }
  },
  computed: {
    templateCategoryId() {
      return this.$route.params.id
    },
    videoUrl(): string {
      try {
        const videoUrl = this.websiteTemplateCategory.video
        if (!videoUrl) return ''

        if (videoUrl.toLowerCase().includes('youtube')) {
          return `https://www.youtube.com/embed/${getVideoId(videoUrl)}`
        }

        if (videoUrl.toLowerCase().includes('vimeo')) {
          const videoId = getVimeoId(videoUrl)
          return `https://player.vimeo.com/video/${getVimeoId(videoUrl)}`
        }

        return videoUrl
      } catch (error) {
        console.error('error while getting video url --> ', error)
        return ''
      }
    },
    inWebsitesTemplateCategoryPage(): boolean {
      return this.templateCategoryType === WebsiteTemplateCategoryType.Websites
    },
    templateCategoryType(): string {
      const routePath = this.$route.path
      if (routePath.indexOf('funnel-templates') > -1) {
        return WebsiteTemplateCategoryType.Funnels
      }

      if (routePath.indexOf('website-templates') > -1) {
        return WebsiteTemplateCategoryType.Websites
      }

      return WebsiteTemplateCategoryType.Websites
    }
  },
  mounted() {
    this.fetchTemplateCategory(this.templateCategoryId)
    this.fetchWebsiteTemplateForCategory(this.templateCategoryId)
  },
  methods: {
    async fetchTemplateCategory(categoryId: string) {
      try {
        this.fetchingCategory = true
        this.errorInFetchingCategory = false

        this.websiteTemplateCategory = await WebsiteTemplateCategory.getById(
          categoryId
        )
      } catch (error) {
        console.error('error in fetching category --> ', error)
        this.errorInFetchingCategory = true
      } finally {
        this.fetchingCategory = false
      }
    },
    async fetchWebsiteTemplateForCategory(categoryId: string) {
      try {
        this.fetchingTemplates = true
        this.errorInFetchingTemplates = false

        const availableTemplates = await WebsiteTemplate.getByCategoryId(
          categoryId
        )
        if (availableTemplates) {
          this.websiteTemplates = availableTemplates.sort(
            (tempA, tempB) =>
              new Date(tempB.dateAdded.format()).getTime() -
              new Date(tempA.dateAdded.format()).getTime()
          )
        }
      } catch (error) {
        console.error(
          'Error while fetching website templates for category --> ',
          error
        )
        this.errorInFetchingTemplates = true
      } finally {
        this.fetchingTemplates = false
      }
    }
  }
})
</script>
<style scoped>
.website-template-category-details .name-image {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.website-template-category-details .name-image .name {
  display: inline-block;
}

.website-template-category-details .name-image .image {
  height: 50px;
  width: 50px;
  border-radius: 5px;
  user-select: none;
  -webkit-user-drag: none;
  margin-right: 12px;
  background-color: darkgray;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.website-template-category-details iframe {
  width: 560px;
  height: 315px;
  max-width: 100%;
  background: lightgray;
}

.website-templates-list h3.title {
  margin-bottom: 1rem;
}

@media (max-width: 767px) {
  .website-template-category-details iframe {
    max-width: 100%;
    width: 100%;
    height: 40vw;
    margin-right: 0px;
  }
}
</style>
