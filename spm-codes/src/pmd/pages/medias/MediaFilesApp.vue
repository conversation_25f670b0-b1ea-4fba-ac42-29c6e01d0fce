<template>
  <div
    id="mediaFiles"
    ref="mediaFiles"
    style="height: 100vh; width: 100%; border: none"
  ></div>
</template>

<script lang="ts">
import Postmate from 'postmate'
import config from '@/config'
import firebase from 'firebase/app'
import { Location } from '@/models'
import { defineComponent, onMounted, ref } from '@vue/composition-api'

export default defineComponent({
  setup(_, ctx) {
    const handshake = ref()
    const mediaFiles = ref()
    const route = ctx.root.$route
    const router = ctx.root.$router
    const store = ctx.root.$store
    const routerVersion = ref('v1')
    const { id: userId, email } = store.state.user.user
    const { locations } = store.state.locations
    const locationData = locations.find(
      (x: Location) => x.id === route.params.location_id
    )

    function getToken() {
      return firebase.auth().currentUser.getIdToken()
    }

    onMounted(() => {
      loadMicroApp()
    })

    async function loadMicroApp() {
      if (process.env.NODE_ENV === 'development') Postmate.debug = true

      handshake.value = new Postmate({
        container: mediaFiles.value as HTMLElement,
        url: `${config.mediaCenterAppUrl}`,
        name: 'media-center-app',
        model: {
          userId,
          email,
          apiKey: locationData.api_key,
          tokenId: await getToken(),
          companyId: locationData.company_id,
          altId: locationData.id,
          altType: 'location',
          hideHeader: true,
          allowedExtensions:['documents', 'image', 'video'],
          maxUploadSize: 30000000,
        },
      })
      handshake.value.then(child => {
        child.on('close', (data: any) => {
          const routeName = routerVersion.value === 'v2' ? 'media-settings-v2' : 'media-settings'
          router.push({ name: routeName })
        })
      })
    }

    return {
      mediaFiles,
      routerVersion
    }
  },
  beforeRouteEnter(to, from, next) {
      next((vm:any) => {
        vm.routerVersion = from?.meta?.router_version || 'v1'
        next()
      })
    },
})
</script>

<style>
#mediaFiles iframe {
  width: 100%;
  height: 100%;
  border: none;
}
</style>
