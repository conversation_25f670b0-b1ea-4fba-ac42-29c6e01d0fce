<template>
	<div
		class="tab-pane fade show active"
		id="tab1"
		role="tabpanel"
		aria-labelledby="tab1-tab"
		:class="{'no-top-padding': mode === 'only-create'}"
	>
		<form>
			<div class="row">
				<div class="col-12" v-if="mode !== 'only-create'">
					<h3>Contact Info
						<span
							style="margin-left: 5px; cursor: pointer;"
							class="input-group-addon"
							v-b-tooltip.hover
							title="Go to Contact Detail Page"
							@click="navigateContactDetailPage"
							v-if="contact"
						>
						<i class="fas fa-address-card"></i>
						</span>
					</h3>
				</div>
				<div class="col-sm-12" v-if="mode !== 'only-create'">
					<div class="form-group dropdown">
						<UITextLabel>Contact Name</UITextLabel>
						<input
							type="text"
							class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800 msgsndr2"
							placeholder="Contact Name"
							v-model.trim="contactName"
							v-validate="'required'"
							name="msgsndr2"
							data-lpignore="true"
							data-vv-as="contact name"
							@input="onChange"
							autocomplete="msgsndr2"
						>
						<div style="position: absolute;top: 60%;right: 18px;">
							<moon-loader :loading="loading" color="#37ca37" size="20px"/>
						</div>
						<div
							class="dropdown-menu show"
							v-show="isOpen"
							ref="dropdownMenu"
							style="width:100%;max-height: 350px;overflow-y: scroll;"
						>
							<span v-for="customer in customers" :key="customer.id" @click="setResult(customer, 'phone')">
								<a class="dropdown-item">
									<span>{{customer.fullName}}</span>
								</a>
							</span>
						</div>
						<span v-show="errors.has('msgsndr2')" class="error">{{ errors.first('msgsndr2') }}</span>
					</div>
				</div>

				<div class="col-sm-6" v-if="mode !== 'only-create'">
					<div class="form-group">
						<UITextInputGroup
							type="email"
							class="msgsndr3"
							placeholder="Email"
							label="Email"
							v-model.trim="email"
							v-validate="'email'"
							name="msgsndr3"
							data-lpignore="true"
             				autocomplete="msgsndr3"
							:error="errors.has('msgsndr3')"
							:errorMsg="errors.first('msgsndr3')"
						/>
					</div>
				</div>
				<div class="col-sm-6" v-if="mode !== 'only-create'">
					<div class="form-group">
						<UITextLabel>Phone</UITextLabel>
						<PhoneNumber
							class="msgsndr1"
							placeholder="Phone"
							v-model="phone"
							v-validate="'phone'"
							name="msgsndr1"
              autocomplete="msgsndr1"
							:currentLocationId="currentLocationId"
							:error="errors.has('msgsndr1')"
							:errorMsg="errors.first('msgsndr1')"
						/>
					</div>
				</div>
				<div class="col-sm-6" v-if="mode !== 'only-create'">
					<TagComponent v-model="tags"/>
				</div>
        <div class="col-sm-6" v-if="mode !== 'only-create'">
					<div class="form-group">
						<UITextInputGroup
							type="text"
							placeholder="Company Name"
							label="Company Name"
							v-model.trim="companyName"
							name="companyName"
              autocomplete="companyName"
						/>
					</div>
				</div>
				<div class="col-12" v-if="mode !== 'only-create'">
					<h3>Opportunity Info</h3>
				</div>
				<div class="col-sm-12">
					<div class="form-group">
						<UITextInputGroup
							type="text"
							placeholder="Add Opportunity Name"
							label="Opportunity Name"
							v-model.trim="opportunityName"
							v-validate="'required'"
							name="opportunityName"
							data-vv-as="opportunity name"
							:error="errors.has('opportunityName')"
							:errorMsg="errors.first('opportunityName')"
						/>
					</div>
				</div>
				<div class="col-sm-6">
					<div class="form-group">
						<UITextLabel>Pipeline</UITextLabel>
						<select
							class="selectpicker mt-1"
							title="Select Pipeline"
							v-model="pipeline"
							name="pipeline"
							v-validate="'required'"
							:disabled="initializing"
						>
							<option v-for="pipeline in pipelines" :value="pipeline.id">{{pipeline.name | toTitleCase}}</option>
						</select>
						<span v-show="errors.has('pipeline')" class="error">{{ errors.first('pipeline') }}</span>
					</div>
				</div>
				<div class="col-sm-6">
					<div class="form-group">
						<UITextLabel>Stage</UITextLabel>
						<select
							class="selectpicker mt-1"
							title="Select Stage"
							v-model="stage"
							name="stage"
							v-validate="'required'"
							:disabled="stages.length === 0"
						>
							<option v-for="stage in stages" :value="stage.id">{{stage.name | toTitleCase}}</option>
						</select>
						<span v-show="errors.has('stage')" class="error">{{ errors.first('stage') }}</span>
					</div>
				</div>
				<div class="col-sm-6">
					<div class="form-group">
						<UITextLabel>Status</UITextLabel>
						<select
							class="selectpicker mt-1"
							title="Contact Type"
							v-model="status"
							name="status"
							data-width="100%"
							v-validate="'required'"
							:disabled="initializing"
						>
							<option value="open">Open</option>
							<option value="won">Won</option>
							<option value="lost">Lost</option>
							<option value="abandoned">Abandoned</option>
						</select>
						<span v-show="errors.has('status')" class="error">{{ errors.first('status') }}</span>
					</div>
				</div>
				<div v-if="user && user.permissions.lead_value_enabled !== false" class="col-sm-6">
					<div class="form-group">
						<UITextLabel>Lead Value</UITextLabel>
						<!-- <div class="input-group">
							<span class="input-group-addon dollar-right">{{location.country | symbole}}</span>
							<UITextInputGroup type="number" placeholder="Enter Value" v-model.number="value"/>
						</div> -->
            <MonetaryField
                :isTW="true"
                placeholder="Enter Value"
                :currentLocationId="location.id"
                v-model.number="value"
              />
					</div>
				</div>
				<div class="col-sm-6">
					<div class="form-group">
						<UITextLabel>Owner</UITextLabel>
						<select class="selectpicker mt-1" title="Unassigned" data-width="100%" v-model="assignedTo">
							<option v-if="assignedTo" value="" style="color: #e03131;">Remove owner</option>
							<option v-for="(user, userIndex) in assignees" :value="user.id" :key="userIndex">{{user.fullName}}</option>
						</select>
					</div>
				</div>
				<div class="col-sm-6">
					<div class="form-group">
						<UITextInputGroup label="Source" type="text" data-lpignore="true" placeholder="Source" v-model.trim="source"/>
					</div>
				</div>
				<div v-if="opportunity && !opportunity.newRecord" class="col-sm-12">
						<span v-if="opportunity.internalSource" class="created-text">Created by:
              <InternalSourceLink v-if="opportunity.internalSource.type !== 'bulk_request'" :object="opportunity" />
              <span v-else>Bulk Operation</span>
            </span>
						<span v-if="opportunity.createdAt" class="created-text">Created on: {{ opportunity.createdAt }}</span>
				</div>
			</div>
		</form>
	</div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Contact, Location, Pipeline, Opportunity, Stage, User } from '@/models';
import { mapState } from "vuex";
import { UserState } from "@/store/state_models";
const TagComponent = () => import('../customer/TagComponent.vue');
const PhoneNumber = () => import('../util/PhoneNumber.vue');
const InternalSourceLink = () => import('../util/InternalSourceLink.vue');
const MonetaryField = () => import('../util/MonetaryField.vue');

declare var $: any;

export default Vue.extend({
	props: ['bus', 'opportunity', 'users', 'mode'],
	components: { TagComponent, PhoneNumber, InternalSourceLink, MonetaryField },
	data() {
		return {
			stage: '',
			status: '',
			opportunityName: '',
			contactName: '',
			companyName: '',
			email: '',
			phone: '',
			source: '',
			value: undefined as number | undefined,
			contact: undefined as Contact | undefined,
			search: Contact.last(Contact.searchByName),
			searchDebouncer: undefined as NodeJS.Timer | undefined,
			customers: [] as Contact[],
			loading: false,
			isOpen: false,
			assignedTo: '',
			tags: [] as string[],
			stages: [] as Stage[],
			pipeline: '',
			initializing: true,
			currentLocationId: '',
			location: {} as Location,
		}
	},
	async created() {
		this.currentLocationId = this.$router.currentRoute.params.location_id;
		await Promise.all([
			this.$store.dispatch('pipelines/syncAll', this.$route.params.location_id)
		])
	},
	watch: {
		'$route.params.location_id': async function (id) {
			this.currentLocationId = id;
			await Promise.all([
				this.$store.dispatch('pipelines/syncAll', id)
			])
		},
		'pipeline': function (pipeline, oldVal) {
			if (!this.pipeline) {
				this.stages = [];
			} else {
				const selectedPipe = <Pipeline>lodash.find(this.pipelines, { id: this.pipeline });
				this.stages = lodash.sortBy(selectedPipe.stages, ['position']);
			}

			if (oldVal) {
				this.stage = '';
			}
		},
		contactName(name) {
			this.contactName.replace(/\w\S*/g, function (txt: string) {
				return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
			});
		},
		opportunity() {
			this.setData()
		}
	},
	computed: {
		pipelines() {
			return this.$store.state.pipelines.pipelines
		},
		assignees() {
			if (this.onlyOwnData && this.users) return this.users.filter(user => user.role === 'admin' || user.id === this.user.id)
			else return this.users || this.localUsers;
		},
		localUsers() {
		return this.$store.state.users.users.map(u => new User(u));
		},
		...mapState('user', {
			user: (s: UserState) => {
				return s.user ? new User(s.user) : undefined;
			},
		}),
		onlyOwnData() {
			return this.user && this.user.role === 'user' && this.user.permissions.assigned_data_only === true
		},
		getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion']
		},
	},
	methods: {
    async setData() {
      const opportunity = this.opportunity
      if (!lodash.isEmpty(opportunity)) {
				this.errors.clear();
				const data: (() => object) = <(() => object)>this.$options.data;
        if (data) Object.assign(this.$data, data.apply(this));

				if (opportunity.contactId) {
          this.contact = await Contact.getById(opportunity.contactId);
        }
        this.currentLocationId = this.$router.currentRoute.params.location_id;
        // if (this.location?.id !== this.currentLocationId) {
        //   this.location = new Location(await this.$store.dispatch('locations/getById', this.currentLocationId))
        // }
				this.stage = opportunity.pipelineStageId;
				this.pipeline = opportunity.pipelineId;
				this.status = opportunity.status;
				this.source = opportunity.source;
				this.value = opportunity.monetaryValue;
        this.assignedTo = opportunity.assignedTo;

				if (this.contact) {
					this.contactName = this.contact.fullName;
					this.email = this.contact.email;
					this.phone = this.contact.phone;
          this.tags = this.contact.tags.map(t => t);
          this.companyName = this.contact.companyName;
				}
				if (opportunity.name) {
					this.opportunityName = opportunity.name;
        }

        this.location = new Location(await this.$store.dispatch('locations/getCurrentLocation'))

        this.errors.clear();
        this.$validator.reset();

        this.initializing = false;
      } else {
        this.errors.clear();
        this.$validator.reset();
        const data: (() => object) = <(() => object)>this.$options.data;
        if (data) Object.assign(this.$data, data.apply(this));
      }
    },
		onChange() {
			this.contact = undefined;
			this.opportunity.contactId = '';
			this.customers = [];
			this.isOpen = false;
			if (this.searchDebouncer) clearTimeout(this.searchDebouncer);
			if (this.contactName) {
				this.searchDebouncer = setTimeout(this.runSearch, 300);
			}
		},
		onClick(e: any) {
			let el: any = this.$refs.dropdownMenu;
			let target = e.target;
			if (el && el !== target && !el.contains(target)) {
				this.isOpen = false;
			}
		},
		runSearch() {
			this.loading = true;
			const term = this.contactName;
			this.search(this.opportunity.locationId, 'all', this.contactName).then((results: Contact[]) => {
				this.loading = false;
				if (this.contactName !== term) return;
				if (results.length > 0) {
					this.isOpen = true;
					this.customers = results;
				}
			}).catch((err: any) => {
        if (err && err.toString().includes(401)) alert('You\'re not allowed to change customer name!! Check your permission with agency admin.');
			});
		},
		async setResult(result: Contact) {
      this.isOpen = false;
			this.contact = await this.$store.dispatch('contacts/syncGet', result.id);
			this.contactName = result.fullName;
			this.phone = result.phone;
			this.email = result.email;
			this.companyName = result.companyName
			this.assignedTo = result.assignedTo
			this.source = result.source
			this.tags = result.tags.map(t => t);
			this.bus.$emit('update_contact', { currentLocationId: result.locationId, contactId: result.id });
			if (this.opportunityName == "") {
				this.opportunityName = this.contactName;
			}
		},
		async submit() {
			this.errors.clear();
			await this.$validator.validateAll();
			if (this.errors.any()) {
				this.bus.$emit('save_opportunity_complete', { error: { tab: 'OpportunityComponent' } });
				return;
      }

			if (!this.contact) {
        if(this.email){
          this.contact = await Contact.getByEmail(this.email, this.opportunity.locationId);
        }
        if(!this.contact && this.phone) {
          this.contact = await Contact.getByPhoneNumberAndLocation(this.opportunity.locationId, this.phone);
        }
        if(!this.contact){
          this.contact = new Contact();
				  this.contact.locationId = this.opportunity.locationId;
					this.contact.type = 'lead';
					this.contact.internalSource = {
						type: 'add_opportunity',
						id: this.opportunity.id,
						userName: this.user.name
					}
        }
      }
      if (this.location && !this.location.allowDuplicateOpportunity) {
        let existingOpportunities = await Opportunity.getByPipelineAndContactId(this.opportunity.locationId, this.pipeline ,this.contact.id);
        if(existingOpportunities.length>0 && !lodash.find(existingOpportunities,{id:this.opportunity.id})){
          this.bus.$emit('save_opportunity_complete', {error: { tab: 'OpportunityComponent', msg: 'Opportunity already exists in this pipeline' }});
          return;
        }
      }
      this.contact.source = this.source ? this.source.toLowerCase() : '';
			this.contact.fullName = this.contactName;
			this.contact.email = this.email;
			this.contact.phone = this.phone;
      this.contact.tags = this.tags;
			this.contact.companyName = this.companyName;
      if (!this.onlyOwnData) this.contact.assignedTo = this.assignedTo;
			else if (this.onlyOwnData && this.opportunity.assignedTo) this.contact.assignedTo = this.opportunity.assignedTo;

			const response = await this.contact.save();
			if (!(response instanceof Contact)) {
				this.bus.$emit('save_opportunity_complete', {error: { tab: 'OpportunityComponent', msg: response }});
			}

			if (!this.opportunity.internalSource && !this.opportunity.name && !this.opportunity.contactId) this.opportunity.internalSource = {
				type: `${this.$route.name}_page`,
				id: this.user.id,
				userName: this.user.name // Saved to data so it doesnt spend time loading it
			}
			this.opportunity.name = this.opportunityName;
			this.opportunity.source = this.source ? this.source.toLowerCase() : '';
			this.opportunity.pipelineId = this.pipeline;
			this.opportunity.pipelineStageId = this.stage;
			this.opportunity.status = this.status;
			this.opportunity.monetaryValue = this.value;
			if (!this.onlyOwnData) this.opportunity.assignedTo = this.assignedTo;
			this.opportunity.isAttribute = this.contact.sessionId ? true : false;
			this.opportunity.contactId = this.contact.id;

			// await this.opportunity.save();
			this.bus.$emit('save_opportunity_complete', { contactId: this.contact.id });
		},
		navigateContactDetailPage() {
			let routeData = this.$router.resolve({ name: this.getSideBarVersion == 'v2' ? 'contact_detail-v2': 'contact_detail', params: { contact_id: this.contact.id }})
			window.open(routeData.href, '_blank');
		}
	},
	mounted() {
    this.bus.$on('save_opportunity', this.submit)
    this.setData()
		document.addEventListener('click', this.onClick);
		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker();
		}
	},
	beforeDestroy() {
		this.bus.$off('save_opportunity');
		document.removeEventListener('click', this.onClick);
	},
	updated() {
    const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker('refresh');
		}
	}
});
</script>

<style scoped>
.created-text {
	color: #607179;
    font-size: 0.75rem;
	display: block;
}
</style>
