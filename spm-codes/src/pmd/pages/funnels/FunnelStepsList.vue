<template>
  <div class="hl_funnels--card-side">
    <div class="launch-checklist">
      <h3><i class="icon icon-ok-circle"></i> Launch Checklist</h3>
    </div>
    <h4>Funnel Steps</h4>
    <ul class="hl_funnels--steps-items">
      <draggable
        class="funnels-steps-list"
        v-model="stepsInFunnel"
        v-bind="dragOptions"
      >
        <router-link
          :to="getRoute(step.id)"
          tag="li"
          class="step-item"
          active-class="active"
          v-for="step in stepsInFunnel"
        >
          <FunnelStepIcon :funnelStepType="step.type" />
          <div class="title">
            <h5>{{ step.name }}</h5>
            <!-- <p>{{ step.type | getFunnelStepTypeName }}</p> -->
          </div>
          <span @click="askDeleteConfirmation(step)" class="close">
            <i class="icon icon-close"></i>
          </span>
        </router-link>
      </draggable>
    </ul>
    <div class="add-new-step">
      <UIButton
        @click="showAddNewStep()"
        type="button"
        use="primary"
        class="w-full justify-center"
      >
        <i class="icon icon-plus mr-2"></i> Add New Step
      </UIButton>
    </div>
    <ConfirmDeleteModal
      :showModal="showDeleteModal"
      @showDeleteModal="val => (showDeleteModal = val)"
      @hidden="showDeleteModal = false"
      @delete="deleteFunnelStep"
    />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { User } from '@/models'
import { FunnelStep } from '../../../models/funnel'
import { FUNNEL_PAGE_TYPES } from '../../../util/funnel_consts'
import ConfirmDeleteModal from '../../components/funnels/ConfirmDeleteModal.vue'
const FunnelStepIcon = () =>
  import('@/pmd/components/funnels/FunnelStepIcon.vue')

export default Vue.extend({
  components: {
    FunnelStepIcon,
    ConfirmDeleteModal
  },
  props: {
    steps: {
      type: Array
    },
    funnelId: {
      type: String
    },
    locationId: {
      type: String
    }
  },
  computed: {
    stepsInFunnel: {
      get(): Array<Object> {
        return [...this.steps]
      },
      set(value) {
        const stepsOrder = value.map((s: FunnelStep) => s.id)
        this.$emit('stepsReordered', stepsOrder)
      }
    },
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  data() {
    return {
      dragOptions: {
        handle: '.step-item',
        animation: 250,
        group: 'funnel-steps',
        disabled: false,
        dragClass: 'sortable-drag',
        fallbackOnBody: true,
        swapThreshold: 0.8
      },
      showDeleteModal: false,
      deletingStep: null
    }
  },
  methods: {
    deleteFunnelStep(step: FunnelStep) {
      this.showDeleteModal = false
      this.$emit('deleteFunnelStep', this.deletingStep)
    },
    showAddNewStep() {
      this.$emit('showAddNewStep')
    },
    askDeleteConfirmation(funnelStep: FunnelStep) {
      this.deletingStep = funnelStep
      this.showDeleteModal = true
    },
    getRoute(stepId: string) {
      if (this.getSideBarVersion == 'v2') {
        return {
            name: 'funnel-steps-v2',
            params: {
              step_id: stepId,
              location_id: this.locationId,
              funnel_id: this.funnelId
            }
          }
      } else {
        return {
            name: 'funnel_steps',
            params: {
              step_id: stepId,
              location_id: this.locationId,
              funnel_id: this.funnelId
            }
          }
      }
    }
  },
  filters: {
    getFunnelStepTypeName: function(funnelStepTypeId: string) {
      const funnelStepType = FUNNEL_PAGE_TYPES.find(
        f => f.id === funnelStepTypeId
      )
      if (funnelStepType) {
        return funnelStepType.title
      } else {
        return 'Unknown Type'
      }
    }
  }
})
</script>
