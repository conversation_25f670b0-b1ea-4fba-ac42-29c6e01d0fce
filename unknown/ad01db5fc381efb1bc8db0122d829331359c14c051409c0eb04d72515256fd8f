<template>
  <div class="relative">
    <label for="company-website" class="block text-xs font-medium text-gray-700"
      >Tags</label
    >
    <div
      class="mt-1 py-1 rounded-md shadow-sm flex flex-wrap items-center block w-full sm:text-sm rounded-md border focus-within:ring-1 border-gray-300 focus-within:ring-blue-600 focus-within:border-blue-600"
      @click="$refs.input.focus()"
    >
      <span
        class="flex justify-center items-center font-medium py-1 ml-2 px-2 text-white rounded-full bg-blue-600 border border-blue-700"
        v-for="(tag, index) in value"
        :key="index"
      >
        <div class="text-xs font-bold leading-none max-w-full flex-initial">
          {{ tag }}
        </div>
        <div class="flex flex-auto flex-row-reverse ml-1">
          <span class="cursor-pointer" @click="removeTag(tag)" v-if="!disabled">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="3"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </span>
        </div>
      </span>
      <span>
        <input
          type="text"
          ref="input"
          class="py-1 outline-none border-0 focus:border-0 focus:ring-0 focus:outline-none sm:text-sm"
          placeholder="Add Tags"
          v-model="tagSearch"
          @enter="addNewTag(tagSearch)"
          @focus="focused = true"
          @blur="focused = false"
        />
      </span>
    </div>
    <ul
      class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm"
      tabindex="-1"
      role="listbox"
      aria-labelledby="listbox-label"
      aria-activedescendant="listbox-option-3"
      v-show="tagSearch"
    >
      <li
        class="group text-gray-900 hover:text-white hover:bg-blue-600 cursor-default select-none relative py-2 pl-3 pr-9"
        id="listbox-option-0"
        role="option"
        v-for="(existingTag, index) in filteredTags"
        :key="index + existingTag"
        @click="pickTag(existingTag)"
      >
        <div class="flex">
          <span class="font-normal truncate">
            {{ existingTag }}
          </span>
        </div>
      </li>
      <li
        class="group text-gray-900 hover:text-white hover:bg-blue-600 cursor-default select-none relative py-2 pl-3 pr-9"
        id="listbox-option-0"
        role="option"
        v-if="!exactMatch"
        @click="addNewTag(tagSearch)"
      >
      <div class="flex">
          <span class="font-normal truncate flex">
             <span class="text-blue-600 group-hover:text-white"> 
           <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
</svg> </span>{{ tagSearch }}
          </span>
          
        </div>
      </li>
    </ul>
  </div>
</template>
<script lang="ts">
import { Tag, User } from '@/models'
import { UserState } from '@/store/state_models'
import { mapState } from 'vuex'
import clone from 'lodash/clone'
import difference from 'lodash/difference'
import filter from 'lodash/filter'

// :class="focused?'ring-1 border-2 ring-blue-500 border-blue-500':'border border-gray-300'"
export default {
  props: {
    value: Array,
    form: {
      default: true,
    },
    inputClass: String,
    disabled: {
      default: false,
    },
    darkLabel: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    onClick(e: any) {
      let el: any = this.$refs.tags
      let target = e.target
      if (el && el !== target && !el.contains(target)) {
        this.tagSearch = ''
      }
    },
    removeTag(tag: string) {
      const tags = clone(this.value)
      tags.splice(tags.indexOf(tag), 1)
      this.$emit('input', tags)
      this.$emit('change')
    },
    async fetchData() {
      this.existingTags = (
        await Tag.getByLocationId(this.currentLocationId)
      ).map(tag => tag.name)
      this.fetching = false
    },
    pickTag(tag: string) {
      const lowerCaseTag = tag.toLowerCase()
      const tags = clone(this.value)
      tags.push(lowerCaseTag)
      this.tagSearch = ''
      this.$emit('input', tags)
      this.$emit('change')
    },
    async addNewTag(tag: string) {
      if (this.exactMatch) return
      const lowerCaseTags = tag
        .split(',')
        .map((t: string) => t.trim().toLowerCase())

      for (const lowerCaseTag of lowerCaseTags) {
        if (!this.existingTags.includes(lowerCaseTag)) {
          await Tag.createTag(this.currentLocationId, lowerCaseTag)
          this.existingTags.push(lowerCaseTag)
        }
      }

      const tags = clone(this.value)
      tags.push.apply(tags, lowerCaseTags)
      this.tagSearch = ''
      this.$emit('input', tags)
      this.$emit('change')
    },
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    getClass() {
      const css = {}
      //if (this.form) css['form-control'] = this.form
      if (this.inputClass) css[this.inputClass] = true
      return css
    },
    filteredTags(): string[] {
      let filtered = <string[]>difference(this.existingTags, this.value)
      filtered = filter(
        filtered,
        tag => tag.indexOf(this.tagSearch.trim().toLowerCase()) !== -1
      )
      return filtered
    },
    exactMatch(): boolean {
      return (
        this.existingTags.indexOf(this.tagSearch.trim().toLowerCase()) !== -1
      )
    },
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.fetchData()
    },
  },
  data() {
    return {
      currentLocationId: '',
      existingTags: [] as string[],
      tagSearch: '',
      fetching: true,
      focused: false,
    }
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchData()
  },
  mounted() {
    document.addEventListener('click', this.onClick)
  },
  beforeDestroy() {
    document.removeEventListener('click', this.onClick)
  },
}
</script>
