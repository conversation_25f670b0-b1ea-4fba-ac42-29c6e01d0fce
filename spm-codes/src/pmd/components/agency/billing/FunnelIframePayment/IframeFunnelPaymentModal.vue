<template>
  <div>
    <modal v-if="showModal" @close="closeModal" maxWidth="1100" :showCloseIcon="showCloseIcon"  :noBackdropClose="true">
      <Checkout :customerId="customerId" :priceId="priceId" :successMsg="successMsg" @cancel="closeModal" @loading="changeCloseIcon" :options="configOptions" :requestSource="requestSource" />
    </modal>
    </div>
</template>

<script>
import Modal from '@/pmd/components/common/Modal.vue'
import Checkout from '@/pmd/components/agency/billing/FunnelIframePayment/Checkout.vue'


export default {
 components: {
    Modal,
    Checkout
  },
  data(){
    return  {
      showModal: false,
      customerId: "",
      requestSource: "",
      priceId: "",
      successMsg: "",
      showCloseIcon: true,
      configOptions: {
        isBackBtn:false
      }
    }
  },
   created(){
    this.listenIframeEvent();
  },
  methods: {
    listenIframeEvent(){
      // Catch data from iframe(from child(funnel) to parent(App).)
      window.addEventListener('message', (e) => {
        if(e.data.handle_checkout) {
          let { priceId, successMessage}  = e.data.payload
          this.priceId = priceId
          this.customerId = this.$store.state?.company?.company?.stripe_id
          this.successMsg = successMessage
          this.showModal = true;
          this.requestSource = e.data.request_source
        }
      })
    },
    closeModal(){
      this.showModal = false;
    },
    changeCloseIcon(payload){
      this.showCloseIcon = payload
    }
  }
}
</script>

<style lang="scss">

</style>
