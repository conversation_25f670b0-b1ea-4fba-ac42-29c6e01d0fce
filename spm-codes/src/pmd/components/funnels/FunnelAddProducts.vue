<template>
  <div class="manage-products">
    <div class="subscription-type pb-2">
      <span class="head-label"
        >Subscription, One-Time product or Payment plan</span
      >
      <span class="label"
        >Is this product available as a subscription, one-time sell or payment
        plan?</span
      >
      <div
        v-for="(type, index) in productTypeOptions"
        :key="index"
        style="padding:2px 0px;"
      >
        <input
          type="radio"
          name="type"
          :value="type.value"
          v-model="paymentType"
          :disabled="actionType === 'edit'"
        />
        <span class="ml-1">{{ type.label }}</span>
      </div>
    </div>
    <div class="product-details">
      <span class="label">
        Product Name
        <sup>*</sup>
      </span>
      <UITextInputGroup
        type="text"
        v-model="productName"
        name="productname"
        v-validate="'required'"
        :error="errors.has('productname')"
      />
    </div>
    <div
      class="product-details split-two"
      v-if="paymentType === productType.ONETIME"
    >
      <div class="split-item">
        <span class="label">
          Product Price
          <sup>*</sup>
        </span>
        <UITextInputGroup
          type="text"
          placeholder="Product price"
          v-model="productPrice"
          v-validate="'required|decimal:3'"
          name="productprice"
          :error="errors.has('productprice')"
        />
      </div>
      <div class="split-item">
        <span class="label">
          Currency
          <sup>*</sup>
        </span>
        <vSelect
          :options="currencyOptions"
          v-model="currencyPicked"
          :clearable="false"
          placeholder="Select Currency"
          v-validate="'required'"
          name="currencyOptions"
          :class="{ input: true, 'is-danger': errors.has('currencyOptions') }"
        ></vSelect>
      </div>
    </div>
    <div class="product-details">
      <span class="label">Price Display Override</span>
      <span class="label-optional"
        >(Optional) Customize the display of the price on the order form.</span
      >

      <UITextInputGroup
        type="text"
        v-model="priceDisplay"
        placeholder="$25 per 3 months"
        name="description"
      />
    </div>
    <div
      class="product-details"
      v-if="
        paymentType === productType.SUBSCRIPTION ||
          paymentType === productType.PAYMENT_PLAN
      "
    >
      <span class="label">
        Stripe Plan
        <sup>*</sup>
      </span>
      <span class="label-optional"
        >exact plan_id must exist in live & test mode for order form testing to
        work.</span
      >
      <vSelect
        :options="stripePlans"
        label="title"
        v-model="selectedPlan"
        :clearable="false"
        placeholder="Select Stripe plan"
        v-validate="'required'"
        name="stripePlan"
        :reduce="plan => plan.plan_id"
        :class="{ input: true, 'is-danger': errors.has('stripePlan') }"
      ></vSelect>
    </div>

    <div class="product-details">
      <span class="label">Product Description</span>
      <span class="label-optional"
        >This will appear on your customers' credit card statements alongside
        your company name that you set in your Payment Gateway settings</span
      >

      <UITextInputGroup
        type="text"
        v-model="productDescription"
        placeholder="Product Description"
        name="description"
      />
    </div>

    <div
      class="product-details"
      v-if="paymentType === productType.SUBSCRIPTION"
    >
      <section class="split-view">
        <div>
          <span class="label">Trial Period (Days)</span>
          <div class="label-optional">
            This value will override any trial period set directly in stripe.
          </div>

          <UITextInputGroup
            class="w-50"
            type="text"
            v-model="trialPeriod"
            v-validate="'numeric'"
            name="trialPeriod"
            placeholder="Days"
            :error="errors.has('trialPeriod')"
          />
        </div>
        <div>
          <!-- up front fee toggle -->
          <span class="label">Enable Upfront cost</span>
          <div class="label-optional">
            Is generally a portion of the total fee that the buyer must pay.
          </div>
          <UIToggle
            id="upfront-tgl"
            v-model="enableUpFrontCost"
            @change="enableUpFrontCost != enableUpFrontCost"
          />
          <label class="tgl-btn" for="upfront-tgl"></label>
          <!-- up front fee toggle -->
        </div>
      </section>
    </div>

    <div
      class="product-details"
      v-if="paymentType === productType.PAYMENT_PLAN"
    >
      <div>
        <span class="label">Number of payments<sup>*</sup></span>
        <div class="label-optional">
          This will cancel subscription after x number of payments but keep
          their access to purchases.
        </div>
        <UITextInputGroup
          class="w-50"
          type="text"
          v-model="numberOfPayments"
          v-validate="`required|min_value:1|max_value:100`"
          name="numberOfPayments"
          placeholder="Number of payments"
          :error="errors.has('numberOfPayments')"
        />
      </div>
    </div>

    <!-- up front cost -->
    <section class="product-details split-two" v-if="enableUpFrontCost">
      <div class="split-item">
        <span class="label">
          Upfront Fee
          <sup>*</sup>
        </span>
        <UITextInputGroup
          type="text"
          placeholder="Product price"
          v-model="upfrontFee.amount"
          v-validate="'required|decimal:3'"
          name="upfrontFee"
          :error="errors.has('upfrontFee')"
        />
      </div>
      <div class="split-item">
        <span class="label">
          Currency
          <sup>*</sup>
        </span>
        <vSelect
          :options="currencyOptions"
          v-model="upfrontFee.currency"
          :clearable="false"
          placeholder="Select Currency"
          v-validate="'required'"
          name="currencyOptions"
          :class="{ input: true, 'is-danger': errors.has('currencyOptions') }"
        ></vSelect>
      </div>
    </section>
    <!-- end up front cost -->
    <!-- Bump product -->
    <div class="product-details bump-product">
      <div class="split-view">
        <section>
          <span class="label">Membership Offer</span>
          <div class="order-bump-check">
            <div class="label-optional"
              >Mark this product as membership offer</div
            >
            <UIToggle
              id="membership-offer-tgl"
              v-model="enableMembershipOffer"
            />
            <label class="tgl-btn" for="membership-offer-tgl"></label>
          </div>
          <div v-if="enableMembershipOffer" class="mt-3">
            <UITextLabel>Select Membership offer</UITextLabel>
            <vSelect
              :options="membershipOffers"
              label="value"
              v-model="selectedMembershipOffer"
              :clearable="false"
              placeholder="Select Membership Offer"
              v-validate="'required'"
              name="offer"
              :reduce="offer => offer.key"
              :class="{ input: true, 'is-danger': errors.has('offer') }"
            ></vSelect>
          </div>
        </section>
        <section>
          <span class="label">Bump Product</span>
          <div class="order-bump-check">
            <div class="label-optional"
              >Should this product be the bump on the order page if
              present?</div
            >
            <UIToggle
              id="bump-product-tgl"
              v-model="bumpProduct"
              @change="bumpProduct != bumpProduct"
            />
            <label class="tgl-btn" for="bump-product-tgl"></label>
          </div>
        </section>
      </div>
    </div>
    <!-- end Bump product -->
    <!-- enable card auth charge  -->
    <div
      class="product-details"
      v-if="enableAuthorizeCard"
    >
      <span class="label">Authorize Card</span>
      <div class="validate-card">
        <div class="label-optional"
          >Authorize the card for
          {{
            cardAuthConfig && cardAuthConfig.amount
              ? currencySymbol(cardAuthConfig.amount, cardAuthConfig.currency)
              : 'x amount'
          }}.</div
        >
        <UIToggle
          id="validate-card-tgl"
          v-model="enableCardAuthCharge"
        />
        <label class="tgl-btn" for="validate-card-tgl"></label>
      </div>
    </div>
    <section class="product-details split-two" v-if="enableCardAuthCharge">
      <div class="split-item">
        <span class="label">
          Authorize Amount
          <sup>*</sup>
        </span>
        <UITextInputGroup
          type="text"
          placeholder="Verification amount"
          v-model="cardAuthConfig.amount"
          v-validate="'required|decimal:3'"
          name="validation-amount"
          :error="errors.has('validation-amount')"
        />
      </div>
      <div class="split-item">
        <span class="label">
          Currency
          <sup>*</sup>
        </span>
        <vSelect
          :options="currencyOptions"
          v-model="cardAuthConfig.currency"
          :clearable="false"
          placeholder="Select Currency"
          v-validate="'required'"
          name="currencyOptions"
          :class="{ input: true, 'is-danger': errors.has('currencyOptions') }"
        ></vSelect>
      </div>
    </section>
    <!-- enable card auth charge  -->

    <div class="payment-actions">
      <UIButton type="button" use="outline" @click="clearAll">
        Clear
      </UIButton>
      <UIButton
        :disabled="disableAction"
        type="button"
        use="primary"
        @click="productId ? updateProduct() : saveProduct()"
      >
        {{ productId ? 'Update' : 'Save' }}
      </UIButton>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { pickBy } from 'lodash'
import vSelect from 'vue-select'
import { Funnel } from '../../../models'
import { v4 as uuid } from 'uuid'
import { currency } from '../../../util/currency_helper'
import VeeValidate from 'vee-validate'

import {
  PRODUCT_TYPE_OPTIONS,
  PRODUCT_TYPE,
  upFrontFee,
  cardAuthConfig
} from '../../../util/funnel_consts'

Vue.use(VeeValidate)

export default Vue.extend({
  components: {
    vSelect
  },
  props: {
    actionType: String,
    productId: String,
    id: String,
    type: String,
    offerId: String
  },
  data() {
    const productTypeOptions = PRODUCT_TYPE_OPTIONS
    const productType = PRODUCT_TYPE
    return {
      stripePlans: [],
      selectedPlan: '',
      selectedMembershipOffer: '',
      productName: 'Unnamed Product',
      priceDisplay: undefined,
      productDescription: undefined,
      trialPeriod: undefined,
      numberOfPayments: undefined,
      paymentType: '',
      currencyPicked: '',
      productPrice: '',
      disableAction: false,
      bumpProduct: false,
      upfrontFee: {} as upFrontFee,
      enableUpFrontCost: false,
      productTypeOptions,
      productType,
      enableMembershipOffer: false,
      enableCardAuthCharge: false,
      cardAuthConfig: {} as cardAuthConfig
    }
  },
  async created() {
    this.clearAll()
    await this.loadStripe()
    if (this.productId) {
      this.loadData()
    }else {
      this.paymentType = this.productType.SUBSCRIPTION
    }
  },
  computed: {
    currencyOptions() {
      let currencyOptions = []
      for (const key of Object.keys(currency)) {
        currencyOptions.push(currency[key].code)
      }
      return currencyOptions
    },
    membershipOffers(): Array<object | []> {
      const { offers } = this.$store.state.membership
      if (offers && offers.length > 0) return offers
      return []
    },
    enableAuthorizeCard():boolean {
      return (this.paymentType === this.productType.SUBSCRIPTION || this.paymentType === this.productType.PAYMENT_PLAN) && !this.enableUpFrontCost
    }
  },
  watch: {
    enableCardAuthCharge(changeState) {
      if (!changeState) {
        this.cardAuthConfig = {} as cardAuthConfig
      }
    },
    selectedPlan(value) {
      if (value && value.statement_descriptor) {
        this.productDescription = value.statement_descriptor
      }
      if (value && value.trial_period_days && this.actionType !== 'edit') {
        this.trialPeriod = value.trial_period_days
      }
    },
    productId() {
      this.loadData()
    },
    planId(val) {
      if (!val) {
        this.clearAll()
      }
    },
    paymentType(newVal,oldVal){
      if(oldVal) {
        this.enableUpFrontCost = false
      }
    }
  },
  methods: {
    currencySymbol(amount: string, currencyCode: string) {
      if (
        currencyCode &&
        currency[currencyCode] &&
        currency[currencyCode].symbol
      ) {
        return currency[currencyCode].symbol + amount
      }
      return amount
    },
    async loadData() {
      const funnel: Funnel = await Funnel.getById(this.$route.params.funnel_id)
      const step = funnel.getStepById(this.$route.params.step_id)
      if (!step.products) return
      const product: any = step.products.find(
        (x: any) => x.id === this.productId
      )
      if (!product) return
      this.productName = product.productName
      this.priceDisplay = product.priceDisplay
      this.productDescription = product.productDescription
      this.trialPeriod = product.trialPeriod
      this.paymentType = product.paymentType
      this.productPrice = product.productPrice
      this.bumpProduct = product.bumpProduct || false
      this.upfrontFee = product.upfrontFee || ({} as upFrontFee)
      this.numberOfPayments = product.numberOfPayments

      this.cardAuthConfig = product.cardAuthConfig || {}
      if (
        this.cardAuthConfig &&
        this.cardAuthConfig.amount &&
        this.cardAuthConfig.currency
      ) {
        this.enableCardAuthCharge = true
      }

      this.currencyPicked = product.currencyPicked || ''

      this.enableUpFrontCost = product.upfrontFee ? true : false // manage upfront fee toggle

      if (
        (this.type === PRODUCT_TYPE.SUBSCRIPTION ||
          this.type === PRODUCT_TYPE.PAYMENT_PLAN) &&
        this.id
      ) {
        this.selectedPlan = this.id
      }
      if (this.offerId) {
        this.selectedMembershipOffer = this.offerId
        this.enableMembershipOffer = true
      }
    },
    async loadStripe() {
      const params = {
        location_id: this.$route.params.location_id,
        funnel_id: this.$route.params.funnel_id,
      }
      const path = '/stripe/funnel/product_plan/list'

      await this.$http
        .get(path, { params })
        .then(resp => {
          const { data } = resp
          const stripePlans = data.map((d: any) => {
            const { title, plan_id, product_id, plan_interval, amount } = d

            const planCurrency = d.currency?.toUpperCase()
            const currencyObj = currency[planCurrency]
            const symbol = currencyObj ? currencyObj.symbol : planCurrency
            const stripePlanTitle = `${title} [${product_id}] - ${symbol}${amount / 100}/${plan_interval} [${plan_id}]`
            console.log('generated stripe plan title --> ', stripePlanTitle)
            return {
              ...d,
              title: stripePlanTitle,
            }
          })
          this.stripePlans = stripePlans
        })
        .catch(() => {
          console.log('unable to fetch stripe plans')
        })
    },
    clearAll() {
      this.productName = 'Unnamed Product'
      this.priceDisplay = undefined
      this.productPrice = ''
      this.productDescription = undefined
      this.trialPeriod = undefined
      this.currencyPicked = ''
      this.numberOfPayments = undefined
      this.upfrontFee = {} as upFrontFee
      this.selectedMembershipOffer = ''
      this.selectedPlan = ''
      this.cardAuthConfig = {} as cardAuthConfig
    },
    async updateProduct() {
      this.$validator.validateAll().then(async result => {
        if (result) {
          this.disableAction = true
          const funnel: Funnel = await Funnel.getById(
            this.$route.params.funnel_id
          )
          const steps: any = funnel.getStepById(this.$route.params.step_id)

          let stripePlan: any
          if (this.selectedPlan) {
            stripePlan = this.stripePlans.find(
              (x: any) => x.plan_id === this.selectedPlan
            )
          }

          const payload = {
            paymentType: this.paymentType,
            productName: this.productName,
            priceDisplay: this.priceDisplay,
            productDescription: this.productDescription,
            trialPeriod: this.trialPeriod,
            currencyPicked: this.currencyPicked,
            productPrice: this.productPrice,
            product_id: stripePlan ? stripePlan.product_id : undefined,
            plan_id: stripePlan ? stripePlan.plan_id : undefined,
            ...(this.enableMembershipOffer && {
              offerId: this.selectedMembershipOffer
            }),
            deleted: false,
            bumpProduct: this.bumpProduct,
            ...(this.enableUpFrontCost &&
              this.upfrontFee && {
                upfrontFee: { ...this.upfrontFee, ...{ id: uuid() } }
              }),
            cardAuthConfig: this.cardAuthConfig,
            numberOfPayments: this.numberOfPayments
          }
          if (funnel) {
            const index = steps.products.findIndex(
              (x: any) => x.id === this.productId
            )
            if (index > -1) {
              const filteredData = pickBy(
                payload,
                (v: any) => v !== null && v !== undefined
              )
              const { id } = steps.products[index]
              const product = { ...filteredData, ...{ id } }
              steps.products[index] = product
              funnel.updateStep(steps)
              await funnel.save()
              this.clearAll()
              this.$parent.switchToProductList()
              this.disableAction = false
            }
          }
        }
      })
    },
    async saveProduct() {
      this.$validator.validateAll().then(async result => {
        if (result) {
          this.disableAction = true

          const funnel: Funnel = await Funnel.getById(
            this.$route.params.funnel_id
          )

          const step = funnel.getStepById(this.$route.params.step_id)

          let stripePlan: any
          if (this.selectedPlan) {
            stripePlan = this.stripePlans.find(
              (x: any) => x.plan_id === this.selectedPlan
            )
          }

          const product = {
            id: uuid(),
            paymentType: this.paymentType,
            productName: this.productName,
            priceDisplay: this.priceDisplay,
            productDescription: this.productDescription,
            trialPeriod: this.trialPeriod,
            currencyPicked: this.currencyPicked,
            productPrice: this.productPrice,
            product_id: stripePlan ? stripePlan.product_id : undefined,
            plan_id: stripePlan ? stripePlan.plan_id : undefined,
            bumpProduct: this.bumpProduct,
            ...(this.enableMembershipOffer && {
              offerId: this.selectedMembershipOffer
            }),

            deleted: false,
            ...(this.enableUpFrontCost &&
              this.upfrontFee && {
                upfrontFee: { ...this.upfrontFee, ...{ id: uuid() } }
              }),
            cardAuthConfig: this.cardAuthConfig,
            numberOfPayments: this.numberOfPayments
          }
          if (funnel) {
            const details = pickBy(
              product,
              (v: any) => v !== null && v !== undefined
            )
            step.products
              ? step.products.push(details)
              : (step.products = [details])
            funnel.updateStep(step)
            await funnel.save()

            this.clearAll()
            this.$parent.switchToProductList()
            this.disableAction = false
          }
        } else {
          console.error('validation faild')
        }
      })
    }
  }
})
</script>
<style scoped>
.v-select .dropdown-toggle {
  padding: 0px !important;
  height: auto;
  font-size: 14px;
  border: 1px solid rgba(34, 36, 38, 0.15) !important;
  color: #2c3438;
  border-radius: 0.28571429rem;
}
.subscription-type {
  display: grid;
  grid-template-columns: 1fr;
}
.subscription-type .head-label {
  font-weight: bold;
  font-size: 18px;
}
.subscription-type span {
  font-size: 15px;
}
.product-details {
  display: grid;
  grid-template-columns: 80%;
  margin-top: 10px;
}
.product-details .label {
  font-weight: bold;
}
.product-details .label-optional {
  opacity: 0.5;
  font-size: 0.8rem;
  margin-top: -2px;
  margin-bottom: 10px;
}
.payment-input {
  margin: 0;
  outline: 0;
  -webkit-appearance: none;
  tap-highlight-color: rgba(255, 255, 255, 0);
  line-height: 1.21428571em;
  padding: 0.67857143em 1em;
  font-size: 1em;
  background: #fff;
  border: 1px solid rgba(34, 36, 38, 0.15);
  color: #2c3438;
  border-radius: 0.28571429rem;
  -webkit-box-shadow: 0 0 0 0 transparent inset;
  box-shadow: 0 0 0 0 transparent inset;
  -webkit-transition: color 0.1s ease, border-color 0.1s ease;
  transition: color 0.1s ease, border-color 0.1s ease;
}
.payment-input::placeholder {
  color: rgba(34, 36, 38, 0.3);
}
.payment-input-select {
  height: 40px;
  background-color: #fff;
  border: 1px solid rgba(34, 36, 38, 0.15);
  color: #2c3438;
  margin: 0;
  outline: 0;
  transition: color 0.1s ease, border-color 0.1s ease;
}
.payment-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
.payment-actions button {
  margin-right: 5px;
}
.split-two {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-column-gap: 20px;
}
.split-item {
  display: grid;
  grid-template-columns: 1fr;
}
.label sup {
  color: red;
}
.manage-products .is-danger {
  border: 1px solid red;
}
.split-view {
  display: grid;
  grid-template-columns: 1fr 1fr;
}
</style>
