<template>
  <div class="flex items-center">
    <input
     type="radio"
     class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 disabled:opacity-50"
     :id="id"
     :value="value"
     :name="name"
     @input="$emit('input', value)"
     :checked="selectedValue == value"
     :disabled="disabled"
    >

    <label v-if="label" :for="id" class="ml-2 mb-0 block text-sm">
      {{label}}
    </label>
  </div>
</template>

<script lang="ts">
  import Vue from 'vue'

  export default Vue.extend({
    props: {
      label: {
        type: String,
        default: ''
      },
      id: {
        type: String,
        default: ''
      },
      value:{
        type: [Boolean,Array,String],
        default: false
      },
      name: {
        type: String,
        default: ''
      },
      selectedValue: {
        type: [Boolean,Array,String],
        default: false
      },
      disabled: {
        type: [Boolean,Array,String],
        default: false
      }
    }
  })
</script>

<style scoped>

</style>
