/**
 * SubAccount Types and Interfaces
 * 
 * Complete type definitions for SubAccount entities based on SPM Location model
 * Renamed from "Location" to "SubAccount" for better clarity in the GHL context
 * Includes all properties, settings, and relationships from the original model
 */

import type { IEntity } from './base';

/**
 * SubAccount status enumeration
 * Based on SPM LocationStatus enum
 */
export enum SubAccountStatus {
  PROSPECT = 'prospect',
  ACCOUNT = 'account',
}

/**
 * SAAS product types
 * Based on SPM SAAS_PRODUCT type
 */
export type SaasProduct =
  | '2-way-text-messaging'
  | 'gmb-messaging'
  | 'web-chat'
  | 'reputation-management'
  | 'facebook-messenger'
  | 'gmb-call-tracking'
  | 'missed-call-text-back'
  | 'text-to-pay'
  | 'calendar'
  | 'crm'
  | 'opportunities'
  | 'email-marketing'
  | 'form-builder'
  | 'survey-builder'
  | 'trigger-links'
  | 'html-builder'
  | 'sms-email-templates'
  | 'funnels'
  | 'websites'
  | 'workflow'
  | 'membership'
  | 'all-reports'
  | 'triggers'
  | 'campaigns';

/**
 * Prospect information interface
 * Based on SPM ProspectInfo interface
 */
export interface IProspectInfo {
  firstName?: string;
  lastName?: string;
  email?: string;
}

/**
 * GMB (Google My Business) settings interface
 * Based on SPM GMBSettingInfo interface
 */
export interface IGMBSettings {
  displayName?: string;
  contactName?: string;
  contactEmail?: string;
  brandName?: string;
  gmbNumber?: string;
  welcomeMessage?: string;
  logoURL?: string;
  privacyPolicyUrl?: string;
}

/**
 * GMB messaging status interface
 * Based on SPM GMBMessagingStaus interface
 */
export interface IGMBMessagingStatus {
  messagingStatus?: boolean;
  callingStatus?: boolean;
}

/**
 * Twilio rebilling settings interface
 * Based on SPM ITwilioRebillingSettings interface
 */
export interface ITwilioRebillingSettings {
  enabled: boolean;
  markupPercentage?: number;
  minimumCharge?: number;
}

/**
 * Mailgun rebilling settings interface
 * Based on SPM IMailgunRebillingSettings interface
 */
export interface IMailgunRebillingSettings {
  enabled: boolean;
  markupPercentage?: number;
  minimumCharge?: number;
}

/**
 * Complementary credits interface
 * Based on SPM IComplementaryCredits interface
 */
export interface IComplementaryCredits {
  sms?: number;
  email?: number;
  voice?: number;
}

/**
 * SAAS settings interface
 * Based on SPM ISaasSettings interface
 */
export interface ISaasSettings {
  saasMode: 'activated' | 'setup_pending' | 'not_activated';
  stripeCustomerId?: string;
  twilioRebilling: ITwilioRebillingSettings;
  mailgunRebilling: IMailgunRebillingSettings;
  complementaryCredits?: IComplementaryCredits;
  saasProducts?: SaasProduct[];
  stripePlanDetails?: {
    subscriptionId: string;
    productId: string;
    priceId: string;
  };
  saasPlanId?: string;
}

/**
 * Email settings interface
 * Based on SPM EmailSettings interface
 */
export interface IEmailSettings {
  fromName?: string;
  fromEmail?: string;
  replyToEmail?: string;
  signature?: string;
  enabled: boolean;
}

/**
 * SMS settings interface
 * Based on SPM SMSSettings interface
 */
export interface ISMSSettings {
  enabled: boolean;
  fromNumber?: string;
  signature?: string;
}

/**
 * Review settings interface
 * Based on SPM ReviewSettings interface
 */
export interface IReviewSettings {
  enabled: boolean;
  platforms?: string[];
  autoRequest?: boolean;
  requestDelay?: number;
}

/**
 * Social settings interface
 * Based on SPM SocialSettings interface
 */
export interface ISocialSettings {
  enabled: boolean;
  platforms?: {
    facebook?: boolean;
    twitter?: boolean;
    instagram?: boolean;
    linkedin?: boolean;
  };
}

/**
 * Widget settings interface
 * Based on SPM WidgetSettings interface
 */
export interface IWidgetSettings {
  enabled: boolean;
  theme?: string;
  position?: string;
  customCSS?: string;
}

/**
 * Chat widget settings interface
 * Based on SPM ChatWidgetSettings interface
 */
export interface IChatWidgetSettings {
  enabled: boolean;
  welcomeMessage?: string;
  theme?: string;
  position?: string;
}

/**
 * Text widget settings interface
 * Based on SPM TextWidgetSettings interface
 */
export interface ITextWidgetSettings {
  enabled: boolean;
  welcomeMessage?: string;
  theme?: string;
}

/**
 * Attribution settings interface
 * Based on SPM AttributionSettings interface
 */
export interface IAttributionSettings {
  enabled: boolean;
  trackingCode?: string;
}

/**
 * Clio settings interface
 * Based on SPM ClioSettings interface
 */
export interface IClioSettings {
  enabled: boolean;
  apiKey?: string;
  clientId?: string;
}

/**
 * DrChrono settings interface
 * Based on SPM DrChronoSettings interface
 */
export interface IDrChronoSettings {
  enabled: boolean;
  apiKey?: string;
  clientId?: string;
}

/**
 * QuickBooks Online settings interface
 * Based on SPM QBOSettings interface
 */
export interface IQBOSettings {
  enabled: boolean;
  companyId?: string;
  accessToken?: string;
}

/**
 * Complete settings interface
 * Based on SPM Settings interface
 */
export interface ISubAccountSettings {
  email: IEmailSettings;
  sms: ISMSSettings;
  review: IReviewSettings;
  social: ISocialSettings;
  widget: IWidgetSettings;
  chatwidget: IChatWidgetSettings;
  textwidget: ITextWidgetSettings;
  attribution: IAttributionSettings;
  clio: IClioSettings;
  drchrono: IDrChronoSettings;
  qbo: IQBOSettings;
  saasSettings: ISaasSettings;
}

/**
 * Product status interface
 * Based on SPM ProductStatus interface
 */
export interface IProductStatus {
  reviews?: boolean;
  listings?: boolean;
  conversations?: boolean;
  social?: boolean;
}

/**
 * Google My Business interface
 * Based on SPM GoogleMyBusiness interface
 */
export interface IGoogleMyBusiness {
  id?: string;
  name?: string;
}

/**
 * Stripe configuration interface
 * Based on SPM Stripe interface
 */
export interface IStripeConfig {
  publishableKey: string;
  secretKey: string;
}

/**
 * Voicemail file interface
 * Based on SPM VoicemailFile interface
 */
export interface IVoicemailFile {
  url: string;
  name: string;
}

/**
 * Beta access settings interface
 * Based on SPM AllowBetaAccess interface
 */
export interface IBetaAccess {
  workflow: boolean;
  outlook: boolean;
  payments: boolean;
}

/**
 * Launchpad status interface
 * Based on SPM LaunchpadStatus interface
 */
export interface ILaunchpadStatus {
  isChatWidgetIntegrated?: boolean;
  isChatWidgetDisconnected?: boolean;
  isMobileAppInstalled?: boolean;
  isStripeDisconnected?: boolean;
  isStripeConnected?: boolean;
}

/**
 * WordPress location settings interface
 * Based on SPM WordpressLocationSettings interface
 */
export interface IWordpressSettings {
  backupFiles?: Array<{ [key: string]: any }>;
  meta?: { [key: string]: any };
  wpId?: string;
  status?: string;
  statusMessage?: string;
  subDomains?: string[];
  installationType?: string;
  domain?: string;
  stagingDomain?: string;
}

/**
 * Complete SubAccount interface
 * Based on SPM Location model with all properties and methods
 */
export interface ISubAccount extends IEntity {
  // Basic Information
  name: string;
  nameLowerCase: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  phone?: string;
  email?: string;
  website?: string;

  // Business Information
  timezone: string;
  language?: string;
  use24hFormat?: boolean;
  startDayOfWeek?: number;

  // Status and Relationships
  status: SubAccountStatus;
  companyId: string;

  // Settings and Configuration
  settings: ISubAccountSettings;
  productStatus: IProductStatus;

  // Integration Data
  googleMyBusiness?: IGoogleMyBusiness;
  stripeConfig?: IStripeConfig;
  voicemailFiles?: IVoicemailFile[];

  // Feature Flags
  allowBetaAccess?: IBetaAccess;
  launchpadStatus?: ILaunchpadStatus;
  wordpressSettings?: IWordpressSettings;

  // Calendar and Availability
  isCalendarV3On?: boolean;
  isUserAvailabilityOn?: boolean;

  // Compliance and Security
  hipaaCompliance?: boolean;

  // API and Integration
  apiKey?: string;
  snapshotsLoaded?: string[];

  // Prospect Information (for prospect status)
  prospectInfo?: IProspectInfo;

  // GMB Integration
  gmbSettings?: IGMBSettings;
  gmbMessagingStatus?: IGMBMessagingStatus;

  // Flags and Status
  deleted: boolean;
  fbTokenExpired?: boolean;
  sendElizaTag?: string;
}

/**
 * SubAccount creation payload
 * Required fields for creating a new sub-account
 */
export interface ICreateSubAccountPayload {
  name: string;
  companyId: string;
  timezone: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  phone?: string;
  email?: string;
  website?: string;
  language?: string;
  use24hFormat?: boolean;
  startDayOfWeek?: number;
  status?: SubAccountStatus;
  settings?: Partial<ISubAccountSettings>;
  productStatus?: Partial<IProductStatus>;
  prospectInfo?: IProspectInfo;
  hipaaCompliance?: boolean;
}

/**
 * SubAccount update payload
 * Fields that can be updated
 */
export interface IUpdateSubAccountPayload {
  name?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  phone?: string;
  email?: string;
  website?: string;
  timezone?: string;
  language?: string;
  use24hFormat?: boolean;
  startDayOfWeek?: number;
  status?: SubAccountStatus;
  settings?: Partial<ISubAccountSettings>;
  productStatus?: Partial<IProductStatus>;
  allowBetaAccess?: Partial<IBetaAccess>;
  launchpadStatus?: Partial<ILaunchpadStatus>;
  wordpressSettings?: Partial<IWordpressSettings>;
  isCalendarV3On?: boolean;
  isUserAvailabilityOn?: boolean;
  hipaaCompliance?: boolean;
  prospectInfo?: IProspectInfo;
  gmbSettings?: Partial<IGMBSettings>;
  gmbMessagingStatus?: Partial<IGMBMessagingStatus>;
  stripeConfig?: Partial<IStripeConfig>;
  voicemailFiles?: IVoicemailFile[];
  sendElizaTag?: string;
}

/**
 * SubAccount query parameters
 * For searching and filtering sub-accounts
 */
export interface ISubAccountQueryParams {
  companyId?: string;
  status?: SubAccountStatus;
  name?: string;
  city?: string;
  state?: string;
  timezone?: string;
  deleted?: boolean;
  limit?: number;
  offset?: number;
  sortBy?: 'name' | 'dateAdded' | 'dateUpdated' | 'city' | 'state';
  sortOrder?: 'asc' | 'desc';
}

/**
 * SubAccount search response
 * Response format for search operations
 */
export interface ISubAccountSearchResponse {
  subAccounts: ISubAccount[];
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}
