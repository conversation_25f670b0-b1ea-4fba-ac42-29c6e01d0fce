<template>
	<section class="hl_wrapper">
		<section class="hl_wrapper--inner triggers" id="triggers">
			<div class="container-fluid" v-if="trigger">
				<div class="hl_controls">
					<div class="hl_controls--left flex" style="flex-grow: 1;">
						<h3 style="width: 100%;">
							<span @click="goToPreviousRoute()" id="back">
								<i class="icon icon-arrow-left-2"></i>
							</span>
							<input
								type="text"
								placeholder="Trigger name"
								v-model="triggerTitleValue"
								@input="dataChanged()"
                class="trigger-name"
                :disabled="disableEdit"
							>
						</h3>
					</div>
					<div class="hl_controls--right">
						<div class="dropdown">
							<UIButton
								type="button"
								use="outline"
								data-toggle="dropdown"
								aria-haspopup="true"
								aria-expanded="false"
								:disabled="disableEdit"
							>{{ getStatus(trigger) }} <i class="fa fa-chevron-down ml-2 text-gray-500" aria-hidden="true"></i></UIButton>
							<div class="dropdown-menu">
								<a
									class="dropdown-item"
									href="javascript:void(0);"
									@click.prevent="trigger.toggleActive"
									v-if="trigger.active"
								>Draft</a>
								<a
									class="dropdown-item"
									href="javascript:void(0);"
									@click.prevent="activeTrigger()"
									v-else
								>Activate</a>
							</div>
						</div>
						<div class="dropdown">
							<button
								class="btn btn-primary dropdown-toggle"
								type="button"
								data-toggle="dropdown"
								aria-haspopup="true"
								aria-expanded="false"
                :disabled="disableEdit"
							>Actions</button>
							<div class="dropdown-menu dropdown-menu-right">
								<a
									class="dropdown-item"
									href="javascript:void(0);"
									@click.prevent="cloneTrigger(trigger)"
								>Duplicate</a>
								<a
									v-if="dateBasedTrigger.includes(triggerType)"
									class="dropdown-item"
									href="javascript:void(0);"
									@click.prevent="runTrigger(trigger)"
								>Run Now</a>
								<a
									class="dropdown-item"
									href="javascript:void(0);"
									@click.prevent="deleteTrigger(trigger)"
								>Delete</a>
							</div>
						</div>
						<UIButton :loading="saving" type="button" @click="save()" :disabled="disableEdit || saved">{{ saved ? 'Saved' : 'Save' }}</UIButton>
					</div>
				</div>

				<div
					class="alert --yellow"
					role="alert"
					v-if="!trigger.active && !trigger.loopIdentified"
				>This rule will not run until you activate it.</div>

				<div
					class="d-flex justify-content-between align-items-center alert --red"
					role="alert"
					v-if="trigger.deleted"
				>
          <span>This Trigger is deleted! Do you wish to restore it?</span>
          <button v-if="trigger.deleted" class="btn btn-danger" type="button" @click="restore()" >Restore</button>
        </div>

				<div
					class="d-flex justify-content-between align-items-center alert --red"
					role="alert"
					v-if="trigger.loopIdentified"
				>
          <span>This Trigger has been locked in Draft status because it is causing a loop. To activate this Trigger, please contact Support.</span>
        </div>

        <div
					class="d-flex justify-content-between align-items-center alert --green"
					role="alert"
					v-if="isInternalUser || $route.query.debug"
				>
          <span>Trigger creation: {{ trigger.dateAdded }}</span>
          <span>Last update: {{ trigger.dateUpdated }}</span>
        </div>

				<div class="hl_rules--wrap">
					<div class="row">
						<div class="col-lg-6">
							<h4>
								<strong>1</strong> What should trigger this rule?
							</h4>
							<div class="card">
								<!-- <a href="#" class="close"><i class="icon icon-close"></i></a> -->
								<div class="card-body">
									<div class="form-group">
										<label>Which event should trigger this automation?</label>
										<div class="form-input-group space-x-1">
											<div class="form-input-group-item dropdown">
												<div data-toggle="dropdown">
													<i class="icon icon-arrow-down-1"></i>
													<input
														type="text"
														class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
														value="Drip"
														placeholder="Source"
														v-model="formattedTriggerSource"
													>
												</div>
												<div class="dropdown-menu">
													<a
														class="dropdown-item"
														href="javascript:void(0);"
														@click.prevent="formattedTriggerSource='highlevel';dataChanged()"
													>{{company.name || 'Internal' }}</a>
													<a
														class="dropdown-item"
														href="javascript:void(0);"
														@click.prevent="formattedTriggerSource='facebook';dataChanged()"
													>Facebook</a>
												</div>
											</div>
											<div class="form-input-group-item dropdown">
												<div data-toggle="dropdown">
													<i class="icon icon-arrow-down-1"></i>
													<input
														type="text"
														class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
														placeholder="Choose a trigger"
														v-model="formattedTriggerType"
													>
												</div>
												<div class="dropdown-menu">
                          <div v-for="option in triggerTypes">
                            <p class="section-heading" v-if="option.title && option.options.length">{{option.title}}</p>
                            <a
                              class="dropdown-item trigger-type"
                              href="javascript:void(0);"
                              v-for="triggerType in option.options"
                              :key="triggerType.value"
                              v-if="checkPermission(triggerType.value)"
                              @click.prevent="formattedTriggerType=triggerType.value;dataChanged()"
                            >
                              <h5>{{triggerType.title}}</h5>
                              <p>{{triggerType.description}}</p>
                            </a>
                          </div>
												</div>
											</div>
											<span
												v-if="dateBasedTrigger.includes(this.triggerType)"
												style="margin-left: 10px;flex: unset !important;"
												class="pointer"
												title="Trigger run at 8:00 AM daily"
												v-b-tooltip.hover
												placement="top"
												data-html="true"
												data-toggle="tooltip"
												>
												<i class="fas fa-info"></i>
												</a>
											</span>
										</div>
									</div>
									<component
										:is="filterTypes"
										v-if="filterTypes"
										:conditions="conditions"
                    :matchYear="matchYear"
										v-on:update:conditions="conditions=$event; dataChanged()"
                    v-on:update:matchYear="matchYear=$event; dataChanged()"
									/>
								</div>
							</div>
						</div>

						<div class="the-arrow">
							<i class="icon icon-arrow-down-2"></i>
						</div>

						<div class="col-lg-6">
							<h4>
								<strong>2</strong> What actions should we perform?
							</h4>

							<ActionCard
								v-for="(action, index) in actions"
								:key="index"
								:action="action"
                :triggerType="triggerType"
                :showFromWarning="showFromWarning"
								v-on:update:action="updateAction(index, $event); dataChanged()"
							></ActionCard>

							<div class="text-center">
								<a href="javascript:void(0);" @click.prevent="addAction" class="btn btn-success">
									<i class="icon icon-plus"></i> Add action
								</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
		<!-- END of .customers -->
		<CopyTriggerModal :values="copyValues" @hidden="copyValues={visible: false}"/>
	</section>
	<!-- END of .hl_wrapper -->
</template>

<script lang="ts">
import Vue from 'vue';
import Trigger, { Condition, Action, TriggerType } from '@/models/trigger';
import { TriggerMaster } from '@/util/trigger';
import { Company, User, Location, Folder } from '@/models/';
import { mapState, mapGetters } from 'vuex';
import { CompanyState, UserState, LocationState } from '../../../store/state_models';
import { EventBus } from '@/models/event-bus';
import { UxMessage } from '@/util/ux_message'

import libphonenumber from 'google-libphonenumber'
const phoneUtil = libphonenumber.PhoneNumberUtil.getInstance()

const CopyTriggerModal = () => import("../../components/triggers/CopyTriggerModal.vue");
const PipelineFilters = () => import('../../components/triggers/PipelineFilters.vue');
const CampaignFilters = () => import('../../components/triggers/CampaignFilters.vue');
const BotFilters = () => import('../../components/triggers/BotFilters.vue');

const AppointmentFilters = () => import('../../components/triggers/AppointmentFilters.vue');
const AppointmentV3Filters = () => import('../../components/triggers/AppointmentV3Filters.vue');
const ContactReplyFilter = () => import('../../components/triggers/ContactReplyFilter.vue');
const TriggerLinkFilters = () => import('../../components/triggers/TriggerLinkFilters.vue');
const FormSubmissionFilters = () => import('../../components/triggers/FormSubmissionFilters.vue');
const TwoStepFormFilters = () => import('../../components/triggers/TwoStepFormFilters.vue');
const CustomerAppointmentFilters = () => import('../../components/triggers/CustomerAppointmentFilters.vue');
const CustomerAppointmentV3Filters = () => import('../../components/triggers/CustomerAppointmentV3Filters.vue');
const TagFilters = () => import('../../components/triggers/TagFilters.vue');
const OpportunityStatusFilter = () => import('../../components/triggers/OpportunityStatusFilter.vue');
const ActionCard = () => import('../../components/triggers/ActionCard.vue');
const OpportunityDecayFilter = () => import('../../components/triggers/OpportunityDecayFilter.vue');
const FacebookLeadTrigger = () => import('../../components/triggers/FacebookLeadTrigger.vue');
const SurveySubmissionFilters = () => import('../../components/triggers/SurveySubmissionFilters.vue');
const BirthdayReminderFilters = () => import('../../components/triggers/BirthdayReminderFilters.vue');
const CustomDateReminderFilters = () => import('../../components/triggers/CustomDateReminderFilters.vue');
const DNDFilters = () => import('../../components/triggers/DNDFilter.vue');
const TaskDueReminderFilters = () => import('../../components/triggers/TaskDueReminderFilters.vue');
const MailgunEmailEventFilters = () => import('../../components/triggers/MailgunEmailEventFilters.vue');
const CallStatusFilter = () => import('../../components/triggers/CallStatusFilter.vue');
const DetailPageModal = () => import('../../components/triggers/DetailPageModal.vue');
const NotesFilter = () => import('../../components/triggers/NotesFilter.vue');
const MembershipProductAccessFilter = () => import('../../components/triggers/MembershipProductAccessFilter.vue')
const MembershipProductCompleteFilter = () => import('../../components/triggers/MembershipProductCompleteFilter.vue')
const MembershipProductProgressFilter = () => import('../../components/triggers/MembershipProductProgressFilter.vue')
const MembershipUserLoginFilter = () => import('../../components/triggers/MembershipUserLoginFilter.vue')
const MembershipContactCreatedFilter = () => import('../../components/triggers/MembershipContactCreated.vue');
const MembershipOfferAccessFilter = () => import('../../components/triggers/MembershipOfferAccessFilter.vue');
const TaskFilter = () => import('../../components/triggers/TaskFilter.vue');
const ValidationErrorFilter = () => import('../../components/triggers/ValidationErrorFilter.vue');

declare var $: any;
export default Vue.extend({
	inject: ['uxmessage'],
  beforeRouteUpdate (to, from, next) {
    if (this.saved || window.confirm('Your changes will not be saved. Are you sure you want to leave?'))
    {
		this.saved = true;
      next();
    }
    else {
      next(false);
    }
  },
  beforeRouteLeave (to, from, next) {
    if (this.saved || window.confirm('Your changes will not be saved. Are you sure you want to leave?'))
    {
		this.saved = true;
      next();
    }
    else {
      next(false);
    }
  },
	components: { CopyTriggerModal, PipelineFilters, CampaignFilters, BotFilters, ActionCard, AppointmentFilters, AppointmentV3Filters, ContactReplyFilter, TriggerLinkFilters, FormSubmissionFilters, TwoStepFormFilters, CustomerAppointmentFilters, CustomerAppointmentV3Filters, OpportunityDecayFilter, TagFilters, OpportunityStatusFilter, FacebookLeadTrigger, SurveySubmissionFilters, BirthdayReminderFilters, CustomDateReminderFilters, DNDFilters, TaskDueReminderFilters, MailgunEmailEventFilters, CallStatusFilter, NotesFilter, MembershipProductAccessFilter, MembershipProductCompleteFilter, MembershipProductProgressFilter, MembershipUserLoginFilter, MembershipContactCreatedFilter, MembershipOfferAccessFilter, TaskFilter, ValidationErrorFilter },
	data() {
		return {
      trigger: {} as Trigger,
      initialTrigger: {} as Trigger,
      currentLocationId: '',
			copyValues: {
				visible: false,
				trigger: undefined as Trigger | undefined,
				currentLocationId: ''
			},
			triggerSource: 'highlevel',
			triggerMaster: TriggerMaster,
			triggerType: '',
      conditions: [] as Condition[],
      matchYear: false,
      actions: [] as { [key: string]: any }[],
      error: false,
			saved: true,
			saving: false,
			dateBasedTrigger: ['task_due_date_reminder', 'custom_date_reminder', 'birthday_reminder', 'opportunity_decay'],
			triggerTitle: '',
			showFromWarning: false,
			folderId: '',
      pageUpdated: false,
      location: undefined,
      // isCalendarV3On: false, // TODO: Delete for later
      triggerTypes: [],
      botService: false
		}
	},
	computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
		...mapState('company', {
			company: (s: CompanyState) => {
				return s.company ? new Company(s.company) : undefined;
			},
		}),
    isInternalUser() {
      return Boolean(this.$store.state.user?.internalUser);
    },
		filterFolders() {
			let filtered = this.folders.filter(f => f.id === this.$route.params.folder_id);
			return filtered;
		},
		filterTypes() {
			switch (this.triggerType) {
				case 'pipeline_stage_updated':
					return 'PipelineFilters';
				case 'customer_reply':
					return 'ContactReplyFilter';
				case 'added_to_campaign':
          return 'CampaignFilters';
        case 'add_to_bot':
        case 'remove_from_bot':
				case 'bot_failed':
					return 'BotFilters'
				case 'appointment':
          return 'AppointmentFilters';
        case 'appointment_v3':
					return 'AppointmentV3Filters';
				case 'trigger_link':
					return 'TriggerLinkFilters';
				case 'customer_appointment':
          return 'CustomerAppointmentFilters';
        case 'customer_appointment_v3':
					return 'CustomerAppointmentV3Filters';
				case 'opportunity_decay':
					return 'OpportunityDecayFilter';
				case 'form_submission':
					return 'FormSubmissionFilters';
				case 'contact_tag':
					return 'TagFilters';
				case 'opportunity_status_changed':
					return 'OpportunityStatusFilter';
				case 'facebook_lead_gen':
          return 'FacebookLeadTrigger';
        case 'survey_submission':
          return 'SurveySubmissionFilters';
        case 'birthday_reminder':
          return 'BirthdayReminderFilters';
        case 'custom_date_reminder':
          return 'CustomDateReminderFilters';
        case 'mailgun_email_event':
          return 'MailgunEmailEventFilters';
        case 'dnd_contact':
          return 'DNDFilters';
        case 'task_due_date_reminder':
          return 'TaskDueReminderFilters';
        case 'two_step_form_submission':
          return 'TwoStepFormFilters';
        case 'call_status':
          return 'CallStatusFilter';
        case 'note_add':
          return 'NotesFilter'
        case 'product_access_granted':
        case 'product_access_removed':
          return 'MembershipProductAccessFilter'
        case 'user_log_in':
          return 'MembershipUserLoginFilter'
        case 'product_completed':
          return 'MembershipProductCompleteFilter'
        case 'product_progress_percentage':
          return 'MembershipProductProgressFilter'
        case 'membership_contact_created':
          return 'MembershipContactCreatedFilter';
        case 'offer_access_granted':
        case 'offer_access_removed':
		 			return 'MembershipOfferAccessFilter';
				case 'task_added':
					return 'TaskFilter';
				case 'validation_error':
					return 'ValidationErrorFilter';
				}
			return '';
		},
		formattedTriggerSource: {
			get(): string {
				if (this.triggerSource === 'highlevel') return this.company.name || 'Internal';
				return this.$options.filters['toTitleCase'](this.triggerSource);
			},
			set(value: string) {
				this.triggerSource = value;
        this.triggerType = '';
        this.setTriggerType()
        this.conditions = [];
        this.matchYear = false;
			}
		},
		formattedTriggerType: {
			get(): string {
        if (!this.triggerSource || !this.triggerType) return '';
        let selected;
				this.triggerMaster[this.triggerSource].some(triggerType => {
          selected = lodash.find(triggerType.options, { value: this.triggerType });
          if (selected) return true;
        });
				return this.$options.filters['toTitleCase'](selected.title);
			},
			set(value: string) {
				this.triggerType = value;
        this.conditions = [];
        this.matchYear = false;
			}
		},
    triggerTitleValue: {
      get(): string {
        return this.triggerTitle
			},
			set(value: string) {
				this.triggerTitle = value;
			}
    },
    disableEdit() {
      return Boolean(!this.trigger || this.trigger.deleted || this.trigger.loopIdentified)
    },
	getSideBarVersion(): string {
      return this.$store.getters['sidebarv2/getVersion']
    },
	},
	methods: {
	goToPreviousRoute() {
		if (this.getSideBarVersion == 'v2') {
			this.$router.push({
				name: 'triggers-v2',
				params: {
					location_id: this.currentLocationId
				}
			})
		} else {
			this.$router.go(-1)
		}
	},
    setTriggerType() {
      let triggers: Array<{[key: string]: any}> = this.triggerMaster[this.triggerSource].filter(x => x);
      // filter calendar appointment triggers based on location selection
      this.triggerTypes = triggers.map(trigger => {
        let options = []
        // if (this.isCalendarV3On) {
        options = trigger.options.filter(x => true)
        // } else {
        //   options = trigger.options.filter(x => x.value !== 'customer_appointment_v3' && x.value !== 'appointment_v3')
        // }

        if((!this.botService) || (this.user && !this.user.permissions.bot_service)){
          if (trigger.title === 'Eliza triggers') options = [];
				}

        trigger.options = options.sort((a, b) => a.title.localeCompare(b.title))
        return trigger;
      });
    },
		hideModal(sameRoute: boolean) {
			this.modal.visible = false;
      document.querySelectorAll('.modal-backdrop')
				.forEach(a => a.remove())
			if(!sameRoute ) {
				this.$router.go(-1)
			}
		},
		hasRule(errors, rule) {
			let has = false
			errors.items.every(element => {
				if (element.rule === rule) {
					has = true
					return false
				}
				return true
			});
			return has
		},
    checkPermission(type: string) {
      // switch(type) {
      //   case 'added_to_campaign':
      //     return this.user && this.user.permissions.campaigns_enabled
      //   case 'customer_appointment':
      //   case 'appointment':
      //     return this.user && this.user.permissions.appointments_enabled
      //   case 'opportunity_decay':
      //   case 'opportunity_status_changed':
      //   case 'pipeline_stage_updated':
      //     return this.user && this.user.permissions.opportunities_enabled
      //   case 'contact_tag':
      //     return this.user && this.user.permissions.tags_enabled
      //   case 'dnd_contact':
      //   case 'task_due_date_reminder':
      //   case 'custom_date_reminder':
      //   case 'birthday_reminder':
      //     return this.user && this.user.permissions.contacts_enabled
      //   case 'two_step_form_submission':
      //     return this.user && this.user.permissions.funnels_enabled
      //   case 'customer_reply':
      //     return this.user && this.user.permissions.conversations_enabled
      // }
      return true;
    },
		getEndPoint(triggerType: string): string {
			if (triggerType === TriggerType.OPPORTUNITY_DECAY) {
				return '/trigger/processDecayOpportunities';
			} else if (triggerType === TriggerType.BIRTHDAY_REMINDER) {
				return '/trigger/processBirthdayReminder';
			} else if (triggerType === TriggerType.CUSTOM_DATE_REMINDER) {
				return '/trigger/processCustomDateReminder';
			} else if (triggerType === TriggerType.TASK_DUE_DATE_REMINDER) {
				return '/trigger/processTaskDueReminder';
			}
			return '';
		},
		async runTrigger() {
			let response = await this.$http.post(this.getEndPoint(this.triggerType), { trigger_id: this.trigger.id });
		},
    checkRequiredCustomDateField() {
      if (this.triggerType === TriggerType.CUSTOM_DATE_REMINDER) {
        const customDateFieldCondition = this.conditions.find(condition => condition.id === 'custom-field');
        if (!customDateFieldCondition) {
          this.$uxMessage('warning', 'Custom Date Field filter is required for Custom Date Reminder Trigger')
          this.error = true
        }
      }
    },
		checkError() {
      this.error = false
      if (!this.triggerType) {
        this.$uxMessage('warning', 'Trigger can\'t be empty')
        this.error = true
      }
      this.checkRequiredCustomDateField();
      if (this.conditions && this.conditions.length !== 0) {
        for(let condition of this.conditions) {
          if (condition.operator !== 'has_value' && (condition.value == undefined || condition.value == null || condition.value.length == 0)) {
            this.$uxMessage('warning', condition.title ? `Condition \'${condition.title}\' can\'t be empty` : 'Empty condition is not allowed.')
            this.error = true
          }
          if (this.error) break;
        }
      }
      if (this.actions) {
        for(let action of this.actions) {
          if (this.error) return;
          switch (action.type) {
            // case 'add_to_bot_conversation':
            //   if(!action.agent || !action.calendarId){
            //     this.$uxMessage('warning', 'Add to Eliza Service requires Eliza and calendarId');
            //     this.error = true
            //     return false;
			// 				}
			// 				if(action.timeout && action.timeout !== 0 && isNaN(action.timeout) ){
			// 					this.$uxMessage('warning', 'Timeout value should be a number');
            //     this.error = true
            //     return false;
			// 				}
            //   break;
            case 'create_opportunity':
            case 'remove_opportunity':
            if (!action.pipeline_id) {
              this.$uxMessage('warning', 'Opportunities actions require you to select a pipeline.')
              this.error = true
            }
            break;
            case 'remove_from_campaign':
            case 'add_to_campaign':
            if (!action.campaign_id) {
              if (action.type === 'remove_from_campaign') this.$uxMessage('warning', 'Remove from campaign action requires campaign.')
              else this.$uxMessage('warning', 'Add to campaign action requires campaign.')
              this.error = true
            }
            break;
            case 'add_contact_tag':
            case 'remove_contact_tag':
            if (!action.tags || !action.tags.length) {
              if (action.type === 'add_contact_tag') this.$uxMessage('warning', 'Add contact tag action requires tag.')
              else this.$uxMessage('warning', 'Remove contact tag action requires tag.')
              this.error = true
            }
            break;
            case 'send_email':
            if (!action.from || action.to.length === 0 || !action.subject || !action.html) {
              this.$uxMessage('warning', 'Send email action requires from, to, subject and message.')
              this.error = true
            }
            break;
            case 'send_sms':
            if (action.to.length === 0 || !action.body) {
              this.$uxMessage('warning', 'Send sms action requires to number and message.')
              this.error = true
            }
            break;
            case 'add_notes':
            if (!action.html) {
              this.$uxMessage('warning', 'Add notes action requires notes data.')
              this.error = true
            }
            break;
            case 'execute_webhook':
            if (!action.webhook_url) {
              this.$uxMessage('warning', 'Execute webhook action requires webhook url.')
              this.error = true
            }
            break;
            case 'task_notification':
            if (!action.title || !action.dueDate) {
              this.$uxMessage('warning', 'Add task action requires title and Due in day.')
              this.error = true
            }
            break;
            case 'facebook_add_to_custom_audience':
            case 'facebook_remove_from_custom_audience':
            if (!action.facebook_account_id || !action.facebook_custom_audience_id) {
              if(action.type === 'facebook_add_to_custom_audience') this.$uxMessage('warning', 'Add to custom audience action requires ad account and custom audience.')
              else this.$uxMessage('warning', 'Remove from custom audience action requires ad account and custom audience.')
              this.error = true
            }
            break;
            case 'dnd_contact':
            if (!action.dnd_contact) {
              this.$uxMessage('warning', 'DND contact action requires DND state to set.')
              this.error = true
            }
            break;
            case 'send_notification':
            if (!action.title || !action.body || !action.userType || !action.redirectPage) {
              this.$uxMessage('warning', 'Send notification action requires title, body and user type for send notification.')
              this.error = true
            } else if (action.userType == 'user' && !action.selectedUser) {
              this.$uxMessage('warning', 'Select user for send notification.')
              this.error = true
            }
            break;
            case 'google_adword':
            if (!action.conversion_name) {
              this.$uxMessage('warning', 'Add to Google Adwords action requires conversion.');
              this.error = true
            }
            break;
            case 'google_analytics':
            if (!action.tracking_id || !action.event_action || !action.event_category) {
              this.$uxMessage('warning', 'Add to Google Analytics action requires tracking id, action type and action category');
              this.error = true
            }
            break;
            case 'stripe_one_time_charge':
            if (!action.stripe_customer_id || !action.currency || !action.amount) {
              alert('Stripe one time charge action requires customer id, amount and currency');
              this.error = true
            } else if (action.currency === 'USD' && action.amount < 0.50) {
              alert('Amount must be greater or equal to 50 cents USD.');
              this.error = true
            } else if (action.currency === 'CAD' && action.amount < 1) {
              alert('Amount must greater or equal to 1 dollar CAD.');
              this.error = true
            } else if (action.currency === 'AUD' && action.amount < 1) {
              alert('Amount must greater or equal to 1 dollar AUD.');
              this.error = true
            }
			break;
			case 'update_appointment_status':
            if (!action.status_type) {
              this.$uxMessage('warning', 'Select Appointment Status')
              this.error = true
            }
            break;
      case 'update_contact_field':
        if (!action.fields || !action.fields.length) {
          this.$uxMessage('warning', 'Update contact field should have atleast 1 field set.');
					this.error = true
        }
				if (action.fields && action.fields.length) {
					for (let field of action.fields) {
						if(!field.value){
							this.$uxMessage('warning', field.title ? `${field.title} can\'t be empty` : 'Empty field is not allowed.')
							this.error = true
						}
						if (this.error) break;
					}
				}
				break;
            case 'membership_grant_offer':
            case 'membership_revoke_offer':
            if (!action.offer_id) {
              if (action.type === 'membership_grant_offer') alert('Membership grant offer action requires offer.')
              else alert('Membership remove offer action requires offer.')
              this.error = true
            }
            break;
					}
				}
				if (this.hasRule(this.errors, 'handlebars') && !this.error){
					this.$uxMessage('warning', 'Some field(s) contain broken custom variables, please correct them before saving.')
					this.error = true
				}
			}
		},
		activeTrigger() {
      this.checkError();
      if (!this.error) {
        this.trigger.toggleActive();
      }
		},
		dataChanged() {
      if (!lodash.isEqual(this.initialTrigger.actions, this.actions) || !lodash.isEqual(this.initialTrigger.conditions, this.conditions) || !lodash.isEqual(this.initialTrigger.title, this.triggerTitle) || !lodash.isEqual(this.initialTrigger.type, this.triggerType) || !lodash.isEqual(this.initialTrigger.matchYear, this.matchYear)) this.saved = false;
      else this.saved = true;

		},
    async restore() {
      this.trigger.deleted = false;
      await this.save();
    },
	  async save() {
      this.checkError();
      if (!this.error) {
				if (this.trigger.folderId !== null) {
					this.trigger.folderId = this.trigger.folderId || this.folderId
				}
			if (this.checkOpportunityTriggerLoop()) return;
		    this.saving = true
        this.trigger.conditions = this.conditions
        this.trigger.type = this.triggerType
        this.trigger.actions = this.actions
        this.trigger.title = this.triggerTitle
        if (this.trigger.type === 'custom_date_reminder') {
          this.trigger.matchYear = this.matchYear
        }
        await this.trigger.save()
		    this.saved = true
		    this.error = false
        this.saving = false
        this.initialTrigger = lodash.cloneDeep(this.trigger);
				this.$router.push({ name: 'triggers_detail', params: { trigger_id: this.trigger.id }});
				this.saveStatus = true
      }
	},
	checkOpportunityTriggerLoop() {
		const warningMessage = 'This trigger will cause an Opportunity creation loop. Please remove the Allow Duplicate Opportunity or change the Stage that you are referencing.'

		const foundAction = this.trigger.actions.find(action => action.type === 'create_opportunity')
		const pipelineCondition = this.trigger.conditions.find(condition => condition.field === 'opportunity.pipelineId')
		const stageCondition = this.trigger.conditions.find(condition => condition.field === 'opportunity.pipelineStageId')
		if (foundAction && foundAction.allow_multiple && pipelineCondition && pipelineCondition.value === foundAction.pipeline_id) {
			if (!stageCondition || (stageCondition.value === foundAction.pipeline_stage_id)) {
				this.uxmessage(UxMessage.warningType(warningMessage))
				return true
			}
		}
		return false
	},
    addAction() {
      this.actions.push({});
    },
    updateAction(index: number, data: undefined | { [key: string]: any }) {
      if (!data) {
        this.actions.splice(index, 1);
      } else {
        this.actions.splice(index, 1, data);
      }
    },
    async fetchData() {
      const location = new Location(await this.$store.dispatch('locations/getById', this.currentLocationId));
      // this.isCalendarV3On = location.isCalendarV3On
      this.botService = location.botService
      if (location.defaultEmailService && location.defaultEmailService !== 'mailgun') {
        this.showFromWarning = await this.$store.dispatch('defaultEmailService/checkFromVisibility', location.defaultEmailService);
      }
      if (this.$router.currentRoute.params.trigger_id !== 'new') {
	    this.trigger = await Trigger.getById(this.$router.currentRoute.params.trigger_id);
        switch (this.trigger.type) {
          case 'pipeline_stage_updated':
          case 'added_to_campaign':
          case 'bot_failed':
          case 'add_to_bot':
          case 'remove_from_bot':
          case 'customer_appointment':
          case 'customer_appointment_v3':
          case 'customer_reply':
          case 'appointment':
          case 'appointment_v3':
          case 'opportunity_decay':
          case 'trigger_link':
          case 'form_submission':
          case 'contact_tag':
          case 'opportunity_status_changed':
          case 'survey_submission':
          case 'birthday_reminder':
          case 'custom_date_reminder':
          case 'mailgun_email_event':
          case 'dnd_contact':
          case 'task_due_date_reminder':
          case 'two_step_form_submission':
          case 'call_status':
          case 'note_add':
		  		case 'task_added':
					case 'validation_error':
          case 'product_access_granted':
          case 'product_access_removed':
          case 'user_log_in':
          case 'product_completed':
          case 'product_progress_percentage':
          case 'membership_contact_created':
		  case 'update_appointment_status':
          case 'offer_access_granted':
          case 'offer_access_removed':
            this.triggerSource = 'highlevel';
            this.triggerType = this.trigger.type;
            this.conditions = this.trigger.conditions;
            this.actions = this.trigger.actions;
            this.matchYear = this.trigger.matchYear;
            break;
          case 'facebook_lead_gen':
            this.triggerSource = 'facebook';
            this.triggerType = this.trigger.type;
            this.conditions = this.trigger.conditions;
            this.actions = this.trigger.actions;
            break;
		}
	  } else {
		this.trigger = new Trigger();
		this.trigger.locationId = this.currentLocationId;
	  }
      this.initialTrigger = lodash.cloneDeep(this.trigger);
      this.triggerTitle = this.trigger.title || this.$route.params.trigger_name;
	},
		cloneTrigger(trigger?: Trigger) {
			this.copyValues = {
				currentLocationId: this.currentLocationId,
				trigger: trigger,
				visible: true
			};
		},
		async deleteTrigger(trigger: Trigger) {
			if (confirm("Are you sure you want to delete this trigger?")) {
				await trigger.delete();
				this.$router.go(-1);
			}
		},
		getStatus(trigger: Trigger) {
			return !trigger.active ? "Draft" : "Active"
		},
	},
	watch: {
		"$route.params.location_id": function (id) {
			console.log({
				name: "triggers",
				params: { location_id: id }
			});
			this.$router.push({
				name: "triggers",
				params: { location_id: id }
			});
		}
	},
	async created() {
    const location = new Location(await this.$store.dispatch('locations/getById', this.$router.currentRoute.params.location_id))
    // this.isCalendarV3On = location.isCalendarV3On
    this.botService = location.botService
    this.setTriggerType()

		this.currentLocationId = this.$router.currentRoute.params.location_id;
		this.folderId = this.$route.params.folder_id
		this.fetchData();
		let _self = this;
		window.addEventListener("beforeunload", event => {
			if (!_self.saved)
			{
				event.preventDefault()
				event.returnValue = '';
			}
		})
	},
	updated() {
		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker('refresh');
		}
	},
	mounted() {
		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker();
		}
	},
})
</script>
<style scoped>
#back {
  cursor: pointer;
  color: #188bf6;
  margin-right: 10px;
}
.section-heading {
  margin: 5px 0;
}
</style>
