<template>
  <div>
    <SideBarV2 v-if="getSideBarVersion === 'v2'"/>
    <SideBar v-else />
    <TopBar />

    <section class="hl_wrapper">
      <section
        class="hl_wrapper--inner hl_agency hl_agency-location--add"
        id="agency_location"
      >
        <div class="container-fluid">
          <div class="hl_controls">
            <div class="hl_controls--left">
              <span @click="$router.go(-1)" class="back">
                <i class="icon icon-arrow-left-2"></i>
              </span>
              <h1>Add account</h1>
            </div>
            <div class="hl_controls--right">
              <UIButton
                :loading="loading"
                @click.prevent="save_account"
              >
                <i class="icon icon-ok mr-2"></i>
                Save
              </UIButton>
            </div>
          </div>

          <div class="row" v-if="location">
            <div class="col-md-8">
              <div class="card" v-if="!selectedSnapshot">
                <div class="card-header">
                  <h2>Account Snapshots</h2>
                </div>
                <div class="card-body">
                  <div class="form-group">
                    <UITextLabel>Select snapshot</UITextLabel>
                    <vSelect
                      :options="accountSnapshots"
                      label="name"
                      v-model="selectedSnapshot"
                      :clearable="false"
                      name="snapshot"
                      class="mt-1"
                    ></vSelect>
                    <span v-show="errors.has('snapshot')" class="error"
                      >Pick a snapshot</span
                    >
                  </div>
                </div>
              </div>
              <div class="card">
                <div class="card-header">
                  <h2>Account info</h2>
                </div>
                <div class="card-body">
                  <div class="form-group">
                    <UITextInputGroup
                      type="text"
                      class="msgsndr5"
                      placeholder="First name"
                      label="First name *"
                      v-model="location.prospectInfo.first_name"
                      v-validate="'required'"
                      name="msgsndr5"
                      autocomplete="msgsndr5"
                      data-vv-as="First name"
                      data-lpignore="true"
                      :error="errors.has('msgsndr5')"
                      :errorMsg="errors.first('msgsndr5')"
                    />
                  </div>
                  <div class="form-group">
                    <UITextInputGroup
                      type="text"
                      class="msgsndr6"
                      placeholder="Last name"
                      label="Last name *"
                      v-model="location.prospectInfo.last_name"
                      v-validate="'required'"
                      name="msgsndr6"
                      autocomplete="msgsndr6"
                      data-vv-as="Last name"
                      data-lpignore="true"
                      :error="errors.has('msgsndr6')"
                      :errorMsg="errors.first('msgsndr6')"
                    />
                  </div>
                  <div class="form-group">
                    <UITextInputGroup
                      type="text"
                      class="msgsndr3"
                      placeholder="Email address *"
                      label="Email address *"
                      v-model="location.prospectInfo.email"
                      v-validate="'required|email'"
                      name="msgsndr3"
                      autocomplete="msgsndr3"
                      data-vv-as="Email"
                      data-lpignore="true"
                      :error="errors.has('msgsndr3')"
                      :errorMsg="errors.first('msgsndr3')"
                    />
                  </div>
                  <!--div class="form-group row" v-if="company.elizaEnabled">
										<div class="col-10">
											<label>Allow first shot by eliza bot</label>
										</div>
                  						<div class="col-2">
											<div class="toggle">
												<input
													type="checkbox"
													class="tgl tgl-light"
													id="first_shot_eliza"
													v-model="first_shot_eliza"
												/>
												<label class="tgl-btn" for="first_shot_eliza"></label>
											</div>
										</div>
									</div> -->
                </div>
              </div>
              <div class="card">
                <div class="card-header">
                  <h2>General info</h2>
                </div>
                <div class="card-body">
                  <div class="form-group">
                    <UITextInputGroup
                      type="text"
                      placeholder="Business name"
                      label="Business name *"
                      v-model="location.name"
                      v-validate="'required'"
                      name="businessName"
                      data-vv-as="Business name"
                      data-lpignore="true"
                      :error="errors.has('businessName')"
                      :errorMsg="errors.first('businessName')"
                    />
                  </div>
                  <!-- <div class="form-group">
                                        <label>Common business name</label>
                                        <input type="text" class="form-control" placeholder="Common business name">
                                        <p class="help-block --right">
                                            <a href="#">+ Add common business name</a>
                                        </p>
									</div>-->
                  <div class="form-group">
                    <UITextInputGroup
                      type="text"
                      class="msgsndr4"
                      placeholder="Street address"
                      label="Street address *"
                      v-model="location.address"
                      v-validate="'required'"
                      name="msgsndr4"
                      autocomplete="msgsndr4"
                      data-vv-as="Street Address"
                      data-lpignore="true"
                      :error="errors.has('msgsndr4')"
                      :errorMsg="errors.first('msgsndr4')"
                    />
                  </div>
                  <div class="form-group">
                    <UITextInputGroup
                      type="text"
                      placeholder="City"
                      label="City *"
                      v-model="location.city"
                      v-validate="'required'"
                      name="city"
                      data-vv-as="City"
                      data-lpignore="true"
                      :error="errors.has('city')"
                      :errorMsg="errors.first('city')"
                    />
                  </div>
                  <div class="form-group">
                    <UITextLabel>Country *</UITextLabel>
                    <select
                      class="selectpicker mt-1"
                      v-model="location.country"
                      v-validate="'required'"
                      data-size="5"
                      name="country"
                      data-vv-as="Country"
                    >
                      <option value>Choose country...</option>
                      <option
                        v-for="(country, value) in countries"
                        :value="value"
                        v-text="country"
                      ></option>
                    </select>
                    <!-- <select
											class="selectpicker"
											title="Country"
											v-model="location.country"
											v-validate="'required'"
											name="country"
										>
                      <option value>Choose one..</option>
                      <option v-for="(country, value) in countries" :value="value" v-text="country"></option>
										</select> -->
                    <span v-show="errors.has('country')" class="error">{{
                      errors.first('country')
                    }}</span>
                  </div>
                  <div class="form-group">
                    <UITextLabel>State / Prov / Region *</UITextLabel>
                    <select
                      v-if="location.country === 'US'"
                      class="selectpicker"
                      title="State / Prov / Region"
                      v-model="location.state"
                      v-validate="'required'"
                      name="state"
                    >
                      <option value>Choose one..</option>
                      <option value="AL">Alabama</option>
                      <option value="AK">Alaska</option>
                      <option value="AZ">Arizona</option>
                      <option value="AR">Arkansas</option>
                      <option value="CA">California</option>
                      <option value="CO">Colorado</option>
                      <option value="CT">Connecticut</option>
                      <option value="DE">Delaware</option>
                      <option value="DC">District Of Columbia</option>
                      <option value="FL">Florida</option>
                      <option value="GA">Georgia</option>
                      <option value="HI">Hawaii</option>
                      <option value="ID">Idaho</option>
                      <option value="IL">Illinois</option>
                      <option value="IN">Indiana</option>
                      <option value="IA">Iowa</option>
                      <option value="KS">Kansas</option>
                      <option value="KY">Kentucky</option>
                      <option value="LA">Louisiana</option>
                      <option value="ME">Maine</option>
                      <option value="MD">Maryland</option>
                      <option value="MA">Massachusetts</option>
                      <option value="MI">Michigan</option>
                      <option value="MN">Minnesota</option>
                      <option value="MS">Mississippi</option>
                      <option value="MO">Missouri</option>
                      <option value="MT">Montana</option>
                      <option value="NE">Nebraska</option>
                      <option value="NV">Nevada</option>
                      <option value="NH">New Hampshire</option>
                      <option value="NJ">New Jersey</option>
                      <option value="NM">New Mexico</option>
                      <option value="NY">New York</option>
                      <option value="NC">North Carolina</option>
                      <option value="ND">North Dakota</option>
                      <option value="OH">Ohio</option>
                      <option value="OK">Oklahoma</option>
                      <option value="OR">Oregon</option>
                      <option value="PA">Pennsylvania</option>
                      <option value="RI">Rhode Island</option>
                      <option value="SC">South Carolina</option>
                      <option value="SD">South Dakota</option>
                      <option value="TN">Tennessee</option>
                      <option value="TX">Texas</option>
                      <option value="UT">Utah</option>
                      <option value="VT">Vermont</option>
                      <option value="VA">Virginia</option>
                      <option value="WA">Washington</option>
                      <option value="WV">West Virginia</option>
                      <option value="WI">Wisconsin</option>
                      <option value="WY">Wyoming</option>
                    </select>
                    <UITextInputGroup
                      v-else
                      type="text"
                      placeholder="State"
                      v-model="location.state"
                      v-validate="'required'"
                      name="state"
                      data-vv-as="State"
                      data-lpignore="true"
                      :error="errors.has('state')"
                      :errorMsg="errors.first('state')"
                    />
                  </div>
                  <div class="form-group">
                    <UITextInputGroup
                      type="text"
                      class="msgsndr7"
                      placeholder="Zip / Postal code"
                      label="Zip / Postal code *"
                      v-model="location.postalCode"
                      v-validate="'required'"
                      name="msgsndr7"
                      autocomplete="msgsndr7"
                      data-vv-as="Zip / Postal code"
                      data-lpignore="true"
                      :error="errors.has('msgsndr7')"
                      :errorMsg="errors.first('msgsndr7')"
                    />
                  </div>
                  <div class="form-group">
                    <UITextLabel>Phone number *</UITextLabel>

                    <PhoneNumber
                      class="msgsndr1"
                      placeholder="Phone number"
                      v-model="location.phone"
                      v-validate="'required|phone'"
                      name="msgsndr1"
                      autocomplete="msgsndr1"
                      data-vv-as="Phone"
                      :error="errors.has('msgsndr1')"
                      :errorMsg="errors.first('msgsndr1')"
                    />
                    <!-- <p class="help-block --right">
                                            <a href="#">+ Add phone number</a>
										</p>-->
                  </div>
                  <div class="form-group">
                    <UITextInputGroup
                      type="text"
                      label="Website"
                      placeholder="Website"
                      v-model="location.website"
                      data-lpignore="true"
                    />
                  </div>
                  <!-- <div class="form-group">
                                        <label>Business categories</label>
                                        <div class="business-category">
                                            <select class="selectpicker" title="Business categories">
                                                <option>Option 1</option>
                                                <option>Option 2</option>
                                                <option>Option 3</option>
                                            </select>
                                        </div>
                                        <div class="business-category">
                                            <select class="selectpicker" title="Business categories">
                                                <option>Option 1</option>
                                                <option>Option 2</option>
                                                <option>Option 3</option>
                                            </select>
                                            <a href="#" class="remove">
                                                <i class="icon icon-close"></i>
                                            </a>
                                        </div>
                                        <p class="help-block --right">
                                            <a href="#">+ Add business category</a>
                                        </p>
									</div>-->
                </div>
              </div>
              <!-- <div class="card">
                                <div class="card-header">
                                    <h2>Hours of operations</h2>
                                </div>
                                <div class="card-body">
                                    <div class="hours-item">
                                        <div class="option">
                                            <input type="checkbox" id="monday" checked>
                                            <label for="monday">Monday</label>
                                        </div>
                                        <select class="selectpicker" title="Please Select">
                                            <option>Option 1</option>
                                            <option>Option 2</option>
                                            <option>Option 3</option>
                                        </select>
                                        <span class="dash"></span>
                                        <select class="selectpicker" title="Please Select">
                                            <option>Option 1</option>
                                            <option>Option 2</option>
                                            <option>Option 3</option>
                                        </select>
                                    </div>
                                    <div class="hours-item">
                                        <div class="option">
                                            <input type="checkbox" id="tuesday" checked>
                                            <label for="tuesday">Tuesday</label>
                                        </div>
                                        <select class="selectpicker" title="Please Select">
                                            <option>Option 1</option>
                                            <option>Option 2</option>
                                            <option>Option 3</option>
                                        </select>
                                        <span class="dash"></span>
                                        <select class="selectpicker" title="Please Select">
                                            <option>Option 1</option>
                                            <option>Option 2</option>
                                            <option>Option 3</option>
                                        </select>
                                    </div>
                                    <div class="hours-item">
                                        <div class="option">
                                            <input type="checkbox" id="wednesday" checked>
                                            <label for="wednesday">Wednesday</label>
                                        </div>
                                        <select class="selectpicker" title="Please Select">
                                            <option>Option 1</option>
                                            <option>Option 2</option>
                                            <option>Option 3</option>
                                        </select>
                                        <span class="dash"></span>
                                        <select class="selectpicker" title="Please Select">
                                            <option>Option 1</option>
                                            <option>Option 2</option>
                                            <option>Option 3</option>
                                        </select>
                                    </div>
                                    <div class="hours-item">
                                        <div class="option">
                                            <input type="checkbox" id="thursday">
                                            <label for="thursday">Thurdsday</label>
                                        </div>
                                        <select class="selectpicker" title="Please Select">
                                            <option>Option 1</option>
                                            <option>Option 2</option>
                                            <option>Option 3</option>
                                        </select>
                                        <span class="dash"></span>
                                        <select class="selectpicker" title="Please Select">
                                            <option>Option 1</option>
                                            <option>Option 2</option>
                                            <option>Option 3</option>
                                        </select>
                                    </div>
                                    <div class="hours-item">
                                        <div class="option">
                                            <input type="checkbox" id="friday">
                                            <label for="friday">Friday</label>
                                        </div>
                                        <select class="selectpicker" title="Please Select">
                                            <option>Option 1</option>
                                            <option>Option 2</option>
                                            <option>Option 3</option>
                                        </select>
                                        <span class="dash"></span>
                                        <select class="selectpicker" title="Please Select">
                                            <option>Option 1</option>
                                            <option>Option 2</option>
                                            <option>Option 3</option>
                                        </select>
                                    </div>
                                    <div class="hours-item">
                                        <div class="option">
                                            <input type="checkbox" id="saturday">
                                            <label for="saturday">Saturday</label>
                                        </div>
                                        <select class="selectpicker" title="Please Select">
                                            <option>Option 1</option>
                                            <option>Option 2</option>
                                            <option>Option 3</option>
                                        </select>
                                        <span class="dash"></span>
                                        <select class="selectpicker" title="Please Select">
                                            <option>Option 1</option>
                                            <option>Option 2</option>
                                            <option>Option 3</option>
                                        </select>
                                    </div>
                                    <div class="hours-item">
                                        <div class="option">
                                            <input type="checkbox" id="sunday">
                                            <label for="sunday">Sunday</label>
                                        </div>
                                        <select class="selectpicker" title="Please Select">
                                            <option>Option 1</option>
                                            <option>Option 2</option>
                                            <option>Option 3</option>
                                        </select>
                                        <span class="dash"></span>
                                        <select class="selectpicker" title="Please Select">
                                            <option>Option 1</option>
                                            <option>Option 2</option>
                                            <option>Option 3</option>
                                        </select>
                                    </div>

                                </div>
                            </div>
                            <div class="card">
                                <div class="card-header">
                                    <h2>Social profiles</h2>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label>Google Plus URL</label>
                                        <input type="text" class="form-control" placeholder="Enter URL">
                                    </div>
                                    <div class="form-group">
                                        <label>Google Places ID</label>
                                        <input type="text" class="form-control" placeholder="ID">
                                    </div>
                                    <div class="form-group">
                                        <label>Google Maps URL</label>
                                        <input type="text" class="form-control" placeholder="Enter URL">
                                    </div>
                                    <div class="form-group">
                                        <label>Facebook</label>
                                        <input type="text" class="form-control" placeholder="Enter URL">
                                    </div>
                                    <div class="form-group">
                                        <label>Linkedin</label>
                                        <input type="text" class="form-control" placeholder="Enter URL">
                                    </div>
                                    <div class="form-group">
                                        <label>Twitter</label>
                                        <input type="text" class="form-control" placeholder="Enter URL">
                                    </div>
                                    <div class="form-group">
                                        <label>Pinterest</label>
                                        <input type="text" class="form-control" placeholder="Enter ID">
                                    </div>
                                    <div class="form-group">
                                        <label>Foursquare</label>
                                        <input type="text" class="form-control" placeholder="Enter URL">
                                    </div>
                                    <div class="form-group">
                                        <label>Instagram</label>
                                        <input type="text" class="form-control" placeholder="Enter URL">
                                    </div>
                                    <div class="form-group">
                                        <label>YouTube</label>
                                        <input type="text" class="form-control" placeholder="Enter URL">
                                    </div>
                                    <div class="form-group">
                                        <label>Blog/RSS</label>
                                        <input type="text" class="form-control" placeholder="Enter URL">
                                    </div>
                                </div>
							</div>-->
            </div>
            <div class="col-md-4" v-if="place">
              <div class="card hl_agency-location--add-sidebar">
                <div class="side-map" id="side-map"></div>
                <div class="card-body">
                  <h4>{{ location.name }}</h4>
                  <p>{{ location.fullAddressLine }}</p>
                  <p>
                    <PhoneNumber type="display" v-model="location.phone" />
                  </p>
                  <!-- <ul class="list-inline">
                                        <li class="list-inline-item">
                                            <a href="#">
                                                <img src="../../../assets/pmd/img/logo-google2.png" alt="Google">
                                            </a>
                                        </li>
                                        <li class="list-inline-item">
                                            <a href="#">
                                                <img src="../../../assets/pmd/img/logo-yelp2.png" alt="Yelp">
                                            </a>
                                        </li>
                                        <li class="list-inline-item">
                                            <a href="#">
                                                <img src="../../../assets/pmd/img/logo-facebook2.png" alt="Facebook">
                                            </a>
                                        </li>
                                        <li class="list-inline-item">
                                            <a href="#">
                                                <img src="../../../assets/pmd/img/logo-twitter2.png" alt="Twitter">
                                            </a>
                                        </li>
									</ul>-->
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <!-- END of .hl_agency-location--add -->
    </section>
    <!-- END of .hl_wrapper -->
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import TopBar from '../../components/TopBar.vue'
import SideBarV2 from '../../components/sidebar/SideBar.vue'
import SideBar from '../../components/agency/SideBar.vue'
import countries from '@/util/countries'
import {
  ScanReport,
  Location,
  Company,
  User,
  MailGunAccount,
  CloneAccount,
  User
} from '@/models'
import firebase from 'firebase/app'
import { mapState } from 'vuex'
import vSelect from 'vue-select'
import { CompanyState, UserState } from '@/store/state_models'
import libphonenumber from 'google-libphonenumber'
import lodash from 'lodash'

import PhoneNumber from '@/pmd/components/util/PhoneNumber.vue'
import depreciatedFeatures from '@/util/depreciated_features';

declare const google: any
const service = new google.maps.places.PlacesService(
  document.createElement('div')
)

const phoneUtil = libphonenumber.PhoneNumberUtil.getInstance()
declare var $: any

const mapOptions = {
  zoom: 14,
  disableDefaultUI: true,
  center: new google.maps.LatLng(40.67, -73.94), // New York
  styles: [
    {
      featureType: 'landscape.man_made',
      elementType: 'geometry.fill',
      stylers: [{ color: '#f4f4f4' }],
    },
    {
      featureType: 'landscape.man_made',
      elementType: 'labels.text',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'landscape.natural',
      elementType: 'geometry.fill',
      stylers: [{ visibility: 'on' }, { color: '#e4e4e4' }],
    },
    {
      featureType: 'landscape.natural.landcover',
      elementType: 'all',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'landscape.natural.landcover',
      elementType: 'geometry.fill',
      stylers: [{ visibility: 'on' }],
    },
    {
      featureType: 'landscape.natural.terrain',
      elementType: 'geometry.fill',
      stylers: [{ visibility: 'off' }, { hue: '#ff0000' }],
    },
    { featureType: 'poi', elementType: 'all', stylers: [{ visibility: 'on' }] },
    {
      featureType: 'poi.attraction',
      elementType: 'all',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'poi.business',
      elementType: 'all',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'poi.government',
      elementType: 'all',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'poi.medical',
      elementType: 'all',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'poi.park',
      elementType: 'all',
      stylers: [{ visibility: 'simplified' }, { gamma: '1' }, { weight: '0' }],
    },
    {
      featureType: 'poi.park',
      elementType: 'geometry.fill',
      stylers: [{ color: '#e4e4e4' }],
    },
    {
      featureType: 'poi.park',
      elementType: 'labels',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'poi.park',
      elementType: 'labels.text',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'poi.place_of_worship',
      elementType: 'all',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'poi.school',
      elementType: 'all',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'poi.sports_complex',
      elementType: 'all',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'road',
      elementType: 'geometry.fill',
      stylers: [
        { saturation: -100 },
        { lightness: 99 },
        { visibility: 'on' },
        { color: '#ffffff' },
      ],
    },
    {
      featureType: 'road',
      elementType: 'geometry.stroke',
      stylers: [{ color: '#808080' }, { lightness: 54 }],
    },
    {
      featureType: 'road',
      elementType: 'labels.text.fill',
      stylers: [{ color: '#a0a0a0' }, { visibility: 'on' }],
    },
    {
      featureType: 'road',
      elementType: 'labels.text.stroke',
      stylers: [{ color: '#ffffff' }],
    },
    {
      featureType: 'road.highway',
      elementType: 'all',
      stylers: [{ visibility: 'on' }],
    },
    {
      featureType: 'road.highway',
      elementType: 'labels',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'road.highway',
      elementType: 'labels.text',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'road.highway',
      elementType: 'labels.icon',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'road.highway.controlled_access',
      elementType: 'all',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'road.local',
      elementType: 'all',
      stylers: [{ visibility: 'on' }],
    },
    {
      featureType: 'transit.line',
      elementType: 'all',
      stylers: [{ visibility: 'on' }],
    },
    {
      featureType: 'transit.line',
      elementType: 'labels',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'transit.line',
      elementType: 'labels.text',
      stylers: [{ color: '#ff0000' }, { visibility: 'off' }],
    },
    {
      featureType: 'transit.line',
      elementType: 'labels.text.fill',
      stylers: [{ visibility: 'off' }],
    },
    {
      featureType: 'transit.line',
      elementType: 'labels.text.stroke',
      stylers: [{ visibility: 'on' }],
    },
    {
      featureType: 'transit.line',
      elementType: 'labels.icon',
      stylers: [{ visibility: 'on' }],
    },
    {
      featureType: 'transit.station',
      elementType: 'all',
      stylers: [{ visibility: 'on' }],
    },
    {
      featureType: 'transit.station',
      elementType: 'labels.text',
      stylers: [{ color: '#ff0000' }, { visibility: 'off' }],
    },
    {
      featureType: 'water',
      elementType: 'all',
      stylers: [{ saturation: 43 }, { lightness: -11 }, { hue: '#0088ff' }],
    },
  ],
}

export default Vue.extend({
  components: {
    TopBar,
    SideBar,
    PhoneNumber,
    vSelect,
    SideBarV2
  },
  data() {
    return {
      //first_shot_eliza: true, //Removing it, as the flow is completely changed for enabling eliza
      location: undefined as Location | undefined,
      loading: false,
      googlePlacesId: '',
      place: undefined as { [key: string]: any } | undefined,
      map: undefined as any,
      selectedSnapshot: undefined as { [key: string]: any } | undefined,
      accountSnapshots: [] as { [key: string]: any }[],
      countries: countries,
    }
  },
  computed: {
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    getSideBarVersion(): string {
        return this.$store.getters['sidebarv2/getVersion'] 
    },
  },
  async created() {
    this.googlePlacesId = this.$route.params.id
    this.getPlaceDetails()
    this.loadData()
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
    if (this.$route.query.snapshot || this.$route.query.s_type) {
      this.selectedSnapshot = {
        id: this.$route.query.snapshot || 'default',
        type: this.$route.query.s_type || '',
      }
    }
  },
  async updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
    this.loadMap()
  },
  watch: {
    '$route.params.id': function (id) {
      this.googlePlacesId = id
      this.getPlaceDetails()
    },
    company: function () {
      this.loadData()
    },
  },
  methods: {
    loadMap() {
      const mapElement = document.getElementById('side-map')
      if (!this.place || !mapElement || this.map) return

      const bounds = new google.maps.LatLngBounds()
      if (this.place.geometry.viewport) {
        bounds.union(this.place.geometry.viewport)
      } else {
        bounds.extend(this.place.geometry.location)
      }

      mapOptions.center = this.place.geometry.location
      this.map = new google.maps.Map(mapElement, mapOptions)
      this.map.fitBounds(bounds)
      const marker = new google.maps.Marker({
        // title: place.name,
        position: this.place.geometry.location,
        map: this.map,
        label: {
          fontFamily: 'Magicons',
          text: '\uE96F',
          color: '#ffff',
          // fontWeight: 400,
          // fontSize: 14
        },
      })
    },
    async loadData() {
      if (this.company) {
        this.mailgunAccount = await MailGunAccount.getByCompanyId(
          this.company.id
        )
        if (this.mailgunAccount) {
          console.log('Getting mail gun hosts')
          const reponse = await this.$http.get(
            '/mailgun/get_domains?company_id=' + this.company.id
          )

          if (reponse && reponse.data && reponse.data.items) {
            this.hosts = lodash.filter(reponse.data.items, { type: 'custom' })
          }
        }

        this.accountSnapshots.length = 0
        const snapshots = await CloneAccount.getByCompanyId(
          this.company.id
        ).then(snapshots => {
          const shared = snapshots
            .filter(s => s.type === 'imported')
            .sort((a, b) => a.name.localeCompare(b.name))
            .map(s => {
              return {
                id: s.id,
                name: `Imported - ${s.name}`,
                type: 'imported',
              }
            })
          const own = snapshots
            .filter(s => s.type === 'own')
            .sort((a, b) => a.name.localeCompare(b.name))
            .map(s => {
              return { id: s.id, name: `Own - ${s.name}`, type: 'own' }
            })
          this.accountSnapshots.push.apply(this.accountSnapshots, shared)
          this.accountSnapshots.push.apply(this.accountSnapshots, own)
        })
        const url = '/snapshot/templates'
        this.$http.get(url).then(response => {
          const defaults = response.data
            .sort((a, b) => a.name.localeCompare(b.name))
            .map(s => {
              return { id: s.id, name: `Default - ${s.name}`, type: 'default' }
            })
          this.accountSnapshots.push.apply(this.accountSnapshots, defaults)
        })
      }
    },
    getPlaceDetails() {
      var _self = this
      if (!_self.googlePlacesId) {
        const location = new Location()
        location.phone = ''
        _self.location = location
        return
      }

      service.getDetails(
        {
          placeId: _self.googlePlacesId,
        },
        function (place: { [key: string]: any }) {
          _self.place = place

          let streetNumber, streetName, city, state, country, postalCode
          lodash.each(place.address_components, function (component) {
            if (component.types.indexOf('street_number') > -1) {
              streetNumber = component.long_name
            }
            if (component.types.indexOf('route') > -1) {
              streetName = component.long_name
            }
            if (component.types.indexOf('locality') > -1) {
              city = component.long_name
            }
            if (component.types.indexOf('administrative_area_level_1') > -1) {
              state = component.short_name
            }
            if (component.types.indexOf('country') > -1) {
              country = component.short_name
            }
            if (component.types.indexOf('postal_code') > -1) {
              postalCode = component.long_name
            }
          })
          const location = new Location()
          location.googlePlacesId = _self.googlePlacesId
          location.name = place.name
          location.address =
            (streetNumber || '') +
            (streetNumber && streetName ? ' ' : '') +
            (streetName || '')
          if (city) location.city = city
          if (state) location.state = state
          if (country) location.country = country
          if (postalCode) location.postalCode = postalCode
          if (place.types) location.categories = place.types
          location.addressGeo = new firebase.firestore.GeoPoint(
            place.geometry.location.lat(),
            place.geometry.location.lng()
          )
          try {
            location.phone = phoneUtil.format(
              phoneUtil.parse(place.international_phone_number),
              libphonenumber.PhoneNumberFormat.E164
            )
          } catch (error) {
            console.error("Can't format phone:", error)
            location.phone = ''
          }
          location.website = place.website
          _self.location = location
        }
      )
    },
    async save_account() {
      if (!this.location) return

      if (this.loading) return

      await this.$validator.validateAll()
      if (this.errors.any()) {
        return Promise.resolve(true)
      }
      this.loading = true
      this.location.companyId = this.company.id
      this.location.timezone = this.company.timezone
      // this.location.scanReportId = "in_progress";
      this.location.status = 'account'
      //this.location.firstShotEnabled = this.first_shot_eliza //Removing it, as the flow is completely changed for enabling eliza
      // Snapshot Import handle depreciated features
      this.location.depreciatedFeatures = depreciatedFeatures
      if (this.selectedSnapshot && this.selectedSnapshot.type !== 'blank') {
        try {
          const snapshot = await CloneAccount.getById(this.selectedSnapshot.id)
          if (snapshot && (snapshot.data.asset_data_paths?.campaigns.length > 0 || snapshot.data.asset_data_paths?.triggers.length > 0)) {
            this.location.depreciatedFeatures = depreciatedFeatures.map(d => { 
                  const route = d
                  route.enable = true
                  return route
              }) as []
          }
        } catch (err) {
          console.log('Clone Account',err)
        }
      }
      await this.location.save()
      // Disabling Twilio Subaccount creation, if company is in twilioTrialMode
      let response
      if (this.company.inIsvMode || this.company.twilioTrialMode !== true) {
        try {
          response = await this.$http.post(
            '/twilio/set_up_phone_number/' + this.location.id
          )
        } catch (err) {
          console.error(err)
        }
      }

      try {
        response = await this.$http.post('/mailgun/setup/' + this.location.id)
      } catch (err) {
        console.error(err)
      }

      try {
        if (this.selectedSnapshot && this.selectedSnapshot.type !== 'blank') {
          const response = await this.$http.post(
            `/snapshot/${this.selectedSnapshot.id}/load_bg/${this.location.id}`,
            {
              snapshotType: this.selectedSnapshot.type,
            }
          )
        }
      } catch (err) {
        console.error(err)
      }
      this.$router.replace({
        name: 'account_detail',
        params: { account_id: this.location.id },
        query: {
          saas_setup: this.$route.query.l_type === 'saas' ? 'true' : null,
        },
      })
    },
  },
})
</script>

<style>
.error {
  color: #a94442;
}
.v-moon.v-moon1 {
  margin: auto;
}
.spinner {
  width: 88px;
}
.back {
  cursor: pointer;
  color: #188bf6;
}
</style>
