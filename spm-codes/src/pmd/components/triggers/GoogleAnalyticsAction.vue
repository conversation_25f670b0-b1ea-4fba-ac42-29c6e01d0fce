<template>
  <div>
    <div class="form-group">
      <UITextInputGroup
        type="text"
        v-model="trackingId"
        v-validate="'required'"
        name="trackingId"
        placeholder="Tracking Id"
        @input="setValue('tracking_id', trackingId)"
        :error="errors.has('trackingId')"
        :errorMsg="'Tracking Id is required.'"
      />
    </div>
    <div class="form-group">
      <UITextInputGroup
        type="text"
        v-model="eventCategory"
        v-validate="'required'"
        name="eventCategory"
        placeholder="Event Category"
        @input="setValue('event_category', eventCategory)"
        :error="errors.has('eventCategory')"
        :errorMsg="'Event Category is required.'"
      />
    </div>
    <div class="form-group">
      <UITextInputGroup
        type="text"
        v-model="eventAction"
        v-validate="'required'"
        name="eventAction"
        placeholder="Event Action"
        @input="setValue('event_action', eventAction)"
        :error="errors.has('eventAction')"
        :errorMsg="'Event Action is required.'"
      />
    </div>
    <div class="form-group">
      <UITextInputGroup
        type="text"
        v-model="eventLabel"
        name="eventLabel"
        placeholder="Event Label"
        @input="setValue('event_label', eventLabel)"
      />
    </div>
    <div class="form-group">
      <div class="form-input-dropdown dropdown">
        <div data-toggle="dropdown">
          <i class="icon icon-arrow-down-1"></i>
          <input
            type="text"
            class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
            placeholder="Event Value"
            v-model="fromattedEventValue"
            name="eventValue"
          />
        </div>
        <div class="dropdown-menu">
          <a
            class="dropdown-item trigger-type"
            href="javascript:void(0);"
            v-for="customField in customFieldsList"
            :key="customField.id"
            @click.prevent="fromattedEventValue = customField.fieldKey"
          >
            <p>
              {{ customField.fieldKey }}
            </p>
          </a>
        </div>
      </div>
    </div>

    <div class="form-group">
      <div class="form-input-dropdown dropdown">
        <div data-toggle="dropdown">
          <i class="icon icon-arrow-down-1"></i>
          <input
            type="text"
            class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
            placeholder="Hit Type"
            v-model="fomattedHitType"
            name="hitType"
            v-validate="'required'"
          />
        </div>
        <div class="dropdown-menu">
          <a
            class="dropdown-item trigger-type"
            href="javascript:void(0);"
            v-for="ht in [
              'pageview',
              'screenview',
              'event',
              'transaction',
              'item',
              'social',
              'exception',
              'timing',
            ]"
            :key="ht"
            @click.prevent="fomattedHitType = ht"
          >
            <p>
              {{ ht }}
            </p>
          </a>
        </div>
      </div>
    </div>
    <span v-show="errors.has('hitType')" class="--red"
      >Hit Type is required.</span
    >
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { CustomField, CustomValue } from '@/models'
import lodash from 'lodash'

export default Vue.extend({
  props: ['action'],
  data() {
    return {
      currentLocationId: '',
      trackingId: '',
      eventAction: '',
      eventCategory: '',
      eventValue: '',
      eventLabel: '',
      customFieldsList: [] as any[],
      hitType: '',
    }
  },
  computed: {
    fromattedEventValue: {
      get() {
        if (lodash.find(this.customFieldsList, { fieldKey: this.eventValue })) {
          return this.eventValue
        }
        return !isNaN(parseInt(this.eventValue))
          ? parseInt(this.eventValue)
          : ''
      },
      set(value) {
        this.eventValue = value
        if (lodash.find(this.customFieldsList, { fieldKey: value })) {
          this.setValue('event_value', this.eventValue)
        } else if (!isNaN(parseInt(value))) {
          this.eventValue = parseInt(value)
          this.setValue('event_value', parseInt(value))
        } else {
          this.eventValue = ''
        }
      },
    },
    fomattedHitType: {
      get() {
        return this.hitType
      },
      set(value: string) {
        this.hitType = value
        this.setValue('hit_type', value)
      },
    },
  },
  methods: {
    setValue(field: string, value: string | number) {
      Vue.set(this.action, field, value)
      this.$emit('update:action', this.action)
    },
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id

    const customFields = await CustomField.getByLocationIdAndType(
      this.currentLocationId,
      'contact'
    )
    this.customFieldsList = customFields.filter(c => {
      if (c.dataType === 'NUMERICAL') {
        c.fieldKey = '{{' + c.fieldKey + '}}'
        return c
      }
    })

    const customValues = await CustomValue.getByLocationId(
      this.currentLocationId
    )
    this.customFieldsList.push(
      ...customValues.filter(c => {
        if (!isNaN(c.value)) {
          c.fieldKey = '{{custom_values.' + c.fieldKey + '}}'
          return c
        }
      })
    )

    if (this.action.tracking_id) this.trackingId = this.action.tracking_id
    if (this.action.event_action) this.eventAction = this.action.event_action
    if (this.action.event_category)
      this.eventCategory = this.action.event_category
    if (this.action.event_label) this.eventLabel = this.action.event_label
    if (this.action.event_value) this.eventValue = this.action.event_value
    this.hitType = this.action.hit_type ? this.action.hit_type : 'event'
  },
})
</script>
