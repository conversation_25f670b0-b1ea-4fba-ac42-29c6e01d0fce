<template>
  <div>
    <section class="hl_wrapper">
      <section class="hl_wrapper--inner customers" id="customers">
        <div class="container-fluid mb-5">
          <div class="hl_controls">
            <div class="hl_controls--left flex">
              <h3>
                Contacts
                <span v-if="total">{{ total }} total</span>
                <span>
                  <a href="#" @click="selectAll">{{
                    isContactAvailable ? 'Unselect All' : 'Select All'
                  }}</a>
                </span>
              </h3>
              <div class="dropdown">
                <button
                  class="btn btn-light dropdown-toggle"
                  type="button"
                  data-toggle="dropdown"
                  aria-haspopup="true"
                  aria-expanded="false"
                >
                  Actions
                </button>
                <div class="dropdown-menu">
                  <!-- <a
										href="#"
										class="dropdown-item"
										data-toggle="modal"
										data-target="#add-message-modal"
										@click="showComposeModal = true"
                  >Compose Message</a>-->
                  <a
                    href="#"
                    class="dropdown-item"
                    data-toggle="modal"
                    data-target="#add-tags-modal"
                    @click=";(showTagsModal = true), (metaData.type = 'add')"
                    >Add Tags</a
                  >
                  <a
                    href="#"
                    class="dropdown-item"
                    data-toggle="modal"
                    data-target="#add-tags-modal"
                    @click=";(showTagsModal = true), (metaData.type = 'remove')"
                    >Remove Tag</a
                  >
                  <!--  <a href="#" class="dropdown-item">Edit</a>
                  <span class="dropdown-divider"></span>-->
                  <span v-if="isAdmin" class="dropdown-divider"></span>
                  <a
                    v-if="isAdmin"
                    href="#"
                    class="dropdown-item"
                    data-toggle="modal"
                    data-target="#add-tags-modal"
                    @click=";(showTagsModal = true), (metaData.type = 'delete')"
                    >Delete</a
                  >
                </div>
              </div>
            </div>
            <div class="hl_controls--right">
              <!-- <select class="selectpicker" data-width="fit">
								<option>All Customers</option>
								<option>Option 2</option>
								<option>Option 3</option>
              </select>-->
              <select
                class="selectpicker"
                title="All"
                data-width="fit"
                name="filterType"
                v-model="filters.type"
              >
                <option value>All</option>
                <option value="customer">Customer</option>
                <option value="lead">Lead</option>
              </select>
              <div v-if="existingTags.length">
                <select
                  class="selectpicker tag-picker"
                  title="Select Tag"
                  data-width="fit"
                  name="existingTags"
                  data-size="10"
                  v-model="filters.tag"
                >
                  <option value>Select Tag</option>
                  <option v-for="tag in existingTags" :value="tag">{{
                    tag
                  }}</option>
                </select>
              </div>
              <div class="search-form">
                <i class="icon icon-loupe"></i>
                <input
                  type="text"
                  class="form-control form-light"
                  :placeholder="'Search Contact'"
                  v-model="filters.text"
                />
                <div style="position: absolute;top: 33%;right: 10px;">
                  <moon-loader
                    :loading="fetching"
                    color="#1ca7ff"
                    size="15px"
                  />
                </div>
              </div>
              <div class="dropdown">
                <button
                  class="btn btn-success dropdown-toggle"
                  type="button"
                  data-toggle="dropdown"
                  aria-haspopup="true"
                  aria-expanded="false"
                >
                  <i class="icon icon-plus"></i>
                  Add Contact
                </button>
                <div class="dropdown-menu">
                  <a
                    class="dropdown-item"
                    @click.prevent="addCustomer"
                    v-if="type === 'customers'"
                    >Add Single Contact</a
                  >
                  <a class="dropdown-item" @click.prevent="addCustomer" v-else
                    >Add Single Lead</a
                  >
                  <a
                    class="dropdown-item"
                    v-if="isAdmin"
                    @click.prevent="showImportModalClicked"
                    >Import Contacts</a
                  >
                </div>
              </div>
              <div
                style="display: inline-block;position: relative;"
                v-if="isAdmin"
              >
                <button
                  :class="{ invisible: downloadLoading }"
                  type="button"
                  class="btn btn-sm btn-primary"
                  @click.prevent="exportAllContacts"
                >
                  <i class="icon icon-download"></i>
                </button>
                <div
                  style="position: absolute;left: 50%;transform: translate(-50%, -50%);top: 50%;"
                  v-show="downloadLoading"
                >
                  <moon-loader
                    :loading="downloadLoading"
                    color="#1ca7ff"
                    size="15px"
                  />
                </div>
              </div>
            </div>
          </div>
          <nav aria-label="Page navigation example">
            <ul class="pagination hl_controls justify-content-end">
              <li class="page-item">
                <vue-ctk-date-time-picker
                  :locale="getCountryInfo('locale')"
                  style="width: 230px;"
                  v-if="filters.orderBy !== 'name'"
                  v-model="filtersDates"
                  :range="true"
                  color="#188bf6"
                  :only-date="true"
                  enable-button-validate
                  :noClearButton="true"
                  name="start_time"
                  :formatted="getCountryDateFormat(false)"
                  @validate="updateFilters"
                />
              </li>
              <li class="page-item hl_selectpicker2_controls">
                <select
                  class="selectpicker"
                  title="Records Per Page"
                  data-width="fit"
                  name="recordsPage"
                  v-model="filters.limit"
                >
                  <option value="25">25</option>
                  <option value="50">50</option>
                  <option value="100">100</option>
                </select>
              </li>
              <li class="page-item hl_selectpicker2_controls">
                <select
                  class="selectpicker"
                  title="Order By"
                  data-width="fit"
                  name="orderBy"
                  v-model="filters.orderBy"
                >
                  <option value="name">
                    <i class="fas fa-sort-alpha-down hint-icon"></i> Name (ASC)
                  </option>
                  <option value="date_desc">
                    <i class="fas fa-sort-amount-down hint-icon"></i> Date Added
                    (DESC)
                  </option>
                  <option value="date_asc">
                    <i class="fas fa-sort-amount-up hint-icon"></i> Date Added
                    (ASC)
                  </option>
                </select>
              </li>
              <li
                class="page-item mr-0"
                :class="{ disabled: filters.page == 1 || fetching }"
              >
                <a
                  class="page-link"
                  href="javascript:void(0);"
                  tabindex="-1"
                  @click.prevent="filters.page--"
                  >Previous</a
                >
              </li>
              <li class="page-item" :class="{ disabled: !hasNext || fetching }">
                <a
                  class="page-link"
                  href="javascript:void(0);"
                  @click.prevent="filters.page++"
                  >Next</a
                >
              </li>
            </ul>
          </nav>
          <div
            v-if="
              selectedContactCheckBox.length >= limit &&
                (selectAllContacts || isContactAvailable)
            "
            class="alert-box-contact"
          >
            <span style="font-size:15px">
              All {{ selectAllContacts ? total : limit }} contacts on this page
              are selected.
              <a style="color:#188bf6" @click="queryToGetAllContact">{{
                selectorMessage
              }}</a>
            </span>
          </div>
          <div class="card hl_customers--table">
            <div class="card-body --no-padding">
              <div class="table-wrap">
                <table class="table table-sort">
                  <thead>
                    <tr>
                      <th data-title></th>
                      <th data-sort="string">Contacts</th>
                      <th data-sort="string">Email</th>
                      <th data-sort="string">Type</th>
                      <th data-sort="string">Phone</th>
                      <th data-sort="string">Date Added</th>
                      <!-- <th data-sort="string">Last Contact
												<i class="icon icon-arrow-down-1"></i>
                      </th>-->
                      <th>Tags</th>
                      <th>
                        <!--Actions-->
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="contact in contacts" v-bind:key="contact.id">
                      <td>
                        <div class="option">
                          <input
                            type="checkbox"
                            :id="contact.id"
                            :value="contact.id"
                            v-model="selectedContactCheckBox"
                          />
                          <label :for="contact.id"></label>
                        </div>
                      </td>
                      <td
                        class="pointer"
                        @click.prevent="loadDetailPage(contact.id)"
                      >
                        <Avatar
                          :contact="contact"
                          :include_name="true"
                          :path="
                            `/location/${currentLocationId}/customers/detail/${contact.id}`
                          "
                        />
                      </td>
                      <td
                        class="pointer"
                        @click.prevent="loadDetailPage(contact.id)"
                      >
                        <a
                          style="color: #000000db"
                          :href="
                            `/location/${currentLocationId}/customers/detail/${contact.id}`
                          "
                        >
                          {{ contact.email }}
                        </a>
                      </td>
                      <td class="pointer">
                        <a
                          style="color: #000000db"
                          :href="
                            `/location/${currentLocationId}/customers/detail/${contact.id}`
                          "
                        >
                          <div class="table_tag">
                            {{ contact.type | toTitleCase }}
                          </div>
                        </a>
                      </td>
                      <td
                        class="pointer"
                        @click.prevent="loadDetailPage(contact.id)"
                      >
                        <a
                          style="color: #000000db"
                          :href="
                            `/location/${currentLocationId}/customers/detail/${contact.id}`
                          "
                        >
                          <PhoneNumber
                            type="display"
                            v-model="contact.phone"
                            :currentLocationId="currentLocationId"
                          />
                        </a>
                      </td>
                      <td
                        class="pointer"
                        @click.prevent="loadDetailPage(contact.id)"
                      >
                        <a
                          style="color: #000000db"
                          :href="
                            `/location/${currentLocationId}/customers/detail/${contact.id}`
                          "
                        >
                          <div v-if="contact.dateAdded">
                            {{
                              contact.dateAdded
                                .tz(timezone)
                                .format(getCountryDateFormat('extended-normal'))
                            }}
                          </div>
                        </a>
                      </td>
                      <td
                        class="pointer"
                        @click.prevent="loadDetailPage(contact.id)"
                      >
                        <a
                          style="color: #000000db"
                          :href="
                            `/location/${currentLocationId}/customers/detail/${contact.id}`
                          "
                        >
                          <!-- <div class="table_tag" v-for="tag in contact.tags">{{tag}}</div> -->
                          <template v-for="(tag, index) in contact.tags">
                            <!-- <div class="table_tag">{{tag}}</div> -->
                            <div
                              class="table_tag"
                              v-if="index <= 2"
                              :key="index"
                            >
                              {{ tag }}
                            </div>
                            <!-- <div class="table_tag-more tag-more" v-if="contact.tags.length === (index - 1)">test</div> -->
                            <div
                              class="table_tag-more"
                              v-if="
                                contact.tags.length > 3 &&
                                  index === contact.tags.length - 1
                              "
                              :key="index"
                            >
                              +{{ contact.tags.length - 3 }}
                            </div>
                          </template>
                        </a>
                      </td>
                      <td>
                        <button
                          v-if="type === 'customers'"
                          type="button"
                          v-show="
                            user && user.permissions.reviews_enabled !== false
                          "
                          class="btn btn-green-lt btn-circle"
                          @click.stop="setSelectedContact(contact)"
                        >
                          <i class="icon icon-send2"></i>
                          <span class="sr-only">View</span>
                        </button>
                        <select
                          class="selectpicker more-select"
                          @change="customerOptionSelected($event, contact)"
                        >
                          <option value>Choose action..</option>
                          <option
                            value="appointment"
                            v-if="user && user.permissions.appointments_enabled"
                            >Schedule appointment</option
                          >
                          <option
                            value="create_opportunity"
                            v-if="
                              user && user.permissions.opportunities_enabled
                            "
                            >Create opportunity</option
                          >
                          <option
                            value="delete"
                            v-if="isAdmin && type === 'customers'"
                            >Delete Customer</option
                          >
                          <option value="delete" v-else-if="isAdmin"
                            >Delete Lead</option
                          >
                        </select>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <nav aria-label="Page navigation example">
            <ul class="pagination justify-content-end">
              <li
                class="page-item"
                :class="{ disabled: filters.page == 1 || fetching }"
              >
                <a
                  class="page-link"
                  href="javascript:void(0);"
                  tabindex="-1"
                  @click.prevent="filters.page--"
                  >Previous</a
                >
              </li>
              <li class="page-item" :class="{ disabled: !hasNext || fetching }">
                <a
                  class="page-link"
                  href="javascript:void(0);"
                  @click.prevent="filters.page++"
                  >Next</a
                >
              </li>
            </ul>
          </nav>
        </div>
      </section>
      <!-- END of .customers -->
    </section>
    <!-- END of .hl_wrapper -->
    <EditAppointmentModal
      @hidden="appointmentData = {}"
      :values="appointmentData"
    />
    <SendReviewModal
      @hidden="
        showSendReviewModal = false
        selectedContact = undefined
      "
      :contact="selectedContact"
      :showModal.sync="showSendReviewModal"
    />
    <EditCustomerModal
      :contact="selectedContact"
      :showModal.sync="showEditCustomerModal"
      @hidden="hideEditWindow"
      :isAdmin="isAdmin"
      :user="user"
    />
    <ImportCustomerModal
      :visible="showImportModal"
      @hidden="hideImportWindow"
      :currentLocationId="currentLocationId"
      :type="type"
      :isAdmin="isAdmin"
      :user="user"
    />
    <OpportunitiesModal
      :values="opportunityModalValues"
      @hidden="hideOppModal"
      :users="users"
    />
    <TagsModal
      :currentPageFilters="currentPageFilters"
      :showModal="showTagsModal"
      :selectedCustomersId="selectedContactCheckBox"
      :selectAllContacts="selectAllContacts"
      :totalContactsCount="total"
      @closeModal="closeModal"
      :meta="metaData"
      :isAdmin="isAdmin"
    ></TagsModal>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import {
  Contact,
  User,
  Tag,
  Opportunity,
  Pipeline,
  Location,
  ManualQueue,
  getCountryDateFormat,
  getCountryInfo
} from '../../models'
import config from '../../config'
import download from 'downloadjs'
import * as json2csv from 'json2csv'
import { UserState } from '../../store/state_models'
import { mapState } from 'vuex'
import moment from 'moment-timezone'
import Datepicker from 'vuejs-datepicker'
import firebase from 'firebase/app'

const Avatar = () => import('../components/Avatar.vue')
const SendReviewModal = () => import('../components/SendReviewModal.vue')
const EditCustomerModal = () => import('../components/EditCustomerModal.vue')
const ImportCustomerModal = () =>
  import('../components/customer/ImportCustomerModal.vue')
const EditAppointmentModal = () =>
  import('../components/EditAppointmentModal.vue')
const OpportunitiesModal = () =>
  import('../components/opportunities/OpportunitiesModal.vue')
const PhoneNumber = () => import('../components/util/PhoneNumber.vue')
const MoonLoader = () => import('../components/MoonLoader.vue')
const TagsModal = () => import('../components/customer/TagsModalComponent.vue')

const store = require('store')

declare var $: any

let realtimeUnsubscribe: () => void

export default Vue.extend({
  props: ['type'],
  components: {
    Avatar,
    SendReviewModal,
    EditCustomerModal,
    MoonLoader,
    EditAppointmentModal,
    ImportCustomerModal,
    PhoneNumber,
    OpportunitiesModal,
    Datepicker,
    TagsModal
  },
  data() {
    return {
      getCountryInfo,
      currentLocationId: '',
      showSendReviewModal: false,
      showEditCustomerModal: false,
      selectedContact: undefined as Contact | undefined,
      contacts: [] as Contact[],
      searchDebouncer: undefined as NodeJS.Timer | undefined,
      showImportModal: false,
      appointmentData: {
        visible: false,
        contactId: ''
      },
      filtersDates: {
        start: moment()
          .subtract(6, 'months')
          .toDate(),
        end: moment()
          .add(1, 'days')
          .toDate()
      },
      filters: {
        startDate: '',
        endDate: '',
        orderBy: '',
        tag: '',
        type: '',
        text: '',
        page: 1,
        limit: 25
      },
      currentPage: 1,
      firstSnapshot: true,
      fetching: false,
      restoring: false,
      existingTags: [] as string[],
      downloadLoading: false,
      opportunityModalValues: {
        visible: false,
        opportunity: undefined as Opportunity | undefined,
        currentLocationId: '',
        tab: '',
        pipeline: undefined as Pipeline | undefined,
        contact: undefined as Contact | undefined
      },
      users: [] as User[],
      location: undefined as Location | undefined,
      timezone: undefined as string | undefined,
      total: undefined,
      seqCalling: 1,
      selectedContactCheckBox: [],
      isContactAvailable: false,
      showTagsModal: false,
      metaData: {
        type: ''
      },
      initialLoad: true,
      selectorMessage: '',
      selectAllContacts: false,
      getCountryDateFormat: getCountryDateFormat
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.location = new Location(
      await this.$store.dispatch('locations/getById', this.currentLocationId)
    )
    this.timezone = await this.location.getTimeZone()
    this.restoring = true

    await this.loadTags()
    const filters = store.get(`contact-filters-${this.currentLocationId}`)
    if (filters) {
      this.filters = filters
      this.filtersDates = {
        start: filters.startDate
          ? new Date(filters.startDate)
          : moment()
              .subtract(6, 'months')
              .toDate(),
        end: filters.startDate
          ? new Date(filters.endDate)
          : moment()
              .add(1, 'days')
              .toDate()
      }
    } else {
      this.filters.orderBy = 'name'
    }
    this.restoring = false
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }
  },
  updated() {
    const _self = this
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  computed: {
    currentPageFilters(): { [key: string]: any } {
      const query: { [key: string]: any } = {
        location_id: this.currentLocationId,
        assigned_to: this.assignedTo || '',
        q: this.filters.text || '',
        type: this.filters.type || '',
        tag: this.filters.tag || '',
        orderBy: this.filters.orderBy,
        page: this.filters.page,
        limit: this.filters.limit,
        country: this.location && this.location.country
      }
      if (this.filters.startDate && this.filters.orderBy !== 'name') {
        query.startDate = this.filters.startDate
        query.endDate = this.filters.endDate
      }
      return query
    },
    isAdmin(): boolean {
      return this.user && this.user.role === 'admin'
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    canAccessAll(): boolean {
      return this.user && this.user.permissions.assigned_data_only !== true
    },
    assignedTo(): string {
      return !this.isAdmin && !this.canAccessAll ? this.user.id : undefined
    },
    limit(): number {
      return parseInt(this.filters.limit ? this.filters.limit : 25)
    },
    hasNext(): boolean {
      return this.total > this.limit * this.filters.page
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  watch: {
    '$route.params.location_id': async function(id) {
      this.currentLocationId = id
      this.location = new Location(
        await this.$store.dispatch('locations/getById', id)
      )
      this.timezone = await this.location.getTimeZone()
      const filters = store.get(`contact-filters-${this.currentLocationId}`)
      if (filters) {
        this.filters = filters
        this.filtersDates = {
          start: new Date(filters.startDate),
          end: new Date(filters.endDate)
        }
      } else {
        this.filters.startDate = ''
        this.filters.endDate = ''
        this.filters.text = ''
        this.filters.type = ''
        this.filters.tag = ''
        this.filters.page = 1
        this.filters.limit = 25
        this.filters.orderBy = 'name'
      }
      if (this.searchDebouncer) clearTimeout(this.searchDebouncer)
      this.searchDebouncer = undefined
      this.firstSnapshot = true
      this.restoring = true
      this.fetching = false
      this.contacts = []
      this.existingTags = []
      this.restoring = false
      this.loadAfterCheckingState()
      this.loadTags()
    },
    filters: {
      handler(val, oldVal) {
        // if (this.searchDebouncer) clearTimeout(this.searchDebouncer)
        if (this.restoring) return
        if (val.page !== 1 && val.page == this.currentPage) {
          this.filters.page = 1
          return
        }
        this.load()
        // this.searchDebouncer = setTimeout(() => {
        //   this.load('reset')
        // }, 300)

        store.set(`contact-filters-${this.currentLocationId}`, this.filters)
      },
      deep: true
    },
    selectedContactCheckBox() {
      if (this.selectedContactCheckBox.length > 0) {
        this.isContactAvailable = true
        return
      }
      this.isContactAvailable = false
      this.selectAllContacts = false
      return
    },
    isContactAvailable() {
      this.selectAllContacts = false
    }
  },
  beforeDestroy() {
    if (realtimeUnsubscribe) realtimeUnsubscribe()
  },
  methods: {
    async loadTags() {
      let tags = await Tag.getByLocationId(this.currentLocationId)
      if (tags.length) this.existingTags = tags.map(tag => tag.name)
    },
    updateFilters() {
      if (!this.filtersDates.end) {
        this.filtersDates.end = moment(this.filtersDates.start).add(1, 'day')
      }
      this.filters.startDate = moment(this.filtersDates.start, [
        'YYYY-MM-DD h:m a'
      ])
        .toDate()
        .getTime()
      this.filters.endDate = moment(this.filtersDates.end, ['YYYY-MM-DD h:m a'])
        .toDate()
        .getTime()

      this.load()
    },
    getFetchEndpoint() {
      return (
        `${config.baseUrl}/search/contact/all?` +
        Object.keys(this.currentPageFilters)
          .map(
            key => key + '=' + encodeURIComponent(this.currentPageFilters[key])
          )
          .join('&')
      )
    },
    async queryToGetAllContact() {
      if (this.selectorMessage === 'Clear selection') {
        this.selectorMessage = `Select all ${this.total} contacts in location`
        this.selectedContactCheckBox = []
        return
      }
      this.selectAllContacts = !this.selectAllContacts
      if (this.selectAllContacts) {
        this.selectorMessage = 'Clear selection'
        this.selectedContactCheckBox = this.contacts.map(c => c.id)
      }
    },
    closeModal(cleanSelections: boolean) {
      if (!cleanSelections) {
        this.showTagsModal = false
        return
      }
      this.showTagsModal = false
      this.selectedContactCheckBox = []
      this.loadTags()
      this.load()
    },
    selectAll() {
      if (!this.contacts) return
      if (!this.selectedContactCheckBox.length > 0) {
        this.selectedContactCheckBox = this.contacts.map(el => el.id)
        return
      }
      this.selectedContactCheckBox = []
      this.selectorMessage = `Select all ${this.total} contacts in location`
    },
    loadAfterCheckingState() {
      const savedState = this.$store.getters['contactsPage/getByLocationId'](
        this.currentLocationId
      )
      if (savedState && savedState.filters) {
        this.restoring = true
        this.filters = JSON.parse(JSON.stringify(savedState.filters))
        this.restoring = false
      }

      this.load('current')
    },
    loadDetailPage(id: string) {
      this.$router.push({ name: this.getSideBarVersion == 'v2' ? 'contact_detail-v2': 'contact_detail', params: { contact_id: id } })
    },
    async fetchData() {
      this.users = this.$store.state.users.users.map(
        user => new User(Object.assign({}, user))
      )

      if (realtimeUnsubscribe) realtimeUnsubscribe()
      realtimeUnsubscribe = Contact.fetchAllContactsByUpdated(
        this.currentLocationId
      )
        .limit(5)
        .onSnapshot(snapshot => {
          for (let i = snapshot.docChanges().length - 1; i >= 0; i--) {
            if (this.firstSnapshot) continue

            const docChange = snapshot.docChanges()[i]
            if (docChange.type === 'removed') continue

            const newContact = new Contact(docChange.doc)
            let oldContact = <Contact | undefined>lodash.find(this.contacts, {
              id: newContact.id
            })

            if (this.assignedTo && newContact.assignedTo !== this.assignedTo) {
              if (oldContact) {
                this.contacts.splice(this.contacts.indexOf(oldContact), 1)
              }
              continue
            }
            if (oldContact)
              this.contacts.splice(this.contacts.indexOf(oldContact), 1)
            if (newContact.deleted) continue
            const sortedIndex = this.sortedIndex(newContact)
            if (
              (this.filters.page === 1 && sortedIndex === 0) ||
              (!this.hasNext && sortedIndex === this.contacts.length) ||
              (sortedIndex > 0 && sortedIndex < this.contacts.length)
            ) {
              this.contacts.splice(sortedIndex, 0, newContact)
            }
          }
          this.firstSnapshot = false
          this.$store.commit('contactsPage/addUpdate', {
            filters: JSON.parse(JSON.stringify(this.filters)),
            locationId: this.currentLocationId
          })
        })
    },
    sortedIndex(element: Contact): number {
      return lodash.sortedIndexBy(this.contacts, element, (value: Contact) => {
        return this.filters.orderBy === 'name'
          ? value.fullName
          : this.filters.orderBy === 'date_desc'
          ? -value.dateAdded.valueOf()
          : value.dateAdded.valueOf()
      })
    },
    showImportModalClicked() {
      if (realtimeUnsubscribe) realtimeUnsubscribe()
      this.showImportModal = true
    },
    hideImportWindow(response: boolean) {
      this.showImportModal = false
      this.firstSnapshot = true
      this.load('current')
    },
    hideEditWindow(response: boolean) {
      this.showEditCustomerModal = false
      this.selectedContact = undefined
    },
    load(fetchType?: 'previous' | 'next' | 'current' | 'reset') {
      this.seqCalling++
      const seqCall = this.seqCalling
      this.selectedContactCheckBox = []
      this.contacts = []
      const term = this.filters.text
      this.restoring = true
      new Promise<Contact[]>(async (resolve, reject) => {
        this.fetching = true
        this.restoring = false

        try {
          const response = await this.$http.get(this.getFetchEndpoint())
          if (response.status !== 200) return reject()
          if (seqCall !== this.seqCalling) return
          this.currentPage = this.filters.page

          this.total = lodash.get(response, 'data.hits.total.value')
          let data: { [key: string]: any }[] = response.data.hits.hits
          if (this.initialLoad) {
            this.initialLoad = false
          }
          this.selectorMessage = `Select all ${this.total} contacts in location`

          return resolve(
            data.map(elasticContact => {
              const data = {
                id: elasticContact._id,
                ...elasticContact._source
              }
              return new Contact(data)
            })
          )
        } catch (err) {
          return reject(err)
        }
      })
        .then((newContacts: Contact[]) => {
          this.searchLoading = false
          this.fetching = false
          if (this.filters.text !== term) return
          this.contacts = newContacts
          if (this.firstSnapshot) this.fetchData()
          this.$store.commit('contactsPage/addUpdate', {
            filters: JSON.parse(JSON.stringify(this.filters)),
            locationId: this.currentLocationId
          })
        })
        .catch(err => {
          this.fetching = false
        })
    },
    setSelectedContact(contact: Contact) {
      this.showSendReviewModal = true
      this.selectedContact = contact
    },
    addCustomer() {
      const contact = new Contact()
      contact.locationId = this.currentLocationId
      // contact.type = this.type === 'customers' ? 'customer' : 'lead';
      this.selectedContact = contact
      this.showEditCustomerModal = true
    },
    async customerOptionSelected(event: any, optionSelectedContact: Contact) {
      const option = event.target.value
      event.target.value = ''
      if (option == 'delete') {
        if (!this.isAdmin) {
          alert('Only admins can delete contacts.')
          return
        }
        if (confirm('Are you sure you want to delete this contact?')) {
          let index = lodash.findIndex(
            this.contacts,
            obj => obj.id === optionSelectedContact.id
          )
          if (index !== -1) {
            this.contacts.splice(index, 1)
          }
          try {
            await this.$http.delete(
              `/contact/${optionSelectedContact.id}/campaign`
            )
          } catch (err) {
            console.log(err)
          }
          await optionSelectedContact.delete()
          await ManualQueue.removeActionsToContact(
            optionSelectedContact.id,
            this.currentLocationId
          )
        }
      } else if (option === 'appointment') {
        this.appointmentData = {
          visible: true,
          contactId: optionSelectedContact.id
        }
      } else if (option === 'create_opportunity') {
        this.opportunityModalValues = {
          opportunity: undefined,
          visible: true,
          currentLocationId: this.currentLocationId,
          tab: 'OpportunityComponent',
          pipeline: undefined,
          contact: await this.$store.dispatch(
            'contacts/syncGet',
            optionSelectedContact.id
          )
        }
      }
    },
    setTag(event: any) {
      // this.searchText = event.target.value;
    },
    async exportAllContacts() {
      if (config.googleAnalyticId && ga) {
        ga('send', 'event', 'DownloadContactStarted', this.currentLocationId)
      }
      this.downloadLoading = true

      const fields = [
        'first name',
        'last name',
        'phone',
        'email',
        'tags',
        'type',
        'source',
        'date added',
        'date updated',
        'dnd',
        'address',
        'city',
        'state',
        'country',
        'postal code',
        'company name',
        'assignee'
      ]
      let values = [] as any[]
      // let page = 1;
      // const pageLimit = 10000

      const limit = 1000
      let snapshot
      let count = 0
      let lastObject: firebase.firestore.DocumentSnapshot | undefined
      do {
        let query = Contact.collectionRef()
          .where('deleted', '==', false)
          .where('location_id', '==', this.currentLocationId)
        if (lastObject) {
          query = query.startAfter(lastObject)
        }
        snapshot = await query.limit(limit).get()
        if (snapshot.size > 0) {
          lastObject = snapshot.docs[snapshot.size - 1]
          await Promise.all(
            snapshot.docs.map(
              async (snap: firebase.firestore.DocumentSnapshot) => {
                const contact = new Contact(snap)
                let row: { [key: string]: any } = {}
                row['first name'] = contact.firstName
                row['last name'] = contact.lastName
                row['phone'] = contact.phone
                row['email'] = contact.email
                row['tags'] = contact.tags.join(', ')
                row['type'] = contact.type
                row['source'] = contact.source
                row['date added'] = contact.dateAdded
                row['date updated'] = contact.dateUpdated
                row['dnd'] = contact.dnd
                row['address'] = contact.address1
                row['city'] = contact.city
                row['state'] = contact.state
                row['country'] = contact.country
                row['postal code'] = contact.postalCode
                row['company name'] = contact.companyName
                let assignee = ''
                if (contact.assignedTo) {
                  let user = lodash.find(this.users, { id: contact.assignedTo })
                  if (user) assignee = user.name
                }
                row['assignee'] = assignee
                values.push(row)
              }
            )
          )
        }
        count += snapshot.size
      } while (snapshot.size === limit)

      // try {
      //    do {
      //      const response = await this.$http.get(`${config.baseUrl}/search/contact/all?location_id=${this.currentLocationId}&limit=${pageLimit}&page=${page}`);

      //       if (response.data.hits.hits && response.data.hits.hits.length > 0) {
      //         response.data.hits.hits.forEach(contactES => {
      //           const contact = new Contact({
      //             id: contactES._id,
      //             ...contactES._source
      //           })
      //           let row: { [key: string]: any } = {}
      //           row['first name'] = contact.firstName
      //           row['last name'] = contact.lastName
      //           row['phone'] = contact.phone
      //           row['email'] = contact.email
      //           row['tags'] = contact.tags.join(', ')
      //           row['type'] = contact.type
      //           row['source'] = contact.source
      //           row['date added'] = contact.dateAdded
      //           row['date updated'] = contact.dateUpdated
      //           row['dnd'] = contact.dnd
      //           row['address'] = contact.address1
      //           row['city'] = contact.city
      //           row['state'] = contact.state
      //           row['country'] = contact.country
      //           row['postal code'] = contact.postalCode
      //           row['company name'] = contact.companyName
      //           let assignee = '';
      //           if (contact.assignedTo) {
      //             let user = lodash.find(this.users, { id: contact.assignedTo });
      //             if (user) assignee = user.name;
      //           }
      //           row['assignee'] = assignee;
      //           values.push(row)
      //         });

      //         if (response.data.hits.hits.length === pageLimit) {
      //           page++;
      //         } else {
      //           page = undefined;
      //         }
      //       } else {
      //         page = undefined
      //       }
      //    } while (page)
      // } catch(err) {
      //   console.error(err);
      // }

      const json2csvParser = new json2csv.Parser({ fields })
      const csv = json2csvParser.parse(values)
      if (config.googleAnalyticId && ga) {
        ga(
          'send',
          'event',
          'DownloadContact',
          this.currentLocationId,
          values.length
        )
      }

      download(csv, 'all_contacts.csv', 'text/csv')
      this.downloadLoading = false
    },
    async hideOppModal(opportunity: Opportunity) {
      let _self = this
      this.opportunityModalValues = { visible: false }
      await Vue.nextTick()
      if (opportunity) {
        _self.$router.push({
          name: 'opportunities',
          params: { location_id: _self.currentLocationId }
        })
      }
    }
  }
})
</script>
<style scoped>
.alert-box-contact {
  background: #cfd8dc;
  padding: 6px;
  border-radius: 7px;
  text-align: center;
}
</style>
