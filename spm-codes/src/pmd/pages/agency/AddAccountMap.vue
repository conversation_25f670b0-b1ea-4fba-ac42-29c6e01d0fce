<template>
  <div>
    <SideBarV2 v-if="getSideBarVersion === 'v2'"/>
    <SideBar v-else />
    <TopBar />

    <section class="hl_wrapper">
      <section
        class="hl_wrapper--inner hl_agency hl_agency-location--map"
        id="agency_location"
      >
        <div class="the-location-map" id="location-map"></div>
        <div class="hl_location--search">
          <i class="icon icon-loupe"></i>
          <a
            href="javascript:void(0);"
            @click.prevent="addManualLocation()"
            class="add-manually"
            >Add account manually</a
          >
          <input
            type="text"
            class="form-control"
            id="map-search"
            placeholder="Search"
            ref="autocomplete"
          />
          <!-- <div class="dropdown">
                        <ul class="dropdown-menu">
                            <li class="dropdown-item">Dr. <PERSON>, 9th Street, Brooklyn, New York, USA</li>
                            <li class="dropdown-item">Crystal Clear Dental, 24 Horacio Trail, New York, USA</li>
                            <li class="dropdown-item">Serenity Dental Clinic, 1888 Skinner Hollow Road, New York, USA</li>
                            <li class="dropdown-item">Meta Clinic, 4226 Stratford Park, New York, USA</li>
                        </ul>
					</div>-->
        </div>
        <div v-show="places.length > 0" class="hl_location--search-results">
          <div class="search-results-header">
            <h4>
              Results
              <span>({{ places.length }})</span>
            </h4>
            <!-- <a href="#" class="hide">Hide results</a> -->
          </div>
          <div class="search-results">
            <a
              v-for="(place, index) in places"
              class="search-result-item"
              @click="chooseLocation(place)"
            >
              <i
                :class="getListingClass(index)"
                class="icon icon-pin-filled"
              ></i>
              <div class="text">
                <h5>{{ place.name }}</h5>
                <p>{{ place.formatted_address }}</p>
              </div>
              <i class="icon icon-arrow-right-1"></i>
            </a>
          </div>
        </div>
      </section>
      <!-- END of .hl_agency-location--map -->
    </section>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import TopBar from '../../components/TopBar.vue'
import SideBar from '../../components/agency/SideBar.vue'
import SideBarV2 from '../../components/sidebar/SideBar.vue'
import { User } from '@/models'

declare const google: any
const mapOptions = {
  zoom: 12,
  disableDefaultUI: true,
  center: new google.maps.LatLng(40.67, -73.94), // New York
  styles: [
    {
      featureType: 'landscape.man_made',
      elementType: 'geometry.fill',
      stylers: [
        {
          color: '#f4f4f4',
        },
      ],
    },
    {
      featureType: 'landscape.man_made',
      elementType: 'labels.text',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'landscape.natural',
      elementType: 'geometry.fill',
      stylers: [
        {
          visibility: 'on',
        },
        {
          color: '#e4e4e4',
        },
      ],
    },
    {
      featureType: 'landscape.natural.landcover',
      elementType: 'all',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'landscape.natural.landcover',
      elementType: 'geometry.fill',
      stylers: [
        {
          visibility: 'on',
        },
      ],
    },
    {
      featureType: 'landscape.natural.terrain',
      elementType: 'geometry.fill',
      stylers: [
        {
          visibility: 'off',
        },
        {
          hue: '#ff0000',
        },
      ],
    },
    {
      featureType: 'poi',
      elementType: 'all',
      stylers: [
        {
          visibility: 'on',
        },
      ],
    },
    {
      featureType: 'poi.attraction',
      elementType: 'all',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'poi.business',
      elementType: 'all',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'poi.government',
      elementType: 'all',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'poi.medical',
      elementType: 'all',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'poi.park',
      elementType: 'all',
      stylers: [
        {
          visibility: 'simplified',
        },
        {
          gamma: '1',
        },
        {
          weight: '0',
        },
      ],
    },
    {
      featureType: 'poi.park',
      elementType: 'geometry.fill',
      stylers: [
        {
          color: '#e4e4e4',
        },
      ],
    },
    {
      featureType: 'poi.park',
      elementType: 'labels',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'poi.park',
      elementType: 'labels.text',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'poi.place_of_worship',
      elementType: 'all',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'poi.school',
      elementType: 'all',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'poi.sports_complex',
      elementType: 'all',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'road',
      elementType: 'geometry.fill',
      stylers: [
        {
          saturation: -100,
        },
        {
          lightness: 99,
        },
        {
          visibility: 'on',
        },
        {
          color: '#ffffff',
        },
      ],
    },
    {
      featureType: 'road',
      elementType: 'geometry.stroke',
      stylers: [
        {
          color: '#808080',
        },
        {
          lightness: 54,
        },
      ],
    },
    {
      featureType: 'road',
      elementType: 'labels.text.fill',
      stylers: [
        {
          color: '#a0a0a0',
        },
        {
          visibility: 'on',
        },
      ],
    },
    {
      featureType: 'road',
      elementType: 'labels.text.stroke',
      stylers: [
        {
          color: '#ffffff',
        },
      ],
    },
    {
      featureType: 'road.highway',
      elementType: 'all',
      stylers: [
        {
          visibility: 'on',
        },
      ],
    },
    {
      featureType: 'road.highway',
      elementType: 'labels',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'road.highway',
      elementType: 'labels.text',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'road.highway',
      elementType: 'labels.icon',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'road.highway.controlled_access',
      elementType: 'all',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'road.local',
      elementType: 'all',
      stylers: [
        {
          visibility: 'on',
        },
      ],
    },
    {
      featureType: 'transit.line',
      elementType: 'all',
      stylers: [
        {
          visibility: 'on',
        },
      ],
    },
    {
      featureType: 'transit.line',
      elementType: 'labels',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'transit.line',
      elementType: 'labels.text',
      stylers: [
        {
          color: '#ff0000',
        },
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'transit.line',
      elementType: 'labels.text.fill',
      stylers: [
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'transit.line',
      elementType: 'labels.text.stroke',
      stylers: [
        {
          visibility: 'on',
        },
      ],
    },
    {
      featureType: 'transit.line',
      elementType: 'labels.icon',
      stylers: [
        {
          visibility: 'on',
        },
      ],
    },
    {
      featureType: 'transit.station',
      elementType: 'all',
      stylers: [
        {
          visibility: 'on',
        },
      ],
    },
    {
      featureType: 'transit.station',
      elementType: 'labels.text',
      stylers: [
        {
          color: '#ff0000',
        },
        {
          visibility: 'off',
        },
      ],
    },
    {
      featureType: 'water',
      elementType: 'all',
      stylers: [
        {
          saturation: 43,
        },
        {
          lightness: -11,
        },
        {
          hue: '#0088ff',
        },
      ],
    },
  ],
}

export default Vue.extend({
  components: {
    TopBar,
    SideBar,
    SideBarV2
  },
  data() {
    return {
      autocomplete: {} as any,
      map: undefined as any,
      places: [] as any,
      markers: [] as any[],
    }
  },
  created() {
    var _self = this
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(function (position) {
        let initialLocation = new google.maps.LatLng(
          position.coords.latitude,
          position.coords.longitude
        )
        mapOptions.center = initialLocation
        if (_self.map) {
          _self.map.setCenter(initialLocation)
        }
      })
    }
  },
  mounted() {
    this.init()
  },
  computed: {
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    getSideBarVersion(): string {
        return this.$store.getters['sidebarv2/getVersion'] 
    },
  },
  methods: {
    init() {
      var _self = this

      const input: any = _self.$refs.autocomplete
      const mapElement = document.getElementById('location-map')
      const searchBox = new google.maps.places.SearchBox(input)
      _self.map = new google.maps.Map(mapElement, mapOptions)

      searchBox.addListener('places_changed', () => {
        let places = searchBox.getPlaces()
        _self.places = places.slice(0, 10)

        // Clear out the old markers.
        _self.markers.forEach(function (marker) {
          marker.setMap(null)
        })
        _self.markers = []

        // For each place, get the icon, name and location.
        var bounds = new google.maps.LatLngBounds()
        places.forEach(function (place: any) {
          if (!place.geometry) {
            return
          }

          var marker = new google.maps.Marker({
            // title: place.name,
            position: place.geometry.location,
            map: _self.map,
            label: {
              fontFamily: 'Magicons',
              text: '\uE96F',
              color: '#ffff',
              // fontWeight: 400,
              // fontSize: 14
            },
          })

          _self.markers.push(marker)

          if (place.geometry.viewport) {
            // Only geocodes have viewport.
            bounds.union(place.geometry.viewport)
          } else {
            bounds.extend(place.geometry.location)
          }
        })

        _self.map.fitBounds(bounds)
      })

      _self.map.addListener('bounds_changed', function () {
        searchBox.setBounds(_self.map.getBounds())
      })
    },
    addManualLocation() {
      this.$router.push({
        name: 'account_add',
        query: {
          snapshot: this.$route.query.snapshot,
          s_type: this.$route.query.s_type,
        },
      })
    },
    getListingClass(index: number) {
      return ['--blue', '--green', '--orange', '--pink'][index % 4]
    },
    chooseLocation(place: any) {
      this.$router.replace({
        name: 'account_add',
        params: { id: place.place_id },
        query: { ...this.$route.query },
      })
    },
  },
})
</script>

<style>
.search-result-item .text h5,
.search-result-item .text p {
  width: 90%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
