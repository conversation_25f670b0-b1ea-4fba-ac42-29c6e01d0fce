<template>
  <router-view />
</template>
<script lang="ts">
import Vue from 'vue'
import config from '../../../config'
export default Vue.extend({
  beforeMount() {
    try {
      const companyId = this.$store.state.company.company.id
      const authorizedCompanyId = config.companyIdForManagingTemplates
      if (companyId !== authorizedCompanyId) {
        throw new Error('Company not authorized to manage templates')
      }
    } catch (error) {
      this.$router.push({ path: '/accounts' })
    }
  }
})
</script>
