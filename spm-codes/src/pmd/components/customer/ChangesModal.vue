<template>
  <div>
    <b-modal id="changesModal" ref="changesModal" title="Log Details" close>
      <div class="d-flex flex-column">
        <span class="mb-1"><strong>Event:</strong> {{ heading }}</span>
        <span class="my-1"><strong>Channel:</strong> {{ channel }}</span>
        <span class="my-1"><strong>Source:</strong> {{ source }}</span>
        <span class="my-1" v-if="log.sourceId"
          ><strong>Source ID:</strong> {{ log.sourceId }}</span
        >
        <div v-if="log.type === 'UPDATED'" class="d-flex flex-column">
          <span class="my-1 line-bottom"><strong>Changes:</strong></span>
          <table>
            <thead>
              <tr>
                <th>Field</th>
                <th>Before</th>
                <th>After</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="change in changes" :key="change.key">
                <td>{{ change.field }}</td>
                <td>{{ change.before }}</td>
                <td>{{ change.after }}</td>
              </tr>
              <tr v-for="change in changesCustomFields" :key="change.key">
                <td>{{ change.field }}</td>
                <td>{{ change.before }}</td>
                <td>{{ change.after }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <template #modal-footer="{ hide }">
        <div class="d-flex justify-content-center">
          <b-button size="sm" variant="success" @click="hide"> OK </b-button>
        </div>
      </template>
    </b-modal>
  </div>
</template>

<script lang="ts">
import * as lodash from 'lodash'
import moment from 'moment'
import Vue from 'vue'

enum types {
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
  DELETED = 'DELETED',
  RESTORED = 'RESTORED',
}

const excludedChanges = [
  'date_updated',
  'last_session_activity_at',
  'birth_month',
  'birth_day',
  'custom_fields',
]

export default Vue.extend({
  props: ['log'],
  mounted() {
    this.$bvModal.show('changesModal')

    this.$root.$on('bv::modal::hidden', (bvEvent, modalId) => {
      if (modalId === 'changesModal') this.close()
    })
  },
  methods: {
    close() {
      this.$emit('close')
    },
  },
  computed: {
    heading(): string {
      switch (this.log.type) {
        case types.CREATED:
          return 'Contact Created'
        case types.UPDATED:
          return 'Contact Updated'
        case types.DELETED:
          return 'Contact Deleted'
        case types.RESTORED:
          return 'Contact Restored'
        default:
          return 'Unknow'
      }
    },
    source(): string {
      return lodash.startCase(this.log.source.toLowerCase())
    },
    channel(): string {
      return lodash.startCase(this.log.channel.toLowerCase())
    },
    changes(): { [key: string]: any }[] {
      const keys = lodash
        .uniq([...Object.keys(this.log.before), ...Object.keys(this.log.after)])
        .filter((key: string) => !excludedChanges.includes(key))
      const map = keys.map((key: string) => {
        let before = this.log.before[key]
        let after = this.log.after[key]

        if (key === 'date_of_birth') {
          if (before) before = moment.utc(before).format('MM/DD/YYYY')
          if (after) after = moment.utc(after).format('MM/DD/YYYY')
        }

        if (key === 'tags') {
          if (before) before = before.join(', ')
          if (after) after = after.join(', ')
        }

        return {
          key,
          field: lodash.startCase(key.toLowerCase()),
          before,
          after,
        }
      })
      return map
    },
    changesCustomFields(): { [key: string]: any }[] {
      let arrayBefore = this.log.before?.custom_fields ?? []
      let arrayAfter = this.log.after?.custom_fields ?? []

      const ids = lodash.uniq([
        ...arrayBefore.map((e: { [key: string]: any }) => {
          return e.id
        }),
        ...arrayAfter.map((e: { [key: string]: any }) => {
          return e.id
        }),
      ])

      const map = ids.map((id: string) => {
        let before = lodash.find(arrayBefore, { id: id })
        let after = lodash.find(arrayAfter, { id: id })

        return {
          id,
          field: after.field_name,
          before: before?.field_value,
          after: after?.field_value,
        }
      })
      return map
    },
  },
})
</script>

<style scoped>
.line-bottom {
  border-bottom: 2px solid #f2f7fa;
}
</style>

<style>
body.modal-open {
  padding-right: 0 !important;
}
</style>
