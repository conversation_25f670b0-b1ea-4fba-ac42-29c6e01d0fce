<!-- prettier-ignore -->
<template>
  <div class="hl_wrapper" id="balegacy">
    <section class="hl_wrapper--inner smartlists customers" id="customers" :style="sidebarV2Styles">
      <div  class="container-fluid">
        <div class="card hl_dashboard--latest-review-requests" >
          <div class="card-header">
              <div class="d-flex justify-content-between" style="width:100%">
                  <h2>Bulk Actions</h2>
                    <!-- <CommonSelector
                      :display="selectedFilter"
                      customclass="dropdown-background hl_datepicker d-flex justify-content-center align-items-center"
                      :items="filters"
                      v-on:selectionChange="filterChange">
                    </CommonSelector> -->
                    <div class="d-inline-flex">
                      <div class="hl_tasks-list bulk-actions-header ">
                      <b-dropdown :text="selectedFilter">
                        <b-dropdown-item v-for="filter in filters" :key="filter.key" @click="filterChange(filter)">{{filter.value}}</b-dropdown-item>
                      </b-dropdown>
                      </div>
                    <div class="d-flex" style="min-width: 190px;" v-if="selectedFilter === 'Completed'">
                      <vue-ctk-date-time-picker
                          v-model="selectedDateRange"
                          :right="true"
                          :range="true"
                          color="#188bf6"
                          :only-date="true"
                          enable-button-validate
                          :noClearButton="true"
                          name="start_time"
                          min-date="2020-05-29 12:00 am"
                          :formatted="getCountryDateFormat(false)"
                          @validate="setSelectedDate"/>
                    </div>
                </div>
                   <!-- -->
                  <!-- <div id="hl_datepicker" class="hl_datepicker d-flex justify-content-center align-items-center"  v-if="selectedFilter === 'Completed'">
                    <i class="icon icon-calendar"></i>
                        <div class="d-flex" style="min-width: 190px;">
                          <vue-ctk-date-time-picker
                              v-model="selectedDateRange"
                              :right="true"
                              :range="true"
                              color="#188bf6"
                              :only-date="true"
                              enable-button-validate
                              :noClearButton="true"
                              name="start_time"
                              min-date="2020-05-29 12:00 am"
                              :formatted="getCountryDateFormat(false)"
									            @validate="setSelectedDate"/>
                        </div>
                    <i class="icon icon-arrow-down-1"></i>
                  </div> -->
              </div>

          </div>
          <div class="card-body --no-padding">
            <div class="table-wrap" v-if="isLoading">
              <table class="table table-sort">
                <tbody>
                <tr  v-for="index in 7" :key="index">
                  <td v-for="index2 in 7" :key="index2">
                    <span class="workinprogress"></span>
                  </td>
                </tr>
                </tbody>
              </table>
             </div>
            <div class="table-wrap" v-else >
              <table class="table table-sort">
                <thead>
                  <tr>
                    <!-- <th><i class="icon-user"></i></th> -->
                    <th>Operation</th>
                    <th>Summary</th>
                    <th>Status</th>
                    <th>Started On</th>
                    <th>Next On</th>
                    <th>Mode</th>
                    <th>Queued</th>
                    <th>Success</th>
                    <th>Error</th>
                    <th>Scheduled By</th>
                    <th v-if="selectedFilter === 'Scheduled'"></th>
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  <template v-for="request in items">
                    <tr :key="request.id">
                      <td >
                        <div class="align-self-center">
                          <div>
                            <a class="my-1 capitalize" v-if="request.operationDescription" href="javascript:void()"
                                                    @click.prevent.stop="openDescriptionReport(request)">
                                                    {{request.operationType ? request.operationType.replaceAll('-', ' ') : '' }}
                            </a>
                          </div>
                          <!-- <div class="tag my-1" v-if="!request.selectedRecordsCount && !request.hasFrozenList"><small>uses dynamic list</small></div> -->
                        </div>
                      </td>
                      <td>
                        <div  class="align-self-center">
                        <div v-if="request.operationNote" class="py-1">{{request.operationNote}}</div>
                        <div> <span class="my-1" v-if="request.operationDescription">{{ request.operationDescription }}</span></div>

                        </div>
                      </td>
                      <td style="width:130px;">
                          <span v-if="request.currentStatus=='pending' || request.currentStatus=='resumed'" class="--dark text-capitalize">{{request.currentStatus}}</span>
                          <span v-else-if="request.currentStatus=='complete'" class="--green text-capitalize">{{request.currentStatus}}</span>
                          <span v-else-if="request.currentStatus=='waiting'" class="--yellow text-capitalize">Queueing</span>
                          <span v-else-if="request.currentStatus=='processing' || request.currentStatus=='queueing'" class="--yellow text-capitalize">{{request.currentStatus}}</span>
                          <span v-else-if="request.currentStatus=='cancelled'" class="--red text-capitalize">{{request.currentStatus}}</span>
                          <span v-else-if="request.error" class="--red text-capitalize"
                                v-b-tooltip.hover :title="`${request.error}`">{{request.currentStatus}}</span>
                          <span v-else class="-orange text-capitalize">{{request.currentStatus}}</span>
                      </td>
                      <td style="width:130px;">
                          <div v-if="request.processingStartedOn" style="align-items: normal !important;"
                              :set="parts = getDateParts(request.processingStartedOn, locationTimeZone)">
                            <div class="date-display text-nowrap">{{parts[0]}}</div>
                            <div class="date-display --blue text-nowrap">{{parts[1]}} <small>({{parts[2]}})</small></div>
                          </div>
                      </td>
                      <td style="width:130px;">
                        <!-- {{request.nextProcessingOn}} -->
                         <div v-if="request.currentStatus !=='processing' && request.currentStatus !=='queueing' && request.currentStatus !=='waiting'">
                          <div v-if="request.nextProcessingOn" style="align-items: normal !important;"
                              :set="parts = getDateParts(request.nextProcessingOn, locationTimeZone)">
                            <div class="date-display text-nowrap">{{parts[0]}}</div>
                            <div class="date-display --blue text-nowrap">{{parts[1]}} <small>({{parts[2]}})</small></div>
                          </div>
                         </div>
                      </td>
                      <td v-if="request.isDripMode">Drip</td>
                      <td v-else-if="request.isProcessAllAtScheduleMode">All On Given Date</td>
                      <td v-else>As Soon As Possible</td>
                      <td v-if="request.queuedCount"><a href="javascript:void()" @click.prevent.stop="openQueuedReport(request)"> {{ request.queuedCount }}</a></td>
                      <td v-else>{{ request.queuedCount }}</td>
                      <td v-if="request.successCount"><a href="javascript:void()" @click.prevent.stop="openSuccessReport(request)"> {{ request.successCount }}</a></td>
                      <td v-else>{{ request.successCount }}</td>
                      <td v-if="request.errorCount"><a href="javascript:void()" @click.prevent.stop="openErrorReport(request)"> {{ request.errorCount }}</a></td>
                      <td v-else>{{ request.errorCount }}</td>
                      <td>{{ request.byUserName || findUsername(request.byUserId) }}</td>
                      <td>
                        <div v-if="request.queuedCount && !request.showStats && (request.operationType === 'bulk-sms' || request.operationType === 'bulk-email')">
                            <a class="my-1 capitalize" href="javascript:void()"
                                                  @click.prevent.stop="getStats(request)"> Show Stats
                           </a>
                        </div>
                        <div v-if="request.showStats">
                            <a class="my-1 capitalize" href="javascript:void()"
                                                  @click.prevent.stop="request.showStats = false"> Hide Stats
                           </a>
                        </div>
                      </td>
                      <td style="width:120px;" v-if="selectedFilter === 'Scheduled'">
                        <!-- {{request.currentStatus}} -->
                        <div class="mx-1 float-left"
                            v-if="processingId === request.id">
                          <moon-loader loading=true  color="#37ca37" size="20px" />
                        </div>
                        <template v-else>
                          <span @click.prevent="action('resume', request)" v-if="request.currentStatus === 'paused'" >
                            <i class="icon icon-play-filled --blue fa-align-justify zoomable mx-1  float-left  --blue pointer" ></i>
                          </span>
                            <!-- v-if="!request.sprintSchedule || !request.sprintSchedule.processAllAtSchedule" -->

                          <span v-if="request.canPause" @click.prevent="action('pause', request)"><i class="icon icon-pause-filled fa-align-justify zoomable mx-1  float-left  --blue pointer"></i></span>
                          <span v-if="request.canCancel" @click.prevent="action('cancel', request)"><i class="icon icon-close-circle-filled fa-align-justify zoomable mx-1  float-left  --blue pointer" ></i></span>
                          {{request.canEditSprintLimit}}
                          <span v-if="request.canEditSprintLimit" >
                            <i @click.stop.prevent="editSprintLimit(request)"
                                class="icon icon-eraser fa-align-justify zoomable mx-1 float-left --blue pointer">
                            </i>
                          </span>
                          <span v-if="request.canEditSchedule" >
                            <i @click.stop.prevent="editSchedule(request)"
                                class="icon icon-pencil fa-align-justify zoomable mx-1 float-left --blue pointer">
                            </i>
                          </span>
                          <span v-if="request.operationType === 'bulk-import' && request.operationImportId" @click.prevent="showSmartList({import_filter: request.operationImportId })">
                            <i class="icon icon-edit fa-align-justify zoomable mx-1 float-left  --blue pointer"></i>
                          </span>
                        </template>
                      </td>
                    </tr>
                    <tr v-if="request.queuedCount && request.showStats === true && request.eventStats && request.eventStats.count && (request.operationType === 'bulk-sms')"
                        :key="`${request.id}-1`"
                        style="border: 0px solid !important;">
                      <td style="border: 0px solid !important;" colspan="100">
                        <SMSStatsPanel :eventStats="request.eventStats" :bulkReqId="request.id"></SMSStatsPanel>
                      </td>
                    </tr>
                    <tr v-else-if="request.queuedCount && request.showStats === true && request.eventStats && request.eventStats.count && (request.operationType === 'bulk-email')"
                        :key="`${request.id}-2`"
                        style="border: 0px solid !important;">
                      <td style="border: 0px solid !important;" colspan="100">
                        <EmailStatsPanel :emailStats="request.eventStats" :bulkReqId="request.id"></EmailStatsPanel>
                      </td>
                    </tr>
                    <tr v-else-if="request.queuedCount && request.showStats === true"
                        :key="`${request.id}-2`"
                        style="border: 0px solid !important;">
                      <td style="border: 0px solid !important;" colspan="100">
                        <div class="h5 --light d-flex justify-content-start py-20 my-20"> No Stats Found </div>
                      </td>
                    </tr>
                  </template>
                </tbody>
              </table>
            </div>
          </div>
        </div>
            <div class="mx-10 my-10">
              * Click the counts to view list of contacts operated upon. Click the operation name to view bulk action details.
            </div>
      </div>

      <BulkActionItemsReport v-if="selectedBulReq && !showDetails"
                             v-on:closed="closeListReport"
                            :showError="showError"
                            :showSuccess="showSuccess"
                            :description="selectedBulReq.opSpecs && selectedBulReq.opSpecs.description || ''"
                            :bulkReqId="selectedBulReq.id"/>
      <BulkActionDescription v-if="selectedBulReq && showDetails"
                            :allowEdit ="allowScheduleEdit"
                            :allowSprintChange="allowSprintChange"
                            :request="selectedBulReq"
                             v-on:closed="closeListReport"/>
    </section>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { ImportRequest, User } from '../../../models'
import { ScheduledBulkActions } from './server/bulkreq_api'
import {
  BulkRequestChangeType,
  BulkRequestV2Subscriber,
} from './server/bulk_request_changes'
import BulkActionItemsReport from './BulkActionItemsReport.vue'
import BulkActionDescription from './BulkActionDescription.vue'
import SMSStatsPanel from './SMSStatsPanel.vue'
import EmailStatsPanel from './EmailStatsPanel.vue'
import { BulkRequestV2 } from './server/bulk_req_v2'
import { getCountryDateFormat, User } from '@/models'
//import CommonSelector from './CommonSelector.vue'
import { KeyVal } from './CommonSelector.vue'
import * as moment from 'moment-timezone'

export default Vue.extend({
  name: 'BulkActionsList',
  data() {
    return {
      showDetails: false,
      users: [] as User[],
      allowScheduleEdit: false,
      allowSprintChange: false,
      requests: [] as ImportRequest[],
      currentLocationId: '',
      changeTracker: null as BulkRequestV2Subscriber | null,
      isLoading: false,
      processingId: '',
      items: [] as BulkRequestV2[],
      selectedBulReq: null as BulkRequestV2 | null,
      showError: false as boolean,
      showSuccess: false as boolean,
      filters: [new KeyVal('Scheduled'), new KeyVal('Completed')],
      selectedFilter: 'Scheduled',
      getCountryDateFormat,
      reportRefreshTimer: undefined as number | undefined,
      selectedDateRange: {
        start: moment().subtract(30, 'days').startOf('day').toDate(),
        end: moment().endOf('day').toDate(),
      },
    }
  },
  components: {
    BulkActionItemsReport,
    //CommonSelector,
    BulkActionDescription,
    SMSStatsPanel,
    EmailStatsPanel,
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    await this.fetchData()
  },
  watch: {
    '$route.params.location_id': async function (id) {
      this.currentLocationId = id
      await this.fetchData()
    },
  },
  computed: {
    locationTimeZone() {
      //console.log(`hi -- ${this.$store.getters[`locations/getCurrentLocationTimeZone`]}`)
      return this.$store.getters[`locations/getCurrentLocationTimeZone`]
    },
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    sidebarV2Styles() {
      return this.user.sidebarVersion ? "" : "min-width:100%";
    }
  },
  methods: {
    clearRefreshTimer() {
      if (this.reportRefreshTimer) clearTimeout(this.reportRefreshTimer)
    },
    startRefreshTimer(ms: number = 1 /*min*/ * 60 * 1000) {
      if (this.selectedFilter !== 'Scheduled') return // Don't start timer on other statuses like complete
      this.reportRefreshTimer = setTimeout(this.fetchData, ms)
    },
    async fetchData() {
      try {
        const previousCount = (this.items && this.items.length) || 0
        if (this.isLoading) return
        this.clearRefreshTimer()
        //if (this.changeTracker) await this.changeTracker.suspendListen()
        this.isLoading = true
        const filters = {
          status: { filterAgent: 'legacy', status: this.selectedFilter === 'Completed' ? 'complete' : 'scheduled' },
        }
        if (this.selectedFilter === 'Completed' && this.selectedDateRange) {
          //console.log(this.selectedDateRange.start)
          //console.log(typeof this.selectedDateRange.start)

          if (this.selectedDateRange.start instanceof Date)
            filters.from = moment(
              this.selectedDateRange.start.toISOString()
            ).format('YYYY-MM-DD HH:mm:ss')
          else if (typeof this.selectedDateRange.start === 'string')
            filters.from = moment(
              this.selectedDateRange.start,
              'YYYY-MM-DD hh:mm:ss am'
            ).format('YYYY-MM-DD HH:mm:ss')

          if (this.selectedDateRange.end instanceof Date) {
            let end = moment(this.selectedDateRange.end.toISOString())
            filters.to = end.format('YYYY-MM-DD HH:mm:ss')
          } else if (typeof this.selectedDateRange.end === 'string') {
            filters.to = moment(
              this.selectedDateRange.end,
              'YYYY-MM-DD hh:mm:ss am'
            ).format('YYYY-MM-DD HH:mm:ss')
          }
          // filters.from = this.selectedDateRange.start ? this.selectedDateRange.start.format('YYYY-MM-DD') : '';
          // filters.to = this.selectedDateRange.end ? this.selectedDateRange.end.format('YYYY-MM-DD') : '';
          filters.timezone = this.locationTimeZone
        }
        //console.log(filters);
        this.items = await ScheduledBulkActions.getList(filters)
        if (previousCount !== ((this.items && this.items.length) || 0)) {
          this.$emit('updated')
        }
        // const temp = this.items.filter(a => a.operationType === 'bulk-sms')
        // await Promise.all(
        //   temp.map(async req => {
        //     req.eventStats = await ScheduledBulkActions.getSMSEventStats(req)
        //     console.log(req.eventStats)
        //   })
        // )
        // const temp2 = this.items.filter(a => a.operationType === 'bulk-email')
        // await Promise.all(
        //   temp2.map(async req => {
        //     req.eventStats = await ScheduledBulkActions.getEmailEventStats(req)
        //     console.log(req.eventStats)
        //   })
        // )
      } catch (err) {
        console.log(err)
      } finally {
        this.isLoading = false
        //if (this.changeTracker) await this.changeTracker.resumeListen();
        this.startRefreshTimer()
      }
    },
    async getStats(req: any) {
      if (!req) return
      if (req.operationType === 'bulk-sms') {
        req.eventStats = await ScheduledBulkActions.getSMSEventStats(req)
      }
      if (req.operationType === 'bulk-email') {
        req.eventStats = await ScheduledBulkActions.getEmailEventStats(req)
      }
      req.showStats = true
    },
    async itemUpdate(
      changeType: BulkRequestChangeType,
      reqUpdate: BulkRequestV2
    ) {
      if (!reqUpdate || !reqUpdate.id) return
      const index = this.items.findIndex(a => a.id === reqUpdate.id)
      // if (changeType === BulkRequestChangeType.ADDED && index === -1) {
      //     this.items.push(reqUpdate);
      // }else
      if (changeType === BulkRequestChangeType.MODIFIED && index !== -1) {
        //alert(reqUpdate.id);
        this.items.splice(index, 1, reqUpdate)
      } else if (changeType === BulkRequestChangeType.REMOVED && index !== -1) {
        this.items.splice(index, 1)
      }
    },
    async clearChangeTracking() {
      if (this.changeTracker) await this.changeTracker.terminate()
    },
    async showSmartList(params: any){
      this.$router.push({name:(`import_filter${this.$router.currentRoute.name?.endsWith('v2') ? '-v2' : ''}`), params})
    },
    async action(
      action: 'pause' | 'cancel' | 'resume',
      bulkRequest: BulkRequestV2
    ) {
      if (this.isLoading || this.processingId) return
      if (!bulkRequest) return
      try {
        this.processingId = bulkRequest.id
        await this.$nextTick()
        bulkRequest.suspendUIAction = true
        await ScheduledBulkActions.action({
          action,
          bulkRequestId: bulkRequest.id,
        })
        await this.fetchData()
      } catch (err) {
        this.processingId = ''
        console.log(err)
      } finally {
        this.processingId = ''
      }
    },
    async editSprintLimit(req: any) {
      this.allowScheduleEdit = false
      this.allowSprintChange = true
      this.selectedBulReq = req
      this.showDetails = true
    },
    async editSchedule(req: any) {
      this.allowScheduleEdit = true
      this.selectedBulReq = req
      this.showDetails = true
    },
    async closeListReport() {
      const requiresRefresh = this.allowScheduleEdit || this.allowSprintChange
      this.allowScheduleEdit = false
      this.allowSprintChange = false
      this.showSuccess = this.showError = false
      this.selectedBulReq = null
      this.showDetails = false
      if (requiresRefresh) this.startRefreshTimer(2000)
    },
    async openErrorReport(bulkReq: BulkRequestV2) {
      this.showError = true
      this.showSuccess = false
      this.showDetails = false
      this.selectedBulReq = bulkReq
    },
    async openSuccessReport(bulkReq: BulkRequestV2) {
      this.showSuccess = true
      this.showError = false
      this.showDetails = false
      this.selectedBulReq = bulkReq
    },
    async openQueuedReport(bulkReq: BulkRequestV2) {
      this.showError = false
      this.showSuccess = false
      this.selectedBulReq = bulkReq
    },
    openDescriptionReport(bulkReq: BulkRequestV2) {
      this.showError = false
      this.showSuccess = false
      this.showDetails = true
      this.selectedBulReq = bulkReq
    },
    filterChange(event: KeyVal) {
      this.selectedFilter = (event && event.value) || ''
      this.fetchData()
    },
    async setSelectedDate() {
      //alert(this.selectedDateRange.start)
      await this.fetchData()
      // 	this.selectedDateRange = this.opportunitiesFiltersDates
    },
    findUsername(userId: string) {
      if (userId) {
        const item = this.users.find(a => a.id === userId)
        if (item) return item.name
      }
      return 'Not mapped'
    },
  },
  async mounted() {
    this.users = await this.$store.getters['users/getAll']
    await this.clearChangeTracking()
    this.changeTracker = new BulkRequestV2Subscriber(this.itemUpdate)
  },
  async beforeDestroy() {
    this.clearRefreshTimer()
    await this.clearChangeTracking()
  },
})
</script>
<style>
.sort {
  margin-left: 20px;
  margin-bottom: 10px;
  cursor: pointer;
}

.form-control:disabled,
.form-control[readonly] {
  background-color: #f7fafc;
}
.vdp-datepicker__calendar .cell:not(.blank):not(.disabled).day:hover,
.vdp-datepicker__calendar .cell:not(.blank):not(.disabled).month:hover,
.vdp-datepicker__calendar .cell:not(.blank):not(.disabled).year:hover {
  border: 1px solid #188bf6 !important;
}
.vdp-datepicker__calendar .cell.selected,
.vdp-datepicker__calendar .cell.selected.highlighted,
.vdp-datepicker__calendar .cell.selected:hover {
  background: #188bf6 !important;
  color: white;
}
#back {
  margin-right: 10px;
  cursor: pointer;
  color: #188bf6;
}
.dropdown-background {
  background: rgb(242 247 250);
}
.campaign_new_header .hl_datepicker {
  background: rgb(242 247 250);
  padding: 0px 8px;
  border-radius: 0.3125rem;
  -webkit-transition: all 0.2s ease-in-out 0s;
  transition: all 0.2s ease-in-out 0s;
  cursor: pointer;
  margin-right: 0;
  height: 40px;
}
.hl_tasks-list.bulk-actions-header {
  display: flex;
  align-items: center;
}
.hl_tasks-list.bulk-actions-header h2 {
  margin-right: 16px;
}
/* .dropdown .dropdown-menu .dropdown-item .custom-width-350 {
  white-space: initial;
  width: 350px;
} */
.hl_tasks-list button.dropdown-toggle {
  background-color: #ffffff !important;
  color: #607179 !important;
}
/* .customerPageClass .hl_tasks-list .show {
      display: inline-block;
    }
    .customerPageClass h2 {
      display: none;
    }
    .customerPageClass .hl_tasks-list .dropdown button {
      font-size: 0.825rem;
      font-weight: 500;
    }
    .customerPageClass .hl_tasks-list .btn.dropdown-toggle {
      padding-right: 35px !important;
    } */

.hl_tasks-list .dropdown .dropdown-menu {
  max-height: 250px;
  overflow: auto;
}
</style>
