<template>
  <div class="hl_settings--body">
    <div class="container-fluid">
      <div class="row">
        <div class="hl_settings--main col-lg-12">
          <div class="card">
            <div class="card-header">
              <h3>Affiliate Settings</h3>
            </div>
            <div class="card-body" style="max-width: 100%;">
              <div class="personal-logo">
                <div class="col-sm-9">
                  <p>This is your affiliate link, use this link to allow people you know to sign up for an account and you will recieve 40% of their monthly recurring revenue for life.</p>
                  <br>You can append this to the following URLs for easy tracking:
                  <ul>
                    <li>
                      <a
                        target="_blank"
                        :href="'https://affiliates.gohighlevel.com/?fp_ref=' + referralId"
                      >https://affiliates.gohighlevel.com/?fp_ref={{referralId}}</a>
                    </li>
                    <li>
                      <a
                        target="_blank"
                        :href="'https://www.gohighlevel.com/main-page?fp_ref=' + referralId"
                      >https://www.gohighlevel.com/main-page?fp_ref={{referralId}}</a>
                    </li>
                  </ul>
                </div>
              </div>

              <div class="personal-logo" v-if="user">
                <div class="col-sm-9">
                  <p>Need to sign up?</p>
                  <br>Go to the URL:
                  <a
                    href="https://gohighlevel.firstpromoter.com"
                    target="_blank"
                  >https://gohighlevel.firstpromoter.com</a>
                  and sign up using the email address
                  <b>{{user.email}}</b> as your email address for FirstPromoter.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="hl_settings--main col-lg-12">
          <div class="card hl_settings--team-management">
            <div class="card-header">
              <h3>Page Settings</h3>
            </div>
            <div class="card-body" style="margin: unset;">
              <div class="form-group">
                <UITextInputGroup
                  type="text"
                  label="Facebook Pixel ID"
                  placeholder="Pixel ID"
                  v-model="company.affiliatePixelId"
                  v-validate="'required'"
                  name="pixelId"
                />
              </div>
            </div>
            <div class="card-footer">
              <div style="display: inline-block;position: relative;">
                <UIButton
                  type="button"
                  use="primary"
                  @click.prevent="saveAccount"
                  :loading="processing"
                >Save</UIButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang='ts'>
import Vue from 'vue'
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'

import { Company, User } from '@/models'
const MoonLoader = () => import('@/pmd/components/MoonLoader.vue')

import libphonenumber from 'google-libphonenumber'
var phoneUtil = libphonenumber.PhoneNumberUtil.getInstance()
var PNF = libphonenumber.PhoneNumberFormat
declare var $: any

export default Vue.extend({
  components: { MoonLoader },
  data() {
    return {
      company: {} as Company,
      currentLocationId: '' as string,
      pixelId: '',
	  processing: false,
	  referralId: ''
    }
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.currentLocationId = id
    }
  },
//   updated() {
//     const $selectpicker = $(this.$el).find('.selectpicker')
//     if ($selectpicker) {
//       $selectpicker.selectpicker('refresh')
//     }
//   },
  async created() {
    var _self = this
    const authUser = await this.$store.dispatch('auth/get')
	this.company = await Company.getById(authUser.companyId)
	try {
		let promoter = await this.$http.get('/firstpromoter/get_promoter_details?company_id=' + this.company.id);
		if( promoter && promoter.data) {
			this.referralId = promoter.data.default_ref_id;
		}
	} catch(err) {}
    if (!this.referralId && !this.company.referralId) {
      let resp = await this.$http.get(
        '/firstpromoter/add_promoter?company_id=' + this.company.id
      )
      this.company.referralId = resp.data
      await this.company.save()
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    })
  },
  methods: {
    async saveAccount() {
      this.processing = true
      await this.company.save()
      this.processing = false
    }
  }
})
</script>
