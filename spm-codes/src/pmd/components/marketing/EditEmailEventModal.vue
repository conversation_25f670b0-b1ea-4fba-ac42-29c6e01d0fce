<template>
  <!-- Edit Event Modal -->
  <div
    class="modal fade hl_edit-event-modal"
    id="edit-event-modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog --mid" role="document">
      <div class="modal-content">
        <button
          type="button"
          class="close"
          data-dismiss="modal"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
        <div class="modal-body">
          <div class="modal-body-inner">
            <div class="hl_edit-event-modal-wrap">
              <div class="hl_edit-event-modal-main">
                <div class="card">
                  <div class="card-header --no-right-padding">
                    <h2>
                      Compose Email {{ isComposingTemplate ? 'Template' : '' }}
                    </h2>
                  </div>
                  <div class="card-body hl_campaign-configuration">
                    <div class="form-group" style="display: block">
                      <UITextInputGroup
                        class="msgsndr2"
                        placeholder="Name"
                        v-model="name"
                        v-validate="'required'"
                        name="msgsndr2"
                        data-lpignore="true"
                        autocomplete="msgsndr2"
                        :error="errors.has('msgsndr2')"
                        :errorMsg="'Name is required.'"
                      />
                    </div>
                    <div class="form-group" style="display: block">
                      <UITextInputGroup
                        type="text"
                        class="msgsndrsbjct"
                        placeholder="Subject"
                        v-model="subject"
                        v-validate="'required|handlebars'"
                        data-vv-validate-on="input"
                        name="subject"
                        autocomplete="subject"
                        v-if="
                          isComposingTemplate ||
                          !selectedTemplate ||
                          selectedTemplate.templatesource === 'emailbuilder'
                        "
                        :error="errors.has('subject')"
                        :errorMsg="errors.first('subject')"
                      />
                    </div>
                    <div
                      class="form-group pickEmail"
                      style="width: 100%"
                      v-if="isComposingTemplate !== true"
                    >
                      <vSelect
                        style="width: 50%"
                        :options="emailTemplates"
                        label="text"
                        v-model="selectedTemplate"
                        name="template"
                        placeholder="Pick email template"
                        v-if="isComposingTemplate !== true"
                      ></vSelect>
                    </div>
                    <div
                      class="form-group"
                      style="margin-top: 20px; flex-direction: column"
                      v-if="!selectedTemplate"
                    >
                      <editor
                        :init="editorOptions"
                        id="editemaileditor"
                        ref="editemaileditor"
                        v-model="content"
                        v-validate="{ handlebars: [true] }"
                        data-vv-validate-on="input"
                        name="editor"
                      ></editor>
                      <span v-show="errors.has('editor')" class="--red">{{
                        errors.first('editor')
                      }}</span>
                    </div>
                    <div class="form-group" v-if="attachments.length > 0">
                      <div>
                        <div
                          class="attachment"
                          v-for="(attachment, index) in attachments"
                          style="position: relative"
                          :key="index"
                        >
                          <i class="fa fa-paperclip"></i>
                          <span class="mx-2">{{ attachment.name }}</span>
                          <i
                            class="icon-close"
                            v-if="attachment.source !== 'tinymce'"
                            @click.prevent="deleteAttachment(attachment.name)"
                          ></i>
                        </div>
                      </div>
                    </div>
                    <WindowComponent
                      v-if="!isComposingTemplate"
                      v-model="templateWindow"
                      parent="email"
                      style="margin-top: 30px"
                    />
                  </div>
                  <div class="card-footer">
                    <div class="modal-footer-left">
                      <input
                        type="file"
                        id="image_uploads"
                        name="image_uploads"
                        multiple
                        @change="updateAttachments"
                        @click="resetAttachments"
                        style="display: none"
                        ref="upload"
                      />
                      <a
                        @click.prevent="$refs.upload.click()"
                        href="javascript:void(0);"
                      >
                        <i
                          class="fa fa-paperclip"
                          style="margin-right: 4px"
                        />Attach File
                      </a>
                    </div>
                    <div class="modal-footer-right">
                      <div style="display: inline-block; position: relative">
                        <UIButton
                          :loading="saving"
                          @click.prevent="save"
                        >
                          Save
                        </UIButton>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="card">
                  <div class="card-header --no-right-padding">
                    <h2>Send Test</h2>
                  </div>
                  <div class="card-footer">
                    <div class="modal-footer-left">
                      <div class="row">
                        <div class="col-sm-6">
                          <div class="form-group">
                            <UITextInputGroup
                              type="text"
                              label="From Address"
                              placeholder="From Address"
                              v-model="fromAddress"
                            />
                          </div>
                        </div>
                        <div class="col-sm-6">
                          <div class="form-group">
                            <UITextInputGroup
                              label="To Address"
                              type="text"
                              placeholder="To Address"
                              v-model="toAddress"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="modal-footer-right">
                      <div style="display: inline-block; position: relative">
                        <UIButton
                          :loading="sending"
                          @click.prevent="sendTest"
                        >
                          Send
                        </UIButton>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="hl_edit-event-modal-preview">
                <div class="hl_edit-event-modal-preview-inner">
                  <div
                    class="hl_settings--preview-inner"
                    style="overflow: hidden; padding: 0px"
                  >
                    <!-- <div v-html="previewHtml"
                            style="color:initial;">
                    </div>-->
                    <iframe
                      style="height: 100%; width: 100%; border: 0px !important"
                      scrolling="yes"
                      :srcdoc="previewHtml"
                    ></iframe>
                    <!-- <template v-if="attachments">
                      <div v-for="attachment in attachments"
                          class="message-bubble attachment"
                          style="background-color: #188bf6;" >
                        <img  v-if="Utils.isImage(attachment.type)" v-bind:src="attachment.url" :alt="attachment.name" />
                        <div v-else class="attachment" style="position: relative;">
                            <i class="fa fa-paperclip" />
                            <span class="mx-2">{{attachment.name}}</span>
                        </div>
                        </div>
                    </template>-->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import firebase from 'firebase/app'
import { v4 as uuid } from 'uuid'
import vSelect from 'vue-select'
import Editor from '@tinymce/tinymce-vue'
import { IFrameDisableDrops } from '@/util/disable_iframe_drop'

import { FileAttachment } from '@/store/state_models'
import ImageTools from '../../../util/image_tools'
import { getTagOptions } from '@/util/merge_tags'
import { Utils } from '../../../util/utils'
import {
  User,
  CustomField,
  CustomValue,
  EmailBuilder,
  Template,
  Campaign,
} from '@/models'
import { defaultTinyMceOptions } from '../../../util/tiny_mce_defaults'

const WindowComponent = () =>
  import(/* webpackChunkName: "marketing-window" */ './WindowComponent.vue')

declare var $: any
const imageTools = new ImageTools()
const triggerLinks: { [key: string]: any }[] = []
let customFieldValues = [] as { [key: string]: any }

import isEmpty from 'lodash/isEmpty'
import find from 'lodash/find'
import each from 'lodash/each'
import clone from 'lodash/clone'
import orderBy from 'lodash/orderBy'

export default Vue.extend({
  props: ['values', 'templates', 'isComposingTemplate'],
  components: {
    editor: Editor,
    WindowComponent,
    vSelect,
  },
  data() {
    return {
      template: {} as { [key: string]: any },
      subject: '',
      content: '',
      name: '',
      Utils: Utils,
      templateWindow: {} as any,
      isPastingImage: false,
      editorOptions: {
        ...defaultTinyMceOptions,
        height: 340,
        plugins: [
          'advlist autolink link image lists charmap hr anchor pagebreak spellchecker',
          'searchreplace wordcount visualblocks visualchars code fullscreen insertdatetime media nonbreaking',
          'table contextmenu directionality emoticons template textcolor link paste',
        ],
        toolbar:
          'sizeselect  | fontselect |  fontsizeselect formatselect  formatpainter removeformat undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent link image forecolor backcolor code | mybutton templates triggerlinks',
        setup: function (editor: any) {
          editor.addButton('mybutton', {
            type: 'listbox',
            text: 'Custom Values',
            icon: false,
            onselect: function () {
              editor.insertContent(this.value())
              this.value(null)
            },
            values: customFieldValues,
            onPostRender: function () {
              // Select the second item by default
              this.value('&nbsp;<em>Some italic text!</em>')
            },
          })
          editor.addButton('triggerlinks', {
            type: 'listbox',
            text: 'Trigger Links',
            icon: false,
            onselect: function () {
              editor.insertContent(this.value())
              this.value(null)
            },
            values: triggerLinks,
          })

          // editor.on('init', function (e) {
          //   editor.execCommand("fontName", false, "Verdana");
          //   editor.execCommand("fontSize", true, "11pt");
          // });
        },
        paste_data_images: true,
        link_list: triggerLinks,
        images_upload_handler: this.editorImagePaste,
        force_br_newlines: false,
        force_p_newlines: false,
        forced_root_block: '',
        convert_urls: false
      },
      existingFiles: [] as FileAttachment[],
      filesAdded: [] as FileAttachment[],
      currentLocationId: '',
      campaignId: '',
      saving: false,
      fromAddress: '',
      toAddress: '',
      sending: false,
      emailTemplates: [] as Array<{ [key: string]: any }>,
      selectedTemplate: undefined as undefined | { [key: string]: any },
      urlAttachment: null,
    }
  },
  computed: {
    user(): User | undefined {
      return new User(this.$store.state.user.user) || undefined
    },
    attachments(): Array<FileAttachment> {
      return [...this.filesAdded, ...this.existingFiles]
    },
    finalHtml(): string {
      let obj = this.previewBodyAndCSS()
      return `<html><head><style>${obj.css}</style></head><body>${obj.body}</body></html>`
    },
    previewHtml(): string {
      let obj: { [key: string]: any } = this.previewBodyAndCSS()
      if (isEmpty(obj.body) || obj.body.url) return ''
      // pad previewhtml with attachments but not the finalhtml - this is very important
      return `<html><head><style>${obj.css}</style></head><body>${
        obj.body
      } ${this.previewAttachments()}</body></html>`
    },
  },
  methods: {
    getStorageDownloadUrl(attachment: FileAttachment) {
      let imagePath =
        'location/' + this.currentLocationId + '/preview/' + uuid()
      var uploadPath = firebase.storage().ref(imagePath)

      if (!attachment.data) return null

      return uploadPath.put(attachment.data, {
        contentType: attachment.type,
        contentDisposition: `inline; filename="${attachment.name}"`,
        customMetadata: { name: attachment.name },
      })
    },
    async extractUrlsForTestEmails(): Promise<
      Array<string | null | undefined | firebase.storage.UploadTaskSnapshot>
    > {
      if (!this.attachments.length) return []
      const publicUrlsPromise = this.attachments.map(async attachment => {
        if (attachment.data) {
          return await this.getStorageDownloadUrl(attachment)
        } else if (attachment.url) {
          return attachment.url
        }
      })
      return Promise.all(publicUrlsPromise)
    },
    async fetchEmailTemplateHtml(selectedTemplate: any) {
      // NOTE: fetch html email builder data from storage
      if (selectedTemplate?.value?.url) {
        const params = {
          location_id: this.currentLocationId,
          template_id: selectedTemplate.id,
          type: selectedTemplate.type,
        }
        const { data } = await this.$http.get(
          `/email_builder/get_builder_template`,
          {
            params,
          }
        )

        if (this.selectedTemplate) {
          this.selectedTemplate.value = data
        }

        const index = this.emailTemplates.findIndex(
          x => x.id === selectedTemplate.id
        )

        if (index > -1) {
          this.emailTemplates[index].value = data
        }
      }
    },
    resetToInitValues() {
      this.existingFiles = []
      this.filesAdded = []
      this.toAddress = ''
      this.fromAddress = ''
      this.content = ''
    },
    async loadTriggerLinks() {
      if (!triggerLinks || triggerLinks.length < 1) {
        let obj = this.$store.getters['conversation/getTriggerLinksMenuItem']
        if (!obj || !obj.text) {
          obj = await this.$store.dispatch(
            'conversation/fetchTriggerLinks',
            {locationId: this.$route.params.location_id}
          )
        }
        delete obj.location_id
        triggerLinks.push.apply(triggerLinks, obj.menu)
      }
    },
    async setAsSelectedTemplate(templateid: any) {
      if (templateid) {
        this.selectedTemplate = this.emailTemplates.find(
          a => a.id === templateid
        )
      }
    },
    previewBodyAndCSS(): { body: string; css: string } {
      let css = 'body{font-family: sans-serif;}'
      let body = this.content
      let template: any = this.selectedTemplate
      if (template && !isEmpty(template.value)) {
        let { value } = template
        if (value.css) {
          css = value.css
        }
        if (value.html) {
          body = value.html
        } else if (value.html_string) {
          body = value.html_string
        } else {
          body = value
        }
      }
      return { body, css }
    },
    previewAttachments(): string {
      if (!this.attachments || !this.attachments.length) {
        return ''
      }

      let attachmentshtml = `<hr style='border-top:1px dotted gray'>`
      let paperclip = `<img style='padding-right:4px;vertical-align:top;' src='https://cdnjs.cloudflare.com/ajax/libs/open-iconic/1.1.1/png/paperclip-2x.png'/>`

      this.attachments.forEach((attachment: FileAttachment) => {
        let cont = ''
        if (this.Utils.isImage(attachment.type)) {
          cont = `<div style='display:inline-block'>${paperclip}<img style='width:80%' src="${attachment.url}" alt="${attachment.name}"/>`
        } else {
          cont = `${paperclip}<span class="mx-2">${attachment.name}</span>`
        }
        cont = `<div style='width:100%; padding:4px;'>${cont}</div>`
        attachmentshtml += cont
      })

      return attachmentshtml
    },
    async rebuildEmailTemplatesList() {
      this.emailTemplates = []
      this.selectedTemplate = undefined
      if (!this.currentLocationId && this.isComposingTemplate !== true) return
      const templates = await EmailBuilder.getByLocationId(
        this.currentLocationId
      )

      let refToTemplates =  templates.reduce((filteredTemplate:any, template: EmailBuilder)=> {
          if (!template.isArchived) {
            let newValue;

              if (template.downloadUrl) {
            newValue =  {
              text: template.name,
              value: template.downloadUrl
                ? { url: template.downloadUrl }
                : template.emailBuilderData,
              id: template.id,
              type: 'grapejs',
              templatesource: 'emailbuilder',
            }
          } else {
            newValue = {
              text: template.name,
              value: { url: template.htmlDownloadUrl },
              id: template.id,
              type: 'email-builder',
              templatesource: 'emailbuilder',
            }
          }
           filteredTemplate.push(newValue);
          }
          return filteredTemplate;
      }, [])

      this.emailTemplates.length = 0
      this.emailTemplates.push.apply(this.emailTemplates, refToTemplates)
      for (let template of this.templates) {
        if (template.type !== Template.TYPE_EMAIL) continue
        this.emailTemplates.push({
          text: template.name,
          value: template.template.html,
          subject: template.template.subject,
          id: template.id,
          type: 'tinymce',
          templatesource: 'other',
          attachments: undefined,
          attachurls: template.template.attachments,
        })
      }

      if (
        this.template &&
        this.template.attributes &&
        this.template.attributes.template_id
      ) {
        this.setAsSelectedTemplate(this.template.attributes.template_id)
        //this.selectedTemplate = lodash.find(refToTemplates, { id: this.template.attributes.template_id })
      }
    },
    deleteAttachment(name: string) {
      let attachment = find(this.filesAdded, { name: name })
      if (attachment) {
        this.filesAdded.splice(this.filesAdded.indexOf(attachment), 1)
      }
      attachment = find(this.existingFiles, { name: name })
      if (attachment) {
        this.existingFiles.splice(this.existingFiles.indexOf(attachment), 1)
      }
    },
    resetAttachments() {
      this.$refs.upload.value = ''
    },
    async updateAttachments() {
      const element = this.$refs.upload as HTMLInputElement
      if (!element.files) return
      for (let i = 0; i < element.files.length; i++) {
        const file = element.files[i]
        const selectedImage = this.filesAdded.filter(
          data => data.name === file.name
        )
        if (selectedImage.length > 0) continue
        const response = (await imageTools.resize(file, {
          height: 1000,
          width: 1000,
        })) as File | Blob

        this.filesAdded.push({
          name: file.name,
          type: file.type,
          url: URL.createObjectURL(response),
          data: response,
        })
      }
    },
    editorReady() {
      const $selectpicker = $(this.$el).find('.selectpicker')
      if ($selectpicker) {
        $selectpicker.selectpicker('refresh')
      }
    },
    async save() {
      await this.$validator.validateAll()
      if (this.errors.any()) {
        return false
      }
      if (this.checkCommonErrors()) {
        return false
      }

      this.saving = true

      if (this.selectedTemplate) {
        this.template.attributes.template_id = this.selectedTemplate.id
        this.template.attributes.templatesource = this.selectedTemplate.templatesource
        delete this.template.attributes.html
        // if (this.template.attributes.attachments) {
        //   delete this.template.attributes.attachments
        //   // don't delete files already uploaded on
        //   // firebase just in case a campaign already ran
        //   // and used it.
        // }
      } else {
        delete this.template.attributes.template_id
        let message = this.finalHtml.toString()
        message = message.replaceAll(String.fromCharCode(160), ' ') // replace &nbsp
        message = message.replaceAll('<wbr />', '')
        this.template.attributes.html = message
      }

      const urls = this.existingFiles
        .map(file => {
          if (file?.source === 'tinymce') return null
          return file.url
        })
        .filter(x => x)

      if (this.filesAdded.length > 0) {
        const newURLs = await Promise.all(
          this.filesAdded.map(async (attachment: any) => {
            let imagePath =
              'location/' +
              this.currentLocationId +
              '/campaign/' +
              this.campaignId +
              '/' +
              uuid()
            var uploadPath = firebase.storage().ref(imagePath)
            const snapshot = await uploadPath.put(attachment.data, {
              contentType: attachment.type,
              contentDisposition: `inline; filename="${attachment.name}"`,
              customMetadata: { name: attachment.name },
            })
            return await snapshot.ref.getDownloadURL()
          })
        )
        urls.push.apply(
          urls,
          newURLs.filter(url => url)
        )
      }
      this.template.attributes.attachments = urls.filter(url => url)

      this.template.attributes.subject = this.subject
      this.template.name = this.name

      Vue.set(this.template, 'window', this.templateWindow)
      this.$emit('save')
      this.$emit('hidden')
      this.saving = false
      return true
    },
    checkCommonErrors() {
      if (this.finalHtml.includes('{{%20')) {
        alert(
          `Please check the links where you have a custom value. There is an extra "%20" between the {{ and the custom_values.`
        )
        return true
      } else {
        return false
      }
    },
    async sendTest() {
      try {
        let campaign
        if (this.campaignId) {
          campaign = await Campaign.getById(this.campaignId)
        }
        if (!this.toAddress) {
          alert('Please insert a to address.')
          return false
        }
        if (this.checkCommonErrors()) {
          return false
        }
        this.sending = true

        let message = this.finalHtml.replaceAll(String.fromCharCode(160), ' ') // replace &nbsp
        message = message.replaceAll('<wbr />', '')

        let data: { [key: string]: any } = {
          location_id: this.currentLocationId,
          email: this.toAddress,
          message,
          from_address: this.fromAddress,
          subject:
            this.selectedTemplate && this.selectedTemplate.subject
              ? this.selectedTemplate.subject
              : this.subject,
          user_id:
            campaign &&
            campaign.campaignData.users &&
            campaign.campaignData.users.length !== 0
              ? campaign.campaignData.users[
                  Math.floor(
                    Math.random() * campaign.campaignData.users.length + 1
                  ) - 1
                ]
              : this.user
              ? this.user.id
              : null,
          type: 'email',
        }

        if (this.attachments.length > 0) {
          const attachmentUrls = await this.extractUrlsForTestEmails()
          data['attachments'] = attachmentUrls.filter(x => x)
        }

        await this.$http.post('/message/preview', data)
      } catch (err) {
        console.error(err)
        alert('An error ocurred, please try again.')
        this.sending = false
      }

      this.sending = false
      return true
    },
    async editorImagePaste(blobInfo: any, success: any) {
      let attachment = blobInfo.blob()
      let imagePath =
        'location/' +
        this.currentLocationId +
        '/campaign/' +
        this.campaignId +
        '/' +
        uuid()
      var uploadPath = firebase.storage().ref(imagePath)
      const snapshot = await uploadPath.putString(blobInfo.base64(), 'base64', {
        contentType: attachment.type,
        contentDisposition: `inline; filename="${attachment.name}"`,
        customMetadata: { name: attachment.name },
      })
      let url = await snapshot.ref.getDownloadURL()
      // this.isPastingImage = true // to skip handling in editorSetContentFired
      success(url)
      console.log(url)
    },
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  watch: {
    async selectedTemplate(newVal, oldVal) {
      setTimeout(() => IFrameDisableDrops.refresh(), 2000)

      if (!newVal) {
        this.existingFiles = []
        this.filesAdded = []
        return
      }

      if (oldVal && newVal && oldVal !== newVal) {
        this.existingFiles = []
        this.filesAdded = []
      }

      await this.fetchEmailTemplateHtml(newVal)

      this.content = ''
      if (newVal.templatesource === 'other') this.subject = ''
      //console.log(`Template changed to ${newVal.text}`);
      if (newVal && newVal.attachments) {
        newVal.attachments.forEach((x: any) => {
          this.existingFiles.push(x)
        })
      } else if (newVal && !newVal.attachments && newVal.attachurls) {
        newVal.attachments = []
        let itr = newVal.attachurls.map((url: string) => ({
          url: url,
          template: newVal,
        }))
        itr.forEach(async (item: any) => {
          const metadata = await firebase
            .storage()
            .refFromURL(item.url)
            .getMetadata()
          let obj = {
            type: metadata.contentType,
            name: metadata.customMetadata.name,
            url: item.url,
            source: 'tinymce',
          }
          if (!item.template.attachments) item.template.attachments = []
          item.template.attachments.push(obj)
          if (
            this.selectedTemplate &&
            this.selectedTemplate.id === item.template.id
          )
            this.existingFiles.push(obj)
        })
      }
    },
    async values(values: { [key: string]: any }) {
      const data = () => this.$options.data
      if (data) Object.assign(this.$data, data.apply(this))
      if (values.visible) $(this.$refs.modal).modal('show')
      else {
        this.errors.clear()
        $(this.$refs.modal).modal('hide')
      }
      this.template = values.template
      this.currentLocationId = values.currentLocationId
      this.campaignId = values.campaignId
      if (values.currentLocationId) {
        this.rebuildEmailTemplatesList()

        this.loadTriggerLinks()
        customFieldValues.length = 0
        customFieldValues.push.apply(customFieldValues, await getTagOptions())
        CustomField.getByLocationIdAndType(
          this.currentLocationId,
          'contact'
        ).then(fields => {
          const availableFields = fields.map(field => {
            return { text: field.name, value: `{{${field.fieldKey}}}` }
          })
          if (availableFields.length > 0) {
            const contactMergeTags = find(customFieldValues, {
              text: 'Contact',
            })

            contactMergeTags.menu.push({
              text: 'Custom Fields',
              menu: orderBy(
                availableFields,
                [value => value.text.toLowerCase()],
                ['asc']
              ),
            })
          }
        })
        CustomValue.getByLocationId(this.currentLocationId).then(values => {
          const availableValues = values.map(value => {
            return {
              text: value.name,
              value: `{{custom_values.${value.fieldKey}}}`,
            }
          })
          if (availableValues.length > 0) {
            customFieldValues.push({
              text: 'Custom Values',
              menu: orderBy(
                availableValues,
                [value => value.text.toLowerCase()],
                ['asc']
              ),
            })
          }
        })
      }
      if (values.template) {
        if (this.template.attributes.html) {
          this.content = this.template.attributes.html || ''
        }
        this.subject = this.template.attributes.subject || ''
        this.name = this.template.name
        if (this.template.attributes.attachments) {
          each(this.template.attributes.attachments, async url => {
            const metadata = await firebase
              .storage()
              .refFromURL(url)
              .getMetadata()
            this.existingFiles.push({
              type: metadata.contentType,
              name: metadata.customMetadata.name,
              url,
            })
          })
        }
        if (values.template.window) {
          this.templateWindow = clone(values.template.window)
        }
      }
    },
  },
  updated() {
    //console.log("Updated")
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
  async mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }

    $(this.$refs.modal).on('hidden.bs.modal', () => {
      this.$emit('hidden')
      this.resetToInitValues()
    })
    $(document).on('focusin', function (e: any) {
      if ($(e.target).closest('.mce-container').length) {
        e.stopImmediatePropagation()
      }
    })
    await this.$nextTick()
    setTimeout(() => IFrameDisableDrops.refresh(), 2000)
  },
})
</script>

<style lang="css">
.pickEmail > div.v-select > ul li.vs__dropdown-option {
  white-space: normal !important;
}
</style>
