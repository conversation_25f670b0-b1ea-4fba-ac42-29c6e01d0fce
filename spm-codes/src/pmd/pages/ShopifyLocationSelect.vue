<template>
  <div class="hl_settings--body" style="width:100%;">
    <div class="container-fluid">
      <div class="row top-buffer">
        <div
          class="col-sm-8 col-md-8 offset-sm-2 offset-md-2"
          style="background: #ffffff; padding: 25px; border-radius: 4px"
        >
          <div class="row">
            <div class="col-sm-12">
              <h5 style="margin-bottom: 20px">
                Select which location you want to link to this shopify store
              </h5>
            </div>
          </div>
          <select
            v-if="locations.length > 0"
            class="selectpicker"
            v-model="selectedLocation"
            v-validate="'required'"
            name="shopifyLocation"
            data-vv-as="Shopify location"
          >
            <option value>Select Page</option>
            <option
              v-for="location in locations"
              v-bind:key="location.id"
              :value="location.id"
            >
              {{ getLocationName(location) }}
            </option>
          </select>
        </div>
      </div>
      <div class="row top-buffer">
        <div
          class="col-sm-8 col-md-8 offset-sm-2 offset-md-2"
          style="text-align: right"
        >
          <button
            v-if="!processing"
            type="submit"
            class="btn btn-success"
            @click.prevent="connectShopify"
          >
            Connect!
          </button>
          <div
            v-if="processing"
            style="width: 130px; height: 43px; float: right"
          >
            <moon-loader :loading="processing" color="#37ca37" size="30px" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
const store = require('store')
import { Location, OAuth2, User } from '@/models'

import lodash from 'lodash'

declare var $: any

export default Vue.extend({
  data() {
    return {
      selectedLocation: undefined as Location | undefined,
      processing: false,
      locations: []
    }
  },
  async created() {
    this.processing = true;
    this.locations = await this.$store.dispatch('locations/getAll');
    this.processing = false;
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  methods: {
    getLocationName(location: Location) {
      let name = location.name
      if (location.city || location.state) name += ' -- '
      if (location.city) name += location.city
      if (location.city && location.state) name += ', '
      if (location.state) name += location.state
      return name
    },
    async connectShopify() {
      if (!this.selectedLocation) return;

      this.processing = true

      try {
        const code = this.$router.currentRoute?.query?.code;
        const shop = this.$router.currentRoute?.query?.shop;
        if (!code || !shop) return;

        let response = await this.$http.get(`/shopify/finish_oauth?code=${code}&shop=${shop}&state=${this.selectedLocation}`);
        this.$router.push({
          name: 'integrations_settings',
          params: { location_id: this.selectedLocation }
        })
      } catch(err) {
      } finally {
        this.processing = false;
      }
    }
  }
})
</script>
