<template>
  <Filters
    :conditions="conditions"
    :filterMaster="filterMaster"
    v-on:update:conditions="$emit('update:conditions', $event)"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import { Campaign } from '@/models';
const Filters = () => import('./Filters.vue')

export default Vue.extend({
  props: ['conditions'],
  components: { Filters },
  async created() {
    let currentLocationId = this.$router.currentRoute.params.location_id;
    const campaignIdOption = lodash.find(this.filterMaster, { value: 'campaign.id' });
    if (campaignIdOption) {
      const campaigns = await Campaign.getWithLocationId(currentLocationId);
      campaignIdOption.options = campaigns.map(campaign => {
        return {
          title: campaign.name,
          value: campaign.id
        }
      });
    }
  },
  data() {
    return {
      filterMaster: [
        {
          placeHolder: 'Select campaign',
          title: 'In campaign',
          value: 'campaign.id',
          valueType: 'text',
          type: 'select',
          options: [] as { [key: string]: any }[]
        },
        {
          placeHolder: 'Select event',
          title: 'Event',
          value: 'mailgun.event',
          valueType: 'text',
          type: 'select',
          options: [
            { title: 'Opened', value: 'opened' },
            { title: 'Clicked', value: 'clicked' },
            { title: 'Unsubscribed', value: 'unsubscribed' },
            { title: 'Complained(SPAM)', value: 'complained' },
            { title: 'Bounced - mailgun only', value: 'failed' }
          ]
        }
      ]
    }
  }
})
</script>
