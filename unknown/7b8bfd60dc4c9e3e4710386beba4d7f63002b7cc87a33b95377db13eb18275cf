<template>
  <div id="review-widget" class="card">
    <div class="card-header --no-right-padding">
      <h3 @click="fetchData()">Customize Review Widget</h3>
    </div>
    <div class="card-body" @click="toggelColorPicker()">
      <div class="two-part">
        <div>
          <div>
            <UITextLabel class="text-xl">Review Widget Display</UITextLabel>
          </div>
          <div class="mt-2">
            <form
              data-vv-scope="emailSettingsForm"
            >
              <div class="form-group">
                <UITextLabel>Header Title</UITextLabel>
                <UITextInputGroup
                  type="text"
                  data-lpignore="true"
                  placeholder="Header Title"
                  v-model="widgetSettings.headertext"
                  @keyup="changeHeaderText"
                />
              </div>
              <div class="form-group">
                <UITextLabel>Title Color</UITextLabel>
                <div
                  class="color-select"
                  @click.stop.prevent="toggelColorPicker('showtitlecolor')"
                  style="cursor: pointer"
                >
                  <p>{{ this.widgetSettings.titlecolor }}</p>
                  <div
                    class="color-selected"
                    :style="
                      'background-color: ' +
                      this.widgetSettings.titlecolor +
                      ';'
                    "
                  ></div>
                  <div class="color-picker" @click.stop>
                    <chrome-picker
                      :value="this.widgetSettings.titlecolor"
                      v-show="showtitlecolor"
                      @input="changeTitleColor"
                    />
                  </div>
                </div>
              </div>
              <div class="form-group">
                <UITextLabel>Rating Color</UITextLabel>
                <div
                  class="color-select"
                  @click.stop.prevent="toggelColorPicker('showratingcolor')"
                  style="cursor: pointer"
                >
                  <p>{{ this.widgetSettings.ratingcolor }}</p>
                  <div
                    class="color-selected"
                    :style="
                      'background-color: ' +
                      this.widgetSettings.ratingcolor +
                      ';'
                    "
                  ></div>
                  <div class="color-picker" @click.stop>
                    <chrome-picker
                      :value="this.widgetSettings.ratingcolor"
                      v-show="showratingcolor"
                      @input="changeRatingColor"
                    />
                  </div>
                </div>
              </div>
              <div class="form-group">
                <UITextLabel>Background Color</UITextLabel>
                <div
                  class="color-select"
                  @click.stop.prevent="toggelColorPicker('showbgcolor')"
                  style="cursor: pointer"
                >
                  <p>{{ this.widgetSettings.bgcolor }}</p>
                  <div
                    class="color-selected"
                    :style="
                      'background-color: ' + this.widgetSettings.bgcolor + ';'
                    "
                  ></div>
                  <div class="color-picker" @click.stop>
                    <chrome-picker
                      :value="this.widgetSettings.bgcolor"
                      v-show="showbgcolor"
                      @input="changeBgColor"
                    />
                  </div>
                </div>
              </div>
              <div class="form-group">
                <UITextLabel>Disable Powered By</UITextLabel>
                <div class="color-select">
                  <UIToggle
                    id="disable_powered_by"
                    v-model="widgetSettings.poweredbyDisabled"
                    @change="changePoweredBy"
                  />
                  <label
                    style="margin-bottom: 0"
                    class="tgl-btn"
                    for="disable_powered_by"
                    id="disable_powered_by_label"
                  ></label>
                </div>
              </div>
            </form>

            <form>
              <div class="form-group from-group--code">
                <div>
                  <div>
                    <label class="secondary-header secondary-header--saperator"
                      >Code</label
                    >
                  </div>

                  <label style="margin:0">Add the below code in your website.</label>
                  <span v-if="this.user.type === 'agency'">
                    Learn <a href="https://help.gohighlevel.com/support/solutions/articles/48000980328-reviews-review-requests-and-the-highlevel-review-widget" target="_blank" rel="noopener noreferrer">  
                    how to use Review Widget on your website or funnel
                    </a>
                  </span>
                </div>
                <div>
                  <span
                    id="copy-code-wrapper"
                    class="copy-code-wrapper noselect"
                    @click="copyToClipboard(widgetCode)"
                    ><span>Copy Code</span>
                    <i class="icon-copy-to-clipboard"></i>
                  </span>
                  <b-tooltip
                    triggers="click"
                    :show.sync="showCopiedCodeToolTip"
                    target="copy-code-wrapper"
                  >
                    Copied
                  </b-tooltip>
                </div>

                <UITextAreaGroup
                  rows="7"
                  data-lpignore="true"
                  class="code-textarea"
                  placeholder="code"
                  :disabled="true"
                  :value="widgetCode"
                ></UITextAreaGroup>
              </div>
            </form>
          </div>
        </div>
        <div>
          <EmulatorComponent :content="emulatorContent" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// @ts-nocheck
import Vue from 'vue'
import EmulatorComponent from '../EmulatorComponent.vue'
import { Chrome } from 'vue-color'
const Avatar = () => import('../Avatar.vue')
import libphonenumber from 'google-libphonenumber'
import {
  Company,
  Location,
  User,
  Notification,
  NotificationType,
} from '@/models'
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'
import defaults from '@/config'
import { WidgetSettings } from '@/models/location'
import { Chrome } from 'vue-color'
import firebase from 'firebase/app'

var phoneUtil = libphonenumber.PhoneNumberUtil.getInstance()
var PNF = libphonenumber.PhoneNumberFormat

export default Vue.extend({
  components: {
    Avatar,
    'chrome-picker': Chrome,
    EmulatorComponent,
  },
  data() {
    return {
      widgetSettings: {
        headertext: 'Customer Testimonials',
        bgcolor: '#ffffff',
        titlecolor: '#2a3135',
        ratingcolor: '#ffbc00',
        poweredbyDisabled: false,
      } as WidgetSettings,
      widget_url: '',
      base_url: defaults.baseUrl,
      tabIndex: 0 as number,
      showbgcolor: false,
      showratingcolor: false,
      showtitlecolor: false,
      location: undefined as undefined | Location,
      updateDebouncer: null,
      showCopiedCodeToolTip: false,
      updateTracker:new Date().getTime()
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.fetchData()
    },
    widgetSettings: function () {
      console.log('widget setting cahanged ')
    },
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    widgetCode: function () {
      let code = `<script type="text/javascript" src="${this.base_url}/js/reviews_widget.js"> <\/script><iframe id="msgsndr_reviews" src="${this.widget_url}" frameborder="0" scrolling="no" style="min-width: 100%;width: 100%;"></iframe>`
      return code
    },

    emulatorContent: function () {
      return {
        type: 'iframe-src',
        src: `${this.widget_url}?cachbust=${this.updateTracker}`,
        emulators: [
          { name: 'mobile' },
          { name: 'tablet' },
          { name: 'laptop', default: true },
        ],
      }
    },
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchData()
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  methods: {
    copyToClipboard(content) {
      this.clipboardCopy(content)
      setTimeout(() => (this.showCopiedCodeToolTip = false), 2000)
    },
    toggelColorPicker(which = null) {
      this.showtitlecolor = this.showbgcolor = this.showratingcolor = false
      switch (which) {
        case 'showtitlecolor':
          this.showtitlecolor = !this.showtitlecolor
          break
        case 'showbgcolor':
          this.showbgcolor = !this.showbgcolor
          break
        case 'showratingcolor':
          this.showratingcolor = !this.showratingcolor
          break
      }
    },
    async fetchData() {
      this.location = new Location(
        lodash.cloneDeep(
          await this.$store.dispatch(
            'locations/getCurrentLocation',
            this.currentLocationId
          )
        )
      )
      this.widgetSettings = {
        ...this.widgetSettings,
        ...this.location.settings.widget,
      }
      this.headerText =
        this.widgetSettings.headertext || 'Customer Testimonials'
      this.widget_url =
        defaults.baseUrl + '/reviews/get_widget/' + this.currentLocationId
    },
    makeActive(event, tab_number: number) {
      this.tabIndex = tab_number
      let children = $('.hl_view-select.nav').children()
      for (var item of children) {
        item.classList.remove('active')
      }
      children[tab_number].classList.add('active')
    },
    setSize(sizeType) {
      let t = $('#hl_visual-preview')
      if (sizeType == 'phone') {
        t.attr({ width: '375px', height: '700px' })
      } else if (sizeType == 'tablet') {
        t.attr({ width: '768px', height: '550px' })
      } else if (sizeType == 'desktop') {
        t.attr({
          width: '100%',
          height: '500px',
        })
      }
    },
    changeBgColor(newColor) {
      this.widgetSettings.bgcolor = newColor.hex
      this.saveLocation()
    },
    changeRatingColor(newColor) {
      this.widgetSettings.ratingcolor = newColor.hex
      this.saveLocation()
    },
    changeTitleColor(newColor) {
      this.widgetSettings.titlecolor = newColor.hex
      this.saveLocation()
    },
    changeHeaderText() {
      this.saveLocation()
    },
    changePoweredBy() {
      this.saveLocation()
    },
    async saveLocation() {
      if (this.updateDebouncer) {
        clearTimeout(this.updateDebouncer)
      }
      this.updateDebouncer = setTimeout(async () => {
       
        this.location.settings.widget = { ...this.widgetSettings }
        await this.location.ref.update({
          'settings.widget':this.location.settings.widget,
          date_updated: firebase.firestore.FieldValue.serverTimestamp(),
        })
        this.widget_url = `${defaults.baseUrl}/reviews/get_widget/${
          this.currentLocationId
        }`
         this.updateTracker = new Date().getTime()
      }, 200)
    },
  },
})
</script>
<style lang="scss" scoped>
#review-widget {
   .card-body {
      max-width: unset;
    }
    
  .two-part {
    display: grid;
    width: 100%;
    grid-template-columns: 1fr 1fr;
    grid-column-gap: 10px;

    & > div:first-child {
      border-right: 1px solid #eaeaea;
      padding-right: 20px;
    }

    .from-group--code {
      display: grid;
      grid-template-columns: 1fr auto;
      width: 100%;
      align-items: end;
      grid-row-gap: 2px;
      /* padding-top: 15px; */
      border-top: 1px solid #e0ecf3;
      margin-top: 30px;

      textarea {
        grid-column: 1/-1;
      }

      .secondary-header--saperator {
        font-size: 1.2rem;
        border-top: 1px solid #e0ecf3;
        margin-bottom: 0;
      }
    }

    .code-textarea {
      font-size: 0.8rem;
    }
    .color-select {
      display: grid;
      max-width: 180px;
      position: relative;
      grid-template-columns: 1fr auto auto;
      .color-picker {
        position: absolute;
        left: 110%;
        top: 0;
      }
    }

    .copy-code-wrapper {
      display: inline-flex;
      border: none;
      // width: 32px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      background: #e5f2fe;
      cursor: pointer;
      align-items: center;
      padding: 5px;
      border-radius: 5px;

      &:hover {
        opacity: 0.7;
      }

      &:active {
        opacity: 1;
      }
      span {
        font-size: 10px;
        margin-right: 5px;
      }
      margin-left: 10px;
    }
  }
}
</style>
