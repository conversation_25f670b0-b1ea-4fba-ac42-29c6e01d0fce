<template>
  <section class="hl_wrapper d-flex" :style="styles">
    <section class="hl_wrapper--inner form-builder-list email-builder">
      <EmailMicroApps :key="locationId" />
    </section>
  </section>
</template>

<script lang="ts">
import Vue from 'vue'
import EmailMicroApps from './EmailMicroApps.vue'
export default Vue.extend({
  components: { EmailMicroApps },
  computed: {
    locationId() {
      return this.$route.params.location_id
    },
    user() {
      return this.$store.state?.user?.user
    },
    styles() {
      return this.getSideBarVersion == 'v2'? 'padding-top: 95px !important;' : ''
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
})
</script>
<style>
.email-builder {
  width: 100%;
  background: #ffffff;
}
</style>
