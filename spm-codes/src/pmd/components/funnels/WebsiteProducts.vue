<template>
  <div>
    <section class="container-fluid">
      <div class="hl_funnels--wrap py-3 website-products">
        <div class="back-to-list-page" ><span @click="switchToListPage"><i  class="icon icon-arrow-left-2 mr-2"></i>Back to pages</span></div>
        <div class="py-4 bg-white px-3 rounded product-container">
          <FunnelStepProduct />
        </div>
      </div>
    </section>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import FunnelStepProduct from '@/pmd/pages/funnels/FunnelStepProduct.vue'
export default Vue.extend({
  components: {
    FunnelStepProduct,
  },
  methods:{
    switchToListPage(){
      this.$router.push({name:'website_pages'})
    }
  }
})
</script>

<style lang="scss">
.website-products {
  .back-to-list-page {
    padding: 10px 0px;
    cursor: pointer;
    color: #188bf6;
    align-items: center;
    display: flex;
  }
  h3 {
    font-size: 18px;
  }
  .product-container {
    min-height: 500px;
  }
}
</style>
