<template>
  <span class="email-receipient">
    <span class="name" v-if="name">{{ name }},</span>
    <i class="fa fa-user"></i>
    <a target="_blank" v-if="contactLink" :href="contactLink">
      {{ email }}
    </a>
    <span v-else>
      <i v-if="hideDeletedContact">&lt;Unknown&gt;</i>
      <span v-else>{{ email }}</span>
    </span>
  </span>
</template>
<script lang="ts">
import Vue from 'vue'
import { Contact } from '@/models'
export default Vue.extend({
  props: {
    data: {
      type: String,
      required: true,
    },
    hideDeletedContact: {
      type: Boolean,
      default: true,
      required: false,
    },
  },
  created() {
    this.locationId = this.$router.currentRoute.params.location_id
  },
  async mounted() {
    const starting = this.data.indexOf('<')
    const ending = this.data.indexOf('>')
    if (starting > -1 && ending > -1) {
      this.email = this.data.slice(starting + 1, ending)
      this.name = this.data.slice(0, starting).trim()
    } else {
      this.email = this.data
    }

    if (this.email) {
      const contact = await Contact.getByEmail(this.email, this.locationId)
      if (contact) {
        this.contactLink = `${window.origin}/location/${this.locationId}/customers/detail/${contact.id}`
      } else {
        // this.contactLink = `mailto:${this.email}`
      }
    }
  },
  data() {
    return {
      email: '',
      name: '',
      contactLink: '',
      locationId: '',
    }
  },
})
</script>
<style lang="scss" scoped>
.email-receipient {
  font-size: 14px;

  .name {
    margin-right: 2px;
  }

  a:hover {
    text-decoration: underline;
  }

  i {
    color: var(--primary);
  }
}
</style>
