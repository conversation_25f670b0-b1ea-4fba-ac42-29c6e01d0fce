import { Modu<PERSON>, ActionContext } from 'vuex'
import { RootState, ProductsState } from './state_models'

export const products: Module<ProductsState, RootState> = {
  namespaced: true,
  state: { products:[]},
  getters: {
  },
  mutations: {
    updateProducts: (
      state: ProductsState,
      products
    ) => {
      state.products = products
    },
    resetProducts: (state: ProductsState) => {
      state.products = []
    },
  },
  actions: {
    updateProducts(
      context: ActionContext<ProductsState, RootState>,
      products
    ) {
      context.commit('updateProducts', products)
    },
    resetProducts(
      context: ActionContext<ProductsState, RootState>
    ) {
      context.commit('resetProducts')
    },
  },
}
