import { RouteConfig, Route, RawLocation } from 'vue-router'

import keys from 'lodash/keys'
import first from 'lodash/first'
import isEmpty from 'lodash/isEmpty'

import store from '../store'
import { AuthUser, Location, User, CustomerType, Company } from '../models'
import EmptyRouterParent from '@/pmd/pages/EmptyRouterParent.vue'
import LaunchpadMicroApp from '@/pmd/pages/LaunchpadMicroApp.vue'
/*** Permissions ***/
import {
  routesRequiredPermissions,
  locationLevelRoutesPermissions,
  paymentBetaAccessRoutes
} from '@/routes/permissions'
// ====================================================== //
const CustomMenuLinkPage = () =>
  import(
    /* webpackChunkName: "custom-menu-link" */ '@/pmd/pages/agency/CustomMenuLinkPage.vue'
  )
// ========================================================== //
/*** Agency Level Settings Components: ***/
const ProfileSettings = () =>
  import(
    /* webpackChunkName: "profile-settings" */ '@/pmd/pages/settings/ProfileSettingsPage.vue'
  )
const ProfileSettingsV3 = () =>
  import(
    /* webpackChunkName: "profile-settings" */ '@/pmd/pages/settings/ProfileSettingsV3Page.vue'
  )
const AccountTeamManagement = () =>
  import(
    /* webpackChunkName: "team-management" */ '@/pmd/pages/agency/AccountTeamManagement.vue'
  ).then(m => m.default)
const AccountTeamsManagement = () =>
  import(
    /* webpackChunkName: "team-management" */ '@/pmd/pages/agency/AccountTeamsManagement.vue'
  ).then(m => m.default)
const AccountBilling = () =>
  import(
    /* webpackChunkName: "agency-billing" */ '@/pmd/pages/agency/billing/Billing.vue'
  ).then(m => m.default)
const AccountCustomFieldsSettings = () =>
  import(
    /* webpackChunkName: "custom-fields-settings" */ '@/pmd/pages/agency/AgencyCustomFieldsSettings.vue'
  ).then(m => m.default)

// ====================================================== //
/*** Report Adwords Components: ***/
const ReportingAdwordsIndex = () =>
  import(
    /* webpackChunkName: "adwords-reporting" */ '../pmd/pages/reporting/adwords/ReportingAdwordsIndex.vue'
  ).then(m => m.default)
const AdwordsCampaigns = () =>
  import(
    /* webpackChunkName: "adwords-reporting" */ '../pmd/pages/reporting/adwords/AdwordsCampaigns.vue'
  ).then(m => m.default)
const AdwordsAdgroups = () =>
  import(
    /* webpackChunkName: "adwords-reporting" */ '../pmd/pages/reporting/adwords/AdwordsAdgroups.vue'
  ).then(m => m.default)
const AdwordsAds = () =>
  import(
    /* webpackChunkName: "adwords-reporting" */ '../pmd/pages/reporting/adwords/AdwordsAds.vue'
  ).then(m => m.default)
const AdwordsKeywords = () =>
  import(
    /* webpackChunkName: "adwords-reporting" */ '../pmd/pages/reporting/adwords/AdwordsKeywords.vue'
  ).then(m => m.default)
const AdwordsConversions = () =>
  import(
    /* webpackChunkName: "adwords-reporting" */ '../pmd/pages/reporting/adwords/AdwordsConversions.vue'
  ).then(m => m.default)

// ========================================================== //
/*** Location Level Components: ***/
// const SideBar = () => import("@/pmd/components/locations/LocationSideBar.vue").then(m => m.default)
const LocationParent = () =>
  import(/* webpackChunkName: "location" */ '@/pmd/pages/LocationParent.vue')

const DashboardPage = () =>
  import(
    /* webpackChunkName: "dashboard" */ '../pmd/pages/DashboardPage.vue'
  ).then(m => m.default)
const AppointmentsPage = () =>
  import(
    /* webpackChunkName: "appointments" */ '../pmd/pages/AppointmentsPage.vue'
  ).then(m => m.default)
const AppointmentsReportPage = () =>
  import(
    /* webpackChunkName: "appointments-reports" */ '@/pmd/pages/AppointmentsReportPage.vue'
  ).then(m => m.default)

const NotificationsPage = () =>
  import(
    /* webpackChunkName: "notifications" */ '../pmd/pages/NotificationsPage.vue'
  ).then(m => m.default)

const NotificationsList = () =>
  import(
    /* webpackChunkName: "notification-list" */ '../pmd/pages/NotificationsList.vue'
  ).then(m => m.default)

const RemindersPage = () =>
  import(
    /* webpackChunkName: "reminders" */ '../pmd/pages/RemindersPage.vue'
  ).then(m => m.default)

// const CalendarPage = () => import("@/pmd/pages/CalendarPage.vue").then(m => m.default)
const ConversationsPage = () =>
  import(
    /* webpackChunkName: "conversations" */ '@/pmd/pages/ConversationsPage.vue'
  ).then(m => m.default)
// const ProfilePage = () => import("@/pmd/pages/ProfilePage.vue").then(m => m.default)
const AnalysisPage = () =>
  import(
    /* webpackChunkName: "analysis-p" */ '@/pmd/pages/AnalysisPage.vue'
  ).then(m => m.default)
const ReviewsAllPage = () =>
  import(
    /* webpackChunkName: "reviews-p" */ '@/pmd/pages/reputation/tabs/ReviewsAllPage.vue'
  ).then(m => m.default)
const ReputationLandingPage = () =>
  import(
    /* webpackChunkName: "reputation-landing-page" */ '@/pmd/pages/reputation/ReputationLandingPage.vue'
  ).then(m => m.default)

const ReputationSetingsPage = () =>
  import(
    /* webpackChunkName: "reputation-settings-page" */ '@/pmd/pages/reputation/tabs/ReputationSettingsPage.vue'
  ).then(m => m.default)

const ReputationOverviewPage = () =>
  import(
    /* webpackChunkName: "reputation-settings-page" */ '@/pmd/pages/reputation/tabs/ReputationOverviewPage.vue'
  ).then(m => m.default)

const ReputationOnlineListing = () =>
  import(
    /* webpackChunkName: "reputation-online-listing-page" */ '@/pmd/pages/reputation/tabs/OnlineListingPage.vue'
  ).then(m => m.default)

const ReviewRequestsAllPage = () =>
  import(
    /* webpackChunkName: "review-requests" */ '@/pmd/pages/reputation/tabs/ReviewRequestsAllPage.vue'
  ).then(m => m.default)
const NumberPoolDashboard = () =>
  import(
    /* webpackChunkName: "numberpool-dashboard" */ '@/pmd/pages/NumberPoolDashboard.vue'
  ).then(m => m.default)
const AgentReporting = () =>
  import(
    /* webpackChunkName: "agent-reporting" */ '@/pmd/pages/AgentReporting.vue'
  ).then(m => m.default)
const CustomersPage = () =>
  import(
    /* webpackChunkName: "customers-p" */ '@/pmd/pages/CustomersPage.vue'
  ).then(m => m.default)
const ManualActionList = () =>
  import(
    /* webpackChunkName: "manual-actions" */ '@/pmd/pages/ManualActionList.vue'
  ).then(m => m.default)
const SmartList = () =>
  import(
    /* webpackChunkName: "smartlist" */ '@/pmd/pages/smartlist/SmartList.vue'
  ).then(m => m.default)
const LeadsPage = () =>
  import(/* webpackChunkName: "leads" */ '@/pmd/pages/LeadsPage.vue').then(
    m => m.default
  )
const CustomerDetailNew = () =>
  import(
    /* webpackChunkName: "customer-details" */ '@/pmd/pages/customers/ContactDetailNew.vue'
  )
const OpportunitiesPage = () =>
  import(
    /* webpackChunkName: "opportunities" */ '@/pmd/pages/OpportunitiesPage.vue'
  )
const CustomerImportsPage = () =>
  import(
    /* webpackChunkName: "customer-imports" */ '@/pmd/pages/customers/ContactBulkRequests.vue'
  )
const BulkRequest = () =>
  import('@/pmd/pages/customers/CustomerImportsPage.vue')

const CustomerRestore = () =>
  import('@/pmd/pages/customers/CustomerRestore.vue')

const BulkActionsPage = () =>
  import(
    /* webpackChunkName: "customer-imports" */ '@/pmd/pages/smartlist/BulkActionsList.vue'
  )
const BulkActionsView = () =>
  import(
    /* webpackChunkName: "customer-imports" */ '@/pmd/pages/smartlist/BulkActionsView.vue'
  )
// ========================================================== //
/*** Location Level Settings Components: ***/
const CustomValues = () =>
  import(
    /* webpackChunkName: "location-custom-values" */ '@/pmd/pages/settings/CustomValues.vue'
  ).then(m => m.default)
const CompanySettingsPage = () =>
  import(
    /* webpackChunkName: "location-company-settings" */ '@/pmd/pages/settings/CompanySettingsPage.vue'
  ).then(m => m.default)
const LocationBillingPage = () =>
  import(
    /* webpackChunkName: "location-company-settings" */ '@/pmd/pages/settings/LocationBillingPage.vue'
  ).then(m => m.default)
const LocationBillingSettings = () =>
  import('@/pmd/pages/settings/LocationBillingSettings.vue').then(
    m => m.default
  )
const LocationWalletTransactions = () =>
  import('@/pmd/pages/settings/LocationWalletTransactions.vue').then(
    m => m.default
  )
const IntegrationSettingsPage = () =>
  import(
    /* webpackChunkName: "location-integration-settings" */ '@/pmd/pages/settings/IntegrationSettingsPage.vue'
  )
const SocialPostingSettings = () =>
  import(
    /* webpackChunkName: "location-social-settings" */ '@/pmd/pages/settings/SocialPostingSettings.vue'
  ).then(m => m.default)
const ReviewPopupSettings = () =>
  import(
    /* webpackChunkName: "location-review-settings-m" */ '@/pmd/pages/settings/ReviewPopupSettings.vue'
  ).then(m => m.default)
const PipelineSettings = () =>
  import(
    /* webpackChunkName: "location-pipeline-settings" */ '@/pmd/pages/settings/PipelineSettings.vue'
  ).then(m => m.default)
const PaymentSettings = () =>
  import(
    /* webpackChunkName: "location-payment-settings" */ '@/pmd/pages/settings/PaymentSettings.vue'
  ).then(m => m.default)
const AppointmentWidgetSettings = () =>
  import(
    /* webpackChunkName: "location-appointment-settings" */ '@/pmd/pages/settings/AppointmentWidgetSettings.vue'
  )
const ChatWidgetSettings = () =>
  import(
    /* webpackChunkName: "location-chat-settings" */ '@/pmd/pages/settings/ChatWidgetSettings.vue'
  ).then(m => m.default)
const NoShowSMSSettings = () =>
  import(
    /* webpackChunkName: "location-nosms-settings" */ '@/pmd/pages/settings/NoShowSMSSettings.vue'
  ).then(m => m.default)
const BirthdaySMSSettings = () =>
  import(
    /* webpackChunkName: "location-birthday-sms-settings" */ '@/pmd/pages/settings/BirthdaySMSSettings.vue'
  ).then(m => m.default)
const AllPhoneNumbersSettings = () =>
  import(
    /* webpackChunkName: "location-phone-number-settings" */ '@/pmd/pages/settings/AllPhoneNumbersSettings.vue'
  ).then(m => m.default)
// const BulkEmailSettings = () =>
//   import('@/pmd/pages/settings/BulkEmailSettings.vue').then(m => m.default)

const LocationMailGunSettings = () =>
  import(
    /* webpackChunkName: "location-mailgun-settings" */ '@/pmd/pages/settings/LocationMailGunSettings.vue'
  )

const CalendarSettings = () =>
  import(
    /* webpackChunkName: "location-calendar-settings" */ '@/pmd/pages/settings/CalendarSettings.vue'
  ).then(m => m.default)
const TemplateSettings = () =>
  import(
    /* webpackChunkName: "location-template-settings" */ '@/pmd/pages/settings/TemplateMessageSettings.vue'
  )
const SocialMediaDashboard = () =>
  import(
    /* webpackChunkName: "social-media-dashboard" */ '@/pmd/pages/social_media/SocialMediaDashboard.vue'
  ).then(m => m.default)
const CampaignList = () =>
  import(
    /* webpackChunkName: "m-campaign-list" */ '@/pmd/pages/marketing/CampaignList.vue'
  ).then(m => m.default)
const CampaignNew = () =>
  import(
    /* webpackChunkName: "m-campaign-new" */ '@/pmd/pages/marketing/CampaignNew.vue'
  ).then(m => m.default)
const CampaignDetail = () =>
  import(
    /* webpackChunkName: "m-campaign-detail" */ '@/pmd/pages/marketing/CampaignDetail.vue'
  ).then(m => m.default)
const CampaignActivity = () =>
  import(
    /* webpackChunkName: "m-campaign-activity" */ '@/pmd/pages/marketing/CampaignActivity.vue'
  ).then(m => m.default)
const CampaignAccountsList = () =>
  import(
    /* webpackChunkName: "m-campaign-acc-list" */ '@/pmd/pages/marketing/CampaignAccountsList.vue'
  ).then(m => m.default)
const BulkCampaignsPage = () =>
  import(
    /* webpackChunkName: "m-bulk-campaigns" */ '@/pmd/pages/marketing/BulkCampaignsPage.vue'
  ).then(m => m.default)
const FormBuilderPage = () =>
  import(
    /* webpackChunkName: "m-form-builder" */ '@/pmd/pages/marketing/FormBuilderPage.vue'
  ).then(m => m.default)
const EmailBuilderPage = () =>
  import(
    /* webpackChunkName: "m-email-builder" */ '@/pmd/pages/emailBuilder/EmailBuilderListPage.vue'
  )
const Emails = () => import('@/pmd/pages/emailBuilder/Emails.vue')
const TextBuilder = () =>
  import(
    /* webpackChunkName: "m-text-builder" */ '@/pmd/pages/marketing/TextTemplates.vue'
  ).then(m => m.default)

const BuilderCreateForm = () =>
  import(
    /* webpackChunkName: "m-builder-create-from" */ '@/pmd/pages/marketing/BuilderCreateForm.vue'
  ).then(m => m.default)

const BuilderCreateFormOld = () =>
  import(
    /* webpackChunkName: "m-builder-create-from-old" */ '@/pmd/pages/marketing/BuilderCreateFormOld.vue'
  ).then(m => m.default)


const EmbedBuilder = () =>
  import(
    /* webpackChunkName: "m-email-builder" */ '@/pmd/pages/marketing/EmbedBuilder.vue'
  ).then(m => m.default)

const FacebookAdFieldsMappingSettings = () =>
  import(
    /* webpackChunkName: "form-builder-mapping-settings" */ '@/pmd/pages/settings/FacebookAdFieldsMappingSettings.vue'
  )

const DomainSettings = () =>
  import(
    /* webpackChunkName: "domain-settings" */ '@/pmd/pages/settings/DomainSettings.vue'
  ).then(m => m.default)

const RedirectSettings = () =>
import(
  '@/pmd/pages/settings/RedirectSettings.vue'
).then(m => m.default)

const TagsSettings = () =>
  import(
    /* webpackChunkName: "tags-settings" */ '@/pmd/pages/settings/TagsSettings.vue'
  ).then(m => m.default)

const SMTPServiceSettings = () =>
  import(
    /* webpackChunkName: "smpt-settings" */ '@/pmd/pages/settings/SMTPServiceSettings.vue'
  ).then(m => m.default)

const EmbedSurvey = () =>
  import(
    /* webpackChunkName: "m-embed-survey" */ '@/pmd/pages/marketing/EmbedSurvey.vue'
  ).then(m => m.default)

const SurveyList = () =>
  import(
    /* webpackChunkName: "m-survey-list" */ '@/pmd/pages/marketing/SurveyList.vue'
  ).then(m => m.default)

const BuilderCreateSurvey = () =>
  import(
    /* webpackChunkName: "m-builder-create-survey" */ '@/pmd/pages/marketing/BuilderCreateSurvey.vue'
  ).then(m => m.default)

const JotboxForm = () =>
  import('@/pmd/pages/marketing/JotboxForm.vue').then(m => m.default)

const JotboxSurvey = () =>
  import('@/pmd/pages/marketing/JotboxSurvey.vue').then(m => m.default)

const FunnelsPage = () =>
  import(
    /* webpackChunkName: "funnels" */ '@/pmd/pages/funnels/FunnelsPage.vue'
  )
const FunnelsList = () =>
  import(
    /* webpackChunkName: "funnels-list" */ '@/pmd/pages/funnels/FunnelsList.vue'
  )

// import FunnelDetailPage from '@/pmd/pages/funnels/FunnelDetailPage.vue'
const FunnelDetailPage = () =>
  import(
    /* webpackChunkName: "funnel-details" */ '@/pmd/pages/funnels/FunnelDetailPage.vue'
  )
const FunnelSteps = () =>
  import(
    /* webpackChunkName: "funnel-steps" */ '@/pmd/pages/funnels/FunnelSteps.vue'
  )
const FunnelStats = () =>
  import(
    /* webpackChunkName: "funnel-stats" */ '@/pmd/pages/funnels/FunnelStats.vue'
  )
const FunnelSales = () =>
  import(
    /* webpackChunkName: "funnel-sales" */ '@/pmd/pages/funnels/FunnelSales.vue'
  )
const FunnelSettings = () =>
  import(
    /* webpackChunkName: "funnel-settings" */ '@/pmd/pages/funnels/FunnelSettings.vue'
  )

const WebsiteProducts = () =>
  import(
    /* webpackChunkName: "website-products" */ '@/pmd/components/funnels/WebsiteProducts.vue'
  )

const FunnelStepOverview = () =>
  import(
    /* webpackChunkName: "funnel-step-overview" */ '@/pmd/pages/funnels/FunnelStepOverview.vue'
  )
const FunnelStepAutomation = () =>
  import(
    /* webpackChunkName: "funnel-step-automation" */ '@/pmd/pages/funnels/FunnelStepAutomation.vue'
  )
const FunnelStepPublish = () =>
  import(
    /* webpackChunkName: "funnel-step-publish" */ '@/pmd/pages/funnels/FunnelStepPublish.vue'
  )
const FunnelStepProduct = () =>
  import(
    /* webpackChunkName: "funnel-step-product" */ '@/pmd/pages/funnels/FunnelStepProduct.vue'
  )

const FunnelStepProductV2 = () =>
  import(
    /* webpackChunkName: "funnel-step-product" */ '@/pmd/pages/funnels/FunnelStepProductV2.vue'
  )

  const FunnelProductsList = () =>
  import('@/pmd/pages/funnels/FunnelProductsList.vue')


const TriggersPage = () =>
  import(
    /* webpackChunkName: "triggers" */ '@/pmd/pages/triggers/TriggersPage.vue'
  )

const TriggerDetailPage = () =>
  import(
    /* webpackChunkName: "trigger-detail" */ '@/pmd/pages/triggers/TriggerDetailPage.vue'
  )

const LinksPage = () =>
  import(
    /* webpackChunkName: "m-links" */ '@/pmd/pages/marketing/LinksPage.vue'
  )

// import TeamSettingsPage from '@/pmd/pages/settings/TeamSettingsPage.vue';
import CalendarV2Page from '@/pmd/pages/CalendarV2Page.vue'
// const CalendarV2Page = () =>
//   import(/* webpackChunkName: "calendar-v2" */ '@/pmd/pages/CalendarV2Page.vue')

/*** Website-specific Components: ***/
const WebsiteTemplateCategory = () =>
  import(
    /* webpackChunkName: "website-template-category" */ '@/pmd/pages/agency/WebsiteTemplateCategory.vue'
  )
const WebsiteTemplateCategoryList = () =>
  import(
    /* webpackChunkName: "website-template-cat-list" */ '@/pmd/pages/agency/WebsiteTemplateCategoryList.vue'
  )
const WebsiteTemplateCategoryDetail = () =>
  import(
    /* webpackChunkName: "website-template-cat-details" */ '@/pmd/pages/agency/WebsiteTemplateCategoryDetail.vue'
  )
const WebsiteTemplateDetail = () =>
  import(
    /* webpackChunkName: "website-template-details" */ '@/pmd/pages/agency/WebsiteTemplateDetail.vue'
  )

const ChooseWebsiteTemplateCategory = () =>
  import(
    /* webpackChunkName: "website-template-category-select" */ '@/pmd/pages/funnels/ChooseWebsiteTemplateCategory.vue'
  )
const ChooseWebsiteTemplateCategoryDetail = () =>
  import(
    /* webpackChunkName: "website-template-cat-details-select" */ '@/pmd/pages/funnels/ChooseWebsiteTemplateCategoryDetail.vue'
  )
const UserWebsiteTemplateDetail = () =>
  import(
    /* webpackChunkName: "user-website-template-details" */ '@/pmd/pages/funnels/UserWebsiteTemplateDetail.vue'
  )
const oldEmailBuilder = () =>
  import('@/pmd/pages/emailBuilder/BuilderDashboard.vue')


const workflowBuilder = () =>
  import('@/pmd/pages/workflow/WorkflowMicroApp.vue')

const membershipBuilder = () =>
  import('@/pmd/pages/membership/MembershipMicroApp.vue')

// ========================================================== //
/*** FB Reporting Components: ***/
const ReportingFacebookIndex = () =>
  import(
    /* webpackChunkName: "fb-reporting" */ '@/pmd/pages/reporting/facebook/ReportingFacebookIndex.vue'
  ).then(m => m.default)
const ReportingFacebookCampaigns = () =>
  import(
    /* webpackChunkName: "fb-reporting" */ '@/pmd/pages/reporting/facebook/ReportingFacebookCampaigns.vue'
  ).then(m => m.default)
const ReportingFacebookAdsets = () =>
  import(
    /* webpackChunkName: "fb-reporting" */ '@/pmd/pages/reporting/facebook/ReportingFacebookAdsets.vue'
  ).then(m => m.default)
const ReportingFacebookAds = () =>
  import(
    /* webpackChunkName: "fb-reporting" */ '@/pmd/pages/reporting/facebook/ReportingFacebookAds.vue'
  ).then(m => m.default)

const ReportingFacebookDemographics = () =>
  import(
    /* webpackChunkName: "fb-reporting" */ '@/pmd/pages/reporting/facebook/ReportingFacebookDemographics.vue'
  ).then(m => m.default)

const ReportingAttribution = () =>
  import(
    /* webpackChunkName: "fb-reporting" */ '@/pmd/pages/reporting/attribution/ReportingAttribution.vue'
  ).then(m => m.default)
const ElizaFAQSetting = () =>
  import(
    /* webpackChunkName: "bot-faq" */ '@/pmd/pages/agency/ElizaFAQSetting.vue'
  ).then(m => m.default)
const TextWidgetSettingsPage = () =>
  import(
    /* webpackChunkName: "textWidgetSetting" */ '@/pmd/pages/settings/textWidgetSetting.vue'
  )
const PaymentsPage = () =>
  import(
    /* webpackChunkName: "Payments" */ '@/pmd/pages/payments/PaymentsPage.vue'
  )

const Products = () =>
  import(
    /* webpackChunkName: "Products" */ '@/pmd/pages/payments/Products.vue'
)

const ProductsPage = () =>
  import(
    /* webpackChunkName: "PaymentsPage" */ '@/pmd/pages/payments/ProductsPage.vue'
  )
const CreateProduct = () =>
  import(
    /* webpackChunkName: "CreateProduct" */ '@/pmd/pages/payments/CreateProduct.vue'
  )

const ImportStripeProduct = () => import(/* webpackChunkName: "ImportStripeProduct" */ '@/pmd/pages/payments/ImportStripeProduct.vue')

const ProductDetails = () =>
  import(
    /* webpackChunkName: "ProductDetails" */ '@/pmd/pages/payments/ProductDetails.vue'
  )


const Transactions = () =>
  import(
    /* webpackChunkName: "Transactions" */ '@/pmd/pages/payments/Transactions.vue'
  )
const WordpressDashboard = () =>
  import(
    /* webpackChunkName: "wordpress-dashboard" */ '@/pmd/pages/WordpressDashboard.vue'
  )
const PaymentIntegration = () =>
  import('@/pmd/pages/payments/PaymentIntegration.vue')

const PaymentIntegrationCallback = () =>
  import('@/pmd/pages/payments/PaymentIntegrationCallback.vue')

const LogViewer = () =>
    import('@/pmd/pages/payments/LogViewer.vue')

const LogsPage = () =>
  import('@/pmd/pages/payments/LogsPage.vue')

// media files
import MediaSettings  from '@/pmd/pages/medias/MediaSettings.vue'

// ========================================================== //
/*** Location Level Launchpad ***/
const LaunchpadMobileAppLink = () =>
  import(
    /* webpackChunkName: "LaunchpadMobileAppLink" */ '@/pmd/components/launchpad/LaunchpadMobileAppLink.vue'
  )

const _requireAccessLevelAndLocation = (
  to: Route,
  from: Route,
  next: (to?: RawLocation) => void
) => {
  store
    .dispatch('auth/get')
    .then(async (auth: AuthUser) => {
      const user = new User(await store.dispatch('user/get'))
      if (user.type === User.TYPE_ACCOUNT && user.role === User.ROLE_USER) {
        const locationId = first(keys(user.locations)) || ''
        const company = await store.dispatch('company/get')
        const sidebarVersion = store.getters['sidebarv2/getVersion']
        if (sidebarVersion != 'v2') {
          return next({
            name: 'dashboard',
            replace: true,
            params: { location_id: locationId },
          })
        } else {
          return next({
            name: 'dashboard-v2',
            replace: true,
            params: { location_id: locationId },
          })
        }
      } else {
        next()
      }
    })
    .catch(err => {
      next({
        name: 'login',
        query: { url: encodeURIComponent(to.fullPath) },
        replace: true,
      })
    })
}

let firstLoad = true

const requireAuthAndLocation = (
  to: Route,
  from: Route,
  next: (to?: RawLocation) => void
) => {
  store
    .dispatch('auth/get')
    .then(async (auth: AuthUser) => {
      const user = new User(await store.dispatch('user/get'))
      const permissions = user.permissions
      await inactiveAuth(to, from, next)

      const requiredPermission = routesRequiredPermissions[to.name]

      if (to.name == 'location-v2') {
        return next({
          name: 'dashboard-v2',
          params: to.params,
          query: to.query,
          replace: true,
        })
      }
      if (permissions[requiredPermission] === false) {
        if (to.name == 'dashboard-v2') {
          return next({
            name: 'conversations-id-v2',
            params: to.params,
            query: to.query,
            replace: true,
          })
        } else if (to.name !== 'conversations-id-v2') {
          return next({
            name: 'dashboard-v2',
            params: to.params,
            query: to.query,
            replace: true,
          })
        } else {
          return next({
            path: from.path,
            replace: true,
          })
        }
      }

      const company = await store.dispatch('company/get')
      const sidebarVersion = store.getters['sidebarv2/getVersion']
      if (
        company &&
        company.onboardingInfo.pending === true &&
        company.status !== 'active_test-agency' &&
        user &&
        user.type === 'agency'
      ) {
        return next({
          name: 'onboarding_micro_app',
          replace: true,
        })
      }
      if (!to.params.location_id) {
        if (
          company.customerType === CustomerType.AGENCY &&
          user.type === User.TYPE_AGENCY
        ) {
          if (
            company.saasSettings &&
            company.saasSettings.agency_dashboard_visible_to === 'individual'
              ? user.saasSettings && user.saasSettings.agency_dashboard_visible
              : user.role === User.ROLE_ADMIN
          ) {
            return next({ name: 'agency_dashboard', replace: true })
          } else {
            return next({ name: 'accounts_list', replace: true })
          }
        } else if (!isEmpty(user.locations)) {
          // This is an location level user who only has access to certain locations.
          const locationId = first(keys(user.locations)) || ''
          if (sidebarVersion != 'v2') {
            return next({
              name: 'dashboard',
              replace: true,
              params: { location_id: locationId },
            })
          } else {
            return next({
              name: 'dashboard-v2',
              replace: true,
              params: { location_id: locationId },
            })
          }

        } else {
          // Direct accounts will come into this case.
          const locations = await Location.fetchAllLocations()
          if (locations.length > 0) {
            return next({
              name: 'dashboard-v2',
              replace: true,
              params: { location_id: locations[0].id },
            })
          }
        }
        return
      } else {
        if (user.type === User.TYPE_ACCOUNT) {
          const availableLocations = keys(user.locations) || []
          if (availableLocations.indexOf(to.params.location_id) === -1) {
            return next({
              path: '/',
              replace: true,
            })
          }
        }
        if (sidebarVersion != 'v2') {
          return next({
            name: 'dashboard',
            replace: true,
            params: { location_id: to.params.location_id },
          })
        }
        const location = await store.dispatch(
          'locations/getById',
          to.params.location_id
        )

        if (firstLoad) {
          firstLoad = false
          await store.dispatch(
            'auth/refreshFirebaseToken',
            { locationId: to.params.location_id || null, refresh: true },
            { root: true }
          )
          await Promise.all([
            store.dispatch('locations/resetCurrentLocation', {
              locationId: to.params.location_id,
            }),
            store.dispatch('users/syncAll', to.params.location_id),
            store.dispatch('campaigns/syncAll', to.params.location_id),
            store.dispatch('workflows/syncAll', to.params.location_id),
            store.dispatch('oauth2/syncAll', to.params.location_id),
            store.dispatch('contacts/syncAll', to.params.location_id),
            store.dispatch('calendars/syncAll', to.params.location_id),
            store.dispatch('userCalendars/syncAll', to.params.location_id),
            store.dispatch('linkedCalendars/syncAll', {
              userId: user.id,
              locationId: to.params.location_id,
            }),
            store.dispatch('teams/syncAll', to.params.location_id),

            store.dispatch('smtpServices/syncAll', to.params.location_id),
            store.dispatch('mailgunServices/syncAll', to.params.location_id),
            store.dispatch('stripeConnect/syncAll', to.params.location_id),
            store.dispatch('membership/syncAll', { locationId: to.params.location_id }),
          ])
          store.dispatch('filters/syncAll', to.params.location_id)
        }

        const locationLevelPermissionKey = locationLevelRoutesPermissions[
          to.name
        ]
          ? locationLevelRoutesPermissions[to.name]
          : requiredPermission

        let locationHasPermission = true

        if (location.permissions) {
          if (Array.isArray(locationLevelPermissionKey)) {
            const keyPermissions = locationLevelPermissionKey.filter(
              key => location.permissions[key] === true
            )
            // location has permission for any of the key in the list for route
            locationHasPermission = keyPermissions.length > 0
          } else {
            locationHasPermission =
              location.permissions[locationLevelPermissionKey] !== false
          }
        }

        if (
          !location ||
          location.company_id !== company.id ||
          location.deleted ||
          !locationHasPermission
        ) {
          if (
            location.permissions &&
            !location.permissions[locationLevelRoutesPermissions.dashboard]
          ) {
            if (to.name === 'dashboard-v2') {
              return next({
                name: 'conversations-id-v2',
                params: to.params,
                replace: true,
              })
            } else {
              return next({
                path: from.path,
                replace: true,
              })
            }
          } else {
            return next({
              name: 'dashboard-v2',
              replace: true,
              params: { location_id: to.params.location_id },
            })
          }
        }
      }
      const allowNewPaymentsAccess = store.getters['company/isAbove97Plan']
      if(!allowNewPaymentsAccess && paymentBetaAccessRoutes.includes(to.name)) {
        return next({
          name: 'payment-integrations-v2',
          replace: true,
          params: { location_id: to.params.location_id },
        })
      }

      next()
    })
    .catch(err => {
      next({
        name: 'login',
        query: { url: encodeURIComponent(to.fullPath) },
        replace: true,
      })
    })
}

const forceLockedTimestamp = 1607990400 // 12/15/2020 @ 12:00am (UTC)

const inactiveAuth = async (
  to: Route,
  from: Route,
  next: (to?: RawLocation) => void
) => {
  const company = await store.dispatch('company/get')
  const user = new User(await store.dispatch('user/get'))
  const lockoutSkippedTimestamp = localStorage.getItem('lockoutSkipped')

  const lockoutSkipped = lockoutSkippedTimestamp
    ? parseInt(lockoutSkippedTimestamp) + 24 * 60 * 60 * 1000 > +new Date()
    : false

  // 1. check if location-level user
  if (user.type === User.TYPE_ACCOUNT) {
    if (
      company.status &&
      company.status.includes('inactive') &&
      +new Date() > forceLockedTimestamp * 1000
    ) {
      if (to.name === 'inactive_location') {
        // to prevent infinite loop
        next()
      } else {
        next({ name: 'inactive_location', replace: true })
      }
    } else {
      // continue
      return
    }
  } else if (!lockoutSkipped) {
    // 2. Agency Level user
    // 2.1 Lock-out with `active_failed-payment` case
    if (
      company.status &&
      company.status === 'active_failed-payment' &&
      company._data &&
      company._data.date_inactivation
    ) {
      // 2.1.1 Date of inactivation must be within next 7 days.
      const msLeftForInactivation =
        company._data.date_inactivation.seconds * 1000 - +new Date()
      if (
        msLeftForInactivation > 0 &&
        msLeftForInactivation < 7 * 24 * 60 * 60 * 1000
      ) {
        // 7 days
        if (to.name === 'inactive' && to.query.type === 'failed-payment') {
          // to prevent infinite loop
          next()
        } else {
          next({
            name: 'inactive',
            replace: true,
            query: { type: 'failed-payment' },
          })
        }
      }
    }
    // 2.2 Lock-out with all other `inactive` cases
    if (company.status && company.status.includes('inactive')) {
      if (to.name === 'inactive') {
        // to prevent infinite loop
        next()
      } else {
        next({ name: 'inactive', replace: true })
      }
    } else {
      // continue
      return
    }
  }
  return
}

export const v2: RouteConfig[] = [
  {
    path: '/v2/location/:location_id',
    name: 'location-v2',
    component: LocationParent,
    beforeEnter: requireAuthAndLocation,
    children: [
      {
        path: 'dashboard',
        name: 'dashboard-v2',
        component: DashboardPage,
      },
      {
        path: 'launchpad',
        name: 'location-launchpad-v2',
        component: LaunchpadMicroApp,
      },
      {
        path: 'conversations',
        name: 'conversations-v2',
        component: EmptyRouterParent,
        beforeEnter: requireAuthAndLocation,
        children: [
          {
            path: 'conversations/:conversation_id?',
            name: 'conversations-id-v2',
            component: ConversationsPage,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'manual_actions',
            name: 'manual-actions-v2',
            component: ManualActionList,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'templates',
            name: 'conversations-templates-v2',
            component: TextBuilder,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'links',
            name: 'triggers-links-conversations-v2',
            component: LinksPage,
            beforeEnter: requireAuthAndLocation,
            children: [
              {
                path: 'link',
                name: 'triggers-links-link-conversations-v2',
                component: LinksPage,
                beforeEnter: requireAuthAndLocation,
              },
              {
                path: 'analyze',
                name: 'triggers-links-analyze-conversations-v2',
                component: LinksPage,
                beforeEnter: requireAuthAndLocation,
              },
            ]
          },
        ]
      },
      {
        path: 'calendars',
        name: 'calendars-v2',
        component: EmptyRouterParent,
        beforeEnter: requireAuthAndLocation,
        children: [
            {
                path: 'view',
                name: 'calendars-page-v2',
                component: CalendarV2Page,
                beforeEnter: requireAuthAndLocation,
            },
            {
                path: 'appointments',
                name: 'appointments-v2',
                component: AppointmentsPage,
                beforeEnter: requireAuthAndLocation,
            },
        ]
      },
      {
        path: 'contacts',
        name: 'contacts-v2',
        component: EmptyRouterParent,
        children: [
          {
            path: 'smart_list/:smart_list',
            name: 'smart-list-v2',
            component: SmartList,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'import_filter/:import_filter',
            name: 'import_filter-v2',
            component: SmartList,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'business_name/:business_name',
            name: 'business_name-v2',
            component: SmartList,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'bulk',
            component: EmptyRouterParent,
            children: [
              {
                path: 'imports',
                name: 'bulk-imports-log-v2',
                component: BulkRequest,
                beforeEnter: requireAuthAndLocation,
              },
              {
                path: 'actions',
                name: 'bulk-actions-log-v2',
                component: BulkActionsView,
                beforeEnter: requireAuthAndLocation,
              },
            ],
          },
          {
            path: 'contacts',
            name: 'contacts-contacts-v2',
            component: CustomersPage,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'leads',
            name: 'leads-v2',
            component: LeadsPage,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'detail/:contact_id',
            name: 'contact_detail-v2',
            component: CustomerDetailNew,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'restore',
            name: 'contact_restore-v2',
            component: CustomerRestore,
            beforeEnter: requireAuthAndLocation,
          },
        ],
      },
      {
        path: 'opportunities',
        name: 'opportunities-v2',
        component: EmptyRouterParent,
        beforeEnter: requireAuthAndLocation,
        children: [
          {
            path: 'list',
            name: 'opportunities-list-v2',
            component: OpportunitiesPage,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'pipeline',
            name: 'pipeline-opportunities-v2',
            component: PipelineSettings,
            beforeEnter: requireAuthAndLocation,
          },
        ]
      },
      {
        path: 'payments/products',
        name: "payments-products-v2",
        component: Products,
        beforeEnter: requireAuthAndLocation,
        children: [
          {
            path: 'create',
            name: 'product-create-v2',
            component: CreateProduct,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: ':product_id',
            name: 'product-details-v2',
            component: ProductDetails,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: ':product_id/edit',
            name: 'product-edit-v2',
            component: CreateProduct,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'stripe/import',
            name: 'import-stripe-product-v2',
            component: ImportStripeProduct,
            beforeEnter: requireAuthAndLocation,
          }
        ]
      },
      {
        path: 'payments',
        name: "payments-v2",
        component: PaymentsPage,
        beforeEnter: requireAuthAndLocation,
        children: [
          {
            path: 'list',
            name: 'product-list-v2',
            component: ProductsPage,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'transactions',
            name: 'transactions-v2',
            component: Transactions,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'integrations',
            name: 'payment-integrations-v2',
            component: PaymentIntegration,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'integrations/:integration/callback',
            name: 'payment-integrations-callback-v2',
            component: PaymentIntegrationCallback,
            beforeEnter: requireAuthAndLocation,
          },
        ]
      },
      {
        path: 'marketing',
        name: 'marketing-v2',
        component: EmptyRouterParent,
        beforeEnter: requireAuthAndLocation,
        children: [
            {
              path: 'emails/:path',
              name: 'emails_home-v2',
              component: Emails,
              beforeEnter: requireAuthAndLocation,
            },
            {
              path: 'emails/detail/:path',
              name: 'emails_home-details-v2',
              component: Emails,
              beforeEnter: requireAuthAndLocation,
            },
            {
              path: 'templates',
              name: 'templates-v2',
              component: TextBuilder,
              beforeEnter: requireAuthAndLocation,
            },
            {
              path: 'links',
              name: 'triggers-links-v2',
              component: LinksPage,
              beforeEnter: requireAuthAndLocation,
              children: [
                {
                  path: 'link',
                  name: 'triggers-links-link-v2',
                  component: LinksPage,
                  beforeEnter: requireAuthAndLocation,
                },
                {
                  path: 'analyze',
                  name: 'triggers-links-analyze-v2',
                  component: LinksPage,
                  beforeEnter: requireAuthAndLocation,
                },
              ]
            },
            {
              path: 'html-builder',
              name: 'email_builder_list-v2',
              component: EmailBuilderPage,
              beforeEnter: requireAuthAndLocation,
            },
            {
              path: 'html-builder/:builder_id',
              name: 'email_builder_dashboard-v2',
              component: oldEmailBuilder,
              beforeEnter: requireAuthAndLocation,
            },
        ]
      },
      {
        path: 'automation',
        name: 'automation-v2',
        component: EmptyRouterParent,
        beforeEnter: requireAuthAndLocation,
        children: [
            {
              path: 'new-workflow',
              component: workflowBuilder,
              name: 'newWorkflows-v2',
              beforeEnter: requireAuthAndLocation,
            },
            {
                path: 'workflows',
                name: 'workflows-v2',
                component: workflowBuilder,
                beforeEnter: requireAuthAndLocation,
            },
            {
                path: 'campaigns',
                name: 'campaigns-v2',
                component: CampaignList,
                beforeEnter: requireAuthAndLocation,
            },
            {
              path: 'campaigns/:campaign_id',
              name: 'campaign_edit-v2',
              component: CampaignNew,
              beforeEnter: requireAuthAndLocation,
            },
            {
              path: 'campaigns/:campaign_id/detail',
              name: 'campaign_detail-v2',
              component: CampaignDetail,
              beforeEnter: requireAuthAndLocation,
            },
            {
              path: 'campaigns/:campaign_id/list',
              name: 'campaign_accounts_list-v2',
              component: CampaignAccountsList,
              beforeEnter: requireAuthAndLocation,
            },
            {
              path: 'campaigns/activity',
              name: 'campaign_activity-v2',
              component: CampaignActivity,
              beforeEnter: requireAuthAndLocation,
            },
            {
              path: 'bulk',
              name: 'bulk_requests-v2',
              component: BulkCampaignsPage,
              beforeEnter: requireAuthAndLocation,
            },
            {
                path: 'triggers',
                name: 'triggers-v2',
                component: TriggersPage,
                beforeEnter: requireAuthAndLocation,
            },
            {
              path: 'triggers/:trigger_id',
              name: 'triggers_detail-v2',
              component: TriggerDetailPage,
              beforeEnter: requireAuthAndLocation,
            },
        ]
      },
      {
        path: 'funnels-websites',
        name: 'funnels-v2',
        component: FunnelsPage,
        beforeEnter: requireAuthAndLocation,
        children: [
            {
                path: 'funnels',
                name: 'funnels-page-v2',
                component: FunnelsList,
                beforeEnter: requireAuthAndLocation,
            },
            {
                path: 'websites',
                name: 'websites-funnels-v2',
                component: FunnelsList,
                beforeEnter: requireAuthAndLocation,
            },
            {
              path: 'favorites',
              name: 'fav_funnels-v2',
              component: FunnelsList,
              beforeEnter: requireAuthAndLocation,
            },
            {
              path: 'recent',
              name: 'recent_funnels-v2',
              component: FunnelsList,
              beforeEnter: requireAuthAndLocation,
            },
            {
              path: 'archived',
              name: 'archived_funnels-v2',
              component: FunnelsList,
              beforeEnter: requireAuthAndLocation,
            },
        ]
      },
      {
        path: 'funnels-websites/funnels/:funnel_id',
        name: 'funnels-detail-v2',
        component: FunnelDetailPage,
        beforeEnter: requireAuthAndLocation,
        children: [
          {
            path: 'steps/:step_id?',
            name: 'funnel-steps-v2',
            component: FunnelSteps,
            beforeEnter: requireAuthAndLocation,
            children: [
              {
                path: 'overview',
                name: 'funnel-step-overview-v2',
                component: FunnelStepOverview,
              },
              {
                path: 'automation',
                name: 'funnel-step-automation-v2',
                component: FunnelStepAutomation,
              },
              {
                path: 'publish',
                name: 'funnel-step-publish-v2',
                component: FunnelStepPublish,
              },
              {
                path: 'product',
                name: 'funnel-step-product-v2',
                component: FunnelStepProduct,
              },
              {
                path: 'products',
                name: 'funnel-step-products-v2',
                component: FunnelProductsList,
              },
              {
                path: 'products/create',
                name: 'funnel-step-product-v2-create',
                component: FunnelStepProductV2,
              },
              {
                path: 'products/:funnel_product_id',
                name: 'funnel-step-product-v2-edit',
                component: FunnelStepProductV2,
              },
            ],
          },
          {
            path: 'stats',
            name: 'funnel-stats-v2',
            component: FunnelStats,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'sales',
            name: 'funnel-sales-v2',
            component: FunnelSales,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'settings',
            name: 'funnel-settings-v2',
            component: FunnelSettings,
            beforeEnter: requireAuthAndLocation,
          },
        ],
      },
      {
        path: 'funnels-websites/funnels/funnel-templates',
        name: 'funnel-templates-category',
        component: ChooseWebsiteTemplateCategory,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'funnels-websites/funnels/funnel-templates/:id',
        name: 'funnel-templates-category-detail',
        component: ChooseWebsiteTemplateCategoryDetail,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'funnels-websites/funnels/funnel-templates/:id/templates/:template_id',
        name: 'funnel-templates-detail',
        component: UserWebsiteTemplateDetail,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'funnels-websites/websites/:funnel_id',
        name: 'websites-detail-v2',
        component: FunnelDetailPage,
        beforeEnter: requireAuthAndLocation,
        children: [
          {
            path: 'pages/:step_id?',
            name: 'website-pages-v2',
            component: FunnelSteps,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'stats',
            name: 'website-stats-v2',
            component: FunnelStats,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'sales',
            name: 'website-sales-v2',
            component: FunnelSales,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'settings',
            name: 'website-settings-v2',
            component: FunnelSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'products/:step_id',
            name: 'website-products-v2',
            component: WebsiteProducts,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'website-products/:step_id',
            name: 'website-step-products-v2',
            component: FunnelProductsList,
          },
          {
            path: 'products/create',
            name: 'website-step-product-v2',
            component: FunnelStepProductV2,
          },
          {
            path: 'products/:funnel_product_id',
            name: 'website-step-product-v2-edit',
            component: FunnelStepProductV2,
          },
        ],
      },
      {
        path: 'funnels-websites/websites/website-templates',
        name: 'website-templates-category',
        component: ChooseWebsiteTemplateCategory,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'funnels-websites/websites/website-templates/:id',
        name: 'website-templates-category-detail',
        component: ChooseWebsiteTemplateCategoryDetail,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'funnels-websites/websites/website-templates/:id/templates/:template_id',
        name: 'website-templates-detail',
        component: UserWebsiteTemplateDetail,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'funnels-websites/wordpress',
        name: 'wordpress_dashboard-v2',
        component: WordpressDashboard,
        beforeEnter: requireAuthAndLocation,
      },
      {
          path: 'funnels-websites/form-builder',
          name: 'form-builder-v2',
          component: FormBuilderPage,
          beforeEnter: requireAuthAndLocation,
          children: [
            {
              path: 'main',
              name: 'form-builder-main-v2',
              component: FormBuilderPage,
              beforeEnter: requireAuthAndLocation,
            },
            {
              path: 'analyze',
              name: 'form-builder-analyze-v2',
              component: FormBuilderPage,
              beforeEnter: requireAuthAndLocation,
            },
            {
              path: 'submissions',
              name: 'form-builder-submissions-v2',
              component: FormBuilderPage,
              beforeEnter: requireAuthAndLocation,
            },
          ],
      },
      {
        path: 'funnels-websites/form-builder/jotform/:builder_id',
        name: 'jotbox_form-v2',
        component: JotboxForm,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'funnels-websites/survey-builder/jotformsurvey/:survey_id',
        name: 'jotbox_survey-v2',
        component: JotboxSurvey,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'funnels-websites/form-builder/:builder_id',
        name: 'form-builder-manage',
        component: BuilderCreateForm,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'funnels-websites/form-builder/formold/:builder_id',
        name: 'form_builder_manage_old-v2',
        component: BuilderCreateFormOld,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'funnels-websites/form-builder/:builder_id?/integrate',
        name: 'form_builder_integrate-v2',
        component: EmbedBuilder,
        beforeEnter: requireAuthAndLocation,
      },
      {
          path: 'funnels-websites/survey-builder',
          name: 'survey-builder-v2',
          component: SurveyList,
          beforeEnter: requireAuthAndLocation,
          children: [
            {
              path: 'main',
              name: 'survey-builder-main-v2',
              component: SurveyList,
              beforeEnter: requireAuthAndLocation,
            },
            {
              path: 'analyze',
              name: 'survey-builder-analyze-v2',
              component: SurveyList,
              beforeEnter: requireAuthAndLocation,
            },
            {
              path: 'submissions',
              name: 'survey-builder-submissions-v2',
              component: SurveyList,
              beforeEnter: requireAuthAndLocation,
            },
          ],
      },
      {
        path: 'funnels-websites/survey-builder/:survey_id',
        name: 'survey-manage',
        component: BuilderCreateSurvey,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'funnels-websites/survey-builder/:survey_id?/integrate',
        name: 'survey_integrate-v2',
        component: EmbedSurvey,
        beforeEnter: requireAuthAndLocation,
      },
      {
          path: 'funnels-websites/chat_widget_settings',
          name: 'chat-widget-settings-v2',
          component: TextWidgetSettingsPage,
          beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'funnels-websites/membership',
        component: membershipBuilder,
        children: [
          {
            path: 'dashboard',
            name: 'membership-v2',
            component: membershipBuilder
          },
          {
            path: 'product_blueprints',
            name: 'product_blueprints-v2',
            component: membershipBuilder
          },
          {
            path: 'products/',
            name: 'membership-products-v2',
            component: membershipBuilder,
            children: [
              {
                path: 'library-order',
                name: 'membership-library-order-v2',
                component: membershipBuilder
              },
              {
                path: ':id*',
                name: 'membership-product-overview-v2',
                component: membershipBuilder
              }
            ]
          },
          {
            path: 'offers/',
            name: 'membership-offers-v2',
            component: membershipBuilder,
            children: [
              {
                path: ':id*',
                name: 'membership-offer-detail-v2',
                component: membershipBuilder
              }
            ]
          },
          {
            path: 'analytics',
            name: 'membership-analytics-v2',
            component: membershipBuilder,
            children: [
              {
                path: 'product-progress',
                name: 'membership-product-progress-v2',
                component: membershipBuilder,
                children: [
                  {
                    path: ':id*',
                    name: 'member-progress-v2',
                    component: membershipBuilder
                  }
                ]
              }
            ]
          },
          {
            path: 'settings',
            name: 'membership-settings-v2',
            component: membershipBuilder,
            children: [
              {
                path: 'site-details',
                name: 'membership-site-details-v2',
                component: membershipBuilder
              },
              {
                path: 'custom-domain',
                name: 'membership-custom-domain-v2',
                component: membershipBuilder
              },
              {
                path: 'email-settings',
                name: 'membership-email-settings-v2',
                component: membershipBuilder
              }
            ]
          },
          {
            path: 'categories',
            name: 'membership-categories-v2',
            component: membershipBuilder,
            children: [
              {
                path: '*',
                name: 'membership-category-edit',
                component: membershipBuilder
              }
            ]
          },
          {
            path: 'posts',
            name: 'membership-posts-v2',
            component: membershipBuilder,
            children: [
              {
                path: '*',
                name: 'membership-post-edit',
                component: membershipBuilder
              }
            ]
          },
          {
            path: 'themes',
            name: 'membership-themes-v2',
            component: membershipBuilder,
            children: [
              {
                path: '*',
                name: 'membership-edit-themes-v2',
                component: membershipBuilder
              }
            ]
          }
        ]
      },
      {
        path: 'reputation',
        name: 'reputation-v2',
        component: ReputationLandingPage,
        beforeEnter: requireAuthAndLocation,
        children: [
            {
              path: 'overview',
              name: 'reputation-overview-v2',
              beforeEnter: requireAuthAndLocation,
              component: ReputationOverviewPage,
            },
            {
              path: 'requests',
              name: 'reputation-review-requests-v2',
              beforeEnter: requireAuthAndLocation,
              component: ReviewRequestsAllPage,
            },
            ,
            {
              path: 'reviews/:review_id?',
              name: 'reputation-reviews-v2',
              beforeEnter: requireAuthAndLocation,
              component: ReviewsAllPage,
            },
        ],
      },
      {
        path: 'reputation/analysis',
        name: 'analysis-v2',
        component: AnalysisPage,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'reputation/listing',
        name: 'reputation_listing-v2',
        beforeEnter: requireAuthAndLocation,
        component: ReputationOnlineListing,
      },
      {
        path: 'reporting',
        name: 'reporting-v2',
        component: EmptyRouterParent,
        beforeEnter: requireAuthAndLocation,
        children: [
            {
                path: 'google',
                name: 'reporting-google-v2',
                component: ReportingAdwordsIndex,
                children: [
                    {
                      path: 'campaigns',
                      name: 'reporting-adwords-campaigns-v2',
                      component: AdwordsCampaigns,
                    },
                    {
                      path: 'adgroups',
                      name: 'reporting-adwords-adgroups-v2',
                      component: AdwordsAdgroups,
                    },
                    {
                      path: 'ads',
                      name: 'reporting-adwords-ads-v2',
                      component: AdwordsAds,
                    },
                    {
                      path: 'keywords',
                      name: 'reporting-adwords-keywords-v2',
                      component: AdwordsKeywords,
                    },
                    {
                      path: 'conversions/:report_type',
                      name: 'reporting-adwords-conversions-v2',
                      component: AdwordsConversions,
                    },
                ]
            },
            {
                path: 'facebook',
                name: 'reporting-facebook-v2',
                component: ReportingFacebookIndex,
                children: [
                  {
                    path: 'campaigns',
                    name: 'reporting-facebook-campaigns-v2',
                    component: ReportingFacebookCampaigns,
                  },
                  {
                    path: 'adsets',
                    name: 'reporting-facebook-adsets-v2',
                    component: ReportingFacebookAdsets,
                  },
                  {
                    path: 'ads',
                    name: 'reporting-facebook-ads-v2',
                    component: ReportingFacebookAds,
                  },
                  {
                    path: 'demographics',
                    name: 'reporting-facebook-demographics-v2',
                    component: ReportingFacebookDemographics,
                  },
                ]
            },
            {
                path: 'attribution/:type?',
                name: 'reporting-attribution-v2',
                component: ReportingAttribution,
                beforeEnter: requireAuthAndLocation,
            },
            {
                path: 'appointments-reports',
                name: 'appointments-reports-v2',
                component: AppointmentsReportPage,
                beforeEnter: requireAuthAndLocation,
            },
            {
                path: 'call_stats',
                name: 'call-stats-v2',
                component: NumberPoolDashboard,
                beforeEnter: requireAuthAndLocation,
            },
            {
                path: 'agent_reporting',
                name: 'agent-reporting-v2',
                component: AgentReporting,
                beforeEnter: requireAuthAndLocation,
            },
        ]
      },
      {
        path: 'custom-menu-link/:id',
        name: 'location-custom-menu-link-v2',
        component: CustomMenuLinkPage,
      },
      {
        path: 'settings',
        name: 'settings-v2',
        component: EmptyRouterParent,
        beforeEnter: requireAuthAndLocation,
        children: [
          {
            path: 'settings-profile',
            name: 'settings-profile-v2',
            component: ProfileSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'profile',
            name: 'profile-v2',
            component: ProfileSettingsV3,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'company',
            name: 'company-settings-v2',
            component: CompanySettingsPage,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'company-billing',
            name: 'location-company-billing',
            beforeEnter: requireAuthAndLocation,
            component: LocationBillingPage,
            children: [
              {
                path: 'billing',
                name: 'location_billing-v2',
                component: LocationBillingSettings,
              },
              {
                path: 'transactions',
                name: 'location_wallet_transactions-v2',
                component: LocationWalletTransactions,
              },
            ],
          },
          {
            path: 'billing',
            name: 'account_billing-v2',
            component: AccountBilling,
          },
          {
            path: 'noshow_sms',
            name: 'no_show_sms_settings-v2',
            component: NoShowSMSSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'birthday_sms',
            name: 'birthday_sms_settings-v2',
            component: BirthdaySMSSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'social_posting',
            name: 'social_posting-v2',
            component: SocialPostingSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'review_widgets',
            name: 'review_widgets-v2',
            redirect: 'reputation/settings',
          },
          {
            path: 'review_popup',
            name: 'review_popup-v2',
            component: ReviewPopupSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'reputation',
            component: ReputationLandingPage,
            beforeEnter: requireAuthAndLocation,
            children: [
              {
                path: 'settings',
                name: 'settings-reputation-v2',
                component: ReputationSetingsPage,
              },
            ],
          },
          {
            path: 'integrations',
            name: 'integrations-v2',
            component: EmptyRouterParent,
            beforeEnter: requireAuthAndLocation,
            children: [
              {
                path: 'list',
                name: 'integrations-settings-v2',
                component: IntegrationSettingsPage,
                beforeEnter: requireAuthAndLocation,
              },
              {
                path: 'facebook',
                name: 'facebook_settings-v2',
                component: FacebookAdFieldsMappingSettings,
                beforeEnter: requireAuthAndLocation,
              },
            ]
          },
          {
            path: 'pipeline',
            name: 'pipeline-settings-v2',
            component: PipelineSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'payment',
            name: 'payment_settings-v2',
            component: PaymentSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'chat_widget',
            name: 'chat_widget-v2',
            component: ChatWidgetSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'appointment_widget',
            name: 'appointment-widget-2',
            component: AppointmentWidgetSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'phone_number',
            name: 'phone-number-v2',
            component: AllPhoneNumbersSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'templates',
            name: 'template-settings-v2',
            component: TemplateSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'staff',
            name: 'staff',
            component: EmptyRouterParent,
            beforeEnter: _requireAccessLevelAndLocation,
            children: [
              {
                path: 'teams',
                name: 'teams-v2',
                component: AccountTeamsManagement,
                beforeEnter: _requireAccessLevelAndLocation,
              },
              {
                path: 'team',
                name: 'team-v2',
                component: AccountTeamManagement,
                beforeEnter: _requireAccessLevelAndLocation,
              },
            ]
          },
          {
            path: 'smart_list/Settings',
            name: 'smart-list-settings-v2',
            component: SmartList,
            beforeEnter: requireAuthAndLocation,
          },
          // {
          //   path: 'custom_fields',
          //   name: 'custom-fields-v2',
          //   component: EmptyRouterParent,
          //   beforeEnter: requireAuthAndLocation,
          //   children: [
          //     {
          //       path: 'fields',
          //       name: 'custom-fields-settings-v2',
          //       component: AccountCustomFieldsSettings,
          //       beforeEnter: requireAuthAndLocation,
          //     },
          //     {
          //       path: 'tags',
          //       name: 'tags-settings-v2',
          //       component: TagsSettings,
          //       beforeEnter: requireAuthAndLocation,
          //     },
          //   ]
          // },
          {
            path: 'fields',
            name: 'custom-fields-settings-v2',
            component: AccountCustomFieldsSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'tags',
            name: 'tags-settings-v2',
            component: TagsSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'custom_values',
            name: 'custom-values-settings-v2',
            component: CustomValues,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'calendar_settings',
            name: 'calendar-settings-v2',
            component: CalendarSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'smtp_service',
            name: 'smtp-service-v2',
            component: SMTPServiceSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'smtp_service',
            name: 'smtp-service-v2',
            component: SMTPServiceSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'media-settings',
            name: 'media-settings-v2',
            component: MediaSettings,
            beforeEnter: requireAuthAndLocation,
            meta:{
              router_version: 'v2'
            }
          },
          {
            path: 'domain',
            name: 'domain-settings-v2',
            component: DomainSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'redirect',
            name: 'redirect-settings-v2',
            component: RedirectSettings,
            beforeEnter: requireAuthAndLocation,
          },
          {
            path: 'eliza_faq',
            name: 'eliza-faq-v2',
            component: ElizaFAQSetting,
            beforeEnter: async (to, from, next) => {
              const location = await Location.getById(to.params.location_id)
              store
                .dispatch('auth/get')
                .then((auth: AuthUser) => {
                  if (location && !location.botService)
                    next({
                      name: 'dashboard-v2',
                      replace: true,
                      params: { location_id: to.params.location_id },
                    })
                  next()
                })
                .catch(err => {
                  next()
                })
            },
          },
        ],
      },
      {
        path: 'logs',
        name: 'log-viewer-v2',
        component: LogsPage,
        beforeEnter: requireAuthAndLocation,
        props: (route) => {status: route.query.status}
      },
      {
        path: 'logs/:request_id',
        name: 'log-viewer-detail-v2',
        component: LogViewer,
        beforeEnter: requireAuthAndLocation,
      },
      {
        path: 'notifications/:notification_id?',
        name: 'notifications-v2',
        component: NotificationsPage,
      },
      {
        path: 'notificationslist',
        name: 'notificationslist-v2',
        component: NotificationsList,
      },
      {
        path: 'reminders',
        name: 'reminders-v2',
        component: RemindersPage,
      },
      {
        path: 'social_media',
        name: 'social_media-v2',
        component: SocialMediaDashboard,
      },
    ],
  },
]
