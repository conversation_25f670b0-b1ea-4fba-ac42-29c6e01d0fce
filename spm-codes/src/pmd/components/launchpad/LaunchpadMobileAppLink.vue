<template>
  <div></div>
</template>
<script lang="ts">
import Vue from 'vue'

const MOBILE_APP_LINK = {
  HIGHLEVEL: {
    android:
      'https://play.app.goo.gl/?link=https://play.google.com/store/apps/details?id=com.gohighlevel',
    ios: 'https://apps.apple.com/us/app/go-highlevel/id1425004076',
  },
  LEADCONNECTOR: {
    android:
      'https://play.app.goo.gl/?link=https://play.google.com/store/apps/details?id=com.LeadConnector',
    ios: 'https://apps.apple.com/app/lead-connector/id1564302502',
  },
}

export default Vue.extend({
  data() {
    return {
      leadConnectorPlan: ['agency_monthly_497', 'agency_annually_497'],
    }
  },
  created() {
    if (
      /Android|webOS|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    ) {
      window.location.href = MOBILE_APP_LINK.LEADCONNECTOR.android
    } else if (/iPhone|iPad|iPod/i.test(navigator.userAgent)) {
      window.location.href = MOBILE_APP_LINK.LEADCONNECTOR.ios
    } else {
      const { locationId, token } = this.$route.query
      const domain = window.location.origin
      const href = `${domain}/login/token?token=${token}&url=${encodeURIComponent(
        `/location/${locationId}/launchpad?mobile_app_error=true`
      )}`
      window.location.href = href
    }
  },
})
</script>
