<template>
  <modal
    v-if="modalShow"
    @close="closeModal"
    :maxWidth="popupWidth"
    modalMaskBgColor="white"
    :showCloseIcon="true"
    :noBackdropClose="true"
  >
    <div id="chat-widget-modal" class="gmb-container">
      <div class="fb-container">
        <div v-if="siteUrl" class="mt-2 fb-title">Is this your website?</div>
        <div v-if="siteUrl" class="fb-sub-title mt-2">
          Please verify your website, so we can install webchat widget.
        </div>
        <div v-else>
          Enter the website url where you want to add the webchat plugin
        </div>
        <form
          @submit.prevent="checkCms()"
          data-vv-scope="form-website"
          name="form-website"
        >
          <div class="form-group mt-2" style="width: 400px">
            <UITextInput
              placeholder="https://example.com"
              v-model="website"
              v-validate="{
                required: true,
                validUrl: true,
              }"
              data-vv-as="website"
              data-vv-validate-on="input"
              name="website"
            />
            <span v-show="errors.has('form-website.website')" class="error">{{
              errors.first('form-website.website')
            }}</span>
          </div>
          <button
            id="launchpad-chatwidget-website"
            type="submit"
            class="launchpad-filled-button launchpad-blue-btn"
            :disabled="
              sending || !website || errors.has('form-website.website')
            "
          >
            <moon-loader
              v-if="sending"
              :loading="sending"
              color="#37ca37"
              size="21px"
            />
            <span v-else>Continue</span>
          </button>
        </form>
      </div>
    </div>
  </modal>
</template>
<script lang="ts">
import Vue from 'vue'
import Modal from '@/pmd/components/common/Modal.vue'
import config from '@/config'
import axios from 'axios'
import { trackGaEvent } from '@/util/helper'

import { Location } from '@/models'

export default Vue.extend({
  props: ['modalShow', 'currentLocationId', 'siteUrl'],
  components: {
    Modal,
  },
  data() {
    return {
      popupWidth: 600,
      cms: 'Other',
      sending: false,
      location: {} as Location,
      website: this.siteUrl,
      cmsList: [
        'wordpress',
        'squarespace',
        'joomla',
        'duda',
        'shopify',
        'weebly',
      ],
    }
  },
  computed: {
    disableButton(): boolean {
      return this.website === '' || this.sending
    },
  },
  async mounted() {
    this.location = new Location(
      await this.$store.dispatch(
        'locations/getCurrentLocation',
        this.currentLocationId
      )
    )
  },
  watch: {
    siteUrl: function (url) {
      this.website = url
    },
  },
  methods: {
    async checkCms() {
      let result = await this.$validator.validateAll('form-subaccount')
      if (!result) {
        return false
      }
      this.sending = true
      try {
        await this.location.ref.update({
          'launchpad.website': this.website,
          website: this.website,
        })
        this.location = new Location(
          await this.$store.dispatch(
            'locations/getCurrentLocation',
            this.currentLocationId
          )
        )
        const response = await axios.get(
          `${config.cloudFunctionsUrl}/websiteCmsList?website=${this.website}`
        )
        if (response.status == 200) {
          const tech = response.data.technologies
          const detectedCms = tech.filter(t =>
            this.cmsList.includes(t.toLowerCase())
          )
          if (detectedCms.length) this.cms = detectedCms[0]
        }
        this.$root.$emit('detected-cms', this.cms)
        this.$root.$emit('get-screenshot')
        this.closeModal()
      } catch (err) {
        this.$root.$emit('detected-cms', this.cms)
        this.$root.$emit('get-screenshot')
        console.log(err)
      }
      this.sending = false
      trackGaEvent(
        'Launchpad',
        'WebChat Widget CMS',
        `Detected CMS for Webchat cms: ${this.cms} for location: ${this.currentLocationId}`
      )
    },
    closeModal() {
      console.log('Event got')
      this.$root.$emit('close-modal')
    },
  },
})
</script>
<style scoped>
.gmb-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 27px 10px;
}
.fb-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 450px;
}
.fb-headsup-logo {
  width: 200px;
  height: 200px;
  margin-bottom: 10px;
  margin-top: 20px;
}
.fb-title {
  font-weight: bold;
  font-size: 20px;
  color: #333333;
}
.fb-sub-title {
  font-weight: normal;
  font-size: 12px;
  color: #718096;
}
.launchpad-filled-button {
  margin-left: 0.75rem;
  display: inline-flex;
  justify-content: center;
  padding: 0.65rem 1.5rem;
  border-color: transparent;
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
    var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  font-size: 0.75rem;
  line-height: 1.25rem;
  font-weight: 500;
  font-family: 'Roboto';
  border-radius: 3px;
  cursor: pointer;
  white-space: nowrap;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-left: auto;
  margin-right: auto;
}
.launchpad-filled-button:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.launchpad-blue-btn {
  color: #fff;
  background-color: #4263eb;
}
.launchpad-blue-btn:hover {
  background-color: #4263eb;
}
#chat-widget-modal button:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}
</style>
