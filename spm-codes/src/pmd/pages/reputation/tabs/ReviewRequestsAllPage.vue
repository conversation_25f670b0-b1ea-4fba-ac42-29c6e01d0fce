<template>
  <section v-if="user && user.permissions.reviews_enabled !== false">
    <section class="customers" id="customers" v-if="requests.length > 0">
      <div>
        <div class="card hl_dashboard--latest-review-requests">
          <div class="card-header">
            <h2>Latest Review Requests</h2>
            <nav
              aria-label="Page navigation example"
              v-if="nextId || previousId"
            >
              <ul class="pagination justify-content-end">
                <li
                  class="page-item"
                  :class="{ disabled: !previousId || fetching }"
                >
                  <a
                    class="page-link"
                    href="javascript:void(0);"
                    tabindex="-1"
                    @click.prevent="load('previous')"
                    >Previous</a
                  >
                </li>
                <li
                  class="page-item"
                  :class="{ disabled: !nextId || fetching }"
                >
                  <a
                    class="page-link"
                    href="javascript:void(0);"
                    @click.prevent="load('next')"
                    >Next</a
                  >
                </li>
              </ul>
            </nav>
          </div>
          <div class="card-body --no-padding">
            <div class="table-wrap">
              <table class="table table-sort">
                <thead>
                  <tr>
                    <th data-sort="string">
                      Invite Sent to
                      <!-- <i class="icon icon-arrow-down-1"></i> -->
                    </th>
                    <th data-sort="string">
                      Email/Phone
                      <!-- <i class="icon icon-arrow-down-1"></i> -->
                    </th>
                    <th data-sort="string">
                      Sent by
                      <!-- <i class="icon icon-arrow-down-1"></i> -->
                    </th>
                    <th>Date Sent</th>
                    <th data-sort="string">
                      Status
                      <!-- <i class="icon icon-arrow-down-1"></i> -->
                    </th>
                    <th>
                     Retries so far
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <ReviewRequestCard
                    v-for="request in requests"
                    :key="request.id"
                    :request="request"
                  />
                  <tr  v-if="nextId || previousId">
                    <td colspan="6">
                      <nav
                        aria-label="Page navigation example"
                        class="container-fluid"

                      >
                        <ul class="pagination justify-content-end">
                          <li
                            class="page-item"
                            :class="{ disabled: !previousId || fetching }"
                          >
                            <a
                              class="page-link"
                              href="javascript:void(0);"
                              tabindex="-1"
                              @click.prevent="load('previous')"
                              >Previous</a
                            >
                          </li>
                          <li
                            class="page-item"
                            :class="{ disabled: !nextId || fetching }"
                          >
                            <a
                              class="page-link"
                              href="javascript:void(0);"
                              @click.prevent="load('next')"
                              >Next</a
                            >
                          </li>
                        </ul>
                      </nav>
                    </td>
                  </tr>

                  <tr></tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </section>
    <section v-else-if="!fetching && requests.length === 0">
      <div class="card">
        <div class="card-body">
              <div class="card-body --no-content" >
              <h3>Oops!</h3>
              <div class="flex justify-center">
                <NoReviewIcon alt="" />
              </div>
              <h5>You have not sent any review request yet</h5>
              <div> Start by <router-link  :to="{name:'settings_reputation_settings'}"> setting up your reviews</router-link> </div>
            </div>
        </div>
      </div>

    </section>
  </section>
</template>

<script lang="ts">
import Vue from 'vue'
import { ReviewRequest, User } from '@/models'
import firebase from 'firebase/app'
import { UserState } from '@/store/state_models'
import { mapState } from 'vuex'
import NoReviewIcon from '@/assets/pmd/img/noreview.svg'

const ReviewRequestCard = () => import('../../../components/ReviewRequestCard.vue')


let reviewRequestsListener: () => void
export default Vue.extend({
  components: {  ReviewRequestCard, NoReviewIcon },
  data() {
    return {
      requests: [] as ReviewRequest[],
      currentLocationId: '',
      previousId: undefined as firebase.firestore.DocumentSnapshot | undefined,
      nextId: undefined as firebase.firestore.DocumentSnapshot | undefined,
      fetching: false,
      limit: 15,
      showSendReviewModal: false,
    }
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchData()
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      if (id) {
        this.fetchData()
      }
    },
  },
  computed:{
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
  },
  methods: {
    fetchData() {
      this.load()
    },
    async load(fetchType?: 'previous' | 'next') {
      this.fetching = true
      let query = ReviewRequest.collectionRef()
        .where('deleted', '==', false)
        .where('location_id', '==', this.currentLocationId)

      if (fetchType === 'previous') {
        query = query.orderBy('date_added', 'asc').startAfter(this.previousId)
      } else if (fetchType === 'next') {
        query = query.orderBy('date_added', 'desc').startAfter(this.nextId)
      } else {
        query = query.orderBy('date_added', 'desc')
      }
      const snapshot = await query.limit(this.limit + 1).get()
      let docs = snapshot.docs
      this.nextId = undefined
      this.previousId = undefined
      if (!fetchType && docs.length === this.limit + 1) {
        docs.splice(this.limit, 1)
        this.nextId = docs[this.limit - 1]
      } else if (fetchType === 'next') {
        this.previousId = docs[0]
        if (docs.length === this.limit + 1) {
          docs.splice(this.limit, 1)
          this.nextId = docs[this.limit - 1]
        }
      } else if (fetchType === 'previous') {
        docs = docs.reverse()
        if (docs.length === this.limit + 1) {
          docs.splice(0, 1)
          this.previousId = docs[0]
        }
        this.nextId = docs[this.limit - 1]
      }
      this.requests = docs.map(d => new ReviewRequest(d))
      this.fetching = false
    },
  },
  beforeDestroy() {
    if (reviewRequestsListener) {
      reviewRequestsListener()
    }
  },
})
</script>
<style scoped>
/* .hl_calltoaction-btn {
  background: unset;
  padding: 0;
  border: unset;
  color: #188bf6;
} */

.card-header {
  padding: 15px;
}
.pagination {
  margin: 0;
}

.table tbody tr td:last-child {
    padding-right: 0px;
}

.--no-content{
  text-align: center;
}
</style>
