import Vue from 'vue';
import { Company, AuthUser } from '../models';
import { Module, ActionContext } from 'vuex';
import firebase from 'firebase/app';
import { CompanyState, RootState } from '../store/state_models';
import defaults from '@/config';
import { getValidUrl } from '@/util/helper';

let unsubscribe: () => void;
export const company: Module<CompanyState, RootState> = {
	namespaced: true,
	state: {},
	getters: {
		get: (state: CompanyState, getters: any, rootState: RootState, rootGetters: any) => {
			return state.company;
    },
    inSaasPlan: (state: CompanyState, getters: any, rootState: RootState, rootGetters: any) => {
      const saasPlans = ['agency_monthly_497', 'agency_annual_497']
      const companyPlan = state.company.stripe_active_plan
      return state.company.is_enterprise || saasPlans.includes(companyPlan);
    },
		isAbove97Plan: (state: CompanyState) => {
			const companyPlan = state.company.stripe_active_plan
			const stripeActivePlanAmount = companyPlan?.split('_')[2]
			if (!stripeActivePlanAmount || stripeActivePlanAmount === '97') return false
			return true
		}
	},
	mutations: {
		set: (state: CompanyState, payload: object) => {
			Vue.set(state, 'company', payload);
		},
		setWhiteLabelDomain: (state: CompanyState, payload: object) => {
			Vue.set(state, 'whiteLabelUrl', payload);
		}
	},
	actions: {
		get: {
			handler: (context: ActionContext<CompanyState, RootState>): Promise<Company> => {
				return new Promise(async (resolve, reject) => {
					if (context.state.company) {
						return resolve(new Company(context.state.company));
					}

					try {
						let credentials = new AuthUser(await context.dispatch('auth/get', null, { root: true }));
						const company = await Company.getById(credentials.companyId);
						context.commit('set', { ...company.secureData, id: company.id });
						resolve(company);
					} catch (e) {
						reject();
					}
				});
			},
		},
		getWhitelabelDomain: {
			handler: (context: ActionContext<CompanyState, RootState>): Promise<string> => {
				return new Promise(async (resolve, reject) => {
					if (context.state.whiteLabelUrl) {
						resolve(context.state.whiteLabelUrl);
					} else {
						let stateCompany;
						if (context.state.company) {
							stateCompany = new Company(context.state.company);
						} else {
							let credentials = new AuthUser(await context.dispatch('auth/get', null, { root: true }));
							stateCompany = await Company.getById(credentials.companyId);
							context.commit('set', { ...stateCompany.secureData, id: stateCompany.id });
						}
						let whiteLabelDomain;
						if (stateCompany.sparedomain) {
							whiteLabelDomain = stateCompany.sparedomain;
						}
						if (!whiteLabelDomain) {
							whiteLabelDomain = defaults.baseUrl;
						}
						context.commit('setWhiteLabelDomain', getValidUrl(whiteLabelDomain));
						resolve(context.state.whiteLabelUrl);
					}
				});
			},
		},
		syncAll: {
			root: true,
			handler: async (context: ActionContext<CompanyState, RootState>) => {
				let credentials: AuthUser;
				try {
					credentials = new AuthUser(await context.dispatch('auth/get', null, { root: true }));
				} catch (e) {
					return;
				}

				unsubscribe = Company.getStreamById(credentials.companyId).onSnapshot((snapshot: firebase.firestore.DocumentSnapshot) => {
          let company = new Company(snapshot.data());
					context.commit('set', { ...company.secureData, id: snapshot.id });
				});
			},
		},
		logout: {
			root: true,
			handler: async (context: ActionContext<CompanyState, RootState>) => {
				if (unsubscribe) {
					unsubscribe();
				}
			},
		},
	},
};
