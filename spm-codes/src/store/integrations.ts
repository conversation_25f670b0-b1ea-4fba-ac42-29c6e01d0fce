import { Module, ActionContext } from 'vuex'
import { RootState, IntegrationsState } from './state_models'

export const integrations: Module<IntegrationsState, RootState> = {
  namespaced: true,
  state: { integrations:[]},
  getters: {
  },
  mutations: {
    integrations: (
      state: IntegrationsState,
      integrations
    ) => {
      state.integrations = integrations
    }
  },
  actions: {
    integrations(
      context: ActionContext<IntegrationsState, RootState>,
      integrations
    ) {
      context.commit('integrations', integrations)
    }
  },
}
