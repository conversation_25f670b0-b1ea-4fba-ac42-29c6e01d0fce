<template>
    <div class="twilio-setup">
        <div id="step1" v-if="currentStep === 1">
            <h5> Step 1: Signup for a new Twilio account</h5>
            <div class="setup-row">
                <iframe width="800" height="450"  frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen
                    :src="`https://www.youtube.com/embed/${getVideoId('https://youtu.be/u8EzbRygMjk')}`" class="setup-video">
                </iframe>
                <!-- <div class="setup-area">
                    <iframe width="560" height="450"  frameborder="0" allowfullscreen
                        src="https://www.twilio.com/referral/0SvZEx" class="setup-area">
                    </iframe>
                    <div class="not-compatible-overlay" v-if="isSafari()">
                        <div>
                            <h5>Your browser doesn't support <br/> In-app Twilio Signup. </h5>
                            <h6>Visit: <a class="text-warning" href="https://www.twilio.com/try-twilio?promo=0SvZEx" target="_blank"> https://www.twilio.com/try-twilio </a> </h6>
                        </div>
                    </div>
                </div> -->
                <div class="setup-next">
                    <div class="setup-action-btn-wrap">
                      <button class="btn btn-primary" style="padding: 16px 56px" @click="twilioSignup()">Sign up for Twilio</button>
                      <br />
                      <p>I already have a twilio account?
                          <span class="skip-link" @click="currentStep++"> Take me directly to login</span>
                      </p>
                    </div>
                    <div class="option">
                        <input type="checkbox" id="signed-up" v-model="signedUp" />
                        <label for="signed-up">
                            I have signed up for Twilio
                        </label>
                    </div>
                    <div class="option">
                        <input type="checkbox" id="accept" v-model="accepted" />
                        <label for="accept">
                            I have recieved the email from Twilio &amp; verified my email-id
                        </label>
                    </div>
                    <br />
                    <button type="button" class="btn btn-success" style="margin-left: auto; display: block;" :disabled="!accepted || !signedUp" @click="currentStep++">Next Step</button>
                </div>
            </div>

        </div>
        <div id="step2" v-if="currentStep === 2">
            <h5> Step 2: Login to your Twilio account &amp; Set it up</h5>
            <div class="setup-row">
                <iframe width="800" height="450"  frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen
                    :src="`https://www.youtube.com/embed/${getVideoId('https://youtu.be/Wshzz6zIqCc')}`" class="setup-video">
                </iframe>
                <!-- <div class="setup-area">
                    <iframe width="560" height="450"  frameborder="0" allowfullscreen
                        src="https://www.twilio.com/login" class="setup-area">
                    </iframe>
                    <div class="not-compatible-overlay" v-if="isSafari()">
                        <div>
                            <h5>Your browser doesn't support <br/> In-app Twilio Login. </h5>
                            <h6>Visit: <a class="text-warning" href="https://www.twilio.com/login" target="_blank"> https://www.twilio.com/login </a> </h6>
                        </div>
                    </div>
                </div> -->
                <div class="setup-next">
                    <div class="setup-action-btn-wrap">
                      <button class="btn btn-primary" style="padding: 16px 56px" @click="twilioLogin()">Login to Twilio</button>
                    </div>
                    <div class="option">
                      1. Copy Account SID &amp; Auth Token from Twilio Dashboard <br />
                      2. Paste details below
                    </div>
                    <br />
                    <button type="button" class="btn btn-light2" style="width: fit-content;" @click="currentStep--">Prev Step</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { getVideoId } from "@/util/video_utils";
// import { isSafari } from "@/util/helper";

export default {
    data() {
        return {
            accepted: false,
            signedUp: false,
            getVideoId: getVideoId,
            currentStep: 1,
            // isSafari: isSafari,
	    }
    },
  methods: {
    twilioSignup() {
      window.open('https://www.twilio.com/try-twilio?promo=0SvZEx', '_blank')
    },
    twilioLogin() {
      window.open('https://www.twilio.com/login', '_blank')
    },
  },
}
</script>

<style scoped>
.twilio-setup{
    padding: 30px 0px;
    border-bottom: 2px solid #f2f7fa;
}
.skip-link{
  color: #188bf6;
  cursor: pointer;
  text-decoration: none;
	/* font-weight: 700; */
}
.setup-row{
    display: flex;
    /* justify-content: space-between; */
    padding: 30px 0px;
}
.setup-video{
    margin-right: 30px;
    max-width: 800px;
}
.setup-area{
    max-width: 560px;
    position: relative;
}
.setup-video, .setup-area{
    width: 100%;
    box-shadow: 2px 2px 20px 2px rgba(0,0,0,0.15);
    height: 450px;
    /* float: right;
    margin-left: 30px;
    margin-bottom: 15px; */
}
@media (max-width: 767px){
    .setup-row{
        flex-direction: column;
    }
    .setup-video{
        margin-bottom: 30px;
    }
    .setup-area{
        margin: auto;
    }
}
.setup-next{
  display: flex;
  flex-direction: column;
  /* justify-content:flex-end; */
  align-items: stretch;
  padding: 0px 32px;
  flex-grow: 1;
}

.not-compatible-overlay{
    width:100%;
    height: 100%;
    /* background-color: rgba(255,255,255,0.8); */
    background: rgba(230, 218, 218, 0.8);  /* fallback for old browsers */
    background: -webkit-linear-gradient(to top, rgba(39, 64, 70, 0.9), rgba(230, 218, 218, 0.9));  /* Chrome 10-25, Safari 5.1-6 */
    background: linear-gradient(to top, rgba(39, 64, 70, 0.9), rgba(230, 218, 218, 0.9)); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
    z-index: 1;
    position: absolute;
    top: 0px;
    left: 0px;

    display: flex;
    justify-content: center;
    align-items: flex-end;
    text-align: center;
    padding: 30px;
}
.not-compatible-overlay h5{
    color: #fff;
    font-weight: 600;
    margin-bottom: 20px;
}
.not-compatible-overlay h6{
    color: #fff;
}
.setup-action-btn-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  flex-grow: 1;
  border-bottom: 1px dashed #d5d4d4;
  margin-bottom: 16px;
}
</style>
