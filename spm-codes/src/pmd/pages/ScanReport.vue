<template>
	<!-- <html lang="en">

    <head>
        <meta charset="UTF-8">
        <title>HighLevel Report</title>
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0">
        <meta name="format-detection" content="telephone=no">

        <link rel="stylesheet" type="text/css" href="./css/report.min.css">
    </head>

	<body>-->
	<div class="hl_report--wrap">
		<div class="hl_report--header">
			<div class="logo-wrap">
				<img v-if="company && company.logoURL" :src="company.logoURL" style="max-width:300px">
				<h1>Snapshot report</h1>
			</div>
			<div class="user">
				<Avatar :contact="user" :size="'md'"/>
				<div class="user-info">
					<h2>{{userInfo.name}}</h2>
					<p>
						<UserInfoIcon />
						{{userInfo.email}}
					</p>
					<p style="word-break:break-all;">
						<Phone2Icon />
						{{userInfo.phone }}
					</p>
				</div>
			</div>
		</div>
		<div class="hl_report--hero">
			<div class="hl_report--hero-img">
				<img src="../../assets/pmd/img/reports/img-notebook.png">
			</div>
			<div class="hl_report--hero-text">
				<h2>Dominate your local market</h2>
				<p>Ranking high for your local market is critical to your success, when your customers are looking for your business they are looking online. You more than just a great website to rank high on Google. We've created this free report on your business to show exactly where you stand and where you can improve to truly dominate local.</p>
				<div class="btns">
					<a
						:href="company.website"
						target="_blank"
						class="btn btn-white"
						style="background-color:#14e075; color: white;"
					>Get started</a>
					<p>{{userInfo.phone }}</p>
				</div>
			</div>
		</div>
		<div class="hl_report--section sec1 --border">
			<div class="sec-heading">
				<img v-if="logoUrl" :src="logoUrl" style="max-height:50px">
				<div class="sec-heading-text">
					<h3>{{location.name}}</h3>
					<ul class="list-inline">
						<li style="word-break:break-all;">
							<PinIcon />
							{{location.address}}, {{location.city}}, {{location.state}} {{location.postalCode}}
						</li>
						<li v-if="location.website" style="word-break:break-all;">
							<WebIcon />
							<a :href="location.website">{{location.website}}</a>
						</li>
						<li style="word-break:break-all;">
							<Phone2Icon />
							{{location.phone }}
						</li>
					</ul>
				</div>
			</div>
			<div
				class="row"
				v-if="googlePlacesData && googlePlacesData.photos && googlePlacesData.photos.length > 0"
			>
				<div v-for="photo in googlePlacesData.photos.slice(0,4)" class="col-sm-3">
					<img
						:src="'https://maps.googleapis.com/maps/api/place/photo?maxwidth=170&photoreference=' + photo.photo_reference + '&key=AIzaSyAEWVDkZgkfkJFWQKMPyJWgzXG-XongSRQ'"
						alt="Photo"
						class="img-responsive"
					>
				</div>
			</div>
		</div>
		<div class="hl_report--section">
			<div class="d-flex">
				<div
					class="progress-bar-circle overall-score"
					data-percent="50"
					data-duration="500"
					:data-color="getDataColor(50)"
				></div>
				<div class="letters">
					<div class="letters-item">
						<h3>Listings</h3>
						<div :class="computeLetterGradeClass(yextLetterGrade)">{{yextLetterGrade}}</div>
					</div>
					<div class="letters-item">
						<h3>Reviews</h3>
						<div
							:class="computeLetterGradeClass(computeLetterGradeByPercent(yextReviewPercentile))"
						>{{computeLetterGradeByPercent(yextReviewPercentile)}}</div>
					</div>
					<div class="letters-item">
						<h3>Social</h3>
						<div
							:class="computeLetterGradeClass(computeLetterGradeByPercent(socialScore))"
						>{{computeLetterGradeByPercent(socialScore)}}</div>
					</div>
					<div class="letters-item">
						<h3>Website</h3>
						<div class="letter --d">D</div>
					</div>
				</div>
			</div>
		</div>
		<div class="hl_report--section --grey">
			<div class="sec-heading">
				<div
					class="progress-bar-circle-sm"
					:data-percent="yextOverallPercentage"
					data-duration="500"
					:data-color="getDataColor(yextOverallPercentage)"
				></div>
				<div class="sec-heading-text">
					<h3>Listings
						<div :class="computeLetterGradeClass(yextLetterGrade)">{{yextLetterGrade}}</div>
					</h3>
					<p>Can customers find your business?</p>
				</div>
			</div>
			<p>Google and other search engines use a network of over 80 websites as directories to find and rank local businesses, if you're not on all 80, then you're competion can out rank you by simply being listed where you aren't.</p>
		</div>
		<div class="hl_report--section sec-cards">
			<div class="listing-heading row sec-cols30">
				<div class="col-md-4">
					<h3>
						<HouseIcon alt="house" class="icon" /> Business name
					</h3>
					<div class="progress-wrap">
						<p>{{yextBusinessNameScore}}%</p>
						<div :class="computeProgressClass(yextBusinessNameScore)">
							<div class="progress-bar" :style="'width: ' + yextBusinessNameScore + '%'"></div>
						</div>
					</div>
				</div>
				<div class="col-md-4">
					<h3>
						<PinIcon alt="pin" class="icon" /> Address
					</h3>
					<div class="progress-wrap">
						<p>{{yextBusinessAddressScore}}%</p>
						<div :class="computeProgressClass(yextBusinessAddressScore)">
							<div class="progress-bar" :style="'width: ' + yextBusinessAddressScore + '%'"></div>
						</div>
					</div>
				</div>
				<div class="col-md-4">
					<h3>
						<PhoneIcon alt="phone" class="icon" /> Phone number
					</h3>
					<div class="progress-wrap">
						<p>{{yextBusinessPhoneScore}}%</p>
						<div :class="computeProgressClass(yextBusinessPhoneScore)">
							<div class="progress-bar" :style="'width: ' + yextBusinessPhoneScore + '%'"></div>
						</div>
					</div>
				</div>
			</div>
			<div class="row sec-cols30">
				<div class="col-md-6">
					<div class="card">
						<div class="card-heading">
							<h3>
								<img src="../../assets/pmd/img/reports/logo-google.png" alt="Google"> Google
							</h3>
							<p v-if="yextGoogleListing" class="listed">
								<CheckIcon alt="check" class="icon" />Listed
							</p>
							<p v-if="!yextGoogleListing" class="listed">
								<MinusIcon alt="check" class="icon" />Not Listed
							</p>
						</div>
						<div class="rating-wrap" v-if="yextGoogleListing && yextGoogleListing.averageReviewScore">
							<div class="rating">
								<StarFilledIcon
									v-for="stars in wholeStars(yextGoogleListing.averageReviewScore)"
								/>
								<StarHalfIcon
									v-for="stars in halfStars(yextGoogleListing.averageReviewScore)"
								/>
								<StarIcon
									v-for="stars in emptyStars(yextGoogleListing.averageReviewScore)"
								/>
							</div>
							<!-- <p class="red">5% are rated higher</p> -->
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="card">
						<div class="card-heading">
							<h3>
								<img src="../../assets/pmd/img/reports/logo-facebook.png" alt="Facebook"> Facebook
							</h3>
							<p v-if="yextFacbookListing" class="listed">
								<CheckIcon alt="check" class="icon" />Listed
							</p>
							<p v-if="!yextFacbookListing" class="listed">
								<MinusIcon alt="check" class="icon" />Not Listed
							</p>
						</div>
						<div class="rating-wrap" v-if="yextFacbookListing && yextFacbookListing.averageReviewScore">
							<div class="rating">
								<StarFilledIcon
									v-for="stars in wholeStars(yextFacbookListing.averageReviewScore)"
								/>
								<StarHalfIcon
									v-for="stars in halfStars(yextFacbookListing.averageReviewScore)"
								/>
								<StarIcon
									v-for="stars in emptyStars(yextFacbookListing.averageReviewScore)"
								/>
							</div>
							<!-- <p class="red">5% are rated higher</p> -->
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="card">
						<div class="card-heading">
							<h3>
								<img src="../../assets/pmd/img/reports/logo-yelp.png" alt="Yelp"> Yelp
							</h3>
							<p v-if="yextYelpListing" class="listed">
								<CheckIcon alt="check" class="icon" />Listed
							</p>
							<p v-if="!yextYelpListing" class="listed">
								<MinusIcon alt="check" class="icon" />Not Listed
							</p>
						</div>
						<div class="rating-wrap" v-if="yextYelpListing && yextYelpListing.averageReviewScore">
							<div class="rating">
								<StarFilledIcon
									v-for="stars in wholeStars(yextYelpListing.averageReviewScore)"
								/>
								<StarHalfIcon
									v-for="stars in halfStars(yextYelpListing.averageReviewScore)"
								/>
								<StarIcon
									v-for="stars in emptyStars(yextYelpListing.averageReviewScore)"
								/>
							</div>
							<!-- <p class="red">5% are rated higher</p> -->
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="card">
						<div class="card-heading">
							<h3>
								<img src="../../assets/pmd/img/reports/logo-bing.png" alt="Bing"> Bing
							</h3>
							<p v-if="yextBingListing" class="listed">
								<CheckIcon alt="check" class="icon" />Listed
							</p>
							<p v-if="!yextBingListing" class="listed">
								<MinusIcon alt="check" class="icon" />Not Listed
							</p>
						</div>
						<div class="rating-wrap" v-if="yextBingListing && yextBingListing.averageReviewScore">
							<div class="rating">
								<StarFilledIcon
									v-for="stars in wholeStars(yextBingListing.averageReviewScore)"
								/>
								<StarHalfIcon
									v-for="stars in halfStars(yextBingListing.averageReviewScore)"
								/>
								<StarIcon
									v-for="stars in emptyStars(yextBingListing.averageReviewScore)"
								/>
							</div>
							<!-- <p class="red">5% are rated higher</p> -->
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="hl_report--section --grey">
			<div class="sec-heading">
				<div
					class="progress-bar-circle-sm"
					:data-percent="yextReviewPercentile"
					data-duration="500"
					:data-color="getDataColor(yextReviewPercentile)"
				></div>
				<div class="sec-heading-text">
					<h3>Reviews
						<div
							:class="computeLetterGradeClass(computeLetterGradeByPercent(yextReviewPercentile))"
						>{{computeLetterGradeByPercent(yextReviewPercentile)}}</div>
					</h3>
					<p>Do your customers trust your business?</p>
				</div>
			</div>
			<p>More people trust online reviews than they trust word of mouth recommendations. It's no longer enough to have a lot of reviews, now those reviews need to be current, if your competion has more reviews than you in the last 30 days, you're at risk of seeing your rankings slip.</p>
		</div>
		<div class="hl_report--section">
			<div class="review-heading row sec-cols20">
				<div class="col-md-4">
					<div
						:class="computeLetterGradeClass(computeLetterGradeByPercent(yextReviewPercentile))"
					>{{computeLetterGradeByPercent(yextReviewPercentile)}}</div>
					<p>Businesses with more reviews</p>
					<h3>
						{{yextReviewPercentile}}
						<span>%</span>
					</h3>
				</div>
				<div class="col-md-4">
					<div
						:class="computeLetterGradeClassByRating(yextAvgScore)"
					>{{computeLetterGradeByRating(yextAvgScore)}}</div>
					<p>Average review rating</p>
					<h3>{{yextAvgScore}}</h3>
				</div>
				<div class="col-md-4">
					<div class="letter --d">D</div>
					<p>Number of reviews (non-Google)</p>
					<h3>{{yextNumberRatings}}</h3>
				</div>
			</div>
			<div class="row sec-cols30">
				<div class="col-md-6">
					<div class="card">
						<div class="card-heading">
							<h3>
								<img src="../../assets/pmd/img/reports/logo-google.png" alt="Google"> Google
							</h3>
							<!-- <p>
                                    <strong>11</strong> Reviews
							</p>-->
						</div>
						<div v-if="yextGoogleListing && yextGoogleListing.averageReviewScore" class="overall-rating">
							<p>Overall rating</p>
							<div class="rating">
								<h4>{{yextGoogleListing.averageReviewScore}}</h4>
								<StarFilledIcon
									v-for="stars in wholeStars(yextGoogleListing.averageReviewScore)"
								/>
								<StarHalfIcon
									v-for="stars in halfStars(yextGoogleListing.averageReviewScore)"
								/>
								<StarIcon
									v-for="stars in emptyStars(yextGoogleListing.averageReviewScore)"
								/>
							</div>
						</div>
						<!-- <div class="reviews-list row">
                                <div class="col-6">
                                    <h4>Top 3 positive words</h4>
                                    <ul>
                                        <li>
                                            <PlusIcon />
                                            <strong>Staff </strong>- 8 occ.</li>
                                        <li>
                                            <PlusIcon />
                                            <strong>Prices</strong> - 6 occ.</li>
                                        <li>
                                            <PlusIcon />
                                            <strong>Location</strong> - 2 occ.</li>
                                    </ul>
                                </div>
                                <div class="col-6">
                                    <h4>Top 3 negative words</h4>
                                    <ul>
                                        <li>
                                            <MinusIcon />
                		                     <strong>Reception</strong> - 3 occ.</li>
                                        <li>
                                            <MinusIcon />
                                             <strong>Waiting time</strong> - 2 occ.</li>
                                        <li>
                                            <MinusIcon />
                                             <strong>Services</strong> - 1 occ.</li>
                                    </ul>
                                </div>
						</div>-->
					</div>
				</div>
				<div class="col-md-6">
					<div class="card">
						<div class="card-heading">
							<h3>
								<img src="../../assets/pmd/img/reports/logo-facebook.png" alt="Facebook"> Facebook
							</h3>
							<p v-if="yextFacbookListing">
								<strong>{{yextFacbookListing.reviewCount}}</strong> Reviews
							</p>
						</div>
						<div v-if="scanReport && scanReport.facebookStarRating" class="overall-rating">
							<p>Overall rating</p>
							<div class="rating">
								<h4>{{scanReport.facebookStarRating}}</h4>
								<StarFilledIcon
									v-for="stars in wholeStars(scanReport.facebookStarRating)"
								/>
								<StarHalfIcon
									v-for="stars in halfStars(scanReport.facebookStarRating)"
								/>
								<StarIcon
									v-for="stars in emptyStars(scanReport.facebookStarRating)"
								/>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row sec-cols30">
				<div class="col-md-4">
					<div class="card --card-sm">
						<div class="card-heading">
							<h3>
								<img src="../../assets/pmd/img/reports/logo-yelp.png" alt="Google"> Yelp
							</h3>
							<p>
								<strong>{{yelpReviews}}</strong> Listed
							</p>
						</div>
					</div>
				</div>
				<div class="col-md-4">
					<div class="card --card-sm">
						<div class="card-heading">
							<h3>
								<img src="../../assets/pmd/img/reports/logo-facebook.png" alt="Facebook"> Facebook
							</h3>
							<p>
								<strong>{{facebookReviews}}</strong> Listed
							</p>
						</div>
					</div>
				</div>
				<div class="col-md-4">
					<div class="card --card-sm">
						<div class="card-heading">
							<h3>
								<img src="../../assets/pmd/img/reports/logo-foursquare.png" alt="Foursquare"> Foursquare
							</h3>
							<p>
								<strong>{{foursquareReviews}}</strong> Listed
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="hl_report--section --grey">
			<div class="sec-heading">
				<div
					class="progress-bar-circle-sm"
					:data-percent="socialScore"
					data-duration="500"
					:data-color="getDataColor(socialScore)"
				></div>
				<div class="sec-heading-text">
					<h3>Social performance
						<div
							:class="computeLetterGradeClass(computeLetterGradeByPercent(socialScore))"
						>{{computeLetterGradeByPercent(socialScore)}}</div>
					</h3>
					<p>Do customers like your business?</p>
				</div>
			</div>
			<p>Being social is a critical way to connect with current and future customers, provide ways to showcase your business and connect. If you're not consistently posting on social media then you're not talking to your audience. Don't let you competitors crowd out your voice online.</p>
		</div>
		<div class="hl_report--section">
			<div class="row sec-cols30">
				<div class="col-md-6">
					<div class="card">
						<div class="card-heading">
							<h3>
								<img src="../../assets/pmd/img/reports/logo-facebook.png" alt="Facebook"> Facebook
							</h3>
						</div>
						<table class="table">
							<thead>
								<tr>
									<th>
										<!--Blank-->
									</th>
									<th>YOUR BUSINESS</th>
									<!-- <th>INDUSTRY</th> -->
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>Overall rating</td>
									<td>
										<div class="rating">
											<h4>{{scanReport.facebookStarRating}}</h4>
											<StarFilledIcon
												v-for="stars in wholeStars(scanReport.facebookStarRating)"
											/>
											<StarHalfIcon
												v-for="stars in halfStars(scanReport.facebookStarRating)"
											/>
											<StarIcon
												v-for="stars in emptyStars(scanReport.facebookStarRating)"
											/>
										</div>
									</td>
									<!-- <td>
                                            <div class="rating">
                                                <h4>4.5</h4>
                                                <StarFilledIcon />
                                                <StarFilledIcon />
                                                <StarFilledIcon />
                                                <StarFilledIcon />
                                                <StarHalfIcon />
                                            </div>
									</td>-->
								</tr>
								<tr>
									<td>Avg. likes/post</td>
									<td>
										<span class="red">{{avgLikesPerPost}}</span>
									</td>
									<!-- <td>210</td> -->
								</tr>
								<tr>
									<td>Avg. shares/post</td>
									<td>
										<span class="red">{{avgSharesPerPost}}</span>
									</td>
									<!-- <td>39</td> -->
								</tr>
								<tr>
									<td>Posts last 30 days</td>
									<td>
										<span class="red">{{totalPosts}}</span>
									</td>
									<!-- <td>82</td> -->
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="col-md-6">
					<div class="card">
						<div class="card-heading">
							<h3>
								<img src="../../assets/pmd/img/reports/logo-twitter.png" alt="Twitter"> Twitter
							</h3>
						</div>
						<table class="table">
							<thead>
								<tr>
									<th>
										<!--Blank-->
									</th>
									<th>YOUR BUSINESS</th>
									<!-- <th>INDUSTRY</th> -->
								</tr>
							</thead>
							<tbody>
								<tr>
									<td>Followers</td>
									<td>
										<span class="red">{{twitterFollowers}}</span>
									</td>
									<!-- <td>2.8K</td> -->
								</tr>
								<tr>
									<td>Following</td>
									<td>
										<span class="red">{{twitterFriends}}</span>
									</td>
									<!-- <td>689</td> -->
								</tr>
								<tr>
									<td>Likes</td>
									<td>
										<span class="green">{{twitterLikes}}</span>
									</td>
									<!-- <td>132</td> -->
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
		<div class="hl_report--section --grey">
			<div class="sec-heading">
				<div
					class="progress-bar-circle-sm"
					:data-percent="websiteOverall"
					data-duration="500"
					:data-color="getDataColor(websiteOverall)"
				></div>
				<div class="sec-heading-text">
					<h3>Website performance
						<div
							:class="computeLetterGradeClass(computeLetterGradeByPercent(websiteOverall))"
						>{{computeLetterGradeByPercent(websiteOverall)}}</div>
					</h3>
					<p>Can your business website convert?</p>
				</div>
			</div>
			<p>After all the work, time, and money spent to drive website traffic, does your website match modern performance standards? Did you know a delay of 1 second could cost you 7% of your revenue? Slow websites or websites that don't perform well on mobile devices cost your businesses money, here is where your website could use a pick-me-up.</p>
		</div>
		<div class="hl_report--section">
			<div class="row circle-stats">
				<div class="col-sm-3">
					<h4>Performance</h4>
				</div>
				<div class="col-sm-3">
					<h4>Progressive web app</h4>
				</div>
				<div class="col-sm-3">
					<h4>Best practices</h4>
				</div>
				<div class="col-sm-3">
					<h4>SEO</h4>
				</div>
			</div>
			<div class="mockups">
				<div
					class="mockup-desktop"
					style="background-image: url('/pmd/img/reports/mockup-desktop.png');"
				>
					<div
						class="screen"
						:style="'background-size:333px,214px;background-repeat:no-repeat;background-image:url(\'' + desktopScreenShot + '\');'"
					></div>
				</div>
				<div class="mockup-phone" style="background-image: url('/pmd/img/reports/mockup-phone.png');">
					<div
						class="screen"
						:style="'background-size:126px,272px;background-repeat:no-repeat;background-image:url(\'' + mobileScreenShot + '\');'"
					></div>
				</div>
			</div>
			<div class="sec-heading2">
				<div class="sec-heading-text">
					<h4>Performance</h4>
					<p>These encapsulate your web app's current performance and opportunities to improve it.</p>
				</div>
				<div
					class="progress-bar-circle-sm"
					:data-percent="websitePerformance"
					data-duration="500"
					:data-color="getDataColor(websitePerformance)"
				></div>
			</div>
			<div class="stat-group">
				<h4>
					<MetricIcon alt="metric" class="icon" /> Metrics
				</h4>
				<div class="stat-progress-item">
					<h4>Page load time</h4>
					<p>First meaningful paint measures when the primary content of a page is visible.</p>
					<div class="progress-wrap">
						<p>{{loadTime | formatNumber}} ms</p>
						<div :class="computeProgressClass((loadTime/metricScale)*100)">
							<div class="progress-bar" :style="'width: ' + (loadTime/metricScale)*100 + '%'"></div>
						</div>
					</div>
				</div>
				<!-- <div class="stat-progress-item">
                    <h4>First interactive</h4>
                    <p>First Interactive marks the time at which the page is minimally interactive.</p>
                    <div class="progress-wrap">
                        <p>{{firstInteractive | formatNumber}} ms</p>
                        <div :class="computeProgressClass((firstInteractive/metricScale)*100)">
                            <div class="progress-bar" :style="'width: ' + (firstInteractive/metricScale)*100 + '%'"></div>
                        </div>
                    </div>
				</div>-->
				<div class="stat-progress-item">
					<h4>Consistently interactive</h4>
					<p>Consistently Interactive marks the time at which the page is fully interactive.</p>
					<div class="progress-wrap">
						<p>{{consistentlyInteractive | formatNumber}} ms</p>
						<div :class="computeProgressClass((consistentlyInteractive/metricScale)*100)">
							<div
								class="progress-bar"
								:style="'width: ' + (consistentlyInteractive/metricScale)*100 + '%'"
							></div>
						</div>
					</div>
				</div>
				<div class="stat-progress-item">
					<h4>Perceptual Speed Index</h4>
					<p>Speed Index shows how quickly the contents of a page are visibly populated.</p>
					<div class="stat">
						<div class="stat-item">{{perceptualSpeedIndex | formatNumber}}</div>
						<div class="stat-item">
							<span :class="computeTextClass(perceptualSpeedIndexScore)">{{perceptualSpeedIndexScore}}</span>/100
						</div>
					</div>
				</div>
				<div class="stat-progress-item">
					<h4>Estimated Input Latency</h4>
					<p>The score above is an estimate of how long your app takes to respond to user input, in milliseconds. There is a 90% probability that a user encounters this amount of latency, or less. 10% of the time a user can expect additional latency. If your latency is higher than 50 ms, users may perceive your app as laggy.</p>
					<div class="stat">
						<div class="stat-item">{{estimatedinputLatency| formatNumber}} ms</div>
						<div class="stat-item">
							<span :class="computeTextClass(estimatedinputLatencyScore)">{{estimatedinputLatencyScore}}</span>/100
						</div>
					</div>
				</div>
			</div>
			<div class="stat-group">
				<h4>
					<LightningIcon alt="lightning" class="icon" /> Opportunities
				</h4>

				<div
					v-if="opportunities && opportunities.length > 0"
					v-for="oppurtunity in opportunities"
					class="stat-progress-item"
				>
					<h4>{{lighthouseData.audits[oppurtunity.id].title}}</h4>
					<p>{{lighthouseData.audits[oppurtunity.id].description}}</p>
					<div class="progress-wrap" v-if="lighthouseData.audits[oppurtunity.id].rawValue > 0">
						<p>{{(lighthouseData.audits[oppurtunity.id].rawValue/opportunityScale * 100).toFixed(0)}}% improvement</p>
						<div
							:class="computeProgressClassReverse((lighthouseData.audits[oppurtunity.id].rawValue/opportunityScale * 100).toFixed(0))"
						>
							<div
								class="progress-bar"
								:style="'width: '+ (lighthouseData.audits[oppurtunity.id].rawValue/opportunityScale * 100).toFixed(0) + '%'"
							></div>
						</div>
					</div>
				</div>
			</div>
			<div class="sec-heading2">
				<div class="sec-heading-text">
					<h4>Progressive web app</h4>
					<p
						style="max-width: 600px;"
					>These checks validate the aspects of a Progressive Web App, as specified by the baseline PWA Checklist.</p>
				</div>
				<div
					class="progress-bar-circle-sm"
					:data-percent="websitePwa"
					data-duration="500"
					:data-color="getDataColor(websitePwa)"
				></div>
			</div>
			<div class="audit-group">
				<h4>Failed audits</h4>

				<div
					v-if="failedPwaAudits && failedPwaAudits.length > 0"
					v-for="audit in failedPwaAudits"
					class="audit-item"
				>
					<i class="icon" style="background-image: url('./img/reports/icon-cross.svg');"></i>
					<h5>{{audit.title}}</h5>
					<p>{{audit.description}}</p>
				</div>

				<!-- <div class="audit-item">
                        <i class="icon" style="background-image: url('./img/reports/icon-cross.svg');"></i>
                        <h5>Does not register a service worker</h5>
                        <p>The service worker is the technology that enables your app to use many Progressive Web App features, such as offline, add to homescreen, and push notifications.</p>
                    </div>
                    <div class="audit-item">
                        <i class="icon" style="background-image: url('./img/reports/icon-cross.svg');"></i>
                        <h5>Does not respond with a 200 when offline</h5>
                        <p>If you're building a Progressive Web App, consider using a service worker so that your app can work offline.</p>
                        <p class="red">WARNING: You may be failing this check because your test URL (http://www.procarepando.com) was redirected to ”https://www.procarepando.com/". Try testing the second URL directly.</p>
                    </div>
                    <div class="audit-item">
                        <i class="icon" style="background-image: url('./img/reports/icon-cross.svg');"></i>
                        <h5>Page load is not fast enough on 3G</h5>
                        <p>A fast page load over a 3G network ensures a good mobile user experience.</p>
                        <p class="red">First Interactive was at 11,100 ms. More details in the "Performance" section.</p>
				</div>-->
			</div>
			<div class="sec-heading2">
				<div class="sec-heading-text">
					<h4>Best practices</h4>
					<p
						style="max-width: 600px;"
					>We've compiled some recommendations for modernizing your web app and avoiding performance pitfalls.</p>
				</div>
				<div
					class="progress-bar-circle-sm"
					:data-percent="websiteBestPractices"
					data-duration="500"
					:data-color="getDataColor(websiteBestPractices)"
				></div>
			</div>
			<div class="audit-group">
				<h4>Failed audits</h4>

				<div
					v-if="failedBestPracticeAudits && failedBestPracticeAudits.length > 0"
					v-for="audit in failedBestPracticeAudits"
					class="audit-item"
				>
					<i class="icon" style="background-image: url('./img/reports/icon-cross.svg');"></i>
					<h5>{{audit.title}}</h5>
					<p>{{audit.description}}</p>
				</div>
				<!-- <div class="audit-item">
                        <i class="icon" style="background-image: url('./img/reports/icon-cross.svg');"></i>
                        <h5>Does not use HTTPS: 1 insecure request found</h5>
                        <p>All sites should be protected with HTTPS, even ones that don't handle sensitive data. HTTPS prevents intruders from tampering with or passively listening in on the communications between your app and your users, and is a prerequisite for HTTP/2 and many new web platform APIs.</p>
                    </div>
                    <div class="audit-item">
                        <i class="icon" style="background-image: url('./img/reports/icon-cross.svg');"></i>
                        <h5>Does not use HTTP/2 for all of its resources: 52 requests were not handled over HTTP/2</h5>
                        <p>HTTP/2 offers many benefits over HTTP/1.1, including binary headers, multiplexing, and server push.</p>
                    </div>
                    <div class="audit-item">
                        <i class="icon" style="background-image: url('./img/reports/icon-cross.svg');"></i>
                        <h5>Includes front-end JavaScript libraries with known security vulnerabilities: 2 vulnerabilities detected.</h5>
                        <p>Some third-party scripts may contain known security vulnerabilities that are easily identified and exploited by attackers.</p>
				</div>-->
			</div>
			<div class="sec-heading2">
				<div class="sec-heading-text">
					<h4>SEO</h4>
					<p
						style="max-width: 600px;"
					>These checks ensure that your page is optimized for search engine results ranking. There are additional factors Lighthouse does not check that may affect your search ranking.</p>
				</div>
				<div
					class="progress-bar-circle-sm"
					:data-percent="websiteSEO"
					data-duration="500"
					:data-color="getDataColor(websiteSEO)"
				></div>
			</div>
			<div class="audit-group" v-if="failedSeoAudits && failedSeoAudits.length > 0">
				<h4>Failed audits</h4>
				<div
					v-if="failedSeoAudits && failedSeoAudits.length > 0"
					v-for="audit in failedSeoAudits"
					class="audit-item"
				>
					<i class="icon" style="background-image: url('./img/reports/icon-cross.svg');"></i>
					<h5>{{audit.title}}</h5>
					<p>{{audit.description}}</p>
				</div>
				<!-- <div class="audit-item">
                        <i class="icon" style="background-image: url('./img/reports/icon-cross.svg');"></i>
                        <h5>Links do not have descriptive text: 5 links found</h5>
                        <p>Lorem ipsum dolor sit amet</p>
                    </div>
                    <div class="audit-item">
                        <i class="icon" style="background-image: url('./img/reports/icon-cross.svg');"></i>
                        <h5>Document doesn't use legible font sizes</h5>
                        <p>Lorem ipsum dolor sit amet</p>
                        <p class="red">93.41% of text is too small.</p>
				</div>-->
			</div>
		</div>
		<div class="hl_report--hero">
			<div class="hl_report--hero-img">
				<img src="../../assets/pmd/img/reports/img-notebook.png">
			</div>
			<div class="hl_report--hero-text">
				<h2>Dominate your local market</h2>
				<p>Ranking high for your local market is critical to your success, when your customers are looking for your business they are looking online. You more than just a great website to rank high on Google. We've created this free report on your business to show exactly where you stand and where you can improve to truly dominate local.</p>
				<div class="btns">
					<a
						:href="company.website"
						target="_blank"
						class="btn btn-white"
						style="background-color:#14e075"
					>Get started</a>
					<p>{{userInfo.phone }}</p>
				</div>
			</div>
		</div>
	</div>
	<!-- </body>

	</html>-->
</template>



<script lang="ts">
import Vue from 'vue';
import * as $ from 'jquery';
import { ScanReport, Location, Company, User } from '../../models';
import { mapState } from 'vuex';
import { UserState } from '@/store/state_models';
import UserInfoIcon from '@/assets/pmd/img/reports/icon-mail.svg'
import Phone2Icon from '@/assets/pmd/img/reports/icon-phone2.svg'
import PinIcon from '@/assets/pmd/img/reports/icon-pin.svg'
import WebIcon from '@/assets/pmd/img/reports/icon-web.svg'
import HouseIcon from '@/assets/pmd/img/reports/icon-house.svg'
import PhoneIcon from '@/assets/pmd/img/reports/icon-phone.svg'
import CheckIcon from '@/assets/pmd/img/reports/icon-check.svg'
import MinusIcon from '@/assets/pmd/img/reports/icon-minus.svg'
import StarFilledIcon from '@/assets/pmd/img/reports/icon-star-filled.svg'
import StarHalfIcon from '@/assets/pmd/img/reports/icon-star-half.svg'
import StarIcon from '@/assets/pmd/img/reports/icon-star.svg'
import PlusIcon from '@/assets/pmd/img/reports/icon-plus.svg'
import MetricIcon from '@/assets/pmd/img/reports/icon-metric.svg'
import LightningIcon from '@/assets/pmd/img/reports/icon-lightning.svg'


const Avatar = () => import( '../components/Avatar.vue');

export default Vue.extend({
	components: {
		Avatar,
		UserInfoIcon,
		Phone2Icon,
		PinIcon,
		WebIcon,
		HouseIcon,
		PhoneIcon,
		CheckIcon,
		MinusIcon,
		StarFilledIcon,
		StarHalfIcon,
		StarIcon,
		PlusIcon,
		MetricIcon,
		LightningIcon,
	},
	data() {
		return {
			scanReport: {} as ScanReport,
			location: {} as Location,
			company: {} as Company,
			userInfo: {} as User,
			currentScanReportId: '',
			logoUrl: '',
		};
	},
	watch: {
		'$route.params.scan_report_id': async function (id) {
			this.currentScanReportId = id;
		},
	},
	async created() {
		console.log("this.$options._scopeId:", this.$options._scopeId)
		this.currentScanReportId = this.$router.currentRoute.params.scan_report_id;
		var _self = this;
		console.log('Created:', this.currentScanReportId);

		this.scanReport = await ScanReport.getById(this.currentScanReportId);
		this.location = new Location(await this.$store.dispatch('locations/getById', this.scanReport.locationId));
		console.log('location:', this.location);

		this.getLogoUrl();

		console.log('scanReport:', this.scanReport);

		this.company = await Company.getById(this.location.companyId);
		this.userInfo = await User.getById(this.user.id)

		this.loadPercentGraphs();


	},
	mounted() {
		document.body.style.backgroundColor = "#eee";
	},
	computed: {
		...mapState('user', {
			user: (s: UserState) => {
				return s.user ? new User(s.user) : undefined;
			},
		}),
		lighthouseData: function () {
			if (this.scanReport && this.scanReport.lighthouseData) {
				return JSON.parse(this.scanReport.lighthouseData);
			}
		},
		metricAudits() {
			if (this.lighthouseData) {
				let perf_audits: { [key: string]: any } | undefined = lodash.find(this.lighthouseData.categories, { id: "performance" });
				if (perf_audits) {
					return perf_audits.auditRefs.filter((audit: { [key: string]: any }) => {
						return audit.group === 'metrics'
					});
				}
			}
		},
		opportunities() {
			if (this.lighthouseData) {
				var perf_audits: { [key: string]: any } | undefined = lodash.find(this.lighthouseData.categories, { id: "performance" });
				if (perf_audits) {
					return perf_audits.auditRefs.filter((audit: { [key: string]: any }) => {
						return audit.group === 'load-opportunities'
					});
				}

			}
		},
		// opportunities() {
		//     if (this.perfHints) {
		//         return this.perfHints.filter((hint: { [key: string]: any }) => hint.weight > 0);
		//     }
		// },
		opportunityScale() {
			if (this.opportunities) {
				const maxWaste = Math.max(...this.opportunities.map((audit: { [key: string]: any }) => this.lighthouseData.audits[audit.id].rawValue));
				const scale = Math.ceil(maxWaste / 1000) * 1000;
				return scale;
			}
		},
		pwaAudits() {
			if (this.lighthouseData) {
				let perf_audits: { [key: string]: any } | undefined = lodash.find(this.lighthouseData.categories, { id: "pwa" });
				if (perf_audits) {
					return perf_audits.auditRefs;
				}
			}
		},
		failedPwaAudits() {


			let audits = [];
			if (this.seoAudits) {
				let seoAuditCategories = this.pwaAudits.filter((audit: { [key: string]: any }) => audit.weight >= 1);
				if (seoAuditCategories && seoAuditCategories.length > 0) {
					seoAuditCategories.forEach(catName => {
						let category = this.lighthouseData.audits[catName.id];
						if (category.score < 1) {
							audits.push(category);
						}
					});
				}

			}
			return audits;
		},
		bestPracticeAudits() {
			if (this.lighthouseData) {
				var perf_audits: { [key: string]: any } | undefined = lodash.find(this.lighthouseData.categories, { id: "best-practices" });
				if (perf_audits) {
					return perf_audits.auditRefs;
				}
			}
		},
		failedBestPracticeAudits() {


			let audits = [];
			if (this.seoAudits) {
				let seoAuditCategories = this.bestPracticeAudits.filter((audit: { [key: string]: any }) => audit.weight >= 1);
				if (seoAuditCategories && seoAuditCategories.length > 0) {
					seoAuditCategories.forEach(catName => {
						let category = this.lighthouseData.audits[catName.id];
						if (category.score < 1) {
							audits.push(category);
						}
					});
				}

			}
			return audits;
		},
		seoAudits() {
			if (this.lighthouseData) {
				var perf_audits: { [key: string]: any } | undefined = lodash.find(this.lighthouseData.categories, { id: "seo" });
				if (perf_audits) {
					return perf_audits.auditRefs;
				}
			}
		},
		failedSeoAudits() {
			let audits = [];
			if (this.seoAudits) {
				let seoAuditCategories = this.seoAudits.filter((audit: { [key: string]: any }) => audit.weight >= 1);
				if (seoAuditCategories && seoAuditCategories.length > 0) {
					seoAuditCategories.forEach(catName => {
						let category = this.lighthouseData.audits[catName.id];
						if (category.score < 1) {
							audits.push(category);
						}
					});
				}

			}
			return audits;
		},
		metricScale() {
			var max = 1;
			if (this.metricAudits) {
				for (let key in this.metricAudits) {
					let value = this.metricAudits[key]
					console.log("key:", key)
					console.log("value:", value)
					let audit = this.lighthouseData.audits[value.id];
					console.log("audit:", audit)
					max = Math.max(max, audit.rawValue)
				}

				const thumbnailAudit = this.lighthouseData.audits["screenshot-thumbnails"];
				console.log("thumbnailAudit:", thumbnailAudit)
				if (thumbnailAudit) {


					const thumbnailResult = thumbnailAudit && thumbnailAudit.result;
					console.log("thumbnailResult:", thumbnailResult)

					if (thumbnailResult && thumbnailResult.details) {
						const thumbnailDetails = (thumbnailResult.details);
						max = Math.max(max, thumbnailDetails.scale);
					}
				}
			}

			return max
		},
		loadTime() {

			if (this.lighthouseData && this.lighthouseData.audits) {
				return this.lighthouseData.audits["first-meaningful-paint"].rawValue;
			}


		},
		// firstInteractive() {
		//         if (this.lighthouseData && this.lighthouseData.audits) {
		//             return this.lighthouseData.audits["interactive"].rawValue;
		//         }

		// },
		consistentlyInteractive() {
			if (this.lighthouseData && this.lighthouseData.audits) {
				return this.lighthouseData.audits["interactive"].rawValue;
			}
		},
		perceptualSpeedIndex() {
			if (this.lighthouseData && this.lighthouseData.audits) {
				return this.lighthouseData.audits["speed-index"].rawValue;
			}
		},
		perceptualSpeedIndexScore() {
			if (this.lighthouseData && this.lighthouseData.audits) {
				return (this.lighthouseData.audits["speed-index"].score * 100).toFixed(0);
			}
		},
		estimatedinputLatencyScore() {
			if (this.lighthouseData && this.lighthouseData.audits) {
				return (this.lighthouseData.audits["estimated-input-latency"].score * 100).toFixed(0);
			}
		},
		estimatedinputLatency() {
			if (this.lighthouseData && this.lighthouseData.audits) {
				return this.lighthouseData.audits["estimated-input-latency"].rawValue;
			}
		},
		websitePerformance() {
			let performance = 0
			if (this.lighthouseData && this.lighthouseData.categories) {
				let performance_score = lodash.find(this.lighthouseData.categories, { id: "performance" })
				if (performance_score) {
					performance = (Number(performance_score.score) * 100).toFixed(0)
				}


			}
			return Number(performance);
		},
		websitePwa() {
			let performance = 0
			if (this.lighthouseData && this.lighthouseData.categories) {
				let performance_score = lodash.find(this.lighthouseData.categories, { id: "pwa" })
				if (performance_score) {
					performance = (Number(performance_score.score) * 100).toFixed(0)
				}


			}
			return Number(performance);
		},
		websiteAccessibility() {
			let performance = 0
			if (this.lighthouseData && this.lighthouseData.categories) {
				let performance_score = lodash.find(this.lighthouseData.categories, { id: "accessibility" })
				if (performance_score) {
					performance = (Number(performance_score.score) * 100).toFixed(0)
				}


			}
			return Number(performance);
		},
		websiteBestPractices() {
			let performance = 0
			if (this.lighthouseData && this.lighthouseData.categories) {
				let performance_score = lodash.find(this.lighthouseData.categories, { id: "best-practices" })
				if (performance_score) {
					performance = (Number(performance_score.score) * 100).toFixed(0)
				}


			}
			return Number(performance);
		},
		websiteSEO() {
			let performance = 0
			if (this.lighthouseData && this.lighthouseData.categories) {
				let performance_score = lodash.find(this.lighthouseData.categories, { id: "seo" })
				if (performance_score) {
					performance = (Number(performance_score.score) * 100).toFixed(0)
				}


			}
			return Number(performance);
		},
		websiteOverall() {
			return Number(((this.websitePerformance + this.websitePwa + this.websiteAccessibility + this.websiteBestPractices + this.websiteSEO) / 5).toFixed(0))
		},
		mobileScreenShot() {
			if (this.scanReport && this.scanReport.mobileScreenShot) {
				return this.scanReport.mobileScreenShot
			} else {
				return "";
			}
		},
		desktopScreenShot() {
			if (this.scanReport && this.scanReport.desktopScreenShot) {
				return this.scanReport.desktopScreenShot
			} else {
				return "";
			}
		},
		googlePlacesData(): any {
			if (this.scanReport && this.scanReport.googlePlacesData) {
				return JSON.parse(this.scanReport.googlePlacesData);
			}
		},
		fbData(): any {
			if (this.scanReport && this.scanReport.facebookData) {
				return JSON.parse(this.scanReport.facebookData);
			}
		},
		twitterData(): any {
			if (this.scanReport && this.scanReport.twitterData) {
				return JSON.parse(this.scanReport.twitterData);
			}
		},

		twitterFollowers() {
			if (this.twitterData && this.twitterData.users) {
				return this.twitterData.users.followers_count;
			}
		},
		twitterFriends() {
			if (this.twitterData && this.twitterData.users) {
				return this.twitterData.users.friends_count;
			}
		},
		twitterLikes() {
			if (this.twitterData && this.twitterData.users) {
				return this.twitterData.users.favourites_count;
			}
		},
		totalPosts() {
			let likes = 0;
			if (this.fbData && this.fbData.page_stats) {

				likes = this.fbData.page_stats.length;
			}
			return likes
		},
		socialScore() {
			return Number(((this.totalPosts / 30) * 100).toFixed(0));
		},
		totalLikes() {

			let likes = 0;
			if (this.fbData && this.fbData.page_stats) {

				for (let key in this.fbData.page_stats) {
					let stat = this.fbData.page_stats[key];
					if (stat && stat.likes) {
						likes += stat.likes.summary.total_count
					}
				}
			}
			return likes
		},
		totalReactions() {
			let likes = 0;
			if (this.fbData && this.fbData.page_stats) {

				for (let key in this.fbData.page_stats) {
					let stat = this.fbData.page_stats[key];
					if (stat && stat.reactions) {
						likes += stat.reactions.summary.total_count
					}
				}
			}
			return likes
		},
		totalShares() {
			let likes = 0;
			if (this.fbData && this.fbData.page_stats) {

				for (let key in this.fbData.page_stats) {
					let stat = this.fbData.page_stats[key];
					if (stat && stat.shares) {
						likes += stat.shares.count;
					}
				}
			}
			return likes
		},
		avgLikesPerPost() {
			let avg = 0;
			if (this.totalLikes > 0 && this.totalPosts > 0) {
				avg = Number((this.totalLikes / this.totalPosts).toFixed(0));
			}
			return avg;
		},
		avgSharesPerPost() {
			let avg = 0;
			if (this.totalShares > 0 && this.totalPosts > 0) {
				avg = Number((this.totalShares / this.totalPosts).toFixed(0));
			}
			return avg;
		},
		yextData(): any {
			if (this.scanReport && this.scanReport.yextData) {
				return JSON.parse(this.scanReport.yextData);
			}
		},
		yextLetterGrade() {
			var score = 0;

			if (this.yextData && this.yextData.reports && this.yextData.reports.length > 0) {
				this.yextData.reports.forEach(function (report: any) {
					if (report.listingFound) {
						score++;
					}
				})
				score = Number(((score / this.yextData.reports.length) * 100).toFixed(0));
			}

			if (score > 90) {
				return "A"
			} else if (score >= 80) {
				return "B"
			} else if (score >= 70) {
				return "C"
			} else if (score >= 65) {
				return "D"
			} else {
				return "F"
			}

		},
		yextOverallPercentage() {
			var score = 0;

			if (this.yextData && this.yextData.reports && this.yextData.reports.length > 0) {
				this.yextData.reports.forEach(function (report) {
					if (report.listingFound) {
						score++;
					}
				})
				score = ((score / this.yextData.reports.length) * 100).toFixed(0);
			}
			return score;
		},
		yextBusinessNameScore() {
			var score = 0;

			if (this.yextData && this.yextData.reports && this.yextData.reports.length > 0) {
				this.yextData.reports.forEach(function (report) {
					if (report.matchName) {
						score++;
					}
				})

				score = ((score / this.yextData.reports.length) * 100).toFixed(0);
			}
			return score;
		},
		yextBusinessAddressScore() {
			var score = 0;

			if (this.yextData && this.yextData.reports && this.yextData.reports.length > 0) {
				this.yextData.reports.forEach(function (report) {
					if (report.matchAddress) {
						score++;
					}
				})
				score = ((score / this.yextData.reports.length) * 100).toFixed(0);
			}
			return score;
		},
		yextBusinessPhoneScore() {
			var score = 0;

			if (this.yextData && this.yextData.reports && this.yextData.reports.length > 0) {
				this.yextData.reports.forEach(function (report) {
					if (report.matchPhone) {
						score++;
					}
				})
				score = ((score / this.yextData.reports.length) * 100).toFixed(0);
			}
			return score;
		},
		yextNumberRatings() {
			var ratings = 0;

			if (this.yextData && this.yextData.reports && this.yextData.reports.length > 0) {
				this.yextData.reports.forEach(function (report) {
					if ("reviewCount" in report) {
						ratings += report.reviewCount;
					}
				})

			}
			return ratings;
		},
		yextRatingsPercent() {
			var ratings = 0;
			var count = 0;

			if (this.yextData && this.yextData.reports && this.yextData.reports.length > 0) {
				this.yextData.reports.forEach(function (report) {
					if (report.hasCount && review.hasScore) {
						ratings += (report.averageCountPercentile + report.averageRatingPercentile) / 2;

					}
				})

				// ratings = (ratings/count)*100;
				// ratings = ratings.toFixed(0);

			}
			return ratings;
		},
		yextAvgScore() {
			var score = 0;
			var count = 0;


			if (this.yextData && this.yextData.reports && this.yextData.reports.length > 0) {
				this.yextData.reports.forEach(function (report) {
					if (report.averageReviewScore) {
						score += report.averageReviewScore;
						count++;
					}
				})

				score = ((score / count)).toFixed(2);

			}
			return score;
		},
		yextReviewPercentile() {
			if (this.yextData && this.yextData.reviewCountPercentile && this.yextData.reviewScorePercentile) {
				return (this.yextData.reviewCountPercentile + this.yextData.reviewScorePercentile) / 2;
			}
		},
		yextGoogleListing() {
			var result = undefined;
			if (this.yextData && this.yextData.reports && this.yextData.reports.length > 0) {
				result = lodash.filter(this.yextData.reports, { 'brandId': 247 })
				if (result.length == 0) {
					result = undefined;
				} else {
					result = result[0]
				}
			}
			return result;
		},
		yextYelpListing() {
			var result = undefined;
			if (this.yextData && this.yextData.reports && this.yextData.reports.length > 0) {
				result = lodash.filter(this.yextData.reports, { 'brandId': 15 })
				if (result.length == 0) {
					result = undefined;
				} else {
					result = result[0]
				}
			}
			return result;
		},
		yextBingListing() {
			var result = undefined;
			if (this.yextData && this.yextData.reports && this.yextData.reports.length > 0) {
				result = lodash.filter(this.yextData.reports, { 'brandId': 24 })
				if (result.length == 0) {
					result = undefined;
				} else {
					result = result[0]
				}
			}
			return result;
		},
		yextFacbookListing() {
			var result = undefined;
			if (this.yextData && this.yextData.reports && this.yextData.reports.length > 0) {
				result = lodash.filter(this.yextData.reports, { 'brandId': 71 })
				if (result.length == 0) {
					result = undefined;
				} else {
					result = result[0]
				}
			}
			return result;
		},
		yextFoursquareListing() {
			var result = undefined;
			if (this.yextData && this.yextData.reports && this.yextData.reports.length > 0) {
				result = lodash.filter(this.yextData.reports, { 'brandId': 16 })
				if (result.length == 0) {
					result = undefined;
				} else {
					result = result[0]
				}

			}
			return result;
		},
		foursquareReviews() {
			if (this.yextFoursquareListing && this.yextFoursquareListing.reviewCount) {
				return this.yextFoursquareListing.reviewCount
			} else {
				return 0
			}
		},
		facebookReviews() {
			if (this.yextFacbookListing && this.yextFacbookListing.reviewCount) {
				return this.yextFacbookListing.reviewCount
			} else {
				return 0
			}
		},
		yelpReviews() {
			if (this.yextYelpListing && this.yextYelpListing.reviewCount) {
				return this.yextYelpListing.reviewCount
			} else {
				return 0
			}
		},


	},
	methods: {
		loadPercentGraphs() {
			var _self = this;

			var DEFAULTS = {
				backgroundColor: '#b3cef6',
				progressColor: '#4b86db',
				percent: 75,
				duration: 2000
			};

			$(".progress-bar-circle, .progress-bar-circle-sm").each(function () {
				var $target = $(this);
				// console.log("Got target:", $target)

				var opts = {
					backgroundColor: $target.data('color') ? $target.data('color').split(',')[0] : DEFAULTS.backgroundColor,
					progressColor: $target.data('color') ? $target.data('color').split(',')[1] : DEFAULTS.progressColor,
					percent: $target.data('percent') ? $target.data('percent') : DEFAULTS.percent,
					duration: $target.data('duration') ? $target.data('duration') : DEFAULTS.duration
				};
				// console.log(opts);

				$target.append('<div ' + _self.$options._scopeId + ' class="background"></div><div ' + _self.$options._scopeId + ' class="rotate"></div><div ' + _self.$options._scopeId + ' class="left"></div><div ' + _self.$options._scopeId + ' class="right"></div><div ' + _self.$options._scopeId + ' class=""><span ' + _self.$options._scopeId + '>' + opts.percent + '%</span></div>');

				$target.find('.background').css('background-color', opts.backgroundColor);
				$target.find('.left').css('background-color', opts.backgroundColor);
				$target.find('.rotate').css('background-color', opts.progressColor);
				$target.find('.right').css('background-color', opts.progressColor);

				var $rotate = $target.find('.rotate');
				setTimeout(function () {
					$rotate.css({
						'transition': 'transform ' + opts.duration + 'ms linear',
						'transform': 'rotate(' + opts.percent * 3.6 + 'deg)'
					});
				}, 1);

				if (opts.percent > 50) {
					var animationRight = 'toggle ' + (opts.duration / opts.percent * 50) + 'ms step-end';
					var animationLeft = 'toggle ' + (opts.duration / opts.percent * 50) + 'ms step-start';
					$target.find('.right').css({
						animation: animationRight,
						opacity: 1
					});
					$target.find('.left').css({
						animation: animationLeft,
						opacity: 0
					});
				}
			});
		},
		computeProgressClass(progressPercentage: number) {
			if (progressPercentage > 90) {
				return "progress --green"
			} else if (progressPercentage <= 50) {
				return "progress --red"
			} else if (progressPercentage > 50) {
				return "progress --yellow"
			}
		},
		computeProgressClassReverse(progressPercentage: number) {
			if (progressPercentage > 90) {
				return "progress --red"
			} else if (progressPercentage <= 20) {
				return "progress --green"
			} else if (progressPercentage > 20) {
				return "progress --yellow"
			}
		},
		computeTextClass(percent: number) {
			if (percent > 90) {
				return "green"
			} else if (percent <= 50) {
				return "red"
			} else if (percent > 50) {
				return "yellow"
			}
		},
		computeLetterGradeClass(letterGrade: string) {
			if (letterGrade == "A") {
				return "letter --a"
			} else if (letterGrade == "B") {
				return "letter --b"
			} else if (letterGrade == "C") {
				return "letter --c"
			} else if (letterGrade == "D" || letterGrade == "F") {
				return "letter --d"
			}
		},
		computeLetterGradeClassByRating(rating: number) {

			if (rating > 4.5) {
				return "letter --a"
			} else if (rating >= 4) {
				return "letter --b"
			} else if (rating >= 3.5) {
				return "letter --c"
			} else {
				return "letter --d"
			}
		},
		computeLetterGradeByRating(rating: number) {

			if (rating > 4.5) {
				return "A"
			} else if (rating >= 4) {
				return "B"
			} else if (rating >= 3.5) {
				return "C"
			} else {
				return "D"
			}
		},
		computeLetterGradeByPercent(percent: number) {
			if (percent > 90) {
				return "A"
			} else if (percent >= 80) {
				return "B"
			} else if (percent >= 70) {
				return "C"
			} else if (percent >= 65) {
				return "D"
			} else {
				return "F"
			}
		},
		getDataColor(percent: number) {
			if (percent > 90) {
				return "#e7e8ea,#14e075"
			} else if (percent <= 50) {
				return "#e7e8ea,#ff4646"
			} else if (percent > 50) {
				return "#e7e8ea,#ffa604"
			}
		},
		wholeStars(starRating: number): number {
			if (starRating && starRating > 0) {
				return Math.floor(starRating);
			} else {
				return 0;
			}
		},
		halfStars(starRating: number): number {
			if (starRating && starRating > 0) {
				return Math.ceil(starRating - this.wholeStars(starRating));
			} else {
				return 0;
			}
		},
		emptyStars(starRating: number): number {
			if (starRating && starRating > 0) {
				return 5 - this.wholeStars(starRating) - this.halfStars(starRating);
			} else {
				return 5;
			}
		},
		async getLogoUrl() {
			if (this.location.logoUrl) {
				this.logoUrl = this.location.logoUrl;
			} else if (this.location.website) {
				let website = this.stripWebsite(this.location.website);
				let url = 'https://logo.clearbit.com/' + website;
				try {
					this.logoUrl = await this.testImageLoad(url);
				} catch (err) { }
			}
		},
		stripWebsite(website: string) {
			return new URL(website).hostname.replace('www.', '');
		},
		testImageLoad(src: string): Promise<string> {
			return new Promise((resolve, reject) => {
				let img = new Image();
				img.onload = function () {
					resolve(src);
				};
				img.onerror = () => {
					reject();
				};
				img.onabort = () => {
					reject();
				};
				img.src = src;
			});
		},
	},
	beforeDestroy() {
		if (this.$private.cancelLocationSubscription) {
			this.$private.cancelLocationSubscription();
		}
	},
});
</script>
<style scoped>
@import url("https://fonts.googleapis.com/css?family=Roboto:300,400,500,700"); /*!
 * Bootstrap Reboot v4.0.0 (https://getbootstrap.com)
 * Copyright 2011-2018 The Bootstrap Authors
 * Copyright 2011-2018 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 * Forked from Normalize.css, licensed MIT (https://github.com/necolas/normalize.css/blob/master/LICENSE.md)
 */
*,
*::before,
*::after {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

html {
	font-family: sans-serif;
	line-height: 1.15;
	-webkit-text-size-adjust: 100%;
	-ms-text-size-adjust: 100%;
	-ms-overflow-style: scrollbar;
	-webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

body {
	background: #eee;
}

@-ms-viewport {
	width: device-width;
}

article,
aside,
dialog,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section {
	display: block;
}

body {
	margin: 0;
	font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
		"Helvetica Neue", Arial, sans-serif, "Apple Color Emoji",
		"Segoe UI Emoji", "Segoe UI Symbol";
	font-size: 1rem;
	font-weight: 400;
	line-height: 1.5;
	color: #212529;
	text-align: left;
	background-color: #fff;
}

[tabindex="-1"]:focus {
	outline: 0 !important;
}

hr {
	-webkit-box-sizing: content-box;
	box-sizing: content-box;
	height: 0;
	overflow: visible;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	margin-top: 0;
	margin-bottom: 0.5rem;
}

p {
	margin-top: 0;
	margin-bottom: 1rem;
}

abbr[title],
abbr[data-original-title] {
	text-decoration: underline;
	-webkit-text-decoration: underline dotted;
	text-decoration: underline dotted;
	cursor: help;
	border-bottom: 0;
}

address {
	margin-bottom: 1rem;
	font-style: normal;
	line-height: inherit;
}

ol,
ul,
dl {
	margin-top: 0;
	margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
	margin-bottom: 0;
}

dt {
	font-weight: 700;
}

dd {
	margin-bottom: 0.5rem;
	margin-left: 0;
}

blockquote {
	margin: 0 0 1rem;
}

dfn {
	font-style: italic;
}

b,
strong {
	font-weight: bolder;
}

small {
	font-size: 80%;
}

sub,
sup {
	position: relative;
	font-size: 75%;
	line-height: 0;
	vertical-align: baseline;
}

sub {
	bottom: -0.25em;
}

sup {
	top: -0.5em;
}

a {
	color: #2a6cff;
	text-decoration: none;
	background-color: transparent;
	-webkit-text-decoration-skip: objects;
}

a:hover {
	color: #004cf6;
	text-decoration: underline;
}

a:not([href]):not([tabindex]) {
	color: inherit;
	text-decoration: none;
}

a:not([href]):not([tabindex]):hover,
a:not([href]):not([tabindex]):focus {
	color: inherit;
	text-decoration: none;
}

a:not([href]):not([tabindex]):focus {
	outline: 0;
}

pre,
code,
kbd,
samp {
	font-family: monospace, monospace;
	font-size: 1em;
}

pre {
	margin-top: 0;
	margin-bottom: 1rem;
	overflow: auto;
	-ms-overflow-style: scrollbar;
}

figure {
	margin: 0 0 1rem;
}

img {
	vertical-align: middle;
	border-style: none;
}

svg:not(:root) {
	overflow: hidden;
}

table {
	border-collapse: collapse;
}

caption {
	padding-top: 0.75rem;
	padding-bottom: 0.75rem;
	color: #6c757d;
	text-align: left;
	caption-side: bottom;
}

th {
	text-align: inherit;
}

label {
	display: inline-block;
	margin-bottom: 0.5rem;
}

button {
	border-radius: 0;
}

button:focus {
	outline: 1px dotted;
	outline: 5px auto -webkit-focus-ring-color;
}

input,
button,
select,
optgroup,
textarea {
	margin: 0;
	font-family: inherit;
	font-size: inherit;
	line-height: inherit;
}

button,
input {
	overflow: visible;
}

button,
select {
	text-transform: none;
}

button,
html [type="button"],
[type="reset"],
[type="submit"] {
	-webkit-appearance: button;
}

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
	padding: 0;
	border-style: none;
}

input[type="radio"],
input[type="checkbox"] {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	padding: 0;
}

input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"] {
	-webkit-appearance: listbox;
}

textarea {
	overflow: auto;
	resize: vertical;
}

fieldset {
	min-width: 0;
	padding: 0;
	margin: 0;
	border: 0;
}

legend {
	display: block;
	width: 100%;
	max-width: 100%;
	padding: 0;
	margin-bottom: 0.5rem;
	font-size: 1.5rem;
	line-height: inherit;
	color: inherit;
	white-space: normal;
}

progress {
	vertical-align: baseline;
}

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
	height: auto;
}

[type="search"] {
	outline-offset: -2px;
	-webkit-appearance: none;
}

[type="search"]::-webkit-search-cancel-button,
[type="search"]::-webkit-search-decoration {
	-webkit-appearance: none;
}

::-webkit-file-upload-button {
	font: inherit;
	-webkit-appearance: button;
}

output {
	display: inline-block;
}

summary {
	display: list-item;
	cursor: pointer;
}

template {
	display: none;
}

[hidden] {
	display: none !important;
}

/*!
 * Bootstrap Grid v4.0.0 (https://getbootstrap.com)
 * Copyright 2011-2018 The Bootstrap Authors
 * Copyright 2011-2018 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */
@-ms-viewport {
	width: device-width;
}

html {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-ms-overflow-style: scrollbar;
}

*,
*::before,
*::after {
	-webkit-box-sizing: inherit;
	box-sizing: inherit;
}

.container {
	width: 100%;
	padding-right: 15px;
	padding-left: 15px;
	margin-right: auto;
	margin-left: auto;
}

@media (min-width: 576px) {
	.container {
		max-width: 540px;
	}
}

@media (min-width: 768px) {
	.container {
		max-width: 720px;
	}
}

@media (min-width: 992px) {
	.container {
		max-width: 960px;
	}
}

@media (min-width: 1200px) {
	.container {
		max-width: 1140px;
	}
}

.container-fluid {
	width: 100%;
	padding-right: 15px;
	padding-left: 15px;
	margin-right: auto;
	margin-left: auto;
}

.row {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-ms-flex-wrap: wrap;
	flex-wrap: wrap;
	margin-right: -15px;
	margin-left: -15px;
}

.no-gutters {
	margin-right: 0;
	margin-left: 0;
}

.no-gutters > .col,
.no-gutters > [class*="col-"] {
	padding-right: 0;
	padding-left: 0;
}

.col-1,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-10,
.col-11,
.col-12,
.col,
.col-auto,
.col-sm-1,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-sm-5,
.col-sm-6,
.col-sm-7,
.col-sm-8,
.col-sm-9,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm,
.col-sm-auto,
.col-md-1,
.col-md-2,
.col-md-3,
.col-md-4,
.col-md-5,
.col-md-6,
.col-md-7,
.col-md-8,
.col-md-9,
.col-md-10,
.col-md-11,
.col-md-12,
.col-md,
.col-md-auto,
.col-lg-1,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg,
.col-lg-auto,
.col-xl-1,
.col-xl-2,
.col-xl-3,
.col-xl-4,
.col-xl-5,
.col-xl-6,
.col-xl-7,
.col-xl-8,
.col-xl-9,
.col-xl-10,
.col-xl-11,
.col-xl-12,
.col-xl,
.col-xl-auto {
	position: relative;
	width: 100%;
	min-height: 1px;
	padding-right: 15px;
	padding-left: 15px;
}

.col {
	-ms-flex-preferred-size: 0;
	flex-basis: 0;
	-webkit-box-flex: 1;
	-ms-flex-positive: 1;
	flex-grow: 1;
	max-width: 100%;
}

.col-auto {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 auto;
	flex: 0 0 auto;
	width: auto;
	max-width: none;
}

.col-1 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 8.33333%;
	flex: 0 0 8.33333%;
	max-width: 8.33333%;
}

.col-2 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 16.66667%;
	flex: 0 0 16.66667%;
	max-width: 16.66667%;
}

.col-3 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 25%;
	flex: 0 0 25%;
	max-width: 25%;
}

.col-4 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 33.33333%;
	flex: 0 0 33.33333%;
	max-width: 33.33333%;
}

.col-5 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 41.66667%;
	flex: 0 0 41.66667%;
	max-width: 41.66667%;
}

.col-6 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 50%;
	flex: 0 0 50%;
	max-width: 50%;
}

.col-7 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 58.33333%;
	flex: 0 0 58.33333%;
	max-width: 58.33333%;
}

.col-8 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 66.66667%;
	flex: 0 0 66.66667%;
	max-width: 66.66667%;
}

.col-9 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 75%;
	flex: 0 0 75%;
	max-width: 75%;
}

.col-10 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 83.33333%;
	flex: 0 0 83.33333%;
	max-width: 83.33333%;
}

.col-11 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 91.66667%;
	flex: 0 0 91.66667%;
	max-width: 91.66667%;
}

.col-12 {
	-webkit-box-flex: 0;
	-ms-flex: 0 0 100%;
	flex: 0 0 100%;
	max-width: 100%;
}

.order-first {
	-webkit-box-ordinal-group: 0;
	-ms-flex-order: -1;
	order: -1;
}

.order-last {
	-webkit-box-ordinal-group: 14;
	-ms-flex-order: 13;
	order: 13;
}

.order-0 {
	-webkit-box-ordinal-group: 1;
	-ms-flex-order: 0;
	order: 0;
}

.order-1 {
	-webkit-box-ordinal-group: 2;
	-ms-flex-order: 1;
	order: 1;
}

.order-2 {
	-webkit-box-ordinal-group: 3;
	-ms-flex-order: 2;
	order: 2;
}

.order-3 {
	-webkit-box-ordinal-group: 4;
	-ms-flex-order: 3;
	order: 3;
}

.order-4 {
	-webkit-box-ordinal-group: 5;
	-ms-flex-order: 4;
	order: 4;
}

.order-5 {
	-webkit-box-ordinal-group: 6;
	-ms-flex-order: 5;
	order: 5;
}

.order-6 {
	-webkit-box-ordinal-group: 7;
	-ms-flex-order: 6;
	order: 6;
}

.order-7 {
	-webkit-box-ordinal-group: 8;
	-ms-flex-order: 7;
	order: 7;
}

.order-8 {
	-webkit-box-ordinal-group: 9;
	-ms-flex-order: 8;
	order: 8;
}

.order-9 {
	-webkit-box-ordinal-group: 10;
	-ms-flex-order: 9;
	order: 9;
}

.order-10 {
	-webkit-box-ordinal-group: 11;
	-ms-flex-order: 10;
	order: 10;
}

.order-11 {
	-webkit-box-ordinal-group: 12;
	-ms-flex-order: 11;
	order: 11;
}

.order-12 {
	-webkit-box-ordinal-group: 13;
	-ms-flex-order: 12;
	order: 12;
}

.offset-1 {
	margin-left: 8.33333%;
}

.offset-2 {
	margin-left: 16.66667%;
}

.offset-3 {
	margin-left: 25%;
}

.offset-4 {
	margin-left: 33.33333%;
}

.offset-5 {
	margin-left: 41.66667%;
}

.offset-6 {
	margin-left: 50%;
}

.offset-7 {
	margin-left: 58.33333%;
}

.offset-8 {
	margin-left: 66.66667%;
}

.offset-9 {
	margin-left: 75%;
}

.offset-10 {
	margin-left: 83.33333%;
}

.offset-11 {
	margin-left: 91.66667%;
}

@media (min-width: 576px) {
	.col-sm {
		-ms-flex-preferred-size: 0;
		flex-basis: 0;
		-webkit-box-flex: 1;
		-ms-flex-positive: 1;
		flex-grow: 1;
		max-width: 100%;
	}

	.col-sm-auto {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 auto;
		flex: 0 0 auto;
		width: auto;
		max-width: none;
	}

	.col-sm-1 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 8.33333%;
		flex: 0 0 8.33333%;
		max-width: 8.33333%;
	}

	.col-sm-2 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 16.66667%;
		flex: 0 0 16.66667%;
		max-width: 16.66667%;
	}

	.col-sm-3 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 25%;
		flex: 0 0 25%;
		max-width: 25%;
	}

	.col-sm-4 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 33.33333%;
		flex: 0 0 33.33333%;
		max-width: 33.33333%;
	}

	.col-sm-5 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 41.66667%;
		flex: 0 0 41.66667%;
		max-width: 41.66667%;
	}

	.col-sm-6 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 50%;
		flex: 0 0 50%;
		max-width: 50%;
	}

	.col-sm-7 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 58.33333%;
		flex: 0 0 58.33333%;
		max-width: 58.33333%;
	}

	.col-sm-8 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 66.66667%;
		flex: 0 0 66.66667%;
		max-width: 66.66667%;
	}

	.col-sm-9 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 75%;
		flex: 0 0 75%;
		max-width: 75%;
	}

	.col-sm-10 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 83.33333%;
		flex: 0 0 83.33333%;
		max-width: 83.33333%;
	}

	.col-sm-11 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 91.66667%;
		flex: 0 0 91.66667%;
		max-width: 91.66667%;
	}

	.col-sm-12 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 100%;
		flex: 0 0 100%;
		max-width: 100%;
	}

	.order-sm-first {
		-webkit-box-ordinal-group: 0;
		-ms-flex-order: -1;
		order: -1;
	}

	.order-sm-last {
		-webkit-box-ordinal-group: 14;
		-ms-flex-order: 13;
		order: 13;
	}

	.order-sm-0 {
		-webkit-box-ordinal-group: 1;
		-ms-flex-order: 0;
		order: 0;
	}

	.order-sm-1 {
		-webkit-box-ordinal-group: 2;
		-ms-flex-order: 1;
		order: 1;
	}

	.order-sm-2 {
		-webkit-box-ordinal-group: 3;
		-ms-flex-order: 2;
		order: 2;
	}

	.order-sm-3 {
		-webkit-box-ordinal-group: 4;
		-ms-flex-order: 3;
		order: 3;
	}

	.order-sm-4 {
		-webkit-box-ordinal-group: 5;
		-ms-flex-order: 4;
		order: 4;
	}

	.order-sm-5 {
		-webkit-box-ordinal-group: 6;
		-ms-flex-order: 5;
		order: 5;
	}

	.order-sm-6 {
		-webkit-box-ordinal-group: 7;
		-ms-flex-order: 6;
		order: 6;
	}

	.order-sm-7 {
		-webkit-box-ordinal-group: 8;
		-ms-flex-order: 7;
		order: 7;
	}

	.order-sm-8 {
		-webkit-box-ordinal-group: 9;
		-ms-flex-order: 8;
		order: 8;
	}

	.order-sm-9 {
		-webkit-box-ordinal-group: 10;
		-ms-flex-order: 9;
		order: 9;
	}

	.order-sm-10 {
		-webkit-box-ordinal-group: 11;
		-ms-flex-order: 10;
		order: 10;
	}

	.order-sm-11 {
		-webkit-box-ordinal-group: 12;
		-ms-flex-order: 11;
		order: 11;
	}

	.order-sm-12 {
		-webkit-box-ordinal-group: 13;
		-ms-flex-order: 12;
		order: 12;
	}

	.offset-sm-0 {
		margin-left: 0;
	}

	.offset-sm-1 {
		margin-left: 8.33333%;
	}

	.offset-sm-2 {
		margin-left: 16.66667%;
	}

	.offset-sm-3 {
		margin-left: 25%;
	}

	.offset-sm-4 {
		margin-left: 33.33333%;
	}

	.offset-sm-5 {
		margin-left: 41.66667%;
	}

	.offset-sm-6 {
		margin-left: 50%;
	}

	.offset-sm-7 {
		margin-left: 58.33333%;
	}

	.offset-sm-8 {
		margin-left: 66.66667%;
	}

	.offset-sm-9 {
		margin-left: 75%;
	}

	.offset-sm-10 {
		margin-left: 83.33333%;
	}

	.offset-sm-11 {
		margin-left: 91.66667%;
	}
}

@media (min-width: 768px) {
	.col-md {
		-ms-flex-preferred-size: 0;
		flex-basis: 0;
		-webkit-box-flex: 1;
		-ms-flex-positive: 1;
		flex-grow: 1;
		max-width: 100%;
	}

	.col-md-auto {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 auto;
		flex: 0 0 auto;
		width: auto;
		max-width: none;
	}

	.col-md-1 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 8.33333%;
		flex: 0 0 8.33333%;
		max-width: 8.33333%;
	}

	.col-md-2 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 16.66667%;
		flex: 0 0 16.66667%;
		max-width: 16.66667%;
	}

	.col-md-3 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 25%;
		flex: 0 0 25%;
		max-width: 25%;
	}

	.col-md-4 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 33.33333%;
		flex: 0 0 33.33333%;
		max-width: 33.33333%;
	}

	.col-md-5 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 41.66667%;
		flex: 0 0 41.66667%;
		max-width: 41.66667%;
	}

	.col-md-6 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 50%;
		flex: 0 0 50%;
		max-width: 50%;
	}

	.col-md-7 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 58.33333%;
		flex: 0 0 58.33333%;
		max-width: 58.33333%;
	}

	.col-md-8 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 66.66667%;
		flex: 0 0 66.66667%;
		max-width: 66.66667%;
	}

	.col-md-9 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 75%;
		flex: 0 0 75%;
		max-width: 75%;
	}

	.col-md-10 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 83.33333%;
		flex: 0 0 83.33333%;
		max-width: 83.33333%;
	}

	.col-md-11 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 91.66667%;
		flex: 0 0 91.66667%;
		max-width: 91.66667%;
	}

	.col-md-12 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 100%;
		flex: 0 0 100%;
		max-width: 100%;
	}

	.order-md-first {
		-webkit-box-ordinal-group: 0;
		-ms-flex-order: -1;
		order: -1;
	}

	.order-md-last {
		-webkit-box-ordinal-group: 14;
		-ms-flex-order: 13;
		order: 13;
	}

	.order-md-0 {
		-webkit-box-ordinal-group: 1;
		-ms-flex-order: 0;
		order: 0;
	}

	.order-md-1 {
		-webkit-box-ordinal-group: 2;
		-ms-flex-order: 1;
		order: 1;
	}

	.order-md-2 {
		-webkit-box-ordinal-group: 3;
		-ms-flex-order: 2;
		order: 2;
	}

	.order-md-3 {
		-webkit-box-ordinal-group: 4;
		-ms-flex-order: 3;
		order: 3;
	}

	.order-md-4 {
		-webkit-box-ordinal-group: 5;
		-ms-flex-order: 4;
		order: 4;
	}

	.order-md-5 {
		-webkit-box-ordinal-group: 6;
		-ms-flex-order: 5;
		order: 5;
	}

	.order-md-6 {
		-webkit-box-ordinal-group: 7;
		-ms-flex-order: 6;
		order: 6;
	}

	.order-md-7 {
		-webkit-box-ordinal-group: 8;
		-ms-flex-order: 7;
		order: 7;
	}

	.order-md-8 {
		-webkit-box-ordinal-group: 9;
		-ms-flex-order: 8;
		order: 8;
	}

	.order-md-9 {
		-webkit-box-ordinal-group: 10;
		-ms-flex-order: 9;
		order: 9;
	}

	.order-md-10 {
		-webkit-box-ordinal-group: 11;
		-ms-flex-order: 10;
		order: 10;
	}

	.order-md-11 {
		-webkit-box-ordinal-group: 12;
		-ms-flex-order: 11;
		order: 11;
	}

	.order-md-12 {
		-webkit-box-ordinal-group: 13;
		-ms-flex-order: 12;
		order: 12;
	}

	.offset-md-0 {
		margin-left: 0;
	}

	.offset-md-1 {
		margin-left: 8.33333%;
	}

	.offset-md-2 {
		margin-left: 16.66667%;
	}

	.offset-md-3 {
		margin-left: 25%;
	}

	.offset-md-4 {
		margin-left: 33.33333%;
	}

	.offset-md-5 {
		margin-left: 41.66667%;
	}

	.offset-md-6 {
		margin-left: 50%;
	}

	.offset-md-7 {
		margin-left: 58.33333%;
	}

	.offset-md-8 {
		margin-left: 66.66667%;
	}

	.offset-md-9 {
		margin-left: 75%;
	}

	.offset-md-10 {
		margin-left: 83.33333%;
	}

	.offset-md-11 {
		margin-left: 91.66667%;
	}
}

@media (min-width: 992px) {
	.col-lg {
		-ms-flex-preferred-size: 0;
		flex-basis: 0;
		-webkit-box-flex: 1;
		-ms-flex-positive: 1;
		flex-grow: 1;
		max-width: 100%;
	}

	.col-lg-auto {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 auto;
		flex: 0 0 auto;
		width: auto;
		max-width: none;
	}

	.col-lg-1 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 8.33333%;
		flex: 0 0 8.33333%;
		max-width: 8.33333%;
	}

	.col-lg-2 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 16.66667%;
		flex: 0 0 16.66667%;
		max-width: 16.66667%;
	}

	.col-lg-3 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 25%;
		flex: 0 0 25%;
		max-width: 25%;
	}

	.col-lg-4 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 33.33333%;
		flex: 0 0 33.33333%;
		max-width: 33.33333%;
	}

	.col-lg-5 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 41.66667%;
		flex: 0 0 41.66667%;
		max-width: 41.66667%;
	}

	.col-lg-6 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 50%;
		flex: 0 0 50%;
		max-width: 50%;
	}

	.col-lg-7 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 58.33333%;
		flex: 0 0 58.33333%;
		max-width: 58.33333%;
	}

	.col-lg-8 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 66.66667%;
		flex: 0 0 66.66667%;
		max-width: 66.66667%;
	}

	.col-lg-9 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 75%;
		flex: 0 0 75%;
		max-width: 75%;
	}

	.col-lg-10 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 83.33333%;
		flex: 0 0 83.33333%;
		max-width: 83.33333%;
	}

	.col-lg-11 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 91.66667%;
		flex: 0 0 91.66667%;
		max-width: 91.66667%;
	}

	.col-lg-12 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 100%;
		flex: 0 0 100%;
		max-width: 100%;
	}

	.order-lg-first {
		-webkit-box-ordinal-group: 0;
		-ms-flex-order: -1;
		order: -1;
	}

	.order-lg-last {
		-webkit-box-ordinal-group: 14;
		-ms-flex-order: 13;
		order: 13;
	}

	.order-lg-0 {
		-webkit-box-ordinal-group: 1;
		-ms-flex-order: 0;
		order: 0;
	}

	.order-lg-1 {
		-webkit-box-ordinal-group: 2;
		-ms-flex-order: 1;
		order: 1;
	}

	.order-lg-2 {
		-webkit-box-ordinal-group: 3;
		-ms-flex-order: 2;
		order: 2;
	}

	.order-lg-3 {
		-webkit-box-ordinal-group: 4;
		-ms-flex-order: 3;
		order: 3;
	}

	.order-lg-4 {
		-webkit-box-ordinal-group: 5;
		-ms-flex-order: 4;
		order: 4;
	}

	.order-lg-5 {
		-webkit-box-ordinal-group: 6;
		-ms-flex-order: 5;
		order: 5;
	}

	.order-lg-6 {
		-webkit-box-ordinal-group: 7;
		-ms-flex-order: 6;
		order: 6;
	}

	.order-lg-7 {
		-webkit-box-ordinal-group: 8;
		-ms-flex-order: 7;
		order: 7;
	}

	.order-lg-8 {
		-webkit-box-ordinal-group: 9;
		-ms-flex-order: 8;
		order: 8;
	}

	.order-lg-9 {
		-webkit-box-ordinal-group: 10;
		-ms-flex-order: 9;
		order: 9;
	}

	.order-lg-10 {
		-webkit-box-ordinal-group: 11;
		-ms-flex-order: 10;
		order: 10;
	}

	.order-lg-11 {
		-webkit-box-ordinal-group: 12;
		-ms-flex-order: 11;
		order: 11;
	}

	.order-lg-12 {
		-webkit-box-ordinal-group: 13;
		-ms-flex-order: 12;
		order: 12;
	}

	.offset-lg-0 {
		margin-left: 0;
	}

	.offset-lg-1 {
		margin-left: 8.33333%;
	}

	.offset-lg-2 {
		margin-left: 16.66667%;
	}

	.offset-lg-3 {
		margin-left: 25%;
	}

	.offset-lg-4 {
		margin-left: 33.33333%;
	}

	.offset-lg-5 {
		margin-left: 41.66667%;
	}

	.offset-lg-6 {
		margin-left: 50%;
	}

	.offset-lg-7 {
		margin-left: 58.33333%;
	}

	.offset-lg-8 {
		margin-left: 66.66667%;
	}

	.offset-lg-9 {
		margin-left: 75%;
	}

	.offset-lg-10 {
		margin-left: 83.33333%;
	}

	.offset-lg-11 {
		margin-left: 91.66667%;
	}
}

@media (min-width: 1200px) {
	.col-xl {
		-ms-flex-preferred-size: 0;
		flex-basis: 0;
		-webkit-box-flex: 1;
		-ms-flex-positive: 1;
		flex-grow: 1;
		max-width: 100%;
	}

	.col-xl-auto {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 auto;
		flex: 0 0 auto;
		width: auto;
		max-width: none;
	}

	.col-xl-1 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 8.33333%;
		flex: 0 0 8.33333%;
		max-width: 8.33333%;
	}

	.col-xl-2 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 16.66667%;
		flex: 0 0 16.66667%;
		max-width: 16.66667%;
	}

	.col-xl-3 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 25%;
		flex: 0 0 25%;
		max-width: 25%;
	}

	.col-xl-4 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 33.33333%;
		flex: 0 0 33.33333%;
		max-width: 33.33333%;
	}

	.col-xl-5 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 41.66667%;
		flex: 0 0 41.66667%;
		max-width: 41.66667%;
	}

	.col-xl-6 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 50%;
		flex: 0 0 50%;
		max-width: 50%;
	}

	.col-xl-7 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 58.33333%;
		flex: 0 0 58.33333%;
		max-width: 58.33333%;
	}

	.col-xl-8 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 66.66667%;
		flex: 0 0 66.66667%;
		max-width: 66.66667%;
	}

	.col-xl-9 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 75%;
		flex: 0 0 75%;
		max-width: 75%;
	}

	.col-xl-10 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 83.33333%;
		flex: 0 0 83.33333%;
		max-width: 83.33333%;
	}

	.col-xl-11 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 91.66667%;
		flex: 0 0 91.66667%;
		max-width: 91.66667%;
	}

	.col-xl-12 {
		-webkit-box-flex: 0;
		-ms-flex: 0 0 100%;
		flex: 0 0 100%;
		max-width: 100%;
	}

	.order-xl-first {
		-webkit-box-ordinal-group: 0;
		-ms-flex-order: -1;
		order: -1;
	}

	.order-xl-last {
		-webkit-box-ordinal-group: 14;
		-ms-flex-order: 13;
		order: 13;
	}

	.order-xl-0 {
		-webkit-box-ordinal-group: 1;
		-ms-flex-order: 0;
		order: 0;
	}

	.order-xl-1 {
		-webkit-box-ordinal-group: 2;
		-ms-flex-order: 1;
		order: 1;
	}

	.order-xl-2 {
		-webkit-box-ordinal-group: 3;
		-ms-flex-order: 2;
		order: 2;
	}

	.order-xl-3 {
		-webkit-box-ordinal-group: 4;
		-ms-flex-order: 3;
		order: 3;
	}

	.order-xl-4 {
		-webkit-box-ordinal-group: 5;
		-ms-flex-order: 4;
		order: 4;
	}

	.order-xl-5 {
		-webkit-box-ordinal-group: 6;
		-ms-flex-order: 5;
		order: 5;
	}

	.order-xl-6 {
		-webkit-box-ordinal-group: 7;
		-ms-flex-order: 6;
		order: 6;
	}

	.order-xl-7 {
		-webkit-box-ordinal-group: 8;
		-ms-flex-order: 7;
		order: 7;
	}

	.order-xl-8 {
		-webkit-box-ordinal-group: 9;
		-ms-flex-order: 8;
		order: 8;
	}

	.order-xl-9 {
		-webkit-box-ordinal-group: 10;
		-ms-flex-order: 9;
		order: 9;
	}

	.order-xl-10 {
		-webkit-box-ordinal-group: 11;
		-ms-flex-order: 10;
		order: 10;
	}

	.order-xl-11 {
		-webkit-box-ordinal-group: 12;
		-ms-flex-order: 11;
		order: 11;
	}

	.order-xl-12 {
		-webkit-box-ordinal-group: 13;
		-ms-flex-order: 12;
		order: 12;
	}

	.offset-xl-0 {
		margin-left: 0;
	}

	.offset-xl-1 {
		margin-left: 8.33333%;
	}

	.offset-xl-2 {
		margin-left: 16.66667%;
	}

	.offset-xl-3 {
		margin-left: 25%;
	}

	.offset-xl-4 {
		margin-left: 33.33333%;
	}

	.offset-xl-5 {
		margin-left: 41.66667%;
	}

	.offset-xl-6 {
		margin-left: 50%;
	}

	.offset-xl-7 {
		margin-left: 58.33333%;
	}

	.offset-xl-8 {
		margin-left: 66.66667%;
	}

	.offset-xl-9 {
		margin-left: 75%;
	}

	.offset-xl-10 {
		margin-left: 83.33333%;
	}

	.offset-xl-11 {
		margin-left: 91.66667%;
	}
}

.d-none {
	display: none !important;
}

.d-inline {
	display: inline !important;
}

.d-inline-block {
	display: inline-block !important;
}

.d-block {
	display: block !important;
}

.d-table {
	display: table !important;
}

.d-table-row {
	display: table-row !important;
}

.d-table-cell {
	display: table-cell !important;
}

.d-flex {
	display: -webkit-box !important;
	display: -ms-flexbox !important;
	display: flex !important;
}

.d-inline-flex {
	display: -webkit-inline-box !important;
	display: -ms-inline-flexbox !important;
	display: inline-flex !important;
}

@media (min-width: 576px) {
	.d-sm-none {
		display: none !important;
	}

	.d-sm-inline {
		display: inline !important;
	}

	.d-sm-inline-block {
		display: inline-block !important;
	}

	.d-sm-block {
		display: block !important;
	}

	.d-sm-table {
		display: table !important;
	}

	.d-sm-table-row {
		display: table-row !important;
	}

	.d-sm-table-cell {
		display: table-cell !important;
	}

	.d-sm-flex {
		display: -webkit-box !important;
		display: -ms-flexbox !important;
		display: flex !important;
	}

	.d-sm-inline-flex {
		display: -webkit-inline-box !important;
		display: -ms-inline-flexbox !important;
		display: inline-flex !important;
	}
}

@media (min-width: 768px) {
	.d-md-none {
		display: none !important;
	}

	.d-md-inline {
		display: inline !important;
	}

	.d-md-inline-block {
		display: inline-block !important;
	}

	.d-md-block {
		display: block !important;
	}

	.d-md-table {
		display: table !important;
	}

	.d-md-table-row {
		display: table-row !important;
	}

	.d-md-table-cell {
		display: table-cell !important;
	}

	.d-md-flex {
		display: -webkit-box !important;
		display: -ms-flexbox !important;
		display: flex !important;
	}

	.d-md-inline-flex {
		display: -webkit-inline-box !important;
		display: -ms-inline-flexbox !important;
		display: inline-flex !important;
	}
}

@media (min-width: 992px) {
	.d-lg-none {
		display: none !important;
	}

	.d-lg-inline {
		display: inline !important;
	}

	.d-lg-inline-block {
		display: inline-block !important;
	}

	.d-lg-block {
		display: block !important;
	}

	.d-lg-table {
		display: table !important;
	}

	.d-lg-table-row {
		display: table-row !important;
	}

	.d-lg-table-cell {
		display: table-cell !important;
	}

	.d-lg-flex {
		display: -webkit-box !important;
		display: -ms-flexbox !important;
		display: flex !important;
	}

	.d-lg-inline-flex {
		display: -webkit-inline-box !important;
		display: -ms-inline-flexbox !important;
		display: inline-flex !important;
	}
}

@media (min-width: 1200px) {
	.d-xl-none {
		display: none !important;
	}

	.d-xl-inline {
		display: inline !important;
	}

	.d-xl-inline-block {
		display: inline-block !important;
	}

	.d-xl-block {
		display: block !important;
	}

	.d-xl-table {
		display: table !important;
	}

	.d-xl-table-row {
		display: table-row !important;
	}

	.d-xl-table-cell {
		display: table-cell !important;
	}

	.d-xl-flex {
		display: -webkit-box !important;
		display: -ms-flexbox !important;
		display: flex !important;
	}

	.d-xl-inline-flex {
		display: -webkit-inline-box !important;
		display: -ms-inline-flexbox !important;
		display: inline-flex !important;
	}
}

@media print {
	.d-print-none {
		display: none !important;
	}

	.d-print-inline {
		display: inline !important;
	}

	.d-print-inline-block {
		display: inline-block !important;
	}

	.d-print-block {
		display: block !important;
	}

	.d-print-table {
		display: table !important;
	}

	.d-print-table-row {
		display: table-row !important;
	}

	.d-print-table-cell {
		display: table-cell !important;
	}

	.d-print-flex {
		display: -webkit-box !important;
		display: -ms-flexbox !important;
		display: flex !important;
	}

	.d-print-inline-flex {
		display: -webkit-inline-box !important;
		display: -ms-inline-flexbox !important;
		display: inline-flex !important;
	}
}

.flex-row {
	-webkit-box-orient: horizontal !important;
	-webkit-box-direction: normal !important;
	-ms-flex-direction: row !important;
	flex-direction: row !important;
}

.flex-column {
	-webkit-box-orient: vertical !important;
	-webkit-box-direction: normal !important;
	-ms-flex-direction: column !important;
	flex-direction: column !important;
}

.flex-row-reverse {
	-webkit-box-orient: horizontal !important;
	-webkit-box-direction: reverse !important;
	-ms-flex-direction: row-reverse !important;
	flex-direction: row-reverse !important;
}

.flex-column-reverse {
	-webkit-box-orient: vertical !important;
	-webkit-box-direction: reverse !important;
	-ms-flex-direction: column-reverse !important;
	flex-direction: column-reverse !important;
}

.flex-wrap {
	-ms-flex-wrap: wrap !important;
	flex-wrap: wrap !important;
}

.flex-nowrap {
	-ms-flex-wrap: nowrap !important;
	flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
	-ms-flex-wrap: wrap-reverse !important;
	flex-wrap: wrap-reverse !important;
}

.justify-content-start {
	-webkit-box-pack: start !important;
	-ms-flex-pack: start !important;
	justify-content: flex-start !important;
}

.justify-content-end {
	-webkit-box-pack: end !important;
	-ms-flex-pack: end !important;
	justify-content: flex-end !important;
}

.justify-content-center {
	-webkit-box-pack: center !important;
	-ms-flex-pack: center !important;
	justify-content: center !important;
}

.justify-content-between {
	-webkit-box-pack: justify !important;
	-ms-flex-pack: justify !important;
	justify-content: space-between !important;
}

.justify-content-around {
	-ms-flex-pack: distribute !important;
	justify-content: space-around !important;
}

.align-items-start {
	-webkit-box-align: start !important;
	-ms-flex-align: start !important;
	align-items: flex-start !important;
}

.align-items-end {
	-webkit-box-align: end !important;
	-ms-flex-align: end !important;
	align-items: flex-end !important;
}

.align-items-center {
	-webkit-box-align: center !important;
	-ms-flex-align: center !important;
	align-items: center !important;
}

.align-items-baseline {
	-webkit-box-align: baseline !important;
	-ms-flex-align: baseline !important;
	align-items: baseline !important;
}

.align-items-stretch {
	-webkit-box-align: stretch !important;
	-ms-flex-align: stretch !important;
	align-items: stretch !important;
}

.align-content-start {
	-ms-flex-line-pack: start !important;
	align-content: flex-start !important;
}

.align-content-end {
	-ms-flex-line-pack: end !important;
	align-content: flex-end !important;
}

.align-content-center {
	-ms-flex-line-pack: center !important;
	align-content: center !important;
}

.align-content-between {
	-ms-flex-line-pack: justify !important;
	align-content: space-between !important;
}

.align-content-around {
	-ms-flex-line-pack: distribute !important;
	align-content: space-around !important;
}

.align-content-stretch {
	-ms-flex-line-pack: stretch !important;
	align-content: stretch !important;
}

.align-self-auto {
	-ms-flex-item-align: auto !important;
	align-self: auto !important;
}

.align-self-start {
	-ms-flex-item-align: start !important;
	align-self: flex-start !important;
}

.align-self-end {
	-ms-flex-item-align: end !important;
	align-self: flex-end !important;
}

.align-self-center {
	-ms-flex-item-align: center !important;
	align-self: center !important;
}

.align-self-baseline {
	-ms-flex-item-align: baseline !important;
	align-self: baseline !important;
}

.align-self-stretch {
	-ms-flex-item-align: stretch !important;
	align-self: stretch !important;
}

@media (min-width: 576px) {
	.flex-sm-row {
		-webkit-box-orient: horizontal !important;
		-webkit-box-direction: normal !important;
		-ms-flex-direction: row !important;
		flex-direction: row !important;
	}

	.flex-sm-column {
		-webkit-box-orient: vertical !important;
		-webkit-box-direction: normal !important;
		-ms-flex-direction: column !important;
		flex-direction: column !important;
	}

	.flex-sm-row-reverse {
		-webkit-box-orient: horizontal !important;
		-webkit-box-direction: reverse !important;
		-ms-flex-direction: row-reverse !important;
		flex-direction: row-reverse !important;
	}

	.flex-sm-column-reverse {
		-webkit-box-orient: vertical !important;
		-webkit-box-direction: reverse !important;
		-ms-flex-direction: column-reverse !important;
		flex-direction: column-reverse !important;
	}

	.flex-sm-wrap {
		-ms-flex-wrap: wrap !important;
		flex-wrap: wrap !important;
	}

	.flex-sm-nowrap {
		-ms-flex-wrap: nowrap !important;
		flex-wrap: nowrap !important;
	}

	.flex-sm-wrap-reverse {
		-ms-flex-wrap: wrap-reverse !important;
		flex-wrap: wrap-reverse !important;
	}

	.justify-content-sm-start {
		-webkit-box-pack: start !important;
		-ms-flex-pack: start !important;
		justify-content: flex-start !important;
	}

	.justify-content-sm-end {
		-webkit-box-pack: end !important;
		-ms-flex-pack: end !important;
		justify-content: flex-end !important;
	}

	.justify-content-sm-center {
		-webkit-box-pack: center !important;
		-ms-flex-pack: center !important;
		justify-content: center !important;
	}

	.justify-content-sm-between {
		-webkit-box-pack: justify !important;
		-ms-flex-pack: justify !important;
		justify-content: space-between !important;
	}

	.justify-content-sm-around {
		-ms-flex-pack: distribute !important;
		justify-content: space-around !important;
	}

	.align-items-sm-start {
		-webkit-box-align: start !important;
		-ms-flex-align: start !important;
		align-items: flex-start !important;
	}

	.align-items-sm-end {
		-webkit-box-align: end !important;
		-ms-flex-align: end !important;
		align-items: flex-end !important;
	}

	.align-items-sm-center {
		-webkit-box-align: center !important;
		-ms-flex-align: center !important;
		align-items: center !important;
	}

	.align-items-sm-baseline {
		-webkit-box-align: baseline !important;
		-ms-flex-align: baseline !important;
		align-items: baseline !important;
	}

	.align-items-sm-stretch {
		-webkit-box-align: stretch !important;
		-ms-flex-align: stretch !important;
		align-items: stretch !important;
	}

	.align-content-sm-start {
		-ms-flex-line-pack: start !important;
		align-content: flex-start !important;
	}

	.align-content-sm-end {
		-ms-flex-line-pack: end !important;
		align-content: flex-end !important;
	}

	.align-content-sm-center {
		-ms-flex-line-pack: center !important;
		align-content: center !important;
	}

	.align-content-sm-between {
		-ms-flex-line-pack: justify !important;
		align-content: space-between !important;
	}

	.align-content-sm-around {
		-ms-flex-line-pack: distribute !important;
		align-content: space-around !important;
	}

	.align-content-sm-stretch {
		-ms-flex-line-pack: stretch !important;
		align-content: stretch !important;
	}

	.align-self-sm-auto {
		-ms-flex-item-align: auto !important;
		align-self: auto !important;
	}

	.align-self-sm-start {
		-ms-flex-item-align: start !important;
		align-self: flex-start !important;
	}

	.align-self-sm-end {
		-ms-flex-item-align: end !important;
		align-self: flex-end !important;
	}

	.align-self-sm-center {
		-ms-flex-item-align: center !important;
		align-self: center !important;
	}

	.align-self-sm-baseline {
		-ms-flex-item-align: baseline !important;
		align-self: baseline !important;
	}

	.align-self-sm-stretch {
		-ms-flex-item-align: stretch !important;
		align-self: stretch !important;
	}
}

@media (min-width: 768px) {
	.flex-md-row {
		-webkit-box-orient: horizontal !important;
		-webkit-box-direction: normal !important;
		-ms-flex-direction: row !important;
		flex-direction: row !important;
	}

	.flex-md-column {
		-webkit-box-orient: vertical !important;
		-webkit-box-direction: normal !important;
		-ms-flex-direction: column !important;
		flex-direction: column !important;
	}

	.flex-md-row-reverse {
		-webkit-box-orient: horizontal !important;
		-webkit-box-direction: reverse !important;
		-ms-flex-direction: row-reverse !important;
		flex-direction: row-reverse !important;
	}

	.flex-md-column-reverse {
		-webkit-box-orient: vertical !important;
		-webkit-box-direction: reverse !important;
		-ms-flex-direction: column-reverse !important;
		flex-direction: column-reverse !important;
	}

	.flex-md-wrap {
		-ms-flex-wrap: wrap !important;
		flex-wrap: wrap !important;
	}

	.flex-md-nowrap {
		-ms-flex-wrap: nowrap !important;
		flex-wrap: nowrap !important;
	}

	.flex-md-wrap-reverse {
		-ms-flex-wrap: wrap-reverse !important;
		flex-wrap: wrap-reverse !important;
	}

	.justify-content-md-start {
		-webkit-box-pack: start !important;
		-ms-flex-pack: start !important;
		justify-content: flex-start !important;
	}

	.justify-content-md-end {
		-webkit-box-pack: end !important;
		-ms-flex-pack: end !important;
		justify-content: flex-end !important;
	}

	.justify-content-md-center {
		-webkit-box-pack: center !important;
		-ms-flex-pack: center !important;
		justify-content: center !important;
	}

	.justify-content-md-between {
		-webkit-box-pack: justify !important;
		-ms-flex-pack: justify !important;
		justify-content: space-between !important;
	}

	.justify-content-md-around {
		-ms-flex-pack: distribute !important;
		justify-content: space-around !important;
	}

	.align-items-md-start {
		-webkit-box-align: start !important;
		-ms-flex-align: start !important;
		align-items: flex-start !important;
	}

	.align-items-md-end {
		-webkit-box-align: end !important;
		-ms-flex-align: end !important;
		align-items: flex-end !important;
	}

	.align-items-md-center {
		-webkit-box-align: center !important;
		-ms-flex-align: center !important;
		align-items: center !important;
	}

	.align-items-md-baseline {
		-webkit-box-align: baseline !important;
		-ms-flex-align: baseline !important;
		align-items: baseline !important;
	}

	.align-items-md-stretch {
		-webkit-box-align: stretch !important;
		-ms-flex-align: stretch !important;
		align-items: stretch !important;
	}

	.align-content-md-start {
		-ms-flex-line-pack: start !important;
		align-content: flex-start !important;
	}

	.align-content-md-end {
		-ms-flex-line-pack: end !important;
		align-content: flex-end !important;
	}

	.align-content-md-center {
		-ms-flex-line-pack: center !important;
		align-content: center !important;
	}

	.align-content-md-between {
		-ms-flex-line-pack: justify !important;
		align-content: space-between !important;
	}

	.align-content-md-around {
		-ms-flex-line-pack: distribute !important;
		align-content: space-around !important;
	}

	.align-content-md-stretch {
		-ms-flex-line-pack: stretch !important;
		align-content: stretch !important;
	}

	.align-self-md-auto {
		-ms-flex-item-align: auto !important;
		align-self: auto !important;
	}

	.align-self-md-start {
		-ms-flex-item-align: start !important;
		align-self: flex-start !important;
	}

	.align-self-md-end {
		-ms-flex-item-align: end !important;
		align-self: flex-end !important;
	}

	.align-self-md-center {
		-ms-flex-item-align: center !important;
		align-self: center !important;
	}

	.align-self-md-baseline {
		-ms-flex-item-align: baseline !important;
		align-self: baseline !important;
	}

	.align-self-md-stretch {
		-ms-flex-item-align: stretch !important;
		align-self: stretch !important;
	}
}

@media (min-width: 992px) {
	.flex-lg-row {
		-webkit-box-orient: horizontal !important;
		-webkit-box-direction: normal !important;
		-ms-flex-direction: row !important;
		flex-direction: row !important;
	}

	.flex-lg-column {
		-webkit-box-orient: vertical !important;
		-webkit-box-direction: normal !important;
		-ms-flex-direction: column !important;
		flex-direction: column !important;
	}

	.flex-lg-row-reverse {
		-webkit-box-orient: horizontal !important;
		-webkit-box-direction: reverse !important;
		-ms-flex-direction: row-reverse !important;
		flex-direction: row-reverse !important;
	}

	.flex-lg-column-reverse {
		-webkit-box-orient: vertical !important;
		-webkit-box-direction: reverse !important;
		-ms-flex-direction: column-reverse !important;
		flex-direction: column-reverse !important;
	}

	.flex-lg-wrap {
		-ms-flex-wrap: wrap !important;
		flex-wrap: wrap !important;
	}

	.flex-lg-nowrap {
		-ms-flex-wrap: nowrap !important;
		flex-wrap: nowrap !important;
	}

	.flex-lg-wrap-reverse {
		-ms-flex-wrap: wrap-reverse !important;
		flex-wrap: wrap-reverse !important;
	}

	.justify-content-lg-start {
		-webkit-box-pack: start !important;
		-ms-flex-pack: start !important;
		justify-content: flex-start !important;
	}

	.justify-content-lg-end {
		-webkit-box-pack: end !important;
		-ms-flex-pack: end !important;
		justify-content: flex-end !important;
	}

	.justify-content-lg-center {
		-webkit-box-pack: center !important;
		-ms-flex-pack: center !important;
		justify-content: center !important;
	}

	.justify-content-lg-between {
		-webkit-box-pack: justify !important;
		-ms-flex-pack: justify !important;
		justify-content: space-between !important;
	}

	.justify-content-lg-around {
		-ms-flex-pack: distribute !important;
		justify-content: space-around !important;
	}

	.align-items-lg-start {
		-webkit-box-align: start !important;
		-ms-flex-align: start !important;
		align-items: flex-start !important;
	}

	.align-items-lg-end {
		-webkit-box-align: end !important;
		-ms-flex-align: end !important;
		align-items: flex-end !important;
	}

	.align-items-lg-center {
		-webkit-box-align: center !important;
		-ms-flex-align: center !important;
		align-items: center !important;
	}

	.align-items-lg-baseline {
		-webkit-box-align: baseline !important;
		-ms-flex-align: baseline !important;
		align-items: baseline !important;
	}

	.align-items-lg-stretch {
		-webkit-box-align: stretch !important;
		-ms-flex-align: stretch !important;
		align-items: stretch !important;
	}

	.align-content-lg-start {
		-ms-flex-line-pack: start !important;
		align-content: flex-start !important;
	}

	.align-content-lg-end {
		-ms-flex-line-pack: end !important;
		align-content: flex-end !important;
	}

	.align-content-lg-center {
		-ms-flex-line-pack: center !important;
		align-content: center !important;
	}

	.align-content-lg-between {
		-ms-flex-line-pack: justify !important;
		align-content: space-between !important;
	}

	.align-content-lg-around {
		-ms-flex-line-pack: distribute !important;
		align-content: space-around !important;
	}

	.align-content-lg-stretch {
		-ms-flex-line-pack: stretch !important;
		align-content: stretch !important;
	}

	.align-self-lg-auto {
		-ms-flex-item-align: auto !important;
		align-self: auto !important;
	}

	.align-self-lg-start {
		-ms-flex-item-align: start !important;
		align-self: flex-start !important;
	}

	.align-self-lg-end {
		-ms-flex-item-align: end !important;
		align-self: flex-end !important;
	}

	.align-self-lg-center {
		-ms-flex-item-align: center !important;
		align-self: center !important;
	}

	.align-self-lg-baseline {
		-ms-flex-item-align: baseline !important;
		align-self: baseline !important;
	}

	.align-self-lg-stretch {
		-ms-flex-item-align: stretch !important;
		align-self: stretch !important;
	}
}

@media (min-width: 1200px) {
	.flex-xl-row {
		-webkit-box-orient: horizontal !important;
		-webkit-box-direction: normal !important;
		-ms-flex-direction: row !important;
		flex-direction: row !important;
	}

	.flex-xl-column {
		-webkit-box-orient: vertical !important;
		-webkit-box-direction: normal !important;
		-ms-flex-direction: column !important;
		flex-direction: column !important;
	}

	.flex-xl-row-reverse {
		-webkit-box-orient: horizontal !important;
		-webkit-box-direction: reverse !important;
		-ms-flex-direction: row-reverse !important;
		flex-direction: row-reverse !important;
	}

	.flex-xl-column-reverse {
		-webkit-box-orient: vertical !important;
		-webkit-box-direction: reverse !important;
		-ms-flex-direction: column-reverse !important;
		flex-direction: column-reverse !important;
	}

	.flex-xl-wrap {
		-ms-flex-wrap: wrap !important;
		flex-wrap: wrap !important;
	}

	.flex-xl-nowrap {
		-ms-flex-wrap: nowrap !important;
		flex-wrap: nowrap !important;
	}

	.flex-xl-wrap-reverse {
		-ms-flex-wrap: wrap-reverse !important;
		flex-wrap: wrap-reverse !important;
	}

	.justify-content-xl-start {
		-webkit-box-pack: start !important;
		-ms-flex-pack: start !important;
		justify-content: flex-start !important;
	}

	.justify-content-xl-end {
		-webkit-box-pack: end !important;
		-ms-flex-pack: end !important;
		justify-content: flex-end !important;
	}

	.justify-content-xl-center {
		-webkit-box-pack: center !important;
		-ms-flex-pack: center !important;
		justify-content: center !important;
	}

	.justify-content-xl-between {
		-webkit-box-pack: justify !important;
		-ms-flex-pack: justify !important;
		justify-content: space-between !important;
	}

	.justify-content-xl-around {
		-ms-flex-pack: distribute !important;
		justify-content: space-around !important;
	}

	.align-items-xl-start {
		-webkit-box-align: start !important;
		-ms-flex-align: start !important;
		align-items: flex-start !important;
	}

	.align-items-xl-end {
		-webkit-box-align: end !important;
		-ms-flex-align: end !important;
		align-items: flex-end !important;
	}

	.align-items-xl-center {
		-webkit-box-align: center !important;
		-ms-flex-align: center !important;
		align-items: center !important;
	}

	.align-items-xl-baseline {
		-webkit-box-align: baseline !important;
		-ms-flex-align: baseline !important;
		align-items: baseline !important;
	}

	.align-items-xl-stretch {
		-webkit-box-align: stretch !important;
		-ms-flex-align: stretch !important;
		align-items: stretch !important;
	}

	.align-content-xl-start {
		-ms-flex-line-pack: start !important;
		align-content: flex-start !important;
	}

	.align-content-xl-end {
		-ms-flex-line-pack: end !important;
		align-content: flex-end !important;
	}

	.align-content-xl-center {
		-ms-flex-line-pack: center !important;
		align-content: center !important;
	}

	.align-content-xl-between {
		-ms-flex-line-pack: justify !important;
		align-content: space-between !important;
	}

	.align-content-xl-around {
		-ms-flex-line-pack: distribute !important;
		align-content: space-around !important;
	}

	.align-content-xl-stretch {
		-ms-flex-line-pack: stretch !important;
		align-content: stretch !important;
	}

	.align-self-xl-auto {
		-ms-flex-item-align: auto !important;
		align-self: auto !important;
	}

	.align-self-xl-start {
		-ms-flex-item-align: start !important;
		align-self: flex-start !important;
	}

	.align-self-xl-end {
		-ms-flex-item-align: end !important;
		align-self: flex-end !important;
	}

	.align-self-xl-center {
		-ms-flex-item-align: center !important;
		align-self: center !important;
	}

	.align-self-xl-baseline {
		-ms-flex-item-align: baseline !important;
		align-self: baseline !important;
	}

	.align-self-xl-stretch {
		-ms-flex-item-align: stretch !important;
		align-self: stretch !important;
	}
}

@-webkit-keyframes progress-bar-stripes {
	from {
		background-position: 1rem 0;
	}

	to {
		background-position: 0 0;
	}
}

@keyframes progress-bar-stripes {
	from {
		background-position: 1rem 0;
	}

	to {
		background-position: 0 0;
	}
}

.progress {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	height: 1rem;
	overflow: hidden;
	font-size: 0.75rem;
	background-color: #e9ecef;
	border-radius: 0.3125rem;
}

.progress-bar {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	-ms-flex-direction: column;
	flex-direction: column;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	color: #fff;
	text-align: center;
	background-color: #2a6cff;
	-webkit-transition: width 0.6s ease;
	-o-transition: width 0.6s ease;
	transition: width 0.6s ease;
}

.progress-bar-striped {
	background-image: -webkit-linear-gradient(
		45deg,
		rgba(255, 255, 255, 0.15) 25%,
		transparent 25%,
		transparent 50%,
		rgba(255, 255, 255, 0.15) 50%,
		rgba(255, 255, 255, 0.15) 75%,
		transparent 75%,
		transparent
	);
	background-image: -o-linear-gradient(
		45deg,
		rgba(255, 255, 255, 0.15) 25%,
		transparent 25%,
		transparent 50%,
		rgba(255, 255, 255, 0.15) 50%,
		rgba(255, 255, 255, 0.15) 75%,
		transparent 75%,
		transparent
	);
	background-image: linear-gradient(
		45deg,
		rgba(255, 255, 255, 0.15) 25%,
		transparent 25%,
		transparent 50%,
		rgba(255, 255, 255, 0.15) 50%,
		rgba(255, 255, 255, 0.15) 75%,
		transparent 75%,
		transparent
	);
	background-size: 1rem 1rem;
}

.progress-bar-animated {
	-webkit-animation: progress-bar-stripes 1s linear infinite;
	animation: progress-bar-stripes 1s linear infinite;
}

.table {
	width: 100%;
	max-width: 100%;
	margin-bottom: 1rem;
	background-color: rgba(0, 0, 0, 0);
}

.table th,
.table td {
	padding: 0.75rem;
	vertical-align: top;
	border-top: 1px solid #dee2e6;
}

.table thead th {
	vertical-align: bottom;
	border-bottom: 2px solid #dee2e6;
}

.table tbody + tbody {
	border-top: 2px solid #dee2e6;
}

.table .table {
	background-color: #fff;
}

.table-sm th,
.table-sm td {
	padding: 0.3rem;
}

.table-bordered {
	border: 1px solid #dee2e6;
}

.table-bordered th,
.table-bordered td {
	border: 1px solid #dee2e6;
}

.table-bordered thead th,
.table-bordered thead td {
	border-bottom-width: 2px;
}

.table-striped tbody tr:nth-of-type(odd) {
	background-color: rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr:hover {
	background-color: rgba(0, 0, 0, 0.075);
}

.table-primary,
.table-primary > th,
.table-primary > td {
	background-color: #c3d6ff;
}

.table-hover .table-primary:hover {
	background-color: #aac5ff;
}

.table-hover .table-primary:hover > td,
.table-hover .table-primary:hover > th {
	background-color: #aac5ff;
}

.table-secondary,
.table-secondary > th,
.table-secondary > td {
	background-color: #ffd8b8;
}

.table-hover .table-secondary:hover {
	background-color: #ffca9f;
}

.table-hover .table-secondary:hover > td,
.table-hover .table-secondary:hover > th {
	background-color: #ffca9f;
}

.table-success,
.table-success > th,
.table-success > td {
	background-color: #bdf6d8;
}

.table-hover .table-success:hover {
	background-color: #a7f3cb;
}

.table-hover .table-success:hover > td,
.table-hover .table-success:hover > th {
	background-color: #a7f3cb;
}

.table-info,
.table-info > th,
.table-info > td {
	background-color: #bef2ec;
}

.table-hover .table-info:hover {
	background-color: #a9eee6;
}

.table-hover .table-info:hover > td,
.table-hover .table-info:hover > th {
	background-color: #a9eee6;
}

.table-warning,
.table-warning > th,
.table-warning > td {
	background-color: #ffe6b9;
}

.table-hover .table-warning:hover {
	background-color: #ffdda0;
}

.table-hover .table-warning:hover > td,
.table-hover .table-warning:hover > th {
	background-color: #ffdda0;
}

.table-danger,
.table-danger > th,
.table-danger > td {
	background-color: #ffcbcb;
}

.table-hover .table-danger:hover {
	background-color: #ffb2b2;
}

.table-hover .table-danger:hover > td,
.table-hover .table-danger:hover > th {
	background-color: #ffb2b2;
}

.table-light,
.table-light > th,
.table-light > td {
	background-color: #e9ebec;
}

.table-hover .table-light:hover {
	background-color: #dbdfe0;
}

.table-hover .table-light:hover > td,
.table-hover .table-light:hover > th {
	background-color: #dbdfe0;
}

.table-dark,
.table-dark > th,
.table-dark > td {
	background-color: #c3c5c6;
}

.table-hover .table-dark:hover {
	background-color: #b6b8ba;
}

.table-hover .table-dark:hover > td,
.table-hover .table-dark:hover > th {
	background-color: #b6b8ba;
}

.table-active,
.table-active > th,
.table-active > td {
	background-color: rgba(0, 0, 0, 0.075);
}

.table-hover .table-active:hover {
	background-color: rgba(0, 0, 0, 0.075);
}

.table-hover .table-active:hover > td,
.table-hover .table-active:hover > th {
	background-color: rgba(0, 0, 0, 0.075);
}

.table .thead-dark th {
	color: #fff;
	background-color: #212529;
	border-color: #32383e;
}

.table .thead-light th {
	color: #495057;
	background-color: #e9ecef;
	border-color: #dee2e6;
}

.table-dark {
	color: #fff;
	background-color: #212529;
}

.table-dark th,
.table-dark td,
.table-dark thead th {
	border-color: #32383e;
}

.table-dark.table-bordered {
	border: 0;
}

.table-dark.table-striped tbody tr:nth-of-type(odd) {
	background-color: rgba(255, 255, 255, 0.05);
}

.table-dark.table-hover tbody tr:hover {
	background-color: rgba(255, 255, 255, 0.075);
}

@media (max-width: 575.98px) {
	.table-responsive-sm {
		display: block;
		width: 100%;
		overflow-x: auto;
		-webkit-overflow-scrolling: touch;
		-ms-overflow-style: -ms-autohiding-scrollbar;
	}

	.table-responsive-sm > .table-bordered {
		border: 0;
	}
}

@media (max-width: 767.98px) {
	.table-responsive-md {
		display: block;
		width: 100%;
		overflow-x: auto;
		-webkit-overflow-scrolling: touch;
		-ms-overflow-style: -ms-autohiding-scrollbar;
	}

	.table-responsive-md > .table-bordered {
		border: 0;
	}
}

@media (max-width: 991.98px) {
	.table-responsive-lg {
		display: block;
		width: 100%;
		overflow-x: auto;
		-webkit-overflow-scrolling: touch;
		-ms-overflow-style: -ms-autohiding-scrollbar;
	}

	.table-responsive-lg > .table-bordered {
		border: 0;
	}
}

@media (max-width: 1199.98px) {
	.table-responsive-xl {
		display: block;
		width: 100%;
		overflow-x: auto;
		-webkit-overflow-scrolling: touch;
		-ms-overflow-style: -ms-autohiding-scrollbar;
	}

	.table-responsive-xl > .table-bordered {
		border: 0;
	}
}

.table-responsive {
	display: block;
	width: 100%;
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
	-ms-overflow-style: -ms-autohiding-scrollbar;
}

.table-responsive > .table-bordered {
	border: 0;
}

body {
	font-family: Roboto, system, -apple-system, BlinkMacSystemFont,
		".SFNSDisplay-Regular", "Helvetica Neue", Helvetica, Arial,
		sans-serif;
	color: #607179;
	font-size: 0.8125rem;
	background: #eee;
	font-weight: 400;
	line-height: 1.8;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

a,
button {
	-webkit-transition: all 0.2s ease-in-out;
	-o-transition: all 0.2s ease-in-out;
	transition: all 0.2s ease-in-out;
	cursor: pointer;
}

a:hover,
a:active,
a:focus,
button:hover,
button:active,
button:focus {
	text-decoration: none;
	outline: none;
}

a:active:focus,
button:active:focus {
	outline: none;
}

a[x-apple-data-detectors] {
	color: inherit !important;
	text-decoration: none !important;
	font-size: inherit !important;
	font-family: inherit !important;
	font-weight: inherit !important;
	line-height: inherit !important;
}

p {
	margin-bottom: 0;
}

p + p {
	margin-top: 0.9375rem;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	color: #2a3135;
	font-weight: bold;
	margin-top: 0;
	line-height: 1.2;
	margin-bottom: 0.625rem;
}

.img-responsive {
	max-width: 100%;
	height: auto;
}

.list-inline {
	list-style: none;
	margin: 0;
	padding: 0;
}

@media (min-width: 800px) {
	.list-inline {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
	}
}

.list-inline li:not(:last-child) {
	margin-right: 15px;
}

.hl_report--wrap {
	max-width: 800px;
	background: #fff;
	margin-left: auto;
	margin-right: auto;
}

.hl_report--header {
	padding: 15px 30px;
}

@media (min-width: 800px) {
	.hl_report--header {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-webkit-box-pack: justify;
		-ms-flex-pack: justify;
		justify-content: space-between;
	}
}

.hl_report--header .logo-wrap {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

@media (max-width: 799px) {
	.hl_report--header .logo-wrap {
		margin-bottom: 20px;
	}
}

.hl_report--header .logo-wrap .logo {
	margin-right: 20px;
}

.hl_report--header .logo-wrap h1 {
	font-size: 24px;
	font-weight: 300;
	color: #607179;
	margin-bottom: 0;
}

.hl_report--header .user {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.hl_report--header .user .avatar {
	max-width: 64px;
	max-height: 64px;
	height: auto;
	border-radius: 50%;
	margin-right: 20px;
}

.hl_report--header .user h2 {
	margin-bottom: 5px;
	font-size: 0.875rem;
}

.hl_report--header .user p {
	font-size: 12px;
	color: #2a3135;
}

.hl_report--header .user p + p {
	margin-top: 0;
}

.hl_report--header .user p img {
	margin-right: 5px;
	position: relative;
	top: -2px;
}

.hl_report--hero {
	color: #fff;
	background-image: -webkit-gradient(
		linear,
		left top,
		right top,
		from(#09cdff),
		to(#685cff)
	);
	background-image: -webkit-linear-gradient(left, #09cdff, #685cff);
	background-image: -o-linear-gradient(left, #09cdff, #685cff);
	background-image: linear-gradient(to right, #09cdff, #685cff);
	padding: 40px 30px;
}

@media (min-width: 800px) {
	.hl_report--hero {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-pack: justify;
		-ms-flex-pack: justify;
		justify-content: space-between;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
	}
}

@media (min-width: 800px) {
	.hl_report--hero img {
		margin-right: 40px;
	}
}

.hl_report--hero h2 {
	color: #fff;
	font-size: 1.5rem;
}

.hl_report--hero .btns {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	margin-top: 20px;
}

.hl_report--hero .btns .btn {
	margin-right: 15px;
}

.btn {
	display: inline-block;
	border-radius: 4px;
	padding: 9px 25px;
	color: #2a6cff;
	font-weight: bold;
}

.btn.btn-white {
	background: #fff;
}

.btn.btn-white:hover,
.btn.btn-white:focus,
.btn.btn-white:active {
	background: #f2f7fa;
	color: #2a6cff;
}

.hl_report--section {
	padding: 30px;
}

.hl_report--section.--grey {
	background-color: #f3f6f9;
	color: #2a3135;
}

.hl_report--section.--border {
	border-bottom: 1px solid #e6e8ed;
}

.hl_report--section .sec-heading {
	margin-bottom: 20px;
}

@media (min-width: 800px) {
	.hl_report--section .sec-heading {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
	}

	.hl_report--section .sec-heading > img {
		margin-bottom: 0;
	}
}

.hl_report--section .sec-heading > img {
	margin-right: 20px;
	margin-bottom: 10px;
}

.hl_report--section .sec-heading .progress-bar-circle-sm {
	margin-right: 20px;
}

.hl_report--section .sec-heading h3 {
	font-size: 1.5rem;
	margin-bottom: 0;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.hl_report--section .sec-heading h3 .letter {
	margin-left: 10px;
}

.hl_report--section .sec-heading ul {
	margin-top: 10px;
}

.hl_report--section .sec-heading ul li {
	font-size: 0.75rem;
}

.hl_report--section .sec-heading ul li img {
	margin-right: 5px;
	position: relative;
	top: -2px;
}

.hl_report--section .sec-heading .sec-heading-text p {
	color: #607179;
}

.hl_report--section .sec-heading2 {
	margin-bottom: 20px;
}

@media (min-width: 800px) {
	.hl_report--section .sec-heading2 {
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
		-webkit-box-align: center;
		-ms-flex-align: center;
		align-items: center;
		-webkit-box-pack: justify;
		-ms-flex-pack: justify;
		justify-content: space-between;
	}

	.hl_report--section .sec-heading2 > img {
		margin-bottom: 0;
	}
}

.hl_report--section .sec-heading2 > img {
	margin-right: 20px;
	margin-bottom: 10px;
}

.hl_report--section .sec-heading2 h4 {
	font-size: 1.125rem;
	margin-bottom: 0;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.hl_report--section .sec-heading2 h4 .letter {
	margin-left: 10px;
}

.hl_report--section .sec-heading2 .sec-heading-text p {
	color: #607179;
}

.hl_report--section.sec1 {
	color: #2a3135;
	padding-bottom: 10px;
}

.hl_report--section.sec1 .row > div {
	margin-bottom: 20px;
}

.hl_report--section.sec1 .row > div img {
	display: block;
	margin: 0 auto;
}

.listing-heading h3 .icon {
	margin-right: 5px;
	position: relative;
	top: -2px;
}

.letters {
	-webkit-box-flex: 1;
	-ms-flex: 1 0 0px;
	flex: 1 0 0;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.letters-item {
	-webkit-box-flex: 1;
	-ms-flex: 1 0 0px;
	flex: 1 0 0;
	text-align: center;
}

.letters-item h3 {
	font-size: 0.75rem;
}

.letters-item .letter {
	margin: 0 auto;
	width: 64px;
	height: 64px;
	font-size: 1.5rem;
	position: relative;
}

.letters-item .letter:before {
	content: "";
	display: block;
	width: 60px;
	height: 60px;
	border-radius: 50%;
	margin: auto;
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	border: 2px solid #fff;
}

.letter {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: center;
	-ms-flex-pack: center;
	justify-content: center;
	font-size: 0.875rem;
	font-weight: bold;
	width: 24px;
	height: 24px;
	border-radius: 50%;
	color: #fff;
	background-color: #000;
}

.letter.--a {
	background-color: #14e075;
}

.letter.--b {
	background-color: #ff4646;
}

.letter.--c {
	background-color: #ffa604;
}

.letter.--d {
	background-color: #ff4646;
}

.sec-cols20 > div {
	margin-bottom: 20px;
}

.sec-cols30 > div {
	margin-bottom: 30px;
}

.sec-cols40 > div {
	margin-bottom: 40px;
}

.sec-cards {
	padding-bottom: 0;
}

.progress-wrap {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.progress-wrap p {
	font-size: 0.875rem;
	margin-right: 10px;
	color: #2a3135;
}

.progress-wrap .progress {
	-webkit-box-flex: 1;
	-ms-flex: 1 0 0px;
	flex: 1 0 0;
}

.progress {
	background-color: #e7e8ea;
	height: 4px;
}

.progress.--green .progress-bar {
	background-color: #14e075;
}

.progress.--red .progress-bar {
	background-color: #ff4646;
}

.progress.--yellow .progress-bar {
	background-color: #ffa604;
}

.card {
	border-radius: 4px;
	background-color: #fff;
	border: solid 2px #e9eaed;
	padding: 25px;
	height: 100%;
}

.card-heading {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-webkit-box-pack: justify;
	-ms-flex-pack: justify;
	justify-content: space-between;
	margin-bottom: 20px;
}

.card-heading h3 {
	margin-bottom: 0;
}

.card-heading h3 > img {
	margin-right: 5px;
	max-width: 28px;
	height: auto;
}

.card-heading p strong {
	font-size: 1.125rem;
	color: #2a3135;
}

.card-heading .listed {
	color: #2a3135;
}

.card-heading .listed .icon {
	margin-right: 5px;
	position: relative;
	top: -2px;
}

.card.--card-sm {
	padding: 10px 15px;
}

.card.--card-sm .card-heading {
	margin-bottom: 0;
}

.card.--card-sm .card-heading h3 {
	font-size: 0.875rem;
}

.card.--card-sm .card-heading p {
	font-size: 0.75rem;
}

.card.--card-sm .card-heading p strong {
	font-size: 0.875rem;
}

.review-heading .letter {
	margin-bottom: 5px;
}

.review-heading h3 {
	font-size: 2.25rem;
	line-height: 1;
}

.review-heading h3 span {
	font-size: 1.125rem;
	vertical-align: top;
	position: relative;
	top: 2px;
}

.circle-stats {
	margin-bottom: 40px;
}

.circle-stats > div {
	text-align: center;
}

.circle-stats h4 {
	font-size: 0.875rem;
}

.mockups {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
	-ms-flex-pack: distribute;
	justify-content: space-around;
	margin-bottom: 60px;
}

.mockup-desktop {
	width: 438px;
	height: 292px;
	background-size: 438px 292px;
	background-position: center;
	background-repeat: no-repeat;
	position: relative;
}

.mockup-desktop .screen {
	width: 333px;
	height: 214px;
	background-color: #000;
	background-position: center;
	background-repeat: no-repeat;
	background-size: cover;
	position: absolute;
	top: 11.5px;
	left: 60px;
}

.mockup-phone {
	width: 163px;
	height: 300px;
	background-size: 163px 300px;
	background-position: center;
	background-repeat: no-repeat;
	position: relative;
}

.mockup-phone .screen {
	width: 126px;
	height: 272px;
	background-color: #000;
	position: absolute;
	top: 14px;
	left: 18px;
	border-radius: 12px;
}

.stat-group > h4 {
	font-size: 0.875rem;
	font-weight: normal;
	text-transform: uppercase;
}

.stat-group > h4 .icon {
	margin-right: 5px;
	position: relative;
	top: -2px;
}

.stat-group + .stat-group {
	margin-top: 40px;
}

.stat-group + .sec-heading2 {
	margin-top: 40px;
}

.stat-progress-item {
	border-bottom: 1px solid #e6e8ed;
	padding-top: 15px;
	padding-bottom: 15px;
}

.stat-progress-item:last-child {
	border-bottom: none;
}

.stat-progress-item h4 {
	margin-bottom: 5px;
	font-size: 0.875rem;
}

.stat-progress-item > p {
	font-size: 0.75rem;
	margin-bottom: 5px;
}

.stat-progress-item .progress-wrap p {
	font-size: 0.875rem;
}

.stat-progress-item .progress-wrap .progress {
	background: #fff;
}

.stat-progress-item .progress-wrap .progress .progress-bar {
	border-radius: 50px;
}

.stat-progress-item .stat {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
}

.stat-progress-item .stat-item {
	margin-right: 20px;
	font-size: 0.875rem;
	color: #2a3135;
}

.stat-progress-item .stat-item span {
	color: #14e075;
}

.audit-group > h4 {
	font-size: 0.875rem;
	font-weight: bold;
}

.audit-group + .sec-heading2 {
	margin-top: 40px;
}

.audit-item {
	margin-bottom: 20px;
	position: relative;
	padding-left: 25px;
}

.audit-item .icon {
	display: block;
	width: 12px;
	height: 12px;
	background-color: #ff4646;
	background-size: 12px;
	background-position: center;
	background-repeat: no-repeat;
	border-radius: 50%;
	color: #fff;
	line-height: 12px;
	font-size: 10px;
	text-align: center;
	position: absolute;
	font-weight: bold;
	top: 2px;
	left: 0;
}

.audit-item:last-child {
	margin-bottom: 0;
}

.audit-item h5 {
	font-size: 0.875rem;
	font-weight: normal;
	margin-bottom: 5px;
}

.audit-item p {
	font-size: 0.75rem;
	line-height: 1.3;
}

.audit-item p + p {
	margin-top: 5px;
	font-size: 0.75rem;
}

.audit-item p.red {
	color: #f43232;
}

.rating {
	margin-right: 20px;
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.rating h4 {
	margin-bottom: 0;
	margin-right: 10px;
	font-size: 1.125rem;
}

.overall-rating p {
	font-size: 0.75rem;
	margin-bottom: 5px;
	line-height: 1;
}

.rating-wrap {
	display: -webkit-box;
	display: -ms-flexbox;
	display: flex;
	-webkit-box-align: center;
	-ms-flex-align: center;
	align-items: center;
}

.rating-wrap p.red {
	color: #ff4646;
}

.rating-wrap p.green {
	color: #14e075;
}

.reviews-list {
	font-size: 0.75rem;
}

.reviews-list h4 {
	color: #607179;
	font-size: 0.75rem;
	font-weight: normal;
}

.reviews-list ul {
	list-style: none;
	margin: 0;
	padding: 0;
}

.reviews-list ul li strong {
	color: #2a3135;
}

.reviews-list ul li img {
	margin-right: 5px;
	position: relative;
	top: -2px;
}

.table thead tr th {
	font-size: 0.6875rem;
	padding: 5px 0;
	border-top: 0;
	border-bottom: 1px solid #eaeef5;
	font-weight: normal;
}

.table tbody tr:first-child td {
	padding-top: 15px;
}

.table tbody tr td {
	padding: 5px 0;
	border: none;
	font-size: 0.875rem;
	font-weight: bold;
	color: #2a3135;
	vertical-align: middle;
}

.table tbody tr td .red {
	color: #ff4646;
}

.table tbody tr td .green {
	color: #14e075;
}

.table tbody tr td:first-child {
	font-size: 0.75rem;
	font-weight: normal;
	line-height: 1.4;
	padding-right: 5px;
	color: #607179;
}

.table tbody tr td .rating h4 {
	font-size: 0.875rem;
}

.table tbody tr td .rating img {
	max-width: 10px;
	height: auto;
}

.overall-score {
	margin: 0 50px;
}

.progress-bar-circle {
	position: relative;
	height: 130px;
	width: 130px;
}

.progress-bar-circle div {
	position: absolute;
	height: 130px;
	width: 130px;
	border-radius: 50%;
}

.progress-bar-circle div span {
	position: absolute;
	font-size: 32.5px;
	line-height: 97.5px;
	height: 97.5px;
	width: 97.5px;
	left: 16.25px;
	top: 16.25px;
	text-align: center;
	border-radius: 50%;
	background-color: white;
	color: #2a3135;
	font-weight: bold;
}

.progress-bar-circle .background {
	background-color: #2a6cff;
}

.progress-bar-circle .rotate {
	clip: rect(0 65px 130px 0);
	background-color: #e7e8ea;
}

.progress-bar-circle .left {
	clip: rect(0 65px 130px 0);
	opacity: 1;
	background-color: #2a6cff;
}

.progress-bar-circle .right {
	clip: rect(0 65px 130px 0);
	-webkit-transform: rotate(180deg);
	-ms-transform: rotate(180deg);
	transform: rotate(180deg);
	opacity: 0;
	background-color: #e7e8ea;
}

.progress-bar-circle-sm {
	position: relative;
	height: 70px;
	width: 70px;
}

.progress-bar-circle-sm div {
	position: absolute;
	height: 70px;
	width: 70px;
	border-radius: 50%;
}

.progress-bar-circle-sm div span {
	position: absolute;
	font-size: 17.5px;
	line-height: 52.5px;
	height: 52.5px;
	width: 52.5px;
	left: 8.75px;
	top: 8.75px;
	text-align: center;
	border-radius: 50%;
	background-color: white;
	color: #2a3135;
	font-weight: bold;
}

.progress-bar-circle-sm .background {
	background-color: #2a6cff;
}

.progress-bar-circle-sm .rotate {
	clip: rect(0 35px 70px 0);
	background-color: #e7e8ea;
}

.progress-bar-circle-sm .left {
	clip: rect(0 35px 70px 0);
	opacity: 1;
	background-color: #2a6cff;
}

.progress-bar-circle-sm .right {
	clip: rect(0 35px 70px 0);
	-webkit-transform: rotate(180deg);
	-ms-transform: rotate(180deg);
	transform: rotate(180deg);
	opacity: 0;
	background-color: #e7e8ea;
}

@-webkit-keyframes toggle {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

@keyframes toggle {
	0% {
		opacity: 0;
	}

	100% {
		opacity: 1;
	}
}

/*# sourceMappingURL=report.min.css.map */
</style>
