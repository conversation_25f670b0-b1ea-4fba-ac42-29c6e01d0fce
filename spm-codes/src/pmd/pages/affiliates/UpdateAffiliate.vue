<template>
  <div>
    <section class="hl_wrapper">
      <section class="hl_wrapper--inner marketplace pb-0" id="marketplace">
        <div class="container-fluid">
          <div v-if="!loading">
            <h3>Add Partner Form</h3>
            <div class="row">
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label for="name">Name</label>
                  <input
                    v-model="partner.name"
                    type="text"
                    class="form-control form-light"
                    placeholder="Name"
                  />
                </div>
              </div>
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label for="name">Email</label>
                  <input
                    v-model="partner.email"
                    type="text"
                    class="form-control form-light"
                    placeholder="Email"
                  />
                </div>
              </div>
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label for="name">City</label>
                  <input
                    v-model="partner.city"
                    type="text"
                    class="form-control form-light"
                    placeholder="City"
                  />
                </div>
              </div>
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label for="name">State</label>
                  <input
                    v-model="partner.state"
                    type="text"
                    class="form-control form-light"
                    placeholder="State"
                  />
                </div>
              </div>
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label for="name">Country</label>
                  <input
                    v-model="partner.country"
                    type="text"
                    class="form-control form-light"
                    placeholder="Country"
                  />
                </div>
              </div>
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label for="name">Languages</label>
                  <input
                    v-model="partner.languages[0]"
                    type="text"
                    class="form-control form-light"
                    placeholder="English, German, Hindi"
                  />
                </div>
              </div>
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label for="name">Experience</label>
                  <input
                    v-model="partner.experience"
                    type="number"
                    class="form-control form-light"
                    placeholder="year of experience"
                  />
                </div>
              </div>
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label for="name">Location Id</label>
                  <input
                    v-model="partner.locationId"
                    type="text"
                    class="form-control form-light"
                    placeholder="location id"
                  />
                </div>
              </div>
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label for="name">Website</label>
                  <input
                    v-model="partner.socialMedia[0].website"
                    type="text"
                    class="form-control form-light"
                    placeholder="link to website"
                  />
                </div>
              </div>
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label for="name">Facebook Url</label>
                  <input
                    v-model="partner.socialMedia[0].facebook"
                    type="text"
                    class="form-control form-light"
                    placeholder="Facebook account"
                  />
                </div>
              </div>
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label for="name">LinkedIn</label>
                  <input
                    v-model="partner.socialMedia[0].linkedin"
                    type="text"
                    class="form-control form-light"
                    placeholder="LinkedIn account"
                  />
                </div>
              </div>
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label for="name">Instagram</label>
                  <input
                    v-model="partner.socialMedia[0].instagram"
                    type="text"
                    class="form-control form-light"
                    placeholder="Instagram account"
                  />
                </div>
              </div>
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label for="name">Youtube Url</label>
                  <input
                    v-model="partner.socialMedia[0].youtube"
                    type="text"
                    class="form-control form-light"
                    placeholder="Youtube account"
                  />
                </div>
              </div>
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label for="name">Twitter Url</label>
                  <input
                    v-model="partner.socialMedia[0].twitter"
                    type="text"
                    class="form-control form-light"
                    placeholder="Twitter account"
                  />
                </div>
              </div>
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label for="name">Company</label>
                  <input
                    v-model="partner.company"
                    type="text"
                    class="form-control form-light"
                    placeholder="Company"
                  />
                </div>
              </div>
            </div>
            <div class="row hl_marketplace--search">
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label>Industry Served</label>
                  <vSelect
                    :options="industry_served_options"
                    placeholder="Industry Served"
                    v-model="partner.industries"
                    :clearable="true"
                    :multiple="true"
                  ></vSelect>
                </div>
              </div>
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label>Service provided</label>
                  <vSelect
                    :options="service_provided_options"
                    placeholder="Services"
                    v-model="partner.services"
                    :clearable="true"
                    :multiple="true"
                  ></vSelect>
                </div>
              </div>
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label>Partner Status</label>
                  <vSelect
                    :options="partner_staus_options"
                    placeholder="Partner Status"
                    v-model="partner.rank"
                    :clearable="true"
                  ></vSelect>
                </div>
              </div>
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label>Badges</label>
                  <vSelect
                    :options="badges_options"
                    placeholder="badges"
                    v-model="partner.badges"
                    :clearable="true"
                    :multiple="true"
                  ></vSelect>
                </div>
              </div>
            </div>
            <div class="row mb-0">
              <div class="col-md-6 col-lg-3">
                <div class="form-group">
                  <label for="name">Profile Photo</label>
                  <input
                    v-model="partner.profilePhoto"
                    type="text"
                    class="form-control form-light"
                    placeholder="profile photo url"
                  />
                </div>
              </div>
              <div class="col-md-6 col-lg-2">
                <div class="marketplace_card--avatar">
                  <div class="img-wrap">
                    <img :src="partner.profilePhoto" alt="photo preview" />
                  </div>
                </div>
              </div>
              <div class="col-md-6 col-lg-7">
                <div class="form-group">
                  <label for="name">Description</label>
                  <textarea
                    v-model="partner.description"
                    class="form-control form-light"
                    placeholder="A brief bio"
                  />
                </div>
              </div>
            </div>
            <div class="mb-5">
              <button
                type="button"
                class="btn btn-blue"
                @click.prevent="updatePartner"
              >
                Update Partner
              </button>
            </div>
          </div>
          <div v-else class="my-5">
            <MoonLoader size="30px" radius="50%" />
          </div>
        </div>
      </section>
    </section>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import Affiliate from '../../components/affiliates/Affiliate'
import vSelect from 'vue-select'
import axios from 'axios'
import defaults from '@/config'
import MoonLoader from '../../components/MoonLoader'
import { Partner } from '../../../models'

export default Vue.extend({
  components: {
    Affiliate,
    vSelect,
    MoonLoader
  },
  data() {
    return {
      industry_served_options: [
        'Advertising / Marketing',
        'Attorney / Legal',
        'Automotive Sales / Repair',
        'Chiro',
        'Computer / Software',
        'Coaching',
        'Construction / Contractors',
        'Consulting',
        'Dental',
        'Design / Architecture / Engineering',
        'Diet / Health / Nutrition',
        'Financial Services',
        'Government Agency',
        'Gym / Crossfit / Personal Training / Martial Arts',
        'Hair / Beauty / Nails',
        'Health / Nutrition',
        'Hospitality',
        'Insurance / Brokage',
        'Jewellery',
        'Lawn Care / Landscaping',
        'Manufacturing',
        'Medical / Health Services',
        'Medspa',
        'Non Profit',
        'Pest / Rodent Control',
        'Plumbing / HVAC',
        'Property Management',
        'Real Estate / Developer',
        'Restaurant / Bar',
        'Retail',
        'Solar / Green Tech',
        'Salon / Beauty',
        'Telecommunications',
        'Transportation',
        'Wholesale Distribution'
      ],
      service_provided_options: [
        'Conversion Rate Optimization',
        'Copy Writing',
        'Facebook & Instagram Ads',
        'Funnel Builds',
        'Google Ads',
        'Graphic Design',
        'HighLevel Builds',
        'Search Engine Optimization',
        'Social Media Management',
        'Video Content',
        'Web Development',
        'YouTube Ads',
        'Other'
      ],
      partner_staus_options: ['Certified Partner', 'Partner'],
      baseURL: defaults.baseUrl,
      loading: false,
      badges_options: ['Premier', 'Top', 'Affiliate'],
      partner: {} as Partner | undefined,
      id: this.$route.params.id
    }
  },
  async mounted(){
      this.loading = true
      this.partner = await Partner.getById(this.id);
      this.loading = false
  },
  computed: {
    inputLanguages: {
        get(){
            //this function will determine what is displayed in the input
            return this.partner.languages[0];
        },
        set(newVal){
            //this function will run whenever the input changes
            this.partner.languages[0] = newVal;
        }
    }
  },
  methods: {
    updatePartner() {
      axios
        .post(`${this.baseURL}/partners/update`, {
            id: this.id,
          name: this.partner.name,
          email: this.partner.email,
          description: this.partner.description,
          locationId: this.partner.locationId,
          profile_photo: this.partner.profilePhoto,
          social_media: this.partner.socialMedia,
          languages: this.partner.languages,
          services: this.partner.services,
          industries: this.partner.industries,
          rank: this.partner.rank,
          city: this.partner.city,
          state: this.partner.state,
          country: this.partner.country,
          company: this.partner.company,
          experience: this.partner.experience,
          badges: this.partner.badges
        })
        .then(res => {
          this.$router.go(-1)
        })
        .catch(err => {
          console.log('error adding partner : ', err)
        })
    },
  }
})
</script>

<style>
.hl_marketplace--search .v-select {
  background: white !important;
}
.hl_marketplace--search .v-select .vs__actions {
  cursor: pointer;
  padding-right: 8px;
}
.hl_marketplace--search .v-select .vs__selected {
  white-space: nowrap;
  height: 2rem;
  align-items: center;
}
.hl_marketplace--search .v-select .vs__selected-options {
  width: 90%;
  overflow: auto;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}
.hl_marketplace--search .v-select .vs__selected-options::-webkit-scrollbar {
  display: none;
}
.hl_marketplace--search .v-select .vs__search::placeholder {
  color: #2a3135;
  opacity: 0.5;
}
.hl_marketplace--search .v-select .vs__dropdown-menu {
  border: none;
  width: auto;
  min-width: 100%;
}
.hl_marketplace--search .v-select .vs__dropdown-menu .vs__dropdown-option {
  padding: 8px;
}
.hl_marketplace--search .dropdown {
  display: flex !important;
}
.hl_marketplace--search div .bootstrap-select #select-dropdown .dropdown-menu {
  width: inherit !important;
}
.hl_marketplace--results {
  list-style: none;
  padding: 0;
  margin: 0;
  margin-bottom: 40px;
}
.hl_marketplace--results li > a {
  display: block;
  color: #607179;
  border-radius: 4px;
}
.hl_marketplace--results li > a:hover .card {
  -webkit-box-shadow: 15px 26px 40px 0 rgba(0, 0, 0, 0.05);
  box-shadow: 15px 26px 40px 0 rgba(0, 0, 0, 0.05);
}
</style>
