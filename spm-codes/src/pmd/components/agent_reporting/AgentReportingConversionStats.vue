<template>
  <div class="conversions col-md-6 mb-3">
    <div class="card">
      <div class="card-header pr-4">
        <div style="width: 100%" class="d-flex justify-content-between align-items-center flex-wrap">
          <h2 class="mr-2">Conversions</h2>
          <b-dropdown :text="selectedPipeline" size="sm" v-if="pipelines && pipelines.length > 0 && filterPipeline">
            <b-dropdown-item v-for="pipeline in pipelines" :key="pipeline.id" @click="selectPipeline(pipeline)">{{pipeline.name.length > 50 ? pipeline.name.substring(0,50) + '...' : pipeline.name}}</b-dropdown-item>
          </b-dropdown>
        </div>
      </div>
      <div v-if="!isLoading" style="max-height: 215px; overflow-y: auto" class="card-body mt-2">
        <table class="table" v-if="data && filterPipeline">
          <tbody>
            <tr v-for="stage of filterPipeline.stages" :key="stage.id">
              <td>{{shortColValue(stage.name)}}</td>
              <td>{{data[stage.id] ? data[stage.id].count : 0}}</td>
              <td>
                <span v-html="getPercentHTML(data[stage.id] ? data[stage.id].percentage || 0 : 0)"/>
              </td>
            </tr>
          </tbody>
        </table>
        <div v-if="!(data && filterPipeline)">
          No conversions data available
        </div>
      </div>
      <div v-else class="card-body d-flex justify-content-center align-items-center">
        <MoonLoader size="30px" radius="50%" />
      </div>
    </div>
  </div>
</template>


<script lang="ts">
import Vue from 'vue'
import MoonLoader from '../MoonLoader'
import { Pipeline } from "../../../models";
import get_percent_change_html from '../../../util/get_percent_change_html'

export default Vue.extend({
  components: {
    MoonLoader,
  },
  props: {
    isLoading: {
      type: Boolean,
      default: false
    },
    data: {}
  },
  data(){
    return{
      pipelines: [] as Pipeline[],
      filterPipeline: undefined as undefined | Pipeline,
    }
  },
  watch: {
    '$route.params.location_id': async function(id) {
      this.fetchPipeline();
    }
  },
  computed:{
    selectedPipeline: function(){
      return this.filterPipeline.name && this.filterPipeline.name.length > 10 ? this.filterPipeline.name.substring(0,10) + '...' : this.filterPipeline.name
    }
  },
  created(){
    this.fetchPipeline();
  },
  methods:{
    getPercentHTML: get_percent_change_html,
    async fetchPipeline(){
      await this.$store.dispatch('pipelines/syncAll', this.$route.params.location_id)

      this.pipelines = this.$store.state.pipelines.pipelines.map(x => new Pipeline(Object.assign({}, x)));
      this.selectPipeline(this.pipelines.length? this.pipelines[0] : undefined);
    },
    selectPipeline(newPipeline){
      this.filterPipeline = newPipeline;
      this.$emit('pipeline-changed', newPipeline && newPipeline._data.id || '');
    },
    shortColValue: function(value){
      return value.length > 15 ? value.substring(0,15) + '...' : value
    }
  }
})
</script>

<style>
.conversions .btn {
  background-color: #fff;
  color: black;
}
.conversions .btn-secondary:not(:disabled):not(.disabled):active, .btn-secondary:not(:disabled):not(.disabled).active, .show > .btn-secondary.dropdown-toggle {
    color: #000;
    background-color: #fff;
    border-color: #fff;
}
.conversions .card .card-body{
  padding-top: 5px;
}
.conversions table tbody tr td {
  border: none;
}
.conversions table tbody tr td:last-child {
    white-space: nowrap;
}
</style>
