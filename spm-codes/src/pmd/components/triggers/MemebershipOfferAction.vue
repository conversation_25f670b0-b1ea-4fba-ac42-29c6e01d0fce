<template>
    <div class="form-group">
        <div class="form-input-dropdown dropdown">
            <div data-toggle="dropdown">
                <i class="icon icon-arrow-down-1"></i>
                <input type="text" class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800" data-lpignore="true" placeholder="Select offer" :value="offerName">
            </div>
            <div class="dropdown-menu">
                <a class="dropdown-item trigger-type" href="javascript:void(0);" v-for="offer in offers" :key="offer.key" @click.prevent="setValue(offer.key)">
                    <p>{{offer.value}}</p>
                </a>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import Vue from 'vue';

export default Vue.extend({
    props: ['action'],
    data() {
      return {
        currentLocationId: ''
      }
    },
    computed:{
      offers() {
        return this.$store.state.membership.offers;
      },
      offerName(): string {
        const offer = lodash.find(this.offers, {key: this.action.offer_id});
        if (offer) return offer.value;
        return '';
      }
    },
    methods: {
      setValue(value: string) {
          Vue.set(this.action, 'offer_id', value);
          this.$emit('update:action', this.action);
      }
    },
    watch: {
      '$route.params.location_id': async function(id) {
        await this.$store.dispatch('membership/syncAll', {
          locationId: id,
          forceRefresh: false
        })
      }
    },
    async created() {
      this.currentLocationId = this.$router.currentRoute.params.location_id;
      await this.$store.dispatch('membership/syncAll', {
        locationId: this.currentLocationId,
        forceRefresh: false
      })
    }
})
</script>
