<template>
  <div
    class="modal fade hl_sms-template--modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Add to <PERSON></h2>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div class="form-group">
              <label>Eliza</label>
              <vSelect
                :options="availableBots"
                label="title"
                v-model="selectedBots"
                :clearable="false"
                v-validate="'required'"
                name="bot"
              ></vSelect>
              <span v-show="errors.has('bot')" class="error"
                >Eliza is required.</span
              >
            </div>
            <div class="form-group">
              <label>Calendar</label>
              <vSelect
                :options="calendars"
                label="name"
                v-model="calendar"
                :clearable="false"
                v-validate="'required'"
                name="calendar"
              ></vSelect>
              <span v-show="errors.has('calendar')" class="error"
                >Calendar is required.</span
              >
            </div>
            <div class="form-group">
              Eliza's Welcome Message
              <div class="form-input-dropdown">
                <div>
                  <vSelect
                      :options="filteredTemplates"
                      label="name"
                      v-model="selectedWelcomeTemplate"
                      :clearable="false"
                      name="welcomeTemplate"
                    >
                      <template slot="option" slot-scope="option">
                        <div class="clearfix text-template-option" style="padding: 10px;">
                          <div style="float:left;">
                            <strong>{{option.name | truncate(80, '...')}}</strong>
                            <p>{{option.template.body | truncate(80, '...')}}</p>
                          </div>
                          <div style="float: right;margin-right: 50px;">
                            <img
                              :src="attachment"
                              style="max-height:50px;max-width:50px;border-radius: 12px;"
                              v-for="attachment in option.template.attachments"
                            />
                          </div>
                        </div>
                      </template>
                  </vSelect>
                </div>
              </div>
            </div>
            <div class="form-group">
              Eliza's Thank you Message
              <div class="form-input-dropdown">
                <div>
                  <vSelect
                      :options="filteredTemplates"
                      label="name"
                      v-model="selectedThankyouTemplate"
                      :clearable="false"
                      name="thankyouTemplate"
                    >
                      <template slot="option" slot-scope="option">
                        <div class="clearfix text-template-option" style="padding: 10px;">
                          <div style="float:left;">
                            <strong>{{option.name | truncate(80, '...')}}</strong>
                            <p>{{option.template.body | truncate(80, '...')}}</p>
                          </div>
                          <div style="float: right;margin-right: 50px;">
                            <img
                              :src="attachment"
                              style="max-height:50px;max-width:50px;border-radius: 12px;"
                              v-for="attachment in option.template.attachments"
                            />
                          </div>
                        </div>
                      </template>
                  </vSelect>
               </div>

              </div>
            </div>
            <div class="form-group">
              <label>Timeout (Hours)</label>
              <input
                type="text"
                class="form-control"
                placeholder="Enter a Timeout"
                v-model="timeout"
                name="timeout"
              />
              <span v-show="timeoutError.length" class="error">{{timeoutError}}</span>
            </div>
            <div v-if="error" class="help --red" style="text-align: center;">
              {{ error }}
            </div>

          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner nav">
            <button type="button" class="btn btn-light2" data-dismiss="modal">
              Cancel
            </button>
            <div style="display: inline-block;position: relative;">
              <button
                :class="{ invisible: saving }"
                type="button"
                class="btn btn-success"
                @click.prevent="addToBot"
              >
                Add
              </button>
              <div
                style="position: absolute;left: 50%;transform: translate(-50%, -50%);top: 50%;"
                v-show="saving"
              >
                <moon-loader :loading="saving" color="#37ca37" size="30px" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import * as moment from 'moment-timezone'
import vSelect from 'vue-select'
import { mapState } from "vuex";
import { UserState } from "@/store/state_models";
import {
  Campaign,
  Location,
  Contact,
  MessageType,
  Calendar,
  Template,
  LogicalEliza
} from '@/models'

declare var $: any

export default Vue.extend({
  props: ['values', 'conversation'],
  components: {
    vSelect
  },
  data() {
    return {
      currentLocationId: '',
      selectedBots: undefined,
      saving: false,
      contact: {} as Contact,
      availableBots: [] as { [key: string]: any }[],
      error: '',
      calendar: undefined as Calendar | undefined,
      templates: [] as { [key: string]: any }[],
      selectedWelcomeTemplate: undefined as { [key: string]: any } | undefined,
      selectedThankyouTemplate: undefined as { [key: string]: any } | undefined,
      timeout: '',
      timeoutError: ''
    }
  },
  created(){
    this.fetchData();
    this.fetchTemplates();
  },
  computed: {
    filteredTemplates(): { [key: string]: any }[] {
      let smsTemplate =  this.templates.filter(t => t.type === 'sms')
      if(smsTemplate.length > 0) return smsTemplate

      return this.templates;
    },
    calendars() {
      return this.$store.state.calendars.calendars
    },
  },
  methods: {
    async fetchData() {
      this.currentLocationId = this.$router.currentRoute.params.location_id;
      this.availableBots = (await LogicalEliza.fetchByLocationId(this.currentLocationId)).map(logicalEliza => {
        return {
          id: logicalEliza.id,
          projectId: logicalEliza.elizaId,
          title: logicalEliza.logicalName
        }
      });
    },
    async fetchTemplates() {
        const snapshot = await Template.fetchByLocationId(
          this.currentLocationId
        ).get()

        this.templates = snapshot.docs.map(d => {
          return { id: d.id, ...d.data() }
        })
    },
    async addToBot() {
      this.error = ''
      this.timeoutError = ''
      const result = await this.$validator.validateAll()
      if (!result || !this.selectedBots || !this.calendar)
        return false

      if(this.timeout && this.timeout !== 0 && isNaN(this.timeout)){
        this.timeoutError = 'Timeout value should be a number'
        return false
      }
      this.saving = true
      try {
        // Add To bot
        if(this.selectedBots && this.contact){
          this.contact.dialogflowBotInfo = {
            agent: this.selectedBots.title ? this.selectedBots.title : '',
            projectId: this.selectedBots.projectId ? this.selectedBots.projectId : '',
            welcomeTemplate: this.selectedWelcomeTemplate ? this.selectedWelcomeTemplate.id : '',
            thankyouTemplate: this.selectedThankyouTemplate ? this.selectedThankyouTemplate.id : '',
            timeout: this.timeout ? this.timeout : 0,
            logicalElizaId: this.selectedBots.id ? this.selectedBots.id : ''
          }
        }
        const data = {
            agent: this.selectedBots.title,
            projectId: this.selectedBots.projectId,
            welcomeTemplate: this.selectedWelcomeTemplate ? this.selectedWelcomeTemplate.id : '',
            thankyouTemplate: this.selectedThankyouTemplate ? this.selectedThankyouTemplate.id : '',
            timeout: this.timeout,
            calendarId: this.calendar.id,
            contactId: this.contact.id,
            logicalElizaId: this.selectedBots.id
        }
        await this.$http.post('/dialogflow/sendMessageToCustomer', data);
        this.saving = false
        this.timeoutError = ''
        this.$emit('hidden')
      } catch (err) {
        this.saving = false
        console.error('Error adding to Bot:', err)
        this.error = lodash.get(err, 'response.data.msg', 'Bad request')
      }
    }
  },
  watch: {
    values(values: { [key: string]: any }) {
      const data: () => object = <() => object>this.$options.data
      if (data) Object.assign(this.$data, data.apply(this))
      if (values.visible) $(this.$refs.modal).modal('show')
      else $(this.$refs.modal).modal('hide')
      if (values.visible) {
        this.contact = values.contact
        this.fetchData();
        this.fetchTemplates();
      }
    },
  },
  updated() {
    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hidden')
    })

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  }
})
</script>
