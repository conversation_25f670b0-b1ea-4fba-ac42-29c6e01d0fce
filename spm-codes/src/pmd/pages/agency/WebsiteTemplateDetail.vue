<template>
  <div>
    <SideBarV2 v-if="getSideBarVersion === 'v2'"/>
    <SideBar v-else />
    <TopBar />
    <section class="hl_wrapper">
      <section class="hl_wrapper--inner website-template-detail" id="websiteTemplateDetail">
        <div class="container-fluid">
          <div class="hl_controls">
            <div v-if="processingTemplate || errorInFetchingTemplate">
              <moon-loader :loading="processingTemplate" color="#188bf6" size="15px"/>
              <span v-if="errorInFetchingTemplate" @click="fetchWebsiteTemplate(templateId)">Error while fetching template. Click here to retry!</span>
            </div>
            <div v-else class="hl_controls--left">
              <h2>
                <a @click="$router.go(-1)" href="javascript:void(0);" class="back">
                  <i class="icon icon-arrow-left-2"></i>
                </a>
                {{ websiteTemplate.name }}
              </h2>
            </div>
            <div v-if="!templateNotFound" class="hl_controls--right">
              <UIButton :disabled="errorInFetchingTemplate || processingTemplate || !websiteTemplate.id" use="primary" @click="updateTemplate()">
                <i class="icon icon-ok mr-2"></i>&nbsp;{{ savingTemplate ? 'Saving' : 'Save Template' }}
              </UIButton>
              <UIButton :disabled="deletingTemplate || processingTemplate" use="danger" @click="deleteTemplate()">
                <i class="icon icon-trash mr-2"></i>&nbsp;{{ deletingTemplate ? 'Deleting' : 'Delete Template' }}
              </UIButton>
            </div>
          </div>
          <div class="row">
            <div class="col-xs-12 col-sm-12 col-lg-12">
              <div v-if="templateNotFound">
                <h3>Template Not Found!</h3>
              </div>
              <div v-else class="card">
                <div class="card-header">
                  <h2>Edit Details for {{ websiteTemplate.name }}</h2>
                </div>
                <div class="card-body">
                  <div class="form-group">
                    <label for="name"></label>
                    <UITextInputGroup label="Template Name" type="text" placeholder="Name for Template" required v-model="websiteTemplate.name" />
                  </div>
                  <div class="form-group">
                    <UITextAreaGroup label="Template Description" type="text" placeholder="Description for Template" v-model="websiteTemplate.description"></UITextAreaGroup>
                  </div>
                  <div class="form-group">
                    <UITextInputGroup label="Image" type="url" placeholder="Image URL for template" v-model="websiteTemplate.image" />
                  </div>
                  <div class="form-group">
                    <UITextInputGroup label="Live Preview" type="url" placeholder="Live Preview URL" v-model="websiteTemplate.livePreview" />
                  </div>
                  <div class="form-group">
                    <label for="">Refresh Template Data</label>
                    <UIButton :disabled="refreshingTemplate" use="secondary" @click="refreshTemplate()">
                      <i class="fas fa-redo mr-2"></i>&nbsp;
                      <span>{{ refreshingTemplate ? 'Refreshing Template' : 'Refresh Template' }}</span>
                    </UIButton>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </section>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import TopBar from '../../components/TopBar.vue'
import SideBar from '../../components/agency/SideBar.vue'
import { WebsiteTemplate, User } from '../../../models'
import SideBarV2 from '../../components/sidebar/SideBar.vue'

export default Vue.extend({
  components: {
    TopBar,
    SideBar,
    SideBarV2
  },
  computed: {
    templateId() {
      return this.$route.params.template_id
    },
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  data() {
    return {
      processingTemplate: false,
      errorInFetchingTemplate: false,
      refreshingTemplate: false,
      websiteTemplate: {} as WebsiteTemplate,
      deletingTemplate: false,
      savingTemplate: false,
      templateNotFound: false
    }
  },
  mounted() {
    this.fetchWebsiteTemplate(this.templateId)
  },
  methods: {
    async fetchWebsiteTemplate(templateId: string) {
      try {
        this.processingTemplate = true
        this.errorInFetchingTemplate = false
        this.templateNotFound = false
        const template = await WebsiteTemplate.getById(templateId)
        if (template) {
          this.websiteTemplate = template
        } else {
          console.error('Website template not found --> ', template)
          this.templateNotFound = true
        }
      } catch (error) {
        this.errorInFetchingTemplate = true
        console.error('Error while fetching template --> ', error)
      } finally {
        this.processingTemplate = false
      }
    },
    validateData(): boolean {
      const { name } = this.websiteTemplate

      if (!name || !name.length) {
        return false
      }

      return true
    },
    async updateTemplate() {
      const dataValid = this.validateData()

      if (dataValid) {
        try {
          this.processingTemplate = true
          this.savingTemplate = true
          await this.websiteTemplate.save()
        } catch (error) {
          console.error('Error while updating template --> ', error)
          alert('Some error while updating template, please try again later!')
        } finally {
          this.savingTemplate = false
          this.processingTemplate = false
        }
      } else {
        alert('Please fill in the required details before updating!')
      }
    },
    async refreshTemplate() {
      try {
        if (confirm('Do you want to update this template?')) {
          this.refreshingTemplate = true
          const response = await this.$http.post(`/website-template/${this.templateId}/refresh`)
        }
      } catch (error) {
        console.error('error while refreshing template --> ', error)
        alert(
          'Error while refreshing template! Make sure to have only one funnel/website in the location.'
        )
      } finally {
        this.refreshingTemplate = false
      }
    },
    async deleteTemplate() {
      try {
        if (confirm('Are you sure you want to delete the template?')) {
          this.deletingTemplate = true
          this.processingTemplate = true
          await this.$http.delete(`/website-template/${this.templateId}`)
          const categoryId = this.$route.params.id
          this.$router.push({ name: 'website_template_category_detail', params: { id: categoryId } })
        }
      } catch (error) {
        console.error('error while deleting template --> ', error)
        alert('Something went wrong while deleting template!')
      } finally {
        this.deletingTemplate = false
        this.processingTemplate = false
      }
    }
  }
})
</script>
