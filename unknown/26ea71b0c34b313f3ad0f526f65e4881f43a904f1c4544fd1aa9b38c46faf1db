<template>
    <div class="modal" ref="new_post_modal">
        <div class="modal-dialog --full" role="document">
            <div class="modal-content">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <div class="modal-body">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-md-6">
                                <h3>Edit Post</h3>
                                <div class="card hl_edit-post-card">
                                    <div class="card-body">
                                        <div class="social-post-form">
                                            <div class="avatar">
                                                <div class="avatar_img">
                                                    <img src="../../../assets/pmd/img/img-avatar-sample1.png" alt="Avatar Name">
                                                </div>
                                            </div>
                                            <textarea class="form-control" rows="4" placeholder="Enter text here"></textarea>
                                            <a href="#" class="dots">
                                                <i class="icon icon-dots"></i>
                                            </a>
                                            <a href="#" class="photo">
                                                <i class="icon icon-camera"></i>
                                            </a>
                                        </div>
                                        <div class="social-post-media">
                                            <div class="media-preview">
                                                <img src="http://via.placeholder.com/150x100" class="image-preview">
                                                <div class="media-preview-text">
                                                    <h4>img(02).png</h4>
                                                    <p>245kb</p>
                                                </div>
                                                <a href="#" class="remove">
                                                    <i class="icon icon-close"></i>
                                                </a>
                                            </div>
                                            <div class="media-preview">
                                                <img src="http://via.placeholder.com/150x80" class="image-preview">
                                                <div class="media-preview-text">
                                                    <textarea>Lorem ipsum dolor sit amet consectetur adipisicing elit.</textarea>
                                                    <textarea>Lorem ipsum dolor sit amet consectetur adipisicing elit.</textarea>
                                                    <p class="link">https://searchengineland.com/google-announces-new-features-for-retail-advertisers-at-smx-advanced-300001</p>
                                                </div>
                                                <a href="#" class="remove">
                                                    <i class="icon icon-close"></i>
                                                </a>
                                            </div>
                                            <div class="progress">
                                                <div class="progress-bar" role="progressbar" style="width: 50%;" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100">50%</div>
                                            </div>
                                            <div class="drag-drop">
                                                <p>Drag or Drop Media Here</p>
                                            </div>
                                        </div>
                                        <div class="social-post-network">
                                            <h4>Network</h4>
                                            <select class="selectpicker" title="Select Social Network">
                                                <option>Option 1</option>
                                                <option>Option 2</option>
                                                <option>Option 3</option>
                                            </select>
                                            <div class="selected-social-network">
                                                <div class="avatar">
                                                    <div class="avatar_img">
                                                        <img src="../../../assets/pmd/img/img-avatar-sample1.png" alt="Avatar Name">
                                                        <span class="icon --facebook">
                                                            <i class="fab fa-facebook-f"></i>
                                                        </span>
                                                    </div>
                                                    <a href="#" class="remove">
                                                        <i class="icon icon-close"></i>
                                                    </a>
                                                </div>
                                                <div class="avatar">
                                                    <div class="avatar_img">
                                                        <img src="../../../assets/pmd/img/img-avatar-sample1.png" alt="Avatar Name">
                                                        <span class="icon --twitter">
                                                            <i class="fab fa-twitter"></i>
                                                        </span>
                                                    </div>
                                                    <a href="#" class="remove">
                                                        <i class="icon icon-close"></i>
                                                    </a>
                                                </div>
                                                <div class="avatar">
                                                    <div class="avatar_img">
                                                        <img src="../../../assets/pmd/img/img-avatar-sample1.png" alt="Avatar Name">
                                                        <span class="icon --google-plus">
                                                            <i class="fab fa-google-plus-g"></i>
                                                        </span>
                                                    </div>
                                                    <a href="#" class="remove">
                                                        <i class="icon icon-close"></i>
                                                    </a>
                                                </div>
                                                <div class="avatar">
                                                    <div class="avatar_img">
                                                        <img src="../../../assets/pmd/img/img-avatar-sample1.png" alt="Avatar Name">
                                                        <span class="icon --linkedin">
                                                            <i class="fab fa-linkedin-in"></i>
                                                        </span>
                                                    </div>
                                                    <a href="#" class="remove">
                                                        <i class="icon icon-close"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <a href="#">Need Review?</a>
                                        <div class="card-footer-btn">
                                            <a href="#" class="btn btn-light3">Cancel</a>
                                            <a href="#" class="btn btn-blue">Save</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h3>Preview Post</h3>
                                <div class="hl_social--post-item card">
                                    <div class="card-header">
                                        <div class="avatar">
                                            <div class="avatar_img">
                                                <img src="../../../assets/pmd/img/img-avatar-sample1.png" alt="Avatar Name">
                                                <span class="icon --facebook">
                                                    <i class="fab fa-facebook-f"></i>
                                                </span>
                                            </div>
                                            <div class="avatar_text">
                                                <h4>
                                                    <strong>Test Tech</strong> via
                                                    <span>Instagram</span>
                                                </h4>
                                                <p>21 Jun 2018
                                                    <span>9.30 AM</span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <img src="../../../assets/pmd/img/image-sample2.jpg" alt="photo" class="post-image">
                                    </div>
                                    <div class="card-footer flex-column align-items-start">
                                        <input type="text" class="post-comment" data-lpignore="true" value="Very Cool!! Looking forward to this!">
                                        <div class="social-tags">
                                            <a href="#" class="tag">#hashtag</a>
                                            <a href="#" class="tag">#awesome</a>
                                            <a href="#" class="tag">#cool</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="hl_social--post-item card">
                                    <div class="card-header">
                                        <div class="avatar">
                                            <div class="avatar_img">
                                                <img src="../../../assets/pmd/img/img-avatar-sample1.png" alt="Avatar Name">
                                                <span class="icon --twitter">
                                                    <i class="fab fa-twitter"></i>
                                                </span>
                                            </div>
                                            <div class="avatar_text">
                                                <h4>
                                                    <strong>Test Tech</strong> via
                                                    <span>Instagram</span>
                                                </h4>
                                                <p>21 Jun 2018
                                                    <span>9.30 AM</span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <img src="../../../assets/pmd/img/image-sample.jpg" alt="photo" class="post-image">
                                    </div>
                                    <div class="card-footer flex-column align-items-start">
                                        <input type="text" class="post-comment" data-lpignore="true" value="Very Cool!! Looking forward to this!">
                                        <div class="social-tags">
                                            <a href="#" class="tag">#hashtag</a>
                                            <a href="#" class="tag">#awesome</a>
                                            <a href="#" class="tag">#cool</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import Vue from 'vue';
import { mapState } from 'vuex';
import { Contact, Message, Conversation, User, MessageType, MessageContentType, Post, PostData } from '@/models';
import libphonenumber from 'google-libphonenumber';
import { UserState } from '../../../store/state_models';

declare var $: any;
var phoneUtil = libphonenumber.PhoneNumberUtil.getInstance();
var PNF = libphonenumber.PhoneNumberFormat;

export default Vue.extend({
    props: ['showModal', 'post_id'],
    data() {
        return {
            post: {} as Post
        };
    },
    updated() {
        this.checkModal();
    },
    watch: {
        '$route.params.location_id': function (id) {
            this.currentLocationId = id;
        },
        showModal() {
            this.checkModal();
        },
        async post_id() {
            if (this.post_id) {
                this.post = await Post.getById(this.post_id);
            }
        }

    },
    created() {
        document.addEventListener('click', this.onClick)
        this.post = new Post();
    },
    destroyed() {
        document.removeEventListener('click', this.onClick)
    },
    computed: {
        ...mapState('user', {
            user: (s: UserState) => {
                return s.user ? new User(s.user) : undefined;
            },
        }),
    },
    methods: {
        checkModal() {
            var _self = this;
            if (this.showModal == true) {
                $(this.$refs.new_post_modal)
                    .modal('show')
                    .on('hidden.bs.modal', function () {
                        _self.$emit('hidden', true);
                    });
            } else {
                $(this.$refs.new_post_modal).modal('hide');
            }
        },


        async validateBeforeSubmit() {

        },
    },
});
</script>


<style>
.spinner {
    display: inline-block;
    margin: 30px 30px 0px;
}
</style>


