<template>
  <div
    class="modal fade hl_membership--modal"
    id="add-new-step"
    tabindex="-1"
    role="dialog"
    aria-hidden="true"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Reset Password</h2>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
             <div class="alert alert-danger mb-3" role="alert" v-if="errorMsg">
                <p style="color:red">
                something went wrong please try again
                </p>
              </div>
            <div class="form-group">
              <div class="password-container">
                <input
                  type="text"
                  placeholder="New password"
                  v-model="password"
                  class="form-control form-control"
                />
                <button
                  type="button"
                  class="btn btn-primary"
                  @click="generateRandomPwd"
                >
                  Generate a strong password
                </button>
              </div>


              <div class="option mt-3">
                <input type="checkbox" id="notify" v-model="notify" /><label
                  for="notify"
                  >Also, send an email notification.</label
                >
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner nav">
            <button type="button" class="btn btn-light2" data-dismiss="modal">
              Cancel
            </button>
            <div style="display: inline-block;position: relative;">
              <button
                :disabled="password.length === 0"
                :class="{ invisible: saving }"
                type="button"
                class="btn btn-success"
                @click.prevent="resetPassword"
              >
                Reset Password
              </button>
              <div
                style="position: absolute;left: 50%;transform: translate(-50%, -50%);top: 50%;"
                v-show="saving"
              >
                <moon-loader :loading="saving" color="#37ca37" size="30px" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
declare var $: any
import { randomString } from '../../../util/random_string_generate'
import axios from 'axios'
import config from '../../../config'
import { AuthUser } from '../../../models'
import firebase from 'firebase/app'

import 'firebase/auth'
export default Vue.extend({
  props: {
    showModal: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      saving: false,
      password: '',
      notify: false,
      errorMsg: false
    }
  },
  watch: {
    showModal(val) {
      console.log(val)
      if (val) {
        $(this.$refs.modal).modal('show')
      } else {
        $(this.$refs.modal).modal('hide')
      }
    }
  },
  mounted() {
    this.$bus.$on('enable-membership-access-modal', this.toggleModalView)
    $(this.$refs.modal).on('hidden.bs.modal', () => {
      this.$bus.$emit('enable-membership-access-modal', false)
    })
    if (this.showModal) {
      $(this.$refs.modal).modal('show')
    }
  },
  beforeDestroy() {
    this.$bus.$off('enable-membership-access-modal')
  },
  methods: {
    toggleModalView(value: boolean) {
      if (value) {
        $(this.$refs.modal).modal('show')
      } else {
        $(this.$refs.modal).modal('hide')
      }
    },
    async resetPassword() {
      try {
        this.saving = true
        this.errorMsg = false
        const params = {
          locationId: this.$route.params.location_id,
          contactId: this.$route.params.contact_id,
          trigger: this.notify,
          pwd: this.password.trim()
        }
        const currentUser = await firebase.auth().currentUser
        let token
        if (currentUser) {
          token = await currentUser.getIdToken()
        }
        await this.membershipBackend
          .post(`/user/password-reset`, params)
          .then(res => {
            this.saving = false
            this.toggleModalView(false)
          })
      } catch (error) {
        this.saving = false
        this.errorMsg = true
      }
    },
    generateRandomPwd() {
      this.password = randomString(10)
    }
  }
})
</script>

<style scoped>
  .password-container {
    display: grid;
    grid-template-columns: 2fr auto;
    grid-gap: 25px;
  }


</style>
