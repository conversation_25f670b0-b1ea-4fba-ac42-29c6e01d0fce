<template>
  <div
    class="modal fade"
    tabindex="-1"
    role="dialog"
    ref="modal"
    @click.self="close"
  >
    <div class="modal-dialog bulk-modal" role="document">
      <div class="modal-content delete-border">
        <div
          :class="`modal-header ${
            operation === 'delete' ? 'delete-border' : 'restore-border'
          }`"
        >
          <h4 class="m-title my-0">{{ operation }} contacts</h4>
          <button
            type="button"
            class="close"
            data-dismiss="modal"
            aria-label="Close"
            @click="close"
          >
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body py-3 px-3">
          <div
            v-if="loading"
            class="spinner my-4 d-flex align-items-center flex-column"
          >
            <moon-loader :loading="loading" color="#188bf6" size="30px" />
          </div>
          <div v-else>
            <div class="mb-2">
              <span>
                You're about to {{ operation }} {{ total }} contact(s). Plese
                click confirm to {{ operation }} the contact(s).
              </span>
            </div>

            <span v-if="operation === 'delete'">
              <span class="font-weight-bold">Note: </span>
              Deleting any contact(s) will also remove the corresponding:
              Conversation, Notes, Opportunities, Tasks, Appointments, Manual
              Actions. It will also stop any active Campaigns and Workflows for
              the contact(s).
            </span>
            <span v-else>
              <span class="font-weight-bold">Note: </span>
              Restoring any contact(s) will also restore the corresponding:
              Conversation, Notes, Opportunities, Tasks, Appointments, Manual
              Actions.
            </span>
          </div>
        </div>
        <div class="modal-footer d-flex flex-column">
          <div class="d-flex justify-content-center">

            <UIButton
              use="outline"
              data-dismiss="modal"
              class="mx-2"
              aria-label="Close"
              @click="close">
              Cancel
            </UIButton>
            <UIButton
              @click="confirm"
              :disabled="loading"
              :loading="loading"
            >
                Confirm
            </UIButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

declare var $: any

export default Vue.extend({
  props: ['currentLocationId', 'operation', 'total'],
  data() {
    return {
      loading: false,
    }
  },
  methods: {
    close() {
      this.$emit('closed')
    },
    confirm() {
      this.loading = true
      this.$emit('confirmBulk')
    },
  },
  async mounted() {
    $(this.$refs.modal).modal('show')
  },
  beforeDestroy() {
    $(this.$refs.modal).modal('hide')
  },
})
</script>
<style>
.avatar_img_small {
  width: 25px;
  height: 25px;
  min-width: 25px;
  min-height: 25px;
  font-size: small;
}

.m-title {
  text-transform: capitalize;
}

.align-middle {
  display: flex;
  justify-content: center;
}

.delete-border {
  border-top: 3px solid #dc3545;
}

.restore-border {
  border-top: 3px solid #17a2b8;
}
</style>
