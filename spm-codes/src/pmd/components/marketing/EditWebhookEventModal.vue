<template>
  <!-- Edit Event Modal -->
  <div
    class="modal fade hl_edit-event-modal"
    id="edit-event-modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog --mid" role="document">
      <div class="modal-content">
        <button
          type="button"
          class="close"
          data-dismiss="modal"
          aria-label="Close"
        >
          <span aria-hidden="true">&times;</span>
        </button>
        <div class="modal-body">
          <div class="modal-body-inner">
            <div class="hl_edit-event-modal-wrap">
              <div class="hl_edit-event-modal-main">
                <div class="card">
                  <div class="card-header --no-right-padding">
                    <h2>Webhook</h2>
                  </div>
                  <div class="card-body hl_campaign-configuration">
                    <div class="form-group" style="display:block;">
                      <UITextInputGroup
                        type="text"
                        placeholder="name"
                        v-model="name"
                        v-validate="'required'"
                        name="msgsndr2"
                        data-lpignore="true"
                        :error="errors.has('msgsndr2')"
                        :errorMsg="'Name is required.'"
                      />
                    </div>
                    <div class="form-group" style="display:block;">
                      <UITextInputGroup
                        type="text"
                        placeholder="URL"
                        v-model="url"
                        v-validate="'required|handlebars'"
                        data-vv-validate-on="input"
                        name="url"
                        :error="errors.has('url')"
                        :errorMsg="errors.first('url')"
                      />
                    </div>

                    <WindowComponent
                      v-model="templateWindow"
                      parent="webhook"
                      style="margin-top: 30px;"
                    />
                  </div>
                  <div class="card-footer">
                    <div class="modal-footer-left">
                      <div style="display: inline-block;position: relative;">
                        <UIButton
                          type="button"
                          @click.prevent="sendTest"
                          :loading="sending"
                        >
                          Send Test
                        </UIButton>
                      </div>
                    </div>
                    <div class="modal-footer-right">
                      <div style="display: inline-block;position: relative;">
                        <UIButton
                          type="button"
                          @click.prevent="save"
                          :loading="saving"
                        >
                          Save
                        </UIButton>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import firebase from 'firebase/app'
import WindowComponent from './WindowComponent.vue'
import { mapState } from 'vuex'
import { UserState, FileAttachment } from '@/store/state_models'
import { User, Template, Link, CustomField, CustomValue } from '@/models'
import Avatar from '../Avatar.vue'
import ImageTools from '../../../util/image_tools'
import { v4 as uuid } from 'uuid'
import { getTagOptions } from '@/util/merge_tags'

const Editor = () => import('@tinymce/tinymce-vue')
const PhoneNumber = () => import('../../components/util/PhoneNumber.vue')

declare var $: any
const imageTools = new ImageTools()
const triggerLinks: { [key: string]: any }[] = []
let customFieldValues = [] as { [key: string]: any }

export default Vue.extend({
  props: ['values', 'templates'],
  components: {
    Avatar,
    editor: Editor,
    PhoneNumber,
    WindowComponent
  },
  data() {
    return {
      url: '',
      name: '',
      method: 'POST',
      templateWindow: {} as any,
      template: {} as { [key: string]: any },
      currentLocationId: '',
      campaignId: '',
      saving: false,
      sending: false
    }
  },
  methods: {
    async save() {
      await this.$validator.validateAll()
      if (this.errors.any()) {
        return false
      }
      this.saving = true

      this.template.attributes.url = this.url
      this.template.attributes.method = 'POST'
      Vue.set(this.template, 'window', this.templateWindow)
      this.template.name = this.name
      this.$emit('save')
      this.$emit('hidden')
    },
    async sendTest() {
      console.log(this.url)
      if (!this.url) return
      this.sending = true

      let data: { [key: string]: any } = {
        location_id: this.currentLocationId,
        campId: this.campaignId,
        url: this.url,
        type: 'webhook'
      }

      try {
        let response = await this.$http.post('/message/preview', data)
      } catch (err) {
        console.error(err)
      }
      this.sending = false
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    })
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  watch: {
    values(values: { [key: string]: any }) {
      const data: () => object = <() => object>this.$options.data
      if (data) Object.assign(this.$data, data.apply(this))
      if (values.visible) $(this.$refs.modal).modal('show')
      else {
        this.errors.clear()
        $(this.$refs.modal).modal('hide')
      }
      this.template = values.template
      this.currentLocationId = values.currentLocationId
      this.campaignId = values.campaignId

      if (values.template) {
        this.url = this.template.attributes.url || ''
        this.method = this.template.attributes.method || 'POST'
        this.name = this.template.name
        if (values.template.window) {
          this.templateWindow = lodash.clone(values.template.window)
        }
      }
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hidden')
    })

    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }

    if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show')
    }
  }
})
</script>
