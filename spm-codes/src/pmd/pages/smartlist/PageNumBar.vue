<template>

  <div class="flex-page-bar">
      <div class="flex-left-align-magic">
        <div class="barsection2 mx-3">
        <span class="my-1" v-if="currentSelectedCount && !areAllPagesSelected"> You have selected <strong>{{currentSelectedCount}}</strong> records.</span>
        <span class="d-inline-block pointer link-on-hover mx-2" @click.prevent="selectAllPages" v-if="isCurrentPageSelected && !areAllPagesSelected && totalPages>1">Select all {{this.totalItems}} records.
        </span>
        <span class="my-1" v-if="areAllPagesSelected">* You have selected <strong><u>all records</u></strong> across all the pages.</span>
        <span class="d-inline-block pointer link-on-hover mx-2"  @click.prevent="unselectAllPages" v-if="areAllPagesSelected">Unselect all {{this.totalItems}} records.</span>
        <span v-if="fetcherror && !disabled" class="d-inline-block pointer link-on-hover mx-2"><span class="alert alert-danger --red">{{fetcherror}}</span></span>
        </div>
      </div>

      <div class="flex-right-portion">

        <div class="barsection mx-3">
          <span v-if="total > 1">Total <strong>{{totalItems}}</strong> records | <strong>{{selected}}</strong> of {{total}} Pages</span>
          <span v-else-if="totalItems > 0 && totalItems <= 20"><strong>{{totalItems}}</strong> records found</span>
          <span v-else-if="totalItems <= 0 && !disabled">No records found</span>
          <!-- <template  v-if="total> 1 && allowCustomInput">
              <input type="number" v-model="inputNum" placeholder="go to page" class="numInput mx-2">
          </template>-->
        </div>
        <div class="pagination">
          <div class="d-inline-flex" style="min-height:38px"  v-if="total > 1 && disabled && numArray">
              <div class="dummy" v-if="selected > 1">Go To First</div>
              <div class="dummy" v-if="selected > 1">❮ &nbsp;</div>
              <div v-for="index in numArray" :key="index">
                <div class="dummy">{{index}}</div>
              </div>
              <div class="dummy" v-if="total > selected ">&nbsp; ❯</div>
          </div>
          <template v-else-if="total > 1 &&  !allowCustomInput && !disabled" >
            <div class="d-inline-flex" >
              <a
                href="javascript:void(0);"
                v-if="selected > 1"
                @click.stop.prevent="first">Go To First</a>
              <a
                href="javascript:void(0)"
                v-if="selected > 1 && !fetcherror"
                @click.stop.prevent="jumpTo(selected - 1)">❮ &nbsp;</a>
              <div v-for="n  in numArray" :key="n">
                 <!--v-if="currentPage <= (total)"-->
                <a
                  href="javascript:void(0);"
                  class="active"
                  v-if="selected === n"
                  @click.stop.prevent="jumpTo(n)">{{n}}</a>
                <a
                  href="javascript:void(0);"
                  v-else
                  @click.stop.prevent="jumpTo(n)">{{n}}</a>
              </div>
              <a
                href="javascript:void(0)"
                v-if="total > selected && !fetcherror"
                @click.stop.prevent="jumpTo(selected + 1)">&nbsp; ❯</a>
            </div>
          </template>
        </div>
        <!-- <a href="javascript:void(0);" @click.stop.prevent="jumpTo(selected || 1)">
                <i class="fa fa-undo mx-2 --blue"></i>
        </a>-->
        <div class="barsection" v-if="totalItems > 20" >
          <div class="dropdown mt-0 mb-0 mx-3" style="position:static !important;min-width:120px !important;">
            <a
              id="dropdownMenuButton"
              @click.prevent="pageSizeToggler.toggle()"
              data-toggle="dropdown"
              aria-haspopup="true"
              aria-expanded="false"
              class="align-middle"
            >
              Page Size: {{pageSize}}
              <i class="fas fa-caret-down --light mx-2"></i>
            </a>
            <div
              aria-labelledby="dropdownMenuButton"
              :id="pageSizeToggler.id"
              style="max-height: 200px; overflow: auto;"
              class="dropdown-menu"
            >
              <div class="dropdown-item" @click.prevent="setPageSize(20)">
                <span class="text align-right">20</span>
              </div>
              <div class="dropdown-item" @click.prevent="setPageSize(50)">
                <span class="text align-right">50</span>
              </div>
              <div class="dropdown-item" @click.prevent="setPageSize(100)">
                <span class="text align-right">100</span>
              </div>

              <!-- class="dropdown-item my-1" v-for="(s,index) in smartLists"
                      v-if="index > 4"
              "-->
            </div>
          </div>
        </div>
        </div>

    </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { UxMessage } from '@/util/ux_message'
import { SafeDropDownToggle } from './safe_drop_down_toggle'

export default Vue.extend({
  name: 'PageNumBar',
  props: ['totalPages', 'totalItems', 'currentPage', 'disabled', 'pageSize', 'areAllPagesSelected', 'isCurrentPageSelected', 'currentSelectedCount', 'fetcherror'],
  inject: ['uxmessage'],
  data() {
    return {
      // selected: Number(this.currentPage),
      // total: Number(this.totalPages),
      allowCustomInput: false,
      inputNum: undefined,
      waitTime: 1000,
      debouncer: undefined as NodeJS.Timer | undefined,
      pageSizeToggler: new SafeDropDownToggle(this, 'pageSizeToggler')
    }
  },
  watch: {
    inputNum(n, o) {
      if (!n) return
      if (this.debouncer) clearTimeout(this.debouncer)
      if (n <= 0 || n > this.total) {
        this.uxmessage(UxMessage.errorType('Select a valid page number'))
        return
      }
      this.debouncer = setTimeout(() => this.jumpTo(n), this.waitTime)
    }
  },
  computed: {
    selected() {
      return Number(this.currentPage)
    },
    total() {
      return Number(this.totalPages)
    },
    numArray(){
      let temp = 5;
      let start = this.currentPage;
      const rt = [];
      do {
          rt.unshift(start);
          start -= 1;
      } while (start > 0 && rt.length < 5)

      return rt;
    }
  },
  methods: {
    customInput() {
      this.allowCustomInput = !this.allowCustomInput
    },
    first() {
      this.jumpTo(1)
    },
    last() {
      this.jumpTo(this.total)
    },
    jumpTo(num: number) {
      this.inputNum = undefined
      this.$emit('goToPage', num)
      //this.allowCustomInput = false;
    },
    setPageSize(num: number) {
      this.$emit('pageSizeChange', num)
    },
    selectAllPages(){
      this.$emit('selectAllPages')
    },
    unselectAllPages(){
      this.$emit('unselectAllPages')
    }
  }
})
</script>

<style  scoped>
.light-font {
  color: #607179 !important;
}
.buttonWidth {
  max-width: 40px !important;
  min-width: 40px !important;
}
.btn-link:focus,
.btn-link:hover {
  color: #23527c !important;
  text-decoration: underline !important;
  background-color: transparent !important;
}
.numInput {
  max-width: 90px !important;
  max-height: 20px !important;
  min-height: 20px !important;
  font-size: small !important;
  -moz-appearance: textfield;
  padding-bottom: 2px;
}

.numInput::-webkit-inner-spin-button,
.numInput::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}

.numInput::-webkit-inner-spin-button,
.numInput::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}




.bar-section-span {
display:table;
margin:0 auto;
}

.barsection2 {
  vertical-align: middle;
  display: inline-block;
}

.barsection {
  align-self: center !important;
  vertical-align: middle;
  display: inline-block;
}

.pagination {
  align-self: center !important;
}
.pad-item{
  padding: 12px 10px;
  padding-top:6px;
}
.pagination a {
  color: #607179;
  float: left;
  text-decoration: none;
  align-self: center;
  padding: 8px 10px;
}
.dummy {
  color: #607179;
  float: left;
  text-decoration: none;
  align-self: center;
  padding: 9px 11px;
}

.pagination input {
  margin-top: 8px;
}

.pagination a.active {
  background-color: #188bf6; /*#4CAF50;*/
  color: white;
  cursor: unset !important;
}

.pagination a:hover:not(.active) {
  background-color: #ddd;

}

.content-pad{
  padding: 8px 10px;
  padding-top:6px;
}

 .link-on-hover{
   color: #188bf6 !important;
   font-size: 15px;
   word-spacing: 1px;
 }

 .link-on-hover:hover{
   text-decoration: underline !important;
 }

.overflow-hidden{
  overflow:hidden
}

.flex-left-align-magic{
  margin-right: auto !important;
}

.fix-bar-height{
  height: 40px !important;
}

.flex-page-bar {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: space-between;
  background:#E8F3FE;
  align-items: center;
}

.flex-right-portion{
  margin-left: auto !important;
  display: flex;
  flex-wrap: wrap;
}

@media only screen and (max-width: 800px) {
  .flex-left-align-magic {
    margin-left: auto !important;
    margin-right: unset !important;
  }
}



</style>

<!--Flex Styling
  https://www.w3schools.com/css/tryit.asp?filename=trycss3_flexbox_flex-wrap_nowrap8
-->
