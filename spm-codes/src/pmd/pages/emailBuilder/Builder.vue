<template>
  <div
    id="emailBuilder"
    ref="emailBuilder"
    style="height: 100vh; width: 100%; border: none"
  ></div>
</template>

<script lang="ts">
import Vue from 'vue'
import Postmate from 'postmate'
import config from '@/config'
import firebase from 'firebase/app'
import { User } from '../../../models'

export const setElementDisplay = function (
  element: HTMLElement | null,
  displayValue: string
) {
  try {
    if (element) {
      if (element.hasAttribute('data-email-builder-display')) {
        element.removeAttribute('data-email-builder-display')
        return
      }
      const computedStyle = window.getComputedStyle(element)
      if (
        element.style.display === displayValue ||
        computedStyle.display === displayValue
      )
        element.setAttribute('data-email-builder-display', displayValue)
      else element.style.display = displayValue
    }
  } catch (err) {
    console.warn(err, '')
  }
}

export default Vue.extend({
  props: {
    location: {
      type: Object,
      required: true,
    },
    user: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      handshake: {},
    }
  },
  mounted() {
    this.loadMicroApp()
    this.toggelCustomJsVisibility('none')
  },
  beforeDestroy() {
    //@ts-expect-error postmate handler
    this.handshake.then((child: any) => {
      child.destroy()
    })
    this.toggelCustomJsVisibility('inline')
  },
  computed: {
    userValue() {
      return new User(this.user)
    },
  },
  methods: {
    toggelCustomJsVisibility(value: string) {
      // agency custom js breaks email builder drag and drop feature
      // hide that DOM temprorly when user opens email builder

      for (let element of [
        'customJS',
        'freshworks-container',
        'beacon-container',
      ]) {
        const customJsEl: HTMLElement | null = document.getElementById(element)
        setElementDisplay(customJsEl, value)
      }

      //@ts-expect-error dom operation
      document.body.children.forEach(e => {
        const child = e.children
        if (
          window.getComputedStyle(e).position === 'fixed' ||
          (e.id !== 'app' &&
            child.length > 0 &&
            window.getComputedStyle(child[0]).position === 'fixed')
        ) {
          setElementDisplay(e, value)
        }
      })
    },
    async getToken() {
      return await firebase.auth().currentUser.getIdToken()
    },
    async loadMicroApp() {
      const routerName = this.$route.name
      if (!routerName) return

      let query = "";
      try{
        query = Object.entries(this.$route.query)
        .flatMap(([key, value]) => [value].flat().map(v => [key, v]))
        .map(it => it.join("="))
        .join("&")
      } catch(err){
        console.warn(err)
      }

      this.handshake = new Postmate({
        container: this.$refs.emailBuilder as HTMLElement,
        url: `${config.emailPreviewAppUrl}${this.$route.path}?${query}`,
        name: 'email-preview',
        model: {
          userId: this.user.id,
          email: this.user.email,
          apiKey: this.location.api_key,
          userName: `${this.user.first_name} ${this.user.last_name}`,
          timeZone: this.location.timezone,
          tokenId: await this.getToken(),
          userPermissions: this.user.permissions,
          enableAssignToData: this.userValue.isAssignedTo,
          companyId: this.location.company_id
        },
      })
      //@ts-expect-error postmate handler
      this.handshake.then((child: any) => {
        child.on('spm-ts', (data: any) => {
          this.$router.push(data.router)
        })

        child.on('refresh-token', async () => {
          child.call('updated-token', await this.getToken())
        })
        child.on('exit-email-builder', (data: any) => {
          let response = false
          if (data.unsavedChanges) {
            response = !window.confirm(data.message)
          }
          !response && this.$router.push(data.router)
        })
      })
    },
  },
})
</script>
<style>
#emailBuilder iframe {
  width: 100%;
  height: 100%;
  border: none;
}
.email-builder {
  width: 100%;
  background: #ffffff;
}
</style>
