<template>
	<div>
		<SideBar/>
		<TopBar/>

		<section class="hl_wrapper">
            <section class="hl_wrapper--inner hl_services" id="services">
                <div class="container-fluid">
                    <div class="hl_services--cards">
						<div class="card">
							<div class="services-info">
								<h2>Whitelabel Services</h2>
								<iframe width="560" height="315"  frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen
									:src="`https://www.youtube.com/embed/${getVideoId('https://youtu.be/Jy38AlOfD3k')}`" id="services-video">
								</iframe>
								<p>
									HighLevel Services is the white-label fulfillment arm of HighLevel, where you can hire us to fulfill popular marketing services like Local SEO, Facebook &amp; Instagram Ads, Google Ads, Funnel Building, and more!
									<br />
									It's a great opportunity for agencies who want to offer more services to their clients without having to hire more staff.
									Maybe you're great at Facebook ads but your clients are asking you for SEO, 
									instead of letting them wander off to another agency who provides it both, 
									you can sell them one of our SEO programs and will fulfill the work.
									<br />
									The HighLevel Services team is a robust group of experienced marketers, 
									who have spent years creating and honing. 
									<br/>
									We look forward to helping you deliver a broader level of success for your clients through high level services.
								</p>									
							</div>
						</div>
                        
                        <div class="row" v-if="services.length > 0">
                            <div class="col-sm-6 col-lg-4" v-for="service in services" :key="service.id" v-if="service.betaOnly? company.betaServicesEnabled :true">
                                <div class="card">
                                    <div class="card-body">
                                        <i :class="`card-icon --${service.iconBgcolorName || 'blue'}`">
                                            <img :src="service.iconUrl" class="service-icon">
                                        </i>
                                        <h3>{{service.name}}</h3>
                                        <p style="min-height:75px">{{service.overview}}</p>
                                        <div class="price">
                                            <p>{{service.plans.length === 1? 'Just' : 'Starting'}} at</p>
                                            <h4 v-if="service.plans[0].wholesale_recur_price">${{service.plans[0].wholesale_recur_price}}<span>/month</span></h4>
                                            <h4 v-else>${{service.plans[0].wholesale_setup_price}}<span></span></h4>
                                        </div>
                                        <button type="button" class="btn btn-success btn-sm" @click="showDetails(service.id)">Find out more</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else class="page-spinner">
                            <moon-loader color="#37ca37" size="30px" />
                        </div>
                    </div>
                </div>
            </section>
            <!-- END of .hl_services -->
		</section>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
import TopBar from '../../../components/TopBar.vue';
import SideBar from '../../../components/agency/SideBar.vue';
import { AgencyService, Company } from '@/models';
import { mapState } from 'vuex';
import { CompanyState } from '@/store/state_models';
import { getVideoId } from "@/util/video_utils";

export default Vue.extend({
    components: { TopBar, SideBar },
    data() {
        return {
			services: [] as AgencyService[],
			getVideoId: getVideoId,
	    }
    },
    mounted(){
        this.fetchData();
    },
    computed: {
		...mapState('company', {
			company: (s: CompanyState) => {
				return s.company ? new Company(s.company) : undefined;
			},
		}),
	},
    methods:{
        async fetchData() {
            // Fetching Services List
            let services = await AgencyService.getAllServices();
            this.services = services.sort( (a,b) => {
                if(a.name > b.name)
                    return 1;
                else if (a.name < b.name)
                    return -1;
                return 0;
            })
        },
        showDetails(serviceId : string){
            this.$router.push({ name: 'service_detail', params: { service_id: serviceId } });   
        }
    }
});
</script>
<style scoped>
.hl_wrapper--inner{
	padding-top: 0px !important;
}
.page-spinner{
    height: 50vh;
    display: flex;
    justify-content: center;
    align-items: center;
}
.service-icon{
    max-height: 48px;
    max-width: 48px;
}
.services-info{
	/* display: flex;
	justify-content: space-between; */
	position:relative;
	padding:30px !important;
	text-align:justify;
}
.services-info h2{
	display: inline-block;
}
#services-video{
	max-width: 560px;
    width: 100%;
    float: right;
    margin-left: 30px;
    margin-bottom: 15px;
}
</style>
