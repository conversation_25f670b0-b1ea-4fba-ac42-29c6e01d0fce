<template>
  <div class="funnel-step-product">
    <div
      class="products-list-container"
      v-if="activeComponent === 'product-list'"
    >
      <div class="product-headline">
        <span class="headline">
          Products
          <span class="count">({{ productList.length }})</span>
        </span>
        <span class="add-new">
          <span class="text-error" v-if="allowStripeOption === false"
            >You don't have a valid stripe configuration to add
            product.&nbsp;</span
          >
          <i
            class="fas fa-plus"
            :class="!allowStripeOption ? 'disable-add' : ''"
            v-b-tooltip.hover
            title="Add Product"
            @click="allowStripeOption ? addProduct() : ''"
          ></i>
        </span>
      </div>
      <div class="product-list">
        <template v-for="(product, index) in productList">
          <div class="product-row" :id="index" :key="index">
            <div class="product-name">
              <i class="fas fa-cart-plus"></i> {{ product.productName }}
            </div>
            <div class="product-price">
              <span class="price-btn gray"
                >Type:{{ product.paymentType | pType }}</span
              >
              <!-- <span class="price-btn" >Price:{{product.priceDisplay ? product.priceDisplay : '$0'   }}</span> -->
              <span class="price-btn"
                >Price:{{
                  product.priceDisplay
                    ? product.priceDisplay
                    : product.productPrice
                    ? product.productPrice
                    : '$0'
                }}</span
              >
            </div>
            <div class="product-action">
              <button
                class="btn-action"
                v-b-tooltip.hover
                title="Edit Product"
                @click="editProduct(product)"
              >
                <i class="fas fa-edit"></i>Edit
              </button>
              <button
                class="btn-action"
                v-b-tooltip.hover
                title="Delete Product"
                @click="deleteProduct(product.id)"
              >
                <i class="far fa-trash-alt"></i>Delete
              </button>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div v-else class="manage-product">
      <span @click="backToList"
        ><i class="fas fa-arrow-left"></i> Products</span
      >
      <hr />
      <FunnelAddProducts
        v-if="activeComponent === 'add' || activeComponent === 'edit'"
        :action-type="activeComponent"
        :product-id="productId"
        :id="id"
        :type="productType"
        :offerId="offerId"
      />
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import FunnelAddProducts from '../../components/funnels/FunnelAddProducts.vue'
import { Funnel } from '../../../models'
import { PRODUCT_TYPE_OPTIONS } from '../../../util/funnel_consts'
import { PaymentServices } from '@/services'
let unsubscribeProduct: () => void

export default Vue.extend({
  components: {
    FunnelAddProducts
  },
  data() {
    return {
      activeComponent: 'product-list',
      productList: [],
      productId: '',
      id: '' as string | undefined,
      productType: '' as string,
      offerId: '' as string | undefined,
      allowStripeOption: ''
    }
  },
  created() {
    this.loadData();
    this.validateStripe();
  },
  beforeDestroy() {
    if (unsubscribeProduct) unsubscribeProduct()
  },
  methods: {
    editProduct(payload: any) {
      const { id, plan_id: planId, paymentType, offerId } = payload
      this.activeComponent = 'edit'
      this.productId = id
      this.id = planId || undefined
      this.productType = paymentType
      this.offerId = offerId || undefined
    },
    addProduct() {
      this.activeComponent = 'add'
      this.cleanPropsValues()
    },
    backToList() {
      this.activeComponent = 'product-list'
      this.cleanPropsValues()
    },
    cleanPropsValues() {
      this.id = ''
      this.productId = ''
      this.offerId = ''
    },
    async deleteProduct(id: string) {
      const funnel = await Funnel.getById(this.$route.params.funnel_id)
      const step: any = funnel.getStepById(this.$route.params.step_id)
      const index = step.products.findIndex((x: any) => x.id === id)
      if (index > -1) {
        step.products[index].deleted = true
      }
      funnel.updateStep(step)
      await funnel.save()
    },
    async loadData() {
      // const funnel = await Funnels.getById(this.$route.params.funnel_id)
      // const step = funnel.getStepById(this.$route.params.step_id)
      // this.productList = step.products
      if (unsubscribeProduct) unsubscribeProduct()
      unsubscribeProduct = Funnel.getFunnelById(
        this.$route.params.funnel_id
      ).onSnapshot(snapshot => {
        const funnel: any = new Funnel(snapshot)
        const { products } = funnel.getStepById(this.$route.params.step_id)

        this.productList = Array.isArray(products)
          ? products.filter((product: any) => product.deleted !== true)
          : []
      })
    },
    switchToProductList() {
      this.activeComponent = 'product-list'
    },
    async validateStripe() {
      const currentLocation = this.$store.state.locations.locations.find(
        (x: any) => x.id === this.$route.params.location_id
      )

      try {
        const { data: locationStripeKeys } = await PaymentServices.findStripeIntegration(this.$route.params.location_id);
        this.allowStripeOption = !!(locationStripeKeys?.live?.publishableKey || locationStripeKeys?.test?.publishableKey);
      } catch (error) {
        console.log(error);
      }

      if (!this.allowStripeOption) this.allowStripeOption = !!currentLocation.stripe?.publishable_key;
    }
  },
  filters: {
    pType(value: string): string {
      if (!value) return ''
      const type = PRODUCT_TYPE_OPTIONS.find(x => x.value === value)
      return type ? type.label : ''
    }
  }
})
</script>

<style scoped>
.text-error {
  color: red;
}
.disable-add {
  color: var(--light) !important;
}
.products-list-container {
  display: grid;
  grid-template-columns: 1fr;
  margin-top: 10px;
}
.product-headline {
  background-color: #e7f3fe;
  width: 100%;
  height: 40px;
  padding: 8px;
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
}
.product-headline .headline {
  font-size: 1rem;
  font-weight: bold;
}
.product-headline .count {
  font-size: 0.8rem;
}
.product-row {
  display: grid;
  grid-template-columns: 2fr 2fr 1fr;
  grid-column-gap: 10px;
  margin-top: 7px;
  border-bottom: 1px solid rgb(231, 243, 254);
  padding-bottom: 5px;
}
.price-btn {
  border: 1px solid #188bf6;
  padding: 5px;
  background-color: #e7f3fe;
  border-radius: 7px;
  margin-right: 5px;
}
.gray {
  background-color: #e8e8e8;
  border: 1px solid #5d5d5d;
}
.product-action {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 0.2rem;
}
.product-action .btn-action {
  background: #f0f5f9;
  color: #607179;
  border-radius: 3px;
  border: none;
}
.product-price {
  padding-top: 5px;
  text-align: right;
}
.product-name {
  padding: 5px;
  font-weight: 500;
}
.btn-action i {
  margin-right: 2px;
}
.product-headline .add-new {
  float: right;
}
.add-new i {
  color: #188bf6;
  font-size: 1rem;
  margin-right: 9px;
  cursor: pointer;
}
.manage-product {
  padding: 10px;
}
.manage-product span {
  color: #188bf6;
  font-size: 13px;
  cursor: pointer;
}
</style>
