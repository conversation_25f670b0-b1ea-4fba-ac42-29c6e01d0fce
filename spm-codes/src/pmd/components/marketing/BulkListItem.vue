<template>
  <tr>
    <td>
      <a
        href="javascript: void(0);"
        @click.prevent="$emit('edit', bulkRequest)"
      >
        <!--<span class="active">A</span>-->
        {{ bulkRequest.name | toTitleCase }}
      </a>
    </td>
    <td>{{ bulkRequest.contactType | toTitleCase }}</td>
    <td>
      <div class="tag" v-for="tag in bulkRequest.tags">{{ tag }}</div>
    </td>
    <td v-if="bulkRequest.scheduledTime">
      {{
        bulkRequest.scheduledTime
          .tz(timezone)
          .format(getCountryDateFormat('normal'))
      }}
    </td>
    <td v-else>Start immediately</td>
    <td v-if="bulkRequest.nextExecutionTime">
      {{
        bulkRequest.nextExecutionTime
          .tz(timezone)
          .format(getCountryDateFormat('normal'))
      }}
    </td>
    <td v-else>-</td>
    <td>{{ capitalize(bulkRequest.status) }}</td>
    <td align="center">
      <div class="icon-with-loader">
        <i
          class="fa fa-pause --red pointer center-inside"
          v-if="
            !loading &&
              (bulkRequest.status == 'queued' ||
                bulkRequest.status == 'running')
          "
          @click.prevent="pauseTask"
        />
        <i
          :class="
            `fa fa-${
              bulkRequest.status === 'finished' ? 'redo' : 'play'
            } --green pointer center-inside`
          "
          v-if="
            !loading &&
              (bulkRequest.status === 'paused' ||
                bulkRequest.status == 'new' ||
                bulkRequest.status === 'finished')
          "
          @click.prevent="startTask"
        />
        <moon-loader
          v-if="loading"
          :customClass="'center-inside'"
          :loading="loading"
          color="#607179"
          size="15px"
        />
      </div>
      <div class="icon-with-loader">
        <i
          @click.prevent="$emit('edit', bulkRequest)"
          class="fa fa-edit pointer hint-icon center-inside"
        />
      </div>
      <div class="icon-with-loader">
        <i
          class="fa fa-trash pointer hint-icon center-inside"
          @click.prevent="deleteBulkRequest"
        />
      </div>
    </td>
  </tr>
</template>

<script lang="ts">
import Vue from 'vue'
import firebase from 'firebase/app'
import * as moment from 'moment-timezone'
import { getCountryDateFormat } from '@/models'

export default Vue.extend({
  props: ['bulkRequest', 'timezone'],
  data() {
    return {
      currentLocationId: '',
      loading: false,
      getCountryDateFormat: getCountryDateFormat
    }
  },
  methods: {
    capitalize(value: string) {
      return lodash.capitalize(value)
    },
    async pauseTask() {
      try {
        this.loading = true
        await this.$http.delete(`/bulk/${this.bulkRequest.id}/pause`)
      } catch (err) {
        console.log(err)
      }
      this.loading = false
    },
    async startTask() {
      try {
        this.loading = true
        if (this.bulkRequest.status === 'finished')
          this.bulkRequest.scheduledTime = moment()
        this.bulkRequest.status = 'queued'
        await this.bulkRequest.save()
        await this.$http.post(`/bulk/${this.bulkRequest.id}/queue`)
      } catch (err) {
        console.log(err)
      }
      this.loading = false
    },
    async deleteBulkRequest() {
      if (confirm('Are you sure you want to delete this bulk request?')) {
        try {
          await this.$http.delete(`/bulk/${this.bulkRequest.id}/pause`)
          this.bulkRequest.ref.update({
            deleted: true,
            date_updated: firebase.firestore.Timestamp.fromMillis(
              moment().valueOf()
            )
          })
        } catch (err) {
          console.log(err)
        }
      }
    }
  }
})
</script>
