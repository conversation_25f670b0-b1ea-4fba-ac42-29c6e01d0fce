<template>
  <div class="card">
    <div class="card-heading">
      <h3 class="website-page-name">{{ cloningWebPage ? 'Cloning..' : step.name }}</h3>
      <div class="dropdown">
        <i @click="toggleDropdown()" class="fa fa-ellipsis-v"></i>
        <div :class="[ showDropdown ? 'show' : '', 'dropdown-menu']">
          <div @click="edit()" class="dropdown-item">
            <i class="icon icon-settings-1"></i>&nbsp;Settings
          </div>
          <div @click="clone()" class="dropdown-item">
            <i class="icon icon-duplicate"></i>&nbsp;Clone
          </div>
          <div @click="showProducts" class="dropdown-item">
           <i class="fab fa-product-hunt"></i>&nbsp;Products
          </div>
          <div @click="askDeleteConfirmation()" class="dropdown-item">
            <i class="icon icon-trash"></i>&nbsp;Delete
          </div>


        </div>
      </div>
    </div>
    <div class="card-body">
      <div class="preview-screenshot">
        <img
          v-if="funnelPage.snapshotPreview"
          @load="screenshotLoaded = true"
          :src="funnelPage.snapshotPreview"
        />
        <img
          v-if="funnelPage.id && !funnelPage.snapshotPreview"
          src="https://via.placeholder.com/400x350?text=Edit Your Funnel"
        />
        <img
          v-if="funnelPage.snapshotPreview && !screenshotLoaded"
          width="50"
          src="../../../assets/pmd/img/loading-spinner.gif"
          alt="Loading"
        />
      </div>
      <div class="controls">
        <a :href="builderUrl" class="btn btn-yellow-lt btn-sm" title="Edit Page">
          <i class="icon icon-edit"></i>
          <span class="sr-only">Edit Page</span>
        </a>
        <a
          :href="previewUrl"
          :style="{float: 'right'}"
          class="btn btn-light2 btn-sm"
          target="_blank"
          data-tooltip="tooltip"
          data-placement="top"
          title="Preview Page"
        >
          <i class="icon icon-share-2"></i>
          <span class="sr-only">Preview Page</span>
        </a>
      </div>
    </div>
    <ConfirmDeleteModal
      :showModal="showDeleteModal"
      @showDeleteModal="val => showDeleteModal = val"
      @hidden="showDeleteModal = false"
      @delete="deletePage"
    />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { FunnelPage } from '../../../models'
import ConfirmDeleteModal from '../../components/funnels/ConfirmDeleteModal.vue'
import config from '@/config'

export default Vue.extend({
  components: {
    ConfirmDeleteModal
  },
  props: {
    step: {
      type: Object
    },
    funnelDomain: {
      type: String
    },
    cloningWebPage: {
      type: Boolean,
      default: false
    },
    orderFormVersion:{
      type:Number,
      default: null
    }
  },
  data() {
    return {
      fetchingFunnelPages: false,
      funnelPage: {} as FunnelPage,
      screenshotLoaded: false,
      cloningFunnelStep: false,
      showDropdown: false,
      showDeleteModal: false
    }
  },
  computed: {
    builderUrl(): string {
      if (this.funnelPage && this.funnelPage.id) {
        const pageId = this.funnelPage.id
        let url
        if (config.mode === 'dev') {
          url = 'http://localhost:3333'
        } else {
          url = `https://${window.location.hostname}`
        }

        return `${url}/v2/builder/${pageId}`
      } else {
        return '#'
      }
    },
    previewUrl(): string {
      let host
      if (config.mode === 'dev') {
        host = 'http://localhost:3333'
      } else {
        host = `https://${window.location.hostname}`
      }

      if (!this.funnelDomain) {
        return `${host}/v2/preview/${this.funnelPage.id}`
      }

      const pageUrl = `https://${this.funnelDomain}${this.step.url}`
      return pageUrl
      // const pageUrl = encodeURIComponent(this.funnelPage.url)
      // return `${host}/v2/preview?domain=${this.funnelDomain}&page_url=${pageUrl}`
    }
  },
  mounted() {
    const { pages } = this.step
    const firstPage = pages && pages.length ? pages[0] : null
    if (firstPage) {
      this.fetchFunnelPage(firstPage)
    }
  },
  methods: {
    toggleDropdown() {
      this.showDropdown = !this.showDropdown
    },
    async fetchFunnelPage(pageId: string) {
      try {
        this.fetchingFunnelPages = true
        this.funnelPage = await FunnelPage.getById(pageId)
      } catch (error) {
        console.error('error while fetching funnel page --> ', error)
      } finally {
        this.fetchingFunnelPages = false
      }
    },
    edit() {
      this.$emit('editWebsitePage', this.step.id)
      this.toggleDropdown()
    },
    clone() {
      this.$emit('cloneWebsitePage', this.step.id)
      this.toggleDropdown()
    },
    deletePage() {
      this.toggleDropdown();
      this.showDeleteModal = false
      this.$emit('deleteWebsitePage', this.step)
    },
    askDeleteConfirmation(){
      this.showDeleteModal = true;
    },
    showProducts(){
      if(this.orderFormVersion === 2){
        this.$router.push({name:'website_step_products',params:{step_id: this.step.id}})
      }else {
        this.$router.push({name:'website_products',params:{step_id: this.step.id}})
      }
    }
  },
  watch: {
    'step.pages': function(newValue, oldValue) {
      try {
        if (
          newValue &&
          newValue.length &&
          newValue.length !== oldValue.length
        ) {
          const firstPage = newValue[0]
          this.fetchFunnelPage(firstPage)
        }
      } catch (error) {
        console.error('error while getting step pages --> ', error)
      }
    }
  }
})
</script>
<style scoped>
.card-heading {
  padding: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-heading .website-page-name {
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 18px;
}

.card-heading i {
  cursor: pointer;
}

.card-body {
  padding: 20px;
  padding-top: 0;
}

.preview-screenshot {
  border-radius: 4px;
  text-align: center;
}

.preview-screenshot img {
  max-width: 100%;
  max-height: 125px;
  border-radius: inherit;
}

.controls {
  margin-top: 12px;
}

.dropdown > i {
  padding: 5px;
  border-radius: 3px;
}

.dropdown > i:hover {
  background-color: lightgray;
}

.dropdown-menu {
  left: -150px;
  top: 25px;
}

.dropdown-item {
  cursor: pointer;
}
</style>
