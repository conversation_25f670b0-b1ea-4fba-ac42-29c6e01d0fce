import Vue from 'vue'
import { User, AuthUser } from '../models'
import { Module, ActionContext } from 'vuex'
import firebase from 'firebase/app'
import { UserState, RootState } from '@/store/state_models'
import axios from 'axios'
import router from '../routes/index'
import moment from 'moment'
import localStorage from 'store'

let unsubscribe: () => void
export const user: Module<UserState, RootState> = {
  namespaced: true,
  state: {
    passwordUpdatedInOnboarding: false,
    internalUser: false
  },
  // getters: {
  //   getPermission: (
  //     state: UserState,
  //     getters: any,
  //     rootState: RootState,
  //     rootGetters: any
  //   ) => (locationId: string) => {
  //     if (locationId && state.user.locationPermissions) {
  //       const locationSpecificPermission = state.user.locationPermissions
  //       if (locationSpecificPermission[locationId]) {
  //         return locationSpecificPermission[locationId]
  //       }
  //     }
  //     return state.user.permissions
  //   },
  // },
  getters: {
    get: (state: UserState) => {
			return state.user;
    },
		isAdminUser: (state: UserState) => {
			return ['admin', 'agency'].includes(state.user.role)
		}
	},
  mutations: {
    set: (state: UserState, payload: object) => {
      Vue.set(state, 'user', payload)
    },
    updatePasswordUpdatedInOnboarding(state: UserState, payload: boolean) {
      console.log('updatePasswordUpdatedInOnboarding', payload)
      state.passwordUpdatedInOnboarding = payload
    },
    setInternalUser(state: UserState, payload: boolean) {
      state.internalUser = payload
    },
  },
  actions: {
    get: {
      handler: async (context: ActionContext<UserState, RootState>) => {
        if (context.state.user) {
          return context.state.user
        }
        try {
          const credentials = new AuthUser(
            await context.dispatch('auth/get', null, { root: true })
          )
          const user = await User.getById(credentials.userId)
          const data = { ...user.data, id: user.id }
          context.commit('set', data)
          return data
        } catch (err) {
          console.error(err)
          console.error(typeof err)
          const currentUser = firebase.auth().currentUser
          if (currentUser) {
            const token = await currentUser.getIdToken()
            console.error(token)
            if (token) {
              const values = atob(token)
              console.error(values)
            }
          }
          // missing or insufficient permissions handeler
          if (err.message === 'Missing or insufficient permissions.') {
            console.log('Error while auth/get in store - firebase auth.')
            try {
              router.push({ name: 'login', params: { logout: 'true' } })
              await context.dispatch('logout')
              try {
                firebase.auth().signOut()
              } catch (err) {
                console.error(
                  'Error while auth/get in store - firebase auth.',
                  err
                )
              }
              try {
                await axios.post('/signout')
              } catch (err) {
                console.error(
                  'Error while auth/get in store - axios signout.',
                  err
                )
              }

              // Clear all the cookies and localostorage
              document.cookie.split(';').forEach(function (c) {
                document.cookie = c
                  .replace(/^ +/, '')
                  .replace(
                    /=.*/,
                    '=;expires=' + new Date().toUTCString() + ';path=/'
                  )
              })
              window.localStorage.clear()

              // Clear the Indexed DB
              if (window.indexedDB) {
                const dbs = [
                  'firebase-installations-database',
                  'firebaseLocalStorageDb',
                  'firebase-messaging-database',
                ]
                dbs.forEach(db => {
                  window.indexedDB.deleteDatabase(db)
                })
              }
              document.getElementById('app').classList.remove('loading')
            } catch (err) {
              console.error('Error while auth/get in store.', err)
            }
          }
        }
      },
    },
    syncAll: {
      root: true,
      handler: async (context: ActionContext<UserState, RootState>) => {
        let credentials: AuthUser
        try {
          credentials = new AuthUser(
            await context.dispatch('auth/get', null, { root: true })
          )
        } catch (e) {
          return
        }

        unsubscribe = User.getStreamById(credentials.userId).onSnapshot(
          async (snapshot: firebase.firestore.DocumentSnapshot) => {
            const data = snapshot.data()

            let logoutDueToPasswordChange = false
            if (data && data.last_password_change) {
              const loginDateFromStorage = localStorage.get('loginDate')
              if (!loginDateFromStorage) {
                logoutDueToPasswordChange = true
              } else {
                const loginDate = moment(loginDateFromStorage)
                const lastPasswordChange = moment(
                  data.last_password_change.toMillis()
                )
                if (
                  loginDate &&
                  lastPasswordChange &&
                  loginDate.isBefore(lastPasswordChange)
                ) {
                  if (!context.state.passwordUpdatedInOnboarding) {
                    console.log(
                      'Logging out due to password change',
                      loginDate,
                      lastPasswordChange
                    )
                    logoutDueToPasswordChange = true
                  }
                }
              }
            }

            const internalUser = localStorage && localStorage.get('app_referer') === 'https://support.leadconnectorhq.com/'
            if (internalUser) context.commit('setInternalUser', true);
            if (!internalUser && data && !data.last_login_time) {
              logoutDueToPasswordChange = true
            }

            if (
              !data ||
              data.deleted === true ||
              data.is_active === false ||
              (context.state &&
                context.state.user &&
                context.state.user.role !== data.role) ||
              logoutDueToPasswordChange
            ) {
              try {
                await axios.post('/signout')
                await context.dispatch('logout', null, { root: true })
                firebase.auth().signOut()
                router.push('/')
              } catch (err) {
                console.error(err)
              }
            }
            context.commit('set', { ...snapshot.data(), id: snapshot.id })
          },
          async error => {
            console.error(error)
            console.error(typeof error)
            const token = await firebase.auth().currentUser.getIdToken()
            console.error(token)
            if (token) {
              const values = atob(token)
              console.error(values)
            }
          }
        )
      },
    },
    deleteUserState: {
      handler: (context: ActionContext<UserState, RootState>) => {
        context.commit('set', undefined)
      },
    },
    logout: {
      root: true,
      handler: async (context: ActionContext<UserState, RootState>) => {
        context.dispatch('deleteUserState')
        if (unsubscribe) {
          unsubscribe()
        }
        if (window && window.pendo) {
          window.pendo.clearSession();
        }
      },
    },
  },
}

