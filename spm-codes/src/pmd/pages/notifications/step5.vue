<template>
    <div class="ps-content col-xs-9">
        <!--WIZARD STEPS-->
        <!-- ngSwitchWhen: type -->
        <!-- ngSwitchWhen: track -->
        <!-- ngSwitchWhen: display -->
        <!-- ngSwitchWhen: message -->
        <!-- ngSwitchWhen: customize -->
        <!-- ngIf: shouldShowStep('customize') -->
        <div class="step-customize">
            <div>
                <div class="notification-live-preview animated bounceInUp">
                    <div class="pb-2 text-success"><i class="fa fa-circle animated fadeIn infinite mr-1"></i> Live Preview</div>
                    <PsBubble :notification="notification" />
                    <!-- <div class="ps-bubble">
                        <div class="bubble-body mx-auto no-text-select ng-scope" ng-class="{'rtl-bubble' : settings.rtl}">
                            <div class="bubble-icon">
                                <img data-ng-src="data:image/svg+xml;base64,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" src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzc5NTU0OCIgZD0iTTExLjUgNS45M3YxLjUwMmMwIC4yNzYuMjI0LjUuNS41cy41LS4yMjQuNS0uNVY1LjkzYy0uMTYuMDQtLjMyNi4wNy0uNS4wN3MtLjM0LS4wMy0uNS0uMDd6Ii8+PHBhdGggZmlsbD0iI0ZGQjMwMCIgZD0iTTE0IDRjMC0xLjEwNC0yLTQtMi00cy0yIDIuODk2LTIgNGMwIC45My42NCAxLjcwNiAxLjUgMS45M1Y1YzAtLjI3Ni4yMjQtLjUuNS0uNXMuNS4yMjQuNS41di45M2MuODYtLjIyNCAxLjUtMSAxLjUtMS45M3oiLz48Y2lyY2xlIGZpbGw9IiNGRjk4MDAiIGN4PSIxMiIgY3k9IjUiIHI9IjEiLz48cGF0aCBmaWxsPSIjRTY1MTAwIiBkPSJNMTIgNC41Yy0uMjc2IDAtLjUuMjI0LS41LjV2LjkzYy4xNi4wNC4zMjYuMDcuNS4wN3MuMzQtLjAzLjUtLjA3VjVjMC0uMjc2LS4yMjQtLjUtLjUtLjV6Ii8+PHBhdGggZmlsbD0iI0Y4QkJEMCIgZD0iTTE0IDE4YzAgLjU1My0uNDQ3IDEtMSAxaC0yYy0uNTUyIDAtMS0uNDQ3LTEtMVY4YzAtLjU1My40NDgtMSAxLTFoMmMuNTUzIDAgMSAuNDQ3IDEgMXYxMHoiLz48cGF0aCBmaWxsPSIjOUZBOERBIiBkPSJNMiAyNGgyMGMxLjEwNCAwIDItLjg5NiAyLTJIMGMwIDEuMTA0Ljg5NiAyIDIgMnoiLz48cGF0aCBmaWxsPSIjRkNFNEVDIiBkPSJNMjIgMTdjMC0uMDQ0LS4wMTItLjA4NS0uMDE0LS4xMy0uNjkgMS4wOTMtMS42MjIgMS42My0yLjg0NyAxLjYzLTEuMjc0IDAtMi4yMzMtLjU3NC0yLjkzMy0xLjc1NWwtLjQwOC0uNjktLjQ0LjY3Yy0uNzcgMS4xOC0xLjkwMiAxLjc3NS0zLjM2IDEuNzc1LTEuNDggMC0yLjU4LS41OC0zLjM2Mi0xLjc3NGwtLjQ0LS42Ny0uNDA4LjY5Yy0uNyAxLjE4LTEuNjU3IDEuNzU0LTIuOTMgMS43NTQtMS40ODQgMC0yLjM1OC0uODU2LTIuODQ4LTEuNjI3IDAgLjA0My0uMDEyLjA4NC0uMDEyLjEyOHYxLjQxOGMuNjYzLjYwNiAxLjU4OCAxLjA4MyAyLjg2IDEuMDgzIDEuMzk1IDAgMi41My0uNTUgMy4zOC0xLjY0Ljk0IDEuMDkgMi4yIDEuNjQgMy43NiAxLjY0IDEuNTM0IDAgMi44Mi0uNTY0IDMuNzU4LTEuNjQzLjg1IDEuMDkyIDEuOTg0IDEuNjQ0IDMuMzgyIDEuNjQ0IDEuMTMgMCAyLjA4NS0uMzcgMi44Ni0xLjA4NlYxN3oiLz48cGF0aCBmaWxsPSIjRkY0MDgxIiBkPSJNNC44NiAxOC41YzEuMjcyIDAgMi4yMy0uNTc0IDIuOTI4LTEuNzU0bC40MDgtLjY5LjQ0LjY3QzkuNDIgMTcuOTIgMTAuNTIgMTguNSAxMiAxOC41YzEuNDU4IDAgMi41ODgtLjU5NyAzLjM2Mi0xLjc3NGwuNDQtLjY3LjQwNy42OWMuNjk3IDEuMTggMS42NTYgMS43NTQgMi45MyAxLjc1NCAxLjIyNCAwIDIuMTU0LS41MzcgMi44NDYtMS42M0MyMS45MTYgMTUuMjc2IDIwLjYxMyAxNCAxOSAxNEg1Yy0xLjYxMyAwLTIuOTIgMS4yNzYtMi45ODcgMi44NzMuNDkuNzcgMS4zNjMgMS42MjcgMi44NDYgMS42Mjd6Ii8+PHBhdGggZmlsbD0iIzYzMkQyMSIgZD0iTTE5LjE0IDE5LjVjLTEuMzk3IDAtMi41MzItLjU1Mi0zLjM4Mi0xLjY0NC0uOTQgMS4wOC0yLjIyNSAxLjY0NC0zLjc1OCAxLjY0NC0xLjU2IDAtMi44Mi0uNTUyLTMuNzYtMS42NDItLjg1IDEuMDktMS45ODQgMS42NDItMy4zOCAxLjY0Mi0xLjI3MiAwLTIuMTk3LS40NzctMi44Ni0xLjA4M1YyMmgyMHYtMy41ODdjLS43NzUuNzE2LTEuNzMgMS4wODctMi44NiAxLjA4N3oiLz48L3N2Zz4=">
                            </div>
                            <div class="bubble-content">
                                <div class="bubble-title text-truncate">
                                    <span ng-style="settings.theme.title" class="ng-binding" style="color: rgb(120, 37, 243); background-color: rgb(242, 233, 255);">50 visitors</span>
                                </div>
                                <div class="bubble-description">
                                    <span class="ng-binding">jumped around and got crazy!</span>
                                </div>
                                <div class="bubble-time">
                                    <div class="ng-binding">in the past 49 minutes
                                        
                                        <div class="ps-link ng-scope" ng-if="!$parent.settings.whitelabel || ($parent.settings.whitelabel &amp;&amp; $parent.settings.branding.active)">
                                            <span class="bubble-bolt ng-binding">⚡ by</span>
                                            <a href="" ng-style="{'color' : $parent.$parent.settings.whitelabel ? $parent.$parent.settings.branding.color || '#7627f3' : '#7627f3'}" class="ng-binding" style="color: rgb(118, 39, 243);">ProveSource</a>
                                        </div>
                                    </div>
                                </div>
                                <span id="ps-bubble-close" ng-show="settings.allowClose" class="ng-hide">
                                    <span class="close-before"></span>
                                    <span class="close-after"></span>
                                </span>
                            </div>
                        </div>
                    </div> -->
                </div>
                <div class="step-content customize-step">
                    <p>Change the following settings to control the behavior and appearance of your notification</p>
                    <div class="step-panel">
                        <div class="title">Design</div>
                        <div class="section row">
                            <div class="col-sm-6 sub-title">Notification position</div>
                            <div class="col-sm-6 text-right">

                                <div class="ps-selectbox " placeholder="Select position">
                                    <select class="selectpicker" :value="settings.position" @change="setPosition($event);">
                                        <option value="bottom_left">Bottom Left</option>
                                        <option value="bottom_right">Bottom Right</option>
                                        <option value="top_left">Top Left</option>
                                        <option value="top_right">Top Right</option>
                                    </select>
                                </div>

                            </div>
                        </div>
                        <div class="section row">
                            <div class="col-sm-6 sub-title ng-binding">Title Color</div>
                            <div class="col-sm-6 text-right">
                                <div class="color-picker-wrapper color-picker-closed" ref="titlepicker">
                                    <div class="color-picker-input-wrapper input-group">
                                        <input type="text" data-lpignore="true" :value="titleColor" class="form-control" @focus="showTitlePicker();" />
                                        <span class="color-picker-swatch ng-scope color-picker-swatch-right input-group-addon" :style="'background-color: ' + titleColor"></span>
                                    </div>
                                    <div class="color-picker-panel color-picker-panel-bottom color-picker-panel-left color-picker-show-hue">
                                        <chrome-picker :value="titleColor" v-show="showTitleColor" @input="changeTitleColor" style="position: absolute;right: 55px;z-index: 99999;" />
                                    </div>

                                </div>

                            </div>
                        </div>

                        <div class="section row">
                            <div class="col-sm-6 sub-title ng-binding">Title Background</div>
                            <div class="col-sm-6 text-right">
                                <div class="color-picker-wrapper color-picker-closed" ref="backgroundpicker">
                                    <div class="color-picker-input-wrapper input-group">
                                        <input type="text" data-lpignore="true" :value="backgroundColor" class="form-control" @focus="showBackgroundPicker();" />
                                        <span class="color-picker-swatch ng-scope color-picker-swatch-right input-group-addon" :style="'background-color: ' + backgroundColor"></span>
                                    </div>
                                    <div class="color-picker-panel color-picker-panel-bottom color-picker-panel-left color-picker-show-hue">
                                        <chrome-picker :value="backgroundColor" v-show="showBackgroundColor" @input="changeBackgroundColor" style="position: absolute;right: 55px;z-index: 99999;" />
                                    </div>

                                </div>

                            </div>
                        </div>

                    </div>
                    <div class="step-panel">
                        <div class="title">Behavior</div>
                        <div class="section row">
                            <div class="col-sm-6 sub-title">
                                Allow users to close the notification
                                <div class="mr-1 help-center-trigger"><i class="fa fa-question"></i></div>
                            </div>

                            <div class="col-sm-6 text-right">
                                <div class="ps-toggle">
                                    <span class="toggle-state no-text-select ">OFF</span>
                                    <label class="switch float-right no-text-select">
                                        <input type="checkbox" @click="settings.allow_close = !settings.allow_close" :checked="settings.allow_close">
                                        <span class="slider round"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="section row">
                            <div class="col-sm-6 sub-title">Clickable Notification
                                <div class="mr-1 help-center-trigger"><i class="fa fa-question"></i></div>
                            </div>
                            <div class="col-sm-6 text-right">
                                <div class="ps-toggle">
                                    <span class="toggle-state no-text-select">OFF</span>
                                    <label class="switch float-right no-text-select">
                                        <input type="checkbox" @click="settings.clickable_notification = !settings.clickable_notification" :checked="settings.clickable_notification">
                                        <span class="slider round"></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="section row sub-section ng-scope">
                            <div class="col-sm-6 sub-title">Notification Link</div>
                            <div class="col-sm-6 text-right">
                                <input type="text" data-lpignore="true" v-model="settings.notification_link" placeholder="Type URL here" class="form-control ps-input ng-pristine ng-empty ng-invalid ng-invalid-required w-100 ng-touched" required="required">
                            </div>
                            <div class="col-sm-12">
                                <div class="ps-tip">
                                    <div class="tip yellow mt-2">
                                        <i class="fa fa-lg fa-info-circle mr-2"></i>
                                        If http:// is omitted, the link will open relatively to the page displayed
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="section row sub-section ng-scope">
                            <div class="col-sm-6 sub-title">Open Link in New Tab</div>
                            <div class="col-sm-6 text-right">
                                <div class="ps-toggle">
                                    <span class="toggle-state no-text-select">OFF</span>
                                    <label class="switch float-right no-text-select">
                                        <input type="checkbox" @click="settings.open_in_new_tab = !settings.open_in_new_tab" :checked="settings.open_in_new_tab">
                                        <span class="slider round"></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="step-panel">
                        <div class="title">Display Rules</div>

                        <div class="section row">
                            <div class="col-sm-6 sub-title">
                                Show only once per session
                                <div class="mr-1 help-center-trigger"><i class="fa fa-question"></i></div>
                            </div>

                            <div class="col-sm-6 text-right">
                                <div class="ps-toggle">
                                    <span class="toggle-state no-text-select ">OFF</span>
                                    <label class="switch float-right no-text-select">
                                        <input type="checkbox" @click="settings.show_only_once_per_session = !settings.show_only_once_per_session" :checked="settings.show_only_once_per_session">
                                        <span class="slider round"></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="section row">
                            <div class="col-sm-6 sub-title">
                                Hide on mobile screens
                                <div class="mr-1 help-center-trigger"><i class="fa fa-question"></i></div>
                            </div>

                            <div class="col-sm-6 text-right">
                                <div class="ps-toggle">
                                    <span class="toggle-state no-text-select ">OFF</span>
                                    <label class="switch float-right no-text-select">
                                        <input type="checkbox" @click="settings.hide_on_mobile = !settings.hide_on_mobile" :checked="settings.hide_on_mobile">
                                        <span class="slider round"></span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="section row ng-scope">
                            <div class="col-sm-6 sub-title ng-binding">
                                Minimum events to display
                                <div class="mr-1 help-center-trigger"><i class="fa fa-question"></i></div>
                            </div>
                            <div class="col-sm-6 text-right">
                                <div class="ps-text-input">
                                    <div class="ps-input-frame">
                                        <form name="msgsndr13" data-lpignore="true" autocomplete="msgsndr13" class="msgsndr13">
                                            <input type="number" name="fieldName" id="displayHold" placeholder="8" class="form-control ps-input" v-model="settings.minimum_events_to_show">
                                        </form>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="section row">
                            <div class="col-sm-6 sub-title">Display notification for</div>
                            <div class="col-sm-6 text-right">
                                <div class="ps-text-input">
                                    <div class="ps-input-frame">
                                        <form name="msgsndr13" data-lpignore="true" autocomplete="msgsndr13" class="ng-pristine ng-valid ng-valid-required msgsndr13">
                                            <span class="right-text " ng-if="rightText">seconds</span>
                                            <input type="number" name="fieldName" id="displayHold" placeholder="8" class="form-control ps-input" v-model="settings.display_time">
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <div class="step-nav-buttons-container">
                    <div class="button-container no-text-select">
                        <button class="wizard-btn next" :disabled="nextDisabled">Next<i class="fa fa-arrow-right ml-2"></i></button>
                        <button class="wizard-btn back ng-scope" v-on:click="$emit('go-back', 4)">Back</button>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script lang='ts'>
import Vue from "vue";

import {
    NotificationType, NotificationTemplate
} from "@/models";

declare var $: any;

import { Chrome } from "vue-color";
import PsBubble from "./psbubble.vue";

export default Vue.extend({
    components: {
        "chrome-picker": Chrome,
        PsBubble
    },
    props: {
        notificationId: String
    },
    data() {
        return {
            notification: {} as NotificationTemplate,
            titleColor: "#FFFFFF",
            backgroundColor: "#FFFFF",
            showTitleColor: false,
            showBackgroundColor: false,
            settings: {}
        };
    },
    mounted() {
        const $selectpicker = $(this.$el).find(".selectpicker");
        if ($selectpicker) {
            $selectpicker.selectpicker("refresh");
        }
    },
    updated() {
        const $selectpicker = $(this.$el).find(".selectpicker");
        if ($selectpicker) {
            $selectpicker.selectpicker("refresh");
        }
    },
    async created() {
        await this.fetchData();
    },
    watch: {
        'notificationId': function (id) {
            this.fetchData();
        },
        'settings': function () {
            console.log("Settings changed");
            this.notification.settings = this.settings;
        }
    },
    computed: {
        nextDisabled(): boolean {
            return true;
        },
    },
    methods: {
        async fetchData() {
            this.notification = await NotificationTemplate.getById(this.notificationId);
            this.settings = this.settings;
            const $selectpicker = $(this.$el).find(".selectpicker");
            if ($selectpicker) {
                $selectpicker.selectpicker("refresh");
            }
        },
        changeTitleColor(newColor) {
            console.log("new color:", newColor);
            this.titleColor = this.settings.title_color = newColor.hex;
        },
        changeBackgroundColor(newColor) {
            console.log("new color:", newColor);
            this.backgroundColor = this.settings.background_color = newColor.hex;
        },
        showTitlePicker() {
            document.addEventListener('click', this.documentClick);
            this.showTitleColor = true;
        },
        hideTitlePicker() {
            document.removeEventListener('click', this.documentClick);
            this.showTitleColor = false;
        },
        documentClick(e) {
            var el = this.$refs.titlepicker, target = e.target;
            if (el !== target && !el.contains(target)) {
                this.hideTitlePicker();
            }
        },
        showBackgroundPicker() {
            document.addEventListener('click', this.backgroundDocumentClick);
            this.showBackgroundColor = true;
        },
        hideBackgroundPicker() {
            document.removeEventListener('click', this.backgroundDocumentClick);
            this.showBackgroundColor = false;
        },
        backgroundDocumentClick(e) {
            var el = this.$refs.backgroundpicker, target = e.target;
            if (el !== target && !el.contains(target)) {
                this.hideTitlePicker();
            }
        },
        setPosition(event: any) {
            const option = event.target.value;
            this.settings.position = option;
        },
        saveAndNext() {
            this.$emit('save-next', 5);
        },
    }
})
</script>
