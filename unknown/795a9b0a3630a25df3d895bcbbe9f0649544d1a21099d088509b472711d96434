<template>
  <div>
    <div class="form-group">
        <div class="form-input-dropdown dropdown">
            <div data-toggle="dropdown">
                <i class="icon icon-arrow-down-1"></i>
                <input type="text" class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800" data-lpignore="true" placeholder="Select conversion" :value="conversionType">
            </div>
            <div class="dropdown-menu">
              <moon-loader :loading="loading" color="#188bf6" size="30px" />
              <a class="dropdown-item trigger-type" href="javascript:void(0);" v-for="conversion in conversions" :key="conversion.conversion_name" @click.prevent="setConversionType(conversion.conversion_name)">
                  <p>{{conversion.conversion_name}}</p>
              </a>
            </div>
          <span v-if="hasConversions === false" style='color: red'>{{apiMessage || 'No conversions found'}}</span>
        </div>
    </div>
    <div class="form-group d-flex align-items-center" v-if="action.conversion_type === 'Other'">
      <input
        type="text"
        class="form-control"
        placeholder="Conversion Name"
        :value="action.conversion_name"
        name="conversionName"
        @input="setValue('conversion_name', $event.target.value)"
      />
      <span
        style="margin-left: 5px;"
        class="pointer"
        v-b-tooltip.hover
        title="Conversion source should be Import From Clicks and category should be Imported Lead or Lead"
      >
        <i class="fa fa-exclamation-triangle text-warning"></i>
      </span>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';

export default Vue.extend({
	props: ['action'],
	data() {
		return {
      currentLocationId: '',
      conversions: [],
      loading: false,
      hasConversions: null,
      apiMessage: null
		}
	},
	methods: {
    setValue(field: string, value: string) {
        Vue.set(this.action, field, value);
        this.$emit('update:action', this.action);
    },
    setConversionType(type: string) {
      if (type !== 'Other') {
        this.setValue('conversion_name', type);
      }
      this.setValue('conversion_type', type);
    }
  },
  computed: {
    conversionType() {
      if (this.action.conversion_type) return this.action.conversion_type;
      else if (this.action.conversion_name) {
        const conversion = lodash.find(this.conversions, {conversion_name: this.action.conversion_name});
        if(conversion) return conversion.conversion_name;
      }
      return '';
    }
  },
	async created() {
    try {
      this.currentLocationId = this.$router.currentRoute.params.location_id;
      this.loading = true;
      let resp = await this.pythonBackend.post(`/1.0/adwords/${this.currentLocationId}/list/conversions/clicks`);
      if (resp && resp.data) this.conversions = resp.data;
      this.hasConversions = this.conversions.length > 0;
      this.conversions.push({ 'conversion_name': 'Other' });
      this.loading = false;
    } catch (err) {
      console.error(err);
      this.hasConversions = false;
      const { response } = err
      this.apiMessage = response?.data?.message || err.message
    }
	}
})
</script>

