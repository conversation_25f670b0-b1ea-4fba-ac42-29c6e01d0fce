<template>
	<div class="form-group">
		<TagComponent v-model="tags"/>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
const TagComponent = () => import( '../customer/TagComponent.vue')
export default Vue.extend({
	props: ['action'],
	components: { TagComponent },
	data() {
		return {
			tags: [] as string[]
		}
	},
	async created() {
		if (this.action.tags) this.tags = this.action.tags;
	},
	watch: {
		tags(tags: string[]) {
			Vue.set(this.action, 'tags', tags);
			this.$emit('update:action', this.action);
		},
		action() {
			if (this.action.tags) this.tags = this.action.tags;
			else this.tags = [];
		}
	},
})
</script>

