<template>

    <div class="ps-content col-xs-9">
        <!--WIZARD STEPS-->
        <!-- ngSwitchWhen: type -->
        <!-- ngSwitchWhen: track -->
        <!-- ngSwitchWhen: display -->
        <!-- ngIf: shouldShowStep('display') -->
        <div class="step-display">
            <div class="ng-scope">
                <div ng-form="displayStepForm" autocomplete="false" class="ng-pristine ng-valid ng-valid-pattern ng-valid-minlength ng-valid-required">
                    <!--<div class="step-content pb-0">-->
                    <!--<div class="step-panel">-->
                    <!--<div class="title">How to Display</div>-->
                    <!--<div class="section">-->
                    <!--<div class="row">-->
                    <!--<div class="col-sm-8">-->
                    <!--<p class="description mb-0 mt-1">How would you like to display your conversions?</p>-->
                    <!--</div>-->
                    <!--<div class="col-sm-4">-->
                    <!--<div class="item float-right ">-->
                    <!--<ps-toggle left-title="AUTO" right-title="CODE"-->
                    <!--ng-model="notification.manuallyShowNotification"></ps-toggle>-->
                    <!--</div>-->
                    <!--</div>-->
                    <!--</div>-->
                    <!--</div>-->

                    <!--</div>-->
                    <!--</div>-->

                    <div class="step-content">
                        <div class="step-panel" ng-switch="notification.manuallyShowNotification">
                            <div class="title">Where to Display</div>
                            <!--AUTO-URLS-->
                            <!-- ngSwitchDefault: -->
                            <div class="section ng-scope" ng-switch-default="">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <p class="description mb-0 mt-2">Select URL match type
                                            <div class="mr-1 help-center-trigger">
                                                <i class="fa fa-question" />
                                            </div>
                                        </p>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="item float-right text-capitalize">
                                            <div class="ps-optionsbox">
                                                <div class="btn-group" role="group">
                                                    <button type="button" :class="{'btn btn-radio-option text-capitalize ': true, 'option-selected':notification.displayType == 'simple'}" @click="setDisplayType('simple')">
                                                        <i class="fa fa-check" v-if="notification.displayType == 'simple'"/> simple
                                                    </button>
                                                    <button type="button" :class="{'btn btn-radio-option text-capitalize ': true, 'option-selected':notification.displayType == 'contains'}" @click="setDisplayType('contains')">
                                                       <i class="fa fa-check" v-if="notification.displayType == 'contains'"/> contains
                                                    </button>
                                                    <button type="button" :class="{'btn btn-radio-option text-capitalize ': true, 'option-selected':notification.displayType == 'regex'}" @click="setDisplayType('regex')">
                                                        <i class="fa fa-check" v-if="notification.displayType == 'regex'"/> regex
                                                    </button>
                                                    <button type="button" :class="{'btn btn-radio-option text-capitalize ': true, 'option-selected':notification.displayType == 'all_pages'}" @click="setDisplayType('all_pages')">
                                                        <i class="fa fa-check" v-if="notification.displayType == 'all_pages'"/> All Pages
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="section" v-if="showCapture">
                                <p class="description" v-if="showCapture && notification.displayType == 'simple'">
                                    <span class="ng-scope">Where would you like us to display this activity?<br>Add one or more page URLs or copy them directly from your web browser.</span><!-- end ngSwitchWhen: -->
                                </p>

                                <div class="ps-tip" v-if="showCapture && notification.displayType == 'simple'">
                                    <div class="tip yellow mb-4">
                                        <i class="fa fa-info-circle"></i> /> We treat http://, https://, www and non-www as separate versions of the URL and page.
                                    </div>
                                </div>
                                <div class="ps-input-add" v-if="showCapture && notification.displayType == 'simple'">
                                    <div class="ps-input-frame">
                                        <form name="msgsndr12" data-lpignore="true" autocomplete="msgsndr12" :class="{'ng-pristine  -pattern -minlength -required msgsndr12':true, 'ng-invalid ng-invalid-pattern':errors.has('watchURL')}" @submit="addURL($event);">
                                            <input type="text" data-lpignore="true" :class="{'form-control border-green -pattern -minlength -required':true, 'ng-invalid ng-invalid-pattern':errors.has('watchURL')}" name="watchURL" ref="matchURL" placeholder="Page URL" v-validate="{ required: true, regex: /(^https:\/\/|^http:\/\/)(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,256}\b([-a-zA-Z0-9@:%_\+.~#?&amp;\/\/=]*)/ }" v-model="newURL">
                                            <button class="btn" type="submit" @click="addURL($event);">
                                                <i class="fa fa-plus" />
                                            </button>
                                            <div v-show="errors.has('watchURL')" class="text-danger pt-1 ">You must add at least one URL. URL is invalid. Make sure to add http:// or https://</div>
                                        </form>
                                    </div>
                                </div>
                                <div v-if="showCapture && notification.displayType == 'simple'">
                                    <span v-for="(url, index) in notificationURLs" :key="url" class="urls-item text-truncate ng-scope">
                                        <span class="remove-button" @click="removeURL(index, url)">×</span>
                                        <a class="text text-truncate ng-binding" :href="url" target="_blank">{{url}}</a>
                                    </span>
                                </div>

                                <!-- Contains & RegEx Section -->
                                <p class="description" v-if="showCapture && (notification.displayType == 'contains' || notification.displayType == 'regex')">
                                    <span>Display on all page URLs that contain the following expression:

                                    </span>
                                </p>
                                <div class="ps-input-add" v-if="showCapture && (notification.displayType == 'contains' || notification.displayType == 'regex')">
                                    <div class="ps-input-frame">
                                        <form name="msgsndr12" data-lpignore="true" autocomplete="msgsndr12" class="ng-pristine ng-valid ng-valid-pattern ng-valid-minlength ng-valid-required msgsndr12">
                                            <input type="text" data-lpignore="true" class="form-control border-green ng-pristine ng-valid ng-empty ng-valid-pattern ng-valid-minlength ng-valid-required ng-touched" name="fieldName" id="add-url" placeholder="Expression" ng-required="isRequired" ng-minlength="1" data-ng-model="model" validate-email="">
                                            <div v-show="errors.has('fieldName')" class="text-danger pt-1 ">{{ errors.first('fieldName') }}</div>
                                        </form>
                                    </div>
                                </div>

                                <div class="ps-tip" v-if="notification.displayType == 'all_pages'">
                                    <div class="tip green mt-2 mb-5" style="">
                                        <i class="fa fa-lg fa-info-circle mr-2"></i>
                                        We will automatically track your website visitors in real time.
                                    </div>
                                </div>
                            </div>
                            <!--CODE-->
                            <!-- ngSwitchWhen: true -->
                        </div>
                        <!-- ngIf: notification.urlTypes.display == 'all' -->
                    </div>
                    <div class="step-nav-buttons-container">
                        <div class="button-container no-text-select">
                            <button class="wizard-btn next" :disabled="nextDisabled">Next<i class="fa fa-arrow-right ml-2"></i></button>
                            <button class="wizard-btn back " v-on:click="$emit('go-back', 2)">Back
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</template>

<script lang='ts'>
import Vue from "vue";

import {
    NotificationTemplate
} from "@/models";

declare var $: any;

export default Vue.extend({
    props: {
        notificationId: String
    },
    computed: {
           nextDisabled(): boolean {
            return true;
        },
    },  
    data() {
        return {
            notification: {} as NotificationTemplate,
            showCapture: false,
            newURL: "",
            notificationURLs: [] as string[],
            nextDisabled: true
        };
    },
    async created() {
        this.fetchData();
    },
    watch: {
        'notificationId': function (id) {
            this.fetchData();
        },
    },
    methods: {
        async fetchData() {
            this.notification = await NotificationTemplate.getById(this.notificationId);
            if (this.notification.displayType == "simple") {
                this.showCapture = true;
            }
        },
        setDisplayType(type: string) {
            this.notification.displayType = type;
        },
        saveAndNext() {
            this.$emit('save-next', 4);
        },
    }
})
</script>
