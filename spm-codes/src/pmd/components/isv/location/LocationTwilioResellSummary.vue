<template>
  <div class="container-fluid">
    <div style="display: flex; align-items: center; justify-content: space-between;">
      <h2 class="summary-card__heading">Summary</h2>
      <div class="agency_only text-center" style="color: #d19850;">( <i class="fa fa-info-circle"></i> This is visible only to Agency Admins)</div>
    </div>
      <!-- <div class="summary-card__info" v-if="user.type === 'agency'">
        <span>Show this section to Clients</span>
        <div class="summary-card__info-toggle">
          <UIToggle
            class="tgl tgl-light"
            id="show_location_twilio_summary"
            v-model="show_location_twilio_summary"
          />
        </div>
        <div class="agency_only">( <i class="fa fa-info-circle"></i> This setting is visible only to Agency Admins)</div>
      </div> -->
    <div class="summary-cards-wrap">
      <LocationTwilioResellSummaryCard v-for="item in summary" :key="item.title" :summary="item" :showOtherCharges="showOtherCharges" @toggleOtherCharges="toggleOtherCharges"/>
    </div>
  </div>
</template>

<script>
import LocationTwilioResellSummaryCard from "./LocationTwilioResellSummaryCard.vue";
import { User } from '@/models'
export default {
    props: ['location'],
    components: {
      LocationTwilioResellSummaryCard,
    },
    data() {
      return {
        local_show_location_twilio_summary: false,
        show_twilio_summary_updated: false,
        showOtherCharges: false,
        summary: [],
      }
    },
    created() {
      this.fetchUsageSummary();
    },
    computed: {
      user() {
        const user = this.$store.state.user.user
        return user ? new User(user) : undefined
      },
      show_location_twilio_summary:{
        get() {
          return this.show_twilio_summary_updated ?
          this.local_show_location_twilio_summary :
          (this.location && this.location.show_twilio_summary === true)
        },
        set: async function(value) {
          try {
            const { data } = await this.saasService.get(
              `/isv-mode/show-twilio-summary/${this.location.id}?show=${value}`
            )
            this.local_show_location_twilio_summary = value;
            this.show_twilio_summary_updated = true;
          } catch (err) {
            console.error('Failed to set.')
          }
        }
      }
    },
    methods: {
      async fetchUsageSummary() {
        try {
          const { data } = await this.saasService.get(
            `call-sms-charge/usage-summary/${this.location.id}`
          )
          console.log(data);
          this.summary = data;
        } catch (err) {
          console.error('Failed to set.')
        }
      },
      toggleOtherCharges(){
        this.showOtherCharges = !this.showOtherCharges;
      }
    }
}
</script>


<style lang='scss'>
.summary-card__heading{
  font-size: 18px;
  font-weight: 500;
  color: #4F4F4F;
  margin-top: 20px;
}
.summary-cards-wrap{
  padding: 20px;
  display: flex;
  justify-content: space-evenly;
  align-items: flex-start;
}

.summary-card__info{
  margin-left: 24px;
  padding: 16px;
  display: flex;
  align-items: center;
  // justify-content: center;

  .agency_only{
    font-size: 12px;
    font-weight: 400;
  }
  .summary-card__info-toggle{
    margin: 0px 12px;
  }
}
</style>
