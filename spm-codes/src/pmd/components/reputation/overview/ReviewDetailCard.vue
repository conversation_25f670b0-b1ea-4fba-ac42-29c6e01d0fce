<template>
  <div id="avg-rating" class="card avg-rating">
    <div class="card-header card-header--compact space-between">
      <h2>Avg. Rating</h2>
      <div>
        <span>{{ filter }}</span>
        <select class="selectpicker more-select" v-model="filter">
          <!-- <option>Today</option> -->
          <option>This Week</option>
          <option>Last Week</option>
          <option>This Month</option>
          <option>Last 6 Months</option>
          <option>This Year</option>
        </select>
      </div>
    </div>
    <div class="card-body">
      <div class="summary">
        <svg
          width="32"
          height="32"
          viewBox="0 0 32 32"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M28 30H22C21.4697 29.9995 20.9613 29.7886 20.5864 29.4136C20.2114 29.0387 20.0005 28.5303 20 28V22C20.0005 21.4697 20.2114 20.9613 20.5864 20.5864C20.9613 20.2114 21.4697 20.0005 22 20H28C28.5303 20.0005 29.0387 20.2114 29.4136 20.5864C29.7886 20.9613 29.9995 21.4697 30 22V28C29.9995 28.5303 29.7886 29.0387 29.4136 29.4136C29.0387 29.7886 28.5303 29.9995 28 30ZM22 22H21.999L22 28H28V22H22Z"
            fill="#FFBC00"
          />
          <path
            d="M18 26H12C11.2046 25.9992 10.442 25.6829 9.87956 25.1204C9.31712 24.558 9.00079 23.7954 9 23V19H11V23C11.0003 23.2651 11.1057 23.5193 11.2932 23.7068C11.4807 23.8943 11.7349 23.9997 12 24H18V26Z"
            fill="#FFBC00"
          />
          <path
            d="M26 18H24V15C23.9997 14.7349 23.8943 14.4807 23.7068 14.2932C23.5193 14.1057 23.2651 14.0003 23 14H18V12H23C23.7954 12.0008 24.558 12.3171 25.1204 12.8796C25.6829 13.442 25.9992 14.2046 26 15V18Z"
            fill="#FFBC00"
          />
          <path
            d="M15.0002 18.0001C14.8258 18.0002 14.6545 17.9547 14.5032 17.8681L10.0002 15.2881L5.49719 17.8681C5.3283 17.9648 5.13487 18.0101 4.9406 17.9985C4.74633 17.9869 4.55967 17.9188 4.40351 17.8027C4.24734 17.6866 4.12846 17.5274 4.06144 17.3447C3.99442 17.1619 3.98217 16.9636 4.02619 16.7741L5.08719 12.2021L2.26919 9.18207C2.14602 9.04992 2.06095 8.88687 2.02302 8.71024C1.98509 8.53362 1.99572 8.35001 2.05377 8.17894C2.11182 8.00787 2.21512 7.85572 2.35271 7.73865C2.4903 7.62159 2.65703 7.54398 2.83519 7.51407L7.03519 6.81207L9.10119 2.56207C9.18246 2.39524 9.30854 2.25432 9.46534 2.15506C9.62214 2.05581 9.80346 2.00214 9.98902 2.00006C10.1746 1.99799 10.3571 2.04759 10.516 2.14331C10.675 2.23903 10.8042 2.3771 10.8892 2.54207L13.0932 6.81207L17.1702 7.51407C17.3479 7.54471 17.5141 7.62285 17.651 7.74021C17.788 7.85758 17.8907 8.0098 17.9482 8.18076C18.0057 8.35171 18.0159 8.53504 17.9777 8.71131C17.9395 8.88759 17.8543 9.05025 17.7312 9.18207L14.9132 12.2021L15.9732 16.7741C16.0072 16.9206 16.0077 17.0729 15.9746 17.2197C15.9416 17.3665 15.8759 17.5039 15.7824 17.6217C15.6888 17.7396 15.5699 17.8348 15.4345 17.9003C15.2991 17.9658 15.1506 17.9999 15.0002 18.0001ZM10.0002 13.0001C10.0002 13.0001 10.3432 13.1801 10.4972 13.2691L13.5072 14.9931L12.7232 11.6161L15.0052 9.17107L11.7722 8.61407L10.0272 5.23207L8.38319 8.61407L5.00219 9.17907L7.27619 11.6161L6.49219 14.9931L9.50219 13.2691C9.65819 13.1801 10.0002 13.0001 10.0002 13.0001Z"
            fill="#FFBC00"
          />
        </svg>

        <h3>
          {{ averageRating }}
        </h3>

        <div class="rating">
          <i
            v-for="(star, index) in stars"
            :key="index"
            :class="getClass(star)"
            class="icon"
          ></i>
        </div>
        <div class="rating_percentage">
          <div>
            <h4 v-if="ratingsChange >= 0">
              <i class="icon icon-arrow-up-2 --green"></i>
              {{ ratingsChange }}%
            </h4>
            <h4 v-else>
              <i class="icon icon-arrow-down-2 --red"></i>
              {{ ratingsChange }}%
            </h4>

            <!--    <p>Your rating has improved.</p> -->
          </div>
          <!--   <div v-if="ratingsChange == 0">
            <h4><i class="icon icon-minus"></i> 0.0%</h4>
            <p>No movement regarding your rating.</p>
          </div>
          <div v-if="ratingsChange < 0">
            <h4>
              <i class="icon icon-arrow-down-2 --red"></i>
              {{ ratingsChange }}%
            </h4>
            <p>Your rating is falling.</p>
          </div> -->
        </div>
      </div>

      <div
        class="ratings"
        v-if="
          currentReviewsAggregate.breakdown['1'] > 0 ||
          currentReviewsAggregate.breakdown['2'] > 0 ||
          currentReviewsAggregate.breakdown['3'] > 0 ||
          currentReviewsAggregate.breakdown['4'] > 0 ||
          currentReviewsAggregate.breakdown['5'] > 0
        "
      >
        <div class="rating_stats">
          <div class="rating_stats-item --star5">
            <h4>5</h4>
            <div class="progress">
              <div
                class="progress-bar"
                role="progressbar"
                :style="{ width: getBarWidth(5) }"
              ></div>
              <span v-if="currentReviewsAggregate.breakdown['5'] > 0">{{
                currentReviewsAggregate.breakdown['5']
              }}</span>
            </div>
          </div>
        </div>
        <div class="rating_stats">
          <div class="rating_stats-item --star4">
            <h4>4</h4>
            <div class="progress">
              <div
                class="progress-bar"
                role="progressbar"
                :style="{ width: getBarWidth(4) }"
              ></div>
              <span v-if="currentReviewsAggregate.breakdown['4'] > 0">{{
                currentReviewsAggregate.breakdown['4']
              }}</span>
            </div>
          </div>
        </div>
        <div class="rating_stats">
          <div class="rating_stats-item --star3">
            <h4>3</h4>
            <div class="progress">
              <div
                class="progress-bar"
                role="progressbar"
                :style="{ width: getBarWidth(3) }"
              ></div>
              <span v-if="currentReviewsAggregate.breakdown['3'] > 0">{{
                currentReviewsAggregate.breakdown['3']
              }}</span>
            </div>
          </div>
        </div>
        <div class="rating_stats">
          <div class="rating_stats-item --star2">
            <h4>2</h4>
            <div class="progress">
              <div
                class="progress-bar"
                role="progressbar"
                :style="{ width: getBarWidth(2) }"
              ></div>
              <span v-if="currentReviewsAggregate.breakdown['2'] > 0">{{
                currentReviewsAggregate.breakdown['2']
              }}</span>
            </div>
          </div>
        </div>
        <div class="rating_stats">
          <div class="rating_stats-item --star1">
            <h4>1</h4>
            <div class="progress">
              <div
                class="progress-bar"
                role="progressbar"
                :style="{ width: getBarWidth(1) }"
              ></div>
              <span v-if="currentReviewsAggregate.breakdown['1'] > 0">{{
                currentReviewsAggregate.breakdown['1']
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { ReviewAggregateData } from '@/store/state_models'

declare var $: any

export default Vue.extend({
  data() {
    return {
      filter: 'Last 6 Months',
      currentLocationId: '',
    }
  },
  computed: {
    currentReviewsAggregate(): ReviewAggregateData {
      return this.$store.getters['reviewAggregate/current'](this.filter)
    },
    previousReviewsAggregate(): ReviewAggregateData {
      return this.$store.getters['reviewAggregate/previous'](this.filter)
    },
    averageRating(): number {
      if (
        this.currentReviewsAggregate &&
        isNaN(this.currentReviewsAggregate.totalRating)
      ) {
        const totalReviews =
          (this.currentReviewsAggregate.breakdown[1] || 0) +
          (this.currentReviewsAggregate.breakdown[2] || 0) +
          (this.currentReviewsAggregate.breakdown[3] || 0) +
          (this.currentReviewsAggregate.breakdown[4] || 0) +
          (this.currentReviewsAggregate.breakdown[5] || 0)

        const totalRating =
          (this.currentReviewsAggregate.breakdown[1] || 0) * 1 +
          (this.currentReviewsAggregate.breakdown[2] || 0) * 2 +
          (this.currentReviewsAggregate.breakdown[3] || 0) * 3 +
          (this.currentReviewsAggregate.breakdown[4] || 0) * 4 +
          (this.currentReviewsAggregate.breakdown[5] || 0) * 5

        return Math.round((totalRating / totalReviews) * 10) / 10
      } else if (
        !this.currentReviewsAggregate ||
        !this.currentReviewsAggregate.totalRating ||
        !this.currentReviewsAggregate.totalReviews
      ) {
        return 0
      } else {
        return (
          Math.round(
            (this.currentReviewsAggregate.totalRating /
              this.currentReviewsAggregate.totalReviews) *
              10
          ) / 10
        )
      }
    },
    ratingsChange(): string {
      const currentRating =
        Math.round(
          (this.currentReviewsAggregate.totalRating /
            (this.currentReviewsAggregate.totalReviews || 1)) *
            10
        ) / 10
      const previousRating =
        Math.round(
          (this.previousReviewsAggregate.totalRating /
            (this.previousReviewsAggregate.totalReviews || 1)) *
            10
        ) / 10
      const baseline = previousRating ? previousRating : 1
      return (((currentRating - previousRating || 0) / baseline) * 100).toFixed(
        2
      )
    },
    stars(): number[] {
      const stars = []
      const rating = this.averageRating
      for (let i = 0; i < 5; i++) {
        if (rating >= i + 1 || rating > i + 0.75) {
          stars.push(1)
        } else if (rating > i + 0.25) {
          stars.push(0.5)
        } else {
          stars.push(0)
        }
      }
      return stars
    },
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.filter = 'Last 6 Months'
    },
    filter: function () {
      if (this.filter === 'This Week') {
        this.$store.dispatch(
          'reviewAggregate/fetchThisWeek',
          this.currentLocationId
        )
      } else if (this.filter === 'Last Week') {
        this.$store.dispatch(
          'reviewAggregate/fetchLastWeek',
          this.currentLocationId
        )
      } else if (this.filter === 'This Month') {
        this.$store.dispatch(
          'reviewAggregate/fetchThisMonth',
          this.currentLocationId
        )
      } else if (this.filter === 'Last 6 Months') {
        this.$store.dispatch(
          'reviewAggregate/fetchLast6Months',
          this.currentLocationId
        )
      } else if (this.filter === 'This Year') {
        this.$store.dispatch(
          'reviewAggregate/fetchThisYear',
          this.currentLocationId
        )
      }
    },
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
  },
  methods: {
    getClass(star: number) {
      if (star === 1) {
        return 'icon-star-filled'
      } else if (star === 0.5) {
        return 'icon icon-star-half'
      } else {
        return 'icon icon-star'
      }
    },
    getBarWidth(star: string): string {
      const total = lodash.sumBy(
        lodash.values(this.currentReviewsAggregate.breakdown)
      )
      const baseline = total ? total : 1
      const aggregate: any = this.currentReviewsAggregate.breakdown
      let barWidth = (aggregate[star] / baseline) * 90;
      return  (barWidth == 0 ? .5 : barWidth) + '%'
    },
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
})
</script>
<style lang="scss" scoped>
#avg-rating {
  .space-between {
    display: flex;
    justify-content: space-between;
  }

  .card-header--compact {
    padding: 12px 50px 10px 30px;
  }

  .card-header {
    span {
      color: #afb8bc;
    }
    .more-select {
      top: 53%;
      transform: translateY(-53%);
    }
  }

  .card-body {
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    .summary {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-right: 40px;

      h3 {
        font-size: 2rem;
        font-weight: 500;
        margin: 10px;
        .rating {
          display: inline-block;
          position: relative;
          top: 5px;
          .icon {
            vertical-align: top;
          }
        }
      }

      .rating_percentage {
        margin-bottom: 40px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
      }
      .rating_percentage h4 {
        font-size: 1rem;
        color: #afb8bc;
        font-weight: 500;
        margin: 10px 0;
      }
      .rating_percentage p {
        max-width: 200px;
      }
    }
    .ratings {
      width: 60%;

      .rating_stats {
        .rating_stats-item {
          margin-bottom: 8px;
          display: flex;
          h4 {
            font-size: 0.875rem;
            margin-bottom: 0;
            /* margin-right: 20px; */
            width: 25px;
            padding-bottom: 2px;
            .icon {
              color: #afb8bc;
              font-size: 0.625rem;
            }
          }
          .progress {
            width: 100%;
            border-radius: 0;
            height: 15px;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            background-color: transparent;
            .progress-bar {
              text-align: right;
              height: 15px;
              border-radius: 0px;
            }
            span {
              position: relative;
              right: -10px;
              color: #2a3135;
            }
          }
        }

        .rating_stats-item.--star5 .progress .progress-bar {
          background-color: #79c9a1;
        }
        .rating_stats-item.--star4 .progress .progress-bar {
          background-color: #aed888;
        }
        .rating_stats-item.--star3 .progress .progress-bar {
          background-color: #ffd935;
        }
        .rating_stats-item.--star2 .progress .progress-bar {
          background-color: #ffb235;
        }
        .rating_stats-item.--star1 .progress .progress-bar {
          background-color: #ff8c5a;
        }
      }
    }
  }
}
</style>
