<template>
  <div v-bind:class="{ hl_wrapper: !this.renderedUnderSettings, 'hl_reputation-padding':  getSideBarVersion == 'v2'}">
    <div
      class="hl_reputation"
      v-bind:class="{ 'padding-top': !this.renderedUnderSettings }"
    >
      <div
        class="hl_reputation--header"
        v-bind:class="{
          'hl_reputation--header--under-settings': this.renderedUnderSettings,
        }"
      >
        <h3 v-if="!isV2SideBarEnable" >Reputation</h3>
        <div class="hl_util--sapcebetween">
          <ul :class="{'invisible': isV2SideBarEnable}" class="hl_reputation--links">
            <router-link
              v-if="!this.renderedUnderSettings && user && ( user.permissions.reviews_enabled !== false)"
              :to="{
                name: this.renderedUnderSettings
                  ? 'settings_reputation_overview'
                  : 'reputation_overview',
              }"
              tag="li"
              active-class="active"
              exact
              id="reputation_overview"
            >
              <a href="javascript:void(0);">Overview</a>
            </router-link>

            <router-link
              v-if="!this.renderedUnderSettings && hasPermission('reviews')"
              :to="{ name: 'reputation_review_requests' }"
              tag="li"
              active-class="active"
              exact
              id="reputation_requests"
            >
              <a href="javascript:void(0);">Requests</a>
            </router-link>
            <router-link
              v-if="!this.renderedUnderSettings && hasPermission('reviews')"
              :to="{ name: 'reputation_reviews' }"
              tag="li"
              active-class="active"
              exact
               id="reputation_reviews"
            >
              <a href="javascript:void(0);">Reviews</a>
            </router-link>
            <router-link
              v-show="false"
              :to="{
                name: this.renderedUnderSettings
                  ? 'settings_reputation_settings'
                  : 'reputation_settings',
              }"
              tag="li"
              active-class="active"
              exact
            >
              <a href="javascript:void(0);">Settings</a>
            </router-link>
          </ul>
          <UIButton
            use="primary"
            @click.prevent="showSendReviewModal = true"
            v-show="hasPermission('reviews')"
            id='send_review_request_btn'
          >
            Send Review Request
          </UIButton>
        </div>
      </div>
      <SendReviewModal
        :showModal.sync="showSendReviewModal"
        @hidden="showSendReviewModal = false"
      />
      <div  v-bind:class="{  'hl_reputation--body':true, 'hl_reputation--scroll-self': !this.renderedUnderSettings,  }" >
        <router-view />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// @ts-nocheck
import Vue from 'vue'
import { Location, User } from '@/models'
import { UserState } from '../../../store/state_models'
import { mapState } from 'vuex'
const SendReviewModal = () =>
  import(
    /* webpackChunkName: "send-review-m" */ './../../components/SendReviewModal.vue'
  )

export default Vue.extend({
  data() {
    return {
      showSendReviewModal: false,
      currentLocationId: '',
      location: undefined as undefined | Location,
    }
  },
  components: {
    SendReviewModal,
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.$store.dispatch('reviewAggregate/reset', this.currentLocationId)
      this.$store.dispatch('reviewRequestAggregate/reset', this.currentLocationId)
      this.fetchData()
    },
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id;
    this.$store.dispatch('reviewAggregate/reset', this.currentLocationId)
    this.$store.dispatch('reviewRequestAggregate/reset', this.currentLocationId)
    this.fetchData()
  },

  methods: {
    hasPermission(option: string): boolean {
      let show = false
      if (!this.user) return show
      switch (option) {
        case 'reviews': {
          show =
            this.userHasPermission('reviews_enabled') &&
            this.locationHasPermission('reviews_enabled')
          break
        }
         case 'online_listings': {
          show = this.userHasPermission('online_listings_enabled')
          break
        }
      }
      return show
    },
    locationHasPermission(key: string): boolean {
      if (!this.location) return false
      if (!this.location.permissions) {
        return true
      }

      const permissions = this.location.permissions
      return permissions[key] === false ? false : true
    },
    userHasPermission(key: string): boolean {
      const globalPermission = this.user.permissions
      if (globalPermission[key] === false) {
        return false
      }
      return true
    },
    async fetchData() {
      this.location = new Location(
        lodash.cloneDeep(
          await this.$store.dispatch(
            'locations/getById',
            this.currentLocationId
          )
        )
      )
      this.$store.dispatch(
        'reviewRequestAggregate/fetchLast6Months',
        this.currentLocationId
      )
      this.$store.dispatch(
        'reviewRequestAggregate/lastSixMonthMonthly',
        this.currentLocationId
      )
      this.$store.dispatch(
        'reviewRequestAggregate/fetchThisMonthWeekly',
        this.currentLocationId
      )

      this.$store.dispatch(
        'reviewAggregate/fetchLast6Months',
        this.currentLocationId
      )
      this.$store.dispatch(
        'reviewAggregate/lastSixMonthMonthly',
        this.currentLocationId
      )
      this.$store.dispatch(
        'reviewAggregate/thisMonthWeekly',
        this.currentLocationId
      )
    },
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),

    renderedUnderSettings: function () {
      if (this.$route && this.$route.name.startsWith('settings_')) {
        return true
      }
      return false
    },
    company() {
      return this.$store.state.company?.company
    },
    getSideBarVersion(): string {
      return this.$store.getters['sidebarv2/getVersion'] 
    },
    isV2SideBarEnable() {
      return this.getSideBarVersion == 'v2'
    }
  },
  mounted() {},
  updated() {},
})
</script>
<style lang="scss" scoped>
.hl_util--sapcebetween {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.padding-top {
  padding-top: 15px;
}
.hl_reputation--scroll-self{
    overflow: auto;
    padding-top: 5px;
    height: calc(100vh - 210px);
    margin-top: 5px;
}
.hl_reputation {
  .hl_reputation--header {
    background: #fff;
    padding: 10px 30px;
    display: grid;
    grid-template-rows: 1fr;
    margin: 0 15px;

    .hl_reputation--links {
      list-style: none;
      padding: 0;
      margin: 0;
      li {
        display: inline-block;
        margin-right: 16px;
        padding: 3px 12px;
        border-radius: 1px;
      }
      li:last-child {
        margin-right: 0;
      }
    }

/*     .hl_calltoaction-btn {
      background: #27ae60;
      border-radius: 3px;
      padding: 7px 20px;
      border: unset;
      color: #fff;
    } */
  }
  .hl_reputation--header--under-settings {
    display: flex;
    justify-content: space-between;
  }

  .hl_reputation--body {
    padding: 10px 15px;
  }
  .active {
    color: #3182ce;
    background: #ebf8ff;
    border-radius: 1px;
  }
}
</style>
