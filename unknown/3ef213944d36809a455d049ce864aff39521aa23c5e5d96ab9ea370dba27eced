<template>
  <div>
    <modal v-if="show" @close="$emit('close')" maxWidth="880" :showCloseIcon="true">
      <div>
        <div class="upgrade-modal__header">Add New Subscription</div>
        <div class="upgrade-modal__shimmer" v-if="activePlanLevel === 0">
          <div class="upgrade-modal__alert--danger" v-if="errorMsg">{{errorMsg}}
              <span class="open-support-widget" @click="contactSupport">contact support</span> !!
          </div>
          <upgrade-modal-shimmer v-else/>
        </div>
        <SaasPricingTable
          v-else
          :current-level="activePlanLevel"
          @upgraded="handleUpgrade"
          @skip="$emit('skip')"
          :company="company"
          :saasPlans="saasPlans"
          :saasProductDetails="saasProductDetails"
          :locationId="locationId"
          :plansStatus="plansStatus"
          :skippable="skippable"
        />
      </div>
    </modal>
  </div>
</template>

<script>
import Modal from '@/pmd/components/common/Modal.vue'
import UpgradeModalShimmer from '@/pmd/components/common/shimmers/UpgradeModalShimmer.vue'

import { trackGaEvent } from "@/util/helper";

import SaasPricingTable from "./SaasPricingTable";

export default {
  props: ['show', 'source', 'type', 'locationId', 'skippable'],
  components: {
    Modal,
    SaasPricingTable,
    UpgradeModalShimmer
  },
  computed: {
    company() {
      return this.$store.state.company.company
    },
  },
  data() {
    return {
      activePlanLevel: 0,
      saasPlans: [],
      saasProductDetails: {},
      plansStatus: 'fetching', // 'fetching', 'exist', 'not-exist'
    }
  },
  created() {
    this.getSaasPlans()
    // trackGaEvent('BillingUpgradeInitiate', this.company.id, 'Upgrade Modal: Step 1', 1);
  },
  methods: {
    async getSaasPlans() {
      this.errorMsg = ''
      try {
        this.plansStatus = 'fetching'
        const { data } = await this.saasService.get(
          `/subscription-plans/${this.company.id}/`
        )
        this.saasProductDetails = data.saasProductDetails;
        this.saasPlans = data.plans;
        this.activePlanLevel = -1;

        if (data.default === true) {
          this.plansStatus = 'not-exist'
        } else {
          this.plansStatus = 'exist'
        }
      } catch (e) {
        this.plansStatus = 'not_exist'
        // if(this.type === 'reactivate'){
        //   this.activePlanLevel = -1;
        // } else{
        //   this.notUpgradable();
        // }
      }
    },
    contactSupport(){
        this.$emit('close');
        window.fcWidget.open();
    },
    handleUpgrade(e) {
      this.$emit('success',
        {...e, locationId: this.locationId }
      );
    },
  }

}
</script>

<style scoped>
.upgrade-modal__shimmer{
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 600px;
}
.upgrade-modal__alert--danger{
    border-left: 2px solid #e93d3d;
    padding: 6px 12px;
    margin: 16px 0px 8px;
    background-color: #fff3f3;
}
.open-support-widget{
    cursor: pointer;
    color: #188bf6;
    font-weight: 600;
}

.upgrade-modal__header{
  position: relative;
  text-align: center;
  padding: 32px;
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  color: #373737;
  border-bottom: 1px solid #e0e0e0;
}
.upgrade-modal__header .plan-name--upgrade-from{
  color: #828282;
}
.upgrade-modal__header .plan-name--upgrade-to{
  color: #2F80ED;
}
.upgrade-modal__back-btn{
  position: absolute;
  top: 24px;
  left: 24px;

  height: 32px;
  width: 32px;

  background-color: #F2F2F2;
  border-radius: 50%;
  cursor: pointer;

  font-size: 16px;
  line-height: 16px;
  padding: 8px;
  transition: all 0.3s ease-in-out;
}
.upgrade-modal__back-btn:hover{
  background-color: #d2d2d2;
}
.upgrade-modal__body{
  height: 460px;
}
.upgrade-modal__body.--expanded{
  height: unset;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
}
</style>
