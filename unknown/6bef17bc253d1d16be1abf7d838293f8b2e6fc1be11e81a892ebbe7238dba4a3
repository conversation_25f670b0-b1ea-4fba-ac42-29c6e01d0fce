<template>
  <div>
    <div class="power-dialer-main">
      <div class="power-dialer-header"></div>
      <div class="power-dialer-body">
        <div class="callDial">
          <p class="phoneText">
            <PhoneNumber
              type="display"
              placeholder="Phone Number1"
              v-model="callTabs[callTabs.currentTab].numberToDial"
              :currentLocationId="currentLocationId"
            />
          </p>
          <div class="name-box">
            <span class="name" v-if="callTabs.currentTab === 'first' && !isUserNotAssigned">
              <i class="icon icon-share-2" @click="loadDetailPage"></i>
            </span>
            <h5 :style="{color:getContactColor}">{{callContactName}}</h5>
            <h6 v-if="callTabs.showOneTab && callTabs.second.callAnswered">+1 Other(s)</h6>
          </div>
          <div v-if="!callTabs[callTabs.currentTab].callDialing" class="recording-box">
            <p class="recording">
              <i class="fa fa-square"></i>
            </p>
            <p>00:{{timerFullTime}}</p>
          </div>
          <p v-else class="text-center dialling-text">
            Dialling
            <img src="./dialling.gif" />
          </p>
          <div style="top: 20px" class="dail-box">
            <button
              href="#"
              :disabled="!callAnswered || this.callTabs.callOnHold"
              role="button"
              class="callLink"
              @click="muteCall"
              :class="{disabledBox: !callAnswered || this.callTabs.callOnHold}"
            >
              <span class="callIconBox space-y-4" :class="{active: isMuted}">
                <i class="flex justify-center">
                  <svg
                    width="17"
                    height="19"
                    viewBox="0 0 17 19"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M8.07418 0.000251088C6.22605 0.000251088 4.71285 1.52764 4.71285 3.3931V5.14781L1.15453 1.55614C1.09188 1.49114 1.01696 1.43947 0.934197 1.40419C0.85143 1.3689 0.762497 1.35072 0.672652 1.35072C0.538915 1.35075 0.408226 1.39105 0.297295 1.46644C0.186363 1.54184 0.100229 1.64892 0.0499049 1.77399C-0.000419471 1.89906 -0.0126477 2.03643 0.0147838 2.16855C0.0422154 2.30068 0.10806 2.42154 0.2039 2.51569L4.7128 7.06685V9.50013C4.7128 11.3656 6.22601 12.893 8.07414 12.893C8.75615 12.893 9.39131 12.682 9.92287 12.3257L11.1427 13.557C10.2703 14.2074 9.21269 14.5894 8.07414 14.5894C5.11912 14.5894 2.696 12.0314 2.696 8.8216C2.69723 8.73082 2.6804 8.6407 2.64651 8.5566C2.61262 8.4725 2.56236 8.39611 2.49869 8.33197C2.43503 8.26783 2.35926 8.21724 2.27587 8.18319C2.19249 8.14914 2.10318 8.13232 2.01324 8.13374C1.83517 8.13654 1.66548 8.21055 1.54141 8.33951C1.41734 8.46848 1.34903 8.64186 1.35147 8.8216C1.35147 12.4893 3.99638 15.5511 7.40188 15.9106V17.6428H6.05735C5.96826 17.6415 5.87982 17.6581 5.79715 17.6917C5.71448 17.7252 5.63924 17.775 5.5758 17.8381C5.51236 17.9013 5.46198 17.9765 5.4276 18.0594C5.39321 18.1424 5.3755 18.2314 5.3755 18.3214C5.3755 18.4113 5.39321 18.5003 5.4276 18.5833C5.46198 18.6662 5.51236 18.7415 5.5758 18.8046C5.63924 18.8678 5.71448 18.9175 5.79715 18.9511C5.87982 18.9846 5.96826 19.0012 6.05735 18.9999H10.091C10.18 19.0012 10.2685 18.9846 10.3511 18.9511C10.4338 18.9175 10.5091 18.8678 10.5725 18.8046C10.6359 18.7415 10.6863 18.6662 10.7207 18.5833C10.7551 18.5003 10.7728 18.4113 10.7728 18.3214C10.7728 18.2314 10.7551 18.1424 10.7207 18.0594C10.6863 17.9765 10.6359 17.9013 10.5725 17.8381C10.5091 17.775 10.4338 17.7252 10.3511 17.6917C10.2685 17.6581 10.18 17.6415 10.091 17.6428H8.74642V15.9106C9.99791 15.7784 11.1421 15.2757 12.0946 14.5177L14.9938 17.444C15.0558 17.509 15.13 17.5609 15.212 17.5966C15.2941 17.6323 15.3824 17.6511 15.4718 17.652C15.5611 17.6528 15.6498 17.6357 15.7325 17.6016C15.8152 17.5675 15.8904 17.517 15.9536 17.4532C16.0168 17.3895 16.0667 17.3136 16.1005 17.2301C16.1343 17.1466 16.1513 17.0571 16.1505 16.9669C16.1496 16.8767 16.131 16.7876 16.0956 16.7048C16.0602 16.6219 16.0088 16.547 15.9445 16.4845L13.0637 13.5767C14.1382 12.3105 14.7968 10.6435 14.7968 8.8214C14.7981 8.73148 14.7816 8.6422 14.7484 8.55876C14.7152 8.47532 14.6659 8.39937 14.6033 8.33534C14.5408 8.2713 14.4662 8.22045 14.384 8.18574C14.3019 8.15104 14.2136 8.13316 14.1246 8.13316C14.0355 8.13316 13.9473 8.15104 13.8651 8.18574C13.7829 8.22045 13.7083 8.2713 13.6458 8.33534C13.5832 8.39937 13.5339 8.47532 13.5007 8.55876C13.4675 8.6422 13.451 8.73148 13.4523 8.8214C13.4523 10.2849 12.9451 11.6093 12.117 12.6211L10.8735 11.366C11.2265 10.8295 11.4355 10.1884 11.4355 9.49997V3.39285C11.4355 1.52739 9.92231 0 8.07418 0L8.07418 0.000251088ZM8.07418 1.35739C9.19479 1.35739 10.091 2.26199 10.091 3.3931V9.50022C10.091 9.81541 10.016 10.1096 9.8914 10.375L6.05739 6.50501V3.39316C6.05739 2.26206 6.95359 1.35746 8.0742 1.35746L8.07418 1.35739ZM6.05738 8.42401L8.9408 11.3345C8.67793 11.4603 8.38648 11.5359 8.07418 11.5359C6.95358 11.5359 6.05738 10.6313 6.05738 9.5002V8.42406V8.42401Z"
                      fill="white"
                    />
                  </svg>
                </i>
                <span class="callIconText">MUTE</span>
              </span>
            </button>
            <button
              href="#"
              :disabled="!callAnswered || callTabs.conferenceStarted"
              role="button"
              class="callLink"
              :class="{disabledBox: !callAnswered || callTabs.conferenceStarted}"
              @click="$emit('openIVR')"
            >
              <span class="callIconBox space-y-4" :class="{active: callTabs.openIVR}">
                <i class="flex justify-center">
                  <svg width="16" height="23" viewBox="0 0 16 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M3.2 3.2H0V0H3.2V3.2ZM9.6 3.2H6.4V0H9.6V3.2ZM16 3.2H12.8V0H16V3.2ZM3.2 9.6H0V6.4H3.2V9.6ZM9.6 9.6H6.4V6.4H9.6V9.6ZM16 9.6H12.8V6.4H16V9.6ZM3.2 16H0V12.8H3.2V16ZM9.6 16H6.4V12.8H9.6V16ZM9.6 22.4H6.4V19.2H9.6V22.4ZM16 16H12.8V12.8H16V16Z" fill="#A1A5A6"/>
                  </svg>
                </i>
                <span class="callIconText">DIAL</span>
              </span>
            </button>
            <button
              href="#"
              :disabled="disableButtons || (callTabs.second.onPhone && !callTabs.conferenceStarted)"
              role="button"
              class="callLink"
              @click="holdCall"
              :class="{disabledBox: disableButtons || (callTabs.second.onPhone && !callTabs.conferenceStarted)}"
            >
              <span class="callIconBox space-y-4" :class="callTabs.callOnHold && callTabs.currentTab !== 'second'? 'active' : ''">
                <i class="flex justify-center">
                  <svg
                    width="7"
                    height="18"
                    viewBox="0 0 7 18"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M5.93455 1.5C5.93455 1.224 5.71057 1 5.43457 1H1.67188C1.39588 1 1.17188 1.224 1.17188 1.5V16.5C1.17188 16.7765 1.39588 17 1.67188 17H5.43457C5.71057 17 5.93455 16.7765 5.93455 16.5V1.5ZM4.93457 16H2.17188V2H4.93457V16Z"
                      fill="#607179"
                      stroke="#607179"
                      stroke-width="0.5"
                    />
                  </svg>
                  <svg
                    width="7"
                    height="18"
                    viewBox="0 0 7 18"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M5.93455 1.5C5.93455 1.224 5.71057 1 5.43457 1H1.67188C1.39588 1 1.17188 1.224 1.17188 1.5V16.5C1.17188 16.7765 1.39588 17 1.67188 17H5.43457C5.71057 17 5.93455 16.7765 5.93455 16.5V1.5ZM4.93457 16H2.17188V2H4.93457V16Z"
                      fill="#607179"
                      stroke="#607179"
                      stroke-width="0.5"
                    />
                  </svg>
                </i>
                <span class="callIconText">HOLD</span>
              </span>
            </button>
            <div class="transfer-buttons">
              <button
                v-if="!callTabs.conferenceStarted"
                href="#"
                :style="'marginRight: 5px;'"
                :disabled="disableButtons || callTabs.second.onPhone"
                role="button"
                class="callLink long"
                @click.prevent="$emit('openTransfer', 'blind')"
                :class="{disabledBox: disableButtons || callTabs.second.onPhone}"
              >
                <span class="callIconBox space-y-4"
                :class="{active: callTabs.transferType === 'blind' && !callTabs.second.onPhone}">
                  <i class="flex justify-center">
                    <svg
                      width="25"
                      height="19"
                      viewBox="0 0 25 19"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M4.93541 6.56025C4.95914 6.46206 4.99802 6.29994 5.03031 6.20373C5.05403 6.20241 5.08368 6.20175 5.11861 6.20175C5.32751 6.20175 5.57727 6.22943 5.83032 6.25645C6.13082 6.28808 6.44186 6.32103 6.72655 6.32103C7.0455 6.32103 7.50547 6.28479 7.81849 5.97111C8.13283 5.65677 8.20796 5.21722 8.26331 4.89695C8.27583 4.82381 8.28769 4.75395 8.29956 4.70189C8.40763 4.28343 8.52823 3.86498 8.64882 3.44652L8.84322 2.77369C9.01456 2.27813 9.15229 1.80036 8.977 1.45505C8.89924 1.25867 8.77732 1.1361 8.7022 1.06032C8.38457 0.742682 7.69197 0.301158 7.26099 0.139706C7.031 0.0481062 6.77202 0.00131788 6.49195 0.00131788C6.04779 0.00131788 5.62735 0.11071 5.19374 0.2346C4.38581 0.474473 3.73869 0.845484 3.21545 1.36806C2.56503 2.01915 2.21312 2.79939 1.87177 3.55459L1.70175 3.92758C1.3795 4.61425 1.14951 5.26138 0.997947 5.90587C0.505681 7.98828 0.505022 11.0117 0.997288 13.0935C1.14886 13.7373 1.38675 14.4029 1.70109 15.0704L1.86913 15.4401C2.21049 16.1967 2.56371 16.9795 3.21545 17.6313C3.73869 18.1552 4.38581 18.5255 5.20033 18.768C5.543 18.8656 6.01286 19 6.49327 19C6.77268 19 7.03034 18.9539 7.24847 18.8669C7.69131 18.7002 8.38391 18.2586 8.70154 17.941C8.77601 17.8672 8.90055 17.742 8.95262 17.5983C9.15163 17.2029 9.01654 16.7278 8.84718 16.2342L8.66398 15.6055C8.53877 15.1706 8.4129 14.7357 8.30285 14.308C8.28769 14.2447 8.27583 14.1736 8.26331 14.1004C8.20796 13.7808 8.13217 13.3432 7.81849 13.0289C7.50547 12.7152 7.04616 12.6796 6.72787 12.6796C6.44252 12.6796 6.13214 12.7126 5.83164 12.7449C5.57859 12.7712 5.32883 12.7996 5.11927 12.7996C5.085 12.7996 5.05667 12.7989 5.0336 12.7976C4.99538 12.6921 4.95716 12.5327 4.93409 12.4351L4.88862 12.2526C4.34298 10.3514 4.34298 8.65452 4.8906 6.73883L4.93541 6.56025ZM3.61809 12.5986L3.65236 12.7383C3.73341 13.079 3.83424 13.5027 4.12617 13.7946C4.41415 14.0826 4.83129 14.1156 5.11993 14.1156C5.38418 14.1156 5.68271 14.0839 5.97134 14.053C6.23296 14.0253 6.4992 13.9956 6.72852 13.9956C6.80563 13.9956 6.86032 13.9996 6.89788 14.0042C6.9249 14.0912 6.94929 14.2309 6.9651 14.3225C6.98289 14.4273 7.00135 14.5281 7.02507 14.6263C7.14039 15.0744 7.26956 15.5212 7.39806 15.9686L7.60103 16.6606C7.62673 16.7337 7.69131 16.9235 7.7203 17.054C7.50218 17.2451 7.02836 17.5403 6.77268 17.6372C6.50447 17.7426 5.92917 17.6043 5.56936 17.5015C4.96902 17.3229 4.51695 17.0685 4.14792 16.6988C3.66686 16.2177 3.37756 15.5765 3.07113 14.8978L2.89452 14.511C2.61774 13.9231 2.41082 13.3445 2.27968 12.791C1.83684 10.9149 1.8375 8.08515 2.281 6.20834C2.41148 5.65282 2.61181 5.0907 2.89584 4.4864L3.07377 4.0976C3.37888 3.42082 3.66752 2.78094 4.14792 2.30053C4.51695 1.9315 4.96836 1.67647 5.56211 1.50052C5.829 1.42408 6.19474 1.31996 6.49195 1.31996C6.60398 1.31996 6.69887 1.33445 6.7852 1.36938C7.02177 1.45834 7.47318 1.73776 7.71899 1.94863C7.68933 2.08043 7.62343 2.26956 7.59839 2.34403L7.38224 3.08275C7.25835 3.51308 7.13446 3.94405 7.02112 4.38492C7.00003 4.47388 6.98224 4.57273 6.9651 4.67553C6.94863 4.77109 6.92359 4.91738 6.9025 4.99514C6.86428 4.99976 6.80826 5.00371 6.72721 5.00371C6.49788 5.00371 6.23165 4.97406 5.96937 4.94638C5.68073 4.91607 5.38287 4.88443 5.11861 4.88443C4.82997 4.88443 4.41415 4.91738 4.12617 5.20536C3.83622 5.49532 3.73539 5.91575 3.65368 6.2525L3.62073 6.38693C3.00457 8.54117 3.00523 10.4621 3.61809 12.5986ZM24.427 5.90521C24.2755 5.26335 24.0376 4.59777 23.7226 3.9289L23.5552 3.56184C23.2132 2.804 22.8599 2.02046 22.2075 1.36806C21.6836 0.844825 21.0372 0.473814 20.2227 0.231965C19.8793 0.133775 19.4101 0 18.9297 0C18.6503 0 18.3926 0.0461291 18.1745 0.133116C17.7317 0.300499 17.0391 0.741363 16.7221 1.059C16.6536 1.12753 16.5257 1.25538 16.4704 1.40233C16.2714 1.79838 16.4071 2.27417 16.5771 2.76842L16.7577 3.38919C16.8836 3.82675 17.0094 4.26301 17.1201 4.69267C17.1353 4.75593 17.1472 4.8271 17.1603 4.90025C17.215 5.21986 17.2908 5.65743 17.6045 5.97177C17.9182 6.28479 18.3768 6.32037 18.6951 6.32037C18.9805 6.32037 19.2902 6.28742 19.5907 6.25579C19.8444 6.22877 20.0942 6.2011 20.3031 6.2011C20.3373 6.2011 20.3657 6.20175 20.3887 6.20307C20.4283 6.31049 20.4671 6.47458 20.4882 6.56552L20.5337 6.74806C21.0787 8.64858 21.0787 10.3455 20.5317 12.2605L20.4876 12.4417C20.4639 12.5386 20.425 12.7001 20.3927 12.7949C20.369 12.7963 20.34 12.7976 20.3044 12.7976C20.0955 12.7976 19.8457 12.7699 19.5927 12.7429C19.2922 12.7113 18.9811 12.6783 18.6964 12.6783C18.3775 12.6783 17.9169 12.7146 17.6045 13.0276C17.2902 13.3419 17.215 13.7801 17.1603 14.1004C17.1472 14.1736 17.136 14.2441 17.1234 14.2968C17.0154 14.7152 16.8941 15.1344 16.7742 15.5522L16.5798 16.225C16.4078 16.7212 16.2707 17.199 16.4466 17.5436C16.5238 17.7394 16.6443 17.8606 16.7214 17.939C17.0391 18.2567 17.7317 18.6975 18.1627 18.8596C18.3933 18.9519 18.6516 18.998 18.9324 18.998C19.3765 18.998 19.797 18.8886 20.2299 18.7647C21.0385 18.5249 21.685 18.1539 22.2082 17.6306C22.8593 16.9789 23.2118 16.1967 23.5539 15.4408L23.7226 15.0711C24.0448 14.3851 24.2755 13.7373 24.4264 13.0928C24.9173 11.0111 24.918 7.98828 24.427 5.90521ZM23.1426 12.7917C23.0128 13.3465 22.8118 13.9086 22.5278 14.5129L22.3512 14.8991C22.0448 15.5772 21.7561 16.2177 21.2751 16.6988C20.906 17.0678 20.4546 17.3222 19.8609 17.4988C19.4918 17.6043 18.9508 17.7538 18.6391 17.6306C18.4019 17.541 17.9491 17.2609 17.7047 17.0507C17.7343 16.9189 17.8002 16.7298 17.8253 16.6553L18.0407 15.9166C18.1646 15.4863 18.2885 15.0553 18.4025 14.6151C18.4236 14.5255 18.4414 14.4259 18.4592 14.3225C18.475 14.2269 18.5001 14.082 18.5218 14.0042C18.5594 13.9996 18.616 13.9956 18.6971 13.9956C18.9264 13.9956 19.1927 14.0253 19.4543 14.053C19.7429 14.0833 20.0408 14.1149 20.305 14.1149C20.5937 14.1149 21.0095 14.0813 21.2968 13.7946C21.5861 13.5053 21.6876 13.0869 21.7687 12.7508L21.8036 12.6124C22.4197 10.4569 22.4191 8.5359 21.8062 6.39945L21.7719 6.2604C21.6902 5.91971 21.5894 5.49663 21.2975 5.20404C21.0095 4.91606 20.593 4.88312 20.3037 4.88312C20.0395 4.88312 19.7409 4.91475 19.4523 4.94506C19.1907 4.97274 18.9245 5.00239 18.6958 5.00239C18.6187 5.00239 18.564 4.9991 18.5264 4.99449C18.4994 4.9075 18.4757 4.76779 18.4592 4.67619C18.4414 4.57141 18.423 4.47059 18.3992 4.3724C18.2833 3.92231 18.1541 3.47354 18.0249 3.0241L17.8239 2.33941C17.7982 2.26561 17.733 2.07516 17.704 1.94468C17.9221 1.75357 18.3959 1.45834 18.6516 1.36147C18.9185 1.25406 19.4945 1.39376 19.8549 1.49722C20.4546 1.67581 20.9067 1.93084 21.2757 2.29987C21.7575 2.7816 22.0474 3.42411 22.3538 4.10353L22.5291 4.48772C22.8072 5.07686 23.0141 5.65545 23.1446 6.20769C23.5868 8.08449 23.5868 10.9142 23.1426 12.7917ZM16.1751 9.75437C16.1488 9.81697 16.1073 9.86969 16.0644 9.92043C16.0519 9.93493 16.0473 9.95338 16.0335 9.96722L14.0143 11.9864C13.8858 12.1149 13.7171 12.1795 13.5484 12.1795C13.3797 12.1795 13.211 12.1149 13.0825 11.9864C12.8248 11.7287 12.8248 11.3122 13.0825 11.0546L13.9768 10.1603H9.85609C9.49233 10.1603 9.1971 9.86508 9.1971 9.50132C9.1971 9.13755 9.49233 8.84233 9.85609 8.84233H13.9768L13.0825 7.94808C12.8248 7.69041 12.8248 7.27393 13.0825 7.01627C13.3402 6.7586 13.7567 6.7586 14.0143 7.01627L16.0335 9.03541C16.0473 9.04925 16.0519 9.0677 16.0644 9.0822C16.1073 9.1336 16.1488 9.18566 16.1751 9.24827C16.2417 9.40972 16.2417 9.59292 16.1751 9.75437Z"
                        fill="#607179"
                      />
                    </svg>
                  </i>
                  <span class="callIconText">BLIND TRANSFER</span>
                </span>
              </button>
              <button
                href="#"
                :disabled="disableButtons || callTabs.second.onPhone"
                role="button"
                class="callLink long"
                @click.prevent="$emit('openTransfer', 'warm')"
                :class="{disabledBox: disableButtons || callTabs.second.onPhone}"
              >
              <span class="callIconBox space-y-4"
                :class="{active: callTabs.transferType==='warm' && callTabs.currentTab !== 'second' && !callTabs.conferenceStarted && !callTabs.second.onPhone}">
                  <i class="flex justify-center">
                    <svg
                      width="22"
                      height="21"
                      viewBox="0 0 22 21"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M19.3538 0H9.42651C8.42657 0 7.61332 0.813816 7.61332 1.81376V9.2578C7.61332 10.2577 8.42657 11.0716 9.42651 11.0716H10.0943V12.981C10.0943 13.2123 10.234 13.4219 10.4476 13.5101C10.5187 13.5399 10.5931 13.5537 10.667 13.5537C10.8159 13.5537 10.9625 13.4953 11.0719 13.3859L13.3862 11.0716H19.3538C20.3537 11.0716 21.167 10.2577 21.167 9.2578V1.81376C21.167 0.813816 20.3537 0 19.3538 0ZM20.0216 9.2578C20.0216 9.62662 19.7221 9.92615 19.3538 9.92615H13.1491C12.9968 9.92615 12.8513 9.98628 12.7442 10.094L11.2397 11.5985V10.4989C11.2397 10.1827 10.9831 9.92615 10.667 9.92615H9.42651C9.05826 9.92615 8.75873 9.62662 8.75873 9.2578V1.81376C8.75873 1.44494 9.05826 1.14541 9.42651 1.14541H19.3538C19.7221 1.14541 20.0216 1.44494 20.0216 1.81376V9.2578ZM12.762 16.6795L12.2717 16.4098C11.9252 16.2185 11.5782 16.0266 11.246 15.8308C11.1979 15.801 11.1469 15.7649 11.0937 15.7271C10.8634 15.5644 10.5479 15.3417 10.1619 15.3417C9.61207 15.3417 9.20086 15.8497 8.76561 16.3874C8.63618 16.5472 8.43401 16.7975 8.30859 16.9103C8.22039 16.8696 8.0984 16.7946 8.02338 16.7488L7.88364 16.6646C6.38029 15.8313 5.33739 14.789 4.49665 13.2753L4.41476 13.139C4.36837 13.064 4.29277 12.9397 4.25383 12.8613C4.36608 12.7358 4.61749 12.5325 4.77842 12.4025C5.31677 11.9667 5.82533 11.5555 5.82533 11.0057C5.82533 10.6191 5.6014 10.303 5.43818 10.0722C5.40153 10.0195 5.36545 9.9691 5.34082 9.92958C5.15011 9.60658 4.96742 9.27555 4.78415 8.94453L4.49035 8.41191C4.29105 8.00185 4.08201 7.6233 3.76302 7.51964C3.59178 7.44518 3.44001 7.44518 3.34952 7.44518C2.95951 7.44518 2.2631 7.59981 1.89828 7.76475C1.27518 8.03221 0.922392 8.66791 0.685864 9.09343C0.337086 9.73773 0.166992 10.3631 0.166992 11.0057C0.166992 11.8052 0.429864 12.5022 0.684719 13.1757L0.809569 13.5096C1.0335 14.1292 1.2895 14.6682 1.59246 15.1572C2.57007 16.7396 4.42736 18.5975 6.00917 19.5751C6.49826 19.8769 7.05321 20.1404 7.6557 20.3568L7.98673 20.4806C8.66195 20.736 9.3595 21 10.1607 21C10.8039 21 11.4293 20.8305 12.0787 20.4783C12.4991 20.2452 13.1354 19.8924 13.3982 19.2807C13.5678 18.9062 13.7218 18.2092 13.7218 17.818C13.7218 17.727 13.7218 17.5741 13.6657 17.4527C13.5454 17.0878 13.1697 16.8788 12.762 16.6795ZM12.3507 18.8174C12.2379 19.0803 11.8084 19.3185 11.5283 19.4737C11.0496 19.7332 10.6149 19.854 10.1613 19.854C9.56969 19.854 8.99813 19.6375 8.39278 19.409L8.04629 19.279C7.51539 19.0889 7.0326 18.8604 6.61166 18.5998C5.18676 17.719 3.44803 15.9802 2.5672 14.5542C2.30605 14.1333 2.08384 13.6636 1.88683 13.1178L1.75683 12.7702C1.52889 12.166 1.31298 11.5956 1.31298 11.0051C1.31298 10.5515 1.43382 10.118 1.69096 9.6438C1.84846 9.35917 2.08728 8.92964 2.36046 8.81223C2.56148 8.72117 3.01105 8.6158 3.29111 8.59461C3.3541 8.69368 3.43027 8.85118 3.46005 8.91246L3.78192 9.49891C3.97034 9.8391 4.15818 10.1793 4.3592 10.52C4.40044 10.5876 4.45084 10.6592 4.50295 10.7331C4.55221 10.8024 4.62723 10.9089 4.66216 10.9696C4.55335 11.1117 4.24867 11.3579 4.05796 11.512C3.53565 11.9341 3.08436 12.2989 3.08436 12.8029C3.08436 13.1596 3.28022 13.4798 3.43715 13.7369L3.49957 13.84C4.44511 15.5433 5.62603 16.7236 7.31609 17.6605L7.42318 17.7253C7.68262 17.8845 8.00505 18.0826 8.363 18.0826C8.86755 18.0826 9.23294 17.6313 9.65559 17.1085C9.80851 16.9195 10.0513 16.6194 10.1934 16.5077C10.2632 16.5438 10.3652 16.6159 10.4322 16.6629C10.5072 16.7162 10.5799 16.7671 10.6555 16.8129C11.0037 17.018 11.36 17.2156 11.7173 17.4126L12.2585 17.7087C12.3193 17.7384 12.4767 17.8158 12.5747 17.8776C12.5586 18.1296 12.4487 18.6015 12.3507 18.8174ZM17.4278 5.13202C17.6517 5.35595 17.6517 5.7179 17.4278 5.94183L15.673 7.6966C15.5613 7.80828 15.4147 7.86441 15.2681 7.86441C15.1215 7.86441 14.9749 7.80828 14.8632 7.6966C14.6393 7.47267 14.6393 7.11072 14.8632 6.8868L15.6404 6.10963H11.9086C11.5919 6.10963 11.3359 5.85306 11.3359 5.53693C11.3359 5.22079 11.5919 4.96422 11.9086 4.96422H15.6404L14.8632 4.18706C14.6393 3.96313 14.6393 3.60118 14.8632 3.37725C15.0871 3.15332 15.4491 3.15332 15.673 3.37725L17.4278 5.13202Z"
                        fill="#607179"
                      />
                    </svg>
                  </i>
                  <span class="callIconText">WARM TRANSFER</span>
                </span>
              </button>
            </div>
          </div>
          <div></div>
        </div>
      </div>
      <div class="power-dialer-footer">
        <div v-if="callTabs.transferType==='warm' && callTabs.second.numberToDial && !callTabs.conferenceStarted" class="footer-buttons">
          <button
             v-if="callTabs.currentTab === 'second'"
             type="button"
             role="button"
             class="callstop2"
             :disabled="callTabs[callTabs.currentTab].hangingupCall"
             :class="{disabledBtn: callTabs[callTabs.currentTab].hangingupCall}"
             @click.prevent="hangupCall('second')"
          >
            <i>
              <svg
                width="20"
                height="9"
                viewBox="0 0 20 9"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M18.8266 7.03796V5.62374L18.1195 3.50242L15.6446 1.73466L9.98778 0.320442L5.03803 1.02755L1.85605 2.79532L0.441836 4.20953V7.03796L1.14894 7.74506H3.97737L5.03803 7.39151L5.39158 5.62374L5.74514 4.56308H13.5233L14.2304 7.39151L17.0588 8.09862L18.8266 7.03796Z"
                  fill="white"
                />
                <path
                  d="M5.36935 7.80044C5.2073 7.96249 5.02316 8.08771 4.81692 8.1761C4.61068 8.26448 4.38971 8.30868 4.15401 8.30868H1.72333C1.49499 8.30868 1.2777 8.26448 1.07146 8.1761C0.865225 8.08771 0.6774 7.96249 0.507989 7.80044C0.345944 7.6384 0.220727 7.45426 0.132338 7.24802C0.04395 7.04178 -0.000244141 6.82081 -0.000244141 6.5851V6.43042C-0.000244141 6.03268 0.00528013 5.6736 0.0163287 5.35319C0.0273772 5.03278 0.0715714 4.73079 0.148911 4.44721C0.226251 4.16363 0.345944 3.88189 0.507989 3.602C0.670034 3.3221 0.905736 3.03116 1.2151 2.72916C1.67545 2.26881 2.22051 1.86738 2.85028 1.52487C3.48005 1.18237 4.16137 0.898787 4.89426 0.674134C5.62715 0.44948 6.40055 0.280069 7.21445 0.165901C8.02836 0.0517325 8.84596 -0.0035102 9.66723 0.000172632C10.4885 0.00385547 11.3061 0.0627809 12.12 0.176949C12.9339 0.291117 13.7036 0.464211 14.4292 0.696231C15.1547 0.92825 15.8305 1.21367 16.4566 1.55249C17.0826 1.89131 17.6167 2.28538 18.0586 2.73469C18.3643 3.04036 18.6 3.32763 18.7657 3.59647C18.9314 3.86532 19.0511 4.14338 19.1248 4.43064C19.1984 4.7179 19.2408 5.01805 19.2518 5.33109C19.2629 5.64414 19.2684 6.00321 19.2684 6.40833V6.5851C19.2684 6.81344 19.2261 7.03257 19.1414 7.24249C19.0567 7.45241 18.9314 7.64024 18.7657 7.80597C18.6037 7.96801 18.4177 8.09139 18.2078 8.17609C17.9978 8.2608 17.7769 8.30499 17.5448 8.30868H15.1142C14.8858 8.30868 14.6685 8.26448 14.4623 8.1761C14.2561 8.08771 14.0682 7.96249 13.8988 7.80044C13.7368 7.6384 13.6208 7.46715 13.5508 7.28669C13.4808 7.10623 13.4329 6.92208 13.4072 6.73426C13.3814 6.54643 13.3759 6.36413 13.3906 6.18736C13.4053 6.01058 13.4127 5.83749 13.4127 5.66808C13.4127 5.49866 13.3998 5.34951 13.374 5.22061C13.3482 5.09171 13.2856 4.97386 13.1862 4.86706C13.0462 4.72711 12.8768 4.65713 12.678 4.65713H6.59021C6.39134 4.65713 6.22193 4.72711 6.08198 4.86706C5.98622 4.96281 5.92546 5.07882 5.89968 5.21509C5.8739 5.35135 5.85917 5.50235 5.85548 5.66808C5.8518 5.8338 5.85733 6.00506 5.87206 6.18183C5.88679 6.35861 5.88311 6.54275 5.86101 6.73426C5.83891 6.92577 5.79288 7.10807 5.7229 7.28116C5.65293 7.45426 5.53508 7.62735 5.36935 7.80044ZM1.9222 3.43627C1.65704 3.70143 1.46001 3.94634 1.33111 4.171C1.20221 4.39565 1.11198 4.62951 1.06042 4.87258C1.00886 5.11565 0.986759 5.37345 0.994125 5.64598C1.00149 5.91851 1.00333 6.23339 0.999649 6.59063C0.999649 6.7895 1.06962 6.95891 1.20957 7.09886C1.27586 7.16515 1.37898 7.21671 1.51893 7.25354C1.65888 7.29037 1.82461 7.31615 2.01611 7.33088C2.20762 7.34561 2.40281 7.35298 2.60169 7.35298C2.80056 7.35298 3.00312 7.3493 3.20936 7.34193C3.4156 7.33456 3.59606 7.3272 3.75074 7.31983C3.90542 7.31247 4.038 7.30878 4.14848 7.30878C4.34736 7.30878 4.51861 7.23697 4.66224 7.09334C4.758 6.99758 4.81876 6.88157 4.84454 6.74531C4.87032 6.60904 4.88321 6.45989 4.88321 6.29784C4.88321 6.1358 4.87769 5.96454 4.86664 5.78408C4.85559 5.60363 4.86111 5.41764 4.88321 5.22613C4.90531 5.03463 4.95134 4.85232 5.02132 4.67923C5.09129 4.50614 5.20914 4.33304 5.37487 4.15995C5.53692 3.9979 5.72106 3.87269 5.9273 3.7843C6.13354 3.69591 6.35451 3.65172 6.59021 3.65172H12.678C12.9063 3.65172 13.1236 3.69591 13.3298 3.7843C13.5361 3.87269 13.7239 3.9979 13.8933 4.15995C14.0553 4.32199 14.1714 4.49325 14.2413 4.67371C14.3113 4.85417 14.3592 5.03831 14.385 5.22613C14.4107 5.41396 14.4163 5.59626 14.4015 5.77304C14.3868 5.94981 14.3813 6.12475 14.385 6.29784C14.3886 6.47094 14.4015 6.62009 14.4236 6.74531C14.4457 6.87053 14.5065 6.98654 14.6059 7.09334C14.7496 7.23697 14.9208 7.30878 15.1197 7.30878C15.2154 7.30878 15.3443 7.31247 15.5064 7.31983C15.6684 7.3272 15.8489 7.33456 16.0478 7.34193C16.2466 7.3493 16.4492 7.35298 16.6554 7.35298C16.8617 7.35298 17.0605 7.34561 17.2521 7.33088C17.4436 7.31615 17.6056 7.29037 17.7382 7.25354C17.8708 7.21671 17.9776 7.16515 18.0586 7.09886C18.1985 6.95891 18.2685 6.7895 18.2685 6.59063V5.6515C18.2685 5.37897 18.2464 5.12117 18.2022 4.8781C18.158 4.63504 18.0715 4.40118 17.9426 4.17652C17.8137 3.95187 17.6185 3.70512 17.357 3.43627C16.9445 3.02379 16.4529 2.66471 15.882 2.35904C15.3112 2.05336 14.687 1.80108 14.0093 1.60221C13.3317 1.40334 12.619 1.25418 11.8714 1.15475C11.1238 1.05531 10.367 1.00559 9.60094 1.00559C8.83491 1.00559 8.08177 1.05899 7.34151 1.16579C6.60126 1.2726 5.89415 1.42728 5.22019 1.62983C4.54623 1.83239 3.92935 2.08466 3.36956 2.38666C2.80977 2.68865 2.32732 3.03852 1.9222 3.43627Z"
                  fill="white"
                />
              </svg>
            </i>

          </button>
          <button
             v-if="callTabs.currentTab === 'first'"
             type="button"
             role="button"
             class="callstop2"
             :disabled="callTabs[callTabs.currentTab].hangingupCall"
             :class="{disabledBtn: callTabs[callTabs.currentTab].hangingupCall}"
             @click.prevent="hangupCall('all')"
          >
            <p>
              <i style="marginRight:5px">
              <svg
                width="20"
                height="9"
                viewBox="0 0 20 9"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M18.8266 7.03796V5.62374L18.1195 3.50242L15.6446 1.73466L9.98778 0.320442L5.03803 1.02755L1.85605 2.79532L0.441836 4.20953V7.03796L1.14894 7.74506H3.97737L5.03803 7.39151L5.39158 5.62374L5.74514 4.56308H13.5233L14.2304 7.39151L17.0588 8.09862L18.8266 7.03796Z"
                  fill="white"
                />
                <path
                  d="M5.36935 7.80044C5.2073 7.96249 5.02316 8.08771 4.81692 8.1761C4.61068 8.26448 4.38971 8.30868 4.15401 8.30868H1.72333C1.49499 8.30868 1.2777 8.26448 1.07146 8.1761C0.865225 8.08771 0.6774 7.96249 0.507989 7.80044C0.345944 7.6384 0.220727 7.45426 0.132338 7.24802C0.04395 7.04178 -0.000244141 6.82081 -0.000244141 6.5851V6.43042C-0.000244141 6.03268 0.00528013 5.6736 0.0163287 5.35319C0.0273772 5.03278 0.0715714 4.73079 0.148911 4.44721C0.226251 4.16363 0.345944 3.88189 0.507989 3.602C0.670034 3.3221 0.905736 3.03116 1.2151 2.72916C1.67545 2.26881 2.22051 1.86738 2.85028 1.52487C3.48005 1.18237 4.16137 0.898787 4.89426 0.674134C5.62715 0.44948 6.40055 0.280069 7.21445 0.165901C8.02836 0.0517325 8.84596 -0.0035102 9.66723 0.000172632C10.4885 0.00385547 11.3061 0.0627809 12.12 0.176949C12.9339 0.291117 13.7036 0.464211 14.4292 0.696231C15.1547 0.92825 15.8305 1.21367 16.4566 1.55249C17.0826 1.89131 17.6167 2.28538 18.0586 2.73469C18.3643 3.04036 18.6 3.32763 18.7657 3.59647C18.9314 3.86532 19.0511 4.14338 19.1248 4.43064C19.1984 4.7179 19.2408 5.01805 19.2518 5.33109C19.2629 5.64414 19.2684 6.00321 19.2684 6.40833V6.5851C19.2684 6.81344 19.2261 7.03257 19.1414 7.24249C19.0567 7.45241 18.9314 7.64024 18.7657 7.80597C18.6037 7.96801 18.4177 8.09139 18.2078 8.17609C17.9978 8.2608 17.7769 8.30499 17.5448 8.30868H15.1142C14.8858 8.30868 14.6685 8.26448 14.4623 8.1761C14.2561 8.08771 14.0682 7.96249 13.8988 7.80044C13.7368 7.6384 13.6208 7.46715 13.5508 7.28669C13.4808 7.10623 13.4329 6.92208 13.4072 6.73426C13.3814 6.54643 13.3759 6.36413 13.3906 6.18736C13.4053 6.01058 13.4127 5.83749 13.4127 5.66808C13.4127 5.49866 13.3998 5.34951 13.374 5.22061C13.3482 5.09171 13.2856 4.97386 13.1862 4.86706C13.0462 4.72711 12.8768 4.65713 12.678 4.65713H6.59021C6.39134 4.65713 6.22193 4.72711 6.08198 4.86706C5.98622 4.96281 5.92546 5.07882 5.89968 5.21509C5.8739 5.35135 5.85917 5.50235 5.85548 5.66808C5.8518 5.8338 5.85733 6.00506 5.87206 6.18183C5.88679 6.35861 5.88311 6.54275 5.86101 6.73426C5.83891 6.92577 5.79288 7.10807 5.7229 7.28116C5.65293 7.45426 5.53508 7.62735 5.36935 7.80044ZM1.9222 3.43627C1.65704 3.70143 1.46001 3.94634 1.33111 4.171C1.20221 4.39565 1.11198 4.62951 1.06042 4.87258C1.00886 5.11565 0.986759 5.37345 0.994125 5.64598C1.00149 5.91851 1.00333 6.23339 0.999649 6.59063C0.999649 6.7895 1.06962 6.95891 1.20957 7.09886C1.27586 7.16515 1.37898 7.21671 1.51893 7.25354C1.65888 7.29037 1.82461 7.31615 2.01611 7.33088C2.20762 7.34561 2.40281 7.35298 2.60169 7.35298C2.80056 7.35298 3.00312 7.3493 3.20936 7.34193C3.4156 7.33456 3.59606 7.3272 3.75074 7.31983C3.90542 7.31247 4.038 7.30878 4.14848 7.30878C4.34736 7.30878 4.51861 7.23697 4.66224 7.09334C4.758 6.99758 4.81876 6.88157 4.84454 6.74531C4.87032 6.60904 4.88321 6.45989 4.88321 6.29784C4.88321 6.1358 4.87769 5.96454 4.86664 5.78408C4.85559 5.60363 4.86111 5.41764 4.88321 5.22613C4.90531 5.03463 4.95134 4.85232 5.02132 4.67923C5.09129 4.50614 5.20914 4.33304 5.37487 4.15995C5.53692 3.9979 5.72106 3.87269 5.9273 3.7843C6.13354 3.69591 6.35451 3.65172 6.59021 3.65172H12.678C12.9063 3.65172 13.1236 3.69591 13.3298 3.7843C13.5361 3.87269 13.7239 3.9979 13.8933 4.15995C14.0553 4.32199 14.1714 4.49325 14.2413 4.67371C14.3113 4.85417 14.3592 5.03831 14.385 5.22613C14.4107 5.41396 14.4163 5.59626 14.4015 5.77304C14.3868 5.94981 14.3813 6.12475 14.385 6.29784C14.3886 6.47094 14.4015 6.62009 14.4236 6.74531C14.4457 6.87053 14.5065 6.98654 14.6059 7.09334C14.7496 7.23697 14.9208 7.30878 15.1197 7.30878C15.2154 7.30878 15.3443 7.31247 15.5064 7.31983C15.6684 7.3272 15.8489 7.33456 16.0478 7.34193C16.2466 7.3493 16.4492 7.35298 16.6554 7.35298C16.8617 7.35298 17.0605 7.34561 17.2521 7.33088C17.4436 7.31615 17.6056 7.29037 17.7382 7.25354C17.8708 7.21671 17.9776 7.16515 18.0586 7.09886C18.1985 6.95891 18.2685 6.7895 18.2685 6.59063V5.6515C18.2685 5.37897 18.2464 5.12117 18.2022 4.8781C18.158 4.63504 18.0715 4.40118 17.9426 4.17652C17.8137 3.95187 17.6185 3.70512 17.357 3.43627C16.9445 3.02379 16.4529 2.66471 15.882 2.35904C15.3112 2.05336 14.687 1.80108 14.0093 1.60221C13.3317 1.40334 12.619 1.25418 11.8714 1.15475C11.1238 1.05531 10.367 1.00559 9.60094 1.00559C8.83491 1.00559 8.08177 1.05899 7.34151 1.16579C6.60126 1.2726 5.89415 1.42728 5.22019 1.62983C4.54623 1.83239 3.92935 2.08466 3.36956 2.38666C2.80977 2.68865 2.32732 3.03852 1.9222 3.43627Z"
                  fill="white"
                />
              </svg>
              </i>
              END ALL
            </p>
          </button>
          <button
           type="button"
           role="button"
           class="patchcall"
           :disabled="!callTabs.second.callAnswered"
           :class="{disabledBtn: !callTabs.second.callAnswered}"
           @click="patchCall">
            PATCH CALL
          </button>
        </div>
        <button
          v-else-if="callTabs.conferenceStarted || callTabs.first.numberToDial"
          type="button"
          role="button"
          class="callStop"
          :disabled="callTabs[callTabs.currentTab].hangingupCall"
          :class="{disabledBtn: callTabs[callTabs.currentTab].hangingupCall}"
          @click.prevent="hangupCall()"
        >
          <i class="flex justify-center">
            <svg
              width="20"
              height="9"
              viewBox="0 0 20 9"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M18.8266 7.03796V5.62374L18.1195 3.50242L15.6446 1.73466L9.98778 0.320442L5.03803 1.02755L1.85605 2.79532L0.441836 4.20953V7.03796L1.14894 7.74506H3.97737L5.03803 7.39151L5.39158 5.62374L5.74514 4.56308H13.5233L14.2304 7.39151L17.0588 8.09862L18.8266 7.03796Z"
                fill="white"
              />
              <path
                d="M5.36935 7.80044C5.2073 7.96249 5.02316 8.08771 4.81692 8.1761C4.61068 8.26448 4.38971 8.30868 4.15401 8.30868H1.72333C1.49499 8.30868 1.2777 8.26448 1.07146 8.1761C0.865225 8.08771 0.6774 7.96249 0.507989 7.80044C0.345944 7.6384 0.220727 7.45426 0.132338 7.24802C0.04395 7.04178 -0.000244141 6.82081 -0.000244141 6.5851V6.43042C-0.000244141 6.03268 0.00528013 5.6736 0.0163287 5.35319C0.0273772 5.03278 0.0715714 4.73079 0.148911 4.44721C0.226251 4.16363 0.345944 3.88189 0.507989 3.602C0.670034 3.3221 0.905736 3.03116 1.2151 2.72916C1.67545 2.26881 2.22051 1.86738 2.85028 1.52487C3.48005 1.18237 4.16137 0.898787 4.89426 0.674134C5.62715 0.44948 6.40055 0.280069 7.21445 0.165901C8.02836 0.0517325 8.84596 -0.0035102 9.66723 0.000172632C10.4885 0.00385547 11.3061 0.0627809 12.12 0.176949C12.9339 0.291117 13.7036 0.464211 14.4292 0.696231C15.1547 0.92825 15.8305 1.21367 16.4566 1.55249C17.0826 1.89131 17.6167 2.28538 18.0586 2.73469C18.3643 3.04036 18.6 3.32763 18.7657 3.59647C18.9314 3.86532 19.0511 4.14338 19.1248 4.43064C19.1984 4.7179 19.2408 5.01805 19.2518 5.33109C19.2629 5.64414 19.2684 6.00321 19.2684 6.40833V6.5851C19.2684 6.81344 19.2261 7.03257 19.1414 7.24249C19.0567 7.45241 18.9314 7.64024 18.7657 7.80597C18.6037 7.96801 18.4177 8.09139 18.2078 8.17609C17.9978 8.2608 17.7769 8.30499 17.5448 8.30868H15.1142C14.8858 8.30868 14.6685 8.26448 14.4623 8.1761C14.2561 8.08771 14.0682 7.96249 13.8988 7.80044C13.7368 7.6384 13.6208 7.46715 13.5508 7.28669C13.4808 7.10623 13.4329 6.92208 13.4072 6.73426C13.3814 6.54643 13.3759 6.36413 13.3906 6.18736C13.4053 6.01058 13.4127 5.83749 13.4127 5.66808C13.4127 5.49866 13.3998 5.34951 13.374 5.22061C13.3482 5.09171 13.2856 4.97386 13.1862 4.86706C13.0462 4.72711 12.8768 4.65713 12.678 4.65713H6.59021C6.39134 4.65713 6.22193 4.72711 6.08198 4.86706C5.98622 4.96281 5.92546 5.07882 5.89968 5.21509C5.8739 5.35135 5.85917 5.50235 5.85548 5.66808C5.8518 5.8338 5.85733 6.00506 5.87206 6.18183C5.88679 6.35861 5.88311 6.54275 5.86101 6.73426C5.83891 6.92577 5.79288 7.10807 5.7229 7.28116C5.65293 7.45426 5.53508 7.62735 5.36935 7.80044ZM1.9222 3.43627C1.65704 3.70143 1.46001 3.94634 1.33111 4.171C1.20221 4.39565 1.11198 4.62951 1.06042 4.87258C1.00886 5.11565 0.986759 5.37345 0.994125 5.64598C1.00149 5.91851 1.00333 6.23339 0.999649 6.59063C0.999649 6.7895 1.06962 6.95891 1.20957 7.09886C1.27586 7.16515 1.37898 7.21671 1.51893 7.25354C1.65888 7.29037 1.82461 7.31615 2.01611 7.33088C2.20762 7.34561 2.40281 7.35298 2.60169 7.35298C2.80056 7.35298 3.00312 7.3493 3.20936 7.34193C3.4156 7.33456 3.59606 7.3272 3.75074 7.31983C3.90542 7.31247 4.038 7.30878 4.14848 7.30878C4.34736 7.30878 4.51861 7.23697 4.66224 7.09334C4.758 6.99758 4.81876 6.88157 4.84454 6.74531C4.87032 6.60904 4.88321 6.45989 4.88321 6.29784C4.88321 6.1358 4.87769 5.96454 4.86664 5.78408C4.85559 5.60363 4.86111 5.41764 4.88321 5.22613C4.90531 5.03463 4.95134 4.85232 5.02132 4.67923C5.09129 4.50614 5.20914 4.33304 5.37487 4.15995C5.53692 3.9979 5.72106 3.87269 5.9273 3.7843C6.13354 3.69591 6.35451 3.65172 6.59021 3.65172H12.678C12.9063 3.65172 13.1236 3.69591 13.3298 3.7843C13.5361 3.87269 13.7239 3.9979 13.8933 4.15995C14.0553 4.32199 14.1714 4.49325 14.2413 4.67371C14.3113 4.85417 14.3592 5.03831 14.385 5.22613C14.4107 5.41396 14.4163 5.59626 14.4015 5.77304C14.3868 5.94981 14.3813 6.12475 14.385 6.29784C14.3886 6.47094 14.4015 6.62009 14.4236 6.74531C14.4457 6.87053 14.5065 6.98654 14.6059 7.09334C14.7496 7.23697 14.9208 7.30878 15.1197 7.30878C15.2154 7.30878 15.3443 7.31247 15.5064 7.31983C15.6684 7.3272 15.8489 7.33456 16.0478 7.34193C16.2466 7.3493 16.4492 7.35298 16.6554 7.35298C16.8617 7.35298 17.0605 7.34561 17.2521 7.33088C17.4436 7.31615 17.6056 7.29037 17.7382 7.25354C17.8708 7.21671 17.9776 7.16515 18.0586 7.09886C18.1985 6.95891 18.2685 6.7895 18.2685 6.59063V5.6515C18.2685 5.37897 18.2464 5.12117 18.2022 4.8781C18.158 4.63504 18.0715 4.40118 17.9426 4.17652C17.8137 3.95187 17.6185 3.70512 17.357 3.43627C16.9445 3.02379 16.4529 2.66471 15.882 2.35904C15.3112 2.05336 14.687 1.80108 14.0093 1.60221C13.3317 1.40334 12.619 1.25418 11.8714 1.15475C11.1238 1.05531 10.367 1.00559 9.60094 1.00559C8.83491 1.00559 8.08177 1.05899 7.34151 1.16579C6.60126 1.2726 5.89415 1.42728 5.22019 1.62983C4.54623 1.83239 3.92935 2.08466 3.36956 2.38666C2.80977 2.68865 2.32732 3.03852 1.9222 3.43627Z"
                fill="white"
              />
            </svg>
          </i>
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import PhoneNumber from '@/pmd/components/util/PhoneNumber.vue'
const Twilio = require('twilio-client')
import { mapState } from 'vuex'
import { UserState } from '@/store/state_models'
import { User } from '@/models'

export default Vue.extend({
  props: [
    'currentLocationId',
    'timerFullTime',
    'callTabs',
    'connection'
  ],
  components: { PhoneNumber },
  data() {
    return {
      callEnded: false,
      isConferenceStarted: false
    }
  },
  computed: {
    disableButtons() {
      return !this.callAnswered || this.callTabs.currentTab === 'second'
    },
    callContact() {
      return this.callTabs[this.callTabs.currentTab].callContact;
    },
    callContactName(): string {
      if (this.isUserNotAssigned) return 'unknown';
      return this.callContact && this.callContact.fullName ? this.callContact.fullName : 'unknown';
    },
    isUserNotAssigned() {
      if (!this.callContact) return true;
      return this.user && this.user.permissions.assigned_data_only && this.callContact.assignedTo !== this.user.id;
    },
    isMuted() {
      if (this.connection) {
        return this.connection.isMuted();
      }
      return false;
    },
    getContactColor(): string {
      if(this.callTabs.currentTab === 'first' && this.callTabs.transferType.length > 0){
         return '#27AE60';
      } else if (this.callTabs.currentTab === 'second' && this.callTabs.transferType.length > 0) {
          return '#FF9416';
      } else { return '#555555'; }
    },
    callAnswered() {
      return this.callTabs[this.callTabs.currentTab].callAnswered;
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion']
		},
  },
  methods: {
    patchCall() {
      this.holdCall();
      Vue.set(this.callTabs, 'showOneTab', true);
      Vue.set(this.callTabs, 'currentTab', 'first');
      Vue.set(this.callTabs, 'conferenceStarted', true);
    },
    async hangupCall(which_call?: string) {
      this.callTabs[this.callTabs.currentTab].hangingupCall = true
      if (which_call) {
        try {
          const data = {
            which_call,
            CallSid: this.connection.parameters.CallSid
          }
          await this.$http.post('/twilio/hangup/outboundcall', data)
        } catch(err) {
          console.log(err);
        }
      } else {
        Twilio.Device.disconnectAll()
      }
    },
    loadDetailPage(id: string) {
      if (!this.callContact) return
      this.$router.push({
        name: this.getSideBarVersion == 'v2' ? 'contact_detail-v2': 'contact_detail',
        params: { contact_id: this.callContact.id }
      })
    },
    muteCall() {
      if (this.connection) {
        this.connection.mute(!this.isMuted);
      }
    },
    async holdCall() {
      this.$root.$emit('holdCall');
    }
  }
})
</script>

<style scoped>
.callIconBox,
.callIconBox:hover {
  background: #ffffff;
  border: 1px solid #ededed;
  box-sizing: border-box;
  border-radius: 5px;
  color: #607179;
}
.callIconBox i {
  margin-bottom: -16px;
}
.callIconBox.active {
  background: #607179;
  color: #ededed;
}
.callIconBox svg path {
  fill: #607179;
}
.callIconBox.active svg path {
  fill: #ededed;
}
.callIcon {
  position: absolute;
  left: 40.24%;
  right: 40.06%;
  top: 17.74%;
  bottom: 51.61%;
  background: #ffffff;
}
.callIconText {
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-size: 11px;
  line-height: 13px;
  text-align: center;
  letter-spacing: 0.06em;
  margin-bottom: 12px;
  margin-top: 7px;
}
.callStop {
  background: #eb5757;
  border-radius: 34px;
  width: 268px;
  height: 37px;
  color: #fff;
  border: none;
}
.dail-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 30px;
}
.dail-box button.callLink {
  position: relative;
  width: 82px;
  height: 62px;
  margin-bottom: 10px;
  padding: 0;
  border: none;
  border-radius:5px;
  outline: none;
}
.dail-box button.callLink.disabledBox, .callStop.disabledBtn {
    pointer-events: none;
    opacity: .5;
}

.dail-box button.callLink.long {
  width: 128px;
}
.callIconBox {
  width: auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
}
.recording-box {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #f0f0f0;
  box-sizing: border-box;
  border-radius: 14px;
  width: 100px;
  margin: auto;
  margin-top: 30px;
}
.recording-box p {
  margin: 0;
  padding: 0;
}
.recording-box p.recording {
  width: 15px;
  height: 15px;
  border: 3px solid #eb5757;
  border-radius: 50%;
  font-size: 5px;
  display: flex;
  margin-right: 10px;
  justify-content: center;
  align-items: center;
  color: #eb5757;
}
.name-box {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 5px;
}
.name-box .name {
  background: #f7f7f7;
  border-radius: 24px;
  color: #2284ff;
  font-size: 12px;
  margin-right: 10px;
  cursor: pointer;
}
.name-box span {
  font-size: 13px;
  color: #2284FF;
  opacity: 1;
  margin-right: 8px;
  height: 25px;
  width: 25px;
  background: #F6F6F6;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding-left: 4px;
  margin-left: -20px;
  cursor: pointer;

}
.name-box h5 {
  font-size: 14px;
  line-height: 16px;
  color: #555555;
  margin-bottom: 0;
}
.name-box h6 {
  font-size: 14px;
  line-height: 16px;
  color: #C2C9CC;
  margin-bottom: 0;
  margin-left: 5px;
}
.phoneText {
  font-family: Roboto;
  font-style: normal;
  font-weight: normal;
  font-size: 23px;
  line-height: 27px;
  letter-spacing: 0.06em;
  color: #000000;
  text-align: center;
  margin-top: 22px;
}
button.callstop2 {
  background: #eb5757;
  border-radius: 34px;
  border: none;
  width: 129px;
  height: 37px;
  font-size: 12px;
  line-height: 14px;
  text-align: center;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  color: #FFFFFF;
}

button.patchcall {
  background: #27ae60;
  border-radius: 34px;
  border: none;
  width: 129px;
  height: 37px;
  font-size: 12px;
  line-height: 14px;
  text-align: center;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  color: #ffffff;
  font-weight: 500;
}
button.patchcall.disableBtn {
  opacity: 0.5;
  pointer-events: none;
}
.transfer-buttons{
  display: flex;
  width: 100%;
  justify-content: space-between;
}
body .dail-box .transfer-buttons .callLink{
  flex:1;
}

</style>
