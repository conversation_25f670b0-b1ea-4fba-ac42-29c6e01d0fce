<template>
  <div class="hl_funnels-list">
    <div v-if="fetchingData">
      <moon-loader :loading="fetchingData" color="#188bf6" size="30px" />
    </div>
    <div v-if="!fetchingData && funnels.length === 0">
      No {{ entityName }}s! Get started by clicking on the New
      {{ entityName }} button.
    </div>
    <div class="pt-3">
      <div v-for="funnel in funnels" :key="funnel.id">
        <div
          class="funnel boder flex justify-between items-center bg-white mb-2 border shadow-sm px-3 py-3 rounded-lg cursor-pointer"
          v-if="funnel.processing"
        >
          <div class="name-updated flex items-center">
          <img
            width="32"
            src="../../../assets/pmd/img/loading-spinner.gif"
            alt="Loading"
          />
            <strong>
              {{ funnel.name }}
            </strong>
          </div>
          <p class="ml-2">{{ timeFromNow(funnel.dateUpdated)  }}</p>
        </div>
        <div
          class="funnel boder flex items-center bg-white mb-2 border shadow-sm px-3  rounded h-14"
          v-else
        >
          <div class="name-updated flex-1">
            <router-link
              :to="getRoute(funnel.id)"
              tag="b"
              class="cursor-pointer"
            >
              {{ funnel.name }}
              <span v-if="funnel.orderFormVersion === 2" class="inline-flex items-center ml-1 px-2 py-0.5 text-xs font-medium rounded bg-purple-100 text-purple-800">Version 2</span>
            </router-link>
          </div>
          <div class="text-gray-700 mr-4 text-left">
            <OnlineUserAvatarGroup :users="activeViewersForFunnel(funnel)"  />

          </div>
          <div class="text-gray-700 mr-2 text-left w-32">
            {{ timeFromNow(funnel.dateUpdated) }}
          </div>
          <div class="steps flex space-x-2 text-apple-500 text-md w-20">
            <b>{{ funnel.steps.length }}</b>
            <p>
              {{ showingWebsites ? 'Page' : 'Step'
              }}{{ funnel.steps.length > 1 ? 's' : '' }}
            </p>
          </div>
          <div class="w-20" v-if="allowPaymentModeOption">
            <UIBadge use="green">
              <ToolsIcon
                v-if="!(funnel.isLivePaymentMode !== undefined ? funnel.isLivePaymentMode : locationConnectMode)"
                class="h-4 w-4"
              />
              <PlayIcon
              v-else
               class="h-4 w-4" />
              <span class="ml-1">
                {{
                (funnel.isLivePaymentMode !== undefined ? funnel.isLivePaymentMode : locationConnectMode) ? 'Live' : 'Test'
              }}</span></UIBadge
            >
          </div>
          <div class="flex justify-between w-20">
            <div
              class="edit cursor-pointer"
              @click="cloneFunnel(funnel.id)"
              :title="`Clone ${entityName}`"
            >
              <DuplicateIcon class="text-curious-blue-500 h-6 w-6" />
            </div>
            <div class="edit" :title="`Edit ${entityName}`">
              <router-link
                :to="getRoute(funnel.id)"
              >
               <EditIcon class="h-6 w-6" />
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import moment from 'moment-timezone'
import { defineComponent, watch, ref, onMounted } from '@vue/composition-api'
import { useDateFormatter } from '@/pmd/composition/dateFormatting'
import { Location, User } from '../../../models/'
import EditIcon from '@/assets/pmd/icons/pencil-alt.svg'
import DuplicateIcon from '@/assets/pmd/icons/duplicate.svg'
import PlayIcon from '@/assets/pmd/icons/play.svg'
import ToolsIcon from '@/assets/pmd/icons/tools.svg'
import { useLocation } from '@/pmd/composition/locationComposition'
import OnlineUserAvatarGroup from '../../components/funnels/OnlineUserAvatarGroup.vue'
import { ActiveUser } from '@/models/editor_active_viewer'
export default defineComponent({
  name: 'FunnelsList',
  components:{EditIcon,DuplicateIcon,PlayIcon,ToolsIcon,OnlineUserAvatarGroup},
  props: {
    funnels: {
      type: Array,
    },
    fetchingData: {
      type: Boolean,
      default: false,
    },
    currentLocationId: {
      type: String,
      required: true,
    },
    showingWebsites: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const { formatDate, timeFromNow } = useDateFormatter()
    const { getById } = useLocation()
    const locationConnectMode = ref();
    const allowPaymentModeOption = ref(false);

    onMounted(() => fetchLocationDetails())

    watch(() => props.currentLocationId, () => {
      fetchLocationDetails()
    })

    async function fetchLocationDetails() {
      try {
        const location: Location =  await getById(props.currentLocationId)
        locationConnectMode.value = location.stripeConnectMode === 'live';
        allowPaymentModeOption.value = !location.stripe?.publishable_key;
      } catch (error) {
        console.error(error)
      }
    }
    return {
      formatDate,
      timeFromNow,
      locationConnectMode,
      allowPaymentModeOption
    }
  },
  computed: {
    entityName(): string {
      let entityName = ''
      if (this.showingWebsites) {
        entityName = 'Website'
      } else {
        entityName = 'Funnel'
      }

      return entityName
    },
    loggedInUser():any{
      return this.$store.state.user.user
    },
    user() {
      const user: User = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    isV2SideBarEnabled() {
      return this.getSideBarVersion == 'v2'
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion']
		},
  },
  methods: {
    cloneFunnel: function (funnelId: string) {
      this.$emit('cloneFunnel', funnelId)
    },
    activeViewersForFunnel(funnel:any){
        const activeUsersArray = this.$store.state.funnel.editorActiveViewers.filter((viewer:any) => viewer.alt_id.includes(`funnel_${funnel.id}`) > 0 )
        let users:ActiveUser[] = []
          activeUsersArray.map((doc:any) => ({
            user: doc?.active_users.forEach((user:any) => {
              if(users.findIndex((u:any) => u.id === user.id) === -1 &&
               !moment(user.date_update.toMillis()).isBefore(
                moment().subtract(15, 'minutes'))){
                users.push(user)
              }
            }),
        }));
       return users.filter(user => user.id !== this.loggedInUser.id)
    },
    getRoute(funnelId: string) {
      if (this.isV2SideBarEnabled) {
        return {
          name: this.showingWebsites ? 'websites-detail-v2' : 'funnels-detail-v2',
          params: {
            location_id: this.currentLocationId,
            funnel_id: funnelId,
          },
        }
      } else {
        return {
          name: this.showingWebsites ? 'websites_detail' : 'funnels_detail',
          params: {
            location_id: this.currentLocationId,
            funnel_id: funnelId,
          },
        }
      }
    }
  },
  filters: {
    readableUpdatedDate: function (dateUpdated: moment.Moment): string {
      const now = moment(new Date())
      const duration = moment.duration(now.diff(dateUpdated))
      const daysAgo = Math.floor(duration.asDays())
      const hoursAgo = Math.floor(duration.asHours())
      const minutesAgo = Math.floor(duration.asMinutes())

      if (daysAgo >= 1) {
        return `Updated ${daysAgo} day${daysAgo > 1 ? 's' : ''} ago`
      }

      if (hoursAgo >= 1) {
        return `Updated ${hoursAgo} hour${hoursAgo > 1 ? 's' : ''} ago`
      }

      if (minutesAgo >= 1) {
        return `Updated ${minutesAgo} minute${minutesAgo > 1 ? 's' : ''} ago`
      }

      return 'Updated few seconds ago'
    },
  },
})
</script>
<style scoped>
/*

.funnel {
  display: flex;
  border-radius: 5px;
  align-items: center;
  padding: 6px;
  margin: 12px 0;
  cursor: pointer;
  background-color: var(--white);
}

.funnel .name-updated {
  flex: 1;
  margin: 0 12px;
  font-size: 16px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.funnel .name-updated b:hover {
  color: var(--primary);
}

.funnel .name-updated p {
  font-size: 12px;
  font-weight: lighter;
}

.funnel .steps {
  text-align: center;
  font-size: 16px;
}

.funnel .steps p {
  line-height: 12px;
}

.funnel .fav {
  font-size: 16px;
  margin-left: 24px;
  cursor: pointer;
}

.funnel .edit {
  font-size: 24px;
  margin-left: 24px;
  cursor: pointer;
  color: var(--gray);
} */
</style>
