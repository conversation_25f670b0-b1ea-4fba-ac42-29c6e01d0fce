<template>
  <div class="pricing-table__wrap">
    <div class="pricing-table__header">
      <h3> {{type === 'reactivate' && currentLevel === -1? 'Reactivate Billing': 'Change Plan'}} for {{company.name}} </h3>
      <!-- Duration Tabs -->
      <div class="pricing-table__duration">
        <div class="pricing-table__duration-btn"
          :class="{active: selectedDuration === 'monthly'}"
          @click="selectedDuration = 'monthly'"
          >Pay Monthly</div>
        <div class="pricing-table__duration-btn"
          :class="{active: selectedDuration === 'annual'}"
          @click="selectedDuration = 'annual'"
          >Pay Annually</div>
      </div>
    </div>
    <!-- Pricing Table -->
    <div class="pricing-table">
      <div class="pricing-table__plan" v-for="plan in $options.plans" :key="plan.level">
        <div class="pricing-table__plan-tag" v-if="currentLevel === plan.level && !isActivePlanLVRG">
          Current Plan
        </div>
        <div class="pricing-table__plan-tag --recommended" v-if="((currentLevel === 1 || currentPlan === 2) && plan.level === 3) || (type === 'reactivate' && plan.level === 3 && currentLevel === -1)">
          Recommended Plan
        </div>
        <div class="pricing-table__plan-info">
          <div class="pricing-table__plan-info-main">
            <div class="pricing-table__plan-name">{{plan.label}}</div>
            <div class="pricing-table__plan-amount --annual" v-if="selectedDuration === 'annual' && plan.level !== 4">
              <span v-if="plan.level !== 4">$</span>
              {{plan.amount_annual}}
              <span class="pricing-table__plan-amount-duration" v-if="plan.level !== 4">/month</span>
            </div>
            <div class="pricing-table__plan-amount" :class="{'--with-discount': selectedDuration === 'annual' && plan.level !== 4}">
              <span v-if="plan.level !== 4">$</span>
              {{plan.amount}}
              <span class="pricing-table__plan-amount-duration" v-if="plan.level !== 4">/month</span>
            </div>
            <div class="pricing-table__plan-tagline" v-if="plan.level===4">Affordable Pricing to scale your business</div>
            <div class="pricing-table__plan-tagline" v-else>Billed {{selectedDuration === 'annual'?'Annually':'Monthly'}}</div>
          </div>
          <div class="pricing-table__plan-info-quick">
            <div class="plan-info-quick__item">
              <div class="plan-info-quick__item-icon">
                <UserIcon />
              </div>
              <div class="plan-info-quick__item-value" v-b-tooltip.hover title="Unlimited Users">
                <InfiniteIcon />
              </div>
            </div>
            <div class="plan-info-quick__item" v-if="plan.level === 1" >
              <div class="plan-info-quick__item-icon">
                <BusinessIcon />
              </div>
              <div class="plan-info-quick__item-value" v-b-tooltip.hover title="1 Business Account">
                <span>1+</span>
              </div>
            </div>
            <div class="plan-info-quick__item" v-else >
              <div class="plan-info-quick__item-icon">
                <BusinessIcon />
              </div>
              <div class="plan-info-quick__item-value" v-b-tooltip.hover title="Unlimited Business Accounts">
                <InfiniteIcon />
              </div>
            </div>
            <div class="plan-info-quick__item" v-if="plan.level >= 3">
              <div class="plan-info-quick__item-icon">
                <ResellIcon />
              </div>
              <div class="plan-info-quick__item-value"  v-b-tooltip.hover title="Unlimited SaaS">
                <InfiniteIcon />
              </div>
            </div>
            <div class="plan-info-quick__item" v-if="plan.level === 4" >
              <div class="plan-info-quick__item-icon">
                <SaveMoneyIcon />
              </div>
              <div class="plan-info-quick__item-value" v-b-tooltip.hover title="Scalable Pricing">
                <PercentIcon />
              </div>
            </div>
          </div>
        </div>
        <ul class="pricing-table__plan-highlights">
          <li v-for="highlight in plan.highlights" :key="highlight">
            <i class="fa fa-check text-success" />  {{highlight}}
          </li>
        </ul>
        <div class="pricing-table__plan-upgrade" >
          <div class="pricing-table__plan-upgrade__upgrade-btn"
            :class="{'--recommended': ((currentLevel === 1 || currentPlan === 2) && plan.level === 3) || (type === 'reactivate' && plan.level === 3 && currentLevel === -1)}"
            v-show="currentLevel < plan.level"
            @click="$emit('upgrade',plan.level, selectedDuration)"
            >
            <span v-if="plan.level === 4">Contact Sales</span>
            <!-- <span v-else-if="type==='reactivate'">Activate</span> -->
            <span v-else>Upgrade</span>
          </div>
          <div class="pricing-table__plan-upgrade__annual-offer"
            v-if="selectedDuration === 'monthly'"
            @click="selectedDuration = 'annual'"
          >Switch to Annual to save 2 months</div>
        </div>
        <ul class="pricing-table__plan-features-list">
          <li class="list-heading"><b>Features</b></li>
        </ul>
        <div class="pricing-table__plan-features">
          <ul class="pricing-table__plan-features-list">
            <!-- <li class="list-heading"><b>Features</b></li> -->
            <li v-if="plan.level !== 1">
              <span class="plan-feature__name">Everything in <b>{{$options.plans[plan.level -2].label}}+</b></span>
            </li>
            <!-- <li v-else>
              <span class="plan-feature__name">Workflow Builder</span>
              <span class="plan-feature__upcomming-tag">Coming Soon</span>
            </li> -->
            <li v-for="feature in plan.features.slice(0,6)" :key="feature.label">
              <span class="plan-feature__name">{{feature.label}}</span>
              <span class="plan-feature__upcomming-tag" v-if="feature.upcoming">Coming Soon</span>
              <span class="plan-feature__new-tag" v-if="feature.new">New</span>
            </li>
          </ul>
          <ul class="pricing-table__plan-features-list --expanded" v-show="expandedPlans.includes(plan.level)">
            <li v-for="feature in plan.features.slice(6)" :key="feature.label">
              <span class="plan-feature__name">{{feature.label}}</span>
              <span class="plan-feature__upcomming-tag" v-if="feature.upcoming">Coming Soon</span>
              <span class="plan-feature__new-tag" v-if="feature.new">New</span>
            </li>
          </ul>
          <div class="pricing-table__plan-features__view-more" v-if="plan.features.length > 6" >
            <span v-if="!expandedPlans.includes(plan.level)" @click="expandedPlans.push(plan.level)">
              View More (+{{plan.features.length - 6}})
              <i class="icon icon-arrow-down-1"></i>
            </span>
            <span v-else @click="viewLess(plan.level)">
              View Less
              <i class="icon icon-arrow-up-1"></i>
            </span>
          </div>
        </div>
      </div>
    </div>
    <!-- End of Pricing Table -->
  </div>
</template>

<script>
import { agencyPlans } from './agencyPlans';
import UserIcon from '@/assets/pmd/img/billing/upgrade-modal/user.svg'
import InfiniteIcon from '@/assets/pmd/img/billing/upgrade-modal/infinite.svg'
import BusinessIcon from '@/assets/pmd/img/billing/upgrade-modal/business.svg'
import ResellIcon from '@/assets/pmd/img/billing/upgrade-modal/resell.svg'
import SaveMoneyIcon from '@/assets/pmd/img/billing/upgrade-modal/save-money.svg'
import PercentIcon from '@/assets/pmd/img/billing/upgrade-modal/percent.svg'

export default {
  props: ['currentLevel', 'company','isActivePlanLVRG','activePlanStatus','type'],
  components: {
    UserIcon,
    InfiniteIcon,
    BusinessIcon,
    ResellIcon,
    SaveMoneyIcon,
    PercentIcon,
  },
  data(){
    return{
      selectedDuration: 'annual', // 'monthly' || 'annual'
      expandedPlans: [],
      // recommendedLevel: 2
    }
  },
  plans: agencyPlans,
  methods:{
    viewLess(planLevel){
      this.expandedPlans = this.expandedPlans.filter( (plan)=>{
        return plan === planLevel ? false : true
      })
    }
  },
}
</script>

<style>
.pricing-table__wrap{
  padding: 32px 16px 16px;
  max-height: calc(100vh - 64px);
  overflow-y: auto;
}
.pricing-table__header{
  text-align: center;
  margin-bottom: 48px;
}
.pricing-table__header h3{
  font-size: 20px;
  line-height: 24px;
  font-weight: 500;
  margin: unset;
  padding: unset;
}
/* Pricing Table Duration ===================> */
.pricing-table__duration{
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid #2F80ED;
  border-radius: 30px;
  background-color: #2F80ED;
  margin: auto;
  margin-top: 20px;
  margin-bottom: 16px;
  width: fit-content;
}
.pricing-table__duration-btn{
  font-size: 14px;
  font-weight: 500;
  width: 140px;
  padding: 8px 0px;
  border-radius: 30px;
  background-color: #2F80ED;
  color: #ffffff;
  /* transition: all 0.3s; */
  cursor: pointer;
  user-select: none;
}
.pricing-table__duration-btn.active{
  background-color: #ffffff;
  color: #2F80ED;
}
/* ===================> Pricing Table Duration */

/* Pricing Table Plan ===================> */
.pricing-table{
  display: flex;
  border: 1px solid #E0E0E0;
  border-radius: 8px;
}
.pricing-table__plan{
  padding: 32px 20px;
  position: relative;
  flex: 1 1;
  border-right: 1px solid #E0E0E0;
  text-align: left;
}
.pricing-table__plan:last-child{
  border-right: none;
}
.pricing-table__plan-tag{
  border: 1px solid #E0E0E0;
  border-radius: 20px;
  padding: 6px;
  width: 180px;
  position: absolute;
  top: 0px;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  font-size: 14px;
  line-height: 16px;
  font-weight: 400;

  background-color: #ffffff;
  color: #4F4F4F;
}
.pricing-table__plan-tag.--recommended{
  border: 1px solid #27AE60;
  background-color: #27AE60;
  color: #ffffff;
}
.pricing-table__plan-info{
  display: flex;
  justify-content: space-between;
  height: 125px; /* As per design */
  height: 144px;
}
.pricing-table__plan-info-main{
  color: #373737;
}
.pricing-table__plan-name{
  font-size: 18px;
  line-height: 20px;
  font-weight: 500;
  /* margin-bottom: 16px; */
  margin-bottom: 24px;
}
.pricing-table__plan-amount{
  font-size: 28px;
  line-height: 32px;
  font-weight: 500;
  margin-bottom: 4px;
  transition: all .2s ease-in-out;
}
.pricing-table__plan-amount-duration{
  font-size: 14px;
  line-height: 16px;
  font-weight: 400;
  color: #828282;
}
.pricing-table__plan-tagline{
  color: #BDBDBD;
  font-size: 14px;
  line-height: 16px;
  font-weight: 400;
  max-width: 150px;

}
.pricing-table__plan-info-quick{

}
.plan-info-quick__item{
  cursor: pointer;
  display: flex;
  align-items: stretch;
  justify-content: space-between;

  margin-bottom: 12px;
}
.plan-info-quick__item-icon{
  max-height: 20px;
  max-width: 20px;
}
.plan-info-quick__item-value{
  max-height: 24px;
  max-width: 20px;
  margin-left: 8px;
  border-bottom: 1px dashed #BDBDBD;
  color: #828282;
}
.pricing-table__plan-highlights{
  list-style: none;
  padding-inline-start: 0px;
  font-size: 14px;
  line-height: 16px;
  color: #4F4F4F;
  height: 108px; /* As per design */
}
.pricing-table__plan-highlights li{
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}
.pricing-table__plan-highlights li i{
  margin-right: 8px;
}
.pricing-table__plan-upgrade{
  text-align: center;
  margin-bottom: 16px;
  height: 60px;
}
.pricing-table__plan-upgrade__upgrade-btn{
  width: 100%;
  background-color: #2F80ED;
  font-weight: 500;
  color: #ffffff;
  line-height: 20px;
  padding: 12px;
  border-radius: 5px;
  user-select: none;
  cursor: pointer;
  transition: all .3s ease-in-out;
}
.pricing-table__plan-upgrade__upgrade-btn:hover{
  background-color: #2761ac;
}
/* .pricing-table__plan-upgrade__upgrade-btn:active{
  transform: translateY(2px);
} */
.pricing-table__plan-upgrade__upgrade-btn.--recommended{
  background-color: #27AE60;
  color: #ffffff;
}
.pricing-table__plan-upgrade__upgrade-btn.--recommended:hover{
  background-color: #249151;
}
.pricing-table__plan-upgrade__annual-offer{
  color: #828282;
  font-size: 12px;
  font-weight: 400px;

  width: fit-content;
  margin: auto;
  /* margin-top: 4px; */
  border-bottom: 1px dashed #828282;
  cursor: pointer;

  opacity: 0;
  transition: all 0.3s;
}
.pricing-table__plan-upgrade:hover .pricing-table__plan-upgrade__annual-offer{
  opacity: 1;
}
.pricing-table__plan-features{
  height: 280px;
  overflow-y: auto;
  position: relative;
}
.pricing-table__plan-features-list{
  list-style: none;
  padding-inline-start: 0px;
  font-size: 14px;
  line-height: 16px;
  color: 828282;
}
.pricing-table__plan-features-list li.list-heading{
  color: #4F4F4F;
  font-weight: 500;
}
.pricing-table__plan-features-list li {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.pricing-table__plan-features__view-more{
  color: #2F80ED;
  font-weight: 14px;
  line-height: 16px;
  font-weight: 500;
  cursor: pointer;
}
.pricing-table__plan-features__view-more i{
  font-size: 10px;
  margin-left: 8px;
}
/* ===================> Pricing Table Plan */
.plan-feature__name{
  margin-right: 8px;
}
.plan-feature__upcomming-tag{
  background-color: #ffe59d;
  padding: 1px 8px;
  border-radius: 10px;
  font-size: 12px;
  /* margin: 0px 0px 0px 8px; */
  /* text-transform: lowercase; */
  white-space: nowrap;
}
.plan-feature__new-tag{
  background-color: #a6f5ad;
  padding: 1px 8px;
  border-radius: 10px;
  font-size: 12px;
  /* margin: 0px 0px 0px 8px; */
  /* text-transform: lowercase; */
  white-space: nowrap;
}
.pricing-table__plan-amount.--annual{
  /* font-size: 20px; */
  position: absolute;
  top: 64px;
  font-size: 24px;
}
.pricing-table__plan-amount.--with-discount{
  font-size: 16px;
  color: #d75046;
  margin-top: 6px;
  padding-top: 10px;
  margin-bottom: -6px;
  text-decoration: line-through;
}
</style>

