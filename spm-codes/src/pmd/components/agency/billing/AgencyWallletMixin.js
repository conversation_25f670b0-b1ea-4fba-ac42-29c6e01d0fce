let walletNotificationTimeout;
import moment from 'moment'

export default {
  created() {
    this.fetchWalletData();
  },
  data() {
    return {
      walletNotification: {
        show: false,
        message: 'Saved!',
        success: true,
      },
      currencySymbol: '$',
      creditsData: {
        currentBalance: '0',
        complimentaryCredits: 0,
        rechargeThresholdBalance: 0,
        autoRechargeAmount: 0,
        createdAt: '',
        loading: false,
        addBalance: {
          showModal: false,
        },
      },
    }
  },
  methods: {
    async fetchWalletData() {
      try {
        this.creditsData.loading = true

        const {
          data: {
            currentBalance,
            autoRechargeAmount,
            rechargeThresholdBalance,
            complimentaryCredits,
            createdAt
          },
        } = await this.saasService.get(
          `/company-wallet/${this.company.id}`,
        )
        this.creditsData.currentBalance = currentBalance
        this.creditsData.autoRechargeAmount = autoRechargeAmount
        this.creditsData.rechargeThresholdBalance = rechargeThresholdBalance
        this.creditsData.complimentaryCredits = complimentaryCredits
        this.creditsData.createdAt = moment(createdAt).format('MMM DD, YYYY')
      } catch (error) {
        console.error('Error while fetching wallet data --> ', error)
      } finally {
        this.creditsData.loading = false
      }
    },
    async updateAutoRechargeAmount(newAmount) {
      try {
        await this.saasService.put(
          `/company-wallet/${this.company.id}`,
          {
            autoRechargeAmount: newAmount,
            rechargeThresholdBalance: this.creditsData.rechargeThresholdBalance,
          }
        )
        this.creditsData.autoRechargeAmount = newAmount

        this.showNotificationForWallet()
      } catch (error) {
        console.error(error)
        this.showNotificationForWallet({ message: 'Failed!', success: false })
      }
    },
    async updateAutoRechargeThreshold(newAmount) {
      try {
        await this.saasService.put(
          `/company-wallet/${this.company.id}`,
          {
            autoRechargeAmount: this.creditsData.autoRechargeAmount,
            rechargeThresholdBalance: newAmount,
          }
        )

        this.creditsData.rechargeThresholdBalance = newAmount
        this.showNotificationForWallet()
      } catch (error) {
        console.error(error)
        this.showNotificationForWallet({ message: 'Failed!', success: false })
      }
    },
    showNotificationForWallet(
      { message, success } = {
        message: 'Saved!',
        success: true,
      }
    ) {
      this.walletNotification = {
        ...this.walletNotification,
        show: true,
        message,
        success,
      }

      if (walletNotificationTimeout) {
        clearTimeout(walletNotificationTimeout)
      }

      walletNotificationTimeout = setTimeout(() => {
        this.walletNotification.show = false
      }, 2000)
    },
  }
}
