import Vue from 'vue'
import { Conversation, Link, Contact } from '../models'
import { Mo<PERSON>le, ActionContext } from 'vuex'
import firebase from 'firebase/app'
import { RootState, ConversationState } from './state_models'
import { sortedIndexBy } from 'lodash'

let unsubscribeRealtime: () => void
let searchDebouncer: NodeJS.Timer
let firstSnapshot: boolean
let locationId: string
let seqFetching = 0
let assignedTo
const limit = 20

function getSortedIndex(
  conversations: Array<{ [key: string]: any }>,
  element: { [key: string]: any }
): number {
  return sortedIndexBy(
    conversations,
    element,
    value => -value.last_message_date
  )
}

function addUpdateContact(
  documents,
  params: {
    currentTab: string
    payload: { [key: string]: any }
    dontAdd?: boolean
  }
) {
  const index = documents.findIndex(obj => obj.id === params.payload.id)
  if (index !== -1) {
    documents.splice(index, 1)
  } else if (params.dontAdd) {
    return
  }
  const sortedIndex = getSortedIndex(documents, params.payload)
  if (documents.length < limit || sortedIndex !== documents.length) {
    documents.splice(sortedIndex, 0, Object.freeze(params.payload))
  } else {
  }
}

function removeContact(
  documents,
  params: { currentTab: string; payload: { [key: string]: any } }
) {
  const index = documents.findIndex(obj => obj.id === params.payload.id)
  if (index !== -1) {
    documents.splice(index, 1)
  }
}

function getConversationContactESData(contact: Contact) {
  if (!contact) return {}

  return {
    email: contact.email,
    phone: contact.phone,
    full_name: contact.fullName,
    company_name: contact.companyName,
    profile_photo: contact.profilePhoto,
    tags: contact.tags
  }
}

export const conversation: Module<ConversationState, RootState> = {
  namespaced: true,
  state: {
    all: {
      done: false,
      documents: [],
      last_document_date: '',
    },
    inbox: {
      done: false,
      documents: [],
      last_document_date: '',
    },
    unread: {
      done: false,
      documents: [],
      last_document_date: '',
    },
    search: {
      done: false,
      documents: [],
      last_document_date: '',
    },
    fetching: false,
    searching: false,
    currentTab: 'unread',
    searchTerm: '',
    triggerLinksMenuItem: {},
    filters: {
      user: ''
    },
    onboarding: false,
    popupStep: 1
  },
  getters: {
    meta: (state: ConversationState) => {
      if (state.searchTerm) return state.search
      if (state.currentTab === 'all') return state.all
      if (state.currentTab === 'inbox') return state.inbox
      if (state.currentTab === 'unread') return state.unread
    },
    conversations: (state: ConversationState) => {
      if (state.searchTerm) return state.search.documents
      if (state.currentTab === 'all') return state.all.documents
      if (state.currentTab === 'inbox') return state.inbox.documents
      if (state.currentTab === 'unread') return state.unread.documents
    },
    getTriggerLinksMenuItem: (state: ConversationState) => {
      return state.triggerLinksMenuItem
    },
  },
  mutations: {
    updateContactInfo: (state: ConversationState, params: {
      contact: Contact
    }) => {
      if (state.searchTerm) {
        const document = state.search.documents && state.search.documents.find(x => x.contact_id === params.contact.id)
        addUpdateContact(state.search.documents, {
          currentTab: 'search',
          payload: {
            ...document,
            ...getConversationContactESData(params.contact)
          },
          dontAdd: true
        }) // Don't add documents that arent in the searched term array
      }

      let documents: Array<{ [key: string]: any }>
      let document
      if (state.currentTab === 'unread') {
        documents = state.unread.documents
        document = state.unread.documents && state.unread.documents.find(x => x.contact_id === params.contact.id)
      } else if (state.currentTab === 'inbox') {
        documents = state.inbox.documents
        document = state.inbox.documents && state.inbox.documents.find(x => x.contact_id === params.contact.id)
      } else if (state.currentTab === 'all') {
        documents = state.all.documents
        document = state.all.documents && state.all.documents.find(x => x.contact_id === params.contact.id)
      }

      if (document) {
        addUpdateContact(documents, {
          currentTab: state.currentTab,
          payload: {
            ...document,
            ...getConversationContactESData(params.contact)
          }
        })
      }
    },
    addOrUpdate: (
      state: ConversationState,
      params: {
        currentTab: string
        payload: { [key: string]: any }
        dontAdd?: boolean
      }
    ) => {
      if (state.searchTerm) {
        addUpdateContact(state.search.documents, { ...params, dontAdd: true }) // Don't add documents that arent in the searched term array
      }

      let documents: Array<{ [key: string]: any }>
      if (params.currentTab === 'unread') {
        documents = state.unread.documents
      } else if (params.currentTab === 'inbox') {
        documents = state.inbox.documents
      } else if (params.currentTab === 'all') {
        documents = state.all.documents
      }
      addUpdateContact(documents, params)
    },
    remove: (
      state: ConversationState,
      params: { currentTab: string; payload: { [key: string]: any } }
    ) => {
      if (state.searchTerm) {
        removeContact(state.search.documents, params)
      }
      let documents: Array<{ [key: string]: any }>
      if (params.currentTab === 'unread') {
        documents = state.unread.documents
      } else if (params.currentTab === 'inbox') {
        documents = state.inbox.documents
      } else if (params.currentTab === 'all') {
        documents = state.all.documents
      }
      removeContact(documents, params)
    },
    add: (
      state: ConversationState,
      params: {
        searchTerm: string
        currentTab: string
        results: Array<{ [key: string]: any }>
      }
    ) => {
      let documents: Array<{ [key: string]: any }>
      if (params.searchTerm) {
        documents = state.search.documents
      } else if (params.currentTab === 'unread') {
        documents = state.unread.documents
      } else if (params.currentTab === 'inbox') {
        documents = state.inbox.documents
      } else if (params.currentTab === 'all') {
        documents = state.all.documents
      }

      Object.entries(params.results).forEach(([key, value]) => {
        documents.push(
          Object.freeze({
            id: value._id,
            ...value._source,
          })
        )
      })
    },
    tab: (state: ConversationState, currentTab: string) => {
      state.currentTab = currentTab
    },
    term: (state: ConversationState, searchTerm: string) => {
      state.searchTerm = searchTerm
    },
    fetching: (state: ConversationState, fetching: boolean) => {
      state.fetching = fetching
    },
    searching: (state: ConversationState, searching: boolean) => {
      state.searching = searching
    },
    finishedFetch: (
      state: ConversationState,
      payload: {
        searchTerm: string
        currentTab: string
        done: boolean
        last_document_date: number
      }
    ) => {
      state.searching = searching;
    },
    onboarding: (
      state: ConversationState,
      onboarding: boolean
    ) => {
      state.onboarding = onboarding;
    },
    popupStep: (
      state: ConversationState,
      popupStep: number
    ) => {
      state.popupStep = popupStep;
    },
    onboarding: (
      state: ConversationState,
      onboarding: boolean
    ) => {
      state.onboarding = onboarding;
    },
    popupStep: (
      state: ConversationState,
      popupStep: number
    ) => {
      state.popupStep = popupStep;
    },
    finishedFetch: (state: ConversationState, payload: { searchTerm: string; currentTab: string, done: boolean; last_document_date: number; }) => {
      if (payload.searchTerm) {
        state.search.done = payload.done
        state.search.last_document_date = payload.last_document_date
      } else if (payload.currentTab === 'unread') {
        state.unread.done = payload.done
        state.unread.last_document_date = payload.last_document_date
      } else if (payload.currentTab === 'inbox') {
        state.inbox.done = payload.done
        state.inbox.last_document_date = payload.last_document_date
      } else if (payload.currentTab === 'all') {
        state.all.done = payload.done
        state.all.last_document_date = payload.last_document_date
      }
    },
    clear: (state: ConversationState, filterClear: boolean) => {
      state.search = {
        done: false,
        last_document_date: '',
        documents: [],
      }
      state.unread = {
        done: false,
        last_document_date: '',
        documents: [],
      }
      state.inbox = {
        done: false,
        last_document_date: '',
        documents: [],
      }
      state.all = {
        done: false,
        last_document_date: '',
        documents: [],
      }
      state.fetching = false
      if (!filterClear) {
        state.currentTab = 'unread'
        state.searchTerm = ''
        assignedTo = undefined // Resetting values so that there is a new search on signIn
        locationId = undefined
      }
    },
    clearSearch: (state: ConversationState) => {
      state.fetching = false
      state.search = {
        done: false,
        last_document_date: '',
        documents: [],
      }
    },
    clearAll(state: ConversationState) {
      if (unsubscribeRealtime) {
        unsubscribeRealtime()
      }
    },
    setTriggerLinks: (
      state: ConversationState,
      links: { value: any; text: string; location_id: string }
    ) => {
      state.triggerLinksMenuItem = links
    },
    setFilters: (state: ConversationState, filters: { user: string }) => {
      state.filters = filters
    },
    filterReadsFromUnreadList(state: ConversationState) {
      state.unread.documents = state.unread.documents.filter(
        doc => doc.unread_count > 0
      )
    },
  },
  actions: {
    submitFilters: async (
      context: ActionContext<ConversationState, RootState>,
      filters
    ) => {
      context.commit('clear', true)
      context.commit('setFilters', filters)
      await Promise.all([
        context.dispatch('loadMore', { currentTab: 'all' }),
        context.dispatch('loadMore', { currentTab: 'inbox' }),
        context.dispatch('loadMore', { currentTab: 'unread' }),
      ])
    },
    markAsReadRemoveFromUnreads: async (
      context: ActionContext<ConversationState, RootState>,
      params: { conversationId: string }
    ) => {
      context.commit('remove', {
        currentTab: 'unread',
        payload: { id: params.conversationId },
      })
    },
    fetchTriggerLinks: async (
      context: ActionContext<ConversationState, RootState>,
      params: { locationId: string }
    ) => {
      let _locationId = locationId
      if(!_locationId) {
        _locationId = params?.locationId
      }
      if (
        !context.state.triggerLinksMenuItem.location_id ||
        context.state.triggerLinksMenuItem.location_id !== _locationId
      ) {
        const snapshot = await Link.fetchAllByLocation(_locationId).get()
        const links = snapshot.docs
          .map((d: any) => new Link(d))
          .map(l => {
            return {
              text: l.name,
              value: `{{trigger_link.${l.id}}}`,
            }
          })
        context.commit('setTriggerLinks', {
          menu: links,
          text: 'Trigger links',
          location_id: locationId,
        })
        return { menu: links, text: 'Trigger links', location_id: _locationId }
      } else {
        return {}
      }
    },
    syncAll: async (
      context: ActionContext<ConversationState, RootState>,
      params: { locationId: string; assignedTo: string }
    ) => {
      if (!params.locationId) {
        context.commit('clearAll')
        context.commit('clear')
        return
      }
      if (locationId === params.locationId) return
      context.commit('clearAll')
      context.commit('clear')
      locationId = params.locationId
      assignedTo = params.assignedTo
      if (!params.locationId) return
      await Promise.all([
        context.dispatch('loadMore', { currentTab: 'all' }),
        context.dispatch('loadMore', { currentTab: 'inbox' }),
        context.dispatch('loadMore', { currentTab: 'unread' }),
      ])
      await context.dispatch('startListening')
    },
    setCurrentTab: async (
      context: ActionContext<ConversationState, RootState>,
      currentTab: string
    ) => {
      context.commit('tab', currentTab)
      if (context.state.searchTerm) {
        context.commit('clearSearch')
        context.commit('searching', true)
        await context.dispatch('loadMore')
      }
    },
    setSearchTerm: async (
      context: ActionContext<ConversationState, RootState>,
      searchTerm: string
    ) => {
      context.commit('term', searchTerm)
      context.commit('clearSearch')
      if (!searchTerm) {
        context.commit('searching', false)
        context.commit('fetching', false)
        return
      }

      context.commit('searching', true)
      if (searchDebouncer) clearTimeout(searchDebouncer)
      searchDebouncer = setTimeout(() => {
        context.dispatch('loadMore')
      }, 300)
    },
    setOnboarding: async (
      context: ActionContext<ConversationState, RootState>,
      onboarding: boolean
    ) => {
      context.commit('onboarding', onboarding)
    },
    setPopupStep: async (
      context: ActionContext<ConversationState, RootState>,
      popupStep: number
    ) => {
      context.commit('popupStep', popupStep)
    },
    setOnboarding: async (
      context: ActionContext<ConversationState, RootState>,
      onboarding: boolean
    ) => {
      context.commit('onboarding', onboarding)
    },
    setPopupStep: async (
      context: ActionContext<ConversationState, RootState>,
      popupStep: number
    ) => {
      context.commit('popupStep', popupStep)
    },
    loadMore: async (
      context: ActionContext<ConversationState, RootState>,
      params?: { currentTab: string }
    ) => {
      let currentTab
      let lastDocumentDate
      if (params && params.currentTab) {
        currentTab = params.currentTab
      } else {
        currentTab = context.state.currentTab
        const data = context.getters.meta
        if (data.last_document_date) lastDocumentDate = data.last_document_date
      }

      let user = context.state.filters.user || assignedTo
      if (lastDocumentDate) context.commit('fetching', true)
      const localSeqFetching = ++seqFetching
      const searchTerm = context.state.searchTerm

      const results = await Conversation.getConversationsFromES(
        locationId,
        currentTab === 'inbox',
        searchTerm,
        lastDocumentDate,
        user,
        undefined,
        Boolean(currentTab === 'unread')
      )
      if (searchTerm && localSeqFetching !== seqFetching) return

      context.commit('searching', false)
      context.commit('fetching', false)

      context.commit('add', {
        currentTab,
        searchTerm,
        results,
      })

      context.commit('finishedFetch', {
        currentTab,
        searchTerm,
        done: results.length !== limit,
        last_document_date:
          results.length > 0
            ? results[results.length - 1]._source.last_message_date
            : '',
      })
    },
    startListening: (context: ActionContext<ConversationState, RootState>) => {
      firstSnapshot = true
      if (unsubscribeRealtime) unsubscribeRealtime()
      unsubscribeRealtime = Conversation.collectionRef()
        .where('location_id', '==', locationId)
        .orderBy('date_updated', 'desc')
        .limit(10)
        .onSnapshot(
          async (snapshot: firebase.firestore.QuerySnapshot) => {
            if (firstSnapshot) {
              firstSnapshot = false
              return
            }

            for (let i = snapshot.docChanges().length - 1; i >= 0; i--) {
              const docChange = snapshot.docChanges()[i]
              let data: { [key: string]: any } = {
                id: docChange.doc.id,
                ...docChange.doc.data(),
              }
              if (data.date_added) data.date_added = data.date_added.toMillis()
              if (data.date_updated)
                data.date_updated = data.date_updated.toMillis()
              if (data.last_message_date)
                data.last_message_date = data.last_message_date.toMillis()

              if (docChange.type === 'removed') continue

              let user = context.state.filters.user || assignedTo

              if ((user && data.assigned_to !== user) || data.deleted) {
                context.commit('remove', {
                  currentTab: 'unread',
                  payload: data,
                })
                context.commit('remove', { currentTab: 'inbox', payload: data })
                context.commit('remove', { currentTab: 'all', payload: data })
                continue
              }

              if (data.contact_id) {
                const contact = await context.dispatch('contacts/syncGet', data.contact_id, { root: true })
                data = Object.assign(getConversationContactESData(contact), data)
              }

              if (data.inbox) {
                context.commit('addOrUpdate', {
                  currentTab: 'inbox',
                  payload: data,
                })
              } else {
                context.commit('remove', { currentTab: 'inbox', payload: data })
              }

              if (data.unread_count > 0) {
                context.commit('addOrUpdate', {
                  currentTab: 'unread',
                  payload: data,
                })
              } else {
                context.commit('addOrUpdate', {
                  currentTab: 'unread',
                  payload: data,
                  dontAdd: true,
                })
              }

              context.commit('addOrUpdate', {
                currentTab: 'all',
                payload: data,
              })
            }
          },
          err => {}
        )
    },
    refreshUnread: (context: ActionContext<ConversationState, RootState>) => {
      context.commit('filterReadsFromUnreadList')
    },
    logout: {
      root: true,
      handler: async (context: ActionContext<ConversationState, RootState>) => {
        if (unsubscribeRealtime) {
          unsubscribeRealtime()
        }
      },
    },
  },
}
