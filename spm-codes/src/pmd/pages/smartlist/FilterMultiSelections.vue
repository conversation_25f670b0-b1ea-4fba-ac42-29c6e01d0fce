<template>
    <div class="form-control-group">

      <div class="dropdown mx-1">
        <a id="dropdownMenuButton1"
            @click.stop.prevent="firstToggler.toggle()"
            data-toggle="dropdown" aria-haspopup="true"
            aria-expanded="false">
            {{ firstDisplay }} <i class="fas fa-caret-down --light"></i>
        </a>
        <div class="dropdown-menu"
              :id="firstToggler.id"
              aria-labelledby="dropdownMenuButton1"
              style="width:100%;  min-height:50px; max-height: 350px; overflow: auto;">
            <div class="dropdown-item pointer text-wrap text-break"
                v-model="firstValueSelections" v-for="s in firstValueSelections"
                @click.stop.prevent="firstSelect(s)">
              <span class="text align-middle text-wrap text-break space-wrap-preline">{{s.value}}</span>
            </div>
        </div>
      </div>

       <div class="dropdown mx-1">
        <a id="dropdownMenuButton2"
            @click.stop.prevent="secondToggler.toggle()"
            data-toggle="dropdown" aria-haspopup="true"
            aria-expanded="false">
            {{ secondDisplay }} <i class="fas fa-caret-down --light"></i>
        </a>
        <div class="dropdown-menu"
            :id="secondToggler.id"
            aria-labelledby="dropdownMenuButton2"
            style="width:100%;  min-height:50px; max-height: 350px; overflow: auto;">
          <div class="dropdown-item pointer text-wrap text-break"
              v-model="secondValueSelections" v-for="s in secondValueSelections"
              @click.stop.prevent="secondSelect(s)">
            <span class="text align-middle text-wrap text-break space-wrap-preline">{{s.value}}</span>
          </div>
      </div>
       </div>

      <div  v-if="!secondValue">
        <span class="--red">* Atleast one {{filterName}} required</span>
      </div>

    </div>
</template>

<script lang="ts">
import Vue from 'vue';
import {FilterOption, IKeyVal} from './vm/option_vm'
import {Tag} from '@/models'
import { SafeDropDownToggle } from './safe_drop_down_toggle'


export default Vue.extend({
  name: 'FilterMultiSelections',
  props : {
    bus : Vue,
    option: FilterOption
  },
  data () {
    return {
      firstValueSelections: [] as IKeyVal[],
      secondValueSelections: [] as IKeyVal[],
      firstValue: null as  IKeyVal | null,
      secondValue: null as IKeyVal | null,
      locationId:'',
      filterName: (this.option ? this.option.filterName.toLowerCase():''),
      waitTime:300,
      debouncer:undefined as NodeJS.Timer | undefined,
      firstToggler : new SafeDropDownToggle(this, 'fistToggler'),
      secondToggler : new SafeDropDownToggle(this, 'secondToggler')
    };
  },
  watch : {
    '$route.params.location_id': function(id) {
      this.locationId = id;
      this.firstValue = null;
      this.secondValue = null;
      this.fetchData1();
      this.fetchData2();
    }
  },
  computed : {
    firstDisplay(){
      if (this.firstValue) return `${this.firstValue.value}`
      return `Select ${this.option.Formatter.firstTitleLabel || this.option.filterName}`;
    },
    secondDisplay(){
      if (this.secondValue) return `${this.secondValue.value}`
      return `Select ${this.option.Formatter.secondTitleLabel || this.option.filterName}`;
    }
  },
  methods: {
    async firstSelect(item: IKeyVal){
      this.firstToggler.toggle();
      await this.$nextTick();
      this.firstValue = item;
      this.secondValue = null;
      this.fetchData2();
      this.publishChange();
    },
    async secondSelect(item: IKeyVal){
      this.secondToggler.toggle();
      await this.$nextTick();
      this.secondValue = item;
      this.publishChange();
    },
    async fetchData1() {
      if(this.option.listFetch) {
        this.firstValueSelections =  await this.option.listFetch({locationId: this.locationId});
      }
    },
    async fetchData2() {
      if (!this.firstValue || !this.firstValue.key) return [];
      if (this.option.listFetch2){
        this.secondValueSelections =  await this.option.listFetch2({locationId: this.locationId, filterBy : [this.firstValue]});
      }
    },
    publishChange(){
      this.option.firstValue = this.firstValue;
      this.option.secondValue = this.secondValue;
      this.$emit('inputChange');
    }
  },
  async mounted(){
    this.locationId = this.$router.currentRoute.params.location_id;
    await this.fetchData1();
    if (this.firstValueSelections && this.option.firstValue && this.option.firstValue.key) this.firstValue = this.firstValueSelections.find(a=> a.key === this.option.firstValue.key );
    await this.fetchData2();
    if (this.secondValueSelections && this.option.secondValue && this.option.secondValue.key)  this.secondValue =  this.secondValueSelections.find(a=> a.key === this.option.secondValue.key);
  }
})
</script>


