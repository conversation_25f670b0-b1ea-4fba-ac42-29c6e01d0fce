declare module '*.vue' {
	import Vue from 'vue';
	export default Vue;
}

declare module 'vue-browserupdate' {
	import { PluginObject} from 'vue';
	const VueBrowserUpdate: PluginObject<{}>;
	export default VueBrowserUpdate;
}

declare module 'vuedraggable';

declare module 'vue2-filters';

declare module 'highcharts/modules/solid-gauge';

declare module 'vue-highcharts';

declare module 'vue2-dropzone';

declare module 'crypto-js';

declare module 'vue-color';

declare module 'vue-analytics';

declare module 'bootstrap-vue/es/components';

declare module 'vue-color';

declare module 'vue-infinite-scroll';

declare module 'vue2-dropzone';

declare module 'vue-infinite-scroll';

declare module 'highcharts-vue';

declare module '@fortawesome/vue-fontawesome';

declare module 'tiptap';

declare module 'tiptap-extensions';

declare module '@tinymce/tinymce-vue';

declare module 'vue2-dropzone';

declare module 'vue-infinite-scroll';

declare module 'vuejs-datepicker';

declare module 'vue-smooth-dnd';

declare module 'vue-save-state';

declare module 'emoji-mart-vue';

declare module 'downloadjs';

declare module 'postmate';
