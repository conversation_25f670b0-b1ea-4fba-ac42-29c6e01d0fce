<template>
  <div class="pending-manual-actions">
    <div class="card-header">
      <h2>Manual Actions</h2>
      <div class="hl_tasks-list">
        <select
          class="selectpicker"
          title="Select Campaign / Workflow"
          data-width="fit"
          name="campaignSelect"
          v-model="filters.campaign"
        >
          <option value>All</option>
          <option
            v-for="campaign in campaigns"
            :key="campaign.id"
            :value="campaign.id"
            >{{ campaign.name }}</option
          >
        </select>

        <select
          class="selectpicker"
          v-if="!user.permissions.assigned_data_only"
          title="Select Assignee"
          data-width="fit"
          name="existingUsers"
          v-model="filters.assignee"
        >
          <option value>All</option>
          <option
            v-for="user in existingUsers"
            :key="user.id"
            :value="user.id"
            >{{ user.name }}</option
          >
        </select>
      </div>
    </div>
    <div class="card-body">
      <div style="text-align: center" class="row">
        <div class="col-4 pending-task-value-col">
          <div style="background-color: #e8bc0d" class="pending-task-value">
            {{ pendingCalls + pendingSms }}
            <span v-if="addPlus">+</span>
          </div>
          <div><i class="fas fa-tasks"></i></div>
          <div>Total Pending</div>
        </div>
        <div class="col-4 pending-task-value-col">
          <div style="background-color: #2f91d3" class="pending-task-value">
            {{ pendingCalls }}
            <span v-if="addPlus">+</span>
          </div>
          <div><i class="fas fa-phone"></i></div>
          <div>Phone</div>
        </div>
        <div class="col-4 pending-task-value-col">
          <div style="background-color: #13987e" class="pending-task-value">
            {{ pendingSms }}
            <span v-if="addPlus">+</span>
          </div>
          <div><i class="fas fa-sms"></i></div>
          <div>SMS</div>
        </div>
      </div>
      <div
        v-if="user.permissions.contacts_enabled"
        class="pt-4 d-flex justify-content-end"
      >
        <router-link
          :to="{
            name: 'manual_actions',
            params: { location_id: filters.locationId }
          }"
        >
          <a>Go to Manual Actions</a>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import {
  Contact,
  User,
  ManualQueue,
  Location,
  MessageType,
  Campaign,
  getCountryDateFormat
} from '../../../models'
import { UserState } from '../../../store/state_models'
import { mapState } from 'vuex'
import lodash, { orderBy } from 'lodash'
import firebase from 'firebase/app'

let realtimeUnsubscribe: () => void
export default Vue.extend({
  data() {
    return {
      pendingCalls: 0,
      pendingSms: 0,
      location: undefined as Location | undefined,
      filters: {
        locationId: '',
        limit: 100,
        assignee: '',
        campaign: ''
      },
      existingUsers: [] as User[],
      campaigns: [] as Campaign[],
      addPlus: false
    }
  },
  async created() {
    this.fetchData()
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }
  },
  updated() {
    const _self = this
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    })
  },
  watch: {
    '$route.params.location_id': async function(id) {
      this.fetchData()
    },
    filters: {
      handler(val, oldVal) {
        console.info('Filters changed')
        this.load()
      },
      deep: true
    }
  },
  beforeDestroy() {
    if (realtimeUnsubscribe) realtimeUnsubscribe()
  },
  methods: {
    async fetchData() {
      const locationId = this.$router.currentRoute.params.location_id
      await Promise.all([
        this.$store.dispatch('campaigns/syncAll', this.$route.params.location_id)
      ])
      this.location = new Location(
        await this.$store.dispatch('locations/getById', locationId)
      )
      this.existingUsers = this.$store.state.users.users.map(
        user => new User(Object.assign({}, user))
      )
      let allCampaigns = this.$store.state.campaigns.campaigns
      this.campaigns = []
      for (let i = 0; i < allCampaigns.length; i++) {
        if (
          allCampaigns[i].campaign_data &&
          allCampaigns[i].campaign_data.templates
        ) {
          let templates = allCampaigns[i].campaign_data.templates
          let isManual = false
          for (let j = 0; j < templates.length; j++) {
            if (
              templates[j].type === 'manual-sms' ||
              templates[j].type === 'manual-call'
            ) {
              isManual = true
            }
          }
          if (isManual) {
            this.campaigns.push(allCampaigns[i])
          }
        }
      }
      this.filters.locationId = locationId

      // We will show all the list of workflows where in case of campaign we were only showing which has manual-call and manual-sms as step
      if (!this.$store.state.workflows.workflows) await this.$store.dispatch('workflows/syncAll')
      let activeWorkflows = this.$store.state.workflows.workflows
        .filter(workflow => workflow.status === 'published')
        .map(workflow => {
          return {
            name: `Workflow - ${workflow.name}`,
            id: `workflow_${workflow.id}`,
          }
        })
      try {
        activeWorkflows = orderBy(activeWorkflows, workflow => workflow?.name?.toLowerCase())
      } catch (err) { }
      this.campaigns.push.apply(this.campaigns, activeWorkflows)
    },
    async load() {
      const { filters } = this
      filters.assignee = this.user.permissions.assigned_data_only
        ? this.user.id
        : filters.assignee

      if (realtimeUnsubscribe) realtimeUnsubscribe()

      const query: firebase.firestore.Query = await ManualQueue.getByUserIdAndLocationIdRealtime(
        filters
      )

      realtimeUnsubscribe = query
        .orderBy('date_added')
        .limit(this.filters.limit)
        .onSnapshot(snapshot => {
          this.pendingCalls = 0
          this.pendingSms = 0
          let docs = snapshot.docs
          docs.map(queue => {
            const manualAction = new ManualQueue(queue)
            if (manualAction.messageType === 13) {
              this.pendingCalls += 1
            } else if (manualAction.messageType === 14) {
              this.pendingSms += 1
            }
          })
          if (docs.length > 99) {
            this.addPlus = true
          } else {
            this.addPlus = false
          }
        })
    }
  }
})
</script>

<style>
.pending-manual-actions
  .hl_tasks-list
  .bootstrap-select
  > .btn.dropdown-toggle.bs-placeholder
  .filter-option {
  font-weight: 500 !important;
  color: dimgrey !important;
}
.pending-manual-actions .card-body .pending-task-value-col {
  min-height: 180px;
}
.pending-manual-actions .card-body .pending-task-value {
  margin: 1rem;
  height: 120px;
  width: 120px;
  line-height: 60px;
  border-radius: 60px;
  color: white;
  text-align: center;
  font-size: 2em;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: auto;
  margin-bottom: 0.5rem;
}
</style>
