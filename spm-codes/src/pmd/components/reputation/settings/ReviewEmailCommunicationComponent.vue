<template>
  <div
    id="review-email-communication"
    class="hl_settings--body hl_settings--customize-communication"
  >
    <div class="container">
      <div class="row">
        <div class="card">
          <div class="card-header --no-right-padding card-header--withbutton">
            <h3>Automatic Email review</h3>
          </div>
          <div class="card-body">
            <div class="form-group">
              <p>
                Automatic “Review Request” emails are sent directly to all your
                customers. Below you can customize when they are sent out.
              </p>
            </div>
            <div class="form-group">
              <label>When to send email after check-in?</label>
              <div class="box-selection row">
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      name="when-send-email"
                      value="0"
                      id="immediately-email"
                      v-model="emailSettings.after_check_in_hours"
                    />
                    <label for="immediately-email">Immediately</label>
                  </div>
                </div>
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      name="when-send-email"
                      value="1"
                      id="hour-email"
                      v-model="emailSettings.after_check_in_hours"
                    />
                    <label for="hour-email">1 Hour</label>
                  </div>
                </div>
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      value="2"
                      name="when-send-email"
                      id="2-hour-email"
                      v-model="emailSettings.after_check_in_hours"
                    />
                    <label for="2-hour-email">2 Hours</label>
                  </div>
                </div>
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      value="4"
                      name="when-send-email"
                      id="4-hour-email"
                      v-model="emailSettings.after_check_in_hours"
                    />
                    <label for="4-hour-email">4 Hours</label>
                  </div>
                </div>
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      value="24"
                      name="when-send-email"
                      id="24-hour-email"
                      v-model="emailSettings.after_check_in_hours"
                    />
                    <label for="24-hour-email">1 Day</label>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label>Repeat this every</label>
              <div class="box-selection row">
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      name="repeat-send-email"
                      id="email_repeat_none"
                      value=""
                      v-model="emailSettings.repeat_every_days"
                    />
                    <label for="email_repeat_none">Don't Repeat</label>
                  </div>
                </div>
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      name="repeat-send-email"
                      id="month-email"
                      value="30"
                      v-model="emailSettings.repeat_every_days"
                    />
                    <label for="month-email">Month</label>
                  </div>
                </div>
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      name="repeat-send-email"
                      id="month6-email"
                      value="180"
                      v-model="emailSettings.repeat_every_days"
                    />
                    <label for="month6-email">6 Month</label>
                  </div>
                </div>
                <div class="col-md">
                  <div class="box-select">
                    <input
                      type="radio"
                      name="repeat-send-email"
                      id="year-email"
                      value="365"
                      v-model="emailSettings.repeat_every_days"
                    />
                    <label for="year-email">Year</label>
                  </div>
                </div>
              </div>
            </div>

            <div class="save-button-holder">
              <button
                type="button"
                class="btn btn-success"
                @click.stop="validateBeforeSubmit"
              >
                 <i class="icon" :class="saving ? 'icon-clock' : 'icon-ok'"></i> {{saveButtonText}} 
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang='ts'>
import Vue from 'vue'
import { Location } from '@/models'
import libphonenumber from 'google-libphonenumber'

var phoneUtil = libphonenumber.PhoneNumberUtil.getInstance()
var PNF = libphonenumber.PhoneNumberFormat
declare var $: any

export default Vue.extend({
  data() {
    return {
      emailSettings: {} as { [key: string]: any },
      currentLocationId: '',
      location: undefined as Location | undefined,
      saving : false
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.fetchData()
    },
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchData()
  },
  methods: {
    async fetchData() {
      this.location = new Location(
        await this.$store.dispatch('locations/getById', this.currentLocationId)
      )
      this.emailSettings = this.location.settings.email
    },
    async validateBeforeSubmit() {
      if (!this.location) return
       this.saving = true;
      await this.location.save();
      this.saving = false;
    },
  },
  computed:{
      saveButtonText(): string {
      return this.saving ? 'Saving' : 'Save'
    },
  }
})
</script>

<style lang="scss" scoped>
#review-email-communication {
  .container {
    left: 15px;
    right: 15px;
    max-width: unset;
  }
  .card {
    width: 100%;
    .card-header--withbutton {
      display: grid;
      grid-template-columns: auto 1fr;
      grid-gap: 1rem;
    }
    .card-body {
      margin: unset;
      max-width: unset;
      .save-button-holder {
        padding: 25px 0 0 0;
        button {
          width: 200px;
        }
      }
    }
  }
}
</style>
