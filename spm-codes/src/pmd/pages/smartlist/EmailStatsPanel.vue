<!-- prettier-ignore -->
<template>
  <div class="more-info more-detail-animation d-flex w-100" :class="{'justify-content-end': !v2, 'justify-content-center': v2}">
    <template v-if="emailStats && emailStats.count > 0">
      <div class="more-info-data text-center space-apart" :class="{'stats-clickable': !v2}" @click="openEmailStatsDetailModal('total')">
        <!-- <div class="stats-clickable h5" @click="openEmailStatsDetailModal('total')">{{getPercentage(emailStats.count ,emailStats.count) + '%'}}</div> -->
        <div class="h5"><span ><wbr></span></div>
        <div class="h5 my-0 py-0" :class="{'--purple': !v2, '--gray': v2}">{{emailStats.count | formatNumberNoDecimal}}</div>
        <div class="--dark">
          <span v-if="v2">Attempted</span>
          <span v-else>Total</span>
        </div>
      </div>
      <div class="more-info-data text-center space-apart" :class="{'stats-clickable': !v2}" @click="openEmailStatsDetailModal('delivered')">
        <div class="h5">
          <span class="--light" v-if="haveSMTP">NA</span>
          <span class="--green" v-else>{{getPercentage(emailStats.delivered ,emailStats.count) + '%'}}</span>
        </div>
        <div class="h5 my-0 py-0" :class="{'--purple': !v2, '--gray': v2}">{{emailStats.delivered | formatNumberNoDecimal}}</div>
        <div class="--dark" >Delivered</div>
      </div>
      <div class="more-info-data  text-center space-apart" :class="{'stats-clickable': !v2}" @click="openEmailStatsDetailModal('opened')" >
        <div class="--green h5">{{getPercentage(emailStats.opened ,emailStats.count) + '%'}}</div>
        <div class="h5 my-0 py-0" :class="{'--purple': !v2, '--gray': v2}">{{emailStats.opened | formatNumberNoDecimal}}</div>
        <div class="--dark" >Opened</div>
      </div>
      <div class="more-info-data  text-center space-apart" :class="{'stats-clickable': !v2}" @click="openEmailStatsDetailModal('clicked')" >
        <div class="--green h5">{{getPercentage(emailStats.clicked ,emailStats.count) + '%'}}</div>
        <div class="h5 my-0 py-0"  :class="{'--purple': !v2, '--gray': v2}">{{emailStats.clicked | formatNumberNoDecimal}}</div>
        <div class="--dark" >
          <span>Clicked</span>
          <span
            style="margin-top: -7px"
            class="input-group-addon mx-1"
            v-b-tooltip.hover
            :title="
              haveSMTP
                ? `NA - Some stats are not avaiable for SMTP.\n Accuracy of metrics depend upon receiver's domain.`
                : `Accuracy of metrics depend upon receiver's domain.`">
            <i class="fas fa-question-circle"></i>
          </span>
        </div>
      </div>
      <div class="more-info-data  text-center space-apart" :class="{'stats-clickable': !v2}" @click="openEmailStatsDetailModal('replied')" >
        <div class="--green h5">{{getPercentage(emailStats.replied ,emailStats.count) + '%'}}</div>
        <div class="h5 my-0 py-0"   :class="{'--purple': !v2, '--gray': v2}">{{emailStats.replied | formatNumberNoDecimal}}</div>
        <div class="--dark" >Replied</div>
      </div>
      <div class="more-info-data  text-center space-apart" :class="{'stats-clickable': !v2}" @click="openEmailStatsDetailModal('permanent_fail')">
        <div class=" h5">
          <span class="--light" v-if="haveSMTP">NA</span>
          <span class="--yellow" v-else>{{getPercentage(emailStats.permanent_fail ,emailStats.count) + '%'}}</span>
        </div>
        <div class="h5 my-0 py-0"   :class="{'--purple': !v2, '--gray': v2}">{{emailStats.permanent_fail | formatNumberNoDecimal}}</div>
        <div class="--dark" >Bounced</div>
      </div>
      <div class="more-info-data  text-center space-apart" :class="{'stats-clickable': !v2}" @click="openEmailStatsDetailModal('unsubscribed')" >
        <div class=" h5">
          <span class="--light" v-if="haveSMTP">NA</span>
          <span class="--red" v-else>{{getPercentage(emailStats.unsubscribed ,emailStats.count) + '%'}}</span>
        </div>
        <div class="h5 my-0 py-0"   :class="{'--purple': !v2, '--gray': v2}">{{emailStats.unsubscribed | formatNumberNoDecimal}}</div>
        <div class="--dark" >Unsubscribed</div>
      </div>
    </template>
    <EmailStatsDetailModal
          v-if="!v2 && showEmailStatsDetailModal"
          :option="emailStatsDetailModalActiveTab"
          @closeEmailStatsDetailModal="showEmailStatsDetailModal = !showEmailStatsDetailModal"
          modalTitle="Email Stats"
          :bulk_req_id='bulkReqId'
          :haveSMTP="haveSMTP"/>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import EmailStatsDetailModal from '../../components/EmailStatsDetailModal.vue'

export default Vue.extend({
  name: 'EmailStatsPanel',
  props: ['emailStats', 'bulkReqId', 'v2'],
  data() {
    return {
      haveSMTP: false,
      showEmailStatsDetailModal: false,
      emailStatsDetailModalActiveTab: 'total',
    }
  },
  components: {
    EmailStatsDetailModal,
  },
  watch: {},
  methods: {
    async openEmailStatsDetailModal(val: string) {
      if (this.v2) return;
      this.emailStatsDetailModalActiveTab = val
      this.showEmailStatsDetailModal = true
    },
    getPercentage(value: number, total: number) {
      return value ? ((value / total) * 100).toFixed(2) : 0
    },
  },
  // async created() {},
  async mounted() {
    if (
      this.emailStats &&
      this.emailStats.provider &&
      this.emailStats.provider.toLowerCase() === 'smtp'
    )
      this.haveSMTP = true
  },
  // async beforeDestroy() {},
})
</script>

<style scoped>
.h5 {
  margin-bottom: 0px;
}
.space-apart {
  margin-right: 25px;
  margin-left: 25px;
}
.fa-phone {
  color: #188bf6;
  margin-right: 10px;
  font-size: 20px;
}
.email-stats__single {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 1rem;
  color: #2a3135;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}

.more-detail-animation {
  max-height: 87px;
  overflow: hidden;
  transition: max-height 0.5s ease-out;
}

.stats-clickable {
  cursor: pointer;
}
</style>
