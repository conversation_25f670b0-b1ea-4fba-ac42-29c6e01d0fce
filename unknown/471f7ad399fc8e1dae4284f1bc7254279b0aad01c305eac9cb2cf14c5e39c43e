<template>
    <div class="form-group">
        <div class="form-input-dropdown dropdown">
            <div data-toggle="dropdown">
                <i class="icon icon-arrow-down-1"></i>
                <input type="text" class="pr-10 mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800" data-lpignore="true" placeholder="Select Appointment Status" :value="statusType">
            </div>
            <div class="dropdown-menu">
                <a class="dropdown-item trigger-type" href="javascript:void(0);" v-for="type in types" :key="type.value" @click.prevent="setValue(type.value)">
                    <p>{{type.name}}</p>
                </a>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import Vue from 'vue';

export default Vue.extend({
    props: ['action'],
    data() {
        return {
            types: [{name: 'Confirmed', value: 'confirmed'},
                {name: 'Invalid', value: 'invalid'},
                {name: 'Cancelled', value: 'cancelled'},
                {name: 'No-Show', value: 'noshow'},
                {name: 'Showed', value: 'showed'}],
        }
    },
    computed:{
        statusType(): string {
            const type = lodash.find(this.types, {value: this.action.status_type});
            if(type) return type.name;
            return '';
    }
    },
    methods: {
        setValue(value: string) {
            Vue.set(this.action, 'status_type', value);
            this.$emit('update:action', this.action);
        }
    },
})
</script>
