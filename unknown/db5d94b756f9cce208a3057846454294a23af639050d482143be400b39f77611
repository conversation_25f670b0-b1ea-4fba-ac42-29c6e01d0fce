<template>
  <div id="review-trends" class="card">
    <div class="card-header card-header--compact space-between">
      <h2>Review Trends</h2>
      <div>
        <span v-if="filter === 'thisMonthWeekly'"> This Month</span>
        <span v-else-if="filter === 'lastSixMonthMonthly'"> Last 6 Month</span>
        <span v-else-if="filter === 'thisYearMonthly'"> This Year </span>
        <span v-else> {{ filter }}</span>
        <select class="selectpicker more-select" v-model="filter">
          <!-- <option>Today</option> -->
          <option value="thisMonthWeekly">This Month</option>
          <option value="lastSixMonthMonthly">Last 6 Months</option>
          <option value="thisYearMonthly">This Year</option>
        </select>
      </div>
    </div>
    <div class="card-body --max-height">
      <div class="chartContainer">
        <highcharts
          class="answered-missed-chart"
          :options="invitesTrendsChart"
        ></highcharts>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { ReviewAggregateData } from '@/store/state_models'
import { Chart } from 'highcharts-vue'

declare var $: any

export default Vue.extend({
  components: {
    highcharts: Chart,
  },
  data() {
    return {
      filter: 'lastSixMonthMonthly',
      currentLocationId: '',
      invitesTrendsChart: {
        credits: {
          enabled: false,
        },
        chart: {
          type: 'column',
          height: 300,
        },
        legend: {
          enabled: false,
        },

        title: {
          text: '',
        },

        xAxis: {
          gridLineWidth: 0,
          categories: [],
          crosshair: true,
        },
        yAxis: {
          allowDecimals: false,
          min: 0,
          title: {
            text: '',
          },
        },
        tooltip: {
          headerFormat:
            '<span style="font-size:10px">{point.key}</span><br/><table>',
          footerFormat: '</table>',
          shared: true,
          useHTML: true,
        },
        plotOptions: {
          column: {
            pointPadding: 0.2,
            borderWidth: 0,
          },
          series: {
            stacking: 'normal',
          },
        },

        series: [
          {
            name: '5 Star',
            color: '#79C9A1',
            data: [],
          },
          {
            name: '4 star',
            color: '#AED888',
            data: [],
          },
          {
            name: '3 star',
            color: '#FFD935',
            data: [],
          },
          {
            name: '2 star',
            color: '#FFB235',
            data: [],
          },
          {
            name: '1 star',
            color: '#FF8C5A',
            data: [],
          },
        ],
      },
    }
  },
  computed: {
    currentReviewsAggregate() {
      let requestAggrigate = this.$store.getters['reviewAggregate/current'](
        this.filter
      )

      let result = { label: [], data: [] }
      if (requestAggrigate && requestAggrigate.length > 0) {
        switch (this.filter) {
          case 'lastSixMonthMonthly':
          case 'thisYearMonthly':
            result = {
              label: requestAggrigate.map((x: any) =>
                x.label.format('MMM, YY')
              ),
              data: requestAggrigate.map((x: any) => x.aggregate.breakdown),
            }
            break
          default:
            result = {
              label: requestAggrigate.map((x: any) =>
                x.label.format('DD-MM-YY')
              ),
              data: requestAggrigate.map((x: any) => x.aggregate.breakdown),
            }
            break
        }
      }
      return result
    },
    /*  previousReviewsAggregate(): ReviewAggregateData {
      return this.$store.getters['reviewAggregate/previous'](this.filter)
    }, */
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.filter = 'lastSixMonthMonthly'
      this.clearGraph()
      this.refreshData()
    },
    filter: function () {
      this.refreshData()
    },
    currentReviewsAggregate: function () {
      this.updateGraph()
    },
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.refreshData();
    this.updateGraph()
  },
  methods: {
    clearGraph() {
      this.invitesTrendsChart.series[0].data = []
      this.invitesTrendsChart.series[1].data = []
      this.invitesTrendsChart.series[2].data = []
      this.invitesTrendsChart.series[3].data = []
      this.invitesTrendsChart.series[4].data = []
      this.invitesTrendsChart.xAxis.categories = []
    },
    refreshData() {
      if (this.filter === 'thisMonthWeekly') {
        this.$store.dispatch(
          'reviewAggregate/thisMonthWeekly',
          this.currentLocationId
        )
      } else if (this.filter === 'lastSixMonthMonthly') {
        this.$store.dispatch(
          'reviewAggregate/lastSixMonthMonthly',
          this.currentLocationId
        )
      } else if (this.filter === 'thisYearMonthly') {
        this.$store.dispatch(
          'reviewAggregate/thisYearMonthly',
          this.currentLocationId
        )
      }
    },
    updateGraph() {
      if (this.currentReviewsAggregate) {
        this.invitesTrendsChart.xAxis.categories =
          this.currentReviewsAggregate.label
        for (let i = 0; i < 5; i++) {
          this.invitesTrendsChart.series[4 - i].data =
            this.currentReviewsAggregate.data.map(x => x[i + 1])
        }
      }
    },
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
})
</script>
<style lang="scss" scoped>
#review-trends {
  width: 100%;
  .space-between {
    display: flex;
    justify-content: space-between;
  }

  .card-header--compact {
    padding: 12px 50px 10px 30px;
  }

  .card-header {
    span {
      color: #afb8bc;
    }
    .more-select {
      top: 53%;
      transform: translateY(-53%);
    }
  }

  .card-body {
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    max-height: 300px;

    .chartContainer {
      width: 35vw;
    }
  }
}
</style>
