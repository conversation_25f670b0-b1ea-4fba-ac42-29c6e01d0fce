<template>
  <div class="power-dialer-main">
    <div class="power-dialer-header">
      <div class="call-section-box">
        <div class="manual-action">
          <p style=" text-align: left;">Call</p>
          <div class="manual-box">
            <i class="fas fa-users"></i>
            <div class="manual-action-box">
              <p>Manual Actions</p>
              <p>{{ count }} contacts</p>
            </div>
            <div style="flex: 1;text-align: right;">
              <i>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM8 14.784C4.256 14.784 1.216 11.744 1.216 8C1.216 4.256 4.256 1.216 8 1.216C11.744 1.216 14.784 4.256 14.784 8C14.784 11.744 11.744 14.784 8 14.784Z"
                    fill="#2284FF"
                  />
                  <path
                    d="M8.00012 12.2239C10.333 12.2239 12.2241 10.3327 12.2241 7.99988C12.2241 5.66703 10.333 3.77588 8.00012 3.77588C5.66727 3.77588 3.77612 5.66703 3.77612 7.99988C3.77612 10.3327 5.66727 12.2239 8.00012 12.2239Z"
                    fill="#2284FF"
                  />
                </svg>
              </i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="power-dialer-body"></div>
    <div class="power-dialer-footer">
      <CallFrom
        :currentLocationId="currentLocationId"
        :phoneNumbers="phoneNumbers"
        iconName="fa fa-play"
        :isDisabled="!manualQueue.length"
        @makeCall="makeCall"
      />
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import PhoneNumber from '@/pmd/components/util/PhoneNumber.vue'
import CallFrom from '@/pmd/components/power_dialer/CallFrom.vue'
import { EventBus } from '@/models/event-bus'

import { ManualQueue, MessageType, Contact } from '../../../models'

let cancelManualqueueSubscription: () => void

export default Vue.extend({
  props: ['phoneNumbers', 'currentLocationId', 'manualQueue'],
  components: { PhoneNumber, CallFrom },
  data() {
    return {
      manualQueueItem: undefined as ManualQueue | undefined
    }
  },
  computed: {
    count() {
      if (this.manualQueue && this.manualQueue.length)
        return this.manualQueue.length
      else return
    }
  },
  methods: {
    async makeCall() {
      if (this.manualQueue.length) {
        this.manualQueueItem = await ManualQueue.getById(this.manualQueue[0].id)
        if (this.manualQueueItem.completed || this.manualQueueItem.inProgress)
          return
        const contact = await Contact.getById(this.manualQueueItem.contactId)
        this.manualQueueItem.ref.update({
          in_progress: true
        })
        this.$root.$emit('makeCall', {
          phone_number: contact.phone,
          manualCallId: this.manualQueueItem.id,
          contact,
          manualCallCampaignId: this.manualQueueItem.campaignId,
          showModal: true
        })
      }
    }
  }
})
</script>
<style lang="scss" scoped>
.call-section-box {
  .manual-action {
    p {
      font-size: 12px;
      line-height: 14px;
      color: #607179;
      margin-bottom: 10px;
    }
    .manual-box {
      background: #ffffff;
      border: 1px solid #eeeeee;
      box-sizing: border-box;
      border-radius: 7px;
      display: flex;
      align-items: center;
      padding: 6px 8px;
      height: 48px;
      .fas {
        background: #f1f1f1;
        height: 32px;
        width: 32px;
        display: flex;
        border-radius: 50%;
        justify-content: center;
        align-items: center;
      }
      svg {
        margin-top: 10px;
      }
      .manual-action-box {
        margin-left: 15px;
        text-align: left;
        p {
          margin: 0;
          font-size: 12px;
          line-height: 14px;
          color: #a4a4a4;
          &:first-child {
            color: #555555;
            font-size: 14px;
            margin-bottom: 5px;
          }
        }
      }
    }
  }
}
</style>
