<template>
  <div>
    <section class="hl_wrapper">
      <section class="hl_wrapper--inner hl_social" id="social">
        <div class="container-fluid">
          <!-- <button type="button" class="btn btn-primary" @click.stop="showNewPostModal=true;">
                        Edit Post Modal
                    </button> -->
          <div class="card hl_social--overview-table">
            <div class="card-header">
              <h2>Social Media Overview</h2>
            </div>
            <div class="card-body --no-padding">
              <div class="table-wrap">
                <table class="table table-sort">
                  <thead>
                    <tr>
                      <th>
                        <!--Social Media-->
                      </th>
                      <th>Total Audience</th>
                      <th>Active Audience</th>
                      <th>Engagement</th>
                      <th>Stories Created</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-if="facebookConnection">
                      <td>
                        <div class="table_social-icon">
                          <span class="icon --facebook">
                            <i class="fab fa-facebook-f"></i>
                          </span>
                          <p v-if="facebookConnection">
                            {{ facebookPageInfo.name }}
                          </p>
                          <p v-else-if="!facebookConnection">
                            Not Connected (Connect Now!)
                          </p>
                        </div>
                      </td>
                      <td>
                        <p class="table_social-total">
                          {{ facebookPageInfo.fan_count }}
                          <!-- <span>+0,1%</span> -->
                        </p>
                      </td>
                      <td>
                        <p class="table_social-total">
                          {{ twitterActiveUserCount }}
                          <!-- <span>+0,1%</span> -->
                        </p>
                      </td>
                      <td>
                        <p class="table_social-total">
                          {{ twitterEngagement }}
                          <!-- <span>+0,1%</span> -->
                        </p>
                      </td>
                      <td>
                        <p class="table_social-total">
                          {{ twitterStories }}
                          <!-- <span>+0,1%</span> -->
                        </p>
                      </td>
                    </tr>
                    <!-- <tr>
                                            <td>
                                                <div class="table_social-icon">
                                                    <span class="icon --google-plus">
                                                        <i class="fab fa-google-plus"></i>
                                                    </span>
                                                    <p>Test Tech</p>
                                                </div>
                                            </td>
                                            <td>
                                                <p class="table_social-total">1
                                                    <span>+0,1%</span>
                                                </p>
                                            </td>
                                            <td>
                                                <p class="table_social-total">1
                                                    <span>+0,1%</span>
                                                </p>
                                            </td>
                                            <td>
                                                <p class="table_social-total">1
                                                    <span>+0,1%</span>
                                                </p>
                                            </td>
                                            <td>
                                                <p class="table_social-total">1
                                                    <span>+0,1%</span>
                                                </p>
                                            </td>
                                        </tr> -->
                    <tr>
                      <td>
                        <div class="table_social-icon">
                          <span class="icon --instagram">
                            <i class="fab fa-instagram"></i>
                          </span>
                          <p>Test Tech</p>
                        </div>
                      </td>
                      <td>
                        <p class="table_social-total">
                          1
                          <!-- <span>+0,1%</span> -->
                        </p>
                      </td>
                      <td>
                        <p class="table_social-total">
                          1
                          <!-- <span>+0,1%</span> -->
                        </p>
                      </td>
                      <td>
                        <p class="table_social-total">
                          1
                          <!-- <span>+0,1%</span> -->
                        </p>
                      </td>
                      <td>
                        <p class="table_social-total">
                          1
                          <!-- <span>+0,1%</span> -->
                        </p>
                      </td>
                    </tr>
                    <tr v-if="linkedInConnection">
                      <td>
                        <div class="table_social-icon">
                          <span class="icon --linkedin">
                            <i class="fab fa-linkedin"></i>
                          </span>

                          <p v-if="linkedInConnection">
                            {{ linkedInProfile.name }}
                          </p>
                        </div>
                      </td>
                      <td>
                        <p class="table_social-total">
                          {{ linkedInTotalAudience }}
                          <!-- <span>+0,1%</span> -->
                        </p>
                      </td>
                      <td>
                        <p class="table_social-total">
                          {{ linkedInActiveAudience }}
                          <!-- <span>+0,1%</span> -->
                        </p>
                      </td>
                      <td>
                        <p class="table_social-total">
                          {{ linkedInEngagement }}
                          <!-- <span>+0,1%</span> -->
                        </p>
                      </td>
                      <td>
                        <p class="table_social-total">
                          {{ linkedInShares }}
                          <!-- <span>+0,1%</span> -->
                        </p>
                      </td>
                    </tr>
                    <tr v-if="twitterConnection">
                      <td>
                        <div class=" table_social-icon ">
                          <span class="icon --twitter ">
                            <i class="fab fa-twitter"></i>
                          </span>
                          <p>{{ twitterUser.name }}</p>
                        </div>
                      </td>
                      <td>
                        <p class="table_social-total ">
                          {{ twitterUser.followers_count }}
                          <!-- <span>+0,1%</span> -->
                        </p>
                      </td>
                      <td>
                        <p class="table_social-total ">
                          {{ twitterActiveUserCount }}
                          <!-- <span>+0,1%</span> -->
                        </p>
                      </td>
                      <td>
                        <p class="table_social-total ">
                          {{ twitterEngagement }}
                          <!-- <span>+0,1%</span> -->
                        </p>
                      </td>
                      <td>
                        <p class="table_social-total ">
                          {{ twitterStories }}
                          <!-- <span>+0,1%</span> -->
                        </p>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div class="hl_controls ">
            <div class="hl_controls--left ">
              <h2>Recent Posts</h2>
            </div>
          </div>
          <div class="hl_social--recent-posts row ">
            <FacebookPost
              v-for="post in facebookPosts"
              :post_id="post.id"
              :postStats="getFbPostStats(post.id)"
              :name="facebookPageInfo.name"
              :picture="facebookPicture"
              :key="post.id"
              v-bind:index="post.id"
            />
            <TwitterPost
              v-for="post in twitterData"
              :post="post"
              :name="twitterUser.name"
              :picture="twitterUser.profile_image_url_https"
              :key="post.id"
              v-bind:index="post.id"
            />
            <LinkedInPost
              v-for="post in linkedInUpdates"
              :post="post"
              :name="linkedInProfile.name"
              :picture="linkedInPicture"
              :key="post.id"
              v-bind:index="post.id"
            />
            <!-- <div class="col-md-6 ">
                            <div class="hl_social--post-item card ">
                                <div class="card-header ">
                                    <div class="avatar ">
                                        <div class="avatar_img ">
                                            <img src="../../../assets/pmd/img/img-avatar-sample3.png " alt="Avatar Name ">
                                            <span class="icon --facebook ">
                                                <i class="fab fa-facebook-f "></i>
                                            </span>
                                        </div>
                                        <div class="avatar_text ">
                                            <h4>
                                                <strong>Test Tech</strong> via
                                                <span>Facebook</span>
                                            </h4>
                                            <p>21 Jun 2018
                                                <span>9.30 AM</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body ">
                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam efficitur luctus lobortis. Nulla in lobortis nisl, id dignissim lectus. Aenean a velit non dui ornare volutpat.</p>
                                    <div class="hl_social--post-item-details ">
                                        <ul class="list-inline ">
                                            <li class="list-inline-item ">
                                                <i class="fas fa-thumbs-up "></i> 122</li>
                                            <li class="list-inline-item ">
                                                <i class="fas fa-comment "></i> 135</li>
                                            <li class="list-inline-item ">
                                                <i class="fas fa-retweet "></i> 000</li>
                                        </ul>
                                        <a href="# ">Details</a>
                                    </div>
                                </div>
                                <div class="card-footer ">
                                    <div class="hl_social--post-footer-item ">
                                        <h4>Potential Reach</h4>
                                        <p>0</p>
                                    </div>
                                    <div class="hl_social--post-footer-item ">
                                        <h4>Engagement</h4>
                                        <p>0</p>
                                    </div>
                                    <div class="hl_social--post-footer-item ">
                                        <h4>Stories Created</h4>
                                        <p>0</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 ">
                            <div class="hl_social--post-item card ">
                                <div class="card-header ">
                                    <div class="avatar ">
                                        <div class="avatar_img ">
                                            <img src="../../../assets/pmd/img/img-avatar-sample1.png " alt="Avatar Name ">
                                            <span class="icon --google-plus ">
                                                <i class="fab fa-google-plus-g "></i>
                                            </span>
                                        </div>
                                        <div class="avatar_text ">
                                            <h4>
                                                <strong>Test Tech</strong> via
                                                <span>Google+</span>
                                            </h4>
                                            <p>21 Jun 2018
                                                <span>9.30 AM</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body ">
                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam efficitur luctus lobortis. Nulla in lobortis nisl, id dignissim lectus. Aenean a velit non dui ornare volutpat.</p>
                                    <div class="hl_social--post-item-details ">
                                        <ul class="list-inline ">
                                            <li class="list-inline-item ">
                                                <i class="fas fa-thumbs-up "></i> 122</li>
                                            <li class="list-inline-item ">
                                                <i class="fas fa-comment "></i> 135</li>
                                            <li class="list-inline-item ">
                                                <i class="fas fa-retweet "></i> 000</li>
                                        </ul>
                                        <a href="# ">Details</a>
                                    </div>
                                </div>
                                <div class="card-footer ">
                                    <div class="hl_social--post-footer-item ">
                                        <h4>Potential Reach</h4>
                                        <p>0</p>
                                    </div>
                                    <div class="hl_social--post-footer-item ">
                                        <h4>Engagement</h4>
                                        <p>0</p>
                                    </div>
                                    <div class="hl_social--post-footer-item ">
                                        <h4>Stories Created</h4>
                                        <p>0</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 ">
                            <div class="hl_social--post-item card ">
                                <div class="card-header ">
                                    <div class="avatar ">
                                        <div class="avatar_img ">
                                            <img src="../../../assets/pmd/img/img-avatar-sample2.png " alt="Avatar Name ">
                                            <span class="icon --twitter ">
                                                <i class="fab fa-twitter "></i>
                                            </span>
                                        </div>
                                        <div class="avatar_text ">
                                            <h4>
                                                <strong>Test Tech</strong> via
                                                <span>Twitter</span>
                                            </h4>
                                            <p>21 Jun 2018
                                                <span>9.30 AM</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body ">
                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam efficitur luctus lobortis. Nulla in lobortis nisl, id dignissim lectus. Aenean a velit non dui ornare volutpat.</p>
                                    <div class="hl_social--post-item-details ">
                                        <ul class="list-inline ">
                                            <li class="list-inline-item ">
                                                <i class="fas fa-thumbs-up "></i> 122</li>
                                            <li class="list-inline-item ">
                                                <i class="fas fa-comment "></i> 135</li>
                                            <li class="list-inline-item ">
                                                <i class="fas fa-retweet "></i> 000</li>
                                        </ul>
                                        <a href="# ">Details</a>
                                    </div>
                                </div>
                                <div class="card-footer ">
                                    <div class="hl_social--post-footer-item ">
                                        <h4>Potential Reach</h4>
                                        <p>0</p>
                                    </div>
                                    <div class="hl_social--post-footer-item ">
                                        <h4>Engagement</h4>
                                        <p>0</p>
                                    </div>
                                    <div class="hl_social--post-footer-item ">
                                        <h4>Stories Created</h4>
                                        <p>0</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 ">
                            <div class="hl_social--post-item card ">
                                <div class="card-header ">
                                    <div class="avatar ">
                                        <div class="avatar_img ">
                                            <img src="../../../assets/pmd/img/img-avatar-sample4.png " alt="Avatar Name ">
                                            <span class="icon --linkedin ">
                                                <i class="fab fa-linkedin-in "></i>
                                            </span>
                                        </div>
                                        <div class="avatar_text ">
                                            <h4>
                                                <strong>Test Tech</strong> via
                                                <span>LinkedIn</span>
                                            </h4>
                                            <p>21 Jun 2018
                                                <span>9.30 AM</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body ">
                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam efficitur luctus lobortis. Nulla in lobortis nisl, id dignissim lectus. Aenean a velit non dui ornare volutpat.</p>
                                    <div class="hl_social--post-item-details ">
                                        <ul class="list-inline ">
                                            <li class="list-inline-item ">
                                                <i class="fas fa-thumbs-up "></i> 122</li>
                                            <li class="list-inline-item ">
                                                <i class="fas fa-comment "></i> 135</li>
                                            <li class="list-inline-item ">
                                                <i class="fas fa-retweet "></i> 000</li>
                                        </ul>
                                        <a href="# ">Details</a>
                                    </div>
                                </div>
                                <div class="card-footer ">
                                    <div class="text-center ">
                                        <em>Additional stats unavailable for this posts.</em>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 ">
                            <div class="hl_social--post-item card ">
                                <div class="card-header ">
                                    <div class="avatar ">
                                        <div class="avatar_img --yellow ">
                                            CS
                                            <span class="icon --instagram ">
                                                <i class="fab fa-instagram "></i>
                                            </span>
                                        </div>
                                        <div class="avatar_text ">
                                            <h4>
                                                <strong>Test Tech</strong> via
                                                <span>Instagram</span>
                                            </h4>
                                            <p>21 Jun 2018
                                                <span>9.30 AM</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body ">
                                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam efficitur luctus lobortis. Nulla in lobortis nisl, id dignissim lectus. Aenean a velit non dui ornare volutpat.</p>
                                    <div class="hl_social--post-item-details ">
                                        <ul class="list-inline ">
                                            <li class="list-inline-item ">
                                                <i class="fas fa-thumbs-up "></i> 122</li>
                                            <li class="list-inline-item ">
                                                <i class="fas fa-comment "></i> 135</li>
                                            <li class="list-inline-item ">
                                                <i class="fas fa-retweet "></i> 000</li>
                                        </ul>
                                        <a href="# ">Details</a>
                                    </div>
                                </div>
                                <div class="card-footer ">
                                    <div class="hl_social--post-footer-item ">
                                        <h4>Potential Reach</h4>
                                        <p>0</p>
                                    </div>
                                    <div class="hl_social--post-footer-item ">
                                        <h4>Engagement</h4>
                                        <p>0</p>
                                    </div>
                                    <div class="hl_social--post-footer-item ">
                                        <h4>Stories Created</h4>
                                        <p>0</p>
                                    </div>
                                </div>
                            </div>
                        </div> -->
          </div>
        </div>
      </section>
      <!-- END of .hl_social -->
    </section>
    <NewPostModal
      :showModal.sync="showNewPostModal"
      @hidden="showNewPostModal = false"
    />
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import {
  Company,
  Location,
  User,
  Notification,
  NotificationType,
  OAuth2
} from '@/models'
import firebase from 'firebase/app'
import moment from 'moment-timezone'

import FacebookPost from './FacebookPost.vue'
import TwitterPost from './TwitterPost.vue'
import LinkedInPost from './LinkedInPost.vue'
import NewPostModal from '../../components/social_media/NewPostModal.vue'

export default Vue.extend({
  components: {
    FacebookPost,
    TwitterPost,
    LinkedInPost,
    NewPostModal
  },
  data() {
    return {
      currentLocationId: '',
      facebookConnection: undefined as OAuth2 | undefined,
      twitterConnection: undefined as OAuth2 | undefined,
      linkedInConnection: undefined as OAuth2 | undefined,
      googleConnection: {} as OAuth2,
      twitterData: [],
      twitterFollowers: [],
      linkedInStats: {},
      linkedInProfile: {},
      location: {} as Location,
      facebookPageInfo: {},
      facebookPageStats: [],
      linkedInUpdates: [],
      linkedInPicture: '' as string,
      showNewPostModal: false as boolean
    }
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.currentLocationId = id
    }
  },
  computed: {
    twitterUser(): {} | undefined {
      if (this.twitterData && this.twitterData.length > 0) {
        return this.twitterData[0].user
      } else {
        return {}
      }
    },
    twitterEngagement(): number {
      let count = 0

      if (this.twitterData && this.twitterData.length > 0) {
        this.twitterData.forEach(tweet => {
          count += tweet.favorite_count
        })
      }
      return count
    },
    twitterStories(): number {
      let count = 0

      if (this.twitterData && this.twitterData.length > 0) {
        this.twitterData.forEach(tweet => {
          count += tweet.retweet_count
        })
      }
      return count
    },
    twitterActiveUserCount(): number {
      let count = 0
      if (
        this.twitterFollowers &&
        this.twitterFollowers.users &&
        this.twitterFollowers.users.length > 0
      ) {
        this.twitterFollowers.users.forEach(follower => {
          if (follower.status) {
            let created_at = moment(follower.status.created_at)
            let now = moment()
            if (
              created_at.year() === now.year() &&
              created_at.month() === now.month()
            ) {
              count++
            }
          }
        })
      }
      return count
    },
    linkedInTotalAudience(): number {
      if (
        this.linkedInStats &&
        this.linkedInStats.statusUpdateStatistics &&
        this.linkedInStats.statusUpdateStatistics.viewsByMonth &&
        this.linkedInStats.statusUpdateStatistics.viewsByMonth.values
      ) {
        return this.linkedInStats.statusUpdateStatistics.viewsByMonth.values[0]
          .impressions
      } else {
        return 0
      }
    },
    linkedInActiveAudience(): number {
      if (
        this.linkedInStats &&
        this.linkedInStats.statusUpdateStatistics &&
        this.linkedInStats.statusUpdateStatistics.viewsByMonth &&
        this.linkedInStats.statusUpdateStatistics.viewsByMonth.values
      ) {
        return (
          this.linkedInStats.statusUpdateStatistics.viewsByMonth.values[0]
            .likes +
          this.linkedInStats.statusUpdateStatistics.viewsByMonth.values[0]
            .shares
        )
      } else {
        return 0
      }
    },
    linkedInEngagement(): number {
      if (
        this.linkedInStats &&
        this.linkedInStats.statusUpdateStatistics &&
        this.linkedInStats.statusUpdateStatistics.viewsByMonth &&
        this.linkedInStats.statusUpdateStatistics.viewsByMonth.values
      ) {
        return this.linkedInStats.statusUpdateStatistics.viewsByMonth.values[0]
          .clicks
      } else {
        return 0
      }
    },
    linkedInShares(): number {
      if (
        this.linkedInStats &&
        this.linkedInStats.statusUpdateStatistics &&
        this.linkedInStats.statusUpdateStatistics.viewsByMonth &&
        this.linkedInStats.statusUpdateStatistics.viewsByMonth.values
      ) {
        return this.linkedInStats.statusUpdateStatistics.viewsByMonth.values[0]
          .shares
      } else {
        return 0
      }
    },
    facebookPosts(): any[] {
      if (this.facebookPageStats && this.facebookPageStats.data) {
        return this.facebookPageStats.data
      }
    },
    facebookPicture(): string {
      if (this.facebookPageInfo && this.facebookPageInfo.picture) {
        return this.facebookPageInfo.picture.data.url
      }
    }
  },
  async created() {
    var _self = this
    _self.currentLocationId = _self.$router.currentRoute.params.location_id

    this.location = new Location(
      await this.$store.dispatch('locations/getById', this.currentLocationId)
    )

    this.$private.cancelGoogleSubscription = OAuth2.getByLocationIdAndType(
      _self.currentLocationId,
      OAuth2.TYPE_GOOGLE
    ).onSnapshot((snapshot: firebase.firestore.QuerySnapshot) => {
      this.googleConnection = snapshot.docs.map(
        (d: firebase.firestore.QueryDocumentSnapshot) => new OAuth2(d)
      )[0]
    })

    this.$private.cancelFacebookSubscription = OAuth2.getByLocationIdAndType(
      _self.currentLocationId,
      OAuth2.TYPE_FACEBOOK
    ).onSnapshot(async (snapshot: firebase.firestore.QuerySnapshot) => {
      this.facebookConnection = snapshot.docs.map(
        (d: firebase.firestore.QueryDocumentSnapshot) => new OAuth2(d)
      )[0]
      let resp = await this.$http.get(
        '/api/facebook/get_facebook_page_stats?location_id=' +
          _self.currentLocationId +
          '&page_name=' +
          _self.location.facebookPageId
      )
      if (resp && resp.data) {
        _self.facebookPageStats = resp.data
      }

      resp = await this.$http.get(
        '/api/facebook/get_page_info?location_id=' + _self.currentLocationId
      )
      if (resp && resp.data) {
        _self.facebookPageInfo = resp.data
      }
    })

    this.$private.cancelTwitterSubscription = OAuth2.getByLocationIdAndType(
      _self.currentLocationId,
      OAuth2.TYPE_TWITTER
    ).onSnapshot(async (snapshot: firebase.firestore.QuerySnapshot) => {
      this.twitterConnection = snapshot.docs.map(
        (d: firebase.firestore.QueryDocumentSnapshot) => new OAuth2(d)
      )[0]
      if (this.twitterConnection) {
        let resp = await this.$http.get(
          '/api/twitter/timeline?location_id=' + _self.currentLocationId
        )
        if (resp && resp.data) {
          _self.twitterData = resp.data
        }

        resp = await this.$http.get(
          '/api/twitter/followers?location_id=' + _self.currentLocationId
        )
        if (resp && resp.data) {
          _self.twitterFollowers = resp.data
        }
      }
    })
    this.$private.cancelLinkedInSubscription = OAuth2.getByLocationIdAndType(
      _self.currentLocationId,
      OAuth2.TYPE_LINKEDIN
    ).onSnapshot(async (snapshot: firebase.firestore.QuerySnapshot) => {
      this.linkedInConnection = snapshot.docs.map(
        (d: firebase.firestore.QueryDocumentSnapshot) => new OAuth2(d)
      )[0]
      let resp = await this.$http.get(
        '/api/linkedin/get_company_stats?location_id=' + _self.currentLocationId
      )
      if (resp && resp.data) {
        _self.linkedInStats = resp.data
      }

      resp = await this.$http.get(
        '/api/linkedin/get_company_profile?location_id=' +
          _self.currentLocationId
      )
      if (resp && resp.data) {
        _self.linkedInProfile = resp.data
      }

      resp = await this.$http.get(
        '/api/linkedin/get_company_updates?location_id=' +
          _self.currentLocationId
      )
      if (resp && resp.data) {
        _self.linkedInUpdates = resp.data.values
      }

      resp = await this.$http.get(
        '/api/linkedin/get_company_logo?location_id=' + _self.currentLocationId
      )
      if (resp && resp.data) {
        _self.linkedInPicture = resp.data
      }
    })
  },
  methods: {
    getFbPostStats(postId: string) {
      if (
        this.facebookPageStats &&
        this.facebookPageStats.data &&
        this.facebookPageStats.data.length > 0
      ) {
        return lodash.find(this.facebookPageStats.data, { id: postId })
      }
    }
  },
  beforeDestroy() {
    if (this.$private.cancelGoogleSubscription) {
      this.$private.cancelGoogleSubscription()
    }

    if (this.$private.cancelFacebookSubscription) {
      this.$private.cancelFacebookSubscription()
    }

    if (this.$private.cancelTwitterSubscription) {
      this.$private.cancelTwitterSubscription()
    }

    if (this.$private.cancelLinkedInSubscription) {
      this.$private.cancelLinkedInSubscription()
    }
  }
})
</script>
