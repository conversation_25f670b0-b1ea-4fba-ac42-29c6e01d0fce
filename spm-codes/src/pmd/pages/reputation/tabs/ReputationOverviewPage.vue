<template>
  <div class="hl_reputation_settings" >
    <template v-if="user && user.permissions.reviews_enabled !== false">
      <div class="rows">
        <section>
          <ReviewRequestGoal />
        </section>
        <section>
          <ReviewsCard />
        </section>
      </div>
      <div class="rows">
        <section>
          <ReviewDetailCard />
        </section>
        <section>
          <ReviewSentiment />
        </section>
      </div>
      <div class="rows">
        <section>
          <InvitesTrendComponent />
        </section>
        <section>
          <ReviewsTrendComponent />
        </section>
      </div>
    </template>
    <div class="rows" v-if="user && user.permissions.reviews_enabled !== false">
      <section>
        <LatestReviewRequestsComponent />
      </section>
      <section>
        <LatestReviewsComponent />
      </section>
    </div>
  </div>
</template>

<script lang="ts">
// @ts-nocheck
import Vue from 'vue'
import { Location, User } from '@/models'
import { UserState } from '../../../store/state_models'
import { mapState } from 'vuex'

import ReviewRequestGoal from '../../../components/reputation/overview/ReviewRequestGoal.vue'
import ReviewsCard from '../../../components/reputation/overview/ReviewsCard.vue'
import ReviewDetailCard from '../../../components/reputation/overview/ReviewDetailCard.vue'
import ReviewSentiment from '../../../components/reputation/overview/ReviewSentiment.vue'
import InvitesTrendComponent from '../../../components/reputation/overview/InvitesTrendComponent.vue'
import ReviewsTrendComponent from '../../../components/reputation/overview/ReviewsTrendComponent.vue'
import LatestReviewsComponent from '../../../components/reputation/overview/LatestReviewsComponent.vue'
import LatestReviewRequestsComponent from '../../../components/reputation/overview/LatestReviewRequestsComponent.vue'

export default Vue.extend({
  data() {
    return {
      currentLocationId: '',
      location: undefined as undefined | Location,
    }
  },
  components: {
    ReviewRequestGoal,
    ReviewsCard,
    ReviewDetailCard,
    ReviewSentiment,
    InvitesTrendComponent,
    ReviewsTrendComponent,
    LatestReviewsComponent,
    LatestReviewRequestsComponent,
  },
  props: {},
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.fetchData()
    },
  },

  methods: {
    hasPermission(option: string): boolean {
      let show = false
      if (!this.user) return show
      switch (option) {
        case 'reviews': {
          show =
            this.userHasPermission('reviews_enabled') &&
            this.locationHasPermission('reviews_enabled')
          break
        }
      }
      return show
    },

    locationHasPermission(key: string): boolean {
      if (!this.location) return false
      if (!this.location.permissions) {
        return true
      }

      const permissions = this.location.permissions
      return permissions[key] === false ? false : true
    },
    userHasPermission(key: string): boolean {
      const globalPermission = this.user.permissions
      if (globalPermission[key] === false) {
        return false
      }
      return true
    },

    async fetchData() {
      this.location = new Location(
        lodash.cloneDeep(
          await this.$store.dispatch(
            'locations/getById',
            this.currentLocationId
          )
        )
      )

      this.$store.dispatch(
        'reviewRequestAggregate/lastSixMonthMonthly',
        this.currentLocationId
      )
      this.$store.dispatch(
        'reviewAggregate/lastSixMonthMonthly',
        this.currentLocationId
      )

      this.$store.dispatch(
        'reviewRequestAggregate/fetchLast6Months',
        this.currentLocationId
      )

      this.$store.dispatch(
        'reviewRequestAggregate/fetchThisMonthWeekly',
        this.currentLocationId
      )

      this.$store.dispatch(
        'reviewAggregate/fetchLast6Months',
        this.currentLocationId
      )

      this.$store.dispatch(
        'reviewAggregate/thisMonthWeekly',
        this.currentLocationId
      )
    },
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchData()
  },
  mounted() {},
  updated() {},
})
</script>
<style lang="scss" scoped>
.rows {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 15px;
  margin-bottom: 15px;
}

.card {
  height: 100%;
}
</style>
