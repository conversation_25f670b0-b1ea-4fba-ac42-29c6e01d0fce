<template>
  <div class="twillio-section form-group mb-0">
    <label>Call from</label>
    <div class="callFromSection">
      <div class="call-dropDown">
        <select
          class="selectpicker dropdown"
          v-model="dialOutNumber"
          v-validate="'required'"
          name="dialOutNumber"
          data-vv-as="DialOutNumber"
        >
          <option
            v-for="number in phoneNumbers"
            v-bind:key="number.phone_number"
            :value="number.phone_number"
          >
            <PhoneNumber
              type="display"
              v-model="number.friendly_name"
              :currentLocationId="currentLocationId"
            />
          </option>
        </select>
      </div>
      <div class="call-wrapper">
        <button
            type="button"
            class="btn call-btn"
            role="button"
            @click="makeCall"
            :disabled="isDisabled"
        >
            <i :class="iconName"></i>
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import PhoneNumber from '@/pmd/components/util/PhoneNumber.vue'

export default Vue.extend({
  props: ['currentLocationId', 'phoneNumbers', 'iconName', 'isDisabled'],
  components: { PhoneNumber },
  computed: {
    dialOutNumber: {
      // getter
      get: function(): string | undefined {
        return this.$store.state.numbers.dialOutNumber
      },
      //setter
      set: function(newValue: string | undefined) {
        if (newValue) {
          this.$store.commit('numbers/setDialOutNumber', newValue)
        }
      }
    },
  },
  methods: {
    makeCall() {
      this.$emit('makeCall')
    }
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
})
</script>

<style lang="scss" scoped>
.twillio-section{
  label {
    font-size: 12px;
    line-height: 14px;
    color: #607179;
    font-weight: normal;
  }
   .callFromSection {
    display: flex;
    align-items: center;
    .call-dropDown {
      flex: 1;
      .dropdown-toggle {
      background: transparent;
      }
      .dropdown{
        display: flex;
        width: 100%;
        background: #FFFFFF;
        border: 1px solid #EBEFF2;
        box-sizing: border-box;
        border-radius: 5px;
        height: 37px !important;
        }
    }
    .call-wrapper {
       margin-left: 10px;
      .call-btn {
      background: #27AE60;
      border-radius: 35px;
      width: 82px;
      height: 37px;
      padding: 7px 20px;
      font-size: 16px;
       }
    }
  }

}
 .bootstrap-select > .btn.dropdown-toggle .filter-option{
    padding-left: 10px
  }
</style>
