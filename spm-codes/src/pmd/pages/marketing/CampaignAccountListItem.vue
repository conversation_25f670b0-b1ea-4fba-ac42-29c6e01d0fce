<template>
  <router-link
    :to=" {name: getSideBarVersion == 'v2' ? 'contact_detail-v2' : 'contact_detail' , params: {contact_id: status.contactId}}"
    tag="tr"
    :class="(contact != undefined) ? 'pointer' : 'pointer-none'"
    v-bind:key="status.id"
    :id="status.id"
  >
    <td
      :class="(contact != undefined) ? 'pointer' : 'pointer-none'"
      @click.prevent="$router.push({name: getSideBarVersion == 'v2' ? 'contact_detail-v2' : 'contact_detail' , params: {contact_id: status.contactId}})"
    >
      <Avatar :contact="contact" :include_name="true"/>
    </td>
    <td>{{status.dateAdded.tz(timezone).format(getCountryDateFormat('extended-normal'))}}</td>
    <td><InternalSourceLink :object="status" /></td>
    <td v-if="contact == undefined && status.status == 'running'">Cancelled</td>
    <td v-else>
      <span>
        {{status.status | toTitleCase}}
        <span
          v-if="status && status.executionLog"
          v-b-tooltip.hover
          :title="Object.values(status.executionLog).join('\n')"
        >
					<i class="fas fa-question-circle" style="color: #afb8bc"></i>
        </span>
      </span>
    </td>
    <td>
      <span v-if="user">
        <Avatar :contact="user" :include_name="true" size="sm"/>
      </span>
    </td>
    <td>
      <span v-if="executionTime">{{executionTime.tz(timezone).format(getCountryDateFormat('normal'))}}</span>
    </td>
    <td>
      <div class="table_progress">
        <p>
          <strong class="--positive">{{getCurrentPosition(status)}}</strong>
          / {{getTotal(status)}}
        </p>
        <div class="progress --green">
          <div
            class="progress-bar"
            role="progressbar"
            :style="'width: ' + getProgress(status) + '%'"
          ></div>
        </div>
      </div>
    </td>
    <td>
      <i
        class="fa fa-running hint-icon showpointer-event"
        style="margin-right: 10px;"
        :class="{'hint-icon-disabled': processing ||  contact == undefined}"
        @click.prevent="runNow"
      />
      <i
        class="fa fa-redo hint-icon showpointer-event"
        style="margin-right: 10px;"
        :class="{'hint-icon-disabled': processing || !nextRunInPast || contact == undefined}"
        @click.prevent="redo"
      />
      <i
        :class="{'hint-icon-disabled': processing || status.status !== 'running' || contact == undefined}"
        style="margin-right: 10px;"
        class="fa fa-times-circle hint-icon showpointer-event"
        @click.prevent="cancel"
      />
      <i
        :class="{'hint-icon-disabled': processing || status.status === 'running'}"
        class="fa fa-trash-alt hint-icon showpointer-event"
        @click.prevent="trash"
      />
    </td>
  </router-link>
</template>

<script lang="ts">
import Vue from 'vue'
import { find, indexOf } from 'lodash'
import { CampaignStatus, TaskRequest, Contact, User, getCountryDateFormat } from '../../../models'
import Avatar from '../../components/Avatar.vue'
import InternalSourceLink from '../../components/util/InternalSourceLink.vue'
import * as moment from 'moment-timezone'
import { mapState } from 'vuex'
import { UserState } from '../../../store/state_models'

export default Vue.extend({
  components: { Avatar, InternalSourceLink },
  props: ['status', 'campaign', 'timezone'],
  data() {
    return {
      taskRequest: undefined as undefined | TaskRequest,
      contact: undefined as undefined | Contact,
      user: undefined as undefined | User,
      processing: false,
      getCountryDateFormat: getCountryDateFormat
    }
  },
  watch: {
    async status() {
      this.taskRequest = undefined
      this.processing = false
      if (this.status.taskId)
        this.taskRequest = await TaskRequest.getById(this.status.taskId)
      if (this.status.userId) {
        this.user = await this.$store.dispatch('agencyUsers/syncGet', this.status.userId)
      }
    }
  },
  async created() {
    if (this.status.contactId) {
      this.contact = await this.$store.dispatch('contacts/syncGet', this.status.contactId);
    }
    if (this.status.taskId)
      this.taskRequest = await TaskRequest.getById(this.status.taskId)
    if (this.status.userId) {
      this.user = await this.$store.dispatch('agencyUsers/syncGet', this.status.userId)
    }
  },
  computed: {
    ...mapState('user', {
      loggedInUser: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    executionTime(): moment.Moment | undefined {
      if (!this.taskRequest) return
      let time = lodash.get(this.taskRequest, 'payload.original.startTime')
      if (time) return moment(time.toMillis())
      else if (this.taskRequest.startTime) return this.taskRequest.startTime
    },
    nextRunInPast(): boolean {
      if (!this.taskRequest) return this.status.status === 'running'
      if (!this.taskRequest.startTime) return false
      if (this.taskRequest.startTime.isBefore(moment().subtract(30, 'minutes')))
        return true
      return false
    },
    debugURL(): string {
      return `/campaign/${this.status.campaignId}/status/${
        this.status.id
      }/execute`
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  methods: {
    async runNow() {
      try {
        this.processing = true
        const response = await this.$http.post(
          `/campaign/${this.status.campaignId}/status/${this.status.id}/runNow`
        )
        this.$emit('update', response.data)
      } catch (err) {
        console.log(err)
      }
      this.processing = false
    },
    loadDetailPage(id: string) {
      this.$router.push({ name: this.getSideBarVersion == 'v2' ? 'contact_detail-v2' : 'contact_detail', params: { contact_id: id } })
    },
    async cancel() {
      if (this.status.status !== 'running') return
      if (confirm('Are you sure you want to cancel?')) {
        try {
          this.processing = true
          const response = await this.$http.post(
            `/campaign/${this.status.campaignId}/status/${
              this.status.id
            }/cancel?user_id=${this.loggedInUser && this.loggedInUser.id}`
          )
          this.$emit('update', response.data)
        } catch (err) {
          console.log(err)
        }
        this.processing = false
      }
    },
    async redo() {
      if (!this.nextRunInPast) return
      if (confirm('Are you sure you want to retry?')) {
        try {
          this.processing = true
          const response = await this.$http.post(
            `/campaign/${this.status.campaignId}/status/${this.status.id}/redo`
          )
          this.$emit('update', response.data)
        } catch (err) {
          console.log(err)
        }
        this.processing = false
      }
    },
    async trash() {
      if (this.status.status === 'running') return
      if (confirm('Are you sure you want to delete?')) {
        try {
          this.processing = true
          const response = await this.$http.post(
            `/campaign/${this.status.campaignId}/status/${this.status.id}/trash`
          )
          this.$emit('delete', response.data)
        } catch (err) {
          console.log(err)
        }
        this.processing = false
      }
    },
    getProgress(campaignStatus: CampaignStatus) {
      if (campaignStatus.status === 'finished' && campaignStatus.templateId === 'completed') {
        return 100
      }

      const template = find(this.campaign.campaignData.templates, {
        id: campaignStatus.templateId
      })
      if (!template) return 0

      const total = this.campaign.campaignData.templates
        ? this.campaign.campaignData.templates.length || 1
        : 1
      let position = indexOf(this.campaign.campaignData.templates, template)
      position = position !== -1 ? position : 0
      return (position / total) * 100
    },
    getCurrentPosition(campaignStatus: CampaignStatus) {
      if (campaignStatus.status === 'finished' && campaignStatus.templateId === 'completed') {
        return this.campaign.campaignData.templates.length || 0
      }

      const template = find(this.campaign.campaignData.templates, {
        id: campaignStatus.templateId
      })
      if (!template) return 0

      let position = indexOf(this.campaign.campaignData.templates, template)
      position = position !== -1 ? position : 0

      return position
    },
    getTotal(campaignStatus: CampaignStatus) {
      return this.campaign.campaignData.templates
        ? this.campaign.campaignData.templates.length || 0
        : 0
    }
  }
})
</script>

<style scoped>
.pointer-none, .hint-icon-disabled {
  pointer-events: none !important;
}
.showpointer-event {
  pointer-events: auto;
  cursor: pointer;
}
</style>

