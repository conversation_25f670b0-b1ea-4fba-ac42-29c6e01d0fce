import { Module } from 'vuex'
import { RootState, FunnelState, Step } from '../store/state_models'
import { lazy } from '../util/lazy'
import Vue from 'vue'
import { ActiveUser } from '../models/editor_active_viewer'

export const funnel: Module<FunnelState, RootState> = {
  namespaced: true,
  state: {
    funnelSteps: {
      /* [id: number]: Item */
    },
    editorActiveViewers:[]
  },
  getters: {},
  mutations: {
    SET_FUNNEL_STEPS: (state, { steps, id }: { steps: Step[]; id: string }) => {
      if (steps) Vue.set(state.funnelSteps, id, steps)
    },
    SET_ACTIVE_EDITOR_VIEWER: (state, payload) => {
      state.editorActiveViewers = payload
    }
  },
  actions: {
    FETCH_FUNNEL_STEPS({ commit, state }, { id }) {
      return lazy(
        (steps: Step[]) => commit('SET_FUNNEL_STEPS', { steps, id }),
        () =>
          (this as any).$http
            .get(`/funnelbuilder/get_steps/${id}`)
            .then(data => data.data),
        Object.assign({}, state.funnelSteps[id]),
        true
      )
    },
    SET_ACTIVE_EDITOR_VIEWER({ commit, state },payload) {
      commit('SET_ACTIVE_EDITOR_VIEWER', payload)
    }
  }
}
