<template>
  <div class="form-group">
    <hr />
    <div><label class="my-2">Offers</label></div>
    <div class="dropdown bootstrap-select" ref="offers">
      <button
        id="dropdownMenuButton"
        @click.stop.prevent="moreDispToggler.toggle()"
        type="button"
        class="btn dropdown-toggle btn-light"
        data-toggle="dropdown"
        role="button"
        :disabled="isLoading || !remainingOffers || !remainingOffers.length">
        <div class="filter-option">
          <div class="filter-option-inner">
            <div class="filter-option-inner-inner">Select Offer</div>
          </div>
        </div>
      </button>
      <!-- <a
          data-toggle="dropdown"
          aria-haspopup="true"
          aria-expanded="false"
          v-if="!disabled">
          <label>Select Offer</label>
          <i class="fas fa-caret-down --light mx-2"></i>
        </a> -->
      <div
        class="dropdown-menu"
        :id="moreDispToggler.id"
        aria-labelledby="dropdownMenuButton"
        style="width:100%;  min-height:50px; max-height: 350px; overflow: auto;"
      >
        <span v-for="(item, index) in remainingOffers" :key="index">
          <a @click="pick(item)" class="dropdown-item">
            <span>{{ item.value }}</span>
          </a>
        </span>
      </div>
    </div>
    <template v-if="isLoading !== false">
      <div class="tag-group my-2">
        <div
          class="tag"
          style="height:28px; width: 70px;"
          :class="{ workinprogress2: isLoading }"
          v-for="idx in progressCount"
          v-bind:key="idx">
          <span></span>
        </div>
      </div>
    </template>
    <template v-else-if="displayOffers.length > 0">
      <div class="tag-group my-2">
        <div
          class="tag"
          v-for="item in displayOffers"
          v-bind:key="item.offerId"
          data-tooltip="tooltip"
          data-placement="top"
          v-b-tooltip.hover
          :title="item.products && item.products.length > 0 ? ['Products :', ...item.products.map(a => a.productName)].join('\n') : 'No Products For This Offer'"
        >
          <span>{{ item.offerName }}</span>
          <a @click="remove(item)" v-if="!disabled">
            <i class="icon icon-close"></i>
          </a>
        </div>
      </div>
      <button class="btn btn-link" @click="$bus.$emit('enable-membership-access-modal',true)">Change Password</button>
      <hr />
    </template>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Tag, Location, User, Contact } from '@/models'
import { UserState } from '@/store/state_models'
import { mapState } from 'vuex'
import { IKeyVal } from '../../pages/smartlist/vm/interfaces'
import { orderBy } from 'lodash'
import { SafeDropDownToggle } from '../../pages/smartlist/safe_drop_down_toggle'
import { ContactMembershipService } from '../../../util/contact_membership'
import { UxMessage } from '@/util/ux_message'

export default Vue.extend({
  name: 'displayOffers',
  props: {
    contact: Contact,
    inputClass: String,
    disabled: { default: false }
  },
  inject: ['uxmessage'],
  data() {
    return {
      currentLocationId: '',
      allOffers: [] as IKeyVal[],
      displayOffers: [] as any[],
      moreDispToggler: new SafeDropDownToggle(this, 'offersDispToggle'),
      remainingOffers: [] as IKeyVal[],
      currentContactOffers: [] as any[],
      isLoading: false,
      progressCount: 1,
    }
  },
  watch: {
    '$route.params.location_id': async function(id) {
      this.currentLocationId = id
      await this.$store.dispatch('membership/syncAll', {
        locationId: id,
        forceRefresh: false
      })
    },
    contact: function(newOne) {
      this.initView()
    }
  },
  methods: {
    async initView() {
      this.allOffers = [...(await this.$store.state.membership.offers)]
      this.updateOffersDisplay()
    },
    async refreshAllOffers() {
      await this.$store.dispatch('membership/syncAll', {
        locationId: this.currentLocationId,
        forceRefresh: false
      })
      this.initView()
    },
    async updateOffersDisplay() {
      try {
        this.isLoading = true
        this.currentContactOffers = [];
        this.displayOffers = [];
        this.remainingOffers = [];
        await this.$nextTick();
        if (this.contact && this.contact.id) this.currentContactOffers = await ContactMembershipService.fetchOffers(this.contact.id)
        const temp: IKeyVal[] = []
        this.displayOffers = orderBy(this.currentContactOffers, ['offerName'])
        this.remainingOffers = orderBy(
          this.allOffers.filter(a => this.currentContactOffers.findIndex(b => b.offerId === a.key) === -1),
          ['value']
        )
      } finally {
        this.isLoading = false
      }
    },
    async remove(item: { [key: string]: any }) {
      try {
        this.isLoading = true
        this.progressCount = this.displayOffers.length - 1 || 1
        if (
          (await ContactMembershipService.removeOffer(
            this.contact.id,
            item.offerId
          )) === true
        ) {
          // const idx = this.currentContactOffers.findIndex(a=> a === item.key)
          // if (idx > -1) this.currentContactOffers.splice(idx,1);
          await this.updateOffersDisplay()
          //this.$emit('change');
        } else {
          this.uxmessage(UxMessage.errorType('There was error removing the offer'))
        }
      } catch {
        this.isLoading = false
      }
    },
    async pick(item: IKeyVal) {
      try {
        this.isLoading = true
        this.progressCount = this.displayOffers.length + 1 || 1
        if (
          (await ContactMembershipService.addOffer(
            this.contact.id,
            item.key,
            false
          )) === true
        ) {
          //this.currentContactOffers.push(item.key)
          await this.updateOffersDisplay()
          //this.$emit('change');
        } else  if (!this.contact.email) {
          this.uxmessage(UxMessage.errorType('Contact requires email to add offers. There was error adding the offer',))
        } else {
          this.uxmessage(UxMessage.errorType('There was error adding the offer'))
        }

      } catch(err){
          this.uxmessage(UxMessage.errorType('There was error',err))
      }
      finally {
        this.isLoading = false
      }
    }
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
  },
  mounted() {
    this.refreshAllOffers()
  }
})
</script>
