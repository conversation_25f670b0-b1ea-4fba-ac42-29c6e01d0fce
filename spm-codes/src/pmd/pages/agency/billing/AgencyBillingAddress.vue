<template>
  <div class="hl_billing--method">
    <div class="card">
      <div class="card-header">
        <h3>Billing Address</h3>
      </div>
      <div class="card-body">
        <form data-vv-scope="form-2">
          <div class="form-group">
            <UITextInputGroup
              type="text"
              label="Address*"
              placeholder="Address"
              v-model="company.address"
              :disabled="!isAdmin"
              v-validate="'required'"
              name="address"
              :error="errors.has('form-2.address')"
              :errorMsg="'Address required'"
            />
          </div>
          <div class="row">
            <div class="col-sm-8">
              <div class="form-group">
                <UITextInputGroup
                  type="text"
                  label="City*"
                  placeholder="City"
                  v-model="company.city"
                  :disabled="!isAdmin"
                  v-validate="'required'"
                  name="city"
                  :error="errors.has('form-2.city')"
                  :errorMsg="'City required'"
                />
              </div>
            </div>
            <div class="col-sm-4">
              <div class="form-group">
                <UITextInputGroup
                  type="text"
                  label="Postal Code*"
                  placeholder="Postal Code"
                  v-model="company.postalCode"
                  :disabled="!isAdmin"
                  v-validate="'required'"
                  name="postalcode"
                  :error="errors.has('form-2.postalcode')"
                  :errorMsg="'Postal Code required'"
                />
              </div>
            </div>
          </div>
          <div
            class="form-group"
            v-if="company.country == 'US'"
            :hidden="company.country !== 'US'"
          >
            <UITextLabel>State / Prov / Region *</UITextLabel>
            <select
              class="selectpicker"
              title="State / Prov / Region"
              v-model="company.state"
              name="state"
              data-size="5"
              ref="stateSelect"
              :disabled="!isAdmin"
              v-validate="'required'"
            >
              <option value>Choose one..</option>
              <option value="AL">Alabama</option>
              <option value="AK">Alaska</option>
              <option value="AZ">Arizona</option>
              <option value="AR">Arkansas</option>
              <option value="CA">California</option>
              <option value="CO">Colorado</option>
              <option value="CT">Connecticut</option>
              <option value="DE">Delaware</option>
              <option value="DC">District Of Columbia</option>
              <option value="FL">Florida</option>
              <option value="GA">Georgia</option>
              <option value="HI">Hawaii</option>
              <option value="ID">Idaho</option>
              <option value="IL">Illinois</option>
              <option value="IN">Indiana</option>
              <option value="IA">Iowa</option>
              <option value="KS">Kansas</option>
              <option value="KY">Kentucky</option>
              <option value="LA">Louisiana</option>
              <option value="ME">Maine</option>
              <option value="MD">Maryland</option>
              <option value="MA">Massachusetts</option>
              <option value="MI">Michigan</option>
              <option value="MN">Minnesota</option>
              <option value="MS">Mississippi</option>
              <option value="MO">Missouri</option>
              <option value="MT">Montana</option>
              <option value="NE">Nebraska</option>
              <option value="NV">Nevada</option>
              <option value="NH">New Hampshire</option>
              <option value="NJ">New Jersey</option>
              <option value="NM">New Mexico</option>
              <option value="NY">New York</option>
              <option value="NC">North Carolina</option>
              <option value="ND">North Dakota</option>
              <option value="OH">Ohio</option>
              <option value="OK">Oklahoma</option>
              <option value="OR">Oregon</option>
              <option value="PA">Pennsylvania</option>
              <option value="RI">Rhode Island</option>
              <option value="SC">South Carolina</option>
              <option value="SD">South Dakota</option>
              <option value="TN">Tennessee</option>
              <option value="TX">Texas</option>
              <option value="UT">Utah</option>
              <option value="VT">Vermont</option>
              <option value="VA">Virginia</option>
              <option value="WA">Washington</option>
              <option value="WV">West Virginia</option>
              <option value="WI">Wisconsin</option>
              <option value="WY">Wyoming</option>
            </select>
            <span v-show="errors.has('form-2.state')" class="error"
              >State required</span
            >
          </div>
          <div class="form-group" :hidden="company.country === 'US'">
            <UITextInputGroup
              type="text"
              label="State / Prov / Region *"
              placeholder="State"
              v-model="company.state"
              name="state"
              :disabled="!isAdmin"
              v-validate="'required'"
              :error="errors.has('form-2.state')"
              :errorMsg="'State required'"
            />
          </div>
          <div class="form-group">
            <UITextLabel>Country*</UITextLabel>
            <select
              class="selectpicker"
              v-model="company.country"
              data-size="5"
              :disabled="!isAdmin"
              v-validate="'required'"
            >
              <option value>Choose country...</option>
              <option
                v-for="(country, value) in countries"
                :key="value"
                :value="value"
                v-text="country"
              ></option>
            </select>
            <span v-show="errors.has('form-2.country')" class="error"
              >Country required</span
            >
          </div>
        </form>
      </div>
      <div class="card-footer">
        <div>
          <div class="card-verify--success" v-if="addressUpdate">
            <i class="far fa-check-circle"></i> Your billing address has been
            updated
          </div>
        </div>
        <div>
          <UIButton
            type="button"
            :loading="saving"
            @click.stop="validateBeforeSubmit"
          >
            {{ 'Save' }}
          </UIButton>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import countries from '@/util/countries'
import { Company, User, UserState } from '@/models'

declare var $: any

export default Vue.extend({
  data() {
    return {
      company: {} as Company,
      saving: false,
      countries: countries,
      addressUpdate: false,
    }
  },
  computed: {
    isAdmin(): boolean {
      return this.user && this.user.role === 'admin'
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
  },
  methods: {
    async fetchData() {
      try {
        this.company = await Company.getById(this.user?.companyId)
      } catch (error) {
        console.log(error)
      }
    },
    async validateBeforeSubmit() {
      this.addressUpdate = false
      this.$validator.errors.clear()
      let result = null
      result = await this.$validator.validateAll('form-2')

      if (!result) {
        console.error('Correct them errors!', this.$validator.errors)
        return false
      }

      this.saving = true

      await this.company.save()
      await this.$http.put('/onboarding/customer_update')
      this.$root.$emit('billing_address_update')
      this.saving = false
      this.addressUpdate = true
    },
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
      if (
        this.company &&
        this.company.country == 'US' &&
        this.$refs.stateSelect
      ) {
        $(this.$refs.stateSelect).selectpicker('val', this.company.state)
      }
    }
  },
  created() {
    this.fetchData()
  },
  async mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
})
</script>
<style>
.card-verify--success{
  text-align: center;
  color: #35ca38;
}
.hl_billing--method .card .card-footer{
  justify-content: space-between;
  align-items: center;
}
</style>
