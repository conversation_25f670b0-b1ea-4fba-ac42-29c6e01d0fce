<template>
    <div>

        <SideBar/>
        <TopBar />

        <section class="hl_wrapper">

            <section class="hl_wrapper--inner customers" id="customers">
                <div class="container-fluid">

                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label>Address</label>
                                <input ref="autocomplete" placeholder="Search" class="search-location" onfocus="value = ''" type="text" style="width: 500px;" />
                            </div>
                            <div class="form-group">
                                <button type="button" class="btn btn-primary" @click="place_selected">Select</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </section>
    </div>
</template>

<script lang="ts">
import Vue from 'vue';
import TopBar from '../../components/TopBar.vue';
import SideBar from '../../components/agency/SideBar.vue';

var google: any;

export default Vue.extend({
    components: { TopBar, SideBar },
    data() {
        return {
            place: {} as any,
            autocomplete: {} as any,
        };
    },
    mounted() {
        var _self = this;
        const input: any = this.$refs.autocomplete;
        this.autocomplete = new google.maps.places.Autocomplete(input, {
            types: ['establishment', 'geocode'],
        });
        this.autocomplete.addListener('place_changed', () => {
            let place = this.autocomplete.getPlace();
            _self.place = place;
        });
    },
    methods: {
        place_selected() {
            this.$router.push({ name: 'account_add', params: { id: this.place.place_id } });
        },
    },
});
</script>