<template>
  <div
    class="modal fade hl_add_payment_method--modal"
    tabindex="-1"
    role="dialog"
    ref="modal"
  >
    <div class="modal-dialog" role="document">
      <div class="modal-content add-card-successful" v-if="addCardSuccessful">
        <i class="fa fa-times hide-modal" @click="hideModal"></i>
        <div class="modal-body">
          <div class="check">
            <i class="fa fa-check" aria-hidden="true"></i>
          </div>
          <h4>Card successfully added</h4>
          <button @click="hideModal" class="btn cancel">Okay</button>
        </div>
      </div>
      <div class="modal-content" v-else>
        <i class="fa fa-times hide-modal" @click="hideModal"></i>
        <div class="modal-header">
          <div class="title">Add Card</div>
        </div>
        <div class="modal-body">
          <div class="form-section">
            <p class="title">Card Details</p>
            <div class="input-container">
              <form action="/charge" method="post" id="add-card-form">
                <div id="card-element">
                  <!-- A Stripe Element will be inserted here. -->
                </div>

                <!-- Used to display form errors. -->
                <div id="card-errors" role="alert"></div>
              </form>
            </div>
            <!-- <p class="info">
              <i class="fa fa-info-circle"></i>
              <span
                >For verification, we will be charging your card
                {{ currencySymbol }}{{ verificationCharge }}, the amount will be
                credited to your wallet!</span
              ><br />
            </p> -->
          </div>
          <div class="form-section">
            <p class="title">Name</p>
            <div class="input-container name">
              <input
                type="text"
                name="name"
                v-model="nameOnCard"
                placeholder="Name on Card"
              />
            </div>
            <div class="default" v-if="!addingFirstCard">
              <div class="title">Make this card primary</div>
              <div class="toggle">
                <UIToggle
                  id="primary_card"
                  v-model="makePrimaryCard"
                />
                <label class="tgl-btn" for="primary_card"></label>
              </div>
            </div>
            <div v-if="error" class="error">{{ error }}</div>
          </div>
        </div>
        <div class="modal-footer">
          <UIButton @click="hideModal" use="outline">Cancel</UIButton>
          <UIButton :disabled="loading" @click="addCard" use="tertiary">
            {{ loading ? 'Adding...' : 'Add Card' }}
          </UIButton>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import config from '@/config'

declare var Stripe: any

export default Vue.extend({
  props: {
    showModal: {
      type: Boolean,
      default: false,
    },
    locationId: String,
    companyId: String,
    stripeAccountId: String,
    stripeCustomerId: String,
    addingFirstCard: {
      type: Boolean,
      default: false,
    },
    currencySymbol: String,
  },
  // computed: {
  //   verificationCharge(): number {
  //     if (this.addingFirstCard) {
  //       return 11
  //     } else {
  //       return 1
  //     }
  //   },
  // },
  data() {
    return {
      stripe: undefined as any,
      card: undefined as any,
      makePrimaryCard: false,
      nameOnCard: '',
      loading: false,
      error: '',
      addCardSuccessful: false,
    }
  },
  created() {
    this.setupStripe()
  },
  mounted() {
    $(this.$refs.modal).on('hidden.bs.modal', () => {
      this.hideModal()
    })
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  methods: {
    setupStripe() {
      let recaptchaScript = document.getElementById('stripeV3')

      if (recaptchaScript) {
        console.log('stripe already setup')
        this.stripe = Stripe(config.stripeKey, {
          stripeAccount: this.stripeAccountId,
        })
        return
      }

      recaptchaScript = document.createElement('script')
      recaptchaScript.setAttribute('id', 'stripeV3')

      recaptchaScript.onload = () => {
        this.stripe = Stripe(config.stripeKey, {
          stripeAccount: this.stripeAccountId,
        })
      }

      recaptchaScript.setAttribute('src', 'https://js.stripe.com/v3/')
      document.head.appendChild(recaptchaScript)
    },
    initCardElement() {
      const elements = this.stripe.elements()

      // Custom styling can be passed to options when creating an Element.
      // (Note that this demo uses a wider set of styles than the guide below.)
      const style = {
        base: {
          color: '#4A5568',
          padding: '15px 20px',
          fontFamily:
            'Roboto,system,-apple-system,BlinkMacSystemFont,".SFNSDisplay-Regular","Helvetica Neue",Helvetica,Arial,sans-serif',
          fontSmoothing: 'antialiased',
          fontSize: '14px',
          '::placeholder': {
            color: '#aab7c4',
          },
          backgroundColor: '#f3f8fb',
        },
        invalid: {
          color: '#fa755a',
          iconColor: '#fa755a',
        },
      }

      // Create an instance of the card Element.
      this.card = elements.create('card', { style: style })

      // Add an instance of the card Element into the `card-element` <div>.
      this.card.mount('#card-element')

      this.card.addEventListener('change', function (event: any) {
        var displayError =
          document.getElementById('card-errors') ||
          document.createElement('div')
        if (event.error) {
          displayError.textContent = event.error.message
        } else {
          displayError.textContent = ''
        }
      })
    },
    hideModal() {
      this.$emit('cancel')
    },
    async addCard() {
      this.loading = true
      this.error = ''

      try {
        const intent = await this.saasService.post(
          `/saas-config/${this.locationId}/setup-stripe-intent`,
          {
            stripeCustomerId: this.stripeCustomerId,
            stripeAccountId: this.stripeAccountId,
          }
        )

        const { error, setupIntent } = await this.stripe.confirmCardSetup(
          intent.data.client_secret,
          {
            payment_method: { card: this.card },
          }
        )

        if (error) {
          throw error
        }

        const existingPaymentMethod = await this.cardAlreadyExists(
          setupIntent.payment_method
        )
        if (existingPaymentMethod.exists) {
          throw new Error('Payment method already exists!')
        }

        const useExistingPaymentSource = !!existingPaymentMethod.paymentSource
        const paymentMethodId = useExistingPaymentSource
          ? existingPaymentMethod.paymentSource.id
          : setupIntent.payment_method

        await this.updateCard(paymentMethodId, useExistingPaymentSource)
      } catch (error) {
        console.error(error)
        this.error = error.message || 'Error while adding card!'
        this.loading = false
      }
    },
    async cardAlreadyExists(
      paymentMethodId: string
    ): Promise<{ exists: boolean; paymentSource: any | null }> {
      const {
        data: { exists, paymentSource },
      } = await this.saasService.post(
        `/location-payment-methods/${this.locationId}/payment-method-exists`,
        {
          paymentMethodId,
          stripeCustomerId: this.stripeCustomerId,
          stripeAccountId: this.stripeAccountId,
        }
      )

      return { exists, paymentSource }
    },
    async updateCard(
      paymentMethodId: string,
      useExistingPaymentSource: boolean
    ) {
      try {
        console.log('update card with payment method id --> ', paymentMethodId)
        // await this.$http.post('/api/stripe/add_card', {
        //   payment_method_id: paymentMethodId
        // })

        const resp = await this.saasService.post(
          `/location-payment-methods/${this.locationId}`,
          {
            companyId: this.companyId,
            paymentMethodId,
            stripeCustomerId: this.stripeCustomerId,
            stripeAccountId: this.stripeAccountId,
            setAsPrimary: this.addingFirstCard ? true : this.makePrimaryCard,
            nameOnCard: this.nameOnCard,
            // verificationCharge: this.verificationCharge,
            useExistingPaymentSource,
          }
        )

        console.log('got resp for add payment method --> ', resp)
        this.addCardSuccessful = true
        this.$emit('success')

        setTimeout(() => {
          this.hideModal()
        }, 2500)
      } catch (error) {
        console.log('error while adding card --> ', error)
        this.error = error?.response?.data?.msg || 'Failed to add card!'
      } finally {
        this.loading = false
        this.card.clear()
      }
    },
  },
  watch: {
    showModal(newValue, oldValue) {
      if (newValue && !oldValue) {
        if (this.card) {
          this.card.clear()
        }

        this.error = ''
        this.nameOnCard = ''
        this.loading = false
        this.addCardSuccessful = false
        $(this.$refs.modal).modal('show')

        setTimeout(() => {
          this.initCardElement()
        })
      } else if (!newValue && oldValue) {
        $(this.$refs.modal).modal('hide')
      }
    },
  },
})
</script>
<style scoped>
.modal-header {
  border: none;
  padding: 42px 32px;
}

.modal-header .title {
  font-size: 25px;
  line-height: 29px;
  color: #333333;
  font-weight: bold;
}

i.hide-modal {
  position: absolute;
  top: 16px;
  right: 16px;
  font-size: 16px;
  cursor: pointer;
  z-index: 2;
}

.modal-body {
  padding: 0 32px;
}

.modal-body p.title {
  font-size: 14px;
  line-height: 16px;
  color: #1a202c;
  font-weight: bold;
}

.modal-body .form-section {
  margin-bottom: 32px;
}

.modal-body .form-section .title {
  margin-bottom: 10px;
}

.modal-body .input-container {
  background: #ffffff;
  font-size: 20px;
  /* color: #cbd5e0; */
  display: flex;
  align-items: center;
}

#add-card-form {
  width: 100%;
}

#card-element {
  padding: 15px 20px;
  width: inherit;
  /* border: 1px solid #4263eb; */
  border-radius: 3px;
  background: #f3f8fb;
}

#card-errors {
  font-size: 14px;
  margin-top: 6px;
  color: red;
}

.modal-body .input-container.name {
  /* border: 1px solid #bcc7d3; */
  padding: 10px 15px;
  font-size: 0.875rem;
  border-radius: 3px;
  background: #f3f8fb;
}

.modal-body .input-container input {
  border: none;
  flex: 1;
  background: inherit;
}

.modal-body .input-container input:focus {
  outline: none;
  --tw-ring-shadow: none;
}

input:-internal-autofill-selected {
  background-color: inherit !important;
}

.modal-body .default {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 16px;
}

.modal-body .default .title {
  margin-right: 10px;
  font-size: 13px;
}

.modal-body .default .tgl-light:checked + .tgl-btn {
  background: #4c51bf;
}

.modal-body .default .tgl-light:checked + .tgl-btn:after {
  background: white;
}

.modal-body p.info i {
  margin-right: 6px;
}

.modal-footer {
  border: none;
  display: flex;
  padding: 32px;
}

.btn {
  font-size: 14px;
  border-radius: 3px;
  padding: 12px 16px;
  width: 155px;
}

.btn.cancel {
  background: #e2e8f0;
  color: #718096;
  margin-right: 12px;
}

.btn.confirm {
  /* background: #4299e1; */
  background: #188bf6;
  color: white;
}

.add-card-successful .modal-body {
  padding: 40px 32px;
  text-align: center;
}

.add-card-successful .check {
  border: 2px solid #68d391;
  padding: 32px;
  border-radius: 100%;
  height: 90px;
  width: 90px;
  display: flex;
  margin: auto;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #68d391;
}

.add-card-successful h4 {
  color: #4f4f4f;
  margin: 25px 0 44px 0;
}
</style>
