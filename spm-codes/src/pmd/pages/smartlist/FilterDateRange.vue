<template>
    <div>
        <div class="d-flex date-time-picker" style="min-width: 190px;">
            <vue-ctk-date-time-picker
                v-model="selectedDateRange"
                :right="true"
                :range="true"
                color="#188bf6"
                :only-date="true"
                enable-button-validate
                :noClearButton="true"
                name="range"
                :formatted="'MMM D, YYYY'"
                @validate="publish"/>
        </div>

      <div class="my-2" v-if="example" ><label style="font-size:1.1em !important;">{{example}}</label></div>
    </div>
</template>

<script lang="ts">

import Vue from 'vue';
import {FilterOption} from './vm/option_vm'
import { Location } from '@/models'
import { FilterBuilder } from './vm/filter_vm_builder';
import { getCountryDateFormat } from '@/models';
import * as moment from "moment-timezone";

export default Vue.extend({
  name: 'FilterDateRange',
  props : {
    bus : Vue,
    option: FilterOption
  },
  data () {
    return {
      waitTime: 300,
      debouncer:undefined as NodeJS.Timer | undefined,
      example: this.option.example || '',
      getCountryDateFormat,
			selectedDateRange: {
				start: this.option && this.option.firstValue ? moment(this.option.firstValue, 'YYYY-MM-DD hh:mm a') : moment(), // moment construction needs expected format for it to work in safari
				end:  this.option && this.option.secondValue ? moment(this.option.secondValue, 'YYYY-MM-DD hh:mm a') : moment()// moment construction needs expected format for it to work in safari
			},
    };
  },
  watch : {
    firstValue(n,o){
     this.option.firstValue = n;
     if(this.debouncer) clearTimeout(this.debouncer);
     this.debouncer = setTimeout(() => this.publish(),this.waitTime);
    }
  },
  // mounted(){
  //     console.log(moment(parseFloat(this.option.firstValue)).toDate());
  //     console.log(moment(parseFloat(this.option.secondValue)).toDate());
  // },
  methods: {
    async publish(){
      this.option.firstValue = `${moment(this.selectedDateRange.start, 'YYYY-MM-DD hh:mm a').format('YYYY-MM-DD hh:mm a')}`; // moment construction needs expected format for it to work in safari browser
      this.option.secondValue = `${moment(this.selectedDateRange.end, 'YYYY-MM-DD hh:mm a').format('YYYY-MM-DD hh:mm a')}`; // moment construction needs expected format for it to work in safari browser
      console.log(this.option.firstValue);
      console.log(this.option.secondValue);
      this.$emit('inputChange');
    },
  },
})
</script>
<style >
  /* .ctk-date-time-picker .field .field-input, .date-time-picker .field .field-input {
     padding: 15px 8px !important;
  } */
</style>
