<template>
  <!--  -->
  <div class="hl_wrapper">
    <div
      id="wordpress"
      v-if="
        location &&
        location.wordpressReseller &&
        this.company &&
        this.company.wordpressReseller &&
        this.company.wordpressReseller.valve
      "
    >
      <template v-if="wpInstalled">
        <div class="card">
          <div class="card-body">
            <template
              v-if="location.wordpressReseller.status != 'COMPLETE' && !loading"
            >
              <template
                v-if="location.wordpressReseller.status === 'INSTALLING'"
              >
                <div class="wp_install_status">
                  <div class="status_modal">
                    <h2>Sit Back and Relax 🏝️</h2>
                    <p
                      v-if="
                        location.wordpressReseller.installation_type ==
                        'existing_website'
                      "
                    >
                      Your Wordpress installation is in progress. Typically it
                      takes about 8 hours.
                    </p>
                    <p v-else>
                      Your Wordpress installation is in progress. Typically it
                      takes about 60 minutes.
                    </p>
                    <button
                      class="btn btn-success hl_calltoaction-btn"
                      @click="updateStatus"
                    >
                      Reload Again
                    </button>
                  </div>
                </div>
              </template>
              <template
                v-else-if="
                  location.wordpressReseller.status &&
                  location.wordpressReseller.status != 'INSTALLING' &&
                  location.wordpressReseller.status !=
                    'MANUAL_INSTALLATION_DONE'
                "
              >
                <div class="wp_install_status">
                  <div class="status_modal">
                    <h2>{{ location.wordpressReseller.status }}</h2>
                    <p>
                      {{ location.wordpressReseller.status_message }}
                    </p>
                    <button
                      class="btn btn-success hl_calltoaction-btn"
                      @click="updateStatus"
                    >
                      Reload Again
                    </button>
                  </div>
                </div>
              </template>
            </template>

            <div class="wp_status">
              <div class="logo_holder">
                <img
                  src="/pmd/img/icon-wordpress-logo_large.jpg"
                  class="logo"
                />
              </div>
            </div>
            <div class="settings_and_info">
              <section
                v-if="
                  location.wordpressReseller.installation_type ==
                    'existing_website' &&
                  location.wordpressReseller.status != 'INSTALLING'
                "
              >
                <span class="section_header"> Configure DNS</span>

                <div class="section_contents">
                  <div
                    class="info_display"
                    v-if="location.wordpressReseller.staging_domain"
                  >
                    <label>Testing URL</label>
                    <span class="info_data"
                      >{{ location.wordpressReseller.staging_domain }}
                    </span>
                    <div
                      class="info_indicators"
                      v-if="location.wordpressReseller.staging_domain"
                    >
                      <img
                        src="/pmd/img/external_link_icon.svg"
                        class="image_icons text_field_valign_padding"
                        @click="
                          openUrl(location.wordpressReseller.staging_domain)
                        "
                      />
                      <span
                        id="copy-code-wrapper"
                        class="
                          copy-code-wrapper
                          noselect
                          text_field_valign_padding
                        "
                        @click="
                          clipboardCopy(
                            location.wordpressReseller.staging_domain
                          )
                        "
                        v-b-tooltip.hover.right="'Copy'"
                        ><span></span>
                        <i class="icon-copy-to-clipboard"></i>
                      </span>
                    </div>
                  </div>
                  <p class="info-highlighter">
                    🎉<b> Congratulations!</b> Your migration has been completed
                    and pointed to this testing URL, please verify your contents
                    and follow the instructions bellow to point your traffic to
                    our blazing fast servers.
                  </p>
                  <div class="info_display">
                    <div class="checklist">
                      <ol>
                        <li>
                          <span class="checked"
                            ><i class="fas fa-check-circle"></i
                          ></span>
                          <span
                            ><b>CNAME</b> must point to <b>wp.msgsndr.com</b> for
                            all domains</span
                          >
                          <span class="optional_line"
                            >For root domains, please add an
                            <b>A record</b> pointing to IP
                            <b>**************</b></span
                          >
                        </li>

                        <li>
                          <span class="checked"
                            ><i class="fas fa-check-circle"></i
                          ></span>
                          If you are using cloudflare turn off the proxy
                        </li>
                      </ol>
                      <a
                        href="https://help.gohighlevel.com/support/solutions/articles/48001199648-add-domain-cname-for-your-new-wordpress-website"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        learn how to
                      </a>
                    </div>
                  </div>
                  <div class="child_right_align" style="padding-right: 0px">
                    <!--                 <UIButton
                      type="button"
                      use="outline"
                      data-dismiss="modal"
                      @click="updateStatus"
                    >
                      Verify
                    </UIButton> -->
                  </div>
                </div>
              </section>

              <section>
                <span class="section_header">Instance Access </span>
                <div class="section_contents">
                  <div class="info_display">
                    <label>Wordpress URL</label>
                    <span class="info_data"
                      >{{ prefixHttp(location.wordpressReseller.domain) }}
                    </span>
                    <div
                      class="info_indicators"
                      v-if="location.wordpressReseller.domain"
                    >
                      <img
                        src="/pmd/img/external_link_icon.svg"
                        class="image_icons text_field_valign_padding"
                        @click="openUrl(location.wordpressReseller.domain)"
                      />
                      <span
                        id="copy-code-wrapper"
                        class="
                          copy-code-wrapper
                          noselect
                          text_field_valign_padding
                        "
                        @click="
                          clipboardCopy(
                            prefixHttp(location.wordpressReseller.domain)
                          )
                        "
                        v-b-tooltip.hover.right="'Copy'"
                        ><span></span>
                        <i class="icon-copy-to-clipboard"></i>
                      </span>
                    </div>
                  </div>
                  <div
                    class="info_display"
                    v-if="location.wordpressReseller.domain"
                  >
                    <label>Wordpress Admin Access</label>
                    <span class="info_data"
                      >{{
                        prefixHttp(
                          `${location.wordpressReseller.domain}/wp-admin`
                        )
                      }}
                    </span>
                    <div
                      class="info_indicators"
                      v-if="location.wordpressReseller.domain"
                    >
                      <img
                        src="/pmd/img/external_link_icon.svg"
                        class="image_icons text_field_valign_padding"
                        @click="
                          openUrl(
                            location.wordpressReseller.domain + `/wp-admin`
                          )
                        "
                      />
                      <span
                        id="copy-code-wrapper"
                        class="
                          copy-code-wrapper
                          noselect
                          text_field_valign_padding
                        "
                        @click="
                          clipboardCopy(
                            prefixHttp(
                              location.wordpressReseller.domain + `/wp-admin`
                            )
                          )
                        "
                        v-b-tooltip.hover.right="'Copy'"
                        ><span></span>
                        <i class="icon-copy-to-clipboard"></i>
                      </span>
                    </div>
                  </div>
                </div>
              </section>
              <section>
                <span class="section_header">Refresh Cache</span>
                <div class="section_contents">
                  <span class="info-highlighter"
                    >This will update your most recent changes to our high-speed
                    servers</span
                  >
                  <div class="child_right_align" style="padding-right: 0px">
                    <UIButton
                      type="button"
                      use="outline"
                      data-dismiss="modal"
                      @click="updateCache"
                      :disabled="!location.wordpressReseller.domain"
                    >
                      Update now
                    </UIButton>
                  </div>
                </div>
              </section>

              <section
                v-if="true || location.wordpressReseller.ftp_url"
                style="opacity: 0.5"
              >
                <span class="section_header">FTP Access </span>
                <div class="section_contents">
                  <div class="info_display">
                    <label>Domain</label>

                    <template v-if="location.wordpressReseller.ftp_url">
                      <span class="info_data">
                        {{ location.wordpressReseller.ftp_url }}
                      </span>
                      <div class="info_indicators">
                        <img
                          src="/pmd/img/external_link_icon.svg"
                          class="image_icons text_field_valign_padding"
                          @click="openUrl(location.wordpressReseller.ftp_url)"
                        />
                        <span
                          id="copy-code-wrapper"
                          class="
                            copy-code-wrapper
                            noselect
                            text_field_valign_padding
                          "
                          @click="
                            clipboardCopy(location.wordpressReseller.ftp_url)
                          "
                          v-b-tooltip.hover.right="'Copy'"
                          ><span></span>
                          <i class="icon-copy-to-clipboard"></i>
                        </span>
                      </div>
                    </template>
                    <template v-else>
                      <span class="info_data">
                        <span class="placeholder"> coming soon</span>
                      </span>
                    </template>
                  </div>

                  <div class="info_display">
                    <label>Password</label>
                    <span class="info_data">
                      <template v-if="location.wordpressReseller.ftp_password">
                        <template v-if="showFTPpassword">
                          {{ location.wordpressReseller.ftp_password }}
                        </template>
                        <template
                          v-if="
                            !showFTPpassword &&
                            location.wordpressReseller.ftp_password
                          "
                        >
                          ************************
                        </template>
                      </template>
                      <template v-else>
                        <span class="placeholder"> coming soon</span>
                      </template>
                    </span>
                    <div
                      class="info_indicators"
                      v-if="location.wordpressReseller.ftp_password"
                    >
                      <img
                        v-if="!showFTPpassword"
                        src="/pmd/img/visibility_on.svg"
                        class="image_icons text_field_valign_padding"
                        @click="showFTPpassword = !showFTPpassword"
                      />
                      <img
                        v-else
                        src="/pmd/img/visibility_off.svg"
                        class="image_icons text_field_valign_padding"
                        @click="showFTPpassword = !showFTPpassword"
                      />
                      <span
                        id="copy-code-wrapper"
                        class="
                          copy-code-wrapper
                          noselect
                          text_field_valign_padding
                        "
                        @click="
                          clipboardCopy(location.wordpressReseller.ftp_password)
                        "
                        v-b-tooltip.hover.right="'Copy'"
                        ><span></span>
                        <i class="icon-copy-to-clipboard"></i>
                      </span>
                    </div>
                  </div>
                </div>
              </section>

              <section>
                <span class="section_header">Pointed Domains & Sub-domains</span>
                <div class="section_contents">
                  <template v-for="eachDomain of existingPointedDomains">
                    <div v-bind:key="eachDomain">
                      <div class="info_display">
                        <span class="info_data">{{ eachDomain }} </span>
                        <div class="info_indicators">
                          <img
                            src="/pmd/img/cancel_icon.svg"
                            class="cancel_icon"
                            @click="removeSubdomain(eachDomain)"
                          />
                        </div>
                      </div>
                    </div>
                  </template>
                  <template
                    v-if="
                      !existingPointedDomains ||
                      existingPointedDomains.length == 0
                    "
                  >
                    <span class="info-highlighter"
                      >You can add sub-domains to your main domain</span
                    >
                  </template>
                  <div
                    class="child_right_align"
                    v-if="!showAddDomainDialog"
                    style="padding-right: 0px"
                  >
                    <UIButton
                      type="button"
                      use="outline"
                      data-dismiss="modal"
                      @click="openSubdomainDialog"
                    >
                      Add domain or sub-domain
                    </UIButton>
                  </div>

                  <div class="inline_children" v-if="showAddDomainDialog">
                    <div class="new_pointed_domain">
                      <UITextInputGroup
                        type="text"
                        label="Domain"
                        placeholder="e.g. blog.yourdomain.com"
                        name="pointed_domain"
                        v-model="pointedDomain"
                      />
                      <div class="checklist">
                        <ol>
                          <li>
                            <span class="checked"
                              ><i class="fas fa-check-circle"></i
                            ></span>
                            <span
                              ><b>CNAME</b> must point to
                              <b>wp.msgsndr.com</b> for all domains</span
                            >
                            <span class="optional_line"
                              >For root domains, please add an
                              <b>A record</b> pointing to IP
                              <b>**************</b></span
                            >
                          </li>

                          <li>
                            <span class="checked"
                              ><i class="fas fa-check-circle"></i
                            ></span>
                            If you are using cloudflare turn off the proxy
                          </li>
                        </ol>
                        <a
                          href="https://help.gohighlevel.com/support/solutions/articles/48001199648-add-domain-cname-for-your-new-wordpress-website"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          learn how to
                        </a>
                      </div>
                      <div class="child_space_between">
                        <div
                          :class="['status', 'status_' + pointingDomainStatus]"
                        >
                          <template v-if="pointingDomainStatus === 'checking'">
                            <moon-loader
                              :loading="true"
                              color="#37ca37"
                              size="20px"
                            />
                            <span>Detecting Domain</span>
                          </template>
                          <template v-if="pointingDomainStatus === 'not_found'">
                            <span>
                              <template v-if="pointingDomainStatusMessage"
                                >{{ pointingDomainStatusMessage }}
                              </template>
                              <template v-else> ❗ Domain not found </template>

                              <a
                                href=" https://help.gohighlevel.com/support/solutions/articles/48001199648-add-domain-cname-for-your-new-wordpress-website"
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                view tutorial</a
                              >
                            </span>
                          </template>
                          <template v-if="pointingDomainStatus === 'verified'">
                            <span> ✔️ Domain detected </span>
                          </template>
                        </div>

                        <UIButton
                          type="button"
                          use="outline"
                          data-dismiss="modal"
                          :disabled="!pointedDomain"
                          id="verify_and_add_pointed_domain"
                          @click="verifyAndAddPointedDomain"
                        >
                          Verify and Add
                        </UIButton>
                      </div>
                    </div>
                    <img
                      src="/pmd/img/cancel_icon.svg"
                      class="cancel_icon"
                      @click="showAddDomainDialog = false"
                    />
                  </div>
                </div>
              </section>
            </div>
          </div>
          <template v-if="loading">
            <div class="loader-backdrop">
              <moon-loader :loading="loading" color="#188bf6" size="30px" />
            </div>
          </template>
        </div>
      </template>
      <template v-else>
        <WordpressShopComponent @statusUpdate="refreshLocationFromDb" />
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Location, User, Company } from '@/models'
import { mapState } from 'vuex'
import { CompanyState, UserState } from '@/store/state_models'

const WordpressShopComponent = () =>
  import('@/pmd/components/wordpress/WordpressShopComponent.vue')
export default Vue.extend({
  components: {
    WordpressShopComponent,
  },
  data() {
    return {
      loading: false,
      currentLocationId: '',
      location: {} as Location,
      showAddDomainDialog: false,
      pointingDomainStatus: '',
      pointingDomainStatusMessage: '',
      showFTPpassword: false,
      pointedDomain: '',
      autoCheckTimer: null,
    }
  },

  watch: {
    '$route.params.location_id': async function (id) {
      this.currentLocationId = this.$router.currentRoute.params.location_id
      await this.fetchData()
      this.statusCheck()
    },
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    await this.fetchData()
    this.statusCheck()
  },
  async mounted() {},
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
    wpInstalled() {
      return this.location && this.location.wordpressReseller.wp_id
    },

    existingPointedDomains() {
      let domains = []
      if (
        this.location &&
        this.location.wordpressReseller.sub_domains &&
        this.location.wordpressReseller.sub_domains.length > 0
      ) {
        domains = this.location.wordpressReseller.sub_domains
      }
      return domains
    },
  },
  methods: {
    async statusCheck() {
      if (this.location.wordpressReseller.status != 'COMPLETE') {
        this.autoCheckTimer = window.setInterval(async _ => {
          if (this.location.wordpressReseller.status == 'COMPLETE') {
            window.clearInterval(this.autoCheckTimer)
          } else {
            await this.updateStatus()
          }
        }, 1000 * 60 * 5)
        this.updateStatus()
      }
    },
    async updateCache() {
      this.loading = true
      let response = await this.$http.post(
        `/wordpress/invalidate_cache?location_id=${this.currentLocationId}`,
        {
          wp_id: this.location.wordpressReseller.wp_id,
          domain: this.location.wordpressReseller.domain,
        }
      )
      this.loading = false
      if (response.data.update) {
        this.$uxMessage(
          'info',
          `Your latest changes will be updated in 4-5 minutes`,
          {
            isMessageInRawHTML: true,
          }
        )
      }
    },
    async fetchData() {
      this.location = new Location(
        await this.$store.dispatch(
          'locations/getCurrentLocation',
          this.currentLocationId
        )
      )
    },
    async refreshLocationFromDb() {
      this.loading = true
      await this.delay(3000)
      this.location = new Location(
        await this.$store.dispatch(
          'locations/getCurrentLocation',
          this.currentLocationId
        )
      )
      this.loading = false
    },
    async delay(time = 1000) {
      return new Promise((resolve, reject) => {
        setTimeout(resolve, time)
      })
    },
    async verifyAndAddPointedDomain() {
      this.pointingDomainStatus = `checking`
      try {
        let response = await this.$http.post('/wordpress/verify_domain', {
          domain: this.pointedDomain.trim(),
          location_id: this.currentLocationId,
          user_type: this.user?.type,
        })
        if (response.data.verified) {
          this.pointingDomainStatus = `verified`
        } else {
          this.pointingDomainStatus = `not_found`
          if (response.data.status_message) {
            this.pointingDomainStatusMessage = response.data.status_message
          }
        }
      } catch (err) {
        console.error(err)
        this.pointingDomainStatus = `not_found`
      }

      if (this.pointingDomainStatus === `verified`) {
        try {
          this.loading = true
          let response = await this.$http.post('/wordpress/add_subdomain', {
            domain: this.pointedDomain.trim(),
            location_id: this.location.id,
          })
          this.showAddDomainDialog = false
          this.pointingDomainStatus = ''
          this.pointingDomainStatusMessage = ''

          await this.refreshLocationFromDb()
          this.$uxMessage(
            'info',
            `New Subdomain added ${this.pointedDomain}. Usually takes around 30 mins to reflect changes`,
            {
              isMessageInRawHTML: true,
            }
          )
          this.pointedDomain = ''
          this.loading = false
        } catch (err) {
          this.loading = false
          console.error(err)
          this.$uxMessage('error', `Unable to add subdomain`, null, {
            isMessageInRawHTML: true,
          })
        }
      }
    },
    removeSubdomain(domain: string) {
      this.$uxMessage(
        'confirmation',
        `Are you sure to remove this subdomain (${domain})`,
        async res => {
          if (res === 'ok') {
            this.loading = true
            try {
              let response = await this.$http.post(
                '/wordpress/remove_subdomain',
                {
                  domain: domain,
                  location_id: this.location.id,
                }
              )

              await this.refreshLocationFromDb()
              this.loading = false
            } catch (e) {
              this.loading = false

              this.$uxMessage('error', `Unable to remove  subdomain`, null, {
                isMessageInRawHTML: true,
              })
            }
          }
        }
      )
    },
    async openUrl(url: string) {
      //this.showManageListingPopup = false
      if (!url.startsWith('http')) {
        url = `https://${url}`
      }
      window.open(window.encodeURI(url))
    },
    prefixHttp(url: string) {
      if (!url.startsWith('http')) {
        url = `https://${url}`
      }
      return url
    },
    async updateStatus() {
      this.loading = true
      try {
        let response = await this.$http.post(`/wordpress/update_status/${this.location.id}`)
        console.log(response.data)

        if (response.data.status_changed) {
          await this.refreshLocationFromDb()
        }
      } catch {
      } finally {
        this.loading = false
      }
    },

    openSubdomainDialog() {
      if (this.existingPointedDomains.length < 5) {
        this.showAddDomainDialog = true
      } else {
        this.$uxMessage(
          'error',
          `You can have upto 5 Sub-Domains connected at a time, you can remove existing one and add a new one`,
          null,
          {
            isMessageInRawHTML: true,
          }
        )
      }
    },
  },
})
</script>
<style lang="scss" scoped>
#wordpress {
  position: relative;
  padding: 15px;
  overflow: auto;
  height: 100%;

  .card {
    min-height: calc(100% - 30px);
    .card-body {
      min-height: 400px;
      display: grid;
      grid-template-columns: 300px 1fr;
      grid-column-gap: 10px;
      position: relative;
      .wp_install_status {
        position: absolute;
        background: #ffffffb3;
        width: 100%;
        height: 100%;
        z-index: 1;
        display: grid;
        justify-content: center;
        align-items: baseline;
        .status_modal {
          padding: 35px 10px 20px 10px;
          background: #fff;
          border: 1px solid #dcdcdc;
          box-shadow: 1px 2px 4px 1px #00000038;
          border-radius: 2px;
          display: grid;
          justify-items: center;
          grid-gap: 16px;
          margin-top: 100px;
        }
      }
      .wp_status {
        border-right: 1px dashed #7a7a7a;
        .logo_holder {
          display: grid;
          justify-content: center;
          align-items: center;
          padding: 10px;
          .logo {
            width: 10vw;
            min-width: 100px;
            max-width: 180px;
          }
        }
        .server_logo {
          width: 16vw;
          margin: -25px auto;
          min-width: 220px;
        }
      }
      .settings_and_info {
        padding: 0 25px;
        section {
          margin-bottom: 40px;
          .section_header {
            color: #464646;
            font-size: 1.4rem;
            font-weight: 500;
          }
          .section_contents {
            padding-top: 20px;
            padding-left: 45px;
            max-width: 680px;
            display: grid;
            grid-row-gap: 20px;

            .new_pointed_domain {
              border: 1px solid #bdbdbdcc;
              border-radius: 5px;
              padding: 10px;
            }
          }
        }
      }
    }

    .loader-backdrop {
      background: #ffffffb5;
      position: absolute;
      z-index: 1;
      width: 100%;
      height: 100%;
      display: grid;
      justify-content: center;
      align-items: center;
    }
  }

  .checklist {
    margin: 10px 0;
    border: 2px dashed #27ae60;
    padding: 0 10px 10px 10px;
    display: flex;
    flex-direction: column;

    ol {
      li {
        margin: 10px 0;
        display: grid;
        align-items: center;
        grid-template-columns: auto 1fr;
        grid-gap: 10px;

        .checked {
          color: #27ae60;
          font-size: 2rem;
        }
        .optional_line {
          grid-column: 1/-1;
          padding-left: 42px;
          margin-top: -18px;
        }
      }
    }
    a {
      text-align: right;
    }
  }

  .info_display {
    display: grid;
    grid-template-columns: 1fr auto;
    grid-template-rows: auto auto;
    label {
      margin-bottom: 2px;
      font-weight: 500;
      font-size: 0.85rem;
      color: #000;
    }
    .info_data {
      grid-column: 1;
      background: #f4f4f4;
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
      padding: 10px 10px 10px 10px;
      display: flex;
      align-items: center;
      min-height: 42px;
      color: #0a5678;

      .placeholder {
        color: #505558;
      }
    }
    .info_indicators {
      display: grid;
      grid-template-columns: auto auto;
      grid-gap: 6px;
      align-items: center;
      background: #f4f4f4;
      padding: 5px 10px 5px 5px;
      border-top-right-radius: 5px;
      border-bottom-right-radius: 5px;
      img,
      .copy-code-wrapper {
        cursor: pointer;
      }
    }
  }
  .text_field_valign_padding {
  }
  .child_right_align {
    display: flex;
    justify-content: right;
    padding-right: 30px;
  }

  .child_space_between {
    display: flex;
    justify-content: space-between;
  }

  .inline_children {
    display: grid;
    grid-template-columns: 1fr auto auto auto;
    grid-gap: 5px;
  }
  .inline_children--vcenter {
    align-items: center;
  }

  .status {
    display: grid;
    grid-template-columns: auto 1fr;
    grid-column-gap: 8px;
    justify-content: flex-start;
    padding: 10px 10px;
    border-bottom-left-radius: 6px;
  }
  .status_verified {
    background: linear-gradient(45deg, #27ae6036, transparent);
    color: #000;
  }
  .status_checking {
    background: linear-gradient(45deg, #ffbc0030, transparent);
    color: #000;
  }
  .status_not_found {
    background: linear-gradient(45deg, #ff595917, transparent);
    color: #f00;
    a {
      color: #188bf6;
    }
  }
  .icon-copy-to-clipboard {
    width: 20px;
    height: 20px;
    background: url('/pmd/img/icon-copy-to-clipboard.svg');
    background-repeat: no-repeat;
    background-size: contain;
    margin-top: 3px;
  }
  .cancel_icon {
    cursor: pointer;
  }
  .info-highlighter {
    color: #464646;
    background: linear-gradient(to right, #00b9001c, #c5ffc50d);
    padding: 10px;
    border-left: 2px solid #37ca37;
  }
}
</style>
