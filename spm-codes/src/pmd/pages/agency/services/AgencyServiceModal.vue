<template>
  <div class="modal" ref="modal">
		<div class="modal-dialog" role="document">
      <div class="modal-content" v-if="showPaymentMethod" >
        <billing-method :show-heading="true" :show-cancel="true" :show-sca-warning="true" save-button-text="Update" @cardChangedTimeout="hidePaymentMethod" @cancel="hidePaymentMethod"/>
      </div>
      <div class="modal-content" v-show="!showPaymentMethod">
				<div class="modal-header">
					<div class="modal-header--inner">
						<h5 class="modal-title" id="client-checkin--modalLabel">
							{{serviceType.formHeading}}
						</h5>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</div>
				<div class="modal-body">
					<div class="modal-body--inner" v-if="!submitted">
						<div class="form-group">
							<label>Agency Name*</label>
							<input
								type="text"
								class="form-control"
								placeholder="Agency name"
								v-validate="'required'"
								name="organizationName"
                				v-model="organizationName"
							>
							<span v-show="errors.has('organizationName')" class="error">Agency name required</span>
						</div>

            			<div class="form-group">
							<label>Full Address*</label>
							<input
								type="text"
								class="form-control"
								placeholder="Agency address"
								v-validate="'required'"
								name="address"
                				v-model="address"
							>
							<span v-show="errors.has('address')" class="error">Address required</span>
						</div>
						<div class="row">
							<div class="form-group col-sm-6">
								<label>Contact Person Name*</label>
								<input
									type="text"
									class="form-control"
									placeholder="Conatct Person name"
									v-validate="'required'"
									name="contactName"
									v-model="userName"
								>
								<span v-show="errors.has('contactName')" class="error">Contact name required</span>
							</div>

							<div class="form-group col-sm-6">
								<label>Role/Title*</label>
								<input
									type="text"
									class="form-control"
									placeholder="Designation"
									v-validate="'required'"
									name="designation"
									v-model="designation"
								>
								<span v-show="errors.has('designation')" class="error">Role/Title is required</span>
							</div>

							<div class="form-group col-sm-6">
								<label>Phone*</label>
								<PhoneNumber
									class="form-control"
									placeholder="Phone"
									v-validate="'required|phone'"
									name="phone"
									v-model="phone"
								/>
								<span v-show="errors.has('phone')" class="error">{{ errors.first('phone') }}</span>
							</div>

							<div class="form-group col-sm-6">
								<label>Email*</label>
								<input
									type="text"
									class="form-control"
									placeholder="Email"
									v-validate="'required|email'"
									name="email"
									v-model="email"
								>
								<span v-show="errors.has('email')" class="error">{{ errors.first('email') }}</span>
							</div>
						</div>
						<div class="option" >
							<input type="checkbox" id="isAgree" v-model="isAgree" />
							<label for="isAgree" style="line-height:20px">
								{{serviceType.agreeText}}
							</label>
						</div>
						<br v-if="values.type === 'hipaa'" />
						<div class="option" v-if="values.type === 'hipaa'">
							<input type="checkbox" id="accept" v-model="accepted" />
							<label for="accept" style="line-height:20px">
								Once Enabled, I can no longer remove the HighLevel HIPAA Enablement and downgrade my account.
							</label>
						</div>
						<div class="warning-bar" v-if="values.company && !values.company.stripeId">
							Your Payment configurations are missing !!
							<br /><NAME_EMAIL>
						</div>

					</div>
					<div class="modal-body--inner" v-else >
						<p class="sucess-msg" v-html="serviceType.successMsg"></p>
					</div>

				</div>
				<div class="modal-footer">
          <div class="modal-footer--inner nav" style="display: block;" v-if="scaPending">
            <div>
              Please <span class="purchase-summary__link" @click="showPaymentMethod = true">authenticate</span> your card details again
              <span class="hl_tooltip pointer" v-b-tooltip.hover title="We have moved to a more secure billing system. For added security you need to authenticate your card again." ><i class="fa fa-info" style="font-size: 10px"></i></span>
            </div>
            <div class="purchase-summary__pay-btn btn btn-blue" @click="showPaymentMethod = true">
              <moon-loader v-if="processing" color="#ffffff" size="20px"/>
              <span v-else>Update Payment Method</span>
            </div>
          </div>
					<div class="modal-footer--inner nav" v-else>
						<div style="display: inline-block;position: relative;" v-if="!submitted">
							<button
								:class="{invisible: processing}"
								type="button"
								class="btn btn-success"
                				@click="subscribe"
								:disabled="!isAgree || !accepted"
							>{{serviceType.buttonText}}</button>
							<div
								style="position: absolute;left: 50%;transform: translate(-50%, -50%);top: 50%;"
								v-show="processing"
							>
								<moon-loader :loading="processing" color="#37ca37" size="30px"/>
							</div>
						</div>
						<div v-else>
							<button
								type="button"
								class="btn btn-primary"
								data-dismiss="modal"
								@click="closeModal"
							>
								Close
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from 'vue'
import {
  Company, User
} from '@/models'

const PhoneNumber = () => import('../../../components/util/PhoneNumber.vue');
import BillingMethod from "@/pmd/pages/agency/billing/BillingMethod.vue";

declare var $: any
export default Vue.extend({
  components: { PhoneNumber, BillingMethod },
  props: ['values'],
  data() {
    return {
	  processing: false,
	  submitted: false,
      designation: '',
      organizationName: '',
      address: '',
      userName: '',
      phone: '',
	  email: '',
	  isAgree: false,
    accepted: false,
    scaPending: false,
    showPaymentMethod: false
    }
  },
  watch: {
    values(values : { [key: string]: any }) {
      const data: (() => object) = <(() => object)>this.$options.data;
			if (data) Object.assign(this.$data, data.apply(this));
			if (values.visible) $(this.$refs.modal).modal('show');
			else $(this.$refs.modal).modal('hide');
			if (values.visible) {
        if (values.company) {
          this.organizationName = values.company.name || '';
          this.address = values.company.address || '';
          this.phone = values.company.phone || '';
          this.email = values.company.email || '';
        }
        if (values.user) {
          this.userName = values.user.name;
          if (!this.phone) this.phone = values.user.phone || '';
          if (!this.email) this.email = values.user.email || '';
        }
        this.verifySCA();
	    }
    }
  },
  methods: {
		closeModal(){
			$(this.$refs.modal).modal('hide');
    },
    async verifySCA(){
      this.processing = true;
      try{
        let verificationData = await this.$http.post('/stripe/verify_sca',{
          company_id: this.values.company.id
        });
        if(!verificationData.data.default_payment_method){
          this.scaPending = true;
        }

      } catch (err) {
        //
        console.error(err);
      } finally{
        this.processing = false;
      }
    },
    hidePaymentMethod(){
      this.scaPending = false;
      this.verifySCA();
      this.showPaymentMethod = false;
    },
    	async subscribe() {
	  		const result = await this.$validator.validateAll();
			if (!result || !this.isAgree || !this.accepted) {
				return false;
      		}
      		this.processing = true;

			const serviceId = this.$route.params.service_id;
			const authUser = await this.$store.dispatch("auth/get");
			try{
				let response = await this.$http.post(`/agency_service/load/${serviceId}`,{
					planLevel: this.values.plan.plan_level || 1,
					upgrade: this.values.upgrade || false,
					agency: {
						companyId: this.values.company.id,
						name: this.organizationName,
						address: this.address,
					},
					user: {
						name: this.userName,
						email: this.email,
						phone: this.phone,
						designation: this.designation,
						userId: authUser.userId
					}
				});

				this.processing = false;
				this.submitted = true;
				this.$emit('success');
			}
			catch (err) {
				this.processing = false;
				//
			}
			finally{
				this.processing = false;
			}
			this.processing = false;
		}
	},
  computed:{
	  serviceType(){
		  let formHeading = "Agency Service Request Form";
		  let buttonText = "Submit";
		  let agreeText = "I Agree to upgrade my HighLevel Agency account";
		  let successMsg = "Thank you for subscribing, we have recieved your request.";
		  switch(this.values.type) {
			  case 'hipaa':{
				  formHeading = "HIPAA Compliance Form";
				  buttonText = "Enable HIPAA Compliant Mode";
				  agreeText = "I Agree to upgrade my HighLevel Agency account to the HighLevel HIPAA Agency Account for an additional $297/month.";
				  successMsg = `Thank you for subscribing, we will be sending you an official BAA for your agency to sign within the next 48-72 hours.
							<br />
							Please check your email to complete the paperwork.`
				  break;
			  }
			  case 'premium':{
				  formHeading = "Agency Premium Upgrade Form";
				  buttonText = "Upgrade to Agency Premium";
				  agreeText = "I Agree to upgrade my HighLevel Agency account to the HighLevel Premium Agency Account for an additional $300/month.";
				  this.accepted = true;
				  break;
			  }
			  case 'whitelabel-app':{
				  formHeading = "Whitelabel App Request";
				  buttonText = "Request Whitelabel App";
				  agreeText = `I agree to addon my agency account with whitelabel app for an additional $${this.values.plan.wholesale_recur_price}/month`;
				  this.accepted = true;
				  break;
			  }
			  case 'jumpstart':{
				  formHeading = "Highlevel Agency Jumpstart";
				  buttonText = `Pay $${this.values.plan.wholesale_setup_price} now`;
				  agreeText = `I Agree to addon my HighLevel Agency account with Agency Jumpstart plan for an additional $${this.values.plan.wholesale_setup_price} for ${this.values.plan.min_duration_months} month(s).`;
				  this.accepted = true;
				  break;
			  }
		  }
		  return { formHeading , buttonText, agreeText, successMsg };
	  }
  },
  updated() {
		if (this.values && this.values.visible) {
      $(this.$refs.modal).modal('show');
		}
	},
	mounted() {
		const _self = this;
		$(this.$refs.modal).on('hidden.bs.modal', function () {
			_self.$emit('hidden');
		});
		if (this.values && this.values.visible) {
			$(this.$refs.modal).modal('show');
		}
  }
})
</script>

<style scoped>
.btn-success[disabled='disabled'] {
	cursor: not-allowed;
	/* pointer-events: none; */
}
.warning-bar{
    width: 100%;
    padding: 12px;
    border-left: 4px solid #ffbc00;
    background-color: #fafafa;
    font-size: 12px;
    line-height: 16px;
    margin: 10px 0px;
}
.purchase-summary__link{
  color: #2F80ED;
  font-weight: 500;
  cursor: pointer;
}
.purchase-summary__pay-btn{
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  font-weight: 500;
  padding: 12px;
  width: 100%;
  margin-top: 8px;
  margin-left: 0px !important;
}
</style>
