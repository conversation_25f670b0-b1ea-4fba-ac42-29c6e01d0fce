<template>
  <div class="px-2 py-3 mx-5">
    <div class="flex flex-col">
      <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div class="space-y-2">
        <UIAlert
          type="info"
        >
          Multiple recurring products and bump products with subscriptions not supported with Paypal.
        </UIAlert>
        <UIAlert
          type="info"
          v-if="allowPaymentModeOption && !paymentMode"
        >
         Funnel products are set to <b>Test</b> mode if you wish to switch mode click
         <router-link :to="{name:'funnel_settings'}">here</router-link>
        </UIAlert>
        </div>

        <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
          <div class="flex justify-end items-center w-full pb-4">
            <UIButton @click="addNewProduct">
              <i class="fas fa-plus mr-1"></i>
              Add product
            </UIButton>
          </div>
          <div class="shadow-md border-b border-gray-200 sm:rounded-lg">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-8/12"
                  >
                    Product Name
                  </th>

                  <th
                    scope="col"
                    class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Internal Product ID
                  </th>
                  <th
                    scope="col"
                    class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Amount
                  </th>
                  <th
                    scope="col"
                    class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Type
                  </th>
                  <th
                    scope="col"
                    class="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                  >
                    Updated
                  </th>

                  <th scope="col" class="relative px-2 py-3">
                    <span class="sr-only">Edit</span>
                  </th>
                </tr>
              </thead>
              <tbody v-if="!loading">
                <tr
                  v-for="(product, productIdx) in funnelProducts"
                  :key="product._id"
                  :class="productIdx % 2 === 0 ? 'bg-white' : 'bg-gray-50'"
                >
                  <td
                    class="px-2 py-3 whitespace-nowrap text-sm font-medium text-gray-900"
                  >
                    {{ product.name }}
                    <UIBadge use="red" v-if="hasIntegrationConnected === false || getProductDisabledText(product)">
                      Disabled
                      <span
                        style="margin-left: 5px"
                        class="input-group-addon"
                        v-b-tooltip.hover
                        :title="hasIntegrationConnected === false ? 'Please connect atleast 1 payment integration' : getProductDisabledText(product)"
                      >
                        <i class="fas fa-question-circle"></i>
                      </span>
                    </UIBadge>
                    <UIBadge
                      use="yellow"
                      v-if="product.price && product.bumpProduct"
                    >
                      Bump
                    </UIBadge>
                  </td>
                  <td class="px-2 py-3 whitespace-nowrap text-sm text-gray-500 whitespace-nowrap text-sm text-gray-500 ">
                    <UIBadge
                    >
                      {{ product._id }}
                    </UIBadge>
                  </td>
                  <td class="px-2 py-3 whitespace-nowrap text-sm text-gray-600">
                    <UIBadge v-if="product.price">
                      {{ currencyCode2Symbol(product.price.currency) }}
                      {{ product.price.amount }}
                      {{ product.price.currency }}
                    </UIBadge>
                  </td>
                  <td class="px-2 py-3 whitespace-nowrap text-sm text-gray-500">
                    <UIBadge use="green" v-if="product.price">
                      <RefreshIcon
                        v-if="product.price.type === 'recurring'"
                        class="h-4 w-4"
                      />
                      <CreditCardIcon v-else class="h-4 w-4" />

                      <span class="ml-1">
                        {{ PriceTypes[product.price.type] }}
                      </span>
                    </UIBadge>
                  </td>

                  <td class="px-2 py-3 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(product.updatedAt) }}
                  </td>

                  <td
                    class="px-2 py-3 whitespace-nowrap text-right text-sm font-medium space-x-2 flex"
                  >
                    <router-link
                      tag="span"
                      :to="{
                        name: isWebsite ? 'website_step_product_v2_edit': 'funnel_step_product_v2_edit',
                        params: {
                          step_id: $route.params.step_id,
                          funnel_product_id: product._id,
                        },
                      }"
                      class="text-curious-blue-600 hover:text-curious-blue-900 cursor-pointer"
                    >
                      <EditIcon class="text-curious-blue-500 h-6 w-6" />
                    </router-link>
                    <DeleteIcon
                      class="text-curious-blue-600 cursor-pointer h-6 w-6"
                      @click="onDelete(product)"
                    />
                  </td>
                </tr>
              </tbody>
              <tbody v-else>
                <tr v-for="(item, index) in 4" :key="index">
                  <td>
                    <div class="px-3 py-3 animate-pulse">
                      <div class="h-6 bg-gray-100 rounded"></div>
                    </div>
                  </td>
                  <td>
                    <div class="px-2 py-3 animate-pulse">
                      <div class="h-6 bg-gray-100 rounded"></div>
                    </div>
                  </td>
                  <td>
                    <div class="px-2 py-3 animate-pulse">
                      <div class="h-6 bg-gray-100 rounded"></div>
                    </div>
                  </td>
                  <td>
                    <div class="px-2 py-3 animate-pulse">
                      <div class="h-6 bg-gray-100 rounded"></div>
                    </div>
                  </td>
                  <td>
                    <div class="px-2 py-3 animate-pulse">
                      <div class="h-6 bg-gray-100 rounded"></div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <DeleteWarningModal
      :showDeleteModal="showDeleteModal"
      :warningText="warningText"
      :name="productName"
      @close="closeDeleteModal"
      @click="onClickDelete"
      deleteBtnId="payments__btn-delete-funnel-product"
      cancelBtnId="payments__btn-cancel-funnel-product"
    />
  </div>
</template>

<script lang="ts">
import { useDateFormatter } from '@/pmd/composition/dateFormatting'
import { useCurrencyFormatter } from '@/pmd/composition/currencyFormatting'
import { FunnelsServices } from '@/services'
import { computed, defineComponent, onMounted, ref } from '@vue/composition-api'
import EditIcon from '@/assets/pmd/icons/pencil-alt.svg'
import DeleteIcon from '@/assets/pmd/icons/trash.svg'
import RefreshIcon from '@/assets/pmd/icons/refresh.svg'
import CreditCardIcon from '@/assets/pmd/icons/credit-card.svg'
import informationCircleIcon from '@/assets/pmd/icons/information-circle.svg'
import DeleteWarningModal from '@/pmd/components/common/DeleteWarningModal.vue'
import { useLocation } from '@/pmd/composition/locationComposition'
import { Funnel, Location } from '@/models'
import { PaymentServices } from '@/services'

const PriceTypes = {
  recurring: 'Recurring',
  one_time: 'One Time',
}

export default defineComponent({
  components: {
    EditIcon,
    DeleteIcon,
    RefreshIcon,
    CreditCardIcon,
    DeleteWarningModal,
    informationCircleIcon,
  },
  data() {
    return {
      showDeleteModal: false,
      warningText:
        'Are you sure you want to delete this product? If this product is integrated with Funnels then it will be disabled.',
      productName: '',
      deleteId: '',
    }
  },
  setup(_, ctx) {
    const route = ctx.root.$route
    const router = ctx.root.$router
    const funnelProducts = ref([])
    const { formatDate } = useDateFormatter()
    const { currencyCode2Symbol } = useCurrencyFormatter()
    const loading = ref(true)
    const locationConnectMode = ref(false)
    const allowPaymentModeOption = ref(false)
    const funnel = ref()
    const paymentMode = ref()
    const hasIntegrationConnected = ref()
    const { getById } = useLocation()
    onMounted(async () => {
      const location:Location =  await getById(route.params.location_id)

      locationConnectMode.value = location.stripeConnectMode === 'live'
      allowPaymentModeOption.value = !location.stripe?.publishable_key;

      await loadFunnel()
      await fetchIntegrations()
      loadFunnelProducts()
    })

    async function loadFunnel() {
      try {
        funnel.value = await Funnel.getById(route.params.funnel_id)
        paymentMode.value = funnel.value.isLivePaymentMode !== undefined ? funnel.value.isLivePaymentMode : locationConnectMode.value
      } catch (error) {
        console.log(error)
      }
    }

    async function fetchIntegrations() {
      try {
        const { data } = await PaymentServices.getIntegrations(route.params.location_id)
        const mode = paymentMode.value ? 'live' : 'test';
        const isStripeConnected = !!data?.stripe[mode]?.accountId;
        const isPaypalConnected = !!data?.paypal[mode]?.merchantClientId;
        hasIntegrationConnected.value = isStripeConnected || isPaypalConnected;
      } catch (error) {
        console.error(error)
      }
    }

    const isWebsite = computed(() =>{
      return funnel.value?.type === 'website'
    })

    function addNewProduct() {
      let routeName = 'funnel_step_product_v2'
      if(isWebsite.value){
        routeName = 'website_step_product_v2'
      }
      router.push({
        name: routeName,
        params: { step_id: route.params.step_id },
      })
    }

    async function loadFunnelProducts() {
      try {
        loading.value = true
        const params = {
          locationId: route.params.location_id,
          funnel: route.params.funnel_id,
          step: route.params.step_id,
        }
        const { data } = await FunnelsServices.listFunnelProducts(params)
        funnelProducts.value = data?.products
        loading.value = false
      } catch (error) {
        console.error(error)
        loading.value = false
      }
    }

    async function onClickDelete() {
      try {
        await FunnelsServices.deleteFunnelProduct(this.deleteId)
        this.closeDeleteModal()
        loadFunnelProducts()
      } catch (error) {
        console.error(error)
      }
    }

    function getProductDisabledText(funnelProduct: any) {
      if (!funnelProduct.product) return 'Selected product deleted. Please re-select.';
      if (!funnelProduct.price) return 'Selected price deleted. Please re-select.'
      if (!funnelProduct.product.stripe && !funnelProduct.product.paypal) return 'Selected product not created in connected payment integration'
      if (funnelProduct.price.type === 'recurring' && !funnelProduct.price.stripe && !funnelProduct.price.paypal) return 'Selected price not created in connected payment integration'
      return false
    }

    return {
      funnelProducts,
      formatDate,
      addNewProduct,
      PriceTypes,
      currencyCode2Symbol,
      onClickDelete,
      loading,
      allowPaymentModeOption,
      paymentMode,
      getProductDisabledText,
      hasIntegrationConnected,
      isWebsite
    }
  },
  methods: {
    onDelete(product: any) {
      this.productName = `product - ${product.name}`
      this.deleteId = product._id
      this.showDeleteModal = true
    },
    closeDeleteModal() {
      this.showDeleteModal = false
      this.deleteId = ''
    },
  },
})
</script>

<style scoped></style>
