<template>
	<Filters
		:conditions="conditions"
		:filterMaster="filterMaster"
		v-on:update:conditions="$emit('update:conditions', $event)"
	/>
</template>

<script lang="ts">
import Vue from 'vue'
import { sortBy } from 'lodash'
import { Campaign, Calendar, Tag } from '@/models';
import { Condition } from '@/models/trigger';
const Filters = () => import('./Filters.vue');

export default Vue.extend({
  props: ['conditions'],
  data() {
    return {
      tags: [] as any[]
    }
  },
  components: { Filters },
  async created() {
    const currentLocationId = this.$router.currentRoute.params.location_id;
    await Promise.all([
      this.$store.dispatch('teams/syncAll', currentLocationId)
    ])
    this.tags = (await Tag.getByLocationId(currentLocationId)).map((tag)=>{
      return {
        title: tag.name,
        value: tag.name
      }
    })
  },
  computed: {
    selectedCalendarIndex() {
      return this.conditions.findIndex(x => x.field === 'calendar.id' && x.value)
    },
    selectedCalendarId() {
      return this.selectedCalendarIndex === -1 ? undefined : this.conditions[this.selectedCalendarIndex].value
    },
    selectedTeamIndex() {
      return this.conditions.findIndex(x => x.field === 'appointment.calendarProviderId' && x.value)
    },
    selectedTeamId() {
      return this.selectedTeamIndex === -1 ? undefined : this.conditions[this.selectedTeamIndex].value
    },
    selectedUserIndex() {
      return this.conditions.findIndex(x => x.field === 'appointment.assignedUserId' && x.value)
    },
    selectedUserId() {
      return this.selectedUserId === -1 ? undefined : this.conditions[this.selectedUserIndex].value
    },
    selectedTeamCalendarIndex() {
      return this.conditions.findIndex(x => x.field === 'appointment.isV3' && x.value)
    },
    filterMaster() {

      const _teams = this.$store.getters['teams/calendarProviders']
      const teamIds = _teams.map((team) => team.id )

      const filters = []

      const v2Calendars = this.$store.state.calendars.calendars.filter(key => !key.provider_id).map(cal => {
        return { 
          title: cal.name, 
          value: cal.id 
        }
      });
      const v3Calendars = this.$store.state.calendars.calendars.filter(key => teamIds.includes(key.provider_id)).map(cal => {
        const calendarProviderName = _teams.find(key => key.id === cal.provider_id)?.calendar_provider_name;
        return { 
          ...cal,
          calendar_provider_name: calendarProviderName
        }
      });
      const sortedV3Calendars = sortBy(v3Calendars, (key) => key.calendar_provider_name ? key.calendar_provider_name.toUpperCase() : '').map(cal => {
        return { 
          title: `${cal.name} (${cal.calendar_provider_name})`, 
          value: cal.id 
        }
      });

      const calendarFilter = {
        placeHolder: 'Select calendar',
        title: 'In calendar',
        value: 'calendar.id',
        valueType: 'text',
        type: 'select',
        options: [...v2Calendars, ...sortedV3Calendars]
      }

      if (this.selectedTeamIndex === -1) {
        filters.push(calendarFilter)

        const selectedCalendarCondition = this.conditions[this.selectedCalendarIndex]
        let selectedCalendar
        if (selectedCalendarCondition && selectedCalendarCondition.value) {
          selectedCalendar = this.$store.state.calendars.calendars.filter((cal) => !cal.provider_id || teamIds.includes(cal.provider_id)).find(x => x.id === selectedCalendarCondition.value)
        }

        if (selectedCalendar && selectedCalendar.team_members && selectedCalendar.team_members.length) {
          const userIds = selectedCalendar.team_members.filter(x => x.selected).map(x => x.user_id)
          const users = this.$store.state.users.users.filter(x => userIds.includes(x.id))
            .map(user => {
              return {
                title: [user.first_name, user.last_name].filter(val => val).join(' '),
                value: user.id
              }
            })

          if (users) {
            const userOption = {
              placeHolder: 'Select User',
              title: 'Select User',
              value: 'appointment.assignedUserId',
              valueType: 'text',
              type: 'select',
              options: users
            }

            filters.push(userOption)
          }
        }
      }

      const teams = this.$store.getters['teams/calendarProviders']

      if (teams && teams.length) {
        const calendarProviderOption = {
          placeHolder: 'Select Calendar Team',
          title: 'Select Calendar Team',
          value: 'appointment.calendarProviderId',
          valueType: 'text',
          type: 'select',
          options: teams.map(team => {
            return {
              title: team.calendar_provider_name,
              value: team.id
            }
          })
        }

        if (this.selectedCalendarIndex === -1) {
          filters.push(calendarProviderOption)

          let users
          const selectedTeamCondition = this.conditions[this.selectedTeamIndex];
          if (selectedTeamCondition && selectedTeamCondition.value) {
            const selectedTeam = this.$store.state.teams.teams.find(x => x.id === selectedTeamCondition.value);

            if (selectedTeam) {
              const userIds = selectedTeam.user_ids
              if (userIds) {
                users = this.$store.state.users.users.filter(x => userIds.includes(x.id))
                .map(user => {
                  return {
                    title: [user.first_name, user.last_name].filter(val => val).join(' '),
                    value: user.id
                  }
                })
              }
            }
          }

          if (users) {
            const userOption = {
              placeHolder: 'Select User',
              title: 'Select User',
              value: 'appointment.assignedUserId',
              valueType: 'text',
              type: 'select',
              options: users
            }

            filters.push(userOption)
          }
        }

        if (this.selectedTeamIndex === -1 && this.selectedCalendarIndex === -1) {
          filters.push({
            placeHolder: 'Only for Team Calendar',
            title: 'Only for Team Calendar',
            value: 'appointment.isV3',
            valueType: 'text',
            type: 'select',
            options: [{ title: 'Yes', value: true }, { title: 'No', value: false } ]
          })
        }
      }

      filters.push({
        placeHolder: 'Select status',
        title: 'Appointment status is',
        value: 'appointment.status',
        valueType: 'text',
        type: 'select',
        options: [{ title: 'new', value: 'new' }, { title: 'confirmed', value: 'confirmed' }, { title: 'cancelled', value: 'cancelled' }, { title: 'Showed', value: 'showed' }, { title: 'No-show', value: 'noshow' }, { title: 'invalid', value: 'invalid' }]
      })

      filters.push({
        id: "has-tag",
        placeHolder: "Has Tag",
        title: "Has Tag",
        value: "contact.tags",
        valueType: "text",
        operator: "index-of-true",
        type: "select",
        options: this.tags
      })

      return filters
    }
  },
  watch: {
    '$route.params.location_id': async function (id) {
      await Promise.all([
        this.$store.dispatch('teams/syncAll', id)
      ])
    },
    selectedCalendarId() {
      if (this.selectedUserIndex !== -1) {
        this.conditions.splice(this.selectedUserIndex, 1);
      }

      if (this.selectedTeamIndex !== -1) {
        this.conditions.splice(this.selectedTeamIndex, 1);
      }

      if (this.selectedTeamCalendarIndex !== -1) {
        this.conditions.splice(this.selectedTeamCalendarIndex, 1);
      }
    },
    selectedTeamId() {
      if (this.selectedUserIndex !== -1) {
        this.conditions.splice(this.selectedUserIndex, 1);
      }

      if (this.selectedCalendarIndex !== -1) {
        this.conditions.splice(this.selectedCalendarIndex, 1);
      }

      if (this.selectedTeamCalendarIndex !== -1) {
        this.conditions.splice(this.selectedTeamCalendarIndex, 1);
      }
    }
  },
  methods: {
    clearInvalidValues(val, oldVal) {
      // TO DO
    }
  }
})
</script>

