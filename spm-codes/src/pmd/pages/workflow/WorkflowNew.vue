<template>
  <section
    :class="['hl_wrapper--inner workflow', {'sidebar-hide': hideAsideSection }]"
    id="workflow"
  >
    <section
      :class="['hl_workflow--main', {'sidebar-hide': hideAsideSection}]"
      id="hl_workflow--main"
    >
      <moon-loader size="40px" v-if="loading"/>
      <div v-if="workflow.id" class="row">
        <div class="col-md-3 mb-4">
          <input
            type="text"
            class="form-control form-light"
            placeholder="Workflow name"
            v-model="workflow.name"
            @change="workflow.save()"
          >
        </div>
      </div>
      <div class="flow_main_content">
        <ul
          v-if="!loadingData"
          :class="[
          'flow_main',
          {
            'dragging': dragging,
            'goto-dragging': isGotoDragging,
            'loading': loading
          }
        ]"
          v-workflow-width
          :style="transformStyle"
          id="flow_main"
        >
          <li class="triggers">
            <ul class="flow_start">
              <li v-if="triggers" v-for="trigger in triggers" :id="trigger.id">
                <trigger-label :trigger="trigger" @trigger-selected="selectTrigger"/>
              </li>
              <li>
                <a
                  @click="addNewTrigger"
                  class="workflow_card--new"
                  id="add-trigger"
                  data-toggle="tooltip"
                  data-placement="bottom"
                  title="Add new way to start this automation."
                >
                  <div class="bubble" data-toggle="modal" data-target="#new-automation-modal">
                    <p>Add new trigger</p>
                  </div>
                </a>
              </li>
            </ul>
          </li>
          <li class="initial-node">
            <ul class="flow_inner">
              <add-bubble :at-end="this.templates.length === 0" css-class="first-node"/>
            </ul>
          </li>
          <node-tree v-for="(child, index) in normilizeTree" :key="index" :nodes="child"/>
        </ul>
      </div>

      <div v-if="!loadingData" class="workflow-scaling d-inline-flex flex-column">
        <button
          class="btn btn-sm btn-primary mb-2"
          :disabled="transformScale == 1"
          @click="transformScale != 1 ? transformScale = parseFloat((transformScale + 0.2).toFixed(2)) : ''"
        >+</button>
        <button
          class="btn btn-sm btn-primary"
          :disabled="transformScale == 0.2"
          @click="transformScale > 0.2 ? transformScale = parseFloat((transformScale - 0.2).toFixed(2)) : ''"
        >-</button>
      </div>
    </section>

    <trigger-modal
      :to-create="toCreate"
      :trigger="selectedTrigger"
      :visible.sync="showTriggerModal"
      @created="triggerCreated"
      @deleted="triggerDeleted"
    />

    <aside-section
      :hideAsideSection.sync="hideAsideSection"
      :published="isPublished"
      @drag-start="dragging = true"
      @drag-end="dragging = false"
      @publish="publishWorkflow"
    />

    <event-modals
      :currentLocationId="currentLocationId"
      @save="saveTemplates"
      :campaignId="workflowId"
    />
  </section>
  <!-- END of .hl_wrapper -->
</template>

<script lang="ts">
import Vue from "vue";
import { Workflow, WorkflowTemplate, Trigger } from "../../../models";
import { get, size} from "lodash";
import { v4 as uuid } from "uuid";

import TopBar from "../../components/TopBar.vue";
import SideBar from "../../components/SideBar.vue";
const AsideSection = () => import( "../../components/workflow/AsideSection.vue");
const EventModals = () => import( "../../components/workflow/EventModals.vue");
const TriggerModal = () => import( "../../components/workflow/TriggerModal.vue");
const TriggerLabel = () => import( "../../components/workflow/TriggerLabel.vue");
const AddBubble = () => import( "../../components/workflow/AddBubble.vue");
const initSelectPicker = () => import( "../../components-initer/selectpicker");


var jsPlumbInstance = require("./../../../../node_modules/jsplumb/dist/js/jsplumb.js").jsPlumb.getInstance(
  {
    ConnectionsDetachable: false,
    ReattachConnections: false
  }
);

let maxWidth = undefined;

export default Vue.extend({
  components: {
    TopBar,
    SideBar,
    AsideSection,
    EventModals,
    TriggerModal,
    TriggerLabel,
    AddBubble
  },
  data() {
    return {
      transformScale: 1.0 as float,
      loading: true,
      isGotoDragging: false,
      dragging: false,
      hideAsideSection: false,
      loadingData: true,
      showTriggerModal: false,
      currentLocationId: "",
      triggers: [] as Trigger[],
      selectedTrigger: {} as Trigger,
      toCreate: false,
      workflowsCount: 0 as number,
      workflowId: "",
      workflow: {} as Workflow,
      treeData: [],
      templates: [] as WorkflowTemplate<T>[],
      dbTemplates: [] as WorkflowTemplate<T>[]
    };
  },
  watch: {
    triggers() {
      this.removeAndRedrawjsPlumbInstance();
    },
    hideAsideSection() {
      jsPlumbInstance.deleteEveryEndpoint();

      // As the aside bar will take some time to close due to transition animation, so it needs to redraw after it get finished
      setTimeout(() => {
        this.drawjsPlumbTriggerLine();
        this.drawjsNodeLabelLine();
      }, 500);
    },
    async templates() {
      this.loading = true;
      this.workflow.workflowData.templates = JSON.parse(
        JSON.stringify(this.templates)
      );
      await this.workflow.save();
      this.loading = false;
    }
  },
  computed: {
    transformStyle() {
      return {
        transform: `scale(${this.transformScale})`,
        "transform-origin": "50% 0%"
      };
    },
    isPublished(): boolean {
      return this.workflow.status === "published";
    },
    normilizeTreeLength(): number {
      return size(this.normilizeTree);
    },
    normilizeTree() {
      return this.treeData.groupBy("order");
    }
  },
  created() {
    // On drop event add new node after selected node
    this.$bus.$on("add-node", this.addNodeAfter);
    // On delete event remove that node
    this.$bus.$on("delete-node", this.deleteNode);
    // On click on any label show event modal
    this.$bus.$on("edit-action", this.editAction);
    // remove goto node connection
    this.$bus.$on("remove-goto", this.removeGoToNext);
    //
    maxWidth = null;

    // Add node to jsplumb addpoint
    // this.$bus.$on('add-to-jsplumb', this.addjsPlumbInstanceEndpointForNode);
    // this.$bus.$on('update-to-jsplumb', this.updatejsPlumbInstanceEndpointForNode);
    // this.$bus.$on('remove-from-js-plumb', this.removejsPlumbInstanceEndpointForNode);

    //
    this.initialData();
  },
  mounted() {
    jsPlumbInstance.deleteEveryEndpoint();

    let container = document.getElementById("flow_main");
    jsPlumbInstance.setContainer(container);
    // jsPlumbInstance.setZoom(0.5);

    jsPlumbInstance.bind("connectionDrag", connection => {
      this.isGotoDragging = true;
    });

    jsPlumbInstance.bind("connectionDragStop", connection => {
      this.handleGotoDragEnd(connection.source, connection.target);
      this.isGotoDragging = false;
    });
  },
  methods: {
    /**
     * Load initial data (workflows, templates) on created
     */
    async initialData() {
      this.currentLocationId = this.$router.currentRoute.params.location_id;
      this.workflowId = this.$router.currentRoute.params.workflow_id;
      this.workflowsCount = await Workflow.getCountByLocationId(
        this.currentLocationId
      );

      if (!this.workflowId) {
        this.createNewWorkflow();
      } else {
        this.workflow = await Workflow.getById(this.workflowId);

        // Probably has wrong ID in url, redirect it workflows page
        if (!this.workflow) {
          this.$router.replace({
            name: "workflows",
            params: this.$route.params
          });
        }
      }

      this.loadTriggers();
      this.templates = get(this.workflow, "workflowData.templates");
      this.listToTree();

      this.loadingData = false;
    },

    /**
     * Fetch & set triggers of current workflow
     */
    async loadTriggers() {
      if (this.workflowId) {
        this.triggers = await Trigger.getByWorkflowId(this.workflowId, this.currentLocationId);
      } else {
        this.triggers = [];
      }
    },

    /**
     * Create new trigger and open the trigger modal
     */
    addNewTrigger() {
      this.selectedTrigger = new Trigger();
      this.selectedTrigger.locationId = this.currentLocationId;
      this.selectedTrigger.workflowId = this.workflowId;
      this.selectedTrigger.belongsTo = "workflow";
      this.toCreate = true;
      this.showTriggerModal = true;
    },

    /**
     * Select trigger and open trigger modal for selected modal
     */
    selectTrigger(id: string) {
      const trigger = this.triggers.find(x => x.id === id);
      if (!trigger) return;

      this.selectedTrigger = trigger;
      this.toCreate = false;
      this.showTriggerModal = true;
    },

    /**
     * On Create trigger append that trigger to triggers array
     */
    triggerCreated(trigger: Trigger) {
      this.toCreate = false;
      this.triggers.push(trigger);
    },

    /**
     * On delete trigger remove it from triggers array
     */
    triggerDeleted(id: string) {
      let index = this.triggers.findIndex(x => x.id === id);

      if (index !== -1) {
        this.triggers.splice(index, 1);
      }
    },

    /**
     * Get element label from trigger
     */
    getElementForTriggerLabel(trigger: Trigger) {
      return $("#" + trigger.id);
    },

    /**
     * If the workflow is not exists then create new workflow, it must be create page
     */
    async createNewWorkflow() {
      this.workflow = new Workflow();
      this.workflow.locationId = this.currentLocationId;
      this.workflow.name = "Workflow " + (this.workflowsCount + 1);
      this.workflow.workflowData = {
        templates: []
      };
      await this.workflow.save();

      this.workflowId = this.workflow.id;
    },

    /**
     * On any label components click open the action event modals
     */
    editAction(id: string) {
      const template = <{ [key: string]: any }>(
        this.templates.find(x => x.id === id)
      );
      if (!template) return;

      this.$bus.$emit("open-event-model", template);
    },

    /**
     * Add new dragged node after whatever label, use to handle drop event
     */
    addNodeAfter(payload) {
      let index = this.templates.findIndex(x => x.id == payload.id);
      let node = undefined;
      if (index !== -1) {
        node = this.templates[index];
      }

      if (
        payload.data.type === "goto" &&
        ((index === -1 && this.templates.length) || (node && node.next))
      ) {
        alert("Can not add goto between the node.");
        return;
      }

      this.loading = true;
      if (this.hasSplit(payload.data)) {
        this.addConditional(payload.id);
        return;
      }

      payload.data.id = uuid();

      // It means the drop is on first node
      if (index === -1) {
        node = {
          parent: null,
          type: null
        };

        if (this.templates.length) {
          node.next = this.templates[0].id;
        }
      }

      let nodeData = this.buildNodeData(node, payload.data);

      if (index !== -1) {
        node.next = payload.data.id;
      }

      this.templates.splice(index + 1, 0, nodeData);
      this.includeChild(nodeData);
      this.editAction(nodeData.id);
      this.listToTree();

      this.removeAndRedrawjsPlumbInstance();
    },

    /**
     * Handle the drop event for conditional node
     */
    addConditional(id: string) {
      let index = this.templates.findIndex(x => x.id == id);
      let node;

      // It means the drop is on first node
      if (index === -1) {
        node = {
          id: null,
          parent: null,
          type: "if_else"
        };

        if (this.templates.length) {
          node.next = this.templates[0].id;
        }
      } else {
        node = this.templates[index];
      }

      let ifElseNodes = this.getConditionalNode(node);
      this.templates.splice(index + 1, 0, ...ifElseNodes);
      this.shiftConditionalNode(ifElseNodes[0], ifElseNodes[1]);
      this.editAction(ifElseNodes[0].id);
      this.listToTree();

      this.removeAndRedrawjsPlumbInstance();
    },

    /**
     * Get templates to consider to order chnage
     */
    getTemplatesToShiftOrder(node: WorkflowTemplate<T>, exclueIds: string[]) {
      return this.templates
        .filter(
          x =>
            x.parent === node.parent &&
            x.order >= node.order &&
            exclueIds.includes(x.id) === false
        )
        .groupBy("order");
    },

    /**
     * Shift below nodes, and add those nodes inside newly added conditional node's if part :)
     */
    shiftConditionalNode(
      ifNode: WorkflowTemplate<T>,
      elseNode: WorkflowTemplate<T>
    ) {
      this.changeTemplateOrders(
        this.getTemplatesToShiftOrder(ifNode, [ifNode.id, elseNode.id]),
        ifNode.order,
        ifNode.id
      );
    },

    /**
     * Shift below nodes after newly node
     */
    includeChild(node: WorkflowTemplate<T>) {
      this.changeTemplateOrders(
        this.getTemplatesToShiftOrder(node, [node.id]),
        this.hasSplit(node) ? 0 : node.order
      );
    },

    /**
     * Remove select node and move their children
     */
    deleteNode(id: string) {
      let index = this.templates.findIndex(x => x.id === id);
      if (index === -1) {
        // Nothing to delete
        return;
      }

      // jsPlumbInstance.deleteEndpoint(id);

      // Node to delete
      this.loading = true;
      let node = this.templates[index];
      let previousNode = this.templates.find(x =>
        Array.isArray(x.next) ? x.next[0] === node.id : x.next === node.id
      );

      if (this.hasSplit(node)) {
        let currentNodeIndex = this.templates.length - 1;
        if (previousNode) {
          let currentNextNodeId = previousNode.next[1]; // else node
          previousNode.next = undefined;

          let currentNextNode = this.templates.find(
            x => x.id === currentNextNodeId
          );
          while (true) {
            let next = currentNextNode.next;

            if (next) {
              if (Array.isArray(next)) {
                currentNextNode = this.templates.find(x => x.id === next[1]);
              } else {
                currentNextNode = this.templates.find(x => x.id === next);
              }
            } else {
              break;
            }
          }

          currentNodeIndex = this.templates.findIndex(
            x => x.id === currentNextNode.id
          );
        }

        this.templates.splice(index, currentNodeIndex - index + 1);
      } else {
        if (previousNode) {
          previousNode.next = node.next;
        }
        this.templates.splice(index, 1);

        this.changeTemplateOrders(
          this.getTemplatesToShiftOrder(node, [node.id]),
          node.order
        );
      }

      this.listToTree();
      this.removeAndRedrawjsPlumbInstance();
    },

    // TO-DO
    deleteRecuriseNodes(node: WorkflowTemplate<T>, index: number) {
      this.loading = true;
      let previousNode = this.templates.find(x => x.next === node.id);
      let elseNodeIndex = this.templates.findIndex(
        x =>
          x.parent === node.parent && x.order === node.order && x.id != node.id
      );

      if (previousNode) {
        previousNode.next = node.next;
      }

      // Remove If Node
      this.templates.splice(index, 1);

      // Remove Else Node
      if (elseNodeIndex !== -1) {
        this.templates.splice(elseNodeIndex, 1);
      }

      this.changeTemplateOrders(
        this.getTemplatesToShiftOrder(node, [node.id]),
        node.order
      );

      this.listToTree();
    },

    /**
     * Change order of templates
     */
    changeTemplateOrders(
      templatesByOrder,
      startOrder: number,
      parent?: string
    ) {
      Object.keys(templatesByOrder).forEach(function(order) {
        let templates = templatesByOrder[order] as WorkflowTemplate<T>[];

        startOrder++;

        templates.forEach((template: WorkflowTemplate<T>) => {
          if (parent) {
            template.parent = parent;
          }
          template.order = startOrder;
        });
      });
    },

    /**
     * Create & get new node data
     */
    buildNodeData(
      node: WorkflowTemplate<T>,
      childData: WorkflowTemplate<T>,
      ignoreNext?: false
    ) {
      return {
        id: childData.id,
        parent: this.hasSplit(node) ? node.id : node.parent,
        order: this.hasSplit(node)
          ? 0
          : node.order != undefined
          ? node.order + 1
          : 0,
        attributes: childData.attributes || {},
        name: childData.name,
        type: childData.type,
        cat: childData.cat,
        next: ignoreNext ? undefined : node.next
      };
    },

    /**
     * Create & get new nodes data for conditional node
     */
    getConditionalNode(node: WorkflowTemplate<T>) {
      const temp = [
        this.buildNodeData(node, {
          id: uuid(),
          name: "Yes",
          type: "if_else",
          attributes: {
            if: true
          },
          order: 0, // will not use, just for ts
          cat: "conditions"
        }),
        this.buildNodeData(
          node,
          {
            id: uuid(),
            name: "No",
            type: "if_else",
            attributes: {
              else: true
            },
            cat: "conditions",
            order: 0, // will not use, just for ts
            next: undefined
          },
          true
        )
      ];

      node.next = [temp[0].id, temp[1].id];

      return temp;
    },

    /**
     * Check if the given node has split
     */
    hasSplit(node: WorkflowTemplate<T>) {
      return node.type === "if_else";
    },

    /**
     * Build tree data from templates which will help to build the tree
     */
    listToTree() {
      var list = JSON.parse(JSON.stringify(this.templates));
      var map = {},
        node,
        roots = [],
        i;
      for (i = 0; i < list.length; i += 1) {
        map[list[i].id] = i; // initialize the map
        list[i].children = []; // initialize the children
      }

      for (i = 0; i < list.length; i += 1) {
        node = list[i];
        if (node.parent && node.parent !== null) {
          // if you have dangling branches check that map[node.parentId] exists
          list[map[node.parent]].children.push(node);
        } else {
          roots.push(node);
        }
      }

      this.treeData = roots;
      this.$nextTick(() => {
        this.loading = false;
      });
    },

    /**
     * Draw jsPlumb line between Triggers, Add to trigger button & first + Node
     */
    drawjsPlumbTriggerLine() {
      jsPlumbInstance.ready(() => {
        this.addjsPlumbInstanceConnection(
          $(".first-node"),
          $("#add-trigger"),
          "add-trigger-conn"
        );

        for (let i = 0; i < this.triggers.length; i++) {
          let trigger = this.triggers[i];
          this.addjsPlumbInstanceConnection(
            $(".first-node"),
            this.getElementForTriggerLabel(trigger),
            trigger.id
          );
        }
      });
    },

    /**
     * Remove the goto node's next data on click on goto label
     */
    removeGoToNext(payload) {
      jsPlumbInstance.deleteConnectionsForElement(payload.el);
      let node = this.templates.find(x => x.id === payload.id);

      if (node) {
        node.next = undefined;
        this.saveTemplates();
      }
    },

    /**
     * When drag stop on any node from goto, set its next
     */
    handleGotoDragEnd(source, target) {
      let node = this.templates.find(x => x.id === source.getAttribute("rel"));

      if (node) {
        node.next = target.getAttribute("rel");
        this.saveTemplates();
      }
    },

    /**
     * Init jsPlumb endpoint for all the workflow label
     */
    drawjsNodeLabelLine() {
      console.log("drawjsNodeLabelLine");
      $(".workflow_card:not(.trigger-label)").each((index, element) => {
        this.addjsPlumbInstanceEndpointForNode({
          el: $(element),
          isGoto: $(element).hasClass("goto"),
          id: $(element).attr("rel")
        });
      });
    },

    /**
     * Remove all jsPlumb endpoint & connections then redraw it
     */
    removeAndRedrawjsPlumbInstance() {
      jsPlumbInstance.deleteEveryEndpoint();

      this.$nextTick(() => {
        this.drawjsPlumbTriggerLine();
        this.drawjsNodeLabelLine();
      });

      console.log("removeAndRedrawjsPlumbInstance");
    },

    /**
     * Add endpoint on node to allow connecter
     */
    addjsPlumbInstanceEndpointForNode(payload) {
      if (payload.isGoto) {
        let node = this.templates.find(x => x.id === payload.id);
        jsPlumbInstance.addEndpoint(
          $(payload.el),
          { anchor: "BottomCenter", uuid: payload.id },
          {
            ConnectionsDetachable: false,
            ReattachConnections: false,
            endpoint: ["Dot", { radius: 5 }],
            paintStyle: { fill: "#a8cade" },
            isSource: true,
            scope: "green",
            connectorStyle: {
              lineWidth: 2,
              strokeWidth: 2,
              stroke: "#a8cade",
              dashstyle: "3 3"
            },
            connector: ["Bezier", { curviness: 20 }],
            maxConnections: 1
          }
        );

        if (node && node.next) {
          console.log(node, node.next);
          console.log($(`.workflow_card[rel="${node.next}"]`));
          this.addjsPlumbInstanceConnection(
            $(payload.el),
            $(`.workflow_card[rel="${node.next}"]`),
            [node.id, node.next]
          );
        }
      } else {
        jsPlumbInstance.addEndpoint(
          $(payload.el),
          { anchor: "TopCenter", uuid: payload.id },
          {
            ConnectionsDetachable: false,
            ReattachConnections: false,
            endpoint: ["Dot", { radius: 5 }],
            paintStyle: { fill: "#a8cade" },
            isTarget: true,
            scope: "green",
            connectorStyle: {
              lineWidth: 2,
              strokeWidth: 2,
              stroke: "#a8cade",
              dashstyle: "3 3"
            },
            connector: ["Bezier", { curviness: 20 }],
            maxConnections: 1
          }
        );
      }
    },

    updatejsPlumbInstanceEndpointForNode(payload) {
      // jsPlumbInstance.repaint($(payload.el));
    },

    removejsPlumbInstanceEndpointForNode(payload) {
      // jsPlumbInstance.deleteEndpoint($(payload.el));
    },

    /**
     * Add jsPlumb connection between two elements, used in trigger
     */
    addjsPlumbInstanceConnection(source, target, uuid) {
      jsPlumbInstance.connect({
        uuids: uuid,
        source: target,
        target: source,
        anchors: ["BottomCenter", "TopCenter"],
        paintStyle: {
          lineWidth: 2,
          strokeWidth: 2,
          stroke: "#a8cade"
          // dashstyle: "3 3"
        },
        endpointStyle: {
          width: 1,
          height: 1
        },
        endpoint: "Rectangle",
        connector: [
          this.triggers.length === 0 ? "Straight" : "Bezier",
          {
            curviness: 20
          }
        ]
      });
    },

    // Save templates, basically update after any edit action
    async saveTemplates() {
      this.workflow.workflowData.templates = JSON.parse(
        JSON.stringify(this.templates)
      );
      await this.workflow.save();

      this.listToTree();
    },

    /**
     * Publish the workflow
     */
    async publishWorkflow() {
      if (this.isPublished) return;

      await this.workflow.publish();
    }
  },
  directives: {
    workflowWidth: {
      inserted(el, binding, vnode) {
        let flowInner = $(el)
          .children(".spliter")
          .children(".flow_inner");
        if (flowInner.length === 0) {
          return;
        }

        let flowWidth = flowInner[0].clientWidth;
        maxWidth = flowWidth > 1400 ? flowWidth : 1380;

        $(el)
          .children("li")
          .not(".spliter")
          .not(".triggers")
          .css("min-width", maxWidth);
        $(el)
          .children("li.triggers")
          .css("min-width", maxWidth);
        $("#hl_workflow--main").scrollLeft(maxWidth / 4);
      },
      componentUpdated(el, binding, vnode) {
        let flowInner = $(el)
          .children(".spliter")
          .children(".flow_inner");
        if (flowInner.length === 0) {
          return;
        }
        let flowWidth = flowInner[0].clientWidth;
        flowWidth = flowWidth > 1400 ? flowWidth : 1380;
        if (maxWidth === flowWidth) {
          return;
        }

        maxWidth = flowWidth;

        $(el)
          .children("li")
          .not(".spliter")
          .not(".triggers")
          .css("min-width", maxWidth);
        $(el)
          .children("li.triggers")
          .css("min-width", maxWidth);

        vnode.context.removeAndRedrawjsPlumbInstance();
      }
    }
  },

  /**
   * Before destory page, remove all the event listner
   */
  beforeDestroy() {
    this.$bus.$off("add-node", this.addNodeAfter);
    this.$bus.$off("delete-node", this.deleteNode);
    this.$bus.$off("edit-action", this.editAction);
    this.$bus.$on("remove-goto", this.removeGoToNext);

    // this.$bus.$off('add-to-jsplumb', this.addjsPlumbInstanceEndpointForNode);
    // this.$bus.$on('update-to-jsplumb', this.updatejsPlumbInstanceEndpointForNode);
    // this.$bus.$on('remove-from-js-plumb', this.removejsPlumbInstanceEndpointForNode);
  }
});
</script>

<style lang="scss">
.hl_wrapper--inner {
  padding-bottom: 0;
}
#hl_workflow--main {
  position: relative;
  padding-top: 0;
  padding-bottom: 0;
  margin-left: 250px;
  min-height: 100vh;
  top: 0;
  left: 0;
  .v-spinner {
    position: absolute;
    top: 10%;
    left: 0;
    right: 0;
    z-index: 2;
  }
  .workflow-scaling {
    position: fixed;
    bottom: 30px;
  }
  .initial-node {
    // margin-left: auto;
    // margin-right: auto;
  }
  .flow_main_content {
    min-height: 100vh;
    position: relative;
    overflow: auto;
    padding-bottom: 30px;
  }
  // .flow_start {
  //   justify-content: left;
  // }
  ul.flow_main {
    position: relative;

    > li {
      margin-left: 0;
    }

    .triggers {
      display: block;
      ul {
        display: block;
        text-align: center;
        white-space: nowrap;
        li {
          display: inline-block;
        }
      }
    }

    li {
      display: -webkit-box;
      // margin-bottom: 0;
    }

    // Don't show tooltip on dragging or loading
    &.goto-dragging .workflow_actions_wrapper,
    &.loading .workflow_actions_wrapper {
      display: none;
    }

    // On dragging or loading add fade
    &.dragging .workflow_card--new,
    &.loading .workflow_card--new,
    &.dragging .workflow_card:not(.workflow_card--add),
    &.loading .workflow_card:not(.workflow_card--add) {
      opacity: 0.5;
    }

    // On dragging highlight the + node
    &.dragging .workflow_card--add a {
      border-color: #2ead2e;
      box-shadow: 0 0 5px #2ead2e;
      .icon.icon-plus {
        color: #2ead2e;
      }
    }
  }
}
</style>
