<template>
  <!-- Add Tags Modal -->
  <div class="modal fade hl_add-tags" id="add-tags-modal" tabindex="-1" role="dialog" ref="modal">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h5 class="modal-title" v-show="meta.type === 'delete'">
              <i class="far fa-trash-alt"></i> Delete Contacts
            </h5>
            <h5 class="modal-title" v-show="meta.type === 'add' || meta.type === 'remove'">
              <i class="icon icon-plus"></i>
              {{ meta.type | capitalize }} Tags
            </h5>
            <button type="button" class="close" @click="close" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <h4 v-show="meta.type === 'delete'">Delete the following contacts</h4>
            <h4
              v-show="meta.type === 'add' || meta.type === 'remove'"
            >{{ meta.type | capitalize }} tags to the following contacts</h4>
            <div class="avatar-group">
              <template v-if="!contacts.length > 0">
                <span style="font-size:15px; color:#188bf6">No contact selected</span>
              </template>
              <template v-for="(contact,index) in contacts">
                <Avatar v-if="index < 10" :contact="contact" :include_name="false"/>
                <template v-if="contacts.length >=10 ">
                  <span
                    v-if="index === contacts.length - 1"
                    style="margin-top:10px; font-size:19px,margin-left:10px; color:#188bf6"
                  >{{ total - 10 }} &nbsp;more contacts...</span>
                </template>
              </template>
            </div>
            <TagComponent v-model="selectedTags" v-if="meta.type !== 'delete'"/>
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner nav" v-if="!sending">
            <button type="button" class="btn btn-primary" @click="close">Cancel</button>
            <div style="display: inline-block;position: relative;" v-if="contacts.length > 0">
              <button
                v-if="meta.type === 'add' || meta.type === 'remove'"
                type="button"
                class="btn btn-blue"
                @click.prevent="meta.type === 'add'? saveTags(): deleteTags()"
                :class="{invisible: sending }"
              >{{ meta.type === 'add'? 'Add': 'Remove' }} &nbsp;Tags</button>
              <button
                v-if="meta.type === 'delete'"
                type="button"
                class="btn btn-blue"
                @click.prevent="deleteContact()"
                :class="{invisible: sending }"
              >Delete</button>
              <div
                style="position: absolute;left: 50%;transform: translate(-50%, -50%);top: 50%;"
                v-show="sending"
              >
                <moon-loader :loading="sending" color="#188bf6" size="24px"/>
              </div>
            </div>
          </div>
          <div class="modal-footer--inner nav" v-else>
            <div class="uploading" style="text-align: center;">
              <div class="progress">
                <div
                  class="progress-bar progress-bar-striped bg-success progress-bar-animated"
                  :style="percent"
                ></div>
              </div>
              <p>{{totalUploaded}}/{{total}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Contact, Tag, ImportRequest, ImportType, ImportStatus } from '../../../models'

const Avatar = () => import('../../components/Avatar.vue')
const MoonLoader = () => import('@/pmd/components/MoonLoader.vue')
const TagComponent = () => import('@/pmd/components/customer/TagComponent.vue')

let unsubscribeImportRequest: () => void;

export default Vue.extend({
  props: ['selectedCustomersId', 'selectAllContacts', 'totalContactsCount', 'showModal', 'meta', 'isAdmin', 'currentPageFilters'],
  components: {
    Avatar,
    MoonLoader,
    TagComponent
  },
  data() {
    return {
      contacts: [] as Contact[],
      // tagSearch: '',
      tags: [] as string[],
      currentLocationId: '',
      disabled: false,
      selectedTags: [],
      sending: false,
      totalUploaded: 0
    }
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchTags()
  },
  methods: {
    async fetchTags() {
      this.tags = (await Tag.getByLocationId(this.currentLocationId)).map(
        tag => tag.name
      )
    },
    async deleteTags() {
      if (!this.total) return
      this.sending = true
      this.totalUploaded = 0
      try {
        const authUser = await this.$store.dispatch('auth/get')
        const request = new ImportRequest()
        request.userId = authUser.userId
        request.locationId = this.currentLocationId
        request.status = ImportStatus.PENDING
        request.type = ImportType.BULK_UPDATE
        await request.save();

        if (unsubscribeImportRequest) unsubscribeImportRequest();
        unsubscribeImportRequest = await ImportRequest.getByIdRealTime(request.id).onSnapshot(snapshot => {
          const data = snapshot.data();
          this.totalUploaded = data.updated || 0;
          if (data.status === ImportStatus.SUCCESS) {
            this.onSuccess();
          }
        });
        const contactIds = !this.selectAllContacts ? this.selectedCustomersId : [];
        await Contact.bulkUpdateTags(contactIds, this.selectAllContacts, this.totalContactsCount, this.selectedTags, this.currentPageFilters, 'remove', request.id);
      } catch (e) {
        console.error(e)
      }
    },
    async saveTags() {
      if (!this.total) return
      this.sending = true
      this.totalUploaded = 0
      try {
        const authUser = await this.$store.dispatch('auth/get')
        const request = new ImportRequest()
        request.userId = authUser.userId
        request.locationId = this.currentLocationId
        request.status = ImportStatus.PENDING
        request.type = ImportType.BULK_UPDATE
        await request.save();
        if (unsubscribeImportRequest) unsubscribeImportRequest();
        unsubscribeImportRequest = await ImportRequest.getByIdRealTime(request.id).onSnapshot(snapshot => {
          const data = snapshot.data();
          this.totalUploaded = data.updated || 0;
          if (data.status === ImportStatus.SUCCESS) {
            this.onSuccess();
          }
        });
        const contactIds = !this.selectAllContacts ? this.selectedCustomersId : [];
        await Contact.bulkUpdateTags(contactIds, this.selectAllContacts, this.totalContactsCount, this.selectedTags, this.currentPageFilters, 'add', request.id);
      } catch (e) {
        console.error(e)
      }
    },
    async deleteContact() {
      if (!this.isAdmin) {
          alert('Only admins can delete contacts.')
          return;
      }
      if (!this.total) return
      if (!confirm('Are you sure you want to delete these contacts?')) return
      this.sending = true
      this.totalUploaded = 0
      try {
        const authUser = await this.$store.dispatch('auth/get')
        const request = new ImportRequest()
        request.userId = authUser.userId
        request.locationId = this.currentLocationId
        request.status = ImportStatus.PENDING
        request.type = ImportType.BULK_DELETE
        await request.save();
        if (unsubscribeImportRequest) unsubscribeImportRequest();
        unsubscribeImportRequest = await ImportRequest.getByIdRealTime(request.id).onSnapshot(snapshot => {
          const data = snapshot.data();
          this.totalUploaded = data.deleted || 0;
          if (data.status === ImportStatus.SUCCESS) {
            this.onSuccess();
          }
        });
        const contactIds = !this.selectAllContacts ? this.selectedCustomersId : [];
        await Contact.bulkDelete(contactIds, this.selectAllContacts, this.totalContactsCount, this.currentPageFilters, request.id);
      } catch (e) {
        console.error(e)
      }
    },
    onSuccess() {
      // existing modal
      if (unsubscribeImportRequest) unsubscribeImportRequest();
      this.sending = false
      this.cleanUp()
      this.$emit('closeModal', true)
      $(this.$refs.modal).modal('hide')
    },
    close() {
      $(this.$refs.modal).modal('hide')
      this.cleanUp()
      this.$emit('closeModal', false)
    },
    cleanUp() {
      this.selectedTags = []
    }
  },
  computed: {
    total(): number {
      return this.selectAllContacts ? this.totalContactsCount : this.selectedCustomersId.length;
    },
    percent(): { [key: string]: any } {
      return { width: (this.totalUploaded / this.total) * 100 + '%' }
    },
    exactMatch(): boolean {
      return this.tags.indexOf(this.tagSearch) !== -1
    },
    filteredTags(): string[] {
      let filtered = <string[]>lodash.difference(this.tags, this.selectedTags)
      filtered = lodash.filter(
        filtered,
        tag => tag.indexOf(this.tagSearch) !== -1
      )
      return filtered
    }
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.currentLocationId = id
      this.fetchTags()
    },
    showModal(val) {
      if (val) {
        $(this.$refs.modal).modal('show')
        return
      }
      $(this.$refs.modal).modal('hide')
    },
    async selectedCustomersId(ids) {
      this.contacts = await Promise.all(ids.slice(0, 10).map(id => Contact.getById(id)))
    }
  },
  filters: {
    capitalize: function(value) {
      if (!value) return ''
      value = value.toString()
      return value.charAt(0).toUpperCase() + value.slice(1)
    }
  },
  beforeDestroy() {
    if(unsubscribeImportRequest) unsubscribeImportRequest();
  }
})
</script>


<style>
</style>
