<template>
 <div class="sharemodal">
      <b-modal ref="modal-sm-d" size="sm" v-if="smartList" @hide="hidePopup" hide-footer>
      <template v-slot:modal-title>
         <moon-loader-title :iconClass="'fa fa-share-alt --gray'" :title="smartList.listName"
                            :loading="loading"/>
      </template>
      <template v-if="smartList">
         <!-- <div v-if="currentUser"> {{currentUser.isAdmin}} </div> -->
          <div class="py-3 mx-3 info-blue --blue">
             <div class="mx-2">
               <span class="input-group-addon mx-1">
                 <i class="fa fa-info-circle" ></i>
              </span>
              <span class="mx-1 --gray">A global list is created when a smart list is shared with all users.</span>
            </div>
             <div class="mx-2">
               <span class="input-group-addon mx-1">
                 <i class="fa fa-info-circle" ></i>
              </span>
              <span class="mx-1 --gray">A global list will always remain shared with all administrators.</span>
            </div>
              <div class="mx-2">
               <span class="input-group-addon mx-1">
                 <i class="fa fa-info-circle" ></i>
              </span>
              <span class="mx-1 --gray">Only an administrator can change filters in a global list and can delete a global list.</span>
            </div>
          </div>
          <div class="py-3">
            <div class="option mx-3 optionBlue" v-if="(adminUsers && adminUsers.length) || (nonAdminUsers && nonAdminUsers.length)">
                <!-- :disabled="vm.areAllPagesSelected" -->
                <input type="checkbox" id="all" :checked='sharedWithAll' @change="allSelectionChange"/>
                <label for="all">Share with all users (Global list)</label>
            </div>
            <div class="mx-3" v-else>
              <div class="py-3 px-3 info-blue --blue">
                There are no users to share with
              </div>
            </div>
            <div class="option mx-3 optionBlue" v-for="user in adminUsers" :key="user.id" >
              <input type="checkbox"
                      :disabled="smartList.global"
                      :id="user.id"
                      :value="user.id"
                      @change="selectionChange"
                      v-model="shared">
              <label :for="user.id">{{user.name}}&nbsp;(admin)</label>
            </div>
            <div class="option mx-3 optionBlue" v-for="user in nonAdminUsers" :key="user.id" >
              <input type="checkbox"
                      :id="user.id"
                      :value="user.id"
                      @change="selectionChange"
                      v-model="shared">
              <label :for="user.id">{{user.name}}</label>
            </div>
          </div>
          <!-- <div class="form-group mx-4">
            <label>Name</label>
            <input type="text" v-model="name" class="form-control" placeholder="Name" />
            <div v-if="showErr"><span class="--red">{{showErr}}</span></div>
          </div> -->
        <div class="modal-footer" v-if="loading !== true">
          <div class="modal-footer--inner" style="min-height:38px;">
            <a href="javascript: void(0)"
              @click="hidePopup()"
              class="mx-1"
              data-dismiss="modal">
              Cancel
             </a>
             <button :disabled='!showSave' v-if="(adminUsers && adminUsers.length) || (nonAdminUsers && nonAdminUsers.length)"
                     :class="{'no-pointer': !showSave, 'light-gray-background': !showSave}"
                     type="button" @click="saveChanges()"
                     class="btn btn-primary mx-1 py-2">
                     Save
             </button>
          </div>
        </div>
      </template>
    </b-modal>
  </div>
</template>

<script lang="ts">

import Vue from 'vue';
import { SmartList, User } from '@/models'
import { Utils } from '@/util/utils';
import {isEqual as lodashIsEqual, sortBy as lodashSortBy, uniq as lodashUniq}  from 'lodash';

export default Vue.extend({
  name:'SmartListSharingModal',
  props: {
    smartListId : String
  },
  data(){
    return {
      loading : false,
      showErr: undefined,
      showSave: false,
      sharedWithAll: false,
      shared: [] as string[],
      nonAdminUsers: [] as User[],
      adminUsers: [] as User[],
      filters: [],
      currentUser: null as User | null,
      smartList:null as SmartList | null,
    }
  },
  watch: {
    smartListId(newId: string){
      this.loadList(newId);
    }
  },
  methods :{
    async loadList(id: string){
      if (Utils.isEmptyStr(id)) return;
      try {
        this.loading = true;
        this.smartList = await SmartList.getById(id);
        if (!this.smartList) return;
        this.currentUser = new User(await this.$store.dispatch('user/get'));
        if (this.smartList) {
          let users = await this.$store.getters['users/getAll'];

          if (users && this.currentUser) users = users.filter((a : User)=> a.id !== (this.currentUser as User).id)
          this.nonAdminUsers = users.filter((a: User)=> !a.isAdmin);
          this.adminUsers = users.filter((a: User)=> a.isAdmin);

          const currentlySharedWithIds = (this.smartList.sharedWith || []).map(a=> a.user_id);
          const nonAdminUserIds = this.nonAdminUsers.map((a: User)=> a.id)
          const adminUserIds = this.adminUsers.map((a: User) => a.id);

          if (this.smartList.global){
            this.sharedWithAll = lodashIsEqual( lodashSortBy(currentlySharedWithIds), lodashSortBy(nonAdminUserIds));
            this.shared = lodashUniq([... adminUserIds,  ...currentlySharedWithIds]);
          } else {
            this.sharedWithAll = false;
            this.shared = lodashUniq((this.smartList.sharedWith|| []).map(a=> a.user_id));
          }

        }
      } finally {
        this.loading = false
      }
    },
    async saveChanges(){
      if (!this.smartList) return;
      this.loading = true;
      try {
        console.log(`this.sharedWithAll  ${this.sharedWithAll}`)
        if (this.sharedWithAll === true) {
          await this.smartList.shareAll();
          if (this.currentUser && !this.currentUser.isAdmin) this.hidePopup();
        }
        else {
          let shareableUsers = [];
          // alert(this.smartList.global)
          if (this.smartList.global === true){
              shareableUsers=  [...this.nonAdminUsers];
             // alert(this.nonAdminUsers.length);
          } else shareableUsers = [...this.adminUsers, ...this.nonAdminUsers];

          const unshared = shareableUsers.filter(a=> !this.shared.includes(a.id)).map(a=> a.id);
          const sharedwith = shareableUsers.filter(a=> this.shared.includes(a.id)).map(a=> a.id);
          await this.smartList.shareWithSelect(sharedwith, unshared);
        }
        this.$emit('saved', this.smartList.listName);
        this.hidePopup();
      } catch(error){
        console.log(error);
      }
      finally {
        this.loading = false;
        this.showSave = false;
      }
    },
    allSelectionChange(event:any){
      const allCheck = event && event.target && (event.target.checked === true);
       this.sharedWithAll = allCheck;
      if (allCheck === true){
        this.shared = this.allUsers().map(a=> a.id);
      } else  {
         this.shared = this.smartList && this.smartList.global ? this.adminUsers.map(a=> a.id) :  [];
      }
      this.detectChanged();
    },
    allUsers(){
      return [...this.adminUsers, ...this.nonAdminUsers];
    },
    selectionChange(event: any){
      if (lodashIsEqual(lodashSortBy(this.shared), lodashSortBy(this.allUsers().map(a=> a.id)))){
        this.sharedWithAll = true;
      } else {
        this.sharedWithAll = false;
      }
      this.detectChanged();
      // console.log(event.target);
    },
    detectChanged(){
      if (!this.smartList || !this.smartList.sharedWith) return;
      if (this.smartList.global) {
        const nonAdminShared = this.nonAdminUsers.filter(a=> this.shared.includes(a.id)).map(a=> a.id);
        if (!lodashIsEqual(lodashSortBy(nonAdminShared), lodashSortBy(this.smartList.sharedWith.map(a=> a.user_id)))){
          this.showSave = true;
          return;
        }
        this.showSave = false;
      } else if (!lodashIsEqual(lodashSortBy(this.shared), lodashSortBy(this.smartList.sharedWith.map(a=> a.user_id)))){
        this.showSave = true;
        return;
      }
      this.showSave = false;
    },
    showPopup() {
      this.loading = false;
      this.$refs['modal-sm-d'].show()
    },
    hidePopup() {
      this.loading = false;
      this.$refs['modal-sm-d'].hide()
      this.$emit('closed');
    },
  },
  // https://stackoverflow.com/questions/17629286/css-vertical-line-between-bullets-in-an-unordered-list/17629815
  async mounted(){
    await this.loadList(this.smartListId)
    this.showPopup();
  }
});

</script>

<style lang="scss">

  .sharemodal div.modal-dialog.modal-sm {
    margin-top: 100px;
  }


  .option>input[type="checkbox"]:disabled+label::after {
    background: #dddddd;
    border-color: #dddddd;
  }

  .optionBlue > label:after{
    background:  #188bf6;
    border: #188bf6 ;
  }
  .modal .modal-body {
      padding: 10px;
  }
  .no-pointer {
    cursor:none;
  }

  .light-gray-background{
    background-color: lightgray  !important;
    border-color:lightgray !important;
  }

</style>

