<template>
  <div id="invite-user" class="launchpad-item-sub-container bg-white my-1">
    <div class="items-tiles mx-3 my-2">
     <img src="https://static.msgsndr.com/img/icon-add-member.a0b41d16.svg" class="integration-icon mx-4">
      <div class="item-info">
        <div class="font-normal text-sm text-gray-500">
          Quickly add one or more team members.<br />
          <span style="font-size: 12px; font-style: italic; color: #718096"
            >(The new user(s) will have the same permissions like yours except
            the ability to add new users.)</span
          >
        </div>
      </div>
      <div class="flex flex-col justify-center item-end content-end w-40">
        <div
          class="flex flex-row justify-end item-end content-end w-full pr-4 pl-4"
        >
          <button
            id="launchpad-lc-sendlink"
            class="green-btn green-filled-button green-btn connetGoogle"
            @click="addUsersModal = true"
          >
            <span>Add</span>
            <!-- <i v-show="isLeadConnectorConnected" class="fa fa-check"></i> -->
          </button>
        </div>
      </div>
    </div>
    <AddUsersModal
      :modalShow="addUsersModal"
      :currentLocationId="currentLocationId"
    />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { Location } from '@/models'
import { trackGaEvent } from '@/util/helper'
import IconAddMember from '@/assets/pmd/img/launchpad/icon-add-member.svg'
import AddUsersModal from './AddUsersModal.vue'

export default Vue.extend({
  props: ['currentLocationId', 'userid'],
  components: {
    AddUsersModal,
    IconAddMember
  },
  data() {
    return {
      addUsersModal: false,
    }
  },
  mounted() {
    this.$root.$on('close-modal', () => {
      this.addUsersModal = false
    })
  },
})
</script>
