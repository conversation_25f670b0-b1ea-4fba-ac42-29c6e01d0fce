<template>
  <div class="tab-pane fade show active" id="email" role="tabpanel" aria-labelledby="email-tab">
    <form>
      <div class="email-box">
        <div class="form-group inline-form-name" v-show="threadId == ''">
          <UITextInput
            placeholder="From Name"
            :class="'msgsndr2'"
            v-model="name"
            name="msgsndr2"
            autocomplete="msgsndr2"
          />
          <!-- Hidden span so that the alignment is correct when there's an error -->
          <span class="hidden-span" v-if="errors.first('msgsndr3')">Hidden</span>
        </div>
        <div class="form-group inline-form-email" v-show="threadId == ''">

          <UITextInput
            placeholder="From Email"
            :class="'msgsndr3'"
            v-model="from"
            v-validate="'email'"
            name="msgsndr3"
            autocomplete="msgsndr3"
          />
          <span
            v-if="showFromWarning && user.type == 'agency'"
            class="hl_help-article"
            style="position: absolute; top: 62px; right: 40px"
            v-b-tooltip.hover
            title="SMTP providers has some limitations. Click here to know more."
          >
            <a href="https://youtu.be/eltAOMf3AUA" target="blank">
              <i class="fa fa-exclamation-triangle text-warning" id="fa-question-circle"></i>
            </a>
          </span>
          <span style="color:red">{{ errors.first('msgsndr3') }}</span>
        </div>
        <div class="form-group" v-show="threadId == ''">
          <!-- <input type="text" class="form-control" data-lpignore="true" placeholder="Subject" v-model="subject" /> -->
           <UITextInput
            placeholder="Subject"
            v-model="subject"
            name="subject"
          />
        </div>
        <div class="form-group">
          <editor
            api-key="a62fc67jmelptppyvtbxwvtesdksxsumzvj5l3q16kalszes"
            v-validate="{handlebars:[true]}"
            data-vv-validate-on="input"
            name="editor"
            :init="editorOptions"
            v-model="body"
            ref="tinymce"
          ></editor>
          <span v-show="errors.has('editor')" class="--red">There are issues in your custom variables, please fix them before sending.</span>
        </div>
        <div v-if="attachments && attachments.length > 0">
          <div
            v-for="(attachment, attachmentindex) in attachments"
            class="mx-2 py-2"
            style="display: inline-block;position:relative"
            :key="attachmentindex"
          >
            <GHLFileAttachment :fileUrl="attachment.url" />
            <a
              @click="deleteAttachment(attachment.name)"
              style="position:absolute;right: -14px; top: -4px; padding: 4px; color: rgb(233, 61, 61) !important;"
              class="pointer"
            >
              <i class="fas fa-minus-circle"></i>
            </a>
          </div>
        </div>

        <div
          v-if="errorMsg"
          class="--red email-action-bottom"
          style="text-align: center;"
        >{{errorMsg}}</div>
        <div class="email-action-bottom">
          <div class="email-action-bottom-left">
            <input
              type="file"
              id="image_uploads"
              name="image_uploads"
              multiple
              @change="updateAttachments"
              style="display:none;"
              ref="upload"
            />
            <a
              @click.prevent="$refs.upload.click()"
              href="javascript:void(0);"
              class="use-template py-0 px-2"
            >
            <i class="fa fa-paperclip" v-b-tooltip.hover title="Attach File"></i>
            </a>
            <a @click.prevent="pickTemplate" class="use-template py-0 px-2" >
              <i class="icon icon-document-text" v-b-tooltip.hover title="Use a template"></i>
            </a>
            <a @click.prevent="requestPayment" class="use-template py-0 px-2" >
              <i class="icon icon-request" v-b-tooltip.hover title="Request Payment"></i>
            </a>
          </div>
          <div class="email-action-bottom-right">
            <!-- <button
              type="submit"
              class="btn btn-link"
              :class="{invisible: sending}"
              @click.prevent="clear"
            >Clear</button>
            <div style="display: inline-block;position: relative;">
              <button
                @click.prevent="send"
                type="submit"
                class="btn btn-blue"
                :class="{invisible: sending}"
                :disabled="sending || errors.has('editor')"
              >
                <i class="icon-send"></i>
                <span>Send</span>
              </button>
              <div
                style="position: absolute;left: 50%;transform: translate(-50%, -50%);top: 50%;"
                v-show="sending"
              >
                <moon-loader :loading="sending" color="#188bf6" size="24px" />
              </div>
            </div> -->

            <UIButton
              :class="{invisible: sending}"
              @click.prevent="clear"
              use="outline"
            >Clear</UIButton>

            <div style="display: inline-block;position: relative;" class="ml-2">
              <UIButton
                @click.prevent="send"
                type="submit"
                :loading="sending"
                :disabled="sending || errors.has('editor')"
              >
                <SendWhiteIcon class="mr-2"/>
                <span>Send</span>
              </UIButton>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import ImageTools from '../../../util/image_tools'
import { EmailMessage, User, Template, Contact, Location, SMTPService, Link } from '@/models'
import Editor from '@tinymce/tinymce-vue'
import { mapState } from 'vuex'
import { UserState, FileAttachment } from '@/store/state_models'
import { EventBus } from '@/models/event-bus'
import { MessageTemplate, EmailTemplate } from '@/models/campaign'
import firebase from 'firebase/app'
import { v4 as uuid } from 'uuid'
import { CustomFields } from '../../../util/custom_fields'
import { IFrameDisableDrops } from '@/util/disable_iframe_drop'
import { defaultTinyMceOptions } from '../../../util/tiny_mce_defaults';
const GHLFileAttachment = () => import('../GHLFileAttachment.vue')
import { trackGaEvent } from "@/util/helper";
import SendWhiteIcon from '/public/pmd/img/icon-send-white.svg'
//import { CustomField, CustomValue, } from '@/models';
import ConversationsService from '../../../services/ConversationsService'

const imageTools = new ImageTools()
const triggerLinks: { [key: string]: any }[] = [];
let customFieldValues = [] as { [key: string]: any }
const path = require('path');
let isReply = false;
let replyFromName, replyFromEmail, replyThreadId, replySubject, replyMessageId;

export default Vue.extend({
  props: ['conversation', 'contact', 'replyEmailMessage'],
  components: { editor: Editor, GHLFileAttachment, SendWhiteIcon, },
  data() {
    return {
      errorMsg: '',
      threadId: '',
      subject: '',
      body: '',
      from: '',
      fromName: '',
      name: '',
      firstLetter: true,
      currentLocationId:'',
      editorOptions: {
        ...defaultTinyMceOptions,
        height: 100,
        init_instance_callback: this.initEditor,
        plugins: [
          'advlist autolink link image lists charmap hr anchor pagebreak spellchecker',
          'searchreplace wordcount visualblocks visualchars code fullscreen insertdatetime media nonbreaking',
          'table contextmenu directionality emoticons template textcolor link paste'
        ],
        toolbar:
          'bold italic bullist numlist link image forecolor backcolor fontselect | fontsizeselect styleselect  | mybutton templates | triggerlinks',
        setup: function(editor: any) {
          editor.addButton('mybutton', {
            type: 'listbox',
            text: 'Custom Values',
            icon: false,
            onselect: function(e: any) {
              editor.insertContent(this.value())
              this.value(null)
            },
            values: customFieldValues,
            onPostRender: function() {
              // Select the second item by default
              this.value('&nbsp;<em>Some italic text!</em>')
            }
          });

          editor.addButton('triggerlinks', {
						type: 'listbox',
						text: 'Trigger Links',
						icon: false,
						onselect: function (e: any) {
							editor.insertContent(this.value());
							this.value(null);
						},
						values: triggerLinks
          });

          // editor.on('init', function (e) { // Make sure that Verdana 11pt is selected when starting the editor
          //   editor.execCommand("fontName", false, "Verdana");
          //   editor.execCommand("fontSize", false, "11pt");
          // });
        },
        link_list: triggerLinks,
        paste_data_images: true,
        images_upload_handler: this.editorImagePath
      },
      sending: false,
      filesAdded: [] as FileAttachment[],
      existingFiles: [] as FileAttachment[],
      showFromWarning: false
    }
  },
  async created() {
    if (this.user) {
      await this.fetchData();
    }
    if (this.contact && this.contact.locationId) {
      const location = new Location(await this.$store.dispatch('locations/getById', this.contact.locationId));
      if (location.defaultEmailService && location.defaultEmailService !== 'mailgun') {
        this.showFromWarning = await this.$store.dispatch('defaultEmailService/checkFromVisibility', location.defaultEmailService);
      }
    }
  },
  watch: {
    contact: async function(newVal, oldVal) {
      this.loadCustomFieldItems(newVal)
      //this.loadEditorImagePath(newVal);
    },
    subject(val) {
      if(this.firstLetter && val.length === 1) {
        return this.subject = lodash.capitalize(val)
      }
      this.firstLetter = false;
      this.subject = val
    },
    conversation: async function (newVal, oldVal) {
      if ((newVal && newVal.id) !== (oldVal && oldVal.id)) { // Reset all fields when chaging conversation
        this.clear(false) // Keep from email and name
        this.autofill();
      }
    },
    replyEmailMessage(val: EmailMessage, old) {
      if (val && val !== old) {
        this.replyToEmailListener(val)
      }
    //       },
    // body(val) {
    //   if (val === '') { // Make sure that Verdana 11pt is selected when clearing the editor
    //     this.$nextTick(() => {
    //       this.$refs.tinymce.editor.editorCommands.execCommand("fontName", false, "Verdana")
    //       this.$refs.tinymce.editor.editorCommands.execCommand("fontSize", false, "11pt")
    //     })
    //   }
    }
  },
  methods: {
    requestPayment() {
      trackGaEvent('Text to Pay', 'Request button clicked (Email)', this.$route.params.location_id, 1);
      EventBus.$emit('request_payment', 'email')
    },
    invoiceCreated(invoice_url: string) {
      this.body = `<a href="${invoice_url}">${invoice_url}</a><br/>` + this.body
    },
    async loadTriggerLinks(id: string) {
      if (!triggerLinks || triggerLinks.length < 1) {
        let obj = this.$store.getters['conversation/getTriggerLinksMenuItem']
        if (!obj || !obj.text) {
          obj = await this.$store.dispatch('conversation/fetchTriggerLinks', {locationId: this.$route.params.location_id})
        }
        delete obj.location_id
        triggerLinks.push.apply(triggerLinks, obj.menu)
      }
    },
    async loadCustomFieldItems(newVal) {
      customFieldValues.length = 0
      if (!newVal || !newVal.locationId) return
      customFieldValues.push.apply(
        customFieldValues,
        await CustomFields.getList(newVal ? newVal.locationId : undefined, true)
      )
    },
    async fetchData() {
        this.currentLocationId = this.$router.currentRoute.params.location_id;
        if(!isReply) {
        this.autofill();
        this.name = this.user.fullName;
        this.from = this.user.email;
        this.threadId = '';
        this.subject = '';
        }
        else {
        if (this.user.userEmailSignature[this.currentLocationId] && this.user.userEmailSignature[this.currentLocationId].reply_signature) {
           this.body = "<p>\n</p>" + "<p>\n</p>" + this.user.userEmailSignature[this.currentLocationId].signature;
        }
        this.firstLetter = false;
        this.name = replyFromName;
        this.from = replyFromEmail;
        this.subject = replySubject;
        this.threadId = replyThreadId
        setTimeout(() =>  isReply = false, 500)
        }
    },
    pickTemplate() {
      EventBus.$emit('select_email_template')
    },
    autofill() {
      if (this.user.userEmailSignature[this.currentLocationId] && this.user.userEmailSignature[this.currentLocationId].autofill) {
           this.body = "<p>\n</p>" + "<p>\n</p>" + this.user.userEmailSignature[this.currentLocationId].signature;
        }
    },
    initEditor(editor: any) {
      this.$emit('editorLoaded')
      //this.loadEditorImagePath(this.contact);
    },
    async editorImagePath(blobInfo: any, success: any, failure: any) {
      //console.log("Got blobInfo:", blobInfo);
      //console.log("Got blobInfo:", blobInfo.blob());
      if (!this.contact) {
        alert('Contact details required to calculate image path.')
        return
      }
      let mceEditorImagePath = `location/${this.contact.locationId}/contact/${this.contact.id}`
      if (!mceEditorImagePath || mceEditorImagePath.trim() === '') {
        alert('Image upload path not found.')
        return
      }
      let attachment = blobInfo.blob()

      var uploadPath = firebase.storage().ref(mceEditorImagePath + '/' + uuid())
      const snapshot = await uploadPath.putString(blobInfo.base64(), 'base64', {
        contentType: attachment.type,
        contentDisposition: `inline; filename="${attachment.name}"`,
        customMetadata: { name: attachment.name }
      })
      let url = await snapshot.ref.getDownloadURL()
      success(url)
      console.log(`Image uploaded to ${url}`)
    },
    // loadEditorImagePath(newVal:Contact){
    //   if(newVal && newVal.locationId && newVal.id){
    //     mceEditorImagePath= `location/${newVal.locationId}/contact/${newVal.id}`;
    //   } else mceEditorImagePath = '';
    // },
    discard() {
      const data: () => object = <(() => object)>this.$options.data
      if (data) Object.assign(this.$data, data.apply(this))
      EventBus.$emit('pick_tab', 'sms')
    },
    clear(clearFrom = true) {
      this.threadId = ''
      this.body = ''
      if (clearFrom) {
        this.from = ''
        this.name = ''
      }
      this.subject = ''
      this.firstLetter = true
      this.filesAdded = []
      this.existingFiles = []
    },
    async getAttachmentsList(locationId, contactId) {
      const urls = [] as string[]
      if (this.filesAdded && this.filesAdded.length > 0) {
        const basePath = `location/${locationId}/contact/${contactId}/`
        const newURLs: string[] = await Promise.all(
          this.filesAdded.map(async attachment => {
            let imagePath = basePath + uuid() + attachment.name;
            var uploadPath = firebase.storage().ref(imagePath)
            let contentDisposition = attachment.type.includes('html') ? `attachment; filename="${attachment.name}"` : `inline; filename="${attachment.name}"`;
            const snapshot = await uploadPath.put(attachment.data, {
              contentType: attachment.type,
              contentDisposition,
              customMetadata: { name: attachment.name }
            })
            return await snapshot.ref.getDownloadURL()
          })
        )
        urls.push.apply(urls, newURLs.filter(url => url))
      }
      if (this.existingFiles)
        urls.push.apply(urls, this.existingFiles.map(item => item.url))
      return urls
    },
    async updateAttachments() {
      const element = <HTMLInputElement>this.$refs.upload
      if (!element.files) return
      for (var i = 0; i < element.files.length; i++) {
        const file = element.files[i]
        const selectedImage = this.filesAdded.filter(
          data => data.name === file.name
        )
        if (selectedImage.length > 0) continue
        const response = <File | Blob>(
          await imageTools.resize(file, { height: 1000, width: 1000 })
        )
        this.filesAdded.push({
          name: file.name,
          type: file.type,
          url: URL.createObjectURL(response),
          data: response
        })
      }
    },
    deleteAttachment(name: string) {
      let attachment = lodash.find(this.filesAdded, { name: name })
      if (attachment) {
        this.filesAdded.splice(this.filesAdded.indexOf(attachment), 1)
      }
      attachment = lodash.find(this.existingFiles, { name: name })
      if (attachment) {
        this.existingFiles.splice(this.existingFiles.indexOf(attachment), 1)
      }
    },
    async send() {
      this.errorMsg = ''
      if (!this.contact) {
        this.errorMsg = 'The contact not selected.'
        return
      }
      if (!this.contact.id || !this.contact.locationId) {
        this.errorMsg = 'Cannot identify contact and its location.'
        return
      }
      if (!this.contact.email) {
        this.errorMsg = 'The contact does not have an email address.'
        return
      }
      if (!this.body) {
        this.errorMsg = 'The e-mail requires a body text to be send.'
        return
      }

      let validator = await this.$validator.validate().then(valid => valid)
      if (!validator) {
        return
      }

      if(this.name) {
        this.fromName = this.name + ' <' + this.from + '>'
      }
      else {
        this.fromName = this.from
      }

      this.sending = true
      try {
        let urls = await this.getAttachmentsList(
          this.contact.locationId,
          this.contact.id
        )
        const data = {
          contactId: this.contact.id,
          subject: this.subject,
          html: this.body,
          emailFrom: this.fromName,
          threadId: this.threadId,
          userId: this.user.id,
          attachments: urls,
          replyMessageId: replyMessageId,
          type: 'Email'
        }
        let response = await ConversationsService.message(data)
        this.$emit('messageSent')
        this.clear(false) // Keep from email and name
      } catch (err) {
        this.$uxMessage('error', 'An error has ocurred, please try sending the message again or check you e-mail provider settings.')
        console.error(err)
      } finally {
        this.sending = false
        this.autofill();
      }
    },
    templateSelected(template: MessageTemplate) {
      console.log('Using template:', template)
      this.clear(false) // Keep from email and name
      const temp = <EmailTemplate>template
      this.body = temp.html
      this.subject = temp.subject
      if (!lodash.isEmpty(temp.attachments)) {
        let itr = temp.attachments.map(url => ({ url: url }))
        itr.forEach(async item => {
          const metadata = await firebase
            .storage()
            .refFromURL(item.url)
            .getMetadata()
          let obj = {
            type: metadata.contentType,
            name: metadata.customMetadata.name,
            url: item.url
          }
          this.existingFiles.push(obj)
        })
      }
    },
    replyToEmailListener(message: EmailMessage) {
      const data: () => object = <(() => object)>this.$options.data
      isReply = true;
      let threadReply;
      if (data) Object.assign(this.$data, data.apply(this))
      if (message.direction === 'outbound') {
          (message.from && message.from.includes(('<'))) ?
          threadReply = [(message.from.substring(0, message.from.indexOf('<'))).trim(), message.from.substring(message.from.indexOf('<')+1, message.from.lastIndexOf('>'))]
          : threadReply = [message.name, message.from];
      }
      else {
          threadReply = [message.name || this.user.fullName, Array.isArray(message.to) ? message.to[0] : message.to];
      }
      replyThreadId = message.threadId
      replySubject =  message.subject
      replyFromName= threadReply[0] || this.name // if none found, don't change
      replyFromEmail = threadReply[1] || this.from // if none found, don't change
      replyMessageId = message.id;
      this.$emit('update:replyEmailMessage', undefined) // Clears the message so it isnt called twice
      this.$emit('editorLoaded'); // Resets the container height so that the scroll is correctly placed
      this.fetchData();
    }
  },
  computed: {
    attachments(): FileAttachment[] {
      return [...this.filesAdded, ...this.existingFiles]
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    })
  },
  async mounted() {
    // EventBus.$on('reply_to_email', this.replyToEmailListener) removed because Watch property + Mounted covers the intial load bug.
    EventBus.$on('selected_email_template', this.templateSelected)
    EventBus.$on('email_invoice_created', this.invoiceCreated)
    this.loadCustomFieldItems(this.contact)
    this.loadTriggerLinks(this.currentLocationId);
    ///this.loadEditorImagePath(this.contact);
    await this.$nextTick()
    setTimeout(() => IFrameDisableDrops.refresh(), 3000)
    if (this.replyEmailMessage) { // Watch property wont get changes on first load.
      this.replyToEmailListener(this.replyEmailMessage)
    }
  },
  beforeDestroy() {
    // EventBus.$off('reply_to_email', this.replyToEmailListener) Will turn off on SendMessageComponent.vue
    EventBus.$off('selected_email_template')
    EventBus.$off('email_invoice_created')
  }
})
</script>

<style scoped>
div[class*='email-action-bottom'] {
  margin-top: 2px !important;
  margin-bottom: 2px !important;
}
.inline-form-name, .inline-form-email {
  display: inline-block;
}
.inline-form-name {
  width: 39%;
  margin-right: 2%;
}
.inline-form-email {
  width: 59%;
}
.email-action-bottom-left.use-template, .email-action-bottom-left.align-next {
  display: inline-block !important;
  left: 130px !important;
  position: absolute !important;
}
.hidden-span {
    opacity: 0;
    color: #ffffff00;
    cursor: default;
}
</style>
