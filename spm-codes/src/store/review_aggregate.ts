import Vue from 'vue';
import { ReviewAggregate } from '../models';
import { Module, ActionContext } from 'vuex';
import firebase from 'firebase/app';
import { ReviewAggregateState, RootState, ReviewAggregateData, ReviewComponentData } from '@/store/state_models';
import * as moment from 'moment-timezone';

let thisWeekCurrent: () => void;
let thisWeekPrevious: () => void;
let lastWeekCurrent: () => void;
let lastWeekPrevious: () => void;
let thisMonthCurrent: () => void;
let thisMonthWeeklyCurrent: () => void;
let thisMonthPrevious: () => void;
let last6MonthsCurrent: () => void;
let lastSixMonthMonthlyCurrent: () => void;
let last6MonthsPrevious: () => void;
let thisYearCurrent: () => void;
let thisYearMonthlyCurrent: () => void;
let thisYearPrevious: () => void;

function getInitialAggregateData(): ReviewAggregateData {
    return {
        totalReviews: 0,
        totalRating: 0,
        breakdown: {
            1: 0,
            2: 0,
            3: 0,
            4: 0,
            5: 0,
        },
        positiveReviews: 0,
        negativeReviews: 0,
        sources: [] as number[],
    };
}

function getInitialComponentData(): ReviewComponentData {
    return {
        previous: getInitialAggregateData(),
        current: getInitialAggregateData(),
        fetched: false,
    };
}

function getInitialData(): ReviewAggregateState {
    return {
        thisWeek: getInitialComponentData(),
        lastWeek: getInitialComponentData(),
        thisMonth: getInitialComponentData(),
        thisMonthWeekly: getInitialComponentData(),
        last6Months: getInitialComponentData(),
        lastSixMonthMonthly: getInitialComponentData(),
        thisYear: getInitialComponentData(),
        thisYearMonthly: getInitialComponentData(),

    };
}

function getReviewAggregate(docs: firebase.firestore.DocumentSnapshot[]) {
    const data = getInitialAggregateData();
    const sources = new Set<number>();
    docs.forEach((snapshot) => {
        const aggregate = new ReviewAggregate(snapshot);
        data.totalReviews += aggregate.totalReviews;
        data.totalRating += aggregate.totalStars;
        data.breakdown['1'] = data.breakdown['1'] + (aggregate.ratingsBreakDown['1'] || 0);
        data.breakdown['2'] = data.breakdown['2'] + (aggregate.ratingsBreakDown['2'] || 0);
        data.breakdown['3'] = data.breakdown['3'] + (aggregate.ratingsBreakDown['3'] || 0);
        data.breakdown['4'] = data.breakdown['4'] + (aggregate.ratingsBreakDown['4'] || 0);
        data.breakdown['5'] = data.breakdown['5'] + (aggregate.ratingsBreakDown['5'] || 0);
        data.positiveReviews += aggregate.positiveReviews;
        data.negativeReviews += aggregate.negativeReviews;
        if (aggregate.sources) aggregate.sources.forEach((source) => sources.add(source));
    });
    data.sources = Array.from(sources);
    return data;
}

export const reviewAggregate: Module<ReviewAggregateState, RootState> = {
    namespaced: true,
    state: getInitialData(),
    getters: {
        current: (state: ReviewAggregateState, getters: any, rootState: RootState, rootGetters: any) => {
            return (filter: string) => {
                if (filter === 'This Week') {
                    return state.thisWeek.current;
                } else if (filter === 'Last Week') {
                    return state.lastWeek.current;
                } else if (filter === 'This Month') {
                    return state.thisMonth.current;
                } else if (filter === 'thisMonthWeekly') {
                    return state.thisMonthWeekly.current;
                }
                else if (filter === 'Last 6 Months') {
                    return state.last6Months.current;
                }else if (filter === 'lastSixMonthMonthly') {
                    return state.lastSixMonthMonthly.current;
                }
                else if (filter === 'This Year') {
                    return state.thisYear.current;
                }
                else if (filter === 'thisYearMonthly') {
                    return state.thisYearMonthly.current;
                }
            };
        },
        previous: (state: ReviewAggregateState, getters: any, rootState: RootState, rootGetters: any) => {
            return (filter: string) => {
                if (filter === 'This Week') {
                    return state.thisWeek.previous;
                } else if (filter === 'Last Week') {
                    return state.lastWeek.previous;
                } else if (filter === 'This Month') {
                    return state.thisMonth.previous;
                } else if (filter === 'Last 6 Months') {
                    return state.last6Months.previous;
                } else if (filter === 'This Year') {
                    return state.thisYear.previous;
                }
            };
        },
    },
    mutations: {
        setThisWeek: (state: ReviewAggregateState, payload: { [key: string]: any }) => {
            Vue.set(state.thisWeek, payload.type, payload.data);
        },
        setLastWeek: (state: ReviewAggregateState, payload: { [key: string]: any }) => {
            Vue.set(state.lastWeek, payload.type, payload.data);
        },
        setThisMonth: (state: ReviewAggregateState, payload: { [key: string]: any }) => {
            Vue.set(state.thisMonth, payload.type, payload.data);
        },
        setThisMonthWeekly: (state: ReviewAggregateState, payload: { [key: string]: any }) => {
            Vue.set(state.thisMonthWeekly, payload.type, payload.data);
        },
        setLast6Months: (state: ReviewAggregateState, payload: { [key: string]: any }) => {
            Vue.set(state.last6Months, payload.type, payload.data);
        },
        setLastSixMonthMonthly: (state: ReviewAggregateState, payload: { [key: string]: any }) => {
            Vue.set(state.lastSixMonthMonthly, payload.type, payload.data);
        },
        setThisYear: (state: ReviewAggregateState, payload: { [key: string]: any }) => {
            Vue.set(state.thisYear, payload.type, payload.data);
        },
        setThisYearMonthly: (state: ReviewAggregateState, payload: { [key: string]: any }) => {
            Vue.set(state.thisYearMonthly, payload.type, payload.data);
        },
        reset: (state: ReviewAggregateState) => {
            Object.assign(state, getInitialData());
        },
    },
    actions: {
        fetchThisWeek: {
            handler: async (context: ActionContext<ReviewAggregateState, RootState>, locationId: string) => {
                if (context.state.thisWeek.fetched) {
                    return true;
                }
                context.commit('setThisWeek', { type: 'fetched', data: true });

                const currentWeek = moment()
                    .utc()
                    .startOf('week');
                const previousWeek = currentWeek.clone().subtract(1, 'week');

                if (thisWeekCurrent) { thisWeekCurrent(); }

                thisWeekCurrent = ReviewAggregate.getByLocationWeekRealtime(locationId, currentWeek).onSnapshot(
                    (snapshot) => {
                        const aggregate = getReviewAggregate(snapshot.docs);
                        context.commit('setThisWeek', { type: 'current', data: aggregate });
                    }
                );

                if (thisWeekPrevious) { thisWeekPrevious(); }

                thisWeekPrevious = ReviewAggregate.getByLocationWeekRealtime(locationId, previousWeek).onSnapshot(
                    (snapshot) => {
                        const aggregate = getReviewAggregate(snapshot.docs);
                        context.commit('setThisWeek', { type: 'previous', data: aggregate });
                    }
                );
            },
        },
        fetchLastWeek: {
            handler: async (context: ActionContext<ReviewAggregateState, RootState>, locationId: string) => {
                if (context.state.lastWeek.fetched) {
                    return true;
                }
                context.commit('setLastWeek', { type: 'fetched', data: true });

                const currentWeek = moment()
                    .utc()
                    .startOf('week')
                    .subtract(1, 'week');
                const previousWeek = currentWeek.clone().subtract(1, 'week');

                if (lastWeekCurrent) { lastWeekCurrent(); }

                lastWeekCurrent = ReviewAggregate.getByLocationWeekRealtime(locationId, currentWeek).onSnapshot(
                    (snapshot) => {
                        const aggregate = getReviewAggregate(snapshot.docs);
                        context.commit('setLastWeek', { type: 'current', data: aggregate });
                    }
                );

                if (lastWeekPrevious) { lastWeekPrevious(); }

                lastWeekPrevious = ReviewAggregate.getByLocationWeekRealtime(locationId, previousWeek).onSnapshot(
                    (snapshot) => {
                        const aggregate = getReviewAggregate(snapshot.docs);
                        context.commit('setLastWeek', { type: 'previous', data: aggregate });
                    }
                );
            },
        },
        fetchThisMonth: {
            handler: async (context: ActionContext<ReviewAggregateState, RootState>, locationId: string) => {
                if (context.state.thisMonth.fetched) {
                    return true;
                }
                context.commit('setThisMonth', { type: 'fetched', data: true });

                const startMonth = moment()
                    .utc()
                    .startOf('month');
                const endMonth = startMonth.clone();
                const previousPeriodStartMonth = startMonth.clone().subtract(1, 'month');
                const previousPeriodEndMonth = previousPeriodStartMonth.clone();

                if (thisMonthCurrent) { thisMonthCurrent(); }

                thisMonthCurrent = ReviewAggregate.getByLocationYearMonthsRealtime(
                    locationId,
                    startMonth,
                    endMonth
                ).onSnapshot((snapshot) => {
                    const aggregate = getReviewAggregate(snapshot.docs);
                    context.commit('setThisMonth', { type: 'current', data: aggregate });
                });

                if (thisMonthPrevious) { thisMonthPrevious(); }

                thisMonthPrevious = ReviewAggregate.getByLocationYearMonthsRealtime(
                    locationId,
                    previousPeriodStartMonth,
                    previousPeriodEndMonth
                ).onSnapshot((snapshot) => {
                    const aggregate = getReviewAggregate(snapshot.docs);
                    context.commit('setThisMonth', { type: 'previous', data: aggregate });
                });
            },
        },
        thisMonthWeekly: {
            handler: async (context: ActionContext<ReviewAggregateState, RootState>, locationId: string) => {
                if (context.state.thisMonthWeekly.fetched) {
                    return true;
                }
                context.commit('setThisMonthWeekly', { type: 'fetched', data: true });

                const startMonth = moment().utc()
                .startOf('month');
                const endMonth = moment().endOf('month');

               // let monthRange = extendMoment(moment).range(startMonth, endMonth);

               let numberOfDays = Math.abs(moment().endOf('month').startOf('day').diff(moment().startOf('month').startOf('day'),'days'))

                // Get all the weeks during the current month
                let weeks = []
                for (let i = 0; i <= numberOfDays; i++) {
                    let mday = startMonth.clone().add(i, 'days');
                    if (!weeks.find(eachWeek => eachWeek.weekNumber === mday.week())) {
                        if (mday.month() === startMonth.month()) {
                            const monthOnStartOfWeek = moment().year(mday.year()).week(mday.week()).startOf('week').month()
                            if (monthOnStartOfWeek === startMonth.month()) {
                                weeks.push({
                                    day: mday,
                                    weekNumber: mday.week()
                                });
                            }

                        }
                    }
                }
                weeks = weeks.map(eachWeek => {
                    return moment().year(eachWeek.day.year()).week(eachWeek.weekNumber).utc()
                        .startOf('week');
                })

                let weeklyReview = [];
                for (let eachWeek of weeks) {
                    let docs = await toPromise(ReviewAggregate.getByLocationWeekRealtime(locationId, eachWeek.clone())) as firebase.firestore.DocumentSnapshot[];
                    const aggregate = getReviewAggregate(docs);
                    weeklyReview.push({
                        label: eachWeek,
                        aggregate
                    })
                }
                context.commit('setThisMonthWeekly', { type: 'current', data: weeklyReview.reverse() });
               
            },
        },

        fetchLast6Months: {
            handler: async (context: ActionContext<ReviewAggregateState, RootState>, locationId: string) => {
                if (context.state.last6Months.fetched) {
                    return true;
                }
                context.commit('setLast6Months', { type: 'fetched', data: true });

                const endMonth = moment()
                    .utc()
                    .startOf('month');
                const startMonth = moment()
                    .utc()
                    .startOf('month')
                    .subtract(6, 'months');
                const previousPeriodEndMonth = startMonth.clone().subtract(1, 'month');
                const previousPeriodStartMonth = previousPeriodEndMonth.clone().subtract(6, 'month');

                if (last6MonthsCurrent) { last6MonthsCurrent(); }

                last6MonthsCurrent = ReviewAggregate.getByLocationYearMonthsRealtime(
                    locationId,
                    startMonth,
                    endMonth
                ).onSnapshot((snapshot) => {
                    const aggregate = getReviewAggregate(snapshot.docs);
                    context.commit('setLast6Months', { type: 'current', data: aggregate });
                });

                if (last6MonthsPrevious) { last6MonthsPrevious(); }

                last6MonthsPrevious = ReviewAggregate.getByLocationYearMonthsRealtime(
                    locationId,
                    previousPeriodStartMonth,
                    previousPeriodEndMonth
                ).onSnapshot((snapshot) => {
                    const aggregate = getReviewAggregate(snapshot.docs);
                    context.commit('setLast6Months', { type: 'previous', data: aggregate });
                });
            },
        },

        lastSixMonthMonthly: {
            handler: async (context: ActionContext<ReviewAggregateState, RootState>, locationId: string) => {
                if (context.state.lastSixMonthMonthly.fetched) {
                    return true;
                }
                context.commit('setLastSixMonthMonthly', { type: 'fetched', data: true });

                const endMonth = moment().utc()
                .startOf('month');
            let monthlyReview = [];
            for (let i = -1 ; i < 5 ; i++ ) {
                let docs = await toPromise(ReviewAggregate.getByLocationYearMonthRealtime(
                    locationId, 
                    endMonth.clone().subtract(i+1, 'month'))) as firebase.firestore.DocumentSnapshot[];;

                const aggregate = getReviewAggregate(docs);
                monthlyReview.push({
                    label: endMonth.clone().subtract(i+1, 'month'),
                    aggregate
                })
            }
            context.commit('setLastSixMonthMonthly', { type: 'current', data: monthlyReview.reverse() });
            },
        },
        fetchThisYear: {
            handler: async (context: ActionContext<ReviewAggregateState, RootState>, locationId: string) => {
                if (context.state.thisYear.fetched) {
                    return true;
                }
                context.commit('setThisYear', { type: 'fetched', data: true });

                const startMonth = moment()
                    .utc()
                    .startOf('year');
                const endMonth = moment()
                    .utc()
                    .startOf('month');
                const previousPeriodEndMonth = startMonth.clone().subtract(1, 'month');
                const previousPeriodStartMonth = previousPeriodEndMonth.clone().startOf('year');

                if (thisYearCurrent) { thisYearCurrent(); }

                thisYearCurrent = ReviewAggregate.getByLocationYearMonthsRealtime(
                    locationId,
                    startMonth,
                    endMonth
                ).onSnapshot((snapshot) => {
                    const aggregate = getReviewAggregate(snapshot.docs);
                    context.commit('setThisYear', { type: 'current', data: aggregate });
                });

                if (thisYearPrevious) { thisYearPrevious(); }

                thisYearPrevious = ReviewAggregate.getByLocationYearMonthsRealtime(
                    locationId,
                    previousPeriodStartMonth,
                    previousPeriodEndMonth
                ).onSnapshot((snapshot) => {
                    const aggregate = getReviewAggregate(snapshot.docs);
                    context.commit('setThisYear', { type: 'previous', data: aggregate });
                });
            },
        },
        thisYearMonthly: {
            handler: async (context: ActionContext<ReviewAggregateState, RootState>, locationId: string) => {
                if (context.state.thisYearMonthly.fetched) {
                    return true;
                }
                context.commit('setThisYearMonthly', { type: 'fetched', data: true });

                const endMonth = moment().utc()
                .startOf('month');
          //  const previousPeriodEndMonth = startMonth.clone().subtract(1, 'month');
           // const previousPeriodStartMonth = previousPeriodEndMonth.clone().subtract(6, 'month');

            let monthlyReview = [];
            for (let i = -1 ; i < 11 ; i++ ) {
                let curMonth =  endMonth.clone().subtract(i+1, 'month').utc()
                if(curMonth.year() !== endMonth.year() ){
                    break;
                }
                let docs = await toPromise(ReviewAggregate.getByLocationYearMonthRealtime(
                    locationId, 
                    curMonth
                )) as firebase.firestore.DocumentSnapshot[];;

                const aggregate = getReviewAggregate(docs);
                monthlyReview.push({
                    label: endMonth.clone().subtract(i+1, 'month'),
                    aggregate
                })
            }

            context.commit('setThisYearMonthly', { type: 'current', data: monthlyReview.reverse() });

              /*   const startMonth = moment()
                    .utc()
                    .startOf('year');
                const endMonth = moment()
                    .utc()
                    .startOf('month');
                const previousPeriodEndMonth = startMonth.clone().subtract(1, 'month');
                const previousPeriodStartMonth = previousPeriodEndMonth.clone().startOf('year');

                if (thisYearCurrent) { thisYearCurrent(); }

                thisYearCurrent = ReviewAggregate.getByLocationYearMonthsRealtime(
                    locationId,
                    startMonth,
                    endMonth
                ).onSnapshot((snapshot) => {
                    const aggregate = getReviewAggregate(snapshot.docs);
                    context.commit('setThisYearMonthly', { type: 'current', data: aggregate });
                }); */

               
            },
        },
        reset: {
            handler: async (context: ActionContext<ReviewAggregateState, RootState>) => {
                if (thisWeekCurrent) thisWeekCurrent();
                if (thisWeekPrevious) thisWeekPrevious();
                if (lastWeekCurrent) lastWeekCurrent();
                if (lastWeekPrevious) lastWeekPrevious();
                if (thisMonthCurrent) thisMonthCurrent();
                if (thisMonthWeeklyCurrent) thisMonthWeeklyCurrent();
                if (thisMonthPrevious) thisMonthPrevious();
                if (last6MonthsCurrent) last6MonthsCurrent();
                if (lastSixMonthMonthlyCurrent) lastSixMonthMonthlyCurrent();
                if (last6MonthsPrevious) last6MonthsPrevious();
                if (thisYearCurrent) thisYearCurrent();
                if (thisYearMonthlyCurrent) thisYearMonthlyCurrent();
                if (thisYearPrevious) thisYearPrevious();
            },
        },
    },
};


function toPromise(foo) {
    return new Promise((resolve, reject) => {
        foo.onSnapshot((docs => {
            return resolve(docs)
        }))
    })

}