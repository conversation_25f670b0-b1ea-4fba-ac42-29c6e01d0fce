<template>
  <div
    id="wp_resell"
    v-if="
      company && company.wordpressReseller && company.wordpressReseller.valve
    "
  >
    <template v-if="!company.stripeConnectId">
      <div class="backdrop saas_backdrop">
        <div
          class="stripe-connect__setup-btn btn"
          @click="gotoStripeSettings()"
        >
          Connect Stripe
          <i class="fas fa-exclamation-circle"></i>
        </div>
      </div>
    </template>
    <div class="card">
      <div class="card-header">
        <div class="toggle header-toggle">
          <label for="yext_show_customer_tgl" class="card-header__label">
            Wordpress Reselling</label
          >
        </div>
      </div>
      <div class="card-body">
        <!--   <div class="choice">
          <h3>Pick Your Plan</h3>
          <div class="selection">
            <UIRadioButton
              label="Standard Pricing (a-al-carte)"
              name="plan_type"
              id="plan_type_standard"
              value="standard"
            />
            <div class="column">
              <UIRadioButton
                label="Unlimited Monthly Plan $325/mo"
                name="plan_type"
                id="plan_type_unlimited_monthly"
                value="unlimited_monthly"
              />
              <span class="checkbox_meta">coming soon</span>
            </div>
            <div class="column">
              <UIRadioButton
                label="Unlimited Annual Plan $3250/year"
                name="plan_type"
                id="plan_type_unlimited_yearly"
                value="unlimited_yearly"
              />
              <span class="checkbox_meta">coming soon</span>
            </div>

            <div class="column">
              <UIRadioButton
                label="Founder's Special Offer $3000/year"
                name="plan_type"
                id="plan_type_founder_spacial"
                value="unlimited_monthly"
              />
              <span class="checkbox_meta"><a href="">ger here</a></span>
              <span class="checkbox_meta checkbox_meta--subheading"
                >bla bla bla</span
              >
            </div>
          </div>
        </div> -->
        <div class="column configuration">
          <div class="logo">
            <img class="sidebar-icon" src="/pmd/img/icon-wordpress-logo.jpg" />
          </div>
          <div class="details">
            <h3>Re-sell Wordpress</h3>
            <a class="how_to_link" href="#" target="_blank"
              >How does it work?</a
            >
            <div class="choice">
              <h3>Pick Your Plan</h3>
              <div class="selection">
                <UIRadioButton
                  label="Standard Pricing (a-al-carte)"
                  name="plan_type"
                  id="plan_type_standard"
                  value="STANDARD"
                  :selectedValue="agencyWPPlan"
                  v-if="agencyWPPlan != 'FOUNDER_SPECIAL'"
                />
  <!--               <div class="column">
                  <UIRadioButton
                    label="Unlimited Monthly Plan $325/mo"
                    name="plan_type"
                    id="plan_type_unlimited_monthly_2"
                    value="UNLIMITED_MONTHLY"
                    disabled="true"
                  />
                  <span class="checkbox_meta">coming soon</span>
                </div>
                <div class="column">
                  <UIRadioButton
                    label="Unlimited Annual Plan $3250/year"
                    name="plan_type"
                    id="plan_type_unlimited_yearly_2"
                    value="UNLIMITED_YEARLY"
                    disabled="true"
                  />
                  <span class="checkbox_meta">coming soon</span>
                </div> -->

                <div class="column">
                  <UIRadioButton
                    label="Founder's Special Offer $3000/year"
                    name="plan_type"
                    id="plan_type_founder_spacial_2"
                    value="FOUNDER_SPECIAL"
                    :selectedValue="agencyWPPlan"
                    :disabled="agencyWPPlan != 'FOUNDER_SPECIAL'"
                  />
                  <span class="checkbox_meta"
                    ><a
                      v-if="agencyWPPlan != 'FOUNDER_SPECIAL'"
                      target="_blank"
                      :href="`https://www.gohighlevel.com/wordpress-founders-offer?full_name=${encodeURIComponent(
                        user.name || ''
                      )}&email=${encodeURIComponent(
                        user.email || ''
                      )}&phone=${encodeURIComponent(
                        user.phone || ''
                      )}&organization=${encodeURIComponent(
                        company.name || ''
                      )}`"
                      >get here</a
                    ></span
                  >
                  <span class="checkbox_meta checkbox_meta--subheading"
                    >(12 months + 2 months early access + truly unlimited)</span
                  >
                </div>
              </div>
            </div>
            <h4 class="sub-heading">Build Your Offer</h4>
            <div class="wp_offer_table">
              <form action="#">
                <label for="hl_price">HighLevel Price</label>
                <div class="prefix_input">
                  <div class="prefix">$</div>
                  <input
                    disabled
                    type="text"
                    id="hl_price"
                    :value="wordpressReseller.hl_price"
                  />
                </div>

                <label for="agency_price">Your Price</label>
                <div>
                  <div class="prefix_input">
                    <div class="prefix">$</div>
                    <input
                      type="text"
                      id="agency_price"
                      v-model.number="agency_price"
                    />
                    <span class="field_error">
                      <template
                        v-if="agency_price < wordpressReseller.hl_price"
                      >
                        Price must be more or equal to HighLevel price
                      </template>
                    </span>
                  </div>
                </div>

                <label for="agency_profit">Your Profit</label>
                <div class="prefix_input">
                  <div class="prefix">$</div>
                  <input
                    disabled
                    type="text"
                    id="agency_profit"
                    :value="
                      agency_price >= wordpressReseller.hl_price
                        ? getFriendlyNumber(agency_price - wordpressReseller.hl_price)
                        : 0
                    "
                  />
                </div>
              </form>
            </div>
            <div class="place-eater"></div>
          </div>
        </div>
        <div class="footer-controls">
          <!--      <div class="showoff">
            HighLevel gives you a discount compared to the stock rate which is
            usually billed at $999 with yearly commitment. HighLevel's wholesale
            price is just $30/month with only 3 month's commitment
          </div> -->
          <button
            :disabled="agency_price == wordpressReseller.agency_price"
            id="wordpress_resell_global"
            class="btn btn-success reselling-btn"
            @click="save"
          >
            <template v-if="saving">
              <i class="icon" :class="saving ? 'icon-clock' : 'icon-ok'"></i>
              Saving
            </template>
            <template v-else> Save </template>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// @ts-nocheck

import Vue from 'vue'
import { UserState, CompanyState } from '@/store/state_models'
import { mapState } from 'vuex'
import { User, Company } from '@/models'
import { getFriendlyNumber } from '@/util/helper'

export default Vue.extend({
  components: {},
  data: function () {
    return {
      saving: false,
      agency_price: 0,
      getFriendlyNumber
    }
  },

  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    ...mapState('company', {
      company: (s: CompanyState) => {
        return s.company ? new Company(s.company) : undefined
      },
    }),
    wordpressReseller: function () {
      return this.company.wordpressReseller
    },
    agencyWPPlan() {
      if (
        this.company.wordpressReseller &&
        this.company.wordpressReseller.plan
      ) {
        return this.company.wordpressReseller.plan
      }
      return 'STANDARD'
    },
  },
  created: function () {
    this.agency_price = this.company.wordpressReseller.agency_price
  },
  watch: {},
  methods: {
    async save() {
      if (this.company.stripeConnectId) {
        this.saving = true
        if (this.agency_price < this.wordpressReseller.hl_price) {
          this.agency_price = this.wordpressReseller.hl_price
        }
        const company = await Company.getById(this.company.id)
        company.wordpressReseller.agency_price = this.agency_price
        await company.save()
        this.saving = false
      }
    },
    gotoStripeSettings() {
      this.$router.push({ name: 'stripe_settings' })
    },
  },
})
</script>
<style lang="scss">
#wp_resell {
  height: 100%;
  position: relative;
  .saas_backdrop {
    position: absolute;
    z-index: 3;
    width: 100%;
    height: 100%;
    display: grid;
    justify-content: center;
    align-items: center;
    background: #ffffffad;
    /* label {
      background: #27ae60;
      padding: 5px 15px;
      color: #fff;
      font-size: 1rem;
      margin: 0;
    } */

    .stripe-connect__setup-btn {
      background: #feebc8;
      border-radius: 3px;
      font-weight: 500;
      font-size: 1rem;
      /* line-height: 14px; */
      text-align: center;
      margin-bottom: 14px;
      color: #f6ad55;
      padding: 10px 20px;
      box-shadow: 0 0 2px 0px #00000038;
      i {
        margin-left: 6px;
      }
      &:hover {
        background: #f6ad55;
        color: #ffffff;
      }
    }

    .country_restriction {
      font-size: 1rem;
      background: #feebc8;
      padding: 10px 20px;
      max-width: 60%;
      margin: auto;
      display: grid;
      grid-template-columns: auto auto;
      grid-column-gap: 10px;
      color: #ed8936;
      border-radius: 2px;
      .fa-exclamation-circle {
        margin-top: 5px;
        font-size: 2rem;
      }
    }
  }

  .card {
    height: 100%;
    .card-header {
      padding: 10px 20px;

      .header-toggle {
        display: grid;
        grid-template-columns: auto auto;
        grid-gap: 10px;
        align-items: center;

        input {
          display: none;
        }
      }

      .card-header__label {
        margin: 0;
        font-size: 1rem;
        font-weight: 400;
        color: #2a3844;
      }
    }
    .card-body {
      display: grid;
      grid-template-columns: auto 1fr;
      grid-column-gap: 40px;
      .choice {
        padding-right: 20px;
        border-right: 1px solid #cad5dc;
        h3 {
          font-size: 1.5rem;
          font-weight: 600;
          color: #4f4f4f;
        }
        .selection {
          display: grid;
          grid-gap: 10px;
          .checkbox_meta {
            color: #ffcb00;
            font-weight: 500;
            font-style: italic;
          }
          .checkbox_meta--subheading {
            padding-left: 25px;
            font-style: normal;
            color: #9e9e9e;
            font-weight: unset;
            grid-column: 1/-1;
          }
        }
      }
      .configuration {
        .logo {
          max-height: 100px;
          max-width: 100px;
          overflow: hidden;
          img {
            height: 100%;
          }
        }
        .details {
          display: grid;
          grid-template-rows: auto auto auto 1fr auto;

          .choice {
            display: none;

            display: block;
            border-right: unset;
            padding: 16px 0;

            h3 {
              font-size: 1.2rem;
              font-weight: 600;
              color: #585858;
            }
          }

          h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #4f4f4f;
          }
          .sub-heading {
            font-size: 1rem;
            margin: 10px 0;
            font-weight: 600;
            color: #4f4f4f;
          }
        }

        .upcoming-product {
          position: absolute;
          width: 100%;
          height: 100%;
          background: #ffffffc7;
          top: 0;
          left: 0;
          z-index: 1;
          display: grid;
          justify-content: center;
          align-items: center;
          h3 {
            background: #feebc8;
            padding: 10px 30px;
            color: #ed8936;
            border-radius: 1px;
            font-size: 1rem;
          }
        }

        .wp_offer_table {
          form {
            display: grid;
            grid-template-columns: auto 1fr;
            grid-gap: 10px;
            label {
              font-size: 1rem;
              color: #4f4f4f;
            }
            .field_error {
              display: block;
              font-size: 0.8rem;
              color: #e93d3d;
              min-height: 14px;
            }

            .prefix_input {
              position: relative;
              .prefix {
                position: absolute;
                top: 2px;
                left: 3px;
                background: white;
                padding: 0 10px;
                display: grid;
                align-items: center;
              }
              input {
                padding: 0 0 0 30px;
                width: 90px;
              }
            }
          }
          [for='agency_profit'] {
            color: #27ae60;
          }
        }
      }
      .footer-controls {
        grid-column: 1/-1;
        display: grid;
        justify-content: right;
        grid-gap: 15px;
        grid-template-columns: 1fr auto;
        align-items: end;
        .showoff {
          padding: 8px;
          border-left: 3px solid #73cb98;
          background: linear-gradient(45deg, #73cb981f, transparent);
        }
      }
    }
  }

  .reselling-btn {
    background: #27ae60;
    padding: 6px 10px;
    justify-self: right;
  }
  .reselling-toggle {
    display: grid;
    grid-template-columns: auto 1fr;
    justify-content: right;
    grid-gap: 10px;
    .tgl-light:checked + .tgl-btn:after {
      background: white;
    }
    .tgl-light:checked + .tgl-btn {
      background: #3182ce;
    }
  }
  .column {
    display: grid;
    grid-template-columns: auto 1fr;
    grid-column-gap: 15px;
  }
  @media (max-width: 1700px) {
    .card {
      .card-body {
        .choice {
          display: none;
        }
        .configuration {
          .details {
            .choice {
              display: block;
            }
          }
        }
      }
    }
  }
}
</style>
