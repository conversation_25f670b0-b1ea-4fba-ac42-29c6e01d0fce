<template>
  <div class="modal fade hl_remove_payment_method--modal" tabindex="-1" role="dialog" ref="modal">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-body">
          <i class="fa fa-times hide-modal" @click="hideModal"></i>
          <div class="title">Remove Card?</div>
          <div class="description">Ending in *{{ cardNo }}</div>
          <div class="controls">
            <button @click="hideModal" class="btn cancel">No</button>
            <button :disabled="processing" @click="$emit('ok')" class="btn confirm">
              <i class="fa fa-trash"></i>
              {{ processing ? 'Removing..' : 'Yes, Remove' }}
            </button>
            <p class="error" v-if="error">{{ error }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    cardNo: {
      type: Number
    },
    processing: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: ''
    }
  },
  mounted() {
    $(this.$refs.modal).on('hidden.bs.modal', () => {
      this.hideModal()
    })
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  methods: {
    hideModal() {
      this.$emit('cancel')
    }
  },
  watch: {
    showModal(newValue, oldValue) {
      if (newValue && !oldValue) {
        $(this.$refs.modal).modal('show')
      } else if (!newValue && oldValue) {
        $(this.$refs.modal).modal('hide')
      }
    }
  }
})
</script>
<style scoped>
i.hide-modal {
  position: absolute;
  right: 16px;
  top: 16px;
  font-size: 16px;
  cursor: pointer;
}

.hl_remove_payment_method--modal {
  text-align: center;
}

.hl_remove_payment_method--modal.modal.show .modal-dialog {
  transform: translate(0, calc(50vh - 50% - 1.75rem));
  /** 1.75rem for margin top */
}

.hl_remove_payment_method--modal .modal-body {
  padding: 36px;
}

.title {
  color: #333333;
  font-size: 25px;
  line-height: 29px;
  font-weight: bold;
  margin-bottom: 12px;
}

.description {
  color: #718096;
  font-size: 13px;
  line-height: 15px;
}

.controls {
  margin-top: 60px;
}

.controls button {
  margin: 0 8px;
  border-radius: 3px;
  padding: 16px;
  font-size: 14px;
  line-height: 16px;
}

.controls button.cancel {
  color: #718096;
  background: #E2E8F0;
}

.controls button.confirm {
  color: white;
  background: #F56565;
}

.controls button.confirm i {
  margin-right: 14px;
}

p.error {
  margin-top: 12px;
}
</style>
