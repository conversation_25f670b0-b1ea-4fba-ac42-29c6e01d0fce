<template>
  <div
    :class="{'dropdown-menu dropdown-menu-right': true,
     'show':showPhoneControls,
     'dialer-center': isCentered,
     'dialer-left': !isCentered}"
    style="position: absolute; will-change: transform;"
  >
    <div v-if="!onPhone || callTabs[callTabs.currentTab].openIVR">
      <div v-if="!manualCallEnded">
        <Dialer
          v-if="tab == 'dial'"
          :currentLocationId="currentLocationId"
          :phoneNumbers="phoneNumbers"
          :connection="connection"
          :callTabs="callTabs"
          @backButton="backButton"
        />

        <Contacts
          v-if="tab == 'contact'"
          :currentLocationId="currentLocationId"
          :phoneNumbers="phoneNumbers"
        />

        <ManualActions
          v-if="tab == 'manual'"
          :currentLocationId="currentLocationId"
          :phoneNumbers="phoneNumbers"
          :manualQueue="manualQueue"
        />

        <ForwardingNumbers
          v-if="tab == 'forwarding'"
          :currentLocationId="currentLocationId"
          :phoneNumbers="phoneNumbers"
          :callTabs="callTabs"
          @backButton="backButton"
        />

        <Users
          v-if="tab == 'users'"
          :callTabs="callTabs"
          :currentLocationId="currentLocationId"
          :phoneNumbers="phoneNumbers"
          @backButton="backButton"
        />
      </div>

      <CallEnded
        v-else
        :time="callTabs.lastCallDetails.time"
        :callContact="callTabs.lastCallDetails.callContact"
        :manualActionsData="manualActionsData"
        :callTabs="callTabs"
        @leaveCallEnded="manualCallEnded = false"
      />
    </div>

    <OnCallModal
      v-else
      :currentLocationId="currentLocationId"
      :timerFullTime="timerFullTime"
      :callContact="callTabs[callTabs.currentTab].callContact"
      :callTabs="callTabs"
      :connection="connection"
      @openTransfer="openTransfer"
      @openIVR="openIVR"
    />
    <div class="collapse-icon" v-if="callTabs.first.onPhone" v-on:click="$emit('closeModal')">
        <span><i class="fa fa-angle-up"></i></span>
    </div>
    <ul class="nav custom-tab" v-if="callTabs.showSideTabs">
      <li class="nav-item" :class="{active: callTabs.currentTab==='first'}">
        <a class="nav-link" @click.prevent="changeTab('first')">
         <span class="active-status"></span>
          <i class="">
            <svg width="23" height="23" viewBox="0 0 23 23" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g opacity="0.55">
              <path d="M21.2292 16.695L18.6018 14.2059C18.0256 13.6297 17.4725 13.5375 17.0807 13.5375C16.3432 13.5606 15.8592 13.9985 15.7209 14.1367L14.3842 15.4274C13.0474 14.759 11.8259 13.9063 10.6966 12.8231L10.5814 12.7309C9.42902 11.6476 8.53018 10.4722 7.81572 9.15856L9.03722 7.77573C9.17551 7.63744 9.59035 7.13041 9.56731 6.3929C9.56731 6.02414 9.42902 5.44797 8.8298 4.91788L6.20242 2.3827C6.04109 2.19832 5.51101 1.71433 4.72741 1.66823C4.31256 1.64519 3.66724 1.76042 3.04496 2.35965C2.92973 2.47488 1.89261 3.44286 1.50081 4.64132C1.22424 5.30968 -0.826957 10.9793 6.01805 17.4555L6.15633 17.5938C10.0052 21.2814 13.4392 22.1571 15.8361 22.1571C17.6799 22.1571 18.9014 21.6501 19.178 21.5118C20.3534 21.0509 21.2753 19.9677 21.3674 19.8755C21.9436 19.2071 22.0358 18.5618 21.9897 18.1469C21.9206 17.3403 21.4135 16.8563 21.2292 16.695ZM19.9846 18.677C19.7772 18.9075 19.1319 19.5759 18.5096 19.8063L18.4174 19.8524C18.3483 19.8524 13.6006 22.1802 7.44697 16.2571L7.30869 16.1188C1.1551 10.3109 3.11411 5.54015 3.20629 5.35578L3.25239 5.26359C3.45981 4.61827 4.08209 3.9499 4.31256 3.71943C4.42779 3.60419 4.56608 3.53505 4.63522 3.53505C4.72741 3.53505 4.81959 3.62724 4.84264 3.65029L7.58525 6.27766C7.70049 6.36985 7.72354 6.43899 7.72354 6.46204C7.72354 6.48509 7.70049 6.50813 7.67744 6.55423L5.62625 8.90504L5.90281 9.45817C6.73251 11.1867 7.86182 12.7078 9.31379 14.0906L9.42902 14.1828C10.881 15.5657 12.4712 16.6258 14.2228 17.3633L14.776 17.5938L17.0115 15.4504L17.0346 15.4043C17.0576 15.3813 17.1037 15.3582 17.1037 15.3582C17.1268 15.3582 17.1959 15.3813 17.3111 15.4965L19.9846 18.0317L20.0538 18.0778C20.0999 18.1008 20.169 18.2161 20.192 18.3083C20.169 18.4005 20.0999 18.5157 19.9846 18.677Z" fill="#27AE60" stroke="#27AE60" stroke-width="0.35"/>
              <path d="M11.8032 1C11.2962 1 10.8813 1.41485 10.8813 1.92189V7.4532C10.8813 7.96024 11.2962 8.37509 11.8032 8.37509C12.3103 8.37509 12.7251 7.96024 12.7251 7.4532V1.92189C12.7251 1.41485 12.3103 1 11.8032 1Z" fill="#27AE60" stroke="#27AE60" stroke-width="0.35"/>
              <path d="M21.0448 10.4263H15.283C14.7759 10.4263 14.3611 10.8411 14.3611 11.3482C14.3611 11.8552 14.7759 12.27 15.283 12.27H21.0448C21.5518 12.27 21.9666 11.8552 21.9666 11.3482C21.9666 10.8411 21.5518 10.4263 21.0448 10.4263Z" fill="#27AE60" stroke="#27AE60" stroke-width="0.35"/>
              <path d="M19.7312 5.24068C20.123 4.89497 20.146 4.31879 19.8233 3.95004C19.4776 3.55824 18.9015 3.53519 18.5327 3.85785L13.9002 7.86805C13.5084 8.21376 13.4854 8.78994 13.808 9.15869C13.9924 9.36612 14.2459 9.48135 14.4995 9.48135C14.7069 9.48135 14.9374 9.41221 15.0987 9.25088L19.7312 5.24068Z" fill="#27AE60" stroke="#27AE60" stroke-width="0.35"/>
              </g>
          </svg>
          </i>
          <h6 v-if="callTabs.currentTab==='second'">00:{{displayFirstCallTime}}</h6>
        </a>
      </li>
      <li class="nav-item" v-if="!callTabs.showOneTab" :class="{active: callTabs.currentTab==='second'}">
        <a class="nav-link"  @click.prevent="changeTab('second')">
          <span v-if="callTabs.transferType==='warm' && callTabs.second.numberToDial" class="second-status"></span>
          <i class="">
           <svg width="27" height="28" viewBox="0 0 27 28" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M11.9705 14.164H13.4158V15.6093C13.4158 16.2479 13.9335 16.7656 14.572 16.7656C15.2105 16.7656 15.7282 16.2479 15.7282 15.6093V14.164H17.1736C17.8121 14.164 18.3298 13.6463 18.3298 13.0078C18.3298 12.3693 17.8121 11.8515 17.1736 11.8515H15.7282V10.4062C15.7282 9.76771 15.2105 9.25 14.572 9.25C13.9335 9.25 13.4158 9.76771 13.4158 10.4062V11.8515H11.9705C11.3319 11.8515 10.8142 12.3693 10.8142 13.0078C10.8142 13.6463 11.3319 14.164 11.9705 14.164Z" fill="#FF9416"/>
            <path d="M25.8446 4.91404H22.0868V1.15624C22.0868 0.517708 21.569 0 20.9305 0C20.292 0 19.7743 0.517708 19.7743 1.15624V4.91404H16.0165C15.3779 4.91404 14.8602 5.43175 14.8602 6.07028C14.8602 6.70882 15.3779 7.22653 16.0165 7.22653H19.7743V10.9843C19.7743 11.6229 20.292 12.1406 20.9305 12.1406C21.569 12.1406 22.0868 11.6229 22.0868 10.9843V7.22653H25.8446C26.4831 7.22653 27.0008 6.70882 27.0008 6.07028C27.0008 5.43175 26.4831 4.91404 25.8446 4.91404Z" fill="#FF9416"/>
            <path d="M17.9989 18.8253C17.3728 18.1194 16.7005 17.9711 16.2466 17.9711C15.4734 17.9711 14.9288 18.3951 14.7626 18.5429L13.4289 19.6797C12.1662 18.9605 11.0317 18.0517 9.97689 16.9169L9.87312 16.8053C8.81863 15.6705 7.99538 14.4729 7.37072 13.1614L8.60096 11.915C8.76717 11.7534 9.25916 11.2088 9.28459 10.3882C9.29789 9.95745 9.18227 9.30938 8.57524 8.68645L6.04191 5.95627C5.69359 5.52788 5.01863 5.05469 4.18584 5.05469C3.5924 5.05469 3.02324 5.28131 2.51189 5.71375C2.37979 5.8207 1.22326 6.78501 0.722893 8.03809C0.370239 8.77172 -2.06712 14.5417 4.60441 21.6699L4.73997 21.8182C8.81978 26.2382 12.6559 27.1713 15.1554 27.1713C15.1557 27.1713 15.1557 27.1713 15.156 27.1713C16.7213 27.1713 17.7639 26.8031 18.055 26.6877C19.3407 26.2802 20.3854 25.193 20.4863 25.0855C21.18 24.3764 21.304 23.6668 21.2858 23.1964C21.2517 22.3191 20.7262 21.7315 20.5218 21.5349L17.9989 18.8253ZM18.8181 23.4855C18.664 23.6491 17.9605 24.3004 17.3338 24.488C17.2962 24.4992 17.2494 24.5189 17.213 24.5339C17.2054 24.5374 16.4258 24.8588 15.156 24.8588C15.156 24.8588 15.1557 24.8588 15.1554 24.8588C13.1175 24.8588 9.95146 24.055 6.43676 20.2474L6.2957 20.0908C0.480371 13.8771 2.71221 9.22757 2.80529 9.04171C2.82668 9.00066 2.84576 8.95875 2.86223 8.91539C3.08828 8.32137 3.73982 7.69671 3.98639 7.49378C4.10491 7.39348 4.17342 7.37093 4.17428 7.36602C4.19278 7.37325 4.2266 7.39753 4.25002 7.42036C4.27517 7.45823 4.27488 7.451 4.31563 7.49523L6.89926 10.2795C6.9114 10.2922 6.9221 10.3037 6.93135 10.3142L5.17154 12.0974C4.84548 12.4278 4.74865 12.9209 4.92526 13.3499C5.68578 15.1967 6.75011 16.842 8.1798 18.3801L8.28358 18.4917C9.71183 20.0286 11.2751 21.2103 13.062 22.1044C13.4774 22.3125 13.9763 22.2515 14.3293 21.9506L16.2359 20.3258C16.2504 20.3402 16.2677 20.3582 16.2874 20.3807L18.8566 23.1404C18.8976 23.1843 18.8904 23.1834 18.9268 23.2118C18.9488 23.2378 18.9719 23.2733 18.9759 23.2733C18.9759 23.2739 18.9572 23.3433 18.8181 23.4855Z" fill="#FF9416"/>
            </svg>
          </i>
        </a>
      </li>
    </ul>

    <ul class="nav nav-tabs">
      <li class="nav-item users-tab" v-if="callTabs.transferType"
      :class="{'disabled-tab': onPhone && tab !== 'users'}">
        <a class="nav-link" :class="{active: tab==='users'}" @click.prevent="tab = 'users'">
          <i class="">
            <svg width="24" height="27" viewBox="0 0 24 27" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect x="1" y="1" width="22" height="25" rx="3" stroke="#A1A5A6" stroke-width="1"/>
              <path d="M14.0491 14.1889C14.6562 14.3969 15.2031 14.6869 15.6897 15.0588C16.1763 15.4307 16.5915 15.8645 16.9353 16.3604C17.279 16.8562 17.5424 17.3941 17.7254 17.974C17.9085 18.554 18 19.1627 18 19.8002H17.1429C17.1429 19.0742 17.0134 18.4012 16.7545 17.7814C16.4955 17.1617 16.1339 16.6215 15.6696 16.1611C15.2054 15.7007 14.6629 15.3443 14.0424 15.092C13.4219 14.8396 12.7411 14.7091 12 14.7002C11.5223 14.7002 11.0625 14.76 10.6205 14.8795C10.1786 14.999 9.76786 15.1673 9.38839 15.3842C9.00893 15.6011 8.66295 15.8645 8.35045 16.1744C8.03795 16.4843 7.77232 16.8274 7.55357 17.2037C7.33482 17.58 7.16295 17.9895 7.03795 18.4322C6.91295 18.8749 6.85268 19.3309 6.85714 19.8002H6C6 19.1627 6.09375 18.554 6.28125 17.974C6.46875 17.3941 6.73438 16.8584 7.07812 16.367C7.42188 15.8756 7.83705 15.4462 8.32366 15.0787C8.81027 14.7113 9.35714 14.4169 9.96429 14.1955C9.61607 14.0096 9.30357 13.7838 9.02679 13.5182C8.75 13.2525 8.51562 12.9581 8.32366 12.635C8.1317 12.3118 7.98214 11.9643 7.875 11.5924C7.76786 11.2205 7.71429 10.8398 7.71429 10.4502C7.71429 9.86139 7.82589 9.31022 8.04911 8.79668C8.27232 8.28314 8.57812 7.83158 8.96652 7.44199C9.35491 7.05241 9.80804 6.74915 10.3259 6.53223C10.8438 6.3153 11.4018 6.20462 12 6.2002C12.5938 6.2002 13.1496 6.31087 13.6674 6.53223C14.1853 6.75358 14.6406 7.05684 15.0335 7.44199C15.4263 7.82715 15.7321 8.2765 15.9509 8.79004C16.1696 9.30358 16.2812 9.85697 16.2857 10.4502C16.2857 10.8398 16.2344 11.2183 16.1317 11.5857C16.029 11.9532 15.8795 12.2985 15.683 12.6217C15.4866 12.9449 15.2522 13.2393 14.9799 13.5049C14.7076 13.7705 14.3973 13.9985 14.0491 14.1889ZM8.57143 10.4502C8.57143 10.9195 8.66071 11.36 8.83929 11.7717C9.01786 12.1834 9.26339 12.542 9.57589 12.8475C9.88839 13.1529 10.2522 13.3964 10.6674 13.5779C11.0826 13.7594 11.5268 13.8502 12 13.8502C12.4732 13.8502 12.9174 13.7617 13.3326 13.5846C13.7478 13.4075 14.1094 13.164 14.4174 12.8541C14.7254 12.5442 14.971 12.1834 15.154 11.7717C15.3371 11.36 15.4286 10.9195 15.4286 10.4502C15.4286 9.98092 15.3393 9.54043 15.1607 9.12871C14.9821 8.71699 14.7366 8.3584 14.4241 8.05293C14.1116 7.74746 13.7478 7.50397 13.3326 7.32246C12.9174 7.14095 12.4732 7.0502 12 7.0502C11.5268 7.0502 11.0826 7.13874 10.6674 7.31582C10.2522 7.4929 9.89062 7.73639 9.58259 8.04629C9.27455 8.35618 9.02902 8.71699 8.84598 9.12871C8.66295 9.54043 8.57143 9.98092 8.57143 10.4502Z" fill="#A1A5A6" stroke="A1A5A6" stroke-width="0.5"/>
            </svg>
          </i>
        <span>USERS</span>
        </a>
      </li>
      <li class="nav-item" v-if="!callTabs.transferType.length"
      :class="{'disabled-tab': onPhone && tab !== 'contact'}">
        <a class="nav-link" :class="{active: tab==='contact'}" @click.prevent="tab = 'contact'">
          <i class="">
            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.1183 12.3354C14.0422 12.6567 14.8744 13.1045 15.6149 13.6787C16.3554 14.2529 16.9872 14.9229 17.5103 15.6885C18.0334 16.4541 18.4342 17.2847 18.7127 18.1802C18.9912 19.0757 19.1305 20.0156 19.1305 21H17.8262C17.8262 19.8789 17.6291 18.8398 17.2351 17.8828C16.8411 16.9258 16.2908 16.0918 15.5843 15.3809C14.8778 14.6699 14.0524 14.1196 13.1081 13.73C12.1638 13.3403 11.1278 13.1387 10.0001 13.125C9.27316 13.125 8.57343 13.2173 7.90088 13.4019C7.22832 13.5864 6.60332 13.8462 6.02588 14.1812C5.44843 14.5161 4.92194 14.9229 4.4464 15.4014C3.97085 15.8799 3.56664 16.4097 3.23376 16.9907C2.90088 17.5718 2.63933 18.2041 2.44911 18.8877C2.2589 19.5713 2.16718 20.2754 2.17398 21H0.869629C0.869629 20.0156 1.01229 19.0757 1.29762 18.1802C1.58294 17.2847 1.98716 16.4575 2.51025 15.6987C3.03335 14.9399 3.66515 14.2769 4.40563 13.7095C5.14612 13.1421 5.97832 12.6875 6.90224 12.3457C6.37235 12.0586 5.8968 11.71 5.47561 11.2998C5.05441 10.8896 4.69775 10.4351 4.40563 9.93604C4.11351 9.43701 3.88593 8.90039 3.72289 8.32617C3.55985 7.75195 3.47832 7.16406 3.47832 6.5625C3.47832 5.65332 3.64816 4.80225 3.98784 4.00928C4.32751 3.21631 4.79286 2.51904 5.3839 1.91748C5.97493 1.31592 6.66447 0.847656 7.45251 0.512695C8.24055 0.177734 9.08974 0.00683594 10.0001 0C10.9036 0 11.7494 0.170898 12.5374 0.512695C13.3255 0.854492 14.0184 1.32275 14.6162 1.91748C15.2141 2.51221 15.6794 3.20605 16.0123 3.99902C16.3452 4.79199 16.515 5.64648 16.5218 6.5625C16.5218 7.16406 16.4437 7.74854 16.2874 8.31592C16.1312 8.8833 15.9036 9.4165 15.6047 9.91553C15.3058 10.4146 14.9491 10.8691 14.5347 11.2793C14.1203 11.6895 13.6482 12.0415 13.1183 12.3354ZM4.78267 6.5625C4.78267 7.28711 4.91854 7.96729 5.19028 8.60303C5.46202 9.23877 5.83566 9.79248 6.31121 10.2642C6.78675 10.7358 7.34042 11.1118 7.97221 11.3921C8.604 11.6724 9.27995 11.8125 10.0001 11.8125C10.7202 11.8125 11.3961 11.6758 12.0279 11.4023C12.6597 11.1289 13.21 10.7529 13.6787 10.2744C14.1475 9.7959 14.5211 9.23877 14.7997 8.60303C15.0782 7.96729 15.2175 7.28711 15.2175 6.5625C15.2175 5.83789 15.0816 5.15771 14.8098 4.52197C14.5381 3.88623 14.1645 3.33252 13.6889 2.86084C13.2134 2.38916 12.6597 2.01318 12.0279 1.73291C11.3961 1.45264 10.7202 1.3125 10.0001 1.3125C9.27995 1.3125 8.604 1.44922 7.97221 1.72266C7.34042 1.99609 6.79015 2.37207 6.3214 2.85059C5.85265 3.3291 5.479 3.88623 5.20047 4.52197C4.92194 5.15771 4.78267 5.83789 4.78267 6.5625Z" fill="#A1A5A6"/>
</svg>
          </i>
        <span>CONTACTS</span>
        </a>
      </li>
      <li class="nav-item" :class="{'disabled-tab': onPhone && tab !== 'dial'}">
        <a class="nav-link" :class="{active: tab==='dial'}" @click.prevent="tab = 'dial'">
          <i class="">
            <svg width="16" height="23" viewBox="0 0 16 23" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M3.2 3.2H0V0H3.2V3.2ZM9.6 3.2H6.4V0H9.6V3.2ZM16 3.2H12.8V0H16V3.2ZM3.2 9.6H0V6.4H3.2V9.6ZM9.6 9.6H6.4V6.4H9.6V9.6ZM16 9.6H12.8V6.4H16V9.6ZM3.2 16H0V12.8H3.2V16ZM9.6 16H6.4V12.8H9.6V16ZM9.6 22.4H6.4V19.2H9.6V22.4ZM16 16H12.8V12.8H16V16Z" fill="#A1A5A6"/>
</svg>
          </i>
          <span>DIAL</span>
        </a>
      </li>
      <!-- <li class="nav-item" v-if="!callTabs.transferType.length"
      :class="{'disabled-tab': onPhone && tab !== 'manual'}">
        <a class="nav-link" :class="{active: tab==='manual'}" @click.prevent="tab = 'manual'">
          <i class="">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M8.67174 17.1212C9.17955 17.379 9.63657 17.6954 10.0428 18.0704C10.4491 18.4454 10.8006 18.8673 11.0975 19.336C11.3944 19.8048 11.617 20.3047 11.7654 20.836C11.9139 21.3672 11.992 21.9219 11.9998 22.5H10.4998C10.4998 21.8828 10.3827 21.3008 10.1483 20.754C9.91391 20.2071 9.5936 19.7305 9.18736 19.3243C8.78112 18.918 8.30065 18.5938 7.74598 18.3516C7.1913 18.1095 6.60927 17.9923 5.99991 18.0001C5.37492 18.0001 4.7929 18.1173 4.25384 18.3516C3.71479 18.586 3.23823 18.9063 2.82418 19.3126C2.41012 19.7188 2.08591 20.1993 1.85153 20.754C1.61716 21.3086 1.49998 21.8907 1.49998 22.5H0C0 21.9297 0.0781238 21.3789 0.234371 20.8477C0.390619 20.3165 0.613272 19.8165 0.90233 19.3477C1.19139 18.879 1.53904 18.4571 1.94528 18.0821C2.35153 17.7071 2.81246 17.3868 3.32807 17.1212C2.74996 16.6915 2.30075 16.1603 1.98044 15.5275C1.66013 14.8947 1.49998 14.2189 1.49998 13.5002C1.49998 12.883 1.61716 12.301 1.85153 11.7541C2.08591 11.2072 2.40621 10.7307 2.81246 10.3244C3.2187 9.91818 3.69526 9.59397 4.24212 9.35179C4.78899 9.1096 5.37492 8.99242 5.99991 9.00023C6.71865 9.00023 7.39442 9.16038 8.02722 9.48069C8.66002 9.801 9.19127 10.2502 9.62095 10.8283C9.91782 10.2424 10.2967 9.71897 10.7576 9.25804C11.2186 8.79711 11.742 8.41821 12.3279 8.12134C11.7498 7.69166 11.3006 7.16041 10.9803 6.52761C10.66 5.89481 10.4998 5.21904 10.4998 4.5003C10.4998 3.88312 10.617 3.3011 10.8514 2.75423C11.0858 2.20736 11.4061 1.73081 11.8123 1.32456C12.2186 0.918321 12.6951 0.594107 13.242 0.351923C13.7889 0.10974 14.3748 -0.00744618 14.9998 0.000366205C15.6169 0.000366205 16.199 0.117552 16.7458 0.351923C17.2927 0.586295 17.7693 0.906602 18.1755 1.31285C18.5817 1.71909 18.906 2.19955 19.1481 2.75423C19.3903 3.30891 19.5075 3.89093 19.4997 4.5003C19.4997 5.21904 19.3395 5.89481 19.0192 6.52761C18.6989 7.16041 18.2497 7.69166 17.6716 8.12134C18.1794 8.37914 18.6364 8.69555 19.0427 9.07054C19.4489 9.44553 19.8005 9.8674 20.0973 10.3361C20.3942 10.8049 20.6169 11.3049 20.7653 11.8361C20.9137 12.3674 20.9919 12.922 20.9997 13.5002H19.4997C19.4997 12.883 19.3825 12.301 19.1481 11.7541C18.9138 11.2072 18.5935 10.7307 18.1872 10.3244C17.781 9.91818 17.3005 9.59397 16.7458 9.35179C16.1912 9.1096 15.6091 8.99242 14.9998 9.00023C14.3748 9.00023 13.7928 9.11741 13.2537 9.35179C12.7147 9.58616 12.2381 9.90646 11.824 10.3127C11.41 10.719 11.0858 11.1994 10.8514 11.7541C10.617 12.3088 10.4998 12.8908 10.4998 13.5002C10.4998 14.2189 10.3397 14.8947 10.0194 15.5275C9.69907 16.1603 9.24986 16.6915 8.67174 17.1212ZM11.9998 4.5003C11.9998 4.91435 12.0779 5.30107 12.2342 5.66044C12.3904 6.01981 12.6053 6.34011 12.8787 6.62136C13.1521 6.9026 13.4685 7.11744 13.8279 7.26588C14.1873 7.41432 14.5779 7.49244 14.9998 7.50025C15.4138 7.50025 15.8005 7.42213 16.1599 7.26588C16.5193 7.10963 16.8396 6.89479 17.1208 6.62136C17.4021 6.34793 17.6169 6.03152 17.7654 5.67215C17.9138 5.31279 17.9919 4.92217 17.9997 4.5003C17.9997 4.08624 17.9216 3.69953 17.7654 3.34016C17.6091 2.98079 17.3943 2.66048 17.1208 2.37924C16.8474 2.09799 16.531 1.88315 16.1716 1.73471C15.8123 1.58628 15.4216 1.50816 14.9998 1.50034C14.5857 1.50034 14.199 1.57847 13.8396 1.73471C13.4803 1.89096 13.16 2.1058 12.8787 2.37924C12.5975 2.65267 12.3826 2.96907 12.2342 3.32844C12.0858 3.68781 12.0076 4.07843 11.9998 4.5003ZM2.99995 13.5002C2.99995 13.9142 3.07808 14.3009 3.23433 14.6603C3.39057 15.0197 3.60541 15.34 3.87885 15.6212C4.15228 15.9025 4.46868 16.1173 4.82805 16.2657C5.18742 16.4142 5.57804 16.4923 5.99991 16.5001C6.41396 16.5001 6.80068 16.422 7.16005 16.2657C7.51942 16.1095 7.83972 15.8947 8.12097 15.6212C8.40222 15.3478 8.61706 15.0314 8.76549 14.672C8.91393 14.3126 8.99205 13.922 8.99986 13.5002C8.99986 13.0861 8.92174 12.6994 8.76549 12.34C8.60924 11.9807 8.3944 11.6603 8.12097 11.3791C7.84754 11.0979 7.53114 10.883 7.17177 10.7346C6.8124 10.5861 6.42178 10.508 5.99991 10.5002C5.58585 10.5002 5.19914 10.5783 4.83977 10.7346C4.4804 10.8908 4.16009 11.1057 3.87885 11.3791C3.5976 11.6525 3.38276 11.9689 3.23433 12.3283C3.08589 12.6877 3.00777 13.0783 2.99995 13.5002ZM14.9998 16.5001H23.9996V18.0001H14.9998V16.5001ZM14.9998 21V19.5001H23.9996V21H14.9998ZM14.9998 24V22.5H23.9996V24H14.9998Z" fill="#A1A5A6"/>
</svg>
          </i>
          <span>MANUAL</span>
        </a>
      </li> -->
      <li class="nav-item" v-if="callTabs.transferType"
      :class="{'disabled-tab': onPhone && tab !== 'forwarding'}">
        <a class="nav-link" :class="{active: tab==='forwarding'}" @click.prevent="tab = 'forwarding'">
          <i class="">
            <svg width="24" height="23" viewBox="0 0 24 23" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 0C13.0557 0 14.0739 0.134766 15.0547 0.404297C16.0355 0.673828 16.9526 1.05941 17.8062 1.56104C18.6597 2.06266 19.4346 2.66162 20.1309 3.35791C20.8271 4.0542 21.4261 4.83285 21.9277 5.69385C22.4294 6.55485 22.8149 7.47201 23.0845 8.44531C23.354 9.41862 23.4925 10.4368 23.5 11.5C23.5 12.5557 23.3652 13.5739 23.0957 14.5547C22.8262 15.5355 22.4406 16.4526 21.939 17.3062C21.4373 18.1597 20.8384 18.9346 20.1421 19.6309C19.4458 20.3271 18.6672 20.9261 17.8062 21.4277C16.9452 21.9294 16.028 22.3149 15.0547 22.5845C14.0814 22.854 13.0632 22.9925 12 23C10.9443 23 9.92611 22.8652 8.94531 22.5957C7.96452 22.3262 7.04736 21.9406 6.19385 21.439C5.34033 20.9373 4.56543 20.3384 3.86914 19.6421C3.17285 18.9458 2.57389 18.1672 2.07227 17.3062C1.57064 16.4452 1.18506 15.5317 0.915527 14.5659C0.645996 13.6001 0.507487 12.5781 0.5 11.5C0.5 10.4443 0.634766 9.42611 0.904297 8.44531C1.17383 7.46452 1.55941 6.54736 2.06104 5.69385C2.56266 4.84033 3.16162 4.06543 3.85791 3.36914C4.5542 2.67285 5.33285 2.07389 6.19385 1.57227C7.05485 1.07064 7.96826 0.685059 8.93408 0.415527C9.8999 0.145996 10.9219 0.00748698 12 0ZM12 21.5625C12.9209 21.5625 13.8081 21.4427 14.6616 21.2031C15.5151 20.9635 16.3162 20.6266 17.0649 20.1924C17.8136 19.7581 18.495 19.2303 19.1089 18.6089C19.7228 17.9875 20.2469 17.3099 20.6812 16.5762C21.1154 15.8424 21.4561 15.0413 21.7031 14.1729C21.9502 13.3044 22.07 12.4134 22.0625 11.5C22.0625 10.5791 21.9427 9.69189 21.7031 8.83838C21.4635 7.98486 21.1266 7.18376 20.6924 6.43506C20.2581 5.68636 19.7303 5.00505 19.1089 4.39111C18.4875 3.77718 17.8099 3.25309 17.0762 2.81885C16.3424 2.3846 15.5413 2.04395 14.6729 1.79688C13.8044 1.5498 12.9134 1.43001 12 1.4375C11.0791 1.4375 10.1919 1.55729 9.33838 1.79688C8.48486 2.03646 7.68376 2.37337 6.93506 2.80762C6.18636 3.24186 5.50505 3.76969 4.89111 4.39111C4.27718 5.01253 3.75309 5.6901 3.31885 6.42383C2.8846 7.15755 2.54395 7.95866 2.29688 8.82715C2.0498 9.69564 1.93001 10.5866 1.9375 11.5C1.9375 12.4209 2.05729 13.3081 2.29688 14.1616C2.53646 15.0151 2.87337 15.8162 3.30762 16.5649C3.74186 17.3136 4.26969 17.995 4.89111 18.6089C5.51253 19.2228 6.1901 19.7469 6.92383 20.1812C7.65755 20.6154 8.45866 20.9561 9.32715 21.2031C10.1956 21.4502 11.0866 21.57 12 21.5625ZM13.2241 5.96338L18.772 11.4888L13.2241 17.0366L12.2134 16.0259L16.0205 12.2188H9.84375C9.17741 12.2188 8.53727 12.0915 7.92334 11.8369C7.30941 11.5824 6.76286 11.2192 6.28369 10.7476C5.81201 10.2759 5.44889 9.73307 5.19434 9.11914C4.93978 8.50521 4.8125 7.86133 4.8125 7.1875H6.25C6.25 7.68164 6.34359 8.14583 6.53076 8.58008C6.71794 9.01432 6.97249 9.39616 7.29443 9.72559C7.61637 10.055 7.99821 10.3133 8.43994 10.5005C8.88167 10.6877 9.34961 10.7812 9.84375 10.7812H12.9209C13.9391 10.7812 14.9686 10.7775 16.0093 10.77L12.2134 6.97412L13.2241 5.96338Z" fill="#A1A5A6"/>
            </svg>
          </i>
        <span>FORWARDS</span>
        </a>
      </li>
      <li class="nav-item disabled-tab" v-if="false">
        <a class="nav-link">
          <i class="">
            <svg width="28" height="21" viewBox="0 0 28 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M24.4211 3.81818C24.8158 3.81818 25.1859 3.89276 25.5313 4.0419C25.8766 4.19105 26.1776 4.39489 26.4342 4.65341C26.6908 4.91193 26.8931 5.2152 27.0411 5.56321C27.1891 5.91122 27.2632 6.28409 27.2632 6.68182V18.1364C27.2632 18.5341 27.1891 18.907 27.0411 19.255C26.8931 19.603 26.6908 19.9062 26.4342 20.1648C26.1776 20.4233 25.8766 20.6271 25.5313 20.7763C25.1859 20.9254 24.8158 21 24.4211 21H3.57895C3.18422 21 2.81415 20.9254 2.46875 20.7763C2.12336 20.6271 1.82237 20.4233 1.56579 20.1648C1.30922 19.9062 1.10691 19.603 0.958886 19.255C0.81086 18.907 0.736847 18.5341 0.736847 18.1364V6.68182C0.736847 6.28409 0.81086 5.91122 0.958886 5.56321C1.10691 5.2152 1.30922 4.91193 1.56579 4.65341C1.82237 4.39489 2.12336 4.19105 2.46875 4.0419C2.81415 3.89276 3.18422 3.81818 3.57895 3.81818H8.3454C8.40461 3.28125 8.55757 2.77912 8.80428 2.31179C9.05099 1.84446 9.36185 1.44176 9.73685 1.10369C10.1118 0.765625 10.5461 0.497159 11.0395 0.298295C11.5329 0.0994318 12.0461 0 12.579 0H14.4737C15.0165 0 15.5296 0.0944602 16.0132 0.283381C16.4967 0.472301 16.926 0.740767 17.301 1.08878C17.676 1.43679 17.9918 1.84446 18.2484 2.31179C18.5049 2.77912 18.6579 3.28125 18.7072 3.81818H24.4211ZM12.579 1.90909C12.3026 1.90909 12.0362 1.95384 11.7796 2.04332C11.523 2.13281 11.2911 2.26705 11.0839 2.44602C10.8766 2.625 10.704 2.82386 10.5658 3.04261C10.4276 3.26136 10.324 3.51989 10.2549 3.81818H16.7977C16.7385 3.53977 16.6398 3.28622 16.5016 3.05753C16.3635 2.82884 16.1859 2.625 15.9688 2.44602C15.7516 2.26705 15.5197 2.13778 15.273 2.05824C15.0263 1.97869 14.7599 1.92898 14.4737 1.90909H12.579ZM19.6842 5.72727H3.57895C3.32237 5.72727 3.10033 5.82173 2.91283 6.01065C2.72533 6.19957 2.63158 6.4233 2.63158 6.68182V18.1364C2.63158 18.3949 2.72533 18.6186 2.91283 18.8075C3.10033 18.9964 3.32237 19.0909 3.57895 19.0909H19.6842V5.72727ZM25.3684 6.68182C25.3684 6.4233 25.2747 6.19957 25.0872 6.01065C24.8997 5.82173 24.6776 5.72727 24.4211 5.72727H21.579V19.0909H24.4211C24.6776 19.0909 24.8997 18.9964 25.0872 18.8075C25.2747 18.6186 25.3684 18.3949 25.3684 18.1364V6.68182Z" fill="#A1A5A6"/>
            </svg>
          </i>
        <span>DEPARTMENT</span>
        </a>
      </li>
    </ul>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import firebase  from 'firebase/app';
import Dialer from './Dialer.vue';
import Contacts from './Contacts.vue';
import ManualActions from './ManualActions.vue';
import ForwardingNumbers from './ForwardingNumbers.vue';
import OnCallModal from './OnCallModal.vue';
import CallEnded from './CallEnded.vue';
import Users from './Users.vue';
import { EventBus } from '@/models/event-bus';
import { ManualQueue, MessageType, Contact, User } from '../../../models';
import { UserState } from '../../../store/state_models'
import { mapState } from 'vuex'
import config from '@/config';

let cancelManualqueueSubscription: () => void
export default Vue.extend({
  props: ['currentLocationId', 'phoneNumbers', 'showPhoneControls', 'callTabs', 'connection', 'isCentered'],
  components: { Dialer, Contacts, ManualActions, ForwardingNumbers, OnCallModal, CallEnded, Users },
  data() {
    return {
      manualCallEnded: false,
      manualActionsData: [] as ManualQueue[],
      manualQueue: [] as ManualQueue[]
    }
  },
  created() {
    // this.fetchData();
  },
  watch: {
    currentLocationId() {
      // this.fetchData();
    }
  },
  computed: {
    onPhone() {
      return this.callTabs[this.callTabs.currentTab].onPhone;
    },
    ...mapState('user', {
      user: (s: UserState) => {
        console.log(s.user)
        return s.user ? new User(s.user) : undefined
      }
    }),
    tab: {
      get() {
        if (this.callTabs && this.callTabs.currentTab) {
          if (this.callTabs.currentTab === 'first') return this.callTabs.first.activeTab;
          else if (this.callTabs.currentTab === 'second') return this.callTabs.second.activeTab;
        }
        return 'dial';
      },
      set(value: string) {
        if (this.callTabs && this.callTabs.currentTab && this.callTabs.currentTab === 'second') {
          Vue.set(this.callTabs.second, 'activeTab', value)
        } else {
          Vue.set(this.callTabs.first, 'activeTab', value)
        }
      }
    },
    timerMinutes(): number {
      return Math.floor(this.callTabs[this.callTabs.currentTab].time / 60)
    },
    timerSeconds(): number {
      return this.callTabs[this.callTabs.currentTab].time - this.timerMinutes * 60
    },
    timerFullTime(): string {
      return (
        this.timerMinutes.toLocaleString(undefined, {
          minimumIntegerDigits: 2
        }) +
        ':' +
        this.timerSeconds.toLocaleString(undefined, { minimumIntegerDigits: 2 })
      )
    },
    displayFirstCallTime(): string {
      let min = Math.floor(this.callTabs.first.time / 60);
      let sec = this.callTabs.first.time - min * 60
      return (
        min.toLocaleString(undefined, {
          minimumIntegerDigits: 2
        }) +
        ':' +
        sec.toLocaleString(undefined, { minimumIntegerDigits: 2 })
      )
    }
  },
  methods: {
    openIVR() {
      this.callTabs[this.callTabs.currentTab].openIVR = true;
      this.callTabs[this.callTabs.currentTab].activeTab = 'dial';
    },
    changeTab(tab: string) {
      Vue.set(this.callTabs, 'currentTab', tab);
    },
    openTransfer(transferType: string) {
      Vue.set(this.callTabs, 'transferType', transferType);
      this.callTabs.showOneTab = false;
      this.changeTab('second');
      Vue.set(this.callTabs, 'showSideTabs', true);
    },
    async fetchData() {
      if (cancelManualqueueSubscription) {
        cancelManualqueueSubscription();
      }
      let query: firebase.firestore.Query = await ManualQueue.getbyLocationIdAndTypeRealtime({
        'locationId': this.currentLocationId,
        'messageType': MessageType.TYPE_CAMPAIGN_MANUAL_CALL,
        'assignedTo': this.user && this.user.permissions.assigned_data_only ? this.user.id : ''
      });
      cancelManualqueueSubscription = query.onSnapshot(async snapshot => {
        this.manualQueue = snapshot.docs.map(d => new ManualQueue(d));
      })
    },
    backButton() {
      if (this.callTabs[this.callTabs.currentTab].openIVR) {
        this.callTabs[this.callTabs.currentTab].openIVR = false;
      } else {
        this.changeTab('first');
        this.callTabs.showOneTab = true;
        this.callTabs.transferType = '';
      }
    },
    markComplete(manualCallId: string) {
      this.manualCallEnded = true;
      if (manualCallId) this.$store.commit('manualCallStatus/show', manualCallId);
    }
  },
  mounted() {
    this.$root.$on('dialer:queueNextManual', this.markComplete)
    EventBus.$on("callNextManual", async (manualCallId: string, action: string) => {
      if (this.tab === 'manual' && manualCallId) {
        this.manualActionsData = [];

        if(this.manualQueue.length > 0)
        this.manualActionsData.push(this.manualQueue[0]);

        const manualCall = await ManualQueue.getById(manualCallId);
        if (manualCall)  {
          await manualCall.ref.update({
            'in_progress': false,
            'completed': true
          });
        }
        if (action === 'completed') { // Completed
          if (manualCall.campaignStatusId.includes('workflow_')) { // Workflow-related
            const workflowStatusId = manualCall.campaignStatusId.replace('workflow_', '')
            this.$http.post(
              `${config.workflowServiceURL}/${this.currentLocationId}/${workflowStatusId}/force-resume`,
              {
                actionFrom: {
                    userId: this.user?.id,
                    channel: 'web_app',
                    source: 'contact_detail_page'
                },
                manualCall: {
                  manualCallStatus: action,
                  id: manualCall.id
                }
              }
            )
          } else { // Campaign-related
            this.$http.post(
              `/campaign/${manualCall.campaignId}/status/${manualCall.campaignStatusId}/call?status=${action}&manualCallId=${manualCall.id}`,
              {}
            )
          }
          await ManualQueue.deleteRepliedActions({
            campaignId: manualCall.campaignId,
            contactId: manualCall.contactId,
            locationId: this.currentLocationId
          })
        } else { // Busy, No Answer or Voicemail
          if (manualCall.campaignStatusId.includes('workflow_')) { // Workflow-related
            const workflowStatusId = manualCall.campaignStatusId.replace('workflow_', '')
            this.$http.post(
              `${config.workflowServiceURL}/${this.currentLocationId}/${workflowStatusId}/force-resume`,
              {
                actionFrom: {
                    userId: this.user?.id,
                    channel: 'web_app',
                    source: 'contact_detail_page'
                },
                manualCall: {
                  manualCallStatus: action,
                  id: manualCall.id
                }
              }
            )
          } else { // Campaign-related
            this.$http.post(
              `/campaign/${manualCall.campaignId}/status/${manualCall.campaignStatusId}/next?status=${action}&manualCallId=${manualCall.id}`,
              {}
            )
          }
        }
      }
    });
  },
  beforeDestroy() {
    EventBus.$off("callNextManual");
    this.$root.$off('dialer:queueNextManual')
    if (cancelManualqueueSubscription) cancelManualqueueSubscription();
  }
})
</script>

<style scoped>
  .bootstrap-select > .btn.dropdown-toggle .filter-option{
    padding-left: 0
  }
  .form-group label {
    font-size: 12px;
    line-height: 14px;
    color: #607179;
  }
 .custom-tab {
    position: absolute;
    top: 28px;
    display: flex;
    flex-direction: column;
    right: -50px;
}
  .custom-tab li {
    background: #F2F2F2;
    border-radius: 0px 5px 5px 0px;
    margin-top: 3px;
    WIDTH: 50px;
    height: 80px;
    display: flex;
    justify-content: center;
    border-left: 1px solid #F2F2F2;
    align-items: center;
  }
  .custom-tab li.active {
    background: #fff;
    border: 1px solid #eaeaea;
    border-left: none;
    /* box-shadow: 0 1px 4px 4px rgba(0, 0, 0, 0.1);*/

  }
  .custom-tab li a {
    padding: 0 !important;
    position: relative;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .custom-tab li a:hover {
    background: transparent !important;
  }
  .custom-tab li a .active-status {
    position: absolute;
    width: 6px;
    height: 6px;
    background: #27AE60;
    border-radius: 50%;
    top: 6px;
    right: 6px;
  }
  .custom-tab li a .second-status {
    position: absolute;
    width: 6px;
    height: 6px;
    background: #FF9416;
    border-radius: 50%;
    top: 6px;
    right: 6px;
  }
  .custom-tab li a h6 {
    font-weight: 500;
    font-size: 10px;
    line-height: 12px;
    color: #9C9C9C;
    margin: 0;
        position: absolute;
    bottom: 6px;
  }
  .collapse-icon{
	  position: absolute;
    top: 0;
    right: 0px;
    background: #FFFFFF;
    border-radius: 3px;
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #f2f2f2;
  }
  .collapse-icon:hover{
    background:#f2f2f2 ;
  }
  .nav-tabs {
    display: flex;
    /* justify-content: space-between; */
    align-items: center;
    border-bottom: none;
    background: #f6f7f8;
    height: 85px;
    justify-content: center;
  }
  .nav-tabs .nav-item {
    margin-right: 0 !important;
    flex: 1;
  }
  .nav-tabs .nav-item a {
    margin-right: 0 !important;
    display: flex;
    flex-direction: column;
    align-items: center;
     color: #A1A5A6 !important;
     border: none;
     font-size: 12px;
      font-weight: 500;
    line-height: 14px;
    text-align: center;
    letter-spacing: 0.1em;
  }
  .nav-tabs .nav-item a span{
    margin-top: 10px;
  }
  .nav-tabs .nav-item a .icon,
  .nav-tabs .nav-item a .icon-user,
  .nav-tabs .nav-item a .fa-users {
      font-size: 24px;
      margin-bottom: 16px;
      color: #C2C9CC;
  }
    .nav.nav-tabs a.active {
      color: #393B3C !important;
    }
    .nav.nav-tabs a.active svg path{
      fill:#393B3C !important;
      stroke-width:1px;
    }
    .nav.nav-tabs a.active svg rect{
      stroke:#393B3C !important;
      stroke-width: 1.5px;
    }
    .nav.nav-tabs .users-tab a.active svg path{
      fill:#393B3C !important;
      stroke:#393B3C !important;
      stroke-width: 0.5px;
    }
    .nav-tabs .nav-item a.active .icon,
    .nav-tabs .nav-item a.active .icon-user,
     .nav-tabs .nav-item a.active .fa-users {
         color: #393B3C;
     }

    .disabled-tab {
      opacity: 0.5;
      cursor: not-allowed !important;
    }
    .disabled-tab a {
      pointer-events: none;
    }
    .dialer-center {
      top: 25px;
      left: auto;
      right: -120px;
    }
    .dialer-left {
      top: 25px;
      left: auto;
      right: 0px;
    }
</style>
