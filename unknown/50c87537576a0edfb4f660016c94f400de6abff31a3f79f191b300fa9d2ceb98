<template>
  <modal
    class="wallet-transaction__container"
    v-if="show"
    @close="closeModal"
    :showCloseIcon="true"
  >
    <div>
      <div class="wallet-transaction__header">
        <div class="wallet-transaction__header-title">
          Transaction Details -
          {{ loading ? '...' : (locationName ? locationName : messageDetails.messageType) }}
        </div>
      </div>
      <div class="wallet-transaction__body" v-if="loading">
        <moon-loader size="30px" />
      </div>
      <div class="wallet-transaction__body" v-else>
        <div class="metadata">
          <div class="transaction-id data">
            <span>Transaction ID:</span>
            <span>{{ transactionData.id }}</span>
          </div>
          <div class="contact-data" v-if="contactLink">
            <div class="contact-name data" v-if="contact.name">
              <span>Contact name:</span>
              <span>
                <a :href="contactLink" target="_blank">{{ contact.name }}</a>
              </span>
            </div>
            <div class="contact-phone data" v-else-if="contact.phone">
              <span>Contact phone:</span>
              <span>
                <a :href="contactLink" target="_blank">{{ contact.phone }}</a>
              </span>
            </div>
            <div class="contact" v-else>
              <span>Contact:</span>
              <span>
                <a :href="contactLink" target="_blank">Open Contact page</a>
              </span>
            </div>
          </div>
          <div v-else-if="messageDetails && messageDetails.contactId">
            Contact:&nbsp;<i>Not Found</i>
          </div>
          <div v-if="messageDetails.sentDate" class="sent-date data">
            <span>{{ sentDateLabel }}:</span>
            <span>{{ messageDetails.sentDate +' '+ timeZoneAbbr  }}</span>
          </div>
          <div
            class="from data"
            v-if="messageDetails.messageType === 'Email' || messageDetails.from"
          >
            <span>From:</span>
            <render-email-address
              :data="messageDetails.from"
              :hide-deleted-contact="false"
            />
          </div>
          <div
            v-if="messageDetails.messageType === 'Email' && messageDetails.to"
          >
            <div class="to data" v-if="contact">
              <span>To:</span>
              <render-email-address
                v-for="toEmail of messageDetails.to"
                :key="toEmail"
                :data="toEmail"
              />
            </div>
            <div class="to data" v-else>
              <span>To:</span>
              <span>
                <i v-if="messageDetails.direction === 'outbound'">
                  &lt;Deleted&gt;
                </i>
                <span v-else>{{ messageDetails.to[0] }}</span>
              </span>
            </div>
          </div>
        </div>
        <div
          class="body"
          v-if="messageDetails && messageDetails.body"
          v-html="messageDetails.body"
        >
          <!-- {{ messageDetails.body }} -->
        </div>
        <div class="details">
          <div class="transaction-details">
            <div class="date-billed data">
              <span>Time billed:</span>
              <span>{{ transactionData.date +' '+ timeZoneAbbr }}</span>
            </div>
            <div class="transaction-amount data">
              <span
                >{{ transactionData.amount > 0 ? 'Credit' : 'Charge' }}:</span
              >
              <span :class="transactionData.amount < 0 ? 'debit' : 'credit'">{{
                getReadableAmount(transactionData.amount)
              }}</span>
            </div>
            <div class="transaction-balance data">
              <span>Balance after transaction:</span>
              <span>{{ currency }}{{ transactionData.balance }}</span>
            </div>
          </div>
          <div class="message-details">
            <div v-if="contactLink" class="link-to-conversation">
              <a :href="contactLink" target="_blank">
                <i class="fas fa-link"></i>&nbsp;&nbsp;Open conversation
              </a>
            </div>
            <div class="type data">
              <span>Type:</span>
              <span>
                {{ messageDetails.messageType }}
                {{
                  messageDetails.direction
                    ? ` [${messageDetails.direction.toUpperCase()}]`
                    : ''
                }}
              </span>
            </div>
            <div v-if="getSID" class="type data">
              <span>{{ messageDetails.messageType + ' SID'}}:</span>
              <span v-if="typeof getSID === 'string'">
                {{ getSID }}
              </span>
              <ul v-else>
                <li style="list-style: initial; font-weight:bold; margin-left: 16px;" v-for="sid in getSID" :key="sid">{{ sid }}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div class="wallet-transaction__footer">
        <button @click="closeModal" class="btn btn-primary">Okay</button>
      </div>
    </div>
  </modal>
</template>
<script lang="ts">
import Vue from 'vue'
import Modal from '../../common/Modal.vue'
import RenderEmailAddress from './RenderEmailAddress.vue'
import { Contact } from '@/models';
import moment from 'moment-timezone';

export default Vue.extend({
  props: {
    show: {
      default: false,
      type: Boolean,
    },
    transactionId: {
      type: String,
      default: '',
    },
    transactionData: {
      type: Object,
    },
    messageDetails: {
      type: Object,
    },
    getReadableAmount: {
      type: Function,
    },
    currency: {
      type: String,
      default: '$',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    locationName: String
  },
  components: {
    Modal,
    RenderEmailAddress,
  },
  data() {
    return {
      contact: null as Contact | null,
    }
  },
  computed: {
    timeZoneAbbr() {
      const date = new Date()
      return moment.tz.zone(moment.tz.guess()).abbr(date);
    },
    contactLink(): string {
      if (this.contact) {
        const { locationId, contactId } = this.messageDetails
        const link = `${window.origin}/location/${locationId}/customers/detail/${contactId}`
        return link
      }

      return ''
    },
    // conversationLink(): string {
    //   if (this.messageDetails && this.messageDetails.conversationId) {
    //     const { locationId, conversationId } = this.messageDetails
    //     const link = `${window.origin}/location/${locationId}/conversations/${conversationId}`
    //     return link
    //   }
    //   return ''
    // },
    sentDateLabel(): string {
      const { messageType, direction } = this.messageDetails

      if (!messageType) return ''

      if (messageType === 'Email') {
        return 'Date Sent'
      } else if (messageType === 'SMS') {
        return `Date ${direction === 'inbound' ? 'Received' : 'Sent'}`
      } else if (messageType.indexOf('Call') > -1) {
        return 'Time of Call'
      } else {
        return 'Date Sent'
      }
    },
    getSID() {
      const sids = []
        if (this.messageDetails.meta?.altId) sids.push(`${this.messageDetails.meta?.altId} (Primary)`)

      if (this.messageDetails.messageType === 'Campaign Call' && this.messageDetails.meta?.messageMeta?.campaign_call) {
        sids.push(this.messageDetails.meta.messageMeta.campaign_call.first_call_sid, this.messageDetails.meta.messageMeta.campaign_call.second_call_sid)
      }
      else if (this.messageDetails.messageType === 'Campaign Voicemail' && this.messageDetails.meta?.messageMeta?.vm_drop) {
        sids.push(this.messageDetails.meta.messageMeta.vm_drop.first_call_sid, this.messageDetails.meta.messageMeta.vm_drop.second_call_sid)
      }
      else if (this.messageDetails.meta?.messageMeta?.power_dialer?.calls?.length || this.messageDetails.meta?.messageMeta?.incoming_call?.child_call_sid) {
        sids.push(...this.messageDetails.meta.messageMeta.power_dialer.calls.map(el => el.call_sid))

        if (this.messageDetails.meta?.messageMeta?.incoming_call?.child_call_sid) {
          sids.push(this.messageDetails.meta.messageMeta.incoming_call.child_call_sid)
        }
      }
      else return this.messageDetails.meta?.altId

      return sids.filter(element => element !== undefined)
    }
  },
  methods: {
    closeModal() {
      this.$emit('close')
      this.contact = null
    },
  },
  watch: {
    messageDetails: async function (newData) {
      try {
        const { contactId } = newData
        this.contact = contactId ? await Contact.getById(contactId) : null
      } catch (err) {
        console.log(err)
      }
    },
  },
})
</script>
<style lang="scss" scoped>
.wallet-transaction__header {
  padding: 16px 32px;
  border-bottom: 1px solid #c4c4c4;

  .wallet-transaction__header-title {
    font-size: 24px;
    line-height: 28px;
    color: #737373;
  }
}

.wallet-transaction__body {
  padding: 32px;
  height: 614px;
  max-height: calc(100vh - 240px);
  overflow: auto;

  .metadata {
    margin-bottom: 25px;
  }

  .body {
    background: #f3f8fb;
    padding: 26px;
    margin: 26px 0;
    font-size: 12px;
    color: #737373;
    border-radius: 6px;
  }

  .details {
    margin-top: 25px;
    display: flex;
    justify-content: space-between;

    .transaction-amount {
      .credit {
        color: green;
      }
      .debit {
        color: red;
      }
    }

    .link-to-conversation {
      a:hover {
        text-decoration: underline;
      }
    }
  }

  .data {
    font-size: 14px;
    color: black;

    span:first-child {
      color: #737373;
      margin-right: 4px;
    }

    span:last-child {
      font-weight: bold;
    }
  }
}

.wallet-transaction__footer {
  padding-bottom: 30px;
  text-align: right;
  padding-right: 32px;

  button {
    padding: 9px 34px;
  }
}
</style>
