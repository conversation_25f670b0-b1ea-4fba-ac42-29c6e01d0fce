<template>
  <div>
    <span :for="name" v-if="label" class="block text-sm font-medium text-gray-700">{{
      label
    }}</span>
    <div class="mt-1 relative rounded-md shadow-sm">
      <UITextArea
        :rows="rows"
        :type="type"
        :name="name"
        :id="id"
        :placeholder="placeholder"
        @input="val => $emit('input', val)"
        @change="val => $emit('change', val)"
        @blur="val => $emit('blur', val)"
        :value="value"
        :class="error ? 'border-red-300 focus:ring-red-400 focus:border-red-400 pr-10' : ''"
        @keyup="val => $emit('keyup', val)"
        :disabled="disabled"
        :maxlength="maxlength"
        :expandable="expandable"
      />
      <div v-if="error" class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
      <i class="fas fa-exclamation-circle h-5 w-5 text-red-400 text-lg mb-2 mr-1 pl-1" aria-hidden="true"></i>
     </div>
    </div>
   <p v-if="errorMsg && error" class="mt-2 text-sm text-red-600" id="email-error">{{errorMsg}}</p>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  $_veeValidate: {
    value() {
      return this.value;
    },
    name() {
      return this.name;
    }
  },
  props: {
    value: {
      type:String,
      default:''
    },
    label: {
      type:String
    },
    id: {
      type:String,
    },
    placeholder: {
      type:String,
      default:''
    },
    name: {
      type:String,
      default:''
    },
    type: {
      type:String,
      default:'text'
    },
    error: {
      type: Boolean,
      default: false
    },
    errorMsg: {
      type: String,
      default: ''
    },
    rows:{
      type: [String,Number],
      default:4
    },
    disabled: {
      type: [Boolean, String]
    },
    maxlength: {
      type: String,
      default: ''
    },
    expandable: {
      type: Boolean,
      default: true
    }
  }
})
</script>

<style scoped></style>
