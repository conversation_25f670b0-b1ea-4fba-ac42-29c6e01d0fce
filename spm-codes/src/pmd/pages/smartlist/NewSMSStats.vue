<template>
  <div class="smsServiceStatsView custom-width">
    <div class="custom-height">
      <b-tabs id="bootstrap-tab-custom-id" content-class="mt-3" lazy>
        <b-tab
          @click="changeTab('total')"
          title="Total Attempted"
          :active="activeTab === 'total'"
        >
          <StatsDetailModalTable
            :innerGridClass="'igs'"
            :title="title"
            :items="items"
            :exportItems="exportItems"
            :fields="fields"
            :currentPage="currentPage"
            :itemPerPage="itemPerPage"
            :rows="rows"
            :isLoading="isLoading"
            :headerOptions="headerOptions"
            :fileName="fileName"
            @pageChanged="pageChanged"
            @perPageChanged="perPageChanged"
            @exportAll="exportAll"
          />
        </b-tab>
        <b-tab
          @click="changeTab('delivered')"
          title="Delivered"
          :active="activeTab === 'delivered'"
        >
          <StatsDetailModalTable
            :innerGridClass="'igs'"
            :title="title"
            :items="items"
            :exportItems="exportItems"
            :fields="fields"
            :currentPage="currentPage"
            :itemPerPage="itemPerPage"
            :rows="rows"
            :isLoading="isLoading"
            :headerOptions="headerOptions"
            :fileName="fileName"
            @pageChanged="pageChanged"
            @perPageChanged="perPageChanged"
            @exportAll="exportAll"
          />
        </b-tab>
        <b-tab
          @click="changeTab('clicked')"
          title="Clicked"
          :active="activeTab === 'clicked'"
        >
          <StatsDetailModalTable
            :innerGridClass="'igs'"
            :title="title"
            :items="items"
            :exportItems="exportItems"
            :fields="fields"
            :currentPage="currentPage"
            :itemPerPage="itemPerPage"
            :rows="rows"
            :isLoading="isLoading"
            :headerOptions="headerOptions"
            :fileName="fileName"
            @pageChanged="pageChanged"
            @perPageChanged="perPageChanged"
            @exportAll="exportAll"
          />
        </b-tab>
        <b-tab
          @click="changeTab('failed')"
          title="Failed"
          :active="activeTab === 'failed'"
        >
          <StatsDetailModalTable
            :innerGridClass="'igs'"
            v-if="!showGraph"
            :title="title"
            :items="items"
            :exportItems="exportItems"
            :fields="fields"
            :currentPage="currentPage"
            :itemPerPage="itemPerPage"
            :rows="rows"
            :isLoading="isLoading"
            :headerOptions="headerOptions"
            :fileName="fileName"
            @pageChanged="pageChanged"
            @perPageChanged="perPageChanged"
            @exportAll="exportAll"
          >
            <template slot="headerButton">
              <button
                type="button"
                @click="showGraphMethod"
                class="btn btn-sm hl-table-button"
                id="hl_smartlists--filter-btn"
              >
                <svg fill="currentColor" height="20px" viewBox="0 0 20 20">
                  <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
                  <path
                    d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"
                  ></path>
                </svg>
                Summary
              </button>
            </template>
          </StatsDetailModalTable>
          <div style="height: inherit" v-else>
            <div class="hl_controls p-2 pb-0 m-0 row">
              <div class="hl_controls--right col justify-content-end">
                <button
                  type="button"
                  @click="showGraphMethod"
                  class="btn btn-sm hl-table-button"
                  id="hl_smartlists--filter-btn"
                >
                  <svg
                    width="1.5em"
                    height="20px"
                    viewBox="0 0 16 16"
                    class="bi bi-arrow-bar-left"
                    fill="currentColor"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M5.854 4.646a.5.5 0 0 0-.708 0l-3 3a.5.5 0 0 0 0 .708l3 3a.5.5 0 0 0 .708-.708L3.207 8l2.647-2.646a.5.5 0 0 0 0-.708z"
                    />
                    <path
                      fill-rule="evenodd"
                      d="M10 8a.5.5 0 0 0-.5-.5H3a.5.5 0 0 0 0 1h6.5A.5.5 0 0 0 10 8zm2.5 6a.5.5 0 0 1-.5-.5v-11a.5.5 0 0 1 1 0v11a.5.5 0 0 1-.5.5z"
                    />
                  </svg>
                  <span>Back</span>
                </button>
              </div>
            </div>
            <div v-if="!chartDataLoading">
              <div class="row" v-if="chartData.length > 0">
                <div class="col-12 d-flex justify-content-center">
                  <h5>Error Code Chart</h5>
                </div>
                <div
                  class="col-sm col-4 d-flex justify-content-end align-items-center"
                >
                  <table class="table table-bordered" style="width: 240px">
                    <thead>
                      <tr style="text-align: center">
                        <th>Error Code</th>
                        <th>Count</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr v-for="data in chartData" :key="data.errCode">
                        <td>{{ data.errCode }}</td>
                        <td>{{ data.count }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div
                  style="width: 320px; height: 320px"
                  class="col-sm col-8 d-flex justify-content-start align-items-center"
                >
                  <highcharts
                    style="width: 240px; height: 240px"
                    :options="callPieChart"
                  ></highcharts>
                </div>
              </div>
              <div v-else class="col-12 d-flex justify-content-center">
                <h5>No error data found</h5>
              </div>
            </div>
            <div
              v-else
              style="height: 70%"
              class="d-flex justify-content-center align-items-center"
            >
              <MoonLoader size="50px" radius="50%" />
            </div>
          </div>
        </b-tab>
      </b-tabs>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import axios from 'axios'
import moment from 'moment'
import defaults from '@/config'
import { mapState } from 'vuex'
import { User } from '@/models'
import { getCountryDateFormat } from '@/models/index'
import StatsDetailModalTable from '../../components/StatsDetailModalTable'
import { Chart } from 'highcharts-vue'

let getData

export default Vue.extend({
  props: {
    modalTitle: String,
    option: String,
    campaign_id: String,
    campaign_step_id: String,
    selectedDate: Object,
    bulk_req_id: String,
  },
  components: {
    StatsDetailModalTable,
    highcharts: Chart,
  },
  data() {
    return {
      isLoading: false,
      activeTab: this.option,
      records: [],
      currentPage: 1,
      itemPerPage: 25,
      rows: 0,
      showGraph: false,
      headerOptions: {
        alwaysShowHeader: true,
        isExportOption: true,
      },
      exportItems: [],
      emailReportingUrl: defaults.emailReportingUrl,
      baseUrl: defaults.baseUrl,
      fields: [
        /* { key: 'first_name', label: 'First Name', sortable: false, checked: true }, */
        {
          key: 'full_name',
          label: 'Full Name',
          sortable: false,
          checked: true,
          hasHtml: true,
        },
        { key: 'phone', label: 'Phone Number', sortable: false, checked: true },
        { key: 'status', label: 'Status', sortable: false, checked: true },
        {
          key: 'timestamp',
          label: 'TimeStamp',
          sortable: false,
          checked: true,
        },
        { key: 'error', label: 'Error', sortable: false, checked: true },
      ],
      contactIds: [],
      contacts: {},
      title: '',
      showCount: true,
      chartData: [],
      chartInnerData: [],
      chartDataLoading: false,
      locationId: this.$route.params.location_id,
      fileName: '',
      get callPieChart() {
        return {
          chart: {
            plotBackgroundColor: null,
            plotBorderWidth: 0,
            plotShadow: false,
            style: {
              fontFamily: 'Roboto',
            },
          },
          title: {
            text: '',
            align: 'center',
            verticalAlign: 'middle',
            y: 40,
          },
          tooltip: {
            pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>',
          },
          plotOptions: {
            pie: {
              dataLabels: {
                enabled: false,
              },
            },
          },
          series: [
            {
              type: 'pie',
              name: 'Call Stats',
              innerSize: '70%',
              data: this.chartInnerData,
            },
          ],
          credits: {
            enabled: false,
          },
        }
      },
    }
  },
  computed: {
    items() {
      if (this.isLoading) {
        return []
      }

      if (!this.records || (this.records && this.records.length < 1)) {
        return []
      }

      let contactObj = JSON.parse(JSON.stringify(this.contacts))

      return this.getFinalObject(this.records, contactObj, false)
    },
    ...mapState('user', {
      userId: (s: UserState) => {
        const user = s.user ? new User(s.user) : undefined
        return user && user.isAssignedTo ? user.id : ''
      },
    }),
  },
  methods: {
    getFinalObject(records, contactObj, isdownloadData) {
      return records.map(record => {
        if (
          this.userId &&
          this.userId !==
            (contactObj[record.contactId] &&
              contactObj[record.contactId].assigned_to)
        ) {
          return {
            full_name: `${this.capital_letter(
              contactObj[record.contactId]
                ? contactObj[record.contactId].contact_name || 'Nil'
                : 'NOT FOUND OR DELETED'
            )}`,
            phone: '**secret**',
            status: this.getStatus(record),
            timestamp: moment(record.lastUpdateOn).format(
              getCountryDateFormat('extended-normal')
            ),
            error: '**secret**',
          }
        } else {
          return {
            full_name:
              !isdownloadData && contactObj[record.contactId]
                ? `<a href="/location/${this.locationId}/customers/detail/${
                    record.contactId
                  }">${this.capital_letter(
                    contactObj[record.contactId].contact_name || '-'
                  )}</a>`
                : contactObj[record.contactId]
                ? contactObj[record.contactId].contact_name
                : `NOT FOUND OR DELETED`,
            phone:
              record.phone ||
              (contactObj[record.contactId]
                ? contactObj[record.contactId].phone || '-'
                : `NOT FOUND OR DELETED`),
            status: this.getStatus(record),
            timestamp: moment(record.lastUpdateOn).format(
              getCountryDateFormat('extended-normal')
            ),
            error: `${record.errCode ? record.errCode + ' : ' : ''} ${
              record.errMessage || ''
            }`,
          }
        }
      })
    },
    sleep(duration) {
      //this is a fake function
      return new Promise(r => setTimeout(r, duration))
    },
    async showGraphMethod() {
      this.showGraph = !this.showGraph
      if (this.showGraph) {
        await this.fetchingChartData()
      }
    },
    getRandomColor() {
      let letters = '0123456789ABCDEF'
      let color = '#'
      for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)]
      }
      return color
    },
    async fetchingChartData() {
      this.chartDataLoading = true
      /* await this.sleep(1000) */
      try {
        let filterParams: { [key: string]: any } = {}
        let path = `${this.emailReportingUrl}/agent_reporting/sms_stats_errorcodes`
        if (this.bulk_req_id) {
          //path = `${path}/bulk_request`
          filterParams.bulk_req_id = this.bulk_req_id
        } else if (this.campaign_id && this.campaign_step_id) {
          filterParams.campaign_id = this.campaign_id
          filterParams.campaign_step_id = this.campaign_step_id
          filterParams.from_date = new Date(
            this.selectedDate.start
          ).toISOString()
          filterParams.to_date = new Date(this.selectedDate.end).toISOString()
        }
        let stats = await axios.post(path, {
          location_id: this.locationId,
          ...filterParams,
        })
        this.chartData = stats.data.response.filter(element => element.errCode)

        this.chartInnerData = this.chartData.map(element => ({
          name: element.errCode,
          y: element.count,
          color: this.getRandomColor(),
        }))

        this.chartDataLoading = false
      } catch (err) {
        this.chartDataLoading = false
        console.log(err)
      }
    },
    fetchingTableData() {
      clearTimeout(getData)
      getData = setTimeout(() => {
        this.fetchData()
      }, 0)
    },
    pageChanged(page) {
      this.showCount = false
      this.currentPage = page
      this.fetchingTableData()
    },
    perPageChanged(perPage) {
      this.showCount = true
      this.itemPerPage = perPage
      this.currentPage = 1
      this.fetchingTableData()
    },
    changeTab(option) {
      if (this.activeTab !== option) {
        this.activeTab = option
        this.showGraph = false
        this.currentPage = 1
        this.fetchingTableData()
      }
      return ''
    },
    capital_letter(str) {
      str = str.split(' ')

      for (var i = 0, x = str.length; i < x; i++) {
        str[i] = str[i][0].toUpperCase() + str[i].substr(1)
      }

      return str.join(' ')
    },
    getStatus(row) {
      if (row.failed) {
        return 'failed'
      } else if (row.clicked) {
        return 'clicked'
      } else if (row.delivered) {
        return 'delivered'
      } else {
        return '-'
      }
    },
    async fetchData() {
      this.isLoading = true
      try {
        let filterParams: { [key: string]: any } = {}
        if (this.activeTab === 'delivered') filterParams['showDelivered'] = true
        else if (this.activeTab === 'clicked')
          filterParams['showClicked'] = true
        else if (this.activeTab === 'failed') filterParams['showFailed'] = true

        let path = `${this.emailReportingUrl}/agent_reporting/sms_stats_report`
        if (this.bulk_req_id) {
          path = `${path}/bulk_request`
          filterParams.bulk_req_id = this.bulk_req_id
        } else if (this.campaign_id && this.campaign_step_id) {
          filterParams.campaign_id = this.campaign_id
          filterParams.campaign_step_id = this.campaign_step_id
          filterParams.from_date = new Date(
            this.selectedDate.start
          ).toISOString()
          filterParams.to_date = new Date(this.selectedDate.end).toISOString()
        }

        let stats = await axios.post(path, {
          location_id: this.locationId,
          pagination: {
            pageNumber: this.currentPage,
            pageSize: this.itemPerPage,
          },
          ...filterParams,
        })

        this.records = stats.data.page.pageRecords
        this.rows = stats.data.queryTotalCount

        //fetching new contacts
        if (!this.records || !this.records.length) {
          this.isLoading = false
          return
        }

        let tempContactId = [...this.contactIds]
        this.records.forEach(element => {
          if (!(element.contactId in this.contacts))
            tempContactId.push(element.contactId)
        })

        this.contactIds = [...new Set(tempContactId)]
        let contactsToFetch = this.contactIds.filter(element => {
          return !(element in this.contacts)
        })

        if (contactsToFetch.length === 0) {
          this.isLoading = false
          return
        }

        let contactsData = await axios.post(
          `${this.baseUrl}/getContactDetails`,
          {
            locationId: this.locationId,
            contactIds: contactsToFetch,
          }
        )

        contactsData.data.hits.forEach(element => {
          this.$set(this.contacts, element.tie_breaker_id, element)
        })

        this.isLoading = false
      } catch (err) {
        console.log(err)
        this.isLoading = false
      }
    },
    async fetchAllData() {
      try {
        let filterParams: { [key: string]: any } = {}
        if (this.activeTab === 'delivered') filterParams['showDelivered'] = true
        else if (this.activeTab === 'clicked')
          filterParams['showClicked'] = true
        else if (this.activeTab === 'failed') filterParams['showFailed'] = true

        let path = `${this.emailReportingUrl}/agent_reporting/sms_stats_report`
        if (this.bulk_req_id) {
          path = `${path}/bulk_request`
          filterParams.bulk_req_id = this.bulk_req_id
        } else if (this.campaign_id && this.campaign_step_id) {
          filterParams.campaign_id = this.campaign_id
          filterParams.campaign_step_id = this.campaign_step_id
          filterParams.from_date = new Date(
            this.selectedDate.start
          ).toISOString()
          filterParams.to_date = new Date(this.selectedDate.end).toISOString()
        }
        let stats = await axios.post(path, {
          location_id: this.locationId,
          pagination: {
            pageNumber: this.currentPage,
            pageSize: 10000000000, //making sure we get all the records
          },
          ...filterParams,
        })

        let records = stats.data.page.pageRecords

        //fetching new contacts
        if (records.length === 0) {
          return []
        }

        let tempContactId = [...this.contactIds]
        records.forEach(element => {
          if (!(element.contactId in this.contacts))
            tempContactId.push(element.contactId)
        })

        let contactIds = [...new Set(tempContactId)]
        let contactsToFetch = contactIds.filter(element => {
          return !(element in this.contacts)
        })

        let contactObj = { ...this.contacts }
        let loopOnContacts = 1000
        for (let i = 0; i < contactsToFetch.length; i += loopOnContacts) {
          let contactsData
          if (i + loopOnContacts <= contactsToFetch.length) {
            contactsData = await axios.post(
              `${this.baseUrl}/getContactDetails`,
              {
                locationId: this.locationId,
                contactIds: contactsToFetch.slice(i, i + loopOnContacts),
              }
            )
          } else {
            contactsData = await axios.post(
              `${this.baseUrl}/getContactDetails`,
              {
                locationId: this.locationId,
                contactIds: contactsToFetch.slice(i, contactsToFetch.length),
              }
            )
          }
          contactsData.data.hits.forEach(element => {
            contactObj[element.tie_breaker_id] = element
          })
        }

        contactObj = JSON.parse(JSON.stringify(contactObj))
        const startStr =
          this.selectedDate && this.selectedDate.start
            ? `_from_${moment(this.selectedDate.start).format('MMM Do YYYY')}`
            : ''
        const endStr =
          this.selectedDate && this.selectedDate.end
            ? `_to_${moment(this.selectedDate.end).format('MMM Do YYYY')}`
            : ''
        this.fileName = `${this.modalTitle || 'sms_stats'}_${
          this.activeTab || ''
        }${startStr || ''}${endStr || ''}`

        return this.getFinalObject(records, contactObj, true)
      } catch (err) {
        console.log('Error while importing stats', err)
        return []
      }
    },
    async exportAll() {
      console.log('exportAll')
      this.exportItems = await this.fetchAllData()
    },
    exportCompleted() {
      this.exportItems = []
    },
  },
  mounted() {
    this.fetchingTableData()
  },
  beforeDestroy() {},
})
</script>
<style lang="scss">
.igs {
  min-height: 400px;
  max-height: 400px;
  overflow: auto;
}
.smsServiceStatsView {
  padding: 4px 12px;
  .tabs {
    height: inherit;

    .tab-content {
      height: inherit;

      .tab-pane {
        height: inherit;
        padding-top: 0 !important;

        .hl-table {
          height: inherit;

          .table-responsive {
            height: 90%;

            table thead tr th {
              position: sticky;
              top: 0;
            }

            .pagination-component {
              left: 0;
              right: 0;
              bottom: 30px;
            }
          }
        }
      }
    }
  }

.pagination-component {
  position:unset !important;
  left: 0;
  right: 0;
  bottom: 30px;
}
}

.disabled-tab {
  cursor: regular;
}

.hl-table-button {
  display: flex;
  justify-content: center;
  background-color: rgb(255, 255, 255);
  box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px,
    rgba(0, 0, 0, 0.12) 0px 1px 1px 0px, rgba(60, 66, 87, 0.16) 0px 0px 0px 1px,
    rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0) 0px 0px 0px 0px,
    rgba(60, 66, 87, 0.08) 0px 2px 5px 0px;
  padding: 0 10px;
  color: rgb(60, 66, 87);
  margin-bottom: 0;
  &:hover,
  &:focus {
    box-shadow: rgba(0, 0, 0, 0) 0px 0px 0px 0px,
      rgba(0, 0, 0, 0) 0px 0px 0px 0px, rgba(0, 0, 0, 0.12) 0px 1px 1px 0px,
      rgba(60, 66, 87, 0.16) 0px 0px 0px 1px, rgba(0, 0, 0, 0) 0px 0px 0px 0px,
      rgba(60, 66, 87, 0.08) 0px 0px 0px 0px,
      rgba(60, 66, 87, 0.08) 0px 2px 5px 0px;
    color: rgb(60, 66, 87);
  }
  height: 35px;
  min-width: 25px;

  &.opened {
    color: var(--button-icon-color);
    background: var(--custom-primary);
    border-color: var(--custom-primary);
  }
}

</style>
