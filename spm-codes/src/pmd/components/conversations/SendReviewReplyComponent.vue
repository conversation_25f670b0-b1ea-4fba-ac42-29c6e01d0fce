<template>
  <div class="tab-pane fade show active" id="sms" role="tabpanel" aria-labelledby="sms-tab">
    <div
      class="message-box"
      @drop="handleDrag"
      @dragover="handleDragover"
      @dragleave="handleDragleave"
      :class="{focused:isFocus, dragoverd: isDrogover}"
    >
      <textarea
        v-model="msg"
        class="form-control"
        rows="4"
        placeholder="Type a message"
        @keydown.enter.exact.prevent
        @keyup.enter.exact="send"
        @keydown.enter.shift.exact="msg+='\n'"
      ></textarea>

      <div v-if="errorMsg" class="--red" style="text-align: center;">{{errorMsg}}</div>
      <div class="message-action-bottom">
        <div class="email-action-bottom-right">

          <div style="display: inline-block;position: relative;">
            <button
              @click.prevent="send"
              type="submit"
              class="btn btn-blue"
              :class="{invisible: sending}"
              :disabled="sending"
            >
              <i class="icon-send"></i>
              <span>Send</span>
            </button>
            <div
              style="position: absolute;left: 50%;transform: translate(-50%, -50%);top: 50%;"
              v-show="sending"
            >
              <moon-loader :loading="sending" color="#188bf6" size="24px" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import ImageTools from '../../../util/image_tools'
import { v4 as uuid } from 'uuid'
import { MessageTemplate } from '@/models/campaign'
import 'emoji-mart-vue-fast/css/emoji-mart.css'
//  import { Picker } from 'emoji-mart-vue-fast'
import {
  Message,
  Conversation,
  MessageType,
  MessageContentType,
  Template,
  User,
  SMSTemplate
} from '../../../models'
import { mapState } from 'vuex'
import { UserState, FileAttachment, CompanyState } from '@/store/state_models'
import * as lodash from 'lodash'
import firebase from 'firebase/app'
import { EventBus } from '@/models/event-bus'
const path = require('path');

const imageTools = new ImageTools()

export default Vue.extend({
  props: ['conversation'],
  watch: {
    conversation: function(newConv, oldConv) {
      if (newConv && newConv.id && (!oldConv || oldConv.id !== newConv.id)) {
        const data: () => object = <(() => object)>this.$options.data
        if (data) Object.assign(this.$data, data.apply(this))
      }
    },
    msg(val: string, before: string) {
      if (this.firstLetter && val && val.length - before.length === 1) {
        this.firstLetter = false
        this.msg = val.charAt(0).toUpperCase() + val.slice(1)
      }
    }
  },
  data() {
    return {
      errorMsg: '',
      showTemplateModal: false,
      attachments: [] as string[],
      filesAdded: [] as FileAttachment[],
      msg: '',
      firstLetter: true,
      showEmojis: false,
      isFocus: false,
      isDrogover: false,
      sending: false
    }
  },
  computed: {
    allAttachments(): string[] {
      if (this.filesAdded.length === 0) return this.attachments
      return this.filesAdded.map(f => f.url).concat(this.attachments)
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    })
  },
  methods: {
    pickTemplate() {
      EventBus.$emit('select_sms_template')
    },
    send: async function() {
      this.errorMsg = ''
      if (this.sending) return
      if (!this.conversation) return

      if (!this.conversation.reviewId) {
        this.errorMsg = 'The conversation does not have a review Id.'
        return
      }

      if (
        this.msg.trim().length == 0
      )
        return
      this.sending = true
      const urls = [] as string[]
      try {
          let messageToSend = this.msg
          const data = {
            locationId: this.conversation.locationId,
            type: MessageType.TYPE_REVIEW,
            message: messageToSend,
            conversationId: this.conversation.id
          }
          let response = await this.$http.post('/message/sendreviewreply', data)
          this.$emit('messageSent')
          this.msg = ''
          this.firstLetter = true
        } catch (err) {
          this.$uxMessage('error', `An error has occured. Error message: ${err.response && err.response.data.msg ? err.response.data.msg : err.message && err.message.error ? err.message.error.message : err.message}`)
          console.error(err)
        } finally {
          this.sending = false
        }
    },
    handleDrag(e: any) {
      var fileList = e.dataTransfer.files
      if (fileList && fileList.length > 0) {
        this.vfileAdded(fileList[0])
      }
      this.isDrogover = false
      e.preventDefault()
    },
    handleDragover(e: any) {
      this.isDrogover = true
      e.preventDefault()
    },
    handleDragleave(e: any) {
      this.isDrogover = false
      e.preventDefault()
    }
  }
})
</script>

<style scoped>
  textarea.form-control {
    padding-right: 35px;
  }
  .hl_conversations--message-body .message-box .message-action-top, .dropdown.my-2, .file-open, a.emoji-open {
    display: block;
  }
  .message-action-top {
    top: 5px;
    right: 20px !important;
  }
  .message-action-top > div, .message-action-top > a {
    margin: 3px 0px 0px 0px !important;
  }
  .message-action-top > a {
    margin-left: -2px !important;
  }
</style>
