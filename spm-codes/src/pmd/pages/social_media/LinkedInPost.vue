<template>
    <div class="col-md-6">
        <div class="hl_social--post-item card">
            <div class="card-header">
                <div class="avatar">
                    <div class="avatar_img">
                        <img :src="picture" alt="Avatar Name">
                        <span class="icon --facebook">
                            <i class="fab fa-linkedin"></i>
                        </span>
                    </div>
                    <div class="avatar_text">
                        <h4>
                            <strong>{{name}}</strong> via
                            <span>LinkedIn</span>
                        </h4>
                        <p>{{postDate}}
                            <span>{{postTime}}</span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <p>{{post.updateContent.companyStatusUpdate.share.comment}}
                    <br/>
                    <img style="width:400px;" :src="postPicture">
                </p>
                <div class="hl_social--post-item-details">
                    <ul class="list-inline">
                        <!-- <li class="list-inline-item">
                            <i class="fas fa-retweet"></i>{{post.retweet_count}}</li> -->
                        <li class="list-inline-item">
                            <i class="fas fa-thumbs-up"></i>{{post.numLikes}}</li>
                        <!-- <li class="list-inline-item">
                            <i class="fas fa-retweet"></i>
                            </i> 000</li> -->
                    </ul>
                    <a href="#">Details</a>
                </div>
            </div>
            <!-- <div class="card-footer">
                <div class="hl_social--post-footer-item">
                    <h4>Potential Reach</h4>
                    <p>{{postImpressions}}</p>
                </div>
                <div class="hl_social--post-footer-item">
                    <h4>Engagement</h4>
                    <p>{{postEngagement}}</p>
                </div>
                <div class="hl_social--post-footer-item">
                    <h4>Stories Created</h4>
                    <p>{{postShares}}</p>
                </div>
            </div> -->
        </div>
    </div>
</template>

<script lang="ts">
import Vue from 'vue';
import { Company, Location, User, Notification, NotificationType, OAuth2 } from '@/models';
import moment from 'moment-timezone';

export default Vue.extend({
    props: ['post', 'name', 'picture'],
    data() {
        return {
            currentLocationId: '',
            siteData: {}
        };
    },
    watch: {
        '$route.params.location_id': function (id) {
            this.currentLocationId = id;
        },
    },
    computed: {
        // postImpressions(): number {
        //     if (this.postInsights && this.postInsights.length > 0) {
        //         let postImp = lodash.find(this.postInsights, { name: 'post_impressions' })
        //         if (postImp && postImp.values && postImp.values.length > 0) {
        //             return postImp.values[0].value
        //         } else {
        //             return 0
        //         }
        //     } else {
        //         return 0
        //     }
        // },
        // postEngagement(): number {
        //     if (this.postInsights && this.postInsights.length > 0) {
        //         let postImp = lodash.find(this.postInsights, { name: 'post_engaged_users' })
        //         if (postImp && postImp.values && postImp.values.length > 0) {
        //             return postImp.values[0].value
        //         } else {
        //             return 0
        //         }
        //     } else {
        //         return 0
        //     }
        // },
        // postShares(): number {
        //     if (this.postStats && this.postStats.shares) {
        //         return this.postStats.shares.count;
        //     } else {
        //         return 0
        //     }
        // },
        postPicture(): string | undefined {
            if (this.post && this.post.updateContent && this.post.updateContent.companyStatusUpdate && this.post.updateContent.companyStatusUpdate.share.content.thumbnailUrl) {
                return this.post.updateContent.companyStatusUpdate.share.content.submittedImageUrl
            } else if (this.siteData && this.siteData.twitter && this.siteData.twitter.twitterImage && this.siteData.twitter.twitterImage.length > 0) {
                return this.siteData.twitter.twitterImage[0].url;

            }
        },
        postDate(): string {
            if (this.post) {
                return moment(this.post.created_at).format("DD MMM YYYY")
            }
        },
        postTime(): string {
            if (this.post) {
                return moment(this.post.created_at).format("hh:mm A")
            }
        }

    },
    async created() {
        var _self = this;
        _self.currentLocationId = _self.$router.currentRoute.params.location_id;

        if (this.post.text && this.post && this.post.entities && !this.post.entities.media) {
            var matches = this.post.text.match(/\bhttps?:\/\/\S+/gi);
            if (matches.length > 0) {
                let resp = await this.$http.get('/api/socialmedia/get_site_data?url=' + matches[0]);
                if (resp) {
                    _self.siteData = resp.data;
                }
            }
        }
    },
});
</script>

    

