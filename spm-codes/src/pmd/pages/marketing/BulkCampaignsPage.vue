<template>
  <section class="hl_wrapper">
    <section
      class="hl_marketing--inner hl_wrapper--inner hl_marketing"
      id="marketing"
    >
      <div class="hl_marketing--header">
        <div class="container-fluid">
          <div class="hl_marketing--header-inner">
            <h2>Bulk Requests</h2>
            <!-- <a
							href="javascript:void(0);"
							class="btn btn-success"
							@click.prevent="bulkModal={visible: true}"
						>Create Bulk Request</a> -->
          </div>
          <!-- <ul class="hl_marketing--nav">
						<li class="active">
							<a href="javascript:void(0);">My Campaigns</a>
						</li>
					</ul>-->
        </div>
      </div>

      <div class="container-fluid">
        <!-- <div class="hl_marketing--heading">
					<div class="hl_marketing--heading-text">
						<h3>Get More Customers</h3>
						<p>Customer acquisition campaigns help you to produce more "hot" sales leads who are ready to engage.</p>
					</div>
				</div>-->
        <div class="hl_customer-acquisition">
          <!-- <div class="card-group --wide-gutter">
                        <div class="card">
                            <div class="card-header">
                                <h2>Total Accounts on Campaigns</h2>
                            </div>
                            <div class="card-body">
                                <h3><i class="icon icon-inbox"></i> 62</h3>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header">
                                <h2>Average Open Rate</h2>
                            </div>
                            <div class="card-body">
                                <h3><i class="icon icon-mail"></i> 43.8%</h3>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header">
                                <h2>Average Click Through Rate</h2>
                            </div>
                            <div class="card-body">
                                <h3><i class="icon icon-link"></i> 32.2%</h3>
                            </div>
                        </div>
					</div>-->
          <div class="row">
            <!-- <div class="col-lg-3">
                            <div class="hl_customer-acquisition--filter">

                                <div class="card">
                                    <div class="card">
                                        <div class="card-header">
                                            <h3>Filter</h3>
                                            <ul>
                                                <li>
                                                    <a href="#">Select All</a>
                                                </li>
                                                <li>
                                                    <a href="#">Clear All</a>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-group">
                                                <label>Campaign Name</label>
                                                <input type="text" class="form-control">
                                            </div>
                                            <div class="form-group">
                                                <label>Market</label>
                                                <select class="selectpicker">
                                                    <option>All Market</option>
                                                    <option>Option 2</option>
                                                    <option>Option 3</option>
                                                    <option>Option 4</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label>Status</label>
                                                <div class="status-item">
                                                    <p>Draft</p>
                                                    <div class="option">
                                                        <input type="checkbox" id="status-draft">
                                                        <label for="status-draft"></label>
                                                    </div>
                                                </div>
                                                <div class="status-item">
                                                    <p>Published</p>
                                                    <div class="option">
                                                        <input type="checkbox" id="status-published">
                                                        <label for="status-published"></label>
                                                    </div>
                                                </div>
                                                <div class="status-item">
                                                    <p>Archived</p>
                                                    <div class="option">
                                                        <input type="checkbox" id="status-archived">
                                                        <label for="status-archived"></label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label>State</label>
                                                <div class="state-item">
                                                    <p>Active</p>
                                                    <div class="option">
                                                        <input type="checkbox" id="state-active">
                                                        <label for="state-active"></label>
                                                    </div>
                                                </div>
                                                <div class="state-item">
                                                    <p>Idle</p>
                                                    <div class="option">
                                                        <input type="checkbox" id="state-idle">
                                                        <label for="state-idle"></label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
						</div>-->
            <div class="col-lg-12">
              <div class="card hl_customer-acquisition--table">
                <div class="card-body --no-padding">
                  <div class="table-wrap">
                    <table class="table table-sort">
                      <thead>
                        <tr>
                          <th>Name</th>
                          <th>Contact Type</th>
                          <th>Tags</th>
                          <th>
                            Scheduled Date
                            <span
                              style="margin-left: 5px"
                              class="input-group-addon"
                              v-b-tooltip.hover
                              :title="`This date is based on this company's timezone, which is ${timezone}`"
                            >
                              <i class="fas fa-question-circle"></i>
                            </span>
                          </th>
                          <th>Next execution</th>
                          <th>Status</th>
                          <th>
                            <!--Actions-->
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <BulkListItem
                          v-for="bulkRequest in bulkRequests"
                          :bulkRequest="bulkRequest"
                          :key="bulkRequest.id"
                          :timezone="timezone"
                          @edit="edit"
                        />
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- END of .hl_marketing -->
    <BulkCampaignModal
      :values="bulkModal"
      @hidden="bulkModal = { visible: false }"
      @edit="
        bulkRequest => (bulkModal = { visible: true, bulkRequest: bulkRequest })
      "
      :timezone="timezone"
    />
  </section>
  <!-- END of .hl_wrapper -->
</template>

<script lang="ts">
import Vue from 'vue'
import { BulkRequest, Location } from '../../../models/'
const BulkListItem = () =>
  import('../../components/marketing/BulkListItem.vue').then(m => m.default)
const BulkCampaignModal = () =>
  import('../../components/marketing/BulkCampaignModal.vue').then(
    m => m.default
  )

let unsubscribeBulkRequests: () => void

export default Vue.extend({
  components: {
    BulkListItem,
    BulkCampaignModal,
  },
  data() {
    return {
      currentLocationId: '',
      bulkRequests: [] as BulkRequest[],
      bulkModal: {
        visible: false,
        bulkRequest: undefined as BulkRequest | undefined,
      },
      timezone: 'UTC',
      foundBulkRequestFromQuery: false,
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.fetchData()
    },
    bulkRequests(val) {
      if (
        val &&
        val.length > 0 &&
        this.$route.query.bulk_request_id &&
        !this.foundBulkRequestFromQuery
      ) {
        console.log('trying to get it')
        const selectedBulkRequest = this.bulkRequests.find(
          bulk => bulk.id === this.$route.query.bulk_request_id
        )
        if (selectedBulkRequest) {
          this.foundBulkRequestFromQuery = true
          this.edit(selectedBulkRequest)
        }
      }
    },
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchData()
  },
  methods: {
    async fetchData() {
      if (unsubscribeBulkRequests) unsubscribeBulkRequests()
      unsubscribeBulkRequests = BulkRequest.getWithLocationIdRealtime(
        this.currentLocationId
      ).onSnapshot(snapshot => {
        this.bulkRequests = snapshot.docs.map(d => new BulkRequest(d))
      })
      const location = new Location(
        await this.$store.dispatch('locations/getById', this.currentLocationId)
      )
      this.timezone = await location.getTimeZone()
    },
    edit(bulkRequest: BulkRequest) {
      this.bulkModal = {
        visible: true,
        bulkRequest: bulkRequest,
      }
    },
  },
  beforeDestroy() {
    if (unsubscribeBulkRequests) unsubscribeBulkRequests()
  },
})
</script>
