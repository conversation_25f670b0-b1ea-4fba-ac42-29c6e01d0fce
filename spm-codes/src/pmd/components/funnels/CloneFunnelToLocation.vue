<template>
  <div class="modal fade hl_add_domain--modal" tabindex="-1" role="dialog" ref="modal">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
						<h2 class="modal-title">Clone {{ entityName }}</h2>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div class="form-group">
              <UITextLabel for="funnelName">{{ entityName }} Name</UITextLabel>
              <UITextInputGroup name="funnelName" v-model="newFunnelName" type="text" :placeholder="`${entityName} Name`" />
            </div>
            <div class="form-group">
              <UITextLabel for="locations">Locations</UITextLabel>

              <UILocationPicker
              	v-model="selectedLocations"
                id="location-picker-funnel-clone"
                :multiple="true"
                :max-selectable-locations="maxSelectableLocations"
               />
              <span class="mt-2">Select upto {{ maxSelectableLocations }} locations</span>
              <div v-show="inputDisabled" class="alert alert-primary text-primary mt-2" role="alert">
                <i class="fa fa-info-circle mr-2"></i>
                <span>Cloning is limited to 3 locations at a time</span>
              </div>
            </div>
            <div v-if="errorInCloning" class="error-message">
              <strong>Error while cloning funnel!</strong>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner">
						<UIButton type="button" use="outline" data-dismiss="modal">Cancel</UIButton>
            <UIButton :disabled="submitDisabled" @click="cloneFunnel()" type="button">
              {{ cloning ? 'Cloning' : `Clone ${entityName}` }}
            </UIButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import axios from 'axios'
import vSelect from 'vue-select'

export default Vue.extend({
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    funnelId: {
      type: String
    },
    funnelName: {
      type: String
    },
    entityName: {
      type: String,
      default: 'Funnel'
    }
  },
  components: {
    vSelect
  },
  computed: {
    submitDisabled(): boolean {
      return this.cloning || !this.newFunnelName || !this.selectedLocations.length
    },
    inputDisabled(): boolean{
      return this.selectedLocations.length >= this.maxSelectableLocations
    }
  },
  data() {
    return {
      newFunnelName: '',
      selectedLocations: [],
      cloning: false,
      errorInCloning: false,
      maxSelectableLocations: 3
    }
  },
  mounted() {
		$(this.$refs.modal).on('hidden.bs.modal',  () => {
			this.$emit('hide');
      this.selectedLocations = [];
    });
  },
  beforeDestroy() {
		$(this.$refs.modal).off('hidden.bs.modal');
  },
  methods: {
    cloneFunnel: async function() {
      try {
        const locationIds = this.selectedLocations.map((l: any) => l.id)

        this.errorInCloning = false
        this.cloning = true

        await axios.post('/funnelbuilder/clone_funnel_to_locations', {
          funnelId: this.funnelId,
          funnelName: this.newFunnelName,
          locationIds
        })

        this.$emit('cloneSuccessful', { locationIds })
      } catch (error) {
        console.error('error while cloning funnel --> ', error)
        this.errorInCloning = true
      } finally {
        this.cloning = false
      }
    }
  },
  watch: {
    showModal(newValue, oldValue) {
      if (newValue && !oldValue) {
        // show the modal
        // reset form
        this.cloning = false
        this.errorInCloning = false
        this.newFunnelName = `${this.funnelName || `Unnamed ${this.entityName}`} Copy`
        this.selectedLocations = []
        $(this.$refs.modal).modal('show');
      } else if(!newValue && oldValue) {
        // hide the modal
        $(this.$refs.modal).modal('hide');
      }
    }
  }
})
</script>
