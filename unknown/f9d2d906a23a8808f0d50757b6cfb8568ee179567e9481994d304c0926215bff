<template>
  <div>
    <div
      class="relative border rounded-md px-3 py-2 shadow-sm focus-within:ring-1"
      :class="
        invalid
          ? 'border-red-300 focus-within:ring-red-600 focus-within:border-red-600'
          : 'border-gray-300 focus-within:ring-blue-600 focus-within:border-blue-600'
      "
    >
      <label
        :for="id"
        class="absolute -top-2 left-2 -mt-px inline-block px-1 bg-white text-xs font-medium"
        :class="invalid ? 'text-red-900' : 'text-gray-900'"
        >{{ label }}</label
      >
      <input
        type="text"
        name="name"
        :value="value"
        :id="id"
        class="block w-full border-0 p-0 focus:ring-0 sm:text-sm"
        :class="
          invalid
            ? 'text-red-900 placeholder-red-300'
            : 'text-gray-900 placeholder-gray-500'
        "
        :placeholder="placeholder"
        @input="$emit('input', $event.target.value)"
      />
    </div>
    <p
      class="mt-1 text-sm text-red-600"
      id="email-error"
      v-if="error_messsage && invalid"
    >
      {{ error_messsage }}
    </p>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: 'text',
    },
    label: {
      type: String,
      default: '',
    },
    invalid: {
      type: Boolean,
      default: false,
    },
    error_messsage: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      id: Date.now().toString(),
    }
  },
}
</script>
