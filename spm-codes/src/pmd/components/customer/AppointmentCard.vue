<template>
  <div class="hl_tasks--note hl_tasks--appointment-detail mb-2">
    <div class="hl_tasks--item-header">
      <h4>
        {{
          calendarEventClone.startTime
            .tz(timezone)
            .format(getCountryDateFormat('extended-normal'))
        }}
      </h4>
      <!-- <p>Status: <strong>{{calendarEventClone.appoinmentStatus}}</strong></p> -->
    </div>
    <div class="hl_tasks--item-footer">
      <div class="hl_tasks--item-footer-info flex-wrap">
        <p>
          Status:
          <strong>{{
            calendarEventClone.appoinmentStatus | capitalize
          }}</strong>
        </p>
        <template v-if="calendar && calendar.name">
          <p>
            Calendar:
            <strong>{{ calendar.name | truncate(30, '...') }}</strong>
          </p>
          <p v-if="calendarEventClone.assignedUserId" style="flex-basis: 100%">
            Assigned User:
            <strong>{{
              userIdwiseLocationUsers[calendarEventClone.assignedUserId] &&
                userIdwiseLocationUsers[calendarEventClone.assignedUserId].name
            }}</strong>
          </p>
        </template>
      </div>

      <div class="hl_tasks--item-footer-trash mr-2">
        <i class="icon icon-edit --light" @click.prevent="editAppointment"></i>
      </div>

      <div class="hl_tasks--item-footer-trash">
        <i
          v-if="calendarEventClone.appoinmentStatus === 'confirmed'"
          class="icon icon-trash --light"
          @click.prevent="cancelAppointment"
        ></i>
        <i
          v-else
          class="icon icon-ok --light"
          @click.prevent="confirmAppointment"
        ></i>
      </div>
    </div>
    <p v-if="calendarEventClone.notes">
      Note:
      <strong>{{ calendarEventClone.notes }}</strong>
    </p>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import {
  AppointmentRequest,
  CalendarEvent,
  Calendar,
  Location,
  getCountryDateFormat,
  User,
} from '@/models/'

let cancelUsersSubscription: () => void

export default Vue.extend({
  props: ['calenderEvent', 'calendars', 'locationId', 'timezone', 'contactId'],

  data() {
    return {
      getCountryDateFormat,
      locationUsers: [] as User[],
      calendarEventClone: this.calenderEvent,
    }
  },
  computed: {
    calendar(): Calendar | undefined {
      if (!this.calendars) return
      return lodash.find(this.calendars, {
        id: this.calendarEventClone.calendarId,
      })
    },
    userIdwiseLocationUsers() {
      const _userIdwiseLocationUsers: { [key: string]: any } = {}
      this.locationUsers.forEach(x => {
        _userIdwiseLocationUsers[x.id] = x
      })
      return _userIdwiseLocationUsers
    },
  },
  methods: {
    async cancelAppointment() {
      if (confirm('Are you sure you want to cancel this appointment?')) {
          await this.calendarEventClone.cancel();
      }
    },

    editAppointment() {
      this.$root.$emit('edit-appointment-modal', this.calendarEventClone.id)
      this.$root.$on('appointment-edited', event => {
        this.calendarEventClone = event
      })
    },
    async confirmAppointment() {
      if (confirm('Are you sure you want to confirm this appointment?')) {
        await this.calendarEventClone.confirm();
      }
    },
    async fetchData() {
      if (this.locationId) {
        cancelUsersSubscription = (
          await User.fetchAllLocationUsers(this.locationId)
        ).onSnapshot(snapshot => {
          this.locationUsers = snapshot.docs.map(d => new User(d))
        })
      }
    },
  },
  async mounted() {
    await this.fetchData()
  },
  beforeDestroy() {
    if (cancelUsersSubscription) cancelUsersSubscription()
  },
  filters: {
    capitalize: function(value) {
      if (!value) return ''
      value = value.toString()
      return value.charAt(0).toUpperCase() + value.slice(1)
    },
  },
})
</script>

<style lang="scss">
.hl_tasks--item-footer-info {
  width: 96%;
  display: flex;
}

.hl_tasks--item-footer-trash {
  width: 4%;
  cursor: pointer;
}
.hl_tasks--appointment-detail {
  box-sizing: border-box;
  border-radius: 3px;
  padding: 7px 60px 0px 10px;
  background: #ffffff;
  border: 1px solid #ebebeb;
}
</style>
