<template>
  <div id="yext_pitch">
    <div class="card">
      <div class="card-body">
        <div class="pitch_image_holder">
          <img
            class="pitch_image"
            src="https://storage.googleapis.com/msgsndr/yeOeXR0Ui1V3VeB1cOOm/media/yext_promo_image-min.jpg"
            alt=""
            srcset=""
          />
        </div>
        <div class="yext_call_to_action">
          <button
            class="btn btn-success hl_calltoaction-btn"
            @click="callToActionClicked"
            id="yext_get_started"
          >
            <label class="main"> Get Started</label>
            <label class="sub" v-if="salePrice"> ${{ salePrice }}/Month</label>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'

export default Vue.extend({
  props: {
    location: {
      type: Object,
    },
    company: {
      type: Object,
    },
  },
  methods: {
    callToActionClicked: function () {
      this.$emit('callToActionClicked')
    },
  },
  computed: {
    salePrice: function () {
      if (this.location && this.location.yextReseller.location_price) {
        return this.location.yextReseller.location_price
      } else if (this.company && this.company.yextReseller.agency_price) {
        return this.company.yextReseller.agency_price
      }
    },
  },
})
</script>
<style lang="scss" scoped>
#yext_pitch {
  padding-top: 10px;
  display: grid;
  justify-content: center;
  align-items: center;
  position: sticky;
  top: 4vh;
  left: 0;
  z-index: 10;
  margin-top: -74vh;

  .card {
    border: 1px solid #e1e1e1;
    box-shadow: 0 2px 4px 0px #b1b1b13b;

    .card-body {
      .pitch_image_holder {
        position: relative;
        max-width: 60vw;
        max-height: 60vh;
        overflow: hidden;

        position: relative;
        .pitch_image {
        }

        /*         .company_logo_holder {
          width: 100px;
          height: 100px;
          position: absolute;
          top: 50%;
          left: 49%;
          .company_logo {
            width: 100px;
            height: 100px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
          }
        } */
      }
      /*  .header {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding-bottom: 20px;
        label {
          color: #ffbc00;
          font-size: 1.5rem;
          margin: 0;
        }
        h3 {
          color: #0c2d3f;
          font-weight: 500;
          margin: 0;
        }
      } */
      /*      .partition {
        display: grid;
        grid-template-columns: auto auto;
        grid-gap: 20px;
        .key_points {
          min-width: 290px;
          ul,
          li {
            margin: 0;
            padding: 8px 0;
            list-style: none;
          }
        }
        .image_container {
          position: relative;
          .highlighter {
            position: absolute;
            width: 236px;
            right: 0;
            top: -18px;
            background: #188bf6;
            color: #fff;
            padding: 10px;
            border-radius: 7px;
            box-shadow: -3px 4px 5px 0px #00000026;
          }
          .image_holder {
            background: white;
            padding: 15px;
            box-shadow: -3px 3px 13px 0px #00000026;
            border-radius: 5px;
            max-height: 250px;
            min-height: 220px;
            min-width: 520px;
            height: 300px;
            img {
              height: 100%;
              width: auto;
            }
          }
        }
      } */
      .yext_call_to_action {
        display: flex;
        justify-content: center;
        padding: 8px 0 0 0;
        .hl_calltoaction-btn {
          background: #27ae60;
          border-radius: 10px;
          color: #fff;
          width: 300px;
          padding: 5px 10px;
          * {
            cursor: pointer;
            color: #fff;
            display: block;
            margin: 0;
          }
          .main {
            font-size: 1.2rem;
          }
          .sub {
            font-size: 0.8rem;
            font-weight: 100;
          }
        }
      }
    }
  }
}
</style>
