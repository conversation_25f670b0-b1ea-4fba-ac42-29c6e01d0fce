<template>
	<div>
		<section class="hl_wrapper notification_popup">
			<div class="container">
				<!--SIDE PANEL-->
				<div
					class="notification-list-container ng-scope"
					id="notification-list-container"
					data-ng-init="loadNotifications()"
				>
					<!--LIST-->
					<div class="notification-list-sub-container">
						<div class="ps-loader"></div>

						<div ng-show="!loaders.loadingNotifications" class>
							<!--CONTROLS-->
							<div class="row list-controls mt-3">
								<div class="d-flex w-100" ng-show="notifications.length > 0">
									<div class="col-sm-12 float-right text-right">
										<button class="btn btn-lg new-notificaiton" @click="addNotification()">
											<span class="h6">
												<i class="fa fa-plus mr-3"></i>New Notification
											</span>
										</button>
									</div>
								</div>
							</div>

							<!--LIST CONTAINER-->
							<div class="list-box">
								<table class="table table-hover">
									<thead class="rounded-top">
										<tr>
											<th class="rounded-top text-center" style="width: 5px" scope="col">Status</th>
											<th scope="col">Name</th>
											<th scope="col">Type</th>
											<th scope="col">Launched</th>
											<th class="status-col rounded-top" scope="col">Preview</th>
										</tr>
									</thead>
									<tbody>
										<tr
											v-for="notification in notifications"
											:key="notification.id"
											@click="editNotfication(notification.id);"
										>
											<td class="text-center" scope="row">
												<i class="fa fa-lg fa-circle text-success"/>
											</td>
											<td scope="row" class="notification-name ng-binding">Home page visitors</td>
											<td class="text-muted ng-binding">{{getTypeName(notification)}}</td>
											<td class="text-muted ng-binding">{{notification.dateUpdated.format(getCountryDateFormat('basic-mdy'))}}</td>
											<td class="preview-bubble">
												<PsBubble :notification="notification"/>
											</td>
										</tr>

										<tr class="no-results ng-hide" v-if="notifications.length == 0">
											<td class="text-center p-lg-5" colspan="5">
												<div class="row">
													<div class="col-md-12 lets-start" style="height: 280px;">
														<div class="h5 p-3">
															Start boosting your conversions by creating your first
															notification
														</div>
														<button class="btn btn-lg new-notificaiton" @click="addNotification()">
															<span class="h6">
																<i class="fa fa-plus"/> New Notification
															</span>
														</button>
													</div>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
import { Location, NotificationTemplate, getCountryDateFormat } from '../../models';

import PsBubble from "./notifications/psbubble.vue";

let cancelAppointmentRequestSubscription: () => void;
export default Vue.extend({
	directives: {},
	components: {
		PsBubble
	},
	data() {
		return {
			currentLocationId: '',
			currentStep: 1 as number,
			notifications: [] as NotificationTemplate[],
			getCountryDateFormat
		};
	},
	async created() {
		this.currentLocationId = this.$router.currentRoute.params.location_id;
		const location = new Location(await this.$store.dispatch('locations/getById', this.currentLocationId));
		this.notifications = await NotificationTemplate.getByLocationId(location.id);

		for (var not in this.notifications) {
			console.log(this.notifications[not])
		}
	},
	methods: {
		getStepClass(stepNumber: number) {
			if (this.currentStep == stepNumber) {
				return "steps-item steps-item-first steps-item-active steps-item-done"
			} else {
				return "steps-item"
			}
		},
		showStep(stepNumber: number) {
			this.currentStep = stepNumber;
		},
		editNotfication(id?: string) {
			this.$router.push({ name: 'notifications', params: { notification_id: id } });
		},
		async addNotification() {
			let notification = new NotificationTemplate();
			notification.locationId = this.currentLocationId;
			await notification.save();
			this.$router.push({ name: 'notifications', params: { notification_id: notification.id } });
		},
		getTypeName(notification: NotificationTemplate) {
			if (notification.type == NotificationTemplate.TYPE_STREAM) {
				return "Stream"
			} else if (notification.type == NotificationTemplate.TYPE_VISITORS) {
				return "Live Visitors"
			} else if (notification.type == NotificationTemplate.TYPE_PAGE_VISITS) {
				return "Page Visits"
			} else if (notification.type == NotificationTemplate.TYPE_CONVERSIONS) {
				return "Conversions"
			} else if (notification.type == NotificationTemplate.TYPE_COMBO) {
				return "Combination"
			} else if (notification.type == NotificationTemplate.TYPE_REVIEW) {
				return "Live Reviews"
			}
		}

	},
	watch: {
		'$route.params.location_id': function (id) {
			this.currentLocationId = id;

		},
	},
	updated() {

	}
});
</script>



<style>
@import url(https://fonts.googleapis.com/icon?family=Material+Icons);

.notification_popup a.button-link:hover {
	opacity: 0.9;
}

.notification_popup .footer > span.version {
	float: left;
	margin-left: 22px;
	margin-bottom: -5px;
}

.notification_popup a.button-link {
	padding: 6px 12px;
	background: #00b3ff;
	border-radius: 3px;
	margin-left: 15px;
	font-size: 14px;
	top: -1px;
	position: relative;
	color: white !important;
	text-decoration: none !important;
}

.notification_popup .top-website-notification {
	padding: 10px;
	background: #7825f3;
	color: white;
}

.notification_popup .top-website-notification.danger a.button-link {
	background: #ff912a;
}

.notification_popup .top-website-notification.danger {
	background: #d42a2a;
	margin-top: -1px;
}

#open-launcher-icon {
	left: -1px !important;
}

#launcher-icon {
	height: 60px !important;
	right: 97px !important;
	min-width: 60px !important;
	max-width: 60px !important;
}

#close-launcher-icon {
	top: 0px !important;
	left: 0px !important;
	width: 60px !important;
	height: 60px !important;
}

body {
	border-top-width: 0px;
	height: 100%;
	overflow-y: hidden;
	overflow-x: auto;
}

.notification_popup .pointer-text {
	cursor: text;
}

pre,
code {
	font-family: monospace, monospace;
}

code > .code-content > .ps-string {
	color: #b35e14;
}

code > .code-content > .ps-property {
	color: #1d75b3;
}

code > .code-content > .ps-variable {
	color: #047d65;
}

code > .code-content {
	font-size: 1rem;
	color: #5b5b5b !important;
}

code > span.comment {
	color: #363839;
	font-style: italic;
	font-size: 0.9rem;
	margin-bottom: 5px;
}

pre {
	overflow: auto;
	font-size: 11px;
	margin: 1rem 0 0;
	border-radius: 0.3125rem;
	border: 2px dashed #dfdfdf;
	color: #7a7a7a;
	resize: none;
	background: #fbfbfb;
	outline: 0;
	cursor: pointer;
	position: relative;
	padding: 0.75rem;
	max-height: 14rem;
	word-wrap: break-word;
	white-space: normal;
}

.notification_popup .integrations ps-section .section.section-box.marked {
	background: #fafafa;
}

.notification_popup
	.integrations
	ps-section:nth-child(even)
	> .section.section-box {
	border-right: 1px solid #eee;
}

.notification_popup .only-white-text {
	color: white !important;
}

.notification_popup .ps-section pre:hover {
	border-color: #7625f3;
	opacity: 0.8;
}

.notification_popup .ps-section > .section.section-box {
	width: 50%;
	float: left;
}

.notification_popup .ps-section .no-events {
	padding: 15px;
	text-align: center;
	margin-top: -20px;
	border-radius: 4px;
	margin-bottom: 15px;
	font-size: 1rem;
	font-weight: 600;
}

.notification_popup .no-events.good {
	background-color: #14cd001a;
	color: #0ba802;
}

.notification_popup .no-events.bad {
	background-color: #ff20201a;
	color: #bf0109;
}

pre > code {
	display: block;
	padding: 1rem;
	word-wrap: normal;
}

button.btn-action.red:hover {
	border-color: #ed7676;
	color: #e75252;
	background-color: #ffceda4a;
}

button.btn-action:hover {
	border-color: #7625f3;
	color: #7625f3;
	background-color: rgba(115, 36, 245, 0.08);
}

button.btn-action {
	color: rgba(43, 57, 80, 0.7);
	border-radius: 4px;
	border: 1px solid #929aa4;
	box-shadow: 0 1px 3px 0 #929aa340;
	background-color: white;
}

.notification_popup .mt-15 {
	margin-top: 15%;
}

.notification_popup .mt-25 {
	margin-top: 20%;
}

.notification_popup .overflow-y {
	overflow-y: auto;
}

.notification_popup .fa-spin-fast {
	-webkit-animation: fa-spin 1.5s infinite linear !important;
	animation: fa-spin 1.5s infinite linear !important;
}

.notification_popup .dropdown-toggle::after {
	margin-left: 0.6em;
}

.notification_popup .app-container > .container {
	margin-bottom: 60px;
	scroll-behavior: smooth;
}

.notification_popup .app-container {
	height: 100%;
	max-height: 100% !important;
	overflow-y: auto;
	overflow-x: auto;
	position: relative;
	padding-bottom: 100px;
	scroll-behavior: smooth;
}

.notification_popup .ps-border-top {
	border-top-color: #7925f3 !important;
	border-top-style: solid;
	border-top-width: 1px;
}

.notification_popup .border-green:hover,
.notification_popup .border-green:focus,
.notification_popup .border-green:active {
	border-color: #1ec07f !important;
	color: #565656 !important;
}

.notification_popup .ps-tip div.tip.red {
	border-left: 3px solid #dd0f11;
	background-color: #ff5d5d1c;
	color: #dc1010;
}

.notification_popup .ps-tip div.tip.yellow {
	border-left: 3px solid #eeac10;
	background-color: #ffdc8a40;
	color: #edac10;
}

.notification_popup .ps-tip div.tip.blue {
	border-left: 3px solid #20bccc;
	background-color: #20bccd17;
	color: #547f84;
}

.notification_popup .ps-tip div.tip.green {
	border-left: 3px solid #17bb42;
	background-color: #21cc4d14;
	color: #19bb42;
}

div.tip {
	padding: 7px;
	padding-left: 10px;
	font-size: 0.9rem;
	border-bottom-right-radius: 4px;
	border-top-right-radius: 4px;
}

div.tip a {
	color: #007bff !important;
	cursor: pointer;
	text-decoration: underline !important;
}

.notification_popup .footer {
	position: absolute;
	width: 100%;
	height: 50px;
	line-height: 60px;
	/*background-color: #f6f7f9;*/
	text-align: center;
	color: #cacaca;
	font-size: 0.8rem;
	bottom: 0;
}

.notification_popup .footer > .container {
	padding-right: 15px;
	padding-left: 15px;
}

.notification_popup .no-text-select {
	-webkit-touch-callout: none; /* iOS Safari */
	-webkit-user-select: none; /* Safari */
	-khtml-user-select: none; /* Konqueror HTML */
	-moz-user-select: none; /* Firefox */
	-ms-user-select: none; /* Internet Explorer/Edge */
	user-select: none;
}

.notification_popup .btn.focus,
.notification_popup .btn:focus {
	outline: 0;
	box-shadow: none;
}

@media (min-width: 1200px) {
	.notification_popup .container {
		width: 1100px;
	}
}

.notification_popup .form-control:focus {
	border-color: #7225f3;
	outline: 0;
	box-shadow: none;
	color: #7225f3;
}

.notification_popup .navbar {
	color: #7a25f3 !important;
}

.notification_popup .navbar ul.navbar-nav li.navbar-item {
	/*border-right: 1px solid #eaeaea;*/
	margin-top: -8px;
	margin-bottom: -9px;
	line-height: 40px;
	padding-left: 7px;
	padding-right: 8px;
	text-align: center;
	border-bottom: 2px solid transparent;
	font-size: 0.95rem;
}

.notification_popup .navbar-item.upgrade > .nav-link {
	color: #7a25f3 !important;
}

.notification_popup .navbar-nav .navbar-item:first-child {
	/*border-left: 1px solid #eaeaea;*/
	/*border-right: 1px solid #eaeaea;*/
}

.notification_popup .navbar-container {
	background-color: white;
	border-bottom: 1px solid #eaeaea;
	cursor: pointer;
}

.notification_popup .navbar ul.navbar-nav li.navbar-item.account-item:hover {
	border-bottom: 2px solid transparent !important;
}

li.navbar-item.current-state > .nav-link.menu {
	color: #7a25f3 !important;
}

li.navbar-item.current-state {
	/*background-color: #e9e9e94a;*/
	border-bottom: 2px solid #7a25f3 !important;
}

li.navbar-item.account-item a.dropdown-toggle:hover {
	opacity: 0.8;
}

li.navbar-item.account-item a.dropdown-toggle {
	color: #767884;
}

li.navbar-item.account-item .dropdown-menu {
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
	margin-top: 0;
	padding: 0;
	border: 0;
	border-color: #eaeaea !important;
	border-left: 1px solid;
	border-right: 1px solid;
	border-bottom: 1px solid;
	border-radius: 0;
	background-color: white;
	width: 100%;
	border-top: 1px solid;
	position: absolute;
	transform: translate3d(-30px, 51px, 0px);
	top: 0px;
	left: 0px;
}

li.navbar-item.account-item .dropdown-menu .dropdown-item:hover {
	color: #1d1d1d !important;
	background: #fbfbfb;
	cursor: pointer;
}

li.navbar-item.account-item .dropdown-menu .dropdown-item {
	background-color: white;
	color: #686868 !important;
	border: 1px solid white;
	padding: 0.2rem 1rem;
	font-size: 0.9rem;
}

li.navbar-item.account-item {
	width: auto !important;
	border-right: 0 !important;
}

.notification_popup .navbar a.nav-link.menu {
	color: rgb(154, 160, 172);
}

.notification_popup .navbar ul.navbar-nav li.navbar-item .nav-link > i.fa {
	margin-right: 3px;
}

.notification_popup .navbar a:not(.menu .navbar-brand) {
	cursor: pointer;
	color: #7a25f3;
}

.notification_popup .navbar a:not(.navbar-brand):hover {
	color: #7a25f3;
}

.notification_popup .navbar li.navbar-item:hover {
	/*background-color: #e9e9e94a;*/
	border-bottom: 2px solid #7a25f3 !important;
}

a.navbar-brand:hover {
	/*background-color: #7725f3 !important;*/
	opacity: 0.8;
	color: #7a25f3 !important;
}

.notification_popup .navbar-brand {
	transition: 0.3s opacity;
	font-size: 1.3rem;
	margin-top: -11px;
	height: 56px;
	line-height: 43px;
	color: #7a25f3;
	margin-bottom: -10px;
	width: 160px;
	text-align: center;
	margin-right: 0;
	padding-bottom: 0;
}

.notification_popup .nav-sub {
	border-top: 1px solid #eaeaea;
}

.notification_popup .general-container,
.notification_popup .notification-list-container {
	padding: 0.5rem 0.4rem 5rem;
}

.notification_popup .notification-list-sub-container {
}

.notification_popup .notification-list-sub-container .row.list-controls {
	margin-top: 45px;
	margin-bottom: 30px;
}

.notification_popup .list-controls .search-box > a.clear-value:hover {
	color: #777777;
}

.notification_popup .list-controls .search-box > a.clear-value {
	position: absolute;
	top: 4px;
	right: 19px;
	padding: 10px 10px 8px 10px;
	background-color: #ededed;
	border-radius: 3px;
	color: #a3a3a3;
	text-align: center;
	cursor: pointer;
}

button.new-notificaiton:hover {
	opacity: 0.9;
}

button.new-notificaiton {
	line-height: 2 !important;
	color: #ffffff;
	/*background-color: #1874cf;*/
	background-color: #7825f3;
	padding-left: 20px;
	padding-right: 20px;
	font-size: 1rem;
}

button.new-notificaiton i {
	color: #c49eff;
}

.notification_popup .list-controls input {
	border: 1px solid #dee2e6;
	color: #222;
	font-size: 1rem;
	line-height: 2rem !important;
}

.notification_popup .list-box .title {
	color: #474a4c;
	font-size: 1.2rem;
	font-weight: bold;
}

.notification_popup .list-box {
	border-radius: 5px;
	border: 1px solid #dee2e6;
	box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 15px -5px;
}

.notification_popup .list-box .table.no-notifications thead {
	display: none;
}

.notification_popup .list-box .table tbody tr td.notification-name {
	/*max-width: 310px;*/
}

.notification_popup .list-box .table tbody tr td.preview-bubble {
	padding-right: 0;
}

.notification_popup .list-box .table tbody tr td.preview-bubble .bubble-body {
	transform: scale(0.9);
	margin-right: 0;
}

.notification_popup .list-box .table tbody tr td:not(.preview-bubble) {
	vertical-align: middle;
}

.notification_popup .list-box .table.no-notifications tbody tr,
.notification_popup .list-box .table.no-notifications tbody td {
	border: 0;
	border-radius: 0.25rem;
}

.notification_popup .list-box .table .status-col {
	width: 100px;
	text-align: center;
}

.notification_popup .list-box .table {
	margin-bottom: 0;
}

.notification_popup .list-box .table.table-hover tbody tr:last-child td {
	border-bottom-left-radius: 0.25rem !important;
	border-bottom-right-radius: 0.25rem !important;
}

.notification_popup .list-box .table.table-hover tbody tr {
	background-color: #ffffff;
	box-sizing: border-box;
	font-size: 0.9rem;
}

.notification_popup
	.list-box
	.table.table-hover
	tbody
	tr:not(.no-results):focus,
.notification_popup
	.list-box
	.table.table-hover
	tbody
	tr:not(.no-results):hover {
	background-color: rgb(249, 249, 249);
	cursor: pointer;
	z-index: 2;
	-webkit-box-shadow: inset 3px 0px 0px #7725f3;
	-moz-box-shadow: inset 3px 0px 0px #7725f3;
	box-shadow: inset 3px 0px 0px #7725f3;
}

.notification_popup .list-box .table thead {
	background-color: #ffffff;
	color: #3c3c3c;
	/*border-top: 1px solid #dee2e6;*/
}

.notification_popup .list-box .table td {
	padding: 1rem;
}

.notification_popup .list-box .table th {
	padding: 1rem;
}

.notification_popup .list-box .table thead th {
	border-bottom: 0;
	border-top: 0;
}

.notification_popup .ps-container {
	padding: 60px 0px 100px;
}

.notification_popup .view-frame {
	max-width: 900px;
	margin: 30px auto;
}

span.h4-sub {
	display: block;
	font-size: 0.9rem;
	color: #9a9a9a;
	padding-top: 5px;
}

.notification_popup .ps-body-container.lg {
	min-width: 960px !important;
	max-width: 960px !important;
	margin: 0 auto;
}

.notification_popup .ps-body-container.md-2 {
	min-width: 860px !important;
	max-width: 860px !important;
	margin: 0 auto;
}

.notification_popup .ps-body-container.md {
	min-width: 800px !important;
	max-width: 800px !important;
	margin: 0 auto;
}

.notification_popup .ps-body-container.sm {
	min-width: 650px !important;
	max-width: 650px !important;
	margin: 0 auto;
}

.notification_popup .ps-box .section-collapse {
	color: #575757;
	font-size: 0.875rem;
	width: 85%;
	margin-bottom: 15px;
	margin-top: 15px;
}

.notification_popup .section-collapse strong.step-title {
	display: block;
	text-transform: uppercase;
	font-size: 0.9rem;
	color: #474747;
	margin-bottom: 5px;
}

.notification_popup .ps-box .section.is-open {
	background: #fafafa;
	border-bottom: 1px solid #eee;
	box-shadow: 0 0px 20px 1px #f4f4f4;
}

.notification_popup .ps-box .section img.title-image.lg {
	transform: scale(1.5);
}

.notification_popup .ps-box .section img.title-image {
	width: 25px;
	margin-right: 25px;
	margin-top: -5px;
	border-radius: 3px;
}

.notification_popup .ps-box .section.not-available {
	opacity: 0.4;
	pointer-events: none;
}

.notification_popup .ps-box .section.hover:hover {
	background: #fafafa;
}

.notification_popup .ps-box .section {
	padding: 20px 25px;
	border-top: 1px solid #eee;
	position: relative;
	cursor: pointer;
}

.notification_popup .ps-box .header p {
	margin: 0;
	font-size: 1rem;
	color: rgb(136, 136, 136);
}

.notification_popup .ps-box .header h1 {
	font-weight: 800;
	font-size: 1.7em;
	margin: 0 0 5px;
	line-height: 1.1em;
	letter-spacing: -0.02em;
	margin-bottom: 13px;
}

.notification_popup .ps-box .header.with-bottom-border {
	border-bottom: 1px solid #eee;
}

.notification_popup .ps-box .header {
	padding: 30px 130px;
	position: relative;
	text-align: center;
}

.notification_popup .ps-box.md {
	width: 750px;
	min-width: 750px !important;
	max-width: 750px !important;
}

.notification_popup .ps-box {
	margin: 0 auto 20px;
	background-color: white;
	box-shadow: 0 5px 10px rgba(21, 57, 130, 0.07),
		0 0 0 1px rgba(0, 0, 0, 0.05);
	overflow-x: hidden;
	position: relative;
	z-index: 0;
	border-radius: 5px;
	min-width: 650px !important;
	max-width: 650px !important;
}

.notification_popup .ps-body-container {
	min-width: 800px;
	max-width: 850px;
	margin: 0 auto;
}

.notification_popup .ps-body > .row > .ps-content {
	width: 770px;
	position: relative;
	box-shadow: -1px 0px 0px 0px #e9ebef;
	z-index: 99999;
}

.notification_popup .ps-body > .row {
	margin: 0;
}

.notification_popup .ps-body {
	min-width: 1000px;
	max-width: 1000px;
	margin: 0 auto;
	background-color: white;
	border-radius: 8px;
	box-shadow: 0 5px 10px rgba(21, 57, 130, 0.07),
		0 0 0 1px rgba(0, 0, 0, 0.05);
}

.notification_popup .ps-body .ps-header {
	padding: 20px;
	text-align: center;
	position: relative;
	background-color: #7726f3;
	border-bottom: 1px solid #e2e5e9;
	border-top-left-radius: 8px;
	border-top-right-radius: 8px;
	color: #ffffff;
	font-size: 20px;
	font-weight: bold;
}

.notification_popup .ps-body .ps-wizard-steps {
	color: white;
	position: relative;
	background: #f7f7f7;
	padding: 0 !important;
	width: 230px;
	border-bottom-left-radius: 8px;
}

.notification_popup
	.ps-body
	.step-content
	.step-panel
	.section
	.sub-step-title {
	margin-bottom: 15px;
	color: #8c8c8c;
	font-size: 1rem;
	background-color: #f5f5f5;
	font-weight: 900;
	display: inline-block;
	padding: 5px 10px;
	box-shadow: -3px 3px 0px 0px #e4e4e4;
	border-radius: 3px;
}

.notification_popup .ps-body .step-content {
	padding: 30px;
}

.notification_popup .step-content.type-step .type-disabled {
	opacity: 0.5;
	pointer-events: none;
}

/*.ps-body .step-content.type-step > .step-panel.selected-type > .title:after {*/
/*content: '';*/
/*display: inline-block;*/
/*width: 15px;*/
/*height: 30px;*/
/*border: solid #21bca7;*/
/*border-width: 0 6px 6px 0;*/
/*transform: rotate(45deg);*/
/*float: right;*/
/*margin-right: 5px;*/
/*margin-top: -6px;*/
/*}*/

.notification_popup .ps-body .step-content.type-step > .step-panel > .section {
	padding: 5px 15px 15px 15px;
	border-bottom: 1px dotted #e9eced;
	font-size: 15px;
	color: rgb(136, 136, 136);
	position: relative;
	height: 205px;
}

.notification_popup
	.ps-body
	.step-content.type-step
	.notification-preview-container {
	margin: 44px 20px 18px -15px;
	padding: 0;
	background: none;
	position: absolute;
	border-bottom-right-radius: 5px;
	bottom: 9px;
}

.notification_popup .ps-body .step-content.type-step > .step-panel .title {
	border-bottom: 0 !important;
}

.notification_popup .ps-body .step-content.type-step > .step-panel:last-child {
	margin-bottom: 35px;
}

.notification_popup .ps-body .step-content.type-step .step-panel .section {
	border-bottom: 0;
}

.notification_popup .ps-body .step-content.type-step > .step-panel {
	position: relative;
	background-color: #fff;
	margin: 5px 9px 15px 6px;
	border-radius: 3px;
	border: 1px solid #efefef;
	width: 340px;
	height: 235px;
	float: left;
	box-shadow: none;
}

.notification_popup
	.ps-body
	.step-content.type-step
	> .step-panel.selected-type
	> .title {
	border-bottom: 1px solid #7627f3;
}

.notification_popup
	.ps-body
	.step-content.type-step
	> .step-panel.selected-type {
	border: 1px solid #7627f3 !important;
	/*transform: scale(1.02);*/
}

.notification_popup .ps-body .step-content.type-step > .step-panel:hover {
	/*transform: scale(1.01);*/
	/*transition: -webkit-transform 0.2s ease-in-out, transform 0.2s ease-in-out;*/
	cursor: pointer;
	border-color: #bebfc2;
}

.notification_popup .ps-body .ps-content .step-nav-buttons-container {
	border-bottom-right-radius: 8px;
	padding: 10px;
	background-color: #f3f3f3;

	display: -webkit-box;
	display: -moz-box;
	display: box;
	display: -webkit-flex;
	display: -moz-flex;
	display: -ms-flexbox;
	display: flex;

	-webkit-box-orient: horizontal;
	-moz-box-orient: horizontal;
	box-orient: horizontal;

	-webkit-box-direction: normal;
	-moz-box-direction: normal;
	box-direction: normal;
	-webkit-flex-direction: row;
	-moz-flex-direction: row;
	flex-direction: row;

	-ms-flex-direction: row;
	-webkit-box-pack: start;
	-moz-box-pack: start;
	box-pack: start;

	-webkit-justify-content: flex-start;
	-moz-justify-content: flex-start;
	-ms-justify-content: flex-start;
	-o-justify-content: flex-start;
	justify-content: flex-start;

	-ms-flex-pack: start;
	-webkit-box-align: center;
	-moz-box-align: center;
	box-align: center;
	-webkit-align-items: center;
	-moz-align-items: center;
	-ms-align-items: center;
	-o-align-items: center;
	align-items: center;
	-ms-flex-align: center;
}

.notification_popup .step-nav-buttons-container > .button-container {
	margin-right: 10px;
	-webkit-box-flex: 1;
	-moz-box-flex: 1;
	box-flex: 1;
	-webkit-flex: 1;
	-moz-flex: 1;
	-ms-flex: 1;
	flex: 1;
}

.notification_popup .step-nav-buttons-container .button-container:last-child {
	margin-right: 0;
}

.notification_popup .errors-container .title {
	padding: 10px 12px;
	font-weight: bold;
	font-size: 17px;
	border-bottom: 1px solid #ffdada;
	background-color: #ffffff12;
	color: #d30000;
}

.notification_popup .errors-container {
	background-color: #fff0f0;
	border: 1px solid #ffdada;
	position: relative;
	margin: 0 auto 0;
	border-radius: 5px;
}

.notification_popup .errors-container .error-item:last-child {
	padding-bottom: 10px;
	border-bottom: 0;
}

.notification_popup .errors-container .error-item {
	padding: 10px 10px 10px 15px;
	border-bottom: 1px solid #ffdcdc;
}

.notification_popup .opacity-1 {
	opacity: 1 !important;
}

.notification_popup .opacity-0 {
	opacity: 0 !important;
}

.notification_popup popup {
	z-index: 99999999999999999999999999999999999999999999999999999999999999999;
}

.notification_popup .wizard-btn.copied-code {
	background-color: #00c069;
	border-color: #00c069;
}

.notification_popup .wizard-btn:disabled {
	opacity: 0.3;
}

.notification_popup .wizard-btn {
	outline: none !important;
	padding: 15px 30px;
	text-align: center;
	font-size: 20px;
	background-color: #7826f3;
	color: white;
	font-weight: bold;
	border-radius: 5px;
	border: 4px solid #7726ef;
	cursor: pointer;
}

.notification_popup .cursor-pointer {
	cursor: pointer;
}

.notification_popup .wizard-btn.next:hover {
	opacity: 0.9;
}

.notification_popup .wizard-btn.next i {
	color: #c49eff;
}

.notification_popup .wizard-btn.next[disabled] {
	background-color: #7726ef;
	opacity: 0.4;
	color: #b687fc;
}

.notification_popup .wizard-btn.sm {
	padding: 5px 25px !important;
	width: auto !important;
	font-size: 16px;
}

.notification_popup .wizard-btn.next {
	display: block;
	text-transform: uppercase;
	width: 260px;
	float: right;
}

.notification_popup .wizard-btn.back:hover {
	background-color: #e6e6e6;
}

.notification_popup .shopify,
.notification_popup .shopify a:link,
.notification_popup .shopify a:hover {
	color: #fff;
}

.notification_popup .shopify:hover {
	opacity: 0.8;
	color: white;
}

.notification_popup .shopify img {
	width: 25px;
	margin-top: -7px;
	margin-right: 10px;
}

.notification_popup .shopify div {
	display: inline-block;
}

.notification_popup .shopify .title {
	padding-top: 5px;
	padding-bottom: 5px;
}

.notification_popup .shopify .shopify-icon {
	margin-right: 5px;
	display: inline-block;
	border-right: 1px solid #81a73d;
}

.notification_popup .shopify {
	background-color: #95c047;
	border: 1px solid #88b140;
}

.notification_popup .wizard-btn.back {
	display: block;
	text-transform: uppercase;
	width: 200px;
	float: right;
	background-color: transparent;
	color: #999;
	margin-right: 5px;
	border: 4px solid transparent;
}

/*ol*/
.notification_popup .steps {
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	counter-reset: steps;
	overflow: hidden;
	list-style: none;
	padding: 0;
	margin: 0;
	position: relative;
	display: table;
	width: 100%;
}

.notification_popup .steps-item a,
.notification_popup .steps-item span {
	text-decoration: none;
	color: #939393 !important;
}

.notification_popup .launch-title > h3 {
	margin-bottom: 3px;
	font-size: 2.5rem;
	font-weight: bolder;
	color: #454849;
}

.notification_popup .launch-title img {
	width: 35%;
	margin-top: -37px;
}

.notification_popup .launch-title {
	margin-top: 10px;
	margin-bottom: 40px;
	color: #758b93;
}

.notification_popup .step-launch {
	text-align: center;
	font-size: 1.1em;
	height: 67px;
	line-height: 67px;
	background-color: white;
	color: #606061;
}

.notification_popup .steps-item {
	counter-increment: steps;
	position: relative;
	white-space: nowrap;
	width: auto;
	cursor: pointer;
	background: #f7f7f7;
	-webkit-box-flex: 1;
	-moz-box-flex: 1;
	box-flex: 1;
	-webkit-flex: 1;
	-moz-flex: 1;
	border-bottom: 1px solid #eeeeef;
	-ms-flex: 1;
	flex: 1;
	text-align: center;
}

.notification_popup .steps-item:last-child {
	border-right: 0;
}

.notification_popup .steps-item:not(.steps-item-active) * {
	color: #8a8a8a !important;
	opacity: 0.3;
}

.notification_popup .steps-item:not(.steps-item-active):hover {
	/*opacity: 0.6;*/
}

.notification_popup .steps-item:not(.steps-item-active):after {
	background: #230155;
}

li.steps-item-active:not(:first-child) {
	/*border-top: 1px solid #e2e4e8;*/
}

.notification_popup .steps-item-active {
	background: #ffffff;
	z-index: 99999999;
	position: relative;
	border-bottom: 1px solid #e2e4e8 !important;
}

.notification_popup .steps-item:not(.steps-item-active) *:before {
	background: #909090;
}

/*.steps-item:after {*/
/*width: 35px;*/
/*height: 35px;*/
/*position: absolute;*/
/*top: 7px;*/
/*left: 100%;*/
/*-webkit-transform: rotate(45deg);*/
/*transform: rotate(45deg);*/
/*content: '';*/
/*z-index: 2;*/
/*border-right: 1px solid #cbced4;*/
/*border-top: 1px solid #cbced4;*/
/*margin-left: -1.1em;*/
/*background: #230155;*/
/*}*/

.notification_popup .steps-item-last:after {
	content: none;
}

#integrations-notice {
	border-bottom: 1px solid #eee;
	background-color: #fbfbfb;
	margin-bottom: 25px;
}

#integrations-notice .int-container a {
	color: #7a25f3 !important;
}

#integrations-notice .int-container {
	color: #575757;
	font-size: 0.875rem;
	width: 85%;
	text-align: center;
}

.notification_popup .steps-link {
	-webkit-transition: 0.25s ease-out;
	transition: 0.25s ease-out;
	display: block;
	text-decoration: none;
	padding: 1em 0;
	color: white;
	text-align: left;
	position: relative;
	z-index: 1;
}

/*.steps-item:not(.steps-item-first) .steps-link:before {*/
/*margin: 0 1em 0em 1.4em;*/
/*}*/

.notification_popup .step-launch-number {
	width: 32px;
	height: 32px;
	display: inline-block;
	content: counter(steps);
	text-align: center;
	border-radius: 50%;
	color: white;
	margin: 0 1em 0 0;
	line-height: 31px;
	font-weight: bold;
	background: #8526f3;
	border: 1px solid #7d1bf1;
}

.notification_popup .steps-link:before {
	width: 32px;
	height: 32px;
	display: inline-block;
	content: counter(steps);
	text-align: center;
	background: #bcbcbc;
	border-radius: 50%;
	color: white;
	margin: 0 1em 0 0;
	line-height: 31px;
	font-weight: bold;
	float: left;
	margin-top: -3px;
	margin-left: 20px;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
	-webkit-appearance: none;
	margin: 0;
}

input[type="number"] {
	-moz-appearance: textfield;
}

input.ps-input {
	width: 90px;
	float: right;
	text-align: center;
}

.notification_popup .ps-text-input .ps-input-frame {
	margin: 0;
}

span.input-right-text {
	float: right;
	margin-left: 10px;
	line-height: 2.3rem;
	font-size: 1rem;
}

.notification_popup .ps-text-input span.right-text {
	float: right;
	margin-left: 10px;
	color: #cccccc;
	text-transform: uppercase;
	line-height: 2.3rem;
	font-size: 0.8rem;
}

.notification_popup .ps-text-input input.ps-input {
	width: 90px;
	float: right;
	text-align: center;
	padding: 5px;
}

/**
 * Change link colors
 */
.notification_popup .steps-item-done .steps-link,
.notification_popup .steps-item-active .steps-link {
	color: #606061 !important;
}

.notification_popup .steps-item-done .steps-link:before,
.notification_popup .steps-item-active .steps-link:before {
	background: #8526f3;
	border: 1px solid #7d1bf1;
}

.notification_popup .box-section.sub-section,
.notification_popup .step-content.customize-step .section.sub-section {
	border-left: 6px solid #e2e4e8 !important;
}

.notification_popup .message-step .section .sub-title,
.notification_popup .step-content.customize-step .section .sub-title {
	line-height: 35px;
}

.notification_popup .step-content.customize-step ps-toggle {
	float: right;
}

span.settings-subtitle {
	float: right;
	margin-left: 10px;
	color: #cccccc;
	text-transform: uppercase;
	line-height: 2.3rem;
}

.notification_popup .step-content .step-panel {
	position: relative;
	background-color: #fff;
	box-shadow: 0 2px 3px rgba(237, 237, 237, 0.85);
	margin: 0 auto 20px;
	border-radius: 5px;
	border: 1px solid #e2e4e8;
}

.notification_popup .step-content .step-panel > .title {
	padding: 10px 14px;
	font-weight: bold;
	font-size: 17px;
	border-bottom: 1px solid #e2e4e8;
	background-color: #ffffff12;
	color: #7725f3;
}

.notification_popup .step-content .step-panel > .step-options > .item {
	position: relative;
	display: inline-block;
	line-height: 2rem;
}

.notification_popup .step-content .step-panel > .step-options {
	color: rgba(32, 32, 32, 0.48);
	position: relative;
	background-color: rgba(234, 237, 238, 0.3);
	border-bottom-left-radius: 5px;
	padding: 10px 15px;
	border-bottom-right-radius: 5px;
	display: inline-block;
	width: 100%;
	margin-bottom: -6px;
}

.notification_popup .step-content .step-panel > .section:last-child {
	border: none;
}

.notification_popup .step-content > p {
	font-size: 1.1rem;
	color: rgb(136, 136, 136);
}

.notification_popup .step-content .step-panel > .section p.description {
	font-size: 1rem;
	color: #9fa3a5;
}

.notification_popup .step-panel input {
	margin: 0;
}

.notification_popup .step-panel .sub-title {
	font-size: 1rem;
}

.notification_popup .step-content .step-panel > .section.row {
	margin: 0;
	padding-left: 0;
	padding-right: 0;
}

.notification_popup
	.step-content
	.step-panel
	> .section
	.option-container
	> .title {
	display: inline-block;
	padding: 7px;
	font-weight: bold;
	padding-left: 5px;
	color: #717171de;
}

.notification_popup .step-content .step-panel > .section .option-container {
	display: block;
	padding: 5px 0px 0px 0px;
}

.notification_popup .step-content .step-panel > .section {
	padding: 15px 15px;
	border-bottom: 1px dotted #e9eced;
	font-size: 15px;
	color: rgb(136, 136, 136);
	position: relative;
}

/*NOTIFICATION*/

.notification_popup .notification-live-preview {
	position: fixed;
	left: 10px;
	bottom: 10px;
	z-index: 100000000;
}

.notification_popup
	.notification-preview-container
	> .mobile-container
	.ps-bubble {
	position: absolute;
	margin-top: 43px;
	margin-left: 9px;
}

.notification_popup .notification-preview-container > .mobile-container {
	background-image: url(/pmd/img/phone.png);
	background-repeat: no-repeat;
	background-position: top;
	margin-top: 0px;
	height: 127px;
	background-size: 41%;
	position: relative;
	margin-bottom: -11px;
}

.notification_popup
	.notification-preview-container.notification-live-type
	.bubble-body.live-type {
	transform: scale(0.8);
	top: 14px;
}

#notification-details .step-panel .sub-title {
	font-size: 0.9rem;
}

#notification-details .step-content .step-panel > .section.row {
	padding-left: 0;
	padding-right: 0;
}

#notification-details .step-content .step-panel > .section {
	padding: 10px 15px;
	font-size: 14px;
}

.notification_popup .ps-visual-value div {
	margin-bottom: -3px;
	margin-top: -3px;
}

.notification_popup .ps-visual-value .fa-toggle-on {
	color: #52be6e;
}

.notification_popup .ps-visual-value .fa-toggle-off {
	color: #dfdfdf;
}

.notification_popup .notification-preview-container {
	margin: 20px -15px -15px -15px;
	padding: 35px 50px;
	background: #fbfbfb;
	position: relative;
	border-bottom-left-radius: 5px;
	border-bottom-right-radius: 5px;
	width: 100%;
}

/*COMBO RTL NOTIFICATION*/
.notification_popup .ps-bubble .bubble-body.rtl-bubble.combo-type .bubble-icon {
	padding-right: 0 !important;
	padding-left: 5px;
}

.notification_popup
	.ps-bubble
	.bubble-body.rtl-bubble.combo-type
	.bubble-content {
	margin-right: auto;
	margin-left: 0;
	padding-left: 21px;
	padding-right: 0;
}

.notification-preview-container .pfs-link svg {
	display: inline-block !important;
	width: 11px !important;
	height: 13px !important;
}

.notification-preview-container .bubble-body .bubble-time .pfs-link a {
	color: #7225f3;
	text-decoration: none;
	font-family: Lato, helvetica, arial, sans-serif !important;
}

.notification-preview-container .bubble-body .bubble-time .pfs-link {
	display: inline-block;
	font-size: 10px !important;
	font-family: Lato, helvetica, arial, sans-serif !important;
}

.ps-bubble .material-icons.md-14 {
	font-size: 14px !important;
}

.ps-bubble .material-icons.md-24 {
	font-size: 24px !important;
}

.notification-preview-container .gfs-popup-052018__stars {
	list-style-type: none !important;
	margin: 0 0 0 -2px !important;
	padding: 0 0 1px 0 !important;
}

.notification-preview-container .gfs-popup-052018__stars li {
	display: inline !important;
	margin-right: -3px !important;
	color: #f6b702 !important;
}

/*RTL*/
.notification_popup .ps-bubble .bubble-body.rtl-bubble {
	direction: rtl;
	font-family: Arial, sans-serif;
}

.notification_popup .ps-bubble .bubble-body.rtl-bubble .bubble-icon {
	border-right: 0;
	border-left: 1px solid #ebebeb;
}

.notification_popup .ps-bubble .bubble-body.rtl-bubble #ps-bubble-close {
	left: 6px !important;
	right: inherit !important;
}

.notification_popup .ps-bubble .bubble-body.rtl-bubble .bubble-content {
	margin-left: 0;
	margin-right: 75px;
	padding-right: 0;
	padding-left: 4px;
	text-align: right;
}

/*STREAM NOTIFICATION*/
.notification_popup
	.ps-bubble
	.bubble-body.stream-type
	a.bubble-product-link:hover {
	text-decoration: underline !important;
	cursor: pointer !important;
}

.notification_popup .ps-bubble .bubble-body.stream-type a.bubble-product-link {
	font-weight: bold !important;
	color: #454545;
}

.notification_popup .ps-bubble .bubble-body.stream-type span.stream-location {
	font-size: 12px !important;
	opacity: 0.6 !important;
	font-weight: normal !important;
	color: inherit !important;
}

.notification_popup .ps-bubble .bubble-body.stream-type .bubble-icon {
	border-right: none;
}

.notification_popup .ps-bubble .bubble-body.stream-type .bubble-icon img {
	border-radius: 5px;
	padding: 0px;
	/*border: 1px solid #f2f2f2;*/
	left: 1px;
	max-width: 65px;
	max-height: 65px;
	width: auto;
	height: auto;
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	margin: auto;
}

.notification_popup
	.ps-bubble
	.bubble-body.stream-type
	.bubble-icon
	span.bubble-initials {
	width: 65px;
	border-radius: 5px;
	padding: 1px;
	/*border: 1px solid #dddddd;*/
	left: 2px;
	height: 65px;
	margin-top: auto;
	margin-bottom: auto;
	position: absolute;
	top: 0;
	bottom: 0;
	background-color: #f8f8f8;
	color: #444a50;
	font-weight: bolder;
	font-size: 27px;
	right: 0;
	text-align: center;
	line-height: 64px !important;
}

.notification_popup
	.ps-bubble
	.bubble-body.stream-type
	.bubble-content
	.bubble-title
	span {
	background-color: transparent;
	padding: 0;
	color: #7825f3;
}

.notification_popup
	.ps-bubble
	.bubble-body.stream-type
	.bubble-content
	.bubble-description {
	margin-top: 0;
}

/*STREAM RTL*/
.notification_popup .ps-bubble .bubble-body.stream-type.rtl-bubble {
	border-radius: inherit;
	border-bottom-right-radius: 5px;
	border-top-right-radius: 5px;
	border-top-left-radius: 5px;
	border-bottom-left-radius: 5px;
}

.notification_popup
	.ps-bubble
	.bubble-body.stream-type.rtl-bubble
	.bubble-icon
	span.bubble-initials {
	margin-right: 2px;
}

.notification_popup
	.ps-bubble
	.bubble-body.stream-type.rtl-bubble
	.bubble-icon
	img {
	right: 2px;
}

.notification_popup
	.ps-bubble
	.bubble-body.stream-type.rtl-bubble
	.bubble-icon {
	border-right: 0;
	border-left: none;
}

/*LIVE HITS - RTL*/
.notification_popup
	.ps-bubble
	.bubble-body.live-type.rtl-bubble
	> .bubble-content {
	margin-left: 0;
	margin-right: 0;
}

/*LIVE HITS*/
.notification_popup .ps-bubble .bubble-body.live-type {
	background-color: #ffffff;
	width: 250px;
}

.notification_popup
	.ps-bubble
	.bubble-body.live-type
	> .bubble-content
	> .bubble-title
	> span {
	font-weight: bolder;
	display: inline-block;
	padding: 3px 10px;
	border-radius: 4px;
	min-width: 120px;
	background-color: #f2e9ff;
	color: #7825f3;
}

.notification_popup
	.ps-bubble
	.bubble-body.live-type
	> .bubble-content
	> .bubble-title {
	line-height: 35px;
	font-size: 21pt;
}

.notification_popup
	.ps-bubble
	.bubble-body.live-type
	> .bubble-live-pulse
	svg
	> circle {
	stroke: #24a03c;
	fill: #24a03c !important;
	stroke-width: 5px;
	stroke-opacity: 1;
}

.notification_popup .ps-bubble .bubble-body.live-type > .bubble-live-pulse {
	margin-top: 10px;
}

.notification_popup
	.ps-bubble
	.bubble-body.live-type
	> .bubble-live-pulse
	svg.ps-pulse-dot {
	height: 25px !important;
	width: 100% !important;
}

.notification_popup
	.ps-bubble
	.bubble-body.live-type
	> .bubble-live-pulse
	svg
	.pulse {
	fill: white !important;
	fill-opacity: 0;
	transform-origin: 50% 50%;
	animation-duration: 2s;
	animation-name: pulse-animation;
	animation-iteration-count: infinite;
}

@keyframes pulse-animation {
	from {
		stroke-width: 3px;
		stroke-opacity: 1;
		transform: scale(0.3);
	}
	to {
		stroke-width: 0;
		stroke-opacity: 0;
		transform: scale(2);
	}
}

.notification_popup
	.ps-bubble
	.bubble-body.live-type
	> .bubble-content
	> .bubble-description {
	line-height: 25px;
	font-size: 14px;
}

.notification_popup .ps-bubble .bubble-body.live-type > .bubble-content {
	padding: 8px 15px 8px 15px !important;
	margin-left: 0 !important;
	text-align: center !important;
}

/*COMBO NOTIFICATION*/
.notification_popup .step-panel ps-bubble .bubble-body.combo-type,
td ps-bubble .bubble-body.combo-type {
	width: 320px;
}

.notification_popup
	.ps-bubble
	.bubble-body.combo-type
	.bubble-content.no-branding {
	padding-top: 20px;
	padding-bottom: 20px;
}

.notification_popup .ps-bubble .bubble-body.combo-type {
	background-color: #ffffff;
	width: 330px;
}

.notification_popup .ps-bubble .bubble-body.rtl-bubble.combo-type .bubble-icon {
	border-left: 0;
}

.notification_popup .ps-bubble .bubble-body.combo-type .bubble-icon {
	border-right: 0;
	width: 35%;
	padding-right: 5px;
}

.notification_popup .ps-bubble .bubble-body.combo-type .bubble-time {
	color: #7627f3;
}

.notification_popup .ps-bubble .bubble-body.combo-type .bubble-time .ps-link a {
	color: #ffffff;
	text-decoration: none;
	font-weight: 700;
}

.notification_popup .ps-bubble .bubble-body.combo-type .bubble-time .ps-link {
	padding: 1px 5px;
	background-color: #fff;
	border-radius: 2px;
	border: 1px solid #7627f3;
	color: #7627f3;
	margin-top: 5px;
}

.notification_popup
	.ps-bubble
	.bubble-body.combo-type
	.bubble-content
	.bubble-description {
	color: #7627f3;
	font-size: 13px;
}

.notification_popup
	.ps-bubble
	.bubble-body.combo-type
	#ps-bubble-close
	> span.close-after,
.notification_popup
	.ps-bubble
	.bubble-body.combo-type
	#ps-bubble-close
	> span.close-before {
	background-color: #7627f3;
}

.notification_popup .ps-bubble .bubble-body.combo-type .bubble-content {
	margin-left: auto;
	padding-top: 10px;
	padding-right: 21px;
	padding-bottom: 10px;
	width: 65%;
	margin-top: 5px;
	margin-bottom: 5px;
}

.notification_popup
	.ps-bubble
	.bubble-body.combo-type
	.bubble-icon
	.combo-number
	> .refer {
	margin-top: -7px;
	font-weight: normal !important;
	font-size: 13px !important;
}

.notification_popup
	.ps-bubble
	.bubble-body.combo-type
	.bubble-icon
	.combo-number
	> .num-value {
	font-size: 28px !important;
	font-weight: 900 !important;
	margin-top: -5px;
}

.notification_popup .ps-bubble .scale {
	animation: pound 1s;
	animation-delay: 3s;
	animation-iteration-count: 3;
}

@keyframes pound {
	from {
		transform: none;
	}
	50% {
		transform: scale(1.2);
	}
	to {
		transform: none;
	}
}

.notification_popup .ps-bubble .ps-link.whitelabel {
	padding: 1px 0 !important;
}

.notification_popup
	.ps-bubble
	.bubble-body.combo-type
	.bubble-icon
	.combo-number {
	position: relative;
	float: left;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	color: #7627f3;
	width: 100%;
	text-align: center;
}

/*REGULAR NOTIFICATION*/
.notification_popup .ps-bubble .bubble-body {
	width: 320px;
	overflow: hidden;
	position: relative;
	border-radius: 5px;
	background-color: #fff;
	box-shadow: 0 0 1px rgba(0, 0, 0, 0.2), 0 1px 2px rgba(0, 0, 0, 0.05),
		0 8px 50px rgba(0, 0, 0, 0.05);
}

.notification_popup .ps-bubble .bubble-body .bubble-content {
	margin-left: 75px;
	padding-top: 8px;
	padding-right: 4px;
	padding-bottom: 5px;
}

.notification_popup .ps-bubble .bubble-body .bubble-content .bubble-time {
	display: block;
	color: #999;
	font-size: 11px;
}

.notification_popup
	.ps-bubble
	.bubble-body
	.bubble-content
	.bubble-description {
	line-height: 17px;
	font-size: 12px;
	color: #323232;
	margin-bottom: 2px;
	margin-top: 2px;
}

.notification_popup .ps-bubble .bubble-body .bubble-content .bubble-title span {
	font-weight: 700;
	display: inline-block;
	padding: 3px 5px;
	background-color: rgba(120, 37, 243, 0.09);
	border-radius: 3px;
	color: #7825f3;
}

.notification_popup .ps-bubble .bubble-body .bubble-content .bubble-title {
	line-height: 17px;
	font-size: 15px;
	font-weight: bold;
	color: black;
}

.notification_popup .ps-bubble .bubble-body .bubble-icon {
	float: left;
	width: 67px;
	display: block;
	position: absolute;
	border-right: 1px solid #ebebeb;
	height: 100%;
}

.notification_popup .ps-bubble .bubble-body .bubble-icon img {
	height: 67px;
	width: 67px;
	padding: 5px;
	margin-top: auto;
	margin-bottom: auto;
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
}

.notification_popup .ps-bubble .bubble-body .bubble-time .ps-link a {
	color: #7225f3;
	text-decoration: none;
}

.notification_popup .ps-bubble .bubble-body .bubble-time .ps-link {
	display: inline-block;
	font-size: 10px;
}

.notification_popup .ps-bubble #ps-bubble-close {
	position: absolute;
	top: 6px;
	right: 6px;
	height: 13px;
	width: 13px;
	transform: rotate(45deg);
	cursor: pointer;
	opacity: 0.5;
}

.notification_popup .ps-bubble #ps-bubble-close > span.close-before {
	content: "";
	display: block;
	width: 100%;
	height: 3px;
	background-color: #c4c4c4;
	position: absolute;
	left: 0;
	top: 5px;
}

.notification_popup .ps-bubble #ps-bubble-close > span.close-after {
	content: "";
	display: block;
	height: 100%;
	width: 3px;
	background-color: #c4c4c4;
	position: absolute;
	left: 5px;
	top: 0;
}

/*ps-bubble #ps-bubble-close:before {*/
/*content: "";*/
/*display: block;*/
/*width: 100%;*/
/*height: 3px;*/
/*background-color: #c4c4c4;*/
/*position: absolute;*/
/*left: 0;*/
/*top: 5px;*/
/*}*/

/*ps-bubble #ps-bubble-close:after {*/
/*content: "";*/
/*display: block;*/
/*height: 100%;*/
/*width: 3px;*/
/*background-color: #c4c4c4;*/
/*position: absolute;*/
/*left: 5px;*/
/*top: 0;*/
/*}*/

.notification_popup .break-text {
	word-break: break-all;
}

/*INPUT*/

/*On the flex container*/

.notification_popup .flexcontainer {
	display: flex;
	align-items: center;
}

.notification_popup .urls-container {
	padding: 5px;
	background-color: #f5faff;
}

.notification_popup .urls-item .remove-button:hover {
	font-weight: bolder;
}

.notification_popup .grey-text {
	color: #adadad;
}

.notification_popup .urls-item .remove-button {
	cursor: pointer;
	padding-right: 9px;
	border-right: 1px solid #d3e2ff;
	color: #2b2b2c;
	position: relative;
	padding-bottom: 5px;
	padding-top: 5px;
}

.notification_popup .urls-item .text {
	padding-left: 4px;
}

.notification_popup .urls-item span > span.opt-itm {
	color: #7890c2;
}

.notification_popup .urls-item a {
	text-decoration: none !important;
	font-style: normal;
}

.notification_popup .urls-item {
	max-width: 100%;
	background: #e6eeff;
	border: 1px solid #cfdfff;
	padding: 0px 10px 15px 10px;
	cursor: default;
	display: inline-block;
	-webkit-border-radius: 2px 3px 3px 2px;
	-moz-border-radius: 2px 3px 3px 2px;
	border-radius: 2px 3px 3px 2px;
	height: 25px;
	margin-right: 10px;
}

.notification_popup .ps-input form.ng-invalid button,
.notification_popup .ps-input-add form.ng-invalid button {
	border: 1px solid #d80400;
	padding: 11px 30px;
	background-color: #d80400;
}

.notification_popup .ps-url-input form input.ng-invalid:not(.ng-pristine),
    /*ps-input form input.ng-invalid,*/
.notification_popup .ps-input-add form input.ng-invalid:not(.ng-pristine) {
	border-color: #d80400 !important;
}

.notification_popup .ps-input-add .ps-input-frame button:not([disabled]):hover {
	background-color: #1fb278;
}

.notification_popup .ps-input-add .ps-input-frame button {
	border: 1px solid #1dc07f;
	padding: 11px 30px;
	background-color: #23c685;
	outline: 0;
	color: #defff2;
	border-radius: 5px;
	border-top-left-radius: 0;
	border-bottom-left-radius: 0;
	position: absolute;
	top: 0px;
	right: 0px;
}

.notification_popup .ps-input-add .ps-input-frame input {
	padding: 10px 88px 10px 10px;
}

.notification_popup .section ps-url-input:last-child .ps-input-frame,
.notification_popup .section ps-input-add:last-child .ps-input-frame,
.notification_popup .section ps-input:last-child .ps-input-frame {
	margin-bottom: 0;
}

.notification_popup .ps-input[disabled] input,
.notification_popup .ps-input[disabled] label {
	/* background: red; */
	opacity: 0.5;
	background-color: #f9f9f9;
	pointer-events: none;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
	transition: background-color 5000s ease-in-out 0s;
}

.notification_popup .flexcontainer > ps-input:last-child {
	margin-left: 5px;
}

.notification_popup .flexcontainer > ps-input:first-child {
	margin-right: 5px;
	margin-top: 5px;
}

.notification_popup .flexcontainer > ps-input {
	flex: 1;
}

.notification_popup .ps-input-frame {
	position: relative;
	margin: 10px 0;
}

.notification_popup .ps-input-frame label.input-title {
	line-height: 1em;
	font-size: 0.8rem;
	text-transform: capitalize;
	font-weight: bold;
	color: #717171de;
	display: inline-block;
	padding: 10px 10px;
	position: absolute;
	z-index: 10;
	top: 0;
	left: 0;
}

.notification_popup
	.ps-input:not([disabled])
	.ps-input-frame
	input:active
	+ label,
.notification_popup
	.ps-input:not([disabled])
	.ps-input-frame
	input:focus
	+ label,
.notification_popup
	.ps-input:not([disabled])
	.ps-input-frame:hover
	> label.input-title {
	color: #7825f3 !important;
}

.notification_popup .ps-input-frame input:hover,
.notification_popup .ps-input-frame input:focus {
	border-color: #7825f3;
}

.notification_popup .ps-input-frame input {
	margin: 0;
	padding: 25px 8px 10px;
	background-color: white;
	border-radius: 5px;
	display: block;
	width: 100%;
	outline: none;
	border: 2px solid #e2e4e8c7;
}

input::-webkit-input-placeholder {
	color: #b4b7bb !important;
}

input:-moz-placeholder {
	/* Firefox 18- */
	color: #b4b7bb !important;
}

input::-moz-placeholder {
	/* Firefox 19+ */
	color: #b4b7bb !important;
}

input:-ms-input-placeholder {
	color: #b4b7bb !important;
}

/*LOGIN / SIGNUP*/
.notification_popup .auth-container {
	display: flex;
	height: 100%;
	min-height: 1px;
	width: 100%;
	flex: 1 1 0%;
}

.notification_popup .signup-container .logo,
.notification_popup .auth-container .logo {
	font-size: 4rem;
	color: #7a25f3 !important;
	text-align: center;
	padding-bottom: 0;
	margin: 0px 0 15px 0px;
}

.notification_popup .signup-container .box-content {
	border: 0 !important;
}

.notification_popup .auth-container input {
	border-color: rgb(226, 228, 229);
}

.notification_popup .auth-container .inner-auth-container {
	box-sizing: border-box;
	display: block;
	height: 100%;
	position: relative;
	border-width: 0px;
	border-style: initial;
	border-color: initial;
	border-image: initial;
	flex: 1 1 0;
	transition: width 0.2s ease-out;
}

.notification_popup .auth-container .inner-auth-container .auth-frame {
	display: flex;
	height: 100%;
	-webkit-box-align: center;
	align-items: center;
	-webkit-box-orient: vertical;
	-webkit-box-direction: normal;
	flex-direction: column;
	-webkit-box-pack: start;
	justify-content: flex-start;
	padding: 20px 0px;
}

.notification_popup
	.auth-container
	.inner-auth-container
	.auth-frame
	.auth-box {
	/*box-shadow: rgb(237, 237, 237) 0px 0px 30px;*/
}

.notification_popup .auth-frame .auth-box {
	width: 470px;
	border: 1px solid rgb(226, 228, 229);
	border-top: 0px;
}

.notification_popup .auth-frame .top-line {
	background-color: rgb(120, 37, 243);
	height: 7px;
	margin-left: -1px;
	width: calc(100% + 2px);
}

.notification_popup .auth-box .auth-form {
	padding: 35px 65px;
	text-align: center;
}

.notification_popup .auth-form .title {
	margin-bottom: 30px;
}

.notification_popup .auth-form .title h2 {
	font-size: 1.2rem;
	color: #c4c4c4;
	text-align: center;
	font-weight: normal;
}

.notification_popup .auth-form .title h1 {
	font-size: 29px;
	color: #2b3950;
	text-align: center;
	font-weight: bolder;
}

.notification_popup .form-divider {
	line-height: 0.9px;
	text-align: center;
	width: 100%;
	border-bottom: 1px solid rgb(226, 228, 229);
	margin: 26px 0px;
}

.notification_popup .step-panel .form-divider {
	margin: 35px 0px;
}

.notification_popup .form-divider > span {
	background-color: rgb(255, 255, 255);
	color: rgb(150, 159, 167);
	font-size: 16px;
	letter-spacing: 0.57px;
	padding: 0px 15px;
}

.notification_popup .auth-form .inputs-container {
	padding-bottom: 4px;
	margin: auto;
}

.notification_popup .social-logins button > .ladda-label {
	width: 100%;
}

button img.google-icon {
	width: 39px;
	float: left;
	margin: 9px;
	margin-right: -50px;
	background-color: white;
	border-radius: 4px;
	margin-top: 4px;
	margin-left: 4px;
	padding: 3px;
	padding-top: 7px;
	padding-left: 5px;
	padding-bottom: 6px;
	padding-right: 5px;
}

.notification_popup .social-logins > button,
.notification_popup .auth-form .inputs-container button.form-submit {
	background-color: #7725f3;
	border: 0;
	border-radius: 3px;
	color: #fff;
	cursor: pointer;
	display: block;
	font-size: 14px;
	height: 50px;
	line-height: 50px;
	letter-spacing: 0.5px;
	margin: 0;
	outline: none;
	padding: 0;
	text-align: center;
	text-transform: uppercase;
	transition: all 0.2s ease-in-out;
	user-select: none;
	width: 100%;
}

.notification_popup .social-logins > button[disabled],
.notification_popup .auth-form .inputs-container button.form-submit[disabled] {
	box-sizing: border-box;
	cursor: default;
	color: rgb(216, 218, 220);
	user-select: none;
	background-color: rgb(242, 243, 243);
	opacity: 1;
	border-width: 0px;
	border-style: initial;
	border-color: initial;
	border-image: initial;
	border-radius: 0px;
	margin: 0px auto;
	outline: none;
	padding: 0px;
	transition: all 0.2s ease-in-out;
}

.notification_popup .auth-form .terms {
	color: rgb(150, 159, 167);
	font-size: 12px;
	letter-spacing: 0.6px;
	line-height: 15px;
	margin-top: 10px;
}

.notification_popup .auth-form a {
	color: #7725f3;
}

.notification_popup .auth-form .join-block {
	color: rgb(150, 159, 167);
	font-size: 12px;
	letter-spacing: 0.6px;
	line-height: 15px;
	margin-top: 20px;
	padding-top: 20px;
	text-align: center;
	width: 100%;
	border-top: 1px solid rgb(216, 218, 220);
}

/*SIGNUP*/

.notification_popup .signup-container {
	background-color: rgb(255, 255, 255);
	height: 100%;
	left: 0px;
	top: 0px;
	width: 100%;
	z-index: 11;
	-webkit-box-align: start;
	display: flex;
	align-items: flex-start;
}

.notification_popup .signup-inner-container {
	position: relative;
	width: 940px;
	margin: 20px auto 60px;
	transition: all 0.2s ease-in-out;
}

.notification_popup .signup-inner-container .auth-frame {
	width: 940px;
	display: flex;
	position: relative;
	width: 100%;
	transition: all 0.2s ease-in-out;
	/*box-shadow: rgb(237, 237, 237) 0px 0px 30px;*/
}

.notification_popup .signup-inner-container .auth-box {
	flex-basis: 0px;
	flex-grow: 1;
	width: 470px;
	background-color: rgb(255, 255, 255);
	border: 1px solid rgb(226, 228, 229);
	border-image: initial;
	border-top: 0px;
}

.notification_popup .side-box {
	flex-basis: 0px;
	flex-grow: 1;
	background-color: rgb(255, 255, 255);
	box-sizing: border-box;
	color: rgb(15, 26, 38);
	display: flex;
	position: relative;
	-webkit-box-pack: center;
	justify-content: center;
	border: 1px solid rgb(226, 228, 229);
	border-image: initial;
	border-left: 0px;
}

.notification_popup .side-box .box-content {
	display: flex;
	height: 150px;
	position: relative;
	width: 370px;
	-webkit-box-align: center;
	align-items: center;
	-webkit-box-pack: center;
	justify-content: center;
}

.notification_popup .box-content i {
	color: #feca17;
}

.notification_popup .box-content .stars {
	display: block;
	height: 20px;
	position: absolute;
	top: 30px;
}

.notification_popup .reviews-signup {
	font-size: 14pt;
}

.notification_popup .reviews-signup img {
	width: 80px;
}

.notification_popup .testimonials {
	margin-top: 60px;
}

.notification_popup .signup-customers img {
	width: 405px;
	opacity: 0.9;
}

.notification_popup .signup-customers {
	position: absolute;
	margin-top: 170px;
}

.notification_popup .box-content .testimonial-quote {
	color: rgb(150, 159, 167);
	font-size: 16px;
	letter-spacing: 0.2px;
	line-height: 23px;
	quotes: none;
	text-align: center;
	margin: 0px 0px 12px;
	display: block;
}

.notification_popup .side-box i.box-image {
	background-image: url(/pmd/img/signup-image.png);
	background-size: 65%;
	bottom: 0px;
	background-position: top;
	display: block;
	height: 325px;
	position: absolute;
	width: 100%;
	background-repeat: no-repeat;
}

/*TOGGLE*/
label.switch {
	margin: 0;
}

.notification_popup .switch {
	position: relative;
	display: inline-block;
	width: 60px;
	height: 34px;
}

.notification_popup .switch input {
	display: none;
}

.notification_popup .switch .slider {
	position: absolute;
	cursor: pointer;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ccc;
	-webkit-transition: 0.4s;
	transition: 0.4s;
}

.notification_popup .switch .slider:before {
	position: absolute;
	content: "";
	height: 26px;
	width: 26px;
	left: 4px;
	bottom: 4px;
	background-color: white;
	-webkit-transition: 0.1s;
	transition: 0.1s;
}

.notification_popup .switch input:checked + .slider {
	background-color: #50bf82;
}

.notification_popup .switch input:focus + .slider {
	box-shadow: 0 0 1px #50bf82;
}

.notification_popup .switch input:checked + .slider:before {
	-webkit-transform: translateX(26px);
	-ms-transform: translateX(26px);
	transform: translateX(26px);
}

/* Rounded sliders */
.notification_popup .switch .slider.round {
	border-radius: 34px;
}

.notification_popup .switch .slider.round:before {
	border-radius: 50%;
}

.notification_popup .customize-step ps-toggle {
	float: right;
	width: 105px;
}

.notification_popup .opacity-half {
	opacity: 0.5;
}

.notification_popup .greyed-out {
	opacity: 0.5;
	pointer-events: none;
}

.notification_popup .ps-toggle .toggle-state.on {
	color: #50bf82;
}

.notification_popup .ps-toggle .toggle-state {
	line-height: 2.2em;
	margin-right: 8px;
	color: #cccccc;
}

/*URL Parser*/
.notification_popup .ps-parser-components {
	display: inline-block;
	position: relative;
	width: 100%;
	margin-top: 10px;
	line-height: 1.3rem;
}

.notification_popup .ps-parser-components > ul {
	list-style: none;
	margin: 0;
	padding: 0;
}

.notification_popup .ps-parser-components > ul > li.url-origin {
	color: black;
	padding-left: 0;
}

.notification_popup .ps-parser-components > ul > li.url-part.selected-url-part,
.notification_popup .ps-parser-components > ul > li.url-part:hover {
	color: #ffffff;
	cursor: pointer;
	border-color: #037ed0;
	background-color: #129bf5;
}

.notification_popup .ps-parser-components > ul > li.url-part {
	border: 1px solid;
	position: relative;
	display: inline-block;
	margin-bottom: 10px;
	max-width: 100%;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	height: 30px;
	margin-right: 5px;
	line-height: 1.1rem;
}

.notification_popup .ps-parser-components > ul > li.url-divider {
	padding: 0;
	/*padding: 5px;*/
}

.notification_popup .ps-parser-components > ul > li {
	float: left;
	padding: 5px 7px;
}

/*WEBHOOOK*/

.notification_popup .ps-webhook-container .step-panel > .section {
	margin-top: 10px;
}

.notification_popup .ps-webhook-container .step-panel {
	margin-top: -15px;
}

.notification_popup .ps-webhook-container button.webhook-btn:hover {
	color: #7825f3;
	background-color: #f8f4ff;
}

.notification_popup .ps-webhook-container button.webhook-btn.is-open {
	background-color: #7825f3;
	color: white;
	border-color: #7825f3;
	box-shadow: -4px 4px 0px 0px #e2e4e8;
}

.notification_popup .ps-webhook-container button.webhook-btn {
	background-color: #ffffff;
	color: #727480;
	border: 1px solid;
	font-size: 0.9rem;
	border-radius: 3px;
	padding: 5px 15px;
	line-height: 1.8rem;
	z-index: 1;
	position: relative;
}

/*SETTINGS*/
.notification_popup .ps-container > .ps-body-container.settings-container {
}

.notification_popup
	.ps-container
	> .ps-body-container.settings-container
	.nav
	> .nav-item
	> a:not([href]):not([tabindex]) {
	color: #31323657;
	text-decoration: none;
}

.notification_popup
	.ps-container
	> .ps-body-container.settings-container
	.nav
	> .nav-item
	> .nav-link.active {
	color: #8032f3 !important;
	background-color: #fff;
	border-color: #dee2e6 #dee2e6 #fff;
	/*font-weight: 700;*/
}

.notification_popup
	.ps-container
	> .ps-body-container.settings-container
	.nav
	> .nav-item:not(:first-child) {
	margin-left: 5px;
}

.notification_popup
	.ps-container
	> .ps-body-container.settings-container
	.nav
	> .nav-item {
	cursor: pointer;
	font-size: 1.1rem;
}

.notification_popup
	.ps-container
	> .ps-body-container.settings-container
	.tab-content
	> .active {
	display: block;
	background-color: white;
	border-left: 1px solid;
	border-bottom: 1px solid;
	border-right: 1px solid;
	border-color: #dee2e6;
	border-bottom-left-radius: 2px;
	border-bottom-right-radius: 2px;
	padding: 15px;
}

.notification_popup .box-content {
	border: 1px solid #dee2e69e;
}

.notification_popup .box-content > .box-panel > .box-title > .box-sub-title {
	font-size: 0.9rem;
	font-weight: normal;
	color: #aeaeae;
}

.notification_popup
	.box-content
	> .box-panel
	> .box-title
	> .infos
	span
	strong {
	font-weight: bolder !important;
}

.notification_popup .box-content > .box-panel > .box-title > .infos i.fa {
	color: #9da1a3;
}

.notification_popup .box-content > .box-panel > .box-title > .infos span {
	font-size: 0.7em;
	font-weight: normal;
	padding: 10px 15px;
	border-radius: 50px;
	border: 1px solid #dadee1;
	margin-left: 5px;
	color: #8d8d8d;
}

.notification_popup .box-content > .box-panel > .box-title > .infos {
	display: inline-block;
	float: right;
}

.notification_popup .box-content > .box-panel > .box-title {
	color: #6b6d70;
	padding: 15px;
	font-size: 1.1rem;
	font-weight: 700;
	border-bottom: 1px solid #ebedf0;
	/*background-color: #ebedf030;*/
}

.notification_popup .box-content > .box-panel {
	position: relative;
	background-color: #fff;
}

.notification_popup .box-content > .box-panel > .box-section.row {
	margin: 0;
	padding-left: 0;
	padding-right: 0;
	line-height: 2.2rem;
}

.notification_popup .box-content > .box-panel > .box-section:last-child {
	border: none;
}

.notification_popup .box-content > .box-panel > .box-section {
	padding: 15px 20px;
	border-bottom: 1px dotted #e9eced;
	font-size: 15px;
	color: rgb(136, 136, 136);
	position: relative;
}

.notification_popup .section-buttons-container .button-container {
	position: relative;
	display: flex;
	flex-direction: row-reverse;
}

.notification_popup .section-buttons-container {
	display: inline-block;
	width: 100%;
	margin-top: 15px;
}

.notification_popup .ps-msg {
	padding: 10px 10px;
	margin-bottom: 20px;
}

.notification_popup .ps-msg.success {
	background-color: #2aa7461f;
	border-left: 3px solid #2aa746;
}

/*COLOR PICKER*/
.notification_popup .color-picker-wrapper .input-group .color-picker-input {
	width: 105px;
}

.notification_popup .color-picker-wrapper .input-group .input-group-addon {
	padding: 6px 20px;
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
}

.notification_popup
	.color-picker-wrapper
	.input-group
	.input-group-addon:last-child {
	border: 1px solid #ced4da;
	border-left-width: 0 !important;
}

.notification_popup .nav-counter.install {
	margin-left: 58px;
}

.notification_popup .nav-counter {
	display: inline-block;
	vertical-align: text-top;
	position: absolute;
	margin-right: 3px;
	margin-left: 11px;
	margin-top: -4px;
	padding: 1px 6px;
	line-height: 16px;
	font-size: 11px;
	color: white;
	text-shadow: 0 1px #902a27;
	background: #e93631;
	border-radius: 10px;
	background-image: -webkit-linear-gradient(top, #f65d5f, #e93631);
	background-image: -moz-linear-gradient(top, #f65d5f, #e93631);
	background-image: -o-linear-gradient(top, #f65d5f, #e93631);
	background-image: linear-gradient(to bottom, #f65d5f, #e93631);
	-webkit-box-shadow: inset 0 1px rgba(255, 255, 255, 0.2),
		0 1px rgba(0, 0, 0, 0.2);
	box-shadow: inset 0 1px rgba(255, 255, 255, 0.2), 0 1px rgba(0, 0, 0, 0.2);
}

.notification_popup .blur {
	filter: blur(1px);
	-ms-filter: blur(1px);
	-webkit-filter: blur(1px);
}

#branding-notice-overlay .overlay-backdrop {
	width: 100%;
	height: 445px;
	position: absolute;
	top: 78px;
	background: #3c3c3c73;
}

#branding-notice-overlay
	.overlay-container
	.locked-feature-content
	.locked-feature-plan-info {
	margin-top: 10px;
}

#branding-notice-overlay
	.overlay-container
	.locked-feature-content
	.locked-feature-plan-info,
#branding-notice-overlay
	.overlay-container
	.locked-feature-content
	.locked-feature-description {
	color: #9ba0ab;
	font-size: 0.9rem;
}

#branding-notice-overlay .overlay-container .locked-feature-title {
	padding: 10px;
	font-size: 1.2rem;
	font-weight: bolder;
	color: #636b7a;
}

#branding-notice-overlay .overlay-container {
	position: absolute;
	top: 30%;
	width: 65%;
	left: 140px;
	text-align: center;
	padding: 25px 35px;
	background: white;
	border: 1px solid #f0f2f4;
	box-shadow: 0 0 46px 3px #53515161;
	border-radius: 5px;
}

#branding-notice-overlay {
}

#beamerOverlay iframe .header.dark {
	background: #7825f3 !important;
}

#sub-bluesnap {
	height: 100%;
}

#bluesnap {
	height: 100%;
	width: 100%;
}

/*SLICK*/
.notification_popup .ps-carousel-wrapper {
	width: 375px;
}

.notification_popup .ps-carousel-slide.ps-message {
	opacity: 0.5;
	transition: opacity 800ms cubic-bezier(0.165, 0.84, 0.44, 1),
		transform 800ms cubic-bezier(0.165, 0.84, 0.44, 1);
	transform: scale(0.6) !important;
	outline: none;
	border: none;
	-webkit-border-radius: 10px;
	-moz-border-radius: 10px;
	border-radius: 10px;
}

.notification_popup .ps-carousel-slide.ps-message.slick-center {
	opacity: 1;
	transform: scale(1) !important;
	z-index: -100;
	cursor: -webkit-grab;
}

.notification_popup .ps-carousel-slide.slick-center:active {
	cursor: -webkit-grabbing;
}

/* Hide Carousel until initialized */
.notification_popup .carousel {
	opacity: 0;
	visibility: hidden;
	transition: opacity 400ms ease-in;
}

.notification_popup .ps-carousel {
	height: 300px;
	margin-right: auto;
	margin-left: auto;
	padding-top: 0px;
}

.notification_popup .ps-carousel-wrapper,
.notification_popup .ps-carousel {
	margin-left: auto;
	margin-right: auto;
}

.notification_popup .ps-carousel-wrapper,
.notification_popup .ps-carousel {
	display: block;
	overflow: hidden;
	margin-top: -10px;
}

.notification_popup .slick-initialized {
	visibility: visible;
	opacity: 1;
}

.notification_popup .fader {
	position: absolute;
	left: 0;
	top: -40px;
	right: 0;
	height: 35%;
	background-image: -webkit-linear-gradient(
		270deg,
		#fff 10%,
		hsla(0, 0%, 100%, 0.8) 35%,
		hsla(0, 0%, 100%, 0)
	);
	background-image: linear-gradient(
		180deg,
		#fff 10%,
		hsla(0, 0%, 100%, 0.8) 35%,
		hsla(0, 0%, 100%, 0)
	);
}

.notification_popup .fader.bottom {
	left: 0;
	top: auto;
	right: 0;
	bottom: 0;
	background-image: -webkit-linear-gradient(
		90deg,
		#fff 10%,
		hsla(0, 0%, 100%, 0.8) 35%,
		hsla(0, 0%, 100%, 0)
	);
	background-image: linear-gradient(
		0deg,
		#fff 10%,
		hsla(0, 0%, 100%, 0.8) 35%,
		hsla(0, 0%, 100%, 0)
	);
}

.notification_popup .lets-start {
	padding-top: 65px;
	border: 2px dashed #efefef;
	border-radius: 4px;
}

.notification_popup .box-section .wizard-btn.row-button {
	padding: 0px 10px !important;
	line-height: 27px;
	font-size: 0.8rem;
}

.notification_popup .api-key .wizard-btn.sm {
	padding: 0 0 !important;
	height: 38px;
	margin-left: 5px;
	line-height: 1;
	width: 85px !important;
	position: relative;
	margin-right: 5px;
}

/*pricing*/
.notification_popup .plan > .plan-container {
	padding: 15px;
	border: 1px solid #4747471a;
	border-radius: 5px;
	width: 100%;
	cursor: pointer;
}

.notification_popup .plan.popular .plan-container,
.notification_popup .plan:hover .plan-container {
	box-shadow: 0px 0px 13px 3px #f2f4f7;
	border-color: #7225f3;
}

.notification_popup .plan .submit-button-4 {
	font-size: 1.1rem;
}

img.money-back {
	margin-top: 30px;
	width: 150px;
}

.notification_popup .plan.popular,
.notification_popup .plan:hover {
	transform: scale(1.01);
}

.notification_popup .country-select-container .country-select {
	width: 305px;
	text-align: center;
	margin-left: auto;
	margin-right: auto;
	margin-top: 20px;
}

.notification_popup .plan-container .plan-title img {
	width: 70px;
}

.notification_popup .plan-container .plan-title {
	text-align: left;
}

.notification_popup .titan-plan {
	padding: 50px 0px;
	border: 1px solid;
	border-color: #e7e7e7;
	border-radius: 4px;
}

.notification_popup .titan-plan .plan-container {
	width: 100%;
	text-align: center;
}

.notification_popup .titan-plan .plan-title .name {
	margin-bottom: 5px;
}

.notification_popup .titan-plan .plan-title .description {
	margin-bottom: 15px;
}

.notification_popup .titan-plan .plan-title {
	text-align: center;
}

.notification_popup .plan-container .plan-title .description {
	color: #3c48578c;
	font-size: 13px;
	line-height: 16px;
	font-weight: 300;
	position: relative;
}

.notification_popup .plan-container .plan-details ul li {
	text-align: left;
	list-style: initial;
}

.notification_popup .plan-container .plan-details {
	/*padding: 25px;*/
	/*padding-top: 15px;*/
}

.notification_popup .trial-notice {
	margin-top: 5px;
	margin-bottom: 30px;
	font-size: 1.2rem;
	font-weight: 700;
}

.notification_popup .plan-container .plan-details ul {
	padding-left: 0;
	list-style: none;
	font-size: 15px;
	color: #3e4857;
	margin-bottom: 0;
}

.notification_popup .plan-container .plan-title .name {
	font-size: 25px;
	color: #3c4858;
	line-height: normal;
	margin-bottom: 10px;
	font-weight: 900;
}

.notification_popup .plan-container .plan-price .plan-duration {
	margin-top: 0;
	margin-left: 25px;
	display: inline-block;
	font-size: 12px;
	text-align: left;
}

.notification_popup .plan-container .plan-price .value .cost {
	color: #3c4858;
}

.notification_popup .plan-container .plan-price .value .dollar-sign {
	font-size: 18px;
	vertical-align: top;
	padding-top: -4px;
	margin-top: 8px;
	top: 6px;
	position: relative;
}

.notification_popup .plan-container .plan-price {
	text-align: center;
	padding: 10px;
	background-color: #f2f4f7;
	margin-left: -15px;
	margin-right: -15px;
	border-radius: 4px;
}

.notification_popup .plan-container .plan-price .value {
	display: inline-block;
	font-size: 45px;
	font-weight: 700;
	text-align: left;
	line-height: normal;
	-webkit-transition: all 0.25s ease;
	transition: all 0.25s ease;
}

.notification_popup .plan {
	margin-bottom: 10px;
}

.notification_popup .w-button {
	display: inline-block;
	padding: 10px 30px;
	background-color: #3898ec;
	color: #fff;
	border: 0;
	line-height: inherit;
	text-decoration: none;
	cursor: pointer;
	border-radius: 0;
	outline: 0;
}

.notification_popup .w-button {
	display: inline-block;
	padding: 10px 30px;
	background-color: #3898ec;
	color: #fff;
	border: 0;
	line-height: inherit;
	text-decoration: none;
	cursor: pointer;
	border-radius: 0;
	outline: 0;
}

.notification_popup .submit-button-4.sm {
	font-size: 1rem;
}

.notification_popup .submit-button-4 {
	background-color: #7324f3 !important;
	box-shadow: 2px 0 3px 0 rgba(0, 0, 0, 0.05), 0 1px 2px 0 rgba(0, 0, 0, 0.08);
	font-weight: 700;
	outline: 0;
	cursor: pointer;
	font-size: 1.3rem;
	border-radius: 4px;
	text-align: center;
}

.notification_popup .submit-button-4:hover {
	opacity: 0.8;
	text-decoration: none;
	color: white;
}

.notification_popup .stv-radio-buttons-wrapper {
	clear: both;
	display: inline-block;
}

.notification_popup .stv-radio-button {
	position: absolute;
	visibility: hidden;
	display: none;
}

.notification_popup .stv-radio-button + label {
	float: left;
	padding: 0.5em 1em;
	cursor: pointer;
	border: 1px solid #7225f3;
	margin-right: -1px;
	color: #7225f3;
	background-color: #f0e9ff;
}

.notification_popup .stv-radio-button + label:first-of-type {
	border-radius: 0.7em 0 0 0.7em;
}

.notification_popup .stv-radio-button + label:last-of-type {
	border-radius: 0 0.7em 0.7em 0;
}

.notification_popup .stv-radio-button:checked + label {
	background-color: #7225f3;
	color: #fff !important;
}

.notification_popup .grey {
	color: #acb2b8;
}

.notification_popup .main-title {
	font-family: "Lato", sans-serif;
	font-weight: 900;
	line-height: 1.1 !important;
}

h1.page-title {
	font-family: Lato, sans-serif;
	color: #7324f3;
	font-weight: 900;
	padding: 15px;
	font-size: 3rem;
	background-color: #ffffff;
	/*border: 1px dashed;*/
	border-radius: 3px;
	margin-bottom: 20px;
}

.notification_popup .vertical-align {
	display: flex;
	align-items: center;
}

/* overlay styles, all needed */
#new-modal {
	position: fixed;
	left: 0;
	right: 0;
	overflow: auto;
	top: 0;
	bottom: 0;
	display: none;
	z-index: 10000;
	-webkit-animation: fadeIn 0.1s;
	animation: fadeIn 0.1s;
}

.notification_popup .custom-modal-content {
	border: none;
	width: 100%;
	overflow: auto;
	height: 100%;
	top: 0;
	left: 0;
	z-index: 9999;
	position: relative;
	display: none;
	border-radius: 0;
}

.notification_popup .custom-modal-sub-container {
	margin: auto;
	border-radius: 0;
	display: block;
	border: none;
	width: 1060px;
	position: relative;
	top: 70px;
	left: 531px;
	-webkit-transform: translate(-50%, -50%);
	-moz-transform: translate(-50%, -50%);
	transform: translate(-50%, -50%);
	box-shadow: 0 54px 20px 0 rgba(0, 0, 0, 0.3) !important;
}

.notification_popup .custom-modal-title {
	text-align: center;
	color: #404040;
	padding: 22px;
	background: #fff;
	font-size: 17px;
	font-weight: 400;
	border-bottom: 1px solid #e3e9ee;
	width: 100%;
	border-top-left-radius: 7px;
	border-top-right-radius: 7px;
}

.notification_popup .close-modal:hover {
	color: #5d5d5d;
}

.notification_popup .close-modal {
	position: absolute;
	right: 20px;
	color: #cecece;
	cursor: pointer;
}

.notification_popup .custom-modal-center-content {
	width: 100%;
	background: #f6f7f9;
	height: 605px;
	float: left;
	/*overflow: auto;*/
	margin-bottom: 35px;
}

.notification_popup .help-center-trigger > i {
	font-size: 1rem;
	color: rgba(42, 54, 74, 0.7) !important;
}

.notification_popup .help-center-trigger:hover {
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.18), 0 0 0 1px rgba(0, 0, 0, 0.19);
}

.notification_popup .help-center-trigger {
	display: inline;
	cursor: pointer;
	position: relative;
	border-radius: 15px;
	padding: 2px 7px;
	margin-left: 9px;
	color: rgba(42, 54, 74, 0.7) !important;
	background-color: #f8f8f8;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.notification_popup .btn-group > .btn-radio-option:last-child {
	margin-right: 0;
}

.notification_popup .btn-group > .btn-radio-option.option-selected {
	background-color: #6701ff29;
}

.notification_popup .btn-group > .btn-radio-option:hover {
	opacity: 0.6;
}

.notification_popup .btn-group > .btn-radio-option {
	color: #6704fd;
	border: 1px solid !important;
	margin-right: 5px;
	border-radius: 4px !important;
}

/*EVENTS FEED*/
.notification_popup .feed-container .button-container {
	margin-top: -15px;
}

.notification_popup .feed-container .feed-val {
	word-break: break-all;
}

.notification_popup .feed-container .feed-key {
	background-color: #f4f4f4;
	padding-left: 7px;
	padding-top: 2px;
	border-radius: 3px;
	padding-bottom: 2px;
	margin-bottom: 4px;
	color: black;
}

.notification_popup .events-container .event-item:not(:first-child) {
}

.notification_popup .events-container .event-item.open,
.notification_popup .events-container .event-item:hover {
	background: #fafafa;
	cursor: pointer;
}

.notification_popup .events-container .event-item-data.open {
	background-color: white;
}

.notification_popup .events-container .event-item i.fa {
	font-size: 21px;
}

.notification_popup .events-container .event-item {
	padding: 15px;
	border: 1px solid #eee;
	border-bottom: 0;
	border-top-left-radius: 3px;
	border-top-right-radius: 3px;
}

/*PLATFORMS*/
.notification_popup .platform-container:hover {
	border: 2px solid #7a25f3;
	cursor: pointer;
	opacity: 0.8;
}

.notification_popup .platform-container.selected-platform .platform-title {
	color: #7a25f3 !important;
}

.notification_popup .platform-container.selected-platform {
	background-color: #6701ff12;
	border: 2px solid #7a25f3;
}

.notification_popup .platform-container:hover .platform-title {
	color: #7a25f3 !important;
}

.notification_popup .webhook-platform {
	margin-top: 20px;
}

.notification_popup .platform-container {
	text-align: center;
	border: 2px solid #e2e4e8;
	border-radius: 5px;
	margin: auto;
	padding: 10px;
}

.notification_popup .platform-container .platform-title {
	color: #b3b4b7;
}

.notification_popup .platform-container img.platform-icon {
	width: 65px;
	margin-top: -10px;
}

#progress-percentage {
	text-align: center;
	display: block;
	width: 100%;
	font-size: 1rem;
	font-weight: 800;
	color: #575757;
	margin-left: auto;
	margin-right: auto;
	position: absolute;
}

.notification_popup .current-plan-title #progress-percentage {
	font-size: 0.8rem;
	padding-right: 14px;
}

.notification_popup .current-plan-title .plan-name {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	word-wrap: unset;
	max-width: 250px;
}

.notification_popup .current-plan-title {
	color: #9f9f9f !important;
	font-size: 0.8rem !important;
	position: relative;
	padding-right: 14px;
	word-wrap: unset;
	width: 205px;
	max-width: 250px;
}

.notification_popup .analytics-top .card .card-body {
	padding-bottom: 20px;
	padding-top: 20px;
}

.notification_popup .analytics-top .card .stats-value {
	color: #273245;
	font-size: 1.8em;
	font-weight: 800;
}

.notification_popup .analytics-top .card .stats-label {
	color: #9aa0ac;
	font-size: 0.9rem;
}

.notification_popup .analytics-top .stat-col:not(:first-child) {
	border-left: 1px solid #f6f6f6;
}

.notification_popup .analytics-top .stat-col {
	border-bottom: 1px solid #f6f6f6;
}

.notification_popup .analytics-top .card {
	box-shadow: none;
	border: none;
}

.notification_popup .analytics-top {
	margin-top: 10px;
	background-color: white;
	margin-bottom: -15px;
}

.notification_popup .analytics-top .analytics-bar .bar-content .bar-item {
	width: auto;
	display: inline-block;
	float: right;
}

.notification_popup .analytics-top .analytics-bar .bar-content .bar-title.sm {
	font-weight: normal;
	font-size: 1rem;
	color: #64696c;
}

.notification_popup .stats-label > i.fa-question-circle {
	cursor: pointer;
}

.notification_popup .analytics-top .analytics-bar .bar-content .bar-title {
	padding: 3px;
	font-weight: 700;
	font-size: 1.08rem;
	color: #64696c;
	float: left;
}

.notification_popup .analytics-top .analytics-bar .bar-content {
	padding: 10px;
	display: block;
	width: 100%;
}

.notification_popup .analytics-top .analytics-bar {
	border-bottom: 1px solid #f6f6f6;
}

.notification_popup .ps-btn-outline:hover {
	box-shadow: 0px 3px 7px 1px #ebebeb;
	color: #979797;
	background-color: #ffffff;
	border-color: #b4b4b4;
}

.notification_popup .ps-btn-outline {
	color: #979797;
	background-color: #ffffff;
	border-color: #b4b4b4;
}

.notification_popup .aff-stat > .aff-container > .num {
	line-height: 1.7rem;
	font-size: 1.7rem;
	font-weight: 800;
	color: #616161;
}

.notification_popup .aff-stat > .aff-container > .name {
	line-height: 1.8rem;
	font-size: 0.9rem;
}

.notification_popup .aff-stat:first-child {
	/*border-left: none;*/
}

.notification_popup .aff-stat.balance .aff-container {
	background-color: #f9f9f9 !important;
	border-radius: 3px;
}

.notification_popup .aff-stat {
	text-align: center;
	/*border-left: 1px solid #e9eced;*/
}

.notification_popup .aff-stat p {
	line-height: 1.4rem !important;
}

.notification_popup .aff-stat .aff-container {
	padding: 20px 10px;
	text-align: center;
	background-color: #ffffff;
}

#pp-logo img {
	width: 60px;
	position: absolute;
	right: 47px;
	top: 46px;
}

#aff-payout .wizard-btn.sm {
	margin-left: 0;
	width: 100px !important;
}

#aff-payout ps-text-input input.ps-input {
	text-align: left;
	padding-left: 10px;
	padding-right: 80px;
}

#analytics-container {
	margin-top: 35px;
	margin-bottom: 5rem;
}

/*DATEPICKER*/
.notification_popup .daterangepicker .ranges {
	font-family: "Lato", sans-serif;
}

.notification_popup .daterangepicker .calendar-table tbody td {
	font-weight: 100;
	font-size: 11pt;
}

.notification_popup .daterangepicker td.active,
.notification_popup .daterangepicker td.active:hover {
	background-color: #7b05f7;
	color: #fff;
}

.notification_popup .daterangepicker .input-mini.active {
	border: 1px solid #7b05f7;
	border-radius: 4px;
}

.notification_popup .daterangepicker td.end-date {
	background-color: #7b05f7 !important;
	border-radius: 0px 4px 4px 0px !important;
}

.notification_popup .daterangepicker td.in-range {
	background-color: #dcd4f8;
	border-radius: 0;
}

.notification_popup .daterangepicker .range_inputs .btn-success {
	color: #fff;
	background-color: #29b461;
	border-color: #1bb25b;
}

.notification_popup .daterangepicker .ranges li:hover {
	background-color: #7b05f7;
	border: 1px solid #6e05db;
	color: #fff;
}

.notification_popup .daterangepicker .ranges li.active {
	background-color: #7b05f7;
	border: 1px solid #6e05db;
	color: #fff;
}

.notification_popup .daterangepicker .ranges li {
	color: #7b05f7;
}

.notification_popup .purple-text {
	color: #7b05f7 !important;
}

.notification_popup .lifetime-deal span.bdg {
	font-size: 9pt;
	padding: 5px 8px;
	margin-left: 3px;
	margin-right: 0;
}

span.bdg {
	background-color: rgba(24, 202, 97, 0.1);
	color: rgb(16, 221, 100);
	padding: 5px 15px;
	border-radius: 25px;
	font-weight: bold;
	margin-right: 10px;
}
</style>
