<template>
  <b-modal
    ignore-enforce-focus-selector=".mce-container"
    ref="modal-sm-d"
    size="sm"
    @hide="close"
    hide-footer
  >
    <template v-slot:modal-title>
      <h5 class="modal-title">
        {{ header }}
      </h5>
    </template>
    <template>
      <div class="modal-body">
        <div class="modal-body--inner">
          <h4>{{ header2 }} following contacts</h4>
          <div class="avatar-group">
            <template v-if="!contacts || contacts.length <= 0">
              <span style="font-size: 15px; color: #188bf6"
                >No contact selected</span
              >
            </template>

            <template v-else v-for="(contact, index) in contacts">
              <Avatar
                v-if="index < 10"
                :contact="contact"
                :include_name="false"
              />
              <template v-if="totalContacts >= 10">
                <span
                  v-if="index === contacts.length - 1"
                  style="margin-top:10px; font-size:19px,margin-left:10px; color:#188bf6"
                >
                  {{ totalContacts - 10 }} &nbsp;more contacts...
                </span>
              </template>
            </template>
          </div>
          <div
            class="card"
            v-if="(showRateWarning || showDisclaimer) && step === 1"
          >
            <div class="px-2 --blue info-blue margin-30">
              <div
                class="d-flex align-items-top justify-content-start mx-1 my-4"
                style="line-height: 1.5"
                v-if="bulkActionsCount"
              >
                <span><i class="fa fa-exclamation-triangle mr-4"></i></span>
                <span
                  >Please note you already have {{ bulkActionsCount }} "{{
                    header
                  }}" operations in progress
                </span>
              </div>
              <div
                class="d-flex align-items-top justify-content-start mx-1 my-4"
                style="line-height: 1.5"
                v-if="showDisclaimer"
              >
                <span><i class="fa fa-exclamation-triangle mr-4"></i></span>
                <span>
                  Please note The actions will be performed over a period of time.
                  You can track the progress on the bulk actions page.
                </span>
              </div>
            </div>
            <div
              class="modal-buttons d-flex align-items-center justify-content-between px-2"
            >
              <UIButton use="outline" data-dismiss="modal" @click.prevent.stop="close">
                Cancel
              </UIButton>
              <div class="d-inline-flex">
                <UIButton
                  use="secondary"
                  @click.prevent="step = 2"
                  :loading="sending"
                >
                  Ok, proceed
                </UIButton>
              </div>
            </div>
          </div>
          <div class="hl_rules--wrap" v-if="step === 2">
            <div class="card">
              <div class="card-body">
                <div>
                  <slot name="action-slot"></slot>
                </div>
                <hr />
                <div class="form-row mx-1 my-2" v-if="allowScheduling">
                  <b-form-group>
                    <b-form-radio-group
                      v-model="schedule.dripMode"
                      :options="modes"
                    >
                    </b-form-radio-group>
                  </b-form-group>
                </div>
                <div class="py-1 text-nowrap">
                  <UITextLabel>Action</UITextLabel>
                </div>
                <div class="form-row">
                  <div class="form-group col-12">
                    <UITextInput
                      type="text"
                      placeholder="Enter a description for the action (to be shown in tracking report)"
                      v-model="actionSpecs.note"
                      v-validate="'required'"
                      name="description"
                      maxlength="50"
                    />
                    <span v-show="errors.has('description')" class="error">
                      Enter a name for the action
                    </span>
                    <!-- no-button-now -->
                  </div>
                </div>
                <AllAtScheduler
                  :schedule="schedule"
                  :modes="modes"
                  v-if="
                    schedule && schedule.dripMode === 'processAllAtSchedule'
                  "
                ></AllAtScheduler>
                <BulkScheduler
                  :schedule="schedule"
                  :modes="modes"
                  v-if="schedule && schedule.dripMode === 'drip'"
                ></BulkScheduler>
                <div
                  class="py-1 px-2 my-2 info-blue --blue"
                  v-if="
                    schedule &&
                    schedule.dripMode === 'drip' &&
                    schedule.dripMode === 'processAllAtSchedule'
                  "
                >
                  <div class="mx-3 my-1">
                    <span class="input-group-addon mx-1">
                      <i class="fa fa-exclamation-triangle"></i>
                    </span>
                    <span class="mx-1 my-2"
                      >The action will run from the oldest contact to the newest
                      contact</span
                    >
                  </div>
                </div>
              </div>
              <!-- <div class="card-body">
                    <div class="header-underline">
                      <div class="d-inline-block">
                        <ul class="header-ul">
                          <li :class="{active: (highlightList === 'action')}">
                              <a @click.prevent="highlightList = 'action'">Action</a>
                          </li>
                          <li :class="{active: (highlightList === 'schedule')}" v-if="allowScheduling === true">
                              <a @click.prevent="highlightList = 'schedule'">Schedule</a>
                          </li>
                        </ul>
                      </div>
                    </div>
                    <div :hidden="highlightList !== 'action'" >
                      <slot name="action-slot"></slot>
                    </div>
                  <BulkScheduler :hidden="highlightList !== 'schedule'" :schedule="schedule" v-if="allowScheduling === true"></BulkScheduler>
                </div> -->
            </div>

            <div
              class="modal-buttons d-flex align-items-center justify-content-between px-2">
              <UIButton use="outline" data-dismiss="modal" @click.prevent.stop="close">
                Cancel
              </UIButton>
              <div class="d-inline-flex">
                <UIButton
                  v-if="operation"
                  use="secondary"
                   @click.prevent="operate"
                  :loading="sending">
                  {{ submitHeader }}
                </UIButton>
              </div>
            </div>
          </div>
          <div class="hl_rules--wrap" v-if="step === 3">
            <div class="py-1 px-2 my-4">
              <div class="--blue">
                <router-link
                  :to="{
                    name: 'smart_list',
                    params: { smart_list: 'bulk_actions' },
                  }"
                  @click.native="close(true)">
                  Your bulk action has been scheduled. Click here to check the
                  progress.
                </router-link>
              </div>
            </div>
            <div
              class="modal-buttons d-flex align-items-center justify-content-end px-2">
              <div class="d-inline-flex">
                <MoonLoader v-if="showWait" color="#188bf6" size="24px" radius="50%" />
                <UIButton v-else
                  use="secondary"
                  @click.prevent.stop="close(true)">
                  Ok
                </UIButton>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </b-modal>
</template>

<script lang="ts">
// Tips for MCE Editor Non editable inputs
// Bootstrap sucks out all focus from tinyMCEEditor popups like adding links or images that causes issue where inputs feel disabled.
// Either use ignore-enforce-focus-selector=".mce-container" on   <b-modal>
// or add attribute no-enforce-focus on   <b-modal>
// See 'Keyboard Navigation' section in https://bootstrap-vue.org/docs/components/modal

import Vue from 'vue'
import {
  Contact,
  User,
  Tag,
  ImportRequest,
  ImportType,
  ImportStatus,
} from '@/models'
import {
  IBulkAction,
  ContactDeletion,
  ContactExports,
  StatusCheck,
} from './vm/action_vm'
import { UxMessage } from '@/util/ux_message'
import { IBulkOpportunitySpecs, IOpSpecs } from './server/bulkreq_interfaces'
import BulkScheduler from './BulkScheduler.vue'
import { BulkSchedule } from './server/bulk_schedule'
import AllAtScheduler from './AllAtScheduler.vue'

const Avatar = () => import('../../components/Avatar.vue')
const MoonLoader = () => import('@/pmd/components/MoonLoader.vue')
const PipelineAction = () =>
  import('@/pmd/components/triggers/PipelineAction.vue')
import { ScheduledBulkActions } from './server/bulkreq_api'
import { BulkRequestV2 } from './server/bulk_req_v2'

let unsubscribeImportRequest: () => void

export default Vue.extend({
  name: 'BulkActionModal',
  provide() {
    return { parentValidator: this.$validator }
  },
  props: {
    type: String,
    header: String,
    header2: String,
    header3: String,
    submitHeader: String,
    startOnLabel: { type: String, default: 'Start On' },
    dripQuantityLabel: { type: String, default: 'Batch Quantity' },
    dripGapLabel: { type: String, default: 'Repeat After' },
    buttonProcesAllLabel: { type: String, default: 'Process all at once' },
    buttonAllAtScheduleLabel: {
      type: String,
      default: 'Process all at schedule',
    },
    buttonProcessScheduleLabel: {
      type: String,
      default: 'Process in drip mode',
    },
    contacts: Array as () => Contact[],
    operation: Object as () => IBulkAction,
    totalContacts: Number,
    action: Object as () => IOpSpecs,
    allowScheduling: Boolean,
    showRateWarning: String,
    showDisclaimer: String,
  },
  components: {
    Avatar,
    MoonLoader,
    PipelineAction,
    BulkScheduler,
    AllAtScheduler,
  },
  inject: ['uxmessage'],
  data() {
    return {
      currentLocationId: '',
      sending: false,
      status: this.operation.status || new StatusCheck(),
      // action: { type : 'create_opportunity',  opType:  'bulk-ops'} as IBulkOpportunitySpecs,
      highlightList: 'action',
      schedule: new BulkSchedule(),
      actionSpecs: this.action as IOpSpecs,
      step: this.showDisclaimer || this.showRateWarning ? 1 : 2,
      bulkActionsCount: 0,
      showWait: false,
      modes: [
        { value: 'none', text: this.buttonProcesAllLabel },
        { value: 'processAllAtSchedule', text: this.buttonAllAtScheduleLabel },
        { value: 'drip', text: this.buttonProcessScheduleLabel },
      ],
    }
  },
  created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
  },
  async mounted() {
    //this.schedule = new BulkSchedule()
    if (this.action) {
      await this.fetchScheduledBulkActionsLength(this.action.opType)
    }
    await this.$nextTick()
    this.showPopup()
  },
  methods: {
    async operate() {
      try {
        if (this.sending === true) return false
        const result = await this.$validator.validateAll()
        if (!result) return false
        this.sending = true
        const authUser = await this.$store.dispatch('auth/get')
        await this.operation.action(
          authUser.userId,
          {
            opSpecs: this.action,
            sprintSchedule: this.schedule.getPersistence(),
            useDynamicList: false,
          },
          (status: string) => {
            this.sending = false
            if (status !== 'success') {
              this.showError(status)
            } else {
              //this.close(true)
              this.showWait = true
              this.step = 3
              setTimeout(() => this.showWait = false, 2500);
            }
          }
        )
      } catch (e) {
        this.sending = false
        this.showError((e && e.message) || 'unknown error', e)
        //this.$emit('onError',e);
      }
    },
    showError(err: string, stack?: string) {
      this.uxmessage(UxMessage.errorType(err, stack))
    },
    close(processed: boolean = false) {
      this.hidePopup()
      this.$emit('closed', this.step === 3 || processed)
    },
    showPopup() {
      if (this.$refs['modal-sm-d']) this.$refs['modal-sm-d'].show()
    },
    hidePopup() {
      if (this.$refs['modal-sm-d']) this.$refs['modal-sm-d'].hide()
    },
    async fetchScheduledBulkActionsLength(
      opType: string,
      status: string = 'Scheduled'
    ): Promise<any> {
      try {
        const filters = {
          status,
          location_id: this.currentLocationId,
          op_type: opType,
        }
        let items = await ScheduledBulkActions.getList(filters)
        if (items && items.length)
          items = items.filter(
            (a: BulkRequestV2) => a.opSpecs && a.opSpecs.opType === opType
          )
        this.bulkActionsCount = (items && items.length) || 0
      } catch (err) {
        console.log(err)
      }
    },
  },
  computed: {
    percent(): { [key: string]: any } {
      return { width: (this.status.updated / this.status.total) * 100 + '%' }
    },
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
    },
  },
})
</script>
<style scoped>
.modal .modal-body {
  padding: 10px 15px;
}
.card-body {
  padding: 2px;
}
.hl_rules--wrap {
  margin-top: 5px;
}
.form-input-dropdown {
  position: absolute;
  will-change: transform;
  top: 0px;
  left: 0px;
  transform: translate3d(0px, 51px, 0px);
  min-width: 0;
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
}

.header-underline {
  display: inline-block;
  border-bottom: 1px solid #d5dde1;
  list-style: none;
  width: 100% !important;
  margin-bottom: 20px;
}
.customized-header {
  display: inline-block;
  list-style: none;
  padding: 0;
  width: 100% !important;
  padding-right: 15px;
  padding-left: 15px;
  margin: 0px;
}

@media (min-width: 768px) {
  ul.header-ul {
    display: inline-flex;
    align-items: center;
    margin-bottom: 0;
    overflow-x: auto;
  }

  ul.header-ul li a {
    padding-left: 5px;
    padding-right: 5px;
    border-bottom: 4px solid transparent;
  }
}

ul.header-ul li a {
  display: block;
  color: #607179;
  height: 40px;
  min-width: 40px;
  text-align: center;
}

ul.header-ul li.active a {
  color: #2a3135;
  border-color: #188bf6;
}

ul.header-ul {
  list-style: none;
  padding: 0;
}

ul.header-ul li:not(:last-child) {
  margin-right: 20px;
}
.margin-30 {
  margin: 30px !important;
}
</style>
