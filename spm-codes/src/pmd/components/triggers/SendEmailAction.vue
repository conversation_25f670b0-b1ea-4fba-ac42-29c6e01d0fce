<template>
	<div>
		<div class="row">
			<div class="col-sm-6">
				<div class="form-group">
					<UITextLabel>
            From
            <a class="hl_help-article" v-if="showFromWarning && user.type == 'agency'" v-b-tooltip.hover title="SMTP providers has some limitations. Click here to know more." href="https://youtu.be/eltAOMf3AUA" target="blank">
				<i class="fa fa-exclamation-triangle text-warning"></i>
			</a>
          </UITextLabel>
					<UITextInputGroup
						type="text"
						class="msgsndr3"
						v-model="from"
						v-validate="{required: !showFromWarning, handlebars: true}"
						data-vv-validate-on="input"
						data-vv-as="from"
						name="msgsndr3"
						data-lpignore="true"
						autocomplete="msgsndr3"
						@input="setValue('from', from)"
						:error="errors.has('msgsndr3')"
						:errorMsg="errors.first('msgsndr3')"
					/>
				</div>
			</div>
			<div class="col-sm-6">
				<div class="form-group">
					<UITextInputGroup
						type="text"
						class="msgsndr3"
						v-model="to"
						label="To"
						v-validate="'required|handlebars'"
						data-vv-validate-on="input"
						data-vv-as="to"
						name="tomsgsndr3"
						autocomplete="tomsgsndr3"
						@input="setValue('to', to.length ? to.split(',').map(e => e.trim()) : [])"
						:error="errors.has('tomsgsndr3')"
						:errorMsg="errors.first('tomsgsndr3')"
					/>
				</div>
			</div>
		</div>
		<div class="form-group">
			<UITextInputGroup
				type="text"
				label="Subject"
				v-model="subject"
				v-validate="'required|handlebars'"
				data-vv-validate-on="input"
				data-vv-as="subject"
				name="subject"
				@input="setValue('subject', subject)"
				:error="errors.has('subject')"
				:errorMsg="errors.first('subject')"
			/>
		</div>
		<div class="form-group">
			<label>Message</label>
			<div class="custom-dropdown-menu">
				<NestedMenu
				class="my-2"
				v-show="displayMenu"
				:items="menuItems"
				:title="'Merge fields'"
				v-on:MenuSelected="menuItemSelected"
				:fromAction="true"
			/>
			<editor
				:init="editorOptions"
				v-model="html"
        :id='getRandomId'
        ref='editor'
				name="emailEditor"
				v-validate="{handlebars:[true]}"
				data-vv-validate-on="input"
			></editor>
			</div>
		</div>
		<span v-show="errors.has('emailEditor')" class="--red">{{errors.first('emailEditor')}}</span>
		<div class="form-group form-btn-group">
			<div style="display: inline-block;position: relative;">
				<UIButton
					type="button"
					use="outline"
					@click.prevent="preview"
					:loading="sendingPreview"
				>Send Test Email</UIButton>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
import Editor from '@tinymce/tinymce-vue';
import { mapState } from 'vuex';
import { UserState } from '../../../store/state_models';
import {
	User,
  CustomField
} from "@/models";
import NestedMenu from '@/pmd/components/NestedMenu.vue'
import { EventBus } from '@/models/event-bus'
import { FilterMergeTags } from '../../../util/filter_merge_tags'

const TagComponent = () => import('../customer/TagComponent.vue');

export default Vue.extend({
	props: ['action', 'editorOptions', 'triggerType', 'showFromWarning', 'displayMenu'],
	components: { TagComponent, 'editor': Editor, NestedMenu },
	data() {
		return {
			html: '',
			subject: '',
			from: '',
			to: '',
			sendingPreview: false,
			currentLocationId: '',
      menuItems: [] as any[],
      customFields: [] as { [key: string]: any }[]
    }
  },
	methods: {
	setValue(field: string, value: string) {
      Vue.set(this.action, field, value)
      this.$emit('update:action', this.action)
	},
    menuItemSelected(item) {
		console.log("From method" + item);
	  EventBus.$emit('customFieldSelected', item);
	},
		async preview() {
			const result = await this.$validator.validateAll();
			if (!result) {
				return false;
			}

			this.sendingPreview = true;

			let data: { [key: string]: any } = {
				"location_id": this.currentLocationId,
				"email": this.to,
				"message": this.html,
				"from_address": this.from,
				"subject": this.subject,
				"user_id": this.user.id,
				"type": "email"

			}

			try {
				let response = await this.$http.post('/message/preview', data);
			} catch (err) {
				console.error(err);
			}

			this.sendingPreview = false;
		}
  },
	computed: {
		...mapState('user', {
			user: (s: UserState) => {
				return s.user ? new User(s.user) : undefined;
			},
    }),
    getRandomId() {
      return 'send_email_' + Math.random().toString(36).substring(7);
    }
	},
	async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id;
    const fields = await CustomField.getByLocationIdAndType(this.currentLocationId, 'contact');
    this.customFields = fields.map(field => {
      return { text: field.name, value: `{{${field.fieldKey}}}` }
    });
    this.menuItems = await FilterMergeTags.syncList(this.currentLocationId, this.customFields, this.menuItems)
    this.menuItems = await FilterMergeTags.resyncList(this.menuItems, this.customFields, this.triggerType)
		if (this.action.from) this.from = this.action.from;
		if (this.action.to) this.to = this.action.to.join(', ');
		if (this.action.html) this.html = this.action.html;
		if (this.action.subject) this.subject = this.action.subject;
  },
  watch: {
    async triggerType() {
      this.menuItems = await FilterMergeTags.resyncList(this.menuItems, this.customFields, this.triggerType);
    },
    html() {
      this.setValue('html', this.html);
    },
    action() {
      if (this.action.from) this.from = this.action.from;
      if (this.action.to) this.to = this.action.to.join(', ');
      if (this.action.html) this.html = this.action.html;
      if (this.action.subject) this.subject = this.action.subject;
    }
  }
})
</script>
