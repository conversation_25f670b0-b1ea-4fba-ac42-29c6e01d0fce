<template>
  <div class="w-100">
    <div
      class="w-100 d-flex py-2"
      style="border-bottom: 1px solid rgba(0,0,0,0.1);"
    >
      <div
        class="w-25 d-flex align-items-center"
        style="padding-left: 10px; min-width: 25%;"
      >
        <i
          class="mr-3 pointer"
          :class="{ 'show-active': storeFolders }"
          @click="toggleShow"
          >&#9658;</i
        >
        <div
          class="cursor-pointer"
          style="font-weight: 500;"
          @click="toggleShow"
        >
          <span class="mr-1">{{ folder.name }}</span>
          <span>({{ campaigns.length }})</span>
        </div>
      </div>
      <div class="w-75 d-flex">
        <span style="min-width: 20px;"></span>
        <span style="min-width: 20px;"></span>
        <span style="min-width: 20px;"></span>
        <span style="min-width: 20px;"></span>
        <span style="min-width: 20px;"></span>
        <span style="min-width: 162px;"></span>
      </div>
      <div class="w-25" style="min-width: 25%; text-align: center;">
        <i
          class="icon icon-trash --light mr-3"
          style="cursor: pointer;"
          v-if="allowEdit"
          @click="deleteFolder(folder.id, folder)"
        ></i>
        <i
          class="icon icon-edit --light"
          style="cursor: pointer;"
          v-if="allowEdit"
          @click="renameFolder(folder.name, folder)"
        ></i>
      </div>
    </div>
    <div v-show="storeFolders">
      <CampaignListItem
        v-for="campaign in campaigns"
        :campaign="campaign"
        :key="campaign.id"
        :folders="folders"
      />
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import { UserState } from '../../../store/state_models'
import { Campaign, Folder, User } from '../../../models/'
import firebase from 'firebase/app'
const CampaignListItem = () =>
  import('../../components/marketing/CampaignListItem.vue').then(m => m.default)

export default Vue.extend({
  props: ['folder', 'folders', 'campaigns', 'storeFolders'],
  components: { CampaignListItem },
  data() {
    return {
      currentLocationId: '',
      newFolderName: ''
    }
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.currentLocationId = id
    }
  },
  computed: {
    allowEdit(): boolean {
      return this.user && this.user.permissions.campaigns_read_only !== true
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    })
  },
  methods: {
    async saveRename(newName: string, folderId: string, folder: Folder) {
      // this.toggleToRename()
      if (!newName) return

      // Filtering campaigns
      let filtered = this.filterByFolderId(folderId)

      if (filtered) {
        // Update campaign's folder_name field
        filtered.forEach(async (campaign: Campaign) => {
          let campaignToRename = new Campaign(campaign)
          await campaignToRename.ref.update({
            folder_name: newName
          })
        })
      }

      // Update folder's name
      folder.name = newName
      await folder.save()
    },
    renameFolder(name: string, folder: Folder) {
      this.$emit('renameFolder', name, folder)
    },
    async deleteFolder(folderId: string, folder: Folder) {
      if (!folder) return
      if (!confirm('Are you sure you want to delete this folder?')) return

      let filtered = this.filterByFolderId(folderId)
      if (filtered) {
        // Remove campaign's folder_name field
        filtered.forEach(async campaign => {
          let campaignToRename = new Campaign(campaign)
          await campaignToRename.ref.update({
            folder_name: firebase.firestore.FieldValue.delete(),
            folder_id: firebase.firestore.FieldValue.delete()
          })
        })
      }

      await folder.delete()
    },
    toggleShow() {
      let event = {
        folderId: this.folder.id,
        locationId: this.currentLocationId
      }
      this.$store.dispatch('campaign_folder/updateFolder', event)
    },
    filterByFolderId(folderId: string) {
      return this.campaigns.filter(obj => obj.folder_id === folderId)
    }
  },
  created() {}
})
</script>

<style lang="css" scoped>
.show-active {
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}
.color-blue {
  color: #188bf6;
}
.folder-rename {
  background: none;
  border: none;
  border-bottom: 1px solid rgb(224, 224, 224);
  width: 80%;
}
textarea:focus,
input:focus {
  outline: none;
}

.cursor-pointer {
  cursor: pointer;
}
</style>
