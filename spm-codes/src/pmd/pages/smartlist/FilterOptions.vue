<template>
<div class="d-block">
    <div v-if="showTitle" style="max-width:100;" class="py-1"><span>{{filter.title}}</span></div>
    <div class="d-block mx-1" v-if="filter">
      <b-form-group v-if="filter.options">
           <b-form-radio
              v-for="o in filter.options"
              v-bind:key="o.id"
              v-model="selected" :value="o"
              :name="o.filterName">
              {{o.conditionUX}} <!-- - {{o.selected}} -->
              <div class="py-1" v-if="o === selected && o.listFetch && o.listFetch2">
                <FilterMultiSelections v-on:inputChange="publish" :bus="bus" :option="o" ></FilterMultiSelections>
              </div>
              <div v-else-if="o === selected && selected.isDateRange">
                <FilterDateRange v-on:inputChange="publish" :bus="bus" :option="o"></FilterDateRange>
              </div>
              <div class="py-1" v-else-if="o === selected && o.listFetch && o.isAutoComplete">
                <ArrayInputAutoComplete v-on:inputChange="publish" :bus="bus" :option="o"  ></ArrayInputAutoComplete>
              </div>
              <div class="py-1" v-else-if="o === selected && o.listFetch && !o.isAutoComplete">
                <ArrayInputDropDown v-on:inputChange="publish" :bus="bus" :option="o" :listFetchParams='listFetchParams'></ArrayInputDropDown>
              </div>
              <div class="py-1" v-else-if="o === selected && selected.firstValueMandatory && !selected.secondValueMandatory">
                <FilterInput v-on:inputChange="publish" :bus="bus" :option="o"></FilterInput>
              </div>
              <div class="py-1" v-else-if="o === selected && selected.secondValueMandatory">
                <FilterMultiInput  v-on:inputChange="publish" :bus="bus" :option="o"></FilterMultiInput>
              </div>

          </b-form-radio>
      </b-form-group>
    </div>
    <template v-if="selected && selected.nextFilters">
    <div  v-for="item in selected.nextFilters"  v-bind:key="item.filterName">
      <Filter-Options showTitle v-if="!item.hidden"
                      :filter="item"
                      :listFetchParams='calcParams'
                      v-on:filterUpdate="childUpdate"
                      v-on:filterRemove="childRemove"
                      v-model="item.expandedUX">
    </Filter-Options>
    </div>
    </template>
</div>
</template>

<script lang="ts">

import Vue from 'vue';
import {IFilterOption} from './vm/option_vm'
import {IFilter} from './vm/filter_vm'
import FilterInput from './FilterInput.vue'
import FilterMultiInput from './FilterMultiInput.vue'
import FilterExpander from './FilterExpander.vue'
import ArrayInputDropDown from './ArrayInputDropDown.vue'
import ArrayInputAutoComplete from './ArrayInputAutoComplete.vue'
import FilterMultiSelections from './FilterMultiSelections.vue'
import FilterDateRange from './FilterDateRange.vue'
import * as moment from "moment-timezone";

export default Vue.extend({
  name: 'Filter-Options',
  props : {
    bus : Vue,
    filter : IFilter,
    showTitle: Boolean,
    listFetchParams: {},
  },
  components :  {
    FilterInput,
    FilterMultiInput,
    FilterMultiSelections,
    FilterExpander,
    ArrayInputDropDown,
    ArrayInputAutoComplete,
    FilterDateRange
  },
  data () {
    return {
      raiseSelectionEvent:false,
      waitTime:500,
      selected:undefined,
      debouncer:undefined as NodeJS.Timer | undefined,
      calcParams: null
    };
  },
  computed: {
     isExpanded(): Boolean{
       return this.filter.expandedUX || false ;
     },
    //  calcParams(){

    //     else return null;
    //  },
     expand: {
			get() { return this.value },
      set(v) {
          //alert(v);
          this.$emit("input", v);
          this.expandChange();
       }
		}
  },
  watch : {
    selected(n) {
      if(this.raiseSelectionEvent === false) return; //otherwise it runs es search two times when list changes.
      if (this.filter && n) {
        if (n.isDateRange) {
          n.firstValue = moment().format('YYYY-MM-DD hh:mm a')
          n.secondValue = moment().format('YYYY-MM-DD hh:mm a')
        }
        this.filter.selectedOption = n;
        this.publish();
      }
    },
  },
  mounted(){
    if (this.filter) {
      this.selected = this.filter.selectedOption;
      this.setCalcParams();
      this.$nextTick(()=> this.raiseSelectionEvent = true);
    }
  },
  methods: {
    setCalcParams(){
      if (this.selected && this.selected.nextFilters && this.selected.firstValue) {
        this.calcParams = {filterBy: this.selected.firstValue}; // for updating sub filters
      }
    },
    async expandChange(){
      this.$emit('expanding',this.filter)
    },
    async publish(){
      this.setCalcParams();
      this.$emit('filterUpdate',this.filter);
    },
    async childUpdate(item: IFilter){
      this.$emit('filterUpdate',item)
    },
    childRemove(item: IFilter){
       this.$emit('filterRemove',[item]);
    }
  },
})
</script>

<style scoped>

  .filter-option {
    padding:10px !important;
    padding-top :10px !important;
    padding-right:10px !important;
    padding-left:10px !important;
  }

  .bootstrap-select .btn.dropdown-toggle .filter-option {
    padding:10px !important;
    padding-top :10px !important;
    padding-right:10px !important;
    padding-left:10px !important;
  }

  .form-group{
    margin-bottom: 0px !important;
  }

</style>
