<template>
  <div class="card leader-board">
    <div class="card-body">
      <div style="width: 100%" class="d-flex justify-content-between align-items-center flex-wrap">
        <div style="font-size: 1.5rem; font-weight: 500;">Leaderboard</div>
        <b-dropdown class="ml-2" :text="parameter" size="sm">
            <b-dropdown-item v-for="parameter in leader_board_parameters" :key="parameter.value" @click="selectedParameter(parameter)">{{parameter.label}}</b-dropdown-item>
        </b-dropdown>
      </div>
      <div v-if="!isLoading" class="hideScrollbar leaders d-flex justify-content-center flex-wrap">
        <table class="table agent-reporting__leaderboard">
          <thead>
            <tr>
              <th>Rank</th>
              <th>Agent Name</th>
              <th>Points</th>
            </tr>
          </thead>
          <tbody v-if="data.length > 0">
            <tr v-for="agent in sortedData" :key="agent.assigned_to">
              <td>{{agent.rank}}</td>
              <td>{{agentName(agent.assigned_to)}}</td>
              <td>{{agent.count}}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div v-else style="height: 100%" class="d-flex justify-content-center align-items-center">
        <MoonLoader size="30px" radius="50%" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import MoonLoader from '../MoonLoader'

export default Vue.extend({
  components: {
    MoonLoader,
  },
  props: {
    isLoading: {
      type: Boolean,
      default: false
    },
    data: {}
  },
  data() {
    return {
      leader_board_parameters: [
        { label: 'Most Won Opportunities', value: 'most_wins' },
        { label: 'Most Opportunities', value: 'most_opportunities' }
      ],
      leader_board_parameter: 'most_wins',
      leader_board_label: 'Most Won Opportunities'
    }
  },
  computed:{
    parameter: function(){
      return this.leader_board_label.length > 10 ? this.leader_board_label.substring(0,10) + '...' : this.leader_board_label
    },
    sortedData: function(){
      this.data.sort((a, b) => b.count - a.count)
      let rankedData = []
      if(this.data.length > 0){
        let prev = this.data[0].count
        let currRank = 1
        rankedData = this.data.map(obj => {
          if(prev > obj.count){
            currRank += 1
            prev = obj.count
          }
          return {rank: currRank, ...obj}
        })
      }
      return rankedData
    }
  },
  created(){
    this.$emit('parameter-changed', 'most_wins');
  },
  methods: {
    selectedParameter(parameter) {
      if(this.leader_board_parameter === parameter.value) return
      this.leader_board_parameter = parameter.value
      this.leader_board_label = parameter.label
      this.$emit('parameter-changed', this.leader_board_parameter);
    },
    agentName(id) {
      for(let user of this.$store.state.users.users){
        if(user.id === id) return `${user.first_name} ${user.last_name}`
      }
      return 'Not Found or Deleted'
    }
  }
})
</script>

<style>
.leader-board .btn {
  background-color: #fff;
  color: black;
}
.leader-board .btn-secondary:not(:disabled):not(.disabled):active, .btn-secondary:not(:disabled):not(.disabled).active, .show > .btn-secondary.dropdown-toggle {
    color: #000;
    background-color: #fff;
    border-color: #fff;
}
.leader-board .card-body {
  max-height: 528px;
}
.leader-board .card-body .table td{
  color: #fff;
}
.leader-board .table thead tr th:nth-child(2) {
  text-align: left;
}
.leader-board .table tbody tr td:nth-child(2) {
  text-align: left;
}
.leaders {
    overflow-y: auto;
    height: 90%;
}
.leader .thumbnail {
  box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.75);
  height: 36px;
  width: 36px;
  background: #fff;
}
.leader-board{
  color: white;
  background-color: #005e47;
  background-image: linear-gradient(to bottom, #009688, #0c2d3e) !important;
}
.hideScrollbar::-webkit-scrollbar {
  display: none;
}
.hideScrollbar {
  -ms-overflow-style: none;
}
.table thead tr th {
  color: #84939a;
}
.agent-reporting__leaderboard tr th{
  color: rgba(255,255,255, 0.7) !important;
}
</style>
