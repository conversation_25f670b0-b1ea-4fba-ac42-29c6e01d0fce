<template>
   <div>
    <SideBarV2 v-if="getSideBarVersion === 'v2'"/>
    <SideBar v-else />
    <TopBar />
    <!-- Content -->
    <section class="hl_wrapper">
      <div class="hl_affiliate--header">
        <div class="container-fluid">
          <h2>Affiliate Portal</h2>
          <ul class="hl_affiliate--nav">
            <router-link :to="{name: 'affiliate-dashboard-frame'}" tag="li" active-class="active" exact>
              <a href="javascript:void(0);">Dashboard</a>
            </router-link>
            <template v-if="tipaltiBetaAccess">
              <router-link :to="{name: 'affiliate-tipalti-payment-details-frame'}" tag="li" active-class="active" exact>
                <a href="javascript:void(0);">Payment Details</a>
              </router-link>
              <router-link :to="{name: 'affiliate-tipalti-payment-history-frame'}" tag="li" active-class="active" exact>
                <a href="javascript:void(0);">Payment History</a>
              </router-link>
              <router-link :to="{name: 'affiliate-tipalti-invoice-history-frame'}" tag="li" active-class="active" exact>
                <a href="javascript:void(0);">Invoice History</a>
              </router-link>
            </template>
             <router-link :to="{name: 'affiliate-about-frame'}" tag="li" active-class="active" exact>
                <a href="javascript:void(0);">About</a>
              </router-link>
          </ul>
        </div>
      </div>
      <router-view/>
    </section>
  </div>
</template>

<script>
import Vue from 'vue'
import TopBar from '../../../components/TopBar.vue'
import SideBar from '../../../components/agency/SideBar.vue'
import SideBarV2 from '../../../components/sidebar/SideBar.vue'
import { User,Company } from '@/models'
export default {
  components: { TopBar, SideBar, SideBarV2 },
  data(){
    return {
      tipaltiBetaAccess: false,
    }
  },
  computed:{
    user() {
        const user = this.$store.state.user.user
        return user ? new User(user) : undefined
    },
    isLevelUpDay() {
			return this.$store.getters['LevelUpDayFlag/isfeatursActive']
		},
    getSideBarVersion() {
			return this.$store.getters['sidebarv2/getVersion']
		},
  },
  async created(){
    this.tipaltiBetaAccess = this.$store.state?.company?.company?.allowBetaAccess?.tipalti || false;
  }
}
</script>

<style lang="scss" scoped>

.hl_affiliate--header {
  background-color: #fff;
  margin-bottom: 0px;
  padding-top: 15px;
  border-top: 1px solid #f2f7fa;
}
@media (min-width: 768px) {
  .hl_affiliate--header {
    position: relative;
    -webkit-box-shadow: inset 2px 0 0 0 #f2f7fa,
      0 10px 10px 0 rgba(0, 0, 0, 0.01);
    box-shadow: inset 2px 0 0 0 #f2f7fa, 0 10px 10px 0 rgba(0, 0, 0, 0.01);
  }
}
@media (max-width: 767px) {
  .hl_affiliate--header {
    padding-top: 20px;
  }
}
.hl_affiliate--header h2 {
  font-size: 1.25rem;
  margin-bottom: 15px;
}
.hl_affiliate--nav {
  list-style: none;
  margin: 0;
  padding: 0;
}
@media (max-width: 767px) {
  .hl_affiliate--nav {
    padding-bottom: 10px;
  }
}
@media (min-width: 768px) {
  .hl_affiliate--nav {
    -webkit-transform: translateY(2px);
    -ms-transform: translateY(2px);
    transform: translateY(2px);
  }
}
@media (min-width: 768px) {
  .hl_affiliate--nav li {
    display: inline-block;
  }
  .hl_affiliate--nav li:not(last-child) {
    padding-right: 15px;
  }
}
@media (min-width: 992px) {
  .hl_affiliate--nav li:not(last-child) {
    padding-right: 30px;
  }
}
.hl_affiliate--nav li a {
  font-size: 0.875rem;
  color: #607179;
}
@media (min-width: 768px) {
  .hl_affiliate--nav li a {
    display: block;
    padding-bottom: 10px;
    border-bottom: 3px solid transparent;
  }
}
.hl_affiliate--nav li a:hover,
.hl_affiliate--nav li a:focus,
.hl_affiliate--nav li a:active {
  color: #188bf6;
}
.hl_affiliate--nav li.active a {
  color: #188bf6;
  font-weight: 500;
  border-color: #188bf6;
}

</style>
