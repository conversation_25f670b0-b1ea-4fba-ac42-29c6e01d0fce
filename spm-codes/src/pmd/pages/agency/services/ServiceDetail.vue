<template>
	<div>
		<SideBar/>
		<TopBar/>

		<section class="hl_wrapper">
            <div class="page-error" v-if="errMsg">
                {{errMsg}}
            </div>
            <section class="hl_wrapper--inner hl_services-details" id="services-details" v-else-if="service.name">
                <div class="container-fluid">

                    <div class="hl_services--detail">
                        <div class="card">
                            <div class="card-heading">
                            <h2>
                                <i :class="`card-icon --${service.icon_bgcolor_name || 'blue'}`">
                                    <img :src="service.icon_url">
                                </i>
                                {{service.name}}
                            </h2>
                            <!-- <button type="button" class="btn btn-success" data-toggle="modal" data-target="#services-add-location-modal">Add to a location</button> -->
                            </div>
                            <div class="card-body ">
                                <div class="overview-row">
                                    <iframe width="560" height="315"  frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen
                                        :src="`https://www.youtube.com/embed/${getVideoId(service.video_url)}`" v-if="service.video_url" id="service_video">
                                    </iframe>
                                    <p class="overview-text" v-html="service.description"></p>
                                    <div class="service-highlights">
                                        <div class="service-highlight" v-for="highlight in service.highlights" :key="highlight">
                                            <i class="icon icon-star-filled"></i>
                                            <p>{{highlight}}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="hl_services--prices">
                    <div class="row">
                        <div :class="`col-sm-6 col-lg-${service.plans.length>3?'3':'4'}`" v-for="(plan,i) in service.plans" :key="plan.name">
                        <div :class="`card --${$options.planClasses[i]}`">
                            <h3 class="card-heading">{{plan.name}}</h3>
                            <div class="card-body">
								<div class="price">
									<h4>${{plan.wholesale_recur_price}}<span>/month</span></h4>
									<p>+ ${{plan.wholesale_setup_price}} setup fee</p>
								</div>
								<div class="srp" v-if="plan.msrp_recur_price || plan.msrp_setup_price">
									<h5 >Suggested Retail Price <a href="javascript: void(0)" class="hl_tooltip" data-toggle="tooltip" data-placement="top" v-b-tooltip.hover data-original-title="This is the recommended Pricing to your client.">i</a></h5>
									<p>${{plan.msrp_recur_price}}/month + ${{plan.msrp_setup_price}} setup fee</p>
								</div>
								<ul v-for="highlight in plan.highlights" :key="highlight">
									<li>
										<i class="icon icon-ok"></i> {{highlight}}
									</li>
								</ul>
								<div v-if="service.agency_level">
                                    <div v-if="user.role == 'admin' && user.type == 'agency'">
                                        <button type="buton" class="btn btn-block" data-toggle="modal" v-if="service.agency_addon_type === 'whitelabel-app'"
                                            @click="subscribeAgencyService(plan,i)"
                                            :class="(subscribeBtn.disabled && activeAgencyPlanIndex === i )?'agency-subscribed':''"
                                            :disabled="subscribeBtn.disabled && activeAgencyPlanIndex === i "
                                        >
                                            {{ (subscribeBtn.disabled && activeAgencyPlanIndex !== i) ? 'Change Plan' :subscribeBtn.status}}
                                        </button>
                                        <button type="buton" class="btn btn-block" data-toggle="modal" v-else
                                            @click="subscribeAgencyService(plan,i)"
                                            :class="(subscribeBtn.disabled )?'agency-subscribed':''"
                                            :disabled="subscribeBtn.disabled"
                                        >
                                            {{ subscribeBtn.status }}
                                        </button>
                                    </div>
									<p v-else>* Only agency admin can enable this service</p>
								</div>
								<div v-else>
									<button type="buton" class="btn btn-block" data-toggle="modal" data-target="#services-add-location-modal" @click="handleSelectPlan(plan)">Add to a location</button>
									<p class="minimum" v-if="plan.min_duration_months">{{plan.min_duration_months}} month minimum
										<a href="javascript: void(0)" class="hl_tooltip" data-toggle="tooltip" data-placement="top" v-b-tooltip.hover data-original-title="All services require a 3 month minimum working with HighLevel.">i</a>
									</p>
								</div>
                            </div>
                        </div>
                        </div>
                    </div>
                    </div>

                    <div class="hl_services--faq">
                    <div class="card">
                        <div class="card-body">
                        <div class="row">
                            <div class="col-md-8 col-xl-9"  v-if="service.faqs && service.faqs.length > 0">
                                <h3>FAQ</h3>
                                <div class="hl_faqs--accordion" id="accordion">
                                    <div class="hl_faqs--accordion-item" v-for=" (faq,i) in service.faqs" :key="faq.que">
                                        <a class="collapsed" data-toggle="collapse" :href="`#faqs${i}`" role="button" aria-expanded="false" :aria-controls="`#faqs${i}`">
                                            <h4>{{faq.que}}</h4>
                                            <i class="icon icon-arrow-down-1"></i>
                                        </a>
                                        <div class="collapse" :id="`faqs${i}`" data-parent="#accordion">
                                            <p>{{faq.ans}}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-xl-3" v-if="service.files && service.files.length > 0">
                                <h3>Files</h3>
                                <div class="hl_files">
                                    <ul>
                                        <li v-for="file in service.files" :key="file.file_name">
											<a :href="file.file_url" target="_blank">
                                            	<i class="icon icon-document"></i> {{file.file_name}}
											</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>
                    </div>

                    <!-- Add to location Modal -->
                    <service-plan-modal
                        :selectedPlan="selectedPlan"
                        :service="service"
                        :company="company"
                        :user="user"
                    />
                    <agency-service-modal
                      :values="showHIPAAModal"
                      @hidden="showHIPAAModal={visible: false}"
                      @success="onSuccessSubscribe"
                    />
                </div>
                </section>
                <!-- END of .hl_services-details -->


            <div v-else class="page-spinner">
                <moon-loader color="#37ca37" size="30px" />
            </div>
        </section>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
import TopBar from '../../../components/TopBar.vue';
import SideBar from '../../../components/agency/SideBar.vue';
import ServicePlanModal from "./ServicePlanModal.vue";
import AgencyServiceModal from './AgencyServiceModal.vue';
import { AgencyService, User, Company } from '@/models';
import { getVideoId } from "@/util/video_utils";
import { mapState } from 'vuex';
import { UserState, CompanyState } from '@/store/state_models';

export default Vue.extend({
    components: { TopBar, SideBar, ServicePlanModal, AgencyServiceModal },
    planClasses : ['basic','plus','premium','elite','basic','plus','premium','elite'],
    data() {
        return {
            service: {} as AgencyService,
            selectedPlan: {},
            getVideoId: getVideoId,
            errMsg: '',
            activeAgencyPlanIndex: 0,
            showHIPAAModal: { visible: false } as { [key: string]: any }
		}
    },
    computed: {
        ...mapState('company', {
            company: (s: CompanyState) => {
                return s.company ? new Company(s.company) : undefined;
            },
        }),
        ...mapState('user', {
            user: (s: UserState) => {
                return s.user ? new User(s.user) : undefined;
            },
        }),

	  subscribeBtn(){
		let disabled = false;
		let status = 'Subscribe';
		if(this.company && this.service && this.service.agency_level) {
			let currentTime = +new Date() / 1000;
			switch(this.service.agency_addon_type){
				case 'hipaa': {
					disabled = this.company.hipaaCompliance || this.company.hipaaStatus === 'Under Review';
					if (disabled)
						status = this.company.hipaaStatus || 'Subscribed';
					break;
				}
				case 'premium': {
                    disabled = this.company.premiumUpgraded || this.company.premiumUpgradeStatus === 'Under Review'
                        || (this.company.jumpstartSupported && this.company.jumpstartSupported.seconds > currentTime);
					if (disabled)
						status = this.company.premiumUpgradeStatus || 'Subscribed';
					break;
                }
                case 'whitelabel-app': {
                    disabled = this.company.whitelabelAppEnabled || this.company.whitelabelAppStatus === 'Under Review';
					if (disabled) {
						status = this.company.whitelabelAppStatus || 'Subscribed';
                    }
					break;
				}
				case 'jumpstart': {
					disabled = this.company.jumpstartSupported && this.company.jumpstartSupported.seconds > currentTime;
					if (disabled)
						status = 'Subscribed';
					break;
                }
			}
		}
		return { disabled, status };
	  },
    },
    mounted(){
        this.fetchData();
    },
    methods:{
        async fetchData() {
            await AgencyService.getById(this.$route.params.service_id).then(response=>{
                if(response) {
                    // TODO: directly use response.
                    // Filtering only active-plan [hiding deleted plans]
                    let activePlans = response._data.plans.filter( plan => plan.deleted !== true);
                    this.service = { ...response._data, id: response._id, plans: [...activePlans] };
                    this.checkActiveAgencyPlan();
                } else {
                    this.errMsg = 'Invalid service !! Please check again...'
                }
            })
        },

        handleSelectPlan(plan){
          this.selectedPlan = plan;
          this.$emit('openModal');
        },
        subscribeAgencyService(plan,i) {
            if ( !(this.subscribeBtn.disabled && this.activeAgencyPlanIndex === i) ) {
                // TODO: change showHIPAAModal name
                this.showHIPAAModal = { visible: true, company: this.company, user: this.user, type: this.service.agency_addon_type, plan, upgrade: this.subscribeBtn.disabled };
            }
        },
        checkActiveAgencyPlan() {
            switch(this.service.agency_addon_type){
                // case 'jumpstart' : {
                //     let currentTime = +new Date() / 1000;
                //     let duration = (this.company.jumpstartSupported && this.company.jumpstartSupported.seconds > currentTime)
                //     break;
                // }
                case 'whitelabel-app': {
                    this.activeAgencyPlanIndex = this.service.plans.findIndex( plan => {
                        return plan.stripe_plan_id === this.company.whitelabelAppPlan;
                    })
                    break;
                }
            }
        },
        onSuccessSubscribe(){
            this.checkActiveAgencyPlan();
            this.showHIPAAModal={visible: false}
        }
    }
});
</script>
<style scoped>
.page-spinner, .page-error{
    height: 50vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* .hl_services--detail .overview-row{
    display: flex;
    justify-content: space-between;
} */
.overview-text{
    margin-bottom: 20px;
}
#service_video{
    float: left;
    margin-right: 20px;
    margin-bottom: 20px;
}
.service-highlights{
    display: flex;
    border-top: 1px solid #eeeeee;
    padding-top: 20px;
}
.service-highlights .icon-star-filled{
    min-width: 30px;
    text-align: center;
    color: #ffbc00;
}
.service-highlight{
    display: flex;
}
.service-highlights p{
    margin-right: 10px;
    margin-bottom: 10px;
}
@media (max-width: 767px) {
    #service_video{
        max-width: 100%;
        width: 100%;
        height: 40vw;
        margin-right: 0px;
    }
}

.hl_services--prices>.row{
    justify-content: center;
}

.agency-subscribed {
    background-color: inherit !important;
    color: #008cfb !important;
    border: 1px #008cfb solid;
	cursor: progress;
}

/* .hipaa-processing {
  background-color: inherit !important;
    color: #37ca37;
    border: 1px #37ca37 solid;
} */
</style>
