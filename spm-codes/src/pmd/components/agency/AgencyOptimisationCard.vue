<template>
  <div class="card">
    <div class="card-header">
      <h3 style="width: 100%">Optimize Experience</h3>
      <!-- <p style="font-size: 12px; opacity: 0.7">
        Applies to new locations moving forward
      </p> -->
    </div>
    <div class="card-body">
      <div class="hl-oe__item-wrap">
        <div class="hl-oe__toggle-switch" @click="toggleExperiance('nps', experianceNpsEnabled)" style="margin-left: 6px">
          <UIToggle
            id="hl-oe__nps"
            :value="experianceNpsEnabled"
          />
          <label class="tgl-btn" for="hl-oe__nps"></label>
        </div>
        <div class="hl-oe__item-info">
          <div class="hl-oe__item-title">NPS Survey</div>
          <div class="hl-oe__item-description">
            We use Net Promoter Score surveys to make the experience better for your clients.
          </div>
          <!-- <div class="hl-oe__item-link">see example</div> -->
          <div class="hl-oe__item-warning" v-if="!experianceNpsEnabled">
            We recommend enabling NPS Survey
          </div>
        </div>
      </div>
      <div class="hl-oe__item-wrap">
        <div class="hl-oe__toggle-switch" @click="toggleExperiance('guides', experianceGuidesEnabled)" style="margin-left: 6px">
          <UIToggle
            id="hl-oe__guides"
            :value="experianceGuidesEnabled"
          />
          <label class="tgl-btn" for="hl-oe__guides"></label>
        </div>
        <div class="hl-oe__item-info">
          <div class="hl-oe__item-title">Product Guides</div>
          <div class="hl-oe__item-description">
            When new users sign-up, we show guides to them as they discover new features.
            It helps with adoption &amp; engagement which results in better retention of clients.
          </div>
          <!-- <div class="hl-oe__item-link">see example</div> -->
          <div class="hl-oe__item-warning" v-if="!experianceGuidesEnabled">
            We recommend enabling Product Guides
          </div>
        </div>
      </div>
      <div class="hl-oe__item-wrap">
        <div class="hl-oe__toggle-switch" @click="toggleExperiance('reports', experianceReportsEnabled)" style="margin-left: 6px">
          <UIToggle
            id="hl-oe__updates"
            :value="experianceReportsEnabled"
          />
          <label class="tgl-btn" for="hl-oe__updates"></label>
        </div>
        <div class="hl-oe__item-info">
          <div class="hl-oe__item-title">Daily Updates</div>
          <div class="hl-oe__item-description">
            Everyday we'll send a report to your clients prompting them to get back to leads faster.
            It is critical to improve response rates and improve lead conversions.
          </div>
          <!-- <div class="hl-oe__item-link">see example</div> -->
          <div class="hl-oe__item-warning" v-if="!experianceReportsEnabled">
            We recommend enabling Daily Updates
          </div>
        </div>
      </div>

      <!-- <div class="form-group">
        <button type="submit" class="btn btn-primary">
          <moon-loader
            v-if="saving"
            :loading="saving"
            color="#188bf6"
            size="21px"
          />
          <span v-else>Update Address</span>
        </button>
      </div> -->
    </div>
    <AgencyOptimisationDisableModal
      :show="showDisableModal"
      v-if="showDisableModal"
      :type="disableType"
      @close="showDisableModal = false"
      @disable="proceedDisable"
    />
  </div>
</template>

<script>
import { Company } from '@/models'
import AgencyOptimisationDisableModal from "./AgencyOptimisationDisableModal.vue";
export default {
  data() {
    return {
      // saving: false,
      showDisableModal: false,
      disableType: '',
      disableTypeKey: '',
    }
  },
  components: {
    AgencyOptimisationDisableModal
  },
  computed: {
    company() {
      return this.$store.state.company.company
    },
    experianceNpsEnabled() {
      if (this.company.experiance_nps_enabled !== undefined) {
        return this.company.experiance_nps_enabled
      } else return true
    },
    experianceGuidesEnabled() {
      if (this.company.experiance_guides_enabled !== undefined) {
        return this.company.experiance_guides_enabled
      } else return true
    },
    experianceReportsEnabled() {
      if (this.company.experiance_reports_enabled !== undefined) {
        return this.company.experiance_reports_enabled
      } else return true
    },
  },
  methods: {
    async toggleExperiance(type, oldValue) {
      //
      let updateKey = '';
      switch(type){
        case 'nps':
          updateKey = 'experiance_nps_enabled'
          break
        case 'guides':
          updateKey = 'experiance_guides_enabled'
          break
        case 'reports':
          updateKey = 'experiance_reports_enabled'
          break
      }
      if (oldValue) {
        this.showDisableModal = true;
        this.disableType = type;
        this.disableTypeKey = updateKey;
      } else {
        await this.updateExperience(updateKey,!oldValue)
      }

    },
    async updateExperience(updateKey,newValue){
      await Company.collectionRef().doc(this.company.id).update({
        [updateKey]: newValue,
      })
    },
    async proceedDisable(){
      await this.updateExperience(this.disableTypeKey, false)
      this.showDisableModal = false
    },
  },
}
</script>

<style lang="scss">
.hl-oe__item-wrap {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  .hl-oe__toggle-switch {
    margin-right: 16px;
    margin-top: 8px;
  }
  .hl-oe__item-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
  }
  .hl-oe__item-description {
    font-size: 12px;
    line-height: 20px;
  }
  .hl-oe__item-link {
    color: #2f80ed;
    cursor: pointer;
    text-decoration: underline;
    margin-top: -4px;
  }
  .hl-oe__item-warning {
    margin-top: 4px;
    color: #fc6e25;
  }
}
</style>
