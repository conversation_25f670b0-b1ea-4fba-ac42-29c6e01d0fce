export default {
  data(){
    return {
      markup: '1',
      billRange: {
        min: 1,
        max: 10,
        step: 0.1,
      },
      twilioCharge: {
        incomingCall: 0.0085,
        outgoingCall: 0.013,
        sms: 0.0075,
      },
    }
  },
  computed:{
    markupPercent(): number {
      const markupAmount = parseFloat(this.markup)
      if (markupAmount === 1) {
        return 5
      }

      // if markup is set to 2.5 ie. 2.5x
      // then markup % would be 150
      return markupAmount * 100 - 100
    },
    incomingCallsProfit(): string {
      return this.computeMarkupProfit(this.twilioCharge.incomingCall)
      // % of markup * actual charge --> 10% of incoming call charge
    },
    outgoingCallsProfit(): string {
      return this.computeMarkupProfit(this.twilioCharge.outgoingCall)
    },
    smsProfit(): string {
      return this.computeMarkupProfit(this.twilioCharge.sms)
    },
    incomingCallsCharge(): string {
      return this.readableFloat(
        parseFloat(this.incomingCallsProfit) + this.twilioCharge.incomingCall
      )
    },
    outgoingCallsCharge(): string {
      return this.readableFloat(
        parseFloat(this.outgoingCallsProfit) + this.twilioCharge.outgoingCall
      )
    },
    smsCharge(): string {
      return this.readableFloat(
        parseFloat(this.smsProfit) + this.twilioCharge.sms
      )
    },
  },
  methods:{
    computeMarkupAmountFromPercent(markupPercent: number): string {
      return `${(markupPercent * 100) / 10000 + 1}`
    },
    computeMarkupProfit(charge: number): string {
      const profit = parseFloat(
        `${((this.markupPercent * 100) / 10000) * charge}`
      )
      return this.readableFloat(profit)
    },
    readableFloat(amount: number): string {
      return parseFloat(amount.toFixed(4)).toString()
    },
  }
}
