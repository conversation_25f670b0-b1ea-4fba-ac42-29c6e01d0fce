/**
 * SubAccount Utilities
 * 
 * Utility functions for SubAccount entities including computed properties,
 * validation, and business logic based on SPM Location model patterns
 */

import type {
  ISubAccount,
  ICreateSubAccountPayload,
  IUpdateSubAccountPayload,
  ISubAccountSettings,
  IProductStatus
} from '../types/subAccount';
import { SubAccountStatus } from '../types/subAccount';

/**
 * SubAccount computed properties
 * Based on SPM Location model computed properties and getters
 */
export class SubAccountComputedProperties {
  /**
   * Get formatted name with address
   * Based on SPM Location.formatedName getter
   */
  static getFormattedName(subAccount: ISubAccount): string {
    let name = subAccount.name;
    if (subAccount.address || subAccount.city || subAccount.state) {
      name += ' -- ';
    }
    if (subAccount.address) {
      name += subAccount.address;
    }
    if (subAccount.address && subAccount.city) {
      name += ', ';
    }
    if (subAccount.city) {
      name += subAccount.city;
    }
    if (subAccount.city && subAccount.state) {
      name += ', ';
    }
    if (subAccount.state) {
      name += subAccount.state;
    }
    return name;
  }

  /**
   * Get full address string
   */
  static getFullAddress(subAccount: ISubAccount): string {
    const parts = [
      subAccount.address,
      subAccount.city,
      subAccount.state,
      subAccount.postalCode,
      subAccount.country
    ].filter(Boolean);
    
    return parts.join(', ');
  }

  /**
   * Check if sub-account is active
   */
  static isActive(subAccount: ISubAccount): boolean {
    return subAccount.status === SubAccountStatus.ACCOUNT && !subAccount.deleted;
  }

  /**
   * Check if sub-account is prospect
   */
  static isProspect(subAccount: ISubAccount): boolean {
    return subAccount.status === SubAccountStatus.PROSPECT;
  }

  /**
   * Get display timezone
   */
  static getDisplayTimezone(subAccount: ISubAccount): string {
    // Handle legacy timezone values
    if (['Etc/Greenwich', 'GMT'].includes(subAccount.timezone)) {
      return 'UTC';
    }
    return subAccount.timezone;
  }

  /**
   * Check if SAAS mode is activated
   */
  static isSaasActivated(subAccount: ISubAccount): boolean {
    return subAccount.settings.saasSettings.saasMode === 'activated';
  }

  /**
   * Check if SAAS setup is pending
   */
  static isSaasSetupPending(subAccount: ISubAccount): boolean {
    return subAccount.settings.saasSettings.saasMode === 'setup_pending';
  }

  /**
   * Get enabled product features
   */
  static getEnabledProducts(subAccount: ISubAccount): string[] {
    const enabled: string[] = [];
    const productStatus = subAccount.productStatus;
    
    Object.entries(productStatus).forEach(([product, isEnabled]) => {
      if (isEnabled) {
        enabled.push(product);
      }
    });
    
    return enabled;
  }

  /**
   * Check if specific product is enabled
   */
  static isProductEnabled(subAccount: ISubAccount, product: keyof IProductStatus): boolean {
    return subAccount.productStatus[product] === true;
  }

  /**
   * Get contact information
   */
  static getContactInfo(subAccount: ISubAccount): {
    phone?: string;
    email?: string;
    website?: string;
  } {
    return {
      phone: subAccount.phone,
      email: subAccount.email,
      website: subAccount.website
    };
  }
}

/**
 * SubAccount validation utilities
 * Based on SPM Location model validation patterns
 */
export class SubAccountValidationUtils {
  /**
   * Validate sub-account creation data
   */
  static validateCreateSubAccount(data: ICreateSubAccountPayload): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Required fields validation
    if (!data.name?.trim()) {
      errors.push('Name is required');
    }

    if (!data.companyId?.trim()) {
      errors.push('Company ID is required');
    }

    if (!data.timezone?.trim()) {
      errors.push('Timezone is required');
    }

    // Email validation
    if (data.email && !this.isValidEmail(data.email)) {
      errors.push('Invalid email format');
    }

    // Phone validation
    if (data.phone && !this.isValidPhone(data.phone)) {
      errors.push('Invalid phone format');
    }

    // Website validation
    if (data.website && !this.isValidWebsite(data.website)) {
      errors.push('Invalid website URL format');
    }

    // Name length validation
    if (data.name && data.name.length > 100) {
      errors.push('Name must be 100 characters or less');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate sub-account update data
   */
  static validateUpdateSubAccount(data: IUpdateSubAccountPayload): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Email validation
    if (data.email && !this.isValidEmail(data.email)) {
      errors.push('Invalid email format');
    }

    // Phone validation
    if (data.phone && !this.isValidPhone(data.phone)) {
      errors.push('Invalid phone format');
    }

    // Website validation
    if (data.website && !this.isValidWebsite(data.website)) {
      errors.push('Invalid website URL format');
    }

    // Name length validation
    if (data.name && data.name.length > 100) {
      errors.push('Name must be 100 characters or less');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate email format
   */
  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone format
   */
  private static isValidPhone(phone: string): boolean {
    // Basic phone validation - can be enhanced based on requirements
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }

  /**
   * Validate website URL format
   */
  private static isValidWebsite(website: string): boolean {
    try {
      new URL(website);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate timezone
   */
  static isValidTimezone(timezone: string): boolean {
    try {
      Intl.DateTimeFormat(undefined, { timeZone: timezone });
      return true;
    } catch {
      return false;
    }
  }
}

/**
 * SubAccount business logic utilities
 * Based on SPM Location model business logic
 */
export class SubAccountBusinessUtils {
  /**
   * Prepare sub-account data for creation
   * Based on SPM Location model initialization patterns
   */
  static prepareForCreation(data: ICreateSubAccountPayload): Partial<ISubAccount> {
    const prepared: Partial<ISubAccount> = {
      ...data,
      nameLowerCase: data.name.toLowerCase(),
      status: data.status || SubAccountStatus.PROSPECT,
      deleted: false,
      // Default settings
      settings: {
        email: { enabled: false },
        sms: { enabled: false },
        review: { enabled: false },
        social: { enabled: false },
        widget: { enabled: false },
        chatwidget: { enabled: false },
        textwidget: { enabled: false },
        attribution: { enabled: false },
        clio: { enabled: false },
        drchrono: { enabled: false },
        qbo: { enabled: false },
        saasSettings: {
          saasMode: 'not_activated',
          twilioRebilling: { enabled: false },
          mailgunRebilling: { enabled: false }
        },
        ...data.settings
      },
      // Default product status
      productStatus: {
        reviews: false,
        listings: false,
        conversations: false,
        social: false,
        ...data.productStatus
      },
      // Default feature flags
      isCalendarV3On: false,
      isUserAvailabilityOn: false,
      hipaaCompliance: data.hipaaCompliance || false
    };

    // Handle timezone normalization
    if (['Etc/Greenwich', 'GMT'].includes(prepared.timezone!)) {
      prepared.timezone = 'UTC';
    }

    return prepared;
  }

  /**
   * Prepare sub-account data for update
   */
  static prepareForUpdate(data: IUpdateSubAccountPayload): IUpdateSubAccountPayload & { nameLowerCase?: string } {
    const prepared: IUpdateSubAccountPayload & { nameLowerCase?: string } = { ...data };

    // Update lowercase name if name is being updated
    if (data.name) {
      prepared.nameLowerCase = data.name.toLowerCase();
    }

    // Handle timezone normalization
    if (data.timezone && ['Etc/Greenwich', 'GMT'].includes(data.timezone)) {
      prepared.timezone = 'UTC';
    }

    return prepared;
  }

  /**
   * Generate API key for sub-account
   * Based on SPM Location.apiKey getter pattern
   */
  static generateApiKey(): string {
    return `sa_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Check if sub-account can be deleted
   */
  static canDelete(subAccount: ISubAccount): {
    canDelete: boolean;
    reason?: string;
  } {
    // Add business logic for deletion validation
    if (subAccount.status === SubAccountStatus.ACCOUNT) {
      return {
        canDelete: false,
        reason: 'Cannot delete active sub-accounts. Change status to prospect first.'
      };
    }

    return { canDelete: true };
  }

  /**
   * Get default settings for new sub-account
   */
  static getDefaultSettings(): ISubAccountSettings {
    return {
      email: { enabled: false },
      sms: { enabled: false },
      review: { enabled: false },
      social: { enabled: false },
      widget: { enabled: false },
      chatwidget: { enabled: false },
      textwidget: { enabled: false },
      attribution: { enabled: false },
      clio: { enabled: false },
      drchrono: { enabled: false },
      qbo: { enabled: false },
      saasSettings: {
        saasMode: 'not_activated',
        twilioRebilling: { enabled: false },
        mailgunRebilling: { enabled: false }
      }
    };
  }

  /**
   * Get default product status for new sub-account
   */
  static getDefaultProductStatus(): IProductStatus {
    return {
      reviews: false,
      listings: false,
      conversations: false,
      social: false
    };
  }
}
