<template>
	<div class="hl_settings--body">
		<div class="container-fluid">
			<div class="hl_settings--controls">
				<div class="hl_settings--controls-left">
					<h2>Custom Fields</h2>
				</div>
				<div class="hl_settings--controls-right">
					<UIButton use="primary" @click="showAddCustomFieldModal">
						<i class="icon icon-plus mr-2"></i> Add custom field
					</UIButton>
				</div>
			</div>
			<div class="card">
				<!-- <div class="card-header">
					<ul class="nav nav-tabs" role="tablist">
							<li class="nav-item">
								<a @click="selectedModel = fieldModel.CONTACT" :class="{'active': selectedModel=== fieldModel.CONTACT }" class="nav-link">CONTACT</a>
							</li>
							<li class="nav-item">
								<a @click="selectedModel = fieldModel.OPPORTUNITY" :class="{'active': selectedModel=== fieldModel.OPPORTUNITY }" class="nav-link">OPPORTUNITY</a>
							</li>
					</ul>
				</div>-->

				<div class="--no-padding">
					<div class="table-wrap">
						<table class="table">
							<thead>
								<tr>
									<th></th>
									<th>Field Name</th>
									<th>Type</th>
									<th>Field Key</th>
									<th>Placeholder</th>
									<th></th>
									<th></th>
								</tr>
							</thead>
							<draggable v-model="customFields" :element="'tbody'" @end="onEnd" handle=".handle">
								<tr v-if="customFields.length > 0" v-for="field in customFields">
									<td>
										<i class="fa fa-align-justify handle"></i>
									</td>
									<td>{{field.name}}</td>
									<td>{{getFieldTypeLabel(field.dataType)}}</td>
									<td style="max-width:450px;">{{field.fieldKey}}</td>
									<td>{{field.placeholder}}</td>
									<td>
										<i
											v-if="!isProcessingDelete(field)"
											class="icon icon-trash pointer --light"
											style="margin-left:15px;"
											@click.prevent="deleteCustomField(field)"
										></i>
										<moon-loader
											v-if="isProcessingDelete(field)"
											:loading="isProcessingDelete(field)"
											color="#188bf6"
											size="15px"
											style="display: inline-block;margin-left: 15px;"
										/>
									</td>
									<td>
										<i class="icon icon-pencil pointer --light" @click.prevent="editCustomField(field)"></i>
									</td>
								</tr>
							</draggable>
						</table>
					</div>
				</div>
			</div>
		</div>
		<AddCustomFieldModal
			v-if="drawComponent"
			:values="addCustomFieldModal"
			@hidden="addCustomFieldModal.visible"
			v-on:hide="closeAddCustomFieldModal"
		/>
	</div>
</template>

<script lang='ts'>
import Vue from "vue";
import draggable from "vuedraggable";

import { Location, User, CustomField, FieldType, Formsurvey, Formbuilder, Trigger, CustomDateField } from "@/models";
const AddCustomFieldModal = () => import( "../../components/agency/AddCustomFieldModal.vue");

enum FieldModels {
	CONTACT = 'contact',
	OPPORTUNITY = 'opportunity'
}

let unsubscribeCustomFields: () => void;

export default Vue.extend({
	components: { AddCustomFieldModal, draggable },
	data() {
		return {
			drawComponent: false as boolean,
			currentLocationId: "" as string,
			customFields: [] as CustomField[],
			location: {} as Location,
			addCustomFieldModal: { visible: false } as { [key: string]: any },
			processingDelete: {} as { [key: string]: boolean },
			fieldModel: FieldModels,
			selectedModel: FieldModels.CONTACT as FieldModels
		};
	},
	watch: {
		"$route.params.location_id": function (id) {
			this.currentLocationId = id;
			this.fetchData();
		},
		selectedModel(val) {
			this.fetchData();
		},
	},
	async created() {
		this.currentLocationId = this.$router.currentRoute.params.location_id;
		this.fetchData();
	},
	computed: {
		user() {
			const user = this.$store.state.user.user
			return user ? new User(user) : undefined
		},
	},
	methods: {
		isProcessingDelete(calendar: CustomField) {
			return this.processingDelete[calendar.id];
		},
		showAddCustomFieldModal() {
			this.reset();
			this.addCustomFieldModal = {
				visible: true,
				location_id: this.currentLocationId
			};
		},
		async fetchData() {
			if (unsubscribeCustomFields) unsubscribeCustomFields();
			unsubscribeCustomFields = CustomField.getByLocationId(this.currentLocationId).onSnapshot(snapshot => {
				this.customFields = snapshot.docs.map(d => new CustomField(d));
			});
			// unsubscribeCustomFields = CustomField.getByLocationIdAndModel(this.currentLocationId,this.selectedModel).onSnapshot(snapshot => {
			// 	this.customFields = snapshot.docs.map(d => new CustomField(d));
			// });

		},
		getFieldTypeLabel(type: FieldType): string {
			return FieldType[type];
		},
		reset() {
			var vm = this;
			vm.drawComponent = false;
			Vue.nextTick(function () {
				vm.drawComponent = true;
			});
		},
		closeAddCustomFieldModal() {
			this.addCustomFieldModal = {
				visible: false
			};
		},
		async deleteCustomField(customField: CustomField) {
			if (confirm('Are you sure you want to delete this custom field?')) {
        try {
            // Delete form field if any match for the location
            const formData = await Formbuilder.getByLocationId(this.currentLocationId)
            formData.forEach(function(form:any, key:number){
              if("form" in form.formData && "fields" in form.formData.form){
                const formFields = form.formData.form.fields
                if(formFields.length > 0){
                  formFields.forEach(function(fieldItem:any, fieldKey:number){
                    if(
                      "id" in fieldItem &&
                      fieldItem.id!="" &&
                      fieldItem.id == customField.id
                    ){
                      formFields.splice(fieldKey, 1)
                      form.save();
                    }
                  })
                }
              }
            });

            // Delete survey fields if any match for the location
            const surveyData = await Formsurvey.getByLocationId(this.currentLocationId)
            surveyData.forEach(function(survey:any, key:number){
              if("slides" in survey.formData){
                const surveySlides = survey.formData.slides
                surveySlides.forEach(function(slideItem:any, keySlideItem:number){
                  if(slideItem.slideData.length > 0){
                    slideItem.slideData.forEach(function(fieldItem:any, fieldKey:number){
                      if(
                        "id" in fieldItem &&
                        fieldItem.id!="" &&
                        fieldItem.id == customField.id
                      ){
                        slideItem.slideData.splice(fieldKey, 1)
                        survey.save();
                      }
                    })
                  }
                })
              }
            });

            //Delete trigger fields if any matches for the location
            const triggerData = await Trigger.getByLocationId(this.currentLocationId)
            triggerData.forEach(function(trigger: any){
              if("conditions" in trigger) {
                const triggerConditions = trigger.conditions
                triggerConditions.forEach(function(condition: any, key: number) {
                  if (
                    "value" in condition &&
                    condition.value == customField.id
                  ) {
                    trigger.conditions.splice(key, 1)
                    trigger.save();
                  }
                })
              }
            })

            //Delete custom date fields
            if (FieldType[customField.dataType] === FieldType.DATE) {
              const customDateFields = await CustomDateField.removeCustomDateFields(customField.id, this.currentLocationId);
            }

            Vue.set(this.processingDelete, customField.id, true);
            customField.deleted = true;
            await customField.save();
            Vue.set(this.processingDelete, customField.id, false);

        } catch (err) {
          console.log(err);
          alert("Opps! something wrong")
        } finally{
          this.$store.dispatch('locationCustomFields/syncAll', { locationId: this.currentLocationId, forceRefresh: true })
        }
			}
		},
		onEnd() {
			this.customFields.forEach((element, index) => {
				if (element.position !== index) {
					element.position = index;
					element.save();
				}
			});
		},
		editCustomField(customField: CustomField){
			this.reset();
			this.addCustomFieldModal = {
				visible: true,
				location_id: this.currentLocationId,
				editCustomField:customField
			};
		}
	},
  beforeDestroy(): void {
    if (unsubscribeCustomFields) unsubscribeCustomFields();
  }
});
</script>


<style scoped>
@media (max-width: 991px) {
  .table-wrap td {
    max-width: 100% !important;
  }
}
</style>
