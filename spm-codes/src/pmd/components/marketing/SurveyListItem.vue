<template>
  <tr>
    <td>
      <router-link
        :to="{name: 'survey_manage', params: {location_id: currentLocationId, survey_id: item.id}}"
        tag="a"
      >{{ item.name ? item.name : "Open" }}</router-link>
    </td>
    <td v-text="item.id"/>
    <td v-text="item.deleted ? 'Inactive' : 'Active'"/>
    <td>
      <div class="btn-group">
          <router-link
            tag="button"
            class="btn btn-primary"
            :to="{name: 'survey_manage', params: {location_id: currentLocationId, survey_id: item.id}}"
          >Edit</router-link>
          <button
            type="button"
            class="btn btn-primary dropdown-toggle dropdown-toggle-split"
            data-toggle="dropdown"
            aria-haspopup="true"
            aria-expanded="true"
          ></button>
          <div class="dropdown-menu dropdown-menu-right">
            <a
              class="dropdown-item"
              href="javascript:void(0);"
              @click.prevent="$emit('clone', item)"
            >Copy</a>
            <a
              class="dropdown-item"
              href="javascript:void(0);"
              @click.prevent="deleteItem"
            >Delete</a>
          </div>
        </div>
    </td>
  </tr>
</template>

<script lang="ts">
import Vue from "vue";
import { mapState } from "vuex";
import { UserState } from "../../../store/state_models";
import { Formsurvey, User } from "../../../models/";

export default Vue.extend({
  props: ["item"],
  data() {
    return {
      currentLocationId: ""
    };
  },
  watch: {
    "$route.params.location_id": function(id) {
      this.currentLocationId = id;
    }
  },
  computed: {
    allowEdit(): boolean {
      //this.user.permissions.campaigns_read_only !== true;
      return this.user ? true : false;
    },
    ...mapState("user", {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined;
      }
    })
  },
  methods: {
    async deleteItem() {
      if (confirm("Are you sure you want to delete this survey?")) {
        let surveyStore = await Formsurvey.getById(this.item.id);
        surveyStore.deleted = true;
        await surveyStore.save();
        this.$emit('updated')
      }
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id;
  }
});
</script>
