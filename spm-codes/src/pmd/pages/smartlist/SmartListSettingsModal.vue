<template>
      <b-modal ref="modal-sm-d" size="sm" v-if="smartList" @hide="hidePopup" hide-footer>
      <template v-slot:modal-title>
         <moon-loader-title :title="(allowCopy ? 'Copy ' : 'Edit') +  ' Smart List'"
                            :loading="loading"/>
      </template>
      <template>
        <div class="form-group mx-4">

          <UITextInputGroup
            label="Name"
            v-model="name"
            placeholder="Name"
          />
          <div v-if="showErr"><span class="--red">{{showErr}}</span></div>
        </div>
       <div class="modal-footer" v-if="loading !== true">
        <div class="modal-footer--inner">
          <UIButton use="outline" @click.prevent="hidePopup" data-dismiss="modal">
            Cancel
          </UIButton>
          <UIButton
              @click.prevent="saveList"
              :loading="loading"
            >
              Save
            </UIButton>
        </div>
      </div>
      </template>
    </b-modal>
</template>

<script lang="ts">

import Vue from 'vue';
import { SmartList, User } from '@/models'
import { Utils } from '@/util/utils';
import { SmartListVM } from './vm/smart_list_vm';

export default Vue.extend({
  name:'SmartListSettingsModal',
  props: {
    smartList : SmartList,
    defaultName: String,
    allowCopy: Boolean,
    smartListNames: { type: Array, default: () => []}
  },
  data(){
    return {
      loading : false,
      showErr: undefined,
      name: this.smartList.listName + (this.allowCopy ? '-copy' : ''),
    }
  },
  methods :{
    async saveList(){
      this.showErr= '';
      if (!this.smartList) return;
      if (!this.name) {
        this.showErr = "Please enter a name";
        return;
      }
      let emptyListName = this.defaultName ? this.defaultName.trim() : 'All';
      emptyListName = emptyListName.toLowerCase();
      let listsettings = SmartListVM.LIST_SETTINGS_VIEW_NAME;
      if (listsettings) listsettings = listsettings.toLowerCase();
      const nm  = Utils.safeLC(this.name);
      if (nm === emptyListName || nm === 'more' || nm === listsettings) {
        this.showErr = "Please enter a different name";
        return;
      }
      if (this.smartListNames && this.smartListNames.indexOf(nm) !== -1){
        this.showErr = "This name is already in use";
        return;
      }
      if (!this.smartList.filterSpecs || !this.smartList.filterSpecs.filters || !this.smartList.filterSpecs.filters.length){
        this.showErr = "Select atleast one filter to save this list";
        return;
      }

      this.loading = true;
      try {
        if (this.allowCopy) {
          let copy = this.smartList.copy();
          copy.listName = this.name;
          copy.displayOrder = this.smartList.displayOrder;
          const currentUser = new User(await this.$store.dispatch('user/get'));
          if (currentUser) copy.userId = currentUser.id;
          await copy.save();
          this.$emit('itemAdded', copy);
          this.$emit('saved',copy.id);
        } else {
          this.smartList.listName = this.name;
          await this.smartList.save();
          this.$emit('saved',this.smartList.id);
        }
        this.hidePopup();
      } catch(error){
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    showPopup() {
      this.loading = false;
      if(this.$refs['modal-sm-d']) this.$refs['modal-sm-d'].show()
    },
    hidePopup() {
      this.loading = false;
      if(this.$refs['modal-sm-d']) this.$refs['modal-sm-d'].hide()
      this.$emit('closed');
    },
  },
  mounted(){
     if (this.smartList) this.showPopup();
  }

});

</script>
