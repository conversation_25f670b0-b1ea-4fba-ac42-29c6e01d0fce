<template>
  <section class="hl_wrapper">
    <section class="hl_wrapper--inner hl_dashboard" id="dashboard">
      <div class="container-fluid">
        <YextPromoDashboardBanner/>
        <WordpressPromoDashboardBanner/>
        <div
          class="card-group --wide-gutter"
          v-if="
            user &&
              user.permissions.dashboard_stats_enabled !== false &&
              user.permissions.opportunities_enabled !== false
          "
        >
          <div class="card hl_dashboard--matters-added">
            <div class="card-header">
              <h2>
                Opportunities
                <!-- <span>Last 6 Months</span> -->
              </h2>
              <!-- <select class="selectpicker more-select">
								<option>This Week</option>
								<option>Last Week</option>
								<option>This Month</option>
								<option>Last 6 Months</option>
								<option>This Year</option>
							</select>-->
              <div class="d-flex" style="width: 220px;">
                <vue-ctk-date-time-picker
                  :locale="getCountryInfo('locale')"
                  v-model="opportunitiesFiltersDates"
                  :range="true"
                  color="#188bf6"
                  :only-date="true"
                  enable-button-validate
                  :noClearButton="true"
                  name="start_time"
                  @is-hidden="updateOpportunitiesFilters"
                  :formatted="getCountryDateFormat('month date year')"
                  id="random24243"
                />
                <!-- <div>
									<datepicker
										:input-class="'form-control opportunities-filter-date-picker start-date'"
										:format="'D, MMM dsu'"
										placeholder="Select start date"
										name="start-date"
										:value="opportunitiesFilters.date"
										@selected="date => {
										opportunitiesFilters.date = date.getTime();
										opportunitiesFilters.disabledEndDate = {
											to: date
										}
									}"
									/>
								</div>

								<span class="opportunities-date-separator">-</span>

								<div>
									<datepicker
										:input-class="'form-control opportunities-filter-date-picker filter-date-picker'"
										:format="'D, MMM dsu'"
										:placeholder="opportunitiesFilters.date ? 'Select end date' : 'Disabled'"
										name="end-date"
										:value="opportunitiesFilters.endDate"
										:disabled-picker="!opportunitiesFilters.date"
										:disabled="opportunitiesFilters.disabledEndDate"
										@selected="date => opportunitiesFilters.endDate = date.getTime()"
									/>
								</div>-->
              </div>
            </div>
            <div class="card-body" v-if="opportunitiesAggrs.counts">
              <!-- <h4>2017-05-31 to 2018-05-31</h4> -->
              <h3>{{ totalOpportunitiesCount }}</h3>
              <ul class="bottom-stats">
                <li>
                  <span>Closed</span>
                  <strong>{{ opportunitiesAggrs.counts.closed }}</strong>
                </li>
                <li>
                  <span>Open</span>
                  <strong>{{ opportunitiesAggrs.counts.open }}</strong>
                </li>
                <li>
                  <span>Lost</span>
                  <strong>{{ opportunitiesAggrs.counts.lost }}</strong>
                </li>
              </ul>
            </div>
          </div>
          <div
            v-if="user && user.permissions.lead_value_enabled !== false"
            class="card hl_dashboard--pipeline-value"
          >
            <div class="card-header">
              <h2>
                Pipeline Value
                <!-- <span>Last 6 Months</span> -->
              </h2>
              <!-- <select class="selectpicker more-select">
								<option>This Week</option>
								<option>Last Week</option>
								<option>This Month</option>
								<option>Last 6 Months</option>
								<option>This Year</option>
							</select>-->
              <div class="d-flex" style="width: 220px;">
                <vue-ctk-date-time-picker
                  :locale="getCountryInfo('locale')"
                  v-model="opportunitiesFiltersDates"
                  :range="true"
                  color="#188bf6"
                  :only-date="true"
                  enable-button-validate
                  :noClearButton="true"
                  name="start_time"
                  @is-hidden="updateOpportunitiesFilters"
                  :formatted="getCountryDateFormat('month date year')"
                  id="random352343"
                />
              </div>
            </div>
            <div class="card-body">
              <!-- <h4>2017-05-31 to 2018-05-31
							</h4>-->
              <h3>
                {{ location.country | symbole
                }}{{ totalOpportunitiesRevenues | formatNumber }}
              </h3>
              <ul class="bottom-stats">
                <li>
                  <span>Closed</span>
                  <strong
                    >{{ location.country | symbole
                    }}{{
                      opportunitiesAggrs.revenues.closed | formatNumber
                    }}</strong
                  >
                </li>
                <li>
                  <span>Open</span>
                  <strong
                    >{{ location.country | symbole
                    }}{{
                      opportunitiesAggrs.revenues.open | formatNumber
                    }}</strong
                  >
                </li>
                <li>
                  <span>Lost</span>
                  <strong
                    >{{ location.country | symbole
                    }}{{
                      opportunitiesAggrs.revenues.lost | formatNumber
                    }}</strong
                  >
                </li>
              </ul>
            </div>
          </div>
          <div class="card hl_dashboard--conversion-rate">
            <div class="card-header">
              <h2>
                Conversion Rate
                <!-- <span>Last 6 Months</span> -->
              </h2>
              <!-- <select class="selectpicker more-select">
								<option>This Week</option>
								<option>Last Week</option>
								<option>This Month</option>
								<option>Last 6 Months</option>
								<option>This Year</option>
							</select>-->
              <div class="d-flex" style="width: 220px;">
                <vue-ctk-date-time-picker
                  :locale="getCountryInfo('locale')"
                  v-model="opportunitiesFiltersDates"
                  :right="true"
                  :range="true"
                  color="#188bf6"
                  :only-date="true"
                  enable-button-validate
                  :noClearButton="true"
                  name="start_time"
                  @is-hidden="updateOpportunitiesFilters"
                  :formatted="getCountryDateFormat('month date year')"
                  id="random3423324"
                />
              </div>
            </div>
            <div class="card-body">
              <!-- <h4>2017-05-31 to 2018-05-31</h4> -->
              <h3>{{ conversionRate | formatNumber }}%</h3>
            </div>
          </div>
        </div>
        <div class="card-group">
          <div
            class="card hl_dashboard--sales-funnel"
            v-if="
              pipelines &&
                pipelines.length > 0 &&
                user &&
                user.permissions.dashboard_stats_enabled !== false
            "
          >
            <div class="card-header">
              <div class="hl_tasks-list pipeline-name">
                <h2>
                  Funnel
                </h2>
                <b-dropdown
                  :text="
                    filterPipeline.name +
                      ' (' +
                      filteredOpportunitiesCount +
                      ')'
                  "
                  variant="primary"
                >
                  <b-dropdown-item
                    v-for="pipeline in pipelines"
                    :key="pipeline.id"
                    @click="filterPipeline = pipeline"
                    >{{ pipeline.name }}</b-dropdown-item
                  >
                </b-dropdown>
              </div>
              <div class="d-flex">
                <div style="width: 220px;">
                  <vue-ctk-date-time-picker
                    :locale="getCountryInfo('locale')"
                    v-model="opportunitiesFiltersDates"
                    :range="true"
                    color="#188bf6"
                    :only-date="true"
                    enable-button-validate
                    :noClearButton="true"
                    @is-hidden="updateOpportunitiesFilters"
                    :formatted="getCountryDateFormat('month date year')"
                    id="random756453"
                  />
                </div>
              </div>
            </div>
            <div class="card-body">
              <div
                class="empty-msg"
                v-if="filterPipeline.showInFunnel === false"
              >
                {{ filterPipeline.name }}'s funnel chart is disabled.
              </div>
              <div
                class="sales-funnel-chart"
                ref="sales-funnel-chart"
                v-else-if="filteredOpportunitiesCount"
              >
                <highcharts
                  :options="funnelchartoptions"
                  :callback="funnelChartLoaded"
                  style="height:100%;width:100%"
                ></highcharts>
              </div>
              <div v-else class="empty-msg">
                No opportunities in {{ filterPipeline.name }} at this time
              </div>
            </div>
          </div>
          <div
            class="card hl_dashboard--sales-funnel"
            v-if="
              pipelines &&
                pipelines.length > 0 &&
                user &&
                user.permissions.dashboard_stats_enabled !== false
            "
          >
            <div class="card-header">
              <div class="hl_tasks-list pipeline-name">
                <h2 style="margin-right:10px;">
                  Stages Distribution
                </h2>
                <b-dropdown
                  :text="
                    filterPipeline.name +
                      ' (' +
                      filteredOpportunitiesPieCount +
                      ')'
                  "
                  variant="primary"
                  size="sm"
                >
                  <b-dropdown-item
                    v-for="pipeline in pipelines"
                    :key="pipeline.id"
                    @click="filterPipeline = pipeline"
                    >{{ pipeline.name }}</b-dropdown-item
                  >
                </b-dropdown>
              </div>
              <div class="d-flex">
                <div style="width: 220px;">
                  <vue-ctk-date-time-picker
                    v-model="opportunitiesFiltersDates"
                    :right="true"
                    :range="true"
                    color="#188bf6"
                    :only-date="true"
                    enable-button-validate
                    :noClearButton="true"
                    @is-hidden="updateOpportunitiesFilters"
                    :formatted="getCountryDateFormat('month date year')"
                    id="random756454"
                  />
                </div>
              </div>
            </div>
            <div class="card-body">
              <div
                class="empty-msg"
                v-if="filterPipeline.showInPieChart === false"
              >
                {{ filterPipeline.name }}'s pie chart is hidden.
              </div>
              <div
                class="sales-funnel-chart"
                ref="sales-funnel-chart"
                v-else-if="filteredOpportunitiesPieCount"
              >
                <highcharts
                  :options="piechartoptions"
                  :callback="pieChartLoaded"
                  style="height:100%;width:100%"
                ></highcharts>
              </div>
              <div v-else class="empty-msg">
                No opportunities in {{ filterPipeline.name }} at this time
              </div>
            </div>
          </div>
          <!-- <TaskListComponent :heading="true" :action="false" message="There are currently no tasks on your account."  class="card hl_dashboard--need-action"/> -->
        </div>
        <div class="card-group">
          <div class="card hl_dashboard--need-action">
            <div style="min-height: 400px;">
              <PendingManualActionCard
                :heading="true"
                :action="false"
                :placeholder="true"
                :fullheight="true"
                message="No tasks assigned to you."
              />
            </div>
          </div>
          <div class="card hl_dashboard--need-action">
            <div style="min-height: 400px;">
              <TaskListComponent
                :heading="true"
                :action="false"
                :placeholder="true"
                :fullheight="true"
                message="No tasks assigned to you."
              />
            </div>
          </div>
        </div>

        <div
          class="card hl_dashboard--appointments"
          v-if="
            appointmentRequests &&
              appointmentRequests.length > 0 &&
              user &&
              user.permissions.dashboard_stats_enabled !== false
          "
        >
          <div class="card-header">
            <h2>Appointments</h2>
            <!-- <select class="selectpicker more-select">
							<option>This Week</option>
							<option>Last Week</option>
							<option>This Month</option>
							<option>Last 6 Months</option>
							<option>This Year</option>
						</select>-->
          </div>
          <div class="card-body">
            <div class="row">
              <div
                class="col-sm-6 col-xl-4 d-flex align-items-center justify-content-center"
              >
                <div class="total-appointment">
                  <h3>{{ appointmentRequests.length }}</h3>
                  <p>Total appointments</p>
                </div>
              </div>
              <div class="col-sm-6 col-xl-4">
                <h5>Appointments by type</h5>
                <div class="appointment-stat">
                  <div class="appointment-stat-group">
                    <div class="appointment-stat-item">
                      <h4>
                        <i class="icon-a-phone"></i>
                        {{ phoneAppointments.length }}
                      </h4>
                      <p>By phone</p>
                    </div>
                    <div class="appointment-stat-item">
                      <h4>
                        <i class="icon-a-laptop"></i>
                        {{ onlineAppointments.length }}
                      </h4>
                      <p>Online</p>
                    </div>
                  </div>
                  <highcharts :options="appointmentsGraphs"></highcharts>
                </div>
              </div>
              <div class="col-sm-6 col-xl-4">
                <h5>New vs returning</h5>
                <div class="appointment-stat">
                  <div class="appointment-stat-group">
                    <div class="appointment-stat-item">
                      <h4>
                        <i class="icon-a-calendar1"></i>
                        {{ newAppointments.length }}
                      </h4>
                      <p>New appts.</p>
                    </div>
                    <div class="appointment-stat-item">
                      <h4>
                        <i class="icon-a-calendar2"></i>
                        {{ returnAppointments.length }}
                      </h4>
                      <p>Returning appts.</p>
                    </div>
                  </div>
                  <highcharts :options="appointmentsGraphs"></highcharts>
                </div>
              </div>
              <!-- <div class="col-sm-6 col-xl-3">
								<h3>Website vs web profiles</h3>
								<div class="appointment-stat">
									<div class="appointment-stat-group">
										<div class="appointment-stat-item">
											<h4><i class="icon-a-pc"></i> 2855</h4>
											<p>Website</p>
										</div>
										<div class="appointment-stat-item">
											<h4><i class="icon-a-web"></i> 1353</h4>
											<p>Web profiles</p>
										</div>
									</div>
									<highcharts :options="appointmentsGraphs"></highcharts>
								</div>
							</div>-->
            </div>
          </div>
        </div>

        <div
          class="card pmd_dashboard--appointments"
          v-if="
            leadSources &&
              user &&
              user.permissions.dashboard_stats_enabled !== false
          "
        >
          <div class="card hl_dashboard--latest-review-requests">
            <div class="card-header">
              <h2>Lead Source Report</h2>
              <div class="d-flex">
                <div style="width: 220px;">
                  <vue-ctk-date-time-picker
                    :locale="getCountryInfo('locale')"
                    v-model="opportunitiesFiltersDates"
                    :range="true"
                    :right="true"
                    color="#188bf6"
                    :only-date="true"
                    enable-button-validate
                    :noClearButton="true"
                    name="start_time"
                    @is-hidden="updateOpportunitiesFilters"
                    :formatted="getCountryDateFormat('month date year')"
                    id="random43423532"
                  />
                </div>
              </div>
            </div>
            <div class="card-body --no-padding">
              <div class="table-wrap">
                <table class="table table-sort">
                  <thead>
                    <tr>
                      <th data-sort="string">
                        Source
                        <!-- <i class="icon icon-arrow-down-1"></i> -->
                      </th>
                      <th data-sort="string">
                        Total Leads
                        <!-- <i class="icon icon-arrow-down-1"></i> -->
                      </th>
                      <th data-sort="string">
                        Total Values
                        <!-- <i class="icon icon-arrow-down-1"></i> -->
                      </th>
                      <th data-sort="string">
                        Open
                        <!-- <i class="icon icon-arrow-down-1"></i> -->
                      </th>
                      <th data-sort="string">
                        Won
                        <!-- <i class="icon icon-arrow-down-1"></i> -->
                      </th>
                      <th data-sort="string">
                        Lost
                        <!-- <i class="icon icon-arrow-down-1"></i> -->
                      </th>
                      <th data-sort="string">
                        Win %
                        <!-- <i class="icon icon-arrow-down-1"></i> -->
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="(value, key) in leadSources" :key="key">
                      <td style="overflow-wrap: break-word; max-width: 150px;">
                        {{ key | toTitleCase }}
                      </td>
                      <td>{{ value['total'] }}</td>
                      <td>
                        {{ newCurrency
                        }}{{ value['total_value'] | formatNumber }}
                      </td>
                      <td>{{ value['open'] }}</td>
                      <td>{{ value['won'] }}</td>
                      <td>{{ value['lost'] }}</td>
                      <td>
                        {{ getWonPercentage(value['won'], value['total']) }}%
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <div
          v-if="
            fbAdsData &&
              fbAdsTotals &&
              user &&
              user.permissions.dashboard_stats_enabled !== false &&
              user.permissions.facebook_ads_reporting_enabled
          "
          class="card pmd_dashboard--appointments"
        >
          <div class="card-header">
            <div style="display: flex;align-items:center;">
              <img src="/pmd/img/logo-facebook2.png" style="width: 30px;" />
              <span style="margin-left:10px;">
                <h2>
                  Facebook Ads
                  <span>Last 30 Days</span>
                </h2>
              </span>
            </div>
          </div>
          <div class="card-body">
            <div class="row">
              <div
                class="col-sm-6 col-xl-2 d-flex align-items-center justify-content-center"
              >
                <div class="total-appointment">
                  <h3>{{ fbAdsTotals.impressions | formatNumberNoDecimal }}</h3>
                  <p>Total impressions</p>
                </div>
              </div>
              <div
                class="col-sm-6 col-xl-2 d-flex align-items-center justify-content-center"
              >
                <div class="total-appointment">
                  <h3>{{ fbAdsTotals.clicks | formatNumberNoDecimal }}</h3>
                  <p>Total clicks</p>
                </div>
              </div>
              <div
                class="col-sm-6 col-xl-2 d-flex align-items-center justify-content-center"
              >
                <div class="total-appointment">
                  <h3>{{ fbAdsTotals.ctr }}</h3>
                  <p>CTR</p>
                </div>
              </div>
              <div
                class="col-sm-6 col-xl-2 d-flex align-items-center justify-content-center"
              >
                <div class="total-appointment">
                  <h3>
                    {{ location.country | symbole
                    }}{{ fbAdsTotals.cpc | formatNumber }}
                  </h3>
                  <p>Average CPC</p>
                </div>
              </div>
              <div
                class="col-sm-6 col-xl-2 d-flex align-items-center justify-content-center"
              >
                <div class="total-appointment">
                  <h3>
                    {{ location.country | symbole
                    }}{{ fbAdsTotals.spend | formatNumber }}
                  </h3>
                  <p>Total Spend</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div
          v-if="
            adwordsData &&
              adwordsTotals &&
              user &&
              user.permissions.dashboard_stats_enabled !== false
          "
          class="card pmd_dashboard--appointments"
        >
          <div class="card-header">
            <div style="display: flex;align-items:center;">
              <img src="/pmd/img/google_my_business.png" style="width: 30px;" />
              <span style="margin-left:10px;">
                <h2>
                  Google Ads
                  <span>Last 30 Days</span>
                </h2>
              </span>
            </div>
            <!-- <div class="dropdown bootstrap-select more-select">
							<select class="selectpicker more-select" tabindex="-98">
								<option>Today</option>
								<option>This Week</option>
								<option>Last Week</option>
								<option>This Month</option>
								<option>Last 6 Months</option>
								<option>This Year</option>
							</select>
							<button type="button" class="btn dropdown-toggle btn-light" data-toggle="dropdown" role="button" title="Today">
								<div class="filter-option">
									<div class="filter-option-inner">Today</div>
								</div>
							</button>
							<div class="dropdown-menu " role="combobox">
								<div class="inner show" role="listbox" aria-expanded="false" tabindex="-1">
									<ul class="dropdown-menu inner show"></ul>
								</div>
							</div>
						</div>-->
          </div>
          <div class="card-body">
            <div class="row">
              <div
                class="col-sm-6 col-xl-2 d-flex align-items-center justify-content-center"
              >
                <div class="total-appointment">
                  <h3>
                    {{ adwordsTotals.Impressions | formatNumberNoDecimal }}
                  </h3>
                  <p>Total impressions</p>
                </div>
              </div>
              <div
                class="col-sm-6 col-xl-2 d-flex align-items-center justify-content-center"
              >
                <div class="total-appointment">
                  <h3>{{ adwordsTotals.Clicks | formatNumberNoDecimal }}</h3>
                  <p>Total clicks</p>
                </div>
              </div>
              <div
                class="col-sm-6 col-xl-2 d-flex align-items-center justify-content-center"
              >
                <div class="total-appointment">
                  <h3>{{ adwordsTotals.CTR }}</h3>
                  <p>CTR</p>
                </div>
              </div>
              <div
                class="col-sm-6 col-xl-2 d-flex align-items-center justify-content-center"
              >
                <div class="total-appointment">
                  <h3>
                    {{ location.country | symbole
                    }}{{ (adwordsTotals.Avg[' CPC'] / 1000000) | formatNumber }}
                  </h3>
                  <p>Average CPC</p>
                </div>
              </div>
              <div
                class="col-sm-6 col-xl-2 d-flex align-items-center justify-content-center"
              >
                <div class="total-appointment">
                  <h3>
                    {{ location.country | symbole
                    }}{{ (adwordsTotals.Cost / 1000000) | formatNumber }}
                  </h3>
                  <p>Total Spend</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          v-if="
            gmbData &&
              user &&
              user.permissions.dashboard_stats_enabled !== false
          "
          class="card pmd_dashboard--appointments"
        >
          <div class="card-header">
            <div style="display: flex;align-items:center;">
              <img
                src="/pmd/img/google_my_business_512dp.png"
                style="width: 30px;"
              />
              <span style="margin-left:10px;">
                <h2>
                  Google My Business
                  <span>Last 30 Days</span>
                </h2>
              </span>
            </div>
            <!-- <div class="dropdown bootstrap-select more-select">
							<select class="selectpicker more-select" tabindex="-98">
								<option>Today</option>
								<option>This Week</option>
								<option>Last Week</option>
								<option>This Month</option>
								<option>Last 6 Months</option>
								<option>This Year</option>
							</select>
							<button type="button" class="btn dropdown-toggle btn-light" data-toggle="dropdown" role="button" title="Today">
								<div class="filter-option">
									<div class="filter-option-inner">Today</div>
								</div>
							</button>
							<div class="dropdown-menu " role="combobox">
								<div class="inner show" role="listbox" aria-expanded="false" tabindex="-1">
									<ul class="dropdown-menu inner show"></ul>
								</div>
							</div>
						</div>-->
          </div>
          <div class="card-body">
            <div class="row">
              <div
                class="col-sm-6 col-xl-3 d-flex align-items-center justify-content-center"
              >
                <div class="total-appointment">
                  <h3>{{ totalInteractions }}</h3>
                  <p>Total interactions</p>
                </div>
              </div>
              <div class="col-sm-6 col-xl-3">
                <h4>Views</h4>
                <div class="appointment-stat">
                  <div class="appointment-stat-group">
                    <div class="appointment-stat-item">
                      <h4>
                        <i class="icon-a-pc"></i>
                        {{ searchViews }}
                      </h4>
                      <p>Search Views</p>
                    </div>
                    <div class="appointment-stat-item">
                      <h4>
                        <i class="icon-a-web"></i>
                        {{ mapsViews }}
                      </h4>
                      <p>Maps Views</p>
                    </div>
                  </div>
                  <!-- <highcharts :options="googlepieoptions" :callback="chartLoaded" style="height:80px;width:80px;float:left;"></highcharts> -->
                </div>
              </div>
              <div class="col-sm-6 col-xl-3">
                <h4>Searches</h4>
                <div class="appointment-stat">
                  <div class="appointment-stat-group" style="float:left">
                    <div class="appointment-stat-item">
                      <h4>
                        <i class="icon-a-calendar1"></i>
                        {{ directQueries }}
                      </h4>
                      <p>Direct</p>
                    </div>
                    <div class="appointment-stat-item">
                      <h4>
                        <i class="icon-a-calendar2"></i>
                        {{ indirectQueries }}
                      </h4>
                      <p>Indirect</p>
                    </div>
                  </div>
                  <!-- <highcharts :options="googlepieoptions" :callback="chartLoaded" style="height:80px;width:80px;float:left;"></highcharts> -->
                </div>
              </div>
              <div class="col-sm-6 col-xl-3">
                <h4>Actions</h4>
                <div class="appointment-stat">
                  <div class="appointment-stat-group">
                    <div class="appointment-stat-item">
                      <h4>
                        <i class="icon-a-laptop"></i>
                        {{ actionsWebsite }}
                      </h4>
                      <p>Website visits</p>
                    </div>
                    <div class="appointment-stat-item">
                      <h4>
                        <i class="icon-a-phone"></i>
                        {{ actionsPhone }}
                      </h4>
                      <p>Calls</p>
                    </div>
                  </div>
                  <!-- <highcharts :options="googlepieoptions" :callback="chartLoaded" style="height:80px;width:80px;float:left;"></highcharts> -->
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          v-show="
            googleAnalyticsData &&
              user &&
              user.permissions.dashboard_stats_enabled !== false
          "
          class="card hl_dashboard--emails-sent"
        >
          <div class="card-header">
            <div style="display: flex;align-items:center;">
              <img
                src="/pmd/img/google_analytics_512dp.png"
                style="width: 30px;"
              />
              <span style="margin-left:10px;">
                <h2>
                  Google Analytics
                  <span>Last 12 Months</span>
                </h2>
              </span>
            </div>

            <!-- <select class="selectpicker more-select">
							<option>Today</option>
							<option>This Week</option>
							<option>Last Week</option>
							<option>This Month</option>
							<option>Last 6 Months</option>
							<option>This Year</option>
						</select>-->
          </div>
          <div class="card-body">
            <ul class="dashboard_emails-sent-chart-stats list-inline">
              <li class="list-inline-item">
                <h4>{{ totalPageViews | formatNumberNoDecimal }}</h4>
                <p>Total Page Views</p>
              </li>
              <li class="list-inline-item">
                <h4>{{ totalDirectViews | formatNumberNoDecimal }}</h4>
                <p>Direct</p>
              </li>
              <li class="list-inline-item">
                <h4>{{ totalPaidViews | formatNumberNoDecimal }}</h4>
                <p>Paid</p>
              </li>
              <li class="list-inline-item">
                <h4>{{ totalSocialViews | formatNumberNoDecimal }}</h4>
                <p>Social</p>
              </li>
              <li class="list-inline-item">
                <h4>{{ totalOrganicViews | formatNumberNoDecimal }}</h4>
                <p>Organic</p>
              </li>
            </ul>
            <highcharts
              :options="googleanalyticschartoptions"
              :callback="analyticsChartLoaded"
            ></highcharts>
          </div>
        </div>

        <div
          class="card hl_dashboard--website-visitors"
          v-if="
            googleAnalyticsDemographicsData &&
              user &&
              user.permissions.dashboard_stats_enabled !== false
          "
        >
          <div class="card-header">
            <h2>Website visitors</h2>
            <!-- <select class="selectpicker more-select">
							<option>Option 1</option>
							<option>Option 2</option>
							<option>Option 3</option>
						</select>-->
          </div>
          <div class="card-body">
            <div class="website-visitors--set1 row">
              <div class="col-xl-6">
                <h3>Conversion Rate</h3>
                <div class="conversion-rate">
                  <div class="total-visitor">
                    <h4>
                      <i class="icon icon-visitors"></i>
                      {{ totalUsers }}
                    </h4>
                    <p>Total visitors</p>
                  </div>
                  <div class="rate --blue">
                    <h4>
                      {{ usertoApptPercentage }}
                      <span>%</span>
                    </h4>
                    <p>Conversion</p>
                  </div>
                  <div class="appointments">
                    <h4>
                      <i class="icon icon-appointments"></i>
                      {{ appointmentRequests.length }}
                    </h4>
                    <p>Appointments</p>
                  </div>
                </div>
              </div>
              <div class="col-xl-6">
                <h3>Visitors by Device</h3>
                <div class="device_stats">
                  <div class="device_stats-item --green">
                    <h4><i class="icon icon-desktop"></i> Desktop</h4>
                    <div class="progress">
                      <div
                        class="progress-bar"
                        role="progressbar"
                        :style="'width:' + desktopPercentage + '%'"
                      ></div>
                      <span>{{ totalDesktopViewers }}</span>
                    </div>
                  </div>
                </div>
                <div class="device_stats">
                  <div class="device_stats-item --blue">
                    <h4><i class="icon icon-mobile"></i> Mobile</h4>
                    <div class="progress">
                      <div
                        class="progress-bar"
                        role="progressbar"
                        :style="'width:' + mobilePercentage + '%'"
                      ></div>
                      <span>{{ totalMobileViewers }}</span>
                    </div>
                  </div>
                </div>
                <div class="device_stats">
                  <div class="device_stats-item --yellow">
                    <h4><i class="icon icon-tablet"></i> Tablet</h4>
                    <div class="progress">
                      <div
                        class="progress-bar"
                        role="progressbar"
                        :style="'width:' + tabletPercentage + '%'"
                      ></div>
                      <span>{{ totalTabletViewers }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- <div class="website-visitors--set2 row">
							<div class="col-xl-8">

								<highcharts :options="visitorChartOptions" />

							</div>
							<div class="col-xl-4">
								<div class="new-visitors">
									<h4>$312.224</h4>
									<p>New visitors</p>
								</div>
								<div class="visitor-count-donut">
									<highcharts :options="visitorDonutOptions" />
									<h4>500K <span>Visitors</span></h4>

								</div>
								<div class="returning-visitors">
									<h4>$10.289</h4>
									<p>Returning visitors</p>
								</div>
							</div>
						</div>-->
            <div v-if="totalDemoUsers > 0" class="website-visitors--set3 row">
              <div class="col-xl-6">
                <h3>Visitors by Age</h3>
                <div class="age_stats">
                  <div class="age_stats-item --green">
                    <h4>65+</h4>
                    <div class="progress">
                      <div
                        class="progress-bar"
                        role="progressbar"
                        :style="'width: ' + percentage65plus + '%'"
                      ></div>
                      <span>{{ total65plus }}</span>
                    </div>
                  </div>
                </div>
                <div class="age_stats">
                  <div class="age_stats-item --blue">
                    <h4>55-64</h4>
                    <div class="progress">
                      <div
                        class="progress-bar"
                        role="progressbar"
                        :style="'width: ' + percentage55 + '%'"
                      ></div>
                      <span>{{ total55 }}</span>
                    </div>
                  </div>
                </div>
                <div class="age_stats">
                  <div class="age_stats-item --yellow">
                    <h4>45-54</h4>
                    <div class="progress">
                      <div
                        class="progress-bar"
                        role="progressbar"
                        :style="'width: ' + percentage45 + '%'"
                      ></div>
                      <span>{{ total45 }}</span>
                    </div>
                  </div>
                </div>
                <div class="age_stats">
                  <div class="age_stats-item --orange">
                    <h4>35-44</h4>
                    <div class="progress">
                      <div
                        class="progress-bar"
                        role="progressbar"
                        :style="'width: ' + percentage35 + '%'"
                      ></div>
                      <span>{{ total35 }}</span>
                    </div>
                  </div>
                </div>
                <div class="age_stats">
                  <div class="age_stats-item --red">
                    <h4>25-34</h4>
                    <div class="progress">
                      <div
                        class="progress-bar"
                        role="progressbar"
                        :style="'width: ' + percentage25 + '%'"
                      ></div>
                      <span>{{ total25 }}</span>
                    </div>
                  </div>
                </div>
                <div class="age_stats">
                  <div class="age_stats-item --purple">
                    <h4>18-24</h4>
                    <div class="progress">
                      <div
                        class="progress-bar"
                        role="progressbar"
                        :style="'width: ' + percentage18 + '%'"
                      ></div>
                      <span>{{ total18 }}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-xl-6">
                <h3>Visitors by Gender</h3>
                <div class="visitors-gender">
                  <div class="female-icon">
                    <i class="icon icon-female"></i>
                  </div>
                  <div class="gender-percentages">
                    <div class="gender-percentages-numbers">
                      <div class="gender-percentages-numbers-item">
                        <h4>{{ femalePercentage }}%</h4>
                        <p>{{ totalFemaleVisitors }}</p>
                      </div>
                      <div class="gender-percentages-numbers-item">
                        <h4>{{ malePercentage }}%</h4>
                        <p>{{ totalMaleVisitors }}</p>
                      </div>
                    </div>
                    <div class="gender-percentages-progress">
                      <div
                        class="progress --pink"
                        :style="'width:' + femalePercentage + '%'"
                      >
                        <div class="progress-bar" role="progressbar"></div>
                      </div>
                      <div
                        class="progress --blue"
                        :style="'width:' + malePercentage + '%'"
                      >
                        <div class="progress-bar" role="progressbar"></div>
                      </div>
                    </div>
                  </div>
                  <div class="male-icon">
                    <i class="icon icon-male"></i>
                  </div>
                </div>
                <!-- <highcharts :options="genderChartOptions" /> -->
              </div>
             
            </div>
          </div>
        </div>
        <div class="card" v-if="user && user.permissions.online_listings_enabled">
        <div class="card-header">
        </div>
        <div class="card-body" style="text-align:center">
            <h5>Online Listings have moved <router-link  :to="{name:'reputation_listing'}"> here</router-link></h5>
        </div>
      </div>
      </div>

    </section>

   
    <!-- END of .hl_dashboard -->
  </section>
</template>

<script lang="ts">
import Vue from 'vue'
import firebase from 'firebase/app'
import ListingHelper from '../../util/listing_helper'
import config from '@/config/'
import moment from 'moment-timezone'
import Datepicker from 'vuejs-datepicker'
import Highcharts from 'highcharts'
import highchartsMore from 'highcharts/highcharts-more'
import highchartsFunnel from 'highcharts/modules/funnel'
import { mapState } from 'vuex'
import { Status } from '@/models/opportunity'
import { Chart } from 'highcharts-vue'
import { UserState } from '../../store/state_models'
import {
  User,
  ReviewRequest,
  Location,
  OAuth2,
  Pipeline,
  AppointmentRequest,
  getCountryDateFormat,
  getCountryInfo
} from '../../models'
import MedallionGmbIcon from '/public/pmd/img/logo_medallions/medallion-google-my-business.svg'
import MedallionAppleIcon from '/public/pmd/img/logo_medallions/medallion-apple.svg'
import MedallionBingIcon from '/public/pmd/img/logo_medallions/medallion-bing.svg'
import MedallionFacebookIcon from '/public/pmd/img/logo_medallions/medallion-facebook.svg'
import MedallionYelpIcon from '/public/pmd/img/logo_medallions/medallion-yelp.svg'
import MedallionFoursquareIcon from '/public/pmd/img/logo_medallions/medallion-foursquare.svg'
import MedallionSuperpagesIcon from '/public/pmd/img/logo_medallions/medallion-superpages.svg' 
import MedallionYahooIcon from '/public/pmd/img/logo_medallions/medallion-yahoo.svg'
import MedallionCitysearch from '/public/pmd/img/logo_medallions/medallion-citysearch.svg'

const TaskListComponent = () =>
  import('@/pmd/components/agency/TaskListComponent.vue').then(m => m.default)
const PendingManualActionCard = () =>
  import('@/pmd/components/agency/PendingManualActionCard.vue').then(
    m => m.default
  )

const YextPromoDashboardBanner = () =>
  import('@/pmd/components/online_listing/YextPromoDashboardBanner.vue').then(
    m => m.default
  )

  const WordpressPromoDashboardBanner = () =>
  import('@/pmd/components/wordpress/WordpressPromoDashboardBanner.vue').then(
    m => m.default
  )

const store = require('store')

highchartsMore(Highcharts)
highchartsFunnel(Highcharts)

let reviewRequestsListener: () => void
let cancelGoogleSubscription: () => void

export default Vue.extend({
  name: 'LocationDashboard',
  components: {
    highcharts: Chart,
    TaskListComponent,
    PendingManualActionCard,
    YextPromoDashboardBanner,
    WordpressPromoDashboardBanner,
    Datepicker,
    MedallionGmbIcon,
		MedallionAppleIcon,
		MedallionBingIcon,
		MedallionFacebookIcon,
		MedallionYelpIcon,
		MedallionFoursquareIcon,
		MedallionSuperpagesIcon,
		MedallionYahooIcon,
		MedallionCitysearch,
  },
  data() {
    let self = this
    return {
      getCountryInfo,
      opportunitiesSeqCalling: 0,
      opportunitiesFiltersDates: {
        start: moment()
          .subtract(6, 'months')
          .toDate(),
        end: moment()
          .add(1, 'days')
          .toDate()
      },
      opportunitiesFilters: {
        startTime: moment()
          .subtract(6, 'months')
          .toDate()
          .getTime(),
        endTime: moment()
          .add(1, 'days')
          .toDate()
          .getTime()
        // disabledStartDate: {},
        // disabledEndDate: {},
        // dateMoment: moment().subtract(6, 'months').toDate(),
        // endDateMoment: moment().add(1, 'days').toDate(),
        // date: moment().subtract(6, 'months').toDate().getTime(),
        // endDate: moment().add(1, 'days').toDate().getTime()
      },
      currentLocationId: '',
      chartOptions: {
        chart: {
          type: 'pie',
          plotBackgroundColor: null
        },
        credits: {
          enabled: !1
        },
        title: {
          text: ''
        },
        plotOptions: {
          pie: {
            allowPointSelect: !0,
            cursor: 'pointer',
            center: ['50%', '50%'],
            innerSize: '70%',
            startAngle: 180,
            dataLabels: {
              style: {
                fontWeight: 'normal'
              },
              formatter: function() {
                return this.point.hideLabel
                  ? null
                  : '\x3cb\x3e' +
                      this.point.name +
                      '\x3c/b\x3e ' +
                      (this.point.labelValue || this.point.y) +
                      '%'
              }
            },
            showInLegend: !0
          }
        },
        legend: {
          symbolRadius: 3,
          margin: 30,
          squareSymbol: !0
        },
        series: [
          {
            type: 'pie',
            name: 'Listings'
          }
        ],
        tooltip: {
          valueSuffix: '%'
        }
      },
      googlepieoptions: {
        chart: {
          plotBackgroundColor: null,
          plotBorderWidth: 0,
          plotShadow: false,
          margin: 0,
          spacingTop: 0,
          spacingBottom: 0,
          spacingLeft: 0,
          spacingRight: 0,
          plotAreaWidth: 80,
          plotAreaHeight: 80
        },
        credits: {
          enabled: !1
        },
        title: {
          text: ''
        },
        tooltip: {
          pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
        },
        plotOptions: {
          pie: {
            dataLabels: {
              enabled: false,
              distance: -50,
              style: {
                // fontWeight: 'bold',
                // color: 'white',
                // textShadow: '0px 1px 2px black'
              }
            },
            startAngle: -180,
            endAngle: 180,
            // center: ['100%', '100%'],
            size: '100%',
            slicedOffset: 0
          }
        },
        series: [
          {
            type: 'pie',
            name: 'Browser share',
            innerSize: '90%',
            data: [
              ['consumed', 75],
              ['left to prognosis', 25]
            ]
          }
        ]
      },
      googleanalyticschartoptions: {
        chart: {
          type: 'column',
          style: {
            fontFamily: 'Roboto'
          }
        },
        credits: {
          enabled: !1
        },
        plotOptions: {
          column: {
            groupPadding: 0.35,
            borderWidth: 0
          },
          series: {
            pointWidth: 6,
            borderRadius: 5
          }
        },
        legend: {
          enabled: false
        },
        title: {
          text: ''
        },
        xAxis: {
          lineColor: '#dfe3e4',
          tickColor: '#fff',
          categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Jul', 'Sun'],
          labels: {
            style: {
              color: '#607179',
              fontSize: '14px'
            }
          }
        },
        yAxis: {
          gridLineColor: '#dfe3e4',
          title: {
            text: ''
          },
          labels: {
            style: {
              color: '#607179',
              fontSize: '14px'
            }
          }
        },
        series: []
      },
      funnelchartoptions: {
        dataLabels: {
          crop: true,
          enabled: false
        },
        colors: [
          '#188bf6',
          '#37ca37',
          '#e93d3d',
          '#ffbc00',
          '#1098ad',
          '#f76707',
          '#495057',
          '#e64980',
          '#cc5de8',
          '#d8f5a2',
          '#087f5b',
          '#ffa8a8',
          '#b2f2bb',
          '#dee2e6'
        ],
        chart: {
          type: 'funnel',
          style: {
            fontFamily: 'Roboto'
          }
          // events: {
          // 	redraw: function () {
          // 		console.log("Funnael chart loiaded!")
          // 		window.vue.populateOpportunitySources();
          // 	}
          // },
        },
        title: {
          text: '',
          style: {
            color: '#2a3135'
          }
        },
        plotOptions: {
          series: {
            showInLegend: true,
            dataLabels: {
              enabled: false
            },
            center: ['50%', '50%'],
            neckWidth: '30%',
            neckHeight: '25%',
            width: '90%'
          }
        },
        legend: {
          enabled: true,
          layout: 'horizontal',
          align: 'center',
          verticalAlign: 'bottom',
          labelFormatter: function() {
            if (self.user && self.user.permissions.lead_value_enabled === false)
              return (
                '(' +
                this.y +
                ') <b>' +
                this.name +
                (this.conversationRate !== null
                  ? ` (${Number(this.conversationRate).toLocaleString(
                      this.locale,
                      { maximumFractionDigits: 2, minimumFractionDigits: 2 }
                    )}%)`
                  : '')
              )
            else
              return (
                '(' +
                this.y +
                ') <b>' +
                this.name +
                '</b> - ' +
                ` ${this.currency}` +
                ' ' +
                Number(this.monetaryValue).toLocaleString(this.locale, {
                  maximumFractionDigits: 2,
                  minimumFractionDigits: 2
                }) +
                (this.conversationRate !== null
                  ? ` (${Number(this.conversationRate).toLocaleString(
                      this.locale,
                      { maximumFractionDigits: 2, minimumFractionDigits: 2 }
                    )}%)`
                  : '')
              )
          }
        },
        series: []
      },
      piechartoptions: {
        dataLabels: {
          crop: true,
          enabled: false
        },
        colors: [
          '#188bf6',
          '#37ca37',
          '#e93d3d',
          '#ffbc00',
          '#1098ad',
          '#f76707',
          '#495057',
          '#e64980',
          '#cc5de8',
          '#d8f5a2',
          '#087f5b',
          '#ffa8a8',
          '#b2f2bb',
          '#dee2e6'
        ],
        chart: {
          type: 'pie',
          style: {
            fontFamily: 'Roboto'
          }
          // events: {
          // 	redraw: function () {
          // 		console.log("Funnael chart loiaded!")
          // 		window.vue.populateOpportunitySources();
          // 	}
          // },
        },
        title: {
          text: '',
          style: {
            color: '#2a3135'
          }
        },
        plotOptions: {
          series: {
            showInLegend: true,
            dataLabels: {
              enabled: false
            },
            center: ['50%', '50%'],
            neckWidth: '30%',
            neckHeight: '25%',
            width: '90%'
          }
        },
        legend: {
          enabled: true,
          layout: 'horizontal',
          align: 'center',
          verticalAlign: 'bottom',
          labelFormatter: function() {
            if (self.user && self.user.permissions.lead_value_enabled === false)
              return (
                '(' +
                this.y +
                ') <b>' +
                this.name +
                (this.conversationRate !== null
                  ? ` (${Number(this.conversationRate).toLocaleString(
                      this.locale,
                      { maximumFractionDigits: 2, minimumFractionDigits: 2 }
                    )}%)`
                  : '')
              )
            else
              return (
                '(' +
                this.y +
                ') <b>' +
                this.name +
                '</b> - ' +
                ` ${this.currency}` +
                ' ' +
                Number(this.monetaryValue).toLocaleString(this.locale, {
                  maximumFractionDigits: 2,
                  minimumFractionDigits: 2
                }) +
                (this.conversationRate !== null
                  ? ` (${Number(this.conversationRate).toLocaleString(
                      this.locale,
                      { maximumFractionDigits: 2, minimumFractionDigits: 2 }
                    )}%)`
                  : '')
              )
          }
        },
        series: []
      },
      appointmentsGraphs: {
        chart: {
          plotBackgroundColor: null,
          plotBorderWidth: 0,
          plotShadow: false,
          margin: 0,
          spacingTop: 0,
          spacingBottom: 0,
          spacingLeft: 0,
          spacingRight: 0,
          plotAreaWidth: 80,
          plotAreaHeight: 80,
          width: 120,
          height: 120
        },
        credits: {
          enabled: !1
        },
        title: {
          text: '',
          align: 'center',
          verticalAlign: 'middle',
          y: 40
        },
        tooltip: {
          pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>',
          outside: true
        },
        plotOptions: {
          pie: {
            dataLabels: {
              enabled: false
            }
          }
        },
        series: [
          {
            type: 'pie',
            name: 'Visitors',
            innerSize: '80%',
            data: [
              {
                name: 'New Visitors',
                y: 40,
                color: '#ffbc00'
              },
              {
                name: 'Returning Visitors',
                y: 50,
                color: '#188bf6'
              }
            ]
          }
        ]
      },
      visitorChartOptions: {
        chart: {
          type: 'column',
          style: {
            fontFamily: 'Roboto'
          }
        },
        plotOptions: {
          column: {
            stacking: 'normal'
          },
          series: {
            pointWidth: 20
          }
        },
        legend: {
          enabled: false
        },
        credits: {
          enabled: !1
        },
        title: {
          text: ''
        },
        xAxis: {
          lineColor: '#dfe3e4',
          tickColor: '#fff',
          categories: ['Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep'],
          labels: {
            style: {
              color: '#607179',
              fontSize: '14px'
            }
          }
        },
        yAxis: {
          gridLineColor: '#dfe3e4',
          title: {
            text: ''
          },
          labels: {
            style: {
              color: '#607179',
              fontSize: '14px'
            }
          }
        },
        series: [
          {
            name: 'New Visitors',
            data: [5000, 3000, 4000, 7000, 2000, 3000, 8000],
            color: '#ffbc00'
          },
          {
            name: 'Returning Visitors',
            data: [2000, 2000, 3000, 2000, 1000, 5000, 6000],
            color: '#188bf6'
          }
        ]
      },
      visitorDonutOptions: {
        chart: {
          plotBackgroundColor: null,
          plotBorderWidth: 0,
          plotShadow: false,
          style: {
            fontFamily: 'Roboto'
          },

          margin: 0,
          spacingTop: 0,
          spacingBottom: 0,
          spacingLeft: 0,
          spacingRight: 0,
          plotAreaWidth: 200,
          plotAreaHeight: 200,
          width: 200,
          height: 200,
          align: 'center',
          events: {
            load: function(event) {
              // var chart = this,
              // 	points = chart.series[0].points,
              // 	len = points.length,
              // 	total = 0,
              // 	i = 0;
              // for (i = 0; i < len; i++) {
              // 	total += points[i].y;
              // }
              // chart.setTitle({
              // 	text: '<br>€' + total,
              // 	verticalAlign: 'middle',
              // 	style: {
              // 		fontFamily: 'Arial,Roboto,Helvetica,sans-serif',
              // 		fontWeight: 'bold',
              // 		fontSize: 34
              // 	},
              // });
              // Adding 'transaction' label - labels below don't support images/icons
            }
          }
        },
        title: {
          text: '',
          align: 'center',
          verticalAlign: 'middle',
          y: 40
        },
        tooltip: {
          pointFormat: '{series.name}: <b>{point.percentage:.1f}%</b>'
        },
        plotOptions: {
          pie: {
            dataLabels: {
              enabled: false
            }
          }
        },
        credits: {
          enabled: !1
        },
        series: [
          {
            type: 'pie',
            name: 'Visitors',
            innerSize: '80%',
            data: [
              {
                name: 'New Visitors',
                y: 40,
                color: '#ffbc00'
              },
              {
                name: 'Returning Visitors',
                y: 50,
                color: '#188bf6'
              }
            ]
          }
        ]
      },
      genderChartOptions: {
        chart: {
          height: 150,
          type: 'column',
          style: {
            fontFamily: 'Roboto'
          }
        },
        plotOptions: {
          column: {
            groupPadding: 0.35,
            borderWidth: 0
          },
          series: {
            pointWidth: 6,
            borderRadius: 5
          }
        },
        legend: {
          enabled: false
        },
        title: {
          text: ''
        },
        credits: {
          enabled: !1
        },
        xAxis: {
          lineColor: '#dfe3e4',
          tickColor: '#fff',
          categories: ['Mar', 'Apr', 'May', 'Jun', 'Jul'],
          labels: {
            style: {
              color: '#607179',
              fontSize: '14px'
            }
          }
        },
        yAxis: {
          gridLineColor: '#dfe3e4',
          title: {
            text: ''
          },
          labels: {
            style: {
              color: '#607179',
              fontSize: '14px'
            }
          }
        },
        series: [
          {
            name: 'Male',
            type: 'spline',
            data: [1500, 2000, 3000, 2500, 1800],
            color: '#188bf6'
          },
          {
            name: 'Female',
            type: 'spline',
            data: [1000, 2500, 2000, 3000, 3500],
            color: '#ff3e7f'
          }
        ]
      },
      theChart: {},
      googleAnalyticsChart: {},
      funnelChart: undefined as undefined | {},
      pieChart: undefined as undefined | {},
      totalCount: 0,
      liveCount: 0,
      processingCount: 0,
      unavCount: 0,
      optedCount: 0,
      listings: [] as { [key: string]: any }[] | undefined,
      location: {} as Location,
      googleConnection: undefined as OAuth2 | undefined,
      gmbData: undefined as undefined | {},
      adwordsData: undefined as undefined | {},
      fbAdsData: undefined as undefined | {},
      googleAnalyticsData: undefined as undefined | {},
      googleAnalyticsDemographicsData: undefined as undefined | {},
      appointmentRequests: [] as AppointmentRequest[],
      visitorDonutChart: {},
      pipelines: [] as Pipeline[],
      // piePipelines: [] as Pipeline[],
      // funnelPipelines: [] as Pipeline[],
      filterPipeline: undefined as undefined | Pipeline,
      // pieFilterPipeline: undefined as undefined | Pipeline,
      leadSources: undefined as { [key: string]: any } | undefined,
      stages: [] as string[],
      opportunitiesAggrs: {
        counts: {
          won: 0,
          closed: 0,
          lost: 0
        },
        revenues: {
          won: 0,
          closed: 0,
          lost: 0
        },
        pipelines: []
      },
      getCountryDateFormat: getCountryDateFormat
    }
  },
  watch: {
    '$route.params.location_id': async function(id) {
      this.currentLocationId = id
      if (id) {
        this.fetchData()
      }
    },
    filterPipeline: function(filterPipeline) {
      if (filterPipeline) {
        store.set(`pipeline-${this.currentLocationId}`, {
          id: filterPipeline.id
        })
      }
      this.filterOppurtunities()
      // this.filterPieOppurtunities();
    },
    // 'pieFilterPipeline': function (filterPipeline) {
    // 	store.set(`pie-pipeline-${this.currentLocationId}`, { id: filterPipeline.id });
    // 	this.filterPieOppurtunities();
    // },
    theChart() {
      if (this.theChart) {
        this.populateChart()
      }
    },
    googleAnalyticsData() {
      if (this.googleAnalyticsChart) {
        this.populateAnalyticsChart()
      }
    },
    filteredAggrOpportunities() {
      if (this.funnelChart) {
        // console.log("opps loaded", this.funnelChart)
        this.populateFunnelChart()
      }
      if (this.pieChart) {
        // console.log("opps loaded", this.pieChart)
        this.populatePieChart()
      }
    },
    // filteredPieAggrOpportunities() {
    // 	if(this.pieChart){
    // 		console.log("opps loaded", this.pieChart)
    // 		this.populatePieChart();
    // 	}
    // },

    canAccessAll() {
      this.loadOpportunityAggregate()
    },
    user() {
      this.loadOpportunityAggregate()
    },
    googleOAuth() {
      this.loadGoogleData()
    }
  },
  async created() {
    let _self = this
    this.currentLocationId = this.$router.currentRoute.params.location_id;
  },
  async mounted() {
    // console.log("Mounted");

    // console.log("Befoee fetch data");
    await this.fetchData()
    // console.log("after fetch data");

    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  async updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  computed: {
    sideBarState() {
      return this.$store.getters.getManualCollapseSidebar
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
    canAccessAll(): boolean {
      return this.user && this.user.permissions.assigned_data_only !== true
    },
    newCurrency() {
      return this.$options.filters.symbole(this.location.country)
    },
    gmbLocationmetrics() {
      if (
        this.gmbData &&
        this.gmbData.locationMetrics &&
        this.gmbData.locationMetrics.length > 0
      ) {
        return this.gmbData.locationMetrics[0].metricValues
      }
    },
    adwordsTotals() {
      if (this.adwordsData && this.adwordsData.length > 0) {
        return this.adwordsData[this.adwordsData.length - 1]
      }
    },
    fbAdsTotals() {
      let data = {
        impressions: 0,
        clicks: 0,
        ctr: 0,
        cpc: 0,
        spend: 0,
        conversions: 0,
        count: 0
      }

      if (this.fbAdsData && this.fbAdsData.length > 0) {
        for (var row of this.fbAdsData) {
          if (
            row &&
            row.insights &&
            row.insights.data &&
            row.insights.data.length > 0
          ) {
            let dataRow = row.insights.data[0]

            if (dataRow.impressions)
              data.impressions += Number(dataRow.impressions)
            if (dataRow.clicks) data.clicks += Number(dataRow.clicks)
            if (dataRow.ctr) data.ctr += Number(dataRow.ctr)
            if (dataRow.cpc) data.cpc += Number(dataRow.cpc)
            if (dataRow.spend) data.spend += Number(dataRow.spend)
            data.count += 1

            if (dataRow.conversions && dataRow.conversions.length > 0) {
              for (var conversionRow of dataRow.conversions) {
              }
            }
          }
        }
      }
      data.ctr = Number(data.ctr.toFixed(2))
      return data
    },
    directQueries() {
      let total = 0
      if (this.gmbLocationmetrics) {
        let obj = lodash.find(this.gmbLocationmetrics, {
          metric: 'QUERIES_DIRECT'
        })
        if (obj) {
          total = Number(obj.totalValue.value)
        }
      }
      return total
    },
    indirectQueries() {
      let total = 0
      if (this.gmbLocationmetrics) {
        let obj = lodash.find(this.gmbLocationmetrics, {
          metric: 'QUERIES_INDIRECT'
        })
        if (obj) {
          total = Number(obj.totalValue.value)
        }
      }
      return total
    },
    actionsWebsite() {
      let total = 0
      if (this.gmbLocationmetrics) {
        let obj = lodash.find(this.gmbLocationmetrics, {
          metric: 'ACTIONS_WEBSITE'
        })
        if (obj) {
          total = Number(obj.totalValue.value)
        }
      }
      return total
    },
    actionsPhone() {
      let total = 0
      if (this.gmbLocationmetrics) {
        let obj = lodash.find(this.gmbLocationmetrics, {
          metric: 'ACTIONS_PHONE'
        })
        if (obj) {
          total = Number(obj.totalValue.value)
        }
      }
      return total
    },
    searchViews() {
      let total = 0
      if (this.gmbLocationmetrics) {
        let obj = lodash.find(this.gmbLocationmetrics, {
          metric: 'VIEWS_SEARCH'
        })
        if (obj) {
          total = Number(obj.totalValue.value)
        }
      }
      return total
    },
    mapsViews() {
      let total = 0
      if (this.gmbLocationmetrics) {
        let obj = lodash.find(this.gmbLocationmetrics, {
          metric: 'VIEWS_MAPS'
        })
        if (obj) {
          total = Number(obj.totalValue.value)
        }
      }
      return total
    },
    totalInteractions() {
      let total = 0
      total +=
        this.directQueries +
        this.indirectQueries +
        this.actionsWebsite +
        this.actionsPhone +
        this.searchViews +
        this.mapsViews
      return total
    },
    totalPageViews() {
      let total = 0
      if (
        this.googleAnalyticsData &&
        this.googleAnalyticsData.rows &&
        this.googleAnalyticsData.rows.length > 0
      ) {
        this.googleAnalyticsData.rows.forEach(element => {
          total += Number(element[2])
        })
      }
      return total
    },
    totalDirectViews() {
      let total = 0
      if (
        this.googleAnalyticsData &&
        this.googleAnalyticsData.rows &&
        this.googleAnalyticsData.rows.length > 0
      ) {
        this.googleAnalyticsData.rows.forEach(element => {
          if (element[0] == 'Direct') {
            total += Number(element[2])
          }
        })
      }
      return total
    },
    totalOrganicViews() {
      let total = 0
      if (
        this.googleAnalyticsData &&
        this.googleAnalyticsData.rows &&
        this.googleAnalyticsData.rows.length > 0
      ) {
        this.googleAnalyticsData.rows.forEach(element => {
          if (element[0] == 'Organic Search') {
            total += Number(element[2])
          }
        })
      }
      return total
    },
    totalPaidViews() {
      let total = 0
      if (
        this.googleAnalyticsData &&
        this.googleAnalyticsData.rows &&
        this.googleAnalyticsData.rows.length > 0
      ) {
        this.googleAnalyticsData.rows.forEach(element => {
          if (element[0] == 'Paid Search' || element[0] == 'Display') {
            total += Number(element[2])
          }
        })
      }
      return total
    },
    totalSocialViews() {
      let total = 0
      if (
        this.googleAnalyticsData &&
        this.googleAnalyticsData.rows &&
        this.googleAnalyticsData.rows.length > 0
      ) {
        this.googleAnalyticsData.rows.forEach(element => {
          if (element[0] == 'Social') {
            total += Number(element[2])
          }
        })
      }
      return total
    },
    totalOpportunitiesCount(): number {
      return lodash.sum(lodash.values(this.opportunitiesAggrs.counts))
    },
    totalOpportunitiesRevenues(): number {
      return lodash.sum(lodash.values(this.opportunitiesAggrs.revenues))
    },
    conversionRate() {
      let rate = 0
      if (
        this.opportunitiesAggrs.counts.closed > 0 &&
        this.totalOpportunitiesCount > 0
      ) {
        rate = Number(
          (
            (this.opportunitiesAggrs.counts.closed /
              this.totalOpportunitiesCount) *
            100
          ).toFixed(2)
        )
      }
      return rate
    },
    filteredAggrOpportunities() {
      if (!this.filterPipeline || !this.opportunitiesAggrs.pipelines.length)
        return []

      return this.opportunitiesAggrs.pipelines.filter(x => {
        var stageIndex = this.filterPipeline.stages.findIndex(
          y => y.id === x.id
        )
        if (stageIndex !== -1) {
          return true
        } else return false
      })
    },
    // filteredPieAggrOpportunities() {
    // 	if (!this.pieFilterPipeline || !this.opportunitiesAggrs.pipelines.length) return [];

    // 	return this.opportunitiesAggrs.pipelines.filter(x => {
    // 		return this.pieFilterPipeline.stages.findIndex(y => y.id === x.id) !== -1;
    // 	});
    // },
    filteredOpportunitiesCount() {
      if (!this.filteredAggrOpportunities.length) return 0

      return lodash.sumBy(
        this.filteredAggrOpportunities,
        x => x.counts.open + x.counts.closed + x.counts.lost
      )
    },
    filteredOpportunitiesPieCount() {
      if (!this.filteredAggrOpportunities.length) return 0

      return lodash.sumBy(
        this.filteredAggrOpportunities,
        x => x.counts.open + x.counts.closed + x.counts.lost
      )
    },
    phoneAppointments(): AppointmentRequest[] {
      return lodash.filter(this.appointmentRequests, {
        channel: AppointmentRequest.CHANNEL_PHONE
      })
    },
    onlineAppointments(): AppointmentRequest[] {
      return lodash.filter(this.appointmentRequests, {
        channel: AppointmentRequest.CHANNEL_WEB
      })
    },
    newAppointments(): AppointmentRequest[] {
      return lodash.filter(this.appointmentRequests, function(request) {
        return !request.existing
      })
    },
    returnAppointments(): AppointmentRequest[] {
      return lodash.filter(this.appointmentRequests, function(request) {
        return request.existing
      })
    },
    totalDesktopViewers(): number {
      let desktopViewers = 0
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.rows
      ) {
        for (var row of this.googleAnalyticsDemographicsData.rows) {
          if (row[2] == 'desktop') {
            desktopViewers += Number(row[3])
          }
        }
      }
      return desktopViewers
    },
    totalMobileViewers(): number {
      let desktopViewers = 0
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.rows
      ) {
        for (var row of this.googleAnalyticsDemographicsData.rows) {
          if (row[2] == 'mobile') {
            desktopViewers += Number(row[3])
          }
        }
      }
      return desktopViewers
    },
    totalTabletViewers(): number {
      let desktopViewers = 0
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.rows
      ) {
        for (var row of this.googleAnalyticsDemographicsData.rows) {
          if (row[2] == 'tablet') {
            desktopViewers += Number(row[3])
          }
        }
      }
      return desktopViewers
    },
    desktopPercentage(): string {
      return (
        (this.totalDesktopViewers /
          (this.totalDesktopViewers +
            this.totalMobileViewers +
            this.totalTabletViewers)) *
        100
      ).toFixed(2)
    },
    mobilePercentage(): string {
      return (
        (this.totalMobileViewers /
          (this.totalDesktopViewers +
            this.totalMobileViewers +
            this.totalTabletViewers)) *
        100
      ).toFixed(2)
    },
    tabletPercentage(): string {
      return (
        (this.totalTabletViewers /
          (this.totalDesktopViewers +
            this.totalMobileViewers +
            this.totalTabletViewers)) *
        100
      ).toFixed(2)
    },
    totalSessions(): number {
      let total = 0
      if (
        this.googleAnalyticsData &&
        this.googleAnalyticsData.totalsForAllResults
      ) {
        total = Number(
          this.googleAnalyticsData.totalsForAllResults['ga:sessions']
        )
      }
      return total
    },
    totalUsers(): number {
      let total = 0
      if (
        this.googleAnalyticsData &&
        this.googleAnalyticsData.totalsForAllResults
      ) {
        total = Number(this.googleAnalyticsData.totalsForAllResults['ga:users'])
      }
      return total
    },
    usertoApptPercentage(): string {
      return (
        ((this.appointmentRequests.length || 0) / (this.totalUsers || 1)) *
        100
      ).toFixed(2)
    },
    totalMaleVisitors(): number {
      let desktopViewers = 0
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.rows
      ) {
        for (var row of this.googleAnalyticsDemographicsData.rows) {
          if (row[0] == 'male') {
            desktopViewers += Number(row[3])
          }
        }
      }
      return desktopViewers
    },
    totalFemaleVisitors(): number {
      let desktopViewers = 0
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.rows
      ) {
        for (var row of this.googleAnalyticsDemographicsData.rows) {
          if (row[0] == 'female') {
            desktopViewers += Number(row[3])
          }
        }
      }
      return desktopViewers
    },
    malePercentage(): string {
      return (
        (this.totalMaleVisitors /
          (this.totalMaleVisitors + this.totalFemaleVisitors)) *
        100
      ).toFixed(0)
    },
    femalePercentage(): string {
      return (
        (this.totalFemaleVisitors /
          (this.totalMaleVisitors + this.totalFemaleVisitors)) *
        100
      ).toFixed(0)
    },
    totalDemoUsers(): number {
      let totalUsers = 0
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.totalsForAllResults
      ) {
        totalUsers = Number(
          this.googleAnalyticsDemographicsData.totalsForAllResults['ga:users']
        )
      }
      return totalUsers
    },
    total65plus(): number {
      let total = 0
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.rows
      ) {
        for (var row of this.googleAnalyticsDemographicsData.rows) {
          if (row[1] == '65+') {
            total += Number(row[3])
          }
        }
      }
      return total
    },
    percentage65plus(): string {
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.totalsForAllResults
      ) {
        return ((this.total65plus / this.totalDemoUsers) * 100).toFixed(0)
      } else {
        return '0'
      }
    },
    total55(): number {
      let total = 0
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.rows
      ) {
        for (var row of this.googleAnalyticsDemographicsData.rows) {
          if (row[1] == '55-64') {
            total += Number(row[3])
          }
        }
      }
      return total
    },
    percentage55(): string {
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.totalsForAllResults
      ) {
        return ((this.total55 / this.totalDemoUsers) * 100).toFixed(0)
      } else {
        return '0'
      }
    },
    total45(): number {
      let total = 0
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.rows
      ) {
        for (var row of this.googleAnalyticsDemographicsData.rows) {
          if (row[1] == '45-54') {
            total += Number(row[3])
          }
        }
      }
      return total
    },
    percentage45(): string {
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.totalsForAllResults
      ) {
        return ((this.total45 / this.totalDemoUsers) * 100).toFixed(0)
      } else {
        return '0'
      }
    },
    total35(): number {
      let total = 0
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.rows
      ) {
        for (var row of this.googleAnalyticsDemographicsData.rows) {
          if (row[1] == '35-44') {
            total += Number(row[3])
          }
        }
      }
      return total
    },
    percentage35(): string {
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.totalsForAllResults
      ) {
        return ((this.total35 / this.totalDemoUsers) * 100).toFixed(0)
      } else {
        return '0'
      }
    },
    total25(): number {
      let total = 0
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.rows
      ) {
        for (var row of this.googleAnalyticsDemographicsData.rows) {
          if (row[1] == '25-34') {
            total += Number(row[3])
          }
        }
      }
      return total
    },
    percentage25(): string {
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.totalsForAllResults
      ) {
        return ((this.total25 / this.totalDemoUsers) * 100).toFixed(0)
      } else {
        return '0'
      }
    },
    total18(): number {
      let total = 0
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.rows
      ) {
        for (var row of this.googleAnalyticsDemographicsData.rows) {
          if (row[1] == '18-24') {
            total += Number(row[3])
          }
        }
      }
      return total
    },
    percentage18(): string {
      if (
        this.googleAnalyticsDemographicsData &&
        this.googleAnalyticsDemographicsData.totalsForAllResults
      ) {
        return ((this.total18 / this.totalDemoUsers) * 100).toFixed(0)
      } else {
        return '0'
      }
    },
    googleOAuth() {
      return this.$store.getters['oauth2/getByType'](OAuth2.TYPE_GOOGLE)
    }
  },
  methods: {
    updateOpportunitiesFilters() {
      var dateFilters = {}
      if (!this.opportunitiesFiltersDates.end) {
        this.opportunitiesFiltersDates.end = this.opportunitiesFiltersDates.start
        dateFilters['endTime'] = moment(
          this.opportunitiesFiltersDates.end,
          'YYYY-MM-DD hh:mm a'
        )
          .add(1, 'days')
          .toDate()
          .getTime()
      } else {
        dateFilters['endTime'] = moment(
          this.opportunitiesFiltersDates.end,
          'YYYY-MM-DD hh:mm a'
        )
          .toDate()
          .getTime()
      }
      dateFilters['startTime'] = moment(
        this.opportunitiesFiltersDates.start,
        'YYYY-MM-DD hh:mm a'
      )
        .toDate()
        .getTime()
      this.opportunitiesFilters = { ...dateFilters }
      this.loadOpportunityAggregate()
    },
    async fetchData() {
      let _self = this
      await Promise.all([
        this.$store.dispatch('pipelines/syncAll', this.$route.params.location_id),
        this.$store.dispatch('oauth2/syncAll', this.$route.params.location_id)
      ])

      if (reviewRequestsListener) {
        reviewRequestsListener()
      }
      // console.log("Getting location:", this.currentLocationId);
      this.location = new Location(
        await this.$store.dispatch('locations/getById', this.currentLocationId)
      )
      // console.log("Got location:", this.location);

      if (this.user) {
        this.loadOpportunityAggregate()
      }

    


      if (cancelGoogleSubscription) {
        cancelGoogleSubscription()
      }

      _self.gmbData = undefined
      _self.adwordsData = undefined
      _self.googleAnalyticsData = undefined
      _self.googleAnalyticsData = undefined
      _self.fbAdsData = undefined

      this.loadGoogleData()

      if (this.location && this.location.facebookAdAccount) {
        try {
          let resp = await this.$http.get(
            '/facebook/get_ad_summary/' + _self.currentLocationId
          )
          _self.fbAdsData = resp.data
        } catch (ex) {
          console.error('Could not load Facebook Ads data :-(\n', ex)
        }
      }

      // if (unsubscribeOpportunities) unsubscribeOpportunities();
      // unsubscribeOpportunities = Opportunity.fetchAllOpportunities(this.currentLocationId).onSnapshot(snapshot => {
      // 	this.opportunities = snapshot.docs.map(d => new Opportunity(d));
      // 	if (this.filterPipeline) {
      // 		this.filteredOpportunities = lodash.filter(this.opportunities, { pipelineId: this.filterPipeline.id });
      // 	}
      // 	// this.populateOpportunitySources();
      // });

      // if (unsubscribeAppointmentRequests) unsubscribeAppointmentRequests();

      // unsubscribeAppointmentRequests = AppointmentRequest.fetchAllAppointmentRequests(this.currentLocationId).onSnapshot(snapshot => {
      // 	this.appointmentRequests = snapshot.docs.map(d => new AppointmentRequest(d));
      // });

      this.pipelines = this.$store.state.pipelines.pipelines.map(
        x => new Pipeline(Object.assign({}, x))
      )
      // this.funnelPipelines = pipelineList.filter( (item,i,arr)=>{
      // 	return (item.showInFunnel !== false)
      // })
      // this.piePipelines = pipelineList.filter( (item,i,arr)=>{
      // 	return (item.showInPieChart !== false)
      // })
      this.filterPipeline = undefined
      if (this.pipelines.length > 0) {
        let existingPipeLine = store.get(`pipeline-${this.currentLocationId}`)
        if (existingPipeLine && existingPipeLine.id) {
          this.filterPipeline = lodash.find(this.pipelines, {
            id: existingPipeLine.id
          })
        }

        if (!this.filterPipeline) {
          this.filterPipeline = this.pipelines[0]
        }
      }
      // this.pieFilterPipeline = undefined;
      // if (this.piePipelines.length > 0) {
      // 	let existingPipeLine = store.get(`pie-pipeline-${this.currentLocationId}`);
      // 	if (existingPipeLine && existingPipeLine.id) {
      // 		this.pieFilterPipeline = lodash.find(this.pipelines, { id: existingPipeLine.id });
      // 	} else {
      // 		this.pieFilterPipeline = this.pipelines[0];
      // 	}
      // }
    },
    async loadGoogleData() {
      if (this.googleOAuth.length > 0) {
        let resp;
        // Google My Business Data
        try {
          resp = await this.$http.get('/gmb/get_location_insights_last_30', {
            params: {
              location_id: this.currentLocationId
            }
          })
          this.gmbData = resp.data
        } catch (e) {
          console.error(e)
        }

        //Google Analytics Data
        // if (this.location && !this.location.googleAnalyticsAccountId) {
        //   try {
        //     resp = await this.$http.get(
        //       '/google/set_google_analytcs_account_id',
        //       {
        //         params: {
        //           location_id: this.currentLocationId
        //         }
        //       }
        //     )
        //   } catch (err) {
        //     console.error(err)
        //   }
        // }

        if (
          this.location &&
          this.location.googleAnalyticsAccountId &&
          (this.location.googleAnalyticsViewId ||
            this.location.googleAnalyticsWebPropertyId)
        ) {
          try {
            resp = await this.$http.get('/google/get_views', {
              params: {
                location_id: this.currentLocationId
              }
            })
            this.googleAnalyticsData = resp.data
          } catch (err) {
            console.error(err)
          }

          //Google Analytics Demographics data
          try {
            resp = await this.$http.get('/google/get_analytics_reporting', {
              params: {
                location_id: this.currentLocationId
              }
            })
            this.googleAnalyticsDemographicsData = resp.data
          } catch (err) {
            console.error(err)
          }
        }

        //Google AdWords Data
        if (this.location && this.location.googleAdwordsId) {
          try {
            resp = await this.$http.get('/google/adwords_last_30', {
              params: {
                location_id: this.currentLocationId
              }
            })
            this.adwordsData = resp.data
          } catch (err) {
            console.error(err)
          }
        }
      }
    },
    loadOpportunityAggregate() {
      this.opportunitiesSeqCalling++
      let opportunitiesSeqCalling = this.opportunitiesSeqCalling

      this.$http
        .get(
          `${config.baseUrl}/aggregate/opportunity?location_id=${this.currentLocationId}&date=${this.opportunitiesFilters.startTime}&endDate=${this.opportunitiesFilters.endTime}&assigned_to=` +
            (this.canAccessAll || !this.user ? '' : this.user.id)
        )
        .then(response => {
          if (opportunitiesSeqCalling !== this.opportunitiesSeqCalling) return

          this.opportunitiesAggrs.counts = this.getDocCount(
            response.data.aggregations.all.buckets,
            true
          )
          this.opportunitiesAggrs.revenues = this.getDocCount(
            response.data.aggregations.all.buckets
          )

          let pipelinesAggrs = response.data.aggregations.pipelines.buckets
          this.opportunitiesAggrs.pipelines = []

          pipelinesAggrs.forEach(pipeline => {
            this.opportunitiesAggrs.pipelines.push({
              id: pipeline.key,
              counts: this.getDocCount(pipeline.status.buckets, true),
              revenues: this.getDocCount(pipeline.status.buckets)
            })
          })
        })
      this.populateOpportunitySources()
    },
    getDocCount(buckets, isGetCount = false) {
      let field = isGetCount ? 'doc_count' : 'revenues.value'

      return {
        closed: lodash.get(
          buckets.find(x => x.key === 'won'),
          field,
          0
        ),
        open: lodash.get(
          buckets.find(x => x.key === 'open'),
          field,
          0
        ),
        lost:
          lodash.get(
            buckets.find(x => x.key === 'abandoned'),
            field,
            0
          ) +
          lodash.get(
            buckets.find(x => x.key === 'lost'),
            field,
            0
          )
      }
    },
    chartLoaded(chart: any) {
      // console.log("Chart:", chart);
      this.theChart = chart
    },
    analyticsChartLoaded(chart: any) {
      this.googleAnalyticsChart = chart
      this.populateAnalyticsChart()
    },
    funnelChartLoaded(chart: any) {
      // console.log("Funnel chart loaded")
      this.funnelChart = chart
      this.populateFunnelChart()
    },
    pieChartLoaded(chart: any) {
      // console.log("Pie chart loaded")
      this.pieChart = chart
      this.populatePieChart()
    },
    visitorDonutChartLoaded(chart: any) {
      this.visitorDonutChart = chart
      // console.log(chart)
      this.visitorDonutChart.renderer
        .label(
          ' <h4>500K <span>Visitors</span></h4>',
          60,
          100,
          null,
          null,
          null,
          true
        )
        .add()
      // this.visitorDonutChart.redraw();
    },
    imgUrl(listing: { [key: string]: any }): string | undefined {
      const networkInfo = ListingHelper.getNetworkInfo(listing.publisherId)
      if (networkInfo) return networkInfo.imgPath
    },
    publisherTitle(listing: { [key: string]: any }): string | undefined {
      const networkInfo = ListingHelper.getNetworkInfo(listing.publisherId)
      if (networkInfo) return networkInfo.name
      else {
        // console.log(listing.publisherId);
      }
    },
    populateAnalyticsChart() {
      // console.log("Populating analytics chart:\n", this.googleAnalyticsChart);

      if (this.googleAnalyticsChart && this.googleAnalyticsData) {
        var _self = this

        const startMonth = moment().subtract(11, 'months')
        const endMonth = moment()

        let series = [
          {
            name: 'Total Views',
            type: 'spline',
            data: [],
            color: '#607179'
          },
          {
            name: 'Direct',
            data: [],
            color: '#ffbc00'
          },
          {
            name: 'Paid',
            data: [],
            color: '#37ca37'
          },
          {
            name: 'Social',
            data: [],
            color: '#188bf6'
          },
          {
            name: 'Organic',
            data: [],
            color: '#e93d3d'
          }
        ]

        let goal_series = series[0]
        let direct_series = series[1]
        let paid_series = series[2]
        let social_series = series[3]
        let organic_series = series[4]

        let categories = []

        if (
          this.googleAnalyticsData &&
          this.googleAnalyticsData.rows &&
          this.googleAnalyticsData.rows.length > 0 &&
          this.googleAnalyticsChart &&
          this.googleAnalyticsChart.xAxis
        ) {
          while (
            endMonth > startMonth ||
            startMonth.format('M') === endMonth.format('M')
          ) {
            categories.push(startMonth.format('MMM'))

            let direct_count = this.findMonthCount(
              startMonth.format('MM'),
              'Direct'
            )
            let organic_count = this.findMonthCount(
              startMonth.format('MM'),
              'Organic Search'
            )
            let paid_count = this.findMonthCount(
              startMonth.format('MM'),
              'Paid Search'
            ) + this.findMonthCount(
              startMonth.format('MM'),
              'Display'
            )
            let social_count = this.findMonthCount(
              startMonth.format('MM'),
              'Social'
            )
            direct_series.data.push(direct_count)
            organic_series.data.push(organic_count)
            paid_series.data.push(paid_count)
            social_series.data.push(social_count)

            const types: string[] = [];
            this.googleAnalyticsData.rows.forEach(element => {
              if (types.indexOf(element[0]) === -1 && ['Direct', 'Organic Search', 'Paid Search', 'Social', 'Display'].indexOf(element[0]) === -1) {
                types.push(element[0])
              }
            })

            let remainingCount = 0;
            types.forEach(type => {
              remainingCount = remainingCount + this.findMonthCount(
                startMonth.format('MM'),
                type
              )
            })

            goal_series.data.push(
              direct_count + organic_count + paid_count + social_count + remainingCount
            )

            startMonth.add(1, 'month')
          }

          this.googleAnalyticsChart.xAxis[0].setCategories(categories)

          // this.googleAnalyticsData.rows.forEach(element => {
          // 	if (element[0] == "Direct") {
          // 		// direct_series.data.push(Number(element[2]))
          // 	} else if (element[0] == "Organic Search") {
          // 		organic_series.data.push(Number(element[2]))
          // 	} else if (element[0] == "Paid Search") {
          // 		paid_series.data.push(Number(element[2]))
          // 	} else if (element[0] == "Social") {
          // 		social_series.data.push(Number(element[2]))
          // 	}
          // });
          for (
            var i = this.googleAnalyticsChart.series.length - 1;
            i > -1;
            i--
          ) {
            this.googleAnalyticsChart.series[i].remove()
          }

          this.googleAnalyticsChart.addSeries(goal_series, false)
          this.googleAnalyticsChart.addSeries(direct_series, false)
          this.googleAnalyticsChart.addSeries(organic_series, false)
          this.googleAnalyticsChart.addSeries(paid_series, false)
          this.googleAnalyticsChart.addSeries(social_series, false)

          this.googleAnalyticsChart.redraw()
        }
      }
    },
    populateFunnelChart() {
      if (!this.filterPipeline) return
      let stageList = this.filterPipeline.stages.filter(stage => stage.showInFunnel !== false)
      const stages = lodash.sortBy(stageList, ['position'])
      const data = []
      let currency = this.$options.filters.symbole(this.location.country)
      const pipelineAggrs = this.filteredAggrOpportunities
      const wonList = lodash.sumBy(pipelineAggrs, x => x.counts.closed)
      const wonAmount = lodash.sumBy(pipelineAggrs, x => x.revenues.closed)

      const locale = getCountryInfo('locale')
      // console.log("MM",this.user.permissions);
      let firstLeadCounts = null
      for (let j = 0; j < stages.length; j++) {
        const currentStage = stages[j]
        let count = 0
        let amount = 0
        let conversationRate = null

        for (let i = stages.length - 1; i >= j; i--) {
          const stage = stages[i]
          const filtered = pipelineAggrs.find(x => x.id === stage.id)
          if (filtered) {
            const counts =
              lodash.get(filtered, 'counts.lost', 0) +
              lodash.get(filtered, 'counts.open', 0)
            const monetaryValue =
              lodash.get(filtered, 'revenues.lost', 0) +
              lodash.get(filtered, 'revenues.open', 0)
            if (currentStage.showInFunnel !== false) {
              count += counts
              amount += monetaryValue
            }
          }
        }
        if (j === 0) {
          firstLeadCounts = count
        } else {
          conversationRate =
            firstLeadCounts + wonList > 0
              ? ((count + wonList) * 100) / (firstLeadCounts + wonList)
              : 0
        }
        if (currentStage.showInFunnel !== false) {
          data.push({
            name: currentStage.name,
            y: count + wonList,
            monetaryValue: amount + wonAmount,
            conversationRate: conversationRate,
            currency: currency,
            locale: locale
          })
        }
      }

      let conversationRate =
        firstLeadCounts + wonList > 0
          ? (wonList * 100) / (firstLeadCounts + wonList)
          : 0
      data.push({
        name: 'Won',
        y: wonList,
        monetaryValue: wonAmount,
        conversationRate: conversationRate,
        currency: currency,
        locale: locale
      })
      if (this.funnelChart) {
        let series = {
          name: this.filterPipeline.name,
          data
        }
        if (this.funnelChart.series) {
          if (this.funnelChart.series.length === 0) {
            this.funnelChart.addSeries(series, false)
          } else {
            this.funnelChart.series[0].update(series, false)
          }
          this.funnelChart.redraw()
        }
      }
    },
    populatePieChart() {
      if (!this.filterPipeline) return
      let stageList = this.filterPipeline.stages
      const stages = lodash.sortBy(stageList, ['position'])
      const data = []
      let currency = this.$options.filters.symbole(this.location.country)
      const pipelineAggrs = this.filteredAggrOpportunities
      const wonList = lodash.sumBy(pipelineAggrs, x => x.counts.closed)
      const wonAmount = lodash.sumBy(pipelineAggrs, x => x.revenues.closed)
      const lostList = lodash.sumBy(pipelineAggrs, x => x.counts.lost)
      const lostAmount = lodash.sumBy(pipelineAggrs, x => x.revenues.lost)
      const locale = getCountryInfo('locale')

      let firstLeadCounts = null
      for (let j = 0; j < stages.length; j++) {
        const currentStage = stages[j]
        let count = 0
        let amount = 0
        let conversationRate = null

        // for (let i = stages.length - 1; i >= j; i--) {
        const stage = stages[j]
        const filtered = pipelineAggrs.find(x => x.id === stage.id)
        if (filtered) {
          const counts = lodash.get(filtered, 'counts.open', 0)
          const monetaryValue = lodash.get(filtered, 'revenues.open', 0)
          if (currentStage.showInPieChart !== false) {
            count = counts
            amount = monetaryValue
          }
        }
        // }
        // if (j === 0) {
        // 	firstLeadCounts = count;
        // } else {
        // 	conversationRate = (firstLeadCounts + wonList) > 0 ? ((count + wonList) * 100 / (firstLeadCounts + wonList)) : 0
        // 	debugger;
        // }

        conversationRate = (count * 100) / this.filteredOpportunitiesPieCount
        if (currentStage.showInPieChart !== false) {
          data.push({
            name: currentStage.name,
            y: count,
            monetaryValue: amount,
            conversationRate: conversationRate,
            currency: currency,
            locale: locale
          })
        }
      }
      let wonConversationRate =
        (wonList * 100) / this.filteredOpportunitiesPieCount

      // let conversationRate = (firstLeadCounts + wonList) > 0 ? (wonList * 100 / (firstLeadCounts + wonList)) : 0;

      data.push({
        name: 'Won',
        y: wonList,
        monetaryValue: wonAmount,
        conversationRate: wonConversationRate,
        currency: currency,
        locale: locale
      })

      let lostConversationRate =
        (lostList * 100) / this.filteredOpportunitiesPieCount
      data.push({
        name: 'Lost/abandoned',
        y: lostList,
        monetaryValue: lostAmount,
        conversationRate: lostConversationRate,
        currency: currency,
        locale: locale
      })

      if (this.pieChart) {
        let series = {
          name: this.filterPipeline.name,
          data
        }

        if (this.pieChart.series && this.pieChart.series.length === 0) {
          this.pieChart.addSeries(series, false)
        } else {
          this.pieChart.series[0].update(series, false)
        }
        this.pieChart.redraw()
      }
    },
    findMonthCount(month: string, type: string): number {
      let count = 0
      if (this.googleAnalyticsData) {
        this.googleAnalyticsData.rows.forEach(element => {
          if (element[0] == type && element[1] == month) {
            count = Number(element[2])
          }
        })
      }
      return count
    },
    async filterOppurtunities() {
      if (!this.filterPipeline) return
      this.populateFunnelChart()
    },
    // async filterPieOppurtunities() {
    // 	if ( !this.pieFilterPipeline) return;
    // 	// this.populateFunnelChart();
    // 	this.populatePieChart();
    // },
    async populateOpportunitySources() {
      this.$http
        .get(
          `${config.baseUrl}/aggregate/lead_source?location_id=${this.currentLocationId}&date=${this.opportunitiesFilters.startTime}&endDate=${this.opportunitiesFilters.endTime}&assigned_to=` +
            (this.canAccessAll || !this.user ? '' : this.user.id)
        )
        .then(response => {
          const buckets: { [key: string]: any }[] =
            response.data.aggregations.source.buckets
          let finalCount: { [key: string]: any } = {}
          buckets.forEach(bucket => {
            const key = bucket.key.toLowerCase()
            if (finalCount[key]) {
              const existing = finalCount[key]
              finalCount[key] = {
                total: bucket.doc_count + existing.total,
                total_value: bucket.revenue.value + existing.total_value,
                open:
                  lodash.get(
                    lodash.find(bucket.count.buckets, { key: 'open' }),
                    'doc_count',
                    0
                  ) + existing.open,
                lost:
                  lodash.get(
                    lodash.find(bucket.count.buckets, { key: 'lost' }),
                    'doc_count',
                    0
                  ) + existing.lost,
                won:
                  lodash.get(
                    lodash.find(bucket.count.buckets, { key: 'won' }),
                    'doc_count',
                    0
                  ) + existing.won
              }
            } else {
              finalCount[key] = {
                total: bucket.doc_count,
                total_value: bucket.revenue.value,
                open: lodash.get(
                  lodash.find(bucket.count.buckets, { key: 'open' }),
                  'doc_count',
                  0
                ),
                lost: lodash.get(
                  lodash.find(bucket.count.buckets, { key: 'lost' }),
                  'doc_count',
                  0
                ),
                won: lodash.get(
                  lodash.find(bucket.count.buckets, { key: 'won' }),
                  'doc_count',
                  0
                )
              }
            }
          })
          finalCount = Object.keys(finalCount)
            .sort()
            .reduce((r, k) => ((r[k] = finalCount[k]), r), {})
          this.leadSources = lodash.isEmpty(finalCount) ? undefined : finalCount
        })
    },
    getWonPercentage(won: number, total: number): string {
      let wonPercent = '0'
      if (won > 0 && total > 0) {
        wonPercent = ((won / total) * 100).toFixed(0)
      }
      return String(wonPercent)
    }
  },
  beforeDestroy() {
    if (cancelGoogleSubscription) {
      cancelGoogleSubscription()
    }
  }
})
</script>
<style scoped>
.empty-msg {
  text-align: center;
  font-size: 16px;
  color: #999;
  padding: 30px 15px;
}
.hl_tasks-list.pipeline-name {
  display: flex;
  align-items: center;
}
.hl_tasks-list.pipeline-name h2 {
  margin-right: 16px;
}
.dropdown .dropdown-menu .dropdown-item {
  white-space: initial;
  width: 350px;
}
</style>
