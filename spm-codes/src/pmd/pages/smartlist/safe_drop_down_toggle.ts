import Vue from 'vue';

export class SafeDropDownToggle {

  public get id(): string {
    return this.myid;
  }
  public onOpen: () => void ;
  public onHide: () => void ;

  private parent: Vue;
  private name: string;
  private isShow: boolean = false;
  private myid: string;

  constructor(vue: Vue, name?: string ) {
    if (!vue) throw new Error('Specify vue instance which will use this toggler');
    this.parent = vue;
    this.name = name;
    if (!this.name || this.name.trim() === '') {
      this.name = `${Date.now()}`;
    }
    this.myid = `${name}-${this.parent ? this.parent._uid : '' }`;
    this.setup();
  }

  public open() {
    if (this.isShow === false) this.toggle();
  }

  public hide() {
    if (this.isShow === true) this.toggle();
  }

  public toggle() {
    this.isShow = !this.isShow;
    const selector = $(`#${this.id}`);
    if (selector && this.isShow === true) selector.show();
    else if (selector) selector.hide();
    if (this.isShow === true && this.onOpen) this.onOpen();
    if (this.isShow === false && this.onHide) this.onOpen();
  }

  public async setup() {
    this.parent.$once('hook:beforeDestroy', this.cleanup);
    document.addEventListener('click', this.outSideClick);
    $(`#${this.id}`).click((e) => {
      /* Clicks within the dropdown won't make
         it past the dropdown itself */
     e.stopPropagation();
    });
  }

  public cleanup() {
    this.parent = undefined;
    document.removeEventListener('click', this.outSideClick);
  }

  private outSideClick = () => {
    this.hide();
  }

}
