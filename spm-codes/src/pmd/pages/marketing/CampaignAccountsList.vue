<template>
  <div>
    <section class="hl_wrapper">
      <section class="hl_wrapper--inner customers" id="customers">
        <div class="container-fluid mb-5">
          <div class="hl_controls">
            <div class="hl_controls--left">
              <h3>
                {{ campaign.name | toTitleCase }}
                <!-- <span>20 total</span> -->
              </h3>
            </div>
            <div class="hl_controls--right">
              <!-- <div class="dropdown">
								<button
									class="btn btn-success dropdown-toggle"
									type="button"
									data-toggle="dropdown"
									aria-haspopup="true"
									aria-expanded="false"
								>
									<i class="icon icon-plus"></i>
									Add {{type | toTitleCase}}
								</button>
								<div class="dropdown-menu">
									<a
										class="dropdown-item"
										@click.prevent="addCustomer"
										v-if="type==='customers'"
									>Add Single Customer</a>
									<a class="dropdown-item" @click.prevent="addCustomer" v-else>Add Single Lead</a>
									<a
										class="dropdown-item"
										@click.prevent="showImportModal = true;"
									>Import {{type | toTitleCase}}</a>
								</div>
							</div>-->
            </div>
          </div>
          <nav aria-label="Page navigation example">
            <ul class="pagination justify-content-end">
              <li
                class="page-item"
                :class="{ disabled: !previousId || fetching }"
              >
                <a
                  class="page-link"
                  href="javascript:void(0);"
                  tabindex="-1"
                  @click.prevent="load('previous')"
                  >Previous</a
                >
              </li>
              <li class="page-item" :class="{ disabled: !nextId || fetching }">
                <a
                  class="page-link"
                  href="javascript:void(0);"
                  @click.prevent="load('next')"
                  >Next</a
                >
              </li>
            </ul>
          </nav>
          <div class="card hl_customers--table">
            <div class="card-body --no-padding">
              <div class="table-wrap">
                <table class="table table-sort">
                  <thead>
                    <tr>
                      <th data-sort="string">
                        Contact
                        <!-- <i class="icon icon-arrow-down-1"></i> -->
                      </th>
                      <th data-sort="string">
                        Date Added
                        <span
                          v-b-tooltip.hover
                          title="The exact date and time at which the contact was added to this campaign. First event start time may vary depending on the configurations."
                        >
                          <i
                            class="fas fa-question-circle"
                            style="color: #afb8bc"
                          ></i>
                        </span>
                        <!-- <i class="icon icon-arrow-down-1"></i> -->
                      </th>
                      <th data-sort="string">
                        Internal Source
                      </th>
                      <th data-sort="string">
                        Status
                        <!-- <i class="icon icon-arrow-down-1"></i> -->
                      </th>
                      <th data-sort="string">
                        Assigned to
                        <span
                          v-b-tooltip.hover
                          title="When this Campaign started, this Contact was assigned to this User. Changes in the Contact's Assigned To will reflect on the events, but won't change this initial value."
                        >
                          <i
                            class="fas fa-question-circle"
                            style="color: #afb8bc"
                          ></i>
                        </span>
                        <!-- <i class="icon icon-arrow-down-1"></i> -->
                      </th>
                      <th data-sort="string">
                        Next execution time
                        <!-- <i class="icon icon-arrow-down-1"></i> -->
                      </th>
                      <!-- <th data-sort="string">Last Contact
												<i class="icon icon-arrow-down-1"></i>
											</th>-->
                      <th>Progress</th>
                      <th></th>
                    </tr>
                  </thead>
                  <tbody>
                    <CampaignAccountListItem
                      v-for="status in campaignStatus"
                      :key="status.id"
                      :status="status"
                      :campaign="campaign"
                      :timezone="timezone"
                      @update="updateObject"
                      @delete="deleteObject"
                    />
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <nav aria-label="Page navigation example">
            <ul class="pagination justify-content-end">
              <!-- <li
								class="page-item"
								:class="{ disabled: queueAllDisabled }"
								v-if="campaignStatus.length > 1"
							>
								<a
									class="page-link"
									@click.prevent="queueAllRunning"
									style="display: flex"
								>
        						<moon-loader v-if="queueAllDisabled" :loading="queueAllDisabled" color="#188bf6" size="15px" style="margin-right: 8px" />
								<i
									class="fa fa-running"
									v-else
									style="margin-right: 8px"
								/>
								Queue all
								</a>
							</li> -->
              <li
                class="page-item"
                :class="{ disabled: !previousId || fetching }"
              >
                <a
                  class="page-link"
                  href="javascript:void(0);"
                  tabindex="-1"
                  @click.prevent="load('previous')"
                  >Previous</a
                >
              </li>
              <li class="page-item" :class="{ disabled: !nextId || fetching }">
                <a
                  class="page-link"
                  href="javascript:void(0);"
                  @click.prevent="load('next')"
                  >Next</a
                >
              </li>
            </ul>
          </nav>
        </div>
      </section>
      <!-- END of .customers -->
    </section>
    <!-- END of .hl_wrapper -->
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
const CampaignAccountListItem = () =>
  import(
    /* webpackChunkName: "campaign-list" */ './CampaignAccountListItem.vue'
  ).then(m => m.default)
import { Location, CampaignStatus, Campaign, User } from '../../../models'
import firebase from 'firebase/app'
import { findIndex } from 'lodash'

declare var $: any

let realtimeUnsubscribe: () => void
export default Vue.extend({
  components: { CampaignAccountListItem },
  data() {
    return {
      currentLocationId: '',
      campaignId: '',
      campaign: {} as Campaign,
      previousId: undefined as firebase.firestore.DocumentSnapshot | undefined,
      nextId: undefined as firebase.firestore.DocumentSnapshot | undefined,
      campaignStatus: [] as CampaignStatus[],
      user: {} as User,
      fetching: false,
      limit: 15,
      timezone: 'UTC',
      queueAllDisabled: false
    }
  },
  async created() {
    this.fetchData()
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  computed: {
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion'] 
		},
  },
  updated() {
    const _self = this
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.fetchData()
    },
    '$route.params.campaign_id': function(id) {
      this.fetchData()
    }
  },
  beforeDestroy() {
    if (realtimeUnsubscribe) realtimeUnsubscribe()
  },
  methods: {
    loadDetailPage(id: string) {
      this.$router.push({ name: this.getSideBarVersion == 'v2' ? 'contact_detail-v2' : 'contact_detail', params: { contact_id: id } })
    },
    async fetchData() {
      this.currentLocationId = this.$router.currentRoute.params.location_id
      const authUser = await this.$store.dispatch('auth/get')
      this.user = await this.$store.dispatch(
        'agencyUsers/syncGet',
        authUser.userId
      )
      const location = new Location(
        await this.$store.dispatch('locations/getById', this.currentLocationId)
      )
      this.timezone = await location.getTimeZone()
      this.campaignId = this.$router.currentRoute.params.campaign_id
      this.campaign = await Campaign.getById(this.campaignId)
      this.load()
    },
    async load(fetchType?: 'previous' | 'next') {
      this.fetching = true

      let query = CampaignStatus.collectionRef()
        .where('campaign_id', '==', this.campaign.id)
        .where('location_id', '==', this.currentLocationId)
      if (
        this.user &&
        this.user.role === 'user' &&
        this.user.permissions.assigned_data_only === true
      ) {
        query = query.where('user_id', '==', this.user.id)
      }
      if (fetchType === 'previous') {
        query = query.orderBy('date_added', 'asc').startAfter(this.previousId)
      } else if (fetchType === 'next') {
        query = query.orderBy('date_added', 'desc').startAfter(this.nextId)
      } else {
        query = query.orderBy('date_added', 'desc')
      }
      const snapshot = await query.limit(this.limit + 1).get()
      let docs = snapshot.docs
      this.nextId = undefined
      this.previousId = undefined
      if (!fetchType && docs.length === this.limit + 1) {
        docs.splice(this.limit, 1)
        this.nextId = docs[this.limit - 1]
      } else if (fetchType === 'next') {
        this.previousId = docs[0]
        if (docs.length === this.limit + 1) {
          docs.splice(this.limit, 1)
          this.nextId = docs[this.limit - 1]
        }
      } else if (fetchType === 'previous') {
        docs = docs.reverse()
        if (docs.length === this.limit + 1) {
          docs.splice(0, 1)
          this.previousId = docs[0]
        }
        this.nextId = docs[this.limit - 1]
      }
      this.campaignStatus = docs.map(d => new CampaignStatus(d))

      let contactVersionIds = this.campaignStatus.map(s => s.campaignVersionId)
      contactVersionIds = contactVersionIds.filter(function(item, pos) {
        return contactVersionIds.indexOf(item) == pos
      })

      // await Promise.all(contactVersionIds.map(async versionId => {
      // 	if (!find(this.campaignVersions, { id: versionId })) {
      // 		this.campaignVersions.push(await CampaignVersion.getById(versionId));
      // 	}
      // }));
      this.fetching = false
    },
    updateObject(obj: { [key: string]: any }) {
      const index = findIndex(this.campaignStatus, { id: obj.id })
      if (index !== -1) {
        this.campaignStatus.splice(index, 1, new CampaignStatus(obj))
      }
    },
    deleteObject(obj: { [key: string]: any }) {
      const index = findIndex(this.campaignStatus, { id: obj.id })
      if (index !== -1) {
        this.campaignStatus.splice(index, 1)
      }
    },
    async queueAllRunning() {
      if (
        confirm(
          `This will immediately fire events from this campaign for all contacts who have the status as 'running'. Depending on the amount of contacts, this could take some time. Do you wish to continue?`
        )
      ) {
        this.queueAllDisabled = true
        const response = await this.$http.post(
          `/campaign/${this.campaignId}/queueAll`
        )
        console.log(response)
        await this.fetchData()
        this.queueAllDisabled = false
      }
    }
  }
})
</script>
