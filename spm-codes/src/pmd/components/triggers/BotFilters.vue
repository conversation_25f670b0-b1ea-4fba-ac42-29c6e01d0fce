<template>
  <Filters
    :conditions="conditions"
    :filterMaster="filterMaster"
    v-on:update:conditions="$emit('update:conditions', $event)"
  />
</template>

<script lang="ts">
import Vue from 'vue'
import { Campaign, Tag, LogicalEliza } from '@/models';
import { Condition } from '@/models/trigger';
const Filters = () => import( './Filters.vue');

export default Vue.extend({
    props: ['conditions'],
    components: { Filters },
    data() {
        return {
            filterMaster: [
                {
                    placeHolder: 'Select Eliza',
                    title: 'In Eliza',
                    value: 'contact.agent',
                    valueType: 'text',
                    type: 'select',
                    options: [] as { [key: string]: any }[]
                },
            ],
            agents: [],
        }
    },
    async created() {
        let currentLocationId = this.$router.currentRoute.params.location_id;
        const botIdOption = lodash.find(this.filterMaster, { value: 'contact.agent' });
        try {
          if (botIdOption) {
            botIdOption.options = (await LogicalEliza.fetchByLocationId(currentLocationId)).map(logicalEliza => {
              return {
                value: logicalEliza.id,
                title: logicalEliza.logicalName
              }
            });
          }
        } catch(err) {
           console.log(err)
        }

    },
})
</script>

