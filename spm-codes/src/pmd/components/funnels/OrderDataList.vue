<template>
  <tbody>
    <tr v-for="(order, index) in orderData" :key="index">
      <td>
        {{
          capitalize(
            order.customer ? order.customer.contact_name : 'DELETED CONTACT'
          )
        }}
      </td>
      <td>{{ order.customer ? order.customer.email : 'DELETED CONTACT' }}</td>
      <td>{{ order.product && capitalize(order.product.productName) }}</td>
      <td>{{ order.charge_id }}</td>
      <td>
        {{ currencySymbol(order) }}
      </td>
      <td>
        <span v-b-tooltip.hover :title="order.stepName">{{
          order.stepName && order.stepName.length > 10
            ? `${order.stepName.substring(0, 25)}...`
            : order.stepName
        }}</span>
      </td>
      <td style="min-width: 250px">
        {{ formatDate(order.date_added.seconds) }}
      </td>
    </tr>
  </tbody>
</template>
<script lang="ts">
import Vue from 'vue'
import moment from 'moment-timezone'
import { currency } from '../../../util/currency_helper'
export default Vue.extend({
  props: {
    orderData: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      loader: false,
    }
  },
  methods: {
    formatDate(value: number) {
      if (!value) return ''
      return moment.unix(value).format('LLL')
    },
    capitalize(value: string) {
      if (!value) return ''
      value = value.toString()
      return value.charAt(0).toUpperCase() + value.slice(1)
    },
    currencySymbol(order: any) {
      const orderCurrency = order.currency
      if (!orderCurrency) return order.amount
      const currencySymbol = currency[orderCurrency.toUpperCase()].symbol
      return currencySymbol + order.amount
    },
  },
})
</script>
