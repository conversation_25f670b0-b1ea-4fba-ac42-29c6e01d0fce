 <template>
  <div class="modal fade hl_sms-template--modal" tabindex="-1" role="dialog" ref="modal">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Copy Trigger</h2>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div class="form-group">
              <label>New Trigger Name</label>

              <input
                type="text"
                class="form-control"
                placeholder="New Trigger Name"
                v-model="triggerName"
              >
            </div>
            <div class="form-group">
              <label>Location</label>
              <vSelect
                multiple
                :options="locations"
                label="name"
                v-model="copyToLocations"
                :clearable="false"
                v-validate="'required'"
                name="location"
                data-vv-as="Location"
                :loading="loading.locations"
              >
                <template #spinner="{ loading }">
                  <div v-if="loading" style="border-left-color: rgba(88,151,251,0.71)" class="vs__spinner"></div>
                </template>
              </vSelect>
              <span v-show="errors.has('location')" class="error">Location is required.</span>
            </div>
            <!-- <ul class="list-group">
							<li v-for="location in locations" :key="location.id" class="list-group-item">
								<div class="option">
									<input
										type="checkbox"
										:id="'copy-' + location.id"
										v-model="copyToLocations"
										:value="location.id"
									>
									<label :for="'copy-' + location.id">
										<strong>{{location.name}}</strong>
									</label>
								</div>
							</li>
            </ul>-->
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner nav">
            <button type="button" class="btn btn-light2" data-dismiss="modal">Cancel</button>
            <button type="button" class="btn btn-success" @click="copyTrigger();">Copy</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { mapState } from 'vuex'
import vSelect from 'vue-select'
import { Campaign, BulkRequest, Location, User } from '@/models'
import { UserState } from '@/store/state_models'
import Trigger, { CreateOpportunity } from '@/models/trigger'

const MoonLoader = () => import('../MoonLoader.vue')

declare var $: any

export default Vue.extend({
  props: ['values'],
  components: {
    MoonLoader,
    vSelect
  },
  data() {
    return {
      locations: [] as { [key: string]: any }[],
      triggerName: '',
      copyToLocations: [] as { [key: string]: any }[],
      loading: {
        locations: false
      }
    }
  },
  methods: {
    async toggleVisibility() {
      if (this.values.visible) {
        $(this.$refs.modal).modal('show')
      } else {
        $(this.$refs.modal).modal('hide')
      }
    },
    async fetchData() {
      if (this.values.trigger?.title || this.values.triggers?.type) {
        this.triggerName =
          (this.values.trigger.title ||
            this.values.trigger.type.split('_').join(' ')) + ' copy'
      } else {
        this.triggerName = ''
      }
      this.copyToLocations = []
      this.locations = []

      const agencyType = this.user.type === User.TYPE_AGENCY
			const admin = this.user.role === User.ROLE_ADMIN

      this.loading.locations = true
			const locations = await this.$store.dispatch('locations/getAll')
      this.loading.locations = false

      if (agencyType || admin && locations.length > 1) {
				locations.forEach(location => {
					this.locations.push(location);
				});
			} else {
				let location = new Location(await this.$store.dispatch('locations/getById', this.values.currentLocationId));
				this.copyToLocations.push({ ...location.data, id: location.id });
			}
    },
    async copyTrigger() {
      const result = await this.$validator.validateAll()
      if (!result) {
        return false
      }
      if (this.copyToLocations && this.copyToLocations.length !== 0) {
        const locationIds = this.copyToLocations.map(loc => loc.id);
        await this.$store.dispatch('auth/refreshFirebaseToken', { locationId: locationIds, refresh: false } , {root: true});
      }
      for (let location of this.copyToLocations) {
        const newTrigger = new Trigger()
        newTrigger.conditions = this.values.trigger.conditions
        newTrigger.actions = this.values.trigger.actions.map(action => {
          if (location.id !== this.$route.params.location_id) {
            if (action.campaign_id) delete action.campaign_id
            if (action.pipeline_id) delete action.pipeline_id
            if (action.pipeline_stage_id) delete action.pipeline_stage_id
            if (action.assignedTo) delete action.assignedTo
          }
          return action
        })
        newTrigger.locationId = location.id
        newTrigger.type = this.values.trigger.type
        newTrigger.title = this.triggerName
        if (this.values.trigger.description)
          newTrigger.description = this.values.trigger.description
        newTrigger.active = false
        await newTrigger.save()
      }

      this.$emit('hidden')
    }
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    })
  },
  watch: {
    async values() {
      this.toggleVisibility()
      if (this.values.visible) this.fetchData()
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
    this.toggleVisibility()
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hidden')
    })

    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker()
    }

    this.toggleVisibility()
  }
})
</script>
<style scoped>
.dropdown-toggle::after {
  content: none;
}
</style>
