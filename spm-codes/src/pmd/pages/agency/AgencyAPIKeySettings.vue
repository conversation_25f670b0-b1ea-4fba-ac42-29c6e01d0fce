<template>
  <div class="container-fluid">
    <div class="container-fluid">
      <div class="mt-6">
        <p>
            This API is for advanced functionality. Our support team is not able to provide direct assistance regarding the API configuration.
        </p>
        <p>
            We do offer a Developer Council which meets for office hours once a month (<a href="https://gohighlevel.com/events">https://gohighlevel.com/events</a>) and a Developer Community Slack Channel where you can find assistance from the community and our API team. (<a href="https://www.gohighlevel.com/dev-slack">https://www.gohighlevel.com/dev-slack</a>)
        </p>
        <p>
            To use zapier you need to use the Location API key for the specific location. You can get that from the list below or by switching into the account using the switcher on the left menu.
        </p>

        <br />
        <br />
      </div>
    </div>
    <template v-if="isAgencyAdmin">
      <div class="container-fluid">
        <div class="hl_marketing--header-inner">
          <h2>Agency API Keys</h2>
          <UIButton
            type="button"
            v-if="isAllowToCreate"
            @click.prevent="showCreateModal()"
          >
            <i class="icon icon-plus" style="color:white;margin-right: 10px;"></i>
            Create New
          </UIButton>
        </div>
      </div>

      <div class="card" v-if="! isApiAcccess">
        <div class="p-4">
          <p class="text-center">This is part of the Advanced API that is available on the Pro Plan. <a @click.prevent="upgradePlan">Click here</a> to upgrade.</p>
        </div>
      </div>

      <div class="card" v-else>
        <div class="--no-padding">
          <div class="table-wrap">
            <table class="table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>API Key</th>
                  <th style="width: 1px;">Action</th>
                </tr>
              </thead>
              <tbody v-if="apiKeys.length">
                <tr v-for="(key, index) in apiKeys" :key="index">
                  <td>
                    {{ apiKeyTitles[index] }}
                  </td>
                  <td>
                    <div class="d-inline-block" style="cursor: pointer;" v-b-tooltip.hover.left="key == copiedKey ? 'Copied!' : 'Copy'" @click.prevent.stop="copyKey(key)">
                      {{ key.slice(0, 7) + '*'.repeat(30) + key.slice(-5) }}
                      <i class="fas fa-clipboard ml-2 --dark copier zoomable" @click.prevent.stop="copyKey(key)"></i>
                      <span v-if="key && key.length < 45" class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 ml-2">
                        Old
                      </span>
                    </div>
                  </td>
                  <td>
                    <div class="d-flex">
                      <i
                      class="icon icon-pencil pointer --light"
                      v-b-tooltip.hover
                      title="Edit api key"
                      @click.prevent="showCreateModal(key)"
                    ></i>

                    <i
                      v-b-tooltip.hover
                      title="Delete api key"
                      class="icon ml-2 icon-trash pointer --light"
                      @click.prevent="deleteCustomMenuLink(key)"
                    ></i>
                    </div>
                  </td>
                </tr>
              </tbody>
              <tbody v-else>
                <tr>
                  <td colspan="3" align="center">You do not have any api keys yet. <a @click.prevent="showCreateModal()">Click here</a> to create your first api keys.</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

      </div>
      <CreateEditAPIKey
        :values="createAPIKeyModal"
        :userId="authUser.userId"
        @hidden="createAPIKeyModal = { visible: false, apiKey: '' }"
      />

      <LocationAPIKeySettings
        :authUser="authUser"
        :company="company"/>
    </template>

    <template v-else>
      <div class="card">
        <div class="p-4">
          <p class="text-center">You don't have access to this section.</p>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import axios from 'axios'
import { AuthUser, Company, User } from '@/models'
import { mapState } from 'vuex'
import { UserState, CompanyState } from '@/store/state_models'
import CreateEditAPIKey from '@/pmd/components/agency/CreateEditAPIKey.vue'
const LocationAPIKeySettings = () =>
  import('@/pmd/components/agency/LocationAPIKeySettings.vue').then(
    m => m.default
  )
import { trackGaEvent } from "@/util/helper";
let unsubscribeCustomMenuLinks: () => void

export default Vue.extend({
  components: {
    CreateEditAPIKey,
    LocationAPIKeySettings
  },
  data: () => ({
    authUser: {} as AuthUser,
    createAPIKeyModal: { visible: false } as { [key: string]: any },
    company: {} as Company,
    copiedKey: ''
  }),
  computed: {
    apiKeys() {
      if (! this.isAgencyAdmin) {
        return []
      }
      if (this.company && this.company.apiKeys) {
        return this.company.apiKeys;
      }

      return[];
    },
    apiKeyTitles() {
      if (! this.isAgencyAdmin) {
        return []
      }
      if (this.company && this.company.apiKeyTitles) {
        return this.company.apiKeyTitles;
      }

      return[];
    },

    isApiAcccess() {
      return this.$store.state.company && this.$store.state.company.company && this.$store.state.company.company.stripe_active_plan && this.$store.state.company.company.stripe_active_plan.includes("497") || this.apiKeys.length;
    },

    isAllowToCreate() {
      return this.isApiAcccess && this.apiKeys.length < 5;
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    isAgencyAdmin(): boolean {
      return this.user && this.user.type === User.TYPE_AGENCY && this.user.role === User.ROLE_ADMIN
    },
  },
  methods: {
    showOnString(link) {
      return [link.show_on_company ? 'Agency' : null, link.show_on_location ? 'Account' : null].filter(obj => obj).join(', ')
    },
    async fetchData() {
      if (unsubscribeCustomMenuLinks) unsubscribeCustomMenuLinks()

      this.authUser = await this.$store.dispatch('auth/get')

      unsubscribeCustomMenuLinks = Company.getStreamById(
        this.authUser.companyId
      ).onSnapshot(snapshot => {
        this.company = new Company(snapshot)
      })
    },
    showCreateModal(id: string) {
      this.createAPIKeyModal = {
        visible: true,
        companyId: this.authUser.companyId,
        apiKey: id
      }
    },
    async deleteCustomMenuLink(apiKey: string) {
      let message = 'This will delete the api key. Do you wish to continue?'

      this.$uxMessage('confirmation', message, async response => {
        if (response === 'ok') {
          await axios.post(
            `agency/api-key/${this.company.id}`,
            {
              apiKey: apiKey,
              action: 'delete'
            }
          );
        }
      })
    },
    copyKey(key: string) {
      this.copiedKey = key;
      this.clipboardCopy(key)

      setTimeout(() => {
        this.copiedKey = '';
      }, 2000)
    },
    upgradePlan() {
      trackGaEvent('AgencyAPIKeyUpgrade', this.company.id, 'Redirecting to billing for upgrade', 1);

      this.$router.push({
        name: 'account_billing'
      })
    }
  },
  async created() {
    this.fetchData()
  },
  beforeDestroy(): void {
    if (unsubscribeCustomMenuLinks) unsubscribeCustomMenuLinks()
  }
})
</script>
<style scoped>
.sm-button::before {
  font-family: var(--ff);
  font-weight: 900;
  /* content: attr(data-icon); */
  content: var(--fa);
  font-style: normal;
}
</style>
