<template>
  <div class="style-wrap">
    <div class="option-card">
      <div class="option-card-header">
        <h3>Layout</h3>
      </div>
      <div class="option-card-body">
        <div class="d-flex justify-content-between" v-if="optFormAction.source!='survey'" style="margin-bottom:10px;">
          <p>Inline Forms</p>
          <div class="toggle">
            <UIToggle
              id="misc-toggle"
              :value="formStyle.inlineForm"
              @input="changeLayout"
            />
            <label class="tgl-btn" for="misc-toggle"></label>
          </div>
        </div>

        <div class="d-flex justify-content-between">
          <p>Show Label</p>
          <div class="toggle">
            <UIToggle
              id="label-toggle"
              :value="formStyle.formLabelVisible"
              @input="layoutFormLabelVisible"
            />
            <label class="tgl-btn" for="label-toggle"></label>
          </div>
        </div>
      </div>
    </div>
    <div class="option-card">
      <div class="option-card-header">
        <h3>{{optFormAction.source!='survey' ? 'Form Style' : 'Survey Style' }}</h3>
      </div>
      <div class="option-card-body">
        <div class="style-group">
          <label>Background</label>
          <div class="color-picker">
            <span class="hash">#</span>
            <input
              type="text"
              class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
              v-bind:value="formStyle.bgColor"
              @change="setFormBackground($event.target.value)"
            >
            <span
              class="the-color"
              v-bind:style="{backgroundColor: '#' + formStyle.bgColor}"
              @click="pickerOnFormBgClick"
            ></span>

            <chrome-picker
              :value="formStyle.bgColor"
              v-show="formStyle.showBgColorPicker"
              style="position: absolute;top: 42px;right: 0px;z-index:99"
              @input="pickerFormBgColor"
            />
          </div>
        </div>
        <div class="style-group">
          <label>Font Color</label>
          <div class="color-picker">
            <span class="hash">#</span>
            <input
              type="text"
              class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
              v-bind:value="formStyle.color"
              @change="setFormColor($event.target.value)"
            >
            <span
              class="the-color"
              v-bind:style="{backgroundColor: '#' + formStyle.color}"
              @click="pickerOnFormColorClick"
            ></span>

            <chrome-picker
              :value="formStyle.color"
              v-show="formStyle.showColorPicker"
              style="position: absolute;top: 50px;right: 0px;z-index:99"
              @input="pickerFormColor"
            />
          </div>
        </div>
        <div class="style-group">
          <label>Border</label>
          <div class="border-picker">
            <input
              type="text"
              class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
              v-bind:value="formStyle.border"
              @change="setFormBorder($event.target.value)"
            >
            <span class="px">px</span>
            <div class="pixel-count-btns">
              <button type="button" class="pixel-up" @click="clickBorderPlus">
                <i class="icon icon-arrow-up-1"></i>
              </button>
              <button type="button" class="pixel-down" @click="clickBorderMinus">
                <i class="icon icon-arrow-down-1"></i>
              </button>
            </div>
            <span class="the-color" style="background: #ddd"></span>
          </div>
        </div>
        <div class="style-group">
          <label>Corner Radius</label>
          <div class="pixel-count">
            <input
              type="text"
              class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
              v-bind:value="formStyle.radius"
              @change="setFormBorderRadius($event.target.value)"
            >
            <span class="px">px</span>
            <div class="pixel-count-btns">
              <button type="button" class="pixel-up" @click="clickRadiusPlus">
                <i class="icon icon-arrow-up-1"></i>
              </button>
              <button type="button" class="pixel-down" @click="clickRadiusMinus">
                <i class="icon icon-arrow-down-1"></i>
              </button>
            </div>
          </div>
        </div>
        <div class="style-group">
          <label>Width</label>
          <div class="pixel-count">
            <input
              type="text"
              class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800"
              v-bind:value="formStyle.width"
              @change="setFormWidth($event.target.value)"
            >
            <span class="px">px</span>
            <div class="pixel-count-btns">
              <button type="button" class="pixel-up" @click="clickWidthPlus">
                <i class="icon icon-arrow-up-1"></i>
              </button>
              <button type="button" class="pixel-down" @click="clickWidthMinus">
                <i class="icon icon-arrow-down-1"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--Custom CSS-->
    <div class="option-card">
      <div class="option-card-header">
        <h3>Custom CSS</h3>
      </div>
      <div class="option-card-body">
        <div class="form-group">
          <UITextAreaGroup rows="4" v-model="formStyle.emebedHtml" @blur="recordCustomStyle"></UITextAreaGroup>
        </div>
        <!-- <div class="form-group d-flex justify-content-end">
          <button class="btn btn-primary btn-sm">Inspector</button>
        </div> -->
      </div>
    </div>

    <div class="option-card">
      <div class="option-card-header">
        <h3>Miscellaneous</h3>
      </div>
      <div class="option-card-body">
        <div class="d-flex justify-content-between">
          <p>Agency Branding</p>
          <div class="toggle">
            <UIToggle
              id="branding-toggle"
              :value="formStyle.isBrandingActive"
              @input="changeBrandingStatus"
            />
            <label class="tgl-btn" for="branding-toggle"></label>
          </div>
        </div>
      </div>
    </div>

    <div class="option-card" v-if="optFormAction.source=='survey'">
      <div class="option-card-header position-relative">
        <div >
            <h3>Background Image</h3>
            <moon-loader class="mt-2" color="#188bf6" size="20px" v-if="loading"/>
        </div>

      </div>
      <div class="option-card-body">
        <div class="form-group">
          <input
            ref= 'fileInput'
            type="file"
            class="form-control-bg-image"
            placeholder="Image"
            name="bg-image"
            @change="onFileChange"
            style="width:100%;"
            accept="image/*"
          >
          <a class="pull-right" @click="removeField" v-if="formStyle.bgImage">
            <i class="icon icon-minus" style="font-size: 0.75em; margin-right: 5px; margin-top: 5px; "></i>
            Remove image
          </a>
          <img
            :src="(filesAdded.length > 0) ? filesAdded[0].url : formStyle.bgImage"
            style="width:100%;margin-top:5px;"
          />
        </div>
      </div>
    </div>

  </div>
</template>



<script lang="ts">
import Vue from "vue";
import { Chrome } from "vue-color";
import { FileAttachment } from "@/store/state_models";
const MoonLoader = () => import("../../components/MoonLoader").then(m => m.default);
import ImageTools from "../../../util/image_tools";
const imageTools = new ImageTools();
import { v4 as uuid } from "uuid";
const path = require('path');

// Firebase
import firebase from "firebase/app";

export default Vue.extend({
	props: {
    styleFormStyle: {} as any,
    optFormAction: {} as any
	},
	components: {
    "chrome-picker": Chrome,
    MoonLoader

	},
	data() {
		return {
      formStyle: this.styleFormStyle,
      filesAdded: [] as FileAttachment[],
      currentLocationId: '',
      builderId: '',
      loading: false
    };
	},
	computed: {

	},
	watch: {

	},
	updated() {

	},
	mounted() {

  },
  created(){
    this.currentLocationId = this.$router.currentRoute.params.location_id;
    this.builderId = this.$router.currentRoute.params.survey_id;
  },
	methods: {
    saveForm(slug: string, value: any){
      this.$bus.$emit('field-setting-form', {
        type: slug,
				value: value
			});
    },
		isValidHexColor(color: string) {
			let isValidColor: boolean = /(^[0-9A-F]{8}$)|(^[0-9A-F]{6}$)|(^[0-9A-F]{3}$)/i.test(
				color
			);
			return isValidColor;
		},
		isColorPickerOpen(which: string) {
			if (which === "form-bg-color") {
				this.formStyle.showColorPicker = false;
			} else if (which === "form-color") {
				this.formStyle.showBgColorPicker = false;
			}
		},

		//---Form Style----
		setFormBackground(color: string) {
			if (this.isValidHexColor(color)) {
        this.formStyle.bgColor = color;
        this.saveForm('bgcolor', color);
			}
		},
		setFormColor(color: string) {
			if (this.isValidHexColor(color)) {
				this.formStyle.color = color;
				this.saveForm('color', color);
			}
		},
		setFormBorder(value: any) {
			if (!isNaN(value)) {
				this.formStyle.border = value;
				this.saveForm('border', value);
			}
		},
		setFormBorderRadius(value: any) {
			if (!isNaN(value)) {
				this.formStyle.radius = value;
				this.saveForm('radius', value);
			}
		},
		setFormWidth(value: any) {
			if (!isNaN(value)) {
				this.formStyle.width = value;
				this.saveForm('width', value);
			}
		},
		clickBorderPlus() {
      let val = parseFloat(this.formStyle.border);
			let border = val >= 100 ? 100 : val + 1;
			this.setFormBorder(border);
		},
		clickBorderMinus() {
      let val = parseFloat(this.formStyle.border);
      let border = val <= 0 ? 0 : val - 1;
			this.setFormBorder(border);
		},
		clickRadiusPlus() {
      let val = parseFloat(this.formStyle.radius);
			let radius = val >= 100 ? 100 : val + 1;
			this.setFormBorderRadius(radius);
		},
		clickRadiusMinus() {
      let val = parseFloat(this.formStyle.radius);
			let radius = val <= 0 ? 0 : val - 1;
			this.setFormBorderRadius(radius);
		},
		clickWidthPlus() {
      let val = parseFloat(this.formStyle.width);
			let width = val + 1;
			this.setFormWidth(width);
		},
		clickWidthMinus() {
      let val = parseFloat(this.formStyle.width);
			let width = val <= 0 ? 0 : val - 1;
			this.setFormWidth(width);
		},
		//---Color picker form
		pickerFormBgColor(newColor: any) {
			var color = newColor.hex8.replace("#", "");
			this.setFormBackground(color);
		},
		pickerOnFormBgClick() {
			this.isColorPickerOpen("form-bg-color");
			this.formStyle.showBgColorPicker = !this.formStyle.showBgColorPicker;
		},
		pickerFormColor(newColor: any) {
			var color = newColor.hex8.replace("#", "");
			this.setFormColor(color);
		},
		pickerOnFormColorClick() {
			this.isColorPickerOpen("form-color");
			this.formStyle.showColorPicker = !this.formStyle.showColorPicker;
		},

		//---Layout change
		changeLayout() {
			this.formStyle.inlineForm = !this.formStyle.inlineForm;
			this.saveForm('layout', this.formStyle.inlineForm);
		},
		layoutFormLabelVisible(){
			this.formStyle.formLabelVisible = !this.formStyle.formLabelVisible;
			this.saveForm('label-visible', this.formStyle.formLabelVisible);
		},
		changeBrandingStatus() {
			this.formStyle.isBrandingActive = !this.formStyle.isBrandingActive;
			this.saveForm('branding-active', this.formStyle.isBrandingActive);
		},
		recordCustomStyle() {
			this.saveForm('custom-style', this.formStyle.emebedHtml);
    },
    async onFileChange(e: any) {
      this.loading= true;
      this.filesAdded = [] as FileAttachment[]; //clear first and then upload
			const element = <HTMLInputElement>e.target;
			if (!element.files) return;
			for (let i = 0; i < element.files.length; i++) {
				this.vfileAdded(element.files[i]);
			}
			element.files = null;
		},
		async vfileAdded(file: File) {
			const response = <File | Blob>(
				await imageTools.resize(file, { height: 1000, width: 1000 })
			);
			this.filesAdded.push({
				name: file.name,
				type: file.type,
				url: URL.createObjectURL(response),
				data: response
      });
      console.log(this.filesAdded);
      this.send();
		},
		send: async function () {
			let el = this;
			const urls = [] as string[];
			if (this.filesAdded.length > 0) {
				const basePath =
					"location/" +
					this.currentLocationId +
					"/form/" +
					this.builderId +
					"/background-image/";
				const newURLs: string[] = await Promise.all(
					this.filesAdded.map(async attachment => {
						let imagePath = basePath + uuid() + path.extname(attachment.name);
						var uploadPath = firebase.storage().ref(imagePath);
						const snapshot = await uploadPath.put(attachment.data, {
              contentType: attachment.type,
              contentDisposition: `inline; filename="${attachment.name}"`,
							customMetadata: { name: attachment.name }
						});
						return await snapshot.ref.getDownloadURL();
					})
				);
        urls.push.apply(urls, newURLs.filter(url => url));
        if(urls.length > 0){
          this.saveForm('bg-img', urls[0]);
        }
      }
      this.loading = false;
    },
    removeField(){
      this.formStyle.bgImage ='';
      this.filesAdded[0]=''
      this.$refs.fileInput.value = null;
      this.saveForm('bg-img-delete');
    }
	}
});
</script>

<style>
.option-card-header .v-spinner {
    position: absolute;
    right: 10px;
    top: 0;
}
</style>
