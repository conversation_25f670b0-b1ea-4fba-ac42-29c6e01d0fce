<template>
  <section class="pmd_wrapper">
    <section class="pmd_wrapper--inner pmd_appointments" id="appointments">
      <div class="container-fluid">
        <div class="pmd_controls">
          <div class="pmd_controls--left">
            <h2 class="heading4">Appointment Reminders</h2>
          </div>
          <div class="pmd_controls--right">
            <h6>
              Sun, Apr 1st, 2018 - Mon, Apr 31st, 2018
              <i
                class="icon-calendar text-secondary ml-5 d-none d-lg-inline-block"
              ></i>
            </h6>
          </div>
        </div>

        <div class="card pmd_appointments--table">
          <div class="card-body --no-padding">
            <div class="table-wrap">
              <table class="table table-sort table-appointment">
                <thead>
                  <tr>
                    <th>Appt Time</th>
                    <th>Calendar</th>
                    <th>Name</th>
                    <th>Phone</th>
                    <th>Email</th>
                    <th>Status</th>
                  </tr>
                </thead>

                <tbody>
                  <ReminderCard
                    v-for="reminder in reminders"
                    :reminder="reminder"
                    :key="reminder.id"
                  />
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- END of .appointments -->
  </section>
</template>

<script lang="ts">
import Vue from 'vue'
import { Reminder } from '../../models'
import firebase from 'firebase/app'
const ReminderCard = () => import('../components/ReminderCard.vue')

export default Vue.extend({
  components: {
    ReminderCard
  },
  data() {
    return {
      reminders: [] as Reminder[]
    }
  },
  computed: {},
  async created() {
    this.$private.cancelReminderSubscription = (
      await Reminder.fetchAllReminders()
    ).onSnapshot((snapshot: firebase.firestore.QuerySnapshot) => {
      this.reminders = snapshot.docs.map(
        (d: firebase.firestore.QueryDocumentSnapshot) => new Reminder(d)
      )
    })
  },
  beforeDestroy() {
    if (this.$private.cancelReminderSubscription) {
      this.$private.cancelReminderSubscription()
    }
  }
})
</script>
