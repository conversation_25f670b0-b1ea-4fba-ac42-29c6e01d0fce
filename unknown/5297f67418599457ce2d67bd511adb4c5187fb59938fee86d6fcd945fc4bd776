<template>
  <div class="modal fade hl_add_domain--modal" tabindex="-1" role="dialog" ref="modal">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-header--inner">
            <h2 class="modal-title">Clone Product</h2>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="modal-body--inner">
            <div class="form-group">
              <label for="productName">Product Name</label>
              <input
                name="productName"
                v-model="newProductName"
                class="form-control"
                type="text"
                placeholder="Product Name"
              />
            </div>
            <div class="form-group">
              <label for="locations">Locations</label>
              <vSelect
                :options="locations"
                v-model="selectedLocations"
                :selectable="() => selectedLocations.length <= maxSelectableLocations"
                name="locations"
                placeholder="Select locations"
                multiple
              />
              <span>Select upto {{ maxSelectableLocations }} locations</span>
            </div>
            <div v-if="errorInCloning" class="error-message">
              <strong>Error while cloning product!</strong>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <div class="modal-footer--inner">
            <button type="button" class="btn btn-light2" data-dismiss="modal">Cancel</button>
            <button
              :disabled="submitDisabled"
              @click="cloneProduct()"
              type="button"
              class="btn btn-success"
            >{{ cloning ? 'Cloning' : 'Clone Product' }}</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import axios from 'axios'
import vSelect from 'vue-select'
import config from '@/config'
import firebase from 'firebase/app'

export default Vue.extend({
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    productDetails: {
      type: Object
    },
    locations: {
      type: Array
    }
  },
  components: {
    vSelect
  },
  computed: {
    submitDisabled(): boolean {
      return (
        this.cloning || !this.newProductName || !this.selectedLocations.length
      )
    }
  },
  data() {
    return {
      newProductName: '',
      selectedLocations: [],
      cloning: false,
      errorInCloning: false,
      maxSelectableLocations: 25
    }
  },
  mounted() {
    const _self = this
    $(this.$refs.modal).on('hidden.bs.modal', function() {
      _self.$emit('hide')
    })
  },
  beforeDestroy() {
    $(this.$refs.modal).off('hidden.bs.modal')
  },
  methods: {
    cloneProduct: async function() {
      try {
        const locationIds = this.selectedLocations.map((l: any) => l.id)

        this.errorInCloning = false
        this.cloning = true

        const currentUser = await firebase.auth().currentUser
        let token
        if (currentUser) {
          token = await currentUser.getIdToken()
        }

        const payload = {
          locations: locationIds,
          productName: this.newProductName
        }

        const { locationId, productId } = this.productDetails

        await this.membershipBackend.post(
          `/locations/${locationId}/products/clone/${productId}`,
          payload
        )

        this.$emit('cloneSuccessful', { locationIds })
      } catch (error) {
        console.error('error while cloning funnel --> ', error)
        this.errorInCloning = true
      } finally {
        this.cloning = false
      }
    }
  },
  watch: {
    showModal(newValue, oldValue) {
      if (newValue && !oldValue) {
        // show the modal
        // reset form
        this.cloning = false
        this.errorInCloning = false
        this.newProductName =
          this.productDetails.productName || 'Unnamed Product Copy'
        this.selectedLocations = []
        $(this.$refs.modal).modal('show')
      } else if (!newValue && oldValue) {
        // hide the modal
        $(this.$refs.modal).modal('hide')
      }
    }
  }
})
</script>
