<template>
  <div>
    <SideBarV2 v-if="getSideBarVersion === 'v2'"/>
    <SideBar v-else />
    <TopBar />
    <section class="hl_wrapper">
      <section
        class="hl_wrapper--inner hl_agency hl_agency-sales-resources"
        id="agency_sales-resources"
      >
        <div class="container-fluid">
          <div class="hl_controls">
            <div class="hl_controls--left">
              <h1>Sales Resources</h1>
            </div>
            <div class="hl_controls--right">
              <div class="search-form">
                <UITextInputGroup
                  type="text"
                  icon="icon-loupe"
                  class="form-light"
                  placeholder="Search Documents"
                  v-model="searchText"
                />
              </div>
              <!-- <button type="button" class="btn btn-light btn-sm hl_reviews--filter-btn" id="hl_location--filter-btn">
                                <span>Filters</span>
                                <i class="icon icon-settings-2"></i>
                            </button>
                            <button type="button" class="btn btn-success">
                                <i class="icon icon-plus"></i> Add Resource</button> -->
            </div>
          </div>
          <div class="hl_location--group" id="hl_location--filter-group">
            <div v-if="loading" class="spinner">
              <moon-loader :loading="loading" color="#37ca37" size="30px" />
            </div>
            <div class="hl_location--filter" id="hl_location--filter">
              <div class="form-group">
                <label>Minimal Rating</label>
                <select class="selectpicker" title="Select">
                  <option data-content="<i class='icon icon-star-filled'></i> 1"
                    >1</option
                  >
                  <option data-content="<i class='icon icon-star-filled'></i> 2"
                    >2</option
                  >
                  <option data-content="<i class='icon icon-star-filled'></i> 3"
                    >3</option
                  >
                  <option data-content="<i class='icon icon-star-filled'></i> 3"
                    >4</option
                  >
                  <option data-content="<i class='icon icon-star-filled'></i> 3"
                    >5</option
                  >
                </select>
              </div>
              <div class="form-group">
                <label>Max Rating</label>
                <select class="selectpicker" title="Select">
                  <option data-content="<i class='icon icon-star-filled'></i> 1"
                    >1</option
                  >
                  <option data-content="<i class='icon icon-star-filled'></i> 2"
                    >2</option
                  >
                  <option data-content="<i class='icon icon-star-filled'></i> 3"
                    >3</option
                  >
                  <option data-content="<i class='icon icon-star-filled'></i> 3"
                    >4</option
                  >
                  <option data-content="<i class='icon icon-star-filled'></i> 3"
                    >5</option
                  >
                </select>
              </div>
              <div class="form-group">
                <label>Date Range</label>
                <select class="selectpicker" title="Select">
                  <option>Option 1</option>
                  <option>Option 2</option>
                  <option>Option 3</option>
                </select>
              </div>
              <div class="form-group">
                <label>User</label>
                <select class="selectpicker" title="Select">
                  <option>Option 1</option>
                  <option>Option 2</option>
                  <option>Option 3</option>
                </select>
              </div>
              <div class="form-group">
                <label>Certified User</label>
                <select class="selectpicker" title="Select">
                  <option>Option 1</option>
                  <option>Option 2</option>
                  <option>Option 3</option>
                </select>
              </div>
              <div class="form-group">
                <label>Tags</label>
                <select class="selectpicker" title="Select">
                  <option>Option 1</option>
                  <option>Option 2</option>
                  <option>Option 3</option>
                </select>
              </div>
              <a href="#0">Clear Filter</a>
            </div>
            <!-- <div class="card hl_agency-sales-resources--item">
                            <div class="card-body">
                                <img src="../../../assets/pmd/img/img-doc-preview.png" alt="Doc">
                                <div class="card-text">
                                    <h3>Listing Power Deck
                                        <i class="icon icon-doc"></i>
                                    </h3>
                                    <p>Postcards have been consistently one of the more popular collectibles and used as promotional materials. More and more business owners are turning to color postcards as a good way of promoting their products and services. This is simply because postcards don’t have to be opened.</p>
                                </div>
                                <div class="card-btns">
                                    <a href="#" class="btn btn-blue btn-block">
                                        <i class="icon icon-download"></i> Download</a>
                                    <a href="#" class="btn btn-light3 btn-block">
                                        <i class="icon icon-google-drive"></i> Google Drive</a>
                                </div>
                            </div>
                        </div>
                        <div class="card hl_agency-sales-resources--item">
                            <div class="card-body">
                                <img src="../../../assets/pmd/img/img-doc-preview.png" alt="Doc">
                                <div class="card-text">
                                    <h3>One Page Flyer
                                        <i class="icon icon-pdf"></i>
                                    </h3>
                                    <p>They are big, bold and beautiful. Billboards have been around for quite a while. In almost all places nowadays you can find billboards of just about every product there is in the market. No matter how you see it, billboard advertising is on the rise.</p>
                                </div>
                                <div class="card-btns">
                                    <a href="#" class="btn btn-blue btn-block">
                                        <i class="icon icon-download"></i> Download</a>
                                    <a href="#" class="btn btn-light3 btn-block">
                                        <i class="icon icon-google-drive"></i> Google Drive</a>
                                </div>
                            </div>
                        </div> -->
            <div
              v-for="file in filteredFiles"
              v-bind:index="file.id"
              class="card hl_agency-sales-resources--item"
            >
              <div class="card-body">
                <img :src="getImagePreview(file)" alt="Doc" />
                <div class="card-text" style="width:710px;">
                  <h3>
                    {{
                      file.name
                        .replace(/\.[^/.]+$/, '')
                        .replace('salesDocs/', '')
                    }}
                    <i :class="getFileIcon(file.contentType)"></i>
                  </h3>
                  <p>{{ file.metadata.description }}</p>
                </div>
                <div class="card-btns">
                  <a
                    :href="file.mediaLink"
                    target="blank"
                    style="color:#fff;"
                    class="btn btn-blue btn-block"
                  >
                    <i class="icon icon-download"></i> Download</a
                  >
                  <a
                    :href="file.mediaLink"
                    target="blank"
                    class="btn btn-light3 btn-block"
                  >
                    <i class="icon icon-google-drive"></i> Google Drive</a
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </section>
  </div>
</template>

<script src="https://apis.google.com/js/platform.js" async defer></script>

<script lang="ts">
import Vue from 'vue'
import firebase from 'firebase/app'
import { Location, User } from '@/models'
import TopBar from '../../components/TopBar.vue'
import SideBar from '../../components/agency/SideBar.vue'
import SideBarV2 from '../../components/sidebar/SideBar.vue'

let unsubscribeLocations: () => void

export default Vue.extend({
  components: { TopBar, SideBar, SideBarV2 },
  data() {
    return {
      files: [],
      searchText: '' as string,
      loading: false as boolean
    }
  },
  async created() {
    this.loading = true
    const response = await this.$http.get(
      '/api/google/list_bucket_files?bucket_id=highlevel-backend.appspot.com&prefix=salesDocs'
    )
    console.log('Response:', response)
    let files = lodash.filter(response.data.items, (item: {}) => {
      return item['name'] != 'salesDocs/'
    })

    this.files = files
    this.loading = false
    const ref_string = 'salesDocs/'

    var upload_path = firebase.storage().ref(ref_string)
  },
  computed: {
    filteredFiles() {
      let _self = this
      if (this.searchText === '') {
        let files = lodash.filter(_self.files, function(file) {
          return file.contentType != 'image/png'
        })
        return files
      } else {
        let files = lodash.filter(_self.files, function(file) {
          return (
            file.contentType != 'image/png' &&
            file.name.toLowerCase().indexOf(_self.searchText.toLowerCase()) > -1
          )
        })

        return files
      }
    },
    imagePreviews() {
      let _self = this
      if (this.files) {
        let files = {}

        let images = lodash.filter(_self.files, function(file) {
          return file.contentType == 'image/png'
        })

        images.forEach(image => {
          files[image.name.replace('.png', '').replace('salesDocs/', '')] =
            image.mediaLink
        })

        return files
      }
    },
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    getSideBarVersion(): string {
      return this.$store.getters['sidebarv2/getVersion'] 
    },
  },
  beforeDestroy() {
    if (unsubscribeLocations) unsubscribeLocations()
  },
  methods: {
    getFileIcon(mimeType: string): string {
      mimeType = mimeType.toLowerCase()
      if (mimeType == 'application/vnd.google-apps.presentation') {
        return 'icon icon-presentation'
      } else if (mimeType == 'application/vnd.google-apps.document') {
        return 'icon icon-doc'
      } else if (mimeType == 'application/vnd.google-apps.spreadsheet') {
        return 'icon icon-sheets'
      } else {
        return ''
      }
    },
    getImagePreview(file) {
      let file_name_replace = file.name
        .replace(/\.[^/.]+$/, '')
        .replace('salesDocs/', '')
      console.log('file_name_replace:', file_name_replace)
      return this.imagePreviews[file_name_replace]
    }
  }
})
</script>

<style scoped>
.spinner {
  display: table;
  margin: 0 auto;
}
</style>
