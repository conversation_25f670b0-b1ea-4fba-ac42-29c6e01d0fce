<template>
  <b-modal ref="modal-sm-d" id="modal-bulkstatuscontact-11" size="xl" @hide="close" hide-footer>
      <template v-slot:modal-title>
        <moon-loader-title :title="'Bulk Actions for ' + (contact && contact.name || '')"
                           :loading="loading"/>
        <h5 class="modal-title"></h5>
      </template>
      <template>
      <div class="modal-body">
        <!-- :exportItems="exportItems"
             :fileName="fileName"-->
            <StatsDetailModalTable
                :timeZone="locationTimeZone"
                :title="title"
                :items="items"
                :fields="fields"
                :currentPage="currentPage"
                :itemPerPage="itemsPerPage"
                :rows="rows"
                :isLoading="loading"
                @pageChanged="pageChanged"
                @perPageChanged="perPageChanged"/>

      </div>
       <div class="modal-footer">
        <div class="modal-footer--inner">
          <div class="modal-footer--inner nav" v-if="!loading">
            <button type="button" class="btn btn-primary" @click="close">Close</button>
          </div>
        </div>
      </div>
      </template>
  </b-modal>
</template>

<script lang="ts">

import Vue from 'vue'
import { Contact } from '@/models'
import { ContactBulkAction } from './server/bulkreq_api'
import { UxMessage } from '@/util/ux_message'
const StatsDetailModalTable = () => import('../../components/StatsDetailModalTable.vue')
import store from '@/store';
export default Vue.extend({
  name:"ContactBulkActionsReport",
  props: {
    contactId: String
  },
  components: {
    StatsDetailModalTable
  },
  inject: ['uxmessage'],
  data() {
    return {
      items: [] as ContactBulkAction[],
      contact: null as Contact | null,
      loading: false,
      title: 'Bulk Action Processing',
      exportItems: [],
      fields: [
        { key: 'date', label: 'Date', sortable: false, checked: true, type: 'tzdate'  },
        { key: 'status', label: 'Status', sortable: false, checked: true, type: 'capitalize' },
        { key: 'type', label: 'Operation', sortable: false, checked: true, type: 'bulk-capitalize'},
        { key: 'action', label: 'Details', sortable: false, checked: true },
        { key: 'error', label: 'Error', sortable: false, checked: true }
      ],
      currentPage: 0,
      itemsPerPage: 0,
      rows: 0,
      fileName: 'hello-world.txt'
    }
  },
  async mounted(){
    await this.$nextTick();
    this.fetch();
    this.showPopup();
  },
  methods: {
    async fetch(page?:number) {
      try {
        this.items = [];
        if (!this.contactId) return
        this.loading = true;
        this.contact = await Contact.getById(this.contactId);
        const result = await ContactBulkAction.getContactHistory(this.contactId, page ? page: 1);
        this.items = result.items;
        this.rows = result.totalCount;
        this.currentPage = result.page;
        this.itemsPerPage = result.pageSize
      } catch (e) {
        this.loading = false
        console.log(e);
        this.showError(e && e.message || 'unknown error',e);
        this.$emit('onError',e);
      } finally {
        this.loading = false;
      }
    },
    async pageChanged(page: number) {
      if (this.currentPage === page) return;
      await this.fetch(page);
    },
    async perPageChanged(perPage: number) {
      // page size change
    },
    showError(err:string, stack?:string){
      this.uxmessage(UxMessage.errorType(err,stack))
    },
    close() {
      this.hidePopup();
      this.$emit('closed')
    },
    showPopup(){
      if(this.$refs['modal-sm-d']) this.$refs['modal-sm-d'].show();
    },
    hidePopup(){
      if(this.$refs['modal-sm-d']) this.$refs['modal-sm-d'].hide();
    }
  },
  computed: {
    locationTimeZone(){
      console.log(`Location Time Zone -- ${this.$store.getters[`locations/getCurrentLocationTimeZone`]}`)
      return this.$store.getters[`locations/getCurrentLocationTimeZone`];
    },
  },
  watch: {
    '$route.params.location_id': function(id) {
      this.close();
    },
    'contactId': function(id) {
      this.fetch();
    },
  },
})
</script>
<style scoped>

.modal .modal-body {
    padding: 10px 15px;
}

.hl_rules--wrap{
  margin-top: 5px;
}
.form-input-dropdown{
    position: absolute;
    will-change: transform;
    top: 0px;
    left: 0px;
    transform: translate3d(0px, 51px, 0px);
    min-width: 0;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
}
</style>

<!-- <table class="table table-sort" v-if="items">
    <thead>
      <th>Date</th>
      <th>Status</th>
      <th>Operation</th>
      <th>Details</th>
      <th>Error</th>
    </thead>
    <template v-for="item in items" >
      <tr v-bind:key="item.id">
        <td>
          <div v-if="item.date" style="align-items: normal !important;"
              :set="parts = getDateParts(item.date, locationTimeZone)">
            <div class="date-display text-nowrap">{{parts[0]}}</div>
            <div class="date-display --blue text-nowrap">{{parts[1]}} <small>({{parts[2]}})</small></div>
          </div>
        </td>
        <td>{{item.status}}</td>
        <td>{{item.type}}</td>
        <td><div style="max-width:300px">{{item.action}}</div></td>
        <td v-if="item.error" style="max-width:300px">{{item.error}}</td>
        <td v-else></td>
      </tr>
    </template>
</table> -->
