<template>
  <div>
    <div class="row">
      <div class="col-12">
        <i v-if="mailgunError" class="fa fa-exclamation-triangle text-danger" v-b-tooltip.click.html="mailgunError"></i>
        {{api<PERSON>ey}}
        <i
          v-if="loading === false"
          class="icon icon-pencil --blue mx-2 pointer"
          @click.prevent="edit"
        ></i>
        <div class="d-inline-block align-middle mx-2" v-show="loading === true">
          <moon-loader :loading="loading" color="#188bf6" size="15px" />
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-8">{{domain}}</div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { MailGunAccount, Company } from '@/models'

export default Vue.extend({
  props: { companyId: String, bus: Vue, companyMailgun: {}, fetchingData: Boolean },
  data() {
    return {
      mailGunAccount: {} as MailGunAccount,
      apiKey: '',
      domain: '',
      loading: false,
      mailgunError: undefined
    }
  },
  async created() {
    this.refresh()
  },
  watch: {
    bus(newVal) {
      this.subscribe()
    },
    fetchingData() {
      this.loading = this.fetchingData;
    },
    companyMailgun(newVal) {
      this.refresh()
    }
  },
  mounted() {
    this.subscribe()
  },
  methods: {
    subscribe() {
      if (this.bus) {
        console.log('subscribing')
        this.bus.$on('refresh', this.refresh)
      }
    },
    edit() {
      this.$emit('edit', { companyId: this.companyId })
    },
    async refresh(event?: any) {
      try {
        this.loading = true
        if (!this.companyId || (this.companyMailGun && this.companyId !== this.companyMailgun.company_id)) return
        if (this.companyMailgun) {
          this.apiKey = this.companyMailgun.api_key
          this.domain = this.companyMailgun.domain
          this.mailgunError = this.companyMailgun.mailgun_error || undefined
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    }
  }
})
</script>

