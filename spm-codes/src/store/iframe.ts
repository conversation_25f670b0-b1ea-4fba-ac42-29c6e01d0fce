import { <PERSON><PERSON><PERSON>, ActionContext } from 'vuex'
import { RootState, IframeState } from './state_models'

export const iframe: Module<IframeState, RootState> = {
  namespaced: true,
  state: { handshake: {} },
  getters: {
    getHandshake: (
      state: IframeState,
      getters: any,
      rootState: RootState,
      rootGetters: any
    ) => {
      return state.handshake
    },
  },
  mutations: {
    updateHandshake: (
      state: IframeState,
      handshake: { [key: string]: any }
    ) => {
      state.handshake = handshake
    },
    updatePassword: (state: IframeState, onboardingPassword: boolean) => {
      state.onboardingPassword = onboardingPassword
    },
  },
  actions: {
    updateHandshake(
      context: ActionContext<IframeState, RootState>,
      handshake: { [key: string]: any }
    ) {
      context.commit('updateHandshake', handshake)
    },
    updatePassword(
      context: ActionContext<IframeState, RootState>,
      onboardingPassword: boolean
    ) {
      context.commit('updatePassword', onboardingPassword)
    },
  },
}