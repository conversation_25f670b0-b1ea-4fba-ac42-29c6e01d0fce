<template>
  <div class="hl_settings--body" style="width:100%;">
    <div class="container-fluid">
      <div class="row top-buffer">
        <div
          class="col-sm-8 col-md-8 offset-sm-2 offset-md-2"
          style="background: #ffffff; padding: 25px; border-radius: 4px"
        >
          <div class="row">
            <div class="col-sm-12">
              <h5 style="margin-bottom: 20px">
                Which business do you want to link to this location?
              </h5>
            </div>
          </div>
          <span v-if="!processing && googleLocations.length === 0">
            {{
              errorMessage ||
              `You don't have any connected businesses under this account.`
            }}
          </span>
          <select
            v-if="googleLocations.length > 0"
            class="selectpicker"
            v-model="selectedPage"
            v-validate="'required'"
            name="googleLocation"
            data-vv-as="Google location"
          >
            <option value>Select Page</option>
            <option
              v-for="page in googleLocations"
              v-bind:key="page.id"
              :value="page"
              :selected="page.name == location.gmb.id"
            >
              {{ page.locationName }} -
              {{ page.address && page.address.locality }},
              {{ page.address && page.address.administrativeArea }}
            </option>
          </select>
          <span v-show="errors.has('googleLocation')" class="--red">{{
            errors.first('googleLocation')
          }}</span>
          <div v-if="googleLocations.length > 0" class="option option-md mt-3">
            <input
              type="checkbox"
              id="enable-call-tracking"
              v-model="enableCallTracking"
            />
            <label for="enable-call-tracking"> Enable GMB call tracking </label>
            <i
              class="info-icon fas fa-info-circle ml-1"
              v-b-tooltip.click.html="gmbCallTrackingInfo"
            ></i>
          </div>
        </div>
      </div>
      <div class="row top-buffer">
        <div
          class="col-sm-8 col-md-8 offset-sm-2 offset-md-2"
          style="text-align: right"
        >
          <div
            v-if="processing"
            style="width: 95px; height: 43px; float: right"
          >
            <moon-loader :loading="processing" color="#37ca37" size="30px" />
          </div>
          <button
            v-else-if="googleLocations.length === 0"
            type="submit"
            class="btn btn-success"
            @click.prevent="closeWindow"
          >
            Close!
          </button>
          <button
            v-else
            type="submit"
            class="btn btn-success"
            @click.prevent="set_google_page"
          >
            Connect!
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { Location, OAuth2, User } from '@/models'
import { UxMessage } from '@/util/ux_message'
import { UserState } from '@/store/state_models'
import { trackGaEvent } from '@/util/helper'

declare var $: any

export default Vue.extend({
  inject: ['uxmessage'],
  data() {
    return {
      googleLocations: [] as { [key: string]: any }[],
      currentLocationId: '',
      selectedPage: '' as { [key: string]: any } | string,
      location: undefined as Location | undefined,
      processing: false,
      errorMessage: null,
      enableCallTracking: true,
      user: undefined as User | undefined,
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
    },
  },
  computed: {
    gmbCallTrackingInfo() {
      if (this.user && this.user.type === 'agency')
        return 'This will send a text message to your callers if you are unable to answer the call. The modification to that can be made under Workflows > GMB Workflow: Missed Call Text-Back <a href="https://help.gohighlevel.com/support/solutions/articles/***********-call-tracking-and-missed-call-text-back-through-google-my-business">Click here for more info</a>'
      else
        return 'This will send a text message to your callers if you are unable to answer the call. The modification to that can be made under Workflows > GMB Workflow: Missed Call Text-Back'
    },
  },
  async created() {
    this.processing = true
    this.currentLocationId = this.$router.currentRoute.params.location_id
    try {
      if (!this.currentLocationId) {
        this.errorMessage = 'No location id'
        throw new Error('No location id')
      }
      this.location = new Location(
        await this.$store.dispatch('locations/getCurrentLocation', this.currentLocationId)
      )

      let googleConnections: OAuth2[] = await OAuth2.getAllByLocationIdAndType(
        this.currentLocationId,
        OAuth2.TYPE_GOOGLE
      ).get()
      googleConnections = googleConnections.docs.map(doc => new OAuth2(doc))
      const { primaryGoogleConnectionId } = this.location

      const googleConnection: OAuth2 = googleConnections.find(
        connection => connection.id === primaryGoogleConnectionId
      )
      const authUser = await this.$store.dispatch('auth/get')
      this.user = await User.getById(authUser.userId)

      let response = await this.$http.get('/gmb/get_locations', {
        params: {
          location_id: this.currentLocationId,
        },
      })

      const isSupport = Boolean(this.$route.query.support === 'true')
      const isAdmin = this.user.isAdmin
      const isTheOwner =
        googleConnection &&
        googleConnection.ownerId &&
        googleConnection.ownerId === authUser.userId

      if (!isAdmin && !isSupport && !isTheOwner) {
        this.errorMessage =
          'Only the user who connected the account can change pages.'
        throw new Error(
          'Only the user who connected the account can change pages.'
        )
      }
      this.googleLocations = response.data
    } catch (err) {
      console.log(err.response)
      if (err.response?.data?.error === 'oauth') {
        this.$uxMessage('confirmation', err.response.data.msg, () => {
          this.$router.push({ name: 'integrations_settings' })
        })
      }
    } finally {
      this.processing = false
    }
  },
  methods: {
    async set_google_page(event: any) {
      if (!this.location || !this.selectedPage) return

      const result = await this.$validator.validateAll()
      if (!result) {
        return false
      }

      this.processing = true

      try {
        // Moved from Created to Set function
        await this.$http.get('/gmb/delete_reviews', {
          params: {
            location_id: this.currentLocationId,
          },
        })
      } catch (err) {
        //
      }
      try {
        let name = this.selectedPage.locationName
        if (this.selectedPage.address) {
          name += ' - '
          if (this.selectedPage.address.locality)
            name += this.selectedPage.address.locality
          if (
            this.selectedPage.address.locality &&
            this.selectedPage.address.administrativeArea
          )
            name += ', '
          if (this.selectedPage.address.administrativeArea)
            name += this.selectedPage.address.administrativeArea
        }
        let response = await this.$http.post('/gmb/link', {
          location_id: this.location.id,
          gmb_location_id: this.selectedPage.name,
          gmb_location_name: name,
          gmb_places_id: this.selectedPage.locationKey.placeId,
          gmb_location_only_name: this.selectedPage.locationName,
          is_verified: this.selectedPage.locationState.isVerified,
          enable_call_tracking: this.enableCallTracking,
        })

        trackGaEvent(
          'Integration_Settings',
          'Successfully GMB connected',
          `Successfully GMB connected for location: ${this.currentLocationId}`
        )

        opener.postMessage(
          { actionType: 'close', page: 'integrations_settings' },
          location.origin
        )
        window.close()
      } catch (err) {
        if (err.message === `Cannot read property 'postMessage' of null`)
          this.$router.push({ name: 'integrations_settings' })
        this.processing = false
      }
    },
    closeWindow() {
      window.close()
      this.$router.push({ name: 'integrations_settings' })
    },
  },
  mounted() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
  updated() {
    const $selectpicker = $(this.$el).find('.selectpicker')
    if ($selectpicker) {
      $selectpicker.selectpicker('refresh')
    }
  },
})
</script>
