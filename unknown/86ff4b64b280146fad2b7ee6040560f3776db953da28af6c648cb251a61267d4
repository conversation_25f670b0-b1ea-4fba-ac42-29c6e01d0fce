<template>
   <div class="search-form">
      <i class="icon icon-loupe"></i>
      <input
        type="text"
        class="form-control form-light"
        :placeholder="'Search Contact'"
        v-model="searchText"
        :disabled="disableSearch"
        @focus="$emit('focus')"
      />
      <div class="search-right">
        <moon-loader color="#1ca7ff" v-if="!searchText.length" :loading="searching" size="15px"/>
        <svg v-if="searchText.length" v-on:click="clearSearchBox" width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M8.5 0C3.80554 0 0 3.80554 0 8.5C0 13.1943 3.80554 17 8.5 17C13.1943 17 17 13.1943 17 8.5C17 3.80554 13.1943 0 8.5 0ZM11.5052 11.5052C11.2113 11.7991 10.7306 11.7991 10.4367 11.5052L8.5 9.56855L6.56332 11.5052C6.26941 11.7991 5.78869 11.7991 5.49478 11.5052C5.20087 11.2113 5.20087 10.7306 5.49478 10.4367L7.43146 8.5L5.49478 6.56332C5.20087 6.26941 5.20087 5.78869 5.49478 5.49478C5.78869 5.20087 6.26941 5.20087 6.56332 5.49478L8.5 7.43146L10.4367 5.49478C10.7306 5.20087 11.2113 5.20087 11.5052 5.49478C11.7991 5.78869 11.7991 6.26941 11.5052 6.56332L9.56855 8.5L11.5052 10.4367C11.7991 10.7306 11.7991 11.2113 11.5052 11.5052Z" fill="#9E9E9E"/>
        </svg>
      </div>
    </div>
</template>

<script lang="ts">
import Vue from 'vue'
export default Vue.extend({
  props: ['searching', 'disableSearch'],
  data() {
    return {
      searchText: ''
    }
  },
  watch: {
    searchText() {
      this.$emit('search', this.searchText);
      this.$emit('updateLabel', this.searchText);
    }
  },
  methods: {
    clearSearchBox(){
        this.searchText = '';
    }
  }
})
</script>
<style lang="scss" scoped>
  .search-form {
    border: 1px solid #AEAEAE;
    box-sizing: border-box;
    border-radius: 5px;
   .icon {
     font-size: 10px;
    }

    input {
       height: 37px;
      &:focus{
      outline: none;
      box-shadow: none;
    }
    &:disabled{
      cursor: no-drop;
    }
  }
  .form-control{
    &:focus,&:active{
      background: #fff !important;
    }
  }
  .search-right {
    position: absolute;
    top: 50%;
    right: 8px;
    transform: translateY(-38%);
  }
}
</style>

