<template>
  <div class="reporting-email-stats col-md-6 mb-3">
    <div class="card">
      <div class="card-header">
        <div class="d-flex">
          <h2 class="mr-0">Email</h2>
          <div class="ml-2 mt-1">
            <span
              style="margin-top: -7px;"
              class="input-group-addon"
              v-b-tooltip.hover
              title="Stats exclude automated Email such as campaign Email."
            >
              <i class="fas fa-question-circle"></i>
            </span>
          </div>
        </div>
      </div>
      <div v-if="!isLoading" class="card-body">
        <table class="table">
          <tbody>
            <tr>
              <td>Sent</td>
              <td>{{data && data.total ? data.total.count || 0 : 0}}</td>
              <td>
                <span v-html="getPercentHTML(data && data.total ? data.total.percentage || 0 : 0)"/>
              </td>
            </tr>
            <tr>
              <td>Opened</td>
              <td>{{data && data.opened ? data.opened.count || 0 : 0}}</td>
              <td>
                <span v-html="getPercentHTML(data && data.opened ? data.opened.percentage || 0 : 0)"/>
              </td>
            </tr>
            <tr>
              <td>Clicked</td>
              <td>{{data && data.clicked ? data.clicked.count || 0 : 0}}</td>
              <td>
                <span v-html="getPercentHTML(data && data.clicked ? data.clicked.percentage || 0 : 0)"/>
              </td>
            </tr>
            <tr>
              <td>Replied</td>
              <td>{{data && data.replied ? data.replied.count || 0 : 0}}</td>
              <td>
                <span v-html="getPercentHTML(data && data.replied ? data.replied.percentage || 0 : 0)"/>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div v-else class="card-body">
        <MoonLoader size="30px" radius="50%" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import MoonLoader from '../MoonLoader'
import get_percent_change_html from '../../../util/get_percent_change_html'

export default Vue.extend({
  components: {
    MoonLoader,
  },
  props: {
    isLoading: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default(){
        return {}
      }
    }
  },
  methods: {
    getPercentHTML : get_percent_change_html
  },
})
</script>
