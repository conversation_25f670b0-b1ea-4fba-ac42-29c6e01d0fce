<template>
  <div class="website-template card">
    <!-- <div class="card-header">
      <h2>{{ template.name }}</h2>
    </div> -->
    <div class="card-body">
      <div class="image" :style="{ backgroundImage: `url(${template.image})` }"></div>
      <div class="content">
        <h6>{{ template.name }}</h6>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { WebsiteTemplate } from '../../../models'
export default Vue.extend({
  props: {
    template: {
      type: Object as () => WebsiteTemplate,
      required: true
    }
  }
})
</script>
<style scoped>
.website-template {
  cursor: pointer;
  transition: transform 0.25s ease;
}

.website-template:hover {
  transform: translateY(-10px);
}

.website-template .card-body {
  padding: 0;
}

.website-template .image {
  background-color: darkgray;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  width: 100%;
  height: 150px;
  border-radius: 0.3125em 0.3125em 0 0;
}

.website-template .content {
  padding: 12px;
}

.website-template .content h6 {
  margin: 0;
}
</style>
