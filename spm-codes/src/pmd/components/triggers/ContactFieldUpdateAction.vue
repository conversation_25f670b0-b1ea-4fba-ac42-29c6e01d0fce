<template>
  <div>
    <div class="form-group" v-if="fields.length>0">
      <label>Define filters</label>
      <div
        class="form-input-group filters items-center flex space-x-1"
        v-for="field in fields"
        style="margin-bottom: 5px;"
      >
        <CustomDropDownElement
          side="left"
          :condition="field"
          :options="availableFilters"
          placeholder="Select field"
          :filterMaster="localFilters"
          group="true"
          @update="saveResponse(field, 'field', $event)"
        />
        <CustomDropDownElement
          v-if="field.field && getFilter(field).type==='select'"
          side="right"
          :condition="field"
          :options="getFilter(field).options"
          :placeholder="getFilter(field).placeHolder"
          :filterMaster="getFilter(field).options"
          :allowMultiple="getFilter(field).allowMultiple"
          @update="saveResponse(field, getFilter(field).allowMultiple ? 'multiple_select' : 'value', $event)"
        />
        <UITextInputGroup
          v-if="field.field && getFilter(field).type==='input'"
          :type="getFilter(field).valueType"
          :placeholder="getFilter(field).placeHolder"
          :value="field.value"
          @input="saveTypeValue(field, $event)"
        />
        <UITextAreaGroup
          v-if="field.field && getFilter(field).type==='large_text'"
          :type="getFilter(field).valueType"
          :placeholder="getFilter(field).placeHolder"
          :value="field.value"
          @input="saveTypeValue(field, $event)"
        />
        <PhoneNumber
          v-if="field.field && getFilter(field).type==='phone'"
          :placeholder="getFilter(field).placeHolder"
          v-model="field.value"
          v-validate="'phone'"
          name="msgsndr_phone"
          autocomplete="new-phone"
          data-vv-as="phone"
          :currentLocationId="currentLocationId"
          @keyup="saveResponse(field, 'phone', field.value)"
        />
        <TextboxListField
          v-if="field.field && getFilter(field).type==='textboxList'"
          class="textbox-list"
          :placeholder="getFilter(field).placeHolder"
          :picklistOptions="getFilter(field).options"
          :type="getFilter(field).valueType"
          :currentLocationId="currentLocationId"
          v-model="field.value"
          @input="saveTypeValue(field, $event)"
        />
        <DateField
          class="date-box"
          v-if="field.field && getFilter(field).type==='date'"
					v-model="field.value"
					:placeholder="getFilter(field).placeHolder"
          @input="saveResponse(field, 'date', $event)"
				/>
        <div style="margin-left: 10px;flex: unset !important;" class="pointer">
          <i class="icon icon-trash --light" @click.prevent="removeField(field)"></i>
        </div>
      </div>
    </div>
    <div class="form-group" v-if="availableFilters.length > 0">
      <a href="javascript:void(0);" @click.prevent="addField">
        <i class="fas fa-plus"></i> Add field
      </a>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
const CustomDropDownElement = () => import('../CustomDropDownElement.vue')
const  PhoneNumber = () => import('../util/PhoneNumber.vue')
import TextboxListField from '../util/TextboxListField.vue'
import DateField from '../util/DateField.vue'
import { CustomField, FieldType, getCountryDateFormat } from '@/models'
import Datepicker from 'vuejs-datepicker';
import { orderBy } from 'lodash'
export default Vue.extend({
  props: ['action'],
  components: { CustomDropDownElement, PhoneNumber, Datepicker, TextboxListField, DateField },
  data() {
    return {
      currentLocationId: '',
      fields: [] as { [key: string]: any }[],
      localFilters: [
        {
          placeHolder: "First Name",
          title: "First Name",
          value: "firstName",
          valueType: "text",
          type: 'input'
        },
        {
          placeHolder: "Last Name",
          title: "Last Name",
          value: "lastName",
          valueType: "text",
          type: 'input'
        },
        {
          placeHolder: "Email",
          title: "Email",
          value: "email",
          valueType: "text",
          type: 'input'
        },
        {
          placeHolder: "Phone",
          title: "Phone",
          value: "phone",
          valueType: "text",
          type: 'phone'
        },
        {
          placeHolder: "Date of Birth",
          title: "Date of Birth",
          value: "dateOfBirth",
          valueType: "text",
          type: 'date'
        },
        {
          placeHolder: "Contact Source",
          title: "Contact Source",
          value: "source",
          valueType: "text",
          type: 'input'
        },
        {
          placeHolder: "Contact Type",
          title: "Contact Type",
          value: "type",
          valueType: "text",
          type: 'select',
          options: [{ title: 'Customer', value: 'customer' }, { title: 'Lead', value: 'lead' }]
        },
        {
          placeHolder: "Business Name",
          title: "Business Name",
          value: "companyName",
          valueType: "text",
          type: 'input'
        }
      ],
      customFields:  [] as CustomField[],
    }
  },
  computed: {
    availableFilters() {
      if (this.localFilters.length !== 0) {
        let {
          true: customFilter = [],
          undefined: filter = []
        } = lodash.groupBy(this.localFilters, 'customField')
        const groupOption = [
          { title: 'Standard filters', options: filter },
          { title: 'Custom field filters', options: customFilter }
        ]
        return groupOption
      }
      return []
    }
  },
  watch: {
    customFields() {
      this.addCustomFilters()
    }
  },
  methods: {
    addField() {
      this.fields.push({ field: '', value: '' });
    },
    publishUpdate() {
      Vue.set(this.action, 'fields', this.fields);
      this.$emit('update:action', this.action);
    },
    saveResponse(field: { [key: string]: any }, type: string, picked: any) {
      if (type === 'field') {
        Vue.set(field, 'field', picked.value);
        Vue.set(field, 'title', picked.title);
        Vue.set(field, 'value', '');
      }
      else if (type === 'multiple_select'){
        let options = this.getFilter(field).options
        let newValues: string[] = []
        if (Array.isArray(field.value)) {
          options.map(data => {
            if (
              field.value.includes(data.value) &&
              !newValues.includes(data.value)
            ) {
              newValues.push(data.value)
            }
          })
        }
        if (newValues.indexOf(picked.value) !== -1)
          newValues.splice(newValues.indexOf(picked.value), 1)
        else newValues.push(picked.value)
        Vue.set(field, 'value', newValues)
      }
      else if (type === 'date') {
        Vue.set(field, 'value', picked);
      }
      else if(type === 'phone'){
        Vue.set(field, 'title', this.getFilter(field).title);
        Vue.set(field, 'value', picked);
        Vue.set(field, 'type', this.getFilter(field).type);
      }
      else {
        Vue.set(field, 'value', picked.value);
      }
      this.publishUpdate();
    },
    getFilter(field: { [key: string]: any }): { [key: string]: any } {
      return lodash.find(this.localFilters, { value: field.field }) || {}
    },
    saveTypeValue(field: { [key: string]: any }, event: any) {
      Vue.set(field, 'value', event);
      Vue.set(field, 'title', this.getFilter(field).title);
      this.publishUpdate();
    },
    removeField(field: { [key: string]: any }) {
      if (this.fields.indexOf(field) !== -1) {
        this.fields.splice(this.fields.indexOf(field), 1)
        this.publishUpdate()
      }
    },
    addCustomFilters(){
      if(this.customFields.length){
          this.customFields.forEach((v: CustomField)=>{
            let data = {
            id: v.id,
            placeHolder: v.placeholder || v.name,
            title: v.name,
            value: v.fieldKey.substring(8),
            valueType: 'text',
            type: 'select',
            options: v.picklistOptions,
            customField: true
          }
          let options: { [key: string]: any }[] = []
          switch (FieldType[v.dataType]) {
            case FieldType.TEXT:
              data.type = "input"
              break;
            case FieldType.LARGE_TEXT:
              data.type = "large_text"
              break;
            case FieldType.TEXTBOX_LIST:
              data.type = "textboxList"
              data.valueType = "input"
              break
            case FieldType.FILE_UPLOAD:
            case FieldType.SIGNATURE:
              data.type = "input"
              break
            case FieldType.DATE:
              data.type = "date"
              break
            case FieldType.PHONE:
              data.type = 'phone'
              break
            case FieldType.MULTIPLE_OPTIONS:
            case FieldType.CHECKBOX:
              data.type = 'select'
              data.allowMultiple = true
              if(v.picklistOptions){
                v.picklistOptions.forEach(poption => {
                  options.push({ title: poption, value: poption })
                })
              }
              data.options = options
              break;
            case FieldType.SINGLE_OPTIONS:
            case FieldType.RADIO:
              data.type = 'select'
              if(v.picklistOptions){
                v.picklistOptions.forEach(poption => {
                  options.push({ title: poption, value: poption })
                })
              }
              if(v.picklistOptionsImage){
                v.picklistOptionsImage.forEach(poption => {
                  options.push({ title: poption.label, value: poption.label })
                })
              }
              data.options = options
              break
            case FieldType.NUMERICAL:
            case FieldType.MONETORY:
              data.type = 'input'
              data.valueType = 'number'
              break;
          }
          if(![FieldType.FILE_UPLOAD, FieldType.SIGNATURE].includes(FieldType[v.dataType]))
            this.localFilters.push(data);
        })
      }
    }
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id;
    if (this.action.fields) this.fields = this.action.fields;
    await CustomField.getByLocationId(this.currentLocationId)
      .get()
      .then((querySnapshot: any) => {
        let fields = querySnapshot.docs.map(
          (d: any) => new CustomField(d)
        )
        try {
          this.customFields = orderBy(fields, [field => field?.name?.toLowerCase()])
        } catch (err) {
          this.customFields = fields
        }
      });
  }
})
</script>
<style scoped>
.file-upload {
  padding: 12px;
}
</style>
