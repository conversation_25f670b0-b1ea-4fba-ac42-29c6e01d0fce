import { <PERSON><PERSON><PERSON>, ActionContext } from 'vuex'
import { RootState, FolderState, Folder } from './state_models'

let unsubscribe: () => void
export const campaign_folder: Module<FolderState, RootState> = {
  namespaced: true,
  state: {
    FolderItems: []
  },
  getters: {
    FoldersList(state: FolderState) {
      return state.FolderItems.map(item => item.folderId)
    },
  },
  mutations: {
    ADD_FOLDER(state: FolderState, payload: { locationId: Folder ,folderId: Folder }) {
      let index = state.FolderItems.findIndex(obj => obj.folderId === payload.folderId)

      if(index === -1) {
        state.FolderItems.push(payload)
      } else {
        state.FolderItems.splice(index, 1)
      }
      
      if(state.FolderItems.length === 0) return
    }
  },
  actions: {
    updateFolder({commit}, event: FolderState) {
      commit('ADD_FOLDER', event)
    }
  }
} 
