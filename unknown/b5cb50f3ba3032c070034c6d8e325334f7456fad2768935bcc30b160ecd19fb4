<template>
  <Filters
		:conditions="conditions"
		:filterMaster="filterMaster"
		v-on:update:conditions="$emit('update:conditions', $event)"
	/>
</template>

<script lang="ts">
import Vue from 'vue'
import { Tag } from '@/models';
import { Condition } from '@/models/trigger';
import Filters from './Filters.vue';

export default Vue.extend({
	props: ['conditions'],
	components: { Filters },
	data() {
		return {
      filterMaster: [],
      initialFilterMaster: [
        {
          id: "tag-added",
					placeHolder: 'Select Tag',
					title: 'Tag Added',
          value: 'tagsAdded',
          operator: "index-of-true",
					valueType: 'text',
					type: 'select',
					options: [] as { [key: string]: any }[]
				},
				{
          id: "tag-removed",
					placeHolder: 'Select Tag',
					title: 'Tag Removed',
          value: 'tagsRemoved',
          operator: "index-of-true",
					valueType: 'text',
					type: 'select',
					options: [] as { [key: string]: any }[]
				},
      ]
		}
  },
  watch: {
    conditions: {
      handler: function(conditions) {
        this.conditionsChanged(conditions)
      },
      deep: true
    }
  },
  methods: {
    conditionsChanged: function (conditions) {
      const tagAddedOption = lodash.find(conditions, { id: 'tag-added' });
      const tagRemovedOption = lodash.find(conditions, { id: 'tag-removed' });
      if (tagAddedOption) {
        if (lodash.findIndex(this.filterMaster, { id: 'tag-added' }) === -1) {
          this.filterMaster.push(lodash.find(this.initialFilterMaster, { id: 'tag-added' }));
        }
        if (lodash.findIndex(this.filterMaster, { id: 'tag-removed' }) !== -1)
          this.filterMaster.splice(lodash.findIndex(this.filterMaster, { id: 'tag-removed' }), 1);
      }
      if (tagRemovedOption) {
        if (lodash.findIndex(this.filterMaster, { id: 'tag-removed' }) === -1) {
          this.filterMaster.push(lodash.find(this.initialFilterMaster, { id: 'tag-removed' }));
        }
        if (lodash.findIndex(this.filterMaster, { id: 'tag-added' }) !== -1)
          this.filterMaster.splice(lodash.findIndex(this.filterMaster, { id: 'tag-added' }), 1);
      }
      if (!tagAddedOption && !tagRemovedOption) {
        this.filterMaster = lodash.clone(this.initialFilterMaster);
      }
    }
  },
	async created() {
		const currentLocationId = this.$router.currentRoute.params.location_id;
    const tagAddedOption = lodash.find(this.initialFilterMaster, { id: 'tag-added' });
    const tagRemovedOption = lodash.find(this.initialFilterMaster, { id: 'tag-removed' });
    const tagOptions = (await Tag.getByLocationId(currentLocationId)).map((tag) => {
      return {
        title: tag.name,
        value: tag.name
      }
    });
    tagAddedOption.options = tagOptions;
    tagRemovedOption.options = tagOptions;
    this.conditionsChanged(this.conditions);
	}
})
</script>

