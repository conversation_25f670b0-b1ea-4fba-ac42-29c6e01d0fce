<template>
  <div v-if="isSlidePanel && slideInfo" class="fields-settings-overlay">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="currentColor"
      class="bi bi-arrow-left"
      viewBox="0 0 16 16"
      @click="goBackToFields"
    >
      <path
        fill-rule="evenodd"
        d="M15 8a.5.5 0 0 0-.5-.5H2.707l3.147-3.146a.5.5 0 1 0-.708-.708l-4 4a.5.5 0 0 0 0 .708l4 4a.5.5 0 0 0 .708-.708L2.707 8.5H14.5A.5.5 0 0 0 15 8z"
      />
    </svg>

    <h6 class="text-center custom-field-name-heading">
      {{ slideInfo.slideName }}
    </h6>
    <div class="form-group">
      <UITextInputGroup
        type="text"
        label="Slide Name"
        v-model="slideInfo.slideName"
        @input="slideNameChange"
      />
    </div>

    <div class="form-group">
      <UITextInputGroup
        type="text"
        label="Slide Position"
        readonly
        :value="slideIndex + 1"
      />
    </div>

    <div class="form-group">
      <UITextLabel>Jump to</UITextLabel>

      <select v-if="isLastSlide" class="select-option" disabled>
        <option :value="undefined">Submit Survey</option>
      </select>

      <select
        v-else
        class="select-option"
        v-model="jumpToSlide"
        @change="handleSlideJumpChange($event)"
      >
        <option :value="undefined">Next Slide (Default)</option>
        <option
          v-for="(slide, index) in dropdownSlides"
          :value="slide.id"
          :key="index"
        >
          {{ slide.slideName }} ({{ findSlideIndex(slide.id) + 1 }}/{{
            allSlides.length
          }}
          )
        </option>
      </select>
    </div>
  </div>

  <div v-else-if="fieldInfo" class="fields-settings-overlay">
    <h6 class="text-center custom-field-name-heading">{{ fieldHeaderName }}</h6>

    <!--All setting for Text/Email/Numeric/Phone etc-->
    <div
      class="fields-settings-commom"
      v-if="
        fieldInfo.type != 'h1' &&
        fieldInfo.type != 'submit' &&
        fieldInfo.type != 'img' &&
        fieldInfo.type != 'captcha' &&
        fieldInfo.type != 'html' &&
        fieldInfo.type != 'source'
      "
    >
      <div v-if="source == 'survey'" class="form-group">
        <UITextLabel>Field description</UITextLabel>
        <button
          type="button"
          class="btn btn-warning btn-block"
          @click="editDescriptionModel"
        >
          Edit description
        </button>
      </div>
      <div class="form-group">
        <UITextInputGroup
          type="text"
          label="Field Title"
          v-bind:placeholder="fieldInfo.placeholder"
          v-model="inputSettings.fieldHeader"
          @input="headerSettingsField"
        />
      </div>
      <div
        class="form-group"
        v-if="
          fieldInfo.dataType !== 'TEXTBOX_LIST' &&
          fieldInfo.dataType !== 'SIGNATURE'
        "
      >
        <UITextInputGroup
          type="text"
          label="Placeholder"
          v-bind:placeholder="fieldInfo.placeholder"
          v-model="inputSettings.fieldDefaultText"
          @input="defaultTextSettingsField"
        />
      </div>

      <div class="form-group" v-if="!fieldArray.includes(fieldInfo.type)">
        <UITextLabel
          >Query key

          <span
            style="margin-left: 5px"
            class="input-group-addon"
            v-b-tooltip.hover
            :title="`The query key that can be used as a URL param to populate this field. ${
              fieldInfo.type === 'date'
                ? 'The date should be passed as yyyy-mm-dd'
                : ''
            } ${
              fieldInfo.type === 'checkbox'
                ? 'Pass in comma separated list of values to check the respective checkboxes'
                : ''
            }`"
          >
            <i class="fas fa-question-circle"></i>
          </span>
        </UITextLabel>
        <UITextInputGroup
          type="text"
          :placeholder="'Enter query string key'"
          v-model="inputSettings.hiddenFieldQueryKey"
          @input="hiddenQueryKeySettingField"
        />
      </div>

      <div class="form-check-req-hid">
        <label class="form-required">
          Required
          <UICheckbox
            class="required-fields"
            :disabled="inputSettings.isHidden"
            v-model="inputSettings.required"
          />
        </label>
        <label class="form-hide" v-if="!fieldArray.includes(fieldInfo.type)">
          Hidden Field
          <UICheckbox
            class="hide-fields"
            :disabled="inputSettings.required"
            v-model="inputSettings.isHidden"
          />
        </label>
      </div>
      <div class="form-group" v-if="inputSettings.isHidden">
        <UITextInputGroup
          v-if="
            fieldInfo.type === 'text' ||
            fieldInfo.type === 'email' ||
            fieldInfo.type === 'large_text' ||
            fieldInfo.type === 'numerical'
          "
          type="text"
          label="Hidden Value"
          :placeholder="'Enter values of ' + fieldInfo.label"
          v-model="inputSettings.hiddenFieldValue"
          @input="hiddenTextSettingsField"
        />
      </div>
    </div>


    <!--Image-->
    <div class="fields-settings-commom" v-if="fieldInfo.type == 'img'">
      <!-- Alt text for Image-->
      <div class="form-group">
        <UITextInputGroup
          type="text"
          label="Alt Text"
          v-bind:value="imageLocalProp.alt"
          @change="imageAlttext($event)"
        />
      </div>

      <div class="form-group">
        <UITextLabel>Alignment</UITextLabel>
        <a
          class="field-seeting-align ml-3"
          title="Align Left"
          @click="alterAlignmentImage('left')"
        >
          <i class="fa fa-align-left"></i>
        </a>
        <a
          class="field-seeting-align"
          title="Align Center"
          @click="alterAlignmentImage('middle')"
        >
          <i class="fa fa-align-center"></i>
        </a>
        <a
          class="field-seeting-align"
          title="Align Right"
          @click="alterAlignmentImage('right')"
        >
          <i class="fa fa-align-right"></i>
        </a>
      </div>

      <!-- Image Width -->
      <div class="style-group">
        <UITextLabel>Image Width</UITextLabel>
        <div class="pixel-count">
          <UITextInputGroup
            type="text"
            v-bind:value="imageLocalProp.width"
            @change="setImageWidth($event)"
          />
          <span class="px">px</span>
          <div class="pixel-count-btns">
            <button
              type="button"
              class="pixel-up"
              @click="clickImageWidthBtnPlus"
            >
              <i class="icon icon-arrow-up-1"></i>
            </button>
            <button
              type="button"
              class="pixel-down"
              @click="clickImageWidthBtnMinus"
            >
              <i class="icon icon-arrow-down-1"></i>
            </button>
          </div>
        </div>
      </div>
      <!-- Image Height -->
      <div class="style-group">
        <UITextLabel>Image Height</UITextLabel>
        <div class="pixel-count">
          <UITextInputGroup
            type="text"
            v-bind:value="imageLocalProp.height"
            @change="setImageHeight($event)"
          />
          <span class="px">px</span>
          <div class="pixel-count-btns">
            <button
              type="button"
              class="pixel-up"
              @click="clickImageHeightBtnPlus"
            >
              <i class="icon icon-arrow-up-1"></i>
            </button>
            <button
              type="button"
              class="pixel-down"
              @click="clickImageHeightBtnMinus"
            >
              <i class="icon icon-arrow-down-1"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
    <!--Image End-->

    <!--Captcha-->
    <div class="fields-settings-commom" v-if="fieldInfo.type == 'captcha'">
      <div class="form-group">
        <label>Field Header</label>
        <UITextInputGroup
          type="text"
          v-bind:placeholder="fieldInfo.placeholder"
          v-model="inputSettings.fieldHeader"
          @input="headerSettingsField"
        />
      </div>
    </div>

    <!--Button submit settings-->
    <div class="fields-settings-btn-submit" v-if="fieldInfo.type == 'submit'">
      <div class="form-group">
        <UITextLabel>Alignment</UITextLabel>
        <a
          class="field-seeting-align"
          title="Align Left"
          @click="alterAlignmentBtn('left')"
        >
          <i class="fa fa-align-left"></i>
        </a>
        <a
          class="field-seeting-align"
          title="Align Center"
          @click="alterAlignmentBtn('center')"
        >
          <i class="fa fa-align-center"></i>
        </a>
        <a
          class="field-seeting-align"
          title="Align Right"
          @click="alterAlignmentBtn('right')"
        >
          <i class="fa fa-align-right"></i>
        </a>
      </div>

      <div class="form-group">
        <UITextInputGroup
          type="text"
          v-model="formButtonText"
          @input="btnSubmitSettingsField"
          placeholder="Submit Text"
          label="Text"
        />
      </div>

      <div class="option-card">
        <div class="option-card-header">
          <h3>Button</h3>
        </div>
        <div class="option-card-body">
          <div class="style-group">
            <UITextLabel>Background</UITextLabel>
            <div class="color-picker">
              <span class="hash">#</span>
              <input
                type="text"
                class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800 disabled:opacity-50"
                v-bind:value="buttonStyle.bgColor"
                @change="setButtonBackground($event.target.value)"
              />
              <span
                class="the-color"
                v-bind:style="{ backgroundColor: '#' + buttonStyle.bgColor }"
                @click="pickerOnButtonBgClick"
              ></span>

              <chrome-picker
                :value="buttonStyle.bgColor"
                v-show="buttonStyle.showBgColorPicker"
                style="position: absolute; top: 42px; right: 0px; z-index: 99"
                @input="pickerButtonBgColor"
              />
            </div>
          </div>
          <div class="style-group">
            <label>Text</label>
            <div class="color-picker">
              <span class="hash">#</span>
              <input
                type="text"
                class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800 disabled:opacity-50"
                v-bind:value="buttonStyle.color"
                @change="setButtonColor($event.target.value)"
              />
              <span
                class="the-color"
                v-bind:style="{ backgroundColor: '#' + buttonStyle.color }"
                @click="pickerOnButtonColorClick"
              ></span>

              <chrome-picker
                :value="buttonStyle.color"
                v-show="buttonStyle.showColorPicker"
                style="position: absolute; top: 50px; right: 0px; z-index: 99"
                @input="pickerButtonColor"
              />
            </div>
          </div>
          <div class="style-group">
            <label>Border</label>
            <div class="border-picker">
              <input
                type="text"
                class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800 disabled:opacity-50"
                v-bind:value="buttonStyle.border"
                @change="setButtonBorder($event.target.value)"
              />
              <span class="px">px</span>
              <div class="pixel-count-btns">
                <button
                  type="button"
                  class="pixel-up"
                  @click="clickBorderBtnPlus"
                >
                  <i class="icon icon-arrow-up-1"></i>
                </button>
                <button
                  type="button"
                  class="pixel-down"
                  @click="clickBorderBtnMinus"
                >
                  <i class="icon icon-arrow-down-1"></i>
                </button>
              </div>
              <span class="the-color" style="background: #ddd"></span>
            </div>
          </div>
          <div class="style-group">
            <label>Corner Radius</label>
            <div class="pixel-count">
              <input
                type="text"
                class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800 disabled:opacity-50"
                v-bind:value="buttonStyle.radius"
                @change="setButtonRadius($event.target.value)"
              />
              <span class="px">px</span>
              <div class="pixel-count-btns">
                <button
                  type="button"
                  class="pixel-up"
                  @click="clickRadiusBtnPlus"
                >
                  <i class="icon icon-arrow-up-1"></i>
                </button>
                <button
                  type="button"
                  class="pixel-down"
                  @click="clickRadiusBtnMinus"
                >
                  <i class="icon icon-arrow-down-1"></i>
                </button>
              </div>
            </div>
          </div>
          <div class="style-group">
            <label>Padding</label>
            <div class="pixel-count">
              <input
                type="text"
                class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800 disabled:opacity-50"
                v-bind:value="buttonStyle.padding"
                @change="setButtonPadding($event.target.value)"
              />
              <span class="px">px</span>
              <div class="pixel-count-btns">
                <button
                  type="button"
                  class="pixel-up"
                  @click="clickPaddingBtnPlus"
                >
                  <i class="icon icon-arrow-up-1"></i>
                </button>
                <button
                  type="button"
                  class="pixel-down"
                  @click="clickPaddingBtnMinus"
                >
                  <i class="icon icon-arrow-down-1"></i>
                </button>
              </div>
            </div>
          </div>
          <br />
          <div class="d-flex justify-content-between">
            <p>Full width</p>
            <div class="toggle">
              <UIToggle
                id="fullwidth-toggle"
                :value="fullwidth"
                @input="alterFullWidth"
              />
              <label class="tgl-btn" for="fullwidth-toggle"></label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--Html element/Heading Tag HTML-->
    <div class="fields-settings-text-html" v-if="fieldInfo.type == 'h1'">
      <div class="form-group">
        <label>Alignment</label>
        <a
          class="field-seeting-align"
          title="Align Left"
          @click="alterAlignment('left')"
        >
          <i class="fa fa-align-left"></i>
        </a>
        <a
          class="field-seeting-align"
          title="Align Center"
          @click="alterAlignment('center')"
        >
          <i class="fa fa-align-center"></i>
        </a>
        <a
          class="field-seeting-align"
          title="Align Right"
          @click="alterAlignment('right')"
        >
          <i class="fa fa-align-right"></i>
        </a>
      </div>
      <div class="form-group">
        <label>Text</label>
        <UITextInputGroup
          type="text"
          v-model="htmlHeadingTag.label"
          @change="alterHeadingText"
          placeholder="Text"
        />
      </div>
      <!--Font Family-->
      <div class="form-group">
        <label>Font Family</label>
        <div class="btn-group font-family-dropdown">
          <button
            type="button"
            class="btn dropdown-toggle form-control"
            data-toggle="dropdown"
            aria-haspopup="true"
            aria-expanded="false"
          >
            {{ htmlHeadingTag.fontFamily | toTitleCase }}
          </button>
          <div class="dropdown-menu">
            <a class="dropdown-item" @click="alterFontFamily('roboto')"
              >Roboto</a
            >
            <a class="dropdown-item" @click="alterFontFamily('arial')">Arial</a>
            <a class="dropdown-item" @click="alterFontFamily('helvetica')"
              >Helvetica</a
            >
            <a class="dropdown-item" @click="alterFontFamily('times')">Times</a>
            <a class="dropdown-item" @click="alterFontFamily('palatino')"
              >Palatino</a
            >
            <a class="dropdown-item" @click="alterFontFamily('garamond')"
              >Garamond</a
            >
          </div>
        </div>
      </div>
      <!--Color-->
      <div class="style-group">
        <label>Font Color</label>
        <div class="color-picker">
          <span class="hash">#</span>
          <input
            type="text"
            class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800 disabled:opacity-50"
            v-bind:value="htmlHeadingTag.color"
            @change="setHeadingColor($event.target.value)"
          />
          <span
            class="the-color"
            v-bind:style="{ backgroundColor: '#' + htmlHeadingTag.color }"
            @click="pickerOnFormColorClick"
          ></span>

          <chrome-picker
            :value="htmlHeadingTag.color"
            v-show="htmlHeadingTag.showColorPicker"
            style="position: absolute; top: 50px; right: 0px; z-index: 99"
            @input="pickerFormColor"
          />
        </div>
      </div>
      <!--Font SIze-->
      <div class="form-group style-group">
        <label>Size</label>
        <div class="pixel-count">
          <input
            type="text"
            class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800 disabled:opacity-50"
            v-bind:value="htmlHeadingTag.fontSize"
            @change="alertHeadingFontSize($event.target.value)"
          />
          <span class="px">px</span>
          <div class="pixel-count-btns">
            <button type="button" class="pixel-up" @click="clickFontSizePlus">
              <i class="icon icon-arrow-up-1"></i>
            </button>
            <button
              type="button"
              class="pixel-down"
              @click="clickFontSizeMinus"
            >
              <i class="icon icon-arrow-down-1"></i>
            </button>
          </div>
        </div>
      </div>
      <!--Font weight-->
      <div class="form-group style-group">
        <label>Weight</label>
        <div class="pixel-count">
          <input
            type="text"
            class="mt-1 shadow-sm focus:ring-curious-blue-500 focus:border-curious-blue-500 block w-full sm:text-sm border-gray-300 rounded-md text-gray-800 disabled:opacity-50"
            v-bind:value="htmlHeadingTag.fontWeight"
            @change="alertHeadingFontWeight($event.target.value)"
          />
          <span class="px">px</span>
          <div class="pixel-count-btns">
            <button type="button" class="pixel-up" @click="clickWeightPlus">
              <i class="icon icon-arrow-up-1"></i>
            </button>
            <button type="button" class="pixel-down" @click="clickWeightMinus">
              <i class="icon icon-arrow-down-1"></i>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!--Custom HTML-->
    <div class="fields-settings-text-html" v-if="fieldInfo.type == 'html'">
      <!--Footer HTML-->
      <div class="option-card">
        <div class="option-card-header">
          <h3>Add Custom HTML/Script</h3>
        </div>
        <div class="option-card-body">
          <div class="form-group">
            <button
              type="button"
              class="btn btn-warning btn-block"
              @click="editCustomHtmlModel"
            >
              Edit Script
            </button>
          </div>
        </div>
      </div>
    </div>
    <!-- Source -->
    <div class="fields-settings-text-html" v-if="fieldInfo.type == 'source'">
      <input
        type="text"
        placeholder="Enter source value"
        class="form-control"
        v-model="sourceName"
        @change="
          $bus.$emit('field-setting-input', {
            type: 'source-name',
            value: sourceName,
          })
        "
      />
    </div>
    <!--Logic section start-->
    <div
      class="logic-container"
      v-if="
        source == 'survey' &&
        (fieldInfo.type == 'single_options' ||
          fieldInfo.type == 'checkbox' ||
          fieldInfo.type == 'radio')
      "
    >
      <hr class="logic-br" />
      <div class="option-card">
        <div class="option-card-header">
          <h3>Logic</h3>
        </div>
        <div class="option-card-body">
          <div v-for="(optionName, index) in pickFieldOptions">
            <div class="justify-content-between" v-if="optionName != ''">
              <p>
                <b>{{ index + 1 }}) </b>
                {{ optionName.label ? optionName.label : optionName }}
              </p>
              <select
                @change="alterLogicSkip($event, fieldInfo, index, optionName)"
                style="width: 100%"
              >
                <option value="-1">Select</option>
                <option
                  v-for="(skipItem, skipIndex) in logicSkipStore"
                  :value="skipIndex"
                  :selected="logicOptChecked(index, skipIndex)"
                >
                  {{ skipItem.name }}
                </option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import Datepicker from 'vuejs-datepicker'
import { mapState } from 'vuex'
import { UserState } from '../../../store/state_models'
import { User } from '../../../models/'
import { Chrome } from 'vue-color'

export default Vue.extend({
  props: {
    fieldInfo: {} as any,
    slideInfo: {} as any,
    isSlidePanel: false as boolean,
    slidePosition: 0 as number,
    allSlides: {} as any,
    formButtonStyle: {} as any,
    source: {
      default: 'form',
    },
    logicSkipStore: {} as any,
    currentSlide: Number,
  },
  components: {
    'chrome-picker': Chrome,
    Datepicker,
  },
  data() {
    return {
      fieldArray: [
        'select',
        'single_options',
        'multiple_options',
        'textbox_list',
        'signature',
        'file_upload',
      ],
      currentLocationId: '',
      loading: false,
      inputSettings: {
        fieldHeader: null,
        fieldDefaultText: '',
        required: false,
        hiddenFieldValue: '',
        hiddenFieldQueryKey: '',
        allowCountrySelection: false,
        isHidden: false,
      },
      htmlHeadingTag: {
        label: 'Text',
        fontFamily: 'Roboto',
        fontSize: 40,
        fontWeight: 400,
        color: '000000',
        showColorPicker: false,
        align: 'left',
      },
      buttonStyle: {
        bgColor: '2A3135',
        color: 'FFFFFF',
        border: 0,
        radius: 0.3125,
        padding: 11,
        showBgColorPicker: false,
        showColorPicker: false,
      },
      fieldHeaderName: '',
      formButtonText: 'Submit',
      fullwidth: false,
      pickFieldOptions: [] as any,
      selectedLogicStore: {} as any,
      sourceName: '',
      imageLocalProp: this.fieldInfo,
    }
  },
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
    },
    'inputSettings.isHidden': function () {
      this.hiddenSettingsField()
    },
    'inputSettings.required': function () {
      this.requiredSettingsField()
    }
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),

    dropdownSlides() {
      let slides = []
      this.allSlides.forEach((slide, index) => {
        if (index !== this.slideIndex) {
          slides.push(slide)
        }
      })
      return slides
    },
    slideIndex() {
      let index = lodash.findIndex(
        this.allSlides,
        (item: any) => item.id === this.slideInfo.id
      )
      console.log('check', index)
      return index
    },
    jumpToSlide: {
      get() {
        if (!this.slideInfo.jumpTo) return undefined

        let index = lodash.findIndex(
          this.allSlides,
          (item: any) => item.id === this.slideInfo.jumpTo
        )
        if (index === -1) {
          return undefined
        } else {
          return this.slideInfo.jumpTo
        }
      },
      set(newValue) {
        this.slideInfo.jumpTo = newValue
      },
    },
    isLastSlide() {
      return this.slideIndex === this.allSlides.length - 1
    },
  },
  methods: {
    saveForm(slug: string, value: any) {
      this.$bus.$emit('field-setting-form', {
        type: slug,
        value: value,
      })
    },
    findSlideIndex(slideId) {
      return lodash.findIndex(this.allSlides, { id: slideId })
    },
    fillFormValues() {
      //Form Input
      this.fieldHeaderName = this.fieldInfo.label
      this.inputSettings.fieldHeader = this.fieldInfo.label
      this.inputSettings.fieldDefaultText = this.fieldInfo.placeholder
      this.inputSettings.required = this.fieldInfo.required
      this.sourceName = this.fieldInfo.value
      this.inputSettings.isHidden = this.fieldInfo.hidden
      this.inputSettings.hiddenFieldValue = this.fieldInfo.hiddenFieldValue
      this.inputSettings.hiddenFieldQueryKey = this.fieldInfo.hiddenFieldQueryKey
      this.inputSettings.allowCountrySelection = this.fieldInfo.allowCountrySelection

      //Hadding tag if exist
      if (this.fieldInfo.type == 'h1') {
        if ('label' in this.fieldInfo) {
          this.htmlHeadingTag.label = this.fieldInfo.label
        }
        if ('font' in this.fieldInfo) {
          this.htmlHeadingTag.fontFamily = this.fieldInfo.font
        }
        if ('size' in this.fieldInfo) {
          console.log('size')
          this.htmlHeadingTag.fontSize = this.fieldInfo.size
        }
        if ('weight' in this.fieldInfo) {
          this.htmlHeadingTag.fontWeight = this.fieldInfo.weight
        }
        if ('color' in this.fieldInfo) {
          this.htmlHeadingTag.color = this.fieldInfo.color
        }
      }

      //Button
      else if (this.fieldInfo.type == 'submit') {
        this.formButtonText = this.fieldInfo.label
        this.buttonStyle.bgColor = this.formButtonStyle.bgColor
        this.buttonStyle.color = this.formButtonStyle.color
        this.buttonStyle.border = this.formButtonStyle.border
        this.buttonStyle.radius = this.formButtonStyle.radius
        this.buttonStyle.padding = this.formButtonStyle.padding
        this.fullwidth = this.fieldInfo.fullwidth
      }

      //Captcha
      else if (this.fieldInfo.type == 'captcha') {
        this.inputSettings.fieldHeader = this.fieldInfo.label
      }
    },
    headerSettingsField() {
      this.$bus.$emit('field-setting-input', {
        value: this.inputSettings.fieldHeader,
        type: 'label',
      })
    },
    goBackToFields() {
      this.$bus.$emit('field-setting-input', {
        type: 'deselect-slide',
      })
    },
    defaultTextSettingsField() {
      this.$bus.$emit('field-setting-input', {
        value: this.inputSettings.fieldDefaultText,
        type: 'placeholder',
      })
    },
    hiddenTextSettingsField() {
      this.$bus.$emit('field-setting-input', {
        value: this.inputSettings.hiddenFieldValue,
        type: 'hidden-value',
      })
    },
    hiddenQueryKeySettingField() {
      this.$bus.$emit('field-setting-input', {
        value: this.inputSettings.hiddenFieldQueryKey,
        type: 'hidden-query-key',
      })
    },
    allowCountrySelectionChange() {
      this.$bus.$emit('field-setting-input', {
        value: this.inputSettings.allowCountrySelection,
        type: 'allow-country-selection',
      })
    },
    dateIsChoosed(date) {
      this.inputSettings.hiddenFieldValue = this.formatDate(date)
      this.hiddenTextSettingsField()
    },
    formatDate(date) {
      const d = new Date(date)
      let month = '' + (d.getMonth() + 1)
      let day = '' + d.getDate()
      const year = d.getFullYear()

      if (month.length < 2) month = '0' + month
      if (day.length < 2) day = '0' + day

      return [year, month, day].join('-')
    },
    requiredSettingsField() {
      this.$bus.$emit('field-setting-input', {
        value: Boolean(this.inputSettings.required),
        type: 'required',
      })
    },
    hiddenSettingsField() {
      // this.fieldInfo.hiddenFieldValue = ''
      // this.inputSettings.hiddenFieldValue = ''
      this.$bus.$emit('field-setting-input', {
        value: Boolean(this.inputSettings.isHidden),
        type: 'hidden',
      })
    },
    btnSubmitSettingsField() {
      this.$bus.$emit('field-setting-input', {
        value: this.formButtonText,
        type: 'submit',
      })
    },
    clickFontSizePlus() {
      this.htmlHeadingTag.fontSize =
        parseFloat(this.htmlHeadingTag.fontSize) + 1
      this.$bus.$emit('field-setting-input', {
        value: this.htmlHeadingTag.fontSize,
        type: 'text-size',
      })
    },
    clickFontSizeMinus() {
      let val = parseFloat(this.htmlHeadingTag.fontSize)
      this.htmlHeadingTag.fontSize = val <= 1 ? 1 : val - 1
      this.$bus.$emit('field-setting-input', {
        value: this.htmlHeadingTag.fontSize,
        type: 'text-size',
      })
    },
    alertHeadingFontSize(size: number) {
      this.htmlHeadingTag.fontSize = size <= 0 ? 1 : size
      this.$bus.$emit('field-setting-input', {
        value: this.htmlHeadingTag.fontSize,
        type: 'text-size',
      })
    },
    slideNameChange() {
      this.$bus.$emit('slide-setting-input', {
        value: this.slideInfo.slideName,
        type: 'slide-name',
        index: this.slideIndex,
      })
    },
    handleSlideJumpChange(e) {
      this.$bus.$emit('slide-setting-input', {
        value: e.target.value,
        type: 'slide-jump-to',
        index: this.slideIndex,
      })
    },
    clickWeightPlus() {
      this.htmlHeadingTag.fontWeight =
        parseFloat(this.htmlHeadingTag.fontWeight) + 1
      this.$bus.$emit('field-setting-input', {
        value: this.htmlHeadingTag.fontWeight,
        type: 'text-weight',
      })
    },
    clickWeightMinus() {
      let val = parseFloat(this.htmlHeadingTag.fontWeight)
      this.htmlHeadingTag.fontWeight = val <= 1 ? 1 : val - 1
      this.$bus.$emit('field-setting-input', {
        value: this.htmlHeadingTag.fontWeight,
        type: 'text-weight',
      })
    },
    alertHeadingFontWeight(weight: number) {
      this.htmlHeadingTag.fontWeight = weight <= 0 ? 1 : weight
      this.$bus.$emit('field-setting-input', {
        value: this.htmlHeadingTag.fontWeight,
        type: 'text-weight',
      })
    },
    alterFontFamily(value: string) {
      this.htmlHeadingTag.fontFamily = value
      this.$bus.$emit('field-setting-input', {
        value: this.htmlHeadingTag.fontFamily,
        type: 'text-font',
      })
    },
    pickerFormColor(newColor: any) {
      var color = newColor.hex.replace('#', '')
      this.htmlHeadingTag.color = color
      this.$bus.$emit('field-setting-input', {
        value: this.htmlHeadingTag.color,
        type: 'text-color',
      })
    },
    setHeadingColor(color: string) {
      if (this.isValidHexColor(color)) {
        this.htmlHeadingTag.color = color
        this.$bus.$emit('field-setting-input', {
          value: this.htmlHeadingTag.color,
          type: 'text-color',
        })
      }
    },
    pickerOnFormColorClick() {
      this.htmlHeadingTag.showColorPicker = !this.htmlHeadingTag.showColorPicker
    },
    alterHeadingText() {
      this.$bus.$emit('field-setting-input', {
        value: this.htmlHeadingTag.label,
        type: 'text-label',
      })
    },
    alterAlignment(alignTo: any) {
      this.htmlHeadingTag.align = alignTo
      this.$bus.$emit('field-setting-input', {
        value: this.htmlHeadingTag.align,
        type: 'text-align',
      })
    },

    //-----Button Style---
    isValidHexColor(color: string) {
      let isValidColor: boolean = /(^[0-9A-F]{8}$)|(^[0-9A-F]{6}$)|(^[0-9A-F]{3}$)/i.test(
        color
      )
      return isValidColor
    },
    isColorPickerOpen(which: string) {
      if (which === 'btn-bg-color') {
        this.buttonStyle.showColorPicker = false
      } else {
        this.buttonStyle.showBgColorPicker = false
      }
    },
    setButtonBackground(color: string) {
      if (this.isValidHexColor(color)) {
        this.buttonStyle.bgColor = color
        this.$bus.$emit('field-setting-button', {
          value: this.buttonStyle.bgColor,
          type: 'bgcolor',
        })
      }
    },
    setButtonColor(color: string) {
      if (this.isValidHexColor(color)) {
        this.buttonStyle.color = color
        this.$bus.$emit('field-setting-button', {
          value: this.buttonStyle.color,
          type: 'color',
        })
      }
    },
    setButtonBorder(value: any) {
      if (!isNaN(value)) {
        this.buttonStyle.border = value
        this.$bus.$emit('field-setting-button', {
          value: this.buttonStyle.border,
          type: 'border',
        })
      }
    },
    setButtonRadius(value: any) {
      if (!isNaN(value)) {
        this.buttonStyle.radius = value
        this.$bus.$emit('field-setting-button', {
          value: this.buttonStyle.radius,
          type: 'radius',
        })
      }
    },
    setButtonPadding(value: any) {
      if (!isNaN(value)) {
        this.buttonStyle.padding = value
        this.$bus.$emit('field-setting-button', {
          value: this.buttonStyle.padding,
          type: 'padding',
        })
      }
    },
    clickBorderBtnPlus() {
      let val = parseFloat(this.buttonStyle.border)
      let border = val >= 100 ? 100 : val + 1
      this.setButtonBorder(border)
    },
    clickBorderBtnMinus() {
      let val = parseFloat(this.buttonStyle.border)
      let border = val <= 0 ? 0 : val - 1
      this.setButtonBorder(border)
    },
    clickRadiusBtnPlus() {
      let val = parseFloat(this.buttonStyle.radius)
      let radius = val >= 100 ? 100 : val + 1
      this.setButtonRadius(radius)
    },
    clickRadiusBtnMinus() {
      let val = parseFloat(this.buttonStyle.radius)
      let radius = val <= 0 ? 0 : val - 1
      this.setButtonRadius(radius)
    },
    clickPaddingBtnPlus() {
      let padding = parseFloat(this.buttonStyle.padding) + 1
      this.setButtonPadding(padding)
    },
    clickPaddingBtnMinus() {
      let val = parseFloat(this.buttonStyle.padding)
      let padding = val <= 0 ? 0 : val - 1
      this.setButtonPadding(padding)
    },
    //Button picker
    pickerButtonBgColor(newColor: any) {
      var color = newColor.hex8.replace('#', '')
      this.setButtonBackground(color)
    },
    pickerOnButtonBgClick() {
      this.isColorPickerOpen('btn-bg-color')
      this.buttonStyle.showBgColorPicker = !this.buttonStyle.showBgColorPicker
    },
    pickerButtonColor(newColor: any) {
      var color = newColor.hex8.replace('#', '')
      this.setButtonColor(color)
    },
    pickerOnButtonColorClick() {
      this.isColorPickerOpen('btn-color')
      this.buttonStyle.showColorPicker = !this.buttonStyle.showColorPicker
    },
    //Button Alignment
    alterAlignmentBtn(alignTo: any) {
      this.$bus.$emit('field-setting-button', {
        value: alignTo,
        type: 'btn-align',
      })
    },
    alterFullWidth() {
      this.fullwidth = !this.fullwidth
      this.$bus.$emit('field-setting-button', {
        value: this.fullwidth,
        type: 'btn-fullwidth',
      })
    },

    // Image style
    imageAlttext(value: any) {
      this.imageLocalProp.alt = value
      this.$bus.$emit('field-setting-image', {
        value: this.imageLocalProp.alt,
        type: 'alt',
      })
    },
    clickImageWidthBtnPlus() {
      let val = parseFloat(this.imageLocalProp.width)
      let width = val >= 1000 ? 1000 : val + 1
      this.setImageWidth(width)
    },
    clickImageWidthBtnMinus() {
      let val = parseFloat(this.imageLocalProp.width)
      let width = val <= 0 ? 0 : val - 1
      this.setImageWidth(width)
    },
    clickImageHeightBtnPlus() {
      let val = parseFloat(this.imageLocalProp.height)
      let height = val >= 1000 ? 1000 : val + 1
      this.setImageHeight(height)
    },
    clickImageHeightBtnMinus() {
      let val = parseFloat(this.imageLocalProp.height)
      let height = val <= 0 ? 0 : val - 1
      this.setImageHeight(height)
    },

    setImageWidth(value: any) {
      if (!isNaN(value)) {
        this.imageLocalProp.width = value
        this.$bus.$emit('field-setting-image', {
          value: this.imageLocalProp.width,
          type: 'width',
        })
      }
    },
    setImageHeight(value: any) {
      if (!isNaN(value)) {
        this.imageLocalProp.height = value
        this.$bus.$emit('field-setting-image', {
          value: this.imageLocalProp.height,
          type: 'height',
        })
      }
    },

    alterAlignmentImage(alignTo: any) {
      this.$bus.$emit('field-setting-image', {
        value: alignTo,
        type: 'img-align',
      })
    },

    // Image style End

    alterLogicSkip(
      event: any,
      fieldInfo: any,
      optionIndex: number,
      optionName: string
    ) {
      let index = event.target.value
      let data = {}
      let fieldId = fieldInfo.id

      if (fieldInfo.tag == 'gender') {
        fieldId = 'gender'
      }
      if (index < 0) {
        data.fieldId = fieldId
        data.optionIndex = optionIndex
        data.optionName = optionName.label ? optionName.label : optionName
        data.status = false
      } else {
        const newSkipStore = {}
        if (this.logicSkipStore[index] != undefined) {
          for (let key in this.logicSkipStore[index]) {
            //Never pass logic it can cause looping...
            if (key !== 'logic') {
              newSkipStore[key] = this.logicSkipStore[index][key]
            }
          }
        }
        data = newSkipStore
        data.fieldId = fieldId
        data.optionIndex = optionIndex
        data.optionName = optionName.label ? optionName.label : optionName
        data.status = true
      }
      this.$bus.$emit('field-setting-form-advance', {
        value: data,
        type: 'logic-skip',
      })
    },
    logicOptChecked(index: number, skipIndex: number) {
      if (!this.selectedLogicStore || !this.selectedLogicStore.id) {
        return false
      }
      let optId = this.fieldInfo.id
      if (this.fieldInfo.tag == 'gender') {
        optId = 'gender'
      }

      if (this.selectedLogicStore.logic[optId] !== undefined) {
        let optionStore = this.selectedLogicStore.logic[optId][
          'option_' + index
        ]
        if (optionStore) {
          const getSlideActualIndex = this.logicSkipStore.findIndex(
            item => item.id == optionStore.id
          )
          if (getSlideActualIndex == skipIndex) {
            return true
          }
        }
      }
    },
    updatePickListOptions() {
      //Fetch Logic for the selected Item
      this.selectedLogicStore = {}
      let fieldId = this.fieldInfo.id

      if (this.fieldInfo.tag == 'gender') {
        fieldId = 'gender'
      }

      if (
        fieldId !== undefined &&
        this.logicSkipStore &&
        this.logicSkipStore.length > 0
      ) {
        if (
          this.logicSkipStore[this.currentSlide] &&
          this.logicSkipStore[this.currentSlide].logic != undefined
        ) {
          this.selectedLogicStore = this.logicSkipStore[this.currentSlide]
        }
        // console.log('fieldId',fieldId)
      }
      if (this.fieldInfo.picklistOptions === undefined) {
        this.pickFieldOptions = this.fieldInfo.picklistOptionsImage
      } else {
        this.pickFieldOptions = this.fieldInfo.picklistOptions
      }
    },
    editCustomHtmlModel() {
      this.$bus.$emit('showCustomHtmlModal', this.fieldInfo.html)
    },
    editDescriptionModel() {
      this.$bus.$emit('showDescriptionModal', this.fieldInfo.description)
    },
  },
  updated() {
    if (
      !this.isSlidePanel &&
      this.inputSettings.fieldHeader != this.fieldInfo.label
    ) {
      console.log('updated')
      this.fillFormValues()
      if (this.source === 'survey') {
        this.updatePickListOptions()
      }
    }
  },
  mounted() {
    if (this.source === 'survey') {
      this.updatePickListOptions()
    }
  },
  async created() {
    console.log('created')
    if (!this.isSlidePanel) this.fillFormValues()
  },
})
</script>
<style>
.option-card-header .v-spinner {
  position: absolute;
  right: 10px;
  top: 0;
}

.select-option {
  width: 100%;
  border-radius: 5px;
  border: 0px;
  background-color: #f3f8fb;
  padding: 15px 20px;
}

.bi-arrow-left {
  cursor: pointer;
}
</style>
