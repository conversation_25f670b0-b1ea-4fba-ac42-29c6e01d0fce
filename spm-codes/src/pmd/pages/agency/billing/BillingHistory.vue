<template>
  <div class="hl_billing--history">
    <div class="card">
      <div class="card-header" v-if="showHeading !== false">
        <h3>Billing History</h3>
      </div>
      <div class="card-body --no-padding">
        <!-- <div v-if="receipts.length > 0">
                <div class="reciept-item" v-for="receipt in receipts" :key="receipt.number">
                  <div>
                    <a :href="receipt.receipt_url" target="_blank">{{receipt.description || '____-____' }}</a>
                  </div>
                  <div class="reciept-details">
                    <div>{{getInvoiceDate(receipt.created)}}</div>
                    <div v-if="receipt.payment_method_details.card">{{ receipt.payment_method_details.card.brand }} •••• {{receipt.payment_method_details.card.last4}}</div>
                    <div v-else></div>
                    <div>${{receipt.amount/100 | formatNumber}}</div>
                  </div>
                </div>
              </div> -->
        <div class="table-wrap" v-if="invoices.length > 0">
          <table class="table">
            <tbody>
              <tr
                v-for="invoice in invoices"
                :key="invoice.number"
                :class="!invoice.paid ? 'unpaid-invoice' : ''"
              >
                <td>
                  <a
                    :href="invoice.hosted_invoice_url"
                    target="_blank"
                    class="invoice-number"
                    >{{ invoice.number }}</a
                  >
                  <div class="invoice-date">
                    {{ getInvoiceDate(invoice.date) }}
                  </div>
                </td>
                <td>
                  <div v-if="!invoice.paid">
                    <button
                      v-if="retrying === invoice.id"
                      class="btn btn-primary retry-btn retrying"
                      disabled
                    >
                      <moon-loader color="#ffffff" size="20px" />
                      <span class="retry-btn__text">Retrying...</span>
                    </button>
                    <button
                      v-else-if="failedInvoice === invoice.id"
                      class="btn btn-primary retry-btn failed"
                    >
                      <!-- <i class="far fa-times-circle"></i> -->
                      <i class="fas fa-exclamation-circle"></i>
                      <span class="retry-btn__text">Failed</span>
                    </button>
                    <button
                      v-else-if="successInvoice === invoice.id"
                      class="btn btn-primary retry-btn success"
                    >
                      <i class="far fa-check-circle"></i>
                      <span class="retry-btn__text">Successful</span>
                    </button>
                    <button
                      v-else
                      class="btn btn-primary retry-btn"
                      @click="retryInvoice(invoice.id)"
                    >
                      <i class="fas fa-sync-alt"></i>
                      <span class="retry-btn__text">Retry</span>
                    </button>
                  </div>
                  <div
                    v-else-if="
                      invoice.charge_data &&
                      invoice.charge_data.payment_method_details.card
                    "
                    class="invoice-card"
                  >
                    {{
                      invoice.charge_data.payment_method_details.card.brand
                    }}
                    ••••
                    {{ invoice.charge_data.payment_method_details.card.last4 }}
                  </div>
                  <div class="invoice-card" v-else>---- ----</div>
                </td>
                <td>
                  <div class="invoice-value">
                    ${{ (invoice.total / 100) | formatNumber }}
                  </div>
                  <!-- <div class="invoice-status">
                          {{invoice.status}}
                        </div> -->
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="no-history" v-else-if="loading">
          <moon-loader color="#37ca37" size="30px" />
        </div>

        <div v-else class="no-history">
          {{ errorMsg || 'No data available. ' }}
          <contact-support
            v-if="
              errorMsg === 'Your stripe configuration is missing !! Please '
            "
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import { Company } from '@/models'
import * as moment from 'moment-timezone'
import ContactSupport from '@/pmd/components/common/ContactSupport.vue'
// import { UxMessage } from '@/util/ux_message'
import { trackGaEvent } from '@/util/helper'

export default Vue.extend({
  props: ['showHeading', 'source'], // 'inactive-screen'
  components: { ContactSupport },
  // inject: ['uxmessage'],
  data() {
    return {
      loading: false,
      invoices: [] as { [key: string]: any }[],
      receipts: [] as { [key: string]: any }[],
      company: Company,
      errorMsg: '',
      retrying: '',
      failedInvoice: '',
      successInvoice: '',
    }
  },
  created() {
    // this.fetchCompanyDetails();
    this.fetchInvoices()
  },
  methods: {
    async fetchInvoices() {
      this.loading = true
      await this.fetchCompanyDetails()
      if (this.company.stripe_id) {
        try {
          let charges = await this.$http.get('/api/stripe/charges')
          this.receipts = charges.data
          // this.receipts = charges ? charges.data.filter( receipt => receipt.paid === true) : [];
          let resp = await this.$http.get('/api/stripe/invoices')
          // console.log(resp.data);
          this.invoices = lodash.filter(resp.data.invoices, invoice => {
            const invoiceOld = moment().diff(
              moment.unix(invoice.date),
              'months'
            )
            if (
              this.source &&
              this.source === 'inactive-screen' &&
              (invoice.paid || invoiceOld >= 3)
            ) {
              return false
            }
            return invoice.status !== 'voided' && invoice.status !== 'void'
          })
          // mapping charges with invoices
          this.invoices.map(invoice => {
            let chargeIndex = this.receipts.findIndex(
              charge => charge.id === invoice.charge
            )
            if (chargeIndex !== -1) {
              invoice['charge_data'] = this.receipts[chargeIndex]
            }
            return invoice
          })
          this.$emit('fetchedList', this.invoices)
          // console.log(this.invoices);
          // this.orders = resp.data.orders;
          this.loading = false
        } catch (e) {
          this.loading = false
        }
      } else {
        this.errorMsg = 'Your stripe configuration is missing !! Please '
      }
      this.loading = false
    },
    async fetchCompanyDetails() {
      let company = await this.$store.dispatch('company/get')
      this.company = company.data
    },
    getInvoiceDate(date: number) {
      return moment.unix(date).format('MMM DD, YYYY')
    },
    async retryInvoice(invoiceId: string) {
      this.retrying = invoiceId
      let invoiceIndex = this.invoices.findIndex(
        invoice => invoice.id === invoiceId
      )
      let invoiceDetail = ''
      let invoiceValue = 0
      if (invoiceIndex !== -1) {
        let invoice = this.invoices[invoiceIndex]
        invoiceValue = invoice.total / 100
        invoiceDetail = `${invoice.number}: $${invoiceValue}`
      }
      try {
        trackGaEvent(
          'BillingInvoiceRetry',
          this.company.id,
          `[RE-TRYING] ${invoiceDetail}`,
          invoiceValue
        )
        let resp = await this.$http.get(
          `/stripe/retry_invoice?invoice_id=${invoiceId}`
        )
        console.log(resp.data)
        if (resp.data.error) {
          console.log(resp.data.error.message)
          // this.uxmessage(UxMessage.errorType(resp.data.error.message));
          this.failedInvoice = invoiceId
          setTimeout(() => {
            this.failedInvoice = ''
          }, 3000)
          trackGaEvent(
            'BillingInvoiceRetry',
            this.company.id,
            `[FAILED] ${invoiceDetail}`,
            invoiceValue
          )
          // this.$uxMessage('error',resp.data.error.message)
        } else {
          this.successInvoice = invoiceId
          setTimeout(() => {
            // TODO: Refetch list of invoices.
            let invoiceIndex = this.invoices.findIndex(
              invoice => invoice.id === invoiceId
            )
            if (invoiceIndex !== -1) {
              this.invoices[invoiceIndex] = {
                ...resp.data.invoice,
                charge_data: resp.data.charge,
              }
            }
            console.log(this.invoices)
            this.successInvoice = ''
            this.$emit('paid', invoiceId, invoiceDetail, invoiceValue)
          }, 3000)
          trackGaEvent(
            'BillingInvoiceRetry',
            this.company.id,
            `[SUCCESS] ${invoiceDetail}`,
            invoiceValue
          )
        }
      } catch (err) {
        console.log(err)
        //
      }
      // retry on server
      this.retrying = ''
    },
  },
})
</script>
<style scoped>
.no-history {
  text-align: center;
  padding: 30px;
}
.reciept-item {
  padding: 15px 30px;
}
.reciept-item:nth-child(odd) {
  background: rgba(242, 247, 250, 0.4);
}
.reciept-details {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.retry-btn {
  border: 1px solid #188bf6;
  padding: 8px;
  width: 120px;
  margin: auto;

  display: flex;
  align-items: center;
  justify-content: center;
}
.retry-btn:hover {
  background-color: #188bf6 !important;
  color: #ffffff !important;
}
.retry-btn.retrying {
  background-color: #e4aa06 !important;
  color: #ffffff !important;
  border: 1px solid #e4aa06 !important;
}
.retry-btn.failed {
  background-color: #e93d3d !important;
  color: #ffffff !important;
  border: 1px solid #e93d3d !important;
}
.retry-btn.success {
  background-color: #37ca37 !important;
  color: #ffffff !important;
  border: 1px solid #37ca37 !important;
}
.retry-btn__text {
  margin-left: 8px;
}
.invoice-card {
  text-align: center;
}
.unpaid-invoice,
.unpaid-invoice .invoice-number,
.unpaid-invoice .invoice-value {
  color: #e93d3d !important;
}
</style>
