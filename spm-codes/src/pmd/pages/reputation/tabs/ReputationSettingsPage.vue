<template>
  <div class="hl_reputation_settings" v-if="hasPermission('reviews') && this.location">
    <section>
      <ReviewLinkComponent />
    </section>
    <section>
      <ReviewRequestBehaviourComponent
        :location="location"
        @locationUpdated="fetchData"
        :key="this.location.id"
      />
    </section>
     <section>
      <ReviewSMSComponent
        :location="location"
        :review_request_behaviour="requestBehavior"
        :key="this.location.id"
      />
    </section> 
    <section>
      <ReviewEmailComponent
        :location="location"
        :review_request_behaviour="requestBehavior"
         :key="this.location.id"
      />
    </section>
    <section>
      <ReviewWidgetComponent 
         :key="this.location.id"
       />
    </section>
  </div>
</template>

<script lang="ts">
// @ts-nocheck
import Vue from 'vue'
import { Location, User } from '@/models'
import { UserState } from '../../../store/state_models'
import { mapState } from 'vuex'

import ReviewLinkComponent from './../../../components/reputation/ReviewLinkComponent.vue'
import ReviewSMSComponent from './../../../components/reputation/ReviewSMSComponent.vue'
import ReviewEmailComponent from './../../../components/reputation/ReviewEmailComponent.vue'
import ReviewWidgetComponent from './../../../components/reputation/ReviewWidgetComponent.vue'
import ReviewRequestBehaviourComponent from './../../../components/reputation/settings/ReviewRequestBehaviourComponent.vue'

export default Vue.extend({
  data() {
    return {
      googlePlacesId: null,
      currentLocationId: '',
      location: undefined as undefined | Location,
      review_request_behaviour: '',
    }
  },
  components: {
    ReviewLinkComponent,
    ReviewSMSComponent,
    ReviewEmailComponent,
    ReviewWidgetComponent,
    ReviewRequestBehaviourComponent,
  },
  props: {},
  watch: {
    '$route.params.location_id': function (id) {
      this.currentLocationId = id
      this.review_request_behaviour = '',
      this.fetchData()
    },
    location: function () {
      if (
        location.settings &&
        location.settings.review &&
        location.settings.review.review_request_behaviour
      ) {
        this.review_request_behaviour =
          location.settings.review.review_request_behaviour
      }
    },
  },

  methods: {
    hasPermission(option: string): boolean {
      let show = false
      if (!this.user) return show
      switch (option) {
        case 'reviews': {
          show =
            this.userHasPermission('reviews_enabled') &&
            this.locationHasPermission('reviews_enabled')
          break
        }
      }
      return show
    },

    locationHasPermission(key: string): boolean {
      if (!this.location) return false
      if (!this.location.permissions) {
        return true
      }

      const permissions = this.location.permissions
      return permissions[key] === false ? false : true
    },
    userHasPermission(key: string): boolean {
      const globalPermission = this.user.permissions
      if (globalPermission[key] === false) {
        return false
      }
      return true
    },

    async fetchData(behaviour = null) {
      this.location = new Location(
        await this.$store.dispatch(
          'locations/getCurrentLocation',
          this.currentLocationId
        )
      )
      if (behaviour) {
        this.review_request_behaviour = behaviour
      } else {
        if(this.location.settings.review && this.location.settings.review.review_request_behaviour){
          this.review_request_behaviour = this.location.settings.review.review_request_behaviour
        }else{
          this.review_request_behaviour = 'immediate';
        }
      }
    },
  },
  computed: {
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      },
    }),
    requestBehavior() {
      if (this.review_request_behaviour) {
        return this.review_request_behaviour
      }

      return this.location.settings &&
        this.location.settings.review &&
        this.location.settings.review.review_request_behaviour
        ? location.settings.review.review_request_behaviour
        : 'immediate'
    },
  },
  async created() {
    this.currentLocationId = this.$router.currentRoute.params.location_id
    this.fetchData()
  },
  mounted() {},
  updated() {},
})
</script>
<style lang="scss" scoped>
.hl_reputation_settings {
}
</style>
