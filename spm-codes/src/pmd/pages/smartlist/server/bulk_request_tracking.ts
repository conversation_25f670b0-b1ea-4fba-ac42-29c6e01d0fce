import moment from 'moment'
import { ScheduledBulkActions } from './bulkreq_api'
import { BulkRequestV2 } from './bulk_req_v2'

export class BulkRequestTracking {

  private trackingIds = []
  private trackingEnabled: boolean
  private timerRunning: boolean
  private static trackOperations = ['bulk-tag-add','bulk-tag-remove', 'bulk-contact-delete', 'bulk-import']
  private timers = []

  public constructor (
    public locationId: string,
    public userId: string,
    protected callback: () => {}
  ) {
    if (!locationId) throw new Error ('BulkRequestTracking needs location')
    if (!callback) throw new Error ('BulkRequestTracking needs observer')
    console.log (`BRTracking constructor - ${this.locationId} - ${this.userId}`)
  }

  public start(skipCompleted: boolean = true) {
    this.trackingEnabled = true
    setTimeout(() => this.startTimer(skipCompleted), 2000)
  }

  public stop() {
    this.timers?.forEach(a=> clearTimeout(a))
    this.timers = []
    this.trackingEnabled = false
    this.callback = null
    this.trackingIds = []
  }

  private async onTimerExpiry(timerName?: string){

    try {
      console.log(`BRTracking - ${timerName} | Timers Len - ${this.timers?.length} `)
      if (!this.trackingEnabled) return
      if (!this.callback) return
      await this.callback()
     } catch(err){
       console.log(err)
     } finally {
       this.timerRunning = false
       if(this.callback) {
         this.startTimer(true) //next check
       }
     }
  }

  private async startTimer(skipCompleted: boolean = true) {
    if (!this.callback) return
    if (!this.trackingEnabled) return
    if (this.timerRunning === true) return

    const items = await this.getTrackableItems(skipCompleted)
    let forceCallback = false
    items.forEach((br:BulkRequestV2) => {
      // console.log(`BRTracking -> Evaluate Item - ${br.id} - ${br.currentStatus}`)
        if (br.currentStatus === 'complete'
            || br.currentStatus === 'cancelled'
            || br.currentStatus === 'error') {
          //console.log(`skipCompleted - ${skipCompleted}`)
          if (!skipCompleted) forceCallback = true
          if (this.trackingIds.includes(br.id)) {
            forceCallback = true // for last check on completed bulk action
            this.removeTrackingId(br.id)
          }
          // console.log(`BRTracking -> NON Trackable Item - ${br.id}`)
        } else {
          // console.log(`BRTracking -> Trackable Item - ${br.id}`)
          if (moment().diff(br.dateAdded) < (60*60*1000)){
            this.addTrackingId(br.id)
          }
        }
     })

    console.log(`BRTracking - Work in progress - ${this.trackingIds.length} ${this.trackingIds.join(', ')} OR Force Check ${forceCallback}`)
    if (this.trackingIds.length)  {
      this.timerRunning = true
      this.timers?.forEach(a=> clearTimeout(a))
      this.timers = []
      this.timers.push(setTimeout(() => this.onTimerExpiry('Active Item Timer'), 3000))
    } else if (forceCallback){
      this.timerRunning = true
      this.timers?.forEach(a=> clearTimeout(a))
      this.timers = []
      this.timers.push(setTimeout(() => this.onTimerExpiry('1st Force Timer'), 2000))
      this.timers.push(setTimeout(() => this.onTimerExpiry('2nd Force Timer'), 8000))
      this.timers.push(setTimeout(() => this.onTimerExpiry('3nd Force Timer'), 15000))
      this.timers.push(setTimeout(() => this.onTimerExpiry('4th Force Timer'), 25000))
      this.timers.push(setTimeout(() => this.onTimerExpiry('5th Force Timer'), 40000))
      this.timers.push(setTimeout(() => this.onTimerExpiry('6th Force Timer'), 60000))
    }
  }

  private async getTrackableItems(skipCompleted: boolean = true){
    const list = []
    const items = await ScheduledBulkActions.getRecentItems({locationId: this.locationId, limit: 10, userId: this.userId})
    items.forEach(br=> {
      if (!br) return
      if (this.userId && br.byUserId !== this.userId) return
      if (this.locationId && br.locationId !== this.locationId) return
      if (BulkRequestTracking.trackOperations.includes(br.operationType)) {
        list.push(br)
      }
    })
    return list
  }

  private addTrackingId(id: string) {
    if (!this.trackingIds.includes(id)) {
      this.trackingIds.push(id)
    }
  }

  private removeTrackingId(id: string) {
    if (this.trackingIds.includes(id)) {
      const idx = this.trackingIds.findIndex(a=> a === id)
      if (idx !== -1) this.trackingIds.splice(idx,1)
    }
  }
}
