import moment from 'moment-timezone'

const phoneCall = {
  namespaced: true,
  state: {
    callDirection: 'inbound',
    callLog_locationId: '',
    dashboard_locationId: '',
    callLog_callType: 'all',
    dashboard_callType: 'all',
    callLog_dateRange: {
      start: moment()
        .subtract(30, 'days')
        .startOf('day')
        .toDate(),
      end: moment()
        .endOf('day')
        .toDate()
    },
    dashboard_dateRange: {
      start: moment()
        .subtract(30, 'days')
        .startOf('day')
        .toDate(),
      end: moment()
        .endOf('day')
        .toDate()
    },
    stats_filters: {
      keyword: '',
      source_type: [],
      duration: '',
      firstTime: false,
      qualified_lead: false,
      direction: '',
      call_status: [],
      device_type: [],
      campaign: '',
      referrer: '',
      landing: '',
      marketingCampaign: []
    }
  },
  mutations: {
    updateCallDirection(state, value) {
      state.callDirection = value
    },
    updateCallLogCallType(state, value) {
      state.callLog_callType = value
    },
    updateDashboardCallType(state, value) {
      state.dashboard_callType = value
    },
    updateCallLogDateRange(state, value) {
      state.callLog_dateRange = value
    },
    updateDashboardDateRange(state, value) {
      state.dashboard_dateRange = value
    },
    updateCallLogLocationId(state, value) {
      state.callLog_locationId = value
      state.callLog_callType = 'all'
      state.dashboard_callType = 'all'
    },
    updateDashboardLocationId(state, value) {
      state.dashboard_locationId = value
      state.callLog_callType = 'all'
      state.dashboard_callType = 'all'
      state.stats_filters = {
        keyword: '',
        source_type: [],
        duration: '',
        firstTime: false,
        qualified_lead: false,
        direction: '',
        call_status: [],
        device_type: [],
        campaign: '',
        referrer: '',
        landing: '',
        marketingCampaign: []
      }
    },
    updateStatsFilters(state, value) {
      state.stats_filters = { ...state.stats_filters, ...value }
    }
  },
  actions: {}
}

export default phoneCall
