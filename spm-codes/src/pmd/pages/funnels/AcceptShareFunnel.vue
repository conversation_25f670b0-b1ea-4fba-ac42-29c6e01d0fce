<template>
  <div>
    <SideBarV2 v-if="isV2SideBar && agencyUser" />
    <SideBar v-else-if="agencyUser" />
    <TopBar v-if="agencyUser" />

    <section :class="agencyUser ? 'hl_wrapper' : ''">
      <section v-if="funnelCloned" class="hl_wrapper--inner">
        <div style="text-align: center" class="container-fluid">
          <h4>{{ entityName }} Cloned! Switch to an account where it was cloned and find it under {{ entityName }}s tab.</h4>
        </div>
      </section>
      <section v-else class="hl_wrapper--inner">
        <div v-if="fetchingShareDetails" class="container-fluid">
          <moon-loader :loading="fetchingShareDetails" color="#188bf6" size="30px" />
        </div>
        <div
          v-else-if="!fetchingShareDetails && !errorInFetchingShareDetails"
          style="text-align: center"
          class="container-fluid"
        >
          <h4 style="margin-bottom: 18px;">Copy of {{ funnelName }}</h4>
          <div v-if="settingEnabled">
            <button
              @click="showCloneFunnelModal()"
              type="button"
              class="btn btn-primary btn-sm"
              style="margin-right: 24px;"
            >Add {{ entityName }}</button>
            <button @click="cancelShareFunnel()" type="button" class="btn btn-light btn-sm">Cancel</button>
          </div>
          <div v-else>
            <h6>Permissions not enabled!</h6>
            <button
              @click="cancelShareFunnel()"
              type="button"
              class="btn btn-primary btn-sm"
            >Go to Dashboard</button>
          </div>
        </div>
        <div v-if="errorInFetchingShareDetails" class="container-fluid">
          <h4>There seems to be an error while getting {{ entityName }} details. Please check the url or try again!</h4>
        </div>
      </section>
    </section>
    <CloneFunnelToLocation
      :show-modal="cloneFunnelModalShown"
      :funnel-id="funnelId"
      :funnel-name="funnelName"
      :entity-name="entityName"
      @hide="hideCloneFunnelModal"
      @cloneSuccessful="cloneSuccessful"
    />
  </div>
</template>
<script lang="ts">
import Vue from 'vue'
import axios from 'axios'
import TopBar from '../../components/TopBar.vue'
import SideBar from '../../components/agency/SideBar.vue'
import SideBarV2 from '../../components/sidebar/SideBar.vue'
import CloneFunnelToLocation from '../../components/funnels/CloneFunnelToLocation.vue'
import Location from '../../../models/location'
import { FunnelType } from '../../../models/funnel'
import store from '@/store'
import { User } from '@/models'

export default Vue.extend({
  components: {
    TopBar,
    SideBar,
    CloneFunnelToLocation,
    SideBarV2
  },
  computed: {
    entityName(): string {
      switch (this.funnelType) {
        case FunnelType.Website: {
          return 'Website'
        }
        case FunnelType.Funnel: {
          return 'Funnel'
        }
        default: {
          return 'Funnel'
        }
      }
    },
    isV2SideBar() {
      return this.$store.getters['sidebarv2/getVersion'] === 'v2'
    },
  },
  data() {
    return {
      allLocations: [],
      fetchingShareDetails: false,
      funnelName: '',
      funnelId: '',
      funnelType: '',
      cloneFunnelModalShown: false,
      funnelCloned: false,
      errorInFetchingShareDetails: false,
      agencyUser: false,
      settingEnabled: false,
      loading: {
        locations: false
      }
    }
  },
  async mounted() {
    const shareId = this.$router.currentRoute.params.share_id

    if (!shareId) {
      console.log('invalid share id --> ', shareId)
      return
    }

    try {
      this.errorInFetchingShareDetails = false
      this.fetchingShareDetails = true
      const user = new User(await store.dispatch('user/get'))
      this.agencyUser = user.type === User.TYPE_AGENCY

      const response = await axios.get(
        `/funnelbuilder/get-share-details/${shareId}`
      )
      const {
        data: { funnelId, funnelName, funnelType }
      } = response
      this.funnelId = funnelId
      this.funnelName = funnelName
      this.funnelType = funnelType

      this.settingEnabled = this.checkSettingEnabled(user.permissions)
    } catch (error) {
      console.error('Error while getting share details --> ', error)
      this.errorInFetchingShareDetails = true
    } finally {
      this.fetchingShareDetails = false
    }
  },
  methods: {
    checkSettingEnabled(userPermissions: any): boolean {
      switch (this.funnelType) {
        case FunnelType.Website: {
          return userPermissions.websites_enabled
        }
        case FunnelType.Funnel: {
          return userPermissions.funnels_enabled
        }
        default: {
          return false
        }
      }
    },
    showCloneFunnelModal() {
      this.cloneFunnelModalShown = true
    },
    hideCloneFunnelModal() {
      this.cloneFunnelModalShown = false
    },
    cloneSuccessful(cloneData: any) {
      const { locationIds } = cloneData

      const locationWhereFunnelCloned = locationIds[0]

      this.redirectToListingPage(locationWhereFunnelCloned)

      this.funnelCloned = true
      this.hideCloneFunnelModal()
    },
    redirectToListingPage(locationId: string) {
      let routeName = ''

      switch (this.funnelType) {
        case FunnelType.Funnel: {
          routeName = 'all_funnels'
          break
        }
        case FunnelType.Website: {
          routeName = 'websites_funnels'
          break
        }
        default: {
          routeName = 'all_funnels'
        }
      }

      this.$router.push({
        name: routeName,
        params: { location_id: locationId }
      })
    },
    cancelShareFunnel() {
      this.$router.push({ name: 'dashboard', replace: true })
    }
  }
})
</script>
<style scoped>
.container-fluid {
  text-align: center;
}
</style>
