<template>
	<div
		class="modal fade hl_add-opportunities--modal"
		id="add-opportunities-modal"
		tabindex="-1"
		role="dialog"
		ref="modal"
	>
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<div class="modal-header--inner">
						<h2 class="modal-title" v-if="isAdd">
							<i class="icon icon-plus" style="font-size: 0.75em;"></i> Add pipeline
						</h2>
						<h2 class="modal-title" v-else>
							<i class="icon icon-edit" style="font-size: 0.75em;"></i> Edit pipeline
						</h2>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</div>
				<div class="modal-body">
					<div class="modal-body--inner">
						<div class="row">
							<div class="col-12">
								<div class="form-group">
									<UITextInputGroup
										lable="Pipeline name"
										type="text"
										class="msgsndr2"
										placeholder="Name"
										v-model="name"
										v-validate="'required'"
										name="msgsndr2"
										data-lpignore="true"
										autocomplete="msgsndr2"
										:error="errors.has('msgsndr2')"
										:errorMsg="'The name field is required'"
									/>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-10">
								<UITextLabel>Stage name</UITextLabel>
							</div>
							<div class="col-2" style="text-align:center">
								<UITextLabel>Actions</UITextLabel>
							</div>
						</div>

						<div class="row stage-holder" v-for="(stage, index) in stages" :key="stage.id">
							<div class="col-10">
								<div class="arrow-holder">
									<i
										class="fa fa-chevron-circle-up"
										v-if="index>0"
										@click.prevent="moveUp(stage)"
									/>
									<i
										class="fa fa-chevron-circle-down"
										v-if="index<stages.length - 1"
										@click.prevent="moveDown(stage)"
									/>
								</div>
								<div class="name-holder">
									<UITextInputGroup
										placeholder="Stage Name"
										v-model="stage.name"
										v-validate="'required'"
										:name="stage.id"
										:error="errors.has(stage.id)"
										:errorMsg="'The Stage name is required'"
									/>
								</div>
							</div>
							<div class="col-2 stage-actions">
								<i class="fa fa-chart-pie" 
									:style="`color: ${stage.showInPieChart===false?'#afb8bc':'#37ca37'}`"
									@click.prevent="toggleShowIn(stage, 'PieChart')"
									:title="`${stage.showInPieChart===false?'Show in':'Hide from'} pie chart (on dashboard)`"/>
								<i class="fa fa-filter" 
									:style="`color: ${stage.showInFunnel===false?'#afb8bc':'#37ca37'}`"
									@click.prevent="toggleShowIn(stage, 'Funnel')"
									:title="`${stage.showInFunnel===false?'Show in':'Hide from'} funnel chart (on dashboard)`"/>
								<i class="fa fa-trash-alt" style="color: #e93d3d" @click.prevent="removeStage(stage)"/>
							</div>
						</div>

						<div class="row" style="margin-top: 15px;">
							<div class="col-12">
								<a @click.prevent="addStage" href="#">
									<i class="icon icon-plus" style="font-size: 0.75em;margin-right: 10px;"></i>Add stage
								</a>
							</div>
						</div>

						<div class="row" style="margin-top: 30px;">
							<div class="col-6">
								<div class="form-group flex-toggle">
									<UITextLabel>Visible in Funnel chart</UITextLabel>
									<div class="toggle">
										<UIToggle
											class="tgl tgl-light"
											id="tgl-show-in-funnel"
											checked
											v-model="showInFunnel"
										/>
										<!-- <label class="tgl-btn" for="tgl-show-in-funnel"></label> -->
									</div>
								</div>
							</div>
							<div class="col-6">
								<div class="form-group flex-toggle">
									<UITextLabel>Visible in Pie chart</UITextLabel>
									<div class="toggle">
										<UIToggle
											class="tgl tgl-light"
											id="tgl-show-in-pie-chart"
											checked
											v-model="showInPieChart"
										/>
										<!-- <label class="tgl-btn" for="tgl-show-in-pie-chart"></label> -->
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<div class="modal-footer--inner nav">
						<UIButton
							:class="{invisible: saving}"
							use="outline"
							data-dismiss="modal"
						>Cancel</UIButton>
						<div style="display: inline-block;position: relative;">
							<UIButton
								:class="{invisible: saving}"
								use="primary"
								@click.prevent="save"
								:loading="saving"
							>Save</UIButton>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from 'vue';
import { Pipeline, Stage, Opportunity } from '@/models';
import { v4 as uuid } from 'uuid';
import { getPipelines } from '../../../util/opportunity_user';

declare var $: any;

export default Vue.extend({
	props: ['values'],
	data() {
		return {
			currentLocationId: "",
			saving: false,
			isAdd: false,
			name: '',
			showInFunnel: true,
			showInPieChart: true,
			pipeline: new Pipeline(),
			stages: [{
				id: uuid(),
				name: '',
				position: -1,
				showInFunnel: true,
				showInPieChart: true
			}] as Stage[],
			deletedStages: [] as Stage[]
		}
	},
	methods: {
		moveUp(stage: Stage) {
			const position = this.stages.indexOf(stage);
			let newPosition = position - 1;
			this.stages.splice(newPosition, 0, this.stages.splice(position, 1)[0]);
		},
		moveDown(stage: Stage) {
			const position = this.stages.indexOf(stage);
			let newPosition = position + 1;
			this.stages.splice(newPosition, 0, this.stages.splice(position, 1)[0]);
		},
		removeStage(stage: Stage) {
			if (this.stages.length <= 1) {
				alert("You need atleast 1 stage in the pipeline");
				return;
			}
			const firstStage = this.stages[0];
			if (confirm('All opportunities in ' + (stage.name || 'this stage') + ' will be moved to ' + (firstStage.name || 'the first stage') + '. Are you sure you want to delete this stage?')) {
				this.deletedStages.push(this.stages.splice(this.stages.indexOf(stage), 1)[0]);
			}
		},
		toggleShowIn(stage: Stage, type: 'Funnel' | 'PieChart') {
			const property = `showIn${type}`
			const position = this.stages.indexOf(stage);
			stage[property] = (stage[property] === false)?true:false;
			this.stages.splice(position,1,stage)
		},
		addStage() {
			this.stages.push({
				id: uuid(),
				name: '',
				position: -1,
				showInFunnel: true,
				showInPieChart: true
			});
		},
		async save() {
			const result = await this.$validator.validateAll();
			if (!result) {
				return false;
			}
			this.saving = true;

			const firstStage = this.stages[0];
			await Promise.all(this.deletedStages.map(async deletedStage => {
				Opportunity.migrateStageId(this.currentLocationId, deletedStage.id, firstStage.id);
				this.$http.get(`/pipeline/remove_stage?pipeline_stage_id=${deletedStage.id}&location_id=${this.currentLocationId}`) // run async
			}));
			this.pipeline.name = this.name;
			this.stages.forEach((stage, index) => {
				stage.position = index;
			});
			this.pipeline.stages = this.stages;
			this.pipeline.showInFunnel = this.showInFunnel;
			this.pipeline.showInPieChart = this.showInPieChart;
      		await this.pipeline.save();
			this.$emit('update');
			this.$emit('hidden');
		}
	},
	beforeDestroy() {
		$(this.$refs.modal).off('hidden.bs.modal');
	},
	watch: {
		values(values: { [key: string]: any }) {
			const data: (() => object) = <(() => object)>this.$options.data;
			if (data) Object.assign(this.$data, data.apply(this));
			if (values.visible) $(this.$refs.modal).modal('show');
			else $(this.$refs.modal).modal('hide');
			this.currentLocationId = values.currentLocationId;
			if (values.pipeline) {
				this.isAdd = false;
				this.pipeline = values.pipeline;
				this.name = this.pipeline.name;
				this.showInFunnel = (this.pipeline.showInFunnel === false)?false:true;
				this.showInPieChart = (this.pipeline.showInPieChart === false)?false:true;
				this.stages = lodash.sortBy(this.pipeline.stages, ['position']);;
			} else {
				this.isAdd = true;
				this.pipeline = new Pipeline();
				this.pipeline.locationId = values.currentLocationId;
			}
		}
	},
	updated() {
		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker('refresh');
		}

		if (this.values && this.values.visible) {
			$(this.$refs.modal).modal('show');
		}
	},
	mounted() {
		const _self = this;
		$(this.$refs.modal).on('hidden.bs.modal', function () {
			_self.$emit('hidden');
		});

		const $selectpicker = $(this.$el).find('.selectpicker');
		if ($selectpicker) {
			$selectpicker.selectpicker();
		}

		if (this.values && this.values.visible) {
			$(this.$refs.modal).modal('show');
		}
	},
})
</script>

<style scoped>
.stage-actions{
	display: flex;
	justify-content: space-between;
}
.stage-holder .fa-chart-pie {
    margin: 15px 10px;
    cursor: pointer;
}
.flex-toggle{
	display: flex;
}
.flex-toggle .toggle{
	margin-left: 20px;
}
</style>
