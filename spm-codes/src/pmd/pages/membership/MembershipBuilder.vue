<template>
  <div
    id="membershipBuilder"
    ref="membershipBuilder"
    :style="getStyle"
  ></div>
</template>
<script lang="ts">
import Vue from 'vue'
import PostMate from 'postmate'
import config from '@/config'
import { User } from '@/models'
import { EventBus } from '@/models/event-bus'
import firebase from 'firebase/app'
import { AuthUser, Location } from '../../../models'

export const setElementDisplay = function (
  element: HTMLElement | null,
  displayValue: string
) {
  try {
    if (element) {
      if (element.hasAttribute('data-email-builder-display')) {
        element.removeAttribute('data-email-builder-display')
        return
      }
      const computedStyle = window.getComputedStyle(element)
      if (
        element.style.display === displayValue ||
        computedStyle.display === displayValue
      )
        element.setAttribute('data-email-builder-display', displayValue)
      else element.style.display = displayValue
    }
  } catch (err) {
    console.warn(err, '')
  }
}

export default Vue.extend({
  props: {
    location: {
      type: Object,
      required: true,
    },
    user: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      childApp: undefined as any,
      previousSideBarState: undefined as boolean | undefined
    }
  },
  computed: {
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    getStyle() {
      return (this.getSideBarVersion == 'v2') ? 'height: calc(100vh - 90px); width: 100%; border: none;' : 'height: 100vh; width: 100%; border: none; padding-left: 70px; padding-top: 80px'
    },
    getSideBarVersion(): string {
			return this.$store.getters['sidebarv2/getVersion']
		},
  },
  async mounted() {
    EventBus.$on('change-membership-route', this.changeMembershipRoute)
    await this.loadMicroApp()
    this.previousSideBarState = this.$store.getters.getManualCollapseSidebar
    if (this.getSideBarVersion !== 'v2') this.$store.commit('setManualCollapseSidebar', true)
    this.toggelCustomJsVisibility('none')
  },
  beforeDestroy() {
    this.childApp.then((child: any) => {
      child.destroy()
    })
    if (this.getSideBarVersion !== 'v2') this.$store.commit('setManualCollapseSidebar', this.previousSideBarState)
    this.toggelCustomJsVisibility('inline')
    EventBus.$off('change-membership-route')
  },
  methods: {
    toggelCustomJsVisibility(value: string) {
      // agency custom js breaks membership builder drag and drop feature
      // hide that DOM temporarily when user opens membership builder

      for (let element of [
        'customJS',
        'freshworks-container',
        'beacon-container',
      ]) {
        const customJsEl: HTMLElement | null = document.getElementById(element)
        setElementDisplay(customJsEl, value)
      }

      //@ts-expect-error dom operation
      document.body.children.forEach(e => {
        const child = e.children
        if (e?.id?.includes('pendo')) {
          // Do nothing as this is from pendo
        } else if (
          window.getComputedStyle(e).position === 'fixed' ||
          (e.id !== 'app' &&
            child.length > 0 &&
            window.getComputedStyle(child[0]).position === 'fixed')
        ) {
          setElementDisplay(e, value)
        }
      })
    },
    async getTokenId(): Promise<string> {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      const tokenId = await firebase.auth().currentUser.getIdToken()
      return tokenId
    },
    async loadMicroApp() {
      try {
        const membershipUrl = config.membershipAppUrl
        let cookie = localStorage.getItem('a')
        if (cookie) {
          cookie = cookie.slice(1, -1)
        }
        const tokenId = await this.getTokenId()
        const location = new Location(
          await this.$store.dispatch('locations/getById', this.location.id)
        )
        const userId = firebase.auth().currentUser?.uid
        const userType = this.$store.state.user?.user?.type
        const userRole = this.$store.state.user?.user?.role
        console.log(`${membershipUrl}${this.$route.fullPath}`)
        const handshake = new PostMate({
          container: this.$refs.membershipBuilder as HTMLElement,
          url: `${membershipUrl}${this.$route.fullPath}`,
          name: 'membership-builder',
          model: {
            locationId: location.id,
            tokenId: tokenId,
            cookie: cookie,
            userId: userId,
            userType: userType,
            userRole: userRole,
            companyId: this.$store.state.company?.company?.id
          },
        })

        handshake
          .then((child: any) => {
            const membershipBuilderFrame = document.getElementsByName(
              'membership-builder'
            )[0]
            // @ts-ignore
            if (!membershipBuilderFrame.allowFullScreen) {
              // @ts-ignore
              membershipBuilderFrame.allowFullscreen = true
              // @ts-ignore
              membershipBuilderFrame.src = membershipBuilderFrame.src
            }
            console.log('Handshake with Child complete.')
            child.on('membership-route-change', (data: any) => {
              this.$router.push(data.path)
            })
            child.on('getRefreshToken', async () => {
              const tokenId = await this.getTokenId()
              child.call('setTokenId', tokenId)
            })
            child.on('membership-sync-offersData', async() => {
              this.$store.dispatch('membership/syncAll', { locationId: location.id, forceRefresh: true })
            })
          })
          .catch(async (err: Error) => {
            console.error({ handledError: err })
            const membershipBuilderRef = this.$refs
              .membershipBuilder as HTMLElement
            membershipBuilderRef.innerHTML = ''
            await this.loadMicroApp()
          })

        this.childApp = handshake
      } catch (e) {
        console.log(e)
      }
    },
    async changeMembershipRoute(pathName: string) {
      if(!this.childApp) return
      this.childApp.then((child: any) => {
        child.call('changeRoute', pathName)
      })
    }
  },
})
</script>
<style>
#membershipBuilder iframe {
  width: 100%;
  height: 100%;
  border: none;
}
.membership-builder {
  width: 100%;
  background: #ffffff;
}
</style>
