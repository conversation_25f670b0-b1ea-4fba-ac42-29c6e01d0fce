<template>
	<div class="modal fade hl_sms-template--modal" tabindex="-1" role="dialog" ref="modal">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<!-- Header - START -->
				<div class="modal-header">
					<div class="modal-header--inner">
						<h2 class="modal-title">Add Custom Field</h2>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				</div>
				<!-- Header - END -->

				<!-- Body - START -->
				<div class="modal-body pb-2">
					<div class="modal-body--inner">
						<p>
							<b>What type of field do you want to add?</b>
						</p>

						<!-- Field type selector - START -->
						<div class="row field-type-group mt-3">
							<div
								class="col-md-4"
								v-for="type in fieldTypes"
								@click="selectFieldTypeHandler(type.id)"
								v-if="!selectedFieldType"
							>
								<p>{{type.label}}</p>
							</div>

							<div class="col-md-4 selected" v-if="selectedFieldType">
								<p>{{fieldType}}</p>
							</div>
						</div>
						<!-- Field type selector - END -->

						<!-- Field input - START -->
						<div class="row mt-3 custom-field-inputs mt-3 mb-5" v-if="selectedFieldType">
							<div class="col-md-12">
								<div class="form-group">
									<UITextLabel class="mr-3">
										<span>Name of the field</span>
										<span
											style="margin-left: 5px;"
											class="input-group-addon"
											v-b-tooltip.hover
											:title="isEdit ? 'Changing the name will not affect the field key.' :
											'The field key will be created according to the name and cannot be changed later.'"
										>
										<i class="fas fa-question-circle"></i>
										</span>
									</UITextLabel>
									<UITextInputGroup
										type="text"
										v-model="customFieldData.field_name"
										v-validate="'required'"
										name="msgsndr2"
										data-lpignore="true"
										class="msgsndr2"
										autocomplete="msgsndr2"
										:error="errors.has('msgsndr2')"
										:errorMsg="'Field name is required.'"
									/>
								</div>
								<div class="form-group" v-if="selectedFieldType !== 'TEXTBOX_LIST' && selectedFieldType !== 'SIGNATURE'">
									<UITextInputGroup
										label="Placeholder"
										type="text"
										v-model="customFieldData.placeholder"
										name="placeholder"
									/>
								</div>
								<div class="form-group">
									<label class="mr-3">Type</label>
									<select class="selectpicker" name="model" v-model="customFieldData.model">
										<option value="contact">Contact</option>
										<!-- <option value="opportunity">Opportunity</option> -->
									</select>
								</div>
								<div class="form-group" v-if="showOptionsInput">
									<draggable v-model="listOptions" @end="onEnd" draggable=".hl-drag-options" handle=".handle">
										<div class="row mb-3 hl-drag-options items-center" v-for="(item,index) in listOptions" :key="'option-'+index">
											<div class="col-1 d-flex justify-content-center">
											<a class="handle">
												<i class="fa fa-align-justify"></i>
											</a>
											</div>
											<div class="col-10 px-0">
											<UITextInputGroup
												type="text"
												:placeholder="item.placeholder"
												v-model="item.text"
												v-validate="'required'"
												:name="'list_options_'+index"
												:error="errors.has('list_options_'+index)"
												:errorMsg="'Blank options are not allowed'"
											/>
											</div>
											<div class="col-1 d-flex justify-content-center">
												<i class="fa fa-trash-alt" style="color:#e93d3d;" v-if="listOptions.length > 1" @click.prevent="deleteOption(index)"/>
												<i class="fa fa-trash-alt" style="color:#6c757d;" v-else />
											</div>

										</div>
									</draggable>
									<a @click.prevent="cloneOption" href="#">
										<i class="icon icon-plus" style="font-size: 0.75em;margin-right: 10px;"></i>Add Option
									</a>
								</div>



				<!--For File upload-->
				<div class="form-group" v-if="selectedFieldType == 'FILE_UPLOAD'">
				<label class="mr-3">Acceptable formats</label>
				<div class="d-flex justify-content-between">
					<div class="flex">

						<input type="checkbox" class="focus:ring-curious-blue-500 h-5 w-5 text-curious-blue-600 border-gray-300 rounded mr-2 disabled:opacity-50" id="pdf" value=".pdf" v-model="checkedFileTypes"/>
						<label class="form-check-label" for="pdf">PDF</label>
					</div>
					<div class="flex">
						<input type="checkbox" class="focus:ring-curious-blue-500 h-5 w-5 text-curious-blue-600 border-gray-300 rounded mr-2 disabled:opacity-50" id="docx" value=".docx" v-model="checkedFileTypes"/>
						<label class="form-check-label" for="docx">DOC/DOCX</label>
					</div>
					<div class="flex">
						<input type="checkbox" class="focus:ring-curious-blue-500 h-5 w-5 text-curious-blue-600 border-gray-300 rounded mr-2 disabled:opacity-50" id="jpeg" value=".jpeg" v-model="checkedFileTypes"/>
						<label class="form-check-label" for="jpeg">JPG/JPEG</label>
					</div>
					<div class="flex">
						<input type="checkbox" class="focus:ring-curious-blue-500 h-5 w-5 text-curious-blue-600 border-gray-300 rounded mr-2 disabled:opacity-50" id="png" value=".png" v-model="checkedFileTypes"/>
						<label class="form-check-label" for="png">PNG</label>
					</div>
					<div class="flex">
						<input type="checkbox" class="focus:ring-curious-blue-500 h-5 w-5 text-curious-blue-600 border-gray-300 rounded mr-2 disabled:opacity-50" id="gif" value=".gif" v-model="checkedFileTypes"/>
						<label class="form-check-label" for="gif">GIF</label>
					</div>


					<div class="flex">

                    <input
                      type="checkbox"
                      class="focus:ring-curious-blue-500 h-5 w-5 text-curious-blue-600 border-gray-300 rounded mr-2 disabled:opacity-50"
                     id="csv" value=".csv"  v-model="checkedFileTypes"
                    />

						<label class="form-check-label" for="csv">CSV/XLSX</label>
					</div>

					<div class="flex">

                    <input
                      type="checkbox"
                      class="focus:ring-curious-blue-500 h-5 w-5 text-curious-blue-600 border-gray-300 rounded mr-2 disabled:opacity-50"
                     id="all" value="all" v-model="checkedFileTypes"
                    />


						<label class="form-check-label" for="all">Any</label>
					</div>

				</div>

					<div class="mt-2" >
					<label>Allow multiple files</label>
					<div class="block">
					<UIToggle
						v-model="multiple_files_allowed"
						id="account-buffer-tgl"
					/>
					</div>
					</div>


					<div v-if="multiple_files_allowed" class="mt-2" >
					<div  class="">
						<label class="form-check-label mr-2" for="multi-file-input-limit">Maximum number of files allowed</label>
						<UITextInputGroup type="number"  id="multi-file-input-limit" min="2" max="10" v-model.number="multi_file_limit"/>
					</div>

					</div>



				</div>


                <!--Currently only for the radio-->
                <div class="form-group" v-if="selectedFieldType == 'RADIO'">
                  <label class="mr-3">Possible values <!-- <small>(Enter one per line)</small> --></label>
                  <draggable v-model="listOptions" @end="onEnd" draggable=".hl-drag-options" handle=".handle">
                    <div class="row mb-3 hl-drag-options" v-for="(item,index) in listOptions" :key="'option-'+index">
                        <div class="col-1 d-flex justify-content-center align-items-center">
                          <a class="handle">
                            <i class="fa fa-align-justify"></i>
                          </a>
                        </div>
                        <div class="col-4 pl-0">
                          <UITextInputGroup
                            type="text"
                            :placeholder="item.placeholder"
                            v-model="item.text"
                            v-validate="'required'"
                            :name="'list_options_'+index"
                          />
                        </div>
                        <div class="col-3 d-flex align-items-center" v-if="! customFieldData.picklist_options" >
                          <input
                            type="file"
                            name="bg-image"
                            @change="(e) => onFileChange(e , index)"
                            :ref="'option_image_' + (index)"
                            style="width:100%;"
                          />
                        </div>
                        <div class="col-1 d-flex align-items-center" v-if="! customFieldData.picklist_options">
                          <img
                            :src="item && item.src"
                            style="width:40px;"
                            />
                        </div>
                        <div class="col-3 d-flex justify-content-center align-items-center">
                          <!-- <button
                            class="btn-link border-0"
                            @click="deleteOption(index)"
                            v-if="listOptions.length > 1"
                          >
                            <i class="fa fa-trash" color="#e93d3d"></i>
                          </button> -->
                          <i class="fa fa-trash-alt" style="color:#e93d3d;" v-if="listOptions.length > 1" @click.prevent="deleteOption(index)"/>
                        </div>
                        <span v-show="errors.has('list_options_'+index)" class="error" style="display:block;width:100%;padding:0 15px;">This option is required.</span>
                    </div>
                  </draggable>
				          <!-- <button
                        class="btn-link border-0"
                        @click="cloneOption"
                      >
					          <i class="icon icon-plus" style="font-size: 0.75em;margin-right: 10px;"></i>Add image
                  </button> -->
                  <a @click.prevent="cloneOption" href="#">
                      <i class="icon icon-plus" style="font-size: 0.75em;margin-right: 10px;"></i>Add Option
                  </a>
                </div>
                <!--Allow custom option only for Radio-->
                <div class="form-group" v-if="showAllowAddCustomOption">
                  <label class="mr-12">
                    <UICheckbox v-model="allowCustomOpt"/> Allow custom values
                  </label>
                </div>

                <!--TEXTBOXLIST FIELDS OPTION START-->
                <div class="form-group" v-if="selectedFieldType == 'TEXTBOX_LIST'">
                  <label class="mr-3">Possible values</label>
                  <draggable v-model="textBoxListOptions" @end="onEnd" draggable=".hl-drag-options" handle=".handle">
                    <div class="row mb-3 hl-drag-options" v-for="(item,index) in textBoxListOptions" :key="'option-'+index">
                        <div class="col-1 d-flex justify-content-center align-items-center">
                          <a class="handle">
                            <i class="fa fa-align-justify"></i>
                          </a>
                        </div>
                        <div class="col-4 pl-0">
                          <UITextInputGroup
                            type="text"
                            :placeholder="'Label'"
                            v-model="item.label"
                            v-validate="'required'"
                            :name="'list_options_'+index"
                          />
                        </div>
                        <div class="col-4 d-flex align-items-center" >
                          <UITextInputGroup
                            type="text"
                            :name="'prefill_options'+index"
                            v-model="item.prefillValue"
                            placeholder="Value"
                          />
                        </div>

                        <div class="col-3 d-flex justify-content-center align-items-center">
							<i class="fa fa-trash-alt" v-if="textBoxListOptions.length > 1" @click.prevent="deleteOptionTextList(index)"></i>
                        </div>
                        <span v-show="errors.has('list_options_'+index)" class="error" style="display:block;width:100%;padding:0 15px;">This option is required.</span>
                    </div>
                  </draggable>
                  <a @click.prevent="cloneOptionTextList" href="#">
                      <i class="icon icon-plus" style="font-size: 0.75em;margin-right: 10px;"></i>Add Option
                  </a>
                </div>

              </div>
            </div>
						<!-- Field input - START -->
					</div>

					<div class="col-md-12 mt-3 mb-3">
						<!-- <p class="text-center"><small>Please bear in mind that customized fields are shared with all users throughout your company.</small></p> -->
					</div>
				</div>
				<!-- Body - END -->

				<!-- Footer - START -->
				<div class="modal-footer" v-if="selectedFieldType">
					<div class="modal-footer--inner nav">
						<UIButton use="outline" data-dismiss="modal">Cancel</UIButton>
						<div style="display: inline-block;position: relative;">
							<UIButton
								use="primary"
								@click.prevent="onSubmit"
								:loading="saving"
							>Save</UIButton>
						</div>
					</div>
				</div>
				<!-- Footer - END -->
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from "vue";
import { Location, Contact, CustomField, FieldType, ICustomField, Formbuilder } from "@/models";
declare var $: any;

import { FileAttachment } from "@/store/state_models";
import ImageTools from "../../../util/image_tools";
const imageTools = new ImageTools();
import { v4 as uuid } from "uuid";
import firebase from "firebase/app";
import draggable from "vuedraggable";
import { cacheUpdateEvents, updateCachingIndex } from '../../../util/caching.helper';
const path = require('path');

export default Vue.extend({
  components: { draggable },
	props: {
		values: {} as any
	},
	data() {
		return {
			selectedFieldType: null as FieldType | null,
			customFieldData: {} as ICustomField,
			saving: false as boolean,
      		isEdit: false as boolean,
			allowCustomOpt: false as boolean,
			currentLocationId: " " as string,
			listOptions:[
				{
					text: "",
					placeholder: "Text Goes Here",
					src: ""
				}
			],
			textBoxListOptions: [
				{
					label : "",
					prefillValue : "",
					id: uuid()
				}
			],
			filesAdded: false as boolean,
			loading: true,
			checkedFileTypes: [],
			multiple_files_allowed: false,
			multi_file_limit: 2
		};
	},
	computed: {
		// Convert FieldTypes enum to array of objects for dynamic rendering
		fieldTypes: function () {
			let typesGroup: { id: string; label: string }[] = [];
			for (var c in FieldType) {
				typesGroup.push({
					id: c,
					label: FieldType[c]
				});
			}
			return typesGroup;
		},
		fieldType: function () {
			const type = this.fieldTypes.find(x => x.id === this.selectedFieldType);
			return type ? type.label : "";
		},
		showOptionsInput: function () {
			const fieldWithOptions = [
				FieldType.MULTIPLE_OPTIONS,
				FieldType.SINGLE_OPTIONS,
       			FieldType.CHECKBOX
			];
			if (
				this.selectedFieldType &&
				fieldWithOptions.indexOf(FieldType[this.selectedFieldType]) > -1
			) {
				return true;
			}
			return false;
    },
    showAllowAddCustomOption: function () {
			const fieldWithOptions = [
				FieldType.RADIO,
			];
			if (
				this.selectedFieldType &&
				fieldWithOptions.indexOf(FieldType[this.selectedFieldType]) > -1 &&
				this.listOptions.length > 0 &&
				this.listOptions[0].src ===''
				) {
				return true;
			}
			return false;
		}
	},
	watch: {
		values(values: { [key: string]: any }) {
			// Handle show/hide states of modal
			if (values.visible) $(this.$refs.modal).modal("show");
			else $(this.$refs.modal).modal("hide");
		},
		selectedFieldType(val) {
			this.customFieldData.data_type = val;
		}
	},
	updated() {
		// Handle show/hide states of modal
    	if (this.values && this.values.visible) {
			$(this.$refs.modal).modal("show");
		}

		const $selectpicker = $(this.$el).find(".selectpicker");
		if ($selectpicker) {
			$selectpicker.selectpicker("refresh");
		}
	},
	mounted() {
    // assign location id coming in from the parent to the customFieldData object
		if (this.values.location_id) {
			this.customFieldData.location_id = this.values.location_id;
		}

		const _self = this;
		// $(this.$refs.modal).on("hidden.bs.modal", function() {
		//   _self.$emit("hidden");
		// });
		this.customFieldData.model = "contact";
		// Handle show/hide states of modal
		if (this.values && this.values.visible) {
			$(this.$refs.modal).modal("show");
		}
		const $selectpicker = $(this.$el).find(".selectpicker");
		if ($selectpicker) {
			$selectpicker.selectpicker();
		}

		if (this.values.editCustomField) {
			this.isEdit = true;
			this.setEditForm();
		}
	},
	methods: {
		// Handle field type selection to render relevant input
		selectFieldTypeHandler(type: FieldType) {
			this.selectedFieldType = type;
		},
		// Form submit handler
		async onSubmit() {
			const result = await this.$validator.validateAll();
			if (!result) {
				return false;
			}
			this.saving = true;

			let customField;

			if (this.isEdit) {
				customField = this.values.editCustomField
			} else {
				customField = new CustomField();
				customField.position = 0;
			}

			if (customField) {
				let optionImageLabel = []
				const fieldKey = this.customFieldData.field_name.trim()
				.replace(/\s+/g, "_")
				.replace(/[\W]+/g, "")
						.toLowerCase();

				if (Contact.standardFields.includes(fieldKey)) {
					this.$uxMessage('warning', `You cannot create a Custom Field with the same name as an existing Standard Field. In this case, ${this.customFieldData.field_name} will conflict with contact's ${fieldKey} field. Please input a different name.`)
					this.saving = false
					this.customFieldData.field_name = ''
					return;
				}

				if (!this.isEdit && (await CustomField.getByFieldKey(this.customFieldData.location_id, this.customFieldData.model + "." + fieldKey)).length !== 0) {
					this.$uxMessage('warning', `The field key ${this.customFieldData.model + "." + fieldKey} already exist.`)
					this.saving = false
					return;
				}

        		customField.dataType = this.customFieldData.data_type;
				customField.model = this.customFieldData.model;
				customField.name = this.customFieldData.field_name;
				customField.placeholder = this.customFieldData.placeholder
					? this.customFieldData.placeholder
					: "";
				customField.locationId = this.customFieldData.location_id;
				if (!this.isEdit) customField.fieldKey = customField.model + "." + fieldKey;
				this.currentLocationId= customField.locationId;

				//reset picklistOption and picklistOptionImage to null before inserting
				customField.picklistOptions = undefined;
				customField.picklistOptionsImage = undefined;

				if (this.customFieldData.picklist_options && customField.dataType !== 'RADIO' && customField.dataType !== 'TEXTBOX_LIST') {
					customField.picklistOptions = this.listOptions.map( item => {return item.text })
				} else if( customField.dataType === 'TEXTBOX_LIST') {
					customField.picklistOptions = this.textBoxListOptions
				} else if(customField.dataType === "FILE_UPLOAD") {

					const finalCheckedTypes = [];
					if(!(this.checkedFileTypes.indexOf('all') > -1)) {

					this.checkedFileTypes.forEach((type) => {
						if(type === ".docx") {
							finalCheckedTypes.push(".docx");
							finalCheckedTypes.push(".doc");
						}else if(type === ".jpeg") {
							finalCheckedTypes.push(".jpeg");
							finalCheckedTypes.push(".jpg");
						}else if(type === ".csv") {
							finalCheckedTypes.push(".xlsx");
							finalCheckedTypes.push(".xls");
							finalCheckedTypes.push(".csv");

						}else if(type === ".jpg" || type === ".doc" || type === ".xls" || type === ".xlsx") {
							//Ignore, residue after unchecking
						}
						else {
							finalCheckedTypes.push(type);
						}
					})

					}


					customField.picklistOptions = finalCheckedTypes;
					customField.multiple_files_allowed = this.multiple_files_allowed;

					if(customField.multiple_files_allowed) {
						customField.multi_file_limit = this.multi_file_limit;
					}
				}
				else if(!this.filesAdded){
					let optionStore:any = [];
					this.listOptions.forEach(function(item , index){
						optionStore.push(item.text)
					})
					customField.picklistOptions = optionStore
				}
				else {
					const el = this;
					let optionStore:any = []
					this.listOptions.forEach(function(item , index){
						const obj = {
							'label' : item.text,
							'src' : ("src" in item) ? item.src : ""
						}
						optionStore.push(obj)
					});

					const optionImageStore = await this.send()
					optionImageStore.forEach(function(item: any, index: number){
						optionStore[item.index]['src'] = item.url;
					});
         			customField.picklistOptionsImage = optionStore;
				}
				customField.allowCustomOption = this.allowCustomOpt;
				await customField.save();
				if (this.isEdit) {
				await this.updateFieldOnForm(customField)
				}
				this.$store.dispatch('locationCustomFields/syncAll', { locationId: this.currentLocationId, forceRefresh: true })

				await updateCachingIndex({
					index_type:  cacheUpdateEvents.CUSTOM_FIELD_UPDATE,
					event_origin: this.currentLocationId
				})
				this.$emit("hide");
			}
		},
    async updateFieldOnForm(customField) {
      const formData = await Formbuilder.getByLocationId(this.currentLocationId)
      formData.forEach(function(form:any, key:number){
        if("form" in form.formData && "fields" in form.formData.form){
          const formFields = form.formData.form.fields
          if(formFields.length > 0){
            formFields.forEach(function(fieldItem:any, fieldKey:number){
              if(
                "id" in fieldItem &&
                fieldItem.id !="" &&
                fieldItem.id == customField.id
              ) {

                if (customField.picklistOptions && customField.picklistOptions.length) {
                  form.formData.form.fields[fieldKey].picklistOptions = customField.picklistOptions
                }

                if (customField.picklistOptionsImage && customField.picklistOptionsImage.length) {
                  form.formData.form.fields[fieldKey].picklistOptionsImage = customField.picklistOptionsImage
                }
                form.formData.form.fields[fieldKey].fieldKey = customField.fieldKey
                form.formData.form.fields[fieldKey].allowCustomOption = customField.allowCustomOption || false
                form.save()
              }
            })
          }
        }
      });
    },
		send: async function () {
			let el = this;
			const urls = [] as string[];
			const urlStore = [] as any;
			if (this.filesAdded) {
				const basePath =
				"location/" +
				this.currentLocationId +
				"/custom-field-store/"
				const newURLs: string[] = await Promise.all(
					this.listOptions.map(async function(attachmentData: any, index: any) {
						if("blobObj" in attachmentData){
							urlStore.push(index)
							const attachment = attachmentData.blobObj;
							let imagePath = basePath + uuid() + path.extname(attachment.name)
							var uploadPath = firebase.storage().ref(imagePath);
							const snapshot = await uploadPath.put(attachment.data, {
								contentType: attachment.type,
								contentDisposition: `inline; filename="${attachment.name}"`,
								customMetadata: { name: attachment.name }
							});
							return await snapshot.ref.getDownloadURL();
						}
					})
        		);
				urlStore.forEach(function(v,i){
					// console.log(v)
					// console.log(i)
					let obj = {} as any;
					obj = {
						"url": newURLs[v],
						"index": v
					};
					urls.push(obj);
				});
				// urls.push.apply(urls, newURLs.filter(url => url));
			}
			return urls;
		},
		setEditForm() {
			this.selectedFieldType = this.values.editCustomField.dataType;
			this.customFieldData.field_name = this.values.editCustomField.name;
			this.customFieldData.model = this.values.editCustomField.model;
			this.customFieldData.placeholder = this.values.editCustomField.placeholder;
			const el = this;
			switch(this.selectedFieldType){
				case 'RADIO':
				this.listOptions=[];
				let optionListItemStore = []; // 'Let' because value has to be read&write
				if(
					this.values.editCustomField.picklistOptions!=null &&
					this.values.editCustomField.picklistOptions.length > 0
					) {
					optionListItemStore = this.values.editCustomField.picklistOptions;
					this.customFieldData.picklist_options = optionListItemStore.join("\n")
					this.filesAdded = false;
				} else {
					optionListItemStore = this.values.editCustomField.picklistOptionsImage;
					this.filesAdded = true;
				}

				if (optionListItemStore && optionListItemStore.length > 0) {
					optionListItemStore.forEach(function(item:any, index:number){
						el.listOptions.push({
						text: (typeof item === 'object') ? item.label : item,
						placeholder: (typeof item === 'object') ? item.label : item,
						src: (typeof item === 'object') ? item.src : ""
						})
					});
				}
				this.allowCustomOpt = this.values.editCustomField.allowCustomOption
				break;
				case 'TEXTBOX_LIST':
					if(
					this.values.editCustomField.picklistOptions!=null &&
					this.values.editCustomField.picklistOptions.length > 0
					) {
						this.textBoxListOptions = this.values.editCustomField.picklistOptions;
						// this.customFieldData.picklist_options = optionListItemStore.join("\n")
					}
				break;

				case 'FILE_UPLOAD':
					this.checkedFileTypes = this.values.editCustomField.picklistOptions;
					this.multiple_files_allowed = this.values.editCustomField.multiple_files_allowed;
						if(this.values.editCustomField.multiple_files_allowed) {
						this.multi_file_limit = this.values.editCustomField.multi_file_limit;
					}
			    break;

				case 'MULTIPLE_OPTIONS':
				case 'SINGLE_OPTIONS':
       			case 'CHECKBOX':
					   this.listOptions = this.values.editCustomField.picklistOptions.map( item => {
						   return {
							   text: item,
							   placeholder: '',
							   src: ''
						   }
					   })
				break;
				default:
				if (this.values.editCustomField.picklistOptions) {
					this.customFieldData.picklist_options = this.values.editCustomField.picklistOptions.join("\n")
				}
				break;
			}
		},
		cloneOption(){
			this.listOptions.push({
				text: "",
				placeholder: "Text Goes Here",
				src: "",
				blobObj: []
			});
		},
		cloneOptionTextList(){
			this.textBoxListOptions.push({
				label: "",
				prefillValue : "",
				id: uuid()
			})
		},
		deleteOption(index:number){
			this.listOptions.splice(index, 1);
		},
		deleteOptionTextList(index:number){
			this.textBoxListOptions.splice(index, 1);
		},
		async onFileChange(e: any, index:number) {
			const element = <HTMLInputElement>e.target;
			if (!element.files) return;
			this.vfileAdded(element.files[0], index);
			this.filesAdded = true;
			element.files = null;
		},
		async vfileAdded(file: File, index: number) {
			const response = <File | Blob>(
				await imageTools.resize(file, { height: 100, width: 1000 })
			);
			const fileData:any = {
				name: file.name,
				type: file.type,
				url: URL.createObjectURL(response),
				data: response
			}
			//Storing image url into src
			this.$set(this.listOptions[index], 'src', fileData.url);
			this.$set(this.listOptions[index], 'blobObj', fileData);
		},
		onEnd(event:any) {
			// console.log(this.listOptions)
		}
	},
});
</script>
