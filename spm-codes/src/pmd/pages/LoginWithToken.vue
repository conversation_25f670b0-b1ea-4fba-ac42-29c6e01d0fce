<template>
  <div style="position: absolute;top: 50%;left: 50%;">
    <moon-loader :loading="true" color="#1ca7ff" size="30px"/>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import { AxiosResponse } from 'axios'
import NotificationHelper from '../../util/notification_helper'

const MoonLoader = () => import('@/pmd/components/MoonLoader.vue')

export default Vue.extend({
  components: {
    MoonLoader
  },
  async created() {
    await this.signout()
    let next = this.$route.query.next
    let locationId = this.$route.query.location_id
    let redirectUrl = this.$route.query.url

    const params = {
      ...this.$route.query,
      device_id: await this.$store.dispatch('getDeviceId'),
      device_type: 'web',
      device_name: navigator.userAgent,
      version: 2
    }

    this.$http
      .post('/api/signin/token', params)
      .then((response: AxiosResponse) => {
        this.$store
          .dispatch('auth/set', {
            apiKey: response.data.apiKey,
            userId: response.data.userId,
            companyId: response.data.companyId
          })
          .then(() => {
            return this.$auth.signInWithCustomToken(response.data.token)
          })
          .then(async () => {
              this.$store.dispatch('auth/set', {
                firebaseToken: response.data.token,
                apiKey: response.data.apiKey,
                userId: response.data.userId,
                companyId: response.data.companyId,
              });
            window.localStorage.setItem('app_referer', document.referrer);
            NotificationHelper.requestPermission()
            this.$store.dispatch('init')
            const url = this.$route.query.url || redirectUrl
            if (url) {
              this.$router.push({
                path: decodeURIComponent(url),
                replace: true
              })
            } else if (next && locationId) {
              this.$router.push({
                name: next,
                params: { location_id: locationId }
              })
            } else {
              this.$router.push({ name: 'dashboard', replace: true })
            }
          })
      }).catch(err => {
        this.$router.push({ name: 'login', replace: true })
      })
  },
  methods: {
    async signout() {
      try {
        await this.$store.dispatch('logout')
        await this.$auth.signOut()
        const response = await this.$http.post('/signout')
      } catch (err) {}
    }
  }
})
</script>

