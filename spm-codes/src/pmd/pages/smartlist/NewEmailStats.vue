<template>
  <div class="emailServiceStatsView custom-width">
    <b-tabs id="bootstrap-tab-custom-id" content-class="mt-1" lazy>
      <div class="custom-height">
        <b-tab
          @click="changeTab('total')"
          title="Total Attempted"
          :active="activeTab === 'total'"
        >
          <StatsDetailModalTable
            :innerGridClass="'igs'"
            :title="title"
            :items="items"
            :exportItems="exportItems"
            :fields="fields"
            :currentPage="currentPage"
            :itemPerPage="itemPerPage"
            :rows="rows"
            :isLoading="isLoading"
            :headerOptions="headerOptions"
            :fileName="fileName"
            @pageChanged="pageChanged"
            @perPageChanged="perPageChanged"
            @exportAll="exportAll"
            @exportCompleted="exportCompleted"
          />
        </b-tab>
        <b-tab
          :disabled="haveSMTP"
          @click="changeTab('delivered')"
          title="Delivered"
          :active="activeTab === 'delivered'"
        >
          <StatsDetailModalTable
            :innerGridClass="'igs'"
            :title="title"
            :items="items"
            :exportItems="exportItems"
            :fields="fields"
            :currentPage="currentPage"
            :itemPerPage="itemPerPage"
            :rows="rows"
            :isLoading="isLoading"
            :headerOptions="headerOptions"
            :fileName="fileName"
            @pageChanged="pageChanged"
            @perPageChanged="perPageChanged"
            @exportAll="exportAll"
            @exportCompleted="exportCompleted"
          />
        </b-tab>
        <b-tab
          @click="changeTab('opened')"
          title="Opened"
          :active="activeTab === 'opened'"
        >
          <StatsDetailModalTable
            :innerGridClass="'igs'"
            :title="title"
            :items="items"
            :exportItems="exportItems"
            :fields="fields"
            :currentPage="currentPage"
            :itemPerPage="itemPerPage"
            :rows="rows"
            :isLoading="isLoading"
            :fileName="fileName"
            :headerOptions="headerOptions"
            @pageChanged="pageChanged"
            @perPageChanged="perPageChanged"
            @exportAll="exportAll"
            @exportCompleted="exportCompleted"
          />
        </b-tab>
        <b-tab
          @click="changeTab('clicked')"
          title="Clicked"
          :active="activeTab === 'clicked'"
        >
          <StatsDetailModalTable
            :innerGridClass="'igs'"
            :title="title"
            :items="items"
            :fields="fields"
            :exportItems="exportItems"
            :currentPage="currentPage"
            :itemPerPage="itemPerPage"
            :rows="rows"
            :isLoading="isLoading"
            :fileName="fileName"
            :headerOptions="headerOptions"
            @pageChanged="pageChanged"
            @perPageChanged="perPageChanged"
            @exportAll="exportAll"
            @exportCompleted="exportCompleted"
          />
        </b-tab>
        <b-tab
          @click="changeTab('replied')"
          title="Replied"
          :active="activeTab === 'replied'"
        >
          <StatsDetailModalTable
            :innerGridClass="'igs'"
            :title="title"
            :items="items"
            :fields="fields"
            :exportItems="exportItems"
            :currentPage="currentPage"
            :itemPerPage="itemPerPage"
            :rows="rows"
            :isLoading="isLoading"
            :headerOptions="headerOptions"
            :fileName="fileName"
            @pageChanged="pageChanged"
            @perPageChanged="perPageChanged"
            @exportAll="exportAll"
            @exportCompleted="exportCompleted"
          />
        </b-tab>
        <b-tab
          @click="changeTab('permanent_fail')"
          title="Bounced"
          :active="activeTab === 'permanent_fail'"
          :disabled="haveSMTP"
        >
          <StatsDetailModalTable
            :innerGridClass="'igs'"
            :title="title"
            :items="items"
            :fields="fields"
            :exportItems="exportItems"
            :currentPage="currentPage"
            :itemPerPage="itemPerPage"
            :rows="rows"
            :isLoading="isLoading"
            :headerOptions="headerOptions"
            :fileName="fileName"
            @pageChanged="pageChanged"
            @perPageChanged="perPageChanged"
            @exportAll="exportAll"
            @exportCompleted="exportCompleted"
          />
        </b-tab>
        <b-tab
          @click="changeTab('unsubscribed')"
          title="Unsubscribed"
          :active="activeTab === 'unsubscribed'"
          :disabled="haveSMTP"
        >
          <StatsDetailModalTable
            :innerGridClass="'igs'"
            :title="title"
            :items="items"
            :fields="fields"
            :exportItems="exportItems"
            :currentPage="currentPage"
            :itemPerPage="itemPerPage"
            :rows="rows"
            :isLoading="isLoading"
            :headerOptions="headerOptions"
            :fileName="fileName"
            @pageChanged="pageChanged"
            @perPageChanged="perPageChanged"
            @exportAll="exportAll"
            @exportCompleted="exportCompleted"
          />
        </b-tab>
      </div>
    </b-tabs>
  </div>
</template>

<script lang="ts">
import Vue from 'vue'
import axios from 'axios'
import moment from 'moment'
import defaults from '@/config'
import { mapState } from 'vuex'
import { User } from '@/models'
import { getCountryDateFormat } from '@/models/index'
import StatsDetailModalTable from '../../components/StatsDetailModalTable'

// const StatsDetailModalTable = () => import()

let getData

export default Vue.extend({
  props: {
    modalTitle: String,
    option: String,
    campaign_id: String,
    campaign_step_id: String,
    selectedDate: Object,
    haveSMTP: Boolean,
    bulk_req_id: String,
  },
  components: {
    StatsDetailModalTable,
  },
  data() {
    return {
      isLoading: false,
      activeTab: this.option,
      records: [],
      currentPage: 1,
      itemPerPage: 25,
      rows: 0,
      headerOptions: {
        alwaysShowHeader: true,
        isExportOption: true,
      },
      exportItems: [],
      emailReportingUrl: defaults.emailReportingUrl,
      baseUrl: defaults.baseUrl,
      fields: [
        /* { key: 'first_name', label: 'First Name', sortable: false, checked: true }, */
        {
          key: 'full_name',
          label: 'Full Name',
          sortable: false,
          checked: true,
          hasHtml: true,
        },
        { key: 'email', label: 'Email', sortable: false, checked: true },
        { key: 'status', label: 'Status', sortable: false, checked: true },
        {
          key: 'timestamp',
          label: 'TimeStamp',
          sortable: false,
          checked: true,
        },
      ],
      contactIds: [],
      contacts: {},
      title: '',
      showCount: true,
      locationId: this.$route.params.location_id,
      fileName: '',
    }
  },
  computed: {
    items() {
      if (this.isLoading) {
        return []
      }

      if (!this.records || (this.records && this.records.length < 1)) {
        return []
      }

      let contactObj = JSON.parse(JSON.stringify(this.contacts))

      return this.getFinalObject(this.records, contactObj, false)
    },
    ...mapState('user', {
      userId: (s: UserState) => {
        const user = s.user ? new User(s.user) : undefined
        return user && user.isAssignedTo ? user.id : ''
      },
    }),
  },
  methods: {
    say(e) {
      // console.log('this stop parent from dragging.')
    },
    getFinalObject(records, contactObj, isdownloadData) {
      return records.map(record => {
        if (
          this.userId &&
          this.userId !==
            (contactObj[record.contactId] &&
              contactObj[record.contactId].assigned_to)
        ) {
          return {
            full_name: `${this.capital_letter(
              contactObj[record.contactId]
                ? contactObj[record.contactId].contact_name || 'Nil'
                : 'NOT FOUND OR DELETED'
            )}`,
            email: '**secret**',
            status: this.getStatus(record),
            timestamp: moment(record.updatedAt).format(
              getCountryDateFormat('extended-normal')
            ),
          }
        } else {
          return {
            full_name:
              !isdownloadData && contactObj[record.contactId]
                ? `<a href="/location/${this.locationId}/customers/detail/${
                    record.contactId
                  }">${this.capital_letter(
                    contactObj[record.contactId].contact_name || '-'
                  )}</a>`
                : contactObj[record.contactId]
                ? contactObj[record.contactId].contact_name
                : `NOT FOUND OR DELETED`,
            email:
              record.email ||
              (contactObj[record.contactId]
                ? contactObj[record.contactId].email || '-'
                : `NOT FOUND OR DELETED`),
            status: this.getStatus(record),
            timestamp: moment(record.updatedAt).format(
              getCountryDateFormat('extended-normal')
            ),
          }
        }
      })
    },
    fetchingData() {
      clearTimeout(getData)
      getData = setTimeout(() => {
        alert('hi')
        this.fetchData()
      }, 0)
    },
    pageChanged(page) {
      this.showCount = false
      this.currentPage = page
      this.fetchingData()
    },
    perPageChanged(perPage) {
      this.showCount = true
      this.itemPerPage = perPage
      this.currentPage = 1
      this.fetchingData()
    },
    changeTab(option) {
      if (this.activeTab !== option) {
        this.activeTab = option
        this.currentPage = 1
        this.fetchingData()
      }
      return ''
    },
    capital_letter(str) {
      str = str.split(' ')

      for (var i = 0, x = str.length; i < x; i++) {
        str[i] = str[i][0].toUpperCase() + str[i].substr(1)
      }

      return str.join(' ')
    },
    getStatus(row) {
      if (row.permanent_fail === 1) {
        return 'bounced'
      } else if (row.unsubscribed === 1) {
        return 'unsubscribed'
      } else if (row.replied === 1) {
        return 'replied'
      } else if (row.clicked === 1) {
        return 'clicked'
      } else if (row.opened === 1) {
        return 'opened'
      } else if (row.delivered === 1) {
        return 'delivered'
      } else {
        return '-'
      }
    },
    async fetchData() {
      this.isLoading = true
      try {
        //feetching stats and email-reporting rows
        let path = ''
        let params: { [key: string]: any } = {}
        if (this.bulk_req_id) {
          path = `${this.emailReportingUrl}/agent_reporting/email_stats_report/bulk_request`
          params = { bulk_req_id: this.bulk_req_id }
        } else {
          path = `${this.emailReportingUrl}/email_reporting/detail_by_option`
          params = {
            campaign_id: this.campaign_id,
            campaign_step_id: this.campaign_step_id,
            startAt: new Date(this.selectedDate.start).toISOString(),
            endAt: new Date(this.selectedDate.end).toISOString(),
          }
        }
        let stats = await axios.post(path, {
          location_id: this.locationId,
          option: this.activeTab,
          showCount: this.showCount,
          maxRows: this.itemPerPage,
          start: (this.currentPage - 1) * this.itemPerPage,
          ...params,
        })
        this.records = stats.data.response.records
        this.rows = this.showCount ? stats.data.response.total : this.rows

        //fetching new contacts
        if (!this.records || !this.records.length) {
          this.isLoading = false
          return
        }

        let tempContactId = [...this.contactIds]
        this.records.forEach(element => {
          if (!(element.contactId in this.contacts))
            tempContactId.push(element.contactId)
        })

        this.contactIds = [...new Set(tempContactId)]
        let contactsToFetch = this.contactIds.filter(element => {
          return !(element in this.contacts)
        })

        if (contactsToFetch.length === 0) {
          this.isLoading = false
          return
        }

        let contactsData = await axios.post(
          `${this.baseUrl}/getContactDetails`,
          {
            locationId: this.locationId,
            contactIds: contactsToFetch,
          }
        )

        contactsData.data.hits.forEach(element => {
          this.$set(this.contacts, element.tie_breaker_id, element)
        })
      } catch (err) {
        console.log(err)
        this.isLoading = false
      } finally {
        this.isLoading = false
      }
    },
    async fetchAllData() {
      try {
        let path = ''
        let params: { [key: string]: any } = {}
        if (this.bulk_req_id) {
          path = `${this.emailReportingUrl}/agent_reporting/email_stats_report/bulk_request`
          params = { bulk_req_id: this.bulk_req_id }
        } else {
          path = `${this.emailReportingUrl}/email_reporting/detail_by_option`
          params = {
            campaign_id: this.campaign_id,
            campaign_step_id: this.campaign_step_id,
            startAt: new Date(this.selectedDate.start).toISOString(),
            endAt: new Date(this.selectedDate.end).toISOString(),
          }
        }
        let stats = await axios.post(path, {
          location_id: this.locationId,
          option: this.activeTab,
          showCount: false,
          maxRows: 1000000000000, //making sure we get all the records
          start: 0,
          ...params,
        })

        let records = stats.data.response.records

        //fetching new contacts
        if (records.length === 0) {
          return []
        }

        let tempContactId = [...this.contactIds]
        records.forEach(element => {
          if (!(element.contactId in this.contacts))
            tempContactId.push(element.contactId)
        })

        let contactIds = [...new Set(tempContactId)]
        let contactsToFetch = contactIds.filter(element => {
          return !(element in this.contacts)
        })

        let contactObj = { ...this.contacts }
        let loopOnContacts = 1000
        for (let i = 0; i < contactsToFetch.length; i += loopOnContacts) {
          let contactsData
          if (i + loopOnContacts <= contactsToFetch.length) {
            contactsData = await axios.post(
              `${this.baseUrl}/getContactDetails`,
              {
                locationId: this.locationId,
                contactIds: contactsToFetch.slice(i, i + loopOnContacts),
              }
            )
          } else {
            contactsData = await axios.post(
              `${this.baseUrl}/getContactDetails`,
              {
                locationId: this.locationId,
                contactIds: contactsToFetch.slice(i, contactsToFetch.length),
              }
            )
          }
          contactsData.data.hits.forEach(element => {
            contactObj[element.tie_breaker_id] = element
          })
        }

        contactObj = JSON.parse(JSON.stringify(contactObj))
        const startStr =
          this.selectedDate && this.selectedDate.start
            ? `_from_${moment(this.selectedDate.start).format('MMM Do YYYY')}`
            : ''
        const endStr =
          this.selectedDate && this.selectedDate.end
            ? `_to_${moment(this.selectedDate.end).format('MMM Do YYYY')}`
            : ''
        this.fileName = `${this.modalTitle || 'email_stats'}_${
          this.activeTab || ''
        }${startStr || ''}${endStr || ''}`
        return this.getFinalObject(records, contactObj, true)
      } catch (err) {
        console.log('Error while importing stats', err)
        return []
      }
    },
    async exportAll() {
      console.log('exportAll')
      this.exportItems = await this.fetchAllData()
    },
    exportCompleted() {
      this.exportItems = []
    },
    fetchingData() {
      clearTimeout(getData)
      getData = setTimeout(() => {
        this.fetchData()
      }, 0)
    },
  },
  mounted() {
    this.fetchingData()
  },
})
</script>
<style lang="scss">
.emailServiceStatsView {
  padding: 4px 12px;
  .tabs {
    height: inherit;

    .tab-content {
      height: inherit;

      .tab-pane {
        height: inherit;
        padding-top: 0 !important;

        .hl-table {
          height: inherit;

          .table-responsive {
            height: 90%;

            table thead tr th {
              position: sticky;
              top: 0;
            }

            .pagination-component {
              left: 0;
              right: 0;
              bottom: 30px;
            }
          }
        }
      }
    }
  }
}

.disabled-tab {
  cursor: regular;
}

.pagination-component {
  position:unset !important;
  left: 0;
  right: 0;
  bottom: 30px;
}

.igs {
  min-height: 400px;
  max-height: 400px;
  overflow: auto;
}
</style>
