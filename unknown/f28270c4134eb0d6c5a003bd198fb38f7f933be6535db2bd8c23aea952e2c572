<template>
  <modal
    v-if="show"
    @close="$emit('close')"
    maxWidth="970"
    :showCloseIcon="true"
  >
    <div>
      <div class="req-card__header">
        <div class="req-card__header-title">
          Request Client’s Payment Details
        </div>
        <div class="req-card__header-description">
          Locations in SaaS mode need your client's credit card to work. You can
          request via sending an email, text or sharing an URL
        </div>
      </div>
      <div class="req-card__body" @click="closeAllSuggestions">
        <div v-if="company && !company.stripe_connect_id">
          Please
          <span
            class="req-card__link"
            @click="$router.push({ name: 'stripe_settings' })"
            >connect your Stripe Account </span
          >first, before enabling SaaS
        </div>
        <div v-else-if="!fetchedUsers">
          <upgrade-modal-shimmer style="max-width: 100%" />
        </div>
        <div v-else-if="fetchedUsers && allLocationUsers.length === 0">
          <request-card-modal-create-user
            @createUser="$emit('createUser', locationId)"
          />
        </div>
        <div v-else-if="fetchedUsers && locationEmailMissing">
          <RequestCardModalAddLocationEmail
            :location="location"
            @saved="locationEmailMissing = false"
          />
          <!-- <ProspectDetailsComponent :location="location" /> -->
        </div>
        <div class="req-card__body-options" v-else>
          <div class="req-card__body-option__left">
            <b-alert class="req-card__body-alert" show variant="warning">
              <i class="fas fa-exclamation-triangle"></i>
              Once you enable SaaS for this location, you can't disable it.
            </b-alert>
            <div class="req-card__body-option">
              <div
                class="req-card__body-option-icon"
                :class="{ '--selected': mode === 'link' }"
                @click="mode = 'link'"
              ></div>
              <div class="req-card__body-option-content">
                <div
                  class="req-card__body-option-label"
                  :class="{ '--selected': mode === 'link' }"
                  @click="mode = 'link'"
                >
                  Share a link
                </div>
                <div class="req-card__body-option-details">
                  <div class="req-option__link-card" v-if="mode === 'link'">
                    <p>
                      Copy and send this link to your client and instruct them
                      to add their credit card. (It can be used only once)
                    </p>
                    <div class="btn btn-primary" @click="copyLink">
                      <i class="far fa-copy"></i> COPY LINK
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="req-card__body-option">
              <div
                class="req-card__body-option-icon"
                @click="mode = 'email'"
                :class="{ '--selected': mode === 'email' }"
              ></div>
              <div class="req-card__body-option-content">
                <div
                  class="req-card__body-option-label"
                  @click="mode = 'email'"
                  :class="{ '--selected': mode === 'email' }"
                >
                  Request via email
                </div>
                <div
                  class="req-card__body-option-details"
                  v-if="mode === 'email'"
                >
                  <div class="req-card__tags" @click.stop>
                    <div
                      class="req-card__tag"
                      :class="{ '--invalid': !isValidEmail(email) }"
                      v-for="email in selectedEmails"
                      :key="email"
                    >
                      {{ email }}
                      <div
                        class="req-card__tag-detele-icon"
                        @click="removeSelectedEmail(email)"
                      >
                        <i class="fa fa-times"></i>
                      </div>
                    </div>
                    <input
                      class="req-card__tag-input"
                      placeholder="Enter User Email"
                      v-model="inputEmail"
                      @input="changeEmail"
                      @focus="showEmailSuggestions = true"
                      @keyup="onChangeEmailKeyUp"
                      autofocus
                      autocomplete="cc-csc"
                    />
                    <div
                      class="req-card__tags-overlay"
                      v-if="showEmailSuggestions"
                    >
                      <div class="req-card__tags-options">
                        <div
                          class="req-card__tags-option"
                          v-if="filteredLocationUsers.length === 0"
                        >
                          No User found in this location
                        </div>
                        <div
                          class="req-card__tags-option"
                          :class="{
                            '--selected': selectedEmails.includes(user.email),
                          }"
                          v-for="user in filteredLocationUsers"
                          :key="user.id"
                          @click="addToSelectedEmails(user.email)"
                        >
                          <div class="req-card__user-avatar">
                            {{
                              user.name ? user.name.split(' ')[0].charAt(0).toUpperCase() : ''
                            }}
                          </div>
                          <div class="req-card__user-details">
                            <div class="req-card__user-name">
                              {{ user.name }}
                            </div>
                            <div class="req-card__user-email">
                              {{ user.email }}
                            </div>
                          </div>
                          <div
                            v-if="selectedEmails.includes(user.email)"
                            class="req-card__tags-option__selected-icon"
                          >
                            <i class="fas fa-check"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="req-card__tags-info">
                    <i class="fas fa-exclamation-circle"></i>
                    Use commas to separate multiple emails.
                  </div>
                  <button
                    class="btn btn-primary"
                    :disabled="selectedEmails.length === 0"
                    @click="sendFinalEmail"
                    v-if="sendEmailStatus === 'none'"
                  >
                    <i class="fas fa-paper-plane"></i> &nbsp; Send
                  </button>
                  <button
                    class="btn btn-warning --sending"
                    v-if="sendEmailStatus === 'sending'"
                  >
                    <moon-loader
                      color="#ffffff"
                      size="20px"
                      style="display: inline"
                    />
                    &nbsp; Sending...
                  </button>
                  <button
                    class="btn btn-success"
                    v-if="sendEmailStatus === 'success'"
                  >
                    <i class="fas fa-check"></i> &nbsp; Sent
                  </button>
                  <button
                    class="btn btn-danger"
                    v-if="sendEmailStatus === 'failed'"
                  >
                    <i class="fas fa-exclamation-circle"></i> &nbsp; Failed
                  </button>
                </div>
              </div>
            </div>
            <div class="req-card__body-option">
              <div
                class="req-card__body-option-icon"
                @click="mode = 'sms'"
                :class="{ '--selected': mode === 'sms' }"
              ></div>
              <div class="req-card__body-option-content">
                <div
                  class="req-card__body-option-label"
                  @click="mode = 'sms'"
                  :class="{ '--selected': mode === 'sms' }"
                >
                  Request via SMS
                </div>
                <div
                  class="req-card__body-option-details"
                  v-if="mode === 'sms'"
                >
                  <div class="req-card__tags" @click.stop>
                    <div
                      class="req-card__tag"
                      :class="{ '--invalid': !isValidPhone(phone) }"
                      v-for="phone in selectedPhones"
                      :key="phone"
                    >
                      {{ phone }}
                      <div
                        class="req-card__tag-detele-icon"
                        @click="removeSelectedPhone(phone)"
                      >
                        <i class="fa fa-times"></i>
                      </div>
                    </div>
                    <input
                      class="req-card__tag-input"
                      placeholder="Enter User Phone Number"
                      v-model="inputPhone"
                      @input="changePhone"
                      @focus="showPhoneSuggestions = true"
                      @keyup="onChangePhoneKeyUp"
                      autofocus
                      autocomplete="cc-csc"
                    />
                    <div
                      class="req-card__tags-overlay"
                      v-if="showPhoneSuggestions"
                    >
                      <div class="req-card__tags-options">
                        <div
                          class="req-card__tags-option"
                          v-if="filteredLocationUsersSms.length === 0"
                        >
                          No User found in this location
                        </div>
                        <div
                          class="req-card__tags-option"
                          :class="{
                            '--selected': selectedPhones.includes(user.phone),
                          }"
                          v-for="user in filteredLocationUsersSms"
                          :key="user.id"
                          @click="addToSelectedPhones(user.phone)"
                        >
                          <div class="req-card__user-avatar">
                            {{
                              user.name ? user.name.split(' ')[0].charAt(0).toUpperCase() : ''
                            }}
                          </div>
                          <div class="req-card__user-details">
                            <div class="req-card__user-name">
                              {{ user.name }}
                            </div>
                            <div class="req-card__user-email">
                              {{ user.phone || 'No Phone Number attached' }}
                            </div>
                          </div>
                          <div
                            v-if="selectedPhones.includes(user.phone)"
                            class="req-card__tags-option__selected-icon"
                          >
                            <i class="fas fa-check"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="req-card__tags-info">
                    <i class="fas fa-exclamation-circle"></i>
                    Use commas to separate multiple phone numbers.
                  </div>
                  <button
                    class="btn btn-primary"
                    :disabled="selectedPhones.length === 0"
                    @click="sendFinalSMS"
                    v-if="sendSmsStatus === 'none'"
                  >
                    <i class="fas fa-paper-plane"></i> &nbsp; Send
                  </button>
                  <button
                    class="btn btn-warning --sending"
                    v-if="sendSmsStatus === 'sending'"
                  >
                    <moon-loader
                      color="#ffffff"
                      size="20px"
                      style="display: inline"
                    />
                    &nbsp; Sending...
                  </button>
                  <button
                    class="btn btn-success"
                    v-if="sendSmsStatus === 'success'"
                  >
                    <i class="fas fa-check"></i> &nbsp; Sent
                  </button>
                  <button
                    class="btn btn-danger"
                    v-if="sendSmsStatus === 'failed'"
                  >
                    <i class="fas fa-exclamation-circle"></i> &nbsp; Failed
                  </button>
                </div>
              </div>
            </div>
            <div class="req-card__body-option">
              <div
                class="req-card__body-option-icon"
                @click="mode = 'stripe'"
                :class="{ '--selected': mode === 'stripe' }"
              ></div>
              <div class="req-card__body-option-content">
                <div
                  class="req-card__body-option-label"
                  @click="mode = 'stripe'"
                  :class="{ '--selected': mode === 'stripe' }"
                >
                  I already have this customer in Stripe
                </div>
                <div
                  class="req-card__body-option-details"
                  v-if="mode === 'stripe'"
                >
                  <div class="req-card__tags" @click.stop>
                    <div class="req-card__tags-input-icon">
                      <i class="fas fa-search"></i>
                    </div>
                    <input
                      class="req-card__tag-input"
                      placeholder="Search customer in stripe"
                      v-model="inputStripeSearch"
                      @input="e => changeStripeSearch(e.target.value)"
                      autofocus
                      autocomplete="cc-csc"
                    />
                    <div
                      class="req-card__tags-input-icon"
                      @click="changeStripeSearch('')"
                    >
                      <i class="fas fa-times-circle"></i>
                    </div>
                    <div
                      class="req-card__tags-overlay"
                      v-if="showStripeSuggestions"
                    >
                      <div class="req-card__tags-options">
                        <div
                          class="req-card__tags-option"
                          v-if="stripeSuggestions.length === 0"
                        >
                          No Stripe account found !!
                        </div>
                        <div
                          class="req-card__tags-option"
                          :class="{
                            '--selected': selectedStripe === user.customer_id,
                          }"
                          v-for="user in stripeSuggestions"
                          :key="user.customer_id"
                          @click="
                            addToSelectedStripe(user.customer_id, user.name)
                          "
                        >
                          <div class="req-card__user-avatar">
                            {{
                              user.name ? user.name.split(' ')[0].charAt(0).toUpperCase() : ''
                            }}
                          </div>
                          <div class="req-card__user-details">
                            <div class="req-card__user-name">
                              {{ user.name }}
                            </div>
                            <div class="req-card__user-email">
                              {{ user.email }} &nbsp;
                              <b>[{{ user.customer_id }}]</b>
                            </div>
                          </div>
                          <div
                            v-if="selectedStripe === user.customer_id"
                            class="req-card__tags-option__selected-icon"
                          >
                            <i class="fas fa-check"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>


          </div>
          <div
            class="req-card__body-options__right --direct-link"
            v-if="mode === 'link'"
          >
            <a :href="cardSetupLink" target="_blank" class="link">
              {{ cardSetupLink }}
            </a>
          </div>
          <div
            class="req-card__body-options__right --email"
            v-if="mode === 'email' || mode === 'sms'"
          >
            Hi!
            <br />
            <br />
            This is {{ user.name }} from <b>{{ company.name }}</b
            ><br />
            <br />
            Please use the following link to attach your payment method.
            <br /><br />
            <a :href="cardSetupLink" target="_blank" class="link">
              {{ cardSetupLink }}
            </a>

            <div class="req-card__body-options__right__test-bar">
              <!-- <div class="link">Save as template</div> -->
              <div></div>
              <div class="req-card__flex" v-if="mode === 'email'">
                <input
                  type="text"
                  placeholder="<EMAIL>"
                  class="form-control req-card__test-btn --input"
                  v-model="testEmail"
                />
                <button
                  class="btn btn-primary req-card__test-btn"
                  :disabled="!testEmail"
                  @click="sendTestEmail()"
                  v-if="sendTestEmailStatus === 'none'"
                >
                  Test
                </button>
                <button
                  class="btn btn-warning --sending req-card__test-btn"
                  v-if="sendTestEmailStatus === 'sending'"
                >
                  Sending...
                </button>
                <button
                  class="btn btn-success req-card__test-btn"
                  v-if="sendTestEmailStatus === 'success'"
                >
                  <i class="fas fa-check"></i> &nbsp; Sent
                </button>
                <button
                  class="btn btn-danger req-card__test-btn"
                  v-if="sendTestEmailStatus === 'failed'"
                >
                  <i class="fas fa-exclamation-circle"></i> &nbsp; Failed
                </button>
              </div>
              <div class="req-card__flex" v-if="mode === 'sms'">
                <input
                  type="text"
                  placeholder="Test Mobile No."
                  class="form-control req-card__test-btn --input"
                  v-model="testPhone"
                />
                <button
                  class="btn btn-primary req-card__test-btn"
                  :disabled="!testPhone"
                  @click="sendTestSMS"
                  v-if="sendTestSmsStatus === 'none'"
                >
                  Test
                </button>
                <button
                  class="btn btn-warning --sending req-card__test-btn"
                  v-if="sendTestSmsStatus === 'sending'"
                >
                  Sending...
                </button>
                <button
                  class="btn btn-success req-card__test-btn"
                  v-if="sendTestSmsStatus === 'success'"
                >
                  <i class="fas fa-check"></i> &nbsp; Sent
                </button>
                <button
                  class="btn btn-danger req-card__test-btn"
                  v-if="sendTestSmsStatus === 'failed'"
                >
                  <i class="fas fa-exclamation-circle"></i> &nbsp; Failed
                </button>
              </div>
            </div>
          </div>
          <div
            class="req-card__body-options__right --stripe"
            v-if="mode === 'stripe'"
          >
            <upgrade-modal-shimmer
              style="max-width: 100%"
              v-if="fetchingCustomerDetails"
            />

            <div
              class="stripe-cus-details__wrap"
              v-else-if="selectedStripeDetails && selectedStripeDetails.id"
            >
              <div class="stripe-cus-details__header">
                <div class="req-card__user-avatar">
                  {{
                    selectedStripeDetails.name ?
                    selectedStripeDetails.name
                      .split(' ')[0]
                      .charAt(0)
                      .toUpperCase() : ''
                  }}
                </div>
                <div class="req-card__user-details">
                  <div class="req-card__user-name">
                    {{ selectedStripeDetails.name }}
                  </div>
                  <div class="req-card__user-email">
                    {{ selectedStripeDetails.email }} &nbsp;
                  </div>
                </div>
              </div>
              <div class="stripe-cus-details__customer-id">
                {{ selectedStripeDetails.id }}
              </div>
              <div class="stripe-cus-details__stats">
                <div class="stripe-cus-details__stat">
                  <div class="stripe-cus-details__stat-label">Member Since</div>
                  <div class="stripe-cus-details__stat-value">
                    {{ formatDate(selectedStripeDetails.created) }}
                  </div>
                </div>
                <div class="stripe-cus-details__stat">
                  <div class="stripe-cus-details__stat-label">Spent</div>
                  <div class="stripe-cus-details__stat-value">
                    ${{ Number(individualLtv / 100).toFixed(2) }}
                  </div>
                </div>
                <div class="stripe-cus-details__stat">
                  <div class="stripe-cus-details__stat-label">MRR</div>
                  <div class="stripe-cus-details__stat-value">
                    ${{ Number(individualMrr / 100).toFixed(2) }}
                  </div>
                </div>
              </div>
              <div class="stripe-cus-details__info-card">
                <div class="stripe-cus-details__info-card__header">
                  Active Subscriptions
                </div>
                <div class="stripe-cus-details__info-card__body">
                  <div v-if="individualSubscriptions.length === 0">
                    No Active Subscription available
                  </div>
                  <div
                    v-for="subscription in individualSubscriptions"
                    :key="subscription._id"
                    style="border-bottom: 1px dashed #ccc; margin-bottom: 4px"
                  >
                    <div>
                      {{ subscription.product.name }}
                      <!-- <span class="light-tag">Trial ends Mar 11</span> -->
                      <!-- <span class="light-tag"
                        >( {{ subscription.plan.amount / 100 }}
                        {{ subscription.plan.currency.toUpperCase() }} per
                        {{ subscription.plan.interval }} )</span
                      > -->
                    </div>
                    <div class="light-text">
                      ( {{ subscription.plan.amount / 100 }}
                      {{ subscription.plan.currency.toUpperCase() }} per
                      {{ subscription.plan.interval }} )
                    </div>
                    <!-- <div>
                      Billing Monthly
                      <span class="light-text"
                        >(Next invoice on Mar 11 for $97.00)</span
                      >
                    </div> -->
                  </div>
                </div>
              </div>
              <div class="stripe-cus-details__info-card">
                <div class="stripe-cus-details__info-card__header">
                  Payment methods
                </div>
                <div class="stripe-cus-details__info-card__body">
                  <div v-if="individualPaymentMethods.length === 0">
                    No Payment Method available
                  </div>
                  <div v-for="card in individualPaymentMethods" :key="card.id">
                    {{ card.card.brand }} {{ card.card.last4 }}
                    <span
                      class="light-tag"
                      v-if="
                        card.id ===
                        selectedStripeDetails.invoice_settings
                          .default_payment_method
                      "
                      >Default</span
                    >
                  </div>
                </div>
              </div>
              <div class="stripe-cus-details__info-card">
                <div class="stripe-cus-details__info-card__header">
                  CREDIT BALANCE
                </div>
                <div class="stripe-cus-details__info-card__body">
                  ${{ Number(selectedStripeDetails.balance / 100).toFixed(2) }}
                  USD
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="req-card__footer" v-if="allLocationUsers.length !== 0">
        <!-- <div class="btn btn-light">
          <i class="far fa-clock"></i> Do it later
        </div> -->

        <div
          class="btn btn-success --sending req-card__next-btn"
          v-if="saasEnableStatus === 'sending'"
          disabled
        >
          <moon-loader
            color="#ffffff"
            size="20px"
            style="display: inline; margin: auto"
          />
        </div>
        <div
          class="btn btn-success req-card__next-btn"
          v-else-if="saasEnableStatus === 'success'"
        >
          <i class="fas fa-check"></i> &nbsp; Success
        </div>
        <div
          class="btn btn-danger req-card__next-btn"
          v-else-if="saasEnableStatus === 'failed'"
        >
          <i class="fas fa-exclamation-circle"></i> &nbsp; Failed
        </div>
        <div
          class="btn btn-primary req-card__next-btn"
          v-else-if="selectedStripe && mode === 'stripe'"
          @click="handleSelectCustomer()"
        >
          Convert to SaaS <i class="fas fa-arrow-right"></i>
        </div>
        <div
          class="btn req-card__next-btn"
          :class="{
            '--disabled btn-primary': completeButtonDisabled,
            'btn-success': !completeButtonDisabled,
          }"
          v-else
          @click="handleMoveNext()"
        >
          Convert to SaaS <i class="fas fa-arrow-right"></i>
        </div>
      </div>
    </div>
  </modal>
</template>

<script>
import { User, Location } from '@/models'
import Modal from '@/pmd/components/common/Modal.vue'
import { UserState } from '@/store/state_models'
import * as moment from 'moment-timezone'
import UpgradeModalShimmer from '@/pmd/components/common/shimmers/UpgradeModalShimmer.vue'
import RequestCardModalCreateUser from './RequestCardModalCreateUserV2'
import RequestCardModalAddLocationEmail from './RequestCardModalAddLocationEmail.vue'
// import ProspectDetailsComponent from "@/pmd/components/agency/ProspectDetailsComponent.vue";
import copyToClipboard from '@/util/copy-to-clipboard.ts'
import { trackGaEvent } from '@/util/helper'
import config from '@/config'

export default {
  props: ['show', 'locationId'],
  components: {
    Modal,
    UpgradeModalShimmer,
    RequestCardModalCreateUser,
    RequestCardModalAddLocationEmail,
    // ProspectDetailsComponent,
  },
  data() {
    return {
      mode: 'link',
      selectedEmails: [],
      selectedPhones: [],
      inputEmail: '',
      inputPhone: '',
      testEmail: '',
      testPhone: '',
      allLocationUsers: [],
      // filteredLocationUsers: [],
      // filteredLocationUsersSms: [],
      showEmailSuggestions: false,
      showPhoneSuggestions: false,
      inputStripeSearch: '',
      showStripeSuggestions: false,
      stripeSuggestions: [],
      selectedStripe: '',
      selectedStripeDetails: {},
      individualMrr: 0.0,
      individualLtv: 0.0,
      individualSubscriptions: [],
      individualPaymentMethods: [],
      completeButtonDisabled: true,
      fetchedUsers: false,
      sendEmailStatus: 'none', // 'none' | 'sending' | 'success' | 'failed'
      sendTestEmailStatus: 'none', // 'none' | 'sending' | 'success' | 'failed'
      sendSmsStatus: 'none', // 'none' | 'sending' | 'success' | 'failed'
      sendTestSmsStatus: 'none', // 'none' | 'sending' | 'success' | 'failed'
      saasEnableStatus: 'none', // 'none' | 'sending' | 'success' | 'failed'
      fetchingCustomerDetails: false,
      location: undefined,
      locationEmailMissing: false,
      stripeLiveMode: config.mode === 'production',
    }
  },
  computed: {
    company() {
      return this.$store.state.company.company
    },
    user() {
      const user = this.$store.state.user.user
      return user ? new User(user) : undefined
    },
    domain() {
      return this.company.domain
        ? `https://${this.company.domain}`
        : window.location.origin || 'https://app.gohighlevel.com'
    },
    cardSetupLink() {
      return `${this.domain}/location/${this.locationId}/settings/billing`
    },
    filteredLocationUsers() {
      let searchTerm = this.inputEmail ? this.inputEmail.toLowerCase() : ''
      return this.allLocationUsers.filter(user => {
        if (
          (user.name && user.name.toLowerCase().includes(searchTerm)) ||
          (user.email && user.email.toLowerCase().includes(searchTerm))
        ) {
          return true
        }
        return false
      })
    },
    filteredLocationUsersSms() {
      let searchTerm = this.inputPhone ? this.inputPhone.toLowerCase() : ''
      return this.allLocationUsers.filter(user => {
        if (
          (user.name ? user.name.toLowerCase().includes(searchTerm) : false) ||
          (user.email
            ? user.email.toLowerCase().includes(searchTerm)
            : false) ||
          (user.phone ? user.phone.toLowerCase().includes(searchTerm) : false)
        ) {
          return true
        }
        return false
      })
    },
  },
  created() {
    this.getAllLocationUsers()
  },
  methods: {
    formatDate(timestamp) {
      return moment.unix(timestamp).format('MMM DD, YYYY')
    },
    closeAllSuggestions() {
      this.addToSelectedEmails(this.inputEmail)
      this.inputEmail = ''
      this.showEmailSuggestions = false
      this.addToSelectedPhones(this.inputPhone)
      this.inputPhone = ''
      this.showPhoneSuggestions = false
      this.showStripeSuggestions = false
    },
    removeSelectedEmail(email) {
      let index = this.selectedEmails.indexOf(email)
      if (email && index !== -1) {
        this.selectedEmails.splice(index, 1)
      }
    },
    removeSelectedPhone(phone) {
      let index = this.selectedPhones.indexOf(phone)
      if (phone && index !== -1) {
        this.selectedPhones.splice(index, 1)
      }
    },
    changeEmail(e) {
      let value = e.target.value
      if (value.includes(',')) {
        let values = value.split(',')
        values.forEach((element, i) => {
          if (i < values.length - 1) {
            // this.selectedEmails.push(element)
            this.addToSelectedEmails(element)
          } else {
            this.inputEmail = element
          }
        })
      }
    },
    changePhone(e) {
      let value = e.target.value
      if (value.includes(',')) {
        let values = value.split(',')
        values.forEach((element, i) => {
          if (i < values.length - 1) {
            // this.selectedEmails.push(element)
            this.addToSelectedPhones(element)
          } else {
            this.inputPhone = element
          }
        })
      }
    },
    onChangeEmailKeyUp(e) {
      if (e.key === 'Enter' || e.keyCode === 13) {
        // Do something
        this.addToSelectedEmails(this.inputEmail)
        this.inputEmail = ''
      }
      if (e.key === 'Backspace' || e.keyCode === 8) {
        if (this.inputEmail === '' && this.selectedEmails.length) {
          this.inputEmail = this.selectedEmails.splice(
            this.selectedEmails.length - 1,
            1
          )
        }
      }
    },
    onChangePhoneKeyUp(e) {
      if (e.key === 'Enter' || e.keyCode === 13) {
        // Do something
        this.addToSelectedPhones(this.inputPhone)
        this.inputPhone = ''
      }
      if (e.key === 'Backspace' || e.keyCode === 8) {
        if (this.inputPhone === '' && this.selectedPhones.length) {
          this.inputPhone = this.selectedPhones.splice(
            this.selectedPhones.length - 1,
            1
          )
        }
      }
    },
    addToSelectedEmails(newEmail) {
      if (newEmail && this.selectedEmails.indexOf(newEmail) === -1) {
        this.inputEmail = ''
        this.selectedEmails.push(newEmail)
      } else {
        // remove email
      }
    },
    addToSelectedPhones(newPhone) {
      if (newPhone && this.selectedPhones.indexOf(newPhone) === -1) {
        this.selectedPhones.push(newPhone)
      } else {
        // remove email
      }
    },
    async changeStripeSearch(value) {
      // let value = e.target.value
      // const liveMode = false
      if (value.length > 1) {
        this.showStripeSuggestions = true
        try {
          const { data } = await this.saasService.get(
            `/connect/find_cus?account_id=${this.company.stripe_connect_id}&find_string=${value}&livemode=${this.stripeLiveMode}`
          )
          console.log(data)
          this.stripeSuggestions = [...data]
        } catch (err) {
          //
        }
      } else {
        this.showStripeSuggestions = false
        this.inputStripeSearch = value
      }
    },
    async addToSelectedStripe(stripeCustomerId, userName) {
      this.selectedStripe = stripeCustomerId
      this.individualSubscriptions = []
      this.individualPaymentMethods = []
      this.fetchingCustomerDetails = true
      this.inputStripeSearch = userName || stripeCustomerId
      this.showStripeSuggestions = false
      // const liveMode = false
      // Get details
      try {
        const { data } = await this.saasService.post(`/connect/cus_details`, {
          account_id: this.company.stripe_connect_id,
          customer_id: this.selectedStripe,
        })
        console.log(data)
        this.selectedStripeDetails = data.customer
        this.individualPaymentMethods = data.paymentMethods.data
        const { data: individual_mrr } = await this.saasService.post(
          `/connect/cus_mrr_contribution`,
          {
            account_id: this.company.stripe_connect_id,
            customer_id: this.selectedStripe,
            livemode: this.stripeLiveMode,
          }
        )
        this.individualMrr = individual_mrr.mrr_contribution
        this.individualLtv = individual_mrr.ltv
        const { data: subscriptions } = await this.saasService.post(
          `/connect/cus_subscriptions`,
          {
            account_id: this.company.stripe_connect_id,
            customer_id: this.selectedStripe,
            livemode: this.stripeLiveMode,
          }
        )
        console.log(subscriptions)
        this.individualSubscriptions = subscriptions
      } catch (err) {
        //
      }
      this.fetchingCustomerDetails = false
    },
    isValidEmail(email) {
      const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      return re.test(String(email).toLowerCase())
    },
    isValidPhone(phone) {
      const re = /^\+[1-9]\d{10,14}$/
      return re.test(String(phone).toLowerCase())
    },
    async getAllLocationUsers() {
      // console.log('getAllLocationUsers', this.locationId)
      let users = await User.getByLocationCompany(
        this.locationId,
        this.company.id
      )
      // console.log(users)
      this.allLocationUsers = users
      try {
        this.location = await Location.getById(this.locationId)
        if (!this.location.prospectInfo.email) {
          this.locationEmailMissing = true
        }
      } catch (err) {
        console.error(err)
      }
      this.fetchedUsers = true
    },
    async sendEmail(emails) {
      const { data } = await this.$http.post(`/saas/request_card_email`, {
        to_emails: [...emails],
        user_name: this.user.name,
        company_id: this.company.id,
        company_name: this.company.name,
        company_email: this.company.email,
        domain: this.domain,
        setup_link: this.cardSetupLink,
      })
    },
    async sendTestEmail() {
      this.sendTestEmailStatus = 'sending'
      try {
        await this.sendEmail([this.testEmail])
        this.sendTestEmailStatus = 'success'
      } catch (err) {
        this.sendTestEmailStatus = 'failed'
      }
      setTimeout(() => {
        this.sendTestEmailStatus = 'none'
      }, 2000)
    },
    async sendFinalEmail() {
      this.sendEmailStatus = 'sending'
      try {
        await this.sendEmail([...this.selectedEmails])
        this.sendEmailStatus = 'success'
      } catch (err) {
        this.sendEmailStatus = 'failed'
      }
      setTimeout(() => {
        this.sendEmailStatus = 'none'
      }, 2000)
      this.completeButtonDisabled = false
    },
    async sendSMS(numbers) {
      let toNumbers = [...numbers]
      await this.saasService.post(`/saas-config/${this.locationId}/send-sms`, {
        // to_number: this.testPhone || '+15615560676',
        to_numbers: toNumbers,
        user_name: this.user.name,
        company_name: this.company.name,
        domain: this.domain,
        setup_link: this.cardSetupLink,
        companyId: this.company.id,
      })
    },

    async sendTestSMS() {
      this.sendTestSmsStatus = 'sending'
      try {
        await this.sendSMS([this.testPhone])
        this.sendTestSmsStatus = 'success'
      } catch (err) {
        this.sendTestSmsStatus = 'failed'
      }
      setTimeout(() => {
        this.sendTestSmsStatus = 'none'
      }, 2000)
    },
    async sendFinalSMS() {
      this.sendSmsStatus = 'sending'
      try {
        await this.sendSMS([...this.selectedPhones])
        this.sendSmsStatus = 'success'
      } catch (err) {
        this.sendSmsStatus = 'failed'
      }
      setTimeout(() => {
        this.sendSmsStatus = 'none'
      }, 2000)
      this.completeButtonDisabled = false
    },

    async handleSelectCustomer() {
      this.saasEnableStatus = 'sending'

      try {
        let location = await Location.getById(this.locationId)
        const payload = {
          stripeAccountId: this.company.stripe_connect_id,
          name: location.name,
          email: location.prospectInfo.email,
          description: `Enable SaaS mode for ${location.name}`,
          customer_id: this.selectedStripe,
          defaultPaymentId:
            this.selectedStripeDetails.invoice_settings
              .default_payment_method || '',
          companyId: this.company.id,
        }
        const {data} = await this.saasService.post(
          `/saas-config/${this.locationId}/enable-saas`,
          payload
        )
        // console.log('got resp --> ', data)

        this.saasEnableStatus = 'success'
        setTimeout(() => {
          this.$emit('success', {msg: 'Account is converted to a SaaS account!', response: data, locationId: this.locationId})
        }, 1000 )
      } catch (err) {
        this.saasEnableStatus = 'failed'
      }
      setTimeout(() => {
        this.saasEnableStatus = 'none'
      }, 2000)
    },
    async handleMoveNext() {
      this.saasEnableStatus = 'sending'
      try {
        let location = await Location.getById(this.locationId)

        const payload = {
          stripeAccountId: this.company.stripe_connect_id,
          name: location.name,
          email: location.prospectInfo.email,
          companyId: this.company.id,
          description: `Enable SaaS mode for ${location.name}`,
        }
        const {data} = await this.saasService.post(
          `/saas-config/${this.locationId}/enable-saas`,
          payload
        )
        // console.log('got resp --> ', data)
        this.saasEnableStatus = 'success'
        setTimeout(() => {
          this.$emit('success', {msg: 'Account is converted to a SaaS account!', response: data, locationId: this.locationId })
        }, 1000 )
      } catch (err) {
        this.saasEnableStatus = 'failed'
      }
      setTimeout(() => {
        this.saasEnableStatus = 'none'
      }, 2000)
    },
    copyLink() {
      copyToClipboard(this.cardSetupLink)
      this.completeButtonDisabled = false
      trackGaEvent( 'SaaS Mode', this.locationId ,'Payment Request Link Copied.',1)
    },
  },
}
</script>

<style lang="scss">
.req-card__header {
  padding: 32px 24px;
  border-bottom: 1px solid #e2e8f0;
}
.req-card__header-title {
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-size: 21px;
  line-height: 25px;
  color: #2d3748;

  margin-bottom: 10px;
}
.req-card__header-description {
  font-family: Roboto;
  font-style: normal;
  font-weight: normal;
  font-size: 12px;
  line-height: 14px;
  color: #4a5568;
}

.req-card__body {
  padding: 32px 24px 24px 32px;
  height: 538px;
  max-height: calc(100vh - 240px);
  overflow: auto;
}
.req-card__body-options {
  display: flex;
  justify-content: space-between;
  position: relative;
  height: 100%;
}
.req-card__body-option__left {
  flex-grow: 1;
  .req-card__body-option {
    display: flex;
    margin-bottom: 16px;
  }
  .req-card__body-option-icon {
    border-radius: 50%;
    height: 18px;
    min-width: 18px;
    border: 1px solid #cbd5e0;
    margin-right: 12px;
    cursor: pointer;
    &.--selected {
      border: 6px solid #63b3ed;
    }
  }
  .req-card__body-option-content {
    flex-grow: 1;
  }
  .req-card__body-option-label {
    // font-size: 16px;
    line-height: 19px;
    color: #4a5568;
    font-weight: 500;
    margin-bottom: 16px;
    cursor: pointer;
    &.--selected {
      color: #4299e1;
    }
  }
}
.req-card__body-options__right {
  min-width: 396px;
  max-width: 396px;
  min-height: 383px;
  height: 280px;
  border: 1px solid #cbd5e0;
  box-sizing: border-box;
  border-radius: 3px;
  padding: 32px;
  margin-left: 30px;
  position: relative;
  &.--direct-link {
    // font-size: 16px;
    line-height: 141.19%;
    background: #f7fafc;
    display: flex;
    align-items: center;
  }
  &.--email {
    // font-size: 16px;
    line-height: 145.19%;
    color: #2d3748;
  }
  &.--stripe {
    border: none;
    border-left: 1px solid #e5e7eb;
    margin-top: -32px;
  }
  .link {
    color: #3182ce;
    // word-wrap: break-word;
    overflow-wrap: anywhere;
    line-height: 18px;
    cursor: pointer;
  }
}

.req-card__footer {
  padding: 24px 24px;

  display: flex;
  justify-content: flex-end;
  align-items: center;
  .btn {
    // margin-left: 24px;
    width: 260px;
    &.--disabled {
      cursor: not-allowed;
      pointer-events: none;
      opacity: 0.5;
    }
  }
}

.req-option__link-card {
  background: #f7fafc;
  border: 1px solid #cbd5e0;
  border-radius: 3px;
  padding: 16px;
  width: 380px;
  p {
    font-size: 14px;
    line-height: 16px;
    color: #2d3748;
    margin-bottom: 16px;
  }
}

.req-card__tags {
  display: flex;
  flex-wrap: wrap;
  align-items: center;

  background: #ffffff;
  position: relative;
  /* blue/400 */

  border: 1px solid #cdcdcd;
  box-sizing: border-box;
  border-radius: 3px;
  padding: 8px 0px 0px 8px;
  &:focus-within {
    border: 1px solid rgb(99, 179, 237);
    box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.4);
    // .req-card__tags-overlay {
    //   display: block !important;
    // }
  }
  .req-card__tags-input-icon {
    color: #9e9e9e;
    padding: 0px 8px 8px;
    cursor: pointer;
  }
  .req-card__tag {
    background-color: #e2e8f0;
    border-radius: 3px;
    padding: 8px;

    color: #2d3748;
    font-size: 14px;
    line-height: 16px;

    display: flex;
    align-items: center;

    margin-right: 8px;
    margin-bottom: 8px;
    &.--invalid {
      color: red;
    }
  }
  .req-card__tag-detele-icon {
    margin-left: 8px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 300;
    padding: 2px 8px;

    opacity: 0.6;
    &:hover {
      opacity: 1;
    }
  }
  .req-card__tag-input {
    outline: none;
    border: none;
    background-color: #ffffff;
    flex-grow: 1;
    line-height: 36px;

    // padding: 8px;
    margin-right: 8px;
    margin-bottom: 8px;
  }
  .req-card__tags-overlay {
    // display: none;
    position: absolute;
    width: 100%;
    box-shadow: 0px 10px 16px 0px rgba(0, 0, 0, 0.2);
    background-color: #fff;
    z-index: 1;
    left: 0px;
    top: calc(100% + 1px);
    border-radius: 0px 0px 6px 6px;

    max-height: 360px;
    overflow-y: auto;
  }

  .req-card__tags-option {
    padding: 8px;
    display: flex;
    align-items: center;
    cursor: pointer;
    border-top: 1px solid #e9ebee;
    &:hover {
      background-color: #f3f4f6;
    }
    &.--selected {
      background-color: #f7fafc;
    }
    .req-card__user-avatar {
      border-radius: 50%;
      height: 32px;
      min-width: 32px;
      background-color: #aaddd4;
      margin-right: 8px;

      display: flex;
      align-items: center;
      justify-content: center;
    }
    .req-card__user-name {
      font-size: 14px;
      line-height: 16px;

      color: #555555;
      margin-bottom: 4px;
    }
    .req-card__user-email {
      font-size: 12px;
      line-height: 14px;
      /* identical to box height */

      color: #a4a4a4;
    }
    .req-card__user-details {
      flex-grow: 1;
    }
  }
  .req-card__tags-option__selected-icon {
    color: #68d391;
    padding: 4px 8px;
  }
}
.req-card__tags-info {
  font-size: 14px;
  line-height: 16px;
  color: #8a939f;
  margin-top: 8px;
  margin-bottom: 12px;
  i {
    color: #b5b8bb;
    margin-right: 6px;
  }
}

.req-card__body-options__right__test-bar {
  position: absolute;
  top: 100%;
  left: 0px;
  margin-top: 12px;

  line-height: 14px;
  font-size: 12px;

  width: 100%;

  display: flex;
  justify-content: space-between;
  align-items: center;

  .req-card__flex {
    display: flex;
  }
  .req-card__test-btn {
    margin-left: 10px;
    padding: 8px 12px !important;
    line-height: 14px;
    height: 30px;
    font-size: 12px;
    &.--input {
      background-color: #ffffff;
      border: 1px solid #63b3ed;
    }
  }
}

.stripe-cus-details__wrap {
  .stripe-cus-details__header {
    display: flex;
    align-items: center;
    // padding: 8px;

    .req-card__user-avatar {
      border-radius: 50%;
      height: 56px;
      min-width: 56px;
      background-color: #aaddd4;
      margin-right: 16px;

      font-weight: 500;
      font-size: 20px;

      display: flex;
      align-items: center;
      justify-content: center;
    }
    .req-card__user-name {
      font-size: 21px;
      line-height: 25px;

      /* gray/900 */

      color: #111827;
      margin-bottom: 4px;
    }
    .req-card__user-email {
      font-size: 14px;
      line-height: 16px;
      /* identical to box height */

      color: #a4a4a4;
    }
    .req-card__user-details {
      flex-grow: 1;
    }
  }
  .stripe-cus-details__customer-id {
    margin-top: 16px;
    margin-bottom: 16px;
    background: #f9fafb;
    border-radius: 3px;
    padding: 8px;
    font-size: 14px;
    line-height: 16px;
    width: fit-content;
    color: #9ca3af;
  }
  .stripe-cus-details__stats {
    background: #f9fafb;
    border-radius: 3px;
    padding: 8px 16px;
    width: 100%;
    margin-bottom: 16px;

    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .stripe-cus-details__stat {
    // flex: 1 1 0;
    min-width: 100px;
    .stripe-cus-details__stat-label {
      font-size: 12px;
      line-height: 14px;
      color: #9ca3af;
    }
    .stripe-cus-details__stat-value {
      font-weight: 500;
      font-size: 14px;
      line-height: 16px;

      color: #4a5568;
    }
  }

  .stripe-cus-details__info-card {
    background: #ffffff;
    /* gray/200 */

    border: 1px solid #e5e7eb;
    box-sizing: border-box;
    box-shadow: 0px 3px 21px rgba(26, 13, 62, 0.04);
    border-radius: 3px;
    margin-bottom: 16px;
    .stripe-cus-details__info-card__header {
      font-weight: 500;
      font-size: 11px;
      line-height: 13px;
      /* identical to box height */

      letter-spacing: 0.055em;
      text-transform: uppercase;

      color: #b5b9c0;
      padding: 12px 16px;
    }
    .stripe-cus-details__info-card__body {
      background: #f9fafb;
      padding: 8px 16px;
      color: #4a5568;
      .light-text {
        font-size: 12px;
        line-height: 14px;
        color: rgba(0, 0, 0, 0.35);
      }
      .light-tag {
        background: #e0e7ff;
        border-radius: 3px;
        margin-left: 10px;
        padding: 2px 4px;
        font-size: 12px;
        line-height: 14px;
        color: rgba(0, 0, 0, 0.55);
      }
    }
  }
}
.req-card__link {
  color: #3182ce;
  // word-wrap: break-word;
  overflow-wrap: anywhere;
  line-height: 18px;
  cursor: pointer;
}
.btn {
  &.--sending {
    background-color: #178af5 !important;
    &:not(.req-card__test-btn) {
      width: 136px;
      display: flex;
      justify-content: space-between;
    }
  }
}
.stripe-cus-details__wrap {
  max-height: 500px;
  overflow-y: auto;
}
.req-card__next-btn {
  width: 300px;
}

.req-card__body-alert {
  // position: absolute;
  margin-bottom: 24px;
  color: #856200;
  border-left: 4px solid #856200;
}
</style>
