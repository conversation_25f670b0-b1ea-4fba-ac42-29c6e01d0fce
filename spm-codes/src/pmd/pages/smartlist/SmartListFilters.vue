<template>
  <div>
    <div class="optionsbar --blue" v-on:dblclick="(showDelete = true)">
      <div v-if="slVM.listName === emptyListName" class="d-inline-block">
        <i @click.stop.prevent="createNewList"
            class="fas fa-plus zoomable mx-2"
						v-b-tooltip.hover
            title="Save combination of filters, columns and sorting as new smart list">
        </i>
        <i @click.stop.prevent="clearFilters"
            class="fa fa-times zoomable mx-2"
						v-b-tooltip.hover
            title="Clear all filters">
        </i>
      </div>

      <div  v-else class="d-inline-block">
        <i @click.stop.prevent="saveList"
            class="fas fa-save zoomable mx-2"
						v-b-tooltip.hover
            title="Save filter changes to this smart list">
        </i>
        <i @click.stop.prevent="createNewList"
            class="fas fa-plus zoomable mx-2"
						v-b-tooltip.hover
             title="Save current filters as new smart list">
        </i>
        <!-- <i @click.stop.prevent="openSettingsModal"
            class="fa fa-pencil zoomable mx-2"
						v-b-tooltip.hover
            title="Edit smart list name">
        </i> -->
        <i @click.stop.prevent="resetFilters"
            class="fa fa-undo zoomable mx-2"
						v-b-tooltip.hover
            title="Undo unsaved changes">
        </i>
        <!-- <i @click.stop.prevent="deleteList"
            v-if="showDelete"
            class="fas fa-trash zoomable mx-2"
						v-b-tooltip.hover
            title="Delete this smart list">
        </i> -->
      </div>
    </div>
  <div class="card" style=" margin-bottom: 0px !important;">
    <div class="header">
      <div class="dropdown" style="width:100%;">
        <UITextInput
          @focus="focusReceived"
          placeholder="Search and add a filter"
          v-model="search"
        />
        <div class="dropdown-menu" :id="srchOptionsToggler.id"
             style="min-height:50px; max-height: 500px; overflow-y: auto; width:100%">
             <!-- v-if="search"  -->
          <template v-if="searchItems && searchItems.length>0">
            <div class="dropdown-item pointer text-wrap"
                v-model="searchItems" v-for="s in searchItems"
                @click.stop.prevent="applyFilter(s)">
              <span  class="text-wrap text-break space-wrap-preline align-middle">{{s}}</span>
            </div>
          </template>
          <div v-else-if="debouncer" class="dropdown-item" >
            <span class="text align-middle --blue">searching...</span>
          </div>
          <div v-else-if="!debouncer && (!searchItems || searchItems.length === 0)">
            <span class="text align-middle --light mx-3">none found</span>
          </div>
        </div>
      </div>
    </div>
    <div class="body" v-if="slVM.displayModels && slVM.displayModels.length > 0" >

       <template v-for="g in slVM.displayModels.filter(a=> !a.hidden)">
          <FilterExpander :filter="g"
                          :isLocked="calcIsLocked(g)"
                          v-model="g.expandedUX"
                          v-on:expanding="expandFilter"
                          v-on:filterUpdate="filterUpdate"
                          v-on:filterRemove="filterRemove">
          </FilterExpander>
      </template>
    </div>

    <SmartListSettingsModal :smartList="newList" :defaultName="emptyListName"
                            :smartListNames="smartListNames"
                            v-on:saved="modalSaved"
                            v-on:closed="modalClosed"
                            v-if="showSettingsModal"></SmartListSettingsModal>
  </div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import { SmartList, User } from '@/models';
import { IFilter } from './vm/filter_vm'
import { FilterBuilder} from './vm/filter_vm_builder'
import { SmartListVM} from './vm/smart_list_vm';
import FilterExpander from './FilterExpander.vue'
import SmartListSettingsModal from './SmartListSettingsModal.vue'
import { UxMessage } from '@/util/ux_message'
import {SafeDropDownToggle} from './safe_drop_down_toggle'
import { mapState } from 'vuex'

export default Vue.extend({
  name: 'SmartListFilters',
  props : {
    slVM : SmartListVM,
    bus: Vue,
    smartListNames: {
        type: Array,
        default: () => []
    },
    emptyListName: String,
    loading: {
      default: false
    }
  },
  components: {
    FilterExpander,
    SmartListSettingsModal,
  },
  inject: ['uxmessage'],
  data () {
    return {
      showDelete: false,
      search:null,
      searchItems : [] as string[],
      waitTime:300,
      debouncer:undefined as NodeJS.Timer | undefined,
      showSettingsModal: false,
      newList: {} as SmartList,
      srchOptionsToggler: new SafeDropDownToggle(this,'srchOptionsToggler'),
      skipSearch:false,
      focus_flag:false
    };
  },
  watch : {
     search(n,o){
       this.loadItems(n);
     },
  },
  computed: {
    searchOptionsMenuId(){
      return `searchDropDownId-${this._uid}`;
    },
    ...mapState('user', {
      user: (s: UserState) => {
        return s.user ? new User(s.user) : undefined
      }
    }),
  },
  methods: {
    focusReceived(){
      setTimeout(() => {
        this.searchItems = FilterBuilder.getNames(this.search);
        if (this.searchItems && this.searchItems.length > 0) this.srchOptionsToggler.open();
      }, 200);
    },
    calcIsLocked(g: IFilter){
      if (!g || !g.isLocked) return false;
      if (!this.slVM) return false;
      if (!this.slVM.smartList) return false;
      if (this.slVM.smartList.canUserEdit(this.user) !== true) return true;
      return false;
    },

    // async focusRemoved(){ //@blur
    //   this.searchItems = [];
    //   this.srchOptionsToggler.hide();
    // },
    loadItems(n:string){
      if(this.skipSearch){
        this.skipSearch = false;
        return;
      }
      if(this.debouncer) clearTimeout(this.debouncer);
      this.debouncer = setTimeout(() => this.loadSearchItems(n),this.waitTime);
      this.srchOptionsToggler.open();
    },
    async expandFilter(filter : IFilter) {
      this.slVM.setExpanded(filter);
    },
    async loadSearchItems(s:string){
      this.searchItems = FilterBuilder.getNames(this.search);
      this.debouncer = undefined;
    },
    resetSearch(){
      this.skipSearch = true; // otherwise it search again in search watch
      this.search = undefined; // this is going to open dropdown so change it before closing
      this.srchOptionsToggler.hide();
    },
    async applyFilter(selected:string){
      this.resetSearch();
      this.slVM.addFilterByName(selected, (shouldUpdateView:boolean) => {
          this.publishChanges();
      });

    },
    clearFilters(){
      this.slVM.clearFilters();
      this.clearVMSelections();
      this.publishChanges();
    },
    resetFilters(){
      if (!this.slVM) return
      this.clearVMSelections();
      this.$emit('listChange',this.slVM.listId);
    },
    async saveList() {
      this.slVM.saveList();
    },
    async deleteList(){
      //if (!this.slVM || !this.slVM.listName || this.slVM.listName.trim() === '') return;
      let msg = `Do you want to delete list titled ${this.slVM.listName} ?`;
      let func = async (resp:string) => {
            if (resp === 'ok'){
              try {
                await this.slVM.deleteList();
                this.$emit('itemDeleted',this.slVM.listId);
              }
              catch (exp) { console.log(exp)}
              finally{ this.loading = false ;}
            }
            return;
      };
      this.uxmessage(UxMessage.confirmationType(msg,func));
    },
    publishChanges() {
      this.$emit('listUpdate', this.slVM.listId);
    },
    async createNewList(){
      if (!this.slVM) return;
      this.newList = this.slVM.getListCopy();
      const currentUser = await this.$store.dispatch('user/get');
      if (currentUser) this.newList.userId = currentUser.id;
      this.showSettingsModal = true;
    },
    async filterUpdate (item: IFilter) {
      if (!this.slVM) return
      this.slVM.unSelectAll()
      this.slVM.filterUpdate(item);
      this.clearVMSelections();
      this.publishChanges();
    },
    async filterRemove(items: IFilter[]) {
      if (!this.slVM) return
      this.slVM.unSelectAll()
      this.slVM.removeFilters(items, async () => {
        this.clearVMSelections();
        this.publishChanges();
      });
    },
    modalSaved(id: string){
      this.showSettingsModal = false;
      this.$emit('itemAdded',id);
    },
    modalClosed(){
      this.showSettingsModal = false;
    },
    clearVMSelections(){
      if (this.slVM) this.slVM.unSelectAll();
    }
  },
})
</script>

<style scoped>
  .optionsbar
  {
      background: #E8F3FE;
      /* margin-bottom: 10px; */
      height: 40px;
      padding: 10px;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      /* border-bottom: 2px solid #dee5e8; */
  }
</style>
