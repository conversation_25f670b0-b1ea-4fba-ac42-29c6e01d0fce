<template>
	<div class="message-body--aside">
		<div class="aside-texts">
			<Avatar :contact="contact" :size="'md'" @click="contactDetail(contact)" class="pointer"/>

			<div class="aside-texts-name">
				<input type="text" v-model="getDisplayName" @blur.stop="saveContact" placeholder="Name">
				<!-- <p>San Diego, California</p> -->
			</div>
			<div class="aside-texts-infos">
				<p>
					<i style="cursor: pointer;" class="icon-phone" @click.stop="makeCall();"></i>
					<PhoneNumber
						customClass
						v-if="contact"
						placeholder="Phone Number"
						v-model="contact.phone"
						:currentLocationId="locationId"
						@change="saveContact"
					/>
					<input v-else type="text" placeholder="Phone Number">
				</p>
				<p>
					<i class="icon-email"></i>
					<input type="text" placeholder="Email" data-lpignore="true" v-model="contactEmail" @blur.stop="saveContact">
				</p>
				<p class="conversation-tags">
					<i class="icon-tag"></i>
					<TagComponent v-model="tags" :form="false" @change="saveContact"/>
				</p>
				<div class="tag-group" v-if="tags.length > 0" style="margin-top: 10px;margin-left: 30px;">
					<div class="tag" v-for="tag in tags">
						{{tag}}
						<a @click="removeTag(tag)">
							<i class="icon icon-close"></i>
						</a>
					</div>
				</div>
				<div class="form-group" style="margin-top: 10px;">
					<label>DND (Opt out of marketing campaigns)</label>
					<input
						type="checkbox"
						class="tgl tgl-light"
						id="account-buffer-tgl-conv"
						v-model="dnd"
						@change="saveContact"
					>
					<label class="tgl-btn" for="account-buffer-tgl-conv"></label>
				</div>
				<div v-if="user && (user.automationPermission.campaigns || user.automationPermission.workflows)" class="form-group">
					<label>Active {{ automationString }}</label>
					<div class="tag-group">
						<div
							class="tag"
							v-for="campaign in activeCampaigns"
							:key="campaign.id"
							style="margin-right: 10px;"
						>
							{{campaign.name}}
							<a @click="removeFromCampaign(campaign)">
								<i class="icon icon-close"></i>
							</a>
						</div>
						<button
							style="display: inline-block;position: relative;"
							type="button"
							class="btn btn-light2 btn-xs"
							@click="showaddToCampaignModal"
						>
							<i class="fas fa-plus"></i> Add
						</button>
					</div>
				</div>
			</div>
		</div>
		<div class="btn-sched-wrap">
			<a
      			v-if="user && user.permissions.opportunities_enabled"
				href="javascript:void(0);"
				@click.prevent="moreOptions"
				class="btn btn-primary btn-sched"
			>Create Opportunity</a>
			<a
				v-if="user && user.permissions.appointments_enabled"
				style="margin-top: 5px;"
				href="javascript:void(0);"
				@click.prevent="scheduleAppointment"
				class="btn btn-primary btn-sched"
			>Schedule</a>
		</div>
	</div>
</template>

<script lang="ts">
import Vue from "vue";
import { mapState } from 'vuex';
import { User } from '@/models'
import { UserState } from '@/store/state_models'
const PhoneNumber = () => import("../../components/util/PhoneNumber.vue");
const TagComponent = () => import("../../components/customer/TagComponent.vue");
const Avatar = () => import("../../components/Avatar.vue");
import { Contact } from "@/models";
export default Vue.extend({
	props: [
		"contact",
		"getDisplayName",
		"locationId",
		"activeCampaigns"
	],
	components: {
		PhoneNumber,
		TagComponent,
		Avatar
	},
	computed: {
    ...mapState('user', {
			user: (s: UserState) => {
				return s.user ? new User(s.user) : undefined
			},
      automationString() {
      return this.user.automationString() || 'Campaigns / Workflows'
    },
		}),
		contactEmail: {
			get: function (): string {
				if (!this.contact) return "";
				return this.contact.email;
			},
			set: function (valuecontactEmail: string) {
				if (this.contact) this.contact.email = value;
			}
		},
		tags: {
			get: function (): string[] {
				if (!this.contact) return [];
				return this.contact.tags;
			},
			set: function (value: string[]) {
				if (this.contact) this.contact.tags = value;
			}
		},
		dnd: {
			get: function (): boolean {
				if (!this.contact) return false;
				return this.contact.dnd || false;
			},
			set: function (value: boolean) {
				if (this.contact) this.contact.dnd = value;
			}
		}
	},
	methods: {
		scheduleAppointment() {
			this.$parent.scheduleAppointment();
		},
		moreOptions() {
			this.$parent.moreOptions();
		},
		removeFromCampaign(campaign: any) {
			this.$parent.removeFromCampaign(campaign);
		},
		removeTag(tag: any) {
			this.$parent.removeTag(tag);
		},
		saveContact() {
			this.$parent.saveContact();
		},
		contactDetail(contact: Contact) {
			this.$parent.contactDetail(contact);
		},
		showaddToCampaignModal() {
			this.$parent.showaddToCampaignModal();
		}
	}
});
</script>


<style>
</style>
