<template>
  <div class="form-group">
    <!-- <label>{{ description || 'Tags'}}</label> -->
    <TagComponent v-model="selectedTags" name="tags-selection" v-validate="'required'" :placeholder="placeholder"/>
    <span v-show="errors.has('tags-selection')" class="error">{{ errors.first('tags-selection') }}
    </span>
    <!-- <span v-show="errors.has('campaign')" class="error">Campaign is required.</span> -->
  </div>
</template>

<script lang="ts">
// import * as lodash from 'lodash'
import Vue from 'vue'
import { ITagOperationSpecs } from './server/bulkreq_interfaces'
const TagComponent = () => import('@/pmd/components/customer/TagComponent.vue')
// import { mapState } from 'vuex'
// import { UserState } from '@/store/state_models'
// import {
//   User,
// } from '@/models'

declare var $: any

export default Vue.extend({
  name: 'TagsSelection',
  inject: ['parentValidator'],
  props: {
    action: Object as () => ITagOperationSpecs,
    locationId: String,
    placeholder: String,
  },
  components: {
    TagComponent,
  },
  data() {
    return {
      selectedTags: [] as [] | null,
    }
  },
  computed: {
    // ...mapState('user', {
    //   user: (s: UserState) => {
    //     console.log(s.user)
    //     return s.user ? new User(s.user) : undefined
    //   },
    // }),
    // automationString() {
    //   return this.user.automationString(false) ||
    // }
  },
  methods: {
    // async fetchData(): Promise<void> {
    // },
  },
  watch: {
    selectedTags(tags: any) {
      this.action.tags = tags
    }
  },
  created() {
    this.$validator = this.parentValidator
  },
  async mounted() {
    // await this.fetchData()
  },
})
</script>
