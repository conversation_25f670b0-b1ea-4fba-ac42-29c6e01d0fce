import { Module } from 'vuex';
import { ManualCallStatusState, RootState } from '@/store/state_models';

export const manualCallStatus: Module<ManualCallStatusState, RootState> = {
    namespaced: true,
    state: {
      manualCallId: ''
    },
    getters: {
        get: (state: ManualCallStatusState, getters: any, rootState: RootState, rootGetters: any) => {
            return state;
        },
    },
    mutations: {
        show: (state: ManualCallStatusState, manualCallId: string) => {
            state.manualCallId = manualCallId;
        },
        hide: (state: ManualCallStatusState) => {
            state.manualCallId = '';
        },
    },
};
