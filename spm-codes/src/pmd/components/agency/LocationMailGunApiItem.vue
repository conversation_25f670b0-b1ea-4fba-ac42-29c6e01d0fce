<template>
  <tr>
    <td>
      <i v-if="mailgunError" class="fa fa-exclamation-triangle text-danger" v-b-tooltip.click.html="mailgunError"></i>
      {{location.name}}</td>
    <td>{{apiKey}}</td>
    <td>{{domain}}</td>
      <td>
      <div v-if="toggleLoading" style="display:flex; justify-items: start; justify-content: start;">
        <moon-loader :loading="toggleLoading" color="#188bf6" size="16px" />
      </div>
      <div v-else class="toggle">
        <UIToggle
          :id="`update_email_validation${locationMailGunAccount.id}`"
          v-model="validate_emails"
          v-on:change="editValidateEmail"
        />
        <label class="tgl-btn" :for="`update_email_validation${locationMailGunAccount.id}`"></label>
      </div>
    </td>
    <td>
      <i v-if="loading === false" class="icon icon-pencil --blue pointer" @click.prevent="edit"></i>
      <div class="d-inline-block align-middle" v-show="loading === true">
        <moon-loader :loading="loading" color="#188bf6" size="15px" />
      </div>
    </td>
  </tr>
</template>

<script lang="ts">
import Vue from 'vue'
import { MailGunAccount, Location } from '@/models'

export default Vue.extend({
  props: { location: {}, locationMailGunAccount: {}, bus: Vue },
  data() {
    return {
      mailGunAccount: {} as MailGunAccount,
      apiKey: '',
      domain: '',
      loading: false,
      validate_emails: false,
      toggleLoading: false,
      mailgunError: undefined
    }
  },
  async created() {
    this.refresh()
  },
  watch: {
    bus(newVal) {
      this.subscribe()
    },
    locationMailGunAccount() {
      this.refresh()
    }
  },
  mounted() {
    this.subscribe()
  },
  methods: {
    async editValidateEmail() {
      this.toggleLoading = true
      let body = {
            forwardingAddresses: this.locationMailGunAccount.forwarding_addresses,
            bccEmails: this.locationMailGunAccount.bcc_emails,
            apiKey: this.locationMailGunAccount.api_key,
            domain: this.locationMailGunAccount.domain,
            emailValidation: this.validate_emails,
            company_id: this.location.company_id
          };
      this.$http.post('/mailgun/edit?location_id=' + this.location.id, body).then(async res => {
        if (res && res.status == 200) this.locationMailGunAccount.validate_emails = this.validate_emails // Update the array's value (not DB)
      }).catch(err => {
        alert('An error has ocurred, please try again.')
      }).finally(() => {
        this.toggleLoading = false
      })
    },
    subscribe() {
      if (this.bus) {
        console.log('subscribing')
        this.bus.$on('refresh', this.refresh)
      }
    },
    edit() {
      this.$emit('edit', { locationId: this.location.id })
    },
    async refresh(event?: any) {
      try {
        if (this.locationMailGunAccount) {
          this.apiKey = this.locationMailGunAccount.api_key
          this.domain = this.locationMailGunAccount.domain
          this.validate_emails = this.locationMailGunAccount.validate_emails || false
          this.mailgunError = this.locationMailGunAccount.mailgun_error || undefined
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    }
  }
})
</script>
