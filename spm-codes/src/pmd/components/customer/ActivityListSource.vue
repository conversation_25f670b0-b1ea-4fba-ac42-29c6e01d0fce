<template>
  <span class="badge" :class="badgeClass">
    <svg
      width="11"
      height="11"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      v-if="activity.adSource == 'Facebook'"
    >
      <path
        d="M9.19795 21.5H13.198V13.4901H16.8021L17.198 9.50977H13.198V7.5C13.198 6.94772 13.6457 6.5 14.198 6.5H17.198V2.5H14.198C11.4365 2.5 9.19795 4.73858 9.19795 7.5V9.50977H7.19795L6.80206 13.4901H9.19795V21.5Z"
        fill="currentColor"
      />
    </svg>

    <svg
      width="11"
      height="11"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      v-if="activity.adSource == 'Google' || activity.adSource == 'Adword'"
    >
      <path
        d="M6 12C6 15.3137 8.68629 18 12 18C14.6124 18 16.8349 16.3304 17.6586 14H12V10H21.8047V14H21.8C20.8734 18.5645 16.8379 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C15.445 2 18.4831 3.742 20.2815 6.39318L17.0039 8.68815C15.9296 7.06812 14.0895 6 12 6C8.68629 6 6 8.68629 6 12Z"
        fill="currentColor"
      />
    </svg>

    {{ activity.source }}
  </span>
</template>

<script lang="ts">
  import Vue from 'vue';
  import {SessionType} from "@/models/index";
  
  export default Vue.extend({
    props: {
      activity: Object
    },
    computed: {

      isPageVisit() {
        if ([SessionType.PAGE_VISIT, SessionType.FB_LEAD_FORM].includes(this.activity.type)) {
          return true;
        }

        return false;
      },

      isShowSource() {
        if (this.isPageVisit && this.activity.source && ['Paid Search', 'Paid Social', 'Social media', 'Organic Search'].includes(this.activity.source)) {
          return true
        }

        return false;
      },
      badgeClass() {
        if (this.isShowSource) {

          if (this.activity.adSource == 'Facebook' || this.activity.source == 'Social media') {
            return 'badge-primary'
          } else if (this.activity.adSource == 'Google' || this.activity.adSource == 'Adword') {
            return 'badge-success'
          }

          return 'badge-custom'
        }

        return '';
      }
    }
  });
</script>